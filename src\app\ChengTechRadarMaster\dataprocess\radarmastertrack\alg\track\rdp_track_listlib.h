﻿/**
 * @file rdp_track_listlib.h
 * @brief 
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _TRACK_LIST_LIB_H__
#define _TRACK_LIST_LIB_H__

#define MERGED_TRACKS_NUM_MAX 3

#ifndef PC_DBG_FW
#include <stdint.h>
#else
#include "app/system_mgr/typedefs.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif



/**
 * @brief 
 *  GTrack ListElement
 *
 * @details
 *  The structure describes a list node which has links to the previous
 *  and next element in the list.
 */
typedef struct GTrack_ListElem_t
{
    uint32_t                    data;
    struct GTrack_ListElem_t    *prev;
    struct GTrack_ListElem_t    *next;
} GTrack_ListElem;

/**
 * @brief 
 *  GTrack List Object
 *
 * @details
 *  The structure describes the list object 
 */
typedef struct
{
    uint32_t            count;
    GTrack_ListElem     *begin;
    GTrack_ListElem     *end;
} GTrack_ListObj;

typedef struct{
    GTrack_ListObj pointList;
    uint32_t status;
    uint8_t trackId[MERGED_TRACKS_NUM_MAX];
    uint8_t trackNum;
}TrackGroupInfo_t;

/**********************************************************************
 **************************** EXPORTED API ****************************
 **********************************************************************/

extern void gtrack_listInit (GTrack_ListObj *list);
extern int32_t gtrack_isListEmpty (GTrack_ListObj *list);
extern void gtrack_listEnqueue (GTrack_ListObj *list, GTrack_ListElem *elem);
extern void gtrack_listInsert (GTrack_ListObj *list, GTrack_ListElem *elem);
extern GTrack_ListElem *gtrack_listDequeue (GTrack_ListObj *list);
extern GTrack_ListElem* gtrack_listGetFirst (GTrack_ListObj *list);
extern GTrack_ListElem* gtrack_listGetNext (GTrack_ListElem *elem);
extern uint32_t gtrack_listGetCount (GTrack_ListObj *list);
extern int32_t gtrack_listRemoveElement (GTrack_ListObj *list, GTrack_ListElem *elem);

#ifdef __cplusplus
}
#endif

#endif /* GTRACK_LIST_LIB_H__ */

