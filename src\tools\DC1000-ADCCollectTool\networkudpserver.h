﻿#ifndef NETWORKUDPSERVER_H
#define NETWORKUDPSERVER_H

#include <windows.h>
#include <string>
#include <mutex>

#include "NETFrame.h"
#include "SafeQueue.h"

typedef void (*NETCallBack)(unsigned short port, int len, unsigned char *data, void *obj);

class NetworkUDPServer
{
public:
    NetworkUDPServer(NETCallBack callback, void *callbackObj, std::string IP, uint16_t port, std::string rIP, uint16_t rPort, bool anyIP);
    ~NetworkUDPServer();

    bool open();
    bool close();
    int sendData(const char *data, int len);

    bool isOpened() const { return mOpened; }

    const std::string &errorstring() const { return mErrorString; }

private:
    int receive();

    friend void deviceReceive(NetworkUDPServer *pUDPServer);

    std::mutex mMutex;
    SOCKET mSocket;

    char mRecviveData[NET_FRAME_DATA_MAX];

    bool mOpened{false};
    std::string mErrorString;

    NETCallBack mNETCallBack{0};
    void *mNETCallBackObj{0};

    bool mBroadcast{false};
    bool mMulticast{false};
    std::string mMulticastIP{ "127.0.0.1" };
    std::string mLocalIP{ "127.0.0.1" };    ///< 本地IP地址
    uint16_t    mLocalPort{ 0 };            ///< 本地端口
    std::string mRemoteIP{ "127.0.0.1" };   ///< 本地IP地址
    uint16_t    mRemotePort{ 0 };           ///< 本地端口
    bool        mAnyIP{ false };            ///< 使用本地任何IP
};

#endif // NETWORKUDPSERVER_H
