#ifndef FRAMECONVERTFORM_H
#define FRAMECONVERTFORM_H

#include "ui_frameconvertform.h"

#include <QWidget>

namespace Ui {
class FrameConvertForm;
}

namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
}

//class IFrameConver;
class FrameConvertModel;

class FrameConvertForm : public QWidget
{
    Q_OBJECT

public:
    explicit FrameConvertForm( Devices::Can::DeviceManager *deviceManager, QWidget *parent = nullptr);
    ~FrameConvertForm();

signals:
    void started();
    void stoped();


private slots:
//    void on_startPushButton_clicked();
//    void on_stopPushButton_clicked();

private:
    Ui::FrameConvertForm *ui;

    Devices::Can::DeviceManager *mDeviceManager;
    //QList<IFrameConver*> mFrameConverList;
    FrameConvertModel* mFrameConvertModel;
};

#endif // FRAMECONVERTFORM_H
