/**
 * @file rdp_pre_ekf.h
 * @brief 
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef __PRE_EKF_H__
#define __PRE_EKF_H__

/* Include Files */
#include <math.h>
#include <stddef.h>
#include <stdlib.h>
#include <string.h>


/* Function Declarations */
extern void RDP_Track_cacEKFMatrixs(float a, float T, float EKF_A[36], float EKF_Q[36]);

#endif

/*
 * File trailer for rdp_pre_ekf.h
 *
 * [EOF]
 */
