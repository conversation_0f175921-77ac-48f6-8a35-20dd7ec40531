﻿/**
 *  @file data_hil_det_trk.c
 *  @brief This file defines the functions for track HIL and detection HIL.
 *  <AUTHOR> (<EMAIL>)
 *  @date 2023-08-09
 *
 *  @details None.
 *  @par 修改日志:
 *  <table>
 *  <tr><th>Date        <th>Version  <th>Author   <th>Description
 *  <tr><td>2023-08-09  <td>v0.0.1   <td>erthfw   <td>初始版本
 *  </table>
 *  @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

/****************************************************************************
  INCLUDE
 ****************************************************************************/
#include <math.h>
#include <string.h>
#include "typedefs.h"
#include "cfg.h"
#include "rsp_types.h"
#include "rdp_interface.h"
#include "rsp_interface.h"
#include "vdy_interface.h"
#include "pcan_protocol.h"
#include "app_vehicle.h"
#include "data_hil_det_trk.h"
#include "app_dbg.h"


/*****************************************************************************
  GLOBAL VARIABLES
 *****************************************************************************/
static int gRxRawObjCnt = 0;                            // 通过CAN收到的检测点目标数量
static int gRxTrkObjCnt = 0;                            // 通过CAN收到的跟踪点目标数量
static volatile float gHILDetTrkFrameTime = 0.0f;       // 数据回灌的帧间隔
uint32_t gHILDetTrkFrameIdx = 0;                        // 回灌模式下检测点/跟踪点的当前数据帧号
static uint32_t gHILDetTrkFrameCnt = 0;                 // 通过CAN收到0x4FN结束帧的帧计数
static HilAdasFunctionState_t ghilAdasFunctionState;    //回灌模式下解析报警功能状态


/**
 *  @b Description
 *  @n
 *      Calculate the Hex value to physical value based on factor and offset.
 *
 *  @param[in]  physicalValue
 *      The input of physical value
 *  @param[in]  factor
 *      The factor used for calculation
 *  @param[in]  offset
 *      The offset used for calculation
 *
 *  @retval
 *      Physical value of CAN data
 */
float COMLIB_calcHexToPhyCanData(uint32_t canRawData, float factor, float offset)
{
    float val;

    val = ((float)(canRawData) * factor) + offset;

    return val;
}

/**
 *  @b Description
 *  @n
 *      Set the HIL frame interval time and update frame index.
 *
 *  @param[in]  trkTime
 *      The HIL frame interval time, in s.
 */
void HIL_setDetTrkFrmTimeAndUpdateFrmIdx(float trkTime)
{
    RDP_DebugInfo_t *pRdpDebugInfo = RDP_getDebugDataPtr();

    if (DBG_getHILDataDbgMode() == DATA_HIL_DET_TRK_NONE)
    {
        return;
    }

    pRdpDebugInfo->rdpTaskTimeCycle = trkTime;
    gHILDetTrkFrameTime = trkTime;
    gHILDetTrkFrameIdx++;
}

/**
 *  @b Description
 *  @n
 *      Get the frame interval time of indetection or track HIL.
 *
 *  @retval
 *      The frame interval time, in s.
 */
float HIL_getDetTrkFrameTime(void)
{
    return gHILDetTrkFrameTime;
}

/**
 *  @b Description
 *  @n
 *      Set the frame index of detection or track HIL mode.
 *
 *  @param[in]  frameIdx
 *      The frame index.
 */
void HIL_setDetTrkFrameIdx(uint32_t frameIdx)
{
    gHILDetTrkFrameIdx = frameIdx;
}

/**
 *  @b Description
 *  @n
 *      Get the frame index of detection or track HIL mode.
 *
 *  @retval
 *      The frame index.
 */
uint8_t HIL_getDetTrkFrameIdx(void)
{
    return gHILDetTrkFrameIdx;
}

/**
 *  @b Description
 *  @n
 *      Set the current frame count of hil frames.
 *
 *  @param[in]  frameCnt
 *      The frame count.
 */
void HIL_setDetTrkFrameCnt(uint32_t frameCnt)
{
    gHILDetTrkFrameCnt = frameCnt;
}

/**
 *  @b Description
 *  @n
 *      Get the current frame count of hil frames.
 *
 *  @retval
 *      The frame count.
 */
uint32_t HIL_getDetTrkFrameCnt(void)
{
    return gHILDetTrkFrameCnt;
}

/**
 *  @b Description
 *  @n
 *      Get the hil Adas function state.
 *
 *  @retval
 *      The frame count.
 */
const HilAdasFunctionState_t *HIL_getHilAdasFunctionState(void)
{
    return &ghilAdasFunctionState;
}

/**
 *  @b Description
 *  @n
 *      Reset the Targets and set Targets number.
 *
 *  @param[in]  objNum
 *      Set the Targets number.
 *
 */
void HIL_resetOutTargets(int objNum)
{
    if (DBG_getHILDataDbgMode() == 0)
    {
        return;
    }

    RSP_DetObjectInfo_t ObjInfo = {0};
    for (int i = 0; i < (MAX_NUM_OF_POINTS * 2); i++)
    {
        RSP_setObjectForTrkDbgMode(i, &ObjInfo);
    }

    gRxRawObjCnt = 0;
    RSP_setDetObjectLists(objNum);
}

/**
 *  @b Description
 *  @n
 *      Reset the TrkObjects and set TrkObjects number.
 *
 *  @param[in]  objNum
 *      Set the TrkObjects number.
 *
 */
void HIL_resetTrkObjects(int objNum)
{
    if (DBG_getHILDataDbgMode() == 0)
    {
        return;
    }

    RDP_TrkObjectInfo_t trkObjInfo = {0};
    for (int i = 0; i < MAX_NUM_OF_POINTS; i++)
    {
        RDP_setTrkObjectForTrkDbgMode(i, &trkObjInfo);
    }

    gRxTrkObjCnt = 0;
    RDP_setTrkObjectNumForTrkDbgMode(objNum);
}

/**
 *  @b Description
 *  @n
 *      Get the RawObjList message from PCAN.
 *
 *  @param[in]  pBuf
 *      The buffer of CAN RX data.
 *  @param[in]  version
 *      The protocol version.
 *
 */
void HIL_getRawObjListFromCan(const void *pBuf, int version)
{
    if ((DBG_getHILDataDbgMode() != DATA_HIL_DET) || gRxRawObjCnt > MAX_NUM_OF_POINTS)
    {
        return;
    }

    /*两个协议无太大差异，暂使用同一解析*/
    if ((version == 1) || (version == 0))
    {
        RSP_DetObjectInfo_t pList = {0};
        const SRR_RawDetections_t *pMsg = (const SRR_RawDetections_t *)pBuf;
        // Obj 1
        if ((RSP_getDetObjectLists() - gRxRawObjCnt) > 0)
        {
            pList.rspDetValid = 1;
            pList.rspDetRange           = pMsg->RL_RawObjectRange01 == 0x1FFFF ? 1024 : COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectRange01, 0.01, 0);
            pList.rspDetVelocity        = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectVelocity01, 0.01, -81.92);
            pList.rspDetAzimuthAngle    = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectAzimuth01, 0.01, -163.84);
            pList.rspDetElevationAngle  = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectElevationAngle01, 0.02, -81.92);
            pList.rspDetSNR             = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectSNR01, 0.5, 0);
            pList.rspDetRCS             = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectRCS01, 0.5, -30);
            pList.rspDetStatus          = pMsg->RL_RawObjectStatus01;

            RSP_setObjectForTrkDbgMode(gRxRawObjCnt, &pList);
            gRxRawObjCnt++;
        }
        // Obj 2
        if ((RSP_getDetObjectLists() - gRxRawObjCnt) > 0)
        {
            pList.rspDetValid = 1;
            pList.rspDetRange           = pMsg->RL_RawObjectRange02 == 0x1FFFF ? 1024 : COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectRange02, 0.01, 0);
            pList.rspDetVelocity        = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectVelocity02, 0.01, -81.92);
            pList.rspDetAzimuthAngle    = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectAzimuth02, 0.01, -163.84);
            pList.rspDetElevationAngle  = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectElevationAngle02, 0.02, -81.92);
            pList.rspDetSNR             = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectSNR02, 0.5, 0);
            pList.rspDetRCS             = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectRCS02, 0.5, -30);
            pList.rspDetStatus          = pMsg->RL_RawObjectStatus02;

            RSP_setObjectForTrkDbgMode(gRxRawObjCnt, &pList);
            gRxRawObjCnt++;
        }
        // Obj 3
        if ((RSP_getDetObjectLists() - gRxRawObjCnt) > 0)
        {
            pList.rspDetValid = 1;
            pList.rspDetRange           = pMsg->RL_RawObjectRange03 == 0x1FFFF ? 1024 : COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectRange03, 0.01, 0);
            pList.rspDetVelocity        = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectVelocity03, 0.01, -81.92);
            pList.rspDetAzimuthAngle    = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectAzimuth03, 0.01, -163.84);
            pList.rspDetElevationAngle  = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectElevationAngle03, 0.02, -81.92);
            pList.rspDetSNR             = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectSNR03, 0.5, 0);
            pList.rspDetRCS             = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectRCS03, 0.5, -30);
            pList.rspDetStatus          = pMsg->RL_RawObjectStatus03;

            RSP_setObjectForTrkDbgMode(gRxRawObjCnt, &pList);
            gRxRawObjCnt++;
        }
        // Obj 4
        if ((RSP_getDetObjectLists() - gRxRawObjCnt) > 0)
        {
            pList.rspDetValid = 1;
            pList.rspDetRange           = pMsg->RL_RawObjectRange04 == 0x1FFFF ? 1024 : COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectRange04, 0.01, 0);
            pList.rspDetVelocity        = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectVelocity04, 0.01, -81.92);
            pList.rspDetAzimuthAngle    = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectAzimuth04, 0.01, -163.84);
            pList.rspDetElevationAngle  = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectElevationAngle04, 0.02, -81.92);
            pList.rspDetSNR             = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectSNR04, 0.5, 0);
            pList.rspDetRCS             = COMLIB_calcHexToPhyCanData(pMsg->RL_RawObjectRCS04, 0.5, -30);
            pList.rspDetStatus          = pMsg->RL_RawObjectStatus04;

            RSP_setObjectForTrkDbgMode(gRxRawObjCnt, &pList);
            gRxRawObjCnt++;
        }
    }
    else
    {
        ///todo
    }
}

/**
 *  @b Description
 *  @n
 *      Get the TrackObjList message from PCAN.
 *
 *  @param[in]  pBuf
 *      The buffer of CAN RX data.
 *  @param[in]  version
 *      The protocol version.
 *
 */
void HIL_getTrackObjListFromCan(const void *pBuf, int version)
{
    if ((DBG_getHILDataDbgMode() != DATA_HIL_TRACK) || gRxTrkObjCnt > MAX_NUM_OF_TRACKS)
    {
        return;
    }

    if (version == 1)
    {
        const COM_trkObjectListVer1_t *pMsg = (const COM_trkObjectListVer1_t *)pBuf;
        RDP_TrkObjectInfo_t trkObjInfo = {0};

        trkObjInfo.id = pMsg->trkObjID01;
        trkObjInfo.rdpTrkObjDistX           = COMLIB_calcHexToPhyCanData(pMsg->trkObjDistLat01, 0.025f, -102.4f);
        trkObjInfo.rdpTrkObjDistY           = COMLIB_calcHexToPhyCanData(pMsg->trkObjDistLong01, 0.025f, -204.8f);
        trkObjInfo.rdpTrkObjDistZ           = COMLIB_calcHexToPhyCanData(pMsg->trkObjDistAltitude01, 0.025f, -12.8f);
        trkObjInfo.rdpTrkObjRange           = sqrt(trkObjInfo.rdpTrkObjDistX * trkObjInfo.rdpTrkObjDistX + trkObjInfo.rdpTrkObjDistY * trkObjInfo.rdpTrkObjDistY);
        trkObjInfo.rdpTrkObjVrelX           = COMLIB_calcHexToPhyCanData(pMsg->trkObjVrelLat01, 0.04f, -163.84f);
        trkObjInfo.rdpTrkObjVrelY           = COMLIB_calcHexToPhyCanData(pMsg->trkObjVrelLong01, 0.04f, -163.84f);
        trkObjInfo.rdpTrkObjArelX           = COMLIB_calcHexToPhyCanData(pMsg->trkObjArelLat01, 0.05f, -10.8f);
        trkObjInfo.rdpTrkObjArelY           = COMLIB_calcHexToPhyCanData(pMsg->trkObjArelLong01, 0.05f, -12.8f);
        trkObjInfo.rdpTrkObjBoxLength       = COMLIB_calcHexToPhyCanData(pMsg->trkObjLength01, 0.2, 0);
        trkObjInfo.rdpTrkObjBoxWidth        = COMLIB_calcHexToPhyCanData(pMsg->trkObjWidth01, 0.2, 0);
        trkObjInfo.rdpTrkObjStatus          = pMsg->trkObjDynProp01;
        trkObjInfo.rdpTrkObjReliability     = COMLIB_calcHexToPhyCanData(pMsg->trkObjProbOfExist01, 3.2258, 0);
        trkObjInfo.rdpTrkObjAzimuthAngle    = atan2f(trkObjInfo.rdpTrkObjDistX, trkObjInfo.rdpTrkObjDistY) * 180 / PI;
        trkObjInfo.rdpTrkObjsnr             = COMLIB_calcHexToPhyCanData(pMsg->trkObjSNR01, 0.5f, 0);
        trkObjInfo.rdpTrkObjBoxCenterY      = COMLIB_calcHexToPhyCanData(pMsg->trkObjBoxCenterLong01, 0.025f, -204.8f);
        trkObjInfo.rdpTrkObjBoxCenterX      = COMLIB_calcHexToPhyCanData(pMsg->trkObjBoxCenterLat01, 0.025f, -102.4f);
        trkObjInfo.rdpTrackType             = pMsg->trkObjTrackType01;
        trkObjInfo.rdpTrkObjVelocity        = -sqrt(trkObjInfo.rdpTrkObjVrelX *trkObjInfo.rdpTrkObjVrelX + trkObjInfo.rdpTrkObjVrelY * trkObjInfo.rdpTrkObjVrelY);

        trkObjInfo.rdpTrkObjType            = pMsg->trkObjClass01;
        trkObjInfo.rdpTrkObjStatus          = pMsg->trkObjStatus01;
        trkObjInfo.rdpLifeCycleCnt          = pMsg->trkObjTrackLifeCycleCnt01;
        trkObjInfo.rdpTrkObjHitCnt          = pMsg->trkObjHitCnt01;
        trkObjInfo.rdpTrkObjMissCnt         = pMsg->trkObjMissCnt01;

        RDP_setTrkObjectForTrkDbgMode(pMsg->trkObjID01, &trkObjInfo);

        trkObjInfo.id = pMsg->trkObjID02;
        trkObjInfo.rdpTrkObjDistX           = COMLIB_calcHexToPhyCanData(pMsg->trkObjDistLat02, 0.025f, -102.4f);
        trkObjInfo.rdpTrkObjDistY           = COMLIB_calcHexToPhyCanData(pMsg->trkObjDistLong02, 0.025f, -204.8f);
        trkObjInfo.rdpTrkObjDistZ           = COMLIB_calcHexToPhyCanData(pMsg->trkObjDistAltitude02, 0.025f, -12.8f);
        
        trkObjInfo.rdpTrkObjRange           = sqrt(trkObjInfo.rdpTrkObjDistX * trkObjInfo.rdpTrkObjDistX + trkObjInfo.rdpTrkObjDistY * trkObjInfo.rdpTrkObjDistY);
        trkObjInfo.rdpTrkObjVrelX           = COMLIB_calcHexToPhyCanData(pMsg->trkObjVrelLat02, 0.04f, -163.84f);
        trkObjInfo.rdpTrkObjVrelY           = COMLIB_calcHexToPhyCanData(pMsg->trkObjVrelLong02, 0.04f, -163.84f);
        trkObjInfo.rdpTrkObjArelX           = COMLIB_calcHexToPhyCanData(pMsg->trkObjArelLat02, 0.05f, -10.8f);
        trkObjInfo.rdpTrkObjArelY           = COMLIB_calcHexToPhyCanData(pMsg->trkObjArelLong02, 0.05f, -12.8f);
        trkObjInfo.rdpTrkObjBoxLength       = COMLIB_calcHexToPhyCanData(pMsg->trkObjLength02, 0.2, 0);
        trkObjInfo.rdpTrkObjBoxWidth        = COMLIB_calcHexToPhyCanData(pMsg->trkObjWidth02, 0.2, 0);
        trkObjInfo.rdpTrkObjStatus          = pMsg->trkObjDynProp02;
        trkObjInfo.rdpTrkObjReliability     = COMLIB_calcHexToPhyCanData(pMsg->trkObjProbOfExist02, 3.2258, 0);
        trkObjInfo.rdpTrkObjAzimuthAngle    = atan2f(trkObjInfo.rdpTrkObjDistX, trkObjInfo.rdpTrkObjDistY) * 180 / PI;
        trkObjInfo.rdpTrkObjsnr             = COMLIB_calcHexToPhyCanData(pMsg->trkObjSNR02, 0.5f, 0);
        trkObjInfo.rdpTrkObjBoxCenterY      = COMLIB_calcHexToPhyCanData(pMsg->trkObjBoxCenterLong02, 0.025f, -204.8f);
        trkObjInfo.rdpTrkObjBoxCenterX      = COMLIB_calcHexToPhyCanData(pMsg->trkObjBoxCenterLat02, 0.025f, -102.4f);
        trkObjInfo.rdpTrkObjVelocity        = -sqrt(trkObjInfo.rdpTrkObjVrelX *trkObjInfo.rdpTrkObjVrelX + trkObjInfo.rdpTrkObjVrelY * trkObjInfo.rdpTrkObjVrelY);        trkObjInfo.rdpTrackType             = pMsg->trkObjTrackType02;
        trkObjInfo.rdpTrkObjType            = pMsg->trkObjClass02;
        trkObjInfo.rdpTrkObjStatus          = pMsg->trkObjStatus02;
        trkObjInfo.rdpLifeCycleCnt          = pMsg->trkObjTrackLifeCycleCnt02;
        trkObjInfo.rdpTrkObjHitCnt          = pMsg->trkObjHitCnt02;
        trkObjInfo.rdpTrkObjMissCnt         = pMsg->trkObjMissCnt02;

        RDP_setTrkObjectForTrkDbgMode(pMsg->trkObjID02, &trkObjInfo);

        gRxTrkObjCnt++;
    }
    else if (version == 0)
    {
        const COM_trkList24Byte_t *pMsg = (const COM_trkList24Byte_t *)pBuf;
        RDP_TrkObjectInfo_t trkObjInfo = {0};

        trkObjInfo.id = pMsg->trkObjID;
        trkObjInfo.rdpTrkObjDistX           = COMLIB_calcHexToPhyCanData(pMsg->trkObjDistLat, 0.05, -102.4);
        trkObjInfo.rdpTrkObjDistY           = COMLIB_calcHexToPhyCanData(pMsg->trkObjDistLong, 0.05, -409.6);
        trkObjInfo.rdpTrkObjRange           = sqrt(trkObjInfo.rdpTrkObjDistX * trkObjInfo.rdpTrkObjDistX + trkObjInfo.rdpTrkObjDistY * trkObjInfo.rdpTrkObjDistY);
        trkObjInfo.rdpTrkObjVrelX           = COMLIB_calcHexToPhyCanData(pMsg->trkObjVrelLat, 0.04, -163.84);
        trkObjInfo.rdpTrkObjVrelY           = COMLIB_calcHexToPhyCanData(pMsg->trkObjVrelLong, 0.04, -163.84);
        trkObjInfo.rdpTrkObjArelX           = COMLIB_calcHexToPhyCanData(pMsg->trkObjArelLat, 0.01, -10.24);
        trkObjInfo.rdpTrkObjArelY           = COMLIB_calcHexToPhyCanData(pMsg->trkObjArelLong, 0.01, -10.24);
        trkObjInfo.rdpTrkObjLength          = COMLIB_calcHexToPhyCanData(pMsg->trkObjLength, 0.2, 0);
        trkObjInfo.rdpTrkObjWidth           = COMLIB_calcHexToPhyCanData(pMsg->trkObjWidth, 0.2, 0);
        trkObjInfo.rdpTrkObjStatus          = pMsg->trkObjDyncPro;
        trkObjInfo.rdpTrkObjReliability     = COMLIB_calcHexToPhyCanData(pMsg->trkObjProbOfExist, 3.2258, 0);
        trkObjInfo.rdpTrkObjAzimuthAngle    = atan2f(trkObjInfo.rdpTrkObjDistX, trkObjInfo.rdpTrkObjDistY) * 180 / PI;

        RDP_setTrkObjectForTrkDbgMode(pMsg->trkObjID, &trkObjInfo);
        gRxTrkObjCnt++;
    }
}

/**
 *  @b Description
 *  @n
 *      Get the Adas function state message from PCAN.
 *
 *  @param[in]  pBuf
 *      The buffer of CAN RX data.
 *
 */
void HIL_getAdasFunctionStateFromCan(const void *pBuf)
{
    SRR_alarmObjInfo24Byte_t *pMsg = (SRR_alarmObjInfo24Byte_t *)(pBuf);
    
    ghilAdasFunctionState.adasBSDFuncState    = pMsg->AlarmBsdState;
    ghilAdasFunctionState.adasDOWFuncState    = pMsg->AlarmDowState;
    ghilAdasFunctionState.adasFCTAFuncState   = pMsg->AlarmFctaState;
    ghilAdasFunctionState.adasFCTBFuncState   = pMsg->AlarmFctbState;
    ghilAdasFunctionState.adasLCAFuncState    = pMsg->AlarmLcaState;
    ghilAdasFunctionState.adasRCTAFuncState   = pMsg->AlarmRctaState;
    ghilAdasFunctionState.adasRCTBFuncState   = pMsg->AlarmRctbState;
    ghilAdasFunctionState.adasRCWFuncState    = pMsg->AlarmRcwState;
}

/**
 *  @b Description
 *  @n
 *      Get the VehicleInfo message from PCAN.
 *
 *  @param[in]  pBuf
 *      The buffer of CAN RX data.
 *  @param[in]  version
 *      The protocol version.
 *
 */
void HIL_getVehicleInfoFromCan(const void *pBuf, int version)
{
    static uint8_t DriveDirection = 0; /* @brief 行驶方向，0-前进，1-后退 */

    if (DBG_getHILDataDbgMode() == 0)
    {
        return;
    }

    /*两个协议无太大差异，暂使用同一解析*/
    if ((version == 1) || (version == 0))
    {
        const SRR_VehicleInfo_t *pMsg = (const SRR_VehicleInfo_t *)pBuf;

        if (pMsg->Veh_Gear == GEAR_SIG_R)
        {
            DriveDirection = 1;
        }
        else
        {
            DriveDirection = 0;
        }
        Vehicle_setActualGear(pMsg->Veh_Gear);
        Vehicle_setKeyStatus(pMsg->Veh_KeyState);
        Vehicle_setSecurityLock(pMsg->Veh_SecurityLock);
        Vehicle_setSpeed(COMLIB_calcHexToPhyCanData(pMsg->Veh_Speed, 0.05625,  0.0), DriveDirection, 1);
        Vehicle_setYawRate(COMLIB_calcHexToPhyCanData(pMsg->Veh_YawRate, 0.00024, -2.093), 1);
        Vehicle_setLatAccel(COMLIB_calcHexToPhyCanData(pMsg->Veh_LatAccel, 0.00098, -21.592), 0, 1);
        Vehicle_setLonAccel(COMLIB_calcHexToPhyCanData(pMsg->Veh_LgtAccel, 0.00098, -21.592), 0, 1);
        Vehicle_setSteeringAngle(COMLIB_calcHexToPhyCanData(pMsg->Veh_SteerWheelAngle, 0.2, -738), 1);
        Vehicle_setTurnLightStatus(0, pMsg->Veh_TurnLightLe);
        Vehicle_setTurnLightStatus(1, pMsg->Veh_TurnLightRi);
        Vehicle_setDoorStatus(0, pMsg->Veh_DoorFrontLe);
        Vehicle_setDoorStatus(1, pMsg->Veh_DoorFrontRi);
        Vehicle_setDoorStatus(2, pMsg->Veh_DoorRearLe);
        Vehicle_setDoorStatus(3, pMsg->Veh_DoorRearRi);

        Vehicle_setWheelDir(0, pMsg->Veh_WhlSpdDirFrontLe);
        Vehicle_setWheelDir(1, pMsg->Veh_WhlSpdDirFrontRi);
        Vehicle_setWheelDir(2, pMsg->Veh_WhlSpdDirRearLe);
        Vehicle_setWheelDir(3, pMsg->Veh_WhlSpdDirRearRi);
        
        Vehicle_setDoorLockStatus(0, pMsg->Veh_DoorLockFrontLe);
        Vehicle_setDoorLockStatus(1, pMsg->Veh_DoorLockFrontRi);
        Vehicle_setDoorLockStatus(2, pMsg->Veh_DoorLockRearLe);
        Vehicle_setDoorLockStatus(3, pMsg->Veh_DoorLockRearRi);

        Vehicle_setWheelSpd(0, COMLIB_calcHexToPhyCanData(pMsg->Veh_WhlSpdFrontLe, 0.05625, 0));
        Vehicle_setWheelSpd(1, COMLIB_calcHexToPhyCanData(pMsg->Veh_WhlSpdFrontRi, 0.05625, 0));
        Vehicle_setWheelSpd(2, COMLIB_calcHexToPhyCanData(pMsg->Veh_WhlSpdRearLe, 0.05625, 0));
        Vehicle_setWheelSpd(3, COMLIB_calcHexToPhyCanData(pMsg->Veh_WhlSpdRearRi, 0.05625, 0));
        
        VDY_setFuncSwitch(1, pMsg->Veh_SwBSDFunc);
        VDY_setFuncSwitch(2, pMsg->Veh_SwBSDFunc);
        VDY_setFuncSwitch(3, pMsg->Veh_SwDowFunc);
        VDY_setFuncSwitch(5, pMsg->Veh_SwRctaFunc);
        VDY_setFuncSwitch(4, pMsg->Veh_SwRcwFunc);
        VDY_setFuncSwitch(6, pMsg->Veh_SwRctbFunc);
        VDY_setFuncSwitch(7, pMsg->Veh_SwFctaFunc);
        VDY_setFuncSwitch(8, pMsg->Veh_SwFctbFunc);

        Vehicle_setAccelPedalPos(pMsg->Veh_AccPedalActLevel);
        Vehicle_setBrakePedalPos(pMsg->Veh_BrkPedalActLevel);
        Vehicle_setTrailerStatus(pMsg->Veh_TrailerSts);

        // 冻结当前的车身数据
        VDY_freezeVehDynData();
    }
    else
    {
        ///todo
    }
}

/**
 *  @b Description
 *  @n
 *      Process the hil message from PCAN.
 *
 *  @param[in]  Can_ID
  *      The CAN ID.
 *  @param[in]  pData
 *      The buffer of CAN RX data.
 *  @param[in]  Len
 *      The data length.
 */
void HIL_procDetTrkHilCanMsg(uint16_t can_id, const uint8_t *rxData, uint8_t len)
{
    static uint16_t tProVersion_raw = 0;
    static uint16_t tProVersion_trk = 0;
    static uint16_t objNum = 0;
    uint8_t pData[64];

    memcpy(pData, rxData, len);

    // 先恢复前8Bytes的字节序，再统一转换
    COMLIB_convertBigLittleEndian(pData, 8);

    if (can_id == CAN_ID_RAW_OBJ_HEADER)
    {
        COMLIB_convertBigLittleEndian(pData, len);
        //原始数据起始帧
        SRR_RawHeader_t *rawHeaderMsg = (SRR_RawHeader_t *)(pData);
        tProVersion_raw = rawHeaderMsg->RL_RawHeaderProtVer;
        objNum = rawHeaderMsg->RL_RawHeaderObjNum;
        //HIL_setDetTrkFrmTimeAndUpdateFrmIdx((float)rawHeaderMsg->RL_RawHeaderRspTaskCycleTime / 1000.0f);

        HIL_resetOutTargets(objNum);
    }
    else if (can_id == CAN_ID_RAW_OBJ_LIST)
    {
        COMLIB_convertBigLittleEndian(pData, len);
        // 回灌更新检测点数据
        HIL_getRawObjListFromCan(pData, tProVersion_raw);
    }
    else if (can_id == CAN_ID_TRK_OBJ_HEADER)
    {
        COMLIB_convertBigLittleEndian(pData, len);
        //跟踪数据起始帧
        if (eDATA_LEN_8 == len)
        {
            COM_trkObjectHeader_t *trkHeaderMsg = (COM_trkObjectHeader_t *)(pData);
            tProVersion_trk = trkHeaderMsg->trkHeaderProtVer;
            objNum = trkHeaderMsg->trkHeaderObjNum;
        }
        else if (eDATA_LEN_16 == len)
        {
            COM_trkObjListHeaderVer1_t *trkHeaderMsg = (COM_trkObjListHeaderVer1_t *)(pData);
            tProVersion_trk = trkHeaderMsg->trkObjHeaderProtVer;
            objNum = trkHeaderMsg->trkObjHeaderObjectNum;
        }
        HIL_resetTrkObjects(objNum);
    }
    else if (can_id == CAN_ID_TRK_OBJ_LIST)
    {
        COMLIB_convertBigLittleEndian(pData, len);
        // 回灌更新跟踪点数据
        HIL_getTrackObjListFromCan(pData, tProVersion_trk);
    }
    else if (can_id == CAN_ID_FRAME_END)
    {
        COMLIB_convertBigLittleEndian(pData, len);
        //接收数据包完成，开始跟踪
        SRR_EndFrame_t *pEndFrame = (SRR_EndFrame_t *)(pData);
        HIL_setDetTrkFrameCnt(pEndFrame->RL_EndFrameMeasCnt);
        CFG_setRadarInstallAngle(COMLIB_calcHexToPhyCanData(pEndFrame->RL_EndFrameEOLInstallAngle, 0.1, -102.4));
        HIL_setDetTrkFrmTimeAndUpdateFrmIdx((float)((float)pEndFrame->RL_EndFrameInterTime / 1000.0f));
    }
    else if (can_id == CAN_ID_ALARM_OBJ)  ///回灌报警信号
    {
        COMLIB_convertBigLittleEndian(pData, len);
        //回灌解析报警信号状态
        HIL_getAdasFunctionStateFromCan(pData);

    }
    else if (can_id == CAN_ID_VEHICLE_INFO)  // 回灌vehicle信息
    {
        COMLIB_convertBigLittleEndian(pData, len);
        // 从CAN报文中获取vehicle信息，默认版本号 0
        HIL_getVehicleInfoFromCan(pData, 0);
    }
    else
    {
    }
}

