#include "sortlib.h"

//冒泡排序算法 ->升序
void do_BubbleSort(float arr[], int length)
{
	float temp; //临时变量
	for (int i = 0; i < length; i++)
	{ //表示趟数，一共length次。
		for (int j = length - 1; j > i; j--)
		{
			if (arr[j] < arr[j - 1])
				//if ((arr[j] - arr[j - 1]) < 1e-6)
			{
				temp = arr[j];
				arr[j] = arr[j - 1];
				arr[j - 1] = temp;
			}
		}
	}
}

//冒泡排序算法 ->升序
void do_BubbleSort_INT8(int8_t arr[], uint8_t length)
{
	int8_t temp; //临时变量
	for (uint8_t i = 0; i < length; i++)
	{ //表示趟数，一共length次。
		for (uint8_t j = length - 1; j > i; j--)
		{
			if (arr[j] < arr[j - 1])
				//if ((arr[j] - arr[j - 1]) < 1e-6)
			{
				temp = arr[j];
				arr[j] = arr[j - 1];
				arr[j - 1] = temp;
			}
		}
	}
}

//冒泡排序算法 ->升序
void do_BubbleSort_Uint16(uint16_t arr[], int length)
{
	uint16_t temp; //临时变量
	for (int i = 0; i < length; i++)
	{ //表示趟数，一共length次。
		for (int j = length - 1; j > i; j--)
		{
			if (arr[j] < arr[j - 1])
				//if ((arr[j] - arr[j - 1]) < 1e-6)
			{
				temp = arr[j];
				arr[j] = arr[j - 1];
				arr[j - 1] = temp;
			}
		}
	}
}