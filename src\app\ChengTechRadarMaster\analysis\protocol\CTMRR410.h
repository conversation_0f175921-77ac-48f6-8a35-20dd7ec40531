/**
* Copyright (c) 2016-2022 ChengTech All rights reserved.
* @file CTMRR410.h
* @brief brief
* <AUTHOR>
* @date 2022-12-22
* @version 1.0.0
* @note
* description
*/

#ifndef CTMRR410_H
#define CTMRR410_H

#include <stdint.h>
#include <stdbool.h>

/**************************************************************************************
 DEFINE
**************************************************************************************/
#define MSG_ID3f0_SRR_VEHICLEINFO 0x3f0
#define MSG_ID400_SRR_RAWHEADER 0x400
#define MSG_ID410_SRR_RAWDETECTIONS 0x410
#define MSG_ID430_SRR_OBJECTHEADER 0x430
#define MSG_ID440_SRR_OBJECTLIST 0x440
#define MSG_ID4c0_SRR_ALARMOBJINFO 0x4c0
#define MSG_ID4f0_SRR_ENDFRAME 0x4f0


/**************************************************************************************
 STRUCT
**************************************************************************************/
// 0x3f0 length = 32
typedef struct CT_SRR_VehicleInfo {
	float Veh_YawRate;
	uint8_t Veh_SwRctaFunc;
	uint8_t Veh_SwRctbFunc;
	uint8_t Veh_SwRcwFunc;
	uint8_t Veh_SwBSDFunc;
	uint8_t Veh_SwDowFunc;
	uint8_t Veh_SwMainFunc;
	uint8_t Veh_SwFctaFunc;
	uint8_t Veh_SwFctbFunc;
	float Veh_Speed;
	uint8_t Veh_DoorFrontLe;
	uint8_t Veh_DoorFrontRi;
	uint8_t Veh_DoorRearLe;
	uint8_t Veh_DoorRearRi;
	uint8_t Veh_Gear;
	uint8_t Veh_KeyState;
	uint8_t Veh_SecurityLock;
	uint8_t Veh_TurnLightLe;
	uint8_t Veh_TurnLightRi;
	uint8_t Veh_BrkPedalSts;
	uint8_t Veh_WhlSpdDirFrontLe;
	uint8_t Veh_WhlSpdDirFrontRi;
	uint8_t Veh_WhlSpdDirRearLe;
	uint8_t Veh_WhlSpdDirRearRi;
	uint8_t Veh_AccPedalActLevel;
	uint8_t Veh_BrkPedalActLevel;
	uint8_t Veh_TrailerSts;
	uint8_t Veh_ESPFailSts;
	float Veh_LgtAccel;
	float Veh_LatAccel;
	float Veh_WhlSpdFrontLe;
	float Veh_WhlSpdFrontRi;
	float Veh_WhlSpdRearLe;
	float Veh_WhlSpdRearRi;
	float Veh_SteerWheelAngle;
	uint8_t RollingCntMsgVehInfo01;
	float Veh_Radius;
	uint8_t ChecksumMsgVehInfo01;
}CT_SRR_VehicleInfo_t;

// 0x400 length = 16
typedef struct CT_SRR_RawHeader {
	uint8_t RawHeaderProtVer;
	float RawHeaderNoiseCurrent;
	float RawHeaderNoiseGlobal;
	uint16_t RawHeaderObjNum;
	uint16_t RawHeaderFuncCalcTime;
	uint16_t RawHeaderRspTaskCycleTime;
	uint8_t RawHeaderDataSource;
	uint8_t RawHeaderObjNumAfterCFAR;
	uint8_t RawHeaderObjNumAfterFilter;
	uint8_t RawHeaderCurrentFrameModeIdx;
	uint8_t RawHeaderFrameModeNum;
	uint8_t RawHeaderRollingCnt;
}CT_SRR_RawHeader_t;

// 0x410 length = 64
typedef struct CT_SRR_RawDetections {
	float RawObjectRange01;
	float RawObjectAzimuth01;
	float RawObjectVelocity01;
	float RawObjectElevationAngle01;
	uint8_t RawObjectProbOfExist01;
	float RawObjectRCS01;
	float RawObjectSNR01;
	uint16_t RawObjectStatus01;
	uint16_t RawObjectDopplerVelocity01;
	uint8_t RawObjectSubFrameID01;
	uint8_t RawObjectAssociatedTrkId01;
	uint8_t RawObjectGroupID01;
	float RawObjectRange02;
	float RawObjectAzimuth02;
	float RawObjectVelocity02;
	float RawObjectElevationAngle02;
	uint8_t RawObjectProbOfExist02;
	float RawObjectRCS02;
	float RawObjectSNR02;
	uint16_t RawObjectStatus02;
	uint16_t RawObjectDopplerVelocity02;
	uint8_t RawObjectSubFrameID02;
	uint8_t RawObjectAssociatedTrkId02;
	uint8_t RawObjectGroupID02;
	float RawObjectRange03;
	float RawObjectAzimuth03;
	float RawObjectVelocity03;
	float RawObjectElevationAngle03;
	uint8_t RawObjectProbOfExist03;
	float RawObjectRCS03;
	float RawObjectSNR03;
	uint16_t RawObjectStatus03;
	uint16_t RawObjectDopplerVelocity03;
	uint8_t RawObjectSubFrameID03;
	uint8_t RawObjectAssociatedTrkId03;
	uint8_t RawObjectGroupID03;
	float RawObjectRange04;
	float RawObjectAzimuth04;
	float RawObjectVelocity04;
	float RawObjectElevationAngle04;
	uint8_t RawObjectProbOfExist04;
	float RawObjectRCS04;
	float RawObjectSNR04;
	uint16_t RawObjectStatus04;
	uint16_t RawObjectDopplerVelocity04;
	uint8_t RawObjectSubFrameID04;
	uint8_t RawObjectAssociatedTrkId04;
	uint8_t RawObjectGroupID04;
	uint8_t RawObjectRollingCnt01;
}CT_SRR_RawDetections_t;

// 0x430 length = 8
typedef struct CT_SRR_ObjectHeader {
	uint8_t ObjHeaderChecksum;
	uint8_t ObjHeaderObjNum;
	uint16_t ObjHeaderMeasCnt;
	uint16_t TrkHeaderFuncCalcTime;
	uint16_t TrkHeaderTaskCycleTime;
	uint8_t ObjHeaderProtVer;
	uint8_t ObjHeaderRollingCnt;
}CT_SRR_ObjectHeader_t;

// 0x440 length = 24
typedef struct CT_SRR_ObjectList {
	uint8_t ObjectChecksum;
	uint8_t ObjectID;
	float ObjectDistLong;
	float ObjectDistLat;
	float ObjectVrelLong;
	uint8_t ObjectClass;
	float ObjectDistLongRms;
	float ObjectVrelLat;
	float ObjectArelLong;
	float ObjectArelLat;
	float ObjectLength;
	float ObjectWidth;
	uint8_t ObjectMeasState;
	float ObjectRCS;
	uint8_t ObjectDynProp;
	float ObjectHeight;
	float ObjectDistLatRms;
	float ObjectVrelLongRms;
	float ObjectVrelLatRms;
	float ObjectArelLongRms;
	float ObjectArelLatRms;
	float ObjectOrientationAngle;
	float ObjectProbOfExist;
	float ObjectDistAltitude;
	uint8_t ObjectRollingCnt;
}CT_SRR_ObjectList_t;

// 0x4c0 length = 24
typedef struct CT_SRR_AlarmObjInfo {
	uint8_t DrvFunc_AlarmModule;
	uint8_t AlarmBsdObjID;
	uint8_t AlarmLcaObjID;
	float AlarmLcaObjTtc;
	uint8_t AlarmDowObjID;
	float AlarmDowObjTtc;
	uint8_t AlarmRcwObjID;
	float AlarmRcwObjTtc;
	uint8_t AlarmRctaObjID;
	float AlarmRctaObjTtc;
	uint8_t AlarmRctbObjID;
	float AlarmRctbObjTtc;
	uint8_t AlarmFctaObjID;
	float AlarmFctaObjTtc;
	uint8_t AlarmFctbObjID;
	float AlarmFctbObjTtc;
	uint8_t AlarmBsdLevel;
	uint8_t AlarmLcaLevel;
	uint8_t AlarmDowLevelFront;
	uint8_t AlarmDowLevelRear;
	uint8_t AlarmRcwLevel;
	uint8_t AlarmRctaLevel;
	uint8_t AlarmFctaLevel;
	uint8_t AlarmBsdState;
	uint8_t AlarmLCAState;
	uint8_t AlarmDOWState;
	uint8_t AlarmRCTAState;
	uint8_t AlarmRCTBState;
	uint8_t AlarmFCTAState;
	uint8_t AlarmFCTBState;
	uint8_t AlarmRCWState;
}CT_SRR_AlarmObjInfo_t;

// 0x4f0 length = 24
typedef struct CT_SRR_EndFrame {
	uint8_t EndFrameCheckSum;
	float EndFrameEOLInstallAngle;
	float EndFrameAutoCalAngleOffset;
	uint8_t EndFrameInterTime;
	uint16_t EndFrameFuncCalcTime;
	float EndFrameRoadSideDist;
	uint8_t EndFrameRollingCnt;
	uint32_t EndFrameTimeTick;
	uint32_t EndFrameMeasCnt;
}CT_SRR_EndFrame_t;


typedef struct CTMRR410 {
	CT_SRR_VehicleInfo_t _SRR_VehicleInfo;
	CT_SRR_RawHeader_t _SRR_RawHeader;
	CT_SRR_RawDetections_t _SRR_RawDetections;
	CT_SRR_ObjectHeader_t _SRR_ObjectHeader;
	CT_SRR_ObjectList_t _SRR_ObjectList;
	CT_SRR_AlarmObjInfo_t _SRR_AlarmObjInfo;
	CT_SRR_EndFrame_t _SRR_EndFrame;
}CTMRR410_t;

typedef struct CANFrame {
	uint32_t id;
	uint8_t dlc;
	unsigned char *data;
}CANFrame;

typedef CANFrame Frame;



/**************************************************************************************
 FUNCTION
**************************************************************************************/
// 0x3f0
bool decode_SRR_VehicleInfo(CT_SRR_VehicleInfo_t *userData, uint8_t *data, int length);
// 0x400
bool decode_SRR_RawHeader(CT_SRR_RawHeader_t *userData, uint8_t *data, int length);
// 0x410
bool decode_SRR_RawDetections(CT_SRR_RawDetections_t *userData, uint8_t *data, int length);
// 0x430
bool decode_SRR_ObjectHeader(CT_SRR_ObjectHeader_t *userData, uint8_t *data, int length);
// 0x440
bool decode_SRR_ObjectList(CT_SRR_ObjectList_t *userData, uint8_t *data, int length);
// 0x4c0
bool decode_SRR_AlarmObjInfo(CT_SRR_AlarmObjInfo_t *userData, uint8_t *data, int length);
// 0x4f0
bool decode_SRR_EndFrame(CT_SRR_EndFrame_t *userData, uint8_t *data, int length);

// 0x3f0
bool encode_SRR_VehicleInfo(CT_SRR_VehicleInfo_t *userData, uint8_t *data, int length);
// 0x400
bool encode_SRR_RawHeader(CT_SRR_RawHeader_t *userData, uint8_t *data, int length);
// 0x410
bool encode_SRR_RawDetections(CT_SRR_RawDetections_t *userData, uint8_t *data, int length);
// 0x430
bool encode_SRR_ObjectHeader(CT_SRR_ObjectHeader_t *userData, uint8_t *data, int length);
// 0x440
bool encode_SRR_ObjectList(CT_SRR_ObjectList_t *userData, uint8_t *data, int length);
// 0x4c0
bool encode_SRR_AlarmObjInfo(CT_SRR_AlarmObjInfo_t *userData, uint8_t *data, int length);
// 0x4f0
bool encode_SRR_EndFrame(CT_SRR_EndFrame_t *userData, uint8_t *data, int length);

bool proc_CTMRR410(CTMRR410_t *userData, Frame *p);

#endif // CTMRR410_H