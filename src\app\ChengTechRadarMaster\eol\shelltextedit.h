#ifndef SHELLTEXTEDIT_H
#define SHELLTEXTEDIT_H

#include <QObject>
#include <QTextEdit>

class ShellTextEdit : public QTextEdit
{
    Q_OBJECT
public:
    ShellTextEdit(QWidget *parent = nullptr);
    void GetHistoryCmd( QStringList& cmds );
    void GetQuickCmd( QStringList& cmds );

signals:
    void shellCmd( const QString& cmd );
    void historyCmdChanged();
    void quickCmdChanged();

public slots:
    void insertCmd( const QString& cmd );
    void showText( const QString& text );
    void clearHistoryCmd();
    void setQuickCmd( const QStringList& cmds );

private:
    virtual void showEvent(QShowEvent *event) override;
    bool    event(QEvent *event);

private:
    bool isInputKey(QKeyEvent *keyEvent);
    QString getCurrentStr( bool bDelete = true );
    bool clearText();

    void backHistoryCmd();
    void nextHistoryCmd();
    void addHistoryCmd( const QString& cmd );

    void saveQuickCmd();
    void loadQuickCmd();

    void completionCmd();//补全命令


private:
    int mMinCursorPos{0};
    QString mClearStr;
    QStringList mQuickCmd;
    QStringList mHistoryCmd;
    int mHistoryIndex{0};

    const QString logo = (
      "   ______  __                          _______               __     \r\n"
      "  |      ||  |--..-----..-----..-----.|_     _|.-----..----.|  |--. \r\n"
      "  |   ---||     ||  -__||     ||  _  |  |   |  |  -__||  __||     | \r\n"
      "  |______||__|__||_____||__|__||___  |  |___|  |_____||____||__|__| \r\n"
      "                               |_____|\r\n"
      "Build:       " __DATE__ " " __TIME__ "\r\n"
      "Version:     v0.0.1\r\n"
      "Copyright:   (c) 2023 Shenzhen Cheng-Tech Co.,Ltd.\r\n");

};

#endif // SHELLTEXTEDIT_H
