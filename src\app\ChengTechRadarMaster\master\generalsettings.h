﻿#ifndef GENERALSETTINGS_H
#define GENERALSETTINGS_H

#include <QWidget>

#include "analysis/analysisdata.h"

namespace Ui {
class GeneralSettings;
}

class GeneralSettings : public QWidget
{
    Q_OBJECT

public:
    typedef struct Settings {
        int mFileSaveFrameCount{48000};
        bool mHozonBreakShort{false};       ///< 哪吒短协议
        EarlyWarningSettings mEarlyWarningSettings;
        AngleCompensation mAngleCompensation[MAX_RADAR_COUNT];
        bool mBYDRaw600ByChannel{false};
        int mBYDHDChannelRadarID[8]{4, 5, 6, 7, 0, 0, 0, 0};                ///< BYD高阶通道-雷达ID设置
        int mGEELYChannelRadarID[2]{0, 1};                ///< GEELY通道-雷达ID设置

        QVariant getSettings() const;
        bool setSettings(const QVariant &settings);
    }Settings;

    explicit GeneralSettings(Settings *settings, QWidget *parent = nullptr);
    ~GeneralSettings();

signals:
    void apply();

private slots:
    void on_pushButtonApply_clicked();

    void on_pushButtonOK_clicked();

    void on_pushButtonAlarmScript_clicked();

private:
    Ui::GeneralSettings *ui;

    Settings *mSettings{0};
};

#endif // GENERALSETTINGS_H
