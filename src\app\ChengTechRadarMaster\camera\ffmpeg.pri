win32: {
contains(QT_ARCH, i386) {
    FFMPEG_HOME=D:/Src/vcpkg-master/installed/x86-windows
} else {
    FFMPEG_HOME=D:/Src/vcpkg-master/installed/x64-windows
}
}

CONFIG(debug, debug|release): FFMPEG_HOME = $$FFMPEG_HOME/debug

INCLUDEPATH += $$FFMPEG_HOME/include
DEPENDPATH += $$FFMPEG_HOME/include

LIBS += -L$$FFMPEG_HOME/lib \
    -lavcodec \
    -lavdevice \
    -lavfilter \
    -lavformat \
    -lavutil \
#    -lpostproc \
    -lswresample \
    -lswscale
