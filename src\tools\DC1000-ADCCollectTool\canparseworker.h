﻿#ifndef CANPARSEWORKER_H
#define CANPARSEWORKER_H

#include <QObject>

#include "CANFrame.h"
#include "SafeQueue.h"
#include "analysisdata.h"
#include "analysisprotocolct410.h"

#include "cansaveworker.h"

class CANParseWorker : public QObject
{
    Q_OBJECT
public:
    explicit CANParseWorker(CANSaveWorker *canSaveWorker, QObject *parent = nullptr);

    bool start();
    bool stop();

    SafeQueue<CANFrame> * frameQueue() { return &mSafeQueue; }
    bool isParsing() const { return mParsing; }
    void parse();
    void analysisEnd(quint8 radarReceived, bool assigned);

signals:
    void radarAlready(unsigned int status);
    void radarCollectStoped(unsigned int count);
    void targets(AnalysisData data);
    void radarReceived(int radarID);

public slots:
    void cameraReady(unsigned long long saveIndex);

public:
    AnalysisData mAnalysisDatas;

private:
    void parse(CANFrame &frame);

    void parse0x3F9(CANFrame &frame);

    SafeQueue<CANFrame> mSafeQueue;
    CANSaveWorker *mCANSaveWorker{0};
    AnalysisProtocolCT410 *mAnalysisProtocolCT410{0};

    bool mParsing{false};

};

#endif // CANPARSEWORKER_H
