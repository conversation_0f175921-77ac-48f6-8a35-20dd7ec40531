﻿#ifndef ALARMCALCULATE_H
#define ALARMCALCULATE_H

#include <QObject>

#include "analysisdata.h"
#include "script/iearlywarningscript.h"

class IEarlyWarningScript;

namespace Analysis {

class AlarmCalculate : public QObject
{
    Q_OBJECT
public:
    enum ScriptType {
        ScriptPython,
        ScriptLua,
        ScriptUnknown
    };

    explicit AlarmCalculate(QObject *parent = nullptr);
    ~AlarmCalculate();

    void setEarlyWarningSettings(const EarlyWarningSettings &settings);
    const EarlyWarningSettings &getEarlyWarningSettings() const { return mEarlyWarningSettings; }

    void earlyWarning(AnalysisData *analysisData);
    quint16 vehicleEarlyWarning(int radarID, quint16 alarmTypes, VehicleData *vehicleData);
    bool calculationEarlyWarning(int radarID, Target *target, quint16 alarmTypes, quint16 vehicleAlarmTypes, VehicleData *vehicleData, QList<EarlyWarningData> &earlyWarningDatas);

signals:

private:
    int alarmedID(const EarlyWarningData &warning, Target *targets, int newIndex, int &oldIndex, double &oldLevel, double &oldTTC);

    void calculationDOWEarlyWarning(Target *target);
    void calculationBSDEarlyWarning(Target *target);
    void calculationLCAEarlyWarning(Target *target);
    void calculationAlarmAndEarlyWarning(int radarID, Targets targets, AnalysisData *analysisData);


    quint16 vehicleEarlyWarningCPlusPlus(int radarID, quint16 alarmTypes, VehicleData *vehicleData);

    /** @brief C++计算告警 */
    bool earlyWaringCPlusPlus(Target *target, quint16 alarmTypes, quint16 vehicleAlarmTypes, VehicleData *vehicleData, QList<EarlyWarningData> &earlyWarningDatas);

    /** @brief 加载脚本文件 */
    bool loadScriptFile();
    /** @brief 卸载脚本文件 */
    bool unloadScriptFile();

    EarlyWarningSettings mEarlyWarningSettings;

    ScriptType mScriptType{ScriptUnknown};
    IEarlyWarningScript *mEarlyWaringScript{0};

    friend class CalculationWorker;
};

} // namespace Analysis

#endif // ALARMCALCULATE_H
