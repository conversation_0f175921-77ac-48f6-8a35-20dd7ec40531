#ifndef COMMON_DEF_H
#define COMMON_DEF_H

/****************************************************/
/* 雷达类型定义                                      */
/****************************************************/
#define LRR_RADAR         0
#define MRR_RADAR         1

/****************************************************/
/* output模式                                       */
/****************************************************/
#define RAW_OUTPUT_MODE_NORMAL			0x0u   //正常输出，会速度解模糊
#define RAW_OUTPUT_MODE_1				0x1u   //分profile输出，每次固定29个
#define RAW_OUTPUT_MODE_2				0x2u   //分profile输出，每个profile混在一次输出
#define RAW_OUTPUT_MODE_3				0x3u   //分profile输出，每个profile中加分割线，数目不定

/****************************************************/
/* Can总线协议类型                                    */
/****************************************************/
// #define CANBUS_PROTOCOL_VER_DEFAULT        0
// #define CANBUS_PROTOCOL_VER_Project_410_QZ 1 //清智LRR 410项目协议版本 //为升科协议
// #define CANBUS_PROTOCOL_VER_HW_MDC         2 //华为MDC
// #define CANBUS_PROTOCOL_VER_CONTI          3 //大陆协议
// #define CANBUS_PROTOCOL_VER_CONTI410       4 //大陆410协议
// #define CANBUS_PROTOCOL_VER_BoschMrr       5 //博世协议
// #define CANBUS_PROTOCOL_VER_BYDGAOJIEMrr   6 //比亚迪高阶协议

//MPC或域控的算法团队
#define ALGORITHM_TEAM_VER_DEFAULT         1 //默认J2 J3 J3行泊一体
#define ALGORITHM_TEAM_VER_HONGJING        2 //宏景智驾
#define ALGORITHM_TEAM_VER_JIANZHI         3 //鉴智高阶(J6E)
#define ALGORITHM_TEAM_VER_ORIN_N          4 //Orin-N
#define ALGORITHM_TEAM_VER_BYD_GAOJIE      5 //比亚迪高阶
#define ALGORITHM_TEAM_VER_BYD_Gao_PCAN    6 //比亚迪高阶只用PCAN,不用VCAN

//私有协议版本
#define	PROTOCOL_VER_0  0  //距离精度 0.2   速度精度 0.2   角度精度 0.25
#define PROTOCOL_VER_1  1  //距离精度 0.1   速度精度 0.1   角度精度 0.25
#define PROTOCOL_VER_2  2  //距离精度 0.05  速度精度 0.1   角度精度 0.25
#define PROTOCOL_VER_3  3  //距离精度 0.2   速度精度 0.2   range存储的是y值，angle存储的是x值
#define PROTOCOL_VER_4  4  //距离精度 0.2   速度精度 0.2，  笛卡尔坐标
#define	PROTOCOL_VER_5  5  //距离精度 0.05  速度精度 0.1    角度精度
#define	PROTOCOL_VER_6  6  //距离精度 0.05  速度精度 0.1    角度精度, 64byte canfd报文
#define	PROTOCOL_VER_7  7  //距离精度 0.05  速度精度 0.1    角度精度, 64byte canfd报文
#define	PROTOCOL_VER_8  8  //距离精度 0.05  速度精度 0.1    角度精度, 64byte canfd报文
#define PROTOCOL_VER_MAX (PROTOCOL_VER_8 + 1)

/****************************************************/
/* 硬件ID定义                                       */
/****************************************************/
#if ( ( 1 == RADAR_ONLY_USE_PCAN ) )

/* MPS PMIC 220Pro*/
#define BOARDID_V3191     0x01   //板子硬件ID v3.1.9.1 220Pr0 单CAN+美信MAX20430方案
#define BOARDID_V3090     0x00   //板子硬件ID v3.0.9.0 220Pr0 单CAN+MPS70331方案

/* MPS PMIC 230Pro*/
#define BOARDID_V3797     0x07   //板子硬件ID v3.7.9.7 230Pr0 单CAN+MPS70331方案

#else

/* MPS PMIC 220Pro*/
#define BOARDID_V3565     0x05   //板子硬件ID v3.5.6.5
#define BOARDID_V3666     0x06   //板子硬件ID v3.6.6.6 220pro降本方案

/* MPS PMIC 230Pro*/
#define BOARDID_V3464     0x04   //板子硬件ID v3.4.6.4
#define BOARDID_V3262     0x02   //板子硬件ID v3.2.6.2 230pro降本方案

#endif

/* MRR HW board id, the same as the second byte of HW_VERSION */
//#define HW_BOARD_ID_V2710   0x07   // 板子硬件ID v2.7.1.0，CTMRR130_PRO新平台第一版，MPS PMIC(MPQ70331)方案
#define HW_BOARD_ID_V2631   0x06   // 板子硬件ID v2.6.3.1，CTMRR130_PRO新平台第二版，MPS PMIC(MPQ70331)方案
#define HW_BOARD_ID_V2032   0x00   // 板子硬件ID v2.0.3.2，CTMRR150_PRO新平台第一版，MPS PMIC(MPQ70331)方案
#define HW_BOARD_ID_V2181   0x01   // 板子硬件ID v2.1.8.1，吉利CTMRR180 PRO，PMIC MAX20430方案, 带灯方案
#define HW_BOARD_ID_V2282   0x02   // 板子硬件ID v2.2.8.2，吉利CTMRR180 PRO，PMIC MAX20430方案
#define HW_BOARD_ID_V2333   0x03   // 板子硬件ID v2.3.3.3，130 PRO，高阶-PMIC MAX20430方案
#define HW_BOARD_ID_V2731   0x07   // 板子硬件ID v2.7.3.1，130 PRO，高阶-PMIC MPQ 70331方案


/****************************************************/
/* 硬件ID字段定义                                    */
/****************************************************/
#define LRR_HW_P1           3
#define MRR_HW_P1           2


#endif
