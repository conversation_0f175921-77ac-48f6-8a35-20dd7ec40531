﻿#include "targetmonitor.h"
#include "ui_targetmonitor.h"
#include "ui_targetsmonitor.h"


#include "utils/flowlayout.h"

#include <QDebug>

namespace Views {
namespace AnalysisView {

TargetMonitor::TargetMonitor(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::TargetMonitor)
{
    setupUi();
}

TargetMonitor::TargetMonitor(AnalysisType monitorType, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::TargetMonitor),
    mMonitorType(monitorType)
{
    setupUi();
}

TargetMonitor::~TargetMonitor()
{
    delete ui;
}

void TargetMonitor::clear()
{
    mGraph->data().data()->clear();
    mGraphLowerLimitY->data().data()->clear();
    mGraphSupperLimitY->data().data()->clear();

    mTargetCount = 0;
    ui->customPlot->xAxis->setRange(0, mViewTargetCount);
}

void TargetMonitor::addData(double value)
{
//    qDebug() << __FUNCTION__ << __LINE__ << mTargetCount << value;
    if (mGraph)
    {
        if (mLimitY)
        {
            mGraphLowerLimitY->addData(mTargetCount, mLowerLimitY);
            mGraphSupperLimitY->addData(mTargetCount, mSupperLimitY);
        }

        mGraph->addData(mTargetCount++, value);
        if (mTargetCount > mViewTargetCount)
        {
            ui->customPlot->xAxis->setRange(mTargetCount - mViewTargetCount, mTargetCount);
        }
    }
    else
    {
        qDebug() << __FUNCTION__ << __LINE__ << mTargetCount << value;
    }
    ui->customPlot->replot();
}

void TargetMonitor::on_comboBoxAnalysisType_currentIndexChanged(int index)
{
//    qDebug() << __FUNCTION__ << __LINE__ << mMonitorType;
    mMonitorType = (AnalysisType)ui->comboBoxAnalysisType->currentData().toInt();

    monitorTypeChanged();
}

void TargetMonitor::on_checkBoxLimitY_stateChanged(int arg1)
{
    mLimitY = arg1 == Qt::Checked;
    mGraphLowerLimitY->data().data()->clear();
    mGraphSupperLimitY->data().data()->clear();
}

void TargetMonitor::on_lineEditMinY_textEdited(const QString &arg1)
{
    ui->customPlot->yAxis->setRange(ui->lineEditMinY->text().toDouble(), ui->lineEditMaxY->text().toDouble());
}

void TargetMonitor::on_lineEditMaxY_textEdited(const QString &arg1)
{
    ui->customPlot->yAxis->setRange(ui->lineEditMinY->text().toDouble(), ui->lineEditMaxY->text().toDouble());
}

void TargetMonitor::on_lineEditLowerLimitY_textEdited(const QString &arg1)
{
    mLowerLimitY = arg1.toDouble();
}

void TargetMonitor::on_lineEditUpperLimitY_textEdited(const QString &arg1)
{
    mSupperLimitY = arg1.toDouble();
}

void TargetMonitor::monitorTypeChanged()
{
//    qDebug() << __FUNCTION__ << __LINE__ << mMonitorType;
    switch (mMonitorType)
    {
    case X:
        ui->customPlot->yAxis->setLabel(QStringLiteral("横向距离[m]"));
        mLowerLimitY = -5;
        mSupperLimitY = 5;
        break;
    case Y:
        ui->customPlot->yAxis->setLabel(QStringLiteral("纵向距离[m]"));
        mLowerLimitY = 0;
        mSupperLimitY = 120;
        break;
    case Vx:
        //ui->customPlot->yAxis->setLabel(QStringLiteral("纵向速度[m/s]"));
        ui->customPlot->yAxis->setLabel(QStringLiteral("横向速度[m/s]"));
        mLowerLimitY = -5;
        mSupperLimitY = 5;
        break;
    case Vy:
        //ui->customPlot->yAxis->setLabel(QStringLiteral("横向速度[m/s]"));
        ui->customPlot->yAxis->setLabel(QStringLiteral("纵向速度[m/s]"));
        mLowerLimitY = -10;
        mSupperLimitY = 10;
        break;
    default:
        ui->customPlot->yAxis->setLabel(QString("%1[%2]").arg(gAnalysisTypeName(mMonitorType)).arg(gAnalysisTypeUnit(mMonitorType)));
        mLowerLimitY = -10;
        mSupperLimitY = 10;
        break;
    }

    ui->lineEditMinY->setText(QString::number(mLowerLimitY));
    ui->lineEditMaxY->setText(QString::number(mSupperLimitY));
    ui->lineEditLowerLimitY->setText(QString::number(mLowerLimitY));
    ui->lineEditUpperLimitY->setText(QString::number(mSupperLimitY));

    ui->customPlot->xAxis->setLabel(QStringLiteral("帧计数[*0.050s]"));
    ui->customPlot->xAxis->setRange(0, mViewTargetCount);
    ui->customPlot->yAxis->setRange(ui->lineEditMinY->text().toDouble(), ui->lineEditMaxY->text().toDouble());
}

void TargetMonitor::setupUi()
{
    ui->setupUi(this);

    AnalysisType analysisType = mMonitorType;

    for (int i = TARGET_TYPE_BEGIN; i < TARGET_TYPE_END; ++i)
    {
        ui->comboBoxAnalysisType->addItem(gAnalysisTypeName(AnalysisType(i)), AnalysisType(i));
    }

    ui->comboBoxAnalysisType->setCurrentIndex(analysisType);

    mGraph = ui->customPlot->addGraph();
    mGraphLowerLimitY = ui->customPlot->addGraph();
    mGraphLowerLimitY->setPen(QPen(Qt::red));
    mGraphSupperLimitY = ui->customPlot->addGraph();
    mGraphSupperLimitY->setPen(QPen(Qt::red));
}


TargetsMonitor::TargetsMonitor(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::TargetsMonitor)
{
    ui->setupUi(this);

    connect(ui->spinBoxMonitorTargetID, QOverload<int>::of(&QSpinBox::valueChanged),
          [=](int i){
        qDebug() << __FUNCTION__ << __LINE__ << mFrameType << i;
        monitorTargetIDChanged(mFrameType, i);
        on_pushButtonClear_clicked();
    });

    TargetMonitor *monitor = new TargetMonitor(X, this);
    mTargetsMonitor << monitor;
    ui->gridLayout->addWidget(monitor, 0, 0, 1, 1);
    monitor = new TargetMonitor(Y, this);
    mTargetsMonitor << monitor;
    ui->gridLayout->addWidget(monitor, 0, 1, 1, 1);
    monitor = new TargetMonitor(Vx, this);
    mTargetsMonitor << monitor;
    ui->gridLayout->addWidget(monitor, 1, 0, 1, 1);
    monitor = new TargetMonitor(Vy, this);
    mTargetsMonitor << monitor;
    ui->gridLayout->addWidget(monitor, 1, 1, 1, 1);

    ui->comboBoxFrameType->setCurrentIndex(1);
}

TargetsMonitor::~TargetsMonitor()
{

}

void TargetsMonitor::monitorTarget(/*AnalysisFrameType*/int frameType, const Target &target)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frameType << mFrameType << target.mID << target.mX << target.mY << target.mVx << target.mVy;
    if (frameType != mFrameType)
    {
        return;
    }

    for (int i = 0; i < mTargetsMonitor.size(); ++i)
    {
        mTargetsMonitor[i]->addData(target.value(mTargetsMonitor[i]->monitorType()));
    }
}

void TargetsMonitor::hideEvent(QHideEvent *event)
{
    if (QHideEvent::Hide == event->type())
    {
        on_pushButtonClear_clicked();
    }

    QWidget::hideEvent(event);
}

void TargetsMonitor::on_lineEditViewTargetCount_textEdited(const QString &arg1)
{
    int count = arg1.toInt();
    if (count <= 0)
    {
        count = 500;
        ui->lineEditViewTargetCount->setText("500");
    }

    for (int i = 0; i < mTargetsMonitor.size(); ++i)
    {
        mTargetsMonitor[i]->setViewTargetCount(count);
    }
}

void TargetsMonitor::on_pushButtonClear_clicked()
{
    for (int i = 0; i < 4; ++i)
    {
        mTargetsMonitor[i]->clear();
    }
}

void TargetsMonitor::on_comboBoxFrameType_currentIndexChanged(int index)
{
    switch (index) {
    case 0:
    case 1:
        mFrameType = (AnalysisFrameType)index;
        break;
    case 2:
        mFrameType = Frame16Track;
        break;
    }
    on_pushButtonClear_clicked();
}

void TargetsMonitor::on_pushButtonAddMonitor_clicked()
{
    TargetMonitor *monitor = new TargetMonitor(X, this);
    mTargetsMonitor << monitor;
    ui->gridLayout->addWidget(monitor, (mTargetsMonitor.size() - 1) / 2, (mTargetsMonitor.size() - 1) % 2, 1, 1);
}

} // namespace AnalysisDataView
} // namespace Views
