﻿#ifndef ANALYSISPROTOCOLCT_H
#define ANALYSISPROTOCOLCT_H

#include "ianalysisprotocol.h"

namespace Analysis {
namespace Protocol {

class AnalysisProtocolCT : public IAnalysisProtocol
{
    Q_OBJECT
public:
    explicit AnalysisProtocolCT(AnalysisWorker *analysisWorker, QObject *parent = nullptr);

    bool analysisFrame(const Devices::Can::CanFrame &frame) override;

signals:

};

} // namespace Protocol
} // namespace Analysis

#endif // ANALYSISPROTOCOLCT_H
