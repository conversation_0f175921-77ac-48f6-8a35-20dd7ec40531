﻿#ifndef FILEBATCHPARSINGFORM_H
#define FILEBATCHPARSINGFORM_H

#include <QWidget>

class FileBatchParsingWorker;

namespace Ui {
class FileBatchParsingForm;
}

class FileBatchParsingForm : public QWidget
{
    Q_OBJECT

public:
    explicit FileBatchParsingForm(QWidget *parent = nullptr);
    ~FileBatchParsingForm();

private slots:
    void message(const QString &msg);

    void stoped();

    void on_pushButtonDataPath_clicked();

    void on_pushButtonSavePath_clicked();

    void on_pushButtonGenerate_clicked();

private:
    Ui::FileBatchParsingForm *ui;

    FileBatchParsingWorker *mFileBatchParsingWorker{0};
};

#endif // FILEBATCHPARSINGFORM_H
