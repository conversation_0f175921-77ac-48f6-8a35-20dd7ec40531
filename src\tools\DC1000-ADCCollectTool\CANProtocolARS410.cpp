﻿#include "CANProtocolARS410.h"

#define _USE_MATH_DEFINES // 使用math.h中的M_PI宏定义需要
#include <math.h>
#include <iostream>

CANProtocolARS410::CANProtocolARS410(SaveData *saveData, QObject *parent)
    : QObject(parent), mSaveData(saveData)
{

}

CANProtocolARS410::~CANProtocolARS410()
{
}

bool CANProtocolARS410::analysisFrame(const CANFrame &frame)
{
    bool ret = true;
                uint64_t id = frame.id();
    if (id >= 0x50 && id <= 0x77) { // Part 1
        mCANFrames.push_back(frame);
    }
    else if (id >= 0x20 && id <= 0x47) { // Part 2
        mCANFrames.push_back(frame);
    }
    else if (id == 0x80) {
        parseStatus(frame);
    }
    else {
        return false;
    }

    return ret;
}

bool CANProtocolARS410::parseStatus(const Devices::Can::CanFrame &frame)
{
                if (frame.length() != 8) {
        mCANFrames.clear();
        return false;
    }

    if (mCANFrames.size() != 80) {
        mCANFrames.clear();
        return false;
    }

    Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
    mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].resetTargets();
    for (int i = 0; i < 40; ++i) {
        const uint8_t *data1 = (const uint8_t *)mCANFrames[i + 40].data().data();

        uint8_t validFlag = ((data1[2] & 0x80U) >> 7);
        if (!validFlag) {
            continue;
        }
        Parser::ParsedDataTypedef::TargetF &target = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack]
                .mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber];
        mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber++;

        target.mValid = true;
        target.mID = (data1[0]);
        target.mUpdateFlag = ((data1[1] & 0x80U) >> 7);
        target.mStDevX = ((data1[1] & 0x7FU) * 0.1);
        target.mValidFlag = ((data1[2] & 0x80U) >> 7);
        target.mStDevY = ((data1[2] & 0x7FU) * 0.1);
        target.mMotionPattern = ((data1[3] & 0xE0U) >> 5);
        target.mObstacleProbability = ((data1[3] & 0x1FU) * 3.2258);
        target.mAx = ((((data1[4] & 0xFEU) >> 1) * 0.15) - 9.6);
        target.mStDevVy = ((((data1[5] & 0xFCU) >> 2) + (((uint16_t)data1[4] & 0x1U) << 6)) * 0.05);
        target.mTrackReliablility = ((((data1[6] & 0xF0U) >> 4) + (((uint16_t)data1[5] & 0x3U) << 4)) * 1.5873);
        target.mAliveCounter = (data1[6] & 0xFU);
        target.mChecksum = (data1[7]);

        const uint8_t *data2 = (const uint8_t *)mCANFrames[i].data().data();
        target.mVy = (((((data2[1] & 0xE0U) >> 5) + (((uint16_t)data2[0] & 0xFFU) << 3)) * 0.1) - 102.4);
        target.mX = -((((data2[2] & 0xFFU) + (((uint16_t)data2[1] & 0x1FU) << 8)) * 0.015625) - 64);
        target.mY = ((((data2[4] & 0xFCU) >> 2) + (((uint16_t)data2[3] & 0xFFU) << 6)) * 0.015625);
        target.mObjectType = (data2[4] & 0x3U);
        target.mVx = (((((data2[6] & 0xE0U) >> 5) + (((uint16_t)data2[5] & 0xFFU) << 3)) * 0.1) - 102.4);
        target.mMeasurementFlag = ((data2[6] & 0x10U) >> 4);
        target.mAliveCounter = (data2[6] & 0xFU);
        target.mChecksum = (data2[7]);

        target.mRange = std::sqrt(target.mX * target.mX + target.mY * target.mY);
        target.mAngle = std::asinf(-target.mX / target.mRange) / M_PI * 180;
        target.mV = target.mVy * cosf(target.mAngle / 180 * M_PI);

//                std::cout << i << " " << mParsedData->mTargets[Parser::ParsedDataTypedef::TrackTarget].mEffectiveNumber << "---: " << target.mID << " " << target.mObjectType << " " << target.mMotionPattern << std::endl;
    }

    mCANFrames.clear();

    const uint8_t *data = (const uint8_t *)frame.data().data();
    Parser::ParsedDataTypedef::VehicleInfomation &vehicleInfo = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mVehicleInfomation;
    Parser::ParsedDataTypedef::RadarInfomation &radarInfo = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mRadarInfomation;
//            userData->FRS_Latency = (((data[0] & 0xFCU) >> 2) * 2);
//            quint32 FRS_TimeStamp = (((data[2] & 0xFCU) >> 2) + (((uint32_t)data[1]) << 6) + (((uint32_t)data[0] & 0x3U) << 14));
    vehicleInfo.mV = (((((data[4] & 0xC0U) >> 6) + (((uint32_t)data[3]) << 2) + (((uint32_t)data[2] & 0x3U) << 10)) * 0.025) - 20);
//            frame->CarYawRate = (((((data[5] & 0xF8U) >> 3) + (((uint16_t)data[4] & 0x3FU) << 5)) * 0.1) - 102.4);
//            float FRS_MeasEnabled = ((data[5] & 0x4U) >> 2);

    Parser::ParsedDataTypedef::VehicleInfomation &vehicleInfoRaw = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mVehicleInfomation;
//            std::cout << __FUNCTION__ << " " << __LINE__ << " " << vehicleInfo.mV << " " << vehicleInfoRaw.mV << std::endl;
    vehicleInfoRaw.mV = vehicleInfo.mV;

    mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mProtocolType = Parser::ParsedDataTypedef::ARS410;

    analysisEnd(Parser::ParsedDataTypedef::TargetTrack);

    return true;
}
