<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DAQControl</class>
 <widget class="QWidget" name="DAQControl">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>864</width>
    <height>282</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>3</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>862</width>
        <height>280</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>1</number>
       </property>
       <property name="leftMargin">
        <number>3</number>
       </property>
       <property name="topMargin">
        <number>3</number>
       </property>
       <property name="rightMargin">
        <number>3</number>
       </property>
       <property name="bottomMargin">
        <number>3</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <widget class="QPushButton" name="pushButtonStartAndStop">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="minimumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="debugsystem.qrc">
             <normaloff>:/playback/images/play.png</normaloff>
             <normalon>:/playback/images/playstop.png</normalon>:/playback/images/play.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>25</width>
             <height>25</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonPrevPrev">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="minimumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="debugsystem.qrc">
             <normaloff>:/playback/images/prevprev.png</normaloff>:/playback/images/prevprev.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>25</width>
             <height>25</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonPrevFrame">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="minimumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="debugsystem.qrc">
             <normaloff>:/playback/images/prev.png</normaloff>:/playback/images/prev.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>25</width>
             <height>25</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSlider" name="playProgressBar">
           <property name="pageStep">
            <number>1</number>
           </property>
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonNextFrame">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="minimumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="debugsystem.qrc">
             <normaloff>:/playback/images/next.png</normaloff>:/playback/images/next.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>25</width>
             <height>25</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonNextNext">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="minimumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="debugsystem.qrc">
             <normaloff>:/playback/images/nextnext.png</normaloff>:/playback/images/nextnext.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>25</width>
             <height>25</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelFrame">
           <property name="text">
            <string>00:00:00.000 0/0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonScreenShot">
           <property name="minimumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="debugsystem.qrc">
             <normaloff>:/playback/images/screenshot.png</normaloff>:/playback/images/screenshot.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>25</width>
             <height>25</height>
            </size>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>项目/文件：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditProjectFile">
           <property name="text">
            <string>E:/Projects/CTHostComputerSorftware/CTHostComputerSorftware/doc/HIL/100Frames/100frames_sampling_20221024.dat</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonProjectFile">
           <property name="text">
            <string>...</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>模式:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxModel">
           <item>
            <property name="text">
             <string>回放</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>回灌</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>采集</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxCollectMode">
           <item>
            <property name="text">
             <string>原始点</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>跟踪点</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>ADC</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1DFFT</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>2DFFT</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxCanIndex">
           <item>
            <property name="text">
             <string>CAN 0</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>CAN 1</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxAlgorithm">
           <property name="text">
            <string>算法ID:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxAlgorithmRadarID">
           <item>
            <property name="text">
             <string>4</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>5</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>6</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>7</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonInit">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="text">
            <string>初始化</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonReboot">
           <property name="text">
            <string>雷达Reboot</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonIntoDebugMode">
           <property name="text">
            <string>雷达进入回灌模式</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonExitDebugMode">
           <property name="text">
            <string>雷达退出回灌模式</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_11">
           <property name="text">
            <string>当前帧：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelCurrentFrame">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>0/0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxSingleStep">
           <property name="text">
            <string>单帧</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_10">
           <property name="text">
            <string>起始帧:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="spinBoxStartFrameIndex">
           <property name="minimum">
            <number>1</number>
           </property>
           <property name="maximum">
            <number>999999</number>
           </property>
           <property name="value">
            <number>1</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_9">
           <property name="text">
            <string>一帧Profile数量：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="spinBoxOneFrameProfileCount">
           <property name="minimum">
            <number>1</number>
           </property>
           <property name="value">
            <number>1</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_13">
           <property name="text">
            <string>采集帧数：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditCollectFrameCount">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>100</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonCollectStart">
           <property name="text">
            <string>开始采集</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonCollectStop">
           <property name="text">
            <string>停止采集</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>协议:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxTransportProtocol">
           <item>
            <property name="text">
             <string>TCP</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>UDP</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxAnalyIP">
           <property name="text">
            <string>任意IP</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditIP">
           <property name="maximumSize">
            <size>
             <width>300</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>*************</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>端口</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditPort">
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>5001</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonStartStopServer">
           <property name="text">
            <string>启动服务</string>
           </property>
           <property name="checkable">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>远程主机IP：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelIP">
           <property name="text">
            <string>0.0.0.0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_8">
           <property name="text">
            <string>端口：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelPort">
           <property name="text">
            <string>0000</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>DCK:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelDCKVersion">
           <property name="text">
            <string>0.0.0</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>配置文件:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditProfilesConfig">
           <property name="text">
            <string>E:/Projects/CTHostComputerSorftware/CTHostComputerSorftware/doc/HIL/100Frames/sensor_config_init0.hxx;E:/Projects/CTHostComputerSorftware/CTHostComputerSorftware/doc/HIL/100Frames/sensor_config_init1.hxx;E:/Projects/CTHostComputerSorftware/CTHostComputerSorftware/doc/HIL/100Frames/sensor_config_init2.hxx;E:/Projects/CTHostComputerSorftware/CTHostComputerSorftware/doc/HIL/100Frames/sensor_config_init3.hxx</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonProfilesConfig">
           <property name="text">
            <string>...</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>295</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="debugsystem.qrc"/>
 </resources>
 <connections/>
</ui>
