﻿#include "savemanager.h"

#include "mastericons.h"
#include "devices/cansaveworker.h"
#include "devices/devicemanager.h"
#include "analysis/analysismanager.h"
#include "media/screenrecordsaveworker.h"
#include "truevalue/hesailiderworker.h"
#include "camera/mulitcamera.h"

#include <QProcess>
#include <QAction>
#include <QDir>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>


static const char saveStartActionText[] = "开始保存";
static const char saveStopActionText[] = "停止保存";

namespace Core {

SaveManager::SaveManager(Devices::Can::DeviceManager *deviceManger,
                         Analysis::AnalysisManager *analysisManger,
                         HeSaiLiderWorker *heSaiLiderWoker,
                         QMap<QString /*description + deviecname*/, Camera::MulitCamera*> *cameras,
                         QObject *parent)
    : QObject(parent),
      mDeviceManager(deviceManger),
      mAnalysisManger(analysisManger),
      mHeSaiLiderWorker(heSaiLiderWoker),
      mCameras(cameras)
{
    connect(mAnalysisManger, &Analysis::AnalysisManager::saveStarted, this, [=](const QString &rawFilename, bool ok = true) {
        emit saveFilename(ok ? rawFilename : "");
    });

    emit saveFilename(mProjectSavePath);

    mActionSelectSavePath = new QAction(QString::fromLocal8Bit("选择保存路径"), this);
    connect(mActionSelectSavePath, &QAction::triggered, this, &SaveManager::selectSavePath);

    mActionOpenSavePath = new QAction(Icons::CHENGTECTRADARMASTER_SAVE_PATH.icon(), QString::fromLocal8Bit("打开保存路径"), this);
    connect(mActionOpenSavePath, &QAction::triggered, this, &SaveManager::openSavePath);

    QIcon iconSave;
    iconSave.addFile(QString::fromUtf8(":/master/images/savecsv.png"), QSize(), QIcon::Normal, QIcon::Off);
    iconSave.addFile(QString::fromUtf8(":/master/images/unsavecsv.png"), QSize(), QIcon::Normal, QIcon::On);
    mActionSave = new QAction(iconSave, QString::fromLocal8Bit(saveStartActionText));
    mActionSave->setCheckable(true);
    mActionSave->setEnabled(false);
    connect(mActionSave, &QAction::triggered, this, &SaveManager::saveStartAndStop);
    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceOpened, mActionSave, [=](){ mActionSave->setEnabled(true); });
    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceClosed, mActionSave, [=](){
        qDebug() << __FUNCTION__ << __LINE__ << "message" << isSaveing();
        if (isSaveing()) {
            saveStartAndStop();
        }
        mActionSave->setEnabled(false);
    });

    mActionSaveAnalysisData = new QAction(QString::fromLocal8Bit("保存分析数据"));
    mActionSaveAnalysisData->setCheckable(true);
    mActionSaveAnalysisData->setChecked(true);

    mActionSaveOldStyle = new QAction(QString::fromLocal8Bit("保存旧格式"));
    mActionSaveOldStyle->setCheckable(true);
    mActionSaveOldStyle->setChecked(true);

    mActionSaveCANFrameASC = new QAction(QString::fromLocal8Bit("保存CAN ASC"));
    mActionSaveCANFrameASC->setCheckable(true);
    mActionSaveCANFrameASC->setChecked(true);

    mActionSaveCANFrameBLF = new QAction(QString::fromLocal8Bit("保存CAN BLF"));
    mActionSaveCANFrameBLF->setCheckable(true);
    mActionSaveCANFrameBLF->setChecked(true);

    mActionSaveVideo = new QAction(QString::fromLocal8Bit("保存视频"));
    mActionSaveVideo->setCheckable(true);
    mActionSaveVideo->setChecked(true);

    mActionSaveHeSaiLider = new QAction(QString::fromLocal8Bit("保存禾赛激光"));
    mActionSaveHeSaiLider->setCheckable(true);
    mActionSaveHeSaiLider->setChecked(true);

    mScreenRecordSaveWorker = new ScreenRecordSaveWorker( this );
}

bool SaveManager::startSave(bool needResponse)
{
    bool saveAnalysisData = mActionSaveAnalysisData->isChecked();
    bool oldSytle = mActionSaveOldStyle->isChecked();
    bool saveVideo = mActionSaveVideo->isChecked();
    bool saveHeSaiLider = mActionSaveHeSaiLider->isChecked();

    qDebug() << __FUNCTION__ << __LINE__ << mSaveCanDataType << oldSytle << needResponse;
    if (!mkSavePath()) {
        mErrorString = QString::fromLocal8Bit("创建保存路径失败!\n%1").arg(mProjectSavePath);
        return false;
    }

    if (!startSaveVideo()) {
        return false;
    }

    mSaveCanDataType = 0;
    if (mActionSaveCANFrameASC->isChecked()) {
        mSaveCanDataType |= Devices::Can::CanSaveWorker::SAVE_ASC;
    }
    if (mActionSaveCANFrameBLF->isChecked()) {
        mSaveCanDataType |= Devices::Can::CanSaveWorker::SAVE_BLF;
    }
    if (mSaveCanDataType) {
        if (!mDeviceManager->startSave(mCANSavePath, mSaveTime, mSaveCanDataType, needResponse)) {
            stopSaveVideo();
            return false;
        }
    }

    if ( saveAnalysisData && !mAnalysisManger->startSave(mProjectSavePath, mSaveTime, oldSytle, needResponse)) {
        stopSaveVideo();
        if (mSaveCanDataType) {
            mDeviceManager->stopSave();
        }
        return false;
    }

//    if( saveVideo/*mScreenRecordSaveWorker*/ && !mScreenRecordSaveWorker->startSave( mVideoSavePath, mSaveTime ) ){
//        if (mSaveCanDataType) {
//            stopSaveVideo();
//            mDeviceManager->stopSave();
//        }
//        if (saveAnalysisData) {
//            stopSaveVideo();
//            mAnalysisManger->stopSave();
//        }
//    }

    if (saveHeSaiLider && !mHeSaiLiderWorker->startSave(mProjectSavePath, mSaveTime)) {
        if (mSaveCanDataType) {
            stopSaveVideo();
            mDeviceManager->stopSave();
        }
        if (saveAnalysisData) {
            stopSaveVideo();
            mAnalysisManger->stopSave();
        }

        if (saveVideo) {
            stopSaveVideo();
//            mScreenRecordSaveWorker->stopSave();
        }
    }

    mActionSave->setText(QString::fromLocal8Bit(saveStopActionText));
    mActionSaveCANFrameASC->setEnabled(false);
    mActionSaveCANFrameBLF->setEnabled(false);
    mActionSaveAnalysisData->setEnabled(false);
    mActionSaveOldStyle->setEnabled(false);
    mActionSaveVideo->setEnabled( false );
    mActionSaveHeSaiLider->setEnabled( false );

    mSaveing = true;
    mActionSave->setChecked(mSaveing);

    return true;
}

void SaveManager::stopSave()
{
    qDebug() << __FUNCTION__ << __LINE__ << mSaveCanDataType << mSavePath << mProjectSavePath;
    if (mSaveCanDataType) {
        mDeviceManager->stopSave();
    }
    mAnalysisManger->stopSave();
//    if( mScreenRecordSaveWorker->isSaving()){
//        mScreenRecordSaveWorker->stopSave();
//    }

    if (mHeSaiLiderWorker->isSaving()) {
        mHeSaiLiderWorker->stopSave();
    }

    stopSaveVideo();

    mActionSave->setText(QString::fromLocal8Bit(saveStartActionText));
    mActionSave->setChecked(false);
    mActionSaveCANFrameASC->setEnabled(true);
    mActionSaveCANFrameBLF->setEnabled(true);
    mActionSaveAnalysisData->setEnabled(true);
    mActionSaveOldStyle->setEnabled(true);
    mActionSaveVideo->setEnabled( true );
    mActionSaveHeSaiLider->setEnabled( true );

    mSaveing = false;
    mActionSave->setChecked(mSaveing);
}

void SaveManager::setSaveAnalysisData(bool bSave)
{
    mActionSaveAnalysisData->setChecked(bSave);
}

void SaveManager::saveStartAndStop()
{
    if (!mSaveing) {
        if (!mDeviceManager->isOpened()) {
            if (QMessageBox::Ok !=
                    QMessageBox::question(0,
                                          QString::fromLocal8Bit("保存"),
                                          QString::fromLocal8Bit("设备未运行，是否继续保存？"),
                                          QMessageBox::Ok | QMessageBox::Cancel,
                                          QMessageBox::Ok)) {
                return;
            }
        }

        if (!startSave()) {
            QMessageBox::warning(0, QString::fromLocal8Bit("保存数据"), QString::fromLocal8Bit("保存数据失败！\n%1").arg(errorString()));
            return;
        }
    }
    else {
        stopSave();
    }
}

void SaveManager::selectSavePath()
{
    QString savePath = QFileDialog::getExistingDirectory(0, QString::fromLocal8Bit("选择保存路径"), mSavePath, QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
    if (savePath.isEmpty())
    {
        return;
    }

    setSavePath(savePath);
}

void SaveManager::openSavePath()
{
    QProcess::startDetached(QString("explorer %1").arg(QFileInfo(mSavePath).absoluteFilePath().replace("/", "\\")));
}

bool SaveManager::startSaveVideo()
{
    QMapIterator<QString /*description + deviecname*/, Camera::MulitCamera*> i(*mCameras);
    while (i.hasNext()) {
        i.next();
        Camera::MulitCamera *camera = i.value();
        if (camera->isOpened()) {
            camera->startSaveVideo(mVideoSavePath, mSaveTime.toMSecsSinceEpoch());
        }
    }

    return true;
}

bool SaveManager::stopSaveVideo()
{
    QMapIterator<QString /*description + deviecname*/, Camera::MulitCamera*> i(*mCameras);
    while (i.hasNext()) {
        i.next();
        Camera::MulitCamera *camera = i.value();
        if (camera->isSaveing()) {
            camera->stopSave();
        }
    }

    return true;
}

bool SaveManager::mkSavePath()
{
    if (mSavePath.isEmpty()) {
        mSavePath = "./data";
    }
    mSaveTime = QDateTime::currentDateTime();
    mProjectSavePath = QString("%1/%2").arg(mSavePath).arg(mSaveTime.toString("yyyy-MM-dd hh-mm-ss-zzz"));
    mCANSavePath = QString("%1/%2").arg(mProjectSavePath).arg("CAN");
    mVideoSavePath = QString("%1/%2").arg(mProjectSavePath).arg("Video");

    QDir logDir(mSavePath);
    if (!logDir.exists()) {
        logDir.setPath("");
        if (!logDir.mkpath(mSavePath)) {
                return false;
            }
    }

    logDir.setPath("");
    if (!logDir.mkpath(mProjectSavePath)) {
        return false;
    }

    logDir.setPath("");
    if (!logDir.mkpath(mCANSavePath)) {
        return false;
    }

    logDir.setPath("");
    if (!logDir.mkpath(mVideoSavePath)) {
        return false;
    }

    return true;
}

} // namespace Core
