include(../libs.pri)

QT += core gui widgets

HEADERS += \
    analysisdata.h \
    analysismanager.h \
    analysissaveworker.h \
    analysisworker.h \
    analysis_global.h \
    calculationworker.h \
    protocol/CTMRR410.h \
    protocol/analysisprotocolct.h \
    protocol/analysisprotocolct410.h \
    protocol/baictargetprotocol.h \
    protocol/byd120targetprotocol.h \
    protocol/ianalysisprotocol.h \
    protocol/truesystemprotocol.h \
    truesystemdata.h

SOURCES += \
    analysisdata.cpp \
    analysismanager.cpp \
    analysissaveworker.cpp \
    analysisworker.cpp \
    calculationworker.cpp \
    protocol/CTMRR410.c \
    protocol/analysisprotocolct.cpp \
    protocol/analysisprotocolct410.cpp \
    protocol/baictargetprotocol.cpp \
    protocol/byd120targetprotocol.cpp \
    protocol/ianalysisprotocol.cpp \
    protocol/truesystemprotocol.cpp

LIBS +=-ldevices -lutils
