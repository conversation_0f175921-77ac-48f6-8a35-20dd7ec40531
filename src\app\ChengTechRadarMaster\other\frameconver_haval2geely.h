#ifndef FRAMECONVER_HAVAL2GEELY_H
#define FRAMECONVER_HAVAL2GEELY_H

#include "iframeconver.h"

#include <QObject>


namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
}

//哈弗协议转换为吉利协议
class FrameConver_Haval2Geely : public IFrameConver
{
    Q_OBJECT

    //haval 0xa1 length = 8
    typedef struct CT_CSA2 {
        uint8_t Checksum_CSA2;
        float CSA2_SteerWheelAng;
        uint8_t CSA2_SteerWheelAngSign;
        float CSA2_SteerWheelSpd;
        uint8_t CSA2_SteerWheelSpdSign;
        uint8_t CSA2_SAS_Sts;
        uint8_t RollingCounter_CS2;
    }CT_CSA2_t;

    //haval 0x102 length = 8
    typedef struct CT_ECM9 {
        uint8_t Checksum_ECM9;
        float ECM9_AccelPedlPosnDiagc;
        uint8_t RollingCounter_ECM9;
    }CT_ECM9_t;

    //haval 0x111 length = 8
    typedef struct CT_ECM1 {
        uint8_t ECM1_BrkPedalSts;
        float ECM1_EngSpd;
        uint8_t ECM1_EngSpdVldty;
    }CT_ECM1_t;

    //haval 0x145 length = 16
    typedef struct CT_ESP2 {
        uint8_t ESP2_CheckSum_ESP2;
        uint8_t ESP2_VehStandstill;
        uint8_t ESP2_NoBrkForce;
        uint8_t ESP2_BrkDskOvrheatd;
        uint8_t ESP2_CTABrkAvailable;
        uint8_t ESP2_DiagActv;
        uint8_t ESP2_AEBBAActv;
        uint8_t ESP2_AEBIBActv;
        uint8_t ESP2_HDCCtrl;
        uint8_t ESP2_RollingCounter_ESP2;
    }CT_ESP2_t;

    //haval 0x165 length = 8
    typedef struct CT_CSA_1 {
        uint8_t CSA_RTurnLmpSwtSts;
        uint8_t CSA_LTurnLmpSwtSts;
    }CT_CSA_1_t;

    //haval 0x221 length = 8
    typedef struct CT_DCT_5 {
        uint8_t DCT_CurrentGearValid;
        uint8_t DCT_CurrentGear;
        uint8_t DCT5_TGS_LEVER;
    }CT_DCT_5_t;

    //haval 0x245 length = 8
    typedef struct CT_ABM2 {
        uint8_t CheckSum_ABM2;
        float ABM2_VehLgtAccel;
        float ABM2_VehLatAccel;
        float ABM2_VehYawRate;
        uint8_t ABM2_VehLgtAccelVld;
        uint8_t ABM2_VehLatAccelVld;
        uint8_t ABM2_VehYawRateVd;
        uint8_t ABM2_RollingCounter_ABM2;
    }CT_ABM2_t;

    //haval 0x265 length = 16
    typedef struct CT_ABS3 {
        uint8_t ABS3_CheckSum_ABS3;
        uint8_t ABS3_VehSpdVd;
        float ABS3_VehSpd;
        uint8_t ABS3_ABSActv;
        uint8_t ABS3_RollingCounter_ABS3;
    }CT_ABS3_t;

    //haval 0x295 length = 8
    typedef struct CT_PEPS2 {
        uint8_t PEPS2_SysPowerMod;
        uint8_t PEPS2_SysPowerModVld;
    }CT_PEPS2_t;

    //haval 0x2c3 length = 64
    typedef struct CT_HUT_FD1 {
        uint8_t HUT_LCASwtReq;
        uint8_t HUT_RCTASwtReq;
        uint8_t HUT_RCTABrkSwtReq;
        uint8_t HUT_RCWSwtReq;
        uint8_t HUT_DOWSwtReq;
    }CT_HUT_FD1_t;

    //haval 0x319 length = 16
    typedef struct CT_BCM_1 {
        uint8_t BCM_DrvDoorSts;
        uint8_t BCM_LRDoorSts;
        uint8_t BCM_PassengerDoorSts;
        uint8_t BCM_RRDoorSts;
    }CT_BCM_1_t;


    //Geely 0x57 length = 64
    typedef struct CT_ASDMSafetyCANFD3Frame7 {
        uint8_t AccrPedlRat_UB;
        float AccrPedlRatAccrPedlRat;
        uint8_t AccrPedlRatChks;
        uint8_t AccrPedlRatCntr;
        uint8_t BrkPedlrRat_UB;
        uint8_t BrkPedlPsd_UB;
        uint8_t SwtIndcr_UB;
        uint8_t AgDataRawSafe_UB;
        float AgDataRawSafeRollRate;
        float AgDataRawSafeYawRate;
        uint8_t AgDataRawSafeChks;
        uint8_t AgDataRawSafeCntr;
        uint8_t AgDataRawSafeRollRateQf;        // 0 "Qf1_DevOfDataUndefd" 1 "Qf1_DataTmpUndefdAndEvlnInProgs" 2 "Qf1_DevOfDataNotWithinRngAllwd" 3 "Qf1_DataCalcdWithDevDefd"
        uint8_t AgDataRawSafeYawRateQf;         // 0 "Qf1_DevOfDataUndefd" 1 "Qf1_DataTmpUndefdAndEvlnInProgs" 2 "Qf1_DevOfDataNotWithinRngAllwd" 3 "Qf1_DataCalcdWithDevDefd"
        uint8_t BSDaudioOnOff_UB;
        uint8_t BSDSwOnOff_UB;
        uint8_t SwtIndcrIndcrTypExtReq;         // 0 "IndcrTypExt1_Off" 1 "IndcrTypExt1_Le" 2 "IndcrTypExt1_Ri"
        uint8_t SwtIndcrIndcrTypExtReqCntr;
        uint8_t SwtIndcrIndcrTypExtReqToUpdQf;  // 0 "Qf1_DevOfDataUndefd" 1 "Qf1_DataTmpUndefdAndEvlnInProgs" 2 "Qf1_DevOfDataNotWithinRngAllwd" 3 "Qf1_DataCalcdWithDevDefd"
        uint8_t SwtIndcrIndcrTypExtReqChks;
        uint8_t BrkPedlPsdBrkPedlNotPsdSafe;    // 0 "NoYes1_No" 1 "NoYes1_Yes"
        uint8_t BrkPedlPsdBrkPedlPsd;           // 0 "NoYes1_No" 1 "NoYes1_Yes"
        uint8_t BrkPedlPsdQf;                   // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        uint8_t BrkPedlPsdCntr;
        uint8_t BrkPedlPsdChks;
        uint8_t BSDaudioOnOff;                  // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t BSDSwOnOffOnOff1;               // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t BSDSwOnOffIdPen;                // 0 "IdPen_ProfUkwn" 1 "IdPen_Prof1" 2 "IdPen_Prof2" 3 "IdPen_Prof3" 4 "IdPen_Prof4" 5 "IdPen_Prof5" 6 "IdPen_Prof6" 7 "IdPen_Prof7" 8 "IdPen_Prof8" 9 "IdPen_Prof9" 10 "IdPen_Prof10" 11 "IdPen_Prof11" 12 "IdPen_Prof12" 13 "IdPen_Prof13" 14 "IdPen_Resd14" 15 "IdPen_ProfAll"
        uint8_t BSDSwOnOffandWarnType;          // 0 "WarmType_Off" 1 "WarmType_Light" 2 "WarmType_LightAndAudio"
        uint8_t BSDSwOnOffandWarnType_UB;
        float BrkPedlrRatPerc;
        uint8_t BrkPedlrRatQf;                  // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        uint8_t CtraSwOnOffOnOff1;              // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t CtraSwOnOffIdPen;               // 0 "IdPen_ProfUkwn" 1 "IdPen_Prof1" 2 "IdPen_Prof2" 3 "IdPen_Prof3" 4 "IdPen_Prof4" 5 "IdPen_Prof5" 6 "IdPen_Prof6" 7 "IdPen_Prof7" 8 "IdPen_Prof8" 9 "IdPen_Prof9" 10 "IdPen_Prof10" 11 "IdPen_Prof11" 12 "IdPen_Prof12" 13 "IdPen_Prof13" 14 "IdPen_Resd14" 15 "IdPen_ProfAll"
        uint8_t CtraSwOnOff_UB;
        uint8_t DoorDrvrLockSts;                // 0 "LockSts2_LockStsUkwn" 1 "LockSts2_Unlckd" 2 "LockSts2_Lockd" 3 "LockSts2_SafeLockd"
        uint8_t DoorDrvrSts;                    // 0 "DoorSts2_Ukwn" 1 "DoorSts2_Opend" 2 "DoorSts2_Clsd"
        uint8_t DoorLeReLockSts;                // 0 "LockSts2_LockStsUkwn" 1 "LockSts2_Unlckd" 2 "LockSts2_Lockd" 3 "LockSts2_SafeLockd"
        uint8_t DoorLeReSts;                    // 0 "DoorSts2_Ukwn" 1 "DoorSts2_Opend" 2 "DoorSts2_Clsd"
        uint8_t DoorPassLockSts;                // 0 "LockSts2_LockStsUkwn" 1 "LockSts2_Unlckd" 2 "LockSts2_Lockd" 3 "LockSts2_SafeLockd"
        uint8_t DoorPassSts;                    // 0 "DoorSts2_Ukwn" 1 "DoorSts2_Opend" 2 "DoorSts2_Clsd"
        uint8_t DoorRiReLockSts;                // 0 "LockSts2_LockStsUkwn" 1 "LockSts2_Unlckd" 2 "LockSts2_Lockd" 3 "LockSts2_SafeLockd"
        uint8_t DoorRiReSts;                    // 0 "DoorSts2_Ukwn" 1 "DoorSts2_Opend" 2 "DoorSts2_Clsd"
        uint8_t DoorDrvrLockSts_UB;
        uint8_t DoorDrvrSts_UB;
        uint8_t DoorLeReLockSts_UB;
        uint8_t DoorLeReSts_UB;
        uint8_t DoorPassLockSts_UB;
        uint8_t DoorPassSts_UB;
        uint8_t DoorRiReLockSts_UB;
        uint8_t DoorRiReSts_UB;
        uint8_t DOWSwOnOffOnOff1;               // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t DOWSwOnOffIdPen;                // 0 "IdPen_ProfUkwn" 1 "IdPen_Prof1" 2 "IdPen_Prof2" 3 "IdPen_Prof3" 4 "IdPen_Prof4" 5 "IdPen_Prof5" 6 "IdPen_Prof6" 7 "IdPen_Prof7" 8 "IdPen_Prof8" 9 "IdPen_Prof9" 10 "IdPen_Prof10" 11 "IdPen_Prof11" 12 "IdPen_Prof12" 13 "IdPen_Prof13" 14 "IdPen_Resd14" 15 "IdPen_ProfAll"
        uint8_t DOWSwOnOff_UB;
        uint8_t EpbSts_UB;
        uint8_t EscCtrlIndcn_UB;
        uint8_t EpbStsEpbSts;                   // 0 "EpbSts_Resd0" 1 "EpbSts_Resd1" 2 "EpbSts_Resd2" 3 "EpbSts_AllAppld" 4 "EpbSts_Resd4" 5 "EpbSts_AllInTran" 6 "EpbSts_BrkgDynByActr" 7 "EpbSts_Resd7" 8 "EpbSts_Resd8" 9 "EpbSts_ActrAllReld" 10 "EpbSts_BrkgDynDegraded" 11 "EpbSts_Resd11" 12 "EpbSts_BrkgDyn" 13 "EpbSts_Resd13" 14 "EpbSts_Resd14" 15 "EpbSts_Err"
        uint8_t EpbStsCntr;
        uint8_t EpbStsChks;
        uint8_t EscCtrlIndcn;                   // 0 "DevSts1_On" 1 "DevSts1_Off" 2 "DevSts1_Flt"
        uint8_t GearLvrIndcn;                   // 0 "GearLvrIndcn2_ParkIndcn" 1 "GearLvrIndcn2_RvsIndcn" 2 "GearLvrIndcn2_NeutIndcn" 3 "GearLvrIndcn2_DrvIndcn" 4 "GearLvrIndcn2_ManModeIndcn" 5 "GearLvrIndcn2_Resd1" 6 "GearLvrIndcn2_Resd2" 7 "GearLvrIndcn2_Undefd"
        uint8_t LcmaLedStsDrvrSide;             // 0 "YesNo1_Yes" 1 "YesNo1_No"
        uint8_t LcmaLedStsDrvrSide_UB;
        uint8_t LcmaLedStsPassSide;             // 0 "YesNo1_Yes" 1 "YesNo1_No"
        uint8_t EscSt_UB;
        uint8_t EscStEscSt;                     // 0 "EscSt1_Inin" 1 "EscSt1_Ok" 2 "EscSt1_TmpErr" 3 "EscSt1_PrmntErr" 4 "EscSt1_UsrOff"
        uint8_t EscStCntr;
        uint8_t EscStChks;
        uint8_t GearLvrIndcn_UB;
        uint8_t EscWarnIndcnReq_UB;
        uint8_t EscWarnIndcnReqEscWarnIndcnReq; // 0 "EscWarnIndcnReq_EscWarnIndcnOnReq" 1 "EscWarnIndcnReq_EscWarnIndcnFlsgReq" 2 "EscWarnIndcnReq_Resd2" 3 "EscWarnIndcnReq_EscWarnIndcnOffReq"
        uint8_t EscWarnIndcnReqCntr;
        uint8_t EscWarnIndcnReqChks;
        uint8_t LcmaLedStsPassSide_UB;
        uint8_t RctaBrkActvd;                   // 0 "NoYes1_No" 1 "NoYes1_Yes"
        uint8_t RctaBrkActvd_UB;
        uint8_t RcwmBrkActvd;                   // 0 "NoYes1_No" 1 "NoYes1_Yes"
        uint8_t RcwmBrkActvd_UB;
        uint8_t RcwmLiActvd;                    // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t RcwmLiActvd_UB;
        uint8_t RCWSwOnOff_UB;
        uint8_t RCWSwOnOffOnOff1;               // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t RCWSwOnOffIdPen;                // 0 "IdPen_ProfUkwn" 1 "IdPen_Prof1" 2 "IdPen_Prof2" 3 "IdPen_Prof3" 4 "IdPen_Prof4" 5 "IdPen_Prof5" 6 "IdPen_Prof6" 7 "IdPen_Prof7" 8 "IdPen_Prof8" 9 "IdPen_Prof9" 10 "IdPen_Prof10" 11 "IdPen_Prof11" 12 "IdPen_Prof12" 13 "IdPen_Prof13" 14 "IdPen_Resd14" 15 "IdPen_ProfAll"
        uint8_t VehTiAndData_UB;
        uint8_t VehTiAndDataDataValid;          // 0 "NoYes1_No" 1 "NoYes1_Yes"
        uint8_t VehTiAndDataSec1;
        uint8_t VehTiAndDataMins1;
        uint8_t VehTiAndDataHr1;
        uint8_t VehTiAndDataDay;
        uint8_t VehTiAndDataMth1;
        uint8_t VehTiAndDataYr1;
    }CT_ASDMSafetyCANFD3Frame7_t;

    //Geely 0x68 length = 64
    typedef struct CT_ASDMSafetyCANFD3Frame4 {
        uint8_t TurnIndcrMonostableChks;
        uint8_t TurnIndcrMonostableCntr;
        uint8_t TurnIndcrMonostableQf;          // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        uint8_t TurnIndcrMonostableTurnIndcrMono; // 0 "Dir10_Idle" 1 "Dir10_Leftflash" 2 "Dir10_Left" 3 "Dir10_Rightflash" 4 "Dir10_Right"
        uint8_t TurnIndcrMonostable_UB;
    }CT_ASDMSafetyCANFD3Frame4_t;

    //Geely 0x120 length = 64
    typedef struct CT_ASDMSafetyCANFD3Frame1 {
        uint8_t ADataRawSafeChks;
        uint8_t ADataRawSafeCntr;
        uint8_t ADataRawSafeALat1Qf;            // 0 "Qf1_DevOfDataUndefd" 1 "Qf1_DataTmpUndefdAndEvlnInProgs" 2 "Qf1_DevOfDataNotWithinRngAllwd" 3 "Qf1_DataCalcdWithDevDefd"
        uint8_t ADataRawSafeALgt1Qf;            // 0 "Qf1_DevOfDataUndefd" 1 "Qf1_DataTmpUndefdAndEvlnInProgs" 2 "Qf1_DevOfDataNotWithinRngAllwd" 3 "Qf1_DataCalcdWithDevDefd"
        float ADataRawSafeALat;
        uint8_t ADataRawSafeAVertQf;            // 0 "Qf1_DevOfDataUndefd" 1 "Qf1_DataTmpUndefdAndEvlnInProgs" 2 "Qf1_DevOfDataNotWithinRngAllwd" 3 "Qf1_DataCalcdWithDevDefd"
        float ADataRawSafeALgt;
        float ADataRawSafeAVert;
        uint8_t ADataRawSafe_UB;
        uint8_t SteerWhlSnsr_UB;
        float SteerWhlSnsrAg;
        uint8_t SteerWhlSnsrQf;                 // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        float SteerWhlSnsrAgSpd;
        uint8_t SteerWhlSnsrChks;
        uint8_t SteerWhlSnsrCntr;
        uint8_t CarTiGlb_UB;
        uint8_t AmbTRaw_UB;
        uint8_t DrvModReq_UB;
        float AsyDataWithCmpSafeYawRateWithCmp;
        uint8_t AsyDataWithCmpSafeChks;
        float AsyDataWithCmpSafeGrdtOfALgt;
        uint8_t AsyDataWithCmpSafeYawRateQf;    // 0 "Qf1_DevOfDataUndefd" 1 "Qf1_DataTmpUndefdAndEvlnInProgs" 2 "Qf1_DevOfDataNotWithinRngAllwd" 3 "Qf1_DataCalcdWithDevDefd"
        uint8_t AsyDataWithCmpSafeCntr;
        uint8_t AsyDataWithCmpSafeALgt1Qf;      // 0 "Qf1_DevOfDataUndefd" 1 "Qf1_DataTmpUndefdAndEvlnInProgs" 2 "Qf1_DevOfDataNotWithinRngAllwd" 3 "Qf1_DataCalcdWithDevDefd"
        uint8_t AsyDataWithCmpSafeALat1Qf;      // 0 "Qf1_DevOfDataUndefd" 1 "Qf1_DataTmpUndefdAndEvlnInProgs" 2 "Qf1_DevOfDataNotWithinRngAllwd" 3 "Qf1_DataCalcdWithDevDefd"
        float AsyDataWithCmpSafeALatWithCmp;
        uint8_t AsyDataWithCmpSafe_UB;
        uint8_t VehMtnSt_UB;
        uint8_t VehMtnStCntr;
        uint8_t VehMtnStVehMtnSt;               // 0 "VehMtnSt2_Ukwn" 1 "VehMtnSt2_StandStillVal1" 2 "VehMtnSt2_StandStillVal2" 3 "VehMtnSt2_StandStillVal3" 4 "VehMtnSt2_RollgFwdVal1" 5 "VehMtnSt2_RollgFwdVal2" 6 "VehMtnSt2_RollgBackwVal1" 7 "VehMtnSt2_RollgBackwVal2"
        uint8_t VehMtnStChks;
        uint8_t VehSpdLgt_UB;
        float VehSpdLgtA;
        uint8_t VehSpdLgtChks;
        uint8_t VehSpdLgtCntr;
        uint8_t VehSpdLgtQf;                    // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        uint8_t DrvModReq;                      // 0 "DrvModReqType1_Undefd" 1 "DrvModReqType1_ECO" 2 "DrvModReqType1_Comfort_Normal" 3 "DrvModReqType1_Dynamic_Sport" 4 "DrvModReqType1_Individual" 5 "DrvModReqType1_Offroad_CrossTerrain" 6 "DrvModReqType1_Adaptive" 7 "DrvModReqType1_Race" 8 "DrvModReqType1_Pure_EV" 9 "DrvModReqType1_Hybrid" 10 "DrvModReqType1_Power" 11 "DrvModReqType1_Snow" 12 "DrvModReqType1_Sand" 13 "DrvModReqType1_Mud" 14 "DrvModReqType1_Rock" 15 "DrvModReqType1_Err"
        uint8_t AmbTRawQly;                     // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        float AmbTRawAmbTVal;
        uint8_t PinionSteerAgGroupChks;
        uint8_t PinionSteerAgGroupPinionSte_0000; // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        float PinionSteerAgGroupPinionSteerAgS;
        uint8_t PinionSteerAgGroupPinionSte_0001; // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        float PinionSteerAgGroupSteerWhlTq;
        float PinionSteerAgGroupPinionSteerAg1;
        uint8_t PinionSteerAgGroupCntr;
        uint8_t PinionSteerAgGroupSteerWhlTqQf; // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        uint8_t PinionSteerAgGroup_UB;
        uint8_t BkpOfDstTrvld_UB;
        uint8_t ALgtStdFromWhlSpd_UB;
        uint8_t ALgtStdFromWhlSpdQf;            // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        uint8_t ALgtStdFromWhlSpdCntr;
        uint8_t ALgtStdFromWhlSpdChks;
        float ALgtStdFromWhlSpdALgtStdFromWhlS;
        uint8_t VehModMngtGlbSafe1Chks;
        uint8_t VehModMngtGlbSafe1Cntr;
        uint8_t VehModMngtGlbSafe1EgyLvlElecMai;
        uint8_t VehModMngtGlbSafe1EgyLvlElecSubt;
        uint8_t VehModMngtGlbSafe1PwrLvlElecMai;
        uint8_t VehModMngtGlbSafe1PwrLvlElecSubt;
        uint8_t VehModMngtGlbSafe1UsgModSts;    // 0 "UsgModSts1_UsgModAbdnd" 1 "UsgModSts1_UsgModInActv" 2 "UsgModSts1_UsgModCnvinc" 11 "UsgModSts1_UsgModActv" 13 "UsgModSts1_UsgModDrvg"
        uint8_t VehModMngtGlbSafe1CarModSts1;   // 0 "CarModSts1_CarModNorm" 1 "CarModSts1_CarModTrnsp" 2 "CarModSts1_CarModFcy" 3 "CarModSts1_CarModCrash" 5 "CarModSts1_CarModDyno"
        uint8_t VehModMngtGlbSafe1CarModSubtypWd;
        uint8_t VehModMngtGlbSafe1FltEgyCnsWdSts; // 0 "FltEgyCns1_NoFlt" 1 "FltEgyCns1_Flt"
        uint8_t VehModMngtGlbSafe1_UB;
        float CarTiGlb;
        uint32_t BkpOfDstTrvld;
        uint8_t VehBattU_UB;
        uint8_t VehBattUSysUQf;                 // 0 "GenQf1_UndefindDataAccur" 1 "GenQf1_TmpUndefdData" 2 "GenQf1_DataAccurNotWithinSpcn" 3 "GenQf1_AccurData"
        float VehBattUSysU;
        uint8_t WipgInfo_UB;
        uint8_t WipgInfoWiprInWipgAr;           // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t WipgInfoWiprActv;               // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t WipgInfoWipgSpdInfo;            // 0 "WipgSpdInfo_Off" 1 "WipgSpdInfo_IntlLo" 2 "WipgSpdInfo_IntlHi" 3 "WipgSpdInfo_WipgSpd4045" 4 "WipgSpdInfo_WipgSpd4650" 5 "WipgSpdInfo_WipgSpd5155" 6 "WipgSpdInfo_WipgSpd5660" 7 "WipgSpdInfo_WiprErr"
    }CT_ASDMSafetyCANFD3Frame1_t;

    //Geely 0x17a length = 32
    typedef struct CT_ASDMSafetyCANFD3Frame8 {
        uint8_t AlrmSts_UB;
        uint8_t AlrmStsSnsrIntrScanrFailr;      // 0 "SnsrIntrScanrFailr_NoFailr" 1 "SnsrIntrScanrFailr_Failr"
        uint8_t AlrmStsSnsrInclnFailr;          // 0 "SnsrInclnFailr_NoFailr" 1 "SnsrInclnFailr_Failr"
        uint8_t AlrmStsAlrmSt;                  // 0 "AlrmSt_Disarmd" 1 "AlrmSt_Armd" 2 "AlrmSt_Actv"
        uint8_t AlrmStsAlrmFailr;               // 0 "SnsrSoundrBattBackedFailr_NoFailr" 1 "SnsrSoundrBattBackedFailr_Failr"
        uint8_t AlrmStsAlrmTrgSrc;              // 0 "AlrmTrgSrc_NoTrigSrc" 1 "AlrmTrgSrc_SnsrSoundrBattBacked" 2 "AlrmTrgSrc_SnsrIncln" 3 "AlrmTrgSrc_SnsrIntrScanr" 4 "AlrmTrgSrc_Hood" 5 "AlrmTrgSrc_Tr" 6 "AlrmTrgSrc_DoorDrvr" 7 "AlrmTrgSrc_DoorPass" 8 "AlrmTrgSrc_DoorReLe" 9 "AlrmTrgSrc_DoorReRi" 10 "AlrmTrgSrc_VehImobnInvld" 11 "AlrmTrgSrc_VSTD" 12 "AlrmTrgSrc_Alcoloch"
    }CT_ASDMSafetyCANFD3Frame8_t;

public:
    explicit FrameConver_Haval2Geely( Devices::Can::DeviceManager* deviceManager, QObject *parent = nullptr);

signals:


protected:
//    bool isRecvID( quint64 id );
    virtual void analysis( const Devices::Can::CanFrame& frame );
    virtual  void conver();
    virtual  void packageFrame();
    virtual void initRecvIDAndEndID();

private://haval
    // 0xa1
    bool decode_CSA2(CT_CSA2_t *userData, uint8_t *data, int length);
    // 0x102
    bool decode_ECM9(CT_ECM9_t *userData, uint8_t *data, int length);
    // 0x111
    bool decode_ECM1(CT_ECM1_t *userData, uint8_t *data, int length);
    // 0x145
    bool decode_ESP2(CT_ESP2_t *userData, uint8_t *data, int length);
    // 0x165
    bool decode_CSA_1(CT_CSA_1_t *userData, uint8_t *data, int length);
    // 0x221
    bool decode_DCT_5(CT_DCT_5_t *userData, uint8_t *data, int length);
    // 0x245
    bool decode_ABM2(CT_ABM2_t *userData, uint8_t *data, int length);
    // 0x265
    bool decode_ABS3(CT_ABS3_t *userData, uint8_t *data, int length);
    // 0x295
    bool decode_PEPS2(CT_PEPS2_t *userData, uint8_t *data, int length);
    // 0x2c3
    bool decode_HUT_FD1(CT_HUT_FD1_t *userData, uint8_t *data, int length);
    // 0x319
    bool decode_BCM_1(CT_BCM_1_t *userData, uint8_t *data, int length);

private://Geely
    // 0x57
    bool encode_ASDMSafetyCANFD3Frame7(CT_ASDMSafetyCANFD3Frame7_t *userData, uint8_t *data, int length);
    // 0x68
    bool encode_ASDMSafetyCANFD3Frame4(CT_ASDMSafetyCANFD3Frame4_t *userData, uint8_t *data, int length);
    // 0x120
    bool encode_ASDMSafetyCANFD3Frame1(CT_ASDMSafetyCANFD3Frame1_t *userData, uint8_t *data, int length);
    // 0x17a
    bool encode_ASDMSafetyCANFD3Frame8(CT_ASDMSafetyCANFD3Frame8_t *userData, uint8_t *data, int length);

private:
//    Devices::Can::DeviceManager* mDeviceManager;
//    QList<quint64> mRecvIDList; //需接收的ID

//    HavalInfo mSrcInfo;
//    GeelyInfo mTarInfo;
private://haval
    CT_CSA2_t mHavalA1;
    CT_ECM9_t mHaval102;
    CT_ECM1_t mHaval111;
    CT_ESP2_t mHaval145;
    CT_CSA_1_t mHaval165;
    CT_DCT_5_t mHaval221;
    CT_ABM2_t mHaval245;
    CT_ABS3_t mHaval265;
    CT_PEPS2_t mHaval295;
    CT_HUT_FD1_t mHaval2C3;
    CT_BCM_1_t mHaval319;

private://Geely
    CT_ASDMSafetyCANFD3Frame7_t mGeely57;
    CT_ASDMSafetyCANFD3Frame4_t mGeely68;
    CT_ASDMSafetyCANFD3Frame1_t mGeely120;
    CT_ASDMSafetyCANFD3Frame8_t mGeely17A;
};

#endif // FRAMECONVER_HAVAL2GEELY_H
