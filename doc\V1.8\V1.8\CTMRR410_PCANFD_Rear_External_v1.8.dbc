VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: SRR_FR SRR_FL ADAS SRR_RR SRR_RL
VAL_TABLE_ ObjectLaneID 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_TABLE_ ObjectRefPointPos 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;


BO_ 774 SRR_Rear_LockTarget06: 6 SRR_RR
 SG_ Lock_Target_Type_06 : 44|4@0+ (1,0) [0|15] ""  ADAS
 SG_ Lock_Target_State_06 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_06 : 47|2@0+ (1,0) [0|3] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_06 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_06 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_06 : 23|8@0+ (1,-100) [-100|155] ""  ADAS
 SG_ Lock_Target_Abscissa_06 : 15|8@0+ (1,-100) [-128|127] ""  ADAS
 SG_ Lock_Target_Lane_ID_06 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 773 SRR_Rear_LockTarget05: 6 SRR_RR
 SG_ Lock_Target_Type_05 : 44|4@0+ (1,0) [0|15] ""  ADAS
 SG_ Lock_Target_State_05 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_05 : 47|2@0+ (1,0) [0|3] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_05 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_05 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_05 : 23|8@0+ (1,-100) [-100|155] ""  ADAS
 SG_ Lock_Target_Abscissa_05 : 15|8@0+ (1,-100) [-128|127] ""  ADAS
 SG_ Lock_Target_Lane_ID_05 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 772 SRR_Rear_LockTarget04: 6 SRR_RR
 SG_ Lock_Target_Type_04 : 44|4@0+ (1,0) [0|15] ""  ADAS
 SG_ Lock_Target_State_04 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_04 : 47|2@0+ (1,0) [0|3] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_04 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_04 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_04 : 23|8@0+ (1,-100) [-100|155] ""  ADAS
 SG_ Lock_Target_Abscissa_04 : 15|8@0+ (1,-100) [-128|127] ""  ADAS
 SG_ Lock_Target_Lane_ID_04 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 771 SRR_Rear_LockTarget03: 6 SRR_RR
 SG_ Lock_Target_Type_03 : 44|4@0+ (1,0) [0|15] ""  ADAS
 SG_ Lock_Target_State_03 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_03 : 47|2@0+ (1,0) [0|3] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_03 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_03 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_03 : 23|8@0+ (1,-100) [-100|155] ""  ADAS
 SG_ Lock_Target_Abscissa_03 : 15|8@0+ (1,-100) [-128|127] ""  ADAS
 SG_ Lock_Target_Lane_ID_03 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 769 SRR_Rear_LockTarget02: 6 SRR_RR
 SG_ Lock_Target_Type_02 : 44|4@0+ (1,0) [0|15] ""  ADAS
 SG_ Lock_Target_State_02 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_02 : 47|2@0+ (1,0) [0|3] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_02 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_02 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_02 : 23|8@0+ (1,-100) [-100|155] ""  ADAS
 SG_ Lock_Target_Abscissa_02 : 15|8@0+ (1,-100) [-128|127] ""  ADAS
 SG_ Lock_Target_Lane_ID_02 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 768 SRR_Rear_LockTarget01: 6 SRR_RR
 SG_ Lock_Target_Type_01 : 44|4@0+ (1,0) [0|15] ""  ADAS
 SG_ Lock_Target_State_01 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_01 : 47|2@0+ (1,0) [0|3] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_01 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_01 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_01 : 23|8@0+ (1,-100) [-100|155] ""  ADAS
 SG_ Lock_Target_Abscissa_01 : 15|8@0+ (1,-100) [-128|127] ""  ADAS
 SG_ Lock_Target_Lane_ID_01 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 704 SRR_RR_ObjectList01: 24 Vector__XXX
 SG_ RR_ObjectRefPointPos_01 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_01 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_01 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_01 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_01 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_01 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_01 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_01 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_01 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_01 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_01 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_01 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_01 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_01 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_01 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_01 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_01 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_01 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_01 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_01 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 641 SRR_RL_ObjectList02: 24 Vector__XXX
 SG_ RL_ObjectRefPointPos_02 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_02 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_02 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_02 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_02 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_02 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_02 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_02 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_02 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_02 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_02 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_02 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_02 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_02 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_02 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_02 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_02 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_02 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_02 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_02 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 705 SRR_RR_ObjectList02: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_02 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_02 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_02 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_02 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_02 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_02 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_02 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_02 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_02 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_02 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_02 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_02 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_02 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_02 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_02 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_02 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_02 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_02 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_02 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_02 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 706 SRR_RR_ObjectList03: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_03 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_03 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_03 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_03 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_03 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_03 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_03 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_03 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_03 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_03 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_03 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_03 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_03 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_03 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_03 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_03 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_03 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_03 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_03 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_03 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 707 SRR_RR_ObjectList04: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_04 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_04 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_04 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_04 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_04 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_04 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_04 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_04 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_04 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_04 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_04 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_04 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_04 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_04 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_04 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_04 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_04 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_04 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_04 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_04 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 708 SRR_RR_ObjectList05: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_05 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_05 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_05 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_05 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_05 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_05 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_05 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_05 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_05 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_05 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_05 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_05 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_05 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_05 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_05 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_05 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_05 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_05 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_05 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_05 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 709 SRR_RR_ObjectList06: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_06 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_06 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_06 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_06 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_06 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_06 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_06 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_06 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_06 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_06 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_06 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_06 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_06 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_06 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_06 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_06 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_06 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_06 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_06 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_06 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 710 SRR_RR_ObjectList07: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_07 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_07 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_07 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_07 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_07 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_07 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_07 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_07 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_07 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_07 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_07 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_07 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_07 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_07 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_07 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_07 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_07 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_07 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_07 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_07 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 711 SRR_RR_ObjectList08: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_08 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_08 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_08 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_08 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_08 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_08 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_08 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_08 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_08 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_08 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_08 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_08 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_08 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_08 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_08 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_08 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_08 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_08 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_08 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_08 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 712 SRR_RR_ObjectList09: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_09 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_09 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_09 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_09 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_09 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_09 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_09 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_09 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_09 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_09 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_09 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_09 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_09 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_09 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_09 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_09 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_09 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_09 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_09 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_09 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 713 SRR_RR_ObjectList10: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_10 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_10 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_10 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_10 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_10 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_10 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_10 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_10 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_10 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_10 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_10 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_10 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_10 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_10 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_10 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_10 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_10 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_10 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_10 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_10 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 714 SRR_RR_ObjectList11: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_11 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_11 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_11 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_11 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_11 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_11 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_11 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_11 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_11 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_11 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_11 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_11 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_11 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_11 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_11 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_11 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_11 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_11 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_11 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_11 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 715 SRR_RR_ObjectList12: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_12 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_12 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_12 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_12 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_12 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_12 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_12 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_12 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_12 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_12 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_12 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_12 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_12 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_12 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_12 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_12 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_12 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_12 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_12 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_12 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 716 SRR_RR_ObjectList13: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_13 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_13 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_13 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_13 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_13 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_13 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_13 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_13 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_13 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_13 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_13 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_13 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_13 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_13 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_13 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_13 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_13 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_13 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_13 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_13 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 717 SRR_RR_ObjectList14: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_14 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_14 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_14 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_14 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_14 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_14 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_14 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_14 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_14 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_14 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_14 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_14 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_14 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_14 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_14 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_14 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_14 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_14 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_14 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_14 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 718 SRR_RR_ObjectList15: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_15 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_15 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_15 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_15 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_15 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_15 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_15 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_15 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_15 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_15 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_15 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_15 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_15 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_15 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_15 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_15 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_15 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_15 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_15 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_15 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 719 SRR_RR_ObjectList16: 24 SRR_RR
 SG_ RR_ObjectRefPointPos_16 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RR_ObjectLink_16 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectCntr_16 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectHeadYawAgl_16 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RR_ObjectExistnc_16 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RR_ObjectDistAltitude_16 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RR_ObjectRollingCnt_16 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RR_ObjectDynProp_16 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RR_ObjectClass_16 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RR_ObjectRCS_16 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RR_ObjectWidth_16 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectLength_16 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RR_ObjectArelLat_16 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectArelLong_16 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RR_ObjectVrelLat_16 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectVrelLong_16 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RR_ObjectDistLat_16 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RR_ObjectDistLong_16 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RR_ObjectID_16 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RR_ObjectChecksum_16 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 640 SRR_RL_ObjectList01: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_01 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_01 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_01 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_01 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_01 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_01 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_01 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_01 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_01 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_01 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_01 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_01 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_01 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_01 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_01 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_01 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_01 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_01 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_01 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_01 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 642 SRR_RL_ObjectList03: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_03 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_03 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_03 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_03 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_03 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_03 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_03 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_03 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_03 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_03 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_03 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_03 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_03 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_03 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_03 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_03 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_03 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_03 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_03 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_03 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 643 SRR_RL_ObjectList04: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_04 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_04 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_04 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_04 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_04 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_04 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_04 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_04 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_04 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_04 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_04 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_04 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_04 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_04 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_04 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_04 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_04 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_04 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_04 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_04 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 644 SRR_RL_ObjectList05: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_05 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_05 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_05 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_05 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_05 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_05 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_05 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_05 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_05 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_05 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_05 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_05 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_05 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_05 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_05 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_05 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_05 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_05 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_05 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_05 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 645 SRR_RL_ObjectList06: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_06 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_06 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_06 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_06 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_06 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_06 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_06 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_06 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_06 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_06 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_06 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_06 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_06 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_06 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_06 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_06 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_06 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_06 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_06 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_06 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 646 SRR_RL_ObjectList07: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_07 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_07 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_07 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_07 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_07 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_07 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_07 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_07 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_07 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_07 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_07 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_07 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_07 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_07 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_07 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_07 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_07 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_07 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_07 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_07 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 647 SRR_RL_ObjectList08: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_08 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_08 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_08 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_08 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_08 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_08 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_08 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_08 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_08 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_08 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_08 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_08 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_08 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_08 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_08 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_08 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_08 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_08 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_08 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_08 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 648 SRR_RL_ObjectList09: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_09 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_09 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_09 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_09 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_09 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_09 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_09 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_09 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_09 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_09 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_09 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_09 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_09 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_09 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_09 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_09 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_09 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_09 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_09 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_09 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 649 SRR_RL_ObjectList10: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_10 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_10 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_10 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_10 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_10 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_10 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_10 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_10 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_10 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_10 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_10 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_10 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_10 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_10 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_10 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_10 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_10 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_10 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_10 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_10 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 650 SRR_RL_ObjectList11: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_11 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_11 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_11 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_11 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_11 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_11 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_11 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_11 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_11 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_11 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_11 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_11 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_11 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_11 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_11 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_11 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_11 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_11 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_11 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_11 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 651 SRR_RL_ObjectList12: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_12 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_12 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_12 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_12 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_12 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_12 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_12 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_12 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_12 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_12 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_12 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_12 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_12 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_12 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_12 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_12 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_12 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_12 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_12 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_12 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 652 SRR_RL_ObjectList13: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_13 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_13 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_13 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_13 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_13 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_13 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_13 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_13 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_13 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_13 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_13 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_13 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_13 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_13 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_13 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_13 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_13 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_13 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_13 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_13 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 653 SRR_RL_ObjectList14: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_14 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_14 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_14 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_14 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_14 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_14 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_14 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_14 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_14 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_14 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_14 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_14 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_14 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_14 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_14 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_14 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_14 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_14 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_14 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_14 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 654 SRR_RL_ObjectList15: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_15 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_15 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_15 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_15 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_15 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_15 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_15 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_15 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_15 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_15 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_15 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_15 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_15 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_15 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_15 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_15 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_15 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_15 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_15 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_15 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 655 SRR_RL_ObjectList16: 24 SRR_RL
 SG_ RL_ObjectRefPointPos_16 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ RL_ObjectLink_16 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectCntr_16 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectHeadYawAgl_16 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ RL_ObjectExistnc_16 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ RL_ObjectDistAltitude_16 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ RL_ObjectRollingCnt_16 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ RL_ObjectDynProp_16 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ RL_ObjectClass_16 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ RL_ObjectRCS_16 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ RL_ObjectWidth_16 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectLength_16 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ RL_ObjectArelLat_16 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectArelLong_16 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ RL_ObjectVrelLat_16 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectVrelLong_16 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ RL_ObjectDistLat_16 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ RL_ObjectDistLong_16 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ RL_ObjectID_16 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ RL_ObjectChecksum_16 : 7|8@0+ (1,0) [0|15] ""  ADAS



CM_ SG_ 774 Lock_Target_Lane_ID_06 "1 left
2 behind
3 right";
CM_ SG_ 773 Lock_Target_Lane_ID_05 "1 left
2 behind
3 right";
CM_ SG_ 772 Lock_Target_Lane_ID_04 "1 left
2 behind
3 right";
CM_ SG_ 771 Lock_Target_Lane_ID_03 "1 left
2 behind
3 right";
CM_ SG_ 769 Lock_Target_Lane_ID_02 "1 left
2 behind
3 right";
CM_ SG_ 768 Lock_Target_Lane_ID_01 "1 left
2 behind
3 right";
CM_ SG_ 704 RR_ObjectDynProp_01 "0x00: Unknown
0x01: Stationary
0x02: Reserved
0x03: Moving
0x04-0F: Reserved";
CM_ SG_ 704 RR_ObjectClass_01 "0x0:Unknown
0x1:Car
0x2:Truck
0x3:2 wheeler
0x4:Pedestrian
0x5-0xF:Reserved";
CM_ SG_ 641 RL_ObjectDynProp_02 "0x00: Unknown
0x01: Stationary
0x02: Reserved
0x03: Moving
0x04-0F: Reserved";
CM_ SG_ 641 RL_ObjectClass_02 "0x0:Unknown
0x1:Car
0x2:Truck
0x3:2 wheeler
0x4:Pedestrian
0x5-0xF:Reserved";
CM_ SG_ 640 RL_ObjectDynProp_01 "0x00: Unknown
0x01: Stationary
0x02: Reserved
0x03: Moving
0x04-0F: Reserved";
CM_ SG_ 640 RL_ObjectClass_01 "0x0:Unknown
0x1:Car
0x2:Truck
0x3:2 wheeler
0x4:Pedestrian
0x5-0xF:Reserved";
BA_DEF_ BO_  "GenericFrameRequirementNb" STRING ;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","OnEvent","Cyclic_And_OnEvent";
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 65535;
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_  "DBName" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_DEF_  "GenericFrameRequirementNb" "";
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenMsgCycleTime" 50;
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_ "BusType" "CAN FD";
BA_ "DBName" "CTMRR410_PCANFD";
BA_ "VFrameFormat" BO_ 704 14;
BA_ "VFrameFormat" BO_ 641 14;
BA_ "VFrameFormat" BO_ 705 14;
BA_ "VFrameFormat" BO_ 706 14;
BA_ "VFrameFormat" BO_ 707 14;
BA_ "VFrameFormat" BO_ 708 14;
BA_ "VFrameFormat" BO_ 709 14;
BA_ "VFrameFormat" BO_ 710 14;
BA_ "VFrameFormat" BO_ 711 14;
BA_ "VFrameFormat" BO_ 712 14;
BA_ "VFrameFormat" BO_ 713 14;
BA_ "VFrameFormat" BO_ 714 14;
BA_ "VFrameFormat" BO_ 715 14;
BA_ "VFrameFormat" BO_ 716 14;
BA_ "VFrameFormat" BO_ 717 14;
BA_ "VFrameFormat" BO_ 718 14;
BA_ "VFrameFormat" BO_ 719 14;
BA_ "VFrameFormat" BO_ 640 14;
BA_ "VFrameFormat" BO_ 642 14;
BA_ "VFrameFormat" BO_ 643 14;
BA_ "VFrameFormat" BO_ 644 14;
BA_ "VFrameFormat" BO_ 645 14;
BA_ "VFrameFormat" BO_ 646 14;
BA_ "VFrameFormat" BO_ 647 14;
BA_ "VFrameFormat" BO_ 648 14;
BA_ "VFrameFormat" BO_ 649 14;
BA_ "VFrameFormat" BO_ 650 14;
BA_ "VFrameFormat" BO_ 651 14;
BA_ "VFrameFormat" BO_ 652 14;
BA_ "VFrameFormat" BO_ 653 14;
BA_ "VFrameFormat" BO_ 654 14;
BA_ "VFrameFormat" BO_ 655 14;
VAL_ 774 Lock_Target_Lane_ID_06 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 773 Lock_Target_Lane_ID_05 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 772 Lock_Target_Lane_ID_04 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 771 Lock_Target_Lane_ID_03 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 769 Lock_Target_Lane_ID_02 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 768 Lock_Target_Lane_ID_01 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 704 RR_ObjectRefPointPos_01 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 641 RL_ObjectRefPointPos_02 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 705 RR_ObjectRefPointPos_02 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 706 RR_ObjectRefPointPos_03 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 707 RR_ObjectRefPointPos_04 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 708 RR_ObjectRefPointPos_05 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 709 RR_ObjectRefPointPos_06 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 710 RR_ObjectRefPointPos_07 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 711 RR_ObjectRefPointPos_08 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 712 RR_ObjectRefPointPos_09 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 713 RR_ObjectRefPointPos_10 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 714 RR_ObjectRefPointPos_11 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 715 RR_ObjectRefPointPos_12 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 716 RR_ObjectRefPointPos_13 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 717 RR_ObjectRefPointPos_14 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 718 RR_ObjectRefPointPos_15 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 719 RR_ObjectRefPointPos_16 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 640 RL_ObjectRefPointPos_01 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 642 RL_ObjectRefPointPos_03 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 643 RL_ObjectRefPointPos_04 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 644 RL_ObjectRefPointPos_05 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 645 RL_ObjectRefPointPos_06 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 646 RL_ObjectRefPointPos_07 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 647 RL_ObjectRefPointPos_08 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 648 RL_ObjectRefPointPos_09 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 649 RL_ObjectRefPointPos_10 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 650 RL_ObjectRefPointPos_11 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 651 RL_ObjectRefPointPos_12 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 652 RL_ObjectRefPointPos_13 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 653 RL_ObjectRefPointPos_14 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 654 RL_ObjectRefPointPos_15 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 655 RL_ObjectRefPointPos_16 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;

