﻿#ifndef HESAILIDERCONFIGDIALOG_H
#define HESAILIDERCONFIGDIALOG_H

#include <QDialog>

class HeSaiLiderWorker;

namespace Ui {
class HeSaiLiderConfigDialog;
}

class HeSaiLiderConfigDialog : public QDialog
{
    Q_OBJECT

public:
    explicit HeSaiLiderConfigDialog(HeSaiLiderWorker *worker, QWidget *parent = nullptr);
    ~HeSaiLiderConfigDialog();

private slots:
    void on_pushButtonApply_clicked();

    void on_pushButtonOK_clicked();

    void on_pushButtonCancel_clicked();

private:
    Ui::HeSaiLiderConfigDialog *ui;

    HeSaiLiderWorker *mHeSaiLiderWorker{0};
};

#endif // HESAILIDERCONFIGDIALOG_H
