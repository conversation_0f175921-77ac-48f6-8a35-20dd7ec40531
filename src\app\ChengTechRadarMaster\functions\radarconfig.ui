<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RadarConfig</class>
 <widget class="QDialog" name="RadarConfig">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>486</width>
    <height>563</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>雷达配置</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4">
   <item row="0" column="0">
    <layout class="QGridLayout" name="gridLayout">
     <item row="1" column="1">
      <widget class="QLineEdit" name="lineEdit_radarAddr_new">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>新地址：</string>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QLabel" name="label">
       <property name="text">
        <string>当前地址：</string>
       </property>
      </widget>
     </item>
     <item row="0" column="2">
      <widget class="QPushButton" name="pushButton">
       <property name="text">
        <string>配置使能</string>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>速度源：</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QComboBox" name="comboBox_spd_model">
       <item>
        <property name="text">
         <string>自动选择</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>OBD速度</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>自测速</string>
        </property>
       </item>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEdit_old_addr"/>
     </item>
     <item row="3" column="2">
      <widget class="QPushButton" name="pushButton_3">
       <property name="text">
        <string>配置</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_6">
       <property name="text">
        <string>角 度：</string>
       </property>
      </widget>
     </item>
     <item row="2" column="2">
      <widget class="QPushButton" name="pushButton_5">
       <property name="text">
        <string>配置</string>
       </property>
      </widget>
     </item>
     <item row="1" column="2">
      <widget class="QPushButton" name="pushButton_2">
       <property name="text">
        <string>配置</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QLineEdit" name="lineEdit_fix_angle"/>
     </item>
     <item row="4" column="0">
      <widget class="QLabel" name="label_7">
       <property name="text">
        <string>输出模式：</string>
       </property>
      </widget>
     </item>
     <item row="4" column="1">
      <widget class="QComboBox" name="comboBox_data_mode">
       <property name="currentIndex">
        <number>2</number>
       </property>
       <item>
        <property name="text">
         <string>RAW</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>TRK</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>RAW+TRK</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>NONE</string>
        </property>
       </item>
      </widget>
     </item>
     <item row="4" column="2">
      <widget class="QPushButton" name="pushButton_data_mode">
       <property name="text">
        <string>配置</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="0" column="1">
    <widget class="Line" name="line">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <layout class="QGridLayout" name="gridLayout_3">
     <item row="0" column="0">
      <widget class="QPushButton" name="pushButton_getVersion">
       <property name="text">
        <string>获取版本</string>
       </property>
      </widget>
     </item>
     <item row="0" column="2">
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="3" column="0" colspan="3">
      <widget class="QLabel" name="label_version">
       <property name="text">
        <string>Version：</string>
       </property>
      </widget>
     </item>
     <item row="4" column="0">
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="0" column="1">
      <widget class="QPushButton" name="pushButton_6">
       <property name="text">
        <string>获取配置</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0" colspan="3">
    <layout class="QGridLayout" name="gridLayout_2">
     <item row="0" column="0">
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>手动发送：</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1" colspan="2">
      <widget class="QLineEdit" name="lineEdit_send_id">
       <property name="placeholderText">
        <string>CAN Id</string>
       </property>
      </widget>
     </item>
     <item row="0" column="3">
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>238</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="1" column="0">
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="1" column="1" colspan="3">
      <widget class="QLineEdit" name="lineEdit_send_data">
       <property name="inputMask">
        <string>HH HH HH HH HH HH HH HH</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="2" column="1">
      <widget class="QPushButton" name="pushButton_4">
       <property name="text">
        <string>发送</string>
       </property>
      </widget>
     </item>
     <item row="2" column="2" colspan="2">
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>278</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="label_5">
       <property name="text">
        <string>指定接收：</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1" colspan="2">
      <widget class="QLineEdit" name="lineEdit_rec_id">
       <property name="placeholderText">
        <string>CAN Id</string>
       </property>
      </widget>
     </item>
     <item row="3" column="3">
      <spacer name="horizontalSpacer_6">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>248</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="4" column="1" colspan="3">
      <widget class="QLineEdit" name="lineEdit_rec_data">
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="4" column="0">
      <widget class="QLabel" name="label_frame_number">
       <property name="layoutDirection">
        <enum>Qt::LeftToRight</enum>
       </property>
       <property name="text">
        <string>--</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="3" column="0" colspan="3">
    <widget class="Line" name="line_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item row="4" column="0" colspan="3">
    <widget class="QPlainTextEdit" name="plainTextEdit_log"/>
   </item>
   <item row="5" column="2">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
   <item row="2" column="0" colspan="3">
    <widget class="QPushButton" name="pushButton_save">
     <property name="text">
      <string>存储</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>RadarConfig</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>RadarConfig</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
