DEFINES += PC_DBG_FW

HEADERS += \
    alg/track/rdp_main_ekf.h \
    alg/track/rdp_matrixMath.h \
    alg/track/rdp_pre_ekf.h \
    alg/track/rdp_track_common.h \
    alg/track/rdp_track_config.h \
    alg/track/rdp_main_ekf.h \
    alg/track/rdp_matrixMath.h \
    alg/track/rdp_pre_ekf.h \
    alg/track/rdp_track_common.h \
    alg/track/rdp_track_config.h \
    alg/track/rdp_clth_radar_lib.h \
    alg/track/rdp_interface.h \
    alg/track/rdp_kf_config.h \
    alg/track/rdp_kf_init.h \
    alg/track/rdp_kf_track.h \
    alg/track/rdp_kf_types.h \
    alg/track/rdp_track_listlib.h \
    alg/track/rdp_track_struct.h \
    alg/track/rdp_types.h \
    app/apar/apar_types.h \
    app/app_common/app_common_types.h \
    app/app_common/app_pcan_protocol.h \
    app/can_com/can_msg.h \
    app/can_com/msg_struct_typedef.h \
    app/em/adas_alm.h \
    app/em/adas_alm_types.h \
    app/em/adas_interface.h \
    app/em/adas_sideLine.h \
    app/em/adas_standard_params.h \
    app/em/adas_state_machine.h \
    app/em/adas_tempdefine.h \
    app/em/adas_types.h \
    app/protocol/com_interface.h \
    app/protocol/com_types.h \
    app/system_mgr/typedefs.h \
    app/vdy/vdy_interface.h \
    app/vdy/vdy_types.h \
    other/pc_vr_mcu.h \
    other/temp.h

SOURCES += \
    alg/track/rdp_main_ekf.c \
    alg/track/rdp_matrixMath.c \
    alg/track/rdp_pre_ekf.c \
    alg/track/rdp_main_ekf.c \
    alg/track/rdp_matrixMath.c \
    alg/track/rdp_pre_ekf.c \
    alg/track/rdp_clth_radar_lib.c \
    alg/track/rdp_kf_init.c \
    alg/track/rdp_kf_track.c \
    alg/track/rdp_Main.c \
    alg/track/rdp_track_listlib.c \
    app/em/adas_alm_bsd.c \
    app/em/adas_alm_dow.c \
    app/em/adas_alm_lca.c \
    app/em/adas_alm_rctab.c \
    app/em/adas_alm_rcw.c \
    app/em/adas_alm_types.c \
    app/em/adas_function.c \
    app/em/adas_sideLine.c \
    app/em/adas_state_machine.c \
    app/em/adas_tempdefine.c \
    other/pc_vr_mcu.c \
    other/temp.c
