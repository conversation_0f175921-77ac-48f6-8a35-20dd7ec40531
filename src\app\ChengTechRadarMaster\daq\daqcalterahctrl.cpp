﻿#include "daqcalterahctrl.h"
#include "ui_daqcalterahctrl.h"
#include "devices/ideviceworker.h"
#include "master/savemanager.h"
#include "utils/settingshandler.h"
#include "devices/devicemanager.h"

#include <QIntValidator>
#include <QDebug>
#include <QDateTime>
#include <QFileDialog>

DAQCalterahCtrl::DAQCalterahCtrl( Core::SaveManager* saveMgr, Devices::Can::DeviceManager* devMgr, QWidget *parent ) :
    QWidget(parent),
    ui(new Ui::DAQCalterahCtrl)
{
    ui->setupUi(this);

//    this->setMaximumSize( QSize( 806, 410 ) );
    this->setWindowTitle( QString( "ADC" ) );
    ui->sizeLineEdit->setValidator( new QRegExpValidator( QRegExp( "[0-9\\.]+$" ) ) );//只能输入数字和小数点
    ui->cntLineEdit->setValidator( new QIntValidator( 1, 1024 * 1024 ) );    //最大数据1T = 1024G = 1024 * 1024MB

    ui->comboBoxRadarType->addItem("Forward");
    ui->comboBoxRadarType->addItem("Angular");

    ui->protocolComboBox->addItem("Alps");
    ui->protocolComboBox->addItem("Alps Pro");
    ui->protocolComboBox->addItem("DC1000");

    ui->comboBoxDataType->addItem("ADC");
    ui->comboBoxDataType->addItem("1DFFT");
    ui->comboBoxDataType->addItem("2DFFT");

    ui->channelComboBox->addItem("CAN 0" );
    ui->channelComboBox->addItem("CAN 1" );

    ui->nextPushButton->setEnabled( false );
    ui->collectPushButton->setEnabled(false);
    ui->HILPushButton->setEnabled(false);

    mSaveMgr = saveMgr;
    mDeviceManager = devMgr;
    //mDevice = device;
    mDAQ = new DAQ::DAQCalterah( saveMgr, mDeviceManager->deviceWorker() );
    mThread = new QThread;
    mDAQ->moveToThread( mThread );
    mThread->start();

    connect(mDAQ, SIGNAL(destroyed()), mThread, SLOT(quit()));
    connect(mThread, SIGNAL(finished()), mThread, SLOT(deleteLater()) );

    //connect( ui->serverPushButton, SIGNAL( clicked() ), mDAQ, SLOT( openAndCloseTcpServer() ) );
    connect( this, &DAQCalterahCtrl::changeProtocol, mDAQ, &DAQ::DAQCalterah::changeProtocol );
    connect( this, &DAQCalterahCtrl::startOrStopTcpServer, mDAQ, &DAQ::DAQCalterah::openOrCloseServer );
    connect( ui->collectPushButton, &QPushButton::clicked, this, &DAQCalterahCtrl::collectStart);
    connect( mDAQ, &DAQ::DAQCalterah::serverStateChanged , this, &DAQCalterahCtrl::serverStateChanged);
    connect( mDAQ, &DAQ::DAQCalterah::clientConnected, this, &DAQCalterahCtrl::clientConnected);
    connect( mDAQ, &DAQ::DAQCalterah::showMsg, this, &DAQCalterahCtrl::showMsg);
    connect( this, &DAQCalterahCtrl::startCollect, mDAQ, &DAQ::DAQCalterah::startCollect);
    connect( this, &DAQCalterahCtrl::stopCollect, mDAQ, &DAQ::DAQCalterah::stopCollect);
    connect( this, &DAQCalterahCtrl::startHIL, mDAQ, &DAQ::DAQCalterah::startHIL);
    connect( mDAQ, &DAQ::DAQCalterah::collectStateChanged, this, &DAQCalterahCtrl::collectFinished);
    connect( mDAQ, QOverload<bool>::of(&DAQ::DAQCalterah::HILFinished), this, &DAQCalterahCtrl::HILFinished);
    connect( this, &DAQCalterahCtrl::HILNextFrame, mDAQ, &DAQ::DAQCalterah::HILData);

    loadSettings();
}

DAQCalterahCtrl::~DAQCalterahCtrl()
{
    delete ui;
    delete mDAQ;
}

void DAQCalterahCtrl::saveSettings()
{
    SETTINGS_SET_VALUE("DAQCalterahCtrl/AnlyIP", ui->checkBoxAnlyIP->isChecked() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/IP", ui->listenLineEdit->text() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/Port", ui->lineEditPort->text() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/CanChannel", ui->channelComboBox->currentIndex() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/Protocol", ui->protocolComboBox->currentIndex() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/RadarType", ui->comboBoxRadarType->currentIndex() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/DataType", ui->comboBoxDataType->currentIndex() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/FrameSize", ui->sizeLineEdit->text() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/FrameSizeByte", ui->checkBoxByte->isChecked() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/FrameCount", ui->cntLineEdit->text() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/CarFile", ui->carFileLineEdit->text() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/ADCFile", ui->ADCFileLineEdit->text() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/CfgFile", ui->cfgFileLineEdit->text() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/IsZeer", ui->ZeerFormatCheckBox->isChecked() );
    SETTINGS_SET_VALUE("DAQCalterahCtrl/SingleHil", ui->singleFrameCheckBox->isChecked()  );

}

void DAQCalterahCtrl::loadSettings()
{
    ui->checkBoxAnlyIP->setChecked(SETTINGS_GET_VALUE("DAQCalterahCtrl/AnlyIP", false).toBool());
    ui->listenLineEdit->setText(SETTINGS_GET_VALUE("DAQCalterahCtrl/IP", "*************").toString());
    ui->lineEditPort->setText(SETTINGS_GET_VALUE("DAQCalterahCtrl/Port", "5001").toString());
    ui->channelComboBox->setCurrentIndex(SETTINGS_GET_VALUE("DAQCalterahCtrl/CanChannel", 0).toInt());
    ui->protocolComboBox->setCurrentIndex(SETTINGS_GET_VALUE("DAQCalterahCtrl/Protocol", 0).toInt());
    ui->comboBoxRadarType->setCurrentIndex(SETTINGS_GET_VALUE("DAQCalterahCtrl/RadarType", 0).toInt());
    ui->comboBoxDataType->setCurrentIndex(SETTINGS_GET_VALUE("DAQCalterahCtrl/DataType", 0).toInt());
    ui->sizeLineEdit->setText(SETTINGS_GET_VALUE("DAQCalterahCtrl/FrameSize", "").toString());
    ui->checkBoxByte->setChecked(SETTINGS_GET_VALUE("DAQCalterahCtrl/FrameSizeByte", false).toBool());
    ui->cntLineEdit->setText(SETTINGS_GET_VALUE("DAQCalterahCtrl/FrameCount", "").toString());
    ui->carFileLineEdit->setText(SETTINGS_GET_VALUE("DAQCalterahCtrl/CarFile", "").toString());
    ui->ADCFileLineEdit->setText(SETTINGS_GET_VALUE("DAQCalterahCtrl/ADCFile", "").toString());
    ui->cfgFileLineEdit->setText(SETTINGS_GET_VALUE("DAQCalterahCtrl/CfgFile", "").toString());
    ui->ZeerFormatCheckBox->setChecked( SETTINGS_GET_VALUE("DAQCalterahCtrl/IsZeer", false).toBool() );
    ui->singleFrameCheckBox->setChecked( SETTINGS_GET_VALUE("DAQCalterahCtrl/SingleHil", false).toBool() );
}

void DAQCalterahCtrl::serverStateChanged(bool opened)
{
    ui->serverPushButton->setText( QString::fromLocal8Bit(opened ? "停止服务" : "启动服务" ));
    showMsg( opened ? "server opened" : "server closed" );
}

void DAQCalterahCtrl::on_serverPushButton_clicked()
{
    QString ip = ui->listenLineEdit->text();
    quint32 port = ui->lineEditPort->text().toUInt();

    quint8 protocol = ui->protocolComboBox->currentIndex();
    emit changeProtocol(protocol);
    emit startOrStopTcpServer( ip, port, ui->checkBoxAnlyIP->isChecked() );
}

void DAQCalterahCtrl::clientConnected(unsigned long addr, unsigned short port, bool connected)
{
    QString msg =  QString(connected ? "connected" : "disconnected");
    msg += QString(": %1.%2.%3.%4 %5")
            .arg((addr & 0xFF000000) >> 24).arg((addr & 0x00FF0000) >> 16)
            .arg((addr & 0x0000FF00) >> 8).arg(addr & 0x000000FF).
            arg( port );
    showMsg( msg );
    ui->labelClient->setText(msg);
    ui->collectPushButton->setEnabled(connected);
    ui->HILPushButton->setEnabled(connected);
}

void DAQCalterahCtrl::collectStart()
{
    if( mDAQ->isRunning() ){
        emit stopCollect();
    } else {
        //if( !mDevice->isOpened() ){
        if( !mDeviceManager->isOpened() ){
            showMsg( QString("not open device!") );
            return;
        }

        if( !checkFrameParam() || !checkCollectParam() )
            return;

        mDAQ->setDevice( mDeviceManager->deviceWorker() );
        quint64 frameByte = 0;
        if (ui->checkBoxByte->isChecked()) {
            frameByte = ui->sizeLineEdit->text().toUInt();
        } else {
            frameByte = ui->sizeLineEdit->text().toDouble() * 1024 * 1024;
        }
        quint64 frameCnt = ui->cntLineEdit->text().toUInt();
        quint8 channel = ui->channelComboBox->currentIndex();
        quint8 collectType = ui->comboBoxDataType->currentIndex();
        QStringList cfgFiles;
        if( ui->protocolComboBox->currentIndex() == 1 ){
            cfgFiles = ui->cfgFileLineEdit->text().split( ';' );
        }

        //保存CAN数据
        if( mSaveMgr->isSaveing() )
            mSaveMgr->stopSave();
        qDebug() << __FUNCTION__ << __LINE__ << ui->protocolComboBox->currentIndex();
        mSaveMgr->startSave(ui->protocolComboBox->currentIndex() == 2);

        ui->msgTextEdit->clear();
        //保存adc数据
        emit startCollect( channel, collectType, frameByte, frameCnt, cfgFiles );
    }
}

void DAQCalterahCtrl::collectFinished(int state)
{
    switch (state) {
    case DAQ::DAQCalterah::Running:
        ui->collectPushButton->setText(QString::fromLocal8Bit("停止采集"));
        break;
    case DAQ::DAQCalterah::Stoped:
    case DAQ::DAQCalterah::Failed:
    case DAQ::DAQCalterah::Success:
        ui->collectPushButton->setText(QString::fromLocal8Bit("开始采集"));
        if( mSaveMgr->isSaveing() ) {
            qDebug() << __FUNCTION__ << __LINE__ << "message";
            mSaveMgr->stopSave();
            qDebug() << __FUNCTION__ << __LINE__ << "message";
        }
        break;
    default:
        break;
    }
}

void DAQCalterahCtrl::showMsg(const QString &msg)
{
    QString text = QString("%1 : %2")
            .arg(QDateTime::currentDateTime().toLocalTime().toString( "hh:mm:ss:zzz" )).arg(msg);
    ui->msgTextEdit->append( text );
    ui->msgTextEdit->moveCursor( QTextCursor::End );
}

void DAQCalterahCtrl::on_selCarFilePushButton_clicked()
{
    QString filename = QFileDialog::getOpenFileName(this,
                                                    "car file",
                                                    ui->carFileLineEdit->text(),
                                                    /*tr("binary (*.binary)" )*/
                                                    tr("blf (*.blf)" ) );
    if ( !filename.isEmpty() ){
        ui->carFileLineEdit->setText( filename );
    }
}

void DAQCalterahCtrl::on_selADCFilePushButton_clicked()
{
    QString filename = QFileDialog::getOpenFileName(this,
                                                    "adc file",
                                                    ui->ADCFileLineEdit->text(),
                                                    tr("dat (*.dat)" ) );
    if ( !filename.isEmpty() ){
        ui->ADCFileLineEdit->setText( filename );
    }
}

void DAQCalterahCtrl::on_HILPushButton_clicked()
{
    if( mDAQ->isRunning() ){
    } else {
        if( !mDeviceManager->isOpened() ){
            showMsg( QString("not open device!") );
            return;
        }

        if( !checkFrameParam() || !checkHILParam() )
            return;

        mDAQ->setDevice( mDeviceManager->deviceWorker() );
        ui->HILPushButton->setEnabled( false );
        quint64 frameByte = 0;
        if (ui->checkBoxByte->isChecked()) {
            frameByte = ui->sizeLineEdit->text().toUInt();
        } else {
            frameByte = ui->sizeLineEdit->text().toDouble() * 1024 * 1024;
        }
        quint64 frameCnt = ui->cntLineEdit->text().toUInt();
        quint8 channel = ui->channelComboBox->currentIndex();
        QString carFile = ui->carFileLineEdit->text();
        QString adcFile = ui->ADCFileLineEdit->text();
        bool bZeerFormat = ui->ZeerFormatCheckBox->isChecked();
        QStringList cfgFiles = ui->cfgFileLineEdit->text().split( ';' );
        quint8 calterahType = ui->protocolComboBox->currentIndex();
        quint8 collectType = ui->comboBoxDataType->currentIndex();
        emit startHIL( calterahType, channel, collectType, frameByte, frameCnt, cfgFiles, carFile, true, adcFile, bZeerFormat );

        ui->singleFrameCheckBox->setEnabled( false );
    }
}

void DAQCalterahCtrl::HILFinished()
{
    ui->HILPushButton->setEnabled( true );
    ui->singleFrameCheckBox->setEnabled( true );
    ui->nextPushButton->setEnabled( false );
}

bool DAQCalterahCtrl::checkFrameParam()
{
    float frameSize = ui->sizeLineEdit->text().toFloat();
    quint32 frameCnt = ui->cntLineEdit->text().toUInt();
    if( 0 == frameSize || 0 == frameCnt ){
        showMsg( "frame param error");
        return false;
    }
    return true;
}

bool DAQCalterahCtrl::checkHILParam()
{
    QString carFile = ui->carFileLineEdit->text();
    QString adcFile = ui->ADCFileLineEdit->text();
    QString cfgFile = ui->cfgFileLineEdit->text();

    if( carFile.length() == 0 || 0 == adcFile.length() || 0 == cfgFile.length() ){
        showMsg( "HIL param error");
        return false;
    }
    return true;
}

bool DAQCalterahCtrl::checkCollectParam()
{
//    QString cfgFile = ui->cfgFileLineEdit->text();
//    if( ui->protocolComboBox->currentIndex() == 1 && cfgFile.length() == 0 ){
//        showMsg( "Collect param error");
//        return false;
//    }
    return true;
}

void DAQCalterahCtrl::on_selCfgFilepushButton_clicked()
{
    QStringList filenames;
    if( ui->protocolComboBox->currentIndex() != 0 ){
        filenames = QFileDialog::getOpenFileNames(this, "Open Profile Config File", ui->cfgFileLineEdit->text(), tr("Text (*.h)"));
    }else{
        filenames = QFileDialog::getOpenFileNames(this, "Open Profile Config File", ui->cfgFileLineEdit->text(), tr("Text (*.hxx)"));
    }
    if (!filenames.size()){
        return;
    }
    ui->cfgFileLineEdit->setText(filenames.join(";"));
}

void DAQCalterahCtrl::on_singleFrameCheckBox_stateChanged(int arg1)
{
//    qDebug() << __FUNCTION__ << __LINE__ << arg1;
    int pauseFrameNum = arg1 ? 1 : -1;
    mDAQ->setHilPauseFramNum( pauseFrameNum );
}

void DAQCalterahCtrl::on_nextPushButton_clicked()
{
    //mDAQ->HILData();
    emit HILNextFrame();
}

void DAQCalterahCtrl::on_protocolComboBox_currentIndexChanged(int index)
{
    ui->cfgFileLineEdit->setText(""); //清空配置文件
}
