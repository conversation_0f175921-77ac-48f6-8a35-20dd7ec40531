﻿#include "hesailiderconfigdialog.h"
#include "ui_hesailiderconfigdialog.h"

#include "hesailiderworker.h"

HeSaiLiderConfigDialog::HeSaiLiderConfigDialog(HeSaiLiderWorker *worker, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::HeSaiLiderConfigDialog),
    mHeSaiLiderWorker(worker)
{
    ui->setupUi(this);

    const HeSaiLiderWorker::Settings &settings = mHeSaiLiderWorker->getConfig();
    ui->lineEditMountingHeight->setText(QString::number(settings.mMountingHeight, 'f', 2));
    ui->lineEditLongitudinalDistanceCompensation->setText(QString::number(settings.mLongitudinalDistanceCompensation, 'f', 2));
    ui->lineEditLateralDistanceCompensation->setText(QString::number(settings.mLateralDistanceCompensation, 'f', 2));
    ui->lineEditUpperFilterX->setText(QString::number(settings.mUpperFilterX, 'f', 2));
    ui->lineEditLowerFilterX->setText(QString::number(settings.mLowerFilterX, 'f', 2));
    ui->lineEditUpperFilterY->setText(QString::number(settings.mUpperFilterY, 'f', 2));
    ui->lineEditLowerFilterY->setText(QString::number(settings.mLowerFilterY, 'f', 2));
    ui->lineEditUpperFilterZ->setText(QString::number(settings.mUpperFilterZ, 'f', 2));
    ui->lineEditLowerFilterZ->setText(QString::number(settings.mLowerFilterZ, 'f', 2));
    ui->lineEditCalibrationAngle->setText(QString::number(settings.mCalibrationAngle, 'f', 2));
}

HeSaiLiderConfigDialog::~HeSaiLiderConfigDialog()
{
    delete ui;
}

void HeSaiLiderConfigDialog::on_pushButtonApply_clicked()
{
    HeSaiLiderWorker::Settings settings;
    settings.mMountingHeight =                   ui->lineEditMountingHeight->text().toFloat();
    settings.mLongitudinalDistanceCompensation = ui->lineEditLongitudinalDistanceCompensation->text().toFloat();
    settings.mLateralDistanceCompensation =      ui->lineEditLateralDistanceCompensation->text().toFloat();
    settings.mUpperFilterX =                ui->lineEditUpperFilterX->text().toFloat();
    settings.mLowerFilterX =                ui->lineEditLowerFilterX->text().toFloat();
    settings.mUpperFilterY =                ui->lineEditUpperFilterY->text().toFloat();
    settings.mLowerFilterY =                ui->lineEditLowerFilterY->text().toFloat();
    settings.mUpperFilterZ =                ui->lineEditUpperFilterZ->text().toFloat();
    settings.mLowerFilterZ =                ui->lineEditLowerFilterZ->text().toFloat();
    settings.mCalibrationAngle =                 ui->lineEditCalibrationAngle->text().toFloat();

    mHeSaiLiderWorker->setConfig(settings);
}

void HeSaiLiderConfigDialog::on_pushButtonOK_clicked()
{
    on_pushButtonApply_clicked();
    accept();
}

void HeSaiLiderConfigDialog::on_pushButtonCancel_clicked()
{
    reject();
}
