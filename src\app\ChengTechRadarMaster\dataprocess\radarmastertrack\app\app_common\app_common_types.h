﻿
#ifndef _APP_COMMON_TYPES_H_
#define _APP_COMMON_TYPES_H_

#ifndef PC_DBG_FW
#include "typedefs.h"
#else
#include "app/system_mgr/typedefs.h"
#endif


// typedef struct signalHeader
// {
//     uint32_t uiTimeStamp;
//     uint16_t uiMeasurementCounter;
// } SignalHeader_t;

// typedef struct APP_signalHeader
// {
//     uint32_t uiTimeStamp;
//     uint32_t uiMeasurementCounter;
// } APP_signalHeader_t;

#endif
