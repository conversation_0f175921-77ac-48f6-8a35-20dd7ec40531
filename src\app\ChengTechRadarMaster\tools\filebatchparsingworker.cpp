﻿#include "filebatchparsingworker.h"

#include "analysis/analysisworker.h"
#include "analysis/analysissaveworker.h"
#include <devices/deviceworkerbinfile.h>

#include <QDir>
#include <QDateTime>
#include <QDebug>

FileBatchParsingWorker::FileBatchParsingWorker(QObject *parent) : QObject(parent)
{
}

bool FileBatchParsingWorker::start(const QString &dataPath, const QString &savePath, int fileType, bool deleteOld)
{
    qDebug() << __FUNCTION__ << __LINE__ << mGenerating << mErrorString;
    if (mGenerating) {
        mErrorString = QString::fromLocal8Bit("文件正在生成，请稍后重试");
        return true;
    }

    mDataPath = dataPath;
    mSavePath = savePath;
    mDeleteOld = deleteOld;

    if (!QDir(mSavePath).exists()) {
        mErrorString = QString::fromLocal8Bit("保存路径不存在，请更换路径");
        return false;
    }

    mFilesText.clear();
    mFiles.clear();
    QFileInfoList fileInfoList = QDir(dataPath).entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot | QDir::NoSymLinks);
    foreach (const QFileInfo info, fileInfoList) {
        QString canDir = info.absoluteFilePath().append("/CAN");
        if (QFileInfo(canDir).isDir()) {
            mFilesText += canDir + "\n";
            QFileInfoList canFileInfoList = QDir(canDir).entryInfoList(QDir::Files | QDir::NoDotAndDotDot | QDir::NoSymLinks);
            foreach (const QFileInfo info, canFileInfoList) {
                if (info.isFile() && (fileType == 0 ? info.suffix() == "asc" :  info.suffix() == "blf")) {
                QString dataFile = info.absoluteFilePath();
                mFilesText += dataFile + "\n";
                mFiles.append(dataFile);
                }
            }
        }
    }

    mGenerating = mFiles.size();

    if (mGenerating) {
        emit started();
    } else {
        mErrorString = QString::fromLocal8Bit("所选择目录没有文件！");
    }

    return mGenerating;
}

bool FileBatchParsingWorker::stop()
{
    if (mDeviceWorker->isOpened()) {
        mDeviceWorker->close();
    }
    mGenerating = false;

    return  true;
}

void FileBatchParsingWorker::generate()
{
    if (!mDeviceWorker && !mAnalysisWorker && !mAnalysisSaveWorker) {
        mDeviceWorker = new Devices::Can::DeviceWorkerBinFile(true);
        mAnalysisWorker = new Analysis::AnalysisWorker;
        mAnalysisSaveWorker = new Analysis::AnalysisSaveWorker(mAnalysisWorker);
        connect(mDeviceWorker, &Devices::Can::IDeviceWorker::frameRecieved,
                mAnalysisWorker, &Analysis::AnalysisWorker::canFrame, Qt::DirectConnection);
        connect(mAnalysisSaveWorker, &Analysis::AnalysisSaveWorker::saveStarted,
                this, &FileBatchParsingWorker::message);
    }

    Devices::Can::DeviceSettings settings;
    settings.mDeviceManufacturer = Devices::Can::DeviceSettings::FILE;
    mGeneratedCount = 0;
    for (int i = 0; i < mFiles.size() && mGenerating; ++i) {
        const QString &file = mFiles[i];
        QString saveDir = QFileInfo(QFileInfo(QFileInfo(file).absoluteDir().absolutePath()).absoluteDir().absolutePath()).baseName();
        QString saveText = QFileInfo(file).baseName().replace("CanData ", "");
        QDateTime saveTime = QDateTime::fromString(saveText, "yyyy-MM-dd hh-mm-ss-zzz");
        if (!saveTime.isValid()) {
            continue;
        }
        QString savePath = QString("%1/%2").arg(mSavePath).arg(saveDir);
        QString text = QString::fromLocal8Bit("%1:\n%2\n%3\n")
                     .arg(i, 4)
                     .arg(file)
                     .arg(savePath);
        qDebug() << __FUNCTION__ << __LINE__ << text;
        emit message(text);
        QDir logDir(savePath);
        if (logDir.exists() && mDeleteOld) {
            QDir(savePath).removeRecursively();
        }
        if (!logDir.exists()){
            logDir.setPath("");
            if (!logDir.mkpath(savePath)) {
                continue;
            }
        }
        mAnalysisSaveWorker->startSave(savePath, saveTime, true);

        settings.mDeviceFileSettings.clear();
        Devices::Can::DeviceFileSettings fileSettings;
        fileSettings.mFilenames << file;
        settings.mDeviceFileSettings << fileSettings ;
        mDeviceWorker->setDeviceChannelSettings(settings);
        mDeviceWorker->open();

        mDeviceWorker->run();

        mDeviceWorker->close();
        mAnalysisSaveWorker->stopSave();
    }

    qDebug() << __FUNCTION__ << __LINE__ << mGenerating;
    mGenerating = false;

    emit stoped();
}
