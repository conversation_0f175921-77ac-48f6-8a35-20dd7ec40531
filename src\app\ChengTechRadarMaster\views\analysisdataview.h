﻿#ifndef ANALYSISDATAVIEW_H
#define ANALYSISDATAVIEW_H

#include "analysisdataviewi.h"
#include "views_global.h"
#include "analysis/analysisdata.h"
#include "targetmonitor.h"

#include <QDockWidget>
#include <QTimer>

class QPushButton;
class QLabel;
class QHBoxLayout;

class AlarmWidget;

namespace Views {
namespace AnalysisView {

class AnalysisDataViewTitleBar;
class VehicleDataModel;
class AlarmDataModel;

class VIEWS_EXPORT AnalysisDataView : public AnalysisDataViewI
{
    Q_OBJECT

public:
    explicit AnalysisDataView(quint8 radarID, AnalysisData *analysisData, QWidget *parent = nullptr);
    ~AnalysisDataView();

    int radarID() const;

    QAction *dbcConfigAction() const { return mActionDBCConfig; }

    void analysisTargets(quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &targets);
    void analysisRadarData(/*TargetModel*/int targetModel, quint8 radarID, AnalysisData *analysisData);

    void startView();
    void stopView();

    void showFaultInfo( bool bShow );
    void showVersionInfo( bool bShow );
    void showAngleInfo( bool bShow );
    void updateRadarResetCount( quint64 count, quint8 channel );

    void setViewAnalysisTypes(int fType, bool moving, bool continuous, const ViewTypes &types) override;

signals:
    void radarIDChanged(quint64 radarID);
    void rawMeasurementCountError(quint64 history, quint64 current);
    void monitorTarget(/*AnalysisFrameType*/int frameType, const Target &target);

private slots:
    void viewTypeChanged() override;

    void changedMonitorTargetID(int frameType, quint16 id);
    void frameBreaking();
    void on_comboBoxRadarID_currentIndexChanged(const QString &arg1);

    void monitoringTrackTarget();

private:
    void setupUi();

    QLabel *mLabelErrorInformation0x4DN{0};
    QLabel *mLabelErrorInformation0x4EN{0};

    QLabel *mLabelRadarResetCount{0};                   ///< 复位次数
    QLabel *mLabelEstablishedAngle{0};                  ///< 安装角(产线安装角/自标定安装角)
    QLabel *mLabelEndFrameTime{0};                      ///< 结束帧时间
    QLabel *mLabelMeasurementCount{0};                  ///< 测量计数
    QLabel *mLabelResponseTaskCycleTime{0};             ///< 响应任务周期时间
    QLabel *mLabelVehicleGear{0};                       ///< 档位
    QLabel *mLabelVehicleSpeedInMeter{0};               ///< 车速m/s
    QLabel *mLabelVehicleSpeedInKilometer{0};           ///< 车速km/h
    QLabel *mLabelVehicleRadious{0};                    ///< 转弯半径
    QLabel *mLabelVehicleYawRate{0};                    ///< 横摆角速度
    QLabel *mLabelVehicleTime{0};                       ///< 车身时间
    QLabel *mTarget16Timestamp{0};                      ///< 16个目标时间
    QLabel *mLabelBlockagePercentRaw{0};                ///< 遮挡程度（原始点）
    QLabel *mLabelBlockagePercentTrack{0};              ///< 遮挡程度（跟踪点）
    QLabel *mLabelInterferencePercentRaw{0};            ///< 干扰程度（原始点）
    QLabel *mLabelInterferencePercentTrack{0};          ///< 干扰程度（跟踪点）
    QLabel *mLabelAutoALNSts{0};                        ///< 自标定运行状态
    QLabel *mLabelFrequencyHoppingState{0};             ///< 调频状态/当前工作模式
    QLabel *mLabelTunnelsceneStateRaw{0};               ///<  隧道场景标记位（原始点）/场景标识
    QLabel *mLabelTunnelsceneStateTrack{0};             ///<  隧道场景标记位（跟踪点）
    QLabel *mLabelWaveType{0};                          ///< AB类型
    QLabel *mLabelNoiseCurrent{0};                      ///< 电流噪声
    QLabel *mLabelNoiseGlobal{0};                       ///< 全局噪声
    QLabel *mLabelTemperature{0};                       ///< 雷达温度

    AlarmWidget *mAlarmWidgetELK{0};
    AlarmWidget *mAlarmWidgetBSD{0};
    AlarmWidget *mAlarmWidgetLCA{0};
    AlarmWidget *mAlarmWidgetDOWR{0};
    AlarmWidget *mAlarmWidgetDOWF{0};
    AlarmWidget *mAlarmWidgetFCTA{0};
    AlarmWidget *mAlarmWidgetFCTB{0};
    AlarmWidget *mAlarmWidgetRCTA{0};
    AlarmWidget *mAlarmWidgetRCTB{0};
    AlarmWidget *mAlarmWidgetRCW{0};
    AlarmWidget *mAlarmWidgetJA{0};
    QLabel *mLabelAlarmTTC{0};
    QLabel *mLabelAlarmDDCI{0};

    QLabel *mLabelRadarVersion{0};                      ///< 雷达版本
    QLabel *mLabelFrameCount{0};                        ///< 帧数统计

    QAction *mActionDBCConfig{0};

    bool mFrameBreakingFirst{true};
    QTimer mTimerFrameBreaking{0};
    CalculatorConfig mCalculatorConfig[FrameTargetCount];

    quint8 mRadarID{0};
    quint64 mRawMeasurementCount{0};
//    bool mShowErrorInfomation{false};

    AnalysisData *mAnalysisData{0};       // 角雷达
};

} // namespace AnalysisView
} // namespace Views

#endif // ANALYSISDATAVIEW_H
