﻿#include "interpolationbydho.h"

#include <QDebug>

namespace Analysis {


bool InterpolationBYDHO::isTrackFrame(int radarID, int channelRadarID[5], const Devices::Can::CanFrame &frame)
{
    int _radarID = 4;

    if (frame.channelIndex() >= 5) {
        return false;
    }
    _radarID = channelRadarID[frame.channelIndex()];
    if (_radarID < 4 || _radarID > 7) {
        return false;
    }

    switch (frame.id())
    {
    case 0x659:
    case 0x65A:
    case 0x65B:
    case 0x65C:
    case 0x65D:
    case 0x65E:
    case 0x65F:
    case 0x660:
    case 0x661:
    case 0x662:
    case 0x663:
    case 0x664:
    case 0x665:
    case 0x666:
    case 0x667:
    case 0x668:
    case 0x669:
    case 0x66A:
    case 0x66B:
    case 0x66C:
    case 0x66D:
    case 0x66E:
        break;
    default:
        return false;
    }

    return radarID == _radarID;
}

void InterpolationBYDHO::saveTimestamps(Devices::Can::FrameTimestamps *timestamps, const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.timestemp() << QDateTime::fromMSecsSinceEpoch(frame.timestemp());
    const uint8_t *data = (const uint8_t *)frame.data().data();
    Devices::Can::FrameTimestamp *timestamp = timestamps->mTimestamps + timestamps->mCount;
        switch (frame.id()) {
        case 0x659:
        case 0x65A:
        case 0x65B:
        case 0x65C:
        case 0x65D:
        case 0x65E:
        case 0x65F:
        case 0x660:
        case 0x661:
        case 0x662:
        case 0x663:
        case 0x664:
        case 0x665:
        case 0x666:
        case 0x667:
        case 0x668:
        case 0x669:
        case 0x66A:
        case 0x66B:
        case 0x66C:
        {
            timestamp->msgCounter = data[2];
            timestamp->frameTimestamps[frame.id() - 0x659] = frame.timestemp();
            timestamp->valids[frame.id() - 0x659] = true;
            timestamp->channel = frame.channelIndex();
        }
        break;
        case 0x66D:
        {
            uint64_t mesTimestamp = ((data[3] & 0xFFU) + (((uint32_t)data[4]) << 8) + (((uint32_t)data[5]) << 16) + (((uint32_t)data[6] & 0xFFU) << 24));
            timestamp->RDI_TimeStampMs = ((data[7] & 0xFFU) + (((uint32_t)data[8]) << 8) + (((uint32_t)data[9]) << 16) + (((uint32_t)data[10] & 0xFFU) << 24));
            timestamp->RDI_TimeStampMs = mesTimestamp * 1000 + timestamp->RDI_TimeStampMs / 1000000;
            timestamp->RDI_InterTimeStampMs = timestamp->RDI_TimeStampMs;
            timestamp->RDI_TimeStampStatus = (data[31]);

            timestamp->_0x66D.id = frame.id();
            timestamp->_0x66D.dlc = frame.length();
            memcpy(timestamp->_0x66D.data, frame.data(), frame.length());
            timestamp->_0x66D.timestamp = frame.timestemp();
            timestamp->_0x66D.channel = frame.channelIndex();

            timestamp->_0x66DValids = true;

            timestamps->mCount++;

//            qDebug() << __FUNCTION__ << __LINE__ << timestamps->mCount;
        }
        break;
        case 0x66E:
        {
//            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            const uint8_t *data = (const uint8_t *)frame.data().data();
            uint64_t mesTimestamp = ((data[3] & 0xFFU) + (((uint32_t)data[4]) << 8) + (((uint32_t)data[5]) << 16) + (((uint32_t)data[6] & 0xFFU) << 24));
            timestamp->OD_TimeStampMs = ((data[7] & 0xFFU) + (((uint32_t)data[8]) << 8) + (((uint32_t)data[9]) << 16) + (((uint32_t)data[10] & 0xFFU) << 24));
            timestamp->OD_TimeStampMs = mesTimestamp * 1000 + timestamp->OD_TimeStampMs / 1000000;
            timestamp->OD_InterTimeStampMs = timestamp->OD_TimeStampMs;
            timestamp->OD_TimeStampStatus = (data[29]);

            uint32_t localTime = ((data[11] & 0xFFU) + (((uint32_t)data[12]) << 8) + (((uint32_t)data[13]) << 16) + (((uint32_t)data[14] & 0xFFU) << 24));


            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << timestamp->OD_TimeStampMs << localTime;
//                     << QDateTime::fromMSecsSinceEpoch(timestamp->OD_TimeStampMs).toString("yyyy-MM-dd hh:mm:ss.zzz")
//                     << QDateTime::fromMSecsSinceEpoch(frame.timestemp() / 1000).toString("yyyy-MM-dd hh:mm:ss.zzz");

            timestamp->_0x66E.id = frame.id();
            timestamp->_0x66E.dlc = frame.length();
            memcpy(timestamp->_0x66E.data, frame.data(), frame.length());
            timestamp->_0x66E.timestamp = frame.timestemp();
            timestamp->_0x66E.channel = frame.channelIndex();

            timestamp->_0x66EValids = true;
        }
        break;
        default:
            break;
        }
}

InterpolationBYDHO::InterpolationBYDHO(AnalysisWorker *analysisWorker)
    : IInterpolation(analysisWorker)
{

}

typedef struct CT_RRCR_0x659 {
    uint16_t Checksum_0x659_S;
    uint8_t Counter_0x659_S;
    float RR_OD_DistX_Obj_00;
    float RR_OD_DistY_Obj_00;
    float RR_OD_VrelX_Obj_00;
    float RR_OD_VrelY_Obj_00;
    float RR_OD_ArelX_Obj_00;
    float RR_OD_ArelY_Obj_00;
    float RR_OD_DistXStd_Obj_00;
    float RR_OD_DistYStd_Obj_00;
    float RR_OD_VrelXStd_Obj_00;
    float RR_OD_VrelYStd_Obj_00;
    float RR_OD_ArelXStd_Obj_00;
    float RR_OD_ArelYStd_Obj_00;
    float RR_OD_RCS_Obj_00;
    float RR_OD_Length_Obj_00;
    float RR_OD_Width_Obj_00;
    float RR_OD_Orientation_Obj_00;
    float RR_OD_OrientationStd_Obj_00;
    uint8_t RR_OD_RefPoint_Obj_00;
    uint8_t RR_OD_Classification_Obj_00;
    uint8_t RR_OD_DynProp_Obj_00;
    uint8_t RR_OD_ProbOfExist_Obj_00;
    uint8_t RR_OD_MirrorProb_Obj_00;
    uint8_t RR_OD_MaintenanceState_Obj_00;
    uint16_t RR_OD_LifeCycle_Obj_00;
    uint8_t RR_OD_StableFlag_00;
    float RR_OD_DistX_Obj_01;
    float RR_OD_DistY_Obj_01;
    float RR_OD_VrelX_Obj_01;
    float RR_OD_VrelY_Obj_01;
    float RR_OD_ArelX_Obj_01;
    float RR_OD_ArelY_Obj_01;
    float RR_OD_DistXStd_Obj_01;
    float RR_OD_DistYStd_Obj_01;
    float RR_OD_VrelXStd_Obj_01;
    float RR_OD_VrelYStd_Obj_01;
    float RR_OD_ArelXStd_Obj_01;
    float RR_OD_ArelYStd_Obj_01;
    float RR_OD_RCS_Obj_01;
    float RR_OD_Length_Obj_01;
    float RR_OD_Width_Obj_01;
    float RR_OD_Orientation_Obj_01;
    float RR_OD_OrientationStd_Obj_01;
    uint8_t RR_OD_RefPoint_Obj_01;
    uint8_t RR_OD_Classification_Obj_01;
    uint8_t RR_OD_DynProp_Obj_01;
    uint8_t RR_OD_ProbOfExist_Obj_01;
    uint8_t RR_OD_MirrorProb_Obj_01;
    uint8_t RR_OD_MaintenanceState_Obj_01;
    uint16_t RR_OD_LifeCycle_Obj_01;
    uint8_t RR_OD_StableFlag_01;
    uint8_t RR_ID_Of_Obj_00;
    uint8_t RR_ID_Of_Obj_01;
    uint8_t RR_ObjObstacleProb_00;
    uint8_t RR_ObjObstacleProb_01;
}CT_RRCR_0x659_t;


// 0x66d length = 32
typedef struct CT_RRCR_0x66D {
    uint16_t Checksum_0x66D_S;
    uint8_t Counter_0x66D_S;
    uint32_t RR_RDI_NS_TimeStampGlobSec;
    uint32_t RR_RDI_NS_TimeStampGlobNSec;
    uint32_t RR_RDI_NS_TimeStampLocal;
    float RR_RDI_NS_Latency;
    uint16_t RR_RDI_NS_MeasCounter;
    uint16_t RR_RDI_NS_CycleCounter;
    uint16_t RR_RDI_NS_NumOfClusters;
    float RR_RDI_NS_AmbigFreeDopplerRange;
    uint8_t RR_RDI_NS_MaxDetectionRange;
    float RR_RDI_NS_RangeResolution;
    uint8_t RR_RDI_NS_TaskValidFlag;
    uint8_t RR_RDI_NS_ExtendedCycleFlag;
    float RR_RDI_NS_EgoVelocity;
    float RR_RDI_NS_EgoVelocityStd;
    float RR_RDI_NS_EgoAcceleration;
    float RR_RDI_NS_EgoYawRate;
    float RR_RDI_NS_EgoCurvature;
    uint8_t ARS_RDI_TimesynSts;
}CT_RRCR_0x66D_t;

// 0x66e length = 32
typedef struct CT_RRCR_0x66E {
    uint16_t Checksum_0x66E_S;
    uint8_t Counter_0x66E_S;
    uint32_t RR_OD_TimeStampGlobSec;
    uint32_t RR_OD_TimeStampGlobNanoSec;
    uint32_t RR_OD_TimeStampLocal;
    float RR_OD_Latency;
    uint16_t RR_OD_MeasCounter;
    uint16_t RR_OD_CycleCounter;
    uint8_t RR_OD_NumOfObjects;
    uint8_t RR_OD_TaskValidFlag;
    uint8_t RR_OD_ExtendedCycleFlag;
    float RR_OD_EgoVelocity;
    float RR_OD_EgoVelocityStd;
    float RR_OD_EgoAcceleration;
    float RR_OD_EgoYawRate;
    float RR_OD_EgoYawRateStd;
    float RR_OD_EgoCurvature;
    uint8_t ARS_OD_TimesynSts;
    uint16_t RRCR_SYNC_offset;
}CT_RRCR_0x66E_t;

#define eDATA_LEN_64 64
#define eDATA_LEN_32 32
#define eDATA_LEN_8 8

bool encode_RRCR_0x659(CT_RRCR_0x659_t *userData, uint8_t *data, int length) {
    if (length != 64) {
        return false;
    }

    uint16_t Checksum_0x659_S = (userData->Checksum_0x659_S);
    uint8_t Counter_0x659_S = (userData->Counter_0x659_S);
    uint16_t RR_OD_DistX_Obj_00 = (((userData->RR_OD_DistX_Obj_00) + 100) / 0.0875);
    uint16_t RR_OD_DistY_Obj_00 = (((userData->RR_OD_DistY_Obj_00) + 179.113) / 0.0875);
    uint16_t RR_OD_VrelX_Obj_00 = (((userData->RR_OD_VrelX_Obj_00) + 128) / 0.125);
    uint16_t RR_OD_VrelY_Obj_00 = (((userData->RR_OD_VrelY_Obj_00) + 128) / 0.125);
    uint16_t RR_OD_ArelX_Obj_00 = (((userData->RR_OD_ArelX_Obj_00) + 31.875) / 0.125);
    uint16_t RR_OD_ArelY_Obj_00 = (((userData->RR_OD_ArelY_Obj_00) + 31.875) / 0.125);
    uint8_t RR_OD_DistXStd_Obj_00 = ((userData->RR_OD_DistXStd_Obj_00) / 0.05);
    uint8_t RR_OD_DistYStd_Obj_00 = ((userData->RR_OD_DistYStd_Obj_00) / 0.1);
    uint8_t RR_OD_VrelXStd_Obj_00 = ((userData->RR_OD_VrelXStd_Obj_00) / 0.1);
    uint8_t RR_OD_VrelYStd_Obj_00 = ((userData->RR_OD_VrelYStd_Obj_00) / 0.2);
    uint8_t RR_OD_ArelXStd_Obj_00 = ((userData->RR_OD_ArelXStd_Obj_00) / 0.25);
    uint8_t RR_OD_ArelYStd_Obj_00 = ((userData->RR_OD_ArelYStd_Obj_00) / 0.25);
    uint16_t RR_OD_RCS_Obj_00 = (((userData->RR_OD_RCS_Obj_00) + 51.1) / 0.2);
    uint8_t RR_OD_Length_Obj_00 = ((userData->RR_OD_Length_Obj_00) / 0.2);
    uint8_t RR_OD_Width_Obj_00 = ((userData->RR_OD_Width_Obj_00) / 0.2);
    uint16_t RR_OD_Orientation_Obj_00 = (((userData->RR_OD_Orientation_Obj_00) + 5.11) / 0.01);
    uint8_t RR_OD_OrientationStd_Obj_00 = ((userData->RR_OD_OrientationStd_Obj_00) / 0.002);
    uint8_t RR_OD_RefPoint_Obj_00 = (userData->RR_OD_RefPoint_Obj_00);
    uint8_t RR_OD_Classification_Obj_00 = (userData->RR_OD_Classification_Obj_00);
    uint8_t RR_OD_DynProp_Obj_00 = (userData->RR_OD_DynProp_Obj_00);
    uint8_t RR_OD_ProbOfExist_Obj_00 = (userData->RR_OD_ProbOfExist_Obj_00);
    uint8_t RR_OD_MirrorProb_Obj_00 = (userData->RR_OD_MirrorProb_Obj_00);
    uint8_t RR_OD_MaintenanceState_Obj_00 = (userData->RR_OD_MaintenanceState_Obj_00);
    uint16_t RR_OD_LifeCycle_Obj_00 = (userData->RR_OD_LifeCycle_Obj_00);
    uint8_t RR_OD_StableFlag_00 = (userData->RR_OD_StableFlag_00);
    uint16_t RR_OD_DistX_Obj_01 = (((userData->RR_OD_DistX_Obj_01) + 100) / 0.0875);
    uint16_t RR_OD_DistY_Obj_01 = (((userData->RR_OD_DistY_Obj_01) + 179.113) / 0.0875);
    uint16_t RR_OD_VrelX_Obj_01 = (((userData->RR_OD_VrelX_Obj_01) + 128) / 0.125);
    uint16_t RR_OD_VrelY_Obj_01 = (((userData->RR_OD_VrelY_Obj_01) + 128) / 0.125);
    uint16_t RR_OD_ArelX_Obj_01 = (((userData->RR_OD_ArelX_Obj_01) + 31.875) / 0.125);
    uint16_t RR_OD_ArelY_Obj_01 = (((userData->RR_OD_ArelY_Obj_01) + 31.875) / 0.125);
    uint8_t RR_OD_DistXStd_Obj_01 = ((userData->RR_OD_DistXStd_Obj_01) / 0.05);
    uint8_t RR_OD_DistYStd_Obj_01 = ((userData->RR_OD_DistYStd_Obj_01) / 0.1);
    uint8_t RR_OD_VrelXStd_Obj_01 = ((userData->RR_OD_VrelXStd_Obj_01) / 0.1);
    uint8_t RR_OD_VrelYStd_Obj_01 = ((userData->RR_OD_VrelYStd_Obj_01) / 0.2);
    uint8_t RR_OD_ArelXStd_Obj_01 = ((userData->RR_OD_ArelXStd_Obj_01) / 0.25);
    uint8_t RR_OD_ArelYStd_Obj_01 = ((userData->RR_OD_ArelYStd_Obj_01) / 0.25);
    uint16_t RR_OD_RCS_Obj_01 = (((userData->RR_OD_RCS_Obj_01) + 51.1) / 0.2);
    uint8_t RR_OD_Length_Obj_01 = ((userData->RR_OD_Length_Obj_01) / 0.2);
    uint8_t RR_OD_Width_Obj_01 = ((userData->RR_OD_Width_Obj_01) / 0.2);
    uint16_t RR_OD_Orientation_Obj_01 = (((userData->RR_OD_Orientation_Obj_01) + 5.11) / 0.01);
    uint8_t RR_OD_OrientationStd_Obj_01 = ((userData->RR_OD_OrientationStd_Obj_01) / 0.002);
    uint8_t RR_OD_RefPoint_Obj_01 = (userData->RR_OD_RefPoint_Obj_01);
    uint8_t RR_OD_Classification_Obj_01 = (userData->RR_OD_Classification_Obj_01);
    uint8_t RR_OD_DynProp_Obj_01 = (userData->RR_OD_DynProp_Obj_01);
    uint8_t RR_OD_ProbOfExist_Obj_01 = (userData->RR_OD_ProbOfExist_Obj_01);
    uint8_t RR_OD_MirrorProb_Obj_01 = (userData->RR_OD_MirrorProb_Obj_01);
    uint8_t RR_OD_MaintenanceState_Obj_01 = (userData->RR_OD_MaintenanceState_Obj_01);
    uint16_t RR_OD_LifeCycle_Obj_01 = (userData->RR_OD_LifeCycle_Obj_01);
    uint8_t RR_OD_StableFlag_01 = (userData->RR_OD_StableFlag_01);
    uint8_t RR_ID_Of_Obj_00 = (userData->RR_ID_Of_Obj_00);
    uint8_t RR_ID_Of_Obj_01 = (userData->RR_ID_Of_Obj_01);
    uint8_t RR_ObjObstacleProb_00 = (userData->RR_ObjObstacleProb_00);
    uint8_t RR_ObjObstacleProb_01 = (userData->RR_ObjObstacleProb_01);

//    data[0] = ((userData->Checksum_0x659_S & 0xFFU) << 0);
//    data[1] = ((userData->Checksum_0x659_S & 0xFF00U) >> 8);
    data[2] = (userData->Counter_0x659_S & 0xFFU);
    data[3] = ((RR_OD_DistX_Obj_00 & 0xFFU) << 0);
    data[4] = (((RR_OD_DistX_Obj_00 & 0xF00U) >> 8) | ((RR_OD_DistY_Obj_00 & 0xFU) << 4));
    data[5] = ((RR_OD_DistY_Obj_00 & 0xFF0U) >> 4);
    data[6] = ((RR_OD_VrelX_Obj_00 & 0xFFU) << 0);
    data[7] = (((RR_OD_VrelX_Obj_00 & 0x700U) >> 8) | ((RR_OD_VrelY_Obj_00 & 0x1FU) << 3));
    data[8] = (((RR_OD_VrelY_Obj_00 & 0x7E0U) >> 5) | ((RR_OD_ArelX_Obj_00 & 0x3U) << 6));
    data[9] = (((RR_OD_ArelX_Obj_00 & 0x1FCU) >> 2) | ((RR_OD_ArelY_Obj_00 & 0x1U) << 7));
    data[10] = ((RR_OD_ArelY_Obj_00 & 0x1FEU) >> 1);
    data[11] = (RR_OD_DistXStd_Obj_00 & 0xFFU);
    data[12] = (RR_OD_DistYStd_Obj_00 & 0xFFU);
    data[13] = ((RR_OD_VrelXStd_Obj_00 & 0x7FU) | ((RR_OD_VrelYStd_Obj_00 & 0x1U) << 7));
    data[14] = (((RR_OD_VrelYStd_Obj_00 & 0x7EU) >> 1) | ((RR_OD_ArelXStd_Obj_00 & 0x3U) << 6));
    data[15] = (((RR_OD_ArelXStd_Obj_00 & 0x7CU) >> 2) | ((RR_OD_ArelYStd_Obj_00 & 0x7U) << 5));
    data[16] = (((RR_OD_ArelYStd_Obj_00 & 0xF8U) >> 3) | ((RR_OD_RCS_Obj_00 & 0x7U) << 5));
    data[17] = (((RR_OD_RCS_Obj_00 & 0x1F8U) >> 3) | ((RR_OD_Length_Obj_00 & 0x3U) << 6));
    data[18] = (((RR_OD_Length_Obj_00 & 0x7CU) >> 2) | ((RR_OD_Width_Obj_00 & 0x7U) << 5));
    data[19] = (((RR_OD_Width_Obj_00 & 0x78U) >> 3) | ((RR_OD_Orientation_Obj_00 & 0xFU) << 4));
    data[20] = (((RR_OD_Orientation_Obj_00 & 0x3F0U) >> 4) | ((RR_OD_OrientationStd_Obj_00 & 0x3U) << 6));
    data[21] = (((RR_OD_OrientationStd_Obj_00 & 0xCU) >> 2) | (userData->RR_OD_RefPoint_Obj_00 & 0x3U) | (userData->RR_OD_Classification_Obj_00 & 0x7U) | ((userData->RR_OD_DynProp_Obj_00 & 0x1U) << 7));
    data[22] = (((userData->RR_OD_DynProp_Obj_00 & 0x6U) >> 1) | ((userData->RR_OD_ProbOfExist_Obj_00 & 0x3FU) << 2));
    data[23] = (((userData->RR_OD_ProbOfExist_Obj_00 & 0x40U) >> 6) | (userData->RR_OD_MirrorProb_Obj_00 & 0x7FU));
    data[24] = ((userData->RR_OD_MaintenanceState_Obj_00 & 0x3U) | ((userData->RR_OD_LifeCycle_Obj_00 & 0x3FU) << 2));
    data[25] = ((userData->RR_OD_LifeCycle_Obj_00 & 0x3FC0U) >> 6);
    data[26] = (((userData->RR_OD_LifeCycle_Obj_00 & 0xC000U) >> 14) | (userData->RR_OD_StableFlag_00 & 0x1U) | ((RR_OD_DistX_Obj_01 & 0x1FU) << 3));
    data[27] = (((RR_OD_DistX_Obj_01 & 0xFE0U) >> 5) | ((RR_OD_DistY_Obj_01 & 0x1U) << 7));
    data[28] = ((RR_OD_DistY_Obj_01 & 0x1FEU) >> 1);
    data[29] = (((RR_OD_DistY_Obj_01 & 0xE00U) >> 9) | ((RR_OD_VrelX_Obj_01 & 0x1FU) << 3));
    data[30] = (((RR_OD_VrelX_Obj_01 & 0x7E0U) >> 5) | ((RR_OD_VrelY_Obj_01 & 0x3U) << 6));
    data[31] = ((RR_OD_VrelY_Obj_01 & 0x3FCU) >> 2);
    data[32] = (((RR_OD_VrelY_Obj_01 & 0x400U) >> 10) | ((RR_OD_ArelX_Obj_01 & 0x7FU) << 1));
    data[33] = (((RR_OD_ArelX_Obj_01 & 0x180U) >> 7) | ((RR_OD_ArelY_Obj_01 & 0x3FU) << 2));
    data[34] = (((RR_OD_ArelY_Obj_01 & 0x1C0U) >> 6) | ((RR_OD_DistXStd_Obj_01 & 0x1FU) << 3));
    data[35] = (((RR_OD_DistXStd_Obj_01 & 0xE0U) >> 5) | ((RR_OD_DistYStd_Obj_01 & 0x1FU) << 3));
    data[36] = (((RR_OD_DistYStd_Obj_01 & 0xE0U) >> 5) | ((RR_OD_VrelXStd_Obj_01 & 0x1FU) << 3));
    data[37] = (((RR_OD_VrelXStd_Obj_01 & 0x60U) >> 5) | ((RR_OD_VrelYStd_Obj_01 & 0x3FU) << 2));
    data[38] = (((RR_OD_VrelYStd_Obj_01 & 0x40U) >> 6) | (RR_OD_ArelXStd_Obj_01 & 0x7FU));
    data[39] = (RR_OD_ArelYStd_Obj_01 & 0xFFU);
    data[40] = ((RR_OD_RCS_Obj_01 & 0xFFU) << 0);
    data[41] = (((RR_OD_RCS_Obj_01 & 0x100U) >> 8) | (RR_OD_Length_Obj_01 & 0x7FU));
    data[42] = ((RR_OD_Width_Obj_01 & 0x7FU) | ((RR_OD_Orientation_Obj_01 & 0x1U) << 7));
    data[43] = ((RR_OD_Orientation_Obj_01 & 0x1FEU) >> 1);
    data[44] = (((RR_OD_Orientation_Obj_01 & 0x200U) >> 9) | (RR_OD_OrientationStd_Obj_01 & 0xFU) | (userData->RR_OD_RefPoint_Obj_01 & 0x3U) | ((userData->RR_OD_Classification_Obj_01 & 0x1U) << 7));
    data[45] = (((userData->RR_OD_Classification_Obj_01 & 0x6U) >> 1) | (userData->RR_OD_DynProp_Obj_01 & 0x7U) | ((userData->RR_OD_ProbOfExist_Obj_01 & 0x7U) << 5));
    data[46] = (((userData->RR_OD_ProbOfExist_Obj_01 & 0x78U) >> 3) | ((userData->RR_OD_MirrorProb_Obj_01 & 0xFU) << 4));
    data[47] = (((userData->RR_OD_MirrorProb_Obj_01 & 0x70U) >> 4) | (userData->RR_OD_MaintenanceState_Obj_01 & 0x3U) | ((userData->RR_OD_LifeCycle_Obj_01 & 0x7U) << 5));
    data[48] = ((userData->RR_OD_LifeCycle_Obj_01 & 0x7F8U) >> 3);
    data[49] = (((userData->RR_OD_LifeCycle_Obj_01 & 0xF800U) >> 11) | (userData->RR_OD_StableFlag_01 & 0x1U));
    data[50] = (userData->RR_ID_Of_Obj_00 & 0xFFU);
    data[51] = (userData->RR_ID_Of_Obj_01 & 0xFFU);
    data[52] = (userData->RR_ObjObstacleProb_00 & 0x7FU);
    data[53] = (userData->RR_ObjObstacleProb_01 & 0x7FU);
    data[54] = 0;
    data[55] = 0;
    data[56] = 0;
    data[57] = 0;
    data[58] = 0;
    data[59] = 0;
    data[60] = 0;
    data[61] = 0;
    data[62] = 0;
    data[63] = 0;


    userData->Checksum_0x659_S = CRC16_CCITT_FALSE(data, eDATA_LEN_64);

    data[0] = ((userData->Checksum_0x659_S & 0xFFU) << 0);
    data[1] = ((userData->Checksum_0x659_S & 0xFF00U) >> 8);

    return true;
}


// 0x66d length = 32
bool encode_RRCR_0x66D(CT_RRCR_0x66D_t *userData, uint8_t *data, int length) {
    if (length != 32) {
        return false;
    }

    uint16_t Checksum_0x66D_S = (userData->Checksum_0x66D_S);
    uint8_t Counter_0x66D_S = (userData->Counter_0x66D_S);
    uint32_t RR_RDI_NS_TimeStampGlobSec = (userData->RR_RDI_NS_TimeStampGlobSec);
    uint32_t RR_RDI_NS_TimeStampGlobNSec = (userData->RR_RDI_NS_TimeStampGlobNSec);
    uint32_t RR_RDI_NS_TimeStampLocal = (userData->RR_RDI_NS_TimeStampLocal);
    uint16_t RR_RDI_NS_Latency = (((userData->RR_RDI_NS_Latency) - 30) / 0.1);
    uint16_t RR_RDI_NS_MeasCounter = (userData->RR_RDI_NS_MeasCounter);
    uint16_t RR_RDI_NS_CycleCounter = (userData->RR_RDI_NS_CycleCounter);
    uint16_t RR_RDI_NS_NumOfClusters = (userData->RR_RDI_NS_NumOfClusters);
    uint16_t RR_RDI_NS_AmbigFreeDopplerRange = ((userData->RR_RDI_NS_AmbigFreeDopplerRange) / 0.01);
    uint8_t RR_RDI_NS_MaxDetectionRange = (userData->RR_RDI_NS_MaxDetectionRange);
    uint8_t RR_RDI_NS_RangeResolution = ((userData->RR_RDI_NS_RangeResolution) / 0.01);
    uint8_t RR_RDI_NS_TaskValidFlag = (userData->RR_RDI_NS_TaskValidFlag);
    uint8_t RR_RDI_NS_ExtendedCycleFlag = (userData->RR_RDI_NS_ExtendedCycleFlag);
    uint16_t RR_RDI_NS_EgoVelocity = (((userData->RR_RDI_NS_EgoVelocity) + 128) / 0.125);
    uint8_t RR_RDI_NS_EgoVelocityStd = ((userData->RR_RDI_NS_EgoVelocityStd) / 0.1);
    uint16_t RR_RDI_NS_EgoAcceleration = (((userData->RR_RDI_NS_EgoAcceleration) + 31.875) / 0.125);
    uint16_t RR_RDI_NS_EgoYawRate = (((userData->RR_RDI_NS_EgoYawRate) + 0.511) / 0.001);
    uint8_t RR_RDI_NS_EgoCurvature = ((userData->RR_RDI_NS_EgoCurvature) / 0.001);
    uint8_t ARS_RDI_TimesynSts = (userData->ARS_RDI_TimesynSts);

    data[0] = ((userData->Checksum_0x66D_S & 0xFFU) << 0);
    data[1] = ((userData->Checksum_0x66D_S & 0xFF00U) >> 8);
    data[2] = (userData->Counter_0x66D_S & 0xFFU);
    data[3] = ((userData->RR_RDI_NS_TimeStampGlobSec & 0xFFU) << 0);
    data[4] = ((userData->RR_RDI_NS_TimeStampGlobSec & 0xFF00U) >> 8);
    data[5] = ((userData->RR_RDI_NS_TimeStampGlobSec & 0xFF0000U) >> 16);
    data[6] = ((userData->RR_RDI_NS_TimeStampGlobSec & 0xFF000000U) >> 24);
    data[7] = ((userData->RR_RDI_NS_TimeStampGlobNSec & 0xFFU) << 0);
    data[8] = ((userData->RR_RDI_NS_TimeStampGlobNSec & 0xFF00U) >> 8);
    data[9] = ((userData->RR_RDI_NS_TimeStampGlobNSec & 0xFF0000U) >> 16);
    data[10] = ((userData->RR_RDI_NS_TimeStampGlobNSec & 0xFF000000U) >> 24);
    data[11] = ((userData->RR_RDI_NS_TimeStampLocal & 0xFFU) << 0);
    data[12] = ((userData->RR_RDI_NS_TimeStampLocal & 0xFF00U) >> 8);
    data[13] = ((userData->RR_RDI_NS_TimeStampLocal & 0xFF0000U) >> 16);
    data[14] = ((userData->RR_RDI_NS_TimeStampLocal & 0xFF000000U) >> 24);
    data[15] = ((RR_RDI_NS_Latency & 0xFFU) << 0);
    data[16] = (((RR_RDI_NS_Latency & 0x700U) >> 8) | ((userData->RR_RDI_NS_MeasCounter & 0x1FU) << 3));
    data[17] = ((userData->RR_RDI_NS_MeasCounter & 0x1FE0U) >> 5);
    data[18] = (((userData->RR_RDI_NS_MeasCounter & 0xE000U) >> 13) | ((userData->RR_RDI_NS_CycleCounter & 0x1FU) << 3));
    data[19] = ((userData->RR_RDI_NS_CycleCounter & 0x1FE0U) >> 5);
    data[20] = (((userData->RR_RDI_NS_CycleCounter & 0xE000U) >> 13) | ((userData->RR_RDI_NS_NumOfClusters & 0x1FU) << 3));
    data[21] = (((userData->RR_RDI_NS_NumOfClusters & 0x1E0U) >> 5) | ((RR_RDI_NS_AmbigFreeDopplerRange & 0xFU) << 4));
    data[22] = ((RR_RDI_NS_AmbigFreeDopplerRange & 0xFF0U) >> 4);
    data[23] = (userData->RR_RDI_NS_MaxDetectionRange & 0xFFU);
    data[24] = ((RR_RDI_NS_RangeResolution & 0x7FU) | (userData->RR_RDI_NS_TaskValidFlag & 0x1U));
    data[25] = ((userData->RR_RDI_NS_ExtendedCycleFlag & 0x1U) | ((RR_RDI_NS_EgoVelocity & 0x7FU) << 1));
    data[26] = (((RR_RDI_NS_EgoVelocity & 0x780U) >> 7) | ((RR_RDI_NS_EgoVelocityStd & 0xFU) << 4));
    data[27] = (((RR_RDI_NS_EgoVelocityStd & 0x70U) >> 4) | ((RR_RDI_NS_EgoAcceleration & 0x1FU) << 3));
    data[28] = (((RR_RDI_NS_EgoAcceleration & 0x1E0U) >> 5) | ((RR_RDI_NS_EgoYawRate & 0xFU) << 4));
    data[29] = (((RR_RDI_NS_EgoYawRate & 0x3F0U) >> 4) | ((RR_RDI_NS_EgoCurvature & 0x3U) << 6));
    data[30] = ((RR_RDI_NS_EgoCurvature & 0xFCU) >> 2);
    data[31] = (userData->ARS_RDI_TimesynSts & 0xFFU);

    return true;
}

// 0x66e length = 32
bool encode_RRCR_0x66E(CT_RRCR_0x66E_t *userData, uint8_t *data, int length) {
    if (length != 32) {
        return false;
    }

    uint16_t Checksum_0x66E_S = (userData->Checksum_0x66E_S);
    uint8_t Counter_0x66E_S = (userData->Counter_0x66E_S);
    uint32_t RR_OD_TimeStampGlobSec = (userData->RR_OD_TimeStampGlobSec);
    uint32_t RR_OD_TimeStampGlobNanoSec = (userData->RR_OD_TimeStampGlobNanoSec);
    uint32_t RR_OD_TimeStampLocal = (userData->RR_OD_TimeStampLocal);
    uint16_t RR_OD_Latency = (((userData->RR_OD_Latency) - 30) / 0.1);
    uint16_t RR_OD_MeasCounter = (userData->RR_OD_MeasCounter);
    uint16_t RR_OD_CycleCounter = (userData->RR_OD_CycleCounter);
    uint8_t RR_OD_NumOfObjects = (userData->RR_OD_NumOfObjects);
    uint8_t RR_OD_TaskValidFlag = (userData->RR_OD_TaskValidFlag);
    uint8_t RR_OD_ExtendedCycleFlag = (userData->RR_OD_ExtendedCycleFlag);
    uint16_t RR_OD_EgoVelocity = (((userData->RR_OD_EgoVelocity) + 128) / 0.125);
    uint8_t RR_OD_EgoVelocityStd = ((userData->RR_OD_EgoVelocityStd) / 0.1);
    uint16_t RR_OD_EgoAcceleration = (((userData->RR_OD_EgoAcceleration) + 31.875) / 0.125);
    uint16_t RR_OD_EgoYawRate = (((userData->RR_OD_EgoYawRate) + 0.511) / 0.001);
    uint16_t RR_OD_EgoYawRateStd = ((userData->RR_OD_EgoYawRateStd) / 0.0001);
    uint8_t RR_OD_EgoCurvature = ((userData->RR_OD_EgoCurvature) / 0.001);
    uint8_t ARS_OD_TimesynSts = (userData->ARS_OD_TimesynSts);
    uint16_t RRCR_SYNC_offset = (userData->RRCR_SYNC_offset);

    data[0] = ((userData->Checksum_0x66E_S & 0xFFU) << 0);
    data[1] = ((userData->Checksum_0x66E_S & 0xFF00U) >> 8);
    data[2] = (userData->Counter_0x66E_S & 0xFFU);
    data[3] = ((userData->RR_OD_TimeStampGlobSec & 0xFFU) << 0);
    data[4] = ((userData->RR_OD_TimeStampGlobSec & 0xFF00U) >> 8);
    data[5] = ((userData->RR_OD_TimeStampGlobSec & 0xFF0000U) >> 16);
    data[6] = ((userData->RR_OD_TimeStampGlobSec & 0xFF000000U) >> 24);
    data[7] = ((userData->RR_OD_TimeStampGlobNanoSec & 0xFFU) << 0);
    data[8] = ((userData->RR_OD_TimeStampGlobNanoSec & 0xFF00U) >> 8);
    data[9] = ((userData->RR_OD_TimeStampGlobNanoSec & 0xFF0000U) >> 16);
    data[10] = ((userData->RR_OD_TimeStampGlobNanoSec & 0xFF000000U) >> 24);
    data[11] = ((userData->RR_OD_TimeStampLocal & 0xFFU) << 0);
    data[12] = ((userData->RR_OD_TimeStampLocal & 0xFF00U) >> 8);
    data[13] = ((userData->RR_OD_TimeStampLocal & 0xFF0000U) >> 16);
    data[14] = ((userData->RR_OD_TimeStampLocal & 0xFF000000U) >> 24);
    data[15] = ((RR_OD_Latency & 0xFFU) << 0);
    data[16] = (((RR_OD_Latency & 0x700U) >> 8) | ((userData->RR_OD_MeasCounter & 0x1FU) << 3));
    data[17] = ((userData->RR_OD_MeasCounter & 0x1FE0U) >> 5);
    data[18] = (((userData->RR_OD_MeasCounter & 0xE000U) >> 13) | ((userData->RR_OD_CycleCounter & 0x1FU) << 3));
    data[19] = ((userData->RR_OD_CycleCounter & 0x1FE0U) >> 5);
    data[20] = (((userData->RR_OD_CycleCounter & 0xE000U) >> 13) | ((userData->RR_OD_NumOfObjects & 0x1FU) << 3));
    data[21] = (((userData->RR_OD_NumOfObjects & 0x20U) >> 5) | (userData->RR_OD_TaskValidFlag & 0x1U) | (userData->RR_OD_ExtendedCycleFlag & 0x1U) | ((RR_OD_EgoVelocity & 0x1FU) << 3));
    data[22] = (((RR_OD_EgoVelocity & 0x7E0U) >> 5) | ((RR_OD_EgoVelocityStd & 0x3U) << 6));
    data[23] = (((RR_OD_EgoVelocityStd & 0x7CU) >> 2) | ((RR_OD_EgoAcceleration & 0x7U) << 5));
    data[24] = (((RR_OD_EgoAcceleration & 0x1F8U) >> 3) | ((RR_OD_EgoYawRate & 0x3U) << 6));
    data[25] = ((RR_OD_EgoYawRate & 0x3FCU) >> 2);
    data[26] = ((RR_OD_EgoYawRateStd & 0xFFU) << 0);
    data[27] = (((RR_OD_EgoYawRateStd & 0x300U) >> 8) | ((RR_OD_EgoCurvature & 0x3FU) << 2));
    data[28] = ((RR_OD_EgoCurvature & 0xC0U) >> 6);
    data[29] = (userData->ARS_OD_TimesynSts & 0xFFU);
    data[30] = ((userData->RRCR_SYNC_offset & 0xFFU) << 0);
    data[31] = ((userData->RRCR_SYNC_offset & 0x300U) >> 8);

    return true;
}

void init_RRCR_0x659(CT_RRCR_0x659_t *userData, Devices::Can::stCanTxMsg *frame, uint8_t msgCounter)
{
    memset(userData, 0, sizeof (CT_RRCR_0x659_t));
    userData->RR_ID_Of_Obj_00 = 0xFF;
    userData->RR_ID_Of_Obj_01 = 0xFF;
    userData->Counter_0x659_S = msgCounter;
    encode_RRCR_0x659(userData, frame->data, frame->dlc);
}

int InterpolationBYDHO::encodeFrame(int radarID, Devices::Can::FrameTimestamp *timestamp, RDP_TrkObjectInfo_t *outputObjList, int16_t trkValidNum, uint8_t msgCounter, Devices::Can::stCanTxMsg *frameArray)
{
    qDebug() << __FUNCTION__ << __LINE__ << radarID << trkValidNum;

    AnalysisData &analysisData = mAnalysisWorker->mAnalysisDatas[radarID];
    Targets &_16Targets = analysisData.m16Targets;
    _16Targets.clear();

    int frameCount = 0;
    for (int i = 0; i < 8; ++i) {
        Devices::Can::stCanTxMsg *frame = frameArray + i;
        frame->id = 0x659 + i;
        frame->dlc = eDATA_LEN_64;
        frame->valid = true;
        frame->timestamp = timestamp->frameTimestamps[i];

        CT_RRCR_0x659_t userData;
        init_RRCR_0x659(&userData, frame, msgCounter);
    }

    CT_RRCR_0x659_t userData;
    Devices::Can::stCanTxMsg *frame = 0;

    uint16_t objIndex = 0;
    uint8_t  sendObjCnt = 0;
    uint32_t delta_time = 0;
    for (objIndex = 0; objIndex < trkValidNum; objIndex++)
    {
        if (outputObjList[objIndex].rdpTrkObjRange < 0.1)
        {
            continue;
        }

        if ((sendObjCnt >= 16) || (trkValidNum > 16))   // BYD协议每次只传输16个目标
        {
            break;
        }

        /* Decide the number in one target list */
        if ((sendObjCnt % 2) == 0)
        {
            frame = frameArray + (sendObjCnt % 8);
            memset(&userData, 0, sizeof(CT_RRCR_0x659_t));
            userData.RR_ID_Of_Obj_00 = 0xFF;
            userData.RR_ID_Of_Obj_01 = 0xFF;
            userData.Counter_0x659_S = msgCounter;

            // Obj 1
            userData.RR_ID_Of_Obj_00        = outputObjList[objIndex].id & 0xFF;
            userData.RR_OD_ProbOfExist_Obj_00  = outputObjList[objIndex].rdpTrkObjReliability;
            userData.RR_ObjObstacleProb_00 = outputObjList[objIndex].rdpTrkObjObstacleProb;
#ifdef BYD_ORIN_OTGVEL_PLATFORM
            userData.RR_OD_DistX_Obj_00        = (uint64_t)(((outputObjList[objIndex].rdpTrkObjDistX + (outputObjList[objIndex].rdpTrkObjVrelX - pVdy->vdySpeedInmps) * (float)delta_time * 0.001f) + 100) / 0.0875);
#else
            userData.RR_OD_DistX_Obj_00        = outputObjList[objIndex].rdpTrkObjDistX + outputObjList[objIndex].rdpTrkObjVrelX * (float)delta_time * 0.001f;
#endif
            userData.RR_OD_DistY_Obj_00        = outputObjList[objIndex].rdpTrkObjDistY + outputObjList[objIndex].rdpTrkObjVrelY * (float)delta_time * 0.001f;

//            qDebug() << __FUNCTION__ << __LINE__ << userData.RR_ID_Of_Obj_00
//                     << outputObjList[objIndex].rdpTrkObjDistX << outputObjList[objIndex].rdpTrkObjDistY
//                     << userData.RR_OD_DistX_Obj_00 << userData.RR_OD_DistY_Obj_00;
            userData.RR_OD_ArelX_Obj_00        = outputObjList[objIndex].rdpTrkObjArelX;
            userData.RR_OD_ArelY_Obj_00        = outputObjList[objIndex].rdpTrkObjArelY;
            userData.RR_OD_VrelX_Obj_00        = outputObjList[objIndex].rdpTrkObjVrelX;
            userData.RR_OD_VrelY_Obj_00        = outputObjList[objIndex].rdpTrkObjVrelY;

            userData.RR_OD_DistXStd_Obj_00     = outputObjList[objIndex].objDistXStd;
            userData.RR_OD_DistYStd_Obj_00     = outputObjList[objIndex].objDistYStd;
            userData.RR_OD_VrelXStd_Obj_00     = outputObjList[objIndex].objRelVelXStd;
            userData.RR_OD_VrelYStd_Obj_00     = outputObjList[objIndex].objRelVelYStd;
            userData.RR_OD_ArelXStd_Obj_00     = outputObjList[objIndex].objAccelXStd;
            userData.RR_OD_ArelYStd_Obj_00     = outputObjList[objIndex].objAccelYStd;
            userData.RR_OD_RCS_Obj_00          = (float)(outputObjList[objIndex].rdpTrkObjRcs) / 10;
            userData.RR_OD_Length_Obj_00       = outputObjList[objIndex].rdpTrkObjBoxLength;
            userData.RR_OD_Width_Obj_00        = outputObjList[objIndex].rdpTrkObjBoxWidth;
            userData.RR_OD_Orientation_Obj_00  = outputObjList[objIndex].rdpTrkObjHeadingAngle;
            userData.RR_OD_OrientationStd_Obj_00  = outputObjList[objIndex].rdpTrkObjHeadingAngleStd;
            userData.RR_OD_RefPoint_Obj_00     = outputObjList[objIndex].rdpTrkObjReferPosi;
            userData.RR_OD_Classification_Obj_00  = outputObjList[objIndex].rdpTrkObjClassification;
            userData.RR_OD_DynProp_Obj_00      = outputObjList[objIndex].rdpTrkObjDynProp;
            userData.RR_OD_MirrorProb_Obj_00   = outputObjList[objIndex].rdpTrkObjMirrorProb;
            userData.RR_OD_MaintenanceState_Obj_00 = outputObjList[objIndex].rdpTrkObjMaintenanceState;
            userData.RR_OD_LifeCycle_Obj_00    = outputObjList[objIndex].rdpLifeCycleCnt;
            userData.RR_OD_StableFlag_00       = outputObjList[objIndex].rdpTrkObjStableFlag;
        }
        else if ((sendObjCnt % 2) == 1)
        {
            // Obj 2
            userData.RR_ID_Of_Obj_01        = outputObjList[objIndex].id & 0xFF;
            userData.RR_OD_ProbOfExist_Obj_01  = outputObjList[objIndex].rdpTrkObjReliability;
            userData.RR_ObjObstacleProb_01 = (uint64_t)(outputObjList[objIndex].rdpTrkObjObstacleProb);
#ifdef BYD_ORIN_OTGVEL_PLATFORM
            userData.RR_OD_DistX_Obj_01        = (outputObjList[objIndex].rdpTrkObjDistX + (outputObjList[objIndex].rdpTrkObjVrelX - pVdy->vdySpeedInmps) * (float)delta_time * 0.001f) + 100) / 0.0875);
#else
            userData.RR_OD_DistX_Obj_01        = outputObjList[objIndex].rdpTrkObjDistX + outputObjList[objIndex].rdpTrkObjVrelX * (float)delta_time * 0.001f;
#endif
            userData.RR_OD_DistY_Obj_01        = outputObjList[objIndex].rdpTrkObjDistY + outputObjList[objIndex].rdpTrkObjVrelY * (float)delta_time * 0.001f;
            userData.RR_OD_ArelX_Obj_01        = outputObjList[objIndex].rdpTrkObjArelX;
            userData.RR_OD_ArelY_Obj_01        = outputObjList[objIndex].rdpTrkObjArelY;
            userData.RR_OD_VrelX_Obj_01        = outputObjList[objIndex].rdpTrkObjVrelX;
            userData.RR_OD_VrelY_Obj_01        = outputObjList[objIndex].rdpTrkObjVrelY;

            userData.RR_OD_DistXStd_Obj_01     = outputObjList[objIndex].objDistXStd;
            userData.RR_OD_DistYStd_Obj_01     = outputObjList[objIndex].objDistYStd;
            userData.RR_OD_VrelXStd_Obj_01     = outputObjList[objIndex].objRelVelXStd;
            userData.RR_OD_VrelYStd_Obj_01     = outputObjList[objIndex].objRelVelYStd;
            userData.RR_OD_ArelXStd_Obj_01     = outputObjList[objIndex].objAccelXStd;
            userData.RR_OD_ArelYStd_Obj_01     = outputObjList[objIndex].objAccelYStd;
            userData.RR_OD_RCS_Obj_01          = (float)(outputObjList[objIndex].rdpTrkObjRcs) / 10;
            userData.RR_OD_Length_Obj_01       = outputObjList[objIndex].rdpTrkObjBoxLength;
            userData.RR_OD_Width_Obj_01        = outputObjList[objIndex].rdpTrkObjBoxWidth;
            userData.RR_OD_Orientation_Obj_01  = outputObjList[objIndex].rdpTrkObjHeadingAngle;
            userData.RR_OD_OrientationStd_Obj_01  = outputObjList[objIndex].rdpTrkObjHeadingAngleStd;
            userData.RR_OD_RefPoint_Obj_01     = outputObjList[objIndex].rdpTrkObjReferPosi;
            userData.RR_OD_Classification_Obj_01  = outputObjList[objIndex].rdpTrkObjClassification;
            userData.RR_OD_DynProp_Obj_01      = outputObjList[objIndex].rdpTrkObjDynProp;
            userData.RR_OD_MirrorProb_Obj_01   = outputObjList[objIndex].rdpTrkObjMirrorProb;
            userData.RR_OD_MaintenanceState_Obj_01 = outputObjList[objIndex].rdpTrkObjMaintenanceState;
            userData.RR_OD_LifeCycle_Obj_01    = outputObjList[objIndex].rdpLifeCycleCnt;
            userData.RR_OD_StableFlag_01       = outputObjList[objIndex].rdpTrkObjStableFlag;

            encode_RRCR_0x659(&userData, frame->data, frame->dlc);
            decode_CR_FL_0x659(radarID, frame);
        }

        sendObjCnt++; //已经发送的目标
    }

    if ((sendObjCnt % 2) == 1) {
        encode_RRCR_0x659(&userData, frame->data, frame->dlc);
        decode_CR_FL_0x659(radarID, frame);
    }

    frameCount += 8;

    // 0x66E
    frame = frameArray + frameCount;
    memcpy(frame, &(timestamp->_0x66E), sizeof (Devices::Can::stCanTxMsg));
    frame->valid = true;

//    frame->data[20] = (((userData->RR_OD_CycleCounter & 0xE000U) >> 13) | ((userData->RR_OD_NumOfObjects & 0x1FU) << 3));
//    frame->data[21] = (((userData->RR_OD_NumOfObjects & 0x20U) >> 5) | ((userData->RR_OD_TaskValidFlag & 0x1U) << 2) | ((userData->RR_OD_ExtendedCycleFlag & 0x1U) << 1) | ((RR_OD_EgoVelocity & 0x1FU) << 3));
    frame->data[20] = (frame->data[20] & 0x07) | ((trkValidNum & 0x1FU) << 3);
    frame->data[21] = ((trkValidNum & 0x20U) >> 5) | (frame->data[21] & 0xFE);


    uint16_t Checksum_0x66E_S = CRC16_CCITT_FALSE(frame->data, frame->dlc);

    frame->data[0] = ((Checksum_0x66E_S & 0xFFU) << 0);
    frame->data[1] = ((Checksum_0x66E_S & 0xFF00U) >> 8);


    frameCount += 1;

    // 0x66D
    frame = frameArray + frameCount;
    memcpy(frame, &(timestamp->_0x66D), sizeof (Devices::Can::stCanTxMsg));
    frame->valid = true;
    frameCount += 1;


//    mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mValid = true;
//    mAnalysisWorker->analysisEnd(radarID, Frame16Track);

    return frameCount;
}

void InterpolationBYDHO::setChannelRadarIDBYDHO(int *channelRadarID, int size)
{
    for (int i = 0; i < size && i < (sizeof (mBYDHDChannelRadarID) / sizeof (mBYDHDChannelRadarID[0])); ++i) {
        mBYDHDChannelRadarID[i] = channelRadarID[i];
        qDebug() << __FUNCTION__ << __LINE__ << channelRadarID[i] << mBYDHDChannelRadarID[i];
    }
}

bool InterpolationBYDHO::decode_CR_FL_0x659(quint8 radarID, Devices::Can::stCanTxMsg *frame)
{

    qDebug() << __FUNCTION__ << __LINE__ << "message" << frame->dlc << frame->data[50] << frame->data[51];
    return true;
    if (frame->dlc != 64) {
        return false;
    }

    const uint8_t *data = (const uint8_t *)frame->data;

//    target.Checksum_0x659_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    target.Counter_0x659_S = (data[2]);

//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount; // 注意必须使用引用
    if (data[50] != 0xFF) {
        Target &target = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets[targetCount++];
        target.mProtocolType = ProtocolBYDHO;

        target.mID = (data[50]);
        target.mObstacleProbability = (data[52] & 0x7FU);

        target.mY = ((((data[3] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.0875) - 100);
        target.mX = (((((data[4] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0xFFU) << 4)) * 0.0875) - 179.113);
        target.mVysog = ((((data[6] & 0xFFU) + (((uint16_t)data[7] & 0x7U) << 8)) * 0.125) - 128);
        target.mVxsog = (((((data[7] & 0xF8U) >> 3) + (((uint16_t)data[8] & 0x3FU) << 5)) * 0.125) - 128);
        target.mAy = (((((data[8] & 0xC0U) >> 6) + (((uint16_t)data[9] & 0x7FU) << 2)) * 0.125) - 31.875);
        target.mAx = (((((data[9] & 0x80U) >> 7) + (((uint16_t)data[10] & 0xFFU) << 1)) * 0.125) - 31.875);
        target.mYStd = ((data[11]) * 0.05);
        target.mXStd = ((data[12]) * 0.1);
        target.mVyStd = ((data[13] & 0x7FU) * 0.1);
        target.mVxStd = ((((data[13] & 0x80U) >> 7) + (((uint16_t)data[14] & 0x3FU) << 1)) * 0.2);
        target.mAyStd = ((((data[14] & 0xC0U) >> 6) + (((uint16_t)data[15] & 0x1FU) << 2)) * 0.25);
        target.mAxStd = ((((data[15] & 0xE0U) >> 5) + (((uint16_t)data[16] & 0x1FU) << 3)) * 0.25);
        target.mRCS = (((((data[16] & 0xE0U) >> 5) + (((uint16_t)data[17] & 0x3FU) << 3)) * 0.2) - 51.1);
        target.mTrackFrameLength = ((((data[17] & 0xC0U) >> 6) + (((uint16_t)data[18] & 0x1FU) << 2)) * 0.2);
        target.mTrackFrameWidth = ((((data[18] & 0xE0U) >> 5) + (((uint16_t)data[19] & 0xFU) << 3)) * 0.2);
        target.mTrackFrameAngle = (((((data[19] & 0xF0U) >> 4) + (((uint16_t)data[20] & 0x3FU) << 4)) * 0.01) - 5.11);
//        target.FL_OD_OrientationStd_Obj_00 = ((((data[20] & 0xC0U) >> 6) + (((uint16_t)data[21] & 0x3U) << 2)) * 0.002);
//        target.FL_OD_RefPoint_Obj_00 = ((data[21] & 0xCU) >> 2);
        target.mClass = ((data[21] & 0x70U) >> 4);
        target.mDynamicProperty = (((data[21] & 0x80U) >> 7) + (((uint16_t)data[22] & 0x3U) << 1));
        target.mExistProbability = (((data[22] & 0xFCU) >> 2) + (((uint16_t)data[23] & 0x1U) << 6));
        target.mMirrProblty = ((data[23] & 0xFEU) >> 1);
        target.mTrackSts = (data[24] & 0x3U);
        target.mTrackLifeCycleCnt = (((data[24] & 0xFCU) >> 2) + (((uint32_t)data[25]) << 6) + (((uint32_t)data[26] & 0x3U) << 14));
//        target.FL_OD_StableFlag_00 = ((data[26] & 0x4U) >> 2);


        target.mValid = (target.mX != 0 || target.mY != 0);

//        qDebug() << __FUNCTION__ << __LINE__ << radarID << target.mID << target.mX << target.mY;
    }
    if (data[51] != 0xFF) {
        Target &target = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets[targetCount++];
        target.mProtocolType = ProtocolBYDHO;

        target.mID = (data[51]);
        target.mObstacleProbability = (data[53] & 0x7FU);

        target.mY = (((((data[26] & 0xF8U) >> 3) + (((uint16_t)data[27] & 0x7FU) << 5)) * 0.0875) - 100);
        target.mX = (((((data[27] & 0x80U) >> 7) + (((uint32_t)data[28]) << 1) + (((uint32_t)data[29] & 0x7U) << 9)) * 0.0875) - 179.113);
        target.mVysog = (((((data[29] & 0xF8U) >> 3) + (((uint16_t)data[30] & 0x3FU) << 5)) * 0.125) - 128);
        target.mVxsog = (((((data[30] & 0xC0U) >> 6) + (((uint32_t)data[31]) << 2) + (((uint32_t)data[32] & 0x1U) << 10)) * 0.125) - 128);
        target.mAy = (((((data[32] & 0xFEU) >> 1) + (((uint16_t)data[33] & 0x3U) << 7)) * 0.125) - 31.875);
        target.mAx = (((((data[33] & 0xFCU) >> 2) + (((uint16_t)data[34] & 0x7U) << 6)) * 0.125) - 31.875);
        target.mYStd = ((((data[34] & 0xF8U) >> 3) + (((uint16_t)data[35] & 0x7U) << 5)) * 0.05);
        target.mXStd = ((((data[35] & 0xF8U) >> 3) + (((uint16_t)data[36] & 0x7U) << 5)) * 0.1);
        target.mVyStd = ((((data[36] & 0xF8U) >> 3) + (((uint16_t)data[37] & 0x3U) << 5)) * 0.1);
        target.mVxStd = ((((data[37] & 0xFCU) >> 2) + (((uint16_t)data[38] & 0x1U) << 6)) * 0.2);
        target.mAyStd = (((data[38] & 0xFEU) >> 1) * 0.25);
        target.mAxStd = ((data[39]) * 0.25);
        target.mRCS = ((((data[40] & 0xFFU) + (((uint16_t)data[41] & 0x1U) << 8)) * 0.2) - 51.1);
        target.mTrackFrameLength = (((data[41] & 0xFEU) >> 1) * 0.2);
        target.mTrackFrameWidth = ((data[42] & 0x7FU) * 0.2);
        target.mTrackFrameAngle = (((((data[42] & 0x80U) >> 7) + (((uint32_t)data[43]) << 1) + (((uint32_t)data[44] & 0x1U) << 9)) * 0.01) - 5.11);
//        target.FL_OD_OrientationStd_Obj_01 = (((data[44] & 0x1EU) >> 1) * 0.002);
//        target.FL_OD_RefPoint_Obj_01 = ((data[44] & 0x60U) >> 5);
        target.mClass = (((data[44] & 0x80U) >> 7) + (((uint16_t)data[45] & 0x3U) << 1));
        target.mDynamicProperty = ((data[45] & 0x1CU) >> 2);
        target.mExistProbability = (((data[45] & 0xE0U) >> 5) + (((uint16_t)data[46] & 0xFU) << 3));
        target.mMirrProblty = (((data[46] & 0xF0U) >> 4) + (((uint16_t)data[47] & 0x7U) << 4));
        target.mTrackSts = ((data[47] & 0x18U) >> 3);
        target.mTrackLifeCycleCnt = (((data[47] & 0xE0U) >> 5) + (((uint32_t)data[48]) << 3) + (((uint32_t)data[49] & 0x1FU) << 11));
//        target.FL_OD_StableFlag_01 = ((data[49] & 0x20U) >> 5);

        target.mValid = (target.mX != 0 || target.mY != 0);

//        qDebug() << __FUNCTION__ << __LINE__ << radarID << target.mID << target.mX << target.mY;
    }

    return true;
}

void InterpolationBYDHO::canFrame(int radarID, const Devices::Can::CanFrame &frame)
{
    if (isTrackFrame(radarID, mBYDHDChannelRadarID, frame)) {
//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex();
        mFirst = false;
        saveTimestamps(&mTimestamps, frame);
    } else {
        if (mFirst) {
            mFirst = false;
            mWriter->writeData(Devices::Can::CanFrame::New(frame.deviceIndex(),
                                                           frame.channelIndex(),
                                                           Devices::Can::CanFrame::TX,
                                                           frame.id(),
                                                           (const uint8_t *)frame.data().data(),
                                                           frame.DLC(),
                                                           frame.timestemp(),
                                                           frame.extended(),
                                                           frame.canFD()));
//            qDebug() << __FUNCTION__ << __LINE__ << frame.timestemp() << QDateTime::fromMSecsSinceEpoch(frame.timestemp());
        }
    }

    injection(radarID, frame);
}

} // namespace Analysis
