<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CTBinaryFileTools</class>
 <widget class="QWidget" name="CTBinaryFileTools">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>700</width>
    <height>537</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ChengTech Binary File Tools</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>Binary File:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditBinaryFile">
       <property name="text">
        <string>C:/Users/<USER>/Documents/WXWork/1688856809353255/Cache/File/2023-12/2023-12-12 17-38-12-873.binary</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonBinaryFile">
       <property name="text">
        <string>...</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOpenBinaryFile">
       <property name="text">
        <string>打开</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>导出文件:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditExportFile">
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxExportType">
       <item>
        <property name="text">
         <string>CT Binary</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>ASC</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonExport">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="text">
        <string>导出</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QCheckBox" name="checkBoxRadar_4">
       <property name="text">
        <string>4</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxRadar_5">
       <property name="text">
        <string>5</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxRadar_6">
       <property name="text">
        <string>6</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxRadar_7">
       <property name="text">
        <string>7</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelRadarFrameCount">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>0</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>跳过:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditSkipCount">
       <property name="text">
        <string>-1</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>保存数量:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditSaveCount">
       <property name="text">
        <string>-1</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelWriteIndex">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>0</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTextEdit" name="textEdit"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
