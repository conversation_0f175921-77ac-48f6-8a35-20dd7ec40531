﻿#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>

#include <QSocketNotifier>
#include <QFile>

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    bool init(quint8 masterRadarID = 4, quint8 sloveRadarID = 5);

private slots:
    void on_pushButtonConnectMasterRadar_clicked();

private:
    Ui::MainWindow *ui;


    QSocketNotifier *mSocketNotifier{0};
    QFile mFile;
};
#endif // MAINWINDOW_H
