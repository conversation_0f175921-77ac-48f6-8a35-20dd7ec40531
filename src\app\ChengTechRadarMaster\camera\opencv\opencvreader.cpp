﻿#include "opencvreader.h"

OpenCVReader::OpenCVReader()
{

}

OpenCVReader::OpenCVReader(std::string url, bool isFile) : mURL(url), mIsFile(isFile)
{

}

bool OpenCVReader::open()
{
    close();
    if (mIsFile) {
        if(!mCapture.open(mURL)) {
            return false;
        }
    } else {
        if(!mCapture.open(atoi(mURL.c_str()), cv::CAP_DSHOW)) {
            return false;
        }
        mCapture.set(cv::CAP_PROP_FRAME_WIDTH,VIDEO_WIDTH);
        mCapture.set(cv::CAP_PROP_FRAME_HEIGHT,VIDEO_HIGNT);
    }

    mOpened = true;

    return true;
}

bool OpenCVReader::open(std::string url, bool isFile)
{
    mURL = url;
    mIsFile = isFile;
    return open();
}

bool OpenCVReader::close()
{
    if (mCapture.isOpened()) {
        mCapture.release();
    }

    mOpened = false;
    return true;
}

cv::Mat *OpenCVReader::read()
{
    if (!mCapture.read(mFrame))//读取ing
    {
        return 0;
    }

    return &mFrame;
}
