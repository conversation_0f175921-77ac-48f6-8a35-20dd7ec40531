/**
 * @file adas_vehicle_ctrls.c
 * @brief 控车模块，做一些刹车逻辑控制等与车身控制相关的设计。
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2023-09-27
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2023-09-27 <td>1.0     <td>shaowei     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifdef ALPSPRO_ADAS
#include "adas_vehicle_ctrls.h"
#include "prot_vcan_byd.h"
#elif defined(PC_DBG_FW)
#include "other/temp.h"
#include "app/adas/customizedrequirements/adas_vehicle_ctrls.h"
#else
#include "app/cfg/cfg.h"
#include "adas_vehicle_ctrls.h"
#endif

rctbInfo_t gRctbCtrl;

#ifndef PC_DBG_FW   //上位机不执行此函数
float holdtime = 0;
/**
 * @brief RCTB功能中，把握制动力度、制动时机等细节
 * 
 * @param pSlaveRadar 从雷达信息
 * @param pVDY 车身数据
 */
#if (ALARM_TYPE_RCTB_EN == 1)
static void ADAS_RCTBraking(const SlaveRadarWarningsStatus *pSlaveRadar, const VDY_Info_t *pVDY, float trackTime)
{
    //static int iCnt = 0;
    static float basetracktime = 0.0f;
    static float rctbHold_AccelExitCnt = 0.0f;   // 油门深度(VCU_Accelerograph_Depth_S)大于等于20%，持续时间超过300ms，退出保压
    static float rctbHold_BreakExitCnt = 0.0f;   //制动踏板深度 大于等于20%，持续时间超过300ms，退出保压

    switch (gadasFunctionState.adasRCTBState)
        {
        case 0U: //关闭或者未触发
            gRctbCtrl.timeToEspHoldState = 0U;
            gRctbCtrl.timeToSendRequest = 0U;
            gadasFunctionState.adasRTCBStatehold = 0U;

            if (
                ((gadasFunctionState.adasRCTBWarning) != 0) ||
                ((pSlaveRadar->radarRCTBBrakingL) != 0U))
            {
                gadasFunctionState.adasRCTBState = 1;
                //if((RTC_TO_MS(timer_rtc_count())/100DDD0.0 - brake_interval_time < RCTB_BRAKEINTERVALTIME) && brake_interval_time != 0) //二次刹车间隔时间判断
                if(brake_interval_time < RCTB_BRAKEINTERVALTIME && fabsf(brake_interval_time) > FLOAT_EPS)
                {
                    //gadasFunctionState.adasBSDWarning = 1;     //调试使用，用完删除
                    gadasFunctionState.adasRCTBState = 0U;
                }
            }
            // if(gadasFunctionState.adasRCTBState == 1)
            // {
            // 	gadasFunctionState.adasLCAWarning = 1;
            // }
            break;
        case 1U: //功能被触发
            gRctbCtrl.timeToEspHoldState = 0U;
            gRctbCtrl.startBrkTime = 255U;
            if (gRctbCtrl.timeToSendRequest < 255U)
            {
                gRctbCtrl.timeToSendRequest++;
            }
            // RCTB功能退出

            brake_interval_time = 0.0f;
            rctbHold_AccelExitCnt = 0.0f;
            rctbHold_BreakExitCnt = 0.0f;

            // RCTB制动请求未正常响应而退出 rctbActive如果异常这里会无法退出
            //if ((gRctbCtrl.timeToSendRequest > 50U) && (pVDY->pVDY_ChassisInfo->vdyEspStatus.rctbActive != 1U) // )
            // RCTB 超时未响应直接退出
            if ((gRctbCtrl.timeToSendRequest > 50U))
            {
                //iCnt = 0;
                basetracktime = 0.0f;
                gadasFunctionState.adasRCTBState = 0U; //功能退出
                gRctbCtrl.timeToLastRctbTrigger = 0U; //开始计数
                break;
            }
            // 功能规范对于RCTB保压的车速设定是小于 1kph
            if ((fabs(pVDY->pVDY_DynamicInfo->vdySpeedInmps) < (0.4f)) ||       //车速
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.standStill == 0x1U) // esp反馈静止
            )
            {
				gadasFunctionState.adasRTCBStatehold = 1;
                gadasFunctionState.adasRCTBState = 2; //进入hold状态
                //iCnt = 0;
                basetracktime = 0.0f;
                break;
            }

            break;
        case 2U: // hold
            gRctbCtrl.timeToSendRequest = 0U;
            if (gRctbCtrl.timeToEspHoldState < 255U)
            {
                gRctbCtrl.timeToEspHoldState++;
            }
            if(pVDY->pVDY_ChassisInfo->vdyBrkPedalSts == 1)
            {
                if(gRctbCtrl.startBrkTime == 255U){
                    gRctbCtrl.startBrkTime = gRctbCtrl.timeToEspHoldState;
                }
            }
            else
            {
                gRctbCtrl.startBrkTime = 255U;
            }

            if (pVDY->pVDY_ChassisInfo->vdyAccelerograph_Depth >= 20) 
            {
                rctbHold_AccelExitCnt += trackTime;
            }
            if (pVDY->pVDY_ChassisInfo->vdyBrakeActLevel >= 20) 
            {
                rctbHold_BreakExitCnt += trackTime;
            }
#ifdef ADAS_FUNCTION_A3
            basetracktime += trackTime;
            if ((basetracktime >= RCTB_HOLD_TIME_S) || (rctbHold_AccelExitCnt >= RCTB_ACCEL_DEPTH_HOLD_EXIT_TIME_S) || (rctbHold_BreakExitCnt > RCTB_ACCEL_DEPTH_HOLD_EXIT_TIME_S)) // 2秒后退出 或者油门深度大于20%且持续300ms
            //if ((iCnt++ >= (RCTB_HOLD_TIME / 50)) || (rctbHold_AccelExitCnt >= RCTB_ACCEL_DEPTH_HOLD_EXIT_TIME) || (rctbHold_BreakExitCnt > RCTB_ACCEL_DEPTH_HOLD_EXIT_TIME)) // 2秒后退出 或者油门深度大于20%且持续300ms
#else
            if (iCnt++ >= (RCTB_HOLD_TIME / 50))			// 2秒后退出
#endif
            {
                //iCnt = 0;
                basetracktime = 0.0f;
                rctbHold_AccelExitCnt = 0.0f;
                rctbHold_BreakExitCnt = 0.0f;
                brake_interval_time = 0.0f;
                gadasFunctionState.adasRTCBStatehold = 0U;
                gadasFunctionState.adasRCTBState = 0U; //功能退出
                gRctbCtrl.timeToLastRctbTrigger = 0U; //开始计数
            }
            //gadasFunctionState.adasFCTBWarning = 1;
            break;

        default:
            gadasFunctionState.adasRTCBStatehold = 0U;
            gadasFunctionState.adasRCTBState = 0U; //功能退出
            gRctbCtrl.timeToEspHoldState = 0U;
            gRctbCtrl.timeToSendRequest = 0U;
            break;
        }
}
#endif

/**
 * @brief FCTB功能中，把握制动力度、制动时机等细节
 *
 * @param pSlaveRadar 从雷达信息
 * @param pVDY  车身信息
 */
#if (ALARM_TYPE_FCTB_EN == 1)
static void ADAS_FCTBraking(const SlaveRadarWarningsStatus *pSlaveRadar, const VDY_Info_t *pVDY, float trackTime)
{
    //static int iCnt = 0;
    static float basetracktime = 0.0f;

    switch (gadasFunctionState.adasFCTBState)
        {
        case 0U: //关闭或者未触发
			gadasFunctionState.adasFTCBStatehold = 0U;
            gRctbCtrl.timeToEspHoldState = 0U;
            gRctbCtrl.timeToSendRequest = 0U;
            if (((gadasFunctionState.adasFCTBWarning) != 0) &&
                (gAdasTriggerRecord.isActivatedInXms.name.fctbIsActivatedInXms == 1))
            {
                gadasFunctionState.adasFCTBState = 1; // 右雷达报警
                gadasFunctionState.adasFCTBRequest = 2;
                if (fctb_brake_interval_time < FCTB_BRAKEINTERVALTIME && fabsf(fctb_brake_interval_time) > FLOAT_EPS)
                {
                    gadasFunctionState.adasFCTBState = 0U;
                }
            }
            else if(((pSlaveRadar->radarFCTBBrakingL) != 0U))
            {
                gadasFunctionState.adasFCTBState = 1;	//左雷达报警
                gadasFunctionState.adasFCTBRequest = 1;
                if(fctb_brake_interval_time < FCTB_BRAKEINTERVALTIME && fabsf(fctb_brake_interval_time) > FLOAT_EPS)
                {
                    gadasFunctionState.adasFCTBState = 0U;
                }
            }
            else if (((gadasFunctionState.adasFCTBWarning != 0U) &&
                      (gAdasTriggerRecord.isActivatedInXms.name.fctbIsActivatedInXms == 1)) &&
                     (pSlaveRadar->radarFCTBBrakingL) != 0U)
            {
                gadasFunctionState.adasFCTBState = 1; // 左右同时报警
                gadasFunctionState.adasFCTBRequest = 3;
                if (fctb_brake_interval_time < FCTB_BRAKEINTERVALTIME && fabsf(fctb_brake_interval_time) > FLOAT_EPS)
                {
                    gadasFunctionState.adasFCTBState = 0U;
                }
            }
            else
            {
                ; //donothing
            }
			break;
        case 1U: //功能被触发
            gRctbCtrl.timeToEspHoldState = 0U;
            gRctbCtrl.startBrkTime = 255U;
            if (gRctbCtrl.timeToSendRequest < 255U)
            {
                gRctbCtrl.timeToSendRequest++;
            }
            fctb_brake_interval_time = 0;
            // FCTB制动请求未正常响应而退出 或  深踩油门退出
            if ((gRctbCtrl.timeToSendRequest > 50U) || (pVDY->pVDY_ChassisInfo->vdyAccelerograph_Depth >= 90) || (ADAS_isInterruptFCTB()))
            {
                //iCnt = 0;
                basetracktime = 0.0f;
                gadasFunctionState.adasFCTBState = 0U; //功能退出
                gRctbCtrl.timeToLastRctbTrigger = 0U; //开始计数
                break;
            }
            //brake_interval_time = RTC_TO_MS(timer_rtc_count())/1000.0;
            gadasFunctionState.adasFTCBStatehold = 1;   // hold需要外置 否则可能会因为active和hold不衔接导致不保压. 
            if ((fabs(pVDY->pVDY_DynamicInfo->vdySpeedInmps) < (1.0f)) ||       //车速
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.standStill == 0x1U) // esp反馈静止
            )
            {
                //gadasFunctionState.adasFTCBStatehold = 1;
                gadasFunctionState.adasFCTBState = 2; //进入hold状态
                //iCnt = 0;
                basetracktime = 0.0f;
                break;
            }
            break;
        case 2U: // hold
            gRctbCtrl.timeToSendRequest = 0U;
            if (gRctbCtrl.timeToEspHoldState < 255U)
            {
                gRctbCtrl.timeToEspHoldState++;
            }
            if((pVDY->pVDY_ChassisInfo->vdyBrkPedalSts == 1))
            {
                if(gRctbCtrl.startBrkTime == 255U){
                    gRctbCtrl.startBrkTime = gRctbCtrl.timeToEspHoldState;
                }
            }
            else
            {
                gRctbCtrl.startBrkTime = 255U;
            }

            // cnt计数会受帧周期影响
            //if (iCnt++ >= (FCTB_HOLD_TIME / 50))			// 2秒后退出
            basetracktime += trackTime;
            if ((basetracktime >= (FCTB_HOLD_TIME_S)) || (ADAS_isInterruptFCTB()))
            {
                //iCnt = 0;
                basetracktime = 0.0f;
                fctb_brake_interval_time = 0;
                gadasFunctionState.adasFTCBStatehold = 0U;
                gadasFunctionState.adasFCTBState = 0U; //功能退出
                gRctbCtrl.timeToLastRctbTrigger = 0U; //开始计数
            }
            break;

        default:
			gadasFunctionState.adasFTCBStatehold = 0U;
            gadasFunctionState.adasFCTBState = 0U; //功能退出
            gRctbCtrl.timeToEspHoldState = 0U;
            gRctbCtrl.timeToSendRequest = 0U;
            break;
        }
}
#endif
#endif


/**
 * @brief 制动车辆，调用具体功能的制动函数
 * 
 * @param pVDY 车身数据
 * @param pCFG 配置信息
 * @param pSlaveRadar 从雷达信息
 */
static void ADAS_brakingVehicle(const VDY_Info_t *pVDY, const ADAS_RadarConfiguration_t *pCFG, const SlaveRadarWarningsStatus *pSlaveRadar, float trackTime)
{
    if ((pCFG->radarId == FRONT_LEFT_RADAR_ID) || (pCFG->radarId == FRONT_RIGHT_RADAR_ID))
    {
#if (ALARM_TYPE_FCTB_EN == 1)
#ifndef PC_DBG_FW // 上位机不执行此函数
        ADAS_FCTBraking(pSlaveRadar, pVDY, trackTime);
#endif
#endif
    }

    if ((pCFG->radarId == REAR_LEFT_RADAR_ID) || (pCFG->radarId == REAR_RIGHT_RADAR_ID))
    {
#if (ALARM_TYPE_RCTB_EN == 1)
#ifndef PC_DBG_FW // 上位机不执行此函数
        ADAS_RCTBraking(pSlaveRadar, pVDY, trackTime);
#endif
#endif
    }

    /**
     * @brief other thing
     *
     */
    // to do thing
}

/**
 * @brief 控车模块主函数
 * 
 * @param pVDY 车身数据
 * @param pCFG 配置信息
 * @param pSlaveRadar 从雷达信息
 */
void ADAS_ctrlVehicle(const VDY_Info_t *pVDY, const ADAS_RadarConfiguration_t *pCFG, const SlaveRadarWarningsStatus *pSlaveRadar, float trackTime)
{
    ADAS_brakingVehicle(pVDY, pCFG, pSlaveRadar, trackTime);   //制动车辆
}