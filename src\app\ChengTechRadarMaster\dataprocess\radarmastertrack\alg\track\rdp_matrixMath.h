/**
 * @file rdp_matrixMath.h
 * @brief 
 * <AUTHOR> (s<PERSON><PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef _MATRIX_MATH_H_
#define _MATRIX_MATH_H_
#include <stdint.h>

extern void matrixAdd(uint16_t rows, uint16_t cols, float *A, float *B, float *C);
extern void matrixSub(uint16_t rows, uint16_t cols, float *A, float *B, float *C);
extern void matrixMultiply(uint16_t rows, uint16_t m, uint16_t cols, float *A, float *B, float *C);
extern void matrixTransposeMultiply(uint16_t rows, uint16_t m, uint16_t cols, float *A, float *B, float *C);
extern void matrixInv2(float *A, float *inv);

#endif
