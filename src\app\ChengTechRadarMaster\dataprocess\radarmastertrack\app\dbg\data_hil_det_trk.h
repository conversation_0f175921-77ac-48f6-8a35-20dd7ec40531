/**
 *  @file data_hil_det_trk.h
 *  @brief This file defines the functions for track HIL and detection HIL.
 *  <AUTHOR> (<EMAIL>)
 *  @date 2023-08-09
 *
 *  @details None.
 *  @par 修改日志:
 *  <table>
 *  <tr><th>Date       <th>Version <th>Author  <th>Description
 *  <tr><td>2023-08-09 <td>v0.0.1  <td>erthfw  <td>初始版本
 *  </table>
 *  @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _DATA_HIL_DET_TRK_H_
#define _DATA_HIL_DET_TRK_H_

/****************************************************************************
  DEFINE
 ****************************************************************************/
#include <stdint.h>


/****************************************************************************
  DEFINE
 ****************************************************************************/
/* data_hil_det_trk enable */
#define DATA_HIL_DET_TRK                0   /* 0:Disable, 1:Enable */

#define DATA_HIL_DET_TRK_NONE           0x0
#define DATA_HIL_DET                    0x1
#define DATA_HIL_TRACK                  0x2

/*hil Adas function state.*/
typedef struct HilAdasFunctionState
{
    uint8_t adasBSDFuncState;
    uint8_t adasLCAFuncState;
    uint8_t adasDOWFuncState;
    uint8_t adasRCWFuncState;
    uint8_t adasRCTAFuncState;
    uint8_t adasRCTBFuncState;
    uint8_t adasFCTAFuncState;
    uint8_t adasFCTBFuncState;
} HilAdasFunctionState_t;

/****************************************************************************
  DECLARATION
 ****************************************************************************/
void HIL_procDetTrkHilCanMsg(uint16_t can_id, const uint8_t *rxData, uint8_t len);
float HIL_getDetTrkFrameTime(void);
void HIL_setDetTrkFrameIdx(uint32_t frameIdx);
uint8_t HIL_getDetTrkFrameIdx(void);
uint32_t HIL_getDetTrkFrameCnt(void);
const HilAdasFunctionState_t *HIL_getHilAdasFunctionState(void);


#endif

