﻿#ifndef OBJECTCOORDINATESYSTEM_H
#define OBJECTCOORDINATESYSTEM_H

#include <QWidget>
#include <QMap>
#include <QPixmap>
#include <QMutex>
#include <QVector>
#include <QPointF>

#include "analysis/analysisdataf.h"
#include "analysis/analysisdata.h"
#include "truevalue/hesailider.h"

namespace Views {
namespace ObjectView {

class CoordinateAxis;

typedef struct Object
{
    quint16 mID{0};                             // 目标ID
    double mX{0.0};                             // 横向坐标
    double mY{0.0};                             // 纵向坐标
    double mVx{0.0};                            // 横向速度
    double mVy{0.0};                            // 纵向速度
    double mAx{0.0};                            // 横向加速度
    double mAy{0.0};                            // 纵向加速度
    double mTrackFrameX{0.0};                   // 航迹框横向坐标
    double mTrackFrameY{0.0};                   // 航迹框纵向坐标
    double mGroupId;                            // 航迹框纵向坐标
    bool mShowV{false};                         // 显示速度
    bool mShowAcc{false};                       // 显示加速度
    bool mShowTrackFrame{false};                // 显示航迹框
    QVector<QLineF> mTrackFrameLines;         // 航迹框

    MoveStatus mStateOfMotion{Motionless};      // 动静属性
    ObjectClass mObjectClass{ObjectUnknown};    // 目标分类
    quint8 mStatus;                             // 目标状态
    quint8 mMatchFlag;                          // AB波匹配结果

    quint16 mAlarmTye{0};                       // 告警类型

    QString mText;
    bool mAlarm{false};                         // 告警
    bool mEarlyWarning{false};                  // 上位机预警
    bool mShow{true};             // 在显示范围内
}Object;

typedef QList<Object> Objects;

enum DrawShape
{
    HollowCircle,
    SolidCircle,
    OutlinedRectangle,
    FilledRectangle
};

QStringList drawShapeNames();


typedef struct DrawConfig
{
    bool mShow{true};
    DrawShape mDrawShape{HollowCircle};
    QColor mColor{Qt::red};
    int mSize{8};
}DrawConfig;

typedef struct ObjectsDrawConfig
{
    DrawConfig mDrawConfig[UnknowMoveStatus];

    CalculatorConfig mCalculatorConfig;
}ObjectsDrawConfig;

typedef struct ObjectShowConfig
{
    bool mObjectShow[FrameTargetCount]{false, false};
    bool mRadarDataShow{true};
    bool mShowRoadSideLine{true};
}ObjectShowConfig;

/** @brief 道路模型 */
typedef struct {
    float a;
    float b;
    float c;
    float d;
    float begin;
    float end;
    bool vaild;
}Polynomial;

typedef struct ObjectData
{
    enum ObjectType
    {
        ObjectRaw = FrameRawTarget,
        ObjectTrack,
        ObjectAlarm,
        ObjectBYD200Raw,
        ObjectBYD16Track,
        ObjectBYD3Track,
        ObjectELKTrack,
        ObjectTrueObject,
        ObjectRoadMode,
        ObjectTypeCount
    };

    ObjectData() {
        ObjectsDrawConfig &rawDrawConfig = mObjectsDrawConfig[FrameRawTarget];
        rawDrawConfig.mDrawConfig[Motionless].mDrawShape = OutlinedRectangle;
        rawDrawConfig.mDrawConfig[InwardMovement].mDrawShape = OutlinedRectangle;
        rawDrawConfig.mDrawConfig[OutwardMovement].mDrawShape = OutlinedRectangle;

        rawDrawConfig.mDrawConfig[Motionless].mColor = Qt::green;
        rawDrawConfig.mDrawConfig[InwardMovement].mColor = Qt::red;
        rawDrawConfig.mDrawConfig[OutwardMovement].mColor = Qt::red;

        ObjectsDrawConfig &trackDrawConfig = mObjectsDrawConfig[FrameTrackTarget];
        trackDrawConfig.mDrawConfig[Motionless].mDrawShape = SolidCircle;
        trackDrawConfig.mDrawConfig[InwardMovement].mDrawShape = SolidCircle;
        trackDrawConfig.mDrawConfig[OutwardMovement].mDrawShape = SolidCircle;

        trackDrawConfig.mDrawConfig[Motionless].mColor = Qt::green;
        trackDrawConfig.mDrawConfig[InwardMovement].mColor = Qt::red;
        trackDrawConfig.mDrawConfig[OutwardMovement].mColor = Qt::red;
    }

    Objects mObjects[ObjectTypeCount];

    bool mValid{false};

    double mRoadSideLineX{0.0};
    Polynomial mPolynomial[2];
    ObjectShowConfig mObjectShowConfigs;
    ObjectsDrawConfig mObjectsDrawConfig[FrameTargetCount];
}ObjectData;

typedef struct ObjectCoordinateSettings
{
    bool mDisplayTargetID{true};        ///< 显示目标ID
    bool mDisplayVelocityFeversal{true};///< 显示速度方向
    bool mDisplayTrackFrame{true};      ///< 显示航迹框
    bool mDisplay16TrackTarget{true};   ///< 显示16个目标
    bool mDisplay16TrackFrame{true};   ///< 显示16个目标航迹框
    bool mDisplay16TrackShowV{true};   ///< 显示16个目标速度方向
    bool mDisplayELKOnly{false};        ///< 只显示ELK目标
    bool mDisplayHeSaiLider{false};     ///< 禾赛激光真值
    bool mDisplayVelocityAmbiguity{true}; ///< 速度解模糊
    bool mDisplayHighlightedVelocityAmbiguity{true}; ///< 凸显解模糊
    bool mDisplayAcceleration{false};   ///< 显示加速度

    bool mStatusFiltrateRaw{false};     ///< 目标状态筛选
    int  mStatusFiltrateValueRaw{3};    ///< 目标状态筛选值

    bool mDisplayZeroLine{true};        ///< 显示零线

    double mHorizontalMinRange{-20};    ///< 水平最小值(m)
    double mHorizontalMaxRange{20};     ///< 水平最大值(m)
    double mHorizontalInterval{5};      ///< 水平间隔(m)
    QString mHorizontalUnit{"m"};       ///< 水平单位
    bool mHorizontalUnitShow{false};    ///< 水平单位显示

    double mVerticalMinRange{-80};      ///< 垂直最小值(m)
    double mVerticalMaxRange{80};       ///< 垂直最大值(m)
    double mVerticalInterval{10};        ///< 垂直间隔(m)
    QString mVerticalUnit{"m"};         ///< 垂直单位
    bool mVerticalUnitShow{false};      ///< 垂直单位显示

    double mLocalVehicleLength{5.0};    ///< 自车长度(m)
    double mLocalVehicleWidth{1.8};     ///< 自身宽度(m)
    double mLaneWidth{2.5};             ///< 车道宽度(m)
    bool mDisplayGridLine{false};        ///< 显示网格线
    bool mDisplayLane{true};            ///< 显示车道线

    bool mDOWDisplay{true};             ///< 显示DOW区域
    double mDOWDistanceOfHeadway{4.96};    ///< DOW车头距
    double mDOWDistanceOfBody{-0.25};     ///< DOW车身距
    double mDOWWidth{2.25};             ///< DOW宽度
    double mDOWLength{45};               ///< DOW长度

    bool mBSDDisplay{true};             ///< 显示BSD区域
    double mBSDDistanceOfHeadway{2};    ///< BSD车头距
    double mBSDDistanceOfBody{0.0};     ///< BSD车身距
    double mBSDWidth{3.75};             ///< BSD宽度
    double mBSDLength{7};               ///< BSD长度

    bool mLCADisplay{true};             ///< 显示LCA区域
    double mLCADistanceOfHeadway{4.96}; ///< LCA车头距
    double mLCADistanceOfBody{0.0};     ///< LCA车身距
    double mLCAWidth{3.75};             ///< LCA宽度
    double mLCALength{70};              ///< LCA长度

    double mRearWheelCenterFrontX{0.85927};  ///< 前角雷达到后轮中心横向距离
    double mRearWheelCenterFrontY{3.472542};  ///< 前角雷达到后轮中心纵向距离
    double mRearWheelCenterRearX{0.67762};   ///< 后角雷达到后轮中心横向距离
    double mRearWheelCenterRearY{0.896058};   ///< 后角雷达到后轮中心纵向距离

    QList<AnalysisType> mTargetVeiwAnalysisTypes;
}ObjectCoordinateSettings;

class ObjectCoordinateSystem : public QWidget
{
    Q_OBJECT
public:
    enum ObjectItemType
    {
        OBJECT_ITEM_CAR,
        OBJECT_ITEM_RAW_OBJECT,
        OBJECT_ITEM_TRACK_OBJECT,
        OBJECT_ITEM_COUNT
    };
    explicit ObjectCoordinateSystem(QWidget *parent = nullptr);
    ~ObjectCoordinateSystem();

    void setObjectCoordinateSettings(const ObjectCoordinateSettings &settings);
    const ObjectCoordinateSettings& getObjectCoordinateSettings();

    void setLineOfSightRange(double hMinRange, double hMaxRange, double hInterval,
                             double vMinRange, double vMaxRange, double vInterval);

    void getLineOfSightRange(double &hMinRange, double &hMaxRange, double &hInterval,
                             double &vMinRange, double &vMaxRange, double &vInterval);

    void setLocalVehicleData(double width, double length);
    void getLocalVehicleData(double &width, double &length);

    void setLaneWidth(double width, bool displayLane, bool displayGridLine);
    void getLaneWidth(double &width, bool &displayLane, bool &displayGridLine);

    void setUnit(const QString &hUnit, bool &hShow, const QString &vUnit, bool &vShow);
    void getUnit(QString &hUnit, bool &hShow, QString &vUnit, bool &vShow);

    void setRadarType(RadarType radarType);

    void setShowTarget(QMap<quint8, QVector<bool> > show);

    void setTargetVeiwAnalysisTypes(QList<AnalysisType> &targetVeiwAnalysisTypes);

    void clear();

    void clearTargets(quint8 radarID, int frameType);

    ObjectShowConfig &coordinateSystemShowConfig(quint8 radarID) { return mObjectDatas[radarID].mObjectShowConfigs; }
    auto coordinateSystemDrawConfig(quint8 radarID) { return mObjectDatas[radarID].mObjectsDrawConfig; }

    QVariant coordinateSystemConfig() const;
    void setCoordinateSystemConfig(const QVariant &config);

    void showTargetsF(const Parser::ParsedDataTypedef::TargetsF &targets);
    void showTargets(/*TargetModel*/int targetModel, quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &targets);
    void showTargets(/*TargetModel*/int targetModel, quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &targets, const EndFrameData &endFrameData);

    void showRadarData(/*TargetModel*/int targetModel, quint8 radarID, AnalysisData *analysisData);
    void showTrueSystemTarget( const Target *targets, int length);
    void showHeSaiTargets(Pandar64_Targets *targets);

signals:
    void calculatorConfigChanged(quint8 radarID, int frameType, /*CalculatorConfig*/void *config);

public slots:
    void showBYDTargetsData(quint8 radarID, int frameType, const Targets &targets);

    void zoom(double scaleFactor);
    void showConfig(quint8 radarID);

    void draw();

protected:
    void paintEvent(QPaintEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;

private:
    /** @brief 计算显示目标点 */
    Object calculationObject(quint8 radarID, /*AnalysisFrameType*/int frameType, const Target *target);
    Object calculationObject(ObjectData::ObjectType objectType, const Parser::ParsedDataTypedef::TargetF &target);

    void drawBackground(QPixmap *pixmapGackground);

    /** @brief 绘制目标到图片 */
    void drawTargets(QPixmap *pixmapTargets);

    /** @brief 绘制目标点图形 */
    void drawTargets(QPainter &painter, quint8 radarID, ObjectData &objectData);

    /** @brief 绘制转弯车道线 */
    void drawLane(QPainter &painter);

    /** @brief 绘制路旁线 */
    void drawRoadSideLine(QPainter &painter, quint8 radarID, ObjectData &objectData);

    /** @brief 绘制道路模型 */
    void drawRoadModel(QPainter &painter, quint8 radarID, ObjectData &objectData);

    /** @brief 绘制告警信息 */
    void drawAlarmInfomation(QPainter &painter, quint8 radarID, ObjectData &objectData);

    /** @brief 绘制速度方向 */
    void drawSpeedDirection(QPainter &painter, quint8 radarID, ObjectData &objectData);

    /** @brief 绘制加速度方向 */
    void drawAccDirection(QPainter &painter, quint8 radarID, ObjectData &objectData);

    /** @brief 绘制航迹框 */
    void drawTrackFrame(QPainter &painter, quint8 radarID, Objects &objects);

    /** @brief 绘制目标数据 */
    void drawTargetData(QPainter &painter, quint8 radarID, ObjectData &objectData);

    /** @brief 绘制16个点目标 */
    void draw16TrackTarget(QPainter &painter, quint8 radarID, ObjectData &objectData);

    /** @brief 绘制戴世真值目标 */
    void drawIFS300TrueTarget(QPainter &painter, ObjectData &objectData);

    /** @brief 绘制禾赛激光真值目标 */
    void drawHeSaiLiderTrueTarget(QPainter &painter, quint8 radarID, ObjectData &objectData);

    bool within(double x, double y);

    void setupUi();

    QString mAlias{"Unnamed"};                  ///< 别名


    QPointF mZeroPoint{0.0, 0.0};
    QPointF mZeroPointF{0.0, 0.0};

    CoordinateAxis *mLeftCustomAxis{0};           ///< 左侧坐标尺
    CoordinateAxis *mRightCustomAxis{0};          ///< 右侧坐标尺
    CoordinateAxis *mTopCustomAxis{0};            ///< 上部坐标尺

    ObjectCoordinateSettings mObjectCoordinateSettings;

    double mHorizontalZeroPixel{0};     ///< 水平零点像素点
    double mHorizontalPixelPerMetre{1}; ///< 水平像素比列(pix/m),一个米需要多少个像素点
    double mVerticalZeroPixel{0};       ///< 垂直零点像素点
    double mVerticalPixelPerMetre{1};   ///< 垂直像素比列(pix/m),一个米需要多少个像素点

    int mVehicleGear{0};                 ///< 档位;1:P;2:R;3:N;4:D
    double mRadius{0.0};                ///< 转弯半径

    QPixmap mPixmapAlarm;               ///< 雷达报警图片
    QPixmap mPixmapEarlyWarning;          ///< 上位机预警图片
    QPixmap mPixmapCar;
    QPixmap mPixmapBackground;
    QPixmap mPixmapTargets;
    bool mNewData{false};

    ObjectData mObjectDatas[MAX_RADAR_COUNT];
};

} // namespace ObjectView
} // namespace Views

#endif // OBJECTCOORDINATESYSTEM_H
