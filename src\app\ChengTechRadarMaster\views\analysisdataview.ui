<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AnalysisDataView</class>
 <widget class="QWidget" name="AnalysisDataView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>468</width>
    <height>416</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayoutMain">
   <property name="spacing">
    <number>3</number>
   </property>
   <property name="leftMargin">
    <number>3</number>
   </property>
   <property name="topMargin">
    <number>3</number>
   </property>
   <property name="rightMargin">
    <number>3</number>
   </property>
   <property name="bottomMargin">
    <number>3</number>
   </property>
   <item>
    <layout class="QFormLayout" name="formLayout">
     <property name="horizontalSpacing">
      <number>0</number>
     </property>
     <property name="verticalSpacing">
      <number>0</number>
     </property>
     <item row="0" column="0">
      <widget class="QLabel" name="label0x4DN">
       <property name="text">
        <string>0x4DN:</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLabel" name="labelErrorInformation0x4DN">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label0x4EN">
       <property name="text">
        <string>0x4EN:</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLabel" name="labelErrorInformation0x4EN">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QWidget" name="layoutWidget">
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <item>
        <widget class="QTabWidget" name="tabWidget">
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="tabTrack">
          <attribute name="title">
           <string>跟踪数据</string>
          </attribute>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QTableView" name="tableViewTrackTargets">
             <property name="font">
              <font>
               <pointsize>7</pointsize>
              </font>
             </property>
             <attribute name="horizontalHeaderDefaultSectionSize">
              <number>50</number>
             </attribute>
             <attribute name="verticalHeaderMinimumSectionSize">
              <number>15</number>
             </attribute>
             <attribute name="verticalHeaderDefaultSectionSize">
              <number>15</number>
             </attribute>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="tabRaw">
          <attribute name="title">
           <string>原始数据</string>
          </attribute>
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QTableView" name="tableViewRawTargets">
             <property name="font">
              <font>
               <pointsize>7</pointsize>
              </font>
             </property>
             <attribute name="horizontalHeaderDefaultSectionSize">
              <number>50</number>
             </attribute>
             <attribute name="verticalHeaderMinimumSectionSize">
              <number>15</number>
             </attribute>
             <attribute name="verticalHeaderDefaultSectionSize">
              <number>15</number>
             </attribute>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="tab">
          <attribute name="title">
           <string>16个目标</string>
          </attribute>
          <layout class="QVBoxLayout" name="verticalLayout_7">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QTableView" name="tableViewBYD16Targets">
             <property name="font">
              <font>
               <pointsize>7</pointsize>
              </font>
             </property>
             <attribute name="horizontalHeaderDefaultSectionSize">
              <number>50</number>
             </attribute>
             <attribute name="verticalHeaderDefaultSectionSize">
              <number>25</number>
             </attribute>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="tabVehicleData">
          <attribute name="title">
           <string>车身数据</string>
          </attribute>
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QTableView" name="tableViewVehicleData">
             <attribute name="horizontalHeaderStretchLastSection">
              <bool>true</bool>
             </attribute>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="tabAlarmData">
          <attribute name="title">
           <string>告警数据</string>
          </attribute>
          <layout class="QVBoxLayout" name="verticalLayout_6">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QTableView" name="tableViewAlarmData">
             <attribute name="horizontalHeaderStretchLastSection">
              <bool>true</bool>
             </attribute>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="comboBoxViewTargetType">
         <item>
          <property name="text">
           <string>None</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>原始点</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>跟踪点</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>16个目标</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>车身数据</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>告警数据</string>
          </property>
         </item>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QTableView" name="tableViewTargets">
      <property name="font">
       <font>
        <pointsize>7</pointsize>
       </font>
      </property>
      <attribute name="horizontalHeaderDefaultSectionSize">
       <number>50</number>
      </attribute>
      <attribute name="verticalHeaderMinimumSectionSize">
       <number>15</number>
      </attribute>
      <attribute name="verticalHeaderDefaultSectionSize">
       <number>15</number>
      </attribute>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
