﻿#ifndef MASTER_H
#define MASTER_H

#include "master_global.h"

#include <QObject>

namespace Core {
namespace Internal {

class MainWindow;

class MASTER_EXPORT Master : public QObject
{
    Q_OBJECT
public:
    explicit Master(QObject *parent = nullptr);

    bool initialize();
    void aboutToShutdown();

signals:


private:
    MainWindow *mMainWindow{0};
};

} // namespace Internal
} // namespace Core

#endif // MASTER_H
