﻿#include "chengtechframecheck.h"
#include "ui_chengtechframecheck.h"

#include <devices/devicemanager.h>
#include <devices/deviceconfigdialog.h>
#include <utils/settingshandler.h>
#include <utils/utils.h>
#include "version.h"
#include "ctanalysisworker.h"

#include <QThread>
#include <QDateTime>
#include <QMessageBox>
#include <QInputDialog>

static const char deviceSettingsKey[] = "Devices/DeviceSettings";

ChengTechFrameCheck::ChengTechFrameCheck(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::ChengTechFrameCheck),
    mDeviceManager(new Devices::Can::DeviceManager(this))
{
    qRegisterMetaType<Devices::Can::CanFrame>("CanFrame");
    qRegisterMetaType<QList<Devices::Can::CanFrame> >("QList<CanFrame>");

    ui->setupUi(this);
    this->setWindowTitle( QString("%1 %2")
                    .arg(CHENGTECH_FRAME_CHECK_NAME)
                    .arg(CHENGTECH_FRAME_CHECK_VERSION));

    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceOpened, this, [=](){
        ui->actionOpenDevice->setEnabled(true);
        ui->actionOpenDevice->setText(QString::fromLocal8Bit("关闭设备"));
        ui->actionSelectDevice->setEnabled(false);
    });
    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceClosed, this, [=](){
        ui->actionOpenDevice->setEnabled(true);
        ui->actionOpenDevice->setText(QString::fromLocal8Bit("打开设备"));
        ui->actionSelectDevice->setEnabled(true);
    });

    QThread *analysisThread = new QThread;
    mCTAnalysisWorker = new CTAnalysisWorker;
    mCTAnalysisWorker->moveToThread(analysisThread);

    connect(mCTAnalysisWorker, &CTAnalysisWorker::radarConnected, this, &ChengTechFrameCheck::radarConnected);
    connect(mCTAnalysisWorker, &CTAnalysisWorker::frameError, this, &ChengTechFrameCheck::frameError);

    analysisThread->start();

    loadSettings();
}

ChengTechFrameCheck::~ChengTechFrameCheck()
{
    delete ui;
}

void ChengTechFrameCheck::closeEvent(QCloseEvent *event)
{
    if (mDeviceManager->isOpened()) {
        mDeviceManager->closeDevice();
    }

    savesettings();
}

void ChengTechFrameCheck::radarConnected(int c, int r)
{
    log(QString::fromLocal8Bit("【%1-%2】 雷达连接.").arg(c).arg(r));
}

void ChengTechFrameCheck::frameError(int c, int r)
{
    log(QString::fromLocal8Bit("【%1-%2】 雷达周期出错!").arg(c).arg(r));
}


void ChengTechFrameCheck::on_actionOpenDevice_triggered()
{
    ui->actionOpenDevice->setEnabled(false);
    if (!mDeviceManager->isOpened()) {
        ui->plainTextEditLog->clear();
        mCTAnalysisWorker->setOutTime(800);
        if (!mDeviceManager->openDevice()) {
            ui->actionOpenDevice->setEnabled(true);
            QMessageBox::warning(this, QString::fromLocal8Bit("运行"), QString::fromLocal8Bit("运行失败!\n%1").arg(mDeviceManager->errorString()));
            return;
        }

        workupRadar();
        QThread::msleep(3000);
        workupRadar();
    } else {

        if (!mDeviceManager->closeDevice()) {
            ui->actionOpenDevice->setEnabled(true);
            return;
        }
    }
}

void ChengTechFrameCheck::on_actionSelectDevice_triggered()
{
    Devices::Can::DeviceConfigDialog *dialog = mDeviceManager ?
                new Devices::Can::DeviceConfigDialog(mDeviceManager->deviceSettings(), this) :
                new Devices::Can::DeviceConfigDialog(this);
    connect(dialog, &Devices::Can::DeviceConfigDialog::applied, this, [=](){
        Devices::Can::DeviceSettings deviceSetting = dialog->settings();
        deviceChanged(deviceSetting);
    });
    dialog->exec();
}

void ChengTechFrameCheck::workupRadar()
{
    int deviceIndex = mDeviceManager->deviceIndex();

    for (int i = 0; i < mDeviceManager->channelCount(); ++i)
    {
        QList<Devices::Can::CanFrame> frames;
        Devices::Can::CanFrame frame= Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x12D, QByteArray::fromHex("000000000c00f300"));
        frames << frame << frame << frame;
        //每个通道独立发送，避免因为通道0错误导致不发送通道1的数据
        mDeviceManager->sendFrames(frames);
        frames.clear();
    }
}

void ChengTechFrameCheck::log(const QString &text)
{
    ui->plainTextEditLog->appendPlainText(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz") + ": " + text);
}

void ChengTechFrameCheck::deviceChanged(Devices::Can::DeviceSettings deviceSettings)
{
    bool newDeviceWorker = false;
    mDeviceManager->setDeviceSettings(deviceSettings, newDeviceWorker);
    if (newDeviceWorker)
    {
        Devices::Can::IDeviceWorker *deviceWorker = mDeviceManager->deviceWorker();
        connect(deviceWorker, &Devices::Can::IDeviceWorker::frameRecieved, mCTAnalysisWorker, &CTAnalysisWorker::canFrame);
    }
}

void ChengTechFrameCheck::loadSettings()
{
    Devices::Can::DeviceSettings deviceSettings = Devices::Can::DeviceSettings{Devices::Can::DeviceSettings::ZLG, 41, 0};
    deviceSettings.mDeviceChannelSettings << Devices::Can::DeviceChannelSettings{6, 0, 0, true, 500000, 2000000} << Devices::Can::DeviceChannelSettings{6, 0, 1, true, 500000, 2000000};
    QVariant settings = SETTINGS_GET_VALUE(QLatin1String(deviceSettingsKey));
    if (settings.isValid())
    {
        deviceSettings.setSettings(settings);
    }

    deviceChanged(deviceSettings);

    mCTAnalysisWorker->setOutTime(SETTINGS_GET_VALUE("OutTime", 800).toInt());
}

void ChengTechFrameCheck::savesettings()
{
    SETTINGS_SET_VALUE(QLatin1String(deviceSettingsKey), mDeviceManager->deviceSettings().getSettings());
    SETTINGS_SET_VALUE("OutTime", mCTAnalysisWorker->outTime());
}

void ChengTechFrameCheck::on_actionSetOutTime_triggered()
{
    bool ok;
    int time = QInputDialog::getInt(this,
                                 QString::fromLocal8Bit("设置超时时间"),
                                 QString::fromLocal8Bit("超时时间(ms)"),
                                 mCTAnalysisWorker->outTime(), 0, 1000000, 1, &ok);
    if (ok)
        mCTAnalysisWorker->setOutTime(time);
}

void ChengTechFrameCheck::on_actionReset_triggered()
{
    mCTAnalysisWorker->reset();
}
