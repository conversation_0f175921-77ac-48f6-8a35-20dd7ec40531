/**
* Copyright (c) 2016-2022 ChengTech All rights reserved.
* @file BAIC.h
* @brief brief
* <AUTHOR>
* @date 2023-02-26
* @version 1.0.0
* @note
* description
*/

#ifndef BAIC_H
#define BAIC_H

#include <stdint.h>
#include <stdbool.h>

/**************************************************************************************
 DEFINE
**************************************************************************************/
#define MSG_ID280_SRR_RL_OBJECTLIST01 0x280
#define MSG_ID300_SRR_REAR_LOCKTARGET01 0x300


/**************************************************************************************
 STRUCT
**************************************************************************************/
// 0x280 length = 24
typedef struct CT_SRR_RL_ObjectList01 {
	uint8_t RL_ObjectChecksum_01;
	uint8_t RL_ObjectID_01;
	float RL_ObjectDistLong_01;
	float RL_ObjectDistLat_01;
	float RL_ObjectVrelLong_01;
	uint8_t RL_ObjectCntr_01;
	float RL_ObjectVrelLat_01;
	float RL_ObjectArelLong_01;
	float RL_ObjectArelLat_01;
	float RL_ObjectLength_01;
	float RL_ObjectWidth_01;
	float RL_ObjectHeadYawAgl_01;
	uint8_t RL_ObjectDynProp_01;
	uint8_t RL_ObjectExistnc_01;
	float RL_ObjectRCS_01;
	uint8_t RL_ObjectClass_01;
	float RL_ObjectDistAltitude_01;
	uint8_t RL_ObjectLink_01;
	uint8_t RL_ObjectRefPointPos_01;        // 3 "RR" 2 "RL" 1 "FR" 0 "FL"
	uint8_t RL_ObjectRollingCnt_01;
}CT_SRR_RL_ObjectList01_t;

// 0x300 length = 6
typedef struct CT_SRR_Rear_LockTarget01 {
	uint8_t Lock_Target_Lane_ID_01;         // 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA"
	float Lock_Target_Abscissa_01;
	float Lock_Target_Ordinate_01;
	uint8_t Lock_Target_LonRelative_Spd_01;
	uint8_t Lock_Target_LatRelative_Spd_01;
	uint8_t Lock_Target_MovingState_01;
	uint8_t Lock_Target_State_01;
	uint8_t Lock_Target_Type_01;
}CT_SRR_Rear_LockTarget01_t;


typedef struct BAIC {
	CT_SRR_RL_ObjectList01_t _SRR_RL_ObjectList01;
	CT_SRR_Rear_LockTarget01_t _SRR_Rear_LockTarget01;
}BAIC_t;

typedef struct CANFrame {
	uint32_t id;
	uint8_t dlc;
	unsigned char *data;
}CANFrame;

typedef CANFrame Frame;



/**************************************************************************************
 FUNCTION
**************************************************************************************/
// 0x280
bool decode_SRR_RL_ObjectList01(CT_SRR_RL_ObjectList01_t *userData, uint8_t *data, int length);
// 0x300
bool decode_SRR_Rear_LockTarget01(CT_SRR_Rear_LockTarget01_t *userData, uint8_t *data, int length);

// 0x280
bool encode_SRR_RL_ObjectList01(CT_SRR_RL_ObjectList01_t *userData, uint8_t *data, int length);
// 0x300
bool encode_SRR_Rear_LockTarget01(CT_SRR_Rear_LockTarget01_t *userData, uint8_t *data, int length);

bool proc_BAIC(BAIC_t *userData, Frame *p);

#endif // BAIC_H