﻿#include "cansaveworker.h"

#include <QDateTime>

CANSaveWorker::CANSaveWorker(QObject *parent) : QObject(parent)
{

}

bool CANSaveWorker::startSave(const QString &canFilename, const QString &objFilename)
{
    if (mSaving) {
        stopSave();
    }
    mSaving = false;
    mSaveCounts = 0;

    mFile.setFileName(objFilename);
    if (!mFile.open(QIODevice::WriteOnly)) {
        return false;
    }
    mFile.write("VehicleHeader,");
    mFile.write(gAnalysisTypeNames(VEHICLE_TYPE_BEGIN, VEHICLE_TYPE_END - VEHICLE_TYPE_BEGIN).join(",").toLocal8Bit());
    mFile.write("\r\n");
    mFile.write("AlarmHeader,");
    mFile.write(gAnalysisTypeNames(ALARM_TYPE_BEGIN, ALARM_TYPE_END - ALARM_TYPE_BEGIN).join(",").toLocal8Bit());
    mFile.write("\r\n");
    mFile.write("EndFrameHeader,");
    mFile.write(gAnalysisTypeNames(END_FRAME_TYPE_BEGIN, END_FRAME_TYPE_END - END_FRAME_TYPE_BEGIN).join(",").toLocal8Bit());
    mFile.write("\r\n");
    mFile.write("TargetHeadHeader,");
    mFile.write(gAnalysisTypeNames(TARGET_HEAD_TYPE_BEGIN, TARGET_HEAD_TYPE_END - TARGET_HEAD_TYPE_BEGIN).join(",").toLocal8Bit());
    mFile.write("\r\n");
    mFile.write(gAnalysisTypeNames(TARGET_TYPE_BEGIN, TARGET_TYPE_END - TARGET_TYPE_BEGIN).join(",").toLocal8Bit());
    mFile.write("\r\n");

    mFile.flush();

    mSaving = mDeviceFileASC.openFile(canFilename.toStdString());
    return mSaving;
}

void CANSaveWorker::stopSave()
{
    mFile.close();
    mDeviceFileASC.closeFile();
    mSaving = false;
}

void CANSaveWorker::pushFrame(const CANFrame &frame)
{
    mSafeQueue.push(frame);
}

void CANSaveWorker::save(AnalysisData analysisData)
{
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << mSafeQueue.size();
    std::shared_ptr<CANFrame> pFrame = mSafeQueue.pop();
    do {
        mDeviceFileASC.writeData(*pFrame.get());
        pFrame = mSafeQueue.pop();
    } while(pFrame);

    if (!mFile.isOpen()) {
        return;
    }

    QDateTime saveTime = QDateTime::currentDateTime();
    int radarID = analysisData.mRadarID;
    Targets &targets = analysisData.mTargets[0];
    VehicleData &vehicleData = analysisData.mVehicleData;
    EndFrameData &endFrameData = analysisData.mEndFrameData;
    AlarmData &alarmData = analysisData.mAlarmData;
    QString header = "Begin\r\n";
    header.append(QString("RadarID,%1,CameraIndex,%2").arg(radarID).arg(analysisData.mCameraSaveIndex));
    header.append("\r\n");
    header.append(QString("HeSaiLiderSaveIndex,%1\r\n").arg(0));
    header.append(QString("RadarFrameNumber,%1\r\n").arg(targets.mTargetHeader.mMeasurementCount));
    header.append(QString("SaveFrameNumber,%1\r\n").arg(mSaveCounts));
    header.append(QString("FrameTime,%1\r\n").arg(QDateTime::fromMSecsSinceEpoch(endFrameData.mFrameTime).toString("yyyy-MM-dd hh:mm:ss.zzz")));
    header.append(QString("SystemFrameTime,%1\r\n").arg(QDateTime::fromMSecsSinceEpoch(endFrameData.mSystemFrameTime).toString("yyyy-MM-dd hh:mm:ss.zzz")));
    header.append(QString("SaveTime,%5\r\n").arg(saveTime.toString("yyyy-MM-dd hh:mm:ss.zzz")));

    mFile.write(header.toLocal8Bit());

    QString vehicleText = "VehicleData,";
    for (int i = VEHICLE_TYPE_BEGIN; i < VEHICLE_TYPE_END; ++i)
    {
        vehicleText.append(QString("%1,").arg(vehicleData.value((AnalysisType)i)));
    }
    vehicleText.append("\r\n");
    mFile.write(vehicleText.toLocal8Bit());

    QString alarmText = "AlarmData,";
    for (int i = ALARM_TYPE_BEGIN; i < ALARM_TYPE_END; ++i)
    {
        alarmText.append(QString("%1,").arg(alarmData.value((AnalysisType)i)));
    }
    alarmText.append(QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4DN, 16).toHex(' ') + ",");
    alarmText.append(QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4EN, 16).toHex(' ') + ",");
    alarmText.append("\r\n");
    mFile.write(alarmText.toLocal8Bit());

    QString trueText = "TrueBegin\r\n";
    quint64 total = 0;
    const Target *trueTarget = targets.mTargets;
    for (int i = 0; i < 5; ++i, ++trueTarget)
    {
        if (!trueTarget->mValid)
        {
//            continue;
        }

        for (int j = TARGET_TYPE_BEGIN; j < TARGET_TYPE_END; ++j)
        {
            trueText.append(QString("%1,").arg(trueTarget->value((AnalysisType)j)));
        }
        trueText.append("\r\n");
        total++;
    }

    trueText.append("TrueEnd\r\n");
    mFile.write(trueText.toLocal8Bit());

    QString endFrameText = "EndFrameData,";
    for (int i = END_FRAME_TYPE_BEGIN; i < END_FRAME_TYPE_END; ++i)
    {
        endFrameText.append(QString("%1,").arg(endFrameData.value((AnalysisType)i)));
    }
    endFrameText.append("\r\n");
    mFile.write(endFrameText.toLocal8Bit());

    QString targetHeadText = "TargetHeadData,";
    for (int i = TARGET_HEAD_TYPE_BEGIN; i < TARGET_HEAD_TYPE_END; ++i)
    {
        targetHeadText.append(QString("%1,").arg(targets.mTargetHeader.value((AnalysisType)i)));
    }
    targetHeadText.append("\r\n");
    mFile.write(targetHeadText.toLocal8Bit());

    QString targetsText = "TargetBegin\r\n";
    total = 0;
//    if (targets.mValid)
    {
        const Target *target = targets.mTargets;
        for (int i = 0; i < targets.mTargetCount; ++i, ++target)
        {
            if (!target->mValid)
            {
                continue;
            }

            for (int j = TARGET_TYPE_BEGIN; j < TARGET_TYPE_END; ++j)
            {
                targetsText.append(QString("%1,").arg(target->value((AnalysisType)j)));
            }
            targetsText.append("\r\n");
            total++;
        }
    }
    mFile.write(targetsText.toLocal8Bit());

    QString tail = QString("TargetEnd,Total,%1\r\nEnd\r\n").arg(total);
    mFile.write(tail.toLocal8Bit());


    mSaveCounts++;
}
