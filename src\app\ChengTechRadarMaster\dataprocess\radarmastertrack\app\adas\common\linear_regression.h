﻿/**
 * @file linear_regression.h
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2024-11-26
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-11-26 <td>1.0     <td>Will <PERSON>     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef __LINEAR_REGRESSION_H__
#define __LINEAR_REGRESSION_H__

#ifdef ALPSPRO_ADAS
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "rdp/track/data_process/rdp_interface.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/common/linear_regression.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#elif defined(PC_DBG_FW)
#include "app/system_mgr/typedefs.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/common/linear_regression.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "other/temp.h"
#else
#include "app/adas/generalalg/adas_manager.h"
#include "common/include/vehicle_cfg.h"
#endif

void ADAS_LinearRegression_FirstOrder(const ALARM_OBJECT_T *pobjAlm,
                                      const OBJ_NODE_STRUCT *pobjPath,
                                      const uint8_t i,
                                      const OBJ_ADAS_TYPE_ENUM type,
                                      const uint8_t n,
                                      float *normalize_x,
                                      float *normalize_y,
                                      float *w,
                                      float *b);
void ADAS_DataNormalizeToMeans(const ALARM_OBJECT_T* pobjAlm,
                               OBJ_NODE_STRUCT* pobjPath,
                               const uint8_t i,
                               const OBJ_ADAS_TYPE_ENUM type,
                               const uint8_t n,
                               float* normalize_x,
                               float* normalize_y);
void ADAS_calculateMovingAverage(const float sample, const uint8_t i, uint8_t n, uint8_t lifecycle, float *avgVel);
void ADAS_calculateWeightedAverage(const float sample, const uint8_t i, uint8_t n, uint8_t lifecycle, float *avgVel);
float ADAS_CalculateTotalMSE(const ALARM_OBJECT_T *pobjAlm,
                             const OBJ_NODE_STRUCT *pobjPath,
                             const uint8_t i,
                             const uint8_t n,
                             const float *normalize_x,
                             const float *normalize_y,
                             const float w,
                             const float b);
float ADAS_CalculateTotalMSE_NonI(const OBJ_NODE_STRUCT *pobjPath,
                                  const uint8_t n,
                                  const float w,
                                  const float b,
                                  const bool ismove);
float ADAS_CalculateRSquared(const ALARM_OBJECT_T *pobjAlm,
                             const OBJ_NODE_STRUCT *pobjPath,
                             const uint8_t i,
                             const uint8_t n,
                             float *normalize_x,
                             float *normalize_y,
                             const float w,
                             const float b);
float ADAS_CalculateVariance(const ALARM_OBJECT_T *pobjAlm,
                             const OBJ_NODE_STRUCT *pobjPath,
                             const uint8_t i,
                             const uint8_t n,
                             float *normalize_x,
                             float *normalize_y);
float ADAS_CalculateSinglePointMSE(const float x,
                                   const float y,
                                   const float w,
                                   const float b);
float ADAS_FCTAB_calculatedExtremum(const ALARM_OBJECT_T *pobjAlm,
                                    const OBJ_NODE_STRUCT *pobjPath,
                                    const uint8_t i,
                                    const VDY_Info_t *pVDY,
                                    const OBJ_ADAS_TYPE_ENUM type,
                                    float *normalize_x,
                                    float *normalize_y,
                                    const float x,
                                    const float y,
                                    const float w,
                                    const float b);
float ADAS_RCTAB_calculatedExtremum(const ALARM_OBJECT_T *pobjAlm,
                                    const OBJ_NODE_STRUCT *pobjPath,
                                    const uint8_t i,
                                    const VDY_Info_t *pVDY,
                                    const OBJ_ADAS_TYPE_ENUM type,
                                    float *normalize_x,
                                    float *normalize_y,
                                    const float x,
                                    const float y,
                                    const float w,
                                    const float b,
                                    st_Rctb_pre *rctbPre);
float ADAS_DOW_calculatedExtremum(const ALARM_OBJECT_T* pobjAlm,
                                  OBJ_NODE_STRUCT* pobjPath,
                                  const uint8_t i,
                                  const VDY_Info_t* pVDY,
                                  const OBJ_ADAS_TYPE_ENUM type,
                                  float* normalize_x,
                                  float* normalize_y,
                                  const float x_new,
                                  const float y_new,
                                  const float w,
                                  const float w1,
                                  const float w2,
                                  const float b);

#endif





