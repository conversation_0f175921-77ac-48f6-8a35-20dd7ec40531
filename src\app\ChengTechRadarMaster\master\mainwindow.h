﻿#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <utils/appmainwindow.h>
#include <devices/ideviceworker.h>
#include "generalsettings.h"

#include <QMap>
#include <QCameraInfo>

class QLabel;
class QSplitter;
class DataProcessForm;
class DebugControlForm;
class statisticsDialog;

class HeSaiLiderWorker;

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Analysis {
class AnalysisManager;
}

namespace Views {
class ViewsManager;
}

//namespace Playback {
class DAQManager;
//}

namespace Camera {
class MulitCamera;
}

class FrameConvertForm;
class DAQCalterahCtrl;
class ChannelAndFrameMonitorForm;
class UDSToolBoxForm;
class WakeUpTestForm;

namespace Core {

class SaveManager;

namespace Internal {

class MainWindow : public Utils::AppMainWindow
{
    Q_OBJECT
public:
    enum RadarType {
        BYD,
        BAIC,
        GEELY,
        Hozon
    };
    enum ROLE{
        ROLE_DRIVE_NULL = 0,
        ROLE_DRIVE_TEST, //路测
        ROLE_AFTER_SALES, //售后
    };

    explicit MainWindow();

    void init();
    void initialized();
    void aboutToShutdown();

signals:
    void sigStartHeSaiRadar(int frameCount, bool save4, const QString &ip, quint16 port, bool anyIP);

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    /** @brief 运行 */
    void run();
    /** @brief 选择设备 */
    void selectDevice();
    /** @brief 雷达输出模式 */
    void radarOutputMode();
    /** @brief 雷达输出2DFFT */
    void radarOutput2DFFT();
    /** @brief 长时间唤醒雷达 */
    void rouseRadarAllTheTime();
    /** @brief 唤醒雷达 */
    void wakeupRadar();
    /** @brief 通用设置 */
    void generalSettings();
    /** @brief 通用设置应用 */
    void generalSettingsApply();
    /** @brief 禾赛激光雷达设置 */
    void heSaiLiderSettings();
    void calculationSetting();
    /** @brief 【菜单】【工具】【雷达配置】 */
    void radarConfig();
    /** @brief 打开命令行 */
    void canCommandLine();
    /** @brief 售后（动态）标定 */
    void afterSalecalibration();
    /** @brief 下线（静态）标定 */
    void staticSalecalibration();
    /** @brief 前雷达标定 */
    void radarCalibration();
    /** @brief 雷达复位标定 */
    void radarReset();
    /** @brief 真值系统 */
    void newTrueSystem();
    /** @brief 调试系统 */
    void newDebugSystem();
    /** @brief 数据处理调试 */
    void newDebugProcess();
    /** @brief 刷新摄像头信息 */
    void refreshCameraInfo();
    /** @brief 添加摄像头 */
    void addCamera();
    /** @brief 添加摄像头 */
    void addCamera(const QCameraInfo &cameraInfo, QSize cameraSzie);
    /** @brief 快捷标记 */
    void newShortcutMarkers();
    /** @brief Binalry File工具 */
    void showBinalryFileTools();
    /** @brief 文件批量处理 */
    void fileBatchParsing();
    /** @brief ADC采集和回放 */
    void showAdcAndHil();
    /** @brief EOL功能 */
    void showEol();
    /** @brief 获取雷达版本 */
    void getRadarVersion();
    /** @brief UDS相关功能 */
    void UDSToolBox();
    /** @brief 唤醒测试 */
    void wakeUpTest();
    /** @brief 精度测试切换按钮 */
    void resolutionTest();
    /** @brief 弱目标测试切换按钮 */
    void weakObjTest();
    /** @brief ECU刷写 */
    void ECUUpdate();
    /** @brief 检测点概率统计信息 */
    void deteStatisticalSettings();


    /******************************************************************
     * 保持以下函数在后面
    ******************************************************************/
    /** @brief 用户手册 */
    void userManual();
    /** @brief 关于 */
    void about();

private:
    /** @brief 设备发生改变 */
    void deviceChanged(Devices::Can::DeviceSettings deviceSettings, Devices::Can::DeviceSettings deviceSettings2);
    /** @brief 新分析数据显示 */
    bool newAnalysisDataView(quint8 radarID);
    /** @brief 新目标显示 */
    bool newObjectView(const QVariant &settings);

    /** @brief 恢复设备设置 */
    void restoreDeviceSettings();
    /** @brief 恢复分析数据显示 */
    void restoreAnalysisDataViews();
    /** @brief 恢复目标显示 */
    void restoreObjectViews();
    /** @brief 恢复真值系统显示 */
    void restoreTrueSystemViews();
    /** @brief 恢复调试系统显示 */
    void restoreDebugSystemViews();
    /** @brief 恢复数据处理调试显示 */
    void restoreDataProcessDebugViews();
    /** @brief 恢复摄像头 */
    void restoreCamera();
    /** @brief 恢复快捷标记 */
    void restoreShortcutMarkers();
    /** @brief 恢复窗口状态 */
    void restoreWindowState();
    /** @brief 恢复ADC窗口显示 */
    void restoreAdcSystemViews();
    /** @brief 恢复保存设置 */
    void restoreSaveSetting();
    /** @brief 恢复通用设置 */
    void restoreGeneralSetting();
    /** @brief 恢复检测点概率统计信息区域设置 */
    void restoreDeteStatisticalSetting();

    /** @brief 恢复默认菜单动作 */
    void registerDefaultActions();

    /** @brief 读取配置 */
    void readSettings();
    /** @brief 保存配置 */
    void saveSettings();
    /** @brief 保存摄像头配置 */
    void saveCameraSettings();
    /** @brief 保存窗口配置 */
    void saveWindowSettings();
    /** @brief 保存【保存】配置 */
    void saveSaveSettings();
    /** @brief 保存通用设置 */
    void saveGeneralSetting();
    /** @brief 检测点概率统计信息区域设置 */
    void saveDeteStatisticalSetting();

    /** @brief 设置界面 */
    void setupUi();

private: //角色相关
    void initRole();
    QString roleStr();
    void initCentralWidget();
    void initStatusBar();

    void initMenuBar();
    void initFileMenu();
    void initRunMenu();
    void initViewMenu();
    void initSetMenu();
    void initDeviceMenu();
    void initCameraMenu();
    void initFunctionMenu();
    void initToolMenu();
    void initHelpMenu();

    void initToolBar();

private:
    QSplitter *mObectViewSplitter{0};

    QMenu *mMenuViews{0};
    QMenu *mMenuAnalysisDataViews{0};
    QMenu *mMenuObjectViews{0};
    QMenu *mMenuCamera{0};

    // actions
    QAction *mActionRun{0};

    QAction *mActionRunSave{0};

    QAction *mActionCalculationSetting{0};

    QAction *mActionSelectDevice{0};

    QAction *mActionResolutionTest{0};     //静态测试分辨率测试模式按钮

    QAction *mActionWeakObjTest{0};     //行人远离靠近测试按钮

    QAction *mActionDisplayRawTarget{0};
    QAction *mActionDisplayTrackTarget{0};
    QAction *mActionDisplay2DFFT{0};
    QAction *mActionRouseRadarAllTheTime{0};
    QAction *mActionRouseRadarAllTheTimeBAIC{0};//北汽【长时间唤醒雷达】
    QAction *mActionRouseRadarAllTheTimeGEELY{0}; //吉利【长时间唤醒雷达】
    QAction *mActionRouseRadarAllTheTimeHozon{0}; //合众【长时间唤醒雷达】

    QAction *mActionRadarConfig{0};
    QAction *mActionCanCommandLine{0};
    QAction *mActionAfterSalecalibration{0};
    QAction *mActionStaticSalecalibration{0};
    QAction* mActionRadarCalibration{0};

    QAction *mActionSWVersion{0};
    QAction *mActionShortcutMarkers{0};
    QAction *mActionDebugSystem{0};
    QAction *mActionDebugProcess{0};
    QAction *mActionTrueSystem{0};


    QStatusBar *mStatusBar{0};

    QLabel *mLabelSaveFilename;

    DataProcessForm *mDataProcessForm{0};
    DebugControlForm *mDebugControlForm{0};

    GeneralSettings::Settings mGeneralSettings;

    HeSaiLiderWorker *mHeSaiLiderWorker{0};
    Analysis::AnalysisManager *mAnalysisManager{0};
    Devices::Can::DeviceManager *mDeviceManager{0};
    Views::ViewsManager *mViewsManager{0};
    /*Playback::*/DAQManager *mDAQManager{0};
    SaveManager *mSaveManager{0};

    QMap<QString /*description + deviecname*/, Camera::MulitCamera*> mListCameraInfo;     ///< 摄像头信息

    QTimer *mTimerSystemTime{0};
    quint64 mRunStartTime{0};

    QTimer *mTimerWakeupRadar{0};

    quint8 mRadarOutPutModeLoopTime{0}; //RadarOutPutMode循环次数

    quint8 mRadarResolutionTestModeLoopTime{0}; //RadarOutPutMode循环次数

    RadarType mRadarType{BYD};
    bool    mSendOutputModeFrame{false};
    FrameConvertForm* mFrameConvertForm{0};
    ChannelAndFrameMonitorForm* mChannelAndFrameMonitorForm{0};
    DAQCalterahCtrl* mDAQCtrl{0};
    UDSToolBoxForm* mUDSBox{NULL};
    WakeUpTestForm* mWakeUpTest{NULL};

    ROLE mRole{ROLE_DRIVE_NULL};

    statisticsDialog *mDeteStatistics;
};

} // namespace Internal
} // namespace Core

#endif // MAINWINDOW_H
