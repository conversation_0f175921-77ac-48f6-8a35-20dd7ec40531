<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>trueTargetsMonitor</class>
 <widget class="QWidget" name="trueTargetsMonitor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>961</width>
    <height>656</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>雷达ID：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxRadarID">
       <item>
        <property name="text">
         <string>4</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>5</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>6</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>7</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>跟踪点ID：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditTargetID"/>
     </item>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>自动搜寻范围:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditSearchRang"/>
     </item>
     <item>
      <widget class="QLabel" name="label_5">
       <property name="text">
        <string>真值点索引</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditTrueTargetIndex"/>
     </item>
     <item>
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>搜寻结果：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditSearchResult">
       <property name="readOnly">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonStart">
       <property name="text">
        <string>开始</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonStop">
       <property name="text">
        <string>结束</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonClear">
       <property name="text">
        <string>清除</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QPushButton" name="pushButtonExportPDF">
       <property name="text">
        <string>导出PDF</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxShowTrue">
       <property name="text">
        <string>真值</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxShowTarget">
       <property name="text">
        <string>指定跟踪点</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxShowSearch">
       <property name="text">
        <string>搜寻跟踪点</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QGridLayout" name="gridLayout"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
