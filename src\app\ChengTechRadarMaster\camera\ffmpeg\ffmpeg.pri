win32: {
contains(QT_ARCH, i386) {
    FFMPEG_HOME=$$MASTER_3RDPARTY_LIBRARY_PATH/ffmpeg/x86-windows
} else {
    FFMPEG_HOME=$$MASTER_3RDPARTY_LIBRARY_PATH/ffmpeg/x64-windows
}
}

INCLUDEPATH += $$FFMPEG_HOME/include
DEPENDPATH += $$FFMPEG_HOME/include

CONFIG(debug, debug|release): FFMPEG_HOME = $$FFMPEG_HOME/debug

LIBS += -L$$FFMPEG_HOME/lib \
    -lavcodec \
    -lavdevice \
    -lavfilter \
    -lavformat \
    -lavutil \
#    -lpostproc \
    -lswresample \
    -lswscale

HEADERS += \
    $$PWD/ffmpegcamera.h \
    $$PWD/ffmpeginitializer.h \
    $$PWD/ffmpegreader.h \
    $$PWD/ffmpegwirter.h

SOURCES += \
    $$PWD/ffmpegcamera.cpp \
    $$PWD/ffmpeginitializer.cpp \
    $$PWD/ffmpegreader.cpp \
    $$PWD/ffmpegwirter.cpp
