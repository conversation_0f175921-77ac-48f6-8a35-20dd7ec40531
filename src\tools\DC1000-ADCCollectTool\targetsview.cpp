﻿#include "targetsview.h"

#include "targetsviewconfigdialog.h"

#include <QtMath>
#include <QPainter>
#include <QDebug>

QVariant TargetsView::ViewSettings::getSettings() const
{
    QMap<QString, QVariant> config;
    config["Xmin"]                   = mXmin;
    config["Xmax"]                   = mXmax;
    config["XInterval"]              = mXInterval;
    config["Ymin"]                   = mYmin;
    config["Ymax"]                   = mYmax;
    config["YInterval"]              = mYInterval;
    config["LocalVehicleLengthHalf"] = mLocalVehicleLengthHalf;
    config["LocalVehicleWidthHalf"]  = mLocalVehicleWidthHalf;
    config["PointSizeRaw"]           = mPointSizeRaw;
    config["PointSizeRawHalf"]       = mPointSizeRawHalf;
    config["PointSizeTrack"]         = mPointSizeTrack;
    config["PointSizeTrackHalf"]     = mPointSizeTrackHalf;
    config["DisplayIDLabelRaw"]      = mDisplayIDLabelRaw;
    config["DisplayIDLabelTrack"]    = mDisplayIDLabelTrack;

    QList<QVariant> listPointDisplay;
    for (int i = 0; i < sizeof(mPointDisplay) / sizeof(mPointDisplay[0]); ++ i) {
        QMap<QString, QVariant> config;
        config["Display"] = mPointDisplay[i].mDisplay;
        config["Color"] = mPointDisplay[i].mColor;
        listPointDisplay << config;
    }
    config["PointDisplay"]              = listPointDisplay;

    return config;
}

void TargetsView::ViewSettings::setSettings(const QVariant &settings)
{
#define VALUE_UINT(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toUInt() : DEFAULT
#define VALUE_BOOL(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toBool() : DEFAULT
#define VALUE_DOUBLE(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toDouble() : DEFAULT
#define VALUE_FLOAT(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toFloat() : DEFAULT
#define VALUE_COLOR(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].value<QColor>() : DEFAULT
    QMap<QString, QVariant> config = settings.toMap();
    mXmin                   = VALUE_FLOAT("Xmin", -20.0f);
    mXmax                   = VALUE_FLOAT("Xmax", 20.0f);
    mXInterval              = VALUE_FLOAT("XInterval", 5.0f);
    mYmin                   = VALUE_FLOAT("Ymin", -10.0f);
    mYmax                   = VALUE_FLOAT("Ymax", 250.0f);
    mYInterval              = VALUE_FLOAT("YInterval", 50.0f);
    mLocalVehicleLengthHalf = VALUE_FLOAT("LocalVehicleLengthHalf", 2.5f);
    mLocalVehicleWidthHalf  = VALUE_FLOAT("LocalVehicleWidthHalf", 0.9f);
    mPointSizeRaw           = VALUE_FLOAT("PointSizeRaw", 3.0f);
    mPointSizeRawHalf       = VALUE_FLOAT("PointSizeRawHalf", 1.5f);
    mPointSizeTrack         = VALUE_FLOAT("PointSizeTrack", 15.0f);
    mPointSizeTrackHalf     = VALUE_FLOAT("PointSizeTrackHalf", 7.5f);
    mDisplayIDLabelRaw      = VALUE_BOOL("DisplayIDLabelRaw", false);
    mDisplayIDLabelTrack    = VALUE_BOOL("DisplayIDLabelTrack", true);

    QList<QVariant> listPointDisplay = config["PointDisplay"].toList();
    for (int i = 0; i < listPointDisplay.size() && i < sizeof(mPointDisplay) / sizeof(mPointDisplay[0]); ++ i) {
        QMap<QString, QVariant> config = listPointDisplay[i].toMap();
        mPointDisplay[i].mDisplay = VALUE_BOOL("Display", true);
        mPointDisplay[i].mColor   = VALUE_UINT("Color", 0xFFFFFFFF);
    }
}

TargetsView::TargetsView(QWidget *parent) : QWidget(parent)
{
    qRegisterMetaType<TargetsView::ViewSettings>("TargetsView::ViewSettings");
}

void TargetsView::config()
{
    TargetsViewConfigDialog *dialog = new TargetsViewConfigDialog(mViewSettings, this);
    connect(dialog, &TargetsViewConfigDialog::changeSettings, this, &TargetsView::changeSettings);
    dialog->exec();
}

QVariant TargetsView::getSettings() const
{
    return mViewSettings.getSettings();
}

void TargetsView::setSettings(const QVariant &settings)
{
    mViewSettings.setSettings(settings);
    changeSettings(mViewSettings);
}

void TargetsView::targets(AnalysisData data)
{
    mAnalysisData = data;
    update();
}

void TargetsView::changeSettings(const TargetsView::ViewSettings &settings)
{
    mViewSettings = settings;
    drawBackground();
    update();
}

void TargetsView::drawBackground()
{
    float width = this->width();
    float height = this->height();
    {
        QMutexLocker locker(&mMutex);
        mPixmapBackground = QPixmap(this->width(), this->height());
        mPixmapBackground.fill(Qt::black);
    }

//    qDebug() << __FUNCTION__ << __LINE__
//             << mViewSettings.mXmax << mViewSettings.mXmin << mViewSettings.mXInterval
//             << mViewSettings.mYmax << mViewSettings.mYmin << mViewSettings.mYInterval
//             << mViewSettings.mPointSize << mViewSettings.mPointSizeHalf;
    mXPixelPerMetre = width / (mViewSettings.mXmax - mViewSettings.mXmin);
    mYPixelPerMetre = height / (mViewSettings.mYmax - mViewSettings.mYmin);
    mZeroPointF.setX(mXPixelPerMetre * -mViewSettings.mXmin);
    mZeroPointF.setY(mYPixelPerMetre * mViewSettings.mYmax);


    QPainter painter;
    painter.begin(&mPixmapBackground);

    float len = 3;

    painter.save();

//    QPixmap pixmap = QPixmap(":/images/car.png");
//    mPixmapCar = pixmap.scaled(mViewSettings.mLocalVehicleWidthHalf * 2 * mXPixelPerMetre,
//                           mViewSettings.mLocalVehicleLengthHalf * 2 * mYPixelPerMetre,
//                      Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
//    painter.drawPixmap(mZeroPointF.x() - mXPixelPerMetre * mViewSettings.mLocalVehicleWidthHalf,
//                       mZeroPointF.y() - mYPixelPerMetre * mViewSettings.mLocalVehicleLengthHalf,
//                       mPixmapCar);

    painter.setPen(QPen(Qt::white, 1, Qt::SolidLine));
    painter.translate(mZeroPointF.x(), 0);
    for (float i = 0;  i < mViewSettings.mXmax; i += mViewSettings.mXInterval) {
        for (int j = 5; j <= 10; j += 5) {
            if (j == 10) {
                len = 5;
                painter.drawText((i + j * mViewSettings.mXInterval / 10) * mXPixelPerMetre, 10, QString::number((i + j * mViewSettings.mXInterval / 10)));
            } else if (j == 5) {
                len = 3;
            } else {
                len = 1;
            }
            painter.drawLine((i + j * mViewSettings.mXInterval / 10) * mXPixelPerMetre, 0, (i + j * mViewSettings.mXInterval / 10) * mXPixelPerMetre, len);
        }
    }
    for (float i = 0;  i > mViewSettings.mXmin; i -= mViewSettings.mXInterval) {
        for (int j = 5; j <= 10; j += 5) {
            if (j == 10) {
                len = 5;
                painter.drawText((i - j * mViewSettings.mXInterval / 10) * mXPixelPerMetre, 10, QString::number(-(i - j * mViewSettings.mXInterval / 10)));
            } else if (j == 5) {
                len = 3;
            } else {
                len = 1;
            }
            painter.drawLine((i - j * mViewSettings.mXInterval / 10) * mXPixelPerMetre, 0, (i - j * mViewSettings.mXInterval / 10) * mXPixelPerMetre, len);
        }
    };
    painter.setPen(QPen(Qt::darkGray, 1, Qt::DotLine));
    for (float i = 1.875; i < mViewSettings.mXmax; i += 3.75) {
         painter.drawLine(i * mXPixelPerMetre, 0,
                          i * mXPixelPerMetre, height);
    }

    for (float i = -1.875; i > mViewSettings.mXmin; i -= 3.75) {
         painter.drawLine(i * mXPixelPerMetre, 0,
                          i * mXPixelPerMetre, height);
    }


    painter.setPen(QPen(Qt::darkRed, 1, Qt::DotLine));
    painter.drawLine(0, 0, 0, height);
    painter.restore();

    painter.save();
    painter.setPen(QPen(Qt::white, 1, Qt::SolidLine));
    painter.translate(0, mZeroPointF.y());
    for (float i = 0;  i < mViewSettings.mYmax; i += mViewSettings.mYInterval) {
        for (int j = 5; j <= 10; j += 5) {
            if (j == 10) {
                len = 5;
                painter.drawText(0, -(i + j * mViewSettings.mYInterval / 10) * mYPixelPerMetre, QString::number((i + j * mViewSettings.mYInterval / 10)));
            } else if (j == 5) {
                len = 3;
            } else {
                len = 1;
            }
            painter.drawLine(0, -(i + j * mViewSettings.mYInterval / 10) * mYPixelPerMetre, len, -(i + j * mViewSettings.mYInterval / 10) * mYPixelPerMetre);
        }
    }
    for (float i = 0;  i > mViewSettings.mYmin; i -= mViewSettings.mYInterval) {
        for (int j = 5; j <= 10; j += 5) {
            if (j == 10) {
                len = 5;
                painter.drawText(0, -(i - j * mViewSettings.mYInterval / 10) * mYPixelPerMetre, QString::number(-(i - j * mViewSettings.mYInterval / 10)));
            } else if (j == 5) {
                len = 3;
            } else {
                len = 1;
            }
            painter.drawLine(0, -(i - j * mViewSettings.mYInterval / 10) * mYPixelPerMetre, len, -(i - j * mViewSettings.mYInterval / 10) * mYPixelPerMetre);
        }
    };
    painter.setPen(QPen(Qt::darkRed, 1, Qt::DotLine));
    painter.drawLine(0, 0, width, 0);
    painter.restore();

    painter.end();
}

void TargetsView::resizeEvent(QResizeEvent *event)
{
    drawBackground();

    QWidget::resizeEvent(event);
}

void TargetsView::paintEvent(QPaintEvent *event)
{
    QPainter painter;
    painter.begin(this);
    {
        // 绘制背景
        QMutexLocker locker(&mMutex);
        painter.drawPixmap(0, 0, mPixmapBackground);
    }

    painter.save();

    painter.translate(mZeroPointF);
//    qDebug() << __FUNCTION__ << __LINE__ << mTargets.mEffectiveNumber;
    EndFrameData &endFramedata = mAnalysisData.mEndFrameData;
    double vehicleSpeed = mAnalysisData.mVehicleData.mVehicleSpeed / 3.6;
    for (int j = 0; j < 2; j++) {
        if (j == FrameRawTarget) {
            //continue;
            //显示俯视图点云
            for (int i = 0; i < mAnalysisData.mTargets[j].mTargetCount; ++i) {
                Target &target = mAnalysisData.mTargets[j].mTargets[i];
                if (target.mValid) {
                    TargetsView::ViewSettings::DisplayType dType = ViewSettings::DisplayTargetRaw;
                    double angle = target.mAngle + endFramedata.mEndOfLineEstablishedAngle;
                    double vehicleV = vehicleSpeed * qCos((angle) * M_PI / 180);
                    target.mVsog = (mAnalysisData.mRadarID == 6 || mAnalysisData.mRadarID == 7) ? (vehicleV + target.mV) : (vehicleV - target.mV);
                    if (qFabs(target.mVsog) < 1.6) {  // 前向加，后向减
                        target.mDynamicProperty = 0x0; // 静止
                        dType = ViewSettings::DisplayTargetRaw;
                    } else {
                        target.mDynamicProperty = 0x1; // 静止
                        dType = ViewSettings::DisplayTargetRawMoved;
                    }
                    uint32_t colorARGB = mViewSettings.mPointDisplay[dType].mColor;
                    if (mViewSettings.mPointDisplay[ViewSettings::DisplayHighlightedVelocityAmbiguity].mDisplay) {
                        if (target.mMatchFlag < 0x3) {
                            painter.setPen(QColor(255, 204, 153)); // 淡黄色
                        } else if (target.mStatus == 8 || target.mStatus == 24 || target.mStatus == 40 || target.mStatus == 56) {
                            painter.setPen(QColor(231, 139, 68)); // 橘色
                        } else if (((uint32_t)target.mStatus & 0x40) == 0x40) {
                            painter.setPen(QColor(255, 102, 102)); // 橘红色
                        } else if (((uint32_t)target.mStatus & 0x10) == 0x10) {
                            painter.setPen(QColor(255, 204, 153)); // 淡黄色
                        } else {
                            painter.setPen(QColor((colorARGB >> 24), (colorARGB >> 16) & 0xFF, (colorARGB >> 8) & 0xFF));
                        }
                    }
                    else
                    {
                        painter.setPen(QColor((colorARGB >> 24), (colorARGB >> 16) & 0xFF, (colorARGB >> 8) & 0xFF));
                    }
                    painter.setBrush(Qt::CrossPattern);

                    double x = 0.0;
                    double y = 0.0;
//                    qDebug() << __FUNCTION__ << __LINE__ << mAnalysisData.mRadarID;
                    switch (mAnalysisData.mRadarID) {
                    case 4:
                        x = -(mViewSettings.mLocalVehicleWidthHalf + target.mX) * mXPixelPerMetre;
                        y = (target.mY + mViewSettings.mLocalVehicleLengthHalf) * mYPixelPerMetre;
                        break;
                    case 5:
                        x = (mViewSettings.mLocalVehicleWidthHalf + target.mX) * mXPixelPerMetre;
                        y = (target.mY + mViewSettings.mLocalVehicleLengthHalf) * mYPixelPerMetre;
                        break;
                    case 6:
                        x = -(mViewSettings.mLocalVehicleWidthHalf + target.mX) * mXPixelPerMetre;
                        y = -(target.mY + mViewSettings.mLocalVehicleLengthHalf) * mYPixelPerMetre;
                        break;
                    case 7:
                        x =  (mViewSettings.mLocalVehicleWidthHalf + target.mX) * mXPixelPerMetre;
                        y = -(target.mY + mViewSettings.mLocalVehicleLengthHalf) * mYPixelPerMetre;
                        break;
                    }
                    QPointF point(x, y);
                    painter.drawEllipse(point.x() - mViewSettings.mPointSizeRawHalf, point.y() - mViewSettings.mPointSizeRawHalf, mViewSettings.mPointSizeRaw, mViewSettings.mPointSizeRaw);

                    if (mViewSettings.mDisplayIDLabelRaw) {
                        painter.setPen(QPen(Qt::white));
                        QPointF point((target.mX) * mXPixelPerMetre, -(target.mY + mViewSettings.mLocalVehicleLengthHalf) * mYPixelPerMetre);
                        painter.drawText(point.x(), point.y() - 3, QString::number(target.mID));
                    }
                }
            }

        }
        else 
        {
            for (int i = 0; i < mAnalysisData.mTargets[j].mTargetCount; ++i)
            {
                Target &target = mAnalysisData.mTargets[j].mTargets[i];
                if (target.mValid) 
                {
                    TargetsView::ViewSettings::DisplayType dType = ViewSettings::DisplayTargetTrack;
                    // 动静属性
                    switch ((int)target.mDynamicProperty)
                    {
                    case 0x1: // moving
                        dType = ViewSettings::DisplayTargetTrackMoved;
                        break;
                    default:
                        dType = ViewSettings::DisplayTargetTrack;
                        break;
                    }
                    uint32_t colorARGB = mViewSettings.mPointDisplay[dType].mColor;
                
                    painter.setBrush(QColor((colorARGB >> 24), (colorARGB >> 16) & 0xFF, (colorARGB >> 8) & 0xFF));
                    painter.setPen(QColor((colorARGB >> 24), (colorARGB >> 16) & 0xFF, (colorARGB >> 8) & 0xFF));
                    if (target.mTrackType < 0x2)
                    {
                        painter.drawEllipse((target.mX)* mXPixelPerMetre,
                            -(target.mY + mViewSettings.mLocalVehicleLengthHalf) * mYPixelPerMetre,
                            mViewSettings.mPointSizeTrackHalf,
                            mViewSettings.mPointSizeTrackHalf);
                    }
                    else
                    {
                        painter.drawRect((target.mX)* mXPixelPerMetre,
                            -(target.mY + mViewSettings.mLocalVehicleLengthHalf) * mYPixelPerMetre,
                            mViewSettings.mPointSizeTrackHalf,
                            mViewSettings.mPointSizeTrackHalf);
                    }

                    if (mViewSettings.mDisplayIDLabelTrack) {
                        painter.setPen(QPen(Qt::white));
                        QPointF point(-(target.mX) * mXPixelPerMetre, (target.mY + mViewSettings.mLocalVehicleLengthHalf) * mYPixelPerMetre);
                        painter.drawText(point.x(), point.y() - 3, QString::number(target.mID));
                    }
                }
            }
        }
    }

    painter.restore();
    painter.end();
    QWidget::paintEvent(event);
}
