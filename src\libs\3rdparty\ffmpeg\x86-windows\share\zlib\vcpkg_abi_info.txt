0001-Prevent-invalid-inclusions-when-HAVE_-is-set-to-0.patch 750b9542cb55e6328cca01d3ca997f1373b9530afa95e04213168676936e7bfa
0002-build-static-or-shared-not-both.patch dd4945e8af55d9feb65af706cb0254a6925dc4ac12789586c0d28acf323ad075
0003-android-and-mingw-fixes.patch 63e62c9bae3952b8cca832e01aa96ef3b2696b21976c5031d6d6d79937d8d54c
cmake 3.29.0
features core
portfile.cmake b14cee469c2da06e036e63293c0b6e6509924462e9b341b317986df46b281695
ports.cmake 0500e9e2422fe0084c99bdd0c9de4c7069b76da14c8b58228a7e95ebac43058a
post_build_checks 2
powershell 7.2.16
triplet x86-windows
triplet_abi 3e71dd1d4afa622894ae367adbbb1ecbd42c57c51428a86b675fa1c8cad3a581-e36df1c7f50ab25f9c182fa927d06c19ae082e0d599f132b3f655784b49e4b33-249e2da0fced5777e3f907c3dc10380ddd2c9c98
usage be22662327df993eebc437495add75acb365ab18d37c7e5de735d4ea4f5d3083
vcpkg-cmake a0a36e21d32b4206f4f67926d6d4f3a1085958580627e52ffa8bff1f0c62cae2
vcpkg-cmake-wrapper.cmake 5d49ef2ee6448479c2aad0e5f732e2676eaba0411860f9bebabe6002d66f57d1
vcpkg.json 8ad7072167e1bf56d2ed531e43ecc95026d5b305ed0393140d5ec78f5c6b6c00
vcpkg_copy_pdbs d57e4f196c82dc562a9968c6155073094513c31e2de475694143d3aa47954b1c
vcpkg_fixup_pkgconfig 904e67c46ecbb67379911bc1d7222855c0cbfcf1129bf47783858bcf0cc44970
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_replace_string d43c8699ce27e25d47367c970d1c546f6bc36b6df8fb0be0c3986eb5830bd4f1
