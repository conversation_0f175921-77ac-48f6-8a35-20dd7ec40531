#ifndef EOLTABLEMGR_H
#define EOLTABLEMGR_H

#include "eoldefine.h"

#include <QObject>

//表类别
typedef enum
{
    SV_AZIMUTH_TABLE = 0,                 /**< 方位导向矢量表@ELE+0deg */
    SV_ELEVATION_TABLE,                   /**< 俯仰导向矢量表@AZI+0deg */
    ANT_BOTH_TABLE,                       /**< 天线间距坐标与初相信息表，双表合并 */
    PATTERN_TABLE,                        /**< 方向图表 */
    SV_ELEVATION_AZI_N45_TABLE,           /**< 俯仰导向矢量表@AZI-45deg */
    SV_ELEVATION_AZI_P45_TABLE,           /**< 俯仰导向矢量表@AZI+45deg */
    BACKGROUND_NOISE_TABLE,               /**< 通道底噪表 */
    UNKNOW_CLASS_TABLE = 0xFF,            /**< 未知表类 */
} EOLTableClass;

/* 表类型 */
typedef enum
{
  PROFILE_0_SV_AZIMUTH_TABLE = 0,       /**< 方位导向矢量表@ELE+0deg */
  PROFILE_1_SV_AZIMUTH_TABLE,           /**< 方位导向矢量表@ELE+0deg */
  PROFILE_2_SV_AZIMUTH_TABLE,           /**< 方位导向矢量表@ELE+0deg */
  PROFILE_3_SV_AZIMUTH_TABLE,           /**< 方位导向矢量表@ELE+0deg */

  PROFILE_0_SV_ELEVATION_TABLE,         /**< 俯仰导向矢量表@AZI+0deg */
  PROFILE_1_SV_ELEVATION_TABLE,         /**< 俯仰导向矢量表@AZI+0deg */
  PROFILE_2_SV_ELEVATION_TABLE,         /**< 俯仰导向矢量表@AZI+0deg */
  PROFILE_3_SV_ELEVATION_TABLE,         /**< 俯仰导向矢量表@AZI+0deg */

  PROFILE_0_ANT_BOTH_TABLE,             /**< 天线间距坐标与初相信息表，双表合并 */
  PROFILE_1_ANT_BOTH_TABLE,             /**< 天线间距坐标与初相信息表，双表合并 */
  PROFILE_2_ANT_BOTH_TABLE,             /**< 天线间距坐标与初相信息表，双表合并 */
  PROFILE_3_ANT_BOTH_TABLE,             /**< 天线间距坐标与初相信息表，双表合并 */

  PROFILE_0_PATTERN_TABLE,              /**< 方向图表 */
  PROFILE_1_PATTERN_TABLE,              /**< 方向图表 */
  PROFILE_2_PATTERN_TABLE,              /**< 方向图表 */
  PROFILE_3_PATTERN_TABLE,              /**< 方向图表 */

  PROFILE_0_SV_ELEVATION_AZI_N45_TABLE, /**< 俯仰导向矢量表@AZI-45deg */
  PROFILE_1_SV_ELEVATION_AZI_N45_TABLE, /**< 俯仰导向矢量表@AZI-45deg */
  PROFILE_2_SV_ELEVATION_AZI_N45_TABLE, /**< 俯仰导向矢量表@AZI-45deg */
  PROFILE_3_SV_ELEVATION_AZI_N45_TABLE, /**< 俯仰导向矢量表@AZI-45deg */

  PROFILE_0_SV_ELEVATION_AZI_P45_TABLE, /**< 俯仰导向矢量表@AZI+45deg */
  PROFILE_1_SV_ELEVATION_AZI_P45_TABLE, /**< 俯仰导向矢量表@AZI+45deg */
  PROFILE_2_SV_ELEVATION_AZI_P45_TABLE, /**< 俯仰导向矢量表@AZI+45deg */
  PROFILE_3_SV_ELEVATION_AZI_P45_TABLE, /**< 俯仰导向矢量表@AZI+45deg */

  PROFILE_ALL_BACKGROUND_NOISE_TABLE,   /**< 所有配置下通道底噪表 */

  UNKNOW_TABLE = 0xFF,                  /**< 未知表类型 */
} EOLTableType;

/* 公共表头信息 */
typedef struct
{
  uint8_t Class_ID_Num;                 /**< 公共头部字段 */
  uint8_t Headr_Check_Sum;              /**< 私有头部数据校验和 */
  uint8_t Header_Size;                  /**< 私有头部数据大小长度 */
  uint8_t Version_MAJOR;                /**< 主版本号 v0 - 255 */
  uint8_t Version_MINOR;                /**< 副版本号 0 - 255 */
  uint8_t Version_REVISION;             /**< 修订版本号 0 - 255 */
  uint8_t Table_Type;                   /**< 表类型 @ref TABLE_Typedef_t */
  uint8_t Data_Type;                    /**< 存储在flash中的数据类型 @ref DATA_Typedef_t */
  uint32_t Data_Size;                   /**< 表数据字节数，也是校验CRC部分大小 */
  uint32_t Crc_Val;                     /**< 表数据CRC */
}TABLE_HEADER_Typedef_t;

/* DoA导向矢量表头信息 */
typedef struct
{
  TABLE_HEADER_Typedef_t Common_Info;   /**< 公共头部信息 */
  float Start_Angle;                    /**< 起始角度 */
  float End_Angle;                      /**< 结束角度 */
  uint16_t Points;                      /**< 点数 */
  uint8_t Channel_Num;                  /**< 通道数量 */
  float Azi_Ele_Angle;                  /**< 水平导向矢量@俯仰角，俯仰导向矢量@水平角 */
  uint8_t Clibration_Tx_Order[4];       /**< TX在校准时的发射配置 */
  uint8_t Profile_ID;                   /**< 当前配置属于哪个校准Profile */
}DOA_TABLE_HEADER_Typedef_t;

/* DoA天线补偿表头信息 */
typedef struct
{
  TABLE_HEADER_Typedef_t Common_Info;   /**< 公共头部信息 */
  uint16_t Points;                      /**< 点数 */
  uint8_t Channel_Num;                  /**< 通道数量 */
  uint8_t Clibration_Tx_Order[4];       /**< TX在校准时的发射配置 */
  uint8_t Profile_ID;                   /**< 当前配置属于哪个校准Profile */
}ANT_TABLE_HEADER_Typedef_t;

/* 通道底噪表头信息 */
typedef struct
{
  TABLE_HEADER_Typedef_t Common_Info;   /**< 公共头部信息 */
  uint8_t Channel_Num[4];               /**< Profile下通道数量 */
  uint8_t Unit;                         /**< 单位 @ref UNIT_Typedef_t */
  uint8_t Clibration_Tx_Order[4][4];    /**< Profile下TX在校准时的发射配置 */
}SYS_NOISE_TABLE_HEADER_Typedef_t;

/* 方向图即角度能量图表头信息 */
typedef struct
{
  TABLE_HEADER_Typedef_t Common_Info;   /**< 公共头部信息 */
  float Start_Angle;                    /**< 起始角度 */
  float End_Angle;                      /**< 结束角度 */
  uint16_t Points;                      /**< 点数 */
  uint8_t Channel_Num;                  /**< 通道数量 */
  uint8_t Unit;                         /**< 单位 @ref UNIT_Typedef_t */
  uint8_t Clibration_Tx_Order[4];       /**< TX在校准时的发射配置 */
  uint8_t Profile_ID;                   /**< 当前配置属于哪个校准Profile */
}SYS_PATTERN_TABLE_HEADER_Typedef_t;

///* 传输表头信息 */
//typedef struct
//{
//  TABLE_HEADER_Typedef_t Common_Info;   /**< 公共头部信息 */
//  quint8 private_header[64];            /**< 私有头部信息 */
//}COMMON_TABLE_HEADER_Typedef_t;

class EOLTableMgr : public QObject
{
    Q_OBJECT
public:
    explicit EOLTableMgr(QObject *parent = nullptr);
    //从csv中获取数据
    bool analysisCsvFile( const QByteArray& head, const QByteArray& value, const QByteArray& data );

    //从报文二进制中获取数据
    bool analysisHeadFromByte( const QByteArray& head );
    bool appendDataFromByte( const QByteArray& data );

    //导出为csv
    bool exportToCsv( const QString& path );

    //导出为二进制
    QByteArray getHead();
    const QByteArray& getData(){ return mData; };

    QString getMsg(){ return mMsg; };
    quint8 getProID(){ return mProID; };
    quint8 getTableType(){ return (quint8)mType; };
    QString tableName(){ return mTableName; };
public:
    static EOLTableClass getTableClass( EOLTableType type );
    static EOLTableType getTableType( EOLTableClass cls, quint8 pro_id );
    static QString getTableName( EOLTableType type );

private:
    bool analysisCommonHead( const QStringList& list, TABLE_HEADER_Typedef_t& commonHead );
    bool analysisDoaTableHead( const QStringList& list );
    bool analysisAntTableHead( const QStringList& list );
    bool analysisSysNoiseTableHead( const QStringList& list );
    bool analysisSysPatternTableHead( const QStringList& list );

private:
    quint8 getPrivateHeadSize();

private:
    void initTableParam();
signals:

private:
    DOA_TABLE_HEADER_Typedef_t mDoaTableHead;
    ANT_TABLE_HEADER_Typedef_t mAntTableHead;
    SYS_NOISE_TABLE_HEADER_Typedef_t mSysNoiseTableHead;
    SYS_PATTERN_TABLE_HEADER_Typedef_t mSysPatternHead;
    QByteArray mData;
    quint64 mCurFrameIndex{0};

    QString mMsg{""};
    QString mTableName{""};
    quint8 mProID{0};
    EOLTableType mType{UNKNOW_TABLE};
    EOLTableClass mClass{UNKNOW_CLASS_TABLE};
};

#endif // EOLTABLEMGR_H
