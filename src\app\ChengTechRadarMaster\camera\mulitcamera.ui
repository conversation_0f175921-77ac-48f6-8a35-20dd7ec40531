<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MulitCamera</class>
 <widget class="QMainWindow" name="MulitCamera">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>288</width>
    <height>207</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>3</number>
    </property>
    <property name="topMargin">
     <number>3</number>
    </property>
    <property name="rightMargin">
     <number>3</number>
    </property>
    <property name="bottomMargin">
     <number>3</number>
    </property>
    <item>
     <widget class="CameraImage" name="videoWidget" native="true">
      <property name="minimumSize">
       <size>
        <width>102</width>
        <height>70</height>
       </size>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>288</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuCamera">
    <property name="title">
     <string>Camera</string>
    </property>
    <addaction name="actionStart"/>
    <addaction name="actionStop"/>
    <addaction name="actionQuit"/>
    <addaction name="separator"/>
    <addaction name="actionAlias"/>
    <addaction name="actionSettings"/>
   </widget>
   <addaction name="menuCamera"/>
  </widget>
  <action name="actionStart">
   <property name="text">
    <string>Start</string>
   </property>
  </action>
  <action name="actionStop">
   <property name="text">
    <string>Stop</string>
   </property>
  </action>
  <action name="actionQuit">
   <property name="text">
    <string>Quit</string>
   </property>
  </action>
  <action name="actionSettings">
   <property name="text">
    <string>Settings</string>
   </property>
  </action>
  <action name="actionAlias">
   <property name="text">
    <string>Alias</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CameraImage</class>
   <extends>QWidget</extends>
   <header>camera/cameraimage.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
