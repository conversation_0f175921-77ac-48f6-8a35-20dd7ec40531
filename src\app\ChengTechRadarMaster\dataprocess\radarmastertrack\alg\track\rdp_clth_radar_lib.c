﻿/**
 * @file rdp_clth_radar_lib.c
 * <AUTHOR> (<PERSON><PERSON><PERSON>@chengtech.net)
 * @brief
 * @version 0.1
 * @date 2022-10-09
 *
 * @copyright Copyright (c) 2022
 *
 */

#include <float.h>
#ifndef PC_DBG_FW
#include "vehicle_cfg.h"
#ifdef  VEHICLE_TYPE_BYD_HAISHI_UKE_5R5V
#include "rdp_types.h"
#include "app/adas/customizedrequirements/adas.h"
#endif
#include "embARC_debug.h"
#include "rdp_kf_init.h"
#include "rdp_kf_config.h"
#include "rdp_kf_track.h"
#include "rdp_main_ekf.h"
#include "rdp_pre_ekf.h"
#include "rdp_clth_radar_lib.h"
#include "rdp_track_config.h"
#include "rdp_matrixMath.h"
#include "rdp_track_common.h"
#include "rdp_interface.h"
#include "sortlib.h"
#include "vdy.h"
#else
#include "alg/track/rdp_main_ekf.h"
#include "alg/track/rdp_pre_ekf.h"
#include "alg/track/rdp_matrixMath.h"
#include "alg/track/rdp_track_config.h"
#include "alg/track/rdp_track_common.h"
#include "alg/track/rdp_kf_config.h"
#include "alg/track/rdp_kf_init.h"
#include "alg/track/rdp_kf_track.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "alg/track/rdp_interface.h"
#include "app/system_mgr/typedefs.h"
#include "app/apar/apar_types.h"
#include "other/pc_vr_mcu.h"
#include "././hal/algo/sortlib.h"
#include "string.h"
#include "../vdy/vdy.h"
#endif

#ifndef M_PI
#define M_PI 3.1415926f
#endif

#define MAX_VEL_DIFF 3
#define MAX_ANGLE_DIFF 5

#define THR_FAR_NEG_FACTOR 0.6f
#define THR_CDI_ANG_FACTOR 1.5f

#define MIN_FENCE_DIS 2   //护栏线的最小距离
#define FENCE_THRD_SIDE 5 //单边有护栏的点数
#define FENCE_CNT_THRD 15 //5---@YOU
#define FCW_FENCE_CNT_THRD 5
#define NEG_CAR_VEL_THRD 7

//#define MIN_CAR_VELOCITY 1

#define HIST_SCALE 5
#define VEL_HIST_NUM 200
#define VEL_SCALE_NUM (VEL_HIST_NUM * HIST_SCALE)

#define FIT_POINT_NUM 8

#define CHECK_BARN(Cnt, Total) ((Cnt > Total / 3 - 2) && (Cnt < Total / 3 + 2))

#define UNVALID_VAL 10000

#define ANGLE_WIN_SIZE 16

#define STATIC_SPEED_THRESHOLD 0.8f

#define FILTER_INTERVAL_CNT 10

#define MAX_MOVING_CNT 8

//起始航迹关联门限，由实验数据设定
#define TRACK_ASSOC_CNT_THR 			15
#define TRACK_ASSOC_DELTAR_RADIAL_THR 	6.5f
#define TRACK_ASSOC_DELTAR_DIST_MIN 	1.5f
#define TRACK_ASSOC_DELTAR_DIST_EXPAND  3.0f
#define TRACK_ASSOC_VELOCITY_THR		6.5f
#define TRACK_ASSOC_ANGLE_THR			20.0f
#define TRACK_ASSOC_CLOSE_VELOCITY_THR  1.0f
#define TRACK_ASSOC_CLOSE_LAT_THR       0.7f
#define TRACK_ASSOC_CLOSE_LONG_THR      1.5f
//北汽运动假点判定门限，由实验数据设定
#define POINT_FAKE_RRATE_MINTHR 		1.5f
#define POINT_FAKE_RRATE_MAXTHR 		5.0f
#define POINT_FAKE_DELTAR_THR 			100.0f
#define POINT_FAKE_VRATE_MINTHR 		0.4f
#define POINT_FAKE_VRATE_MAXTHR 		1.5f
#define POINT_FAKE_DELTAV_THR 			1.0f
#define POINT_FAKE_DELTAA_THR			20.0f
//吉利RCTA多径运动假点判定门限，由实验数据设定
#define POINT_HORIZMULTI_RRATE_MINTHR 	0.95f
#define POINT_HORIZMULTI_RRATE_MAXTHR 	1.15f
#define POINT_HORIZMULTI_DELTAR_THR 	2.0f
#define POINT_HORIZMULTI_VRATE_MINTHR 	0.9f
#define POINT_HORIZMULTI_VRATE_MAXTHR 	1.0f
#define POINT_HORIZMULTI_DELTAV_THR 	2.0f
#define SCORE_DELTA_THR 				300
#define SCORE_RATE_THR 					3.0f
#define TRACK_LOCATION_CHANGED_THR_X    0.5f
#define TRACK_LOCATION_CHANGED_THR_Y    0.8f

extern float binParam[3];
extern float gVelocityRes[2];
extern float gRangeRes[2];
extern float gRDP_storedFrameTime[STORE_FRAME_TIME_NUM];

float RList[3][3];

float rightFenceRange = 0;
float rightFrenceBuf[5] = {0, 0, 0, 0, 0};

int gFirstId;

#ifdef SCENARIO_ESTIMATE
//自身车速
static float gCarVelocity = 0;
static float gAbsCarVel = 0;
static short gCacheCnt = 0;
static short gCacheCnt1 = 0;
static short gCacheCnt2 = 0;

static short gInRural = 0;     //村庄道路计数
static short gInRuralFlag = 0; //村庄道路标记

//地下车库
static short gInBarn = 0; //地下车库计数
static short YawRateCnt = 0;
static short gInBarnFlag = 0; //地下车库标识

//处理被超车时候段时间内有新点起来的现象，添加定时器(帧时间)
#define MAX_TRACKER_INTER_TIMECNT 30 // 1s时间

//场景标志
uint16_t gRadarFrenceFlag = 0x8000; //bit 0代表全部有效还是无效  bit1-地库flag   bit2-乡村道路 。。。

//护栏场景
float gAngleOffset = 0.0f; //护栏场景补偿角度值
static int gInFrenceSence = 0;
#endif

//雷达安装角度偏差
//static float gRadarAngleOffset = 0;       //角度偏差
//static float gAngleArray[ANGLE_WIN_SIZE]; //角度偏差滑窗
//static uint16_t gAngleCnt = 0;            //滑窗次数
//static uint16_t gAvgMissCnt = 0;          //计算出的角度偏差和当前偏差差异较大的次数

//安装角度标定
#define INSTALL_ANGLE_WIN_SIZE 10
#define INSTALL_ANGLE_WAIT_FRAME 120 //初始等待帧数
static float gInstallAngleArray[INSTALL_ANGLE_WIN_SIZE];
static float gInstallAngleArrayCnt[INSTALL_ANGLE_WIN_SIZE][2]; //角度平局和个数
uint16_t gInstallAngleCnt = 0;                                 //滑窗次数
//static uint16_t gInstallAngleCalcCnt = 0;           //校准帧数统计，用于超时统计
//static uint16_t gInstallInitWaitCnt = 0;  //校准帧数统计，用于超时统计
//static uint16_t gInstallCalcNoObjCnt = 0; //安装校准时，没有检测到目标的统计，用于超时统计
#define LAST_HIT_NUM_MAX 10
static float probOfExistHitCoeff[LAST_HIT_NUM_MAX];
#define INSTALL_CALC_USE_TRACK_DATA 1
#define INSTALL_CALC_INVALID_ANGLE -100
//static uint8_t TrueCnt = 0;


//#define INTERFERENCE_MAX_TIMEOUT 10     //判别为干扰的环境情况下，持续10帧认为是干扰状态
//volatile static uint8_t interferenceFlag = 0;       //当前是否为干扰状态
//volatile static uint8_t interferenceTimeout = 0;    //干扰次数超时，最大为  INTERFERENCE_MAX_TIMEOUT
//volatile static uint8_t interferenceAllPoint = 0;   //统计部分区域的点数
//volatile static float interferenceMaxSpeed;
//volatile static float interferenceMinSpeed;
//volatile static uint32_t histogramGoSpeed; //去向目标的直方图分布，每  2m/s 一个刻度
//volatile static uint32_t histogramComeSpeed; //来向目标的直方图分布，每  2m/s 一个刻度

typedef struct POINT
{
    float x;
    float y;
} POINTS;
uint8_t gCalcValueCnt = 0, gCalcCycleCnt = 0, gCalcErrorCnt = 0;
uint8_t gCalcSycleInterval = 0;
float calibrationDis = 0;
float Calcing_Dis = 0;
#define CALC_LAST_CALC_ANGLE_INVALUE 0xFFFF                 //上一帧角度无效值
//static float gLastCalcAngle = CALC_LAST_CALC_ANGLE_INVALUE; //初始未无效值
float gCalcAngle = 0;
#define CALC_CYCLE_BUF 15 //滑窗平均个数,最大10个
//static float gCalcCycle[CALC_CYCLE_BUF];
uint8_t gCalc_En = 0;

#define CALC_INTERVAL_FRAME 1200 //1200帧 / 20 = 60s = 1分钟
#define CALC_FENCE_FRAME 15      //有效护栏场景的门限值帧数
uint16_t gCalcInterval = CALC_INTERVAL_FRAME;

#define ALL_ANGLE_CNT 41 // 0.5 度一个步进，然后从当前设置的安装角度分开两边进行处理 ,z注意，只能是奇数
#if ((ALL_ANGLE_CNT % 2) == 0)
#error "CALC_START_RUN%2 must == 1"
#endif
#define ANGLE_CALC_STEP (0.5f) //处理步进
#define CALC_TIMEOUT_CNT 200
#define CALC_CNT 10 //进行校准的次数，最后进行平均或是其他计算

#define MAX_GROUP2_NUM 256

float stdValue[ALL_ANGLE_CNT] = {0};
float angleOffsetAry[CALC_CNT] = {45};
int selfCalcAllCnt = 0;
int selfCalcOkCnt = 0;

#define AUTO_SELCALC_TIMER_INTERVAL 600000//(10*60*1000)    //每10分钟进行一次触发自动校准的操作，触发后直到本次校准完成呢还是需要超时？ 目前是直到完成本轮
float autoStdValue[ALL_ANGLE_CNT] = {0};
float autoAngleOffsetAry[CALC_CNT] = {45};
int autoSelfCalcAllCnt = 0;
int autoSelfCalcOkCnt = 0;

//#ifndef PC_DBG_FW
//uint32_t g_autoSelfCalcOkTime = 0; //标定完成的时间标签
//#endif

float fastAngAutoCalBuf[3] = {0};//缓存快速标定的角度信息
uint8_t isFastAutoCal = 0;       //是否快速标定的标志，本次标定角度大于2°，进入快速标定，2min标定一次
uint8_t fastAngCalCnt = 0;       //快速标定的次数

static uint8_t gVelHist[VEL_HIST_NUM];
uint8_t gMemberNumPerGroup[MAX_GROUP2_NUM];
static uint16_t gGroupStatus[MAX_GROUP2_NUM];
static uint8_t gResolutionObjCnt = 0;

float skewCrossAngle[4] = { 25.f, 35.f, 0.f, 0.f };	// 斜穿角度
float skewStopAngle[4] = { 8.f, 0.f, 0.f, 0.f };	// 斜停角度
uint8_t skewCrossTargetsCnt = 0;					// 统计斜穿目标，用于判断斜穿场景

float gLastFrameVehicleAbsSpeed = 0.f;
uint8_t gGridMap[4][3];
uint8_t straightFence;          // 直护栏标记
uint8_t curFrameMovedTargetCnt; // 当前帧moved目标计数
uint8_t forwardMovingTarget;    // 正前方是否存在运动目标

//边线直方图
#define FENCE_HIST_NUM 25
#define FENCE_SCALE 3
#define FENCE_SCALE_NUM (FENCE_HIST_NUM * FENCE_SCALE)
#define FENCE_THRESHOLD(DIS, X) (((-fabsf(DIS) - 0.5f) < X) && (X < (fabsf(DIS) + 0.5f)))

float calcMaxXYRang(int idx, cdi_pkg_t *pCdiPkg, int IsXMax);
void pkg_RuralSceneEstimate(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg);
void pkg_BarnSceneEstimate(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg);
void pkg_FenceSceneEstimate(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg);
void RDP_Track_initPointsGroup(uint32_t detNum, cdi_t *pdetObj);
uint16_t RDP_Track_clusterOnCdis(cdi_t *pdetObj, VDY_DynamicEstimate_t *pRDP_inVehicleData, uint32_t detNum, trk_pkg_t* pTrkPkg);
void RDP_TRACK_judgeBasementScene(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg);
void RDP_TRACK_judgeSideCarScene(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg,  VDY_DynamicEstimate_t* pRDP_inVehicleData);
bool RDP_TRACK_inCoverAera(float x, float y);
void RDP_Track_markShelterPoint(cdi_pkg_t *pCdiPkg, trk_pkg_t* pTrkPkg);
bool RDP_TRACK_inShelterArea(cdi_t *assoCdi, cdi_pkg_t *pCdiPkg);
void RDP_TRACK_updateTrkSlide(cdi_t *cdi, trk_t *trk);
void updateStaticObj(cdi_pkg_t* pCdiPkg, trk_t* trk, float time, VDY_DynamicEstimate_t* pRDP_inVehicleData);
void updateObjInDeadZone(cdi_pkg_t* pCdiPkg, trk_t* trk, float time, VDY_DynamicEstimate_t* pRDP_inVehicleData);
//static float calcRelativePositonDiff(trk_t* trk);
uint8_t existNoumenonTarget(trk_pkg_t* pTrkPkg, cdi_t *pdetObj, int curIdx);
uint8_t existPoleTarget(cdi_pkg_t* pCdiPkg, trk_pkg_t* pTrkPkg, int curIdx);
float calcRawLngVelocity(cdi_pkg_t* pCdiPkg, trk_t* trk);
bool isTruckMultipath(trk_pkg_t* pTrkPkg, int curIdx);
bool isMirrorTarget(trk_pkg_t* pTrkPkg, int curIdx);
bool trackMerge(trk_pkg_t *pTrkPkg, int curIdx);
bool isLngOnComingTarget(trk_pkg_t *pTrkPkg, int curIdx);
void isBumperTarget(cdi_pkg_t* pCdiPkg, trk_t* trk, VDY_DynamicEstimate_t* pRDP_inVehicleData);
bool clusterInSildLineSide(trk_pkg_t* pTrkPkg, cdi_t *pdetObj, int cdiIdx1, int cdiIdx2);
bool clusterDynamicOrNot(cdi_t *pdetObj, int cdiIdx1, int cdiIdx2);
bool sideCarclusterDynamicOrNot(cdi_t *pdetObj, int cdiIdx1, int cdiIdx2);
bool clusterDynamicOrNot2(cdi_t* pdetObj, int cdiIdx1, int cdiIdx2);
float RDP_calcVehicleMoveRange(uint8_t validCnt, trk_pkg_t *pTrkPkg);
bool RDP_multipathScene(trk_pkg_t* pTrkPkg, cdi_t *pdetObj, int curIdx);
bool judgeFenceHarmonic(cdi_t* pCdi, trk_t* pTrk);
uint8_t isDowShelterScene(trk_pkg_t* pTrkPkg, uint32_t curIdx);
uint8_t staticCdiCntAround(cdi_pkg_t* pCdiPkg, short curIdx);
uint8_t isSuspectedPedestrianInCPTALF(cdi_t* cdi, cdi_pkg_t* pCdiPkg, trk_t *pTrk);
void updateGridMap(trk_t* trk);
void updateLngMoveStatus(trk_t* trk);
void updateMovingStatusCnt(trk_t* trk);
void updateStaticStatusCnt(trk_t* trk, cdi_t* cdi);

static uint32_t frameCnt = 0;
TrackGroupInfo_t gGroupInfo[ALL_GROUP_NUM];
GTrack_ListElem gPointListElem[MAX_NUM_OF_POINTS];
static float ghostCarFrameMoveRange[STORE_RANGE_FRAME_NUM];       //存储过去若干帧本车车速

static uint8_t isBasementWallScene = 0;
static uint8_t basementSceneCnt = 0;
BasementWallPoint gBWPoint;

static uint8_t isSideCarScene = 0;
static uint8_t sideCarSceneCnt = 0;
SideCarPoint gSCPoint;

void poitnListElemInit()
{
    uint32_t i;
    for (i = 0; i < MAX_NUM_OF_POINTS; i++)
        gPointListElem[i].data = i;
}

void initGlobalVars(void)
{
#define ZERO_ARRAY(array)                                      \
    for (int i = 0; i < sizeof(array) / sizeof(array[0]); i++) \
    {                                                          \
        array[i] = 0;                                          \
    }

    rightFenceRange = 0;

    ZERO_ARRAY(rightFrenceBuf)

//    gCarVelocity = 0;
//    gAbsCarVel = 0;
//    gCacheCnt = 0;
//    gCacheCnt1 = 0;
//    gCacheCnt2 = 0;
//
//    gInRural = 0;
//    gInRuralFlag = 0;
//
//    gInBarn = 0;
//    YawRateCnt = 0;
//    gInBarnFlag = 0;
//
//    gRadarFrenceFlag = 0x8000; //bit 0代表全部有效还是无效  bit1-地库flag   bit2-乡村道路 。。。
//
//    gAngleOffset = 0.0f; //护栏场景补偿角度值
//    gInFrenceSence = 0;

    //雷达安装角度偏差
//    gRadarAngleOffset = 0; //角度偏差
//    memset(gAngleArray, 0, sizeof(gAngleArray));
//    gAngleCnt = 0;     //滑窗次数
//    gAvgMissCnt = 0;   //计算出的角度偏差和当前偏差差异较大的次数
//    gDelayFrmCnt = 0;  //补偿角度后允许的帧数
//    gStableFrmCnt = 0; //角度无偏差允许帧数
    memset(gVelHist, 0, sizeof(gVelHist));

    memset(gInstallAngleArray, 0, sizeof(gInstallAngleArray));
    memset(gInstallAngleArrayCnt, 0, sizeof(gInstallAngleArrayCnt));
    gInstallAngleCnt = 0;

//    TrueCnt = 0;
	skewCrossTargetsCnt = 0;
    gCalcValueCnt = 0, gCalcCycleCnt = 0, gCalcErrorCnt = 0;
    gCalcSycleInterval = 0;
    calibrationDis = 0;
    Calcing_Dis = 0;
//    gLastCalcAngle = CALC_LAST_CALC_ANGLE_INVALUE; //初始未无效值
    gCalcAngle = 0;
//    ZERO_ARRAY(gCalcCycle)
    gCalc_En = 0;
    gCalcInterval = CALC_INTERVAL_FRAME;
    poitnListElemInit();
    float sum = 0, sumInv;
    uint32_t i;
    for(i = 0; i < LAST_HIT_NUM_MAX; i++)
    {
        probOfExistHitCoeff[i] = expf((-1.f/LAST_HIT_NUM_MAX)*i);
        sum += probOfExistHitCoeff[i];
    }
    sumInv = 1.f/sum;
    for(i = 0; i < LAST_HIT_NUM_MAX; i++)
    {
        probOfExistHitCoeff[i] *= sumInv;
    }

    gFirstId = MAX_NUM_OF_TRACKS;
    gLastFrameVehicleAbsSpeed = 0.f;
	memset(&gGridMap[0][0], 0, sizeof(uint8_t) * 12);
    straightFence = 0;
    curFrameMovedTargetCnt = 0;
    forwardMovingTarget = 0;
}

extern volatile unsigned char pTestCanCli;
float getTrackHeading(float vx, float vy, float* cosAngle, float* sinAngle)
{
    float v;
    v = sqrtf(vx*vx + vy*vy);
    if(v != 0.f)
    {
        *cosAngle = vx/v;
        *sinAngle = vy/v;
    }else
    {
        *cosAngle = 1.f;
        *sinAngle = 0.f;
    }
    return v;
}
void getTrackBoxCenter(float* X, const VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_t* trk)
{
    float v;
    float Xoffset[2], Xtemp[2];
    float rotMatrix[4];
    v = getTrackHeading(trk->x[2], trk->x[3] - pRDP_inVehicleData->vdySpeedInmps, &rotMatrix[0], &rotMatrix[2]);
    rotMatrix[3] = rotMatrix[0];
    rotMatrix[1] = -rotMatrix[2];
    Xtemp[0] = 0.5f*(trk->front - trk->back);
    Xtemp[1] = 0.5f*(trk->left - trk->right);
    matrixMultiply(2, 2, 1, rotMatrix, Xtemp, Xoffset);
    matrixAdd(2, 1, trk->x, Xoffset, X);
}

void getTrackBoxConner(float* X, const VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_t* trk, float x, float y)
{
	float v;
	float Xoffset[2], Xtemp[2];
	float rotMatrix[4];
	v = getTrackHeading(trk->x[2], trk->x[3] - pRDP_inVehicleData->vdySpeedInmps, &rotMatrix[0], &rotMatrix[2]);
	rotMatrix[3] = rotMatrix[0];
	rotMatrix[1] = -rotMatrix[2];
	Xtemp[0] = y;//0.5f*(trk->front - trk->back);
	Xtemp[1] = x; // 0.5f*(trk->left - trk->right);
	matrixMultiply(2, 2, 1, rotMatrix, Xtemp, Xoffset);
	matrixAdd(2, 1, trk->x, Xoffset, X);
}

/*
int32_t pointInTrackBox(cdi_t * point, trk_t *trk)
{
    float* Xt;      // track point position
    float X0[2], Xtmp[2], Xp[2];    // center of the box, tmp, point position
    float X0based[2]; // position base on X0
    float headingVector[2];
    float cosHeadingAngle, sinHeadingAngle;
    float dRangeRate1, dRangeRate2, dRangeRate;
    float Xoffset[2] = {0.5f*(trk->front-trk->back), 0.5f*(trk->left-trk->right)};
    Xt = &trk->p_x[0];
    getTrackHeading(vx, vy, &cosHeadingAngle, &sinHeadingAngle);
    float rotMatrix[4] = {
        cosHeadingAngle, -sinHeadingAngle,
        sinHeadingAngle, cosHeadingAngle
        };
    float rotMatrixInv[4] = {
        cosHeadingAngle, sinHeadingAngle,
        -sinHeadingAngle, cosHeadingAngle
        };
    matrixMultiply(2, 2, 1, rotMatrix, Xoffset, Xtmp);
    matrixAdd(2, 1, Xt, Xtmp, X0);
    Xp[0] = point->x;
    Xp[1] = point->y;
    matrixSub(2, 1, Xp, X0, Xtmp);
    matrixMultiply(2, 2, 1, rotMatrixInv, Xtmp, X0based); 
    dRangeRate1 = fabsf((vx*point->x + vy*point->y)/point->mea_z[1] - point->mea_z[2]);
    dRangeRate2 = fabsf(trk->stored_last_z[0][2] - point->mea_z[2]);
    dRangeRate = dRangeRate1 < dRangeRate2 ? dRangeRate1 : dRangeRate2;
    
}
*/
void calcBoxCenterPoint(trk_t* trk, float* Xt, float* rotMatrix, float* X0)
{
    float Xtmp[2];
    float Xoffset[2] = {0.5f*(trk->front-trk->back), 0.5f*(trk->left-trk->right)};
    matrixMultiply(2, 2, 1, rotMatrix, Xoffset, Xtmp);
    matrixAdd(2, 1, Xt, Xtmp, X0);
}
uint32_t pointInUprightBoxRectRegion(float* X, trk_t* trk, float frontBackExtension, float leftRightExtension)
{
    if(trk)
    {
        if(( X[0] > -trk->back - frontBackExtension && \
            X[0] < trk->front + frontBackExtension && \
            X[1] > -trk->left - leftRightExtension && \
            X[1] < trk->right + leftRightExtension))
            return TRUE;
    }else
    {
        if( X[0] > - frontBackExtension && \
            X[0] < frontBackExtension && \
            X[1] >  - leftRightExtension && \
            X[1] < leftRightExtension)
            return TRUE;
    }
    return FALSE;
}
uint32_t pointInUprightBoxCrossRegion(float* X, trk_t* trk, float frontBackExtension, float leftRightExtension)
{
    if(trk)
    {
        if(( X[0] > -trk->back - frontBackExtension && \
            X[0] < trk->front + frontBackExtension && \
            X[1] > -trk->left && \
            X[1] < trk->right \
			&& !isCPTALFScene) ||
            ( X[0] > -trk->back && \
            X[0] < trk->front && \
            X[1] > -trk->left - leftRightExtension && \
            X[1] < trk->right + leftRightExtension))
            return TRUE;
    }
    return FALSE;
}
/*
uint32_t rangeRateOK()
{
    return 1;
}
*/
void boxBuffAdd(Buffer_ST* sideBuff, int8_t offset, uint32_t n)
{
    int8_t*pBuff = sideBuff->buffer;
    if(sideBuff->len < BUFFER_LEN_MAX)
    {
        pBuff[sideBuff->len++] = offset;
        return;
    }
    switch(n)
    {
        case 0: pBuff[BUFFER_LEN_MAX/2 -1] = offset; break;
        case 1: pBuff[BUFFER_LEN_MAX/2] = offset; break;
        case 2: pBuff[BUFFER_LEN_MAX/2 - 2] = offset; break;
        case 3: pBuff[BUFFER_LEN_MAX/2 + 1] = offset; break;
        default:
//            EMBARC_PRINTF(" Error(exit): file = %s, line = %d\r\n", __FILE__, __LINE__);
            exit(0);
    }
    return;
}
void boxPointBuffCollection(trk_t* trk, int8_t* lengthArray, int8_t* widthArray, uint8_t pointNum)
{
    uint8_t k;
    if(pointNum <= 4)
    {
        for(k = 0; k < pointNum; k++)
        {
            boxBuffAdd(&trk->lengthBuff, lengthArray[k], k);
            boxBuffAdd(&trk->widthBuff, widthArray[k], k);
        }
    }else
    {
        do_BubbleSort_INT8(lengthArray, pointNum);
        do_BubbleSort_INT8(widthArray, pointNum);
        for(k = 0; k < 2; k++)
        {
            boxBuffAdd(&trk->lengthBuff, lengthArray[k], (k << 1));
            boxBuffAdd(&trk->widthBuff, widthArray[k], (k << 1));
            boxBuffAdd(&trk->lengthBuff, lengthArray[pointNum-1-k], (k << 1)+1);
            boxBuffAdd(&trk->widthBuff, widthArray[pointNum-1-k], (k << 1)+1);
        }
    }
}
#define ALL_CLASS_NUM (TARGET_CLASS_NUM+2)
static const float rectTarget[ALL_CLASS_NUM][2] = {
{0, 0},
{PEDESTRIAN_LENGTH, PEDESTRIAN_WIDTH},
{BICYCLE_LENGTH, BICYCLE_WIDTH},
{CAR_LENGTH, CAR_WIDTH},
{LARGE_CAR_LENGTH, LARGE_CAR_WIDTH},
{30, LARGE_CAR_WIDTH}
};
#define BOX_FACTOR_ALPHA 0.8f
#define BOX_FACTOR_BETA  (1-BOX_FACTOR_ALPHA)
void trackBoxUpdate(trk_t* trk)
{
    float newFront, newBack, newLeft, newRight, newLength, newWidth;
    float oldLength;//, oldWidth;
    float delta;
    float weightedLength;
    if(trk->lengthBuff.len < BUFFER_LEN_MAX)
        return;
    do_BubbleSort_INT8(trk->lengthBuff.buffer, BUFFER_LEN_MAX);
    do_BubbleSort_INT8(trk->widthBuff.buffer, BUFFER_LEN_MAX);
    newFront = (float)trk->lengthBuff.buffer[BUFFER_LEN_MAX-2]*DISTANCE_ACCURACY;
    newBack = (float)(-trk->lengthBuff.buffer[1])*DISTANCE_ACCURACY;
    newLeft = (float)trk->widthBuff.buffer[BUFFER_LEN_MAX-4]*DISTANCE_ACCURACY;
    newRight = (float)(-trk->widthBuff.buffer[3])*DISTANCE_ACCURACY;
    if(newFront < -0.2f || newBack < -0.2f || newLeft < -0.2f || newRight < -0.2f)
        return;
    newLength = newFront + newBack;
    newWidth = newLeft + newRight;
    oldLength = trk->front + trk->back;
    // oldWidth = trk->left + trk->right;
    if(newLength > oldLength)
    {
        if(newLength < newWidth)
        {
            if(newWidth <= CAR_WIDTH)
            {
                delta = 0.5f*(newWidth - newLength);
            }else
            {
                delta = 0.5f*(CAR_WIDTH - newLength);
            }
            trk->front = newFront + delta;
            trk->back = newBack + delta;
        }else
        {
            trk->front = newFront;
            trk->back = newBack;
        }
    }else if(oldLength > CAR_LENGTH)
    {
        if(newLength > CAR_LENGTH)
        {
            weightedLength = oldLength*BOX_FACTOR_ALPHA + newLength*BOX_FACTOR_BETA;
        }else if(oldLength > CAR_LENGTH + CLASS_CAR_DISTANCE_TO_BOX_FRONT_INIT)
        {
            weightedLength = oldLength*BOX_FACTOR_ALPHA + CAR_LENGTH*BOX_FACTOR_BETA;
        }else
        {
            weightedLength = CAR_LENGTH;
        }
        delta = oldLength - weightedLength;
        if(trk->sim_z[2] < 0)
        {
            trk->front = trk->front*BOX_FACTOR_ALPHA + newFront*BOX_FACTOR_BETA;
            trk->back = weightedLength - trk->front;
        }else
        {
            trk->back = trk->back*BOX_FACTOR_ALPHA + newBack*BOX_FACTOR_BETA;
            trk->front = weightedLength - trk->back;
        }
        trk->front = trk->front*0.8f + newFront*0.2f;
        trk->back = trk->back*0.8f + newBack*0.2f;
    }

    for(uint32_t i = 1, j; i < ALL_CLASS_NUM; i++)
    {
        j = i - 1;
        if(oldLength >= rectTarget[j][0] && oldLength < rectTarget[i][0])
        {
            if(oldLength < rectTarget[j][1])
            {
                if(newWidth < oldLength)
                {
                    trk->left = newLeft;
                    trk->right = newRight;
                    
                }else
                {
                    delta = (oldLength - newWidth)*0.5f;
                    trk->left = newLeft + delta;
                    trk->right = newRight + delta;
                }
            }else
            {
                float halfWidth = rectTarget[j][1]*0.5f;
                if(newWidth < rectTarget[j][1])
                {
                    delta = (rectTarget[j][1] - newWidth)*0.5f;
                    trk->left = newLeft + delta;
                    trk->right = newRight + delta;
                }else if(newLeft < newRight)
                {
                    if(newLeft < halfWidth)
                    {
                        trk->left = newLeft;
                        trk->right = rectTarget[j][1] - newLeft;
                    }else
                    {
                        trk->left = halfWidth;
                        trk->right = halfWidth;
                    }
                }else
                {
                    if(newRight < halfWidth)
                    {
                        trk->left = rectTarget[j][1] - newRight;
                        trk->right = newRight;
                    }else
                    {
                        trk->left = halfWidth;
                        trk->right = halfWidth;
                    }
                }
            }
            break;
        }
    }
}

#define TARGET_CLASS_CAR_VELOCITY_MIN		10.f			// m/s
#define TARGET_CLASS_BICYCLE_VELOCITY_MIN	10 / 3.6f	// m/s
void trackBoxBasingOnVelocity(trk_t* trk, float vehicleSpeed)
{
    float vgx = trk->x[2];
    float vgy = trk->x[3] - vehicleSpeed;
    float vg = sqrtf(vgx*vgx + vgy*vgy);
    float length = trk->front + trk->back;
    float width = trk->left + trk->right;
    float delta;
    if(vg > TARGET_CLASS_CAR_VELOCITY_MIN)
    {
        if(trk->sim_z[1] > 10 && trk->sim_z[2] < 0)
        {
            if(length < CAR_LENGTH)
            {
                if(trk->front < CLASS_CAR_DISTANCE_TO_BOX_FRONT_INIT)
                    trk->front = CLASS_CAR_DISTANCE_TO_BOX_FRONT_INIT;
                if(trk->back < CLASS_CAR_DISTANCE_TO_BOX_BACK_INIT)
                    trk->back = CLASS_CAR_DISTANCE_TO_BOX_BACK_INIT;
                if(width < CAR_WIDTH)
                {
                    delta = (CAR_WIDTH - width)*0.5f;
                    trk->left += delta;
                    trk->right += delta;
                }
            }
        }else if(length < CAR_BOX_SIDE_LENGTH)
        {
            if(trk->front < CLASS_CAR_DISTANCE_TO_BOX_FRONT_INIT)
                trk->front = CLASS_CAR_DISTANCE_TO_BOX_FRONT_INIT;
            if(trk->back < CAR_BOX_SIDE_LENGTH/2)
                trk->back = CAR_BOX_SIDE_LENGTH/2;
            if(width < CAR_BOX_SIDE_LENGTH)
            {
                delta = (CAR_BOX_SIDE_LENGTH - width)*0.5f;
                trk->left += delta;
                trk->right += delta;
            }
        }
    }else if(vg > TARGET_CLASS_BICYCLE_VELOCITY_MIN)
    {
        if(trk->sim_z[1] > 10 && trk->sim_z[2] < 0)
        {
            if(length < BICYCLE_LENGTH)
            {
                if(trk->front < CLASS_BICYCLE_DISTANCE_TO_BOX_FRONT_INIT)
                    trk->front = CLASS_BICYCLE_DISTANCE_TO_BOX_FRONT_INIT;
                if(trk->back < CLASS_BICYCLE_DISTANCE_TO_BOX_BACK_INIT)
                    trk->back = CLASS_BICYCLE_DISTANCE_TO_BOX_BACK_INIT;
                if(width < BICYCLE_WIDTH)
                {
                    delta = (BICYCLE_WIDTH - width)*0.5f;
                    trk->left += delta;
                    trk->right += delta;
                }
            }
        }else if(length < BICYCLE_BOX_SIDE_LENGTH)
        {
            if(trk->front < CLASS_BICYCLE_DISTANCE_TO_BOX_FRONT_INIT)
                trk->front = CLASS_BICYCLE_DISTANCE_TO_BOX_FRONT_INIT;
            if(trk->back < BICYCLE_BOX_SIDE_LENGTH/2)
                trk->back = BICYCLE_BOX_SIDE_LENGTH/2;
            if(width < BICYCLE_BOX_SIDE_LENGTH)
            {
                delta = (BICYCLE_BOX_SIDE_LENGTH - width)*0.5f;
                trk->left += delta;
                trk->right += delta;
            }
        }
    }
	//else
	//{
	//	// 相对靠近低速行人
	//	if (trk->sim_z[1] > 10 && trk->sim_z[2] < 0 && length > PEDESTRIAN_LENGTH)
	//	{
	//		if (trk->front > CLASS_PEDESTRIAN_DISTANCE_TO_BOX_FRONT_INIT)
	//			trk->front = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_FRONT_INIT / 2;
	//		if (trk->back > CLASS_PEDESTRIAN_DISTANCE_TO_BOX_BACK_INIT)
	//			trk->back = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_BACK_INIT;

	//		if (width > PEDESTRIAN_WIDTH)
	//		{
	//			trk->left = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_LEFT_INIT;
	//			trk->right = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_RIGHT_INIT;
	//		}
	//	}
	//}
}
/*
 *  function: the first and the last elements are aged
 */
void trackBoxBufferAge(Buffer_ST* sideBuff)
{
    int8_t* pBuff = sideBuff->buffer;
    if(sideBuff->len < BUFFER_LEN_MAX)
        return;
    for(uint32_t i = 0; i < BUFFER_LEN_MAX-2; i++)
    {
        pBuff[i] = pBuff[i+1];
    }
    sideBuff->len -= 2;
}

#define TRACK_NEAR_AREA_DISTANCE       7.0f
#define DRADIALVEL_THRESHOLD_MIN (2.5f/3.6f)    // m/s
#define DRADIALVEL_THRESHOLD_MAX (5.0f/3.6f)    // m/s
#define FRONT_BACK_EXTENSION_MIN 2.f    //m
#define FRONT_BACK_EXTENSION_MAX 4.f    //m
#define SIDE_EXTENSION_MIN_NORMAL       0.5f    // m
#define SIDE_EXTENSION_MIN_RESOLUTION   0.3f    // m
#define SIDE_EXTENSION_MAX  1.5f    // m
#define RANGE_DELTA_THR 0.85f    // m
#define ANGLE_DELTA_THR (2*DEG2RAD) //rad
#define MAX_POINT_NUM_PER_TRACK 32
/**
 * @brief 
 * @param pdetObj RDP内部用于跟踪的原始点全局变量指针 
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param pGroupInfo 组指针
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 * @param trkType 航迹类型
 * @param groupIdStart 组开始的ID号
 * @return uint8_t 返回聚类的组数
 */
static uint8_t RDP_Track_clusterOnTypeTracks(cdi_t *pdetObj, VDY_DynamicEstimate_t *pRDP_inVehicleData, TrackGroupInfo_t* pGroupInfo, trk_pkg_t *pTrkPkg, trk_type_t trkType, uint8_t groupIdStart)
{
    u32 i, j;
    float v;   // v relative to the sensor
    float vg;   // v relative to the ground
    float vx, vy;   // x&y component of v
    float vgx,vgy; // x&y component of vg
    float vref; // reference velocity to calculate the dRadical threshold
    float sinHeadingAngle, cosHeadingAngle;
    float dRange, dRadialVel, dRadialVel2, dAngle;
    float dRadialVelThre;
//    float xThre, yThre;
//    float score;
    trk_t* trk;
    u16 groupId;
    short pointId = 0, pointId2 = 0;
    int32_t retVal;
    GTrack_ListObj* groupList;
    GTrack_ListObj* groupNoneList = &pGroupInfo[GROUP_ID_NONE].pointList;
    GTrack_ListElem *pointElemNoneGroup = NULL, *pointElemNoneGroupNext, *pointElemGrouped = NULL;
    float rotMatrixInv[4];    // rotation matrix
    float* Xt;      // track point position
//    float X0[2]; 
    float Xtmp[2];    // center of the box, tmp
    float XtBased[2]; // position base on Xt
//    float headingVector[2];
//    float dRangeRate1, dRangeRate2, dRangeRate;
    float frontBackExtension, leftRightExtension;
    float projectedRadialVel;
    float nearestRange, maxVal;
    short nearestDetIdx, maxValDetIdx;
    float SIDE_EXTENSION = SIDE_EXTENSION_MIN_NORMAL;
    //距离分辨率模式下需要调整门限值，否则0.5m会刚好卡在规格附近
    if(RDP_getTrackConfigPointer()->radarResolutionTestMode == TEST_MODE_RESOLUTION)
    {
        SIDE_EXTENSION = SIDE_EXTENSION_MIN_RESOLUTION;
    }
	sideLine_pkr_t* pSideLinePkg = &pTrkPkg->sideLines;
    groupId = groupIdStart;
    for (i = 0; i < MAX_NUM_OF_TRACKS && groupId <= GROUP_ID_END; i++)
    {
        trk = &pTrkPkg->trk[i];
    
        if (trk->type != trkType)
            continue;

        Xt = &trk->p_x[0];
        vgx = vx = trk->p_x[2];
        vy = trk->p_x[3];
        vgy = vy - pRDP_inVehicleData->vdySpeedInmps;
        v  = sqrtf(vx*vx + vy*vy);
        vg = getTrackHeading(vgx, vgy, &cosHeadingAngle, &sinHeadingAngle);
        vref = v > vg ? v : vg;
        dRadialVelThre = vref * 0.1f;
        if (dRadialVelThre > DRADIALVEL_THRESHOLD_MAX)
            dRadialVelThre = DRADIALVEL_THRESHOLD_MAX;
        if (dRadialVelThre < DRADIALVEL_THRESHOLD_MIN)
            dRadialVelThre = DRADIALVEL_THRESHOLD_MIN;
        /* construct the rotation matrix */
        rotMatrixInv[0] = rotMatrixInv[3] = cosHeadingAngle;
        rotMatrixInv[2] = -sinHeadingAngle;
        rotMatrixInv[1] = sinHeadingAngle;
//        calcBoxCenterPoint(trk, Xt, rotMatrix, X0);
		trk->nearestPosition[0] = trk->nearestPosition[1] = 10e3;
		trk->groupNonzeroVelCnt = 0;
        trk->groupStaticPointCnt = 0;

        groupList = &pGroupInfo[groupId].pointList;

        /* 1. the associated point is added to the group */
        if(trk->status&TRACK_STATUS_ASSOCIATED_BMP)
        {
            pointId = trk->idx_1;
            if(pdetObj[pointId].groupId == GROUP_ID_NONE)
            {
                pdetObj[pointId].groupId = groupId;
                pGroupInfo[groupId].status |= GROUP_STATUS_ASSOCIATED_BMP;
                if(trk->type == TRACK)
                    pGroupInfo[groupId].status |= GROUP_STATUS_ACTIVE;
                retVal = gtrack_listRemoveElement(groupNoneList, &gPointListElem[pointId]);
                if(retVal != 0)
                {
                    EMBARC_PRINTF(" Error(poin not in the list): file = %s, line = %d, trackId = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, i, pointId, groupId);
                }
                gtrack_listEnqueue(groupList, &gPointListElem[pointId]);
                pGroupInfo[groupId].trackId[0] = i;		//关联点的对应跟踪点作为第一个索引
                pGroupInfo[groupId].trackNum = 1;		//跟踪点数量加1
                Xtmp[0] = pdetObj[pointId].x - Xt[0];	//关联点减去跟踪点的预测值
                Xtmp[1] = pdetObj[pointId].y - Xt[1];
                matrixMultiply(2, 2, 1, rotMatrixInv, Xtmp, XtBased); //旋转矩阵
				//SEL_MIN(trk->nearestPosition[0], XtBased[1]);
				//SEL_MIN(trk->nearestPosition[1], XtBased[0]);
				SEL_MIN(trk->nearestPosition[0], pdetObj[pointId].x);
				SEL_MIN(trk->nearestPosition[1], pdetObj[pointId].y);
				if (fabsf(pdetObj[pointId].mea_z[2]) > 0.1f)
				{
					trk->groupNonzeroVelCnt++;
				}
                if (!(pdetObj[pointId].status & POINT_STATUS_DYNAMIC_BMP))
                {
                    trk->groupStaticPointCnt++;
                }
            }else
            {
                // the track may be merged
//                EMBARC_PRINTF(" Error(point's group is wrong): file = %s, line = %d, trackId = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, i, pointId, groupId);
            }
            nearestRange = pdetObj[pointId].mea_z[1];	//	以关联点的径向距离作为最近距离
            nearestDetIdx = pointId;
            maxVal = pdetObj[pointId].mea_z[0];			//关联点的信噪比作为最大信噪比
            maxValDetIdx = pointId;
        }
		else           //没有关联点的跟踪点
        {
            nearestRange = 1.0e3f;
            nearestDetIdx = POINT_ID_INVALID;
            maxVal = -1.0e3f;
            maxValDetIdx = POINT_ID_INVALID;
        }
        
        /* 2. points in the track box and matching the track is added to the group */
        if(vref > 1.f)
        {
            frontBackExtension = FRONT_BACK_EXTENSION_MIN + vref*0.25f;
            if (frontBackExtension > FRONT_BACK_EXTENSION_MAX)
                frontBackExtension = FRONT_BACK_EXTENSION_MAX;
        }
        else
        {
            frontBackExtension = SIDE_EXTENSION;
        }
        leftRightExtension = trk->sim_z[1]*ANGLE_DELTA_THR;
        if(leftRightExtension < SIDE_EXTENSION)
            leftRightExtension = SIDE_EXTENSION;
        if(leftRightExtension > SIDE_EXTENSION_MAX)
            leftRightExtension = SIDE_EXTENSION_MAX;

        pointElemNoneGroup = gtrack_listGetFirst(groupNoneList);	//	取出没有分组的原始点指针
        while(pointElemNoneGroup)
        {
            pointId = pointElemNoneGroup->data;
            Xtmp[0] = pdetObj[pointId].x - Xt[0];
            Xtmp[1] = pdetObj[pointId].y - Xt[1];
            if(fabsf(Xtmp[0]) > 10.0f || fabsf(Xtmp[1]) > 10.0f)
            {
                pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                continue;
            }
			if (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f
				&& trk->status & TRACK_STATUS_MOVING_BMP
				&& trk->objType == 1 && trk->activeTrkCnt > 20
				&& ((trk->idx_1 < 0 && !(pdetObj[pointId].status & POINT_STATUS_DYNAMIC_BMP))
					|| (trk->idx_1 >= 0 && pointId != trk->idx_1 && clusterDynamicOrNot(pdetObj, trk->idx_1, pointId))))
			{
				pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
				continue;
			}

            matrixMultiply(2, 2, 1, rotMatrixInv, Xtmp, XtBased); 
            if(pointInUprightBoxCrossRegion(XtBased, trk, frontBackExtension, leftRightExtension) && RDP_getTrackConfigPointer()->radarResolutionTestMode != TEST_MODE_RESOLUTION)
            {
                projectedRadialVel = (trk->p_x[2]*pdetObj[pointId].x + trk->p_x[3]*pdetObj[pointId].y)/pdetObj[pointId].mea_z[1];
                dRadialVel = fabsf(projectedRadialVel - pdetObj[pointId].mea_z[2]);
                if(dRadialVel < dRadialVelThre)		//预测的径向速度与原始点的径向速度差值小于阈值
                {
                    pointElemNoneGroupNext = gtrack_listGetNext(pointElemNoneGroup);	//取出下一个原始点
                    retVal = gtrack_listRemoveElement(groupNoneList, pointElemNoneGroup);	//在原来的原始点指针列表中取出当前原始点的指针
                    if(retVal != 0)
                    {
                        EMBARC_PRINTF(" Error( poin not in the list): file = %s, line = %d, trackId = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, i, pointId, groupId);
                    }
                    gtrack_listEnqueue(groupList, pointElemNoneGroup);	//将当前原始点指针加入聚类组
                    pointElemNoneGroup = pointElemNoneGroupNext;
                    pdetObj[pointId].groupId = groupId;
					//当前原始点是关联点，记录相关信息
                    if((pdetObj[pointId].status & POINT_STATUS_ASSOCIATED_BMP) && pdetObj[pointId].index >= 0 && (pGroupInfo[groupId].trackNum < MERGED_TRACKS_NUM_MAX))
                    {
                        pGroupInfo[groupId].trackId[pGroupInfo[groupId].trackNum] = pdetObj[pointId].index;
                        pGroupInfo[groupId].trackNum++;
                        if(pTrkPkg->trk[pdetObj[pointId].index].type == TRACK)
                        {
                            pGroupInfo[groupId].status |= GROUP_STATUS_ACTIVE;
                        }
                    }
                    if ( pdetObj[pointId].mea_z[0] > maxVal )
                    {
                        maxVal = pdetObj[pointId].mea_z[0];
                        maxValDetIdx = pointId;
                    }
                    if ( pdetObj[pointId].mea_z[1] < nearestRange  && pdetObj[pointId].mea_z[0] - maxVal > -6)
                    {
                        nearestRange = pdetObj[pointId].mea_z[1];
                        nearestDetIdx = pointId;
                    }
					//SEL_MIN(trk->nearestPosition[0], XtBased[1]);
					//SEL_MIN(trk->nearestPosition[1], XtBased[0]);
					SEL_MIN(trk->nearestPosition[0], pdetObj[pointId].x);
					SEL_MIN(trk->nearestPosition[1], pdetObj[pointId].y);
					if (fabsf(pdetObj[pointId].mea_z[2]) > 0.1f)
					{
						trk->groupNonzeroVelCnt++;
					}
                    if (!(pdetObj[pointId].status & POINT_STATUS_DYNAMIC_BMP))
                    {
                        trk->groupStaticPointCnt++;
                    }
                }
                else
                {
                    pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                }
            }
			else
            {
				if (pointId != trk->idx_1 && pdetObj[pointId].groupId1 == pdetObj[trk->idx_1].groupId1 && pdetObj[pointId].mea_z[2] > 0.1f)
				{
					trk->groupNonzeroVelCnt++;
				}
                pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
            }
        }
		if (nearestDetIdx != POINT_ID_INVALID && trk->idx_1 == trk->pidNearest)
		{
			trk->pidNearest = nearestDetIdx;
			//            trk->pidStrongest = maxValDetIdx;
			if (!(trk->status & TRACK_STATUS_ASSOCIATED_BMP))
			{
				trk->status |= TRACK_STATUS_WEAK_ASSOCIATED_BMP;
			}
		}

        /* 3. points that are neighbored to the group points are added to the group */
        pointElemGrouped = gtrack_listGetFirst(groupList);
        while(pointElemGrouped)
        {
            pointId = pointElemGrouped->data;
            pointElemNoneGroup = gtrack_listGetFirst(groupNoneList);
            while(pointElemNoneGroup)
            {
                pointId2 = pointElemNoneGroup->data;
                Xtmp[0] = pdetObj[pointId2].x - pdetObj[pointId].x;
                Xtmp[1] = pdetObj[pointId2].y - pdetObj[pointId].y;
                if(fabsf(Xtmp[0]) > 10.0f || fabsf(Xtmp[1]) > 10.0f)
                {
                    pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                    continue;
                }
				if (clusterInSildLineSide(pTrkPkg, pdetObj, pointId, pointId2))	// 抑制护栏边运动目标被误聚类
				{
					pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
					continue;
				}
				if (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f && clusterDynamicOrNot(pdetObj, pointId, pointId2))
				{
					pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
					continue;
				}
                if ((1 == pTrkPkg->aebsidecarsence) && sideCarclusterDynamicOrNot(pdetObj, pointId, pointId2))
                {
                    pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                    continue;
                }
                matrixMultiply(2, 2, 1, rotMatrixInv, Xtmp, XtBased); 
                if(pointInUprightBoxRectRegion(XtBased, NULL, frontBackExtension, leftRightExtension) && RDP_getTrackConfigPointer()->radarResolutionTestMode != TEST_MODE_RESOLUTION)
                {
                    projectedRadialVel = (trk->p_x[2]*pdetObj[pointId2].x + trk->p_x[3]*pdetObj[pointId2].y)/pdetObj[pointId2].mea_z[1];
                    dRadialVel = fabsf(projectedRadialVel - pdetObj[pointId2].mea_z[2]);
                    dRadialVel2 = fabsf(pdetObj[pointId].mea_z[2] - pdetObj[pointId2].mea_z[2]);
                    if(dRadialVel2 < dRadialVel)
                        dRadialVel = dRadialVel2;
                    if(dRadialVel < dRadialVelThre)
                    {
                        pointElemNoneGroupNext = gtrack_listGetNext(pointElemNoneGroup);
                        retVal = gtrack_listRemoveElement(groupNoneList, pointElemNoneGroup);
                        if(retVal != 0)
                        {
                            EMBARC_PRINTF(" Error( poin not in the list): file = %s, line = %d, trackId = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, i, pointId, groupId);
                        }
                        gtrack_listEnqueue(groupList, pointElemNoneGroup);
                        pdetObj[pointId2].groupId = groupId;
                        pointElemNoneGroup = pointElemNoneGroupNext;
                        if((pdetObj[pointId].status & POINT_STATUS_ASSOCIATED_BMP) && pdetObj[pointId].index >= 0 && (pGroupInfo[groupId].trackNum < MERGED_TRACKS_NUM_MAX))
                        {
                            pGroupInfo[groupId].trackId[pGroupInfo[groupId].trackNum] = pdetObj[pointId].index;
                            pGroupInfo[groupId].trackNum++;
                            if(pTrkPkg->trk[pdetObj[pointId].index].type == TRACK)
                            {
                                pGroupInfo[groupId].status |= GROUP_STATUS_ACTIVE;
                            }
                        }
						//SEL_MIN(trk->nearestPosition[0], XtBased[1]);
						//SEL_MIN(trk->nearestPosition[1], XtBased[0]);
						SEL_MIN(trk->nearestPosition[0], pdetObj[pointId].x);
						SEL_MIN(trk->nearestPosition[1], pdetObj[pointId].y);
						if (fabsf(pdetObj[pointId2].mea_z[2]) > 0.1f)
						{
							trk->groupNonzeroVelCnt++;
						}
                        if (!(pdetObj[pointId2].status & POINT_STATUS_DYNAMIC_BMP))
                        {
                            trk->groupStaticPointCnt++;
                        }
                    }else
                    {
                        pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                    }
                }
				else
                {
					//if (pointId2 != trk->idx_1 && pdetObj[pointId2].groupId2 == pdetObj[trk->idx_1].groupId2)
					//{
					//	trk->groupNonzeroVelCnt++;
					//}
                    pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                }
                
            }
            pointElemGrouped = gtrack_listGetNext(pointElemGrouped);
        }
        groupId++;
    }
    
    /* 4. points in the box and extension area are forced grouped and the status is labeled as bias velocity */
    for (j = 0; j < groupId; j++)
    {
        groupList = &pGroupInfo[j].pointList;
        for(i = 0; i < pGroupInfo[j].trackNum; i++)
        {
            trk = &pTrkPkg->trk[pGroupInfo[j].trackId[i]];
            Xt = trk->p_x;
            if(pGroupInfo[j].trackNum > 1 && trk->mergeCnt < 0xff)
            {
                trk->mergeCnt++;
            }else
            {
                trk->mergeCnt = 0;
            }
            vgx = vx = trk->p_x[2];
            vy = trk->p_x[3];
            vgy = vy - pRDP_inVehicleData->vdySpeedInmps;
            v = sqrtf(vx*vx + vy*vy);
            vg = getTrackHeading(vgx, vgy, &cosHeadingAngle, &sinHeadingAngle);
            vref = v > vg ? v : vg;
            if(vref > 1.f)
            {
                frontBackExtension = FRONT_BACK_EXTENSION_MIN + vref*0.25f;
                if (frontBackExtension > FRONT_BACK_EXTENSION_MAX)
                    frontBackExtension = FRONT_BACK_EXTENSION_MAX;
            }
            else
            {
                frontBackExtension = SIDE_EXTENSION;
            }
            leftRightExtension = trk->sim_z[1]*ANGLE_DELTA_THR;
            if(leftRightExtension < SIDE_EXTENSION)
                leftRightExtension = SIDE_EXTENSION;
            if(leftRightExtension > SIDE_EXTENSION_MAX)
                leftRightExtension = SIDE_EXTENSION_MAX;
            dRadialVelThre = vref * 0.2f;
            if(dRadialVelThre > DRADIALVEL_THRESHOLD_MAX*2)
                dRadialVelThre = DRADIALVEL_THRESHOLD_MAX*2;
            if(dRadialVelThre < DRADIALVEL_THRESHOLD_MIN)
                dRadialVelThre = DRADIALVEL_THRESHOLD_MIN;
            /* construct the rotation matrix */
            rotMatrixInv[0] = rotMatrixInv[3] = cosHeadingAngle;
            rotMatrixInv[2] = -sinHeadingAngle;
            rotMatrixInv[1] = sinHeadingAngle;
            pointElemNoneGroup = gtrack_listGetFirst(groupNoneList);
			float tempSideLines = getSideLineDis(trk->p_x[1], pSideLinePkg);
			if (pSideLinePkg->strongSildLineValid && trk->p_x[1] < pSideLinePkg->strongFenceRange[1] && \
				fabsf(trk->p_x[0] - tempSideLines) < 0.3f)
			{
				leftRightExtension *= 0.6f;
			}
            while(pointElemNoneGroup)
            {
                pointId = pointElemNoneGroup->data;
                Xtmp[0] = pdetObj[pointId].x - Xt[0];
                Xtmp[1] = pdetObj[pointId].y - Xt[1];
                if(fabsf(Xtmp[0]) > 10.0f || fabsf(Xtmp[1]) > 10.0f)
                {
                    pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                    continue;
                }

				if (!(trk->status & TRACK_STATUS_MOVING_BMP) && (pdetObj[pointId].status & POINT_STATUS_DYNAMIC_BMP))
				{
					frontBackExtension = SIDE_EXTENSION;
				}
				float tempSideLines = getSideLineDis(pdetObj[pointId].y, pSideLinePkg);
				//如果是护栏和靠近护栏的二轮车或者行人，需要特殊处理，否则容易被护栏给聚类掉，因为leftRightExtension比较大
				if (pSideLinePkg->strongSildLineValid
					&& fabsf(pRDP_inVehicleData->vdySpeedInmps) > 5.0f
					&& fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800
					&& tempSideLines > pdetObj[pointId].x
					&& tempSideLines < Xt[0]
					&& !(trk->status & TRACK_STATUS_MOVING_BMP)
					&& (pdetObj[pointId].status & POINT_STATUS_DYNAMIC_CERTAIN_BMP))
				{
                    leftRightExtension = leftRightExtension > 0.5f ? leftRightExtension * 0.6f : leftRightExtension;
                    frontBackExtension *= 0.6f;
                }
				projectedRadialVel = (trk->p_x[2] * pdetObj[pointId].x + trk->p_x[3] * pdetObj[pointId].y) / pdetObj[pointId].mea_z[1];
				dRadialVel = fabsf(projectedRadialVel - pdetObj[pointId].mea_z[2]);
                matrixMultiply(2, 2, 1, rotMatrixInv, Xtmp, XtBased); 
                if (pointInUprightBoxCrossRegion(XtBased, trk, frontBackExtension, leftRightExtension)
					&& (!(trk->status & TRACK_STATUS_MOVING_BMP) && (pdetObj[pointId].status & POINT_STATUS_DYNAMIC_CERTAIN_BMP) && !((trk->status & TRACK_STATUS_GUARDRAIL_BMP) && pdetObj[pointId].status & POINT_STATUS_DYNAMIC_BMP) )
					&& dRadialVel < 10.f)
                {
                    pointElemNoneGroupNext = gtrack_listGetNext(pointElemNoneGroup);
                    retVal = gtrack_listRemoveElement(groupNoneList, pointElemNoneGroup);
                    if(retVal != 0)
                    {
                        EMBARC_PRINTF(" Error( poin not in the list): file = %s, line = %d, trackId = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, i, pointId, groupId);
                    }
                    gtrack_listEnqueue(groupList, pointElemNoneGroup);
                    pointElemNoneGroup = pointElemNoneGroupNext;
                    pdetObj[pointId].groupId = j;
                    if((pdetObj[pointId].status & POINT_STATUS_ASSOCIATED_BMP) && pdetObj[pointId].index >= 0 && (pGroupInfo[j].trackNum < MERGED_TRACKS_NUM_MAX))
                    {
                        pGroupInfo[j].trackId[pGroupInfo[j].trackNum] = pdetObj[pointId].index;
                        pGroupInfo[j].trackNum++;
                        if(pTrkPkg->trk[pdetObj[pointId].index].type == TRACK)
                        {
                            pGroupInfo[j].status |= GROUP_STATUS_ACTIVE;
                        }
                    }
                    if(dRadialVel > dRadialVelThre)
                    {
                        pdetObj[pointId].status |= POINT_STATUS_BIAS_VELOCITY_BMP;
                    }
                }
				else if (pointInUprightBoxRectRegion(XtBased, trk, frontBackExtension, leftRightExtension)
					&& (!(trk->status & TRACK_STATUS_MOVING_BMP) && (pdetObj[pointId].status & POINT_STATUS_DYNAMIC_CERTAIN_BMP) && !((trk->status & TRACK_STATUS_GUARDRAIL_BMP) && pdetObj[pointId].status & POINT_STATUS_DYNAMIC_BMP))
					&& dRadialVel < 10.f)
                {
                    pointElemNoneGroupNext = gtrack_listGetNext(pointElemNoneGroup);
                    retVal = gtrack_listRemoveElement(groupNoneList, pointElemNoneGroup);
                    if(retVal != 0)
                    {
                        EMBARC_PRINTF(" Error( poin not in the list): file = %s, line = %d, trackId = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, i, pointId, groupId);
                    }
                    gtrack_listEnqueue(groupList, pointElemNoneGroup);
                    pointElemNoneGroup = pointElemNoneGroupNext;
                    pdetObj[pointId].groupId = j;
                    if((pdetObj[pointId].status & POINT_STATUS_ASSOCIATED_BMP) && pdetObj[pointId].index >= 0 && (pGroupInfo[j].trackNum < MERGED_TRACKS_NUM_MAX))
                    {
                        pGroupInfo[j].trackId[pGroupInfo[j].trackNum] = pdetObj[pointId].index;
                        pGroupInfo[j].trackNum++;
                        if(pTrkPkg->trk[pdetObj[pointId].index].type == TRACK)
                        {
                            pGroupInfo[j].status |= GROUP_STATUS_ACTIVE;
                        }
                    }
                    pdetObj[pointId].status |= POINT_STATUS_BIAS_VELOCITY_BMP;  // temporarily use this definition
                }
				else
                {
                    pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                }
            }
            /* cluster basing on point that has been clustered */
            pointElemGrouped = gtrack_listGetFirst(groupList);
            while(pointElemGrouped)
            {
                pointId = pointElemGrouped->data;
                if(pdetObj[pointId].status & POINT_STATUS_BIAS_VELOCITY_BMP)
                {
                    pointElemGrouped = gtrack_listGetNext(pointElemGrouped);
                    continue;
                }
                pointElemNoneGroup = gtrack_listGetFirst(groupNoneList);
                while(pointElemNoneGroup)
                {
                    pointId2 = pointElemNoneGroup->data;
                    Xtmp[0] = pdetObj[pointId2].x - pdetObj[pointId].x;
                    Xtmp[1] = pdetObj[pointId2].y - pdetObj[pointId].y;
                    if(fabsf(Xtmp[0]) > 10.0f || fabsf(Xtmp[1]) > 10.0f)
                    {
                        pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                        continue;
                    }
					if (clusterInSildLineSide(pTrkPkg, pdetObj, pointId, pointId2))	// 抑制护栏边运动目标被误聚类
					{
						pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
						continue;
					}
					if (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f && clusterDynamicOrNot(pdetObj, pointId, pointId2))
					{
						pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
						continue;
					}
                    if ((1 == pTrkPkg->aebsidecarsence) && sideCarclusterDynamicOrNot(pdetObj, pointId, pointId2))
                    {
                        pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                        continue;
                    }
                    dRange = fabsf(pdetObj[pointId2].mea_z[1] - pdetObj[pointId].mea_z[1]);
                    dAngle = fabsf(pdetObj[pointId2].mea_z[3] - pdetObj[pointId].mea_z[3])*DEG2RAD;
                    projectedRadialVel = (trk->p_x[2]*pdetObj[pointId2].x + trk->p_x[3]*pdetObj[pointId2].y)/pdetObj[pointId2].mea_z[1];
                    dRadialVel = fabsf(projectedRadialVel - pdetObj[pointId2].mea_z[2]);
                    dRadialVel2 = fabsf(pdetObj[pointId].mea_z[2] - pdetObj[pointId2].mea_z[2]);
                    if(dRadialVel2 < dRadialVel)
                        dRadialVel = dRadialVel2;
                    matrixMultiply(2, 2, 1, rotMatrixInv, Xtmp, XtBased); 
                    if(pointInUprightBoxRectRegion(XtBased, NULL, frontBackExtension, leftRightExtension)
						&& (!(trk->status & TRACK_STATUS_MOVING_BMP) && (pdetObj[pointId2].status & POINT_STATUS_DYNAMIC_CERTAIN_BMP))
						&& dRadialVel < 10.f)
                    {
                        pointElemNoneGroupNext = gtrack_listGetNext(pointElemNoneGroup);
                        retVal = gtrack_listRemoveElement(groupNoneList, pointElemNoneGroup);
                        if(retVal != 0)
                        {
                            EMBARC_PRINTF(" Error( poin not in the list): file = %s, line = %d, trackId = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, i, pointId, groupId);
                        }
                        gtrack_listEnqueue(groupList, pointElemNoneGroup);
                        pointElemNoneGroup = pointElemNoneGroupNext;
                        pdetObj[pointId2].groupId = j;
                        if(dRadialVel > dRadialVelThre)
                        {
                            pdetObj[pointId2].status |= POINT_STATUS_BIAS_VELOCITY_BMP;
                        }
                        if((pdetObj[pointId2].status & POINT_STATUS_ASSOCIATED_BMP) && pdetObj[pointId2].index >= 0 && (pGroupInfo[j].trackNum < MERGED_TRACKS_NUM_MAX))
                        {
                            pGroupInfo[j].trackId[pGroupInfo[j].trackNum] = pdetObj[pointId2].index;
                            pGroupInfo[j].trackNum++;
                            if(pTrkPkg->trk[pdetObj[pointId2].index].type == TRACK)
                            {
                                pGroupInfo[j].status |= GROUP_STATUS_ACTIVE;
                            }
                        }
                    }
					else if (dRange < RANGE_DELTA_THR
						&& (dAngle < ANGLE_DELTA_THR || pdetObj[pointId].mea_z[1] * dAngle < RANGE_DELTA_THR)
						&& (!(trk->status & TRACK_STATUS_MOVING_BMP) && (pdetObj[pointId2].status & POINT_STATUS_DYNAMIC_CERTAIN_BMP))
						&& dRadialVel < 10.f)
                    {
                        pointElemNoneGroupNext = gtrack_listGetNext(pointElemNoneGroup);
                        retVal = gtrack_listRemoveElement(groupNoneList, pointElemNoneGroup);
                        if(retVal != 0)
                        {
                            EMBARC_PRINTF(" Error( poin not in the list): file = %s, line = %d, trackId = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, i, pointId, groupId);
                        }
                        gtrack_listEnqueue(groupList, pointElemNoneGroup);
                        pointElemNoneGroup = pointElemNoneGroupNext;
                        pdetObj[pointId2].groupId = j;
                        pdetObj[pointId2].status |= POINT_STATUS_BIAS_VELOCITY_BMP; // temporarily use this definition
                        if((pdetObj[pointId2].status & POINT_STATUS_ASSOCIATED_BMP) && pdetObj[pointId2].index >= 0 && (pGroupInfo[j].trackNum < MERGED_TRACKS_NUM_MAX))
                        {
                            pGroupInfo[j].trackId[pGroupInfo[j].trackNum] = pdetObj[pointId2].index;
                            pGroupInfo[j].trackNum++;
                            if(pTrkPkg->trk[pdetObj[pointId2].index].type == TRACK)
                            {
                                pGroupInfo[j].status |= GROUP_STATUS_ACTIVE;
                            }
                        }
                    }else
                    {
                        pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                    }
                    
                }
                pointElemGrouped = gtrack_listGetNext(pointElemGrouped);
            }
        }
        
    }
    return (groupId - groupIdStart);
}
/**
 * @brief 
 * @param pdetObj RDP内部用于跟踪的原始点全局变量指针 
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param pGroupInfo 组指针
 * @param groupIdStart 组开始的ID号
 * @return uint8_t 返回聚类的组数
 */
static uint8_t RDP_Track_clusterOnRestCandis(cdi_t *pdetObj, VDY_DynamicEstimate_t *pRDP_inVehicleData, TrackGroupInfo_t* pGroupInfo, uint8_t groupIdStart)
{
    float dRange, dRadialVel, dAngle, dX, dY;
    short pointId = 0, pointId2 = 0;
    int32_t retVal;
    GTrack_ListObj* groupList;
    GTrack_ListObj* groupNoneList = &pGroupInfo[GROUP_ID_NONE].pointList;
    GTrack_ListElem *pointElemNoneGroup = NULL, *pointElemNoneGroupNext, *pointElemGrouped = NULL;
    float nearestRange, maxVal;
    short nearestDetIdx, maxValDetIdx;
    /* points of the box are added to the buffer */
    float SIDE_EXTENSION = SIDE_EXTENSION_MIN_NORMAL;
    //距离分辨率模式下需要调整门限值，否则0.5m会刚好卡在规格附近
    if(RDP_getTrackConfigPointer()->radarResolutionTestMode == TEST_MODE_RESOLUTION)
    {
        SIDE_EXTENSION = SIDE_EXTENSION_MIN_RESOLUTION;
    }
    float crossThr = SIDE_EXTENSION;
    float verticalThr = SIDE_EXTENSION + fabsf(pRDP_inVehicleData->vdySpeedInmps)*0.25f;
    u16 groupId = groupIdStart;
    if (verticalThr > FRONT_BACK_EXTENSION_MAX)
        verticalThr = FRONT_BACK_EXTENSION_MAX;
    /* other no-grouped points are to be grouped  */
    pointElemNoneGroup = gtrack_listGetFirst(groupNoneList);
    while(pointElemNoneGroup && groupId <= GROUP_ID_END)
    {
        pointId = pointElemNoneGroup->data;
//        if(pdetObj[pointId].groupId == GROUP_ID_NONE)
        {
            pdetObj[pointId].groupId = groupId;
            groupList = &pGroupInfo[groupId].pointList;
            pointElemNoneGroupNext = gtrack_listGetNext(pointElemNoneGroup);
            retVal = gtrack_listRemoveElement(groupNoneList, pointElemNoneGroup);
            if(retVal != 0)
            {
                EMBARC_PRINTF(" Error( poin not in the list): file = %s, line = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, pointId, groupId);
            }
            gtrack_listEnqueue(groupList, pointElemNoneGroup);
            pointElemNoneGroup = pointElemNoneGroupNext;
            maxVal = pdetObj[pointId].mea_z[0];
            nearestRange = pdetObj[pointId].mea_z[1];
            nearestDetIdx = maxValDetIdx = pointId;
            pointElemGrouped = gtrack_listGetFirst(groupList);
            while(pointElemGrouped)
            {
                pointId = pointElemGrouped->data;
                pointElemNoneGroup = gtrack_listGetFirst(groupNoneList);
                while(pointElemNoneGroup)
                {
                    pointId2 = pointElemNoneGroup->data;
                    dX = fabsf(pdetObj[pointId].x - pdetObj[pointId2].x);
                    dY = fabsf(pdetObj[pointId].y - pdetObj[pointId2].y);
                    dRange = fabsf(pdetObj[pointId].mea_z[1] - pdetObj[pointId2].mea_z[1]);
                    dAngle = fabsf(pdetObj[pointId].mea_z[3] - pdetObj[pointId2].mea_z[3])*DEG2RAD;
                    DELTA_RAD_NORM(dAngle);
                    if(nearestRange > TRACK_NEAR_AREA_DISTANCE && \
                        !((dX < SIDE_EXTENSION && dY < SIDE_EXTENSION) || (dRange < 0.8f && dAngle < DEG2RAD*3)))
                    {
                        dRadialVel = fabsf(pdetObj[pointId].mea_z[2] - pdetObj[pointId2].mea_z[2]);
                        if((dRadialVel > 2) ||\
                           (nearestRange > 18.1f&& dRadialVel > 1.2f) ||\
                           (nearestRange > 35.1f&& dRadialVel > 1) ||\
                           (pdetObj[pointId].status & POINT_STATUS_DYNAMIC_BMP && !(pdetObj[pointId2].status & POINT_STATUS_DYNAMIC_BMP))||\
                           (pdetObj[pointId2].status & POINT_STATUS_DYNAMIC_BMP && !(pdetObj[pointId].status & POINT_STATUS_DYNAMIC_BMP)))
                        {
                            pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                            continue;
                        }
                    }
                    if((dRange < 1.5f && (dAngle*pdetObj[pointId].mea_z[1] < 1.0f || dAngle < 4.0f*DEG2RAD)) ||\
                        (dX < crossThr && dY < verticalThr))
                    {
                        pointElemNoneGroupNext = gtrack_listGetNext(pointElemNoneGroup);
                        retVal = gtrack_listRemoveElement(groupNoneList, pointElemNoneGroup);
                        if(retVal != 0)
                        {
                            EMBARC_PRINTF(" Error( poin not in the list): file = %s, line = %d, pointId = %d, groupId = %d\r\n", __FILE__, __LINE__, pointId, groupId);
                        }
                        pdetObj[pointId2].groupId = groupId;
                        gtrack_listEnqueue(groupList, pointElemNoneGroup);
                        pointElemNoneGroup = pointElemNoneGroupNext;
                        if(pdetObj[pointId2].mea_z[0] > maxVal)
                        {
                            maxValDetIdx = pointId2;
                            maxVal = pdetObj[pointId2].mea_z[0];
                        }
                        if(pdetObj[pointId2].mea_z[1] < nearestRange)
                        {
                            nearestDetIdx = pointId2;
                            nearestRange = pdetObj[pointId2].mea_z[1];
                        }
                    }else
                    {
                        pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
                    }
                }
                pointElemGrouped = gtrack_listGetNext(pointElemGrouped);
            }
        }
        groupId++;

        pointElemNoneGroup = gtrack_listGetFirst(groupNoneList);
    }
    return groupId - groupIdStart;
}
/**
 * @brief 
 * @param pdetObj RDP内部用于跟踪的原始点全局变量指针 
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param pGroupInfo 组指针
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 * @return uint32_t 返回总共的组数
 */
uint32_t RDP_Track_clusterOnTracks(cdi_t *pdetObj, VDY_DynamicEstimate_t *pRDP_inVehicleData, TrackGroupInfo_t* pGroupInfo, trk_pkg_t *pTrkPkg)
{
    uint8_t groupIdStart = GROUP_ID_START;
    groupIdStart += RDP_Track_clusterOnTypeTracks(pdetObj, pRDP_inVehicleData, pGroupInfo, pTrkPkg, TRACK, groupIdStart);
    groupIdStart += RDP_Track_clusterOnTypeTracks(pdetObj, pRDP_inVehicleData, pGroupInfo, pTrkPkg, CANDI, groupIdStart);
    groupIdStart += RDP_Track_clusterOnRestCandis(pdetObj, pRDP_inVehicleData, pGroupInfo,  groupIdStart);
    return (groupIdStart - GROUP_ID_START);
}

void trackingBoxAdjust(trk_t* trk, VDY_DynamicEstimate_t *pRDP_inVehicleData)
{
    float Xtmp[2], dXtbased[2], rotMatrixInv[4];
    float temp1, temp2;
    getTrackHeading(trk->x[2], trk->x[3] - pRDP_inVehicleData->vdySpeedInmps, &rotMatrixInv[0], &rotMatrixInv[1]);
    rotMatrixInv[3] = rotMatrixInv[0];
    rotMatrixInv[2] = -rotMatrixInv[1];
    matrixSub(2, 1, trk->x, trk->p_x, Xtmp);
    matrixMultiply(2, 2, 1, rotMatrixInv, Xtmp, dXtbased);
    temp1 = trk->front - dXtbased[0];
    temp2 = trk->back + dXtbased[0];
    if(temp1 > 0 && temp2 > 0)
    {
        trk->front = temp1;
        trk->back = temp2;
    }
    temp1 = trk->left - dXtbased[1];
    temp2 = trk->right + dXtbased[1];
    if(temp1 > 0 && temp2 > 0)
    {
        trk->left = temp1;
        trk->right = temp2;
    }
}

void trackingBoxAdjustScd(trk_t* trk, VDY_DynamicEstimate_t *pRDP_inVehicleData, float deltaX, float deltaY)      //second time adjust box
{
    float Xtmp[2], dXtbased[2], rotMatrixInv[4];
    float temp1, temp2;
    getTrackHeading(trk->x[2], trk->x[3] - pRDP_inVehicleData->vdySpeedInmps, &rotMatrixInv[0], &rotMatrixInv[1]);
    rotMatrixInv[3] = rotMatrixInv[0];
    rotMatrixInv[2] = -rotMatrixInv[1];
    Xtmp[0] = deltaX;
    Xtmp[1] = deltaY;    
    matrixMultiply(2, 2, 1, rotMatrixInv, Xtmp, dXtbased);
    temp1 = trk->front - dXtbased[0];
    temp2 = trk->back + dXtbased[0];
    if(temp1 > 0 && temp2 > 0)
    {
        trk->front = temp1;
        trk->back = temp2;
    }
    temp1 = trk->left - dXtbased[1];
    temp2 = trk->right + dXtbased[1];
    if(temp1 > 0 && temp2 > 0)
    {
        trk->left = temp1;
        trk->right = temp2;
    }
}

/**
 * @brief 标记已被聚类的候选点
 * @param pdetObj RDP内部用于跟踪的原始点全局变量指针 
 * @param pGroupInfo 组指针
 * @param groupNum 组数
 */
void RDP_Track_syncGroupStatus(cdi_t *pdetObj, TrackGroupInfo_t* pGroupInfo, uint32_t groupNum)
{
    uint32_t i;
    uint32_t pointId;
    GTrack_ListElem *pointElem;
    for(i = 0; i < groupNum; i++)
    {
        if(pGroupInfo[i].status&GROUP_STATUS_ACTIVE)
        {
            pointElem = gtrack_listGetFirst(&pGroupInfo[i].pointList);
            while(pointElem)
            {
                pointId = pointElem->data;
                pdetObj[pointId].status |= POINT_STATUS_CLUSTERED_BMP;
                pointElem = gtrack_listGetNext(pointElem);
            }
        }
    }
}

/**
 * @brief 航迹框功能，估计目标大小
 * @param pdetObj RDP内部用于跟踪的原始点全局变量指针 
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param pGroupInfo 组指针
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 */
void RDP_Track_collecteBoxPoints(cdi_t *pdetObj, VDY_DynamicEstimate_t *pRDP_inVehicleData, TrackGroupInfo_t* pGroupInfo, trk_pkg_t *pTrkPkg)
{
    uint32_t i;
    int16_t groupId, pointId;
    float vgx,vgy; // x&y component of vg
    trk_t* trk;
    GTrack_ListObj* groupList;
    GTrack_ListElem *pointElem;
    float rotMatrixInv[4];    // rotation matrix
    // float* Xt;      // track point position
//    float X0[2]; 
    float Xtmp[2];    // center of the box, tmp
    float XtBased[2]; // position base on Xt
    int8_t lengthArray[MAX_POINT_NUM_PER_TRACK];
    int8_t widthArray[MAX_POINT_NUM_PER_TRACK];
    uint8_t boxPointNum;
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrkPkg->trk[i];
    
        if (trk->type == NONE)
            continue;
        if(trk->status & TRACK_STATUS_ASSOCIATED_BMP)
        {
            boxPointNum = 0;
            groupId = pdetObj[trk->idx_1].groupId;
            groupList = &pGroupInfo[groupId].pointList;
            vgx = trk->p_x[2];
            vgy = trk->p_x[3] - pRDP_inVehicleData->vdySpeedInmps;
            getTrackHeading(vgx, vgy, &rotMatrixInv[0], &rotMatrixInv[1]);
            rotMatrixInv[3] = rotMatrixInv[0];
            rotMatrixInv[2] = -rotMatrixInv[1];
            
            pointElem = gtrack_listGetFirst(groupList);
			//if (trk->idx_1 >= 0)
			//{
			//	trk->nearestX = pdetObj[trk->idx_1].x;
			//	trk->nearestY = pdetObj[trk->idx_1].y;
			//}
			//else
			//{
			//	;

            while(pointElem)
            {
                pointId = pointElem->data;
                if(!(pdetObj[pointId].status & POINT_STATUS_BIAS_VELOCITY_BMP))
                {
					//if (trk->nearestX > pdetObj[pointId].x && pdetObj[pointId].x < trk->x[0])
					//{
					//	trk->nearestX = pdetObj[pointId].x;
					//}
					//if (trk->nearestY > pdetObj[pointId].y && pdetObj[pointId].y < trk->x[1])
					//{
					//	trk->nearestY = pdetObj[pointId].y;
					//}
                    Xtmp[0] = pdetObj[pointId].x - trk->p_x[0];
                    Xtmp[1] = pdetObj[pointId].y - trk->p_x[1];
					//限制航迹框长度，和宽度，左右两边限制在±5m；前后长度限制在正负8m，基本可以覆盖所有大车。
					if (fabsf(Xtmp[0]) > 5 || (fabsf(Xtmp[1]) > 16 && trk->objType == 4) || (fabsf(Xtmp[1]) > 8 && trk->objType != 4))
					{
						pointElem = gtrack_listGetNext(pointElem);
						continue;
					}
                    //XtBased[0]代表y方向，XtBased[1]代表x方向
                    matrixMultiply(2, 2, 1, rotMatrixInv, Xtmp, XtBased);
                    if(boxPointNum < MAX_POINT_NUM_PER_TRACK)
                    {
                        lengthArray[boxPointNum] = (int8_t)(XtBased[0]*DISTANCE_ACCURACY_RECIPROCAL);
                        widthArray[boxPointNum] = (int8_t)(XtBased[1]*DISTANCE_ACCURACY_RECIPROCAL);
                        boxPointNum++;
                    }
                }
                pointElem = gtrack_listGetNext(pointElem);
            }
            boxPointBuffCollection(trk, lengthArray, widthArray, boxPointNum);
        }
    }
}

//统计弱侧rcta区域目标
static void RDP_UpdataRctaAreaCdi(VDY_DynamicEstimate_t *pVdy, cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg)
{
    //清空相关
    pTrkPkg->rctaAreabufferCnt = 0;
    memset(pTrkPkg->rctaAreabufferX,0,sizeof (pTrkPkg->rctaAreabufferX));
    memset(pTrkPkg->rctaAreabufferV,0,sizeof (pTrkPkg->rctaAreabufferV));
    pTrkPkg->rctaAreaMaxlat = pTrkPkg->rctaAreaMaxlng = FLT_MIN;
    pTrkPkg->rctaAreaMinlat = pTrkPkg->rctaAreaMinlng = FLT_MAX;
    int8_t rctaTrkId = FILTER_NON_ASSOC;
    static uint32_t blockcnt = 0;        // DOW遮挡计数
    uint8_t singleblockcnt = 0;          // 单次遮挡计数
    rdp_config_t* config = RDP_getTrackConfigPointer();

    //在不满足rcta/fcta条件时，不做处理
    if(pVdy->vdyGearState != GEAR_SIG_R && pVdy->vdySpeedInmps > 3)
    {
        pTrkPkg->rctaAreaValid = 0;
    }
    else
    {
        //查找跟踪目标,关联点的径向速度应该是负的
        for( uint32_t i = 0 ; i < MAX_NUM_OF_TRACKS ; i++)
        {
            if(pTrkPkg->trk[i].type < CANDI
                    || pTrkPkg->trk[i].idx_1 == FILTER_NON_ASSOC
                    || pTrkPkg->trk[i].x[0] > 1.0f
                    || pTrkPkg->trk[i].x[1] > 10.f
                    || pTrkPkg->trk[i].trkCnt > 30
                    || ((pTrkPkg->trk[i].status & TRACK_STATUS_MOVING_BMP) == 0 && pCdiPkg->cdi[pTrkPkg->trk[i].idx_1].mea_z[2] > -0.5f)
                    || pCdiPkg->cdi[pTrkPkg->trk[i].idx_1].mea_z[2] > -0.1f)
            {
                continue;
            }
            rctaTrkId = i;
            break;
        }
    }

    //无效不处理
    if(rctaTrkId != FILTER_NON_ASSOC)
    {
        //生成区域
        float minX = pTrkPkg->trk[rctaTrkId].x[0] - 0.5f;
        float maxX = pTrkPkg->trk[rctaTrkId].x[0] + 4.0f;
        float minY = pTrkPkg->trk[rctaTrkId].x[1] - 0.5f;
        float maxY = pTrkPkg->trk[rctaTrkId].x[1] + 0.5f;

        //统计目标点
        for( uint32_t i = 0 ; i < pCdiPkg->number ; i++)
        {
            if(fabsf(pCdiPkg->cdi[i].mea_z[2]) < 0.1f
                    || pCdiPkg->cdi[i].x < minX
                    || pCdiPkg->cdi[i].x > maxX
                    || pCdiPkg->cdi[i].y < minY
                    || pCdiPkg->cdi[i].y > maxY
                    )
            {
                continue;
            }

            if(pTrkPkg->rctaAreabufferCnt < 10)
            {
                pTrkPkg->rctaAreabufferX[pTrkPkg->rctaAreabufferCnt] = pCdiPkg->cdi[i].x;     //x
                pTrkPkg->rctaAreabufferV[pTrkPkg->rctaAreabufferCnt++] = pCdiPkg->cdi[i].mea_z[2];   //v

                //统计最大最小值，用于计算横纵向速度
                pTrkPkg->rctaAreaMinlat = pCdiPkg->cdi[i].x < pTrkPkg->rctaAreaMinlat ? pCdiPkg->cdi[i].x : pTrkPkg->rctaAreaMinlat;
                pTrkPkg->rctaAreaMaxlat = pCdiPkg->cdi[i].x > pTrkPkg->rctaAreaMaxlat ? pCdiPkg->cdi[i].x : pTrkPkg->rctaAreaMaxlat;
                pTrkPkg->rctaAreaMinlng = pCdiPkg->cdi[i].y < pTrkPkg->rctaAreaMinlng ? pCdiPkg->cdi[i].y : pTrkPkg->rctaAreaMinlng;
                pTrkPkg->rctaAreaMaxlng = pCdiPkg->cdi[i].y > pTrkPkg->rctaAreaMaxlng ? pCdiPkg->cdi[i].y : pTrkPkg->rctaAreaMaxlng;
            }
            else
            {
                break;
            }
        }
    }

    //若区域个数满足，大小判断,x小的速度小，满足的个数和错误的个数
    uint8_t matchCnt = 0;
    uint8_t matchErrCnt = 0;
    uint8_t existProb = 0;
    if(pTrkPkg->rctaAreabufferCnt > 3)
    {
        for(int i = 0; i < pTrkPkg->rctaAreabufferCnt; i++)
        {
            for(int j = i + 1; j < pTrkPkg->rctaAreabufferCnt; j++)
            {
                if(pTrkPkg->rctaAreabufferX[i] > pTrkPkg->rctaAreabufferX[j]
                        && pTrkPkg->rctaAreabufferV[i] > pTrkPkg->rctaAreabufferV[j] )
                {
                    matchCnt++;
                }
                else if(pTrkPkg->rctaAreabufferX[i] < pTrkPkg->rctaAreabufferX[j]
                        && pTrkPkg->rctaAreabufferV[i] < pTrkPkg->rctaAreabufferV[j] )
                {
                    matchCnt++;
                }
                else
                {
                    matchErrCnt++;
                }
            }
        }
        //计算概率
        existProb = (uint8_t)((matchCnt * 1.0f) / (matchCnt + matchErrCnt) * 100.0f);
    }

    //判定是否rcta目标生效
    pTrkPkg->rctaAreaValid = (existProb < 70) ? 0 : 1;

    //跟踪目标rcta目标判断，判断后打标记，后续使用
    for(uint32_t i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if(pTrkPkg->trk[i].type == NONE)
        {
            continue;
        }
        float dx = (pTrkPkg->trk[i].x[0] - pTrkPkg->trk[i].startPosition[0]);
        float dy = (pTrkPkg->trk[i].x[1] - pTrkPkg->trk[i].startPosition[1]);
        //区域内刚起的目标打上标记
        if((pTrkPkg->rctaAreaValid || pVdy->vdyGearState == GEAR_SIG_R || pVdy->vdySpeedInmps < 3)
                && pTrkPkg->trk[i].type == CANDI
                && pTrkPkg->trk[i].idx_1 != FILTER_NON_ASSOC
                && pTrkPkg->trk[i].x[0] < 1.0f
                && pTrkPkg->trk[i].x[1] < 10 
                //&& pTrkPkg->trk[i].trkCnt < 3
                && pCdiPkg->cdi[pTrkPkg->trk[i].idx_1].mea_z[2] < -1.f
                && pCdiPkg->cdi[pTrkPkg->trk[i].idx_1].mea_z[3] < -10.f
                //增加横向移动和纵向移动距离确认，否则路试红绿灯容易判断错误
                && dx > fabsf(2 * dy)
                && (pTrkPkg->trk[i].status & TRACK_STATUS_RFCTA_AREA_OBJ_BMP) == 0
                )
        {
            pTrkPkg->trk[i].status |= TRACK_STATUS_RFCTA_AREA_OBJ_BMP;
        }
        //需要去标记
        else if(pTrkPkg->rctaAreaValid == 0
                && pTrkPkg->trk[i].type == TRACK
                && pTrkPkg->trk[i].x[0] > 2.0f
                && (pTrkPkg->trk[i].status & TRACK_STATUS_RFCTA_AREA_OBJ_BMP)
                )
        {
            pTrkPkg->trk[i].status &= ~TRACK_STATUS_RFCTA_AREA_OBJ_BMP;
        }
    }

    // DOW相关护栏目标也在此处处理

    // 产品走查一般双护栏场景dow晚报.  检测此场景,  dow横向碰撞预测略微放开, 降低晚报概率
    // 基本思路是一定纵向速度以上的目标 横向一定区域是否包含一定数量的静止点
    // 仅在自车静止的场景下检测
    if (fabsf(pVdy->vdySpeedInmps) < 0.1f)
    {
        for( uint32_t i = 0 ; i < MAX_NUM_OF_TRACKS ; i++)
        {
            if(pTrkPkg->trk[i].type == NONE)
            {
                continue;
            }
            if ((pTrkPkg->trk[i].sim_z[2] < (-4.0f)))
            {
                uint8_t num = 0;
                for (uint32_t k = 0; k < MAX_NUM_OF_POINTS; k++)
                {
                    // 原始点超过跟踪点一定距离.  舍弃判断
                    if (pCdiPkg->cdi[k].mea_z[1] > (pTrkPkg->trk[i].sim_z[1] + 6))
                    {
                        break;
                    }
                    // 动态原始点不参与护栏场景计算 原始点X在目标右侧  不参与统计 整体判断逻辑尽可能精简 
                    if (((pCdiPkg->cdi[k].status & POINT_STATUS_DYNAMIC_BMP) != 0) || (pCdiPkg->cdi[k].x < (pTrkPkg->trk[i].x[0])))
                    {
                        continue;
                    }
        
                    // 原始点在跟踪点的一定径向距离内, 且 原始点X与在跟踪点X的左侧一定范围内
                    // 原始点与跟踪点的Y值在一定范围内
                    if ((fabsf(pTrkPkg->trk[i].sim_z[1] - pCdiPkg->cdi[k].mea_z[1]) < 8.0f) && (pCdiPkg->cdi[k].x - pTrkPkg->trk[i].x[0] < 4.0f) &&
                        (pCdiPkg->cdi[k].x - pTrkPkg->trk[i].x[0] > 0.0f) && (fabsf(pCdiPkg->cdi[k].y - pTrkPkg->trk[i].x[1]) < 5.0f))
                    {
                        num++;
                    }
                    // 超过一定数值,  累计一次护栏计数.  
                    if (num >= 3)
                    {
                        if (pTrkPkg->trk[i].dowguardCrossCnt < MAX_CHAR)
                        {
                            pTrkPkg->trk[i].dowguardCrossCnt++;
                        }
                        break;
                    }
                }
            }
            else
            {
                pTrkPkg->trk[i].dowguardCrossCnt = 0;
            }
        }
    }

    // DOW 产品走查 遮挡场景.  4号雷达 后方2米有遮挡   左侧 3-6米存在护栏. 此场景下尽早起批 是否需要同步抑制目标的横向速度? 
    if (((config->installPosition == SENSOR_POSITION_REAR_LEFT) || (config->installPosition == SENSOR_POSITION_FRONT_RIGHT)) && (fabsf(pVdy->vdySpeedInmps) < 0.1f))
    {
        // for(uint32_t i = 0 ; i < pCdiPkg->number ; i++)
        // {
        //     if (pCdiPkg->cdi[i].mea_z[1] > 5.0f)
        //     {
        //         break;
        //     }
        //     if((fabsf(pCdiPkg->cdi[i].mea_z[1]) > 1.0f) && (fabsf(pCdiPkg->cdi[i].mea_z[1]) < 5.0f))
        //     {
        //         if ((pCdiPkg->cdi[i].x < 0.5f) && (pCdiPkg->cdi[i].x > -1.5f) && 
        //             (pCdiPkg->cdi[i].y > 1.5f) && (pCdiPkg->cdi[i].y < 4.5f) &&
        //             ((pCdiPkg->cdi[i].status & POINT_STATUS_DYNAMIC_BMP) == 0) &&
        //             (pCdiPkg->cdi[i].mea_z[0] > 40.0f))
        //         {
        //             singleblockcnt++;
        //         }
        //     }
        // }
        // if (singleblockcnt >= 2)
        // {
        //     blockcnt++;
        // }
		float tempSideLines = pTrkPkg->sideLines.strongParam[0];
        // 后方存在遮挡  左侧存在护栏. 产品走查场景.
        if ((pTrkPkg->sideLines.strongSildLineValid) &&
            (tempSideLines >= 3.0f) && (tempSideLines <= 6.0f) &&
            ((pTrkPkg->sideLines.strongFenceRange[1] - pTrkPkg->sideLines.strongFenceRange[0]) >= 10.0f))
        {
            pTrkPkg->dowguardrailblocksence = 1;
        }
    }
    else
    {
        pTrkPkg->dowguardrailblocksence = 0;
        blockcnt = 0;
    }
}


/**
 *  针对各种需要特殊处理的场景, 统一在此处进行识别
 */
static void RDP_SceneRecognition(VDY_DynamicEstimate_t *pVdy, cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg)
{
    uint8_t sideCar = 0;
    int i = 0;
    float steeringangle = fabsf(pVdy->vdySteeringAngle);
    float turningRadius = fabsf(pVdy->vdyCurveRadius);
    trk_t* trk;
    rdp_config_t* config = RDP_getTrackConfigPointer();

    // AEB 鬼探头场景识别
    if ((1 == config->isFront) && (steeringangle < 15.0f) && (turningRadius > 500.0f) && (fabsf(pVdy->vdySpeedInmps) > 2.0f) && (fabsf(pVdy->vdySpeedInmps) < 6.5f))
    {
        if (pTrkPkg->stableDrvCnt < MAX_CHAR)
        {
            pTrkPkg->stableDrvCnt++;
        }
    }
    else
    {
        // if (pTrkPkg->stableDrvCnt > 10)
        // {
        //     pTrkPkg->stableDrvCnt -= 10;
        // }
        // else
        //{
            pTrkPkg->stableDrvCnt = 0;      // 场景暂定一票否决
            pTrkPkg->aebsidecar_x = 0.0f;
            pTrkPkg->aebsidecar_y = 0.0f;
        //}
    }

    if (pTrkPkg->stableDrvCnt >= 60)
    {
        for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
        {
            trk = &pTrkPkg->trk[i];
            if ((trk->type != TRACK) || (trk->x[1] > 30.0f) || (trk->x[0] > 5.0f) || (trk->status & TRACK_STATUS_MOVING_BMP))
            {
                continue;
            }
            if ((trk->x[1] < 30.0f) && (trk->x[0] > 1.5f) && (trk->x[0] < 4.5f) && (trk->activeTrkCnt > 40) 
                && (!(trk->status & TRACK_STATUS_MOVING_BMP)) && (trk->sim_z[0] > 45))
            {
                sideCar = 1;
                pTrkPkg->aebsidecar_x = trk->x[0];
                pTrkPkg->aebsidecar_y = trk->x[1];
            }
        } 
    }
    pTrkPkg->aebsidecarsence = sideCar;

    // AEB 无遮挡标准横穿场景识别
    if ((1 == config->isFront) && (steeringangle < 15.0f) && (turningRadius > 500.0f) && (fabsf(pVdy->vdySpeedInmps) > 7.0f) && (fabsf(pVdy->vdySpeedInmps) < 12.5f))
    {
        if (pTrkPkg->aebstandcrossstableDrvCnt < MAX_CHAR)
        {
            pTrkPkg->aebstandcrossstableDrvCnt++;
        }
    }
    else
    {
        // if (pTrkPkg->aebstandcrossstableDrvCnt > 10)
        // {
        //     pTrkPkg->aebstandcrossstableDrvCnt -= 10;
        // }
        // else
        // {
            pTrkPkg->aebstandcrossstableDrvCnt = 0;
        //}
    }    
    if (pTrkPkg->aebstandcrossstableDrvCnt >= 15)       // 一定时间的稳定直行.
    {
        uint8_t standcrosssence = 1;
        for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
        {
            trk = &pTrkPkg->trk[i];
            if ((trk->type == TRACK) && (trk->x[0] > (-1.0f)) && (trk->x[0] < (3.5f)) && (trk->x[1] > (2.0f)) && (trk->x[1] < (30.0f)))
            {
                standcrosssence = 0;
                break;
            }
        }
        if ((i >= (MAX_NUM_OF_TRACKS - 1)) && (standcrosssence != 0))
        {
            pTrkPkg->aebstandcrosssence = 1;
        }
        else
        {
            pTrkPkg->aebstandcrosssence = 0;
        }
    }
    else
    {
        pTrkPkg->aebstandcrosssence = 0;
    }
}


float calcMaxStaticRang(cdi_pkg_t *pCdiPkg)
{
    float maxRange = 0;
    float distance = 0;

    for (int i = 0; i < pCdiPkg->number; i++)
    {
        for (int j = i + 1; j < pCdiPkg->number; j++)
        {
            distance = powf(pCdiPkg->cdi[i].x - pCdiPkg->cdi[j].x, 2) + powf(pCdiPkg->cdi[i].y - pCdiPkg->cdi[j].y, 2);
            if (distance > maxRange)
            {
                maxRange = distance;
            }
        }
    }

    return sqrtf(maxRange);
}

//IsXMax 0--Y 。1--X 。2--右边的个数
float calcMaxXYRang(int idx, cdi_pkg_t *pCdiPkg, int IsXMax)
{
    //得到速度
    // float VHist = (VEL_SCALE_NUM / 2 - (idx * HIST_SCALE + HIST_SCALE / 2)) * gVelocityRes[0];
    float HistX[10] = {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1};
    float HistY[10] = {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1};
    int Histcnt = 0;
    float HistXMax = 0, HistYMax = 0, HistRightNumber = 0;
    //找到对应目标的X和Y
    for (int k = 0; k < pCdiPkg->number; k++)
    {
        /*
        if ((fabsf(VHist + pCdiPkg->cdi[k].vy) < gVelocityRes[0] * 2) && Histcnt < 10)
        {
            HistX[Histcnt] = pCdiPkg->cdi[k].x;
            HistY[Histcnt++] = pCdiPkg->cdi[k].y;
        }
        */
    }
	//查找X , Y的最大差距值 以及右边的个数
    for (int k = 0; k < Histcnt; k++)
    {
        for (int z = k; z < Histcnt; z++)
        {
            HistXMax = fabsf(HistX[k] - HistX[z]) > HistXMax ? fabsf(HistX[k] - HistX[z]) : HistXMax;
            HistYMax = fabsf(HistY[k] - HistY[z]) > HistYMax ? fabsf(HistY[k] - HistY[z]) : HistYMax;
        }

        //查找右边的个数
        if (HistX[k] < 0)
        {
            HistRightNumber++;
        }
    }

    //返回
    switch (IsXMax)
    {
    case 0:
        return HistYMax;
        break;
    case 1:
        return HistXMax;
        break;
    case 2:
        return HistRightNumber;
        break;
    default:
        return 0;
        break;
    }
}


#ifdef  SCENARIO_ESTIMATE
//栏杆场景判断
void pkg_FenceSceneEstimate(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg)
{
    /*
	0、没有弯道时。
	1、统计左边类似护栏的静止点个数-x【-0.5~-4】。
	2、统计左边的类似护栏静止点个数-x【0.5~-4】
	3、如果左边护栏个数 > 10 & 右边 < 10 ,AngleOffset = -0.5;
	4、如果右边护栏个数 > 10 & 左边 < 10 ,AngleOffset = +0.5;
	5、如果右边护栏个数 > 10 & 左边 > 10 ,AngleOffset = 0
	6、如果右边护栏个数 < 10 & 左边 < 10 ,AngleOffset = 0
	可能出现的现象：
	这里修改的是跟踪前的，突然出现修改是否会和跟踪后的目标匹配不上。
	gCarVelocity  gVehicleInfo
	*/
    int i, l_FenceCnt = 0, r_FenceCnt = 0;
    float AngleOffset = 0.0f, angle;
    if (fabsf(pRDP_inVehicleData->vdyCurveRadius) < 2000 || fabsf(gCarVelocity) < 1 || pCdiPkg->number < 15 //原始点个数小于15个
    )
    {
        //不满足条件 - 也不用调整角度等
        gInFrenceSence = gInFrenceSence > 0 ? gInFrenceSence - 1 : gInFrenceSence;
        //矫正恢复时需要30帧才恢复0
        if (gInFrenceSence == 0)
        {
            gAngleOffset = 0;
        }
        return;
    }

    for (i = 0; i < pCdiPkg->number; i++)
    {
        if ((pCdiPkg->cdi[i].valid != CDI_STATUS_NORMAL)                    //无效目标
            || fabsf(pCdiPkg->cdi[i].vy + gCarVelocity) > 2 //非静止点不做统计
        )
        {
            continue;
        }

        //统计左边
        if ((pCdiPkg->cdi[i].x < -1) && (pCdiPkg->cdi[i].x > -4))
        {
            l_FenceCnt++;
        }

        //统计右边
        if ((pCdiPkg->cdi[i].x > 1) && (pCdiPkg->cdi[i].x < 4))
        {
            r_FenceCnt++;
        }
    }

    //角度调整
    if (l_FenceCnt < 15 && r_FenceCnt > 15)
    {
        gInFrenceSence = 30;
        AngleOffset = 0.5f;
    }
    else if (l_FenceCnt > 15 && r_FenceCnt < 15)
    {
        gInFrenceSence = 30;
        AngleOffset = -0.5f;
    }
    else
    {
        //必须减到0才恢复
        gInFrenceSence = gInFrenceSence > 0 ? gInFrenceSence - 1 : gInFrenceSence;
        //矫正恢复时需要30帧才恢复0
        if (gInFrenceSence == 0)
        {
            AngleOffset = 0;
        }
        else
        {
            //没有减到0 用之前的
            AngleOffset = gAngleOffset;
        }
    }

    //调整角度、横向距离、Centeroffset 0时没变，可不用调整
    if (AngleOffset != 0)
    {
        for (i = 0; i < pCdiPkg->number; i++)
        {
            if (pCdiPkg->cdi[i].valid != CDI_STATUS_NORMAL || fabsf(pCdiPkg->cdi[i].vy + gCarVelocity) > 2 || (pCdiPkg->cdi[i].y < 20 && fabsf(pCdiPkg->cdi[i].x) < 1.2f) //20m内的正前方车道±1内不调整 ，怕把角反拉跑
            )
            {
                //无效点和非绝对静止点不处理-只处理绝对静止的点
                continue;
            }

            angle = pCdiPkg->cdi[i].mea_z[3] + AngleOffset;
            pCdiPkg->cdi[i].mea_z[3] = angle;
            angle = angle / 180 * M_PI;
            pCdiPkg->cdi[i].x = pCdiPkg->cdi[i].mea_z[1] * sinf(angle);
            pCdiPkg->cdi[i].y = pCdiPkg->cdi[i].mea_z[1] * cosf(angle);
            pCdiPkg->cdi[i].vx = pCdiPkg->cdi[i].mea_z[2] * sinf(angle);
            pCdiPkg->cdi[i].vy = pCdiPkg->cdi[i].mea_z[2] / cosf(angle);
            pCdiPkg->cdi[i].centerOffset = pCdiPkg->cdi[i].x; //曲率半径大时，centerOffset就等于x
        }
    }
    gAngleOffset = AngleOffset;
}

//地下车库场景判断
void pkg_BarnSceneEstimate(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg)
{
    /*
	1、本车速度 < 6m/s
	2、一帧数据平均SNR偏大
	
	1、点的分布，左边车道  中间车道 右边车道
	2、SNR 10-20内
	*/
    int i, snrCnt = 0, LaneCnt = 0, lcnt = 0, rcnt = 0, Mcnt = 0, Othercnt = 0, YmaxTarget = 0, lcnt2 = 0, rcnt2 = 0, Mcnt2 = 0, xMore5 = 0;
    cdi_t *cdi = NULL;
    float franeAllSnr = 0, frameAvgSnr = 0, lcnt_y = 0, Mcnt_y = 0, rcnt_y = 0;
    for (i = 0; i < pCdiPkg->number; i++)
    {
        if (pCdiPkg->cdi[i].valid == CDI_STATUS_NONE)
        {
            continue;
        }
        cdi = &pCdiPkg->cdi[i];
        //SNR统计
        franeAllSnr += cdi->mea_z[0];
        snrCnt++;
        //点分布统计
        if (fabsf(cdi->x) <= 6 && fabsf(cdi->vy + gCarVelocity) < 2 && cdi->y > 20)
        {
            LaneCnt++;
            if (cdi->x < -2)
            {
                lcnt++;
                lcnt_y += cdi->y;
            }
            else if (fabs(cdi->x) <= 2)
            {
                Mcnt++;
                Mcnt_y += cdi->y;
            }
            else if (cdi->x > 2)
            {
                rcnt++;
                rcnt_y += cdi->y;
            }
        }
        else if (fabsf(cdi->x) > 6 && cdi->y > 20)
        {
            Othercnt++;
        }
        //非绝对静止目标数量统计 -- 应该改用跟踪偶的
        if (fabsf(cdi->vy + gCarVelocity) > 2 && cdi->y > 20)
        {
            if (cdi->x < -2)
            {
                lcnt2++;
            }
            else if (fabs(cdi->x) <= 2)
            {
                Mcnt2++;
            }
            else if (cdi->x > 2)
            {
                rcnt2++;
            }

            if (fabsf(cdi->x) > 5) //x很大的非绝对禁止点
            {
                xMore5++;
            }
        }

        if (YmaxTarget < cdi->y)
        {
            YmaxTarget = cdi->y;
        }
    }
    //每个车道的Y均值计算--暂时没有用
    lcnt_y = (float)lcnt_y / lcnt;
    Mcnt_y = (float)Mcnt_y / Mcnt;
    rcnt_y = (float)rcnt_y / rcnt;

    //SNR平均值
    frameAvgSnr = franeAllSnr / snrCnt;
    //地库条件判断 -- 相对较严 - 一旦出发地库模式会持续 200/75 ms
    if (LaneCnt > 6 && (frameAvgSnr > 13) && (frameAvgSnr < 20) && ((lcnt2 == 0) || (rcnt2 == 0)) && (xMore5 < 2) && (YawRateCnt > 0) && (gAbsCarVel < 6) && (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 500) &&
        ((CHECK_BARN(lcnt, LaneCnt) && CHECK_BARN(Mcnt, LaneCnt) && CHECK_BARN(rcnt, LaneCnt) && (Mcnt > 3)) || ((Mcnt > 7) && (lcnt > LaneCnt / 3 - 2) && (rcnt > LaneCnt / 3 - 2))))
    {
        gInBarn = 200;
    }
    else
    {
        if (gInBarn > 0)
        {
            gInBarn--;
        }
    }

    //弯道判断 -- 如果长时间没有大弯道 - -表示不是地库
    if (fabsf(pRDP_inVehicleData->vdyCurveRadius) < 50)
    {
        YawRateCnt = 400;
    }
    else if (YawRateCnt > 0)
    {
        YawRateCnt--;
    }

#if 0
	//地库模式转弯时 加大地库延时大约 5s  -- 如果不接地库会导致地库判断变弱
	if (fabsf(pRDP_inVehicleData->vdyCurveRadius) < 40 && gInBarn > 0 && gInBarn < 200)
	{
		gInBarn += 60;
	}
#else
    //地库模式转弯时 加大地库延时大约 5s  -- 如果不接地库会导致地库判断变弱
    if (fabsf(pRDP_inVehicleData->vdyCurveRadius) < 40 && gInBarn < 100)
    {
        gInBarn += 4;
    }
#endif // 0

    //当车速太大或是0时 直接允许跳出地库模式
    if (fabsf(gCarVelocity > 6) || gCarVelocity == 0)
    {
        if (gInBarn > 0)
        {
            gInBarn = gInBarn - 10;
        }
        else
        {
            gInBarn = 0;
        }
    }
    //地库模式标志
    if (gInBarn <= 0)
    {
        gInBarn = 0;
        gInBarnFlag = 0;
        gRadarFrenceFlag &= 0xBFFF; //无效
    }
    else
    {
        gInBarnFlag = 1;
        gRadarFrenceFlag |= 0xC000; //有效
    }
}

void pkg_RuralSceneEstimate(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg)
{
    /*乡村道路判断条件
	1、本车速度 5 - 12
	2、跟踪后的目标列表中无 （x > 3 && 非绝对禁止目标） 的目标存在
	3、10米外的原始点 5/3以上的点都在三车道内
	gInRural 是在乡村道路的标记 -- >=JudgeNum表示在乡村  <JudgeNum表示不在乡村
	*/
    int ruralFlag = 0, x_lessTraCnt = 0, x_lessRawCnt = 0, x_MoreRawCnt = 0, x_MoreRawCntTarget = 0;
    int i;
    //float lessMore = 3 / 5.0f;
    int JudgeNum = 20;
    //查询 个数 跟踪后的目标列表中无 （x > 3 && 非绝对禁止目标） 的目标存在
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        while (pTrkPkg->trk[i].type != TRACK)
        {
            i++;
            if (i >= MAX_NUM_OF_TRACKS)
            {
                break;
            }
        }
        if (i < MAX_NUM_OF_TRACKS && (fabsf(pTrkPkg->trk[i].x[0]) > 3) && (fabsf(pTrkPkg->trk[i].x[3] + gCarVelocity) > 1))
        {
            x_lessTraCnt++;
        }
    }
    //查询 10米外的原始点 三车道内与三车道外的点个数
    for (i = 0; i < pCdiPkg->number; i++)
    {
        while (pCdiPkg->cdi[i].y < 20 || pCdiPkg->cdi[i].valid == 0)
        {
            i++;
            if (i >= pCdiPkg->number)
            {
                break;
            }
        }
        //三车道内外的个数统计
        if (fabsf(pCdiPkg->cdi[i].x) <= 8)
        {
            x_lessRawCnt++; //三车道内的目标个数
        }
        else
        {
            x_MoreRawCnt++; //三车道外的目标个数
        }
        //绝对静止车辆在三车道外的原始点统计
        if (fabsf(pCdiPkg->cdi[i].vy + gCarVelocity) > 1 && fabsf(pCdiPkg->cdi[i].x) > 3)
        {
            x_MoreRawCntTarget++;
        }
    }
    //判断本帧是否是村庄道路
    if ((fabsf(gCarVelocity) >= 5 && fabsf(gCarVelocity) <= 12) && (x_lessTraCnt < 2) // 跟踪后无 （x > 3 && 非绝对禁止目标） 的目标存在
        //&& ( (x_lessRawCnt /(x_lessRawCnt + x_MoreRawCnt)) > lessMore) //y > 10 且在 三车道内与三车道外的点个数
        && (x_lessRawCnt > x_MoreRawCnt * 2) && (x_MoreRawCntTarget < 3) //原始点 且在±3m外的 非绝对禁止点
    )
    {
        ruralFlag = 1;
    }
    else
    {
        ruralFlag = 0;
    }
    //村庄道路计数
    if (gInRural > 0 && ruralFlag == 0)
    {
        gInRural--;
    }
    else if (gInRural < JudgeNum * 2 && ruralFlag == 1)
    {
        gInRural++;
    }

    //村庄道路标记
    if (gInRural > JudgeNum)
    {
        gInRuralFlag = 1;
    }
    else
    {
        gInRuralFlag = 0;
    }
}
#endif

/**
 * @brief 利用匀加速模型对航迹下一时刻状态进行预测
 * @param time 帧间隔
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 */
void RDP_Track_predictState(float time, trk_pkg_t *pTrkPkg, rdp_config_t* config, float EKF_A[36])
{
    uint32_t i;
    trk_t* trk;
    float halfDt2 = 0.5f*time*time;
	const stVehicleStatus* pVdy = getVdyStatus();
	float x[6];
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrkPkg->trk[i];
        if (trk->type == NONE)
            continue;
		trk->x[3] += config->speedDiff; //自车加速的车速补偿。
		//singer预测模型
		predictEkf(EKF_A, trk->x, x);

		////根据车身姿态旋转坐标系
		trk->p_x[0] = x[0] * pVdy->cosYaw - x[1] * pVdy->sinYaw;
		trk->p_x[1] = x[1] * pVdy->cosYaw + x[0] * pVdy->sinYaw;
		trk->p_x[2] = x[2];
		trk->p_x[3] = x[3];
		trk->p_x[4] = x[4];
		trk->p_x[5] = x[5];

        //trk->p_x[0] = trk->x[0] + trk->x[2]*time + trk->x[4]*halfDt2;
        //trk->p_x[1] = trk->x[1] + trk->x[3]*time + trk->x[5]*halfDt2;
        //trk->p_x[2] = trk->x[2] + trk->x[4]*time;
        //trk->x[3] += config->speedDiff;
        //trk->p_x[3] = trk->x[3] + trk->x[5]*time;
        //trk->p_x[4] = trk->x[4];
        //trk->p_x[5] = trk->x[5];
        trk->pre_z[0] = trk->sim_z[0];
        trk->pre_z[1] = sqrtf(trk->p_x[0]*trk->p_x[0]+trk->p_x[1]*trk->p_x[1]);
        if(trk->pre_z[1] < 0.1f)
            trk->pre_z[1] = 0.1f;
        trk->pre_z[2] = (trk->p_x[0]*trk->p_x[2] + trk->p_x[1]*trk->p_x[3])/trk->pre_z[1];
        trk->pre_z[3] = atan2f(trk->p_x[0], trk->p_x[1]) *RAD2ANG;
    }
}

/*
 * function name: RDP_Track_associateCdi
 * auther: Zhang Jian
 * data: 2022-2-9
 */
#define ACTIVE_ASSOCIATION_THR_X        0.5f //m
#define ACTIVE_ASSOCIATION_THR_Y        0.7f //m
#define ACTIVE_ASSOCIATION_THR_R        0.7f //m
#define ACTIVE_ASSOCIATION_THR_V_MAX    8.0f // m/s
#define ACTIVE_ASSOCIATION_THR_V_MIN    2.5f // m/s

/**
 * @brief NN准则关联
 * @param pCdiPkg RDP内部用于跟踪的原始点列表全局变量指针 
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 */
void RDP_Track_associateCdi(cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_pkg_t *pTrkPkg, float time)
{
    u32 i, j;
    float sinPreAngle, cosPreAngle;
    float dRange, dRadialVel, dRadialVel2, dAngle, dX, dY;
    float dRangeThre, dRadialVelThre, dAngleThre = 0.f;
    float xThre, yThre, xThreMax = 0.f;
    float score, scoreCoeff;
    uint32_t scoreU32, bestIdx;
    uint16_t scoreU16, bestScore;
    trk_t* trkCur;
    cdi_t* cdi = pCdiPkg->cdi;//进入函数内部后，结构体命名dataIn
	rdp_config_t* config = RDP_getTrackConfigPointer();
	uint8_t cdiRow;
    uint8_t trkCol;
    uint8_t cdiCol;
	sideLine_pkr_t* pSideLinePkg = &pTrkPkg->sideLines;

    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trkCur = &pTrkPkg->trk[i];
        if (trkCur->type == NONE)
            continue;
        
        trkCur->idx_1 = FILTER_NON_ASSOC;
        trkCur->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
        bestIdx = POINT_ID_INVALID;
        bestScore = UINT16_MAX;
        sinPreAngle = fabsf(trkCur->p_x[0])/trkCur->pre_z[1];
        cosPreAngle = fabsf(trkCur->p_x[1])/trkCur->pre_z[1];
		dAngleThre = 4.f;

        if (trkCur->type == TRACK)
        {
            if(trkCur->status&TRACK_STATUS_MOVED_BMP)
            {
                if(trkCur->p_x[0] > 0 && trkCur->p_x[0] < 6 && fabsf(trkCur->p_x[1]) < 10)
                {
                    dRangeThre = 1.0f;
                    xThre = 1.0f;
                    yThre = 2.5f;
                }
                else if(pRDP_inVehicleData->vdyDriveDirection)
                {
                    dRangeThre = ACTIVE_ASSOCIATION_THR_R;
                    if(trkCur->activeTrkCnt < 15)       //动态航迹建立初期，位置估计偏差较大
                    {
                        dRangeThre += (float)(15 - trkCur->activeTrkCnt) * 0.1f;
                    }
                    xThre = ACTIVE_ASSOCIATION_THR_Y;
                    yThre = ACTIVE_ASSOCIATION_THR_X;
                }
                else if (fabsf(pRDP_inVehicleData->vdyCurveRadius) < 500.f)
                {
                    dRangeThre = 2.0f;
                    xThre = 1.5f;
                    yThre = 1.0f;
                }
                else
                {
                    dRangeThre = ACTIVE_ASSOCIATION_THR_R;
                    if(trkCur->activeTrkCnt < 15)       //动态航迹建立初期，位置估计偏差较大
                    {
                        dRangeThre += (float)(15 - trkCur->activeTrkCnt) * 0.1f;
                    }
                    xThre = ACTIVE_ASSOCIATION_THR_X;
                    yThre = ACTIVE_ASSOCIATION_THR_Y;
                }
            }
            else if(pRDP_inVehicleData->vdyDriveDirection)
            {
                dRangeThre = ACTIVE_ASSOCIATION_THR_R;
                xThre = ACTIVE_ASSOCIATION_THR_Y*0.6f;
                yThre = ACTIVE_ASSOCIATION_THR_X*0.6f;
            }else
            {
                dRangeThre = ACTIVE_ASSOCIATION_THR_R*0.6f;
                xThre = ACTIVE_ASSOCIATION_THR_X*0.6f;
                yThre = ACTIVE_ASSOCIATION_THR_Y*0.6f;
            }
        }
        else
        {
            if(pRDP_inVehicleData->vdyDriveDirection)
            {
                xThre = maxVx*time;
                yThre = (0.5f*maxVy)*time;
            }
            else
            {
                // 当前帧周期偏大，自车静止目标低速时横纵向距离门限计算偏大，容易误关联
                if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
                    && !ANGLE_IN_VERTICAL_AREA(trkCur->sim_z[3], 10.f)
                    && !ANGLE_IN_CROSS_AREA(trkCur->sim_z[3], 10.f)
                    && fabsf(trkCur->stored_last_z[0][2]) > 0.f  && fabsf(trkCur->stored_last_z[0][2]) < 1.f)
                {
                    // 对于径向速度小于1m/s的检测点按1m/s计算横纵向最大速度用于设置横纵向距离门限
                    xThre = fabsf(1.f / sinf(trkCur->stored_last_z[0][3] * PI / 180.f) * time * 1.5f);
                    yThre = fabsf(1.f / cosf(trkCur->stored_last_z[0][3] * PI / 180.f) * time * 1.5f);
                    dRangeThre = (xThre * sinPreAngle + yThre * cosPreAngle);
                }
                else
                {
                    xThre = (1.0f / 3.0f * maxVx) * time;
                    yThre = maxVy * time;
                    yThre = (yThre > 3.f) ? 3.f : yThre;
                }
            }
            dRangeThre = (xThre*sinPreAngle + yThre*cosPreAngle);
        }

        if(trkCur->activeTrkCnt > 25 && trkCur->miss >= 2 && trkCur->status & TRACK_STATUS_MOVING_BMP && trkCur->x[1] > 1.0f)
        {
            xThre = 1.0f;
            yThre = 2.5f;
            dRangeThre = (xThre * sinPreAngle + yThre * cosPreAngle);
        }
        if(pRDP_inVehicleData->vdyDriveDirection)
        {
            dRadialVelThre = ACTIVE_ASSOCIATION_THR_V_MAX*cosPreAngle;
        }
        else
        {
            dRadialVelThre = ACTIVE_ASSOCIATION_THR_V_MAX*sinPreAngle;
        }
        if(dRadialVelThre < ACTIVE_ASSOCIATION_THR_V_MIN)
        {
            dRadialVelThre = ACTIVE_ASSOCIATION_THR_V_MIN;
        }

        if (fabsf(trkCur->x[3] - pRDP_inVehicleData->vdySpeedInmps) > (50 / 3.6f))
        {
            xThre = 1.0f;
            yThre = 2.5f;
        }
		else if (fabsf(trkCur->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 5.f && trkCur->x[1] > 10.0f)
		{
            if (fabsf(trkCur->x[3]) < 5.f)
            {
                // 相对速度较小的目标纵向距离关联门限设置相对小
                yThre = 1.5f;
            }
            else
            {
                yThre = 2.5f;
            }
		}
		else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 50.f / 3.6f
			&& trkCur->status & TRACK_STATUS_MOVING_BMP && trkCur->type == TRACK && trkCur->activeTrkCnt < 15.f
			&& trkCur->x[0] > 0.9f && trkCur->x[1] < 5.f)
		{
			// 自车车速较高，速度盲区新起批的运动目标放大纵向距离关联门限
			yThre = 2.0f;
		}
        else
        {
            ;///
		}

		if ((trkCur->status & TRACK_STATUS_CROSSING_BMP && trkCur->crossAllCnt > 10) || (trkCur->status & TRACK_STATUS_ABS_CROSSING_BMP))
		{
            if (trkCur->objType <= 2 || fabsf(trkCur->x[2]) < (10 / 3.6f))
            {
                xThre = 1.5f;
                yThre = 1.0f;
            }
            else
            {
                xThre = 2.0f;
                yThre = 1.5f;
            }
			//if (dRadialVelThre < 5.0f)
			//	dRadialVelThre = 5.0f;
		}
		else if (fabsf(trkCur->x[2]) > 6.f)
		{
			xThre = 3.0f;
		}
		else if (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
			&& trkCur->type == TRACK
			&& trkCur->status & TRACK_STATUS_MOVING_BMP
			&& trkCur->objType == 4
			&& IN_BSD_AREA(trkCur->x[0], trkCur->x[1]))
		{
			xThre = 2.f;	// 90°附近检测点规律由外侧到内测（大车更明显），放大横向距离门限
		}
		else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < (15 / 3.6f) && trkCur->type == TRACK && trkCur->status & TRACK_STATUS_MOVING_BMP && trkCur->x[0] > LANE_WIDTH * 2 && trkCur->sim_z[1] > 10.f)
		{
			// F/RCTAB低速行人横穿，目标转动态初期速度更新不准导致的关联失败
			xThre = 1.5f;
		}
		else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 20.f / 3.6
			&& trkCur->x[0] > 5.f
			&& trkCur->x[2] < -0.5f
			&& fabsf(trkCur->x[3] - pRDP_inVehicleData->vdySpeedInmps) < 2.f)
		{
			xThre = 1.5f;
		}

		// 斜穿目标放大横纵向距离门限
		if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
			&& trkCur->type == TRACK
			&& trkCur->status & TRACK_STATUS_MOVING_BMP
			&& trkCur->activeTrkCnt > 50
			&& trkCur->x[2] < -3.f && trkCur->x[3] < -3.f)
		{
			xThre = 1.5f;
			yThre = 2.5f;
		}
		// 调整大转弯场景近处静止目标的关联门限
		if (isCPTALFScene && !(trkCur->status & TRACK_STATUS_MOVING_BMP) && trkCur->sim_z[1] < 15.f && trkCur->sim_z[3] > 45.f)
		{
			xThre = 1.5f;
			yThre = 1.5f;
			dAngleThre = 20.f;
		}

		// 分辨率模式下调整横向距离关联门限，防止因自身点迹不稳被拉扯切ID
		if (RDP_getTrackConfigPointer()->radarResolutionTestMode == TEST_MODE_RESOLUTION
			&& fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
			&& trkCur->type == TRACK
			&& trkCur->status & TRACK_STATUS_MOVING_BMP
			&& !(trkCur->status & TRACK_STATUS_CROSSING_BMP)
			&& trkCur->x[3] < -1.f)
		{
			xThre = 1.f;
		}

        xThreMax = (trkCur->status & TRACK_STATUS_MOVING_BMP) ? 2.f : 0.5f;
		dRangeThre = (xThre * sinPreAngle + yThre * cosPreAngle);

		if (trkCur->miss == 0 && trkCur->type == TRACK && trkCur->sim_z[1] > 40.0f)
		{
			dAngleThre = 2.0f;
		}

        for(j = 0; j < pCdiPkg->number; j++) 
        {
			if ((trkCur->type == TRACK && !(trkCur->status & TRACK_STATUS_MOVING_BMP) && cdi[j].status & POINT_STATUS_ABNORMAL_BMP && !isCPTALFScene) \
				|| ((trkCur->status & TRACK_STATUS_MOVING_BMP) && (cdi[j].status & POINT_STATUS_GUARDRAIL_BMP) && !(trkCur->status & TRACK_STATUS_CROSSING_BMP)))
			{
				continue;
			}

            if (cdi[j].valid == 0)
            {
                continue;
			}
			
			if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (5 / 3.6f) && trkCur->type == TRACK && trkCur->status & TRACK_STATUS_MOVING_BMP
                && cdi[j].status & POINT_STATUS_ABNORMAL_BMP  && cdi[j].mea_z[1] < 10.f && cdi[j].mea_z[0] < 40.f)
            {
                continue;
            }

			//跟踪点在异侧边缘的时候，不与40点关联
			if ((trkCur->sim_z[3] - config->installAngle) < FOVEDGE_MAX_THRE \
				&& (cdi[j].status & POINT_STATUS_ABNORMAL_BMP))
			{
				continue;
			}

			float tempSideLines_trkCur = getSideLineDis(trkCur->x[1], pSideLinePkg);
			float tempSideLines_cdi = getSideLineDis(cdi[j].y, pSideLinePkg);
			// 较远目标不关联护栏外的点
			if (!(trkCur->status & TRACK_STATUS_CROSSING_BMP)
				&& trkCur->x[1] > 50.f && trkCur->sim_z[2] < -5.f
				&& pSideLinePkg->strongSildLineValid
				&& (trkCur->x[0] - tempSideLines_trkCur) * (cdi[j].x - tempSideLines_cdi) < 0.f
				&&  cdi[j].x - trkCur->x[0] > 0.5f)
			{
				continue;
			}

			scoreCoeff = 1.0f;
            dRange = fabsf(cdi[j].mea_z[1] - trkCur->pre_z[1]);
            dRadialVel = fabsf(cdi[j].mea_z[2] - trkCur->pre_z[2]);
            dRadialVel2 = fabsf(cdi[j].mea_z[2] - trkCur->stored_last_z[0][2]);
            if(dRadialVel2 < dRadialVel)
                dRadialVel = dRadialVel2;
            dAngle = fabsf(cdi[j].mea_z[3] - trkCur->pre_z[3])*DEG2RAD;
            DELTA_RAD_NORM(dAngle);
            dX = fabsf(cdi[j].x - trkCur->p_x[0]);
			if (dX > xThre && fabsf(cdi[j].x - trkCur->stored_last_outputX[0]) < dX)
			{
				dX = fabsf(cdi[j].x - trkCur->stored_last_outputX[0]);
			}
			if (dX > xThre && fabsf(cdi[j].x - trkCur->stored_last_16trk_outputXY[0]) < dX && fabsf(trkCur->stored_last_16trk_outputXY[0]) > 0.01f \
				&& trkCur->status &TRACK_STATUS_MOVING_BMP)
			{
				dX = fabsf(cdi[j].x - trkCur->stored_last_16trk_outputXY[0]);
			}
            dY = fabsf(cdi[j].y - trkCur->p_x[1]);
            if (dRadialVel < dRadialVelThre
                && ((dRange < dRangeThre && dX < xThreMax && (dAngle < (dAngleThre*DEG2RAD) || dAngle * trkCur->pre_z[1] < 1.0f))
                    || (dX < xThre && dY < yThre)
                    || (dX < xThre * 0.6f && dY < yThre * 1.5f)
                    || (dX < xThre * 1.5f && dY < yThre * 0.6f)))
            {
                if(dRadialVel > 2.0f
                    && ((trkCur->status & TRACK_STATUS_MOVING_BMP
                            && trkCur->pre_z[1] > 3
                            && !(cdi[j].status & POINT_STATUS_DYNAMIC_KEEP_BMP))
                        || (!(trkCur->status & TRACK_STATUS_MOVING_BMP)
                            && trkCur->pre_z[1] > 3
                            && cdi[j].status & POINT_STATUS_DYNAMIC_CERTAIN_BMP)))
                {
					continue;       //超过最低速度差门限时，明显运动目标不与绝对静止点关联，明显静止目标不与明显运动点关联
                }
				//有一定横向速度的目标，在门限超过阈值的时候，不向后关联
				if (fabsf(cdi[j].x - trkCur->stored_last_outputX[0]) > 1.f \
					&& (cdi[j].x - trkCur->stored_last_outputX[0]) * trkCur->x[2] < 0.f 
					&& fabsf(trkCur->x[2]) > 2.f)
				{
					continue;
				}

                // 超过最低速度差门限时，若前后帧点迹速度反向
                if (trkCur->status & TRACK_STATUS_MOVING_BMP
                    && !(trkCur->status & TRACK_STATUS_CROSSING_BMP)
                    && dRadialVel > 2.0f
                    && cdi[j].mea_z[2] * trkCur->stored_last_z[0][2] < 0.f
                    && (cdi[j].status & POINT_STATUS_DEBLUR_FAILED || cdi[j].status & POINT_STATUS_ABNORMAL_BMP))
                {
                    continue;
                }
                if (trkCur->status & TRACK_STATUS_MOVING_BMP
					&& pSideLinePkg->strongSildLineValid
					&& fabsf(trkCur->x[0] - tempSideLines_trkCur) > 0.5f
					&& fabsf(trkCur->x[0] - tempSideLines_trkCur) < 2.5f	// 限制护栏最大关联距离，防止误判护栏后护栏外的真实运动无法被关联
					&& !(trkCur->status & TRACK_STATUS_GUARDRAIL_BMP)
					&& !(trkCur->status & TRACK_STATUS_GUARDRAIL_OUTSIDE_BMP)
					&& (cdi[j].status & POINT_STATUS_GUARDRAIL_BMP || cdi[j].status & POINT_STATUS_GUARDRAIL_OUTSIDE_BMP))
                {
                    continue;   // 限制与护栏关联
                }

                // 抑制保杠假点关联1---横向0.5m内的相对0速检测点
				// TODO：测试下稳定跟车场景是否会丢目标
				if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 12.f /3.6
					&& trkCur->status & TRACK_STATUS_MOVING_BMP
					&& trkCur->type == TRACK
					&& IN_BSD_AREA(trkCur->x[0], trkCur->x[1])
					&& trkCur->x[0] > 0.5f
					&& cdi[j].x < 0.5f && fabsf(cdi[j].mea_z[2]) < 0.01f)
				{
					continue;   // 横向距离过小的0速检测点多为保杠假点
				}

                // 抑制保杠假点关联2---横向1m内SNR低于60的相对0速的0x40标识检测点
                // 以改善误关联保杠假点导致的滞留问题，自车静止/运动场景均有发现
                if (trkCur->status & TRACK_STATUS_MOVING_BMP
                    && cdi[j].status & POINT_STATUS_ABNORMAL_BMP
                    && trkCur->type == TRACK
                    && IN_BSD_AREA(trkCur->x[0], trkCur->x[1])
                    && trkCur->x[0] > 1.f
                    && cdi[j].x < 1.f && fabsf(cdi[j].mea_z[2]) < 0.01f && cdi[j].mea_z[0] < 50.f)
                {
                    continue;
                }

                // 抑制保杠假点关联3---横向7.5m内RCS低于-20且相对0速的0x40标识检测点
                // 个别车型保杠假点的横向距离较大，超车/被超车目标容易误关联
                if (trkCur->status & TRACK_STATUS_MOVING_BMP
                    && trkCur->type == TRACK
                    && cdi[j].status & POINT_STATUS_ABNORMAL_BMP
                    && fabsf(cdi[j].mea_z[2]) < 0.01f
                    && cdi[j].x < 7.5f && cdi[j].y < 5.f
                    && cdi[j].mea_z[0] < 50.f && cdi[j].rcs < (-20 * 10))
                {
                    continue;
                }

				// DOW场景FOV边界目标无检测点，误关联到保杠静止假点，导致滞留晚退
				if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 5.f / 3.6
					&& trkCur->status & TRACK_STATUS_MOVING_BMP && trkCur->type == TRACK
					&& trkCur->activeTrkCnt > 20
					&& trkCur->x[0] > 0.75f && cdi[j].x < 0.75f && trkCur->x[1] < 1.f
					&& trkCur->x[1] < cdi[j].y && fabsf(cdi[j].mea_z[2]) < 0.01f)
				{
					continue;
				}

                // 邻车道被超车目标FOV边界即将消失时，易误关联到横向较远处的静止点或谐波假点
                // 抑制跨车道关联
                if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 5.f / 3.6
                    && trkCur->status & TRACK_STATUS_MOVING_BMP
                    && !(trkCur->status & TRACK_STATUS_CROSSING_BMP)
                    && trkCur->type == TRACK && trkCur->activeTrkCnt > 12
                    && IN_NEIGHBOUR_LANE(trkCur->x[0]) && trkCur->x[1] < 0.f
                    && cdi[j].x > (0.9f + 3.75f))
                {
                    continue;
                }

                // 自车低速，目标低速---对向目标或被超车目标抑制反向较远关联
                if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 10.f / 3.6
                    && trkCur->status & TRACK_STATUS_MOVING_BMP
                    && !(trkCur->status & TRACK_STATUS_CROSSING_BMP)
                    && trkCur->type == TRACK && trkCur->activeTrkCnt > 12
                    && trkCur->x[0] > 0.f && trkCur->x[0] < 5.f && trkCur->x[1] < 0.f
                    && (dY > 1.5f || (cdi[j].status & POINT_STATUS_ABNORMAL_BMP && fabsf(cdi[j].mea_z[2]) < 0.01f))
                    && (cdi[j].y - trkCur->x[1]) * trkCur->x[3] < 0.f)
                {
                    continue;
                }

                // 自车低速场景，横穿目标横穿至自车正前方，抑制反向较远关联
                // 若直接抑制反向关联，容易造成车头跟踪点外推，从而报警早退---该类情况正常关联，在滤波处理中采用维持
                if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 10.f / 3.6
                    && trkCur->status & TRACK_STATUS_MOVING_BMP
                    && trkCur->status & TRACK_STATUS_CROSSING_BMP
                    && trkCur->type == TRACK && trkCur->activeTrkCnt > 12
                    && (trkCur->x[0] < 0.9f || ANGLE_IN_VERTICAL_AREA(trkCur->sim_z[3], 10))
                    && trkCur->x[1] > 0.5f && trkCur->x[2] < (-5.f / 3.6)
                    && dX > 1.f && (cdi[j].x - trkCur->x[0]) * trkCur->x[2] < 0.f)
                {
                    continue;
                }

#if CUSTOMER_PROTOCOL == CUSTOMER_PROTOCOL_BYD_16
				// 130pro高阶保杠特性不同于180pro，考虑区分项目或车型
				// BSD场景FOV边界目标无检测点，误关联到保杠静止假点，导致滞留
				if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 5.f / 3.6	// 临时设置：130pro高阶BSD启用最低车速是？
					&& trkCur->status & TRACK_STATUS_MOVING_BMP && trkCur->type == TRACK
					&& trkCur->activeTrkCnt > 50
					&& trkCur->x[0] > 0.75f && trkCur->x[1] < 0.f
					&& trkCur->x[1] < cdi[j].y && fabsf(cdi[j].mea_z[2]) < 0.01f
					&& cdi[j].mea_z[0] < 60.f)
				{
					continue;
				}
#endif
				// 针对走查后车遮挡DOW场景，不与绝对静止点关联
				if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
					&& trkCur->status & TRACK_STATUS_MOVING_BMP && trkCur->type == TRACK
					&& IN_NEIGHBOUR_LANE(trkCur->x[0]) && trkCur->x[3] < (float)(-10 / 3.6f)
					&& fabsf(trkCur->x[0] - cdi[j].x) > 0.5f
					&& cdi[j].x < 0.9f
					&& fabsf(cdi[j].mea_z[2]) < 0.1f)
				{
					continue;
				}

                // 防止静止目标误关联护栏测错角的点
                if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (10 / 3.6f)
                    && pSideLinePkg->strongSildLineValid
                    && !(trkCur->status & TRACK_STATUS_MOVING_BMP)
                    && cdi[j].status & POINT_STATUS_ABNORMAL_BMP
                    && cdi[j].y < 15.f
                    && (fabsf(cdi[j].otgVel) > 0.75f || cdi[j].status & POINT_STATUS_DYNAMIC_CERTAIN_BMP)
                    && fabsf(pRDP_inVehicleData->vdySpeedInmps * cosf(trkCur->stored_last_z[0][3] / 180 * M_PI) - trkCur->stored_last_z[0][2]) < 0.3f)
                {
                    continue;
                }

                if (cdi[j].status & POINT_STATUS_DEBLUR_FAILED && fabsf(trkCur->sim_z[2]) > 22.f)
                {
                    scoreCoeff = 10.f;
                }
                if (pSideLinePkg->strongSildLineValid && tempSideLines_cdi - cdi[j].x < 1.5f && !(trkCur->status & TRACK_STATUS_CROSSING_BMP) && trkCur->status & TRACK_STATUS_MOVING_BMP && !(trkCur->status & TRACK_STATUS_GUARDRAIL_BMP))
                {
                    // 护栏边目标检测点横向有波动，选择远离护栏的点
                    scoreCoeff = (tempSideLines_cdi > 0.9f && tempSideLines_cdi - cdi[j].x > 0.5f) ? 1.f : 10.f;
                }
				if (trkCur->sim_z[1] > 10.f && fabsf(trkCur->sim_z[2]) < 2.5f
					&& fabsf(trkCur->sim_z[2] - cdi[j].mea_z[2]) > fabsf(trkCur->sim_z[2]) * 0.5f
					&& fabsf(trkCur->stored_last_z[0][2] - cdi[j].mea_z[2]) > fabsf(trkCur->sim_z[2]) * 0.5f)
				{
					// 低速目标限制速度差异较大的点迹关联
					// TODO：是否会导致非最近关联点
					scoreCoeff = 2.f;
				}
				if (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
					&& trkCur->status & TRACK_STATUS_MOVING_BMP
					&& !(trkCur->status & TRACK_STATUS_CROSSING_BMP)
					&& trkCur->type == TRACK
					&& (trkCur->objType == 4 || trkCur->objType == 3)
					&& IN_BSD_AREA(trkCur->x[0], trkCur->x[1])
					&& cdi[j].x > 0.9f
					&& fabsf(trkCur->headingAngle) > 160.f)
				{
					if (fabsf(cdi[j].mea_z[2]) < 0.01f)
					{
						// 不关联0速点（以抑制关联到保杠假点）
						scoreCoeff = 4 / 0.1f;
						//continue;	// 是否考虑直接不关联
					}
					else
					{
						scoreCoeff = cdi[j].x / 0.1f;
						if (cdi[j].x < trkCur->stored_last_16trk_outputXY[0] && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 20 / 3.6f)
						{
							dX *= 0.2;
							scoreCoeff /= 2;
						}
						//scoreCoeff = 0.3f;
					}
				}
				
                if (IN_GRIDMAP_AREA(cdi[i].x, cdi[j].y) && IN_GRIDMAP_AREA(trkCur->x[0], trkCur->x[1]))
                {
                    cdiRow = GET_GRID_ROW(cdi[j].y);
                    trkCol = GET_GRID_COL(trkCur->x[0]);
                    cdiCol = GET_GRID_COL(cdi[j].x);

                    // 抑制两车道的目标误关联（缓行场景下容易发生）
                    if ((fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
                        || (fabsf(pRDP_inVehicleData->vdySpeedInmps) < (10 / 3.6f) && fabsf(pRDP_inVehicleData->vdyYawRate) < 0.1f))
                        && trkCur->lngMovingStatus > 0)
                    {
                        /*
                        * 1、优先关联横向上距离所在车道中轴较近的点：1）两侧相邻车道均有目标；2）横向运动方向存在目标。
                        * 2、抑制跨车道关联。
                        */
                        if (!(trkCur->lngMovingStatus ^ (GRIDMAP_NEIGHBOUR_MINUS | GRIDMAP_NEIGHBOUR_PLUS))
                            || (trkCur->lngMovingStatus & GRIDMAP_NEIGHBOUR_MINUS && trkCur->x[2] < 0.f)
                            || (trkCur->lngMovingStatus & GRIDMAP_NEIGHBOUR_PLUS && trkCur->x[2] > 0.f))
                        {
                            dX = fabsf(cdi[j].x - (0.9f + (trkCol - 1) * (GRID_LAT * 0.5f)));
                        }
                        if ((!(trkCur->lngMovingStatus ^ (GRIDMAP_NEIGHBOUR_MINUS | GRIDMAP_NEIGHBOUR_PLUS)) && trkCol != cdiCol)
                            || (trkCur->lngMovingStatus & GRIDMAP_NEIGHBOUR_MINUS && (trkCol - cdiCol) == 1)
                            || (trkCur->lngMovingStatus & GRIDMAP_NEIGHBOUR_PLUS && (cdiCol - trkCol) == 1))
                        {
                            continue;
                        }
                    }

                    // 自车低速场景下，正后方近处目标限制跨车道关联
                    if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < (10 / 3.6f)
                        && trkCur->status & TRACK_STATUS_MOVED_BMP
                        && IN_HONE_LANE(trkCur->x[0])
                        && trkCur->x[1] < 5.f && fabsf(trkCur->x[2]) < 0.75f
                        && cdiCol > trkCol)
                    {
                        continue;
                    }

                    //// 位于gridMap处理区域外的目标（如异侧邻车道目标）也做碰撞策略，若关联点所在grid存在其它目标则抑制关联
                    //// 作用区域外的目标不能使用cdiCol和trkCol判断，计算异常
                    //if (trkCur->type == TRACK && trkCur->status & TRACK_STATUS_MOVING_BMP && !(trkCur->status & TRACK_STATUS_CROSSING_BMP)
                    //    && cdi[j].x > (-0.9f - 2.f) && trkCur->x[0] < (-0.9f - 2.f)
                    //    && IN_GRIDMAP_LNG_AREA(trkCur->x[1]) && IN_GRIDMAP_LNG_AREA(cdi[j].y)
                    //    && (1 == gGridMap[cdiRow][cdiCol]
                    //        || (cdiRow - 1 >= 0 && 1 == gGridMap[cdiRow - 1][cdiCol])
                    //        || (cdiRow + 1 <= 3 && 1 == gGridMap[cdiRow - 1][cdiCol])))
                    //{
                    //    continue;
                    //}
                }
				//在BSD区域的CANDI点优先向后关联 
				if (trkCur->type == CANDI && IN_BSD_AREA(trkCur->x[0], trkCur->x[1]) \
                    && (trkCur->x[1] - cdi[j].y) > 0.f \
                    && (config->installPosition == 4 || config->installPosition == 5))
				{
					scoreCoeff = scoreCoeff + (trkCur->x[1] - cdi[j].y) / 0.2f + 5.f;
				}

				if (trkCur->status & TRACK_STATUS_CROSSING_BMP || isCPTALFScene)
				{
					dX *= 0.5f;
					dY *= 2.0f;
				}
				else
				{
					dY *= 0.5f;
				}
                
                /* calculate cost */
                //兼容近处原始点速度异常问题
                if (trkCur->x[0] > 0.9f && trkCur->x[0] < 0.9f + LANE_WIDTH && trkCur->x[1] < 10.f 
                    && fabsf(trkCur->x[3]) < 2.f && fabsf(trkCur->x[2]) < 1.5f
                    && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 1.5f
                    && !(trkCur->status & TRACK_STATUS_CROSSING_BMP)
                    && trkCur->status & TRACK_STATUS_MOVING_BMP)
                {
                    score = dX*dX + dY*dY + dRadialVel * dRadialVel;
                }
                else
                {
                    score = dX*dX + dY*dY;
                }
                if(score < 12.f)
                {
					score = 0.7f*sqrtf(score);
					if (((trkCur->pre_z[1] > 5 || fabsf(trkCur->pre_z[2]) < 4) && (pRDP_inVehicleData->vdyDriveDirection || ANGLE_IN_VERTICAL_AREA(trkCur->pre_z[3], 25))) || (trkCur->status & TRACK_STATUS_GUARDRAIL_BMP))
					{
						if (trkCur->pre_z[1] > 18 || pRDP_inVehicleData->vdyDriveDirection || (trkCur->pre_z[2] <= 0 && trkCur->pre_z[2] > -4))
							score += 0.3*dRadialVel;
					}
                    score *= scoreCoeff;
                    scoreU32 = (uint32_t)(score*1000 + 0.5f);
                    if(scoreU32 > UINT16_MAX)
                        scoreU16 = UINT16_MAX;
                    else
                        scoreU16 = (uint16_t)scoreU32;
                    if(scoreU16 < bestScore)
                    {
                        bestScore = scoreU16;
                        bestIdx = j;
                    }
                }
            }
        }
    
        if(bestIdx != POINT_ID_INVALID)
        {
            trkCur->idx_1 = bestIdx;
            trkCur->bestScore = bestScore;
            trkCur->status |= TRACK_STATUS_ASSOCIATED_BMP;
            cdi[bestIdx].status |= POINT_STATUS_ASSOCIATED_BMP;
            /*
            if(cdi[bestIdx].numBestUnit < ASSOCIATED_TRACKS_NUM_MAX)
            {
                cdi[bestIdx].bestUnit[pointOtherInfo[bestIdx].numBestUnit] = unitId;
                cdi[bestIdx].numBestUnit++;
                trkCur->bestScore = bestScore;
            }
            */
            
            //一个原始的最大支持3个跟踪点匹配的方式,不进行选点操作，直接使用
            if (cdi[bestIdx].index == FILTER_NON_ASSOC)
            {
                cdi[bestIdx].index = i;
            }
            else if (cdi[bestIdx].index2 == FILTER_NON_ASSOC)
            {
                cdi[bestIdx].index2 = i;
            }
            else if (cdi[bestIdx].index3 == FILTER_NON_ASSOC)
            {
                cdi[bestIdx].index3 = i;
            }
            else
            {
                /* unable to associate to this point */
                trkCur->idx_1 = FILTER_NON_ASSOC;
                trkCur->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
            }
        }else
        {
            trkCur->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
        }
    }
}

/*
    function: Judge whether two scores differ greatly 
    author: Pan Yunfei
    data: 2023-3-24
*/
uint16_t scoreDisparityCheck(u16 score1, u16 score2)		
{
	if(score1 < score2)
	{
		if(score2 - score1 >= SCORE_DELTA_THR && (float)score2 / ((float)score1 + 0.01f) >= SCORE_RATE_THR)
		{
			return 1;
		}
	}
	else
	{
		if(score1 - score2 >= SCORE_DELTA_THR && (float)score1 / ((float)score2 + 0.01f) >= SCORE_RATE_THR)
		{
			return 2;
		}
	}
	return 0;
}
/**
 * @brief 匈牙利匹配
 * @param pCdiPkg RDP内部用于跟踪的原始点列表全局变量指针 
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 */
void RDP_Track_getBestAssociation(cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_pkg_t *pTrkPkg)
{
    u32 j;
    cdi_t* cdi = pCdiPkg->cdi;
    uint16_t numBestUnit, numStableTrack, oldestUid, uid;
    uint16_t bestUnit[4];
    uint16_t stableTrackId[4];
    uint16_t oldestLifeTime, idx, bestScore, scoreCheckRes;
    trk_t *oldestUnitInst,*unitInst, *stableTrackInst[4];
    for(j = 0; j < pCdiPkg->number; j++) 
    {
        if (cdi->valid == 0)
        {
            continue;
        }
        numBestUnit = 0;
        if(cdi[j].index != FILTER_NON_ASSOC)
        {
            bestUnit[numBestUnit] = cdi[j].index;
            numBestUnit++;
        }
        if(cdi[j].index2 != FILTER_NON_ASSOC)
        {
            bestUnit[numBestUnit] = cdi[j].index2;
            numBestUnit++;
        }
        if(cdi[j].index3 != FILTER_NON_ASSOC)
        {
            bestUnit[numBestUnit] = cdi[j].index3;
            numBestUnit++;
        }
        if(numBestUnit > 0)
        {
            oldestUid = bestUnit[0];
            oldestUnitInst = &pTrkPkg->trk[oldestUid];
            oldestLifeTime = oldestUnitInst->trkCnt;
            numStableTrack = 0;
            for(idx = 1; idx < numBestUnit; idx++)
            {
                uid = bestUnit[idx];
                unitInst = &pTrkPkg->trk[uid];
				scoreCheckRes = 0;
				if(oldestUnitInst->type == TRACK && unitInst->type == TRACK)
				{
					if ((oldestUnitInst->status & TRACK_STATUS_MOVING_BMP) && !(unitInst->status & TRACK_STATUS_MOVING_BMP))
					{
						scoreCheckRes = 1;
					}
					else if (!(oldestUnitInst->status & TRACK_STATUS_MOVING_BMP) && (unitInst->status & TRACK_STATUS_MOVING_BMP))
					{
						scoreCheckRes = 2;
					}
					else
					{
						scoreCheckRes = scoreDisparityCheck(oldestUnitInst->bestScore, unitInst->bestScore);
					}
				}
				else if (oldestUnitInst->type == TRACK && unitInst->type == CANDI)
				{
					unitInst->idx_1 = POINT_ID_CLUSTERED;
					unitInst->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
					cdi[j].index = oldestUid;
					continue;
				}
				else if (oldestUnitInst->type == CANDI && unitInst->type == TRACK)
				{
					oldestUnitInst->idx_1 = POINT_ID_INVALID;
					oldestUnitInst->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
					cdi[j].index = uid;
					oldestUid = uid;
					continue;
				}
				if(scoreCheckRes == 1)
				{
				    unitInst->idx_1 = POINT_ID_INVALID;
                    unitInst->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
				}
				else if(scoreCheckRes == 2)
				{
					oldestUnitInst->idx_1 = POINT_ID_INVALID;
                    oldestUnitInst->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
                    oldestLifeTime = unitInst->trkCnt;
                    oldestUnitInst = unitInst;
                    oldestUid = uid;
				}
                else if(unitInst->trkCnt > oldestLifeTime || (unitInst->trkCnt == oldestLifeTime && unitInst->pre_z[1] < oldestUnitInst->pre_z[1]))
                {
             //       if(oldestUnitInst->trkCnt > 40 || fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f)
                    if(oldestUnitInst->trkCnt > 40 || (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && oldestUnitInst->type == TRACK))
                    {
                        stableTrackInst[numStableTrack] = oldestUnitInst;
                        stableTrackId[numStableTrack] = oldestUid;
                        numStableTrack++;
                    }else
                    {
                        oldestUnitInst->idx_1 = POINT_ID_CLUSTERED;
                        oldestUnitInst->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
                    }

                    oldestLifeTime = unitInst->trkCnt;
                    oldestUnitInst = unitInst;
                    oldestUid = uid;
                }
				else
                {
                    if(unitInst->trkCnt > 40 || (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && unitInst->type == TRACK) || unitInst->movingStatusCnt > 15)
                    {
                        stableTrackInst[numStableTrack] = unitInst;
                        stableTrackId[numStableTrack] = uid;
                        numStableTrack++;
                    }else
                    {
                        unitInst->idx_1 = POINT_ID_CLUSTERED;
                        unitInst->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
                    }
                }
            }
            if(numStableTrack > 0)
            {
                bestScore = oldestUnitInst->bestScore;
                for(idx = 0; idx < numStableTrack; idx++)
                {
                    if(stableTrackInst[idx]->bestScore < ((bestScore*7)>>3))
                    {
                        oldestUnitInst->idx_1 = POINT_ID_INVALID;
                        oldestUnitInst->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
                        bestScore = stableTrackInst[idx]->bestScore;
                        oldestUnitInst = stableTrackInst[idx];
                        oldestUid = stableTrackId[idx] ;
                    }else
                    {
                        stableTrackInst[idx]->idx_1 = POINT_ID_INVALID;
                        stableTrackInst[idx]->status &= ~TRACK_STATUS_ASSOCIATED_BMP;
                    }
                }
            }
//            bestUnit[0] = oldestUid;
//            numBestUnit = 1;
            cdi[j].index = oldestUid;

            if(oldestUnitInst->type == TRACK)
            {
                cdi[j].status |= POINT_STATUS_ACTIVE_BMP;
                if(oldestUnitInst->status & TRACK_STATUS_MOVING_BMP)
                    cdi[j].status |= POINT_STATUS_ACTIVE_MOVING_BMP;
            }
        }
    }

}

int16_t getVerticalStaticPointNum(cdi_t *cdi, u32 num, int16_t idx)
{
    int16_t staticNum = 0;
    int16_t i;
    float xmin = cdi[idx].x - 0.7f;
    float xmax = cdi[idx].x + 0.7f;
    float ymin = cdi[idx].y + 1.0f;
    float ymax = cdi[idx].y + 25.0f;
    for(i = 0; i < num; i++)
    {
        if(!(cdi[i].status & POINT_STATUS_DYNAMIC_BMP) && cdi[i].x > xmin && cdi[i].x < xmax && cdi[i].y > ymin && cdi[i].y < ymax)
        {
            staticNum++;
        }
    }
    return staticNum;
}

/**
*  @b Description
*  @n
*		Algorithm level cluster funtion.
*       Application calls this function to execute the cluster of detected objects
*
*  @param[in]  inst
*      Pointer to GTRACK module instance
*  @param[in]  pdetObj
*      Pointer to an array of input measurments. Each measurement has range/angle/radial velocity information
*  @param[in]  detNum
*      point number
*
*  \ingroup GTRACK_ALG_EXTERNAL_FUNCTION
*
*  @retval
*      number of the group
*  @author:  Zhang Jian
*/
#define MAX_OBJ_PER_GROUP_VX_VY       36 //解径向速度中group点的最大数目
#define MAX_OBJ_PER_GROUP       24
#define MAX_OBJ_PER_GROUP2      16
#define UNAPPOINTED_GROUP_ID    0
#define INVALID_GROUP_ID        0xFF
#define CLUSTER_DELTA_X 1.0f       //m
#define CLUSTER_DELTA_Y 3.0f       //m
#define MAX_ASSOCIATED_NUM_PER_GROUP 8
#define TRACK_AREA_DELTA 0.4f   //m
#define TRACK_AREA_X 2.5f    //m
#define TRACK_AREA_Y 6.0f    //m
#define ASSOCIATED_TRACKS_NUM_MAX 8
#define GTRACK_LOCAL_AREA_DISTANCE 5.0f

/**
 * @brief 原始点与航迹聚类
 * @param pdetObj RDP内部用于跟踪的原始点全局变量指针 
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param detNum 原始点目标数
 * @param pTrack RDP内部的航迹，包含多种航迹状态的目标
 * @return uint16_t 
 */
uint16_t RDP_Track_clusterOnCdis(cdi_t *pdetObj, VDY_DynamicEstimate_t *pRDP_inVehicleData, uint32_t detNum, trk_pkg_t* pTrkPkg)
{
    trk_t* pTrack = &(pTrkPkg->trk[0]);
	sideLine_pkr_t* pSideLinePkg = &pTrkPkg->sideLines;
    uint16_t detIdx, detIdx1, detIdx2, nearestDetIdx, nearestDetIdx2, groupId, groupId2, groupNum, buffIdx, buffIdx2, buffLen, buff2Idx, buff2Len, dycertainIdx = 0;
    uint16_t maxValDetIdx, maxValDetIdx2;
    uint16_t groupBuff[MAX_OBJ_PER_GROUP], group2Buff[MAX_OBJ_PER_GROUP2], groupBuffDetIdx, groupBuff2DetIdx;
    float deltaRange, deltaAngle, deltaDoppler, deltaX, deltaY;
    float maxVal, nearestRange;
    float deltaX2Nearest, deltaY2Nearest;
    uint16_t groupStatus, group2Status;
    uint32_t groupDynamicStatus;
    uint16_t objNum, objNumValid, objNumDynamic, objNumReverse, dynamicCertainFlag;
    trk_t *unitInst, *unitInstNearest;
    // volatile uint8_t associatedId[MAX_ASSOCIATED_NUM_PER_GROUP];
    uint16_t associatedNum;
    float crossThr, verticalThr, crossBoundary, verticalBoundary, angleThr = 4.f;
    nearestDetIdx2 = 0;
    maxValDetIdx2 = 0;
    group2Status = 0;
    rdp_config_t* config = RDP_getTrackConfigPointer();

    if(pRDP_inVehicleData->vdyDriveDirection)
    {
        crossThr = 4.f;
        verticalThr = 2.f;
        crossBoundary = 3.f;
        verticalBoundary = 2.f;
    }else
    {
        crossThr = 1.2f;
        verticalThr = 2.f;
        crossBoundary = CROSS_OUT_X_LIMIT;
        verticalBoundary = 3.f;
    }

    for(detIdx = 0, groupId = groupId2 = GROUP_ID_START; detIdx < detNum&& groupId <= GROUP_ID_END; detIdx++)
    {
        if(pdetObj[detIdx].groupId != GROUP_ID_NONE)
            continue;
        associatedNum = 0;
        pdetObj[detIdx].groupId = groupId;
		pdetObj[detIdx].groupId1 = groupId;
        groupStatus = (pdetObj[detIdx].status & (POINT_STATUS_ASSOCIATED_BMP | POINT_STATUS_ACTIVE_BMP | POINT_STATUS_ACTIVE_MOVING_BMP));
        if(groupStatus&POINT_STATUS_ASSOCIATED_BMP)
        {
            // associatedId[associatedNum] = detIdx;
            associatedNum++;
        }
        groupBuff[0] = detIdx;
        //maxVal = pdetObj[detIdx].mea_z[0];
        maxVal = (pdetObj[detIdx].status & POINT_STATUS_DEBLUR_FAILED) ? 0.f : pdetObj[detIdx].mea_z[0];
        nearestRange = pdetObj[detIdx].mea_z[1];
        nearestDetIdx = maxValDetIdx = detIdx;
        for(buffIdx = 0, buffLen = 1; buffIdx < buffLen; buffIdx++)		//	每一组开始聚类
        {
            if(buffLen >= MAX_OBJ_PER_GROUP)
                break;
            groupBuffDetIdx = groupBuff[buffIdx];	// 取出该组的每一个成员
            for(detIdx1 = detIdx + 1; detIdx1 < detNum; detIdx1++)	//遍历这一帧所有原始点，寻找聚类成员
            {
                if(buffLen >= MAX_OBJ_PER_GROUP)
                    break;
                if(pdetObj[detIdx1].groupId != GROUP_ID_NONE)	
                    continue;
                deltaX2Nearest = fabsf(pdetObj[nearestDetIdx].x - pdetObj[detIdx1].x);
                deltaY2Nearest = fabsf(pdetObj[nearestDetIdx].y - pdetObj[detIdx1].y);
                if(deltaX2Nearest > 5.f || deltaY2Nearest > 8.f)
                    continue;
                deltaX = fabsf(pdetObj[groupBuffDetIdx].x - pdetObj[detIdx1].x);
                deltaY = fabsf(pdetObj[groupBuffDetIdx].y - pdetObj[detIdx1].y);
                deltaRange = fabsf(pdetObj[groupBuffDetIdx].mea_z[1] - pdetObj[detIdx1].mea_z[1]);
                deltaAngle = fabsf(pdetObj[groupBuffDetIdx].mea_z[3] - pdetObj[detIdx1].mea_z[3])*PI/180;
                deltaDoppler = fabsf(pdetObj[groupBuffDetIdx].mea_z[2] - pdetObj[detIdx1].mea_z[2]);
                DELTA_RAD_NORM(deltaAngle);

				if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
                    &&((pdetObj[groupBuffDetIdx].index >= 0 && pTrack[pdetObj[groupBuffDetIdx].index].status & TRACK_STATUS_CROSSING_BMP && fabsf(pTrack[pdetObj[groupBuffDetIdx].index].x[3]) < 1.f)
                        || (pTrack[pdetObj[detIdx1].index].status & TRACK_STATUS_CROSSING_BMP && fabsf(pTrack[pdetObj[detIdx1].index].x[3]) < 1.f)))
				{
					crossThr = 3.f;
				}
				if ((pdetObj[groupBuffDetIdx].index >= 0
						&& pTrack[pdetObj[groupBuffDetIdx].index].type == TRACK
						&& pTrack[pdetObj[groupBuffDetIdx].index].objType == 4
						&& pTrack[pdetObj[groupBuffDetIdx].index].status & TRACK_STATUS_MOVING_BMP
						&& IN_BSD_AREA(pTrack[pdetObj[groupBuffDetIdx].index].x[0], pTrack[pdetObj[groupBuffDetIdx].index].x[1]))
					|| (pdetObj[detIdx1].index >= 0
						&& pTrack[pdetObj[detIdx1].index].type == TRACK
						&& pTrack[pdetObj[detIdx1].index].objType == 4
						&& pTrack[pdetObj[detIdx1].index].status & TRACK_STATUS_MOVING_BMP
						&& IN_BSD_AREA(pTrack[pdetObj[detIdx1].index].x[0], pTrack[pdetObj[detIdx1].index].x[1])))
				{
					crossThr = 2.5f;
					verticalThr = 5.f;
				}

                if(nearestRange > TRACK_NEAR_AREA_DISTANCE && \
                    !((deltaX < CROSS_OUT_X_LIMIT && deltaY < verticalBoundary) || (deltaRange < 0.8f && deltaAngle < DEG2RAD*3)))
                {
                    //deltaDoppler = fabsf(pdetObj[groupBuffDetIdx].mea_z[2] - pdetObj[detIdx1].mea_z[2]);
                    if((deltaDoppler > 4) || (nearestRange > 5&&deltaDoppler > 2) || \
                       (nearestRange > 18.1f&&deltaDoppler > 1.2f) ||\
                       (nearestRange > 35.1f&&deltaDoppler > 1))
                        continue;
                }

                //自车非转弯场景，邻车道30m内目标，两检测点横向距离超过0.5m或其中一检测点对地径向速度超过0.5m/s，则动静不聚类
				if (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f && clusterDynamicOrNot(pdetObj, groupBuffDetIdx, detIdx1))
				{
					continue;
				}
                ////自车静止场景，明显动静检测点不聚类
                //if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && clusterDynamicOrNot2(pdetObj, groupBuffDetIdx, detIdx1))
                //{
                //    continue;
                //}
				//隔车道的目标，若是两个点的SNR都挺强，且一个点运动，一个点静止，则，动静不聚类
				else if (pdetObj[groupBuffDetIdx].x > 0.75f + LANE_WIDTH && pdetObj[groupBuffDetIdx].x < 0.75f + 2*LANE_WIDTH
					&& pdetObj[detIdx1].x > 0.75f + LANE_WIDTH && pdetObj[detIdx1].x < 0.75f + 2 * LANE_WIDTH
					&& pdetObj[groupBuffDetIdx].y < 10.f && pdetObj[detIdx1].y < 10.f
					&& (pdetObj[groupBuffDetIdx].status & POINT_STATUS_DYNAMIC_BMP) != (pdetObj[detIdx1].status & POINT_STATUS_DYNAMIC_BMP)
					&& fabsf(pdetObj[groupBuffDetIdx].x - pdetObj[detIdx1].x) > 0.5f && pdetObj[groupBuffDetIdx].mea_z[0] > 45.f && pdetObj[detIdx1].mea_z[0] > 45.f)
				{
					continue;
				}
                // AEB鬼探头场景  动静不聚类 X y在 5 - 15米之间
                else if ((1 == pTrkPkg->aebsidecarsence) && sideCarclusterDynamicOrNot(pdetObj, groupBuffDetIdx, detIdx1))
                {
                    continue;
                }
                
				float tempSideLines_detIdx1 = getSideLineDis(pdetObj[detIdx1].y, pSideLinePkg);
				float tempSideLines_groupBuffDetIdx = getSideLineDis(pdetObj[detIdx1].y, pSideLinePkg);
				if (pSideLinePkg->strongSildLineValid && pdetObj[groupBuffDetIdx].y < pSideLinePkg->strongFenceRange[1] && pdetObj[detIdx1].y < pSideLinePkg->strongFenceRange[1])
				{
					if (fabsf(pdetObj[groupBuffDetIdx].x + pdetObj[detIdx1].x - tempSideLines_detIdx1 - tempSideLines_groupBuffDetIdx) < 1.0f && fabsf(pdetObj[groupBuffDetIdx].x - pdetObj[detIdx1].x) > 0.5f)
					{
						continue;	//近护栏目标之间，横向距离足够远时暂不做凝聚
					}
					if (clusterInSildLineSide(pTrkPkg, pdetObj, groupBuffDetIdx, detIdx1))
					{
						continue;	// 运动点不与护栏点聚类
					}
				}

                // 对于运动目标横向4m、纵向2m聚类门限过大，容易造成行人或二轮车等缓行目标与静止点误聚类，导致无法起批
                if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < (20 / 3.6f)
                    && ((pdetObj[groupBuffDetIdx].status & POINT_STATUS_DYNAMIC_BMP && !(pdetObj[detIdx1].status & POINT_STATUS_DYNAMIC_BMP))
                        || (pdetObj[detIdx1].status & POINT_STATUS_DYNAMIC_BMP && !(pdetObj[groupBuffDetIdx].status & POINT_STATUS_DYNAMIC_BMP)))
                    && fabsf(pdetObj[groupBuffDetIdx].otgVel - pdetObj[detIdx1].otgVel) > 0.5f)
                {
                    continue;   // 动静不聚类
                }
                // FCTB遮挡场景.   不与侧面遮挡静止车聚类.  需关注是否会导致前角近处目标分裂.
                // 主要思路是自车低速时. 将前角近处的不同目标区分开. 同时需规避大车分裂.
                if ((config->isFront == 1) && (fabsf(pRDP_inVehicleData->vdySpeedInmps) < (20.0f / 3.6f)) && (fabsf(pRDP_inVehicleData->vdySteeringAngle) < (60.0f))
                     && (pdetObj[groupBuffDetIdx].x < 3.0f) && (pdetObj[groupBuffDetIdx].y < 3.0f)
                     && (pdetObj[groupBuffDetIdx].x > 0.5f) && (pdetObj[groupBuffDetIdx].y > -2.0f) 
                     && (pdetObj[detIdx1].x < 3.0f) && (pdetObj[detIdx1].y < 5.0f)
                     && (pdetObj[detIdx1].x > 0.5f) && (pdetObj[detIdx1].y > -2.0f) 
                     && (fabsf(deltaDoppler) > 1.0f) && (fabsf(deltaY) > 1.5f) && fabsf(deltaAngle) > (DEG2RAD * 30))
                {
                    continue;
                } 

				if (RDP_getTrackConfigPointer()->radarResolutionTestMode == TEST_MODE_RESOLUTION
					&& pdetObj[groupBuffDetIdx].mea_z[1] > 30.f
					&& fabsf(pdetObj[groupBuffDetIdx].mea_z[2]) > 1.f)
				{
					// 远距离各检测点角度差较小，横向差较大点易误聚类（同距同速场景）
					angleThr = RAD2ANG * atan2f(2.f, pdetObj[groupBuffDetIdx].y);
				}

                if ((deltaRange < 1.5f && deltaDoppler < 4.f
						&& (deltaAngle * pdetObj[groupBuffDetIdx].mea_z[1] < 1.f || deltaAngle < angleThr * PI / 180.f))
                    || (deltaX < crossThr
                        && deltaY < verticalThr
                        && (deltaDoppler < 4.f
                            || (deltaDoppler < 10.f
                                && deltaAngle > 40.0 * PI / 180.f
                                && pdetObj[groupBuffDetIdx].x > 0.f
                                && pdetObj[groupBuffDetIdx].x < 3.5f
                                && pdetObj[groupBuffDetIdx].y > -2.f
                                && pdetObj[groupBuffDetIdx].y < 3.f))))
                {
                    pdetObj[detIdx1].groupId = groupId;
					pdetObj[detIdx1].groupId1 = groupId;
                    groupBuff[buffLen] = detIdx1;
                    buffLen++;
                    if(pdetObj[detIdx1].mea_z[0] > maxVal && !(pdetObj[detIdx1].status & POINT_STATUS_DEBLUR_FAILED))   // 若记录0x10标记的点则会导致无法新建
                    {
                        maxValDetIdx = detIdx1;
                        maxVal = pdetObj[detIdx1].mea_z[0];
                    }
                    if(pdetObj[detIdx1].mea_z[1] < nearestRange)
                    {
                        nearestDetIdx = detIdx1;
                        nearestRange = pdetObj[detIdx1].mea_z[1];
                    }

                    if(pdetObj[detIdx1].status & POINT_STATUS_ACTIVE_BMP)
                    {
                        groupStatus |= POINT_STATUS_ACTIVE_BMP;
                    }
                    if(pdetObj[detIdx1].status & POINT_STATUS_ACTIVE_MOVING_BMP)
                    {
                        groupStatus |= POINT_STATUS_ACTIVE_MOVING_BMP;
                    }
                    if (pdetObj[detIdx1].status & POINT_STATUS_ASSOCIATED_BMP)
                    {
                        groupStatus |= POINT_STATUS_ASSOCIATED_BMP;
                        if (associatedNum < ASSOCIATED_TRACKS_NUM_MAX)
                        {
                            associatedNum++;
                        }
                    }
                }
            }
        }

        if(groupStatus & POINT_STATUS_ACTIVE_MOVING_BMP)
        {
            for(buffIdx = 0; buffIdx < buffLen; buffIdx++)
            {
                groupBuffDetIdx = groupBuff[buffIdx];
                pdetObj[groupBuffDetIdx].status |= groupStatus | POINT_STATUS_CLUSTERED_BMP;
            }
        }
        else if(groupStatus & POINT_STATUS_ACTIVE_BMP)
        {
            for(buffIdx = 0; buffIdx < buffLen; buffIdx++)
            {
                groupBuffDetIdx = groupBuff[buffIdx];
                pdetObj[groupBuffDetIdx].status |= groupStatus | POINT_STATUS_CLUSTERED_BMP;
            }
        }
        else if(groupStatus & POINT_STATUS_ASSOCIATED_BMP)
        {
            for(buffIdx = 0; buffIdx < buffLen; buffIdx++)
            {
				groupBuffDetIdx = groupBuff[buffIdx];
				if (!(pdetObj[groupBuffDetIdx].status & POINT_STATUS_ASSOCIATED_BMP))
					pdetObj[groupBuffDetIdx].status |= POINT_STATUS_CLUSTERED_BMP;
				pdetObj[groupBuffDetIdx].status |= groupStatus;
            }
        }
        else
        {
            for(buffIdx = 0; buffIdx < buffLen; buffIdx++)
            {
                groupBuffDetIdx = groupBuff[buffIdx];
                pdetObj[groupBuffDetIdx].status |= groupStatus;
                if(groupBuffDetIdx != maxValDetIdx)
                    pdetObj[groupBuffDetIdx].status |= POINT_STATUS_CLUSTERED_BMP;
            }
        }
        for(buffIdx = 0; buffIdx < buffLen && groupId2 <= GROUP_ID_END; buffIdx++)
        {
            detIdx2 = groupBuff[buffIdx];
            if(pdetObj[detIdx2].groupId2 != GROUP_ID_NONE)
                continue;
            pdetObj[detIdx2].groupId2 = groupId2;
            group2Buff[0] = detIdx2;
            nearestDetIdx2 = maxValDetIdx2 = detIdx2;
            nearestRange = pdetObj[detIdx2].mea_z[1];
            maxVal = pdetObj[detIdx2].mea_z[0];
            group2Status = (pdetObj[detIdx2].status & (POINT_STATUS_ACTIVE_BMP|POINT_STATUS_ASSOCIATED_BMP));
            for(buff2Idx = 0, buff2Len = 1; buff2Idx < buff2Len; buff2Idx++)
            {
                if(buff2Len >= MAX_OBJ_PER_GROUP2)
                    break;
                groupBuff2DetIdx = group2Buff[buff2Idx];
                for(buffIdx2 = buffIdx+1; buffIdx2 < buffLen; buffIdx2++)
                {
                    if(buff2Len >= MAX_OBJ_PER_GROUP2)
                        break;
                    detIdx1 = groupBuff[buffIdx2];
                    if(pdetObj[detIdx1].groupId2 != GROUP_ID_NONE)
                        continue;
                    deltaRange = fabsf(pdetObj[groupBuff2DetIdx].mea_z[1] - pdetObj[detIdx1].mea_z[1]);
                    deltaAngle = fabsf(pdetObj[groupBuff2DetIdx].mea_z[3] - pdetObj[detIdx1].mea_z[3])*PI/180;
                    DELTA_RAD_NORM(deltaAngle);
                    deltaX = fabsf(pdetObj[groupBuff2DetIdx].x - pdetObj[detIdx1].x);
                    deltaY = fabsf(pdetObj[groupBuff2DetIdx].y - pdetObj[detIdx1].y);
                    deltaDoppler = fabsf(pdetObj[groupBuff2DetIdx].mea_z[2] - pdetObj[detIdx1].mea_z[2]);
                    if( (deltaDoppler < (2/3.6f)&& ((deltaRange < CROSS_OUT_X_LIMIT && (deltaAngle*pdetObj[groupBuff2DetIdx].mea_z[1] < CROSS_OUT_X_LIMIT||deltaAngle <  3.0f*PI/180)) ||\
                        (deltaX < CROSS_OUT_X_LIMIT && deltaY < CROSS_OUT_X_LIMIT))) || 
                        ((fabsf(pRDP_inVehicleData->vdySpeedInmps) > 2 || groupStatus&POINT_STATUS_ACTIVE_MOVING_BMP)&& ((deltaRange < crossBoundary && (deltaAngle*pdetObj[groupBuff2DetIdx].mea_z[1] < crossBoundary||deltaAngle <  4.0f*PI/180)) ||\
                        (deltaX < crossBoundary && deltaY < verticalBoundary))))
                    {
                        pdetObj[detIdx1].groupId2 = groupId2;
                        group2Buff[buff2Len] = detIdx1;
                        buff2Len++;
                        if(pdetObj[detIdx1].mea_z[1] < nearestRange)
                        {
                            nearestRange = pdetObj[detIdx1].mea_z[1];
                            nearestDetIdx2 = detIdx1;
                        }
                        if(pdetObj[detIdx1].mea_z[0] > maxVal)
                        {
                            maxVal = pdetObj[detIdx1].mea_z[0];
                            maxValDetIdx2 = detIdx1;
                        }
                        if(pdetObj[detIdx1].status & POINT_STATUS_ACTIVE_BMP)
                        {
                            group2Status |= POINT_STATUS_ACTIVE_BMP;
                        }
                        if(pdetObj[detIdx1].status & POINT_STATUS_ASSOCIATED_BMP)
                        {
                            group2Status |= POINT_STATUS_ASSOCIATED_BMP;
                        }
                    }
                }
            }
            gMemberNumPerGroup[groupId2] = buff2Len;
            objNum = objNumValid = objNumDynamic = objNumReverse = dynamicCertainFlag = 0;
            for(buff2Idx = 0; buff2Idx < buff2Len; buff2Idx++)
            {
                groupBuff2DetIdx = group2Buff[buff2Idx];
                if(pdetObj[groupBuff2DetIdx].status & POINT_STATUS_DYNAMIC_BMP && pdetObj[groupBuff2DetIdx].valid == 1)
                {
                    objNumValid++;
                    objNumDynamic++;
                    if(pdetObj[groupBuff2DetIdx].status & POINT_STATUS_REVERSE_BMP)
                    {
                        objNumReverse++;
                    }
                    if(pdetObj[groupBuff2DetIdx].status & POINT_STATUS_DYNAMIC_CERTAIN_BMP)
                    {
                        dynamicCertainFlag = 1;
                        dycertainIdx = groupBuff2DetIdx;
                    }
                }
                else if(!ANGLE_IN_CROSS_AREA(pdetObj[groupBuff2DetIdx].mea_z[3], 12.25f))
                {
                    objNumValid++;
                }
                objNum++;
            }

            // 记录groupId2状态，用于航迹新建和航迹管理
            groupDynamicStatus = 0;
            if(objNumDynamic > 4
                || ((pdetObj[nearestDetIdx2].mea_z[1] > 4
                        || objNumDynamic > 1
                        || pRDP_inVehicleData->vdyDriveDirection
                        || fabsf(pRDP_inVehicleData->vdySpeedInmps) < (10.f/3.6f)
                        || (dynamicCertainFlag && getVerticalStaticPointNum(pdetObj, detNum, dycertainIdx) < 2))
                    && (objNumDynamic << 1) > objNumValid)
                || (objNumDynamic == 1 && objNumValid == 1)
				/*|| (IN_BSD_AREA(pdetObj[maxValDetIdx2].x, pdetObj[maxValDetIdx2].y) && objNumDynamic >= 1 && objNumValid >= 1)*/)	// 两侧静止车，自车穿行可能个别点速度异常判为运动，可能会导致聚类组运动，暂时屏蔽
            {
                groupDynamicStatus |= POINT_STATUS_DYNAMIC_BMP;
                if((objNumReverse << 1) >= objNumDynamic)
                    groupDynamicStatus |= POINT_STATUS_REVERSE_BMP;
            }
            else if (objNumValid > 0 && objNumDynamic * 3 < objNumValid)
            {
                groupDynamicStatus &= ~POINT_STATUS_DYNAMIC_BMP;
            }
            else
            {
                groupDynamicStatus |= POINT_STATUS_STATIC_UNCERTAIN_BMP;
            }
            gGroupStatus[groupId2] = groupDynamicStatus;

            for(buff2Idx = 0; buff2Idx < buff2Len; buff2Idx++)
            {
                groupBuff2DetIdx = group2Buff[buff2Idx];
                // 这是什么场景？
                if(nearestDetIdx2 == nearestDetIdx && !(group2Status & POINT_STATUS_ACTIVE_BMP) && group2Status & POINT_STATUS_ASSOCIATED_BMP)
                {
                    pdetObj[groupBuff2DetIdx].status &= ~POINT_STATUS_CLUSTERED_BMP;
                }
                if(pdetObj[groupBuff2DetIdx].index != FILTER_NON_ASSOC)
                {
                    unitInst = &pTrack[pdetObj[groupBuff2DetIdx].index];
                    if(pdetObj[groupBuff2DetIdx].mea_z[1] - nearestRange < 3.0f && (pdetObj[groupBuff2DetIdx].mea_z[0] - pdetObj[nearestDetIdx2].mea_z[0]) < 5.0f)
                    {
                        if(!(pdetObj[nearestDetIdx2].status & POINT_STATUS_ASSOCIATED_BMP))
                            unitInst->pidNearest = nearestDetIdx2;
                        else
                        {
                            unitInstNearest = &pTrack[pdetObj[nearestDetIdx2].index];
                            if(unitInstNearest->type != TRACK)
                                unitInst->pidNearest = nearestDetIdx2;
                            else
                                unitInst->pidNearest = groupBuff2DetIdx;
                        }
                    }else
                    {
                        unitInst->pidNearest = groupBuff2DetIdx;
                    }
                    unitInst->pidStrongest = maxValDetIdx2;
                }
            }
            
            groupId2++;
        }
        if(nearestDetIdx2 == nearestDetIdx && !(group2Status&(POINT_STATUS_ACTIVE_BMP|POINT_STATUS_ASSOCIATED_BMP)))
            pdetObj[maxValDetIdx2].status &= ~POINT_STATUS_CLUSTERED_BMP;
            
        groupId++;
    }
//    shell_sort(ptrGuardrailModule->crossRangeBuffer[GUARDRAIL_SEARCH_BUFFER_STATIC_ID].buffer, GUARDRAIL_SEARCH_BUFFER_LEN);
//    ptrGuardrailModule->guardRailCross = ptrGuardrailModule->crossRangeBuffer[GUARDRAIL_SEARCH_BUFFER_STATIC_ID].buffer[0];
    //if(pRDP_inVehicleData->vdyDriveDirection)
    //{
    //    crossBoundary = TRACK_AREA_Y;
    //    verticalBoundary = TRACK_AREA_X;
    //}else
    //{
    //    crossBoundary = TRACK_AREA_X;
    //    verticalBoundary = TRACK_AREA_Y;
    //}
    for(detIdx = 0; detIdx < detNum; detIdx++)
    {
        if(pdetObj[detIdx].status & POINT_STATUS_ASSOCIATED_BMP && pdetObj[detIdx].status & POINT_STATUS_ACTIVE_BMP
            && (pTrack[pdetObj[detIdx].index].status & TRACK_STATUS_MOVING_BMP || fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f))
        {
            for(detIdx1 = detIdx + 1; detIdx1 < detNum; detIdx1++)
            {
                if(!(pdetObj[detIdx1].status & POINT_STATUS_CLUSTERED_BMP)
                    && ((pdetObj[detIdx].status & POINT_STATUS_DYNAMIC_BMP && pdetObj[detIdx1].status & POINT_STATUS_DYNAMIC_BMP)
                        || (!(pdetObj[detIdx].status & POINT_STATUS_DYNAMIC_BMP) && !(pdetObj[detIdx1].status & POINT_STATUS_DYNAMIC_BMP))))
                {
                    deltaX = (pdetObj[detIdx1].x - pdetObj[detIdx].x);
                    deltaY = (pdetObj[detIdx1].y - pdetObj[detIdx].y);
                    if( ((pdetObj[detIdx].x >= 0 && deltaX > -TRACK_AREA_DELTA && deltaX < crossBoundary) ||
                         (pdetObj[detIdx].x < 0  && deltaX > -crossBoundary    && deltaX < TRACK_AREA_DELTA)) &&
                        ((pdetObj[detIdx].y >= 0 && deltaY > -TRACK_AREA_DELTA && deltaY < verticalBoundary) ||
                         (pdetObj[detIdx].y < 0  && deltaY > -verticalBoundary && deltaY < TRACK_AREA_DELTA)) )
                    {
						if (gMemberNumPerGroup[pdetObj[detIdx1].groupId2] >= 3 \
							&& !(pdetObj[detIdx].status & POINT_STATUS_DYNAMIC_BMP) \
							&& (pdetObj[detIdx1].status &  POINT_STATUS_DYNAMIC_BMP))
						{
							;
						}
						else
						{
							pdetObj[detIdx1].status |= POINT_STATUS_CLUSTERED_BMP;
						}
                        
                    }
                }
            }
        }
    }
    groupNum = groupId - GROUP_ID_START;
    return groupNum;
}

/**
 * @brief 候选点组ID置为GROUP_ID_NONE
 * @param detNum 原始点目标数 
 * @param pdetObj RDP内部用于跟踪的原始点全局变量指针  
 */
void RDP_Track_initPointsGroup(uint32_t detNum, cdi_t *pdetObj)
{
    uint32_t i;
    for(i = 0; i < detNum; i++)
    {
        pdetObj[i].groupId = GROUP_ID_NONE;
    }
}

/**
*  @b Description
*  @n
*       This function estimate the vx&vy of a track
*  @author: Zhang Jian
*/

#define LS_LEN_MAX (STORE_POINT_NUM + 1 + 16)
uint8_t trackVxVyEst(trk_t *trk, uint16_t storePointNum, cdi_t *cdi, uint16_t num)
{
    int16_t i;
    uint16_t associatePointId = trk->idx_1;
#if 1
    /* estimate vx&vy based on LSM */
    /*
    vr = [sin(a) cos(a)]*[vx vy]'
    VrArray = DirArray*VcartArray;
    VcartArray = inv(DirArray'*DirArray)*DirArray'*VrArray;
    */
    uint16_t lsLen = 0;
    float VrArray[LS_LEN_MAX],DirArrayT[2][LS_LEN_MAX] = {0}, VcartFit[2];
    float temp2[2*2], invtemp2[2*2];
    float minCosValue, maxCosValue, minSinValue, maxSinValue, dCosValue;//, dSinValue;
    float deltaCosThr;
    float temp;
    uint16_t groupId = cdi[associatePointId].groupId2;
    for(i = 0; i < storePointNum; i++)
    {
        if(trk->stored_last_status[i] & POINT_STATUS_DEBLUR_FAILED)
        {
            continue;
        }
        DirArrayT[0][lsLen] = sinf(trk->stored_last_z[i][3]*DEG2RAD);
        DirArrayT[1][lsLen] = cosf(trk->stored_last_z[i][3]*DEG2RAD);
        if(trk->trkCnt > 10)
        {
            temp = DirArrayT[0][lsLen]*trk->x[2] + DirArrayT[1][lsLen]*trk->x[3];
            if(fabsf(temp-trk->stored_last_z[i][2]) < 3.0f || fabsf(cdi[associatePointId].mea_z[2]-trk->stored_last_z[i][2]) < 3.0f)
            {
                VrArray[lsLen] = trk->stored_last_z[i][2];
                lsLen++;
            }
        }else
        {
            VrArray[lsLen] = trk->stored_last_z[i][2];
            lsLen++;
        }
    }
    for(i = 0; i < num; i++)
    {
        if(lsLen >= LS_LEN_MAX)
            break;
        if(cdi[i].groupId2 == groupId && fabsf(cdi[i].mea_z[2] - cdi[associatePointId].mea_z[2]) < 15.f && !(cdi[i].status & POINT_STATUS_DEBLUR_FAILED))
        {
            DirArrayT[0][lsLen] = cdi[i].x/cdi[i].mea_z[1];
            DirArrayT[1][lsLen] = cdi[i].y/cdi[i].mea_z[1];
            if(trk->trkCnt > 10)
            {
                temp = DirArrayT[0][lsLen]*trk->x[2] + DirArrayT[1][lsLen]*trk->x[3];
                if(fabsf(temp-cdi[i].mea_z[2]) < 3.0f || fabsf(cdi[associatePointId].mea_z[2]-cdi[i].mea_z[2]) < 3.0f)
                {
                    VrArray[lsLen] = cdi[i].mea_z[2];
                    lsLen++;
                }
            }else
            {
                VrArray[lsLen] = cdi[i].mea_z[2];
                lsLen++;
            }
        }
    }
    float *fltPtr = &DirArrayT[0][0];
    minSinValue = maxSinValue = fltPtr[0];
    minCosValue = maxCosValue = fltPtr[LS_LEN_MAX];
    for(i = 0; i < lsLen; i++)
    {
        fltPtr[lsLen+i] = fltPtr[LS_LEN_MAX+i];
        if(fltPtr[i] < minSinValue)
            minSinValue = fltPtr[i];
        if(fltPtr[i] > maxSinValue)
            maxSinValue = fltPtr[i];
        if(fltPtr[lsLen+i] < minCosValue)
            minCosValue = fltPtr[lsLen+i];
        if(fltPtr[lsLen+i] > maxCosValue)
            maxCosValue = fltPtr[lsLen+i];
        
        
    }
    dCosValue = maxCosValue-minCosValue;
    // dSinValue = maxSinValue-minSinValue;
    deltaCosThr = (trk->trkCnt>>2)*0.15f+0.25f;
    if(deltaCosThr > 0.7f)
        deltaCosThr = 0.7f;

    if(dCosValue > deltaCosThr || (!(trk->status & TRACK_STATUS_VELOCITY_CONFIRMED_BMP) && dCosValue > 0.25f))
    {
        matrixTransposeMultiply(2, lsLen, 2, DirArrayT[0], DirArrayT[0], temp2);
        matrixInv2(temp2, invtemp2);
        matrixMultiply(2, lsLen, 1, DirArrayT[0], VrArray, temp2);
        matrixMultiply(2, 2, 1, invtemp2, temp2, VcartFit);

        float vx = VcartFit[0];
        float vy = VcartFit[1];
        float diffLat = trk->stored_last_x0[0] - trk->stored_last_x0[storePointNum - 1];
        float diffLng = trk->stored_last_x1[0] - trk->stored_last_x1[storePointNum - 1];
        float deltsec = 0.0f;
        float vThr = 0.f;
        for(i = 0; i < storePointNum; i++)
        {
            deltsec += gRDP_storedFrameTime[i];
        }

        //速度不在0附近时，判断下位移的方向和速度的方向是否相反，防止估计出来的速度很离谱
        vThr = fabsf(trk->sim_z[2]) > 10.f ? 5.f : 2.5f;
        if(((diffLat * vx) < 0 && fabsf(vx) > 0.5f) || ((diffLng * vy) < 0 && fabsf(vy) > 0.5f))
        {
            return false;
        }
        //跟踪已经很多帧的情况下，使用位移和速度判断下，防止估计出来的速度很异常
        else if(trk->trkCnt > 15 && (fabsf((diffLng / deltsec) - vy) > vThr || fabsf((diffLat / deltsec) - vx) > vThr))
        {
            return false;
        }
        // 帧间速度相差较大
        else if (trk->status & TRACK_STATUS_IN_DEADZONE_BMP && fabsf(trk->sim_z[2]) < 1.f
            && ((trk->type == CANDI && fabsf(trk->x[3] - VcartFit[1]) > 5.f)
                || (trk->type == TRACK && fabsf(trk->x[3] - VcartFit[1]) > 2.5f)))
        {
            return false;
        }
        else if(!(trk->status & TRACK_STATUS_VELOCITY_CONFIRMED_BMP) || deltaCosThr < 0.7f || (fabsf(trk->x[2]-VcartFit[0]) < 0.3f && fabsf(trk->x[3]-VcartFit[1]) < 0.6f))
        {
            trk->x[2] = VcartFit[0];
            trk->x[3] = VcartFit[1];
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        return false;
    }
    /* end */
#endif
}

/**
*  @b Description
*  @n
*       根据同一个目标的多个量测点的径向速度和角度去解横纵向速度；LSM准则
*       Matrices are all real, single precision floating point.
*       Matrices are in row-major order
*
*  @param[in]  n
*       量测点的个数
*  @param[in]  v_z_vector
*       量测点的径向速度
*  @param[in]  angle_vector
*       量测点的水平角
*  @param[in]  p_angle_vector
*       量测点的俯仰角
*  @param[out]  vx_y_vector
*       解出的vx,vy,为二维向量
*   @return  isSolveSuccess
*       解径向速度是否成功
*       0：失败
*       0x01：Vx可信
*       0x02：Vy可信
*
*  <AUTHOR>
*/
uint8_t solveRadialVelocity(uint8_t n, float* v_z_vector, float* angle_vector, float* p_angle_vector, float* vx_vy, float delta_angle)
{
    if (n < 3 || n > MAX_OBJ_PER_GROUP_VX_VY)
        return 0;
    uint8_t result = 0;
    float A[MAX_OBJ_PER_GROUP_VX_VY * 2];
    float AT[MAX_OBJ_PER_GROUP_VX_VY * 2];
    float tmp[4];
    float inv[4];
    float tmp2[MAX_OBJ_PER_GROUP_VX_VY * 2];
    float sumSinAngle = 0.f;
    float sumCosAngle = 0.f;
    float sum_v_z = 0.f;//径向速度均值
    for (uint8_t i = 0;i < n;i++) {
        float sinAngle = sinf(angle_vector[i] * DEG2RAD);
        float cosAngle = cosf(angle_vector[i] * DEG2RAD);
        sumSinAngle += sinAngle;
        sumCosAngle += cosAngle;
        A[i] = sinAngle;
        A[n + i] = cosAngle;
        AT[i * 2] = sinAngle;
        AT[i * 2 + 1] = cosAngle;
        sum_v_z += v_z_vector[i];
    }
    matrixTransposeMultiply(2, n, 2, A, A, tmp);   //tmp = A * A'
    matrixInv2(tmp, inv);   //inv = inv(A * A')
    matrixMultiply(2, 2, n, inv, A, tmp2);   //tmp2 = inv(A * A')* A
    matrixMultiply(2, n, 1, tmp2, v_z_vector, vx_vy);   //vx_vy = inv(A * A')* A * v_z_vector
    float v_z_revert[MAX_OBJ_PER_GROUP_VX_VY];
    float v_z_diff[MAX_OBJ_PER_GROUP_VX_VY];
    float variance[1];
    matrixMultiply(n, 2, 1, AT, vx_vy, v_z_revert);
    matrixSub(1, n, v_z_vector, v_z_revert, v_z_diff);
    matrixTransposeMultiply(1, n, 1, v_z_diff, v_z_diff, variance);
    float mean_var = sqrtf(variance[0] / n);
    float mean_v_z = max(fabsf(sum_v_z / n) , 1.f);
    float mean_var_thr, mean_v_z_thr;
    if (n <= 8 && delta_angle < 10.f) {
        mean_var_thr = 0.3f;
        mean_v_z_thr = 0.1f;
    }
    else if (n <= 16 && delta_angle < 20.f)
    {
        mean_var_thr = 0.8f;
        mean_v_z_thr = 0.15f;
    }
    else
    {
        mean_var_thr = 2.f;
        mean_v_z_thr = 0.2f;
    }
    float menSinAngle = sumSinAngle / n;
    float menCosAngle = sumCosAngle / n;
    float vzGroupMen = menSinAngle * vx_vy[0] + menCosAngle * vx_vy[1];
    float vzGroupMenRevert = menSinAngle * vx_vy[1] + menCosAngle * vx_vy[0];
    float vMod = sqrtf(vx_vy[0] * vx_vy[0] + vx_vy[1] * vx_vy[1]);
    do_BubbleSort(v_z_diff, n);
    float maxDiff = max(fabsf(v_z_diff[n - 1]) , fabsf(v_z_diff[0]));
    // 解出的误差均值、方差小于阈值
    if (mean_var < mean_var_thr && mean_var < mean_v_z_thr * mean_v_z && fabsf(vzGroupMen) > 0.15f * vMod
        && fabsf(vzGroupMen - vzGroupMenRevert) > 0.5f && !(maxDiff > 0.1f && maxDiff > 10.f * mean_var))
    {
        if (fabsf(menSinAngle) > 0.3f || (fabsf(menSinAngle) > 0.15f && delta_angle > 20.f)) // 非横向速度盲区
            result |= SOLVE_GROUR_SUCCESS_VX;
        if (fabsf(menCosAngle) > 0.3f || (fabsf(menCosAngle) > 0.15f && delta_angle > 20.f)) // 非纵向速度盲区
            result |= SOLVE_GROUR_SUCCESS_VY;
    }
    return result;
}

uint8_t trackVxVyEstFromGroup(cdi_pkg_t* pCdiPkg, VDY_DynamicEstimate_t* pRDP_inVehicleData, uint16_t groupId, float* vx_vy) {
    if (groupId == GROUP_ID_NONE || groupId == GROUP_ID_INVALID || gGroupInfo[groupId].pointList.count < 3) {
        return 0;
    }
    float delta_angle = 0.f;
    uint8_t idx_group = 0;
    float v_z_vector[MAX_OBJ_PER_GROUP_VX_VY];
    float angle_vector[MAX_OBJ_PER_GROUP_VX_VY];
    float min_angle = FLT_MAX;
    float max_angle = FLT_MIN;
    float max_Vz = FLT_MIN;

    GTrack_ListElem* pointElemGrouped = gGroupInfo[groupId].pointList.begin;
    // 找出对地速度最大值
    while (pointElemGrouped)
    {
        uint32_t pointId = pointElemGrouped->data;
        float otgVel = fabsf(pCdiPkg->cdi[pointId].otgVel);
        if (!(pCdiPkg->cdi[pointId].status & POINT_STATUS_DEBLUR_FAILED) && max_Vz < otgVel)
        {
            max_Vz = otgVel;
        }
        pointElemGrouped = pointElemGrouped->next;
    }

    // 求出对地速度平均值
    float menOtgVel = 0.f;
    float menRange = 0.f;
    pointElemGrouped = gGroupInfo[groupId].pointList.begin;
    while (pointElemGrouped)
    {
        uint32_t pointId = pointElemGrouped->data;
        // 解速度异常、绝对速度小、相对速度小,不选取该点
        if (!(pCdiPkg->cdi[pointId].status & POINT_STATUS_DEBLUR_FAILED || (max_Vz > 1.f && fabsf(pCdiPkg->cdi[pointId].otgVel) < 0.4f * max_Vz)))
        {
            menOtgVel += pCdiPkg->cdi[pointId].otgVel;
            menRange += pCdiPkg->cdi[pointId].mea_z[1];
            idx_group++;
        }
        pointElemGrouped = pointElemGrouped->next;
    }

    if (idx_group < 3)
        return 0;
    menOtgVel /= idx_group;
    menRange /= idx_group;

    // 筛选原始点
    pointElemGrouped = gGroupInfo[groupId].pointList.begin;
    idx_group = 0;
    float otgVelThr = max(fabsf(menOtgVel) * 0.2f , 0.4f);
    while (pointElemGrouped && idx_group < MAX_OBJ_PER_GROUP_VX_VY)
    {
        uint32_t pointId = pointElemGrouped->data;
        // 解速度异常、相对速度小、跟均值差距大、绝对速度小,不选取该点
        if (!(pCdiPkg->cdi[pointId].status & POINT_STATUS_DEBLUR_FAILED || (max_Vz > 1.f && fabsf(pCdiPkg->cdi[pointId].otgVel) < 0.4f * max_Vz)
            || fabsf(menOtgVel - pCdiPkg->cdi[pointId].otgVel) > otgVelThr || fabsf(pCdiPkg->cdi[pointId].mea_z[2]) < 0.6f)) // 
        {
            SEL_MIN(min_angle, pCdiPkg->cdi[pointId].mea_z[3]);
            SEL_MAX(max_angle, pCdiPkg->cdi[pointId].mea_z[3]);
            v_z_vector[idx_group] = pCdiPkg->cdi[pointId].mea_z[2];
            angle_vector[idx_group] = pCdiPkg->cdi[pointId].mea_z[3];
            idx_group++;
        }
        pointElemGrouped = pointElemGrouped->next;
    }
    delta_angle = max_angle - min_angle;
    float angleThr = max(3.f , menRange * 0.5f);
    if (idx_group >= 3 && delta_angle > angleThr)
    {
        return solveRadialVelocity(idx_group, v_z_vector, angle_vector, NULL, vx_vy, delta_angle);
    }
    return 0;
}

//前方车辆与绝对静止目标点的反射（反射路径：自车->目标车->静止目标->目标车->自车 或者 自车->静止目标->目标车->自车）
uint8_t isSecondaryReflection2(cdi_t* pCdi, trk_pkg_t *pTrkPkg, cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *pRDP_inVehicleData)
{
    //只做运动目标 trk_t* trk
    trk_t* trk;
    bool haveReflection = false;
    float v1 ,v2;
    if((pCdi->status & TRACK_STATUS_MOVING_BMP) == 0
                || fabsf(pRDP_inVehicleData->vdySpeedInmps) < 5.0f
                || pCdi->mea_z[0] > 25
                || pCdi->mea_z[1] < 20
            )
    {
        return haveReflection;
    }

    //遍历关系查找反射点目标
    for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if(pTrkPkg->trk[i].type != TRACK
                || (pTrkPkg->trk[i].status & TRACK_STATUS_MOVING_BMP) == 0
                || pTrkPkg->trk[i].sim_z[0] < 15)
        {
            continue;
        }
        trk = &pTrkPkg->trk[i];
        v1 = (trk->sim_z[2] - pRDP_inVehicleData->vdySpeedInmps);
        v2 = (trk->sim_z[2] - pRDP_inVehicleData->vdySpeedInmps * 0.65f); //因为大车与静止目标之前存在角度，所以会小一些，但是这个角度不确定
        if(trk->sim_z[1] < pCdi->mea_z[1] - 5.0f
                && pCdi->mea_z[2] < v1
                && pCdi->mea_z[2] > v2
                && fabsf(pCdi->mea_z[3]  - trk->sim_z[3]) < 5.0f
                )
        {
            haveReflection = true;
            break;
        }
    }
    return haveReflection;
}

void RDP_updateObjType(trk_t* trk, VDY_DynamicEstimate_t *pRDP_inVehicleData)   //0 = Unknown; 1 = pedestran; 2 = motorcycle; 3 = car; 4 = truck
{
    float vgx = trk->x[2];
    float vgy = trk->x[3] - pRDP_inVehicleData->vdySpeedInmps;
    float vg = sqrtf(vgx * vgx + vgy * vgy);
    float length = trk->front + trk->back;
    float width = trk->left + trk->right;
    if(trk->activeTrkCnt == 1)
    {
        trk->objType = 0;       //目标初始默认Unknown
    }
    if(!(trk->status & TRACK_STATUS_MOVING_BMP))
    {
        return;                 //静态目标不改变分类
    }
    if(length >= 6.5f)
    {
        trk->objType = 4;       //卡车
        return;
    }
    else if(length >= 2.9f)
    {
        if(trk->objType != 4 || length < 4.5f)      //此前未判为卡车，或航迹框长度充分小
        {
            trk->objType = 3;   //小汽车
        }
        return;
    }    
    else if(length >= 1.5f)
    {
        if(vg >= 8.0f)                             //对地速度超过一般二轮车
        {
            trk->objType = 3;   //小汽车
        }
        else if(trk->activeTrkCnt > 15 && trk->objType < 3)     //中小尺寸慢速目标，且之前未判为汽车以上目标，待生命周期较大再行判断
        {
            if(vg > 3.0f || width > 0.75f)
            {
                trk->objType = 2;   //二轮车
            }
        }
    }
    else
    {
        if(vg >= 8.0f)                             //对地速度超过一般二轮车
        {
            trk->objType = 3;   //小汽车
        }
        else if(trk->activeTrkCnt > 15 && trk->objType <= 3 && vg >= 4.0f)    //小尺寸中速目标，且之前未判为汽车以上目标，待生命周期较大再行判断
        {
            trk->objType = 2;   //二轮车
        }
        else if(trk->activeTrkCnt > 15 && trk->objType <= 2 && vg < 4.0f)     //小尺寸慢速目标，且之前未判为二轮车以上目标，待生命周期较大再行判断
        {
            trk->objType = 1;   //行人
        }
    }
}

void RDP_updateObjReferPosi(trk_t* trk)
{
    rdp_config_t* config = RDP_getTrackConfigPointer();
    switch(config->installPosition)
    {
        case SENSOR_POSITION_REAR_LEFT:
            if(trk->activeTrkCnt == 1)      //初值
            {
                if(trk->x[0] > 0)
                {
                    if(trk->x[1] > 0)
                    {
                        trk->objReferPosi = 1;  //右前
                    }
                    else
                    {
                        trk->objReferPosi = 3;  //右后
                    }
                }
                else
                {
                    if(trk->x[1] > 0)
                    {
                        trk->objReferPosi = 0;  //左前
                    }
                    else
                    {
                        trk->objReferPosi = 2;  //左后
                    }
                }
            }
            else        //状态翻转增加区域滤波
            {
                if(trk->objReferPosi == 1 || trk->objReferPosi == 3)        //原先在右侧
                {
                    if(trk->x[0] < -0.5f)       //翻转至左侧
                    {
                        trk->objReferPosi--;
                    }
                }
                else                                                        //原先在左侧 
                {
                    if(trk->x[0] > 0.5f)        //翻转至右侧
                    {
                        trk->objReferPosi++;
                    }
                }
                if(trk->objReferPosi <= 1)                                  //原先在前侧
                {
                    if(trk->x[1] < -0.5f)       //翻转至后侧
                    {
                        trk->objReferPosi += 2;
                    }
                }
                else                                                        //原先在后侧 
                {
                    if(trk->x[1] > 0)           //翻转至前侧
                    {
                        trk->objReferPosi -= 2;
                    }
                }
            }
        break;
        case SENSOR_POSITION_REAR_RIGHT:
            if(trk->activeTrkCnt == 1)      //初值
            {
                if(trk->x[0] < 0)
                {
                    if(trk->x[1] > 0)
                    {
                        trk->objReferPosi = 1;  //右前
                    }
                    else
                    {
                        trk->objReferPosi = 3;  //右后
                    }
                }
                else
                {
                    if(trk->x[1] > 0)
                    {
                        trk->objReferPosi = 0;  //左前
                    }
                    else
                    {
                        trk->objReferPosi = 2;  //左后
                    }
                }
            }
            else        //状态翻转增加区域滤波
            {
                if(trk->objReferPosi == 1 || trk->objReferPosi == 3)        //原先在右侧
                {
                    if(trk->x[0] > 0.5f)       //翻转至左侧
                    {
                        trk->objReferPosi--;
                    }
                }
                else                                                        //原先在左侧 
                {
                    if(trk->x[0] < -0.5f)       //翻转至右侧
                    {
                        trk->objReferPosi++;
                    }
                }
                if(trk->objReferPosi <= 1)                                  //原先在前侧
                {
                    if(trk->x[1] < -0.5f)       //翻转至后侧
                    {
                        trk->objReferPosi += 2;
                    }
                }
                else                                                        //原先在后侧 
                {
                    if(trk->x[1] > 0)           //翻转至前侧
                    {
                        trk->objReferPosi -= 2;
                    }
                }
            }
        break;
        case SENSOR_POSITION_FRONT_LEFT:
            if(trk->activeTrkCnt == 1)      //初值
            {
                if(trk->x[0] > 0)
                {
                    if(trk->x[1] < 0)
                    {
                        trk->objReferPosi = 1;  //右前
                    }
                    else
                    {
                        trk->objReferPosi = 3;  //右后
                    }
                }
                else
                {
                    if(trk->x[1] < 0)
                    {
                        trk->objReferPosi = 0;  //左前
                    }
                    else
                    {
                        trk->objReferPosi = 2;  //左后
                    }
                }
            }
            else        //状态翻转增加区域滤波
            {
                if(trk->objReferPosi == 1 || trk->objReferPosi == 3)        //原先在右侧
                {
                    if(trk->x[0] < -0.5f)       //翻转至左侧
                    {
                        trk->objReferPosi--;
                    }
                }
                else                                                        //原先在左侧 
                {
                    if(trk->x[0] > 0.5f)        //翻转至右侧
                    {
                        trk->objReferPosi++;
                    }
                }
                if(trk->objReferPosi <= 1)                                  //原先在前侧
                {
                    if(trk->x[1] > 0)           //翻转至后侧
                    {
                        trk->objReferPosi += 2;
                    }
                }
                else                                                        //原先在后侧 
                {
                    if(trk->x[1] < -0.5f)       //翻转至前侧
                    {
                        trk->objReferPosi -= 2;
                    }
                }
            }
        break;
        case SENSOR_POSITION_FRONT_RIGHT:
            if(trk->activeTrkCnt == 1)      //初值
            {
                if(trk->x[0] < 0)
                {
                    if(trk->x[1] < 0)
                    {
                        trk->objReferPosi = 1;  //右前
                    }
                    else
                    {
                        trk->objReferPosi = 3;  //右后
                    }
                }
                else
                {
                    if(trk->x[1] < 0)
                    {
                        trk->objReferPosi = 0;  //左前
                    }
                    else
                    {
                        trk->objReferPosi = 2;  //左后
                    }
                }
            }
            else        //状态翻转增加区域滤波
            {
                if(trk->objReferPosi == 1 || trk->objReferPosi == 3)        //原先在右侧
                {
                    if(trk->x[0] > 0.5f)        //翻转至左侧
                    {
                        trk->objReferPosi--;
                    }
                }
                else                                                        //原先在左侧 
                {
                    if(trk->x[0] < -0.5f)       //翻转至右侧
                    {
                        trk->objReferPosi++;
                    }
                }
                if(trk->objReferPosi <= 1)                                  //原先在前侧
                {
                    if(trk->x[1] > 0)           //翻转至后侧
                    {
                        trk->objReferPosi += 2;
                    }
                }
                else                                                        //原先在后侧 
                {
                    if(trk->x[1] < -0.5f)       //翻转至前侧
                    {
                        trk->objReferPosi -= 2;
                    }
                }
            }
        break;
        default:
        break;   
    }
}

/*
    function: Judge the detection point is secondary reflection or not
    author: Zhang Jian
    data: 2022-3-12
*/
uint8_t isSecondaryReflection(cdi_t* pCdiCur, cdi_t* pCdi, uint16_t idxStart, uint16_t idxEndn, trk_pkg_t *pTrkPkg)
{
    if(RDP_getTrackConfigPointer()->radarResolutionTestMode == TEST_MODE_RESOLUTION
            && pCdiCur->mea_z[0] > 35
            && pCdiCur->mea_z[1] < 5
            && fabsf(pCdiCur->mea_z[2]) < 0.1f
            )
    {
        return false;
    }
    uint16_t i;
    float range = pCdiCur->mea_z[1];
    float rangeHalf = 0.5f*range;
    float angle = pCdiCur->mea_z[3];
    float snr = pCdiCur->mea_z[0];
    float velocity = pCdiCur->mea_z[2];
    float angleThr = RAD2ANG*0.5f/rangeHalf;
    float deltaRange, deltaRange2;
    trk_t* trk;
    if(angleThr < 5)
        angleThr = 5;
    
    //for(i = idxStart; i < idxEndn; i++)
    //{
    //    deltaRange = range - pCdi[i].mea_z[1];
    //    deltaRange2 = rangeHalf - pCdi[i].mea_z[1];
    //    if(((deltaRange > 5.f && deltaRange < 20.f) || (deltaRange2 > -0.25f && deltaRange2 < 1.5f))
    //            && fabsf(angle - pCdi[i].mea_z[3]) < angleThr
    //            && pCdi[i].mea_z[0] - snr > 3.f
    //            && fabsf(pCdi[i].mea_z[2] - velocity) < 1.5f)   // Q：1、近距离谐波角度波动较大，角度门限过小；2、速度门限过小。
    //        return true;
    //}

    //for(i = idxStart; i < idxEndn; i++)
    //{
    //    deltaRange = range - pCdi[i].mea_z[1];
    //    if (fabsf(RDP_getTrackConfigPointer()->speed) < 0.1f
    //        && pCdi[i].mea_z[1] < 5.f
    //        && range >= 1.5f * pCdi[i].mea_z[1]
    //        && deltaRange <= 20.f
    //        && fabsf(pCdi[i].mea_z[2]) > 0.5f
    //        //&& fabsf(angle - pCdi[i].mea_z[3]) < angleThr
    //        && pCdi[i].mea_z[0] - snr > 3.f)
    //    {
    //        return true;
    //    }
    //}
    
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrkPkg->trk[i];
        if(trk->type == NONE
                || (!(trk->status & TRACK_STATUS_MOVING_BMP) && (pCdiCur->status & POINT_STATUS_DYNAMIC_BMP)))
            continue;
        if(trk->sim_z[1] > 10.f)
            continue;
        if(trk->sim_z[1] > 0.3f)
            angleThr = RAD2ANG*0.5f/trk->sim_z[1];
        else
            angleThr = RAD2ANG*0.5f/0.3f;
        if(angleThr < 5)
            angleThr = 5;
        deltaRange = range - trk->sim_z[1];
        deltaRange2 = rangeHalf - trk->sim_z[1];
        if(((deltaRange > 3.f && deltaRange < 20.f) || (deltaRange2 > -0.25f && deltaRange2 < 1.5f))\
            && fabsf(angle - trk->sim_z[3]) < angleThr && (trk->sim_z[0] - snr > 3.f || trk->status & TRACK_STATUS_MOVING_BMP))
            return true;
        
    }
    return false;
}
/*
    function: Judge the detection point is too weak or not
    author: Zhang Jian
    data: 2022-6-30
*/
uint8_t isPointTooWeak(cdi_t* pCdi)
{
    float radarAngleAbs;
    radarAngleAbs = fabsf(pCdi->mea_z[3] - grdpInstallAzimuthAngle);
    if(radarAngleAbs < 35)
    {
        if(pCdi->mea_z[1] < 6.5f)
        {
            if(pCdi->mea_z[0] < -55)
                return true;
            else
                return false;
        }else if(pCdi->mea_z[1] < 12)
        {
            if(pCdi->mea_z[0] < -60)
                return true;
            else
                return false;
        }
    }else
    {
        if(pCdi->mea_z[1] < 6.5f)
        {
            if(pCdi->mea_z[0] < -61)
                return true;
            else
                return false;
        }else if(pCdi->mea_z[1] < 12)
        {
            if(pCdi->mea_z[0] < -65)
                return true;
            else
                return false;
        }
    }
    return false;
}
/*
    function: Judge the Candi is associated to a stable track or not
    author: Pan Yunfei
    data: 2023-3-20
*/
uint8_t isAssociate2Track(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg, int candiIdx, float vspeed)
{
    int i;
    trk_t* trk = pTrkPkg->trk;
    cdi_t* cdi = pCdiPkg->cdi;
    float valRange,valCross,valVelo1,valVelo2,valAngle,valLong,crossThr,longThr,headingAngle;
    rdp_config_t* config = RDP_getTrackConfigPointer();
    if(fabsf(vspeed) > 0.5f && (cdi[trk[candiIdx].idx_1].status & POINT_STATUS_DYNAMIC_BMP) == 0 && !(trk[candiIdx].status & TRACK_STATUS_MOVING_BMP))		//自车运动时的静态目标不考虑关联
	{
		return false;
	}
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if(trk[i].type == TRACK && trk[i].idx_1 != POINT_ID_CLUSTERED && (trk[i].status & (TRACK_STATUS_MOVING_BMP | TRACK_STATUS_EVER_NONCROSSED_MOVED_BMP)) != 0)
        {
            if (trk[i].objType == 0x01) {
                // 默认行人目标不会分裂
                continue;
            }
			// 根据航迹类型适当调整合并阈值
            float costThr = 1.0f;
            if (trk[i].objType == 0x02) {
                costThr = 0.5f;
			}
            valRange = fabsf(trk[i].sim_z[1] - cdi[trk[candiIdx].idx_1].mea_z[1]);
            if(valRange > TRACK_ASSOC_DELTAR_RADIAL_THR * costThr)
            {
                continue;		//径向距离门限不满足
            }
            //针对距离分辨率模式下修改，否则额如果曾经拿着运动过的角反目标。会导致另外一个角反一直起不来
            if(valRange > 0.3f && !(cdi[trk[candiIdx].idx_1].status & POINT_STATUS_DYNAMIC_BMP) && RDP_getTrackConfigPointer()->radarResolutionTestMode == TEST_MODE_RESOLUTION)
            {
                continue;		//径向距离门限不满足
            }
            valAngle = fabsf(trk[i].sim_z[3] - cdi[trk[candiIdx].idx_1].mea_z[3]);
            if(cdi[trk[candiIdx].idx_1].mea_z[1] > 12.0f && valAngle > (TRACK_ASSOC_ANGLE_THR * costThr))
            {
                continue;		//远处目标角度门限不满足
            }
            if(trk[i].idx_1 >= 0)
            {
                valVelo1 = fabsf(cdi[trk[i].idx_1].mea_z[2] - cdi[trk[candiIdx].idx_1].mea_z[2]);
                valVelo2 = fabsf(trk[i].sim_z[2] - cdi[trk[candiIdx].idx_1].mea_z[2]);
            }
            else if(trk[i].sim_z[3] > -15.0f)
            {
                valVelo1 = fabsf(trk[i].sim_z[2] - cdi[trk[candiIdx].idx_1].mea_z[2]);
                valVelo2 = fabsf(trk[i].stored_last_z[0][2] - cdi[trk[candiIdx].idx_1].mea_z[2]);
            }
            else
            {
                valVelo1 = 0;      //异侧目标速度检测不好，失关联时不比较速度
                valVelo2 = valVelo1;
            }
            if(valVelo1 > TRACK_ASSOC_VELOCITY_THR * costThr && valVelo2 > TRACK_ASSOC_VELOCITY_THR * costThr && valAngle < TRACK_ASSOC_ANGLE_THR * costThr)
            {
                continue;		//速度门限不满足
            }
            headingAngle = atan2f(trk[i].x[2], trk[i].x[3] - vspeed) * RAD2ANG;
            if(!(trk[i].status & TRACK_STATUS_MOVING_BMP))
            {
                headingAngle = 180.0f;
            }
            valCross = fabsf(trk[i].x[0] - cdi[trk[candiIdx].idx_1].x);
            crossThr = TRACK_ASSOC_DELTAR_DIST_MIN + TRACK_ASSOC_DELTAR_DIST_EXPAND * fabsf(sinf(headingAngle * ANG2RAD));
            valLong = fabsf(trk[i].x[1] - cdi[trk[candiIdx].idx_1].y);
            longThr = TRACK_ASSOC_DELTAR_DIST_MIN + TRACK_ASSOC_DELTAR_DIST_EXPAND * fabsf(cosf(headingAngle * ANG2RAD));
            if(fabsf(headingAngle) > 40.0f && fabsf(headingAngle) < 140.0f && fabsf(trk[i].x[2]) > 0.5f)
            {
                crossThr += 1.0f;   //横穿目标增加聚类门限，防止分裂
                longThr += 1.0f;
            }
            //if(trk[i].sim_z[2] > 0 || fabsf(vspeed) > 0.2f)

            if(config->radarResolutionTestMode != TEST_MODE_RESOLUTION)
            {
                crossThr += 0.5f + 0.5f * fabsf(sinf(headingAngle * ANG2RAD));   //远离目标增加聚类门限，防止分裂
                longThr += 0.5f + 0.5f * fabsf(cosf(headingAngle * ANG2RAD));
            }
            crossThr *= costThr;
            longThr *= costThr;
    		if(valCross >= crossThr || valLong >= longThr)
            {      
                if(cdi[trk[candiIdx].idx_1].mea_z[1] <= 25.0f)
                {
                    continue;       //近距离目标横纵向距离门限有一处不满足，直接退出
                }
                else if(valCross >= (TRACK_ASSOC_DELTAR_DIST_MIN + TRACK_ASSOC_DELTAR_DIST_EXPAND) * costThr || valLong >= ((TRACK_ASSOC_DELTAR_DIST_MIN + TRACK_ASSOC_DELTAR_DIST_EXPAND) * 0.6f) * costThr)
                {
                    continue;       //远距离目标检测点横飘现象较重，增做防横飘聚类
                }
            }        
            if(trk[i].idx_1 == FILTER_NON_ASSOC && trk[i].miss >= 3 && (trk[candiIdx].status & TRACK_STATUS_MOVING_BMP) && (trk[i].status & TRACK_STATUS_MOVING_BMP))
            {
                trk[i].idx_1 = trk[candiIdx].idx_1;	//主航迹连续丢失关联点，将凝聚成功的CANDI航迹关联点赋给主航迹，改善切ID问题
                if((cdi[trk[candiIdx].idx_1].x - trk[i].x[0]) * trk[i].x[2] < 0)
                {
                    trk[i].x[0] = trk[i].x[0] * 0.5f + cdi[trk[candiIdx].idx_1].x * 0.5f;
                    if (trk->LngSpeedHeadingAngleCnt > 30)
                    {
                        trk[i].x[2] = 0;
                        trk[i].x[4] = 0;
                    }
                }
            }
			return true;		//所有门限都满足
		}
    }
    return false;
}

uint8_t isCoveredByOtherTrack(trk_pkg_t *pTrkPkg, int trkIdx, rdp_config_t *cfg)
{
    int i;
    trk_t* trk = pTrkPkg->trk;
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if(trk[i].type == TRACK && (trk[i].status & (TRACK_STATUS_MOVING_BMP | TRACK_STATUS_EVER_NONCROSSED_MOVED_BMP)) && i != trkIdx)
        {
            float lngdist = cfg->isFront ? trk[i].front : trk[i].back;
            lngdist = lngdist < 4.0f ? 4.0f : lngdist;
            if(fabsf(trk[i].x[0] - trk[trkIdx].x[0]) < 1.5f && trk[trkIdx].x[1] > trk[i].x[1] && trk[trkIdx].x[1] - trk[i].x[1] < lngdist)
            {
                return true;
            }
		}
    }
    return false;
}

/*
    function: Judge the static-to-moving track is closely associated to a stable moving track or not
    author: Pan Yunfei
    data: 2023-8-24
*/
uint8_t isAssociateClose2Track(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg, int candiIdx)
{
    int i;
    trk_t* trk = pTrkPkg->trk;
    cdi_t* cdi = pCdiPkg->cdi;
    float valVelo,valCross,valLong;
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if(trk[i].type == TRACK && trk[i].idx_1 >= 0 && (trk[i].status & TRACK_STATUS_MOVING_BMP))
        {
            valVelo = fabsf(trk[i].sim_z[2] - cdi[trk[candiIdx].idx_1].mea_z[2]);
            if(valVelo > TRACK_ASSOC_CLOSE_VELOCITY_THR)
            {
                continue;		//速度门限不满足
            }
            valCross = fabsf(trk[i].x[0] - cdi[trk[candiIdx].idx_1].x);
            if(valCross >= TRACK_ASSOC_CLOSE_LAT_THR)
            {
                continue;		//横向距离门限不满足
            }
            valLong = fabsf(trk[i].x[1] - cdi[trk[candiIdx].idx_1].y);
            if(valLong >= TRACK_ASSOC_CLOSE_LONG_THR)
            {
                continue;		//纵向距离门限不满足
            }
			return true;		//所有门限都满足
		}
    }
    return false;
}

/*
    function: Judge the Candi is associated to a candi track or not
    author: Wang Qiuying
    data: 2023-4-27
*/
uint8_t isAssociate2Candi(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg, int candiIdx)
{
    int i;
    trk_t* trk = pTrkPkg->trk;
    cdi_t* cdi = pCdiPkg->cdi;
    if((cdi[trk[candiIdx].idx_1].status & POINT_STATUS_DYNAMIC_BMP) == 0)		//静态目标不考虑关联
    {
        return false;
    }
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
       if(i != candiIdx && trk[i].type == CANDI && trk[i].idx_1 >= 0 && cdi[trk[candiIdx].idx_1].groupId2 == cdi[trk[i].idx_1].groupId2)
        {
           if(cdi[trk[candiIdx].idx_1].mea_z[1] > cdi[trk[i].idx_1].mea_z[1])
           {
               return true;
           }
		}
    }
    return false;
}

/*
    function: Judge the Candi is associated by a fake target or not
    author: Pan Yunfei
    data: 2023-3-21
*/
uint8_t isAssociateByFake(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg, int candiIdx)
{
	int i;
    trk_t* trk = pTrkPkg->trk;
    cdi_t* cdi = pCdiPkg->cdi;
    float val;
	if((cdi[trk[candiIdx].idx_1].status & POINT_STATUS_DYNAMIC_BMP) == 0)		//静态目标暂不考虑假点
	{
		return false;
	}
	for(i = 0; i < trk[candiIdx].idx_1; i++)	//根据BeiQi场景测试的动态倍程同向假点特征，筛选到candi关联目标的前一个目标为止
	{
		val = cdi[trk[candiIdx].idx_1].mea_z[1] / cdi[i].mea_z[1];
		if(val < POINT_FAKE_RRATE_MINTHR || val > POINT_FAKE_RRATE_MAXTHR)
		{
			continue;		//距离比值门限不满足
		}
		val = cdi[trk[candiIdx].idx_1].mea_z[1] - cdi[i].mea_z[1];
		if(val > POINT_FAKE_DELTAR_THR)
		{
			continue;		//距离差值门限不满足
		}
		if(fabsf(cdi[i].mea_z[2]) < 0.1f)
		{
			continue;		//参考目标静止时不考虑比较
		}
		val = cdi[trk[candiIdx].idx_1].mea_z[2] / cdi[i].mea_z[2];
		if(val < POINT_FAKE_VRATE_MINTHR || val > POINT_FAKE_VRATE_MAXTHR)
		{
			continue;		//速度比值门限不满足
		}
		val = fabsf(cdi[trk[candiIdx].idx_1].mea_z[2] - cdi[i].mea_z[2]);
		if(val > POINT_FAKE_DELTAV_THR)
		{
			continue;		//速度差值门限不满足
		}
		val = fabsf(cdi[trk[candiIdx].idx_1].mea_z[3] - cdi[i].mea_z[3]);
		if(val > POINT_FAKE_DELTAA_THR)
		{
			continue;		//角度差值门限不满足
		}
        if(cdi[trk[candiIdx].idx_1].groupId2 == cdi[i].groupId2)
        {
            continue;
        }
		return true;		//所有门限都满足
	}
    for(i = 0; i < pCdiPkg->number; i++)	//根据Geely的RCTA测试的动态近程异向假点特征，与所有参考目标进行匹配筛查
    {
        if(i == trk[candiIdx].idx_1)
        {
            continue;
        }
        val = cdi[trk[candiIdx].idx_1].mea_z[1] / cdi[i].mea_z[1];
        if(val < POINT_HORIZMULTI_RRATE_MINTHR || val > POINT_HORIZMULTI_RRATE_MAXTHR)
        {
            continue;		//距离比值门限不满足
        }
        val = cdi[trk[candiIdx].idx_1].mea_z[1] - cdi[i].mea_z[1];
        if(val > POINT_HORIZMULTI_DELTAR_THR)
        {
            continue;		//距离差值门限不满足
        }
        if(val < -1.0f)
        {
            break;			//参考目标比筛查点更远，筛查结束
        }
        if(fabsf(cdi[i].mea_z[2]) < 0.1f)
        {
            continue;		//参考目标静止时不考虑比较
        }
        val = cdi[trk[candiIdx].idx_1].mea_z[2] / cdi[i].mea_z[2];
        if(val < POINT_HORIZMULTI_VRATE_MINTHR || val > POINT_HORIZMULTI_VRATE_MAXTHR)
        {
            continue;		//速度比值门限不满足
        }
        val = fabsf(cdi[trk[candiIdx].idx_1].mea_z[2] - cdi[i].mea_z[2]);
        if(val > POINT_HORIZMULTI_DELTAV_THR)
        {
            continue;		//速度差值门限不满足
        }
        if(cdi[trk[candiIdx].idx_1].groupId2 == cdi[i].groupId2)
        {
            continue;
        }
        return true;		//所有门限都满足
    }

	return false;
}

void objProbOfExistInit(trk_t *trk)
{
    float pointNumAvg = (float)trk->groupPointNumAccum/(float)trk->trkCnt;
    float x0, tauInv;
    float p1;
    if(trk->sim_z[1] < 6.f)
    {
        x0 = 1.5f;
        tauInv = 1.f/1.5f;
    }
    else if(trk->sim_z[1] < 30.f)
    {
        x0 = 1.25f;
        tauInv = 1.f/1.25f;
    }
    else
    {
        x0 = 1;
        tauInv = 1.f;
    }
    if(pointNumAvg < x0)
        p1 = expf((pointNumAvg-x0)*tauInv);
    else
        p1 = 2 - expf((x0-pointNumAvg)*tauInv);
    trk->probOfExist = (uint8_t)((100*0.5f)*p1);
    return;
}

void objProbOfExistUpdate(trk_t *trk, cdi_t* cdi)
{
    float objExistProbHit = 0;
    float objExistProbSimilarity;
    float objExistProbLife;
    float objExistProbPointNum;
    float objExistProbCurrent;
    float alpha;
    uint32_t hitMask = trk->hitBmp & ((1<<LAST_HIT_NUM_MAX)-1);
    float sigmmaRangeNorm, sigmmaRangeRateNorm, sigmmaAngleNorm, sigmmaArcNorm, dAngle, similarity;
    for (uint32_t i = 0; i < LAST_HIT_NUM_MAX; i++)
    {
        if(hitMask & 1U)
            objExistProbHit += probOfExistHitCoeff[i];
        hitMask >>= 1;
    }
    if(cdi)
    {
        sigmmaRangeNorm = (1.f/FILTER_NOISE_STD_RNG)*(trk->pre_z[1] - cdi->mea_z[1]);
        sigmmaRangeRateNorm = (1.f/FILTER_NOISE_STD_VEL)*(trk->pre_z[2] - cdi->mea_z[2]);
        dAngle = fabsf(trk->pre_z[3] - cdi->mea_z[3]);
        sigmmaAngleNorm = (1.f/FILTER_NOISE_STD_ANG)*dAngle;
        sigmmaArcNorm = (1.f/FILTER_NOISE_STD_RNG*ANG2RAD)*trk->pre_z[1]*dAngle;
        sigmmaAngleNorm = sigmmaAngleNorm < sigmmaArcNorm ? sigmmaAngleNorm : sigmmaArcNorm;
        similarity = sqrtf((sigmmaRangeNorm*sigmmaRangeNorm + sigmmaRangeRateNorm*sigmmaRangeRateNorm +sigmmaAngleNorm*sigmmaAngleNorm)*(1.f/3.f));
        objExistProbSimilarity = expf(-similarity*(1.f/6.f));
    }else
    {

        objExistProbSimilarity = expf(-0.5f*(float)trk->miss);
    }
    objExistProbLife = 1.f - 0.5f*expf(-0.1f*(float)trk->activeTrkCnt);
    objExistProbPointNum = 1 - 0.5f*expf(-0.5f*(float)trk->groupPointNumAccum);
    objExistProbCurrent = objExistProbHit*0.5f + objExistProbSimilarity*0.25f + objExistProbLife*0.1f + objExistProbPointNum*0.15f;
    if(trk->activeTrkCnt < 5)
    {
        trk->probOfExist = roundf((trk->probOfExist*(trk->activeTrkCnt+1) + objExistProbCurrent*100)/(trk->activeTrkCnt+2));
    }
    else
    {
        alpha = 0.65f + objExistProbLife*0.2f;
        trk->probOfExist = roundf(trk->probOfExist*alpha + objExistProbCurrent*(1-alpha)*100);
    }
    if(trk->probOfExist > 100)
        trk->probOfExist = 100;
    return;
}

/**
 * @brief 根据点迹更新航迹的速度
 * @param trk 当前航迹指针 
 * @param cdi 航迹关联点迹的指针 
 */
#define ANGLE_BAIS_AXIS_THR 19 // unit: deg
void objVelUpdateBasingPoint(trk_t* trk, cdi_t *cdi)
{
    float angleX, angleY, angleXMin, angleYMin;
    int16_t i, angleXMinId = -1, angleYMinId = -1;
    angleXMin = fabsf(cdi->mea_z[3] - 90);    // 相对x轴正方向角度偏差
    angleYMin = fabsf(cdi->mea_z[3]);    // 相对y轴正方向角度偏差
    for(i = 0; i < STORE_POINT_NUM; i++)
    {
        angleX = fabsf(trk->stored_last_z[i][3] - 90);
        angleY = fabsf(trk->stored_last_z[i][3]);
        if(angleX < angleXMin)
        {
            angleXMin = angleX;
            angleXMinId = i;
        }
        if(angleY < angleYMin)
        {
            angleYMin = angleY;
            angleYMinId = i;
        }
    }
    // update vx basing the point nearest to 90 deg
    if(angleXMin < ANGLE_BAIS_AXIS_THR)
    {
        if(angleXMinId < 0)
        {
            trk->x[2] = cdi->mea_z[2];
        }else
        {
            trk->x[2] = trk->stored_last_z[angleXMinId][2];
        }
    }
    // update vy basing the point nearest to 0 deg
    if(angleYMin < ANGLE_BAIS_AXIS_THR)
    {
        if(angleYMinId < 0)
        {
            trk->x[3] = cdi->mea_z[2];
        }else
        {
            trk->x[3] = trk->stored_last_z[angleYMinId][2];
        }
    }
}

/**
 * @brief 航迹目标的RCS处理
 * @param trk 跟踪航迹 
 * @param cdi 跟踪航迹的量测值Cdi
 */
void RDP_TrkObjRcsProcess(trk_t* trk, cdi_t* cdi)
{
    trk->rcs = cdi->rcs;
}

//针对来向二轮车测试起批完的问题，特殊处理起批条件
bool RDP_ObjTestPreTrackProcess(trk_t* trk, cdi_t* cdi)
{
    //只针对特殊的模式下才做该识别
    if(RDP_getTrackConfigPointer()->radarResolutionTestMode != TEST_MODE_WEAK_OBJ)
    {
        return false;
    }

    //2类目标，1、满足二轮车来向起批条件。2、刚正常起批的目标  TRACK_STATUS_PRE_TRECK_OBJ_BMP为这类特殊的目标标记
    if(fabsf(cdi->x) < 2 && cdi->mea_z[2] < -0.1f && trk->trkCnt < 20 && trk->x[1] > 15 && trk->x[1] < 75)
    {
        trk->status |= TRACK_STATUS_PRE_TRECK_OBJ_BMP;
        return true;
    }
    else
    {
        return false;
    }
}

#define IN_2METERS_RANGE(r, rMin, rMax) ((r < rMin && r > rMin - 2.f) || (r > rMax && r < rMax + 2.f))
uint8_t isSuspectedPedestrianInCPTALF(cdi_t* cdi, cdi_pkg_t* pCdiPkg, trk_t *pTrk)
{
	cdi_t *pCdi = pCdiPkg->cdi;
	uint8_t rst = 0, tFlag = 0, tCnt = 0;

	for (uint32_t j = 0; j < pCdiPkg->number; j++)
	{
		if (j == pTrk->idx_1 || pCdi[j].groupId == cdi->groupId)
			continue;

		if (IN_2METERS_RANGE(pCdi[j].x, cdi->latLim[0], cdi->latLim[1]) && IN_2METERS_RANGE(pCdi[j].y, cdi->lngLim[0], cdi->lngLim[1]))
		{
			tCnt++;
			if (tCnt >= 3)
			{
				tFlag = 1;
				break;
			}
		}
	}

	if (!tFlag)
	{
		pTrk->isSuspPedestrianCnt = (pTrk->isSuspPedestrianCnt < 4) ? (pTrk->isSuspPedestrianCnt + 1) : pTrk->isSuspPedestrianCnt;
		rst = (pTrk->isSuspPedestrianCnt >= 3) ? 1 : 0; // 延迟一帧，防止个别帧检测点异常
	}
	else
	{
		pTrk->isSuspPedestrianCnt = (pTrk->isSuspPedestrianCnt > 0) ? (pTrk->isSuspPedestrianCnt - 1) : pTrk->isSuspPedestrianCnt;
	}

	return rst;
}

//判断TRACK动静属性,无论CANDI/TRK目标，连续三帧判断关联到运动点，即判断为运动目标
static void judgeTrkDynamic(cdi_pkg_t* pCdiPkg, trk_pkg_t *pTrkPkg, trk_t *pTrk, VDY_DynamicEstimate_t *pRDP_inVehicleData, TrackGroupInfo_t* pGroupInfo, int idx)
{
    cdi_t *cdi = pCdiPkg->cdi;
    rdp_config_t *config = RDP_getTrackConfigPointer();
	const stVehicleStatus *pVdy = getVdyStatus();
	uint16_t activeCntThr = fabsf(pRDP_inVehicleData->vdySpeedInmps) > (25 / 3.6f) ? 15 : 30;
	sideLine_pkr_t* pSideLinePkg = &pTrkPkg->sideLines;

    if (pTrk->type == NONE)
    {
        return;
    }
    if (!(pTrk->status & TRACK_STATUS_MOVING_BMP))
    {
        if (pTrk->idx_1 != FILTER_NON_ASSOC)
        {
            if (isCPTALFScene
                && fabsf(pRDP_inVehicleData->vdySpeedInmps) < (35 / 3.6f)
                && !(pTrk->status & TRACK_STATUS_GUARDRAIL_BMP)
                //&& pTrk->activeTrkCnt > activeCntThr
                && ((pTrk->startPosition[0] > 0.9f + LANE_WIDTH * 2 && pTrk->startPosition[1] > 15.f) || (pTrk->x[1] < 5.f && pTrk->x[1] > 1.f))
                && pTrk->x[0] > 0.f && pTrk->x[1] > 0.f && pTrk->x[1] < 30.f) // 作用区域暂定
            {
                ///判断疑似行人目标
                pTrk->isSuspPedestrian = isSuspectedPedestrianInCPTALF(&cdi[pTrk->idx_1], pCdiPkg, pTrk);
            }

            if (gGroupStatus[cdi[pTrk->idx_1].groupId2] & POINT_STATUS_DYNAMIC_BMP)
            {
                //法线附近的低径向速度被遮挡点，不允许变红
                if(RDP_TRACK_inShelterArea(&cdi[pTrk->idx_1], pCdiPkg)
                    && cdi[pTrk->idx_1].mea_z[1] > 3.f
                    && fabsf(cdi[pTrk->idx_1].mea_z[2]) < 0.15f
                    && fabsf(cdi[pTrk->idx_1].mea_z[3]) > 85.f && fabsf(cdi[pTrk->idx_1].mea_z[3]) < 95.f)
                {
                    ;
                }
                else if (fabsf(cdi[pTrk->idx_1].mea_z[2]) < 0.01f && fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f)
                {
                    ;
                }
				else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
					&& pTrk->type == TRACK && !(pTrk->status & TRACK_STATUS_MOVED_BMP)
					&& IN_NEIGHBOUR_TWO_LANE(pTrk->x[0]) && pTrk->x[1] < 3.f
					&& isTruckMultipath(pTrkPkg, idx))
				{
					;/// 近处两车道内疑似二次谐波点引起的静止点聚类组动静属性异常
				}
				else if (pRDP_inVehicleData->vdyGearState == 2 && fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && pTrk->activeTrkCnt > 10 && pTrk->sim_z[1] < 15.f)
				{
					// 风吹动树木
					if (cdi[pTrk->idx_1].mea_z[0] < 35.f || pTrk->snrAvg < 30.f)
					{
						/// 受否存在风险，看下行人等弱目标
						;
					}
					else if (staticCdiCntAround(pCdiPkg, pTrk->idx_1) > 5)
					{
						pTrk->movingHit = pTrk->movingHit > 0 ? pTrk->movingHit - 1 : 0;
					}
                    else
					{
						pTrk->movingHit = pTrk->movingHit < 10 ? pTrk->movingHit + 1 : 10;
					}
				}
                else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.05f && fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.25f
                    && fabsf(pRDP_inVehicleData->vdySpeedInmps) < (gLastFrameVehicleAbsSpeed + 1e-6f)
                    && pTrk->activeTrkCnt > 30
                    && fabsf(pRDP_inVehicleData->vdySpeedInmps * cosf(cdi[pTrk->idx_1].mea_z[3] * ANG2RAD) - cdi[pTrk->idx_1].mea_z[2]) < 0.5f)
                {
                    // 考虑低速驶停场景，车身的抖动或阻位器的影响导致的车速与行进方向不一致，进而导致检测点的动静属性异常情况
                    ;
                }
                else
                {
                    pTrk->movingHit = pTrk->movingHit < 10 ? pTrk->movingHit + 1 : 10;
                }

				float tempSideLines = getSideLineDis(cdi[pTrk->idx_1].y, pSideLinePkg);
                if (!(cdi[pTrk->idx_1].status & POINT_STATUS_DYNAMIC_BMP)
                    && !pTrk->isSuspPedestrian
                    && ((fabsf(pRDP_inVehicleData->vdyCurveRadius) < 50.f && fabsf(pRDP_inVehicleData->vdySpeedInmps) > (5 / 3.6f))
                        || cdi[pTrk->idx_1].status & POINT_STATUS_GUARDRAIL_BMP
                        || (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 4.f && getVerticalStaticPointNum(cdi, pCdiPkg->number, pTrk->idx_1) >= 2)))
                {
                    pTrk->movingHit = 0;
                }
				else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 5.f
					&& pSideLinePkg->strongSildLineValid
					&& pTrk->status & TRACK_STATUS_GUARDRAIL_OUTSIDE_BMP
					&& !(pTrk->status & TRACK_STATUS_MOVED_BMP)
					&& pTrk->activeTrkCnt > 50
					&& cdi[pTrk->idx_1].status & POINT_STATUS_GUARDRAIL_OUTSIDE_BMP)
				{
					// 护栏外的静止目标关联到运动点（可能为护栏内目标的多径假点）
					pTrk->movingHit = 0;
				}
				else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 5.f
					&& pSideLinePkg->strongSildLineValid
					&& !(pTrk->status & TRACK_STATUS_MOVED_BMP)
					&& (pTrk->status & TRACK_STATUS_GUARDRAIL_BMP
						|| cdi[pTrk->idx_1].status & POINT_STATUS_GUARDRAIL_BMP
						|| (fabsf(tempSideLines - cdi[pTrk->idx_1].x) < 0.75f && fabsf(cdi[pTrk->idx_1].mea_z[2]) < 0.1f)))
				{
					// 护栏静止点目标关联到运动点（可能为护栏内目标的多径假点或自车的保杠假点）
					pTrk->movingHit = 0;
				}
				else
				{
					;
				}
            }
			else if (pTrk->isSuspPedestrian
				&& pTrk->activeTrkCnt >  5)
			{
				float cosAngle = cosf(cdi[pTrk->idx_1].mea_z[3] / 180 * M_PI);
				float sinAngle = sinf(cdi[pTrk->idx_1].mea_z[3] / 180 * M_PI);
				float radialVelocityDiff = fabsf(pVdy->vcsLngVel * cosAngle + pVdy->vcsLatVel * sinAngle + cdi[pTrk->idx_1].mea_z[2]);
				if (pTrk->isSuspPedestrian \
					&& (radialVelocityDiff > (1.f / 3.6f)
						|| (radialVelocityDiff > (0.6f / 3.6f) && pTrk->x[0] < 5.f && pTrk->x[0] > 0.f && pTrk->x[1] < 5.f && pTrk->x[1] > 1.f)))// && fabsf(pTrk->x[3] + pVdy->vcsLngVel) > 0.5f) // 转弯场景行人目标在静止目标分支更新的纵向速度不准
				{
					pTrk->movingHit++;
				}
				else
				{
					pTrk->movingHit = pTrk->movingHit > 0 ? pTrk->movingHit - 1 : 0;
				}
			}
            else
            {
                pTrk->movingHit = 0;
                pTrk->isSuspPedestrian = 0;
            }

			//// 特殊场景下的动静判断--护栏旁跟车目标，90°附近检测点径向速度较小会被判为静止状态
			//if (pTrkPkg->sildLineValid
			//	&& fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f
			//	&& cdi[pTrk->idx_1].status & POINT_STATUS_IN_DEADZONE_BMP
			//	&& fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.f
			//	&& pTrkPkg->sildLineDis - pTrk->x[0] > 0.75f
			//	&& fabsf(pTrk->x[2]) < 1.f
			//	&& pTrk->trkCnt > 3)
			//{
			//	pTrk->movingHit = pTrk->movingHit < 10 ? pTrk->movingHit + 1 : 10;
			//}
        }
        if (pTrk->movingHit >= 3)// || (pTrk->type == TRACK && pTrk->movingHit > 0 && pTrk->x[0] > 0.8 && pTrk->x[0] < 3 && pTrk->x[1] < 2 && pTrk->x[1] > -3 && fabsf(pTrk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 3))
        {
			//转弯时，严格限制运动点转换
			if(fabsf(pRDP_inVehicleData->vdyCurveRadius) > 80.f || (pTrk->movingHit >= 5) || isCPTALFScene)
				pTrk->status |= (TRACK_STATUS_MOVING_BMP | TRACK_STATUS_MOVED_BMP);
        }
    }
    else
    {
		float otgVelX;
		if (config->installPosition == 5 || config->installPosition == 7)
		{
			otgVelX  = pTrk->x[2] - pVdy->vcsLatVel;
		}
		else
		{
			otgVelX = pTrk->x[2] + pVdy->vcsLatVel;
		}
        float otgVelY = pRDP_inVehicleData->vdySpeedInmps *cosf(pRDP_inVehicleData->vdyYawRate * ANG2RAD) - pTrk->x[3];
        otgVelY = config->isFront ? -otgVelY : otgVelY;

        if (pTrk->idx_1 != FILTER_NON_ASSOC)
        {
            float otgVel = pRDP_inVehicleData->vdySpeedInmps * cosf(cdi[pTrk->idx_1].mea_z[3] * ANG2RAD) - cdi[pTrk->idx_1].mea_z[2];
            if (gGroupStatus[cdi[pTrk->idx_1].groupId2] & POINT_STATUS_DYNAMIC_BMP)
            {
				pTrk->movingHit = 3;
            }
			//在大转弯时候，对于已经存在60帧的运动点，如果它的纵向速度比较大，则不让其变为静止点 （防止转弯的时候，大角度附近运动点变成静止点）
			else if (fabsf(pRDP_inVehicleData->vdyCurveRadius) < 50 && pTrk->hit > 60 && (fabsf(otgVelX) > 0.5 || fabsf(otgVel) > 0.5 || fabsf(pTrk->x[3]) > 2))
			{
				;
			}
            else if (!(gGroupStatus[cdi[pTrk->idx_1].groupId2] & POINT_STATUS_STATIC_UNCERTAIN_BMP) 
                || cdi[pTrk->idx_1].status & (POINT_STATUS_GUARDRAIL_BMP | POINT_STATUS_GUARDRAIL_OUTSIDE_BMP)
                || !(cdi[pTrk->idx_1].status & POINT_STATUS_DYNAMIC_BMP))
            {
                if (fabsf(otgVelX) < 0.5f && fabsf(otgVelY) < 0.5f && fabsf(otgVel) < 0.5f)
                {
                    pTrk->movingHit = (pTrk->movingHit > 0) ? (pTrk->movingHit - 1) : pTrk->movingHit;
                    if (pTrk->movingHit == 0)
                    {
                        pTrk->status &= ~(TRACK_STATUS_MOVING_BMP);
                    }
                }
                else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && fabsf(cdi[pTrk->idx_1].mea_z[2]) < 0.01f && pTrk->staticStatusCnt > 10)
                {
                    // 自车静止，当前帧关联点速度为0且连续10帧均关联0速点，则将跟踪点属性置静止
                    // 关联场景：驶停目标滤波速度一直未收敛至上述0.5m/s门限内
                    pTrk->movingHit = 0;
                    pTrk->status &= ~(TRACK_STATUS_MOVING_BMP);
                }
				else
				{
					pTrk->movingHit = 3;
				}
            }
        }
        else
        {
            if (fabsf(otgVelX) < 0.5f && fabsf(otgVelY) < 0.5f)
            {
				if(pTrk->movingHit > 0 )
					pTrk->movingHit -= 1;
				else
					pTrk->status &= ~(TRACK_STATUS_MOVING_BMP);
            }
        }
    }
}

//斜停8度时，护栏外会出现真实目标多径，向内横漂误触功能;
//超车或被超车时，法线附近会出现真实目标的二次谐波，导致小车分裂
static bool judgeMultiPathObj(cdi_t *cdi, VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_pkg_t *pTrkPkg, cdi_pkg_t *pCdiPkg)
{
    trk_t *trk;
    cdi_t *cdiTemp;
    bool rst = false;
    rdp_config_t* config = RDP_getTrackConfigPointer();
    float carVel = config->isFront == 0 ? pRDP_inVehicleData->vdySpeedInmps : -pRDP_inVehicleData->vdySpeedInmps;

    for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrkPkg->trk[i];
        if (trk->type != TRACK || trk->idx_1 == -1 || !(trk->status & TRACK_STATUS_MOVING_BMP))
        {
            continue;
        }
        //已经起批的跟踪点的关联点
        cdiTemp = &pCdiPkg->cdi[trk->idx_1];
        //仅用于十米外的假点判断
        float vy = cdi->y > 10.f ? cdi->mea_z[2] / cosf(cdi->mea_z[3] * DEG2RAD) : 0;

        if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
            && fabsf(cdiTemp->mea_z[1] - cdi->mea_z[1]) < 1.f && fabsf(cdiTemp->mea_z[2] - cdi->mea_z[2]) < 1.f
            && fabsf(cdi->mea_z[2]) > 5.f && cdi->groupId2 != cdiTemp->groupId2
            && cdi->x > cdiTemp->x && cdi->x > 10.f
            && fabsf(cdi->mea_z[3] - cdiTemp->mea_z[3]) > 55.f && fabsf(cdi->mea_z[3] - cdiTemp->mea_z[3]) < 65.f)
        {
            rst = true;
            break;
        }
        //自车运动时，法线附近存在运动点，则该运动目标的 横向距离外&&纵向距离内区域内的目标不允许起批
        else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
            && fabsf(trk->x[1]) < 1.f && (cdi->y < trk->x[1] || cdi->y < cdiTemp->y)
            && (cdi->x > trk->x[0] || cdi->x > cdiTemp->x)
            && cdi->x < 3 * cdiTemp->x)
        {
            rst = true;
            break;
        }
        //speed = 2 * vel + carspeed速度特征的假点
        else if (fabsf(carVel) > 10.f && cdi->y > cdiTemp->y && cdi->x * cdiTemp->x > 0
            && cdi->y > 20.f && config->isFront == 1
            && fabsf(vy - (2 * trk->x[3] + carVel)) < 2.f)
        {
            rst = true;
            break;
        }
    }
    return rst;
}

//根据原始点的速度还原为纵向速度，判断目标是否为对地来向目标
static bool isOnComingObj(trk_t *trk, cdi_t *cdi, cdi_pkg_t *pCdipkg, VDY_DynamicEstimate_t *pRDP_inVehicleData)
{
    bool rst = false;
    rdp_config_t* config = RDP_getTrackConfigPointer();
    GTrack_ListElem* pointElem;
    int16_t pointId;
    cdi_t *pCdi2;

    //航迹起批横向位置须在邻车道，防止RCTA被误判
    if (config->isFront == 0
		&& (pRDP_inVehicleData->vdySpeedInmps > 0.1f || fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f)
        && cdi->x > 0.75f && trk->startPosition[0] > 0.75f)
    {
        if (fabsf(trk->startPosition[1]) < 0.3f && trk->startPosition[1] > -3.f && fabsf(cdi->mea_z[3] - 90.f) < 1.5f)
        {
            trk->onComingObjCnt++;
        }
        else if (trk->startPosition[1] < 5.f && trk->startPosition[1] > -5.f)
        {
            pointElem = gtrack_listGetFirst(&gGroupInfo[cdi->groupId].pointList);
	        while (pointElem)
	        {
		        pointId = pointElem->data;

                pCdi2 = &pCdipkg->cdi[pointId];
                
                if (fabsf(pCdi2->mea_z[3] - 90.f) > 1.f
                    && pCdi2->mea_z[2] / cosf(pCdi2->mea_z[3] * DEG2RAD) > pRDP_inVehicleData->vdySpeedInmps + 0.5f)
                {
                   trk->onComingObjCnt++;
                   break;
                }
		        pointElem = gtrack_listGetNext(pointElem);
	        }
        }
    }
    if (trk->onComingObjCnt >= 2)
    {
        rst = true;
    }
    return rst;
}

/**
 * @brief 航迹管理，包括航迹的计分，升级，删除等
 * @param pCdiPkg RDP内部用于跟踪的原始点列表全局变量指针 
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 */
#define RCS_THRESHOLD_IN_CANDI			0 * 10
#define RCS_THRESHOLD_TO_TRACK			6 * 10
#define AVGSNR_THRESHOLD_TO_TRACK		30
#define AVGRCS_THRESHOLD_TO_TRACK		-10 * 10    // RCS优化后的门限//#define AVGRCS_THRESHOLD_TO_TRACK -18 * 10
#define AVGRCS_THRESHOLD_TO_TRACK_MIN   -20 * 10    // RCS优化后的门限//#define AVGRCS_THRESHOLD_TO_TRACK -18 * 10
#define AVGSNR_THRESHOLD_MINUS_Y		30
#define SNR_THRESHOLD_MINUS_Y			35  
void RDP_Track_manageTracks(cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *pRDP_inVehicleData, TrackGroupInfo_t* pGroupInfo, trk_pkg_t *pTrkPkg, float time)
{
    int i,j;
    trk_t* trk;
    cdi_t* cdi = pCdiPkg->cdi;
    rdp_config_t* config = RDP_getTrackConfigPointer();
	sideLine_pkr_t* pSideLinePkg = &pTrkPkg->sideLines;

    //存储历史20帧的本车距离运动信息（本车车速*时间）
    memmove((ghostCarFrameMoveRange + 1), ghostCarFrameMoveRange, sizeof(float) * (STORE_RANGE_FRAME_NUM - 1));
    ghostCarFrameMoveRange[0] = pRDP_inVehicleData->vdySpeedInmps * time;

    //计算历史20帧本车移动距离
    uint8_t k;
    float hostCarMoveRange = 0.0f;
    for(k = 0; k < STORE_RANGE_FRAME_NUM; k++)
    {
        hostCarMoveRange += ghostCarFrameMoveRange[k];
    }

    /**
     * @brief update candidate
     */
	curFrameMovedTargetCnt = 0;
	forwardMovingTarget = 0;
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrkPkg->trk[i];
        trk->trkCnt = (trk->trkCnt < UINT16_MAX) ? trk->trkCnt + 1 : trk->trkCnt;
        trk->hitBmp <<= 1;
        trk->hitBmp = (trk->idx_1 != FILTER_NON_ASSOC) ? trk->hitBmp + 1 : trk->hitBmp;

        judgeTrkDynamic(pCdiPkg, pTrkPkg, trk, pRDP_inVehicleData, pGroupInfo, i);
        trk->ObjAssoDetIdx = (trk->idx_1 >= 0) ? (pCdiPkg->cdi[trk->idx_1].DetIdx) : -1;
		// 统计三车道内的moved目标
		if (trk->status & TRACK_STATUS_MOVED_BMP && IN_THREE_LANES(trk->x[0]) && trk->x[1] < 70.f)
		{
			curFrameMovedTargetCnt++;
		}
		// 判断自车正前方是否存在运动目标
		if (trk->status & TRACK_STATUS_MOVING_BMP && trk->activeTrkCnt > 12 && IN_HONE_LANE(trk->x[0]) && trk->x[1] < 30.f)
		{
			forwardMovingTarget = 1;
		}

        //debug info
        if (trk->type == CANDI)
        {
            if (trk->idx_1 != FILTER_NON_ASSOC)
            {
                if (trk->idx_1 == POINT_ID_CLUSTERED)
                {
                    trk->type = NONE;
                }
                else if(cdi[trk->idx_1].status&POINT_STATUS_CLUSTERED_BMP)
                {
                    trk->type = NONE;
                }
                else if(RDP_TRACK_inShelterArea(&cdi[trk->idx_1], pCdiPkg) && cdi[trk->idx_1].mea_z[0] < 35.f)
                {
                    trk->type = NONE;
                }
                else if(isSecondaryReflection2(&cdi[trk->idx_1], pTrkPkg, pCdiPkg, pRDP_inVehicleData))
                {
                    trk->type = NONE;
                }
                else if (judgeMultiPathObj(&cdi[trk->idx_1], pRDP_inVehicleData, pTrkPkg, pCdiPkg))
                {
                    trk->type = NONE;
                }
				else if (RDP_multipathScene(pTrkPkg, pCdiPkg->cdi, i))
				{
					trk->type = NONE;
                }
                else if (judgeFenceHarmonic(&cdi[trk->idx_1], trk))
                {
                    trk->type = NONE;
                }
                //自车在护栏旁运动时，车后方会出现横漂假点
                //当目标初始出现位置在5-25m之内，目标原始点为远离速度；目标横向速度大或关联点为0x40异常点；此类目标不起批
                else if (config->isFront == 0 && pRDP_inVehicleData->vdySpeedInmps > 5.f && pSideLinePkg->strongSildLineValid == 1
                    && (fabsf(trk->x[2]) > 3.f || ((cdi[trk->idx_1].status & POINT_STATUS_ABNORMAL_BMP) && cdi[trk->idx_1].mea_z[1] > 10.f))
                    && trk->startPosition[1] > 5.f && trk->startPosition[1] < 25.f
                    && cdi[trk->idx_1].mea_z[2] > 0)
                {
                    trk->type = NONE;
                }
				//有横向速度的CANI点，关联上了0x40点不起批  （目标横漂）
				else if (trk->type == CANDI \
					&& (fabsf(trk->x[2]) > 0.5f) \
					&& (cdi[trk->idx_1].status & POINT_STATUS_ABNORMAL_BMP) \
					&& !isCPTALFScene)
				{
					trk->type = NONE;
				}
				//else if (fabsf(pRDP_inVehicleData->vdyCurveRadius) < 100.f && cdi[trk->idx_1].mea_z[0] < 30.f) // 大转弯的时候，关联点的SNR太小的，直接不起批
				//{
				//	trk->type = NONE;
				//}
				else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.f && trk->x[0] < -2.f && cdi[trk->idx_1].mea_z[0] < 30.f)  //自车高速运动的时候，异侧关联到的SNR值太小的点直接不起批
				{
					trk->type = NONE;
				}
                
                //自车静止时，异侧的RCTA目标会在雷达同侧出现多径假点，造成功能误报；这种点不新建
                // else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f 
                //     && cdi[trk->idx_1].y < 0 && cdi[trk->idx_1].mea_z[1] < 7.f
                //     && cdi[trk->idx_1].mea_z[2] < 0 && trk->x[2] < -0.5f
                //     && (cdi[trk->idx_1].mea_z[0] < 45.f || cdi[trk->pidStrongest].mea_z[0] < 45.f))
                // {
                //     trk->type = NONE;
                // }
                //异侧护栏测错角的假点
                else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 15.f
                    && cdi[trk->idx_1].y > 10.f && cdi[trk->idx_1].y < 30.f && cdi[trk->idx_1].x < 0.f
                    && cdi[trk->idx_1].mea_z[3] - config->installAngle < -60.f && cdi[trk->idx_1].mea_z[3] - config->installAngle > -70.f
                    && fabsf(pRDP_inVehicleData->vdySpeedInmps - (cdi[trk->idx_1].mea_z[2] / cosf((cdi[trk->idx_1].mea_z[3] + 60.f) * DEG2RAD))) < 0.5f)
                {
                    trk->type = NONE;
                }
                //else if(isOnComingObj(trk, &cdi[trk->idx_1], pRDP_inVehicleData))
                //{
                //    trk->type = NONE;
                //}
                //自车快速运动时，邻车道以外的纵向距离小的运动点，snravg低于35的不起批
                else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 8.f
                    && fabsf(pRDP_inVehicleData->vdyCurveRadius) > 500.f
                    && trk->x[0] > 0.75f + LANE_WIDTH && trk->x[1] < 2.f
                    && fabsf(cdi->mea_z[2]) < 1.f && trk->snrAvg < 35.f)
                {
                    trk->type = NONE;
                }
				else
                {
                    uint16_t groupId = cdi[trk->idx_1].groupId2;
                    uint16_t cdi2TrkCntThr = CDI2TRACK_CNT_THR;
                    trk->groupPointNumAccum += gMemberNumPerGroup[groupId];
                    if(cdi[trk->idx_1].status & POINT_STATUS_GUARDRAIL_BMP)
                    {
                        if(!ANGLE_IN_CROSS_AREA(cdi[trk->idx_1].mea_z[3], 7))
                        {
                            trk->x[2] = 0;
                            trk->x[3] = pRDP_inVehicleData->vdySpeedInmps;
                        }
                        trk->status |= TRACK_STATUS_GUARDRAIL_BMP;
                    }
                    if(cdi[trk->idx_1].status & POINT_STATUS_GUARDRAIL_OUTSIDE_BMP)
                    {
                        trk->status |= TRACK_STATUS_GUARDRAIL_OUTSIDE_BMP;
                    }
                    if(gGroupStatus[cdi[trk->idx_1].groupId2] & POINT_STATUS_DYNAMIC_BMP)
                    {
                        if(gGroupStatus[cdi[trk->idx_1].groupId2] & POINT_STATUS_REVERSE_BMP && trk->reverseCnt < 10)
                        {
                            trk->reverseCnt++;
                        }
                    }
                    else if(!(gGroupStatus[cdi[trk->idx_1].groupId2] & POINT_STATUS_STATIC_UNCERTAIN_BMP))
                    {
                        ;
                    }
                    if (cdi[trk->idx_1].status & POINT_STATUS_IN_DEADZONE_BMP)
                    {
                        trk->status |= TRACK_STATUS_IN_DEADZONE_BMP;
                    }
                    else
                    {
                        trk->status &= ~TRACK_STATUS_IN_DEADZONE_BMP;
                    }
                    trk->hit++;
                    if(fabsf(cdi[trk->idx_1].mea_z[2]) < 0.1f)
                    {
                        if(trk->vr0_hit < 60000/* && !ANGLE_IN_CROSS_AREA(cdi[trk->idx_1].mea_z[3], 2)*/)        //根据径向速度接近零的程度，分级增加vr0_hit计数
                        {
                            if(fabsf(cdi[trk->idx_1].mea_z[2]) < 0.005f)
                            {
                                trk->vr0_hit += 5;
                            }
                            else if(fabsf(cdi[trk->idx_1].mea_z[2]) < 0.015f)
                            {
                                trk->vr0_hit += 2;
                            }
                            else
                            {
                                trk->vr0_hit += 1;
                            }
                        }

						// 前后帧关联点的横纵向位置基本不变
						if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
							//&& trk->status & TRACK_STATUS_MOVING_BMP
							&& fabsf(cdi[trk->idx_1].x - trk->stored_last_xy[0]) < 0.005f
							&& fabsf(cdi[trk->idx_1].y - trk->stored_last_xy[1]) < 0.005f)
						{
							if (trk->vr0_hit < 60000)
							{
								trk->vr0_hit += 5;
							}
						}
                    }
                    else
                    {
                        if(trk->vr0_hit > 800)
                        {
                            trk->vr0_hit = 800;
                        }
                        else if(trk->vr0_hit >= 5)
                        {
                            trk->vr0_hit -= 5;
                        }
                        else
                        {
                            trk->vr0_hit = 0;
                        }
                    }

                    if (cdi[trk->idx_1].status & (POINT_STATUS_ABNORMAL_BMP | POINT_STATUS_DEBLUR_FAILED))
                    {
                        trk->assoAbnormalCnt++;
                    }

                    cdi2TrkCntThr = trk->status & TRACK_STATUS_MOVING_BMP ? CDI2TRACK_CNT_THR : CDI2TRACK_CNT_STATIC_THR;

                    // 针对地库场景处理
                    if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
                        && RDP_TRACK_inCoverAera(trk->x[0], trk->x[1])
                        && trk->pidStrongest >= 0
                        )
                    {
                        if (trk->hit >= cdi2TrkCntThr)
                        {
                            // 确认帧
                            if (pCdiPkg->cdi[trk->pidStrongest].rcs < RCS_THRESHOLD_IN_CANDI)
                            {
                                trk->type = NONE;
                                continue;
                            }
                            else if (pCdiPkg->cdi[trk->pidStrongest].rcs < RCS_THRESHOLD_TO_TRACK)
                            {
                                continue;
                            }
                            else
                            {
                                ///TODO
                            }

                            continue;
                        }
                        else if (trk->hit < cdi2TrkCntThr && pCdiPkg->cdi[trk->pidStrongest].rcs < RCS_THRESHOLD_IN_CANDI)
                        {
                            // 中间帧
                            trk->type = NONE;
                            continue;
                        }
                        else
                        {
                            ///TODO
                        }
                    }

                    if (trk->pidStrongest >= 0)
                    {
                        RDP_TRACK_updateTrkSlide(&cdi[trk->pidStrongest], trk);
                    }
                    else if (trk->pidNearest >= 0)
                    {
                        RDP_TRACK_updateTrkSlide(&cdi[trk->pidNearest], trk);
                    }
                    else
                    {
                        RDP_TRACK_updateTrkSlide(&cdi[trk->idx_1], trk);
                    }

                    /* 确认航迹时进行SNR和RCS的判断 */
					//// 1、护栏外多径假目标
					//if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
					//	/*&& trk->sim_z[1] < 50.f*/
					//	&& trk->x[1] > 0.f && trk->x[1] < 30.f
					//	&& trk->hit >= cdi2TrkCntThr)
					//{
					//	if ((trk->snrAvg < AVGSNR_THRESHOLD_TO_TRACK || trk->rcsAvg < AVGRCS_THRESHOLD_TO_TRACK) \
					//		&& cdi[trk->idx_1].mea_z[2] < -30.f / 3.6)
					//	{
					//		trk->type = NONE;
					//		continue;
					//	}
					//}
					// 2、灯杆镜像假目标导致的RCTA误报
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
						&& existPoleTarget(pCdiPkg, pTrkPkg, i)
						&& trk->x[0] > 0.9f + LANE_WIDTH && trk->x[1] < 5.f
						&& trk->hit >= cdi2TrkCntThr)
					{
						if (((trk->snrAvg < AVGSNR_THRESHOLD_TO_TRACK || trk->rcsAvg < RCS_THRESHOLD_IN_CANDI) && (cdi[trk->idx_1].mea_z[2] < -20.f / 3.6 || trk->sim_z[2] < -20.f / 3.6))
							|| (trk->rcsAvg < AVGRCS_THRESHOLD_TO_TRACK_MIN && cdi[trk->idx_1].mea_z[2] < -5.f / 3.6))	// RCS小于-20的相对靠近目标直接抑制起批
						{
							trk->type = NONE;
							continue;
						}

                        //斜停8°时，护栏遮挡范围内的来向车辆不起批，防止误判RCTA
                        if ((trk->snrAvg < AVGSNR_THRESHOLD_TO_TRACK || trk->rcsAvg < RCS_THRESHOLD_IN_CANDI)
                            && (trk->startPosition[1] < 0.f || (cdi[trk->idx_1].mea_z[2] > 0 && cdi[trk->idx_1].mea_z[3] < 90.f)))
                        {
                            trk->type = NONE;
							continue;
                        }
					}

                    // 抑制侧边护栏点
                    if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (10.f / 3.6f)
                        && trk->x[0] > 0.f
                        && trk->x[1] < 0.5f && trk->x[1] > -2.f
                        && trk->hit >= cdi2TrkCntThr)
                    {
                        if (trk->snrAvg < AVGSNR_THRESHOLD_MINUS_Y)
                        {
                            trk->suspicious += 1;
							cdi2TrkCntThr = CDI2TRACK_CNT_MAX_THR;
                        }
                        if (cdi[trk->pidStrongest].mea_z[0] < SNR_THRESHOLD_MINUS_Y)
                        {
                            trk->suspicious += 10;
							cdi2TrkCntThr = CDI2TRACK_CNT_MAX_THR;
                        }

                        /*if (trk->startPosition[1] < 0.f && fabsf(trk->x[1] - trk->startPosition[1]) < 0.5f && fabsf(pRDP_inVehicleData->vdyCurveRadius) > 100.f)
                        {
                            trk->suspicious += 100;
							cdi2TrkCntThr = CDI2TRACK_CNT_MAX_THR;
                        }*/
                    }
                    if (pRDP_inVehicleData->vdyGearState != GEAR_SIG_R)
                    {
                        if (isOnComingObj(trk, &cdi[trk->idx_1], pCdiPkg, pRDP_inVehicleData))
                        {
                            cdi2TrkCntThr = CDI2TRACK_CNT_MAX_THR;
                            if (trk->hit >= CDI2TRACK_CNT_MAX_THR && fabsf(cdi[trk->idx_1].mea_z[3] - 90.f) > 1.f)
                            {
                                /*float vy = cdi[trk->idx_1].mea_z[2] / cosf(cdi[trk->idx_1].mea_z[3] * DEG2RAD);*/
                                if (config->isFront == 0
                                    && trk->onComingObjCnt >= 3
                                    && trk->x[3] < pRDP_inVehicleData->vdySpeedInmps)
                                {
                                    trk->type = NONE;
                                    continue;
                                }
                            }
                        }
                    }
                    // 自车快速运动场景，本车道正前方突然起批的目标多为多径假目标
                    if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (10 / 3.6f)
                        && (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f || fabsf(pRDP_inVehicleData->vdyYawRate) < 0.05f)
                        && trk->hit >= cdi2TrkCntThr
                        && IN_HONE_LANE(trk->startPosition[0])
                        && IN_HONE_LANE(trk->x[0])
                        && trk->x[1] < 10.f
                        && trk->snrAvg < 50.f
                        && cdi[trk->idx_1].mea_z[2] < -1.f)
                    {
                        trk->type = NONE;
                        continue;
                    }

                    // 
                    if (trk->hit >= cdi2TrkCntThr && cdi[trk->idx_1].status & POINT_STATUS_ABNORMAL_BMP && (trk->assoAbnormalCnt / (float)trk->trkCnt) > 0.5f)
                    {
                        trk->type = NONE;
                        continue;
                    }
					else if (trk->hit >= cdi2TrkCntThr \
						&& (trk->assoAbnormalCnt / (float)trk->trkCnt) > 0.4f \
						&& trk->type == CANDI && IN_BSD_AREA(trk->x[0], trk->x[1])
						&& config->isFront == 0 && pRDP_inVehicleData->vdyGearState == 4
						&& trk->x[1] < 0.f)
					{
						trk->type = NONE;
						continue;
					}

                    if(trk->sim_z[1] < 6)
                    {
                        if(trk->hit >= cdi2TrkCntThr)
                        {
                            if(trk->hit <= CDI2TRACK_CNT_MAX_THR)
                            {
                                if (trk->hit == CDI2TRACK_CNT_MAX_THR)
                                {
                                    if (((trk->suspicious % 10) >= 5 && ((trk->suspicious / 10) % 10) >= 5)
                                        || (trk->suspicious / 100) >= 5)
                                    {
                                        trk->type = NONE;
                                        continue;
                                    }
                                }

								// 疑似保杠假点限制起批
								if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
									&& trk->status & TRACK_STATUS_MOVING_BMP
									&& trk->vr0_hit >= trk->hit * 5/* && (trk->stored_last_z[0][1] + cdi[trk->idx_1].mea_z[1]) * 0.5f < 0.5f*/)
								{
									trk->type = NONE;
									continue;
								}

                                if(groupId != GROUP_ID_NONE && groupId != GROUP_ID_INVALID && (gMemberNumPerGroup[groupId] > 1 || fabsf(pRDP_inVehicleData->vdySpeedInmps) < 2 || pCdiPkg->cdi[trk->idx_1].mea_z[0] > 40))
                                {
                                    //抑制异侧护栏旁运动假点、弱SNR点
                                    if(cdi[trk->idx_1].x < -3.0f && cdi[trk->idx_1].y < 15.0f && trk->status&TRACK_STATUS_MOVING_BMP 
                                        && (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.2f || fabsf(cdi[trk->idx_1].mea_z[2]) > 1.0f))
                                    {
                                        trk->type = NONE;
                                        continue;
                                    }
									// 抑制90度附近大车目标外侧多径导致的假目标起批
									if (/*fabsf(pRDP_inVehicleData->vdySpeedInmps) > 2.f
										&& */isTruckMultipath(pTrkPkg, i))
									{
										trk->type = NONE;
										continue;
									}
									// 判断附近是否存在已起批的稳定航迹（待起批航迹可能为分裂航迹）
                                    if(isAssociate2Track(pCdiPkg, pTrkPkg, i, pRDP_inVehicleData->vdySpeedInmps) || (trk->sim_z[3] < -30.0f && (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.2f || fabsf(cdi[trk->idx_1].mea_z[2]) > 1.0f)))		//与现有航迹关联性检查
                                    {
                                        continue;
                                    }
                                    trk->type = TRACK;
                                    trk->hit = 0;
                                    objProbOfExistInit(trk);
                                    if(trk->status&TRACK_STATUS_MOVING_BMP)
                                    {
                                        if((fabsf(pRDP_inVehicleData->vdyCurveRadius) > 50 && !ANGLE_IN_CROSS_AREA(cdi[trk->idx_1].mea_z[3], 10))) //
                                        {
                                            //trk->status |= TRACK_STATUS_MOVING_BMP|TRACK_STATUS_MOVED_BMP;
                                            if(trk->reverseCnt > 1)
                                                trk->status |= TRACK_STATUS_REVERSE_BMP;
                                        }
                                        uint16_t histcnt = trk->trkCnt - 1;
                                        float histtime = 0;
                                        if(histcnt > STORE_X0_FRAME_NUM)
                                        {
                                            histcnt = STORE_X0_FRAME_NUM;
                                        }
                                        for(j = 0; j < histcnt; j++)
                                        {
                                            histtime += gRDP_storedFrameTime[j];
                                        }
                                        if (!(trk->status & TRACK_STATUS_VELOCITY_CONFIRMED_BMP))
                                        {
                                            /*trk->x[2] = 1.0f/histtime*(cdi[trk->idx_1].x - trk->stored_last_x0[histcnt-1]);
                                            trk->x[3] = 1.0f/histtime*(cdi[trk->idx_1].y - trk->stored_last_x1[histcnt-1]);*/

                                            //rcta异侧雷达的目标，纵向速度设置为0
                                            if(trk->status & TRACK_STATUS_RFCTA_AREA_OBJ_BMP)
                                            {
                                                trk->x[3] = 0;
                                            }
                                        }
                                        //抑制异侧横向速度较大的异向运动假点
                                        if(cdi[trk->idx_1].x < -3.0f && trk->x[2] < -8.0f && trk->status&TRACK_STATUS_MOVING_BMP)
                                        {
                                            trk->type = NONE;
                                            continue;
                                        }
                                        objVelUpdateBasingPoint(trk, &cdi[trk->idx_1]);
                                    }
                                    else
                                    {
                                        //trk->status &= ~TRACK_STATUS_MOVING_BMP;
                                        if(!ANGLE_IN_CROSS_AREA(cdi[trk->idx_1].mea_z[3], 7))
                                        {
                                            trk->x[2] = 0;
                                            trk->x[3] = pRDP_inVehicleData->vdySpeedInmps;
                                        }
                                    }
                                }
                            }else
                            {
                                trk->type = NONE;
                            }
                        }
                    }
                    else
                    {                        
                        if(trk->hit >= cdi2TrkCntThr || (RDP_ObjTestPreTrackProcess(trk, &cdi[trk->idx_1]) && trk->hit >= 1))
                        {
                            //抑制异侧护栏旁运动假点
                            //if(cdi[trk->idx_1].x < -3.0f && cdi[trk->idx_1].y < 15.0f &&trk->status&TRACK_STATUS_MOVING_BMP
                            //    && (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.2f || fabsf(cdi[trk->idx_1].mea_z[2]) > 1.0f))
                            //{
                            //    trk->type = NONE;
                            //    continue;
                            //}
							// 抑制自车静止场景，后向大车的多径假目标导致的RCTA误报
							if (trk->status & TRACK_STATUS_MOVING_BMP
								&& trk->startPosition[0] > 10.f && trk->x[2] < -5.f
								&& trk->rcsAvg < 0.f
								&& existNoumenonTarget(pTrkPkg, &cdi[trk->idx_1], i))
							{
								trk->type = NONE;
								continue;
							}
							/*
							* 抑制90度附近目标外侧多径导致的假目标起批
							* 不限于大车，小车目标同样存在多径现象，该类假目标基本不影响功能
							*/
							if (//fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.f
								//&& trk->status & TRACK_STATUS_MOVING_BMP
								isTruckMultipath(pTrkPkg, i))
							{
								trk->type = NONE;
								continue;
							}
							// 抑制护栏外或横向较远处的运动目标起批 原始点 cdi[trk->idx_1].x < -6.0f的限制 会导致DOW测试时FOV边缘区域穿行的目标给抑制掉 静止场景暂不限制异侧X值
                            if((trk->status & (TRACK_STATUS_GUARDRAIL_BMP | TRACK_STATUS_GUARDRAIL_OUTSIDE_BMP)
									&& trk->status & TRACK_STATUS_MOVING_BMP
									&& (cdi[trk->idx_1].x > 4.0f || cdi[trk->idx_1].mea_z[1] > 15.0f))
								|| ((fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.2f /*|| fabsf(cdi[trk->idx_1].mea_z[2]) > 1.0f*/)
									&& trk->status & TRACK_STATUS_MOVING_BMP
									&& (cdi[trk->idx_1].x < -20.0f || (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.0f &&  cdi[trk->idx_1].x > 10.0f))))
                            {
                                trk->type = NONE;     
                                continue;
                            }
                            else if(RDP_getTrackConfigPointer()->radarResolutionTestMode != TEST_MODE_RESOLUTION && isAssociate2Track(pCdiPkg, pTrkPkg, i, pRDP_inVehicleData->vdySpeedInmps))		//与现有航迹关联性检查
                            {
                                continue;
                            }
                            else
                            {
                                trk->type = TRACK;
                            }

                            trk->hit = 0;
                            objProbOfExistInit(trk);
                            if(trk->status & TRACK_STATUS_GUARDRAIL_BMP || \
                                cdi[trk->idx_1].status & (POINT_STATUS_GUARDRAIL_BMP|POINT_STATUS_GUARDRAIL_OUTSIDE_BMP))
                            {
                                if(!ANGLE_IN_CROSS_AREA(cdi[trk->idx_1].mea_z[3], 7))
                                {
                                    trk->x[2] = 0;
                                    trk->x[3] = pRDP_inVehicleData->vdySpeedInmps;
                                }
                            }
                            else if(trk->status & TRACK_STATUS_MOVING_BMP)
                            {
                                //trk->status |= TRACK_STATUS_MOVING_BMP|TRACK_STATUS_MOVED_BMP;
                                //uint16_t histcnt = trk->trkCnt - 1;
                                //float vx0, vy0, vx1, vy1, alpha;
                                //float histtime = 0;
                                //if(histcnt > STORE_X0_FRAME_NUM)
                                //{
                                //    histcnt = STORE_X0_FRAME_NUM;
                                //}
                                //for(j = 0; j < histcnt; j++)
                                //{
                                //    histtime += gRDP_storedFrameTime[j];
                                //}
                                //vx0 = 1.0f/histtime*(cdi[trk->idx_1].x - trk->stored_last_x0[histcnt-1]);
                                //vy0 = 1.0f/histtime*(cdi[trk->idx_1].y - trk->stored_last_x1[histcnt-1]);
                                //if(cdi[trk->idx_1].mea_z[1] < 20.0f || fabsf(cdi[trk->idx_1].mea_z[3]) > 15.0f)   //近距离或非纵向目标，起始速度采用位移计算
                                //{
                                //    trk->x[2] = vx0;
                                //    trk->x[3] = vy0;
                                //}
                                //else    //远距离纵向目标，起始速度根据位置增加经验估计的权重
                                //{
                                //    vx1 = 0;
                                //    vy1 = cdi[trk->idx_1].mea_z[2] / cosf(cdi[trk->idx_1].mea_z[3] / 180.0f * M_PI);
                                //    alpha = (cdi[trk->idx_1].mea_z[1] - 20.0f) / 60.0f + (15.0f - fabsf(cdi[trk->idx_1].mea_z[3])) / 30.0f;
                                //    alpha = alpha < 0 ? 0 : alpha;
                                //    alpha = alpha > 0.9f ? 0.9f : alpha;
                                //    trk->x[2] = vx1 * alpha + vx0 * (1.0f - alpha);
                                //    trk->x[3] = vy1 * alpha + vy0 * (1.0f - alpha);
                                //}
                                //抑制异侧横向速度较大的异向运动假点
                                if(cdi[trk->idx_1].x < -3.0f && trk->x[2] < -8.0f && trk->status & TRACK_STATUS_MOVING_BMP)
                                {
                                    trk->type = NONE;
                                    continue;
                                }
                                if(trk->reverseCnt > 1)
                                    trk->status |= TRACK_STATUS_REVERSE_BMP;
                            }
                            else
                            {
                                //trk->status &= ~TRACK_STATUS_MOVING_BMP;
                                if(!ANGLE_IN_CROSS_AREA(cdi[trk->idx_1].mea_z[3], 7))
                                {
                                    trk->x[2] = 0;
                                    trk->x[3] = pRDP_inVehicleData->vdySpeedInmps;
                                }
                            }
                        }
						else if (trk->dowShelterScene && !isAssociate2Track(pCdiPkg, pTrkPkg, i, pRDP_inVehicleData->vdySpeedInmps))	// 抑制分裂
						{
							// 识别场景后直接起批，并修改运动属性
							trk->type = TRACK;
							trk->movingHit = 3;
							trk->status |= (TRACK_STATUS_MOVING_BMP | TRACK_STATUS_MOVED_BMP);
						}
                    }
                }

                RDP_TrkObjRcsProcess(trk, &(cdi[trk->idx_1]));
            }
            else
            {     
                trk->miss++;  
                if ((trk->trkCnt >= CDI2TRACK_CNT_STATIC_THR) && (trk->trkCnt - trk->hit > 1))
                {
                    trk->type = NONE; 
                }
                //在测试下二轮车和行人靠近的模式下，特殊处理
                else if( config->radarResolutionTestMode == TEST_MODE_WEAK_OBJ
                            && fabsf(trk->x[0]) < 2.0f
                            && trk->x[3] < -0.1f
                            && trk->miss < 5)
                {
                    trk->miss++;
                }
                else if(trk->hit == 0
                        || trk->miss >= 2
                        //|| !(trk->status&TRACK_STATUS_MOVING_BMP)
                        || trk->status & (TRACK_STATUS_GUARDRAIL_BMP | TRACK_STATUS_GUARDRAIL_OUTSIDE_BMP))
                        //|| (trk->sim_z[1] < 40 && fabsf(trk->stored_last_z[0][2]) < 10))
                {
                    trk->type = NONE;       //距离大于40m或径向速度大的运动目标允许miss一次，改善客户静态测试起始跟踪距离不够的问题
                }
            }
        }
    }
    
    /**
     * @brief update track
     */
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)		//CANDI航迹处理完毕后，处理TRACK航迹
    {
        trk = &pTrkPkg->trk[i];
        if(trk->type == TRACK)
        {
            if(trk->activeTrkCnt < UINT16_MAX)
                trk->activeTrkCnt++;
            
            if(trk->activeTrkCnt == 1)		//本帧刚刚转为TRACK
            {
                if(trk->sim_z[3] > 100.0f && trk->x[0] < 2.5f)
                {
                    trk->status |= TRACK_STATUS_STARTFROM_SIDEBLIND_BMP;
                }
                continue;
            }
            if(trk->idx_1 != FILTER_NON_ASSOC)
            {
				//判断一下目标是否是满足预Track目标
                RDP_ObjTestPreTrackProcess(trk, &cdi[trk->idx_1]);
				if (trk->idx_1 == POINT_ID_CLUSTERED)
				{
					trk->type = NONE;
				}
                else
                {
                    trk->miss = 0;
                    if(fabsf(cdi[trk->idx_1].mea_z[2]) < 0.1f)
                    {
                        if(trk->vr0_hit < 60000 && trk->x[0] > 0.f && trk->x[0] < 2.f && trk->x[1] < 2.f/* && !ANGLE_IN_CROSS_AREA(cdi[trk->idx_1].mea_z[3], 2)*/)        //根据径向速度接近零的程度，分级增加vr0_hit计数
                        {
                            if(fabsf(cdi[trk->idx_1].mea_z[2]) < 0.005f && CAL_BOX_LENGTH(cdi->lngLim) < 3.f)
                            {
                                trk->vr0_hit += 5;
                            }
                            else if(fabsf(cdi[trk->idx_1].mea_z[2]) < 0.015f && CAL_BOX_LENGTH(cdi->lngLim) < 3.f)
                            {
                                trk->vr0_hit += 2;
                            }
                            else
                            {
                                trk->vr0_hit += 1;
                            }
                        }
						// 聚类组内存在非0速点
						if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
							&& trk->status & TRACK_STATUS_MOVING_BMP
							&& trk->activeTrkCnt > 15
							&& trk->groupNonzeroVelCnt > 0)
						{
							if (trk->vr0_hit > 800)
							{
								trk->vr0_hit = 800;
							}
							else if (trk->vr0_hit >= 25)
							{
								trk->groupNonzeroVelCnt = (trk->groupNonzeroVelCnt >= 10) ? 10 : trk->groupNonzeroVelCnt;
								trk->vr0_hit -= 5 * (trk->groupNonzeroVelCnt / 2);
							}
                            else
                            {
                                trk->vr0_hit = 0;
                            }
						}

                        if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
							&& trk->status & TRACK_STATUS_MOVING_BMP
							&& trk->activeTrkCnt > 15
                            && CAL_BOX_LENGTH(cdi->lngLim) > 3.f)
                        {
                            if (trk->vr0_hit > 800)
							{
								trk->vr0_hit = 800;
							}
                            else if (trk->vr0_hit >= 5)
                            {
                                trk->vr0_hit -= 5;
                            }
                        }
                        
						//// 前后帧关联点的横纵向位置基本不变
						//if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
						//	&& trk->status & TRACK_STATUS_MOVING_BMP
						//	&& trk->activeTrkCnt > 15
						//	&& fabsf(cdi[trk->idx_1].x - trk->stored_last_xy[0]) > 0.005f
						//	&& fabsf(cdi[trk->idx_1].y - trk->stored_last_xy[1]) > 0.005f)
						//{
						//	if (trk->vr0_hit > 800)
						//	{
						//		trk->vr0_hit = 800;
						//	}
						//	else if (trk->vr0_hit >= 5)
						//	{
						//		trk->vr0_hit -= 5;
						//	}
						//	else
						//	{
						//		trk->vr0_hit = 0;
						//	}
						//}
                    }
                    else
                    {
                        if(trk->vr0_hit > 800)
                        {
                            trk->vr0_hit = 800;
                        }
                        else if(trk->vr0_hit >= 5)
                        {
                            trk->vr0_hit -= 5;
                        }
                        else
                        {
                            trk->vr0_hit = 0;
                        }
                    }

                    if (trk->hit < MAX_CHAR)
					{
                        trk->hit++;
                    }
                    trk->groupPointNumAccum = gMemberNumPerGroup[cdi[trk->idx_1].groupId2];

                    // 更新BSD待切入目标计数
                    if (fabsf(fabsf(pRDP_inVehicleData->vdySpeedInmps) - (50 / 3.6f)) < (5 / 3.6f) && trk->status & TRACK_STATUS_MOVING_BMP)
                    {
                        if (trk->x[0] > 5.f && trk->x[1] > 0.f && trk->x[1] < 5.f && fabsf(trk->x[3]) < (10 / 3.6f))
                        {
                            trk->BSDCutInTargetCnt = (trk->BSDCutInTargetCnt < 0xFF) ? (trk->BSDCutInTargetCnt + 1) : trk->BSDCutInTargetCnt;
                        }
                        else
                        {
                            trk->BSDCutInTargetCnt = (trk->BSDCutInTargetCnt > 0) ? (trk->BSDCutInTargetCnt - 1) : trk->BSDCutInTargetCnt;
                        }
                    }
                    else
                    {
                        trk->BSDCutInTargetCnt = 0;
                    }
                    
					// 护栏标识和护栏外标识
					if(cdi[trk->idx_1].status & POINT_STATUS_GUARDRAIL_BMP)
                    {
                        trk->status |= TRACK_STATUS_GUARDRAIL_BMP;
                    }
                    else
                    {
                        trk->status &= ~TRACK_STATUS_GUARDRAIL_BMP;
                    }
                    if(cdi[trk->idx_1].status & POINT_STATUS_GUARDRAIL_OUTSIDE_BMP)
                    {
                        trk->status |= TRACK_STATUS_GUARDRAIL_OUTSIDE_BMP;
						trk->guardrailOutsideCnt = (trk->guardrailOutsideCnt < 0x7F) ? (trk->guardrailOutsideCnt + 1) : trk->guardrailOutsideCnt;
                    }
                    else if(!(cdi[trk->idx_1].status & POINT_STATUS_GUARDRAIL_BMP))
                    {
                        trk->status &= ~TRACK_STATUS_GUARDRAIL_OUTSIDE_BMP;     //关联点无护栏及护栏外标志位，则取消航迹护栏外标志位
						trk->guardrailOutsideCnt = 0;
                    }
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 5.f
						&& trk->status & TRACK_STATUS_MOVING_BMP
						&& trk->status & TRACK_STATUS_GUARDRAIL_OUTSIDE_BMP
						&& trk->guardrailOutsideCnt > 10)
					{
						// 连续10帧标记护栏外的运动目标直接撤批，防止静止目标被假点带起来后无法消除运动状态
						trk->type = NONE;
						continue;
					}

					// 速度盲区标识
                    if (cdi[trk->idx_1].status & POINT_STATUS_IN_DEADZONE_BMP)
                    {
                        trk->status |= TRACK_STATUS_IN_DEADZONE_BMP;
                    }
                    else
                    {
                        trk->status &= ~TRACK_STATUS_IN_DEADZONE_BMP;
                    }

					// 判断是否为分裂航迹
					if (trk->status & TRACK_STATUS_MOVING_BMP && trk->activeTrkCnt > 10 && trackMerge(pTrkPkg, i))
					{
						trk->type = NONE;
						continue;
					}
                    // 判断是否为镜像目标
                    if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.f && trk->status & TRACK_STATUS_MOVING_BMP && isMirrorTarget(pTrkPkg, i))
                    {
                        trk->type = NONE;
                        continue;
                    }

					//// 判断是否未保杠假点
					//if (trk->status & TRACK_STATUS_MOVING_BMP && IN_BSD_AREA(trk->x[0], trk->x[1]))
					//{
					//	isBumperTarget(pCdiPkg, trk, pRDP_inVehicleData);	// 保杠假目标内部维持不输出
					//}

                    if(trk->status & TRACK_STATUS_MOVING_BMP)
                    {
                        if(cdi[trk->idx_1].status&(POINT_STATUS_DYNAMIC_BMP|POINT_STATUS_DYNAMIC_KEEP_BMP) || gGroupStatus[cdi[trk->idx_1].groupId2]&POINT_STATUS_DYNAMIC_BMP)
                        {
                            if(gGroupStatus[cdi[trk->idx_1].groupId2] & POINT_STATUS_REVERSE_BMP)
                            {
                                if(trk->reverseCnt < 10)
                                {
                                    trk->reverseCnt++;
                                }
                                if(trk->reverseCnt > 2)
                                {
                                    trk->status |= TRACK_STATUS_REVERSE_BMP;
                                }
                            }
                            else
                            {
                                if(trk->reverseCnt > 0)
                                {
                                    trk->reverseCnt--;
                                }
                                if(trk->reverseCnt == 0)
                                {
                                    trk->status &= ~TRACK_STATUS_REVERSE_BMP;
                                }
                            }
                        }

                        if(!ANGLE_IN_CROSS_AREA(trk->sim_z[3], 10) && !(trk->status & TRACK_STATUS_GUARDRAIL_BMP))
                        {
                            trk->status |= TRACK_STATUS_EVER_NONCROSSED_MOVED_BMP;
                        }

						/*
						* 根据目标的横向速度统计直行状态
						* 暂只针对DOW场景或自车低速场景
						*/
						if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 10.f && fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f)
						{
							if (trk->x[2] > 1.5f)
							{
								trk->isLngMovingStatusCnt = (trk->isLngMovingStatusCnt < 120) ? (trk->isLngMovingStatusCnt + 5) : trk->isLngMovingStatusCnt;
							}
							else if (trk->x[2] < -1.5f)
							{
								trk->isLngMovingStatusCnt = (trk->isLngMovingStatusCnt > -120) ? (trk->isLngMovingStatusCnt - 5) : trk->isLngMovingStatusCnt;
							}
							else
							{
								if (trk->x[2] > 0.f)
								{
									trk->isLngMovingStatusCnt = (trk->isLngMovingStatusCnt < 127) ? (trk->isLngMovingStatusCnt + 1) : trk->isLngMovingStatusCnt;
								}
								else
								{
									trk->isLngMovingStatusCnt = (trk->isLngMovingStatusCnt > -127) ? (trk->isLngMovingStatusCnt - 1) : trk->isLngMovingStatusCnt;
								}
							}
						}
                    }
                    /*
                    else if (trk->status & TRACK_STATUS_MOVED_BMP)
                    {
                        //场景：自车运动，位于速度盲区的并行目标(当前状态静止，相对位置满足门限)起步，动静状态异常
                        float disRangeDiff = calcRelativePositonDiff(trk);
                        if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.6f
                            && (trk->status & TRACK_STATUS_IN_DEADZONE_BMP)
                            && disRangeDiff < 0.5f
                            && fabsf(hostCarMoveRange) > 1.5f)
                        {
                            trk->movingCnt = trk->movingCnt + 2;
                        }
                    }
                    */
                    else
                    {
                        if(gGroupStatus[cdi[trk->idx_1].groupId2]&POINT_STATUS_DYNAMIC_BMP)
                        {
                            if(gGroupStatus[cdi[trk->idx_1].groupId2] & POINT_STATUS_REVERSE_BMP && trk->reverseCnt < 10)
                            {
                                trk->reverseCnt++;
                            }
                            else if(!(gGroupStatus[cdi[trk->idx_1].groupId2] & POINT_STATUS_REVERSE_BMP) && trk->reverseCnt > 0)
                            {
                                trk->reverseCnt--;
                            }
                        }
                        else if(!(gGroupStatus[cdi[trk->idx_1].groupId2]&POINT_STATUS_STATIC_UNCERTAIN_BMP) || fabsf(pRDP_inVehicleData->vdySpeedInmps) < 1.5f)
                        {
                            trk->status &= ~TRACK_STATUS_REVERSE_BMP;
                            trk->reverseCnt = 0;
                            if(!(trk->status & TRACK_STATUS_MOVING_BMP))
                            {
                                trk->x[2] = 0;
                                trk->x[3] = pRDP_inVehicleData->vdySpeedInmps;
                            }
                        }
                        else if(!(cdi[trk->idx_1].status & POINT_STATUS_DYNAMIC_KEEP_BMP))   //自车行驶较快，检测点达不到dynamic_keep门限，且UNCERTAIN被置位，movingcnt最多减至-1
                        {
                            trk->status &= ~TRACK_STATUS_REVERSE_BMP;
                        }
                    }                    
                    if(trk->status & TRACK_STATUS_MOVING_BMP)
                    {
                        ;
                    }
                    else
                    {
                        if((fabsf(pRDP_inVehicleData->vdyCurveRadius) > 50 && cdi[trk->idx_1].status & POINT_STATUS_DYNAMIC_CERTAIN_BMP))
                        {
                            if((trk->activeTrkCnt < 20 || !(trk->status &TRACK_STATUS_MOVED_BMP)) && isAssociate2Track(pCdiPkg, pTrkPkg, i, pRDP_inVehicleData->vdySpeedInmps)) //
                            {
                                trk->type = NONE;
                                continue;
                            }
                            if(isAssociateClose2Track(pCdiPkg, pTrkPkg, i))
                            {
                                trk->type = NONE;
                                continue;
                            }
                            //trk->status |= TRACK_STATUS_MOVING_BMP|TRACK_STATUS_MOVED_BMP;
                            if(trk->reverseCnt > 1)
                            {
                                trk->status |= TRACK_STATUS_REVERSE_BMP;
                            }
                            if(trk->vr0_hit > 220)
                            {
                                trk->vr0_hit = 220;
                            }
                        }
                    }
                }
                    //update;
            }
            else
            {
                uint16_t track2noneThr;
                track2noneThr = trk->trkCnt/10 + TRACK2NONE_CNT_THR;
                if(track2noneThr > 10)
                {
                    track2noneThr = 10;
                }
                trk->miss++;
				// 横向速度较大，纵向速度较小的目标，自车运动，前进挡的，连续3帧没有关联上的撤批
				if (curFrameMovedTargetCnt >= 3 \
					&& forwardMovingTarget == 1 \
					&& fabsf(trk->x[2]) > 1.f \
					&& fabsf(trk->x[3]) < 2.f \
					&& trk->x[1] > 5.f \
					&& fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f \
					&& config->vdyGearState == GEAR_SIG_D \
					&& trk->miss > 2)
				{
					trk->type = NONE;
				}

                if(trk->status & TRACK_STATUS_MOVING_BMP)
                {
                    track2noneThr += (uint16_t)(trk->sim_z[1] * 0.05f);
                }
				if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 12.f / 3.6 && trk->bsdTargetMaintainEnd)
				{
					track2noneThr = 5;
				}
                else if (trk->status & TRACK_STATUS_MOVING_BMP && trk->activeTrkCnt > 20 && fabsf(trk->sim_z[3] - config->installAngle) > 65.f)
                {
                    // FOV边界的运动目标提早撤批
                    track2noneThr = 5;
                }
                else
                {
                    ;///
                }

                if(trk->status & TRACK_STATUS_PRE_TRECK_OBJ_BMP && trk->x[1] > 15 && trk->x[1] < 65)
                {
                    //特殊的弱目标目标，加大消批门限
                    if(trk->miss >= 15)
                    {
                        trk->type = NONE;
                    }
                    else
                    {
                        ;//暂时不消批
                    }
                }
                else if(trk->miss >= track2noneThr)
                {
                    trk->type = NONE;
                }
                else if(trk->miss >= 2
                    && ((trk->x[0] < 0.5f && trk->x[1] < 0.5f)
                        //|| (trk->x[0] < 8.0f && trk->x[1] < -1.5f && trk->x[3] < -0.2f && trk->objType != 4)
                        || (trk->x[0] > 3.0f && trk->x[1] < 0.0f && trk->x[2] > 0.0f && trk->x[3] < -0.5f)
                        || (trk->sim_z[1] > 130.0f && trk->x[3] > 0.5f)))
                {
                    trk->type = NONE;
                }
            }

			// 
            updateStaticStatusCnt(trk, &(cdi[trk->idx_1]));
            updateMovingStatusCnt(trk);
			updateLngMoveStatus(trk);
            RDP_TrkObjRcsProcess(trk, &(cdi[trk->idx_1]));
        }
    }

    gLastFrameVehicleAbsSpeed = fabsf(pRDP_inVehicleData->vdySpeedInmps);   // 记录当前帧的车速
}

#define REVERSE_LOW_PRIORITY_RANGE          60.0f
#define REVERSE_MID_PRIORITY_RANGE          30.0f
#define FORWARD_LOW_PRIORITY_XLIMIT_OUT     12.0f
#define FORWARD_LOW_PRIORITY_XLIMIT_IN      (-10.0f)
#define FORWARD_MID_PRIORITY_XLIMIT_OUT     8.0f
#define FORWARD_MID_PRIORITY_XLIMIT_IN      (-5.0f)

/**
 * @brief 按优先级航迹释放，用于生成新的航迹
 * @param cdi_t *cdi 检测点列表起始地址
 * @param uint32_t pid 当前检测点id
 * @param trk_t *trk 目标列表起始地址
 * @param VDY_DynamicEstimate_t *pRDP_inVehicleData RDP内部维护的车身信息全局变量指针
 * @describe    只释放静止目标；先释放CANDI，若无则释放TRACK；起始点本身为低优先级时不予释放
                低优先级(直接释放)：非倒档: 远处车道(横向<-10m或>12m)，倒档: 距离60m以外
                中优先级(无低优先级时释放)：非本邻车道(横向-10~-5m或8~12m, 倒档:  距离30~60m
 */
static uint32_t trackReleaseByPriority(cdi_t *cdi, uint32_t pid, trk_t *trk, VDY_DynamicEstimate_t *pRDP_inVehicleData)
{
    uint32_t i;
    uint32_t midPriId = MAX_NUM_OF_TRACKS;
    uint32_t midPriSetFlag = 0;
    if(pRDP_inVehicleData->vdyDriveDirection)   //倒挡
    {
        if(!(cdi[pid].status & POINT_STATUS_DYNAMIC_BMP) && cdi[pid].mea_z[1] > REVERSE_LOW_PRIORITY_RANGE)
        {
            return MAX_NUM_OF_TRACKS;   //起始点本身为低优先级时不予释放
        }
        for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
        {
            if(trk[i].type == CANDI && trk[i].idx_1 >= 0 && !(cdi[trk[i].idx_1].status & POINT_STATUS_DYNAMIC_BMP))
            {
                if(cdi[trk[i].idx_1].mea_z[1] > REVERSE_LOW_PRIORITY_RANGE)
                {
                    return i;   //低优先级直接释放
                }
                else if(midPriSetFlag == 0 && cdi[trk[i].idx_1].mea_z[1] > REVERSE_MID_PRIORITY_RANGE)
                {
                    midPriId = i;       //中优先级暂存
                    midPriSetFlag = 1;  //避免重复查找
                }
            }
        }
        if(midPriId != MAX_NUM_OF_TRACKS)
        {
            return midPriId;
        }
        for (i = 0; i < MAX_NUM_OF_TRACKS; i++)     //CANDI无可释放资源，搜索TRACK
        {
            if(trk[i].type == TRACK && !(trk[i].status & TRACK_STATUS_MOVED_BMP))
            {
                if(trk[i].sim_z[1] > REVERSE_LOW_PRIORITY_RANGE)
                {
                    return i;   //低优先级直接释放
                }
                else if(midPriSetFlag == 0 && trk[i].sim_z[1] > REVERSE_MID_PRIORITY_RANGE)
                {
                    midPriId = i;       //中优先级暂存
                    midPriSetFlag = 1;  //避免重复查找
                }
            }
        }
        return midPriId;
    }
    else
    {
        if(!(cdi[pid].status & POINT_STATUS_DYNAMIC_BMP) && (cdi[pid].x < FORWARD_LOW_PRIORITY_XLIMIT_IN || cdi[pid].x > FORWARD_LOW_PRIORITY_XLIMIT_OUT))
        {
            return MAX_NUM_OF_TRACKS;   //起始点本身为低优先级时不予释放
        }
        for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
        {
            if(trk[i].type == CANDI && trk[i].idx_1 >= 0 && !(cdi[trk[i].idx_1].status & POINT_STATUS_DYNAMIC_BMP))
            {
                if(cdi[trk[i].idx_1].x < FORWARD_LOW_PRIORITY_XLIMIT_IN || cdi[trk[i].idx_1].x > FORWARD_LOW_PRIORITY_XLIMIT_OUT)
                {
                    return i;   //低优先级直接释放
                }
                else if(midPriSetFlag == 0 && (cdi[trk[i].idx_1].x < FORWARD_MID_PRIORITY_XLIMIT_IN || cdi[trk[i].idx_1].x > FORWARD_MID_PRIORITY_XLIMIT_OUT))
                {
                    midPriId = i;       //中优先级暂存
                    midPriSetFlag = 1;  //避免重复查找
                }
            }
        }
        if(midPriId != MAX_NUM_OF_TRACKS)
        {
            return midPriId;
        }
        for (i = 0; i < MAX_NUM_OF_TRACKS; i++)     //CANDI无可释放资源，搜索TRACK
        {
            if(trk[i].type == TRACK && !(trk[i].status & TRACK_STATUS_MOVED_BMP))
            {
                if(trk[i].x[0] < FORWARD_LOW_PRIORITY_XLIMIT_IN || trk[i].x[0] > FORWARD_LOW_PRIORITY_XLIMIT_OUT)
                {
                    return i;   //低优先级直接释放
                }
                else if(midPriSetFlag == 0 && (trk[i].x[0] < FORWARD_MID_PRIORITY_XLIMIT_IN || trk[i].x[0] > FORWARD_MID_PRIORITY_XLIMIT_OUT))
                {
                    midPriId = i;       //中优先级暂存
                    midPriSetFlag = 1;  //避免重复查找
                }
            }
        }
        return midPriId;
    }
}

/**
 * @brief 航迹起始，将剩余原始点用于生成新的航迹
 * @param pCdiPkg RDP内部用于跟踪的原始点列表全局变量指针 
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 */
#define RCS_THRESHOLD_IN_START 6 * 10
void RDP_Track_startTracks(cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *pRDP_inVehicleData, TrackGroupInfo_t *pGroupInfo, trk_pkg_t *pTrkPkg)
{
    int8_t i;
    uint32_t j;
    uint16_t groupId;
    rdp_config_t* config = RDP_getTrackConfigPointer();
    cdi_t * cdi;

	GTrack_ListObj* groupList;
	GTrack_ListObj* groupNoneList = &pGroupInfo[GROUP_ID_NONE].pointList;
	GTrack_ListElem *pointElemNoneGroup = NULL, *pointElemNoneGroupNext, *pointElemGrouped = NULL;
	short pointId = 0;
	const stVehicleStatus *pVdy = getVdyStatus();

    //之前目标排序从远到近了，这里新点的时候从近的开始取，防止近距离跟踪不上的现象
    for (j = 0, i = 0; j < pCdiPkg->number; j++)
    {
        cdi = &pCdiPkg->cdi[j];
        groupId = pCdiPkg->cdi[j].groupId2;

		////同一组内的原始点，取一个最近点新建CANDI点 （BSD报警中断，大车分裂 5号雷达162900帧，2024-09-18 15-37-06-855/2024-09-18 15-37-06-855.binary）
		//groupList = &pGroupInfo[pCdiPkg->cdi[j].groupId].pointList;
		//pointElemNoneGroup = gtrack_listGetFirst(groupList);
		//while (pointElemNoneGroup)
		//{
		//	pointId = pointElemNoneGroup->data;
		//	if (pCdiPkg->cdi[pointId].mea_z[1] < pCdiPkg->cdi[j].mea_z[1] && pCdiPkg->cdi[j].y > 1 && pCdiPkg->cdi[j].y < 20)
		//	{
		//		pCdiPkg->cdi[j].status |= POINT_STATUS_CLUSTERED_BMP;
		//	}
		//	//else if (pCdiPkg->cdi[j].y < 0 && pCdiPkg->cdi[pointId].x < pCdiPkg->cdi[j].x)
		//	//{
		//	//	pCdiPkg->cdi[j].status |= POINT_STATUS_CLUSTERED_BMP;
		//	//}
		//	pointElemNoneGroup = gtrack_listGetNext(pointElemNoneGroup);
		//}

        //自车静止时，异侧的RCTA目标会在雷达同侧出现多径假点，造成功能误报；这种点不新建
        if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f 
            && cdi->y < 0.f && cdi->mea_z[2] < 0.f && cdi->mea_z[0] < 45.f && cdi->mea_z[1] < 6.f)
        {
            continue;
        }
        //异侧护栏测错角的假点
        else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 15.f 
            && cdi->y > 10.f && cdi->y < 30.f && cdi->x < 0.f
            && cdi->mea_z[3] - config->installAngle < -60.f && cdi->mea_z[3] - config->installAngle > -70.f
            && fabsf(pRDP_inVehicleData->vdySpeedInmps - (cdi->mea_z[2] / cosf((cdi->mea_z[3] + 60.f)* DEG2RAD))) < 0.5f)
        {
            continue;
        }
        else if (judgeMultiPathObj(cdi, pRDP_inVehicleData, pTrkPkg, pCdiPkg))
        {
            continue;
        }
        else if (cdi->status & POINT_STATUS_ABNORMAL_BMP)
        {
            continue;
        }
        if ((pCdiPkg->cdi[j].index == FILTER_NON_ASSOC)
			&& pCdiPkg->cdi[j].valid
			&& !(pCdiPkg->cdi[j].status & (POINT_STATUS_CLUSTERED_BMP | POINT_STATUS_ABNORMAL_BMP))
            && (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.3f
                || pCdiPkg->cdi[j].mea_z[1] > 6.f
                || (groupId != GROUP_ID_NONE && groupId != GROUP_ID_INVALID
                    && (gMemberNumPerGroup[groupId] > 1 || fabsf(pRDP_inVehicleData->vdySpeedInmps) < 2.f || pCdiPkg->cdi[j].mea_z[0] > 40.f))))
        {
            // 自车静止，位于遮挡区内且RCS不满足门限的大速度来向目标不新建航迹
            if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
                //&& pCdiPkg->cdi[j].mea_z[2] < -2.0f
                && RDP_TRACK_inCoverAera(pCdiPkg->cdi[j].x, pCdiPkg->cdi[j].y)
                && pCdiPkg->cdi[j].rcs < RCS_THRESHOLD_IN_START)
            {
                continue;
            }

            gFirstId = gFirstId == MAX_NUM_OF_TRACKS ? 0 : gFirstId;
            for (i = gFirstId; i < MAX_NUM_OF_TRACKS; i++)
            {
                if (pTrkPkg->trk[i].type == NONE)
                {
                    memset(&pTrkPkg->trk[i], 0, sizeof(trk_t));
                    gFirstId = i + 1;
                    pTrkPkg->trk[i].idx_1 = j;
                    pTrkPkg->trk[i].type = CANDI;
                    pTrkPkg->trk[i].hit = 0;
                    pTrkPkg->trk[i].miss = 0;
                    pTrkPkg->trk[i].trkCnt = 1;
                    pTrkPkg->trk[i].hitBmp = 0;
                    pTrkPkg->trk[i].heighAngle = pCdiPkg->cdi[j].heighAngle;
                    pCdiPkg->cdi[j].index = i;
                    pTrkPkg->trk[i].status = 0;
                    // sim
                    pTrkPkg->trk[i].sim_z[0] = pCdiPkg->cdi[j].mea_z[0];
                    pTrkPkg->trk[i].sim_z[1] = pCdiPkg->cdi[j].mea_z[1];
                    pTrkPkg->trk[i].sim_z[2] = pCdiPkg->cdi[j].mea_z[2];
                    pTrkPkg->trk[i].sim_z[3] = pCdiPkg->cdi[j].mea_z[3];
                    pTrkPkg->trk[i].x[0] = pCdiPkg->cdi[j].x;
                    pTrkPkg->trk[i].x[1] = pCdiPkg->cdi[j].y;
                    pTrkPkg->trk[i].x[4] = 0;
                    pTrkPkg->trk[i].x[5] = 0;
                    pTrkPkg->trk[i].rcs = pCdiPkg->cdi[j].rcs;           
                    pTrkPkg->trk[i].startPosition[0] = pTrkPkg->trk[i].x[0];
                    pTrkPkg->trk[i].startPosition[1] = pTrkPkg->trk[i].x[1];
                    float group_vx_vy[2];// 拟合出来的vxvy
                    uint8_t result = 0;// 拟合出来的vxvy是否可用
                    if (!(pCdiPkg->cdi[j].status & POINT_STATUS_GUARDRAIL_BMP))
                    {
                       result = trackVxVyEstFromGroup(pCdiPkg, pRDP_inVehicleData, pCdiPkg->cdi[j].groupId, group_vx_vy); 
                    }

                    if(pCdiPkg->cdi[j].status & POINT_STATUS_GUARDRAIL_BMP)
                    {
						if (pRDP_inVehicleData->vdySpeedInmps * pVdy->vcsLngVel > 0.f)
						{
							pTrkPkg->trk[i].x[2] = pVdy->vcsLatVel;
							pTrkPkg->trk[i].x[3] = pVdy->vcsLngVel;
						}
						else
						{
							pTrkPkg->trk[i].x[2] = -pVdy->vcsLatVel;
							pTrkPkg->trk[i].x[3] = -pVdy->vcsLngVel;
						}
                        pTrkPkg->trk[i].status |= TRACK_STATUS_GUARDRAIL_BMP;
                    }
                    else if (0 !=result) {
                        if (result & SOLVE_GROUR_SUCCESS_VX)
                            pTrkPkg->trk[i].x[2] = group_vx_vy[0];
                        if (result & SOLVE_GROUR_SUCCESS_VY)
                            pTrkPkg->trk[i].x[3] = group_vx_vy[1];
                    }
	                else if(ANGLE_IN_VERTICAL_AREA(pTrkPkg->trk[i].sim_z[3], 15))
	                {
						pTrkPkg->trk[i].x[2] = 0;
						pTrkPkg->trk[i].x[3] = pTrkPkg->trk[i].sim_z[2];
						//pTrkPkg->trk[i].x[2] = 0;
						//pTrkPkg->trk[i].x[3] = 0;
	                }
	                else if(ANGLE_IN_CROSS_AREA(pTrkPkg->trk[i].sim_z[3], 15))
	                {
						//pTrkPkg->trk[i].x[2] = pTrkPkg->trk[i].sim_z[2];
						//pTrkPkg->trk[i].x[3] = 0.f;
						if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.f && cdi->y < 0.5f && cdi->x > 2.f && cdi->x < 0.9f + LANE_WIDTH * 2 && cdi->mea_z[3] > 95.f)
						{
							pTrkPkg->trk[i].x[2] = 0;
							pTrkPkg->trk[i].x[3] = cdi->mea_z[2] / cosf(cdi->mea_z[3] * ANG2RAD);
							pTrkPkg->trk[i].x[3] = (fabsf(pTrkPkg->trk[i].x[3]) > 15.f) ? 15.f : pTrkPkg->trk[i].x[3]; // 限制最大速度，防止关联点异常导致纵向速度计算异常
						}
						else if (pTrkPkg->trk[i].x[0] > 20.f)
						{
							/// 横穿目标
							pTrkPkg->trk[i].x[2] = pTrkPkg->trk[i].sim_z[2];
							pTrkPkg->trk[i].x[3] = 0.f;
						} 
						else
						{
							;
						}
	                }
	                //rcta车辆正前后方，异侧起的目标,径向速度与横向速度是相反的
                    else if ((pTrkPkg->trk[i].sim_z[3]) < -15
                                && pCdiPkg->cdi[j].y < 7
                                && pTrkPkg->trk[i].sim_z[2] < 0
                                && (config->vdyGearState == GEAR_SIG_R || fabsf(config->speed) < 3))
                    {
                        pTrkPkg->trk[i].x[2] = fabsf(pTrkPkg->trk[i].sim_z[2]);
                        pTrkPkg->trk[i].x[3] = 0;
                    }
                    else if(pCdiPkg->cdi[j].status&POINT_STATUS_DYNAMIC_BMP)
                    {
                        if (cdi->x < 0 
                            && (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f || fabsf(cdi->mea_z[2]) > 7.f)
                            && config->vdyGearState != GEAR_SIG_R)
                        {
                            pTrkPkg->trk[i].x[2] = 0;
                            pTrkPkg->trk[i].x[3] = pTrkPkg->trk[i].sim_z[2];
                        }
                        else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.f && cdi->y < 0 && cdi->x > 0.5f && cdi->mea_z[3] > 110.f)
                        {
                            pTrkPkg->trk[i].x[2] = 0;
                            pTrkPkg->trk[i].x[3] = cdi->mea_z[2] / cosf(cdi->mea_z[3] * ANG2RAD);
                        }
                        else
                        {
                            pTrkPkg->trk[i].x[2] = 0;
                            pTrkPkg->trk[i].x[3] = 0;
                        }
                    }else
                    {
						if (pRDP_inVehicleData->vdySpeedInmps * pVdy->vcsLngVel > 0.f)
						{
							pTrkPkg->trk[i].x[2] = pVdy->vcsLatVel;
							pTrkPkg->trk[i].x[3] = pVdy->vcsLngVel;
						}
						else
						{
							pTrkPkg->trk[i].x[2] = -pVdy->vcsLatVel;
							pTrkPkg->trk[i].x[3] = -pVdy->vcsLngVel;
						}
                    }

                    if(gGroupStatus[pCdiPkg->cdi[j].groupId2] & POINT_STATUS_DYNAMIC_BMP)
                    {
                        if(gGroupStatus[pCdiPkg->cdi[j].groupId2] & POINT_STATUS_REVERSE_BMP)
                        {
    //                            pTrkPkg->trk[i].status |= TRACK_STATUS_REVERSE_BMP;
                            pTrkPkg->trk[i].reverseCnt++;
                        }
                    }

                    pTrkPkg->trk[i].front = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_FRONT_INIT;
                    pTrkPkg->trk[i].back = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_BACK_INIT;
                    pTrkPkg->trk[i].left = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_LEFT_INIT;
                    pTrkPkg->trk[i].right = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_RIGHT_INIT;
                    pTrkPkg->trk[i].stored_last_outputX[0] = 1000.0f;     //初始置无效值
                    pTrkPkg->trk[i].stored_last_outputX[1] = 1000.0f;     //初始置无效值
                    pTrkPkg->trk[i].groupPointNumAccum = gMemberNumPerGroup[groupId];
                    memcpy(&pTrkPkg->trk[i].stored_last_z[0][0], &pCdiPkg->cdi[j].mea_z[0], sizeof(float)*4);
    #ifdef INCLUDE_HEIGHT_ANGLE
                    pTrkPkg->trk[i].stored_last_z[0][MEASURED_VALUE_NUM-1] = pCdiPkg->cdi[j].heighAngle;
    #endif
                    pTrkPkg->trk[i].rcs = pCdiPkg->cdi[j].rcs;
                    memset(pTrkPkg->trk[i].latestSNR, 0.f, 5 * sizeof(float));
                    memset(pTrkPkg->trk[i].latestRCS, 0.f, 5 * sizeof(float));
                    RDP_TRACK_updateTrkSlide(&pCdiPkg->cdi[j], &pTrkPkg->trk[i]);

                    pTrkPkg->trk[i].suspicious = 0;
                    pTrkPkg->trk[i].movingHit = 0;
                    pTrkPkg->trk[i].crossCnt = 0;
                    pTrkPkg->trk[i].crossAllCnt = 0;
					pTrkPkg->trk[i].absCrossCnt = 0;
                    pTrkPkg->trk[i].keepInBackCnt = 0;
                    pTrkPkg->trk[i].keepDowStateCnt = 0;
                    pTrkPkg->trk[i].kepRcwAreaCnt = 0;
                    pTrkPkg->trk[i].rcwoverlapxcnt = 0;
                    pTrkPkg->trk[i].dowoverlapxcnt = 0;
                    pTrkPkg->trk[i].rcwoverlapsumx = 0.0f;
                    pTrkPkg->trk[i].rcwoverlapx = 0.0f;
                    pTrkPkg->trk[i].boxCenter[0] = 0;
                    pTrkPkg->trk[i].boxCenter[1] = 0;
                    pTrkPkg->trk[i].stored_last_16trk_outputXY[0] = pTrkPkg->trk[i].x[0];
                    pTrkPkg->trk[i].stored_last_16trk_outputXY[1] = pTrkPkg->trk[i].x[1];
                    //pTrkPkg->trk[i].nearestX = pTrkPkg->trk[i].x[0];
                    //pTrkPkg->trk[i].nearestY = pTrkPkg->trk[i].x[1];
                    pTrkPkg->trk[i].nearestPosition[0] = pTrkPkg->trk[i].x[0];
                    pTrkPkg->trk[i].nearestPosition[1] = pTrkPkg->trk[i].x[1];
                    pTrkPkg->trk[i].bsdSuppressCnt = 0;
                    pTrkPkg->trk[i].fissionCnt = 0;
                    pTrkPkg->trk[i].LngSpeedHeadingAngleCnt = 0;
                    pTrkPkg->trk[i].objDeleteFlag = 0;
                    pTrkPkg->trk[i].stored_last_xy[0] = pCdiPkg->cdi[j].x;
                    pTrkPkg->trk[i].stored_last_xy[1] = pCdiPkg->cdi[j].y;

                    pTrkPkg->trk[i].rangeStartDiffValue = 0.f;
                    pTrkPkg->trk[i].objFakeDOt = 0;
                    pTrkPkg->trk[i].isTruePoint = 0;
                    pTrkPkg->trk[i].bumperTimestampCnt = 0;
                    pTrkPkg->trk[i].pointTrackingFrameCount = 0;
                    pTrkPkg->trk[i].guardrailOutsideCnt = 0;
                    pTrkPkg->trk[i].onComingObjCnt = 0;
                	pTrkPkg->trk[i].dowguardCrossCnt = 0;
                    pTrkPkg->trk[i].bsdTargetMaintainEnd = 0;
                    pTrkPkg->trk[i].dowMove2Static				= 0;
					pTrkPkg->trk[i].isSuspPedestrianCnt			= 0;
					pTrkPkg->trk[i].isSuspPedestrian			= 0;
					pTrkPkg->trk[i].isLngMovingStatusCnt		= 0;
					if (pCdiPkg->cdi[j].status & (POINT_STATUS_ABNORMAL_BMP | POINT_STATUS_DEBLUR_FAILED))
					{
						pTrkPkg->trk[i].assoAbnormalCnt = 1;
					}
					else
					{
                    pTrkPkg->trk[i].assoAbnormalCnt             = 0;
					}
                    pTrkPkg->trk[i].movingStatusCnt             = 0;
                    pTrkPkg->trk[i].staticStatusCnt             = 0;
                    pTrkPkg->trk[i].BSDCutInTargetCnt           = 0;
					pTrkPkg->trk[i].LatCutInCnt = 0;
					pTrkPkg->trk[i].LatCutOutCnt = 0;
                    pTrkPkg->trk[i].isMirrorCnt                 = 0;
                    pTrkPkg->trk[i].ObjAssoDetIdx               = pCdiPkg->cdi[j].DetIdx;

					pTrkPkg->trk[i].dowShelterScene = fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f ? isDowShelterScene(pTrkPkg, i) : 0;
					if (pTrkPkg->trk[i].dowShelterScene)
					{
						pTrkPkg->trk[i].x[2] = 0.f;
						pTrkPkg->trk[i].x[3] = cdi->mea_z[2] / cosf(cdi->mea_z[3] * ANG2RAD);
					}

                    if (config->isFront == 0 && (pRDP_inVehicleData->vdySpeedInmps > 0.1f || fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f)
                        && cdi->x > 0.75f
                        && ((cdi->y < 5.f && fabsf(cdi->mea_z[3] - 90.f) > 1.f
                            && cdi->mea_z[2] / cosf(cdi->mea_z[3] * DEG2RAD) > pRDP_inVehicleData->vdySpeedInmps + 0.5f)
                        || (fabsf(cdi->mea_z[3] - 90.f) < 1.5f)))
                    {
                        pTrkPkg->trk[i].onComingObjCnt++;
                    }

                    break;
                }
                //若遍历一轮之后，仍未新建，firstId置0
                if (i == MAX_NUM_OF_TRACKS - 1 && gFirstId != 0)
                {
                    i = gFirstId = 0;
                    i--;
                }
                else if (i == MAX_NUM_OF_TRACKS - 1 && gFirstId == 0)
                {
                    i = trackReleaseByPriority(pCdiPkg->cdi, j, pTrkPkg->trk, pRDP_inVehicleData);
                    if (i != MAX_NUM_OF_TRACKS)
                    {
                        pTrkPkg->trk[i].type = NONE;
                    }
                    i--;
                }
            }
        }
    }
}

//根据X或者Y的位移来估算相应的速度,中间间隔Cnt计算，currR是当前的值，r是历史值
float RDP_vcalc(trk_t *trk, float currR, float *r, uint8_t Cnt)
{
    float v = 0;
    uint8_t histcnt = (trk->trkCnt - 1) > Cnt ? Cnt : (trk->trkCnt - 1);
    float histtime = 0;
    for(uint8_t j = 0; j < histcnt; j++)
    {
        histtime += gRDP_storedFrameTime[j];
    }
    v = 1.0f/histtime*(currR - r[histcnt-1]);
    return v;
}

/*
场景：异侧邻车道来向目标在Fov边界原始点特性导致内飘问题。
*/
void RDP_FovEdgeFilterUpdate(cdi_t *cdi, trk_t* trk, VDY_DynamicEstimate_t *pRDP_inVehicleData, rdp_config_t* config)
{
    uint8_t flag = 0;
    float trueAngle = (cdi != NULL) ? (cdi->mea_z[3] - config->installAngle) : (trk->sim_z[3] - config->installAngle);
    if (trk->status & TRACK_STATUS_MOVING_BMP && !(trk->status & TRACK_STATUS_CROSSING_BMP))
    {
        if (trueAngle < FOVEDGE_MAX_THRE && trk->sim_z[2] < (-10 / 3.6f) && trk->x[2] > 0.f)   //纵向靠近，横向远离，整体是靠近的时候，会对目标横向速度有影响
        {
            // -70°外的相对靠近目标抑制横向速度
            flag = 1;
        }
        else if (trueAngle < FOVEDGE_THRE && trk->sim_z[2] < 0.f)
        {
            if (trk->activeTrkCnt >= 12)
            {
                if (trk->x[0] < -2.f
                    && GET_GRID_COL(-1.f * (trk->stored_last_x0[STORE_X0_FRAME_NUM - 1] + 0.9f + 2.f)) == GET_GRID_COL(-1.f * (trk->x[0] + 0.9f + 2.f))
                    && fabsf(trk->x[0] - trk->stored_last_x0[STORE_X0_FRAME_NUM - 1]) < 1.5f
                    && trk->x[0] > FOVEDGE_X_THRE
                    && trk->stored_last_x0[STORE_X0_FRAME_NUM - 1] > FOVEDGE_X_THRE)
                {
                    flag = 1;
                }
            }
            else
            {
                // FOV边界新起批的目标
                ;
            }

            // 前角雷达对向来车
            if (config->isFront
                && (trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) < -5.f
                && fabsf(pRDP_inVehicleData->vdyCurveRadius) > 500.f
                && trk->objType < 3 && trk->type == TRACK
                && trk->x[0] < -1.5f && trk->startPosition[0] > FOVEDGE_X_THRE)
            {
                flag = 1;
            }
        }
        else
        {
            ;
        }
    }
    else
    {
        ;///
    }

    if (flag && (trk->x[2] > 0.f || trk->x[2] < -0.5f))
    {
        // 强制抑制横向速度
        trk->x[2] *= 0.5f;
        trk->x[4] = 0.f;
    }
}

/*
场景：异侧RCTA低速目标速度反向导致融合的右侧RCTA目标报警退出早
*/
void RDP_FovEdgeRCTAFilterUpdate(trk_t* trk)
{
	if ((trk->status & TRACK_STATUS_MOVING_BMP)\
		&& trk->activeTrkCnt <= 50 \
		&& trk->crossAllCnt > 10 \
		&& trk->x[0] < 3.0f \
		&& trk->x[0] > -3.0f \
		&& trk->x[1] < 4.0f \
		&& trk->x[2] < 4.0f \
		&& trk->x[2] > 0.0f \
		&& trk->startPosition[0] < 0.f \
		&& trk->startPosition[0] > -3.f \
		&& trk->startPosition[1] < 4.f \
		&& trk->startPosition[1] > 0.f
		)
	{
		//强制抑制纵向速度
		trk->x[3] = 0.f;
		trk->x[5] = 0.f;
		//更新
		trk->sim_z[2] = trk->x[2] * trk->x[0] / trk->sim_z[1] + trk->x[3] * trk->x[1] / trk->sim_z[1];
	}
}

/*
*低速DOW中断问题抑制策略
*出现100帧以上的航迹，自车静止，横向距离小于3m，大于0m，纵向距离小于10m，大于-1m.
*横向速度小于2m/s，纵向速度小于4m/s，且历史航迹的航向角统计有大于175°的大于60帧.
*/
void RDP_LowSpeedDOWUpdate(trk_t* trk, VDY_DynamicEstimate_t *pRDP_inVehicleData)
{
	if (trk->activeTrkCnt > 100 && fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && trk->x[0] < 3.f && trk->x[0] > 0.f \
		&& trk->x[1] < 10.f && trk->x[1] > -1.f && fabsf(trk->x[2]) < 2.f &&  trk->x[3] < 4.f \
		&& trk->LngLowSpeedHeadingAngleCnt > 60 )
	{
		
		if (trk->LngLowSpeedHeadingAngleCnt > 120)
		{
			trk->x[2] = trk->x[2] * 0.2f;
		}
		else
		{
			trk->x[2] = trk->x[2] * 0.5f;
		}
		trk->x[4] = 0.f;
		trk->sim_z[2] = trk->x[2] * trk->x[0] / trk->sim_z[1] + trk->x[3] * trk->x[1] / trk->sim_z[1];
	}
}


/**
 * @brief 更新模块结果，主要未包含各种类型的航迹列表
 * @param pCdiPkg RDP内部用于跟踪的原始点列表全局变量指针 
 * @param time 帧间隔
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针 
 * @param EKF_A 
 * @param EKF_Q 扰动矩阵实参
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 */
static uint16_t VehicleStaticTime = 0;  //车辆静止时间
static uint16_t VehicleMovingTime = 0;	//车辆运动时间
void RDP_Track_updateResults(cdi_pkg_t *pCdiPkg, float time, VDY_DynamicEstimate_t *pRDP_inVehicleData
                            , float EKF_A[36], float EKF_Q[36], float EKF_R[9], trk_pkg_t *pTrkPkg)
{
    u32 i, j;
    float tmp_sigma[2], tmp_P[36], tmp_x[6], x[2], tmp_lastX[4];
    float kalTime = time;
    trk_t* trk;
    cdi_t* cdi;
    uint8_t flag = 0, useEkf;
	float rawLngSpeed;
	rdp_config_t* config = RDP_getTrackConfigPointer();
    float mea_z[3] = { 0.f };
	uint8_t storedValidCnt;
	float vehicleMoveRange = 0.0f, objmoveLng = 0.f;
    const stVehicleStatus* pVdy = getVdyStatus();

	if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.01f)
	{
		VehicleStaticTime = VehicleStaticTime < 3000 ? VehicleStaticTime + 1 : 3000;
		if (VehicleStaticTime > 2400) // 静止时间超过两分钟直接置零运动时间计数 两分钟时长考虑红绿灯
			VehicleMovingTime = 0;
		else
			VehicleMovingTime = VehicleMovingTime > 0 ? VehicleMovingTime - 1 : 0;
	}
	else
	{
		VehicleStaticTime = 0;
		VehicleMovingTime = VehicleMovingTime < 40000 ? VehicleMovingTime + 1 : 40000;
	}

    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrkPkg->trk[i];
        if (trk->type == NONE)
        {
            continue;
        }

		storedValidCnt = (trk->trkCnt > STORE_X1_FRAME_NUM) ? STORE_X1_FRAME_NUM : trk->trkCnt;
		vehicleMoveRange = RDP_calcVehicleMoveRange(storedValidCnt, pTrkPkg);		//计算自车移动位移

        if ((trk->status & TRACK_STATUS_MOVING_BMP) && \
			((fabsf(trk->stored_last_outputX[2]) > 3 && fabsf(trk->stored_last_outputX[3] - pRDP_inVehicleData->vdySpeedInmps * cosf(pRDP_inVehicleData->vdyYawRate)) < 3) \
			|| (fabsf(trk->stored_last_outputX[2]) > 1.5*fabsf(trk->stored_last_outputX[3] - pRDP_inVehicleData->vdySpeedInmps * cosf(pRDP_inVehicleData->vdyYawRate)))))
		{
			trk->crossCnt += 1;
			trk->crossAllCnt = trk->crossAllCnt >= 127 ? 127: trk->crossAllCnt + 1;
			if (trk->crossCnt >= 3)
			{
				trk->status |= TRACK_STATUS_CROSSING_BMP;
				trk->crossCnt = 3;
			}
		}
		else
		{
			if (trk->crossCnt > 0)
				trk->crossCnt -= 1;
			if (trk->crossCnt <= 0)
			{
				trk->status &= ~TRACK_STATUS_CROSSING_BMP;
				//trk->crossAllCnt = 0;
				trk->crossAllCnt = trk->crossAllCnt <= 0 ? 0 : trk->crossAllCnt - 1;
			}
		}

		if ((trk->status & TRACK_STATUS_MOVING_BMP) && \
			trk->headingAngle < -100.f &&
			trk->headingAngle > -160.f &&
			trk->activeTrkCnt > 20
			)
		{
			trk->skewCrossCnt = trk->skewCrossCnt < 127 ? trk->skewCrossCnt + 1 : 127;
		}
		else
		{
			trk->skewCrossCnt = trk->skewCrossCnt > 0 ? trk->skewCrossCnt - 1 : 0;
		}

		if (trk->skewCrossCnt > 5)
		{
			trk->status |= TRACK_STATUS_SKEW_CROSSING_BMP;
		}
		else
		{
			trk->status &= ~TRACK_STATUS_SKEW_CROSSING_BMP;
		}

        if (pTrkPkg->trk[i].idx_1 != FILTER_NON_ASSOC)
        {
            cdi = &pCdiPkg->cdi[trk->idx_1];
            if (pTrkPkg->trk[i].type == CANDI && pTrkPkg->trk[i].hit == 0)
            {
                // 新建航迹更新加速度方差和状态协方差
                float temp;
                trk->sigma[0] = trk->x[1] * trk->x[1] * (0.04f * 0.04f);
                trk->sigma[1] = trk->x[0] * trk->x[0] * (0.04f * 0.04f);
                trk->sigma[0] = trk->sigma[0] < 0.5f ? 0.5 : trk->sigma[0];
                trk->sigma[1] = trk->sigma[1] < 0.5f ? 0.5 : trk->sigma[1];

                memset(trk->P, 0, sizeof(trk->P));
                if(trk->sim_z[1] < 7)
                {
                    trk->P[0] = 0.4 * 0.4f;
                    trk->P[1+6] = 1.0f * 1.0f;
                }
                else
                {
                    temp = sinf(trk->sim_z[3] * DEG2RAD);
                    temp = temp * temp;
                    trk->P[0] = trk->x[1] * trk->x[1] * NOISE2_A + temp * NOISE2_R;
                    trk->P[1+6] = trk->x[0] * trk->x[0] * NOISE2_A + (1- temp) * NOISE2_R;
                }
                temp = 100;
                trk->P[2 + 2 * 6] = (trk->P[0] * temp);
                trk->P[3 + 3 * 6] = (trk->P[1+6] * temp);
                trk->P[1] = trk->P[6] = sinf(trk->sim_z[3] * 2 * DEG2RAD) * (NOISE2_R/2 - trk->sim_z[1] * trk->sim_z[1] * (NOISE2_A/2));
            }
            else if (pTrkPkg->trk[i].type == CANDI && trk->status & TRACK_STATUS_PRE_TRECK_OBJ_BMP && trk->hit < 20)
            {
                trk->x[0] = trk->x[0] * 0.95f+ cdi->x * 0.05f;
                trk->x[1] = cdi->y;
                trk->x[2] = 0;
                trk->x[3] = cdi->mea_z[2];

                trk->sim_z[1] = sqrtf(trk->x[0] * trk->x[0] + trk->x[1] * trk->x[1]);
                trk->sim_z[1] = trk->sim_z[1] < 0.1f ? 0.1f : trk->sim_z[1];
                trk->sim_z[2] = (trk->x[0] * trk->x[2] + trk->x[1] * trk->x[3]) / trk->sim_z[1];
                trk->sim_z[3] = atan2f(trk->x[0], trk->x[1]) * RAD2ANG;
                trk->rcs = (trk->rcs + cdi->rcs) >> 1; // trkRcs = trkRcs*50% + pointRcs*50%
                trk->heighAngle = 0.5f * (trk->heighAngle + cdi->heighAngle);
            }
            else
            {
                useEkf = 0;
                x[0] = pTrkPkg->trk[i].x[0];
                x[1] = pTrkPkg->trk[i].x[1];

                if (cdi->mea_z[1] > 50)
                {
                    EKF_R[0] = RList[2][0];
                    EKF_R[4] = RList[2][1];
                    EKF_R[8] = RList[2][2];
                }
                else if (cdi->mea_z[1] < 10)
                {
                    EKF_R[0] = RList[0][0];
                    EKF_R[4] = RList[0][1];
                    EKF_R[8] = RList[0][2];
                }
                else
                {
                    EKF_R[0] = RList[1][0];
                    EKF_R[4] = RList[1][1];
                    EKF_R[8] = RList[1][2];
                }

                //if((ANGLE_IN_CROSS_AREA(trk->sim_z[3], 65) && trk->sim_z[1] < 15.0f && trk->status&TRACK_STATUS_MOVING_BMP) || (trk->sim_z[1] < 10.0f && trk->status&TRACK_STATUS_MOVING_BMP))
                //{
                //    flag = trackVxVyEst(trk, STORE_POINT_NUM, pCdiPkg->cdi, pCdiPkg->number);
                //    if(flag)
                //    {
                //        trk->status |= TRACK_STATUS_VELOCITY_CONFIRMED_BMP; // TODO：p_x[]
                //    }
                //}
                if(trk->activeTrkCnt == 1)
                {
                    trk->stored_last_outputX[2] = trk->x[2];
                }

				/* 1、静止目标 */
                if (!(trk->status & TRACK_STATUS_MOVING_BMP)
					&& !(cdi->status & POINT_STATUS_DYNAMIC_BMP)
					&& !(gGroupStatus[cdi->groupId2] & POINT_STATUS_DYNAMIC_BMP))
                {
                    updateStaticObj(pCdiPkg, trk, time, pRDP_inVehicleData);
                }
				/* 2、速度盲区或大角度运动目标(暂只针对超车/被超车) */
                else if ((trk->status & TRACK_STATUS_IN_DEADZONE_BMP)
					&& !(trk->status & TRACK_STATUS_CROSSING_BMP)
					&& fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.f / 3.6f
					&& trk->activeTrkCnt < 15)
                {
                    updateObjInDeadZone(pCdiPkg, trk, time, pRDP_inVehicleData);
                }
				/* 3、盲区内纵向来向目标速度不准，不进ekf滤波（DOW测项）
				* TODO：与上述分支合并
				*/
				else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 10.f / 3.6
					&& !(trk->status & TRACK_STATUS_CROSSING_BMP)
					&& trk->status & TRACK_STATUS_MOVING_BMP
					&& trk->activeTrkCnt > 12 //&& trk->startPosition[1] > 10.f  //测试版本，防止目标来回不切ID导致的起始位置问题，正式版本放开
					&& trk->x[0] > 0.75f && trk->x[0] < 0.75f + 3.75f && trk->x[1] < 1.f	// 纵向作用距离
					&& fabsf(trk->x[2]) < 0.5f * fabsf(trk->x[3]) && trk->x[3] < -1.5f
					&& trk->keepDowStateCnt < (roundf(5.f / fabsf(trk->x[3])) * 10))
				{
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
						&& cdi->x > 1.f
						&& fabsf(cdi->mea_z[2]) < 0.01f
						&& trk->groupNonzeroVelCnt < 1)
					{
						trk->dowMove2Static++;
					}
					else
					{
						trk->dowMove2Static = 0;
					}

					if (trk->dowMove2Static >= 3)
					{
						trk->x[2] = 0.f;
						trk->x[3] = 0.f;
						trk->sim_z[2] = 0.f;
						trk->keepDowStateCnt = 0;
					}
					else
					{
						trk->keepDowStateCnt = trk->keepDowStateCnt >= 127 ? trk->keepDowStateCnt : trk->keepDowStateCnt + 1;
						//trk->x[0] = LP_FILTER(trk->p_x[0], SIDE_R_COEF, cdi->x);
						//trk->x[1] = LP_FILTER(trk->p_x[1], SIDE_R_COEF, cdi->y);
                        trk->x[0] = fabsf(trk->p_x[0] - cdi->x) < 0.1f ? LP_FILTER(trk->p_x[0], SIDE_R_COEF, cdi->x) : trk->p_x[0];
                        trk->x[1] = fabsf(trk->p_x[1] - cdi->y) < 0.2f ? LP_FILTER(trk->p_x[1], SIDE_R_COEF, cdi->y) : trk->p_x[1];
						trk->x[2] *= 0.75f;
						trk->sim_z[1] = hypotf(trk->x[0], trk->x[1]);
						trk->sim_z[2] = trk->x[2] * trk->x[0] / trk->sim_z[1] + trk->x[3] * trk->x[1] / trk->sim_z[1];
						trk->sim_z[3] = RAD2ANG * atan2f(trk->x[0], trk->x[1]);
					}
				}
				else
                {
					// 低速RCTA目标，在x小于0的时候保持在那
					if (trk->x[2] > -15.f / 3.6f && trk->crossAllCnt > 20 && trk->x[0] < -0.4f \
						&&  trk->x[2] < 0.f && trk->x[0] > -3 && trk->x[1] < 5 && trk->x[1] > 0 \
						&& trk->startPosition[0] > -3.f && trk->startPosition[1] < 5.f && trk->startPosition[1] > -2.f)
					{
						uint8_t keepInBackCnt = (uint8_t)(fabsf(4.f / trk->x[2]) * 1000 / 50) < 60 ? (uint8_t)(fabsf(4.f / trk->x[2]) * 1000 / 50) : 60;
                        //当自车静止且检测点绝对静止时，不再维持
                        if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && fabsf(cdi->mea_z[2]) < 0.1f && trk->keepInBackCnt > keepInBackCnt / 2)
                        {
                            useEkf = 1;
                        }
						else if (keepInBackCnt > 0 && keepInBackCnt < 70 && trk->keepInBackCnt < keepInBackCnt + 20)
						{
							trk->keepInBackCnt += 1;
						}
						else
						{
							useEkf = 1;
						}
					}
                    //// 横穿标记目标正后方，向后关联的时候，保持住，防止横穿目标分裂
                    //else if (trk->crossAllCnt > 20 && pCdiPkg->cdi[pTrkPkg->trk[i].idx_1].x > trk->x[0] && trk->x[0] < 0 \
                    //    && trk->x[2] < 0.f && trk->x[0] > -3 && trk->x[1] < 5 && trk->x[1] > 0 && trk->keepInBackCnt < 80 \
                    //    && trk->fixHeadAngle == 0)
                    //{
                    //    if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && fabsf(cdi->mea_z[2]) < 0.1f && trk->keepInBackCnt > 25)
                    //    {
                    //        useEkf = 1;
                    //    }
                    //    else
                    //    {
                    //        trk->keepInBackCnt += 1;
                    //    }
                    //}
                    // 横穿目标至正后方，采用维持策略
                    else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 10.f / 3.6
                        && trk->status & TRACK_STATUS_MOVING_BMP
                        && trk->status & TRACK_STATUS_CROSSING_BMP
                        && trk->type == TRACK && trk->activeTrkCnt > 12
                        && (IN_HONE_LANE(trk->x[0]) || ANGLE_IN_VERTICAL_AREA(trk->sim_z[3], 10))
                        && trk->x[1] > 0.5f && trk->x[2] < (-5.f / 3.6)
                        && (cdi->x - trk->x[0]) * trk->x[2] < 0.f
                        && trk->crossAllCnt > 20
                        && trk->keepInBackCnt < 80)
                    {
                        //当自车静止且检测点绝对静止时，不再维持
                        if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && fabsf(cdi->mea_z[2]) < 0.1f && trk->keepInBackCnt > 25)
                        {
                            useEkf = 1;
                        }
                        else
                        {
                            trk->keepInBackCnt += 1;
                        }
                    }
					else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 12.f / 3.6 && trk->bsdTargetMaintainEnd && fabsf(cdi->mea_z[2]) < 0.01f)
					{
						trk->x[1] = trk->p_x[1];
						trk->x[2] *= 0.5;
					}
					// 大车超车，或者超越大车时候，在跟踪点向后关联的时候，保持在原地，不更新。防止超越大车或被大车超越的时候目标分裂或者目标外漂导致BSD中断
					else if (!(trk->status & TRACK_STATUS_CROSSING_BMP) && (trk->objType == 4) && cdi->y > trk->x[1] \
						&& trk->x[0] < 4 && trk->x[0] > 0 && trk->x[1] < 0 && trk->x[1] > -3 && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f)
					{
						/*
						* 若目标与自车的相对速度变化，保持策略会导致BSD误报或漏报
						* 若原始点可用，重新计算纵向速度（向后关联时目标头尾大概率位于FOV边界）
						*/
						rawLngSpeed = calcRawLngVelocity(pCdiPkg, trk);
						if (fabsf(rawLngSpeed) > 0.f && fabsf(rawLngSpeed) < 20.f && fabsf(rawLngSpeed - trk->x[3]) < 2.5f)
						{
							trk->x[3] = LP_FILTER(rawLngSpeed, SIDE_R_COEF, trk->x[3]);
						}
					}
					else
                    {
                        useEkf = 1;
                    }
                }

                ////低速DOW抑制横向速度策略，选择聚类族中横向最近点更新;近距离由于没有目标车角点，导致横向速度偏大
                //if (config->isFront == 0
                //    && useEkf && fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
                //    && trk->x[3] < -1.f && trk->x[3] > -4.f
                //    && trk->x[0] > 0.5f && trk->x[0] < 0.5f + LANE_WIDTH_HALF
                //    && trk->x[1] < 10.f)
                //{
                //    float measLat = cdi->latLim[0];
                //    mea_z[0] = hypotf(cdi->latLim[0], cdi->y);
                //    mea_z[1] = cdi->mea_z[2];
                //    mea_z[2] = asinf(cdi->latLim[0] / mea_z[0]) * RAD2ANG;
                //}
                //else
                {
                    memcpy(mea_z, cdi->mea_z + 1, 3 * sizeof(float));
                }

                if (useEkf)
                {
                    RDP_runMainEKF(EKF_A, EKF_Q, pTrkPkg->trk[i].sigma, pTrkPkg->trk[i].P, gEKF_R,
                             pTrkPkg->trk[i].x, 0, mea_z, kalTime,
                             tmp_sigma, tmp_P, tmp_x,
                             (pTrkPkg->trk[i].sim_z) + 1, ((pTrkPkg->trk[i].pre_z) + 1));
                    
					if ((trk->status & TRACK_STATUS_SKEW_CROSSING_BMP) \
						&& trk->x[0] < 0.f && trk->x[0] > -3.f \
						&& trk->x[1] < 5.f && trk->x[1] > -0.5f)
					{
						if ((tmp_x[0] - trk->x[0]) > 0.5f && trk->x[2] < 0.f)
						{
							tmp_x[0] = trk->p_x[0];
						}
						if ( (tmp_x[2] - trk->x[2]) > 0.5f && trk->x[2] < 0.f)
						{
							tmp_x[2] = trk->x[2];
							tmp_x[4] *= 0.2f;
						}
						if ((tmp_x[1] - trk->x[1]) > 0.5f && trk->x[3] < 0.f)
						{
							tmp_x[1] = trk->p_x[1];
						
						}
						if ((tmp_x[3] - trk->x[3]) > 0.f && trk->x[3] < 0.f)
						{
							tmp_x[3] = trk->x[3];
							tmp_x[5] *= 0.2f;
						}
					}

                    //自车运动时的非横穿目标，若目标前后帧的对地纵向速度反向，不允许反向
                    if (config->isFront == 0 && (trk->status && TRACK_STATUS_MOVING_BMP)
                        && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 1.5f
                        && trk->x[3] > pRDP_inVehicleData->vdySpeedInmps
                        && tmp_x[3] < pRDP_inVehicleData->vdySpeedInmps
                        && fabsf(cdi->mea_z[3] - 90.f) > 1.f
                        && cdi->mea_z[2] / cosf(cdi->mea_z[3] * DEG2RAD) > pRDP_inVehicleData->vdySpeedInmps + 0.5f)
                    {
                        tmp_x[3] = LP_FILTER(trk->x[3], 0.65f, (cdi->mea_z[2] / cosf(cdi->mea_z[3] * DEG2RAD)));
                    }
                    if (trk->activeTrkCnt < 10 
                        && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 8.f
                        && (fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 8.f ||  trk->x[0] < 0.f)
                        && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && fabsf(tmp_x[2] - trk->x[2]) > 0.5f)
                    {
                        tmp_x[2] = LP_FILTER(trk->p_x[2], 0.95f, tmp_x[2]);
                    }
                    //6khp DOW测试项纵向速度过大误报警
                    if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && trk->status & TRACK_STATUS_MOVING_BMP
                        && fabsf(tmp_x[3]) > fabsf(cdi->mea_z[2] / cosf(cdi->mea_z[3] * ANG2RAD))
                        && fabsf(tmp_x[3] - cdi->mea_z[2] / cosf(cdi->mea_z[3] * ANG2RAD)) < 1.f
                        && tmp_x[3] < 0 && cdi->mea_z[3] < 80.f\
						&& fabsf(tmp_x[3]) < 15/3.6f)
                    {
                        tmp_x[3] = cdi->mea_z[2] / cosf(cdi->mea_z[3] * ANG2RAD);
                        tmp_x[5] = LP_FILTER(trk->p_x[5], 0.5f, 0.f);
                    }
                    //80kph DOW测试项纵向速度过小误报警
                    //近处保持速度，速度低于80kph后保持
                    else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && trk->status & TRACK_STATUS_MOVING_BMP
                        && tmp_x[3] < 0 && tmp_x[1] < 10.f
                        && tmp_x[3] > -80.f / 3.6f && trk->x[3] < -80.f / 3.6f)
                    {
                        tmp_x[3] = trk->x[3];
                        tmp_x[5] = LP_FILTER(trk->p_x[5], 0.5f, 0.f);
                    }
                    else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && trk->status & TRACK_STATUS_MOVING_BMP
                        && trk->x[3] < 0 && trk->x[3] > -1.9f && tmp_x[3] < -1.9f && cdi->mea_z[3] > 80.f
                        && (tmp_x[5] < -1.f || (tmp_x[3] - trk->x[3]) / kalTime < -8.f))
                    {
                        tmp_x[3] = trk->p_x[3];
                        tmp_x[5] = LP_FILTER(trk->p_x[5], 0.5f, 0.f);
                    }
                    else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && trk->status & TRACK_STATUS_MOVING_BMP
                        && cdi->status & POINT_STATUS_DYNAMIC_BMP
                        && trk->x[3] < 0.f && trk->x[3] > -3.f
                        && trk->x[0] > 0.75f && trk->x[1] < 10.f && cdi->mea_z[3] < 80.f
                        && (cdi->mea_z[1] - trk->sim_z[1]) * cdi->mea_z[2] < 0)
                    {
                        tmp_x[3] = trk->p_x[3];
                        tmp_x[5] = LP_FILTER(trk->p_x[5], 0.5f, 0.f);
                    }
                    else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 1.5f && fabsf(pRDP_inVehicleData->vdySpeedInmps) < 25 / 3.6f
                        && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && trk->status & TRACK_STATUS_MOVING_BMP
                        && trk->x[3] < 3.f && trk->x[1] > 0
                        && IN_BSD_AREA(trk->x[0], trk->x[1]) && trk->startPosition[0] < 0.9f + LANE_WIDTH * 1.5f
                        && ((cdi->y - trk->x[1]) * trk->x[3] < 0 || fabsf(cdi->y - trk->p_x[1]) > 1.f))
                    {
                        tmp_x[3] = cdi->mea_z[3] < 80.f ? LP_FILTER(trk->p_x[3], 0.75f, cdi->mea_z[2] / cosf(cdi->mea_z[3] * DEG2RAD)) : trk->x[3];
                        tmp_x[5] = LP_FILTER(tmp_x[5], 0.5f, 0);
                    }

					// 快速目标，跟踪点，纵向距离跟不上原始点问题，使用原始点的纵向距离平滑一下跟踪点的纵向距离。
					if (fabsf(cdi->mea_z[2] - trk->sim_z[2]) > 5 && fabsf(cdi->y - tmp_x[1]) > 1.5)
					{
						tmp_x[1] = LP_FILTER(tmp_x[1], 0.5, cdi->y);
					}
					else if (fabsf(cdi->mea_z[2]) > 50 / 3.6f && fabsf(trk->sim_z[2]) > 50 / 3.6f && fabsf(cdi->y - tmp_x[1]) > 1.5)
					{
						tmp_x[1] = LP_FILTER(tmp_x[1], 0.5, cdi->y);
					}
					/*
					* 1、BSD区域内90°附近同速目标，检测点不稳定导致的横向更新异常
					* 2、红绿灯路口DOW场景，目标（特别是大车）主动超车过程中关联或滤波异常导致的横漂
					*/ 
					if ((fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10 / 3.6f
							|| (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && trk->activeTrkCnt > 50))
						&& trk->type == TRACK
						&& trk->status & TRACK_STATUS_MOVING_BMP
						&& IN_BSD_AREA(trk->x[0], trk->x[1])
						//&& trk->x[1] < 0.5f
						&& (fabsf(tmp_x[0] - cdi->x) > 0.3f || (trk->x[0] - cdi->x) * (tmp_x[0] - cdi->x) < 0.f
							|| ((trk->objType == 3 || trk->objType == 4) && (tmp_x[2] > 0.5f || fabsf(tmp_x[2] - trk->x[2]) > 0.3f))))
					{
						tmp_x[0] = LP_FILTER(trk->x[0], 0.75f, cdi->x);
						tmp_x[2] = fabsf(tmp_x[2]) < 1.f ? LP_FILTER(trk->p_x[2], 0.75f, tmp_x[2]) : trk->p_x[2];
						tmp_x[4] = LP_FILTER(trk->p_x[4], 0.5f, 0.f);
					}

                    // 自车静止，对于前角雷达超车目标，新建时横纵向速度初始化为0，第二帧更新时速度异常
                    if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
                        && config->isFront
                        && trk->type == CANDI
                        && cdi->mea_z[2] < -5.f
                        && (cdi->mea_z[3] - config->installAngle) > 60.f
                        && fabsf(tmp_x[2] - trk->x[2]) > 5.f)
                    {
                        tmp_x[2] = 0.f;
                        tmp_x[4] = 0.f;
                    }

					memcpy(&tmp_lastX[0], &trk->x[0], sizeof(float) * 4);       //记录上一帧状态
                    float dvxmax = 0;
                    for (j = 0; j < 6; j++)
                    {
                        pTrkPkg->trk[i].x[j] = tmp_x[j];
                    }
                    // 自车加速向前时，斜穿目标航向角容易异常，增加滤波
                    if ((trk->type == TRACK) && (trk->headingAngle < -45) && (trk->headingAngle > -120) && (trk->status & TRACK_STATUS_CROSSING_BMP)
                        && (((fabsf(pRDP_inVehicleData->vdyAccelLong) > 0.2f) && (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.2f)) || (trk->objType == 4) || (gMemberNumPerGroup[pCdiPkg->cdi[trk->idx_1].groupId2] >= 4))) {
                            trk->x[3] = LP_FILTER(trk->x[3], 0.5f, trk->p_x[3]);
                            trk->x[5] = LP_FILTER(trk->x[5], 0.5f, 0.f);
                    }

					/* 异常处理 */
					// 滤除因为关联点导致的目标横向距离跳变，横向速度跳变问题
                    if (!ANGLE_IN_VERTICAL_AREA(cdi->mea_z[3], 15) && !ANGLE_IN_CROSS_AREA(cdi->mea_z[3], 15))
                    {
                        float latVelMax = fabsf(cdi->mea_z[2] / sinf(cdi->mea_z[3] * PI / 180.f));
                        float lngVelMax = fabsf(cdi->mea_z[2] / cosf(cdi->mea_z[3] * PI / 180.f));
                        if (trk->sim_z[1] > 15.f
                            && fabsf(trk->stored_last_x0[0] - trk->x[0]) > 0.8f
                            && fabsf(trk->stored_last_outputX[2] - trk->x[2]) > 2.f
                            && fabsf(trk->x[2]) > latVelMax)
                        {
                            trk->x[0] = LP_FILTER(trk->x[0], 0.25f, cdi->x);
                            trk->x[2] *= 0.25f;
                            trk->x[4] = (trk->type == CANDI) ? 0.f : (trk->x[4] * 0.25f);
                        }

                        // 针对法规切入场景，目标纵向速度滤波值异常，利用检测点分解的最大纵向速度加以修正
                        if (fabsf(fabsf(pRDP_inVehicleData->vdySpeedInmps) - (50 / 3.6f)) < (5 / 3.6f)
                            && trk->BSDCutInTargetCnt > 30
                            && trk->status && TRACK_STATUS_MOVING_BMP && trk->type == TRACK
                            && fabsf(trk->x[3]) < (10 / 3.6f)
                            && trk->x[1] > -2.f && trk->x[1] < 5.f
                            && lngVelMax < 3.f
                            && fabsf(trk->x[3]) > lngVelMax)
                        {
                            trk->x[3] = LP_FILTER(trk->x[3], 0.25f, lngVelMax);
                        }
                        if ((trk->x[3]-pRDP_inVehicleData->vdySpeedInmps) * (trk->p_x[3] - pRDP_inVehicleData->vdySpeedInmps) < -1.f && (trk->x[3] * (trk->x[1] - cdi->y)) > 0.f )
                        {
                            // 当预测的速度和滤波后的速度反向，且相差较大，增加滤波
                            trk->x[1] = LP_FILTER(trk->x[1], 0.5f, trk->p_x[1]);
                            trk->x[3] = LP_FILTER(trk->x[3], 0.5f, trk->p_x[3]);
                        }
                    }
                    else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (10 / 3.6f)
                        && fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f
                        && trk->status & TRACK_STATUS_MOVING_BMP && trk->type == TRACK
                        && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && fabsf(trk->p_x[0] - cdi->x) > 0.5f
                        && fabsf(trk->x[0] - cdi->x) > 0.5f
                        && fabsf(trk->stored_last_outputX[2]) < 1.5f    //对于存在较大横向速度的目标不做处理，可能为斜穿场景
                        && fabsf(trk->stored_last_outputX[2] - trk->x[2]) > 0.5f)
                    {
                        // 关联点横向距离波动较大
                        trk->x[0] = LP_FILTER(trk->stored_last_outputX[0], 0.75f, trk->x[0]);
                        trk->x[2] *= 0.25f;
                        trk->x[4] = (trk->type == CANDI) ? 0.f : (trk->x[4] * 0.25f);
                    }

					if (trk->type == TRACK && fabsf(trk->stored_last_x0[0] - trk->x[0]) > 0.8f && fabsf(trk->stored_last_outputX[2]) < 2.f  \
						&& fabsf(trk->stored_last_outputX[2] - trk->x[2]) > 2.f)
					{
						pTrkPkg->trk[i].x[0] = LP_FILTER(trk->x[0], 0.5, pTrkPkg->trk[i].p_x[0]);
						pTrkPkg->trk[i].x[2] = LP_FILTER(pTrkPkg->trk[i].p_x[2], 0.5, pTrkPkg->trk[i].x[2]);
					}
					// 过滤横向目标跳变，关联点正常，因滤波异常的点
                    float tmpLatRangeThr = 0.5f, tmpLatRangeThr2 = 0.5f;
                    if (IN_NEIGHBOUR_LANE(trk->x[0]) && trk->x[1] < 10.f && fabsf(trk->stored_last_outputX[2]) < 0.25)    // 使用上一帧的横向速度，当前帧横向速度可能已更新异常
                    {
                        tmpLatRangeThr = 0.25f;
                    }
                    if (IN_NEIGHBOUR_LANE(trk->x[0]) && IN_NEIGHBOUR_LANE(cdi->x) && cdi->mea_z[0] > 60.f && cdi->y < 3.f)
                    {
                        // 超车目标速度盲区内
                        tmpLatRangeThr2 = 1.f;
                    }
					if (trk->status & TRACK_STATUS_MOVING_BMP && trk->type == TRACK
						&& (fabsf(trk->x[0] - trk->stored_last_x0[0]) > tmpLatRangeThr
                            || fabsf(trk->x[0] - cdi->x) > tmpLatRangeThr
                            || fabsf(trk->x[2] - trk->stored_last_outputX[2]) > 0.5f)
						&& fabsf(trk->stored_last_x0[0] - cdi->x) < tmpLatRangeThr2
						&& fabsf(tmp_lastX[2]) < 0.5f
						&& !(trk->status & TRACK_STATUS_CROSSING_BMP))
					{
						pTrkPkg->trk[i].x[0] = LP_FILTER(trk->stored_last_x0[0], 0.5, cdi->x);
						pTrkPkg->trk[i].x[2] = pTrkPkg->trk[i].p_x[2];// LP_FILTER(pTrkPkg->trk[i].p_x[2], 0.5, pTrkPkg->trk[i].x[2]);
					}
					// 过滤横穿目标纵向跳变，关联点正常，因滤波异常的点
					if (trk->type == TRACK && (trk->status & TRACK_STATUS_MOVING_BMP)
						&& ((trk->status & TRACK_STATUS_CROSSING_BMP) || (trk->status & TRACK_STATUS_ABS_CROSSING_BMP))
						&& (trk->x[1] - tmp_lastX[1]) * trk->x[3] < 0.f
						&& fabsf(trk->x[1] - tmp_lastX[1] - pTrkPkg->stored_move_range[0]) > 0.5f)
					{
						trk->x[1] = LP_FILTER(tmp_lastX[1], 0.5f,cdi->y);
						trk->x[3] = trk->p_x[3];
					}
					// 滤除横穿目标纵向距离跳变，纵向速度跳变问题
					if (trk->type == TRACK && (trk->status & TRACK_STATUS_MOVING_BMP)
						&& ((trk->status & TRACK_STATUS_CROSSING_BMP) || (trk->status & TRACK_STATUS_ABS_CROSSING_BMP))
						&& ((fabsf(cdi->y - trk->p_x[1]) > 0.5f && (cdi->y - trk->p_x[1]) * tmp_lastX[3] < 0.f)
                            || fabsf(trk->x[3] - tmp_lastX[3]) > 0.5f))
					{
                        trk->x[1] = LP_FILTER(tmp_lastX[1], 0.5f, cdi->y);
                        trk->x[3] = trk->p_x[3];
					}
					// 过滤90°附近纵行目标纵向速度异常，关联点正常，因滤波异常的点
					if (trk->type == TRACK && (trk->status & TRACK_STATUS_MOVING_BMP)
						&& !(trk->status & TRACK_STATUS_CROSSING_BMP)
						&& (trk->objType == 3 || trk->objType == 4)
						&& trk->x[0] > 0.5f && trk->x[0] < 0.9f + LANE_WIDTH
						&& trk->x[1] < 0.f && (trk->x[1] - tmp_lastX[1]) * tmp_lastX[3] < 0.f)	// 当前帧滤波后的纵向速度可能已经异常，故使用上一帧的纵向速度
					{
						trk->x[1] = trk->p_x[1];
						trk->x[3] = fabsf(trk->x[3] - tmp_lastX[3]) < 1.f ? LP_FILTER(trk->p_x[3], 0.75, tmp_x[3]) : trk->p_x[3];
						trk->x[5] = LP_FILTER(trk->p_x[5], 0.5f, 0.f);
					}
					// 超车目标跟踪点超前关联点之后导致的滤波速度异常
					if (trk->type == TRACK && (trk->status & TRACK_STATUS_MOVING_BMP)
						&& !(trk->status & TRACK_STATUS_CROSSING_BMP)
						&& trk->activeTrkCnt < 10
						&& trk->x[0] > 0.5f && trk->x[0] < 0.9f + LANE_WIDTH && trk->x[1] < 3.f
						&& ((trk->x[1] - tmp_lastX[1]) * tmp_lastX[3] < 0.f || fabsf(trk->x[3] - tmp_lastX[3]) > 1.f))
					{
						trk->x[1] = LP_FILTER(trk->p_x[1], 0.5f, cdi->y);
						trk->x[3] = fabsf(trk->x[3] - tmp_lastX[3]) < 1.f ? LP_FILTER(trk->p_x[3], 0.75f, tmp_x[3]) : trk->p_x[3];
						trk->x[5] = LP_FILTER(trk->p_x[5], 0.5f, 0.f);
					}

					/*
					* 优化由于关联点异常导致的纵向距离跳变
					* 近处检测点不稳定：1、只检测到车尾；2、有效检测点被滤除
					*/
					if (trk->type == TRACK && trk->status & TRACK_STATUS_MOVING_BMP
						&& trk->x[0] > 0.9f && trk->x[0] < 0.9f + 3.75f * 2.f && trk->x[1] < 10.f && fabsf(tmp_lastX[3]) < 5.f
						&& ((fabsf(pCdiPkg->cdi[trk->idx_1].y - trk->x[1]) > 2.5f) || (fabsf(tmp_lastX[1] - trk->x[1]) > 0.5f)))
					{
						trk->x[1] = LP_FILTER(tmp_lastX[1], 0.75, trk->x[1]);
						if ((trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) * (tmp_lastX[3] - pRDP_inVehicleData->vdySpeedInmps) < 0.f)
						{
							trk->x[3] = tmp_lastX[3];
						}
						else
						{
							trk->x[3] = LP_FILTER(tmp_lastX[3], 0.75, trk->x[3]);
						}
					}
					/*
					* 优化BSD/LCA场景横向速度偏大导致的误报漏报
					*/
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 12 / 3.6f	// LCA最小激活车速12kph
						&& fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
						&& trk->type == TRACK && !(trk->status & TRACK_STATUS_CROSSING_BMP)/* && trk->activeTrkCnt > 50*/
						&& fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 25 / 3.6f
						&& trk->x[0] > 0.75f && trk->x[0] < 0.75f + 2 * LANE_WIDTH && trk->x[1] > 4.f
						//&& trk->x[1] < fabsf(trk->x[3]) * 4.f	// ttc=4s
						&& isLngOnComingTarget(pTrkPkg, i))
					{
						// 1、自车运动，快速纵行目标限制横向速度（LCA场景：邻车道漏报/中断、隔车道误报）
						// TODO：路试场景目标车变道是否会延迟？
						trk->x[2] *= 0.2f;
						rawLngSpeed = calcRawLngVelocity(pCdiPkg, trk);
						if (fabsf(rawLngSpeed) > 0.f && fabsf(rawLngSpeed) < 20.f)
						{
							trk->x[3] = LP_FILTER(rawLngSpeed, 0.75, tmp_lastX[3]);
						}
					}
					else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 12 / 3.6f
                        && (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f || fabsf(pRDP_inVehicleData->vdyYawRate) < 0.1f)
						&& trk->type == TRACK && !(trk->status & TRACK_STATUS_CROSSING_BMP) && trk->activeTrkCnt > 30
						&& trk->x[0] > 0.75f && trk->x[1] < 10.f && fabsf(trk->x[2]) > 1.2f && fabsf(trk->x[3]) < 5.f)
					{
						// 2、针对切入切出场景，横向速度过大可能导致不满足组法规要求（0.75±0.25）导致BSD误报或晚报
						// 注：法规测试切入切出场景对于目标切入切出速度和位置有具体要求
						trk->x[2] *= 0.5f;
					}
					else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 12 / 3.6f
                        && (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f || fabsf(pRDP_inVehicleData->vdyYawRate) < 0.1f)
						&& !(trk->status & TRACK_STATUS_CROSSING_BMP) && trk->status & TRACK_STATUS_MOVING_BMP
                        && IN_BSD_AREA(trk->x[0], trk->x[1])
						&& trk->activeTrkCnt < 50 && trk->startPosition[0] < 0.9f + LANE_WIDTH * 2
                        && trk->startPosition[1] < 0.1f && fabsf(trk->x[2]) > 0.5f)
					{
						// 3、自车车速较高时，限制速度盲区刚起批目标的横向速度（检测点特点为外->内）
						// 注：可能存在起批帧横向速度计算异常（航迹管理中计算的）
						trk->x[2] *= 0.2f;
						// 刚起批目标由于横向速度限制，导致横向位置更新不及时，会引起大车的BSD晚报
						if ((trk->x[0] - pCdiPkg->cdi[trk->idx_1].x) > 0.5f && pCdiPkg->cdi[trk->idx_1].x > 0.9f)
						{
							trk->x[0] = LP_FILTER(trk->x[0], 0.5, pCdiPkg->cdi[trk->idx_1].x);
						}
					}
					else
					{
						;
					}

                    // DOW场景, 已经识别到产品走查. 抑制目标横向速度. 
                    if ((1 == pTrkPkg->dowguardrailblocksence) && (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f) && 
                        (trk->type == TRACK) && (trk->status & TRACK_STATUS_MOVING_BMP) && (!(trk->status & TRACK_STATUS_CROSSING_BMP)) &&
                        (trk->x[0] > 0.75f) && (trk->x[0] < 0.75f + LANE_WIDTH) && ((trk->startPosition[0] > -1.f) && ((trk->startPosition[0]) < 5.0f)))
                    {
                        trk->x[2] *= 0.8f;      // DOW产品走查的遮挡场景抑制横向速度 
                    }

					// DOW场景，邻车道远处高速靠近目标，抑制横向速度
					// if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 5.f / 3.6f
					// 	&& fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
					// 	&& trk->type == TRACK && trk->status & TRACK_STATUS_MOVING_BMP && !(trk->status & TRACK_STATUS_CROSSING_BMP)
					// 	&& trk->x[0] > 0.75f && trk->x[0] < 0.75f + LANE_WIDTH// * 2.f		// 考虑正向两车道，防止隔车道目标产生较大的负横向速度导致误报
					// 	//&& trk->x[1] < fabsf(trk->x[3]) * 3.f								// ttc=3s
					// 	&& trk->x[3] < -30.f / 3.6)
					// {
					// 	if (trk->startPosition[0] < -1.f || trk->startPosition[0] > 5.f || pTrkPkg->skewStopSence == 1)
					// 	{
					// 		// 要同时考虑以下两种场景不能抑制横向速度：
					// 		// 1、目标车从隔车道切入邻车道测项dow不报警；2、斜停8°场景左右雷达dow误报（异侧-1m门限，同侧5m门限）
					// 		;
					// 	}
					// 	else
					// 	{
					// 		trk->x[2] *= 0.5f;
					// 	}
					// }
					//纵向行驶目标到近出低速行驶的，限制横向速度
					if (trk->LngSpeedHeadingAngleCnt > 100 && trk->LngLowSpeedHeadingAngleCnt > 5 && trk->x[0] > 0.f \
						&& trk->x[0] < 4.f && trk->x[1] < 10.f && trk->x[1] > 0.5f && fabsf(trk->x[2]) < 3.f && fabsf(trk->x[3]) < 3.f\
						&& pTrkPkg->skewStopSence != 1 && !(trk->status & TRACK_STATUS_CROSSING_BMP) && fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f)
					{
						trk->x[2] *= 0.5;
						trk->LngSpeedHeadingAngleCnt -= 2;
						trk->LngLowSpeedHeadingAngleCnt -= 2;
					}

					//// 针对被超车目标（特别是大车）跟踪点从车头移至车尾的变化，可能会导致BSD报警中断或退出早
					//if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 30.f / 3.6f
					//	&& trk->type == TRACK && (trk->status & TRACK_STATUS_MOVING_BMP) && trk->activeTrkCnt < 30
					//	&& trk->x[0] > 0.9f && trk->x[0] < 0.9f + 3.75f * 2 && trk->x[1] > 1.f && trk->x[1] < 3.f)
					//{
					//	float tmpY = trk->nearestPosition[1] > 0.5f ? trk->nearestPosition[1] : 0.5;
					//	trk->x[1] = LP_FILTER(trk->stored_last_x1[0], 0.9, tmpY);	// BSD报警区域内刚起批的目标纵向距离滤波
					//}
					// 过滤帧间纵向距离/速度变化异常的点
					if (trk->type == TRACK \
						&& (fabsf(trk->x[1] - trk->stored_last_x1[0]) > 1 || fabsf(trk->x[3] - trk->stored_last_outputX[3]) > 0.5) \
						&& fabsf(trk->stored_last_x1[0] - pCdiPkg->cdi[pTrkPkg->trk[i].idx_1].y) < 0.3 \
						&& trk->x[0] < 4 && trk->x[1] > -3)
					{
						pTrkPkg->trk[i].x[1] = LP_FILTER(trk->stored_last_x1[0], 0.5, pCdiPkg->cdi[pTrkPkg->trk[i].idx_1].y);
						if (IN_BSD_AREA(trk->x[0], trk->x[1])
							&& trk->activeTrkCnt < 5
							&& trk->x[3] > 15.f / 3.6f && trk->x[3] - trk->p_x[3] > 1.f)
						{
							;
						}
						else
						{
							trk->x[3] = trk->p_x[3];
						}
					}
					
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 25 / 3.6f
                        && config->isFront == 0
						&& !(trk->status & TRACK_STATUS_CROSSING_BMP)
						&& trk->type == TRACK && trk->activeTrkCnt < 35
						&& IN_BSD_AREA(trk->x[0], trk->x[1])
						&& trk->stored_last_outputX[3] > BSD_LNGVEL_THR)		// 大于超车抑制速度门限
					{
						// 位于BSD报警区域内的新起批目标，特定条件下维持纵向速度，以防止滤波后速度不准导致的误报
						if (trk->x[3] < 15 / 3.6f && trk->stored_last_outputX[3] - trk->x[3] > 1.f)
						{
							trk->x[3] = trk->stored_last_outputX[3];
						}
						else if (trk->x[3] - trk->stored_last_outputX[3] > 1.f)
						{
							trk->x[3] = LP_FILTER(trk->stored_last_outputX[3], 0.75, trk->x[3]);
						}
						else
						{
							trk->x[3] = LP_FILTER(trk->stored_last_outputX[3], 0.9, trk->x[3]);
						}
					}
					else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 12 / 3.6f && !(trk->status & TRACK_STATUS_CROSSING_BMP) && IN_BSD_AREA(trk->x[0], trk->x[1]) && trk->BSDCutInTargetCnt < 10 && config->isFront == 0)
					{
						rawLngSpeed = calcRawLngVelocity(pCdiPkg, trk);
						if (fabsf(rawLngSpeed) > 0.f && fabsf(rawLngSpeed) < 20.f)
						{
							// CANDI航迹可能会出现滤波值异常
							// 若hit==1，上一帧纵向速度未赋值
							if (rawLngSpeed > BSD_LNGVEL_THR)
							{
								// 优先选择rawLngSpeed
								trk->x[3] = fabsf(rawLngSpeed - trk->stored_last_outputX[3]) < 1.5f ? LP_FILTER(rawLngSpeed, SIDE_V_COEF, trk->stored_last_outputX[3]) : rawLngSpeed;
							}
							else if (trk->stored_last_outputX[3] > BSD_LNGVEL_THR)
							{
								// rawLngSpeed有效，选择滤波来维持
								// 由于护栏、干扰等情况，rawLngSpeed的计算可能存在偏差
								trk->x[3] = LP_FILTER(trk->stored_last_outputX[3], SIDE_V_COEF, rawLngSpeed);
							}
							else
							{
								// CANDI航迹暂时不考虑x[3]
								trk->x[3] = rawLngSpeed;
							}
						}
						else
						{
							if (trk->stored_last_outputX[3] > BSD_LNGVEL_THR)
							{
								// 优先维持
								trk->x[3] = trk->stored_last_outputX[3];
							}
							else if(fabsf(trk->stored_last_outputX[3]) > 0.1f)
							{
								trk->x[3] = LP_FILTER(trk->stored_last_outputX[3], SIDE_LNG_V_COEF, trk->x[3]);
							}
						}
						//被超车的大车目标关联点不是最近点，会导致纵向距离超出BSD区别;
                        //后角雷达被超车的目标纵向距离根据聚类组中最近纵向距离矫正
                        if (config->isFront == 0
                            && trk->startPosition[1] < 0.5f && trk->x[1] > 0.f && cdi->mea_z[2] > 0.f
                            && trk->status & TRACK_STATUS_MOVING_BMP && trk->x[3] > 0.f)
                        {
                            if (trk->idx_1 != trk->pidNearest && cdi->y > pCdiPkg->cdi[trk->pidNearest].y)
                            {
                                trk->x[1] = LP_FILTER(trk->p_x[1], 0.6f, pCdiPkg->cdi[trk->pidNearest].y);
                            }
                            else if (cdi->y > trk->nearestPosition[1])
                            {
                                trk->x[1] = LP_FILTER(trk->p_x[1], 0.6f, trk->nearestPosition[1]);
                            }
                        }
					}

                    /*
                    * 异侧临车道目标内飘问题,抑制横飘
                    * 注：自车静止时先不进入此策略，优先保证DOW走查斜停测试
                    */
                    RDP_FovEdgeFilterUpdate(&pCdiPkg->cdi[pTrkPkg->trk[i].idx_1], &pTrkPkg->trk[i], pRDP_inVehicleData, config);
					RDP_FovEdgeRCTAFilterUpdate(&pTrkPkg->trk[i]);

					//低速DOW中断问题抑制策略
					RDP_LowSpeedDOWUpdate(&pTrkPkg->trk[i], pRDP_inVehicleData);

					if(trk->activeTrkCnt == 1)
                    {
                        if(!(trk->status & TRACK_STATUS_MOVING_BMP))
                        {
                            pTrkPkg->trk[i].x[2] = 0;
                        }
                        else if(fabsf(pRDP_inVehicleData->vdySpeedInmps) > 5.0f && pCdiPkg->cdi[trk->idx_1].mea_z[1] > 20.0f)   //远处纵向目标增加速度估计经验权重
                        {
                            if(fabsf(pCdiPkg->cdi[trk->idx_1].mea_z[3]) < 15.0f)
                            {
                                float vx0, vy0, vx1, vy1, alpha;
                                vx0 = trk->x[2];
                                vy0 = trk->x[3];
                                vx1 = 0;
                                vy1 = pCdiPkg->cdi[trk->idx_1].mea_z[2] / cosf(pCdiPkg->cdi[trk->idx_1].mea_z[3] / 180.0f * M_PI);
                                alpha = (pCdiPkg->cdi[trk->idx_1].mea_z[1] - 20.0f) / 60.0f + (15.0f - fabsf(pCdiPkg->cdi[trk->idx_1].mea_z[3])) / 30.0f;
                                alpha = alpha < 0 ? 0 : alpha;
                                alpha = alpha > 0.9f ? 0.9f : alpha;
                                trk->x[2] = vx1 * alpha + vx0 * (1.0f - alpha);
                                trk->x[3] = vy1 * alpha + vy0 * (1.0f - alpha);
                            }
                            if(fabsf(pCdiPkg->cdi[trk->idx_1].mea_z[3]) < 45.0f)       //非横穿区域限制初始横向速度
                            {
                                trk->x[2] = trk->x[2] > 3.0f ? 3.0f : trk->x[2];
                                trk->x[2] = trk->x[2] < -3.0f ? -3.0f : trk->x[2];
                            }
                        }
                        if(trk->status & TRACK_STATUS_MOVING_BMP && trk->x[0] < -1.5f && trk->x[2] < -1.5f)  //异侧目标，严格限制异侧远离的横向初始速度
                        {
                            trk->x[2] = -1.5f;
                        }
						// 刚起批的目标，纵向速度太大，导致的问题，滤波平滑
						if (trk->x[3] > 80 / 3.6f)
						{
							trk->x[3] = LP_FILTER(pTrkPkg->trk[i].p_x[3], 0.5, pTrkPkg->trk[i].x[3]);
						}
						// 超车刚起批的目标 由于物理特性，检测的点都在目标外沿，所以，强制将起批的目标内移
						if (trk->x[1] < 0 && trk->x[0] > 3.2 && trk->x[0] < 5.2 && (trk->status & TRACK_STATUS_MOVING_BMP) && pRDP_inVehicleData->vdySpeedInmps > 15/3.6f)
						{
							trk->x[0] -= trk->x[0]/3.2;
						}
                    }
                    if(trk->activeTrkCnt <= 5 && trk->status & TRACK_STATUS_MOVING_BMP && trk->x[0] > 0 && trk->x[0] < 4.0f && trk->x[1] < 2.0f && config->isFront == 0)    //起始近处侧边运动目标，抑制负值横纵向速度，按经验值校正为零
                    {
                        trk->x[2] = trk->x[2] < 0 ? 0 : trk->x[2];
                        trk->x[3] = trk->x[3] < 0 ? 0 : trk->x[3];
                    }

					//防止斜穿RCW误报 在自车横向速度减小到1的时候，维持横向速度，直到出了正后方区域
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
						&& (trk->startPosition[0] > 5.f || trk->startPosition[0] < - 5.f )&& trk->x[0] > -2.f && trk->x[0] < 0.f 
						&& trk->startPosition[1] > 20 && (fabsf(trk->x[2]) < 1.f && fabsf(trk->stored_last_outputX[2]) > 1.f && trk->x[1] > 10))
					{
						trk->x[2] = trk->stored_last_outputX[2];
					}
					//横向速度一帧内反向的，且上一帧的横向速度大于0.5的，保持上一帧横向速度
					if (trk->type == TRACK && trk->x[2] * trk->stored_last_outputX[2] < 0.f \
						&& fabsf(trk->stored_last_outputX[2]) > 0.5f \
						&& !isCPTALFScene)
					{
						trk->x[2] = trk->stored_last_outputX[2];
					}
					// 纵向速度一帧内反向导致的航迹框反向、航迹框中心不前移等问题
					else if (trk->type == TRACK && trk->x[3] * trk->stored_last_outputX[3] < 0.f \
						&& fabsf(trk->stored_last_outputX[3]) > 0.5f \
						&& fabsf(trk->x[3]) < 1.f \
						&& !isCPTALFScene)
					{
						trk->x[3] = trk->stored_last_outputX[3];
					}

                    //优化F/RCTA-B 测试直穿情况下，纵向速度偏大或者变小的问题，严格识别直穿后，使用卡尔曼的结果再使用车速进行滤波处理下
                    if(fabsf(pRDP_inVehicleData->vdySpeedInmps) < (20 / 3.6f)
                        && fabsf(pRDP_inVehicleData->vdyYawRate) < 0.01f
                        && trk->trkCnt > STORE_X1_FRAME_NUM
                        && trk->status & TRACK_STATUS_MOVING_BMP
                        && trk->x[1] < 15.f //&& trk->x[1] > 1.f	// 针对吉利B柱位置横穿测项，暂不设置纵向距离下限
                        && trk->x[2] < -1.f
                        && fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) < 2.f)
                    {
                        //计算目标纵向，除去自车移动后的移动位移
                        objmoveLng = 0.0f;
                        objmoveLng = (trk->stored_last_x1[0] - trk->stored_last_x1[storedValidCnt - 1]);

						float diffLngRangeThr = 0.5f;
						//if (trk->status & TRACK_STATUS_ABS_CROSSING_BMP && trk->x[0] > -0.5f && trk->x[0] < 1.f && trk->x[1] < 4.f)
						//{
						//	// 当前帧周期有点异常，适当调整门限
						//	diffLngRangeThr = 1.f;
						//}

                        // 对于已判为绝对横穿的目标，当目标转弯运动时，关联点非最优点，纵向绝对位移一直满足横穿条件，导致纵向速度无法有效更新，后续容易切ID
                        // 加入聚类框判断
                        if (trk->status & TRACK_STATUS_CROSSING_BMP && (trk->objType == 1 || trk->objType == 2)
                            && fabsf(cdi->latLim[1] - cdi->latLim[0]) < 2.f * fabsf(cdi->lngLim[1] - cdi->lngLim[0])
                            && ((trk->x[1] - cdi->lngLim[0]) > 0.5f || (cdi->lngLim[1] - trk->x[1]) > 0.5f)
                            && trk->x[2] < (-10 / 3.6f))
                        {
                            diffLngRangeThr = 0.f;  // 不做处理
                        }

                        //如果目标移动的纵向距离和自车移动的纵向距离基本相当，认为是90度横穿，vy大小即为自车速度
                        if (fabsf(vehicleMoveRange - objmoveLng) < diffLngRangeThr)
                        {
							if (fabsf(trk->sim_z[3]) < 10.f || trk->x[0] < 0.5f)
							{
                                //当自车静止且检测点绝对静止时，不再维持
                                if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f && fabsf(cdi->mea_z[2]) < 0.1f)
                                {
                                    ;
                                }
                                // 对于横穿至正后方的目标，由于检测点速度不准且选点问题滤波后会导致速度跳变，故采用维持处理
                                else
                                {
                                    trk->x[2] = tmp_lastX[2];
                                }
							}
							trk->absCrossCnt = (trk->absCrossCnt < 0xFF) ? (trk->absCrossCnt + 1) : trk->absCrossCnt;
                            trk->status |= (TRACK_STATUS_CROSSING_BMP | TRACK_STATUS_ABS_CROSSING_BMP);
                            if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.2f)
                            {
                                if (fabsf(cdi->latLim[0] - trk->p_x[0]) < 0.75f)
                                {
                                    trk->x[0] = LP_FILTER(trk->x[0], 0.5f, cdi->latLim[0]);
                                }
                                else
                                {
                                    // 若相差较大，可能存在异常聚类或关联
                                    trk->x[0] = LP_FILTER(trk->x[0], 0.5f, trk->p_x[0]);
                                }
                            }
                            else
                            {
                                trk->x[0] = LP_FILTER(trk->x[0], 0.9f, cdi->latLim[0]);
                            }
                            trk->x[3] = pRDP_inVehicleData->vdySpeedInmps * 0.85f + trk->x[3] * 0.15f;
                        }
						else if (trk->status & TRACK_STATUS_ABS_CROSSING_BMP)
						{
							trk->absCrossCnt = (trk->absCrossCnt > 0) ? (trk->absCrossCnt - 1) : 0;
							if (trk->absCrossCnt <= 0)
							{
								trk->status &= ~TRACK_STATUS_ABS_CROSSING_BMP;
							}
						}
						//针对横穿急转的修改
						if (((trk->x[0] < 2.f && trk->x[0] > 1.7f) || (trk->x[0] > 0.7 && trk->x[0] < 1.f) || (trk->x[0] > 0.2 && trk->x[0] < 0.5f)) \
							&& (trk->objType == 1 || trk->objType == 2) \
							&& fabsf(vehicleMoveRange - objmoveLng) > 0.3f)
						{
							float storeTime = 0.f;
							for (int j = 0; j < storedValidCnt; j++)
							{
								storeTime += pTrkPkg->stored_frameNumber_time[j];
							}
							trk->x[2] *= 0.8f;
							if (storeTime > 0.2f && fabsf(vehicleMoveRange - objmoveLng) < 1.f)
							{
								trk->x[3] = fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 0.5 ? trk->x[3] : ((objmoveLng - vehicleMoveRange) / storeTime + pRDP_inVehicleData->vdySpeedInmps);
							}
						}
                    }
					else
					{
                        trk->absCrossCnt = 0;
						trk->status &= ~TRACK_STATUS_ABS_CROSSING_BMP;
					}

					// 横向运动方向车道存在目标的情况，抑制目标的横向速度
					if (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
						&& trk->status & TRACK_STATUS_MOVING_BMP
						&& !(trk->status & TRACK_STATUS_CROSSING_BMP)
						&& ((trk->lngMovingStatus & GRIDMAP_NEIGHBOUR_MINUS && trk->x[2] < 0.f)
							|| (trk->lngMovingStatus & GRIDMAP_NEIGHBOUR_PLUS && trk->x[2] > 0.f))
                        && (pRDP_inVehicleData->vdyGearState != GEAR_SIG_P))
					{
						trk->x[2] *= 0.25f;
                        trk->x[4] *= 0.25f;
					}

                    // 对地速度较大的邻车道目标抑制横向速度
                    if (fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
                        && trk->status & TRACK_STATUS_MOVING_BMP
                        && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && trk->activeTrkCnt > 12
                        && IN_NEIGHBOUR_LANE(trk->stored_last_x0[STORE_X0_FRAME_NUM - 1])
                        && IN_NEIGHBOUR_LANE(trk->x[0])
                        && trk->x[1] < 15.f
                        && IN_NEIGHBOUR_LANE(cdi->latLim[0])
                        && IN_NEIGHBOUR_LANE(cdi->latLim[1])
                        && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
                        && fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 5.f
                        && trk->BSDCutInTargetCnt < 10
						&& ((trk->LatCutInCnt == 0 && trk->LatCutOutCnt == 0) || fabsf(trk->x[2]) > 2.f)   //2m/s是为了限制FCTB的策略
						&& (config->isFront == 0 || (config->isFront == 1 && fabsf(trk->x[2]) > 1.f))) // 增加一个后角雷达限制，前角雷达横向速度大于1的才进，入为的是小速度切入
                    {
                        trk->x[2] *= 0.25f;
                        trk->x[4] *= 0.25f;
                    }

                    // 异侧邻车道超车目标刚出FOV边界时点迹不稳定，横向速度更新异常
                    if (pRDP_inVehicleData->vdyGearState == GEAR_SIG_D && config->isFront
                        && fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
                        && trk->status & TRACK_STATUS_MOVING_BMP
                        && !(trk->status & TRACK_STATUS_CROSSING_BMP)
                        && trk->activeTrkCnt > 12
                        && trk->x[1] > 10.f
                        && IN_OTHER_NEIGHBOUR_LANE(trk->stored_last_x0[STORE_X0_FRAME_NUM - 1])
                        && IN_OTHER_NEIGHBOUR_LANE(trk->x[0])
                        && IN_OTHER_NEIGHBOUR_LANE(cdi->latLim[0])
                        && IN_OTHER_NEIGHBOUR_LANE(cdi->latLim[1])
                        && fabsf(pRDP_inVehicleData->vdySpeedInmps) > (50 / 3.6f)
                        && fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 5.f)
                    {
                        trk->x[2] *= 0.25f;
                        trk->x[4] *= 0.25f;
                    }

                    /*
                    * 1、本车道/邻车道目标起步（已检出，静止转运动）横向速度异常
                    * 2、邻车道超车/被超车目标检出后横向速度异常
                    * 3、自车运动场景，邻车道正常纵行目标
                    * 4、本车道/邻车道目标刹停横向速度异常
                    * 5、邻车道/隔车道对象来车目标横向速度异常
                    * 6、自车运动场景，邻车道假点横向速度异常
                    */
                    if ((pRDP_inVehicleData->vdyGearState == GEAR_SIG_P || pRDP_inVehicleData->vdyGearState == GEAR_SIG_D)
                        && fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
                        && !(trk->status & TRACK_STATUS_CROSSING_BMP))
                    {
                        // 场景1
                        // 考虑可能冲突场景：FCTAB/RCTAB场景测试，目标起批晚（在隔/邻车道起批）或斜穿场景未被判断位横穿目标
                        if (trk->type == TRACK
                            && trk->activeTrkCnt > 12
                            && ((IN_HONE_LANE(trk->x[0]) && IN_HONE_LANE(trk->stored_last_x0[STORE_X0_FRAME_NUM - 1]) && IN_HONE_LANE(cdi->latLim[0]) && IN_HONE_LANE(cdi->latLim[1]))
                                || (IN_NEIGHBOUR_LANE(trk->x[0]) && IN_NEIGHBOUR_LANE(trk->stored_last_x0[STORE_X0_FRAME_NUM - 1]) && IN_NEIGHBOUR_LANE(cdi->latLim[0]) && IN_NEIGHBOUR_LANE(cdi->latLim[1])))
                            && trk->x[1] < 15.f
                            && trk->movingStatusCnt < 15)
                        {
                            trk->x[2] *= 0.25f;
                            trk->x[4] *= 0.25f;
                        }

                        // 场景2
                        // 自车静止或运动均有可能出现，暂只考虑邻车道目标
                        if (IN_NEIGHBOUR_LANE(trk->startPosition[0]) && trk->startPosition[1] < 1.f
                            && IN_NEIGHBOUR_LANE(trk->x[0]) && trk->x[1] < 10.f
                            && IN_NEIGHBOUR_LANE(cdi->latLim[0])
                            && IN_NEIGHBOUR_LANE(cdi->latLim[1]))
                        {
                            uint8_t tmpActiveThr = fabsf(trk->x[3]) > 2.f ? 15 : 30;// 低速目标考虑放大生命周期门限
                            if (trk->activeTrkCnt < tmpActiveThr)
                            {
                                // 超车目标CANDI阶段也抑制横向速度
                                trk->x[2] = (trk->type == CANDI) ? 0.f : (trk->x[2] * 0.25f);
                                trk->x[4] *= 0.25f;
                            }
                            else
                            {
                                ;/// 若超车目标起批后并行则归类于场景3
                            }

                        }
                        // 场景3
                        // 并行场景或正常已检出目标的靠近/远离
                        if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (10 / 3.6f)
                            && (trk->status & TRACK_STATUS_MOVING_BMP) && trk->activeTrkCnt > 12
                            && IN_NEIGHBOUR_LANE(trk->x[0]) && trk->x[1] < 30.f
                            && IN_NEIGHBOUR_LANE(trk->stored_last_x0[STORE_X0_FRAME_NUM - 1])
                            && fabsf(cdi->latLim[0] - cdi->latLim[1]) < 2.f
                            && IN_NEIGHBOUR_LANE(cdi->latLim[0]) && IN_NEIGHBOUR_LANE(cdi->latLim[1])
                            && fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 3.f * fabsf(trk->x[2])
                            && trk->BSDCutInTargetCnt < 10
							&& ((trk->LatCutInCnt == 0 && trk->LatCutOutCnt == 0) || fabsf(trk->x[2]) > 2.f))   //2m/s是为了限制FCTB的策略
                        {
                            trk->x[2] *= 0.25f;
                            trk->x[4] *= 0.25f;
                        }
                        //// 场景4
                        //if (((IN_HONE_LANE(trk->x[0]) && IN_HONE_LANE(cdi->latLim[0]) && IN_HONE_LANE(cdi->latLim[1]))
                        //    || (IN_NEIGHBOUR_LANE(trk->x[0]) && IN_NEIGHBOUR_LANE(cdi->latLim[0]) && IN_NEIGHBOUR_LANE(cdi->latLim[1]))))
                        //{
                        //    trk->x[2] = trk->x[2];
                        //}
                        // 场景5
                        if (config->isFront
                            && ((IN_NEIGHBOUR_LANE(trk->x[0]) && IN_NEIGHBOUR_LANE(cdi->latLim[0]) && IN_NEIGHBOUR_LANE(cdi->latLim[1]) && IN_NEIGHBOUR_LANE(trk->startPosition[0]))
                                || (IN_SEPARATED_LANE(trk->x[0]) && IN_SEPARATED_LANE(cdi->latLim[0]) && IN_SEPARATED_LANE(cdi->latLim[1]) && IN_SEPARATED_LANE(trk->startPosition[0])))
                            && trk->x[3] < -1.f && (trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) < (-10 / 3.6f) && (pRDP_inVehicleData->vdyGearState != GEAR_SIG_P))
                        {
                            trk->x[2] = (trk->type == CANDI) ? 0.f : (trk->x[2] * 0.25f);
                            trk->x[4] *= 0.25f;
                        }
                        // 场景5补充：三车道内存在3个及以上moved目标或正前方存在moved目标，抑制远处横漂目标
                        // 多是因为邻车道或隔车道有目标，遮挡情况下检测点不稳定，miss后容易横漂，可测试自车为首位且无遮挡目标情况下的对向来车
                        if ((trk->status & TRACK_STATUS_MOVING_BMP || fabsf(trk->x[2]) > 1.5f)
                            && (curFrameMovedTargetCnt >= 3 || (config->isFront && pRDP_inVehicleData->vdyGearState == GEAR_SIG_D && forwardMovingTarget))
                            && IN_NUMTHREE_LANE(trk->x[0]) && IN_NUMTHREE_LANE(trk->startPosition[0])
                            && (trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) < (-10 / 3.6f))
                        {
                            trk->x[2] = (trk->type == CANDI) ? 0.f : (trk->x[2] * 0.25f);
                            trk->x[4] *= 0.25f;
                        }

						if (curFrameMovedTargetCnt >= 3 \
							&& (trk->status & TRACK_STATUS_MOVING_BMP) \
							&& trk->activeTrkCnt > 20  
							&& fabsf(trk->x[3]) < 2.f \
							&& fabsf(pRDP_inVehicleData->vdySpeedInmps) < 5.f / 3.6f )
						{
							if (VehicleStaticTime < 2400)  // 考虑红绿灯时长不会超过两分钟
							{
								trk->x[2] *= 0.25f;
								trk->x[4] *= 0.25f;
							}
							else if(IN_THREE_LANES(trk->x[0]) && trk->x[1] < 50.f \
								&& trk->startPosition[0] < 8.f && trk->startPosition[0] > -5.f \
								&& abs(trk->LngSpeedHeadingAngleCnt - trk->activeTrkCnt) < 10 \
								&& abs(trk->LngLowSpeedHeadingAngleCnt - trk->activeTrkCnt) < 15)
							{
								trk->x[2] *= 0.25f;
								trk->x[4] *= 0.25f;
							}
						}

						if (VehicleMovingTime > 1200 \
							&& curFrameMovedTargetCnt >= 3 \
							&& VehicleStaticTime < 200 \
							&& fabsf(trk->x[3]) < 2.f
							&& fabsf(pRDP_inVehicleData->vdySpeedInmps) < 10.f / 3.6f &&\
							fabsf(trk->x[0]) < 6.f)
						{
							trk->x[2] *= 0.25f;
							trk->x[4] *= 0.25f;
						}


                        //// 场景6
                        //if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (10 / 3.6f)
                        //    && (IN_NEIGHBOUR_LANE(trk->x[0]) && IN_NEIGHBOUR_LANE(cdi->latLim[0]) && IN_NEIGHBOUR_LANE(cdi->latLim[1]) && IN_NEIGHBOUR_LANE(trk->startPosition[0])))
                        //{
                        //    ;
                        //}

                        //// 护栏外的目标抑制横向速度
                        //if (pTrkPkg->sildLineValid && trk->x[0] > pTrkPkg->sildLineDis && trk->x[1] < 30.f)
                        //{
                        //    trk->x[2] *= 0.25f;
                        //    trk->x[4] *= 0.25f;
                        //}
                    }

					//低速切入横向速度更新不及时
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.f \
						&& trk->x[1] > 1.f \
						&& trk->x[0] < 5.f \
						&& trk->x[0] > -0.5f \
						&& trk->activeTrkCnt > 12 \
						&& fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 10.f / 3.6f)
					{
						float temp_diff_x0 = trk->stored_last_x0[0] - trk->stored_last_x0[11];
						float temp_diff_x1 = trk->stored_last_x0[0] - trk->stored_last_x0[5];
						float temp_diff_x2 = trk->stored_last_x0[5] - trk->stored_last_x0[11];
						uint8_t cnt = 0, cntOut = 0;
						for (uint8_t j = 0; j < 11; j++)
						{
							if (trk->stored_last_x0[j] < trk->stored_last_x0[j + 1])
							{
								cnt++;
							}
							else
							{
								cntOut++;
							}
						}
						if ((temp_diff_x0 < -0.5f && temp_diff_x1 < -0.25f && temp_diff_x2 < -0.3f) \
							|| (cnt >= 8 && fabsf(temp_diff_x0) > 0.3f) \
							|| trk->LatCutInCnt > 5)
						{
							if (trk->x[2] < 0.f && trk->x[2] > -0.5f \
								&& ((trk->x[0] - trk->stored_last_x0[0] < 0.f) || (cdi->x - trk->stored_last_x0[0]) < 0.f))
							{
								trk->x[2] = -0.5f;
							}
							
						}
						else if ((temp_diff_x0 > 0.5f && temp_diff_x1 > 0.25f && temp_diff_x2 > 0.3f) \
							|| (cntOut >= 8 && fabsf(temp_diff_x0) > 0.3f) \
							|| trk->LatCutOutCnt > 5)
						{
							if (trk->x[2] > 0.f && trk->x[2] < 0.5f \
								&& ((trk->x[0] - trk->stored_last_x0[0] > 0.f) || (cdi->x - trk->stored_last_x0[0]) > 0.f))
							{
								trk->x[2] = 0.5f;
							}

						}
						if((temp_diff_x0 < -0.5f && temp_diff_x1 < -0.25f && temp_diff_x2 < -0.3f) || cnt >= 8)
						{
							trk->LatCutInCnt = trk->LatCutInCnt < 20 ? trk->LatCutInCnt + 1 : 20;
						}
						else
						{
							trk->LatCutInCnt = trk->LatCutInCnt > 0 ? trk->LatCutInCnt - 1 : 0;
						}

						if ((temp_diff_x0 > 0.5f && temp_diff_x1 > 0.25f && temp_diff_x2 > 0.3f) || cnt >= 8)
						{
							trk->LatCutOutCnt = trk->LatCutOutCnt < 20 ? trk->LatCutOutCnt + 1 : 20;
						}
						else
						{
							trk->LatCutOutCnt = trk->LatCutOutCnt > 0 ? trk->LatCutOutCnt - 1 : 0;
						}
					}
					else
					{
						trk->LatCutInCnt = trk->LatCutInCnt > 0 ? trk->LatCutInCnt - 1 : 0;
						trk->LatCutOutCnt = trk->LatCutOutCnt > 0 ? trk->LatCutOutCnt - 1 : 0;
					}
                    //RCTA异侧起批的目标，不更新纵向速度
                    if(trk->status & TRACK_STATUS_RFCTA_AREA_OBJ_BMP)
                    {
                        if(pTrkPkg->rctaAreaValid)
                        {
                            trk->x[2] = trk->x[2] * 0.9f + RDP_vcalc(trk,(pTrkPkg->rctaAreaMaxlat + pTrkPkg->rctaAreaMinlat) / 2, &trk->stored_last_x0[0],3) * 0.1f;
                            trk->x[3] = trk->x[3] * 0.1f;
                        }
                        //else
                        //{
                        //    trk->x[2] = x2;
                        //    trk->x[3] = 0;
                        //}
                    }

					// 对于横穿的目标，跟踪点和关联点的横向距离相差0.5m以上的， 横向距离平滑取线性滤波
					if (trk->status & TRACK_STATUS_CROSSING_BMP
                        && (cdi->x - tmp_lastX[0]) * tmp_lastX[2] > 0.f
                        && fabsf(trk->x[0] - pCdiPkg->cdi[trk->idx_1].x) > 0.5f
                        && fabsf(trk->x[2]) > 10.f / 3.6)
					{
						trk->x[0] = LP_FILTER(trk->x[0], 0.5, pCdiPkg->cdi[trk->idx_1].x);
					}

					// 自车正常行车场景，限制非横穿目标的最大横向速度
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 12.f / 3.6
						&& fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
						&& trk->type == TRACK
						&& !(trk->status & TRACK_STATUS_CROSSING_BMP)
						&& trk->LngSpeedHeadingAngleCnt > 20)
					{
						//float tempHeading = atan2f(trk->x[2], trk->x[3] - pRDP_inVehicleData->vdySpeedInmps)*RAD2ANG;
						trk->x[0] = LP_FILTER(trk->x[0], 0.5, tmp_lastX[0]);
						if (trk->x[2] < -1.5f )
						{
							if (fabsf(trk->x[3]) < 1.5f)
							{
								trk->x[2] = -(fabsf(trk->x[3])-0.1f);
							}
							else
							{
								trk->x[2] = -1.35f;//-fabsf(tanf(155.f / 180 * PI)*(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps));
							}
						}
						else if (trk->x[2] > 2.f)
						{
							trk->x[2] = 2.f;
						}
					}

					// 法规DOW遮挡测项场景识别后，抑制目标的横向速度
					if (trk->dowShelterScene)
					{
						trk->x[2] *= 0.3f;
					}

                    if (isCPTALFScene
                        && trk->type == TRACK
                        && (trk->status & TRACK_STATUS_MOVING_BMP)
                        && trk->isSuspPedestrian)
                    {
                        if (((trk->x[2] + pVdy->vcsLatVel) * (trk->stored_last_outputX[2] + pVdy->vcsLatVel) < 0.f)
                            || (fabsf(trk->x[2] + pVdy->vcsLatVel) < 0.2f && fabsf(trk->stored_last_outputX[2] - trk->x[2]) > 1.f)
                            || ((trk->stored_last_outputX[2] + pVdy->vcsLatVel) > 0.5f && (trk->stored_last_outputX[2] + pVdy->vcsLatVel) < 1.5f))
                        {
                            if (trk->stored_last_outputX[2] - trk->x[2] > 0.5f)
                            {
                                trk->x[2] = trk->stored_last_outputX[2] + trk->x[4] * time;
                                trk->x[4] *= 0.5;
                            }
                            else if (trk->x[0] < 0.5f \
                                && (trk->stored_last_outputX[0] - cdi->x) < 0.5f \
                                && (trk->stored_last_outputX[2] + pVdy->vcsLatVel) > 1.f \
                                && (trk->stored_last_outputX[2] + pVdy->vcsLatVel) < 1.5f)
                            {
                                trk->x[2] = trk->stored_last_outputX[2] + trk->x[4] * time;
                                trk->x[4] *= 0.5;
                            }
                        }
                        if (fabsf(trk->x[2] + pVdy->vcsLatVel) > 1.5f || fabsf(trk->x[3] + pVdy->vcsLngVel) > 1.5f)
                        {
                            if (trk->x[2] + pVdy->vcsLatVel < -5 / 3.6f)
                            {
                                trk->x[2] = LP_FILTER((-5 / 3.6f - pVdy->vcsLatVel), 0.75f, trk->x[2]);
                            }
                            else if (trk->x[2] + pVdy->vcsLatVel > 5 / 3.6f)
                            {
                                trk->x[2] = LP_FILTER((5 / 3.6f - pVdy->vcsLatVel), 0.75f, trk->x[2]);
                            }
                            else
                            {
                                trk->x[2] *= 0.95f;
                            }
                            if (trk->x[3] + pVdy->vcsLngVel < -5 / 3.6f)
                            {
                                trk->x[3] = LP_FILTER((-5 / 3.6f - pVdy->vcsLngVel), 0.75f, trk->x[3]);
                            }
                            else if (trk->x[3] + pVdy->vcsLngVel > 5 / 3.6f)
                            {
                                trk->x[3] = LP_FILTER((5 / 3.6f - pVdy->vcsLngVel), 0.75f, trk->x[3]);
                            }
                            else
                            {
                                trk->x[3] *= 0.95f;
                            }
                            trk->x[4] *= 0.25f;
                            trk->x[5] *= 0.25f;
                        }
                        else
                        {
                            ;
                        }
                    }
                    
					//提前起批的目标特殊处理
                    if(trk->status & TRACK_STATUS_PRE_TRECK_OBJ_BMP && trk->hit < 20)
                    {
                        trk->x[0] = x[0] * 0.9f + pCdiPkg->cdi[trk->idx_1].x * 0.1f;
                        trk->x[1] = pCdiPkg->cdi[trk->idx_1].y;
                        trk->x[2] = 0.f;
                        trk->x[3] =  pCdiPkg->cdi[trk->idx_1].mea_z[2];
                    }

					// 同距同速场景测项，点迹检测不稳定导致的两个目标互相拉扯，限制横向速度
					if (RDP_getTrackConfigPointer()->radarResolutionTestMode == TEST_MODE_RESOLUTION
						&& fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
						&& trk->status & TRACK_STATUS_MOVING_BMP && !(trk->status & TRACK_STATUS_CROSSING_BMP)
						&& trk->type == TRACK
						&& trk->x[1] < 60.f && trk->x[3] < -1.f)
					{
						trk->x[0] = x[0] * 0.9f + pCdiPkg->cdi[trk->idx_1].x * 0.1f;
						trk->x[2] *= 0.2f;
					}

                    if(trk->trkCnt > 25 || trk->sim_z[1] > 5)
                    {
                        //远处稳定跟踪纵行目标，限制每帧vx变化率，减少横飘
                        // 暂定自车静止时先不限制.  自车静止时限制 会导致DOW真正的斜传目标,横向速度被抑制. 当前比亚迪又比较关注TTC
                        if(fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.0f
							&& trk->sim_z[1] > 30.0f
							&& fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 5.0f
							&& (trk->activeTrkCnt > 20 || trk->sim_z[1] > 60.0f)
                            && (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f))   
                        {
                            dvxmax = 0.15f + fabsf(tmp_lastX[2]) * 0.1f;
                            dvxmax = fabsf(trk->x[2]) > fabsf(tmp_lastX[2]) ? dvxmax : dvxmax * 3.0f;     //横向速度减小时，放宽抖动门限
                            if(fabsf(trk->x[2] - tmp_lastX[2]) > dvxmax)
                            {
                                if(trk->x[2] > tmp_lastX[2])
                                {
                                    trk->x[2] = tmp_lastX[2] + dvxmax;
                                }
                                else
                                {
                                    trk->x[2] = tmp_lastX[2] - dvxmax;
                                }
                            }
                        }
                    }
                    // 根据group拟合出Vx,Vy，并与航迹Vx,Vy滤波
                    if (trk->idx_1 != FILTER_NON_ASSOC)
                    {
                        uint16_t groupId = pCdiPkg->cdi[trk->idx_1].groupId;
                        uint32_t groupNum = gGroupInfo[groupId].pointList.count;
                        float group_vx_vy[2] = { 0.f };
                        float ekfVz = sinf(mea_z[2] * DEG2RAD) * trk->x[2] + cosf(mea_z[2] * DEG2RAD) * trk->x[3];
                        float vzDiff = fabsf(ekfVz - mea_z[1]);
                        float vzdiffDiv = fabsf(mea_z[1]) > 0.1f ? vzDiff / fabsf(mea_z[1]) : 0.f;
                        if((fabsf(pRDP_inVehicleData->vdyCurveRadius) < 60 && groupNum >= 3)  // 大转弯场景
                            || (vzdiffDiv > 0.05f && fabsf(trk->x[2]) > 1.f && trk->x[0] < 0.9f + LANE_WIDTH * 3 && groupNum >= 4) // 近车道横向速度大
                            || (trk->type == CANDI && groupNum >= 3) // 起批场景
                            || (vzdiffDiv > 0.2f && fabsf(mea_z[1]) > 2.f && groupNum >= 6) // 航迹速度和关联点速度偏差大场景
                            || (vzdiffDiv > 0.05f && ANGLE_IN_CROSS_AREA(trk->sim_z[3], 25) && fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) > 2.f && fabsf(mea_z[1]) > 2.f && groupNum >= 8)) // 纵向速度盲区且纵向速度大
                        {
                            uint8_t result = trackVxVyEstFromGroup(pCdiPkg, pRDP_inVehicleData, groupId, group_vx_vy);
                            float groupVz = sinf(mea_z[2] * DEG2RAD) * group_vx_vy[0] + cosf(mea_z[2] * DEG2RAD) * group_vx_vy[1];
                            float groupVzDiff = fabsf(groupVz - mea_z[1]);
                            if (result && groupVzDiff < vzDiff)
                            {
                                float filterCoef = 0.f;
                                if (vzdiffDiv > 0.8f || fabsf(pRDP_inVehicleData->vdyCurveRadius) < 30)
                                    filterCoef = 0.25f;
                                else if (vzdiffDiv > 0.25f || fabsf(pRDP_inVehicleData->vdyCurveRadius) < 60)
                                    filterCoef = 0.5f;
                                else
                                    filterCoef = 0.75f;

                                if (result & SOLVE_GROUR_SUCCESS_VX)
                                    trk->x[2] = LP_FILTER(trk->x[2], filterCoef, group_vx_vy[0]);
                                if (result & SOLVE_GROUR_SUCCESS_VY)
                                    trk->x[3] = LP_FILTER(trk->x[3], filterCoef, group_vx_vy[1]);
                                // 横向速度盲区，且速度偏差较大，说明出现纵向速度了，此时横向速度适当减小
                                if (fabsf(trk->x[2]) > 3.f && !(result & SOLVE_GROUR_SUCCESS_VX) && vzdiffDiv > 0.15f)
                                    trk->x[2] = LP_FILTER(trk->x[2], 0.75f, 0.f);
                            }
                        }
                    }

					// 针对走查场景，邻车道运动目标在其外侧带起的谐波假点导致的外侧护栏等静止目标转运动内飘等问题
					// 邻车道后向来车（特别是大车）会因为遮挡导致护栏识别失败
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
						&& !(trk->status & TRACK_STATUS_MOVING_BMP) && !(trk->status & TRACK_STATUS_MOVED_BMP)
						&& trk->type == TRACK
						&& cdi->y < 3.f
						&& (fabsf(cdi->mea_z[2]) < 0.1f || isTruckMultipath(pTrkPkg, i)))
					{
						trk->x[0] = trk->p_x[0];
						trk->x[1] = trk->p_x[1];
						trk->x[2] = 0.f;
						trk->x[3] = 0.f;
						trk->x[4] = 0.f;
						trk->x[4] = 0.f;
					}
                   
                    for (j = 0; j < 2; j++)
                    {
                        pTrkPkg->trk[i].sigma[j] = tmp_sigma[j];
                    }
                    for (j = 0; j < 36; j++)
                    {
                        pTrkPkg->trk[i].P[j] = tmp_P[j];
                    }
                }

				// ekf后x[]状态有调整，在此统一重新计算
				trk->sim_z[0] = pCdiPkg->cdi[trk->idx_1].mea_z[0];
				trk->sim_z[1] = sqrtf(trk->x[0] * trk->x[0] + trk->x[1] * trk->x[1]);
				trk->sim_z[2] = (trk->x[0] * trk->x[2] + trk->x[1] * trk->x[3]) / trk->sim_z[1];
				trk->sim_z[3] = atan2f(trk->x[0], trk->x[1]) / 3.1415926f * 180.0f;

#ifdef INCLUDE_HEIGHT_ANGLE
                float middle;
                if(pCdiPkg->cdi[trk->idx_1].heighAngle > trk->stored_last_z[0][MEASURED_VALUE_NUM-1])
                {
                    if(trk->stored_last_z[0][MEASURED_VALUE_NUM-1] > trk->stored_last_z[1][MEASURED_VALUE_NUM-1])
                        middle = trk->stored_last_z[0][MEASURED_VALUE_NUM-1];
                    else if(pCdiPkg->cdi[trk->idx_1].heighAngle > trk->stored_last_z[1][MEASURED_VALUE_NUM-1])
                        middle = trk->stored_last_z[1][MEASURED_VALUE_NUM-1];
                    else
                        middle = pCdiPkg->cdi[trk->idx_1].heighAngle;
                }else
                {
                    if(trk->stored_last_z[0][MEASURED_VALUE_NUM-1] < trk->stored_last_z[1][MEASURED_VALUE_NUM-1])
                        middle = trk->stored_last_z[0][MEASURED_VALUE_NUM-1];
                    else if(pCdiPkg->cdi[trk->idx_1].heighAngle > trk->stored_last_z[1][MEASURED_VALUE_NUM-1])
                        middle = pCdiPkg->cdi[trk->idx_1].heighAngle;
                    else
                        middle = trk->stored_last_z[1][MEASURED_VALUE_NUM-1];
                }
                trk->heighAngle = trk->heighAngle*(2.f/3.f)+middle*(1.f/3.f);
#endif
                trk->rcs = (trk->rcs * 3 + pCdiPkg->cdi[trk->idx_1].rcs) >> 2; // trkRcs = trkRcs*75% + pointRcs*25%;
                objProbOfExistUpdate(trk, &pCdiPkg->cdi[trk->idx_1]);
            }
        }
        else
        {
            if (trk->type == CANDI)
            {
                trk->x[0] = trk->x[0] + trk->x[2] * time;      //使用匀速模型近似处理
                trk->x[1] = trk->x[1] + trk->x[3] * time;
            }
            else
            {
                RDP_runMainEKF(EKF_A, EKF_Q, pTrkPkg->trk[i].sigma, pTrkPkg->trk[i].P, EKF_R,
                            pTrkPkg->trk[i].x, 1, NULL, kalTime,
                            tmp_sigma, tmp_P, tmp_x,
                            (pTrkPkg->trk[i].sim_z) + 1, ((pTrkPkg->trk[i].pre_z) + 1));
				//针对斜穿没有关联点的正后方目标，限制其横向加速度。
				if ((trk->status & TRACK_STATUS_SKEW_CROSSING_BMP) \
					&& trk->x[0] < 0.f && trk->x[0] > -3.f \
					&& trk->x[1] > 5.f && trk->x[1] < -0.5f)
				{
					tmp_x[4] *= 0.2f;
				}

				memcpy(&tmp_lastX[0], &trk->x[0], sizeof(float) * 4);
                for (j = 0; j < 2; j++)
                {
                    pTrkPkg->trk[i].sigma[j] = tmp_sigma[j];
                }
                for (j = 0; j < 36; j++)
                {
                    pTrkPkg->trk[i].P[j] = tmp_P[j];
                }
                if(trk->status & TRACK_STATUS_MOVING_BMP)
                {
                    memcpy(pTrkPkg->trk[i].x, pTrkPkg->trk[i].p_x, sizeof(pTrkPkg->trk[i].x));
                    memcpy(pTrkPkg->trk[i].sim_z, pTrkPkg->trk[i].pre_z, sizeof(pTrkPkg->trk[i].sim_z));

					// 自车正常行车场景，限制非横穿目标的最大横向速度
					if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 12.f / 3.6
						&& trk->type == TRACK
						&& !(trk->status & TRACK_STATUS_CROSSING_BMP)
						&& trk->LngSpeedHeadingAngleCnt > 20)
					{
						trk->x[0] = tmp_lastX[0];
						trk->x[2] *= 0.5f;
					}
                }
                else
                {
                    trk->x[1] = trk->x[1] + pRDP_inVehicleData->vdySpeedInmps*time;
                    trk->sim_z[0] = 0;
                    trk->sim_z[1] = sqrtf(trk->x[0]*trk->x[0]+trk->x[1]*trk->x[1]);
                    if(trk->sim_z[1] < 0.1f)
                        trk->sim_z[1] = 0.1f;
                    trk->sim_z[2] = (trk->x[0]*trk->x[2] + trk->x[1]*trk->x[3])/trk->sim_z[1];
                    trk->sim_z[3] = atan2f(trk->x[0], trk->x[1]) *RAD2ANG;
                }
				if (trk->keepInBackCnt > 10)
				{
					trk->x[3] *= 0.2f;
					trk->x[5] = 0;
				}

                RDP_FovEdgeFilterUpdate(NULL, &pTrkPkg->trk[i], pRDP_inVehicleData, config);
                objProbOfExistUpdate(trk, NULL);
            }
        }
		
        if (trk->type == TRACK)
        {
			//自车低速行驶，目标也低速行驶在90度附近，容易造成速度反向。
			/*if (trk->LngSpeedHeadingAngleCnt > 100 \
				&& trk->x[0] > -2.f &&trk->x[0] < 6.f \
				&& trk->x[1] < 3.f \
				&& fabsf(pRDP_inVehicleData->vdySpeedInmps) < 20.f / 3.6f \
                && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
				&& (trk->headingAngle < 150.f || trk->headingAngle > -150.f) \
				&& fabsf(trk->x[2]) < 1.f)
			{
				trk->x[2] *= 0.5;
				if (trk->x[3] - pRDP_inVehicleData->vdySpeedInmps > 0.f)
				{
					trk->x[3] = -trk->x[3];
				}
			}*/
			float lastHeadingAngle = trk->headingAngle;
			float vcsLat = 0, vcsLng = 0;
			if (config->installPosition == 5 || config->installPosition == 7)
			{
				vcsLat = -pVdy->vcsLatVel;
			}
			else
			{
				vcsLat = pVdy->vcsLatVel;
			}

			if (config->installPosition == 6 || config->installPosition == 7)
			{
				vcsLng = -pVdy->vcsLngVel;
			}
			else
			{
				vcsLng = pVdy->vcsLngVel;
			}

			trk->headingAngle = atan2f(trk->x[2] + vcsLat, trk->x[3] - vcsLng)*RAD2ANG;
			if (trk->crossAllCnt >= 20 && trk->activeTrkCnt > 20 \
				&& (trk->x[0] < 0 && trk->x[1] < 5 && trk->x[1] > 0) \
				&& fabsf(lastHeadingAngle) > 65 && fabsf(lastHeadingAngle - trk->headingAngle) > 30.f)
			{
				trk->headingAngle = lastHeadingAngle;
			}
			else if (trk->activeTrkCnt > 20 && trk->status & TRACK_STATUS_MOVING_BMP)
			{
				// 横穿目标起始位置到目前位置的横向距离大于20m， 现在的位置在车的后方，且纵向距离在0到5m内的，上一帧的航向角小于45度的，这一帧的航向角大于45的，直接调回来。
				if (fabsf(trk->startPosition[0] - trk->x[0]) > 20\
					&& (trk->x[0] < 0.f && trk->x[0] > -2.5f && trk->x[1] < 5 && trk->x[1] > 0) \
					&& (fabsf(lastHeadingAngle) < 45 && fabsf(trk->headingAngle) > 45))
				{
					;
				}
				else if (((fabsf(trk->headingAngle - lastHeadingAngle) > 40 || ((fabsf(trk->startPosition[0] - trk->x[0]) > 20) && trk->x[0] < 0))) \
					&& (trk->x[0] < 0 && trk->x[1] < 5 && trk->x[1] > 0))
				{
					trk->headingAngle = lastHeadingAngle;
				}
				else if (fabsf(trk->headingAngle - lastHeadingAngle) > 20 \
					&& fabsf(trk->headingAngle - lastHeadingAngle) < 90
					&& (trk->x[0] > -2 && trk->x[0] < 3.75f) && (trk->x[1] < 8) \
					&& fabsf(trk->sim_z[2]) < 20 / 3.6f && trk->activeTrkCnt > 20)
				{
					trk->headingAngle = LP_FILTER(lastHeadingAngle, 0.9, trk->headingAngle);
				}
				else
				{
					if (fabsf(lastHeadingAngle - trk->headingAngle) < 90)
						trk->headingAngle = LP_FILTER(lastHeadingAngle, 0.5, trk->headingAngle);
				}
			}
			// 低速行驶航向角大于175°的次数统计或者小于5°
			if ((fabsf(trk->headingAngle) > 175.f || fabsf(trk->headingAngle) < 5.f) && fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) < 4.f)
			{
				trk->LngLowSpeedHeadingAngleCnt = trk->LngLowSpeedHeadingAngleCnt >= 127 ? trk->LngLowSpeedHeadingAngleCnt : trk->LngLowSpeedHeadingAngleCnt + 1;
			}

			if (fabsf(trk->headingAngle) > 150.f || fabsf(trk->headingAngle) < 10.f)
			{
				if (trk->LngSpeedHeadingAngleCnt < 127)
					trk->LngSpeedHeadingAngleCnt += 1;
			}
			else if ((trk->headingAngle < -60.f && trk->headingAngle > -120.f))
			{
				if (trk->LngSpeedHeadingAngleCnt > 0)
					trk->LngSpeedHeadingAngleCnt -= 1;
			}
			else
			{
				if (trk->LngSpeedHeadingAngleCnt > 0)
					trk->LngSpeedHeadingAngleCnt -= 1;
			}

            RDP_updateObjType(trk, pRDP_inVehicleData);
            RDP_updateObjReferPosi(trk);
        }

        memmove(&trk->stored_last_x0[1], &trk->stored_last_x0[0], sizeof(float) * (STORE_X0_FRAME_NUM - 1));
        memmove(&trk->stored_last_x1[1], &trk->stored_last_x1[0], sizeof(float) * (STORE_X1_FRAME_NUM - 1));
        if((trk->status & TRACK_STATUS_RFCTA_AREA_OBJ_BMP) && pTrkPkg->rctaAreaValid)
        {
            trk->stored_last_x0[0] = (pTrkPkg->rctaAreaMaxlat + pTrkPkg->rctaAreaMinlat) / 2;
            trk->stored_last_x1[0] = (pTrkPkg->rctaAreaMaxlng + pTrkPkg->rctaAreaMinlng) / 2;
        }
        else
        {
            trk->stored_last_x0[0] = trk->x[0];
            trk->stored_last_x1[0] = trk->x[1];
        }
        memmove(&trk->stored_last_status[1], &trk->stored_last_status[0], sizeof(uint16_t) * (STORE_POINT_NUM - 1));
        //memmove(&trk->stored_last_range[1], &trk->stored_last_range[0], sizeof(float) * (STORE_RANGE_FRAME_NUM - 1));
        //trk->stored_last_range[0] = trk->sim_z[1];
        for(j = STORE_POINT_NUM-1; j > 0; j--)
        {
            memcpy(&trk->stored_last_z[j][0], &trk->stored_last_z[j-1][0], sizeof(float)*MEASURED_VALUE_NUM);
        }
        if(trk->idx_1 != POINT_ID_INVALID && trk->idx_1 != POINT_ID_CLUSTERED)
        {
            memcpy(&trk->stored_last_z[0][0], &pCdiPkg->cdi[trk->idx_1].mea_z[0], sizeof(float)*4);
            trk->stored_last_status[0] = pCdiPkg->cdi[trk->idx_1].status;
			trk->stored_last_xy[0] = pCdiPkg->cdi[trk->idx_1].x;
			trk->stored_last_xy[1] = pCdiPkg->cdi[trk->idx_1].y;
        }
        else
        {
            memcpy(&trk->stored_last_z[0][0], &trk->sim_z[0], sizeof(float)*4);
            trk->stored_last_status[0] = trk->stored_last_status[1];
			trk->stored_last_xy[0] = trk->x[0];
			trk->stored_last_xy[1] = trk->x[1];
        }
        trackBoxUpdate(trk);
        if(trk->status & TRACK_STATUS_MOVED_BMP)
        {
            if((trk->trkCnt & 7) == 0)
            {
                trackBoxBufferAge(&trk->lengthBuff);
                trackBoxBufferAge(&trk->widthBuff);
            }
            if(trk->trkCnt > 20 && trk->status & TRACK_STATUS_MOVING_BMP)
            {
                /* identify the box basing on velocity */
                trackBoxBasingOnVelocity(trk, pRDP_inVehicleData->vdySpeedInmps);
            }
        }else
        {
            trackBoxBufferAge(&trk->lengthBuff);
            trackBoxBufferAge(&trk->widthBuff);
            trk->front = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_FRONT_INIT;
            trk->back = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_BACK_INIT;
            trk->left = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_LEFT_INIT;
            trk->right = CLASS_PEDESTRIAN_DISTANCE_TO_BOX_RIGHT_INIT;
        }
    }
    //自车若干帧移动的位移
    memmove(&pTrkPkg->stored_move_range[1], &pTrkPkg->stored_move_range[0], sizeof(float) * (STORE_X1_FRAME_NUM - 1));
    pTrkPkg->stored_move_range[0] = pRDP_inVehicleData->vdySpeedInmps * time;
	memmove(&pTrkPkg->stored_frameNumber_time[1], &pTrkPkg->stored_frameNumber_time[0], sizeof(float) * (STORE_X1_FRAME_NUM - 1));
	pTrkPkg->stored_frameNumber_time[0] = time;
    RDP_TRACK_judgeBasementScene(pCdiPkg, pTrkPkg);
    RDP_TRACK_judgeSideCarScene(pCdiPkg, pTrkPkg, pRDP_inVehicleData);
}

/**
 * @brief 判断地库场景，确认墙体目标并划分遮挡区
 * @param pTrkPkg 航迹列表
 * @param
 */
#define X_BEG_THRESHOLD     -0.5f
#define X_END_THRESHOLD     2.0f
#define Y_BEG_THRESHOLD     1.0f
#define Y_END_THRESHOLD     30.0f
#define RNAGE_MIN_THRESHOLD 0.5f    // 镜面目标最小有效距离
#define RNAGE_MAX_THRESHOLD 2.5f    // 镜面目标最大有效距离
#define SNR_THRESHOLD       38      // 真实目标SNR门限（有干扰时，SNR会波动）
#define SNR_THRESHOLD_DDM	50		// DDM调整该阈值，抑制保杠假点
#define MIR_TARGET_OFFSET   0.5f
void RDP_TRACK_judgeBasementScene(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg)
{
    trk_t* trk;
    int8_t min_idx = -1, staticPointCnt = 0;
    float minValue = 1000.0f;
    float xBegThred, xEndThred, yBegThred, yEndThred;
    uint16_t mode;
    rdp_config_t* config = RDP_getTrackConfigPointer();

    for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrkPkg->trk[i];
        if (trk->type < TRACK
            || trk->idx_1 < 0
            || trk->status & TRACK_STATUS_MOVED_BMP
            || trk->sim_z[1] < RNAGE_MIN_THRESHOLD
            || trk->sim_z[1] > RNAGE_MAX_THRESHOLD
            || trk->sim_z[3] > 100.f
            || trk->sim_z[3] < -10.f)
            continue;

        // 记录最近的静止目标（镜面目标）
        if (pCdiPkg->cdi[trk->pidStrongest].mea_z[0] > SNR_THRESHOLD_DDM)// && trk->trkCnt > 100)
        {
            if (trk->sim_z[1] < minValue)
            {
                minValue = trk->sim_z[1];
                min_idx = i;
            }
        }
    }

    if (min_idx >= 0)
    {
        // 根据墙体目标位置判断车辆是横停或竖停
        if (pTrkPkg->trk[min_idx].sim_z[3] > 45.f)
        {
            // TODO：是否需要判断该墙体目标的位置是否异常？
            // 墙体目标位于雷达侧方
            xBegThred   = X_BEG_THRESHOLD;
            xEndThred   = X_END_THRESHOLD;
            yBegThred   = Y_BEG_THRESHOLD;
            yEndThred   = Y_END_THRESHOLD;
            mode = 0;
        }
        else
        {
            // 墙体目标位于雷达前方
            xBegThred   = Y_BEG_THRESHOLD;
            xEndThred   = Y_END_THRESHOLD;
            yBegThred   = X_BEG_THRESHOLD;
            yEndThred   = X_END_THRESHOLD;
            mode = 1;
        }

        // 根据停放方向统计静止点分布
		for (int j = 0; j < pCdiPkg->number; j++)
        {
            if (!(pCdiPkg->cdi[j].status & POINT_STATUS_DYNAMIC_BMP)
				&& !(pCdiPkg->cdi[j].status & POINT_STATUS_GUARDRAIL_BMP)
				&& !(pCdiPkg->cdi[j].status & POINT_STATUS_GUARDRAIL_OUTSIDE_BMP)
                && pCdiPkg->cdi[j].x > xBegThred && pCdiPkg->cdi[j].x < xEndThred
                && pCdiPkg->cdi[j].y > yBegThred && pCdiPkg->cdi[j].y < yEndThred)
            {
				staticPointCnt++;
            }
        }
    }

    if (min_idx >= 0 && staticPointCnt >= 5 && 0 == config->isFront)
    {
        isBasementWallScene     = 1;
        basementSceneCnt        = 100;  //延迟退出（目标经过时会遮挡影响静止目标统计）
        gBWPoint.parkingMode    = mode;
        gBWPoint.angle          = pTrkPkg->trk[min_idx].sim_z[3];
        gBWPoint.x              = mode ? (pTrkPkg->trk[min_idx].x[0] + MIR_TARGET_OFFSET) : pTrkPkg->trk[min_idx].x[0];
        gBWPoint.y              = mode ? pTrkPkg->trk[min_idx].x[1] : (pTrkPkg->trk[min_idx].x[1] + MIR_TARGET_OFFSET);
    }
    else
    {
        if (isBasementWallScene)
        {
            basementSceneCnt = (basementSceneCnt > 0) ? (basementSceneCnt - 1) : 0;
            if (basementSceneCnt == 0)
            {
                isBasementWallScene = 0;
                memset(&gBWPoint, 0.0f, sizeof(gBWPoint));
            }
        }
        else
        {
            ;///TODO
        }
    }
}

/**
 * @brief 判断车辆护栏场景并划分遮挡区
 * @param pCdiPkg 点迹列表
 * @param pTrkPkg 航迹列表
 */
#define X_BEG_THRESHOLD1        0.0f   // 车辆原始点扩大横向范围：0~3
#define X_END_THRESHOLD1        3.0f
#define Y_BEG_THRESHOLD1        -1.0f
#define Y_END_THRESHOLD1        30.0f
#define RNAGE_MIN_THRESHOLD1    0.5f    // 车辆目标最小/最大有效距离
#define RNAGE_MAX_THRESHOLD1    2.5f
#define SNR_THRESHOLD1          50      // 车辆目标SNR较大
#define CAR_TARGET_OFFSET       0.5f    // 0.5~1.0
void RDP_TRACK_judgeSideCarScene(cdi_pkg_t *pCdiPkg, trk_pkg_t *pTrkPkg, VDY_DynamicEstimate_t* pRDP_inVehicleData)
{
    trk_t* trk;
    int8_t min_idx = -1, staticPointCnt = 0;
    float minValue = 1000.0f;
    float xBegThred, xEndThred, yBegThred, yEndThred;
    uint16_t mode;
    float curPointY = 0.f;
    rdp_config_t* config = RDP_getTrackConfigPointer();

    for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrkPkg->trk[i];
        if (trk->type < TRACK
            || trk->idx_1 < 0
            || trk->status & TRACK_STATUS_MOVING_BMP
            || trk->sim_z[1] < RNAGE_MIN_THRESHOLD1
            || trk->sim_z[1] > RNAGE_MAX_THRESHOLD1
            //|| trk->sim_z[3] > 100.f
            || trk->sim_z[3] < -10.f)
            continue;

        // 记录最近的静止目标
        if (pCdiPkg->cdi[trk->pidStrongest].mea_z[0] > SNR_THRESHOLD1)// && trk->trkCnt > 100)
        {
            if (trk->sim_z[1] < minValue)
            {
                minValue = trk->sim_z[1];
                min_idx = i;
            }
        }
    }

    if (min_idx >= 0)
    {
        // 根据墙体目标位置判断车辆是横停或竖停
        if (pTrkPkg->trk[min_idx].sim_z[3] > 45.f)
        {
            // TODO：是否需要判断该墙体目标的位置是否异常？
            // 墙体目标位于雷达侧方
            xBegThred   = X_BEG_THRESHOLD1;
            xEndThred   = X_END_THRESHOLD1;
            yBegThred   = Y_BEG_THRESHOLD1;
            yEndThred   = Y_END_THRESHOLD1;
            mode = 0;
        }
        else
        {
            // 墙体目标位于雷达前方
            xBegThred   = Y_BEG_THRESHOLD1;
            xEndThred   = Y_END_THRESHOLD1;
            yBegThred   = X_BEG_THRESHOLD1;
            yEndThred   = X_END_THRESHOLD1;
            mode = 1;
        }

        curPointY = pTrkPkg->trk[min_idx].x[1];

        // 根据停放方向统计连续静止点分布
        for (uint32_t j = 0; j < pCdiPkg->number; j++)
        {
            /*
             * 中间会有截断，暂时不考虑连续性
            if (pCdiPkg->cdi[j].y - curPointY > 5.f)
                continue;
            */

            // 这里存在一个问题, RCTA法规测试时, 两侧有遮挡车  当自车后方十几米有车辆或者围墙等时,如果不考虑连续性, 会导致遮挡区域误判. 
            // 对于截断的场景要如何处理?  把挡位限制在R挡时才考虑连续性.
            if (pRDP_inVehicleData->vdyGearState == GEAR_SIG_R)
            {
                if (pCdiPkg->cdi[j].y - curPointY > 5.f)
                    continue;
            }

            if (!(pCdiPkg->cdi[j].status & POINT_STATUS_DYNAMIC_BMP)
                && pCdiPkg->cdi[j].x > xBegThred && pCdiPkg->cdi[j].x < xEndThred
                && pCdiPkg->cdi[j].y > yBegThred && pCdiPkg->cdi[j].y < yEndThred)
            {
                curPointY = pCdiPkg->cdi[j].y;
                staticPointCnt++;
            }
        }
    }

    if (min_idx >= 0 && staticPointCnt >= 5 && 0 == config->isFront)
    {
        isSideCarScene          = 1;
        sideCarSceneCnt         = 100;  // 延迟退出
        gSCPoint.parkingMode    = mode;
        gSCPoint.angle          = pTrkPkg->trk[min_idx].sim_z[3];
        gSCPoint.x              = mode ? (pTrkPkg->trk[min_idx].x[0] + CAR_TARGET_OFFSET) : pTrkPkg->trk[min_idx].x[0];
        //gSCPoint.y              = mode ? pTrkPkg->trk[min_idx].x[1] : (pTrkPkg->trk[min_idx].x[1] + MIR_TARGET_OFFSET);
        gSCPoint.y              = curPointY;
    }
    else
    {
        if (isSideCarScene)
        {
            sideCarSceneCnt = (sideCarSceneCnt > 0) ? (sideCarSceneCnt - 1) : 0;
            if (sideCarSceneCnt == 0)
            {
                isSideCarScene = 0;
                memset(&gSCPoint, 0.0f, sizeof(gSCPoint));
            }
        }
        else
        {
            ;///TODO
        }
    }
}

#define ANGLE_THRESHOLD_IN_AERA 30
bool RDP_TRACK_inCoverAera(float x, float y)
{
    bool rst = false;
    uint16_t mode;
    float angle = 0;

    if (isBasementWallScene)
    {
        mode = gBWPoint.parkingMode;
        if (mode)
        {
            angle = atan2f(x - gBWPoint.x, y - gBWPoint.y) * 180.f / M_PI;
            if (y >= gBWPoint.y
                && ((x <= gBWPoint.x) || (angle < (90 - ANGLE_THRESHOLD_IN_AERA))))
            {
                rst = true;
            }
        }
        else
        {
            angle = atan2f(x - gBWPoint.x, y - gBWPoint.y) * 180.f / M_PI;
            if (x >= gBWPoint.x
                && ((y <= gBWPoint.y) || (angle > ANGLE_THRESHOLD_IN_AERA)))
            {
                rst = true;
            }
        }
    }

    if (isSideCarScene)
    {
        mode = gSCPoint.parkingMode;
        if (!mode)
        {
            // 暂时只考虑车辆侧停
            angle = atan2f(x - gSCPoint.x, y - gSCPoint.y) * 180.f / M_PI;
            if (x >= gSCPoint.x
                && ((y <= gSCPoint.y) || (angle > ANGLE_THRESHOLD_IN_AERA)))
            {
                rst = true;
            }
        }
    }

    return rst;
}

static void inFreeSpaceObj(cdi_pkg_t* pCdiPkg, trk_pkg_t* pTrkPkg)
{
    float angle, lat, cdiNum = 0, range, prob;
    uint16_t groupId;
    //思路，一个运动目标的角度A，将其和其他所有点云旋转A角度，后计算横向距离，如果距离在一定范围内，认为是遮挡点，遮挡点积累到一定数量认为目标被遮挡
    for (u32 i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if (pTrkPkg->trk[i].type == NONE
                /* || (pTrkPkg->trk[i].type == TRACK && (pTrkPkg->trk[i].status & TRACK_STATUS_MOVING_BMP) == 0)*/)
        {
            pTrkPkg->trk[i].inFreeSpaceProb = 0x0;//无效目标默认不在
            continue;
        }

        cdiNum = 0;
        angle = pTrkPkg->trk[i].sim_z[3];
        range = pTrkPkg->trk[i].sim_z[1];
        groupId = (pTrkPkg->trk[i].idx_1 != FILTER_NON_ASSOC) ? pCdiPkg->cdi[pTrkPkg->trk[i].idx_1].groupId : GROUP_ID_NONE;
        
        for (u32 j = 0; j < pCdiPkg->number; j++)
        {
            if (pCdiPkg->cdi[j].mea_z[1] > range)
            {
                break;
            }

            //同一个聚类组的目标不计算
            if (((groupId == pCdiPkg->cdi[j].groupId) && (groupId != GROUP_ID_NONE))
                    || ((pCdiPkg->cdi[j].status & POINT_STATUS_DEBLUR_FAILED) != 0))
            {
                continue;
            }

            lat = pCdiPkg->cdi[j].mea_z[1] * sinf((pCdiPkg->cdi[j].mea_z[3] - angle) * DEG2RAD);
            cdiNum = (fabsf(lat) < 1.2f) ? cdiNum + 1 : cdiNum;

            //运动目标数量权重放大
            if ((pCdiPkg->cdi[j].status & POINT_STATUS_DYNAMIC_BMP) != 0 && (fabsf(lat) < 0.9f))
            {
                cdiNum += 0.6f;
            }
        }

        prob = cdiNum * 20.0f;
        prob = 100.0f - prob;
        //候选目标不做滤波
        if (pTrkPkg->trk[i].type == CANDI)
        {
            pTrkPkg->trk[i].inFreeSpaceProb = (prob < 0) ? 0 : prob;
        }
        else
        {
            pTrkPkg->trk[i].inFreeSpaceProb = (prob < 0) ? LP_FILTER(pTrkPkg->trk[i].inFreeSpaceProb, 0.65f, 0) :
                                                           LP_FILTER(pTrkPkg->trk[i].inFreeSpaceProb, 0.75f, prob);
        }
    }
}


#define SHELTER_ANGLE_THR1 5.f
#define SHELTER_ANGLE_THR2 10.f     // 近处目标遮挡区域放大
void RDP_Track_markShelterPoint(cdi_pkg_t *pCdiPkg, trk_pkg_t* pTrkPkg)
{
    cdi_t* cdi;
    uint32_t i;
    float angleThreshold = 0.f;

    for (i = 0; i < pCdiPkg->number; i++)
    {
        cdi = &pCdiPkg->cdi[i];
        // 遮挡点有效区域
        if (cdi->x > (0.9f + LANE_WIDTH) || cdi->x < -2.f || cdi->y > 45.f || cdi->y < -1.f)
        {
            continue;
        }

        angleThreshold = (cdi->mea_z[1] > 5.f) ? SHELTER_ANGLE_THR1 : SHELTER_ANGLE_THR2;

        /* 
		 * 暂时不做静止目标的遮挡处理，会导致有静止假点后后方的车都不起批
		 * 发现有问题：1)不应该直接用range；2)应该要使用TRACK的目标；3)应该使用运动状态，不直接用速度
		 */
        if (!(cdi->status & POINT_STATUS_ABNORMAL_BMP)  // 0x40异常点标记的点不做遮挡处理
            && (cdi->status & POINT_STATUS_DYNAMIC_BMP
                || (fabsf(cdi->mea_z[3]) > 80.f && fabsf(cdi->mea_z[3]) < 100.f && fabsf(cdi->mea_z[2]) < 0.15f)))
        {
            // 1、运动点，直接判是否满足遮挡条件
            if (cdi->mea_z[0] > 30.f && cdi->mea_z[2] < 10.f)//-0.1f)  //相对靠近目标
            {
                cdi->shelterFlag = 1;
                cdi->shelterAreaAngle[0] = cdi->mea_z[3] - angleThreshold;
                cdi->shelterAreaAngle[1] = cdi->mea_z[3] + angleThreshold;
            }
            else
            {
				;///TODO
            }
        }
		else if (!(cdi->status & POINT_STATUS_DYNAMIC_BMP) && (pTrkPkg->trk[cdi->index].status & TRACK_STATUS_MOVED_BMP))
		{
			// 2、历史运动点
			cdi->shelterFlag = 1;
			cdi->shelterAreaAngle[0] = cdi->mea_z[3] - angleThreshold;
			cdi->shelterAreaAngle[1] = cdi->mea_z[3] + angleThreshold;
		}
    }

    //目标被遮挡检测【是否inFreeSpace目标】
    inFreeSpaceObj(pCdiPkg, pTrkPkg);
}

bool RDP_TRACK_inShelterArea(cdi_t *assoCdi, cdi_pkg_t *pCdiPkg)
{
    uint32_t i;
    cdi_t* cdi;
    bool rst = false;

    for (i = 0; i < pCdiPkg->number; i++)
    {
        cdi = &pCdiPkg->cdi[i];
        if (!cdi->shelterFlag
                || (assoCdi->groupId2 == cdi->groupId2)         // 同一聚类组或距离相差不大的不做处理
			|| fabsf(assoCdi->mea_z[1] - cdi->mea_z[1]) < 1.0f)
            continue;

        if (assoCdi->mea_z[0] < cdi->mea_z[0]
            && assoCdi->mea_z[1] > cdi->mea_z[1]
            && assoCdi->mea_z[3] > cdi->shelterAreaAngle[0]
            && assoCdi->mea_z[3] < cdi->shelterAreaAngle[1])
        {
            rst = true;
            break;
        }
    }

    return rst;
}

/**
 * @brief 滑窗计算平均SNR和平均RCS
 * @param 原始点指针
 * @param 航迹指针
 * 暂只用于CANDI航迹起批判断，miss情况及TRACK航迹的更新待优化
 */
void RDP_TRACK_updateTrkSlide(cdi_t *cdi, trk_t *trk)
{
    if (trk->type == NONE)
    {
        return;
    }

    uint32_t age = trk->hit;
    uint32_t cnt;

    if (cdi != NULL)
    {
        if (age < 5)
        {
            trk->latestSNR[age] = cdi->mea_z[0];
            trk->latestRCS[age] = cdi->rcs;
            trk->snrAvg = (trk->snrAvg * age + cdi->mea_z[0]) / (age + 1);
            trk->rcsAvg = (trk->rcsAvg * age + cdi->rcs) / (age + 1);
        }
        else
        {
            cnt = age % 5;
            trk->snrAvg = (trk->snrAvg * 5 - trk->latestSNR[cnt] + cdi->mea_z[0]) / 5;
            trk->rcsAvg = (trk->rcsAvg * 5 - trk->latestRCS[cnt] + cdi->rcs) / 5;
            trk->latestSNR[cnt] = cdi->mea_z[0];
            trk->latestRCS[cnt] = cdi->rcs;
        }
    }
    else
    {
        if (age < 5)
        {
            trk->latestSNR[age]  = 0.f; // miss情况赋异常值（特殊标记，暂不用）
        }
        else
        {
            cnt = age % 5;
            trk->latestSNR[cnt]  = 0.f;
        }
    }
}

void updateStaticObj(cdi_pkg_t* pCdiPkg, trk_t* trk, float time, VDY_DynamicEstimate_t* pRDP_inVehicleData)
{
	cdi_t* cdi = &pCdiPkg->cdi[trk->idx_1];
	cdi_t* cdiNearest = &pCdiPkg->cdi[trk->pidNearest];
    rdp_config_t *config = RDP_getTrackConfigPointer();

	const stVehicleStatus *pVdy = getVdyStatus();
	float vcsLatVel = (config->installPosition == SENSOR_POSITION_FRONT_RIGHT) ? -pVdy->vcsLatVel : pVdy->vcsLatVel;
	float vcsLngVel = pVdy->vcsLngVel;

    //区分自车动还是静止，静止时静止目标滤波加严格
    float lat, lng;
    if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f)
    {
		// CPTALF场景纵向预测过大，检测点滤波权重增大，优化关联失败的情况
		if (isCPTALFScene)
		{
			lat = LP_FILTER(cdi->x, STATIC_R_COEF, trk->p_x[0]);
			lng = LP_FILTER(cdi->y, STATIC_R_COEF, trk->p_x[1]);
		}
		else
		{
			lat = LP_FILTER(trk->p_x[0], STATIC_R_COEF, cdi->x);
			lng = LP_FILTER(trk->p_x[1], STATIC_R_COEF, cdi->y);
		}
    }
    else
    {
        lat = LP_FILTER(trk->p_x[0], 0.98f, cdi->x);
        lng = LP_FILTER(trk->p_x[1], 0.98f, cdi->y);
    }

    float latVel, latVelRaw;
    latVelRaw = (lat - trk->x[0]) / time * 0.75f;    // TODO：需要补偿自车转弯导致的横向位移
    if (!(cdi->status & POINT_STATUS_GUARDRAIL_BMP))
    {
		// 暂时只针对FCTALF场景-->横向速度直接取车速的负值
		//if (isCPTALFScene)
		{
			latVel = -vcsLatVel;
		}
		/*else
		{
			latVel = LP_FILTER(trk->p_x[2], STATIC_VEL_COEF, latVelRaw);
		}*/
    }
    else
    {
        latVel = 0; // 护栏目标限制横向速度
    }
    
	float lngVel = 0;
	//在85度以上的位置，且关联的静止点速度小于0.1，则将其纵向速度置车速。
	if (cdi->mea_z[3] > 85 && fabsf(cdi->mea_z[2]) < 0.1)
	{
		//lngVel = pRDP_inVehicleData->vdySpeedInmps;
        lngVel = trk->p_x[3];
	}
	else
	{
		if (cdi->mea_z[3] >= 80)
		{
			uint8_t cnt = 0;
			for (int8_t i = STORE_X1_FRAME_NUM - 1; i >= 0; i--)
			{
				if (fabsf(trk->stored_last_x1[i]) > 0.001f)
				{
					if (fabsf((trk->stored_last_x1[i - 1] - trk->stored_last_x1[i]) / gRDP_storedFrameTime[i - 1]) > 20)
					{
						continue;
					}
					lngVel += (trk->stored_last_x1[i - 1] - trk->stored_last_x1[i]) / gRDP_storedFrameTime[i - 1];
					cnt++;
				}
			}
			cnt = cnt > 0 ? cnt : (cnt + 1);
			lngVel = LP_FILTER(trk->p_x[3], SIDE_V_COEF, lngVel / cnt);
		}
		else
		{
			if (isCPTALFScene && trk->isSuspPedestrian)
			{
				lngVel = LP_FILTER(trk->x[3], STATIC_VEL_COEF, -vcsLngVel);
			}
			else
			{
				lngVel = trk->x[5] < -2.f ? LP_FILTER(trk->x[3], STATIC_VEL_COEF, cdi->mea_z[2] / cos(cdi->mea_z[3] * DEG2RAD)) : LP_FILTER(trk->p_x[3], STATIC_VEL_COEF, cdi->mea_z[2] / cos(cdi->mea_z[3] * DEG2RAD));
			}
		}

		if (IN_BSD_AREA(trk->x[0], trk->x[1]))
		{
			float tmpLngVel = calcRawLngVelocity(pCdiPkg, trk);
			if (fabsf(tmpLngVel) > 0.f && fabsf(tmpLngVel) < 10.f && fabsf(tmpLngVel - lngVel) > 1.5f)
			{
				lngVel = LP_FILTER(tmpLngVel, SIDE_V_COEF, lngVel);
			}
		}
	}
	
	// 绝对静止目标取最近的原始点滤波
	if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f
		&& trk->activeTrkCnt > 15
		&& !(cdi->status & POINT_STATUS_DYNAMIC_BMP)
		&& trk->pidNearest == trk->pidStrongest)
	{
		lat = LP_FILTER(lat, STATIC_R_COEF, cdiNearest->x);
		lng = LP_FILTER(lng, STATIC_R_COEF, cdiNearest->y);
		latVel = LP_FILTER(latVel, STATIC_VEL_COEF, 0.f);
		lngVel = LP_FILTER(lngVel, STATIC_VEL_COEF, 0.f);
	}

    //静止目标的横纵向加速度，按雷达安装位置赋予对应车身加速度
    //相对横向加速度，左角雷达为负车身加速度，右角雷达为正；相对纵向加速度，前角雷达为负车身加速度，后角雷达为正
    float latAcc = (config->installPosition == SENSOR_POSITION_REAR_LEFT || config->installPosition == SENSOR_POSITION_FRONT_LEFT) ? -pRDP_inVehicleData->vdyAccelLat : pRDP_inVehicleData->vdyAccelLat;
    float lngAcc = (config->installPosition == SENSOR_POSITION_FRONT_LEFT || config->installPosition == SENSOR_POSITION_FRONT_RIGHT) ? -pRDP_inVehicleData->vdyAccelLong : pRDP_inVehicleData->vdyAccelLong;


	// 计算旋转引起的位移
	float delta_angle = pVdy->yawrate * time;
	trk->x[0] = lat * cosf(delta_angle) - lng * sinf(delta_angle); //lat;// 
	trk->x[1] = lat * sinf(delta_angle) + lng * cosf(delta_angle); //lng;// 
    trk->x[2] = latVel;
    trk->x[3] = lngVel;
    trk->x[4] = LP_FILTER(trk->p_x[4], STATIC_ACCVEL_COEF, latAcc);
    trk->x[5] = LP_FILTER(trk->p_x[5], STATIC_ACCVEL_COEF, lngAcc);
    trk->sim_z[1] = hypotf(trk->x[0], trk->x[1]);
    trk->sim_z[2] = trk->x[2] * trk->x[0] / trk->sim_z[1] + trk->x[3] * trk->x[1] / trk->sim_z[1];
    trk->sim_z[3] = RAD2ANG * atan2f(trk->x[0], trk->x[1]);
}

// 速度盲区特殊处理
void updateObjInDeadZone(cdi_pkg_t* pCdiPkg, trk_t* trk, float time, VDY_DynamicEstimate_t* pRDP_inVehicleData)
{
    float lat = 0.f, latVel = 0.f, lng = 0.f, lngVel = 0.f;
    float rawLngVel = 0.f;
	cdi_t* cdi = &pCdiPkg->cdi[trk->idx_1];

	// 更新横纵向位置
    lat = LP_FILTER(trk->p_x[0], SIDE_R_COEF, cdi->x);
    lng = LP_FILTER(trk->p_x[1], SIDE_R_COEF, cdi->y);

	// 更新横向速度
    if (fabsf(trk->p_x[1]) > 2.f || (fabsf(trk->p_x[2]) > 1 && fabsf(trk->x[2]) > 1) || (fabsf(trk->p_x[2] / fabsf(trk->p_x[3])) > 0.5) )
    {
        float rawLatVel = (lat - trk->x[0]) / time * 0.4f;
	    latVel = LP_FILTER(trk->p_x[2], SIDE_LAT_V_COEF, rawLatVel);
    }
    else
    {
        latVel = trk->p_x[2] * 0.5f;
    }

	// 更新纵向速度
    if (cdi->mea_z[3] > (90.f - 20.f) && cdi->mea_z[3] < (90.f + 20.f))
    {
		//在大角度这里防止速度除以大角度余弦值造成速度估计错误，取过去10帧位移计算速度的均值与预测值取滤波作为纵向速度。
		uint8_t cnt = 0;
		for (uint8_t i = STORE_X1_FRAME_NUM-1; i > 0; i--)
		{
			if (fabsf(trk->stored_last_x1[i]) > 0.001f)
			{
				if (fabsf((trk->stored_last_x1[i - 1] - trk->stored_last_x1[i]) / gRDP_storedFrameTime[i - 1]) > 20)
				{
					continue;
				}
				lngVel += (trk->stored_last_x1[i - 1] - trk->stored_last_x1[i]) / gRDP_storedFrameTime[i - 1];
				cnt++;
			}
		}
		cnt = cnt > 0 ? cnt : (cnt + 1);
		lngVel = lngVel / cnt;
		if (trk->stored_last_outputX[3] > BSD_LNGVEL_THR)
		{
			// 优先选择维持，lngVel/cnt的计算方式有风险
			lngVel = fabsf(trk->stored_last_outputX[3] - lngVel) < 1.f ? LP_FILTER(trk->stored_last_outputX[3], SIDE_V_COEF, lngVel) : trk->stored_last_outputX[3];
		}
		else if (trk->stored_last_outputX[3] > lngVel)
		{
			lngVel = LP_FILTER(trk->stored_last_outputX[3], SIDE_V_COEF, lngVel);
		}
		else
		{
			// 抑制lngVel/cnt明显偏小的情况
			lngVel = (trk->p_x[3] - lngVel / cnt > 1.f) ? trk->p_x[3] : LP_FILTER(trk->p_x[3], SIDE_V_COEF, lngVel / cnt);
		}

		/*
		* 刚起批阶段目标位于BSD区域内，若给一个不报警的默认值（>15kph），可能会导致关联问题
		* 由于检测点纵向距离变化不大导致的纵向速度lngVel明显减小，维持上一帧速度
		*/
		if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.f
			&& trk->trkCnt < 30
			&& trk->stored_last_outputX[3] > BSD_LNGVEL_THR
			&& IN_BSD_AREA(trk->x[0], trk->x[1]))
		{
			rawLngVel = calcRawLngVelocity(pCdiPkg, trk);
			if (fabsf(rawLngVel) > 0.f && fabsf(rawLngVel) < 10.f && fabsf(rawLngVel - trk->stored_last_outputX[3]) < 1.f)
			{
				lngVel = LP_FILTER(trk->stored_last_outputX[3], SIDE_V_COEF, rawLngVel);
			}
		}
    }
    else
    {
		rawLngVel = calcRawLngVelocity(pCdiPkg, trk);
		if (fabsf(rawLngVel) > 0.f && fabsf(rawLngVel) < 10.f)
		{
			if (trk->hit > 1 && trk->trkCnt > 2)
			{
				// 第二帧目标的预测速度未赋值，不进行线性滤波
				if (fabsf(cdi->y - trk->x[1]) / time  < fabsf(rawLngVel) && fabsf(cdi->y - trk->x[1]) / time > fabsf(trk->p_x[3]) && ((cdi->y - trk->x[1]) * rawLngVel > 0))
				{
					rawLngVel = (cdi->y - trk->x[1]) / time;
				}
				if (rawLngVel > 0 && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10.f)
				{
					lngVel = LP_FILTER(trk->p_x[3], 1 - SIDE_LNG_V_COEF, rawLngVel);
				}
				else
				{
					lngVel = LP_FILTER(trk->p_x[3], SIDE_LNG_V_COEF, rawLngVel);
				}
			}
			else
			{
				lngVel = fabsf(rawLngVel - trk->p_x[3]) < 1.5f ? LP_FILTER(rawLngVel, SIDE_LNG_V_COEF, trk->p_x[3]) : rawLngVel;
			}
		}
		else
		{
			lngVel = cdi->mea_z[2] / cos(cdi->mea_z[3] * DEG2RAD);
		}

        //对于CANDI目标，径向速度大且预测速度明显小于径向速度的目标直接赋值，防止对向来车目标速度更新不上
        if (trk->type == CANDI && fabsf(trk->p_x[3] - rawLngVel) > 5.f && rawLngVel > 10.f)
        {
            lngVel = rawLngVel;
        }
    }

	// 异常处理：BSD报警区域内的新建航迹速度不准，可能导致漏报或误报
	if (trk->bsdSuppressCnt < 3)
	{
		if (lngVel > BSD_LNGVEL_THR)
			trk->bsdSuppressCnt++;
		else
			trk->bsdSuppressCnt = 0;
	}
	else
	{
		if (trk->x[1] > 1.5f || gGroupInfo[cdi->groupId].pointList.count >= 4)
			trk->bsdSuppressCnt--;
	}
	if (trk->trkCnt > 2 && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10
		&& trk->x[0] > 0.75f && trk->x[0] < 5.f && trk->x[1] < 5.f && fabsf(trk->x[2]) < 1.f
		&& trk->x[3] > BSD_LNGVEL_THR && trk->bsdSuppressCnt >= 3
		&& lngVel < trk->x[3])
	{
		// 速度维持
		lngVel = trk->x[3];
	}

    trk->x[4] = LP_FILTER(trk->p_x[4], SIDE_ACC_COEF, (latVel - trk->x[2]) / time);
    trk->x[5] = LP_FILTER(trk->p_x[5], SIDE_ACC_COEF, (lngVel - trk->x[3]) / time);

    trk->x[0]       = lat;
    trk->x[1]       = lng;
    trk->x[2]       = latVel;
    trk->x[3]       = lngVel;
    trk->sim_z[1]   = hypotf(trk->x[0], trk->x[1]);
    trk->sim_z[2]   = trk->x[2] * trk->x[0] / trk->sim_z[1] + trk->x[3] * trk->x[1] / trk->sim_z[1];
    trk->sim_z[3]   = RAD2ANG * atan2f(trk->x[0], trk->x[1]);
}

/*
* 计算自车与目标之间平均径向距离偏差
*/
//static float calcRelativePositonDiff(trk_t* trk)
//{
//    uint16_t calcFrameCnt = (trk->trkCnt < STORE_RANGE_FRAME_NUM)? trk->trkCnt : STORE_RANGE_FRAME_NUM;
//    float disRange = 0.0f;
//    int j, k;
//
//    for(j = 0; j < calcFrameCnt; j++)
//    {
//        disRange += trk->stored_last_range[j];  //计算历史20帧目标相对本车的距离位移
//    }
//
//    float disRangeAvg = disRange/calcFrameCnt;  //记录目标的平均径向距离
//    float disRangeDiff = 0.0f;
//    for(k = 0; k < calcFrameCnt; k++)
//    {
//        disRangeDiff += fabsf(trk->stored_last_range[k] - disRangeAvg);
//    }
//    disRangeDiff = disRangeDiff/calcFrameCnt;   //计算每帧的平均径向距离偏差
//
//    return disRangeDiff;
//}

// 是否存在本体目标
uint8_t existNoumenonTarget(trk_pkg_t* pTrkPkg, cdi_t *pdetObj, int curIdx)
{
	trk_t* curTrk = &pTrkPkg->trk[curIdx];
	trk_t* trk;
	uint8_t rst = 0;

	for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
	{
		trk = &pTrkPkg->trk[i];
		if (trk->type != TRACK || !(trk->status & TRACK_STATUS_MOVING_BMP) || i == curIdx)
			continue;

		// 三车道内快速来向目标
		// 刚转TRACK的点速度可能还未收敛，暂不用跟踪点径向速度来判断
		if (trk->x[0] > -1.5f && trk->x[0] < 0.9f + 2 * 3.75f && trk->x[1] < 50.f && trk->x[3] < -10.f
			&& fabsf(pdetObj->mea_z[2] - trk->sim_z[2]) < 2.f && fabsf(curTrk->sim_z[1] - trk->sim_z[1]) < 5.f)
		{
			rst = 1;
			break;
		}
	}

	return rst;
}

// 是否存在灯杆目标
uint8_t existPoleTarget(cdi_pkg_t* pCdiPkg, trk_pkg_t* pTrkPkg, int curIdx)
{
	trk_t* trk;
	trk_t* curTrk = &pTrkPkg->trk[curIdx];
	cdi_t* cdi = pCdiPkg->cdi;
	uint8_t rst = 0;
    float angleThrd = 10.f;

	for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
	{
		trk = &pTrkPkg->trk[i];
		if (trk->type != TRACK || trk->sim_z[1] > 4.f || (trk->status & TRACK_STATUS_MOVING_BMP) || i == curIdx || trk->idx_1 == -1)
			continue;

		// 近处强反射目标
		if (trk->x[0] > 1.f && trk->x[1] > -0.5f
			&& (cdi[trk->idx_1].mea_z[0] > 65.f || trk->snrAvg > 60)
			&& ((fabsf(cdi[curTrk->idx_1].mea_z[3] - trk->sim_z[3]) < 2.f)
                || (fabsf(cdi[curTrk->idx_1].mea_z[3] - cdi[trk->idx_1].mea_z[3]) < angleThrd)
                || (cdi[curTrk->idx_1].mea_z[3] > 90.f && cdi[curTrk->idx_1].mea_z[2] < 0)))
		{
			rst = 1;
			break;
		}
	}

	return rst;
}

float calcRawLngVelocity(cdi_pkg_t* pCdiPkg, trk_t* trk)
{
	GTrack_ListElem* pointElem;
	cdi_t* cdi = &pCdiPkg->cdi[trk->idx_1];
	int16_t pointId;
	uint8_t validCnt = 0;
	float lngVel = 0.f, rstLngVel = 0.f, cdiLngVel = -100.f;

	pointElem = gtrack_listGetFirst(&gGroupInfo[cdi->groupId].pointList);
	while (pointElem)
	{
		pointId = pointElem->data;

        //遍历的点与跟踪点的径向速度必须同向，且两者角度必须同大于90°或小于90°
		if ((pCdiPkg->cdi[pointId].y > 1.5f
			|| pCdiPkg->cdi[pointId].mea_z[3] < 70.f
			|| pCdiPkg->cdi[pointId].y < -1.5f
			|| pCdiPkg->cdi[pointId].mea_z[3] > 110.f)
            && ((pCdiPkg->cdi[pointId].mea_z[2] * trk->sim_z[2] > 0 && cosf(trk->sim_z[3] * DEG2RAD) * cosf(pCdiPkg->cdi[pointId].mea_z[3] * DEG2RAD) > 0)
                || (pCdiPkg->cdi[pointId].mea_z[2] * trk->sim_z[2] < 0 && cosf(trk->sim_z[3] * DEG2RAD) * cosf(pCdiPkg->cdi[pointId].mea_z[3] * DEG2RAD) < 0))
            && (pCdiPkg->cdi[pointId].status & POINT_STATUS_DEBLUR_FAILED) == 0)
		{
			// 认为检测点的径向速度比较准确，可用于计算目标的纵向速度，且点云估计的纵向速度需要与跟踪点速度方向同向
            if((pCdiPkg->cdi[pointId].mea_z[2] / cosf(pCdiPkg->cdi[pointId].mea_z[3] * DEG2RAD)) * trk->x[3] > 0)
            {
                lngVel += pCdiPkg->cdi[pointId].mea_z[2] / cosf(pCdiPkg->cdi[pointId].mea_z[3] * DEG2RAD);
			    validCnt++;
            }
            if (pointId == trk->idx_1)
            {
                cdiLngVel = pCdiPkg->cdi[trk->idx_1].mea_z[2] / cosf(pCdiPkg->cdi[pointId].mea_z[3] * DEG2RAD);
            }
		}
		pointElem = gtrack_listGetNext(pointElem);
	}
    
	rstLngVel = (validCnt > 0) ? (lngVel / validCnt) : -100.f;

	if (cdiLngVel > -50.f)
	{
		if (validCnt > 0 && fabsf(cdiLngVel - rstLngVel) > 0.5f)
		{
			rstLngVel = fabsf(cdiLngVel - trk->x[3]) > fabsf(rstLngVel - trk->x[3]) ? rstLngVel : cdiLngVel;
		}
	}

	return rstLngVel;
}

// 90°附近目标外侧产生的多径（二次谐波）假点
bool isTruckMultipath(trk_pkg_t* pTrkPkg, int curIdx)
{
	trk_t* curTrk = &pTrkPkg->trk[curIdx];
	trk_t* trk;
	bool rst = false;

	for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
	{
		trk = &pTrkPkg->trk[i];
		if (trk->type != TRACK || i == curIdx)
			continue;

		/*
		* 场景1：自车运动，运动目标超越自车；
		* 场景2：自车运动（前行和倒车），超越静止目标。
		*/
        if (trk->x[0] > 0.9f && trk->x[0] < 0.9f + 3.75f * 2.f && trk->x[1] < 7.5f
            && curTrk->x[0] > trk->x[0] * 1.5f && curTrk->x[0] < trk->x[0] * 3.5f	// 横向假点的位置不稳定，暂时放宽到3.5倍)
            && ((trk->status & TRACK_STATUS_MOVING_BMP && (trk->objType == 3 || trk->objType == 4) && curTrk->x[1] < trk->x[1] + 7.5f)
                || (!(trk->status & TRACK_STATUS_MOVED_BMP) && trk->activeTrkCnt > 30 && trk->snrAvg > 50.f && curTrk->x[1] < trk->x[1] + 5.f)))
        {
            rst = true;
            break;
        }
	}

	return rst;
}

/*
* 镜像假目标判断
* TODO：后续加入隧道场景识别
*/
bool isMirrorTarget(trk_pkg_t* pTrkPkg, int curIdx)
{
    trk_t* curTrk = &pTrkPkg->trk[curIdx];
    trk_t* trk;
    bool rst = false;
    uint8_t findTrk = 0;

    for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrkPkg->trk[i];
        if (trk->type != TRACK || !(trk->status & TRACK_STATUS_MOVING_BMP) || i == curIdx)
        {
            continue;
        }

        // 主要考虑纵向速度和纵向距离
        if (fabsf(curTrk->x[0]) - fabsf(trk->x[0]) > 1.f
            && fabsf(curTrk->x[1] - trk->x[1]) < 1.f
            && (fabsf(curTrk->stored_last_z[0][2] - trk->stored_last_z[0][2]) < 0.5f
                || fabsf(curTrk->sim_z[2] - trk->sim_z[2]) < 0.5f))
        {
            findTrk = 1;
            break;
        }
    }

    if (findTrk)
    {
        curTrk->isMirrorCnt = curTrk->isMirrorCnt < 10 ? curTrk->isMirrorCnt + 1 : 10;
        if (curTrk->isMirrorCnt > 3)
            rst = true;
    }
    else
    {
        curTrk->isMirrorCnt = 0;
    }

    return rst;
}

/*
* 合并航迹
* 暂时先考虑相对靠近大车分裂场景
* 以抑制分裂目标导致的LCA/BSD中断等问题
*/
bool trackMerge(trk_pkg_t *pTrkPkg, int curIdx)
{
	trk_t* curTrk = &pTrkPkg->trk[curIdx];
	trk_t* trk;
	bool rst = false;
	uint8_t findTrk = 0;

	for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
	{
		trk = &pTrkPkg->trk[i];
		if (trk->type != TRACK || !(trk->status & TRACK_STATUS_MOVING_BMP) || i == curIdx || trk->activeTrkCnt < 30)
			continue;

		// 考虑并排场景加严横向距离限制
		if (fabsf(curTrk->x[0] - trk->x[0]) < 0.5f
			&& curTrk->x[1] > trk->x[1]
			&& fabsf(curTrk->x[1] - trk->x[1]) < 7.5f
			&& fabsf(curTrk->x[3] - trk->x[3]) < 2.f
			&& trk->mergeCnt > 10)
		{
			findTrk = 1;
			break;
		}
	}

	if (findTrk)
	{
		curTrk->fissionCnt = curTrk->fissionCnt < 10 ? curTrk->fissionCnt + 1 : 10;
		if (curTrk->fissionCnt > 3)
			rst = true;
	}
	else
	{
		curTrk->fissionCnt = 0;
	}

	return rst;
}

// 判断目标是否为严格纵向靠近目标
bool isLngOnComingTarget(trk_pkg_t *pTrkPkg, int curIdx)
{
	/*
	* 1、最近12帧都位于同一车道内
	* 2、TODO：边上有护栏
	*/
	trk_t* curTrk = &pTrkPkg->trk[curIdx];
	bool rst = false;
	int8_t difLane;
	float latMovingDiff = 0.f;

	// 远处角精度原因可能导致startPosition[0]不准
	difLane = (int8_t)((curTrk->stored_last_x0[0] - 0.75f) / LANE_WIDTH) - (int8_t)((curTrk->stored_last_x0[STORE_X0_FRAME_NUM - 1] - 0.75f) / LANE_WIDTH);
	latMovingDiff = fabsf(curTrk->stored_last_x0[0] - curTrk->stored_last_x0[STORE_X0_FRAME_NUM - 1]);
	if (curTrk->sim_z[2] < 0.f && fabsf(curTrk->x[3]) > 3 * fabsf(curTrk->x[2]) && difLane == 0 && latMovingDiff < 1.f)
	{
		rst = true;
	}

	return rst;
}

/*
* 保险杠假点抑制
*/
void isBumperTarget(cdi_pkg_t* pCdiPkg, trk_t* trk, VDY_DynamicEstimate_t* pRDP_inVehicleData)
{
	trk->objFakeDOt = 0;	// 默认非假点，每一帧都重新判断
	float tmpX = 0.f, tmpY = 0.f, difValue = 0.f;
	float difValueThd = 3.0f;
	float vehSpeed = fabsf(pRDP_inVehicleData->vdySpeedInmps) * 3.6f;
	uint8_t fakeDOt = 0; // false 非假点

	tmpX = (trk->x[0] - trk->startPosition[0]);
	tmpY = (trk->x[1] - trk->startPosition[1]);
	difValue = sqrtf(tmpX * tmpX + tmpY * tmpY);
	trk->rangeStartDiffValue = difValue;
	
	/**
	 * 目标位移差值连续10帧大于3m认为是真点，否则认为是假点，这是个累加，不是实时
	 */
	if (trk->rangeStartDiffValue >= ADAS_POINT_MAX_MOVEMENT_DIFFERENCE) // 
	{
		if (trk->bumperTimestampCnt < ADAS_POINT_MAX_MOVEMENT_DIF_FRAME_CNT)
		{
			trk->bumperTimestampCnt++; // 这里是在本次ID生命周期内都有效，如果清除：1切id、2下电； 
		}
		else
		{
			trk->isTruePoint = 1;  // 1，非假点，0 假点
		}
	}
	else
	{
		trk->bumperTimestampCnt = 0;
	}

	// 根据不同速度来定假点位移阈值 
	if (vehSpeed <= BSD_SCENE_MIN_SPD)
	{
		difValueThd = BSD_FAKEDOT_MIN_DIF;
	}
	else if (vehSpeed <= BSD_SCENE_MID_SPD) // 要看下高峰期这个的影响
	{
		difValueThd = BSD_FAKEDOT_MID_DIF;
	}
	else
	{
		difValueThd = BSD_FAKEDOT_MAX_DIF;
	}

	/*
	* 1、当前与起点的位移
	* 2、目标与原点达到的最大位移且10帧以上
	* 3、目标长度大于3
	*/
	if (trk->rangeStartDiffValue >= difValueThd
		|| trk->isTruePoint
		|| (trk->front + trk->back) >= ADAS_POINT_BOX_LENGTH_THRESHOLD)
	{
		trk->objFakeDOt = 0;
	}
	else
	{
		trk->objFakeDOt = 1; // 0，非假点，1 假点
	}

	/**/
	if (trk->startPosition[1] > 0) // 非超车
	{
		/**
		* @brief BSD报警条件: 未报警的目标，非超车且（当前 Y > 3或起航和报警帧的绝对位移大于 3 m
		*
		*/
		if (trk->objFakeDOt)
		{
			fakeDOt = 1;
		}
	}
	else
	{
		/**
		* 对于-0.5<startY<0的点在-0.5<Y<0的情况进行累计记数达到600帧及以上，可能是假点，表示出来，
		*      但Y到达1m后取消标识，有该表示的跟踪点不报警
		*   0，非假点，1 假点
		*/
		if (trk->pointTrackingFrameCount < ADAS_POTENTIA_FALSE_POINT_TRACKING_FRAME_CNT_MAX)
		{
			if (trk->startPosition[1] > ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_STARTY_MIN
				&& trk->x[1] < ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_MAXY_MAX
				&& trk->x[1] > ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_MAXY_MIN)
			{
				trk->pointTrackingFrameCount++;
			}
		}
		else // if (pobjPath[i].pointTrackingFrameCount >= ADAS_POTENTIA_FALSE_POINT_TRACKING_FRAME_CNT_MAX) // 
		{
			if (trk->x[1] < 1)
			{
				fakeDOt = 1;
			}
		}
	}

	/**
	* @brief 目标新增径向速度为零检测点的连续关联帧数：vr0_hit，应用于bsd报警场景的假目标判定。推荐假目标判据：
	* 1. vr0_hit ≥ 40 或 vr0_hit ≥ 生命周期；
	* 2. length < 2
	*/
	if (trk->vr0_hit >= 200)// || trk->vr0_hit >= 4 * trk->activeTrkCnt)
	{
		fakeDOt = 1;
	}

	trk->objFakeDOt = fakeDOt;
}

/*
* 护栏边的运动目标聚类判断
*/
#define CLUSTER_IN_SLS_X_THR	0.5f
#define CLUSTER_IN_SLS_X_THR1	1.0f
#define CLUSTER_IN_SLS_Y_THR1	3.0f
bool clusterInSildLineSide(trk_pkg_t* pTrkPkg, cdi_t *pdetObj, int cdiIdx1, int cdiIdx2)
{
	bool rst = false;
	sideLine_pkr_t* pSideLinePkg = &pTrkPkg->sideLines;
	float tempSideLines_cdiIdx1 = getSideLineDis(pdetObj[cdiIdx1].y, pSideLinePkg);
	float tempSideLines_cdiIdx2 = getSideLineDis(pdetObj[cdiIdx2].y, pSideLinePkg);
	if (pSideLinePkg->strongSildLineValid)
	{
		if ((((tempSideLines_cdiIdx1 - pdetObj[cdiIdx1].x > CLUSTER_IN_SLS_X_THR && pdetObj[cdiIdx1].status & POINT_STATUS_DYNAMIC_BMP) || (tempSideLines_cdiIdx1 - pdetObj[cdiIdx1].x > CLUSTER_IN_SLS_X_THR1 && pdetObj[cdiIdx1].y < CLUSTER_IN_SLS_Y_THR1))
				&& pdetObj[cdiIdx2].status & (POINT_STATUS_GUARDRAIL_BMP | POINT_STATUS_GUARDRAIL_OUTSIDE_BMP))
			|| (((tempSideLines_cdiIdx2 - pdetObj[cdiIdx2].x > CLUSTER_IN_SLS_X_THR && pdetObj[cdiIdx2].status & POINT_STATUS_DYNAMIC_BMP) || (tempSideLines_cdiIdx2 - pdetObj[cdiIdx2].x > CLUSTER_IN_SLS_X_THR1 && pdetObj[cdiIdx2].y < CLUSTER_IN_SLS_Y_THR1))
				&& pdetObj[cdiIdx1].status & (POINT_STATUS_GUARDRAIL_BMP | POINT_STATUS_GUARDRAIL_OUTSIDE_BMP)))
		{
			rst = true;
		}
	}

	return rst;
}

/*
* 自车直行场景下的动静聚类判断
*/
#define CLUSTER_DYNAMIC_LOW_THR		0.25f
#define CLUSTER_DYNAMIC_HIGH_THR	0.5f
bool clusterDynamicOrNot(cdi_t *pdetObj, int cdiIdx1, int cdiIdx2)
{
	bool rst = false;
	if (pdetObj[cdiIdx1].x > -2.f && pdetObj[cdiIdx1].x < 0.f + LANE_WIDTH && pdetObj[cdiIdx1].y < 30.f
		&& pdetObj[cdiIdx2].x > -2.f && pdetObj[cdiIdx2].x < 0.f + LANE_WIDTH && pdetObj[cdiIdx2].y < 30.f)
	{
		if (((pdetObj[cdiIdx1].status & POINT_STATUS_DYNAMIC_BMP && !(pdetObj[cdiIdx2].status & POINT_STATUS_DYNAMIC_BMP)
					&& (fabsf(pdetObj[cdiIdx1].otgVel) > CLUSTER_DYNAMIC_HIGH_THR || fabsf(pdetObj[cdiIdx2].otgVel) < CLUSTER_DYNAMIC_LOW_THR))
				|| (pdetObj[cdiIdx2].status & POINT_STATUS_DYNAMIC_BMP && !(pdetObj[cdiIdx1].status & POINT_STATUS_DYNAMIC_BMP)
					&& (fabsf(pdetObj[cdiIdx2].otgVel) > CLUSTER_DYNAMIC_HIGH_THR || fabsf(pdetObj[cdiIdx1].otgVel) < CLUSTER_DYNAMIC_LOW_THR)))
			&& (fabsf(pdetObj[cdiIdx1].x - pdetObj[cdiIdx2].x) > 0.5f
				|| fabsf(pdetObj[cdiIdx1].y - pdetObj[cdiIdx2].y) > 0.5f
				|| fabsf(pdetObj[cdiIdx1].mea_z[1] - pdetObj[cdiIdx2].mea_z[1]) > 0.5f))
		{
			rst = true;
		}
	}

	return rst;
}

/**
 * 鬼探头场景下动静不聚类
 */
bool sideCarclusterDynamicOrNot(cdi_t *pdetObj, int cdiIdx1, int cdiIdx2)
{
	bool rst = false;
    if ((pdetObj[cdiIdx2].x < 4.5f) && (pdetObj[cdiIdx2].y < 15.0f) 
        && !(pdetObj[cdiIdx1].status & POINT_STATUS_DYNAMIC_BMP) && (pdetObj[cdiIdx2].status & POINT_STATUS_DYNAMIC_BMP))
        {
            rst = true;
        }

	return rst;
}


/*
* 抑制动静或速度相差较大的检测点聚类
*/
bool clusterDynamicOrNot2(cdi_t* pdetObj, int cdiIdx1, int cdiIdx2)
{
    bool rst = false;
    if (pdetObj[cdiIdx1].mea_z[1] < 20.f && pdetObj[cdiIdx2].mea_z[1] < 20.f
        && (fabsf(pdetObj[cdiIdx1].otgVel) > CLUSTER_DYNAMIC_HIGH_THR || fabsf(pdetObj[cdiIdx2].otgVel) > CLUSTER_DYNAMIC_HIGH_THR)
        && fabsf(pdetObj[cdiIdx1].otgVel - pdetObj[cdiIdx2].otgVel) > CLUSTER_DYNAMIC_HIGH_THR
        //&& ANGLE_IN_VERTICAL_AREA(pdetObj[cdiIdx1].mea_z[3], 15) && ANGLE_IN_CROSS_AREA(pdetObj[cdiIdx1].mea_z[3], 15)
        //&& ANGLE_IN_VERTICAL_AREA(pdetObj[cdiIdx2].mea_z[3], 15) && ANGLE_IN_CROSS_AREA(pdetObj[cdiIdx2].mea_z[3], 15)
        && (fabsf(pdetObj[cdiIdx1].x - pdetObj[cdiIdx2].x) > 0.5f
            || fabsf(pdetObj[cdiIdx1].y - pdetObj[cdiIdx2].y) > 0.5f
            || fabsf(pdetObj[cdiIdx1].mea_z[1] - pdetObj[cdiIdx2].mea_z[1]) > 0.5f))
    {
        rst = true;
    }

    return rst;
}

// 计算自车运动距离
float RDP_calcVehicleMoveRange(uint8_t validCnt, trk_pkg_t *pTrkPkg)
{
	float rst = 0.f;
	for (int j = 0; j < validCnt; j++)
	{
		rst += pTrkPkg->stored_move_range[j];
	}

	return rst;
}

/*
* 多径场景1
* 多径关系：侧方静止目标（静止车辆或护栏）与后向来车的一次反射
* 速度关系：≈运动目标速度
*/
#define MULTIPATH_SCENE_VELOCITY_THR	0.5f
#define MULTIPATH_SCENE_ANGLE_THR		30.f
bool RDP_multipathScene(trk_pkg_t* pTrkPkg, cdi_t *pdetObj, int curIdx)
{
	/*
	* 1、邻车道存在静止目标；
	* 2、后向来车；
	* 3、待起批目标与后向来车目标两者检测点速度一致（存在一定偏差）
	* TODO：是否放开至未检测的护栏场景？
	*/
	
	trk_t* curTrk = &pTrkPkg->trk[curIdx];
	trk_t* trk;
	bool rst = false;
	float nearLng = -100.f;
	uint8_t existStaticTarget = 0, existOncomingTarget = 0;
	int8_t staticTargetId = -1, onComingTargetId = -1;
	int16_t idx1 = -1, idx2 = -1;

	for (int i = 0; i < TRACK_NUM_TRK; i++)
	{
		trk = &pTrkPkg->trk[i];
		if (trk->type != TRACK || i == curIdx || trk->activeTrkCnt < 30)
			continue;
		
		if (!(trk->status & TRACK_STATUS_MOVING_BMP) && trk->x[0] > 0.9f && trk->x[0] < 0.9f + 3.75f && trk->x[1] < 5.f)
		{
			existStaticTarget = 1;
			if (trk->x[1] > nearLng)	// 记录纵向较远的目标
			{
				nearLng = trk->x[1];
				staticTargetId = i;
			}
		}

		if (!existOncomingTarget && (trk->status & TRACK_STATUS_MOVING_BMP) && trk->x[0] > -2.f && trk->x[0] < 0.9f && trk->x[1] < 30.f && fabsf(trk->x[2]) < 0.5f && trk->x[3] < -5.f / 3.6)
		{
			existOncomingTarget = 1;
			onComingTargetId = i;
		}
	}

	if (existStaticTarget && existOncomingTarget && curTrk->idx_1 >= 0 && pTrkPkg->trk[onComingTargetId].idx_1 >= 0 && staticTargetId >= 0 && onComingTargetId >= 0)	// TODO：若运动目标没有关联点，是否可用跟踪点信息来判断？
	{
        idx1 = curTrk->idx_1;
        idx2 = pTrkPkg->trk[onComingTargetId].idx_1;

		// 方位角门限设置30°：1、近处方位角变化较大；2、检测点较少及关联点选择问题，关联点未必为遮挡车辆目标的角点，因此只判断大致范围。
		if ((pTrkPkg->trk[onComingTargetId].x[0] - curTrk->x[0]) * (pTrkPkg->trk[staticTargetId].x[0] - curTrk->x[0]) < 0.f
			&& fabsf(pdetObj[idx1].mea_z[2] - pdetObj[idx2].mea_z[2]) < MULTIPATH_SCENE_VELOCITY_THR
			&& fabsf(pdetObj[idx1].mea_z[3] - pdetObj[idx2].mea_z[3]) < MULTIPATH_SCENE_ANGLE_THR)
		{
			rst = true;
		}
	}

	return rst;
}

/*
* 护栏谐波场景：自车运动，近处护栏外产生的多次谐波假点，形成航迹后内漂。
* 个别类型护栏检测点稀疏，无法有效识别为护栏
* 在近处90°附近持续多帧存在SNR较打的静止点，对于其外侧的运动目标严格起批。
*/
bool judgeFenceHarmonic(cdi_t* pCdi, trk_t* pTrk)
{
    bool rst = false;

    if (fenceCntInBlindArea >= 10
        && pTrk->x[0] > 1.2f * fencePointLatRange
        && fabsf(pTrk->x[1]) < 1.f
        && pCdi->status & POINT_STATUS_DYNAMIC_BMP
        && fabsf(pCdi->y) < 1.f)
    {
        rst = true;
    }

    return rst;
}

/*
* CNCAP场景：正后方2m处静止遮挡车，邻车道二轮车/四轮车匀速（15kph/20kph/30kph）靠近DOW正常报警
* 检测点不稳定导致的起批晚及横向速度不准，致使DOW报警晚
*/
#define CNCAP_DOW_VELOCITY_THR_MIN	(float)(-10 / 3.6)
#define CNCAP_DOW_VELOCITY_THR_MAX	(float)(-40 / 3.6)
uint8_t isDowShelterScene(trk_pkg_t* pTrkPkg, uint32_t curIdx)
{
	trk_t* curTrk = &pTrkPkg->trk[curIdx];
	trk_t* trk;
	uint8_t rst = 0;
	
	for (uint32_t i = 0; i < TRACK_NUM_TRK; i++)
	{
		trk = &pTrkPkg->trk[i];
		if (trk->type != TRACK || trk->status & TRACK_STATUS_MOVING_BMP || trk->activeTrkCnt < 30 || i == curIdx)
			continue;

		/*
		* 1、针对正后方静止车辆检测点抖动导致的距离不准放宽有效区域，x：-1~0.5，y：2.5~5
		* 2、目标startPosition满足有效区域，x：0.9~0.9+3.75，y：10~30，v：10~40
		*/
		if (trk->x[0] > -1.f && trk->x[0] < 0.5f && trk->x[1] > 2.0f && trk->x[1] < 5.f
			&& curTrk->x[0] > 0.9f &&  curTrk->x[0] < 0.9f + LANE_WIDTH && curTrk->x[1] > 10.f && curTrk->x[1] < 30.f
			&& curTrk->sim_z[2] > CNCAP_DOW_VELOCITY_THR_MAX && curTrk->sim_z[2] < CNCAP_DOW_VELOCITY_THR_MIN)
		{
			rst = 1;
			break;
		}
	}

	return rst;
}

uint8_t staticCdiCntAround(cdi_pkg_t* pCdiPkg, short curIdx)
{
	// 
	uint8_t rst = 0;
	cdi_t * cdi;
	cdi_t* curCdi = &pCdiPkg->cdi[curIdx];
	
	for (uint32_t i = 0; i < pCdiPkg->number; i++)
	{
		cdi = &pCdiPkg->cdi[i];
		if (i == curIdx || fabsf(cdi->mea_z[1] - curCdi->mea_z[1]) > 5.f || cdi->status & POINT_STATUS_DYNAMIC_BMP)
			continue;
		
		if (fabsf(cdi->x - curCdi->x) < 2.f && fabsf(cdi->y - curCdi->y) < 2.f)
		{
			rst++;
		}
	}

	return rst;
}
void updateGridMap(trk_t* trk)
{
	uint8_t gridRow = 0, gridCol = 0;
	gridRow = GET_GRID_ROW(trk->x[1]);
	gridCol = GET_GRID_COL(trk->x[0]);

	gGridMap[gridRow][gridCol] = 1;
	trk->gridMapFlag = 1;
}

void updateLngMoveStatus(trk_t* trk)
{
	uint8_t row, col;
	trk->lngMovingStatus = 0;

	if (trk->gridMapFlag && (trk->status & TRACK_STATUS_MOVING_BMP) && !(trk->status & TRACK_STATUS_CROSSING_BMP))
	{
		row = GET_GRID_ROW(trk->x[1]);
		col = GET_GRID_COL(trk->x[0]);
		for (int m = -1; m < 2; m++)
		{
			if (row + m < 0 || row + m > 3)
				continue;

			for (int n = -1; n < 2; n++)
			{
				if (col + n < 0 || col + n > 2 || n == 0)	// 同车道不做判断
					continue;

				if (gGridMap[row + m][col + n] == 1)
				{
					trk->lngMovingStatus = (n < 0) ? (trk->lngMovingStatus | GRIDMAP_NEIGHBOUR_MINUS) : (trk->lngMovingStatus | GRIDMAP_NEIGHBOUR_PLUS);
				}
			}
		}
	}
}

void updateMovingStatusCnt(trk_t* trk)
{
    // 更新目标的运动状态计数
    if (trk->status & TRACK_STATUS_MOVING_BMP)
    {
        trk->movingStatusCnt = (trk->movingStatusCnt < 0xFF) ? (trk->movingStatusCnt + 1) : trk->movingStatusCnt;
    }
    else
    {
        trk->movingStatusCnt = 0;
    }
}

void updateStaticStatusCnt(trk_t* trk, cdi_t* cdi)
{
    // 更新目标关联零速点计数
    if (trk->status & TRACK_STATUS_CROSSING_BMP && ANGLE_IN_VERTICAL_AREA(trk->sim_z[3], 15))
    {
        // 正前方区域的横穿目标不做处理
        trk->staticStatusCnt = 0;
    }
    else
    {
        if (fabsf(cdi->mea_z[2]) < 0.01f)
        {
            trk->staticStatusCnt = (trk->staticStatusCnt < 0xFF) ? (trk->staticStatusCnt + 1) : trk->staticStatusCnt;
        }
        else
        {
            trk->staticStatusCnt = 0;
        }
    }
}

static void selectPoint(cdi_pkg_t* pCdiPkg, trk_pkg_t* pTrkPkg, TrackGroupInfo_t* pGroupInfo)
{
#if 0
    //思路根据x*factor1+y*factor2的方式,选择最小的，当作最近点
    float xFactor = 1.0f;
    float yFactor = 1.5f;
    float weight, minWeight = FLT_MAX;
    GTrack_ListElem* pointElem;
    int16_t pointId;
    int16_t selectId = FILTER_NON_ASSOC;
    for (uint32_t i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if (pTrkPkg->trk[i].type == NONE || pTrkPkg->trk[i].idx_1 == FILTER_NON_ASSOC)
        {
            continue;
        }
        trk_t* trk = &pTrkPkg->trk[i];
        cdi_t* cdi = &pCdiPkg->cdi[pTrkPkg->trk[i].idx_1];

        pointElem = gtrack_listGetFirst(&pGroupInfo[cdi->groupId].pointList);
        while (pointElem)
        {
            pointId = pointElem->data;
            cdi_t* cdi = &pCdiPkg->cdi[pointId];
            weight = cdi->x * xFactor + cdi->y * yFactor;
            if (weight < minWeight)
            {
                minWeight = weight;
                selectId = pointId;
            }
            pointElem = gtrack_listGetNext(pointElem);
        }

        trk->pidNearest = selectId;
    }
#else
    float weight, minWeight = FLT_MAX, sumx;
    float minLat, maxLat;
    float minLng, maxLng;
    GTrack_ListElem* pointElem;
    int16_t pointId;
    int16_t selectId = FILTER_NON_ASSOC;
    for (uint32_t i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if (pTrkPkg->trk[i].type == NONE || pTrkPkg->trk[i].idx_1 < 0)
        {
            continue;
        }
        trk_t* trk = &pTrkPkg->trk[i];
        cdi_t* cdi = &pCdiPkg->cdi[pTrkPkg->trk[i].idx_1];
        minLat = cdi->x;
        maxLat = cdi->x;
        minLng = cdi->y;
        maxLng = cdi->y;
        sumx = 0;

        pointElem = gtrack_listGetFirst(&pGroupInfo[cdi->groupId].pointList);
        while (pointElem)
        {
            pointId = pointElem->data;
            sumx += pCdiPkg->cdi[pointId].x;
            pointElem = gtrack_listGetNext(pointElem);

            SEL_MIN(minLat, pCdiPkg->cdi[pointId].x);
            SEL_MAX(maxLat, pCdiPkg->cdi[pointId].x);
            SEL_MIN(minLng, pCdiPkg->cdi[pointId].y);
            SEL_MAX(maxLng, pCdiPkg->cdi[pointId].y);
        }

        cdi->latLim[0] = minLat;
        cdi->latLim[1] = maxLat;
        cdi->lngLim[0] = minLng;
        cdi->lngLim[1] = maxLng;

        trk->meaNearest[0] = sumx / pGroupInfo[cdi->groupId].pointList.count;
        trk->meaNearest[1] = pCdiPkg->cdi[trk->pidNearest].y;

        // 统计一次聚类的点云分布, 避免二次聚类点云不充分导致重叠率偏差较大

        /**
         * @brief 在点云统计RCW和DOW重叠率检测
         * 整体思路, 后方目标在横向一定距离内, 统计原始点的分布情况, 多帧综合判断当前RCW的重叠率
         * 根据路测情况考虑是否增加转弯半径  自车静止等限制条件
         * 初步仿真均值在 (0.1 - (-0.5) 这是是基于雷达的 之间  符合高重叠率场景) 注意: 此计算是基于雷达而言的, 功能端注意车身边缘偏移补偿
         * 由于正后方车辆右侧边缘在FOV区域, 后期考虑是否只统计车辆边缘外侧区域. 
         * 同时对DOW尝重叠率尝试判断
         */ 
        if ((trk->x[3] < -2.0f) && (trk->x[0] < 0.8f) && (trk->x[0] > -1.2f) && ((trk->x[1] / fabsf(trk->x[3])) < 3.0f) &&
            (trk->x[1] > 1.0f) && (fabsf(trk->x[2]) < 1.0f))
        {
            // 针对RCW目标， 使用一次聚类组的原始点信息进行重叠率判断
            float latLimmin = 0.0f, latLimmax = 0.0f;
            uint16_t k = 0;
            // 仅针对有关联点时计算   无关联点时不计算
            if (trk->idx_1 >= 0)
            {
                uint16_t group = pCdiPkg->cdi[trk->idx_1].groupId1;       // 一次聚类组
                latLimmin = pCdiPkg->cdi[trk->idx_1].x;
                latLimmax = pCdiPkg->cdi[trk->idx_1].x;

                SEL_MIN(latLimmin, pCdiPkg->cdi[trk->idx_1].x);
                SEL_MAX(latLimmax, pCdiPkg->cdi[trk->idx_1].x);

                for(k = 0; k < pCdiPkg->number; k++)
                {
                    // 寻找横向最近邻
                    if(pCdiPkg->cdi[k].groupId1 == group)
                    {
                        SEL_MIN(latLimmin, pCdiPkg->cdi[k].x);
                        SEL_MAX(latLimmax, pCdiPkg->cdi[k].x);
                    }
                }
                // RCW 使用更少的TTC时间判断 避免TTC时间过高时, 一开始重叠率累计偏差较多
                if ((trk->x[1] / fabsf(trk->x[3])) < 2.2f)
                {
                    trk->rcwoverlapxcnt++;
                    trk->rcwoverlapsumx += ((latLimmin + latLimmax) / 2);
                    trk->rcwoverlapx = (trk->rcwoverlapsumx / trk->rcwoverlapxcnt);  
                }
                trk->dowoverlapxcnt++;
                // 对于斜穿类的点云不应该生效, 如何判断斜穿类点云
                trk->dowoverlapsumx += latLimmin;
                trk->dowoverlapx = (trk->dowoverlapsumx / trk->dowoverlapxcnt);  // 靠近车辆内测的点, 相当于重叠程度, 超过一定值就不报警.  比如小于 -0.3就不报警   
            }
        }

        /**
         * @brief 在点云判断目标原始点是否有进入到自车正前方横向区域内
         *  
         */ 
        //if ((trk->x[0] > -3.0f) && (trk->x[0] < 2.f) &&  // 目标横向位置
        //    (trk->x[1] > -1.0f) && (trk->x[1] < 10.f)    // 目标纵向位置
        //)
        //{
        //    // 使用一次聚类组的原始点信息
        //    float latLimmin = 0.0f, latLimmax = 0.0f;

        //    // 仅针对有关联点时计算   无关联点时不计算
        //    if (trk->idx_1 >= 0)
        //    {
        //        if (pCdiPkg->cdi[trk->idx_1].x)
        //        {
        //            
        //        }
        //            uint16_t group = pCdiPkg->cdi[trk->idx_1].groupId1; // 一次聚类组
        //        latLimmin = pCdiPkg->cdi[trk->idx_1].x;
        //        latLimmax = pCdiPkg->cdi[trk->idx_1].x;
        //    }
        //}

        // 目标朝相反方向运动时, 清除相关计数, 避免反复测试时误赋值
        // 限制横向速度, 对于切入类场景不记录, 避免切入类场景一开始较靠外, 均值偏大 不满足报警
        if ((trk->x[3] > 0.0f) || (fabsf(trk->x[2]) > 1.2f))
        {
            trk->rcwoverlapxcnt = 0;
            trk->dowoverlapxcnt = 0;
            trk->rcwoverlapsumx = 0.0f;
            trk->rcwoverlapx = 0.0f;
            trk->dowoverlapsumx = 0.0f;
            trk->dowoverlapx = 0.0f;
        }
    }
#endif
}

static void RDP_do_trackManage(cdi_pkg_t* pCdiPkg, VDY_DynamicEstimate_t* pRDP_inVehicleData, trk_pkg_t* pTrkPkg, TrackGroupInfo_t *pGroupInfo, float time)
{
    // 航迹管理
    RDP_Track_manageTracks(pCdiPkg, pRDP_inVehicleData, pGroupInfo, pTrkPkg, time);

    // 航迹起始
    RDP_Track_startTracks(pCdiPkg, pRDP_inVehicleData, pGroupInfo, pTrkPkg);
}

static void RDP_do_assoc(cdi_pkg_t* pCdiPkg, VDY_DynamicEstimate_t* pRDP_inVehicleData, trk_pkg_t* pTrkPkg, float time, rdp_config_t* config, float EKF_A[36])
{
    // 航迹预测
    RDP_Track_predictState(time, pTrkPkg, config, EKF_A);

    // 航迹第一次关联
    RDP_Track_associateCdi(pCdiPkg, pRDP_inVehicleData, pTrkPkg, time);

    // 航迹第二次关联
    RDP_Track_getBestAssociation(pCdiPkg, pRDP_inVehicleData, pTrkPkg);
}

static void RDP_do_cluster(cdi_pkg_t* pCdiPkg, VDY_DynamicEstimate_t* pRDP_inVehicleData, trk_pkg_t* pTrkPkg, TrackGroupInfo_t* pGroupInfo)
{
    uint32_t groupNum;

    // 关联原始点与航迹聚类
    RDP_Track_clusterOnCdis(pCdiPkg->cdi, pRDP_inVehicleData, pCdiPkg->number, pTrkPkg);

    // 候选点组ID置为GROUP_ID_NONE
    RDP_Track_initPointsGroup(pCdiPkg->number, pCdiPkg->cdi);

    // 航迹和航迹聚类
    groupNum = RDP_Track_clusterOnTracks(pCdiPkg->cdi, pRDP_inVehicleData, pGroupInfo, pTrkPkg);

    // 标记已被聚类的候选点
    RDP_Track_syncGroupStatus(pCdiPkg->cdi, pGroupInfo, groupNum);

    //最近点和最强点选取
    selectPoint(pCdiPkg, pTrkPkg, pGroupInfo);
}

void positionWorkaround(trk_t* trk, float deltaT, cdi_pkg_t* pCdiPkg, VDY_DynamicEstimate_t* pRDP_inVehicleData)
{
    if (trk->idx_1 == FILTER_NON_ASSOC)
    {
        return;
    }

    cdi_t* pCdiNearest = &pCdiPkg->cdi[trk->pidNearest];
    float deltaX, deltaY;
    deltaX = trk->x[0];
    deltaY = trk->x[1];
	// 采用横向平均值会导致横穿目标横向位置回跳
    //float r = sqrtf(trk->meaNearest[0] * trk->meaNearest[0] + trk->meaNearest[1] * trk->meaNearest[1]);
    //if (fabsf(trk->sim_z[1] - r) > 0.4f)
    //{
    //    trk->x[0] = trk->x[0] * 0.75f + trk->meaNearest[0] * 0.25f;
    //    trk->x[1] = trk->x[1] * 0.75f + trk->meaNearest[1] * 0.25f;
    //}
    if (pCdiNearest->mea_z[1] - trk->sim_z[1] < -0.4f)
    {
        trk->x[0] = trk->x[0] * 0.75f + pCdiNearest->x * 0.25f;
        trk->x[1] = trk->x[1] * 0.75f + pCdiNearest->y * 0.25f;
    }

    /* 针对CNCAP目标车切入（四轮车目标，50kph-50kph）BSD报警晚
    * 1、没有最近检测点或最近检测点横向距离较真值偏大（此类情况暂不处理）
    * 2、跟踪点未关联至最近原始点
    */ 
    cdi_t* pCdiAsso = &pCdiPkg->cdi[trk->idx_1];
    if (fabsf(fabsf(pRDP_inVehicleData->vdySpeedInmps) - (50 / 3.6f)) < (5 /3.6f)
        && trk->BSDCutInTargetCnt > 35
        && pCdiAsso->latLim[0] < 5.f
        && trk->x[1] > 0.f && trk->x[1] < 5.f
        /*&& trk->x[2] < -0.25f */&& fabsf(trk->x[3]) < (10 / 3.6f))
    {
        if (trk->x[0] - pCdiAsso->latLim[0] > 0.25f)
        {
            trk->x[0] = trk->x[0] * 0.5f + pCdiAsso->latLim[0] * 0.5f;
        }
    }

    deltaX = trk->x[0] - deltaX;
    deltaY = trk->x[1] - deltaY;
    trk->sim_z[1] = sqrtf(trk->x[0] * trk->x[0] + trk->x[1] * trk->x[1]);
    trk->sim_z[1] = trk->sim_z[1] < 0.1f ? 0.1f : trk->sim_z[1];
    trk->sim_z[2] = (trk->x[0] * trk->x[2] + trk->x[1] * trk->x[3]) / trk->sim_z[1];
    trk->sim_z[3] = atan2f(trk->x[0], trk->x[1]) * RAD2ANG;
    trackingBoxAdjustScd(trk, pRDP_inVehicleData, deltaX, deltaY);      //second time adjust box
}
/*
 * function name: RDP_Track_getLaneNearestTarget
 * @brief 寻找各车道最近的目标点，并作应变处理
 * @param pCdiPkg RDP内部用于跟踪的原始点列表全局变量指针
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 * auther: Zhang Jian
 * data: 2022-12-16
 */
void RDP_Track_getLaneNearestTarget(trk_pkg_t* pTrkPkg, float time, cdi_pkg_t* pCdiPkg, VDY_DynamicEstimate_t* pRDP_inVehicleData)
{
	memset(&gGridMap[0][0], 0, sizeof(uint8_t) * 12);
    for (u32 i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if (pTrkPkg->trk[i].type == NONE
            || !(pTrkPkg->trk[i].status & TRACK_STATUS_MOVED_BMP)
            || pTrkPkg->trk[i].trkCnt < 10)
        {
            continue;
        }

		// 更新gridMap
		if (IN_GRIDMAP_AREA(pTrkPkg->trk[i].x[0], pTrkPkg->trk[i].x[1]))
		{
			updateGridMap(&pTrkPkg->trk[i]);
		}

        //更新目标参考点位置
		if (pTrkPkg->trk[i].status & TRACK_STATUS_MOVING_BMP && pTrkPkg->trk[i].idx_1 != FILTER_NON_ASSOC)
		{
			positionWorkaround(&pTrkPkg->trk[i], time, pCdiPkg, pRDP_inVehicleData);
		}
    }

    return;
}
/**
 * @brief 目标跟踪处理, 此函数及更下层的函数，函数参数名沿用原来的
 * @param pCdiPkg RDP内部用于跟踪的原始点列表全局变量指针
 * @param pGroupInfo 组指针
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针
 * @param time 帧间隔
 * @param pTrkPkg RDP内部的航迹列表，包含多种航迹状态的目标
 * @return clthRadarSolveReturnType 处理状态，返回成功或失败
 */
clthRadarSolveReturnType RDP_solveRadarTrack(cdi_pkg_t *pCdiPkg, TrackGroupInfo_t *pGroupInfo, VDY_DynamicEstimate_t *pRDP_inVehicleData, float time, rdp_config_t* config, trk_pkg_t *pTrkPkg)
{
    // 初始化矩阵
    RDP_Track_cacEKFMatrixs(E_KAL_ACC_TRK, time, gEKF_A, gEKF_Q);

    //关联
    RDP_do_assoc(pCdiPkg, pRDP_inVehicleData, pTrkPkg, time, config, gEKF_A);

    //聚类
    RDP_do_cluster(pCdiPkg, pRDP_inVehicleData, pTrkPkg, pGroupInfo);

    // 航迹框功能，估计目标大小
    RDP_Track_collecteBoxPoints(pCdiPkg->cdi, pRDP_inVehicleData, pGroupInfo, pTrkPkg);

    // 遮挡点标记
    RDP_Track_markShelterPoint(pCdiPkg, pTrkPkg);

    //弱侧雷达RCTA区域目标统计-防止弱侧雷达起批导致DOW误报问题
    RDP_UpdataRctaAreaCdi(pRDP_inVehicleData, pCdiPkg, pTrkPkg);

    RDP_SceneRecognition(pRDP_inVehicleData, pCdiPkg, pTrkPkg);

    //航迹管理
    RDP_do_trackManage(pCdiPkg, pRDP_inVehicleData, pTrkPkg, pGroupInfo, time);

    // 更新航迹模块的结果
    RDP_Track_updateResults(pCdiPkg, time, pRDP_inVehicleData, gEKF_A, gEKF_Q, gEKF_R, pTrkPkg);

    RDP_Track_getLaneNearestTarget(pTrkPkg, time, pCdiPkg,pRDP_inVehicleData);

    return SOLVE_OK_WITH_UPDATE;
}
