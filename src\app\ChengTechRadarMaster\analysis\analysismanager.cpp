﻿#include "analysismanager.h"

#include "analysisworker.h"
#include "calculationworker.h"
#include "analysissaveworker.h"
#include "framebinarysaveworker.h"

#include <QThread>
#include <QDebug>

namespace Analysis {

AnalysisManager::AnalysisManager(QObject *parent)
    : QObject(parent)
{
    mAnalysisWorker = new AnalysisWorker;
    QThread *analysisWorkerThread = new QThread;
    mAnalysisWorker->moveToThread(analysisWorkerThread);

    analysisWorkerThread->start();

    mCalculationWorker = new CalculationWorker(mAnalysisWorker);
    QThread *calculationWorkerThread = new QThread;
    mCalculationWorker->moveToThread(calculationWorkerThread);

    calculationWorkerThread->start();

    mAnalysisSaveWorker = new AnalysisSaveWorker(mAnalysisWorker);
    QThread *analysisSaveWorkerThread = new QThread;
    mAnalysisSaveWorker->moveToThread(analysisSaveWorkerThread);

    connect(mAnalysisSaveWorker, &AnalysisSaveWorker::saveStarted, this, &AnalysisManager::saveStarted);
    connect(mAnalysisSaveWorker, &AnalysisSaveWorker::saveStarted, analysisSaveWorkerThread, [=](const QString &rawFilename, bool ok){
        if (ok){
            analysisSaveWorkerThread->start();
        }
    });
    connect(mAnalysisSaveWorker, &AnalysisSaveWorker::saveFinished, analysisSaveWorkerThread, &QThread::quit);
    connect(mAnalysisSaveWorker, &AnalysisSaveWorker::destroyed, analysisSaveWorkerThread, &QThread::deleteLater);

    //二进制保存
    mFrameBinarySaveWorker = new FrameBinarySaveWorker();
    QThread *mFrameBinarySaveWorkerThread = new QThread;
    mFrameBinarySaveWorker->moveToThread(mFrameBinarySaveWorkerThread);
    connect( mAnalysisWorker, &AnalysisWorker::analysisRadarFrameFinished,
             mFrameBinarySaveWorker, &FrameBinarySaveWorker::addRadarBody );
    connect( mAnalysisWorker, &AnalysisWorker::analysisTargetPoint16FrameFinished,
             mFrameBinarySaveWorker, &FrameBinarySaveWorker::addTargetPoint16Body );
    connect( mAnalysisWorker, &AnalysisWorker::analysisLanePointFrameFinished,
             mFrameBinarySaveWorker, &FrameBinarySaveWorker::addLanePointBody );
    mFrameBinarySaveWorkerThread->start();
}

bool AnalysisManager::startSave(const QString &savePath, const QDateTime &beginTime, bool oldStyle, bool needResponse)
{
    mFrameBinarySaveWorker->startSave( savePath, beginTime );
    return mAnalysisSaveWorker->startSave(savePath, beginTime, oldStyle, true, needResponse);
}

bool AnalysisManager::stopSave()
{
    mFrameBinarySaveWorker->stopSave();
    return mAnalysisSaveWorker->stopSave();
}

void AnalysisManager::setSaveCountMax(quint64 max)
{
    mAnalysisSaveWorker->setSaveCountMax(max);
}

void AnalysisManager::setAngleCompensation(AngleCompensation angleCompensation[], int count)
{
    for (int i = 0; i < count; ++i) {
        mAnalysisWorker->mAnalysisDatas[i].mAngleCompensation = angleCompensation[i];
        mCalculationWorker->mAnalysisDatas[i].mAngleCompensation = angleCompensation[i];
    }
}

} // namespace Analysis
