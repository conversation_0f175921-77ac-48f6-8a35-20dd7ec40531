﻿#ifndef CTANALYSISWORKER_H
#define CTANALYSISWORKER_H

#include <QObject>
#include <QTimer>

#include "devices/canframe.h"

class CTAnalysisWorker : public QObject
{
    Q_OBJECT
public:

    explicit CTAnalysisWorker(QObject *parent = nullptr);

    void reset();
    void setOutTime(int ms) { mOutTime = ms; }
    int outTime() const { return mOutTime; }

signals:
    void radarConnected(int c, int r);
    void frameError(int c, int r);

public slots:
    void canFrame(const Devices::Can::CanFrame &frame);

private:
    void timeOut(int c, int r);

    int mOutTime{800};
    QTimer *mTimer[2][4];
    bool mFirst[2][4];
    int mRadarFrameReceived[2][4];
};

#endif // CTANALYSISWORKER_H
