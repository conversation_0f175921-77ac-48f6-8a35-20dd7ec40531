<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ObjectView</class>
 <widget class="QWidget" name="ObjectView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ObjectView</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="Views::ObjectView::ObjectCoordinateSystem" name="coordinateSystem" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="toolWidget" native="true"/>
   </item>
  </layout>
  <action name="actionShowConfig">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="views.qrc">
     <normaloff>:/views/images/showconfig.png</normaloff>:/views/images/showconfig.png</iconset>
   </property>
   <property name="text">
    <string>显示设置</string>
   </property>
  </action>
  <action name="actionClearObject">
   <property name="icon">
    <iconset resource="views.qrc">
     <normaloff>:/views/images/clearobject.png</normaloff>:/views/images/clearobject.png</iconset>
   </property>
   <property name="text">
    <string>清除目标</string>
   </property>
  </action>
  <action name="actionPause">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="views.qrc">
     <normaloff>:/views/images/objectpaused.png</normaloff>
     <normalon>:/views/images/objectplay.png</normalon>:/views/images/objectpaused.png</iconset>
   </property>
   <property name="text">
    <string>暂停</string>
   </property>
  </action>
  <action name="actionClose">
   <property name="icon">
    <iconset resource="views.qrc">
     <normaloff>:/views/images/close.png</normaloff>:/views/images/close.png</iconset>
   </property>
   <property name="text">
    <string>关闭</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>Views::ObjectView::ObjectCoordinateSystem</class>
   <extends>QWidget</extends>
   <header>views/objectcoordinatesystem.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="views.qrc"/>
 </resources>
 <connections/>
</ui>
