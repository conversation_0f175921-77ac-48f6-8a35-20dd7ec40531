﻿#ifndef DC1000WORKER_H
#define DC1000WORKER_H

#include <QObject>
#include <QDateTime>
#include <QTimer>
#include <QFile>

#include "NETFrame.h"
#include "SafeQueue.h"

class NetworkUDPServer;
class CANDeviceZLG;
class <PERSON>Worker;
class CA<PERSON><PERSON>Worker;
class DeviceFileASC;

class DC1000Worker : public QObject
{
    Q_OBJECT
public:
    enum Type {
        Record,
        Playback,
    };
    explicit DC1000Worker(CANDeviceZLG *canDevice, CANSaveWorker *canSaveWorker, CameraWorker *cameraWorker, QObject *parent = nullptr);

    bool open(Type openType);
    void close();

    bool isOpened() const { return mOpened; }
    bool isConnected() const { return mConnected; }
    bool isCollecting() const { return mCollecting; }
    const std::string &errorstring() const { return mErrorString; }
    const QString &savePath() const { return mSavePath; }

    void pushFrame(unsigned short port, int len, unsigned char *data);

signals:
    void showLog(const QString &text);
    void opened();
    void clientConnected(unsigned short port, bool ok);
    void collectStatusChanged();
    void frameReceived();
    void saveFilename(const QString &filename);

public slots:
    void radarReceived(int radarReceived);
    void checkRadarReceived();
    void stop();
    void start(int frameSize, unsigned long long size, int canChannel, int chirpCount,
               int radarReceived, int vechileJump, const QString &ADCFilename, const QString &CANFilename);
    void startCollect();
    void stopCollect();
    void radarAlready(unsigned int status);
    void radarCollectStoped(unsigned int count);
    void startPlayback();
    void stopPlayback();
    void playbackStart();
    void playback_cmd1_frameInterrupt();
    void playbackData();
    void playbackStop();

private slots:
    void config();
    void parse();

private:
    bool sendRadarMode();
    bool sendVehicle();
    void prepareRecord();
    void preparePlayback();
    bool startSave();
    void stopSave();
    bool startSaveADC(QString filename, QString errorFilename);
    void stopSaveADC();
    void stopRadar();
    void parseFrame(const NETFrame &frame);
    void parseDC1000_4096(const NETFrame &frame);
    void parseDC1000_4098(const NETFrame &frame);
    QByteArray getDC1000Data(quint16 command);
    bool mkSavePath();

    friend void netReceived(unsigned short port, int len, unsigned char *data, void *obj);

    std::string mErrorString;

    NetworkUDPServer *mNetworkUDPServer{0};
    NetworkUDPServer *mNetworkUDPServerConfig{0};

    CANDeviceZLG *mCANDeviceZLG{0};
    CANSaveWorker *mCANSaveWorker{0};
    CameraWorker *mCameraWorker{0};
    DeviceFileASC *mDeviceFileASC;

    QDateTime mSaveTime;
    QString mSavePath{"./data"};
    QString mProjectSavePath;
    QString mCANSavePath;
    QString mVideoSavePath;

    QString mADCFilename;
    QString mCANFilename;

    Type mType{Record};
    bool mOpened{false};
    bool mConnected{false};
    bool mCollecting{false};
    bool mStarted{false};
    bool mRadarReceived{false};
    bool mRadarAlready{false};

    QTimer mTimerStop;

    SafeQueue<NETFrame> mSafeQueue;

    int mRadarReceivedCheckCount{0};
    int mRadarID{0};
    int mCanChannel;
    int mFrameSize;
    int mChirpSize;
    int mChirpCount;
    int mCurrntChirp{0};
    int mVechileJump{0};
    unsigned long long mNeedSize;
    quint64 mSequenceNumber{0};
    unsigned long long mCollectSize;
    unsigned long long mSavedSize;
    unsigned int mColletCout{0};

    unsigned int mRadarCollectCount{0};
    unsigned int mRadarFrameCount{0};

//    QByteArray chirpData;

    QByteArray cmd1_frameInterrupt;
    QByteArray cmd2_chirpInterrupt;
    QFile mFile;
    int mFileLength{0};

    FILE *mpFile{0};
    FILE *mpFileError{0};
};

#endif // DC1000WORKER_H
