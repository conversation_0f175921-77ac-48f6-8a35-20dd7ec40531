CMakeLists.txt 8935f858d0308fccf551559696797f36170e1404eefbb0bbb60f255e296d10e3
bzip2.pc.in bba1241c5dd9e86e9dc81915816edc32bd32bcef148715372b82ed62838ea740
cmake 3.29.0
features core;tool
fix-import-export-macros.patch 38918c1257c884cc5cde39d9f4235d0e71574f20c7e5ec686a506612f647495e
portfile.cmake 0581cada7dfe9bfac7e24badd7543b0593350727c3f2242faf80212ec37fce03
ports.cmake 0500e9e2422fe0084c99bdd0c9de4c7069b76da14c8b58228a7e95ebac43058a
post_build_checks 2
powershell 7.2.16
triplet x86-windows
triplet_abi 3e71dd1d4afa622894ae367adbbb1ecbd42c57c51428a86b675fa1c8cad3a581-e36df1c7f50ab25f9c182fa927d06c19ae082e0d599f132b3f655784b49e4b33-249e2da0fced5777e3f907c3dc10380ddd2c9c98
usage 75d339011fc11a43ee5093142ca1e8cb5e54db2ac93822abc5de694cda5a9881
vcpkg-cmake a0a36e21d32b4206f4f67926d6d4f3a1085958580627e52ffa8bff1f0c62cae2
vcpkg.json 96594ea06b5c6bc85d1e041e80d1f2988c08ecb92ec05d9df888a1f22fad8427
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d57e4f196c82dc562a9968c6155073094513c31e2de475694143d3aa47954b1c
vcpkg_download_distfile 2d57e1336ae9daab2f04c0623217094b026080d19bdca3a92396d68ea3e729c7
vcpkg_extract_source_archive 9c733832f8b58b05e6c329ae8b6ad1cfa858224fdc06d4a2d08b428e31de1a51
vcpkg_fixup_pkgconfig 904e67c46ecbb67379911bc1d7222855c0cbfcf1129bf47783858bcf0cc44970
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
