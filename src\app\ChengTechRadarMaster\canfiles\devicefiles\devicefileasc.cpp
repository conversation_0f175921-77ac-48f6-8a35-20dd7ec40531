﻿#include "devicefileasc.h"


#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <iostream>
#include <regex>

namespace Devices {
	namespace Can {

		bool startsWith(const std::string &str, const std::string &prefix) {
			return str.size() >= prefix.size() && str.compare(0, prefix.size(), prefix) == 0;
		}

		DeviceFileASC::DeviceFileASC(CANDeviceFile *device) : IDeviceFile(device)
		{

		}

		bool DeviceFileASC::openFile()
		{
            mInFileStream.open(mFilename.c_str(), mOpenInMode ? std::ios::in : std::ios::out);
			if (!mInFileStream.is_open()) {
				mErrorString = std::string("打开文件失败!\r\n ").append(mFilename);
				return false;
			}

			if (mOpenInMode) {
				parseHeader();
			}
			else {
				mInFileStream.setf(std::ios::fixed, std::ios::floatfield);
				mInFileStream.precision(6);
			}

			return true;
		}

		bool DeviceFileASC::closeFile()
		{
			if (mInFileStream.is_open()) {
				mInFileStream << "End Triggerblock\n";
				mInFileStream.close();
			}
			return true;
		}

		bool DeviceFileASC::readData()
		{
			std::string line;
			if (!std::getline(mInFileStream, line)) {
				return false;
			}

            if (line.size() < 32) {
                std::cout << __FUNCTION__ << " " << __LINE__ << " line size error! " << line << std::endl;
                return false;
            }

			bool bCanfd = false;
			bool bWait = false;

			switch (mMajorVersion)
			{
			case 7:
				return readDataVersion_7(line);
			case 12:
				return readDataVersion_12(line);
				break;
			default:
				break;
			}


			//time_diff = timeStamp - mPreTime;
			//mPreTime = timeStamp;

			return false;
		}

		bool DeviceFileASC::writeData(const CanFrame::Ptr pFrame)
		{
            if (!mSaveTimestamp && !writeHeader(pFrame->timestemp())) {
				return false;
			}
            float timeSpan = (pFrame->timestemp() - mSaveTimestamp) / 1000000.0f;
            if (pFrame->isCAN()) {
				// <Time>	 <Channel> <ID> <Dir> d <DLC> <D0> <D1>...<D8>
				mInFileStream << timeSpan << " " 
                    << pFrame->globalChannelIndex() + 1 << " "
                    << std::hex << pFrame->id() << " "
                    << (pFrame->isRx() ? "Rx" : "Tx") << " d "
                    << (uint16_t)pFrame->DLC() << " "
                    << pFrame->dataHex().toStdString() << " " << "\n";
			}
			else {
				// <Time>     CANFD <Channel> <Dir> <ID> <SymbolicName> <BRS> <ESI> d <DLC> <DataLength> <D1> … <D64>
				mInFileStream << timeSpan << " CANFD "
                    << pFrame->globalChannelIndex() + 1 << " "
                    << std::hex << pFrame->id() << " "
                    << (pFrame->isRx() ? "Rx" : "Tx") << " "
                    << (uint16_t)pFrame->brs() << " "
                    << (uint16_t)pFrame->esi() << " d "
                    << std::dec << (uint16_t)pFrame->DLC() << " "
                    << (uint16_t)pFrame->length() << " "
                    << pFrame->dataHex().toStdString() << " " << "\n";

            }

            return true;
		}

		static  const char *abday[7] = {
			"Sun",
			"Mon",
			"Tue",
			"Wed",
			"Thu",
			"Fri",
			"Sat"
		};

		static const char *abmon[] = {
			"Jan",
			"Feb",
			"Mar",
			"Apr",
			"May",
			"Jun",
			"Jul",
			"Aug",
			"Sep",
			"Oct",
			"Nov",
			"Dec"
		};

		bool DeviceFileASC::readDataVersion_7(const std::string & line)
		{
//            std::cout << __FUNCTION__ << " " << __LINE__ << " " << line << std::endl;
			if (line.empty() || startsWith(line, "End")) {
				return false;
			}
            float timeSpan = 0.0f;  // s
			uint32_t channelIndex = 0;
			uint32_t id = 0;
			char rx_tx[4];
			uint32_t brs = 0;
			uint32_t esi = 0;
			uint32_t dlc = 0;
			uint32_t length = 0;
            uint8_t data[128];
			sscanf(line.c_str(), "%f %u", &timeSpan, &channelIndex);
            uint64_t time_diff = timeSpan * 1000000;
            uint64_t timestamp = mSaveTimestamp + time_diff; // us
            //std::cout << mSaveTimestamp << " " << timeSpan << " " << timestamp << " " << channelIndex << std::endl;
			if (channelIndex) { // CAN
				// <Time>	 <Channel> <ID> <Dir> d <DLC> <D0> <D1>...<D8>
				// 12.650500 1		   3f0  Rx    d 8     00 00 00 00 00 00 00 00
				sscanf(
					line.c_str(), "%f %u %x %s d %u %x %x %x %x %x %x %x %x",
					&timeSpan, &channelIndex, &id, rx_tx, &dlc,
                    data, data + 1, data + 2, data + 3, data + 4, data + 5, data + 6, data + 7);

                if (!validID(id)) {
                    return true;
                }
                Devices::Can::CanFrame::Ptr frame = Devices::Can::CanFrame::New(
                            0,
                            channelIndex - 1,
                            (strcmp("Tx", rx_tx) ? Devices::Can::CanFrame::TX : Devices::Can::CanFrame::RX),
                            id,
                            data,
                            dlc,
                            timestamp);
                dely(time_diff);
				callback(frame);
			}
			else {
				// <Time>     CANFD <Channel> <Dir> <ID> <SymbolicName> <BRS> <ESI> d <DLC> <DataLength> <D1> … <D64>
				// 258.283700 CANFD 1               720  Rx             1     0     d 8     8            00 00 00 09 18 c0 02 46
                sscanf(
                    line.c_str(), "%f CANFD %u %x %s %u %u d %x %u"
                    "%x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x "
                    "%x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x "
                    "%x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x "
                    "%x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x",
                    &timeSpan, &channelIndex, &id, rx_tx, &brs, &esi, &dlc, &length,
                    data + 0, data + 1, data + 2, data + 3, data + 4, data + 5, data + 6, data + 7,
                    data + 8, data + 9, data + 10, data + 11, data + 12, data + 13, data + 14, data + 15,
                    data + 16, data + 17, data + 18, data + 19, data + 20, data + 21, data + 22, data + 23,
                    data + 24, data + 25, data + 26, data + 27, data + 28, data + 29, data + 30, data + 31,
                    data + 32, data + 33, data + 34, data + 35, data + 36, data + 37, data + 38, data + 39,
                    data + 40, data + 41, data + 42, data + 43, data + 44, data + 45, data + 46, data + 47,
                    data + 48, data + 49, data + 50, data + 51, data + 52, data + 53, data + 54, data + 55,
                    data + 56, data + 57, data + 58, data + 59, data + 60, data + 61, data + 62, data + 63);

                if (!validID(id)) {
                    return true;
                }
                Devices::Can::CanFrame::Ptr frame = Devices::Can::CanFrame::New(
                            0,
                            channelIndex - 1,
                            (strcmp("Tx", rx_tx) ? Devices::Can::CanFrame::TX : Devices::Can::CanFrame::RX),
                            id,
                            data,
                            length,
                            timestamp);

                dely(time_diff, frame->id());
                callback(frame);
			}

			return true;
		}

		bool DeviceFileASC::readDataVersion_12(const std::string & line)
		{
//			std::cout << __FUNCTION__ << " " << __LINE__ << " " << line << std::endl;
			if (startsWith(line, "End")) {
				return false;
			}
			float timeSpan = 0.0f;
			uint32_t channelIndex = 0;
			uint32_t id = 0;
			char rx_tx[4];
			uint32_t brs = 0;
			uint32_t esi = 0;
			uint32_t dlc = 0;
			uint32_t length = 0;
            uint8_t data[128];
			sscanf(line.c_str(), "%f %u", &timeSpan, &channelIndex);

            uint64_t time_diff = timeSpan * 1000000;
            uint64_t timestamp = mSaveTimestamp + time_diff;
			//std::cout << timeSpan << " " << channelIndex << std::endl;
			if (channelIndex) { // CAN
				// <Time>	 <Channel> <ID> <Dir> d <DLC> <D0> <D1>...<D8>
				// 12.650500 1		   3f0  Rx    d 8     00 00 00 00 00 00 00 00
				sscanf(
					line.c_str(), "%f %u %x %s d %u %x %x %x %x %x %x %x %x",
					&timeSpan, &channelIndex, &id, rx_tx, &dlc,
					data, data + 1, data + 2, data + 3, data + 4, data + 5, data + 6, data + 7);

                if (!validID(id)) {
                    return true;
                }
                Devices::Can::CanFrame::Ptr frame = Devices::Can::CanFrame::New(
                            0,
                            channelIndex - 1,
                            (strcmp("Tx", rx_tx) ? Devices::Can::CanFrame::TX : Devices::Can::CanFrame::RX),
                            id,
                            data,
                            dlc,
                            timestamp);
                dely(time_diff);
				callback(frame);
			}
			else {
				// <Time>     CANFD <Channel> <Dir> <ID> <SymbolicName> <BRS> <ESI> d <DLC> <DataLength> <D1> … <D64>
				// 258.283700 CANFD 1               720  Rx             1     0     d 8     8            00 00 00 09 18 c0 02 46
				sscanf(
					line.c_str(), "%f CANFD %u %s %x %u %u %x %u", 
					&timeSpan, &channelIndex, rx_tx, &id, &brs, &esi, &dlc, &length);
				for (int i = 0; i < length; ++i) {
					sscanf(line.c_str() + 80 + i * 3, "%2x", data + i);
				}


                if (!validID(id)) {
                    return true;
                }
                Devices::Can::CanFrame::Ptr frame = Devices::Can::CanFrame::New(
                            0,
                            channelIndex - 1,
                            (strcmp("Tx", rx_tx) ? Devices::Can::CanFrame::TX : Devices::Can::CanFrame::RX),
                            id,
                            data,
                            length,
                            timestamp);
                dely(time_diff);
				callback(frame);
			}

			return true;
		}

		bool DeviceFileASC::parseHeader()
		{
			std::string line;
			while (std::getline(mInFileStream, line)) {
				std::cout << __FUNCTION__ << __LINE__ << line << std::endl;
				if (startsWith(line, "date")) {
					parseDate(line);
				}
				// base hex  timestamps absolute
				else if (startsWith(line, "base")) {
				}
				// internal events logged
				else if (startsWith(line, "internal")) {
				}
				else if (startsWith(line, "// version")) {
					parseVersion(line);
					break;
				}
				else {
					return false;
				}
			}

			return true;
		}

		bool DeviceFileASC::parseDate(const std::string & line)
		{
			// date Wed Mar 22 03:41:23 PM 2023
			// date Thu Jun 29 04:29:36.497 pm 2023
			char cWeek[64], cAPM[64], cMonth[64], cSecond[64];
			uint32_t week{ 0 }, year{ 0 }, month{ 0 }, day{ 0 }, hour{ 0 }, minute{ 0 }, second{ 0 }, millisecond{ 0 };
			sscanf(line.c_str(), "date %s %s %u %u:%u:%s %s %u", cWeek, cMonth, &day, &hour, &minute, cSecond, cAPM, &year);
			//std::cout << cWeek << " " << cMonth << " " << day << " " << hour << " " << minute << " " << cSecond << " " << cAPM << " " << year << std::endl;

			struct tm timeinfo;
			for (int i = 0; i < 12; ++i) {
				if (startsWith(std::string(cMonth), abmon[i])) {
					month = i + 1;
					break;
				}
			}
			std::string apm1("PM");
			std::string apm2("pm");
			if (hour < 12 && ((std::string(cAPM).compare(0, apm1.size(), apm1) == 0) ||
				(std::string(cAPM).compare(0, apm2.size(), apm2) == 0))) {
				hour += 12;
			}

			std::string sSecond(cSecond);
			std::cout << sSecond << std::endl;
			std::string::size_type pos = sSecond.find(".", 0);
			if (pos == -1) {
				second = atoi(cSecond);
				//std::cout << second << " - " << millisecond << std::endl;
			}
			else {
				second = atoi(sSecond.substr(0, pos).c_str());
				millisecond = atoi(sSecond.substr(pos + 1, sSecond.length() - pos).c_str());
				//std::cout << second << " . " << millisecond << std::endl;
			}

			std::cout << year << "-" << month << "-" << day << " " << hour << ":" << minute << ":" << second << "." << millisecond << std::endl;
			struct tm tm_;
			tm_.tm_year = year - 1900;
			tm_.tm_mon = month - 1;
			tm_.tm_mday = day;
			tm_.tm_hour = hour;
			tm_.tm_min = minute;
			tm_.tm_sec = second;
			tm_.tm_isdst = 0;

			time_t timeStamp = mktime(&tm_);

			// 微妙级时间戳
			mSaveTimestamp = timeStamp * 1000000 + millisecond * 1000;

			return true;
		}
		bool DeviceFileASC::parseVersion(const std::string & line)
		{
			// version 7.0.0
			// version 12.0.0
			sscanf(line.c_str(), "// version %u.%u.%u", &mMajorVersion, &mMinorVersion, &mPatchVersion);
			std::cout << mMajorVersion << "." << mMinorVersion << "." << mPatchVersion << std::endl;

			return true;
		}
		bool DeviceFileASC::writeHeader(uint64_t timestamp)
		{
			mSaveTimestamp = timestamp;
			time_t now_time = timestamp / 1000000;
			struct tm *pTM = localtime(&now_time);
			
			char number[8];
			// date Wed Mar 22 03:41:23 PM 2023
			mInFileStream << "date ";
			mInFileStream << abday[pTM->tm_wday] << " ";
			mInFileStream << abmon[pTM->tm_mon] << " ";
			sprintf_s(number, "%02d", pTM->tm_mday);
			mInFileStream << number << " ";
			sprintf_s(number, "%02d", pTM->tm_hour > 12 ? pTM->tm_hour - 12 : pTM->tm_hour);
			mInFileStream << number << ":";
			sprintf_s(number, "%02d", pTM->tm_min);
			mInFileStream << number << ":";
			sprintf_s(number, "%02d", pTM->tm_sec);
			mInFileStream << number << " ";
			mInFileStream << (pTM->tm_hour > 12 ? "PM" : "AM") << " ";
			mInFileStream << pTM->tm_year + 1900;
			mInFileStream << "\n";
			mInFileStream << "base hex timestamps absolute\n";
			mInFileStream << "// version 7.2.0\n";
			return true;
		}
	}
}
