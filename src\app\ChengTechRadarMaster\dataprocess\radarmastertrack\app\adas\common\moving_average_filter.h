/**
*********************************************************************************************************
*               Copyright (C) SHL. All rights reserved.
**********************************************************************************************************
* 文件名file:moving_average_filter.h
* 简述brief:
* 作者author:
* 日期date:
* 版本version:
*********************************************************************************************************
*/

#ifndef __MOVING_AVERAGE_FILTER_H__
#define __MOVING_AVERAGE_FILTER_H__

#include "stdint.h"

/***********************
 * * 宏定义
 ***********************/
#define WEIGHTED_MOVING_WIN_SIZE 6
#define MOVING_MEAN_WIN_SIZE 3

typedef struct __data_record_item_t
{
    float x;               // x轴距离，m
    float y;               // y轴距离，m
    float velSpeedVal;     /*本车的速度信息*/
    uint32_t alarmType;     // 目标报警类型，倒车工况下，功能会同时发生，如RCTA和RCTB
    uint32_t lastAlarmType; // 目标上一次报警类型，倒车工况下，功能会同时发生，如RCTA和RCTB
    uint8_t adasBSDWarning;    // 报警等级
    uint8_t id; 
} data_record_item_t;
struct __filter_t
{
    float filter_x;           // x轴距离，m
    float filter_y;           // y轴距离，m
    float filter_velSpeedVal; /*本车的速度信息*/
};
typedef struct __data_record_t
{
    struct __filter_t filter; // filter
    uint8_t currentAlarm;     // 当前报警状态
    uint8_t lastAlarm;        // 最后的报警状态
    int8_t id;
    uint8_t extendedAlarmTime; // 计时标志，非0开始计时，最大500ms，这个时候
    uint8_t delayCnt; //
    uint8_t adasBSDWarning;    // 报警等级
} data_record_t;

typedef data_record_t (*pmoving_filter_t)(data_record_item_t *current);

data_record_t moving_mean_filter(data_record_item_t *current);
data_record_t weighted_moving_filter(data_record_item_t *current);

#endif


