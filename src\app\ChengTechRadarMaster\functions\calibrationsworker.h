﻿#ifndef CALIBARATIONSWORKER_H
#define CALIBARATIONSWORKER_H

#include <QObject>

#include "devices/canframe.h"

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Functions {
class UDS;
}

class CalibrationWorker : public QObject
{
    Q_OBJECT
public:
    enum CalibrationType {
        AfterSale,          // 售后标定
        OffLineCalibration, // 下线标定
        ExitFactoryMode     // 退出工厂模式
    };

    enum ProtocolType {
        ProtocolBYD220,
        PtotocolHSAE,
        ProtocolUnknown
    };

    explicit CalibrationWorker(Devices::Can::DeviceManager *deviceManager, QObject *parent = nullptr);
    Functions::UDS *uds() { return mUDS; }
    void setProtocolType(ProtocolType protocol, quint8 radarID);
    void setCalibrationType(CalibrationType t) { mCalibrationType = t; }

signals:
    void message(const QString message);
    void calibrationStarted();
    void calibrationFinished(bool ok = true);

public slots:
    void canFrame(const Devices::Can::CanFrame &frame);

    void start();
    void stop();
    void read();

private:
    /** @brief 售后标定 */
    void afterSaleCalibration();
    void stopAfterSaleCalibration();
    void readAfterSaleCalibration();

    /** @brief 下线标定 */
    void offlineSaleCalibration();
    void stopOfflineCalibration();
    void readOfflineCalibration();

    /** @brief 退出工厂模式 */
    void exitFactoryMode();

    QString verifyDTC(const QByteArray &data);

    quint32 mResponseAddress[ProtocolUnknown][4]{{0x7FA, 0x7FA, 0x7FA, 0x7FA}, {0x7B1, 0x7B1, 0x7B1, 0x7B1}}; // 4,5,6,7
    quint32 mFunctionAddress[ProtocolUnknown][4]{{0x7DF, 0x7DF, 0x7DF, 0x7DF}, {0x7DF, 0x7DF, 0x7DF, 0x7DF}};
    quint32 mPhysicalAddress[ProtocolUnknown][4]{{0x7F2, 0x7F2, 0x7F2, 0x7F2}, {0x731, 0x731, 0x731, 0x731}};
    uint32_t mMASK[ProtocolUnknown][4]{{0x94, 0x94, 0x94, 0x94}, {0x23344556, 0x23344556, 0x23344556, 0x23344556}};

    ProtocolType mProtocolType{ProtocolBYD220};
    quint8 mRadarID{0};

    CalibrationType mCalibrationType{AfterSale};

    Functions::UDS *mUDS{0};

    quint32 mCalibrationCount{0};
};

#endif // CALIBARATIONSWORKER_H
