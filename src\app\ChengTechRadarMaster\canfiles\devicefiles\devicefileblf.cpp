﻿#include "devicefileblf.h"

#include <time.h>
#include <sys/timeb.h>
#include <iostream>

namespace Devices {
	namespace Can {

		/** @brief 微妙级时间戳 */
		uint64_t blfTimestamp(SYSTEMTIME &systemtime) {
			struct tm time;
			time.tm_year = systemtime.wYear - 1900;
			time.tm_mon = systemtime.wMonth - 1;
			time.tm_mday = systemtime.wDay;
			time.tm_hour = systemtime.wHour;
			time.tm_min = systemtime.wMinute;
			time.tm_sec = systemtime.wSecond;
			time_t ltime_new = mktime(&time);

			return ((uint64_t)ltime_new) * 1000000 + ((uint64_t)systemtime.wMilliseconds) * 1000;
		}

		DeviceFileBLF::DeviceFileBLF(CANDeviceFile *device) : IDeviceFile(device)
		{

		}
		//1844 6744 0737 0955 1615;
		//1844 6744 0737 0867 3616;
		//     32590461000
		//1688634158122000
		//1688666748583000
		//2023-07-06 17:02:38

		bool DeviceFileBLF::openFile()
		{
			if (mOpenInMode) {

			}
			mHBLF = BLCreateFile(mFilename.c_str(), mOpenInMode ? GENERIC_READ : GENERIC_WRITE);

			if (INVALID_HANDLE_VALUE == mHBLF)
			{
				mErrorString = std::string("打开BLF文件失败!\r\n%1").append(mFilename);
				return false;
			}

			if (!mOpenInMode) {
				mSaveTimestamp = 0;
				return true;
			}

			VBLFileStatisticsEx statistics = { sizeof(statistics) };
			BLGetFileStatisticsEx(mHBLF, &statistics); //这一行是不是可以不要
			SYSTEMTIME &systemtime = statistics.mMeasurementStartTime;
			// 微妙级时间戳
			mSaveTimestamp = blfTimestamp(systemtime);
			std::cout << "[" << mSaveTimestamp << "]" << std::endl;

			return true;
		}

		bool DeviceFileBLF::closeFile()
		{
			if (mHBLF != INVALID_HANDLE_VALUE) {
				BLCloseHandle(mHBLF);
			}

            mHBLF = INVALID_HANDLE_VALUE;

			return true;
		}

		bool DeviceFileBLF::readData()
		{
			if (!mHBLF)
				return false;

			uint64_t time_diff = 0; // us
			bool bSuccess = false;
			VBLObjectHeaderBase base;

			BLPeekObject(mHBLF, &base);
			switch (base.mObjectType)
			{
			case BL_OBJ_TYPE_CAN_MESSAGE:
			{
				VBLCANMessage can_msg;
				/* read CAN message */
				can_msg.mHeader.mBase = base;
				bSuccess = BLReadObjectSecure(mHBLF, &can_msg.mHeader.mBase, sizeof(can_msg));
				/* free memory for the CAN fd message */
				if (bSuccess) {
					//32,590,461,000ns
					time_diff = can_msg.mHeader.mObjectTimeStamp / 1000;

                    if (!validID(can_msg.mID)) {
                        return true;
                    }
                                        Devices::Can::CanFrame::Ptr frame = Devices::Can::CanFrame::New(
                                                    0,
                                                    can_msg.mChannel - 1,
                                                    Devices::Can::CanFrame::RX,
                                                    can_msg.mID,
                                                    can_msg.mData,
                                                    can_msg.mDLC,
                                                    mSaveTimestamp + time_diff);
                                        // 延时
                                        dely(time_diff);
                                        callback(frame);
                                        BLFreeObject(mHBLF, &can_msg.mHeader.mBase);
                                }
                                break;
			}
			case BL_OBJ_TYPE_CAN_FD_MESSAGE_64:
			{
				VBLCANFDMessage64 canfd_msg_64;
				/* read CAN fd message */
				canfd_msg_64.mHeader.mBase = base;
				bSuccess = BLReadObjectSecure(mHBLF, &canfd_msg_64.mHeader.mBase, sizeof(canfd_msg_64));
				/* free memory for the CAN fd message */
				if (bSuccess) {
                    time_diff = canfd_msg_64.mHeader.mObjectTimeStamp / 1000; // us
                    if (!validID(canfd_msg_64.mID)) {
                        return true;
                    }
                    Devices::Can::CanFrame::Ptr frame = Devices::Can::CanFrame::New(
                                0,
                                canfd_msg_64.mChannel - 1,
                                Devices::Can::CanFrame::RX,
                                canfd_msg_64.mID,
                                canfd_msg_64.mData,
                                canfd_msg_64.mValidDataBytes ? canfd_msg_64.mValidDataBytes : Devices::Can::CanFrame::DLCToLength((uint8_t)canfd_msg_64.mDLC),
                                mSaveTimestamp + time_diff);
                    // 延时
                    dely(time_diff);
                    callback(frame);
                    BLFreeObject(mHBLF, &canfd_msg_64.mHeader.mBase);
                                }
                                break;
			}
			case BL_OBJ_TYPE_CAN_FD_MESSAGE:
			{
				VBLCANFDMessage canfd_msg;
				/* read CAN fd message */
				canfd_msg.mHeader.mBase = base;
				bSuccess = BLReadObjectSecure(mHBLF, &canfd_msg.mHeader.mBase, sizeof(canfd_msg));
				/* free memory for the CAN fd message */
				if (bSuccess) {
					time_diff = canfd_msg.mHeader.mObjectTimeStamp / 1000;

                    if (!validID(canfd_msg.mID)) {
                        return true;
                    }
                    Devices::Can::CanFrame::Ptr frame = Devices::Can::CanFrame::New(
						0,
						canfd_msg.mChannel - 1,
                                Devices::Can::CanFrame::RX,
						canfd_msg.mID,
						canfd_msg.mData,
						canfd_msg.mValidDataBytes ? canfd_msg.mValidDataBytes : Devices::Can::CanFrame::DLCToLength((uint8_t)canfd_msg.mDLC),
                                                mSaveTimestamp + time_diff);
					// 延时
					dely(time_diff);
					callback(frame);
					BLFreeObject(mHBLF, &canfd_msg.mHeader.mBase);
				}
				break;
			}
			default:
				/* skip all other objects */
				bSuccess = BLSkipObject(mHBLF, &base);
				break;
			}
			return bSuccess;
		}
		bool DeviceFileBLF::writeData(const CanFrame::Ptr pFrame)
		{
            if (!mSaveTimestamp && !writeBLFHead(pFrame->timestemp())) {
				return false;
			}

            if (pFrame->isCAN()) {
				VBLCANMessage message;
				memset(&message, 0, sizeof(VBLCANMessage));
				message.mHeader.mBase.mSignature = BL_OBJ_SIGNATURE;
				message.mHeader.mBase.mHeaderSize = sizeof(message.mHeader);
				message.mHeader.mBase.mHeaderVersion = 1;
				message.mHeader.mBase.mObjectSize = sizeof(VBLCANMessage);
				message.mHeader.mBase.mObjectType = BL_OBJ_TYPE_CAN_MESSAGE;
				message.mHeader.mObjectFlags = BL_OBJ_FLAG_TIME_ONE_NANS;
                message.mHeader.mObjectTimeStamp = (pFrame->timestemp() - mSaveTimestamp) * 1000;

				/* setup CAN message */
                message.mChannel = pFrame->globalChannelIndex() + 1;
                message.mFlags = CAN_MSG_FLAGS((pFrame->isRx()? 0 : 1), 0);
				//message.mDLC = 8;
                message.mDLC = pFrame->DLC();
                message.mID = pFrame->id();
                memcpy(message.mData, pFrame->data(), message.mDLC);

				/* write CAN message */
				return BLWriteObject(mHBLF, &message.mHeader.mBase);
			} else /*if (frame.isCANFD()) */{
				VBLCANFDMessage message;
				memset(&message, 0, sizeof(VBLCANFDMessage));
				message.mHeader.mBase.mSignature = BL_OBJ_SIGNATURE;
				message.mHeader.mBase.mHeaderSize = sizeof(message.mHeader);
				message.mHeader.mBase.mHeaderVersion = 1;
				message.mHeader.mBase.mObjectSize = sizeof(VBLCANFDMessage);
				message.mHeader.mBase.mObjectType = BL_OBJ_TYPE_CAN_FD_MESSAGE;
				message.mHeader.mObjectFlags = BL_OBJ_FLAG_TIME_ONE_NANS;
                message.mHeader.mObjectTimeStamp = (pFrame->timestemp() - mSaveTimestamp) * 1000;

                message.mID = pFrame->id();
                message.mDLC = pFrame->DLC();
                message.mChannel = pFrame->globalChannelIndex() + 1;
                message.mFlags = CAN_MSG_FLAGS((pFrame->isRx() ? 0 : 1), 0);
                message.mValidDataBytes = pFrame->length();
                memcpy(message.mData, pFrame->data(), message.mValidDataBytes);

				return BLWriteObject(mHBLF, &message.mHeader.mBase);
			}

			return true;
		}
		bool DeviceFileBLF::writeBLFHead(uint64_t timestamp)
		{
			if (INVALID_HANDLE_VALUE == mHBLF) {
				return false;
			}
			mSaveTimestamp = timestamp;

            time_t t = timestamp / 1000000;
			struct tm temptm = *localtime(&t);
			SYSTEMTIME systemTime{ 
                static_cast<WORD>(1900 + temptm.tm_year),
                static_cast<WORD>(1 + temptm.tm_mon),
                static_cast<WORD>(temptm.tm_wday),
                static_cast<WORD>(temptm.tm_mday),
                static_cast<WORD>(temptm.tm_hour),
                static_cast<WORD>(temptm.tm_min),
                static_cast<WORD>(temptm.tm_sec),
                static_cast<WORD>(timestamp / 1000 % 1000) };

			BLSetApplication(mHBLF, BL_APPID_CANCASEXLLOG, 1, 0, 1);
			BLSetMeasurementStartTime(mHBLF, &systemTime); //测量时间
			BLSetWriteOptions(mHBLF, 0, 0);//设置是否压缩

			VBLAppTrigger appTrigger;
			memset(&appTrigger, 0, sizeof(VBLAppTrigger));
			appTrigger.mHeader.mBase.mSignature = BL_OBJ_SIGNATURE;
			appTrigger.mHeader.mBase.mHeaderSize = sizeof(appTrigger.mHeader);
			appTrigger.mHeader.mBase.mHeaderVersion = 1;
			appTrigger.mHeader.mBase.mObjectSize = sizeof(VBLAppTrigger);
			appTrigger.mHeader.mBase.mObjectType = BL_OBJ_TYPE_APP_TRIGGER;
			appTrigger.mHeader.mObjectFlags = BL_OBJ_FLAG_TIME_ONE_NANS;

			//32.590,461,000s
			appTrigger.mHeader.mObjectTimeStamp = 0;
			return BLWriteObject(mHBLF, &appTrigger.mHeader.mBase);
		}
	}
}
