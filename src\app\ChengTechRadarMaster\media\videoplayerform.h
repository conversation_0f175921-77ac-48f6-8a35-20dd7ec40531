﻿#ifndef VIDEOPLAYERFORM_H
#define VIDEOPLAYERFORM_H

#include <QWidget>
#include <QMediaPlayer>

namespace Ui {
class VideoPlayerForm;
}

class QListWidgetItem;

class VideoPlayerForm : public QWidget
{
    Q_OBJECT

public:
    explicit VideoPlayerForm(QWidget *parent = nullptr);
    ~VideoPlayerForm();

signals:
    void skipToTimestamp( quint64 timestamp );

public slots:
    void skipToVideoFrame( quint64 ms );

private slots:
    void on_pushButtonSelectFile_clicked();

    void on_pushButtonPlay_clicked();

    void on_pushButtonPause_clicked();

    void on_pushButtonStop_clicked();

    void positionChanged(qint64 position);


    void on_pushButtonPreFrame_clicked();

    void on_pushButtonNextFrame_clicked();

    void on_pushButtonPreFrame2_clicked();

    void on_pushButtonNextFrame2_clicked();

    void on_pushButtonPreFrame3_clicked();

    void on_pushButtonNextFrame3_clicked();

    void on_listWidget_itemDoubleClicked(QListWidgetItem *item);

    void on_pushButtonSkipBLF_clicked();

    void on_dateTimeEditBegin_dateTimeChanged(const QDateTime &dateTime);

    void on_dateTimeEditEnd_dateTimeChanged(const QDateTime &dateTime);

private:
    //void initBeginEndTime( const QString& fileName );
    bool initBookMark( const QString& fileName );
    bool initVideoInfo( const QString& fileName );
//    void initVideoTime();
    void updateCurrentTime();

    quint64 timestampToPos( quint64 timestamp );
    quint64 posToTimestamp( quint64 pos );

    QString timeStr( quint64 position );



private:
    Ui::VideoPlayerForm *ui;
    QMediaPlayer mMediaPlayer;

    QString mPath;
    QString mFileName;

    quint64 mCurrentPos{0};
    quint32 mNextStep{10};

    quint64 mBeginTimeMs{0};
    quint64 mEndTimeMs{0};

    //帧和时间戳的索引
    QMap< quint64, quint64 > mFrameTimestamp;
    QMap< quint64, quint64 > mTimestampFrame;
    QList< quint64 > mTimestampList;
};

#endif // VIDEOPLAYERFORM_H
