<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Camera</class>
 <widget class="QMainWindow" name="Camera">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>668</width>
    <height>422</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Camera</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_3">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item row="0" column="0" rowspan="2">
     <widget class="QStackedWidget" name="stackedWidget">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>1</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="palette">
       <palette>
        <active>
         <colorrole role="Base">
          <brush brushstyle="SolidPattern">
           <color alpha="255">
            <red>255</red>
            <green>255</green>
            <blue>255</blue>
           </color>
          </brush>
         </colorrole>
         <colorrole role="Window">
          <brush brushstyle="SolidPattern">
           <color alpha="255">
            <red>145</red>
            <green>145</green>
            <blue>145</blue>
           </color>
          </brush>
         </colorrole>
        </active>
        <inactive>
         <colorrole role="Base">
          <brush brushstyle="SolidPattern">
           <color alpha="255">
            <red>255</red>
            <green>255</green>
            <blue>255</blue>
           </color>
          </brush>
         </colorrole>
         <colorrole role="Window">
          <brush brushstyle="SolidPattern">
           <color alpha="255">
            <red>145</red>
            <green>145</green>
            <blue>145</blue>
           </color>
          </brush>
         </colorrole>
        </inactive>
        <disabled>
         <colorrole role="Base">
          <brush brushstyle="SolidPattern">
           <color alpha="255">
            <red>145</red>
            <green>145</green>
            <blue>145</blue>
           </color>
          </brush>
         </colorrole>
         <colorrole role="Window">
          <brush brushstyle="SolidPattern">
           <color alpha="255">
            <red>145</red>
            <green>145</green>
            <blue>145</blue>
           </color>
          </brush>
         </colorrole>
        </disabled>
       </palette>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="viewfinderPage">
       <layout class="QGridLayout" name="gridLayout_5">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <widget class="QCameraViewfinder" name="viewfinder" native="true"/>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="previewPage">
       <layout class="QGridLayout" name="gridLayout_4">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <widget class="QLabel" name="lastImagePreviewLabel">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="MinimumExpanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="frameShape">
           <enum>QFrame::Box</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>668</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionStartCamera"/>
    <addaction name="actionStopCamera"/>
    <addaction name="separator"/>
    <addaction name="actionSettings"/>
    <addaction name="separator"/>
   </widget>
   <widget class="QMenu" name="menuDevices">
    <property name="title">
     <string>Devices</string>
    </property>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuDevices"/>
  </widget>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
  </action>
  <action name="actionStartCamera">
   <property name="text">
    <string>Start Camera</string>
   </property>
  </action>
  <action name="actionStopCamera">
   <property name="text">
    <string>Stop Camera</string>
   </property>
  </action>
  <action name="actionSettings">
   <property name="text">
    <string>Settings</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCameraViewfinder</class>
   <extends>QWidget</extends>
   <header>qcameraviewfinder.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>actionExit</sender>
   <signal>triggered()</signal>
   <receiver>Camera</receiver>
   <slot>close()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>154</x>
     <y>130</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionSettings</sender>
   <signal>triggered()</signal>
   <receiver>Camera</receiver>
   <slot>configureCaptureSettings()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>333</x>
     <y>210</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionStartCamera</sender>
   <signal>triggered()</signal>
   <receiver>Camera</receiver>
   <slot>startCamera()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>333</x>
     <y>210</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionStopCamera</sender>
   <signal>triggered()</signal>
   <receiver>Camera</receiver>
   <slot>stopCamera()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>333</x>
     <y>210</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>record()</slot>
  <slot>pause()</slot>
  <slot>stop()</slot>
  <slot>enablePreview(bool)</slot>
  <slot>configureCaptureSettings()</slot>
  <slot>takeImage()</slot>
  <slot>startCamera()</slot>
  <slot>toggleLock()</slot>
  <slot>setMuted(bool)</slot>
  <slot>stopCamera()</slot>
  <slot>setExposureCompensation(int)</slot>
 </slots>
</ui>
