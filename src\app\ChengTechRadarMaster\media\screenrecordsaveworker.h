﻿#ifndef SCREENRECORDSAVEWORKER_H
#define SCREENRECORDSAVEWORKER_H

#include <QObject>
#include <QFile>
#include <QMutex>

class ScreenRecordSaveWorker : public QObject
{
    Q_OBJECT
public:
    explicit ScreenRecordSaveWorker(QObject *parent = nullptr);
    bool startSave(const QString &savePath, const QDateTime &beginTime );
    void stopSave();

    bool isSaving() { return mSaving; }

private slots:
    void screenRecord();
    void mergePictures();

signals:

private:
    void saveImage( const QPixmap& pixmap, quint64 index );

private:
    QFile mVtFile;

    QString mVideoFileName;
    QString mVtFileName;
    QString mPicSavePath;

    bool mSaving{false};
    quint64 mFrameIndex{0};
    quint64 mBeginTimes{0};
    quint64 mEndTimes{0};
    QMutex mMutex;
};

#endif // SCREENRECORDSAVEWORKER_H
