﻿#include "CANDeviceZLG.h"

#include <string>
#include <iostream>
#include <thread>

static void deviceReceive(CANDeviceZLG *pCANDevice)
{
    std::cout << "线程结束，开始读取CAN数据..." << std::endl;
    while (pCANDevice->isOpened()) {
        pCANDevice->receive();
    }
    pCANDevice->closeDevice();
    std::cout << "线程结束，停止读取CAN数据." << std::endl;
}

CANDeviceZLG::CANDeviceZLG(SafeQueue<CANFrame> *safeQueue) : mSafeQueue(safeQueue)
{
}

bool CANDeviceZLG::open(int index, int baudRate, int dataRate, bool fd)
{
    mDeviceIndex = index;
    mBaudRate = baudRate;
    mDataRate = dataRate;
    mCANFD = fd;

    if (isOpened()) {
        return true;
    }

    mOpened = openDevice();

    if (mOpened) {
        std::thread(deviceReceive, this).detach();
    }

    return mOpened;
}

void CANDeviceZLG::close()
{
    mOpened = false;
}

void CANDeviceZLG::receive()
{
    for (int i = 0; i < 2; ++i) {
        receiveFrame(i);
    }
}

bool CANDeviceZLG::sendFrame(CANFrame &frame)
{
    if (frame.fd && mCANFD) {
        return transmitFrameCanFD(&frame);
    } else {
        return transmitFrameCan(&frame);
    }
}

bool CANDeviceZLG::openDevice()
{
    mDeviceHandle = ZCAN_OpenDevice(ZCAN_USBCANFD_200U, mDeviceIndex, 0);
    if (mDeviceHandle == INVALID_DEVICE_HANDLE) {
        mErrorString = "open ZLG device(" + mDeviceName + ") error!";
        return false;
    }

    ZCAN_DEVICE_INFO deviceInfo;
    mSN = "Device " + mDeviceName;
    if (ZCAN_GetDeviceInf(mDeviceHandle, &deviceInfo) == STATUS_OK) {
        mSN = std::string((char*)deviceInfo.str_Serial_Num);
    }

    for (int i = 0; i < 2; ++i) {
        if (!openChannel(i)) {
            for (int j = 0; j < i; ++j) {
                closeChannel(j);
            }
            return false;
        }
    }

    mClosed = false;

    return true;
}

bool CANDeviceZLG::closeDevice()
{
    for (int i = 0; i < 2; ++i) {
        closeChannel(i);
    }

    if (/*mOpened && */ZCAN_CloseDevice(mDeviceHandle) != STATUS_OK) {
        return false;
    }


    mClosed = true;

    return true;
}

int CANDeviceZLG::receiveFrame(int channelIdx)
{
    int len = receiveCan(channelIdx);
    if (mCANFD) {
        len += receiveCanFD(channelIdx);
    }

    return len;
}

int CANDeviceZLG::sendFrame(CANFrame *frame, int len)
{
    for (int i = 0; i < len; ++i) {
        if (!sendFrame(frame[i])) {
            return i;
        }
    }
    return len;
}

bool CANDeviceZLG::openCan(int channel)
{
    mProperty[channel] = GetIProperty(mDeviceHandle);
    char path[50] = { 0 };
    char value[10] = { 0 };
    sprintf_s(path, "%d/baud_rate", channel);
    sprintf_s(value, "%d", mBaudRate);

    if (mProperty[channel]->SetValue(path, value) != 1) {
        mErrorString = std::string("set canfd_abit_baud_rate error! ").append(mDeviceName) + " " +
                std::to_string(mBaudRate);
        return false;
    }

    ZCAN_CHANNEL_INIT_CONFIG initConfig;
    memset(&initConfig, 0, sizeof(initConfig));

    initConfig.can_type = TYPE_CAN;
    initConfig.can.mode = 0;
    initConfig.can.filter = 1;
    initConfig.can.acc_code = 0x00000000;
    initConfig.can.acc_mask = 0xFFFFFFFF;

    mChannelHandle[channel] = ZCAN_InitCAN(mDeviceHandle, channel, &initConfig);
    if (mChannelHandle[channel] == INVALID_DEVICE_HANDLE) {
        mErrorString = std::string("ZLG can init error! ").append(mDeviceName);
        return false;
    }

    ZCAN_ResetCAN(mChannelHandle[channel]);
    if (ZCAN_StartCAN(mChannelHandle[channel]) != STATUS_OK) {
        mErrorString = std::string("ZLG can start error! ").append(mDeviceName);
        return false;
    }

    mErrorString = std::string("ZLG can open success! ").append(mDeviceName);
    return true;
}

bool CANDeviceZLG::openCanFD(int channel)
{
    mProperty[channel] = GetIProperty(mDeviceHandle);

    char path[50] = { 0 };
    char value[50] = { 0 };

    // 正常模式
    sprintf_s(path, "%d/canfd_standard", (int)channel);
    sprintf_s(value, "%d", 0);//
    if (0 == mProperty[channel]->SetValue(path, value)) {
        mErrorString = std::string("set canfd_standard error! ").append(mDeviceName);
        return false;
    }

    // 关闭合并接收功能
    sprintf_s(path, "%d/set_device_recv_merge", (int)channel);
    sprintf_s(value, "%d", 0);
    if (0 == mProperty[channel]->SetValue(path, value)) {
        mErrorString = std::string("set set_device_recv_merge error! ").append(mDeviceName);
        return false;
    }

    // 仲裁域波特率
    sprintf_s(path, "%d/canfd_abit_baud_rate", (int)channel);
    sprintf_s(value, "%d", mBaudRate);//
    if (0 == mProperty[channel]->SetValue(path, value)) {
        mErrorString = std::string("set canfd_abit_baud_rate error! ").append(mDeviceName);
        return false;
    }

    // 数据域波特率
    sprintf_s(path, "%d/canfd_dbit_baud_rate", (int)channel);
    sprintf_s(value, "%d", mDataRate);
    if (0 == mProperty[channel]->SetValue(path, value)) {
        mErrorString = std::string("set canfd_dbit_baud_rate error! ").append(mDeviceName);
        return false;
    }

    //设置发送超时
    sprintf_s(path, "%d/tx_timeout", (int)channel);
    sprintf_s(value, "%d", 50);
    if (0 == mProperty[channel]->SetValue(path, value)) {
        mErrorString = std::string("set tx_timeout error! ").append(mDeviceName);
        return false;
    }

    // 终端电阻使能
    sprintf_s(path, "%d/initenal_resistance", (int)channel);
    sprintf_s(value, "%d", 1);
    if (0 == mProperty[channel]->SetValue(path, value)) {
        mErrorString = std::string("set initenal_resistance error! ").append(mDeviceName);
        return false;
    }

    //初始化通道
    ZCAN_CHANNEL_INIT_CONFIG config;
    memset(&config, 0, sizeof(config));
    config.can_type = TYPE_CANFD;
    config.canfd.mode = 0;
    //初始化通道
    mChannelHandle[channel] = ZCAN_InitCAN(mDeviceHandle, (UINT)channel, &config);
    if (INVALID_DEVICE_HANDLE == mChannelHandle[channel]) {
        mErrorString = std::string("Init CAN FD  Error! ").append(mDeviceName);
        return false;
    }

    //启动CAN
    ZCAN_ResetCAN(mChannelHandle[channel]);
    if (ZCAN_StartCAN(mChannelHandle[channel]) == INVALID_DEVICE_HANDLE) {
        mErrorString = std::string("Start CAN FD  Error! ").append(mDeviceName);
        return false;
    }

    return true;
}

bool CANDeviceZLG::openChannel(int channel)
{
    if (mCANFD) {
        return openCanFD(channel);
    }
    else {
        return openCan(channel);
    }
}

bool CANDeviceZLG::closeChannel(int channel)
{
    ZCAN_ResetCAN(mChannelHandle[channel]);
    if (isOpened() && mProperty[channel] && ReleaseIProperty(mProperty[channel]) != STATUS_OK) {
        return  false;
    }

    return true;
}

int CANDeviceZLG::receiveCan(int channel)
{
    UINT len = 0;
    //接收
    if ((len = ZCAN_GetReceiveNum(mChannelHandle[channel], TYPE_CAN)) > 0) {
        len = ZCAN_Receive(mChannelHandle[channel], mReceiveData,
                           CAN_DEVICE_MAX_RECEIVE_FRAME, CAN_DEVICE_WITE_TIME);
            for (UINT i = 0; i < len; ++i) {
                mSafeQueue->push(CANFrame(channel,
                                   mReceiveData[i].frame.can_id,
                                   mReceiveData[i].frame.data,
                                   mReceiveData[i].frame.can_dlc,
                                   false,
                                   true));
            }
    }

    return len;
}

int CANDeviceZLG::receiveCanFD(int channel)
{
    UINT len = 0;
    len = ZCAN_GetReceiveNum(mChannelHandle[channel], TYPE_CANFD);
    if (len > 0) {
        len = ZCAN_ReceiveFD(mChannelHandle[channel], mReceiveDataFD,
                             CAN_DEVICE_MAX_RECEIVE_FRAME, CAN_DEVICE_WITE_TIME);
        if (mSafeQueue) {
            for (UINT i = 0; i < len; i++) {
                mSafeQueue->push(CANFrame(channel,
                                   mReceiveDataFD[i].frame.can_id,
                                   mReceiveDataFD[i].frame.data,
                                   mReceiveDataFD[i].frame.len,
                                   true,
                                   true));
            }
        }
    }

    return len;
}

bool CANDeviceZLG::transmitFrameCan(CANFrame *frame)
{
    ZCAN_Transmit_Data frameinfo;

    frameinfo.frame.can_id = MAKE_CAN_ID(frame->id, 0, 0, 0);
    frameinfo.frame.can_dlc = frame->len;
    frameinfo.transmit_type = 0;
    memcpy(frameinfo.frame.data, frame->data, frame->len);

    if (ZCAN_Transmit(mChannelHandle[frame->channel], &frameinfo, 1) <= 0) {
        mErrorString = std::string("ZLG can send error! ") + mDeviceName + " " + std::to_string(frame->channel) + " " + std::to_string(frame->id);
        return false;
    }

    mSafeQueue->push(*frame);
    return true;
}

bool CANDeviceZLG::transmitFrameCanFD(CANFrame *frame)
{
    ZCAN_TransmitFD_Data frameinfo;
    frameinfo.frame.can_id = MAKE_CAN_ID(frame->id, 0, 0, 0);
    frameinfo.frame.len = frame->len;
    frameinfo.frame.flags = 0;
    frameinfo.transmit_type = 0;
    frameinfo.frame.flags |= CANFD_BRS; // CANFD 加速
    memcpy(frameinfo.frame.data, frame->data, frame->len);

    if (ZCAN_TransmitFD(mChannelHandle[frame->channel], &frameinfo, 1) <= 0) {
        mErrorString = std::string("ZLG canfd send error! ") + mDeviceName  + " " + std::to_string(frame->channel) + " " + std::to_string(frame->id);
        return false;
    }
    mSafeQueue->push(*frame);

    return true;
}
