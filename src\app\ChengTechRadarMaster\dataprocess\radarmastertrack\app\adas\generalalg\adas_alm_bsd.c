﻿/**
 * @file adas_alm_bsd.c
 * @brief
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#include <stdbool.h>
#include <math.h>
#include <string.h>
#ifdef ALPSPRO_ADAS
#include "arc_timer.h"
//#include "sys/system_mng.h"
#include "rdp/track/data_process/rdp_clth_radar_lib.h" 
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#include "embARC_debug.h"
#elif defined (PC_DBG_FW)
#include "app/system_mgr/typedefs.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h" 
#include "alg/track/rdp_clth_radar_lib.h"
#include "app/vehicle/vdy/vdy_types.h"
#include <time.h>
#include <stdio.h>
#else
#include "app/sys/system_mng.h"
#include "app/rdp/rdp_clth_radar_lib.h"
#include "app/adas/common/ring_queue_struct_buff.h"
#include "app/adas/common/moving_average_filter.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "common/include/vehicle_cfg.h"
#endif

/**
 * @brief 
 * 
 */
struct __road_line_buff_t
{
    float roadline_buf0;
    float roadline_buf1;
};
typedef struct __road_line_buff_t road_line_buff_t;

/**
 * @brief 
 * 
 */
struct __turn_buf_t
{
    float turn_x_buf;
    float turn_y_buf;
    bool stopAlarm; 
};
typedef struct __turn_buf_t turn_buf_t;

/**
 * @brief 
 * 
 */
struct __alarm_param_buff_t
{
    float spd_buf;
    float range_buf;
    float range_front_buf;
    float x_near_buf;
    float x_far_buf;
};
typedef struct __alarm_param_buff_t alarm_param_buff_t;

struct __range_flag_t
{
    uint8_t range_x_flag : 2; // 1，目标x在范围内为1，不在范围内为0
    uint8_t range_y_flag : 2; // 1，目标y在范围内为1，不在范围内为0
    uint8_t reserved0    : 6; // 保留
}; 
typedef struct __range_flag_t range_flag_t;


/**
 * @brief 求数组元素个数
 * 
 */
#ifndef dimof
    #define dimof(__array)     (sizeof(__array)/sizeof(__array[0]))
#endif

static uint8_t init_flag = 0;

/**
 * @brief BSD功能模块的初始化
 * 
 */
static void ADAS_BSD_Init(void)
{
    init_flag = 1; 
}

/**
 * @brief 计算BSD相对速度门限，对已报警的目标扩大buff
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return BSD_ReletiveSpeedThr 相对速度门限
 */
static float ADAS_BSD_ReletiveSpeedThreshold(const ALARM_OBJECT_T *pobjAlm, const OBJ_NODE_STRUCT *pobjPath, const uint8_t i)
{
    float BSD_ReletiveSpeedThr = BSD_RELATIVE_SPEED_MIN;

    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD) != false)
    {
        BSD_ReletiveSpeedThr += BSD_RELATIVE_SPEED_MIN_BUF; // 临时系数，防止目标航迹框长度介于两者之间，导致报警阈值跳跃
    }

    return BSD_ReletiveSpeedThr; 
}
 
/**
 * @brief 根据不同速度配置目标车和本车的边线buff，速度越快本车离边线越远，目标车子开的越快，车子要离开边线越大，防止碰边
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return roadline_buf roadline_buf0本车边线buffer，roadline_buf1目标边线buffer
 */
static road_line_buff_t ADAS_BSD_RoadLineJudge(const ALARM_OBJECT_T *pobjAlm, const OBJ_NODE_STRUCT *pobjPath, const uint8_t i)
{
    road_line_buff_t roadline_buf = {.roadline_buf0 = 0, .roadline_buf1 = 0}; 
    float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;

    /*策略一：边线判断条件*/
    /*本车边线buffer，速度越快本车离边线越远,车开的越快，车子离开边线距离越大，防止碰边*/
    if (fabsf(BSDVelSpeedVal) >= 35) /*35，+*/
    {
        roadline_buf.roadline_buf0 = BSD_CARSPEED_ROADLINEBUF_HIGH;
    }
    else if (fabsf(BSDVelSpeedVal) >= 20) /*20，35*/
    {
        roadline_buf.roadline_buf0 = BSD_CARSPEED_ROADLINEBUF_MID;
    }
    else /*0，20*/
    {
        roadline_buf.roadline_buf0 = BSD_CARSPEED_ROADLINEBUF_LOW;
    }

    /*目标边线buffer，速度越快本车离边线越远，目标车子开的越快，车子要离开边线越大，防止碰边*/
    if (fabsf((float)(pobjPath[i].vy * 3.6)) >= 30) /*30，+*/
    {
        roadline_buf.roadline_buf1 = BSD_OBJSPEED_ROADLINEBUF_HIGH;
    }
    if (fabsf((float)(pobjPath[i].vy * 3.6)) >= 15) /*15，30*/
    {
        roadline_buf.roadline_buf1 = BSD_OBJSPEED_ROADLINEBUF_MID;
    }
    else /*0，15*/
    {
        roadline_buf.roadline_buf1 = BSD_OBJSPEED_ROADLINEBUF_LOW;
    } 

    return roadline_buf;
}
 
/**
 * @brief 转弯半径阈值
 * 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return radiusThr
 */
static float ADAS_BSD_RadiusThreshold(const OBJ_NODE_STRUCT *pobjPath, const uint8_t i)
{
    float radiusThr = BSD_TURNING_RADIUS_MIN;

    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD) != false)    //上次报警的目标增加缓冲    
    {
        radiusThr -= BSD_TURNING_RADIUS_MIN_BUF;
    }

    return radiusThr;
}
 
/**
 * @brief 策略二：转弯收缩报警区域
 *      转弯处理，上次没有报警，且现在正在转弯
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param radiusThr 转弯半径阈值
 * @return turn_buf_t
 */
static turn_buf_t ADAS_BSD_TurningProcess(const ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath,
                                          const uint8_t i,
                                          const float radiusThr)
{
    turn_buf_t turnBuf = {.stopAlarm = 0, .turn_x_buf = 0, .turn_y_buf = 0};
    float tmpAadiusAbs = fabsf(pobjAlm->centerx);
    TVehicleInfo *pVehicleInfo = pobjAlm->pVehicleInfo;

    if ((ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD) == false) &&
        (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA) == false) &&
        ((pVehicleInfo->turn_state.bit.is_turning == 1U) || (pVehicleInfo->after_turn_timer > 0U)) //
    )
    {
        if (pVehicleInfo->turn_state.bit.is_turning == 1U)
        {
            /*正在转向阶段，补偿x和y方向距离,缩小报警区域*/
            turnBuf.turn_x_buf = BSD_TURNING_X_BUF;
            turnBuf.turn_y_buf = BSD_TURNING_Y_BUF;
        }
        else if (pVehicleInfo->after_turn_timer > 0U)
        {
            /*转向延时阶段，补偿x和y方向距离，缩小报警区域*/
            turnBuf.turn_x_buf = BSD_TURNDELAY_X_BUF;
            turnBuf.turn_y_buf = BSD_TURNDELAY_Y_BUF;
        }
        else
        {
            ;   //donothing
        }

        if (tmpAadiusAbs < radiusThr)
        {
            //不报警，转弯半径太小
            turnBuf.stopAlarm = true;
            turnBuf.turn_x_buf = 0.0;
            turnBuf.turn_y_buf = 0.0;
        }
        else if (tmpAadiusAbs < 100) /*radiusThr,100*/
        {
            //转弯比较急，缩小y报警区域，缩小x报警区域，转弯越急，缩小越多
            turnBuf.turn_x_buf = turnBuf.turn_x_buf * (1.0 - ((tmpAadiusAbs - radiusThr) / (100 - radiusThr))); // 这里乘出来之后turn_x_buf比上面小
            turnBuf.turn_y_buf = turnBuf.turn_y_buf * (1.0 - ((tmpAadiusAbs - radiusThr) / (100 - radiusThr)));
        } /*100，200*/
        else
        {
            ;   //donothing
        }
    }

    if ((ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD) != false) || 
        (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA) != false)
    )
    {
        turnBuf.turn_x_buf = -BSD_TURNING_X_BUF;    //已报警的目标，转弯横向区域再扩大
    }

    return turnBuf;
}

/**
 * @brief 策略四：上次达到报警条件，之后都扩大报警区域
 *      如果之前报警过，且该点为触发报警的目标，buffer加大，减少报警中断
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return alarm_param_buff_t
 */
static alarm_param_buff_t ADAS_BSD_ExpandAlarmBuff(const ALARM_OBJECT_T *pobjAlm, const OBJ_NODE_STRUCT *pobjPath, const uint8_t i)
{
    alarm_param_buff_t alarm_param_buff = {.spd_buf = 0, .range_buf = 0, .x_near_buf = 0, .x_far_buf = 0};

    alarm_param_buff.spd_buf = BSD_ACTIVE_MIN_SPEED_BUF;
    alarm_param_buff.x_near_buf = BSD_NEAR_ALARM_WIDTH_SIZE_BUF;
    alarm_param_buff.x_far_buf = BSD_FAR_ALARM_WIDTH_SIZE_BUF;
    alarm_param_buff.range_buf = BSD_LENGTH_BUF;
    alarm_param_buff.range_front_buf = BSD_LENGTH_FRONT_BUF;

    return alarm_param_buff;
}

/**
 * @brief 策略八：边线外鬼点抑制报警  
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 */
static void ADAS_BSD_OutsideLineSuppression(const ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, const uint8_t i)
{
    float tmpRoadLine = pobjAlm->RoadLine;

    // 边线外加
    if ((tmpRoadLine > MIN_SIDE_VALUE) && (pobjPath[i].x > tmpRoadLine))
    {
        if (pobjPath[i].outRoadLineCnt < 0xffU)
        {
            pobjPath[i].outRoadLineCnt++;
        }
    }
    // 边线内减
    if ((tmpRoadLine <= MIN_SIDE_VALUE) || (pobjPath[i].x < tmpRoadLine))
    {
        if (pobjPath[i].outRoadLineCnt > 0)
        {
            pobjPath[i].outRoadLineCnt--;
        }
    }

    if (pobjPath[i].outRoadLineCnt >= BSD_OUT_ROAD_LINE_TARGET_SUPPRESS_CNT_TRESHOLD)   //边线外累计到一定程度，则推迟报警
    {
        pobjPath[i].startBsdAlarmDly = (0 - (char)BSD_OUT_ROAD_LINE_TARGET_SUPPRESS_ALARM_DELAY);
    }
}

/**
 * @brief 策略：目标长度length小于1m的认为是异常点
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址 
 * @param i 
 * @param pStopFlag
 */
// static void ADAS_BSD_AbnormalLengthFilter(const ALARM_OBJECT_T *pobjAlm,
//                                           const OBJ_NODE_STRUCT *pobjPath,
//                                           const uint8_t i,
//                                           bool *pStopFlag)
// {
//     (void)pobjAlm;
//     if (pobjPath[i].boxLength < 1.0f) // 所有length小于1的都不报警
//     {
//         *pStopFlag = true;
//     } 
// }

/**
 * @brief 策略
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址 
 * @param i 
 * @return true 假点
 * @return false 非假点
 */
static bool ADAS_BSD_isFakeJudgment(const ALARM_OBJECT_T *pobjAlm,
                                    OBJ_NODE_STRUCT *pobjPath,
                                    const uint8_t i)
{
    (void)pobjAlm;
    bool fakeDOt = false; // false 非假点

    if (pobjPath[i].startCandiY > 0.5) // 非超车  起批晚的目标可能startY会稍微大一些, Y给一些冗余
    {
        /**
         * @brief BSD报警条件: 未报警的目标，非超车且（当前 Y > 3或起航和报警帧的绝对位移大于 3 m
         *
         */
        if (pobjPath[i].objFakeDOt)
        {
            fakeDOt = true;
        }
    }
    else // if (pobjPath[i].startY <= 0) // 方便QT调试
    {
        /**
         * @brief 二、对于-0.5<startY<0的点在-0.5<Y<0的情况进行累计记数达到600帧及以上，可能是假点，表示出来，
         *      但Y到达1m后取消标识，有该表示的跟踪点不报警
         *   0，非假点，1 假点
         *
         */
        if (pobjPath[i].pointTrackingFrameCount < ADAS_POTENTIA_FALSE_POINT_TRACKING_FRAME_CNT_MAX)
        {
            if ((pobjPath[i].startCandiY > ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_STARTY_MIN) &&                                                              //
                ((pobjPath[i].y < ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_MAXY_MAX) && (pobjPath[i].y > ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_MAXY_MIN)) //
            )
            {
                pobjPath[i].pointTrackingFrameCount++; 
            }
        }
        else // if (pobjPath[i].pointTrackingFrameCount >= ADAS_POTENTIA_FALSE_POINT_TRACKING_FRAME_CNT_MAX) // 
        {
            if (pobjPath[i].y < 1)
            {
                fakeDOt = true; 
            }
        }
        
    }

    /**
     * @brief 目标新增径向速度为零检测点的连续关联帧数：vr0_hit，应用于bsd报警场景的假目标判定。推荐假目标判据：
     * 1. vr0_hit ≥ 40 或 vr0_hit ≥ 生命周期；
     * 2. length < 2
     *
     */
    if (((pobjPath[i].TrkObjVr0_hit >= 400) || (pobjPath[i].TrkObjVr0_hit >= 4 * pobjPath[i].lifeCycle)) 
    )
    {
        fakeDOt = true;
    }

    return fakeDOt;
}

/**
 * @brief 目标x、y方向距离是否在范围内
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param alarm_param_buff 报警相关参数缓冲
 * @param turn_buf 转弯时x、y方向距离补偿
 * @return range_flag_t
 */
static range_flag_t ADAS_BSD_ObjRangeCompliance(const ALARM_OBJECT_T *pobjAlm, const OBJ_NODE_STRUCT *pobjPath,
                                                const uint8_t i,
                                                alarm_param_buff_t alarm_param_buff,
                                                const turn_buf_t turn_buf)
{
    range_flag_t tmpRangeFlag = {.range_x_flag = 0U, .range_y_flag = 0U, .reserved0 = 0U};
    float tmpAadiusAbs = fabsf(pobjAlm->centerx); // 本车的转弯半径

    bool cncapflag = false;
    // cncap24法规场景车对车切入 汽车的横向放的更宽.
    if ((1 == pobjPath[i].cncapBsdcarCheckIn) &&
         ((ADAS_BSD_CHECKIN == pobjPath[i].bsd_checkinoutflag) && (pobjPath[i].x <= (BSD_FAR_ALARM_WIDTH_SIZE + 0.6)) && (pobjPath[i].boxDot1X <= BSD_FAR_ALARM_WIDTH_SIZE + 0.3)) &&
         (tmpAadiusAbs > 350))
    {
        cncapflag = true;
    }
    // cncap24法规场景摩托车切入场景  可能会导致路测误报
    if ((1 == pobjPath[i].cncapBsdmotoCheckIn) &&
         ((ADAS_BSD_CHECKIN == pobjPath[i].bsd_checkinoutflag) && (pobjPath[i].x <= (BSD_FAR_ALARM_WIDTH_SIZE + 0.6)) && (pobjPath[i].boxDot1X <= BSD_FAR_ALARM_WIDTH_SIZE + 0.35)) &&
         (tmpAadiusAbs > 350))
    {
        cncapflag = true;
    }

	// 异常跟踪点过滤
    // if (fabsf(pobjPath[i].y) < 1.2)
    // {
    //     if (tmpAadiusAbs <= 50) //转弯半径较小时，扩大横向区域外边界；转弯半径越小，区域扩得越大
    //     {
    //         alarm_param_buff.x_far_buf += BSD_ABNORMAL_TURN_XBUF_HIGH;
    //     }
    //     else if (tmpAadiusAbs <= 100)
    //     {
    //         alarm_param_buff.x_far_buf += BSD_ABNORMAL_TURN_XBUF_MID;
    //     }
    //     else if (tmpAadiusAbs <= 200)
    //     {
    //         alarm_param_buff.x_far_buf += BSD_ABNORMAL_TURN_XBUF_LOW;
    //     }
    //     else
    //     {
    //         ;   //donothing
    //     }
    // }

    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD) != false)
    {
        /*
            D1:Dot1,距离原点（雷达）最近的点，这个在4个雷达中都是如此

            D3  D4  |   |   D4  D3
            +---+   |   |   +---+
            |obj|   |   |   |obj|
            +---+   |   |   +---+
            D2  D1  |   |   D1  D2
            --6-----+---+-----7---
                    | V |
            --4-----+---+-----5---
            D2  D1  |   |   D1  D2
            +---+   |   |   +---+
            |obj|   |   |   |obj|
            +---+   |   |   +---+
            D3  D4          D4  D3
        */
        // 航迹框中距离雷达最近的顶点认为是Dot1点
        if (((pobjPath[i].boxDot1X <= BSD_FAR_ALARM_WIDTH_SIZE + BSD_REDUCED_WIDTH_MARGIN + alarm_param_buff.x_far_buf) && //
             (pobjPath[i].boxDot1X > (BSD_NEAR_ALARM_WIDTH_SIZE + alarm_param_buff.x_near_buf))) ||                        // 航迹框不完全位于F线左侧
            ((pobjPath[i].x <= BSD_FAR_ALARM_WIDTH_SIZE + alarm_param_buff.x_far_buf) &&                                   //
             (pobjPath[i].x > (BSD_NEAR_ALARM_WIDTH_SIZE + alarm_param_buff.x_near_buf)))                                  // 跟踪点不完全位于F线左侧
        )
        {
            tmpRangeFlag.range_x_flag = 1;
        }
        else
        {
            tmpRangeFlag.range_x_flag = 0U;
        }
    }
    else
    {
        // 第一次报警需要用聚类的点的x 或者聚类点和顶点结合的方式(且目标车处于切入状态,主要用以应对cncap24)
        if (((pobjPath[i].x <= BSD_FAR_ALARM_WIDTH_SIZE) || cncapflag) && (pobjPath[i].x > BSD_NEAR_ALARM_WIDTH_SIZE))
        {
            tmpRangeFlag.range_x_flag = 1;
        }
        else
        {
            tmpRangeFlag.range_x_flag = 0U;
        }
    }

    if ((pobjPath[i].y <= (BSD_LENGTH + alarm_param_buff.range_buf + turn_buf.turn_y_buf)) &&   /*y轴区域判断*/
        (pobjPath[i].y >= (BSD_LENGTH_FRONT + alarm_param_buff.range_front_buf))    /*往前探测范围*/
        )
    {
        tmpRangeFlag.range_y_flag = 1;
    }
    else
    {
        tmpRangeFlag.range_y_flag = 0U;
    }

    /**
     * @brief 上面x不满足时，这里在转弯时可能满足
     * 
     */
    if (((pobjAlm->centerx < 0) && ((pobjPath[i].rcDis - tmpAadiusAbs) <= BSD_FAR_ALARM_WIDTH_SIZE + alarm_param_buff.x_far_buf) && ((pobjPath[i].rcDis - tmpAadiusAbs) > (BSD_NEAR_ALARM_WIDTH_SIZE + turn_buf.turn_x_buf))) ||
        ((pobjAlm->centerx > 0) && ((tmpAadiusAbs - pobjPath[i].rcDis) <= BSD_FAR_ALARM_WIDTH_SIZE + alarm_param_buff.x_far_buf) && ((tmpAadiusAbs - pobjPath[i].rcDis) > (BSD_NEAR_ALARM_WIDTH_SIZE + turn_buf.turn_x_buf))) //
    )
    {
        tmpRangeFlag.range_x_flag = 1U;
    }

    /**
     * @brief 产生过明显X位置关系不满足, 但是直接触发BSD的目标. 对X进行强制限制
     * 相对直行的场景, 对X的最大值进行限制 
     */
    if ((false == ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD)) && ((fabsf(pobjAlm->centerx) > 300)) && (pobjPath[i].x > (BSD_FAR_ALARM_WIDTH_SIZE + 1.0f)))
    {
        tmpRangeFlag.range_x_flag = 0;
    }

    return tmpRangeFlag;
}

/**
 * @brief 方向为相向而行，为1，同向而行为0，不过不绝对，比如本车超目标车，类似于相向而行
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param tmpRangeFlag 目标距离是否在范围内的标志
 * @param pStopFlag 抑制标志
 */
static void ADAS_BSD_isOncomingVehicle(const ALARM_OBJECT_T *pobjAlm,
									   OBJ_NODE_STRUCT *pobjPath,
                                       const uint8_t i,
                                       const range_flag_t tmpRangeFlag,
                                       bool *pStopFlag)
{
    uint8_t isOncomingVehicle = 0U;
    float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal; 

    if (tmpRangeFlag.range_x_flag == 1U) // x满足的点，记录才有意义
    {
        if ((pobjPath[i].y >= BSD_SUPPRESS_REVERSE_RANGE_Y_MIN) && (pobjPath[i].y <= BSD_SUPPRESS_REVERSE_RANGE_Y_MAX))
        {
            pobjPath[i].isRearVehicleMarked = 1; // 后方过来的车辆都标记一下
        }
    }

    if ((tmpRangeFlag.range_y_flag == 1U) && (tmpRangeFlag.range_x_flag == 1U)) // 加这两个是为了方便调试，不在报警范围内的点可以不用在意行驶方向
    {
        if (BSDVelSpeedVal <= 30) // 本车行驶速度
        {
            isOncomingVehicle = pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_REVERSE_BMP;
            // 对向行驶&&非后方来车
            if ((isOncomingVehicle != 0U) && (pobjPath[i].isRearVehicleMarked == 0U))
            {
                //*pStopFlag = true;
            }
        }
        else // if (BSDVelSpeedVal > 30) // 本车行驶速度
        {
            // 对向行驶,高速时可以完全相信对向标志，同向误识别为对向主要是低速时
            if (isOncomingVehicle != 0U)
            {
                //*pStopFlag = true;
            }
        }
    }
}

/**
 * @brief 策略七：隔车道超车报警抑制
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param pStopFlag 抑制标志
 */
static void ADAS_BSD_suppressAdjacentLaneAlarms(const ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, const uint8_t i, bool *pStopFlag)
{
//    float BSDVelSpeedVal = 0.0f;
//    BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;

    if ((pobjPath[i].startCandiX <= BSD_OVERTAKE_SUPPRESS_START_X_THRESHOLD) &&
        (pobjPath[i].startCandiY <= BSD_OVERTAKE_SUPPRESS_START_Y_THRESHOLD))
    {
        if ((pobjPath[i].lifeCycle < BSD_OVERTAKE_SUPPRESS_LIFE_CYCLE_THRESHOLD) && (pobjPath[i].isRearVehicleMarked == 0U))
        {
            if (pobjPath[i].overtake_cnt == 0)
            {
                pobjPath[i].overtake_cnt++;
            }
        }

        // 超车抑制帧数统一改小
        if ((pobjPath[i].overtake_cnt > 0U) && (pobjPath[i].overtake_cnt <= (BSD_OVERTAKE_SUPPRESS_ALARM_DELAY_MIN)))
        {
            *pStopFlag = true;
            pobjPath[i].overtake_cnt++;
        }

        // // 超车抑制帧数随车速调整
        // if ((BSDVelSpeedVal > 30) && (pobjPath[i].overtake_cnt > 0U))
        // {
        //     if (pobjPath[i].overtake_cnt <= (BSD_OVERTAKE_SUPPRESS_ALARM_DELAY_MIN))
        //     {
        //         *pStopFlag = true;
        //         pobjPath[i].overtake_cnt++;
        //     }
        // }
        // else if ((pobjPath[i].overtake_cnt > 0U) && (pobjPath[i].overtake_cnt <= (BSD_OVERTAKE_SUPPRESS_ALARM_DELAY)))
        // {
        //     *pStopFlag = true;
        //     pobjPath[i].overtake_cnt++;
        // }
    }

    // A3功能规范要求目标最小速度大于2米每秒
#ifdef  ADAS_FUNCTION_A3
    if ((pobjPath[i].absolutevabs < BSD_TARGET_ABS_SPEED_MIN) && (false == ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD)))
    {
        *pStopFlag = true;
    }
#endif
}
 
/**
 * @brief 策略六：进入报警
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param tmpObjPath 保存已报警的数据
 * @return tmpAlarmFlag
 */
static bool ADAS_BSD_setAlarmManager(const ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, const uint8_t i)
{
    bool  tmpAlarmFlag = false;
    //float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;

    if (pobjPath[i].startBsdAlarmDly >= BSD_START_ALARM_DELAY)
    {
        /*多帧都报警*/
        tmpAlarmFlag = true;

        ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_BSD);
    }
    else
    {
        pobjPath[i].startBsdAlarmDly++;
    }

    return tmpAlarmFlag;
}

/**
 * @brief 抑制各类假点
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param tmpRangeFlag 
 */
static void ADAS_BSD_suppressFalsePositivesWithoutHistory(const ALARM_OBJECT_T *pobjAlm,
                                                          OBJ_NODE_STRUCT *pobjPath,
                                                          const uint8_t i,
                                                          const range_flag_t tmpRangeFlag,
                                                          bool *pStopFlag)
{
    (void)pobjAlm;
#ifndef PC_DBG_FW
    if (tmpRangeFlag.range_y_flag) // x满足的点，记录才有意义
#else 
    if (tmpRangeFlag.range_y_flag && tmpRangeFlag.range_x_flag) // x满足的点，记录才有意义 QT仿真用这里 
#endif
    {
        if (pobjPath[i].attrFlag & TRACK_STATUS_LOCATION_UNCHANGED_BMP) // 表示此点几乎没有动过，认为是假点
        {
            *pStopFlag = true;
        } 
    }

    if((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) == 0U)    //过滤静态点
    {
        *pStopFlag = true;
    }

    /**
     * @brief 置信度判断
     *
     */
    if ((pobjPath[i].TrkObjReliability < 75) && (pobjPath[i].TrkObjMissCnt > 10))   // 置信度
    {
        *pStopFlag = true;
    }

#if(BUMPER_FALSE_POINT_STRATEGY_ENABLED == 1)    //保杠假点过滤
    if (ADAS_BSD_isFakeJudgment(pobjAlm, pobjPath, i))
    {
        *pStopFlag = true;
    }
#endif
}

/**
 * @brief 检查目标在X方向上是否整体在靠近
 * 
 * @param pobjPath 目标相关结构体地址
 * @param size 所统计的帧数量
 * @return uint8_t 1：在靠近 0：不在靠近
 */
static uint8_t ADAS_BSD_checkTargetIsApproachingOnX(OBJ_NODE_STRUCT *pobjPath, uint8_t i, uint8_t size)
{
    uint8_t k = 0U, num = 0U, outnum = 0, ydiffnum = 0;
    uint8_t flag = 0;
    for (k = 0U; k < (size-1U); k++){
        if (pobjPath[i].stored_last_x[k] <= pobjPath[i].stored_last_x[k+1])
        {
            num++;
        }
    }
    for (k = 0U; k < (size-1U); k++){
        if (pobjPath[i].stored_last_x[k] >= pobjPath[i].stored_last_x[k+1])
        {
            outnum++;
        }
    }
    for (k = 0U; k < (size-1U); k++){
        if (pobjPath[i].stored_last_y[k] < pobjPath[i].stored_last_y[k+1])
        {
            ydiffnum++;
        }
    }

    // 符合递减趋势且最大X已更新  场景需是从隔车道切入到临车道.
    if ((num >= (12)) && ((pobjPath[i].stored_last_x[size-1] - pobjPath[i].stored_last_x[0]) >= (BSD_CHECKINOUT_MAXDIFF)) && (pobjPath[i].maxbsdx > (BSD_CHECKIN_MAX_X)) &&
        ((false == ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD))))
    {
        pobjPath[i].bsd_checkinoutflag = ADAS_BSD_CHECKIN;
    }

    // 符合递减趋势且已经切入过  已经切入过且最近距离达到了一定值才触发切出标志。
    if ((outnum >= (12)) && ((pobjPath[i].stored_last_x[0] - pobjPath[i].stored_last_x[size-1]) >= (BSD_CHECKINOUT_MAXDIFF)) && (pobjPath[i].minbsdx < (BSD_CHECKOUT_MIN_X)) &&
        ((ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD))) && (pobjPath[i].maxbsdx > (BSD_CHECKIN_MAX_X)))
    {
        pobjPath[i].bsd_checkinoutflag = ADAS_BSD_CHECKNOUT;
    }        

    // 非横切测试
    if ((ydiffnum >= 12) && (pobjPath[i].stored_last_y[size - 1] - pobjPath[i].stored_last_y[0] > 2) && 
        ((pobjPath[i].x > 0.8) && (pobjPath[i].x < 2.9)) && (fabsf(pobjPath[i].stored_last_x[0] - pobjPath[i].stored_last_x[size-1])) < 1.2f)
    {
        pobjPath[i].bsd_checkinoutflag = ADAS_BSD_CHECKNULL;
    }

    return flag;
}


/**
 * @brief  BSD 检测切入切出
 * @param pobjAlm 
 * @param pobjPath 
 * @param i 
 * @param pVDY 
 * @param timeClase_t 
 */
static void ADAS_BSD_CHECKINOUT(OBJ_NODE_STRUCT *pobjPath, uint8_t i, ALARM_OBJECT_T *pobjAlm)
{
    if ((1 == pobjPath[i].cncapBsdcarCheckIn) || (1 == pobjPath[i].cncapBsdmotoCheckIn))
    {
        ADAS_BSD_checkTargetIsApproachingOnX(pobjPath, i, ADAS_HISTORY_NUM);
    }
}

/**
 * @brief BSD报警策略主函数
 * 
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param pVDY VDY所有信息
 * @param timeClase_t 时间变量
 * @return alarmFlag
 */
bool ADAS_BSD_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath,
                             uint8_t i, const VDY_Info_t *pVDY,
                             const ADAS_TimeClase_t timeClase_t)
{
    //float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
    
    float RoadLine = pobjAlm->RoadLine;

    alarm_param_buff_t alarm_param_buff = {.spd_buf = 0, .range_buf = 0, .range_front_buf = 0, .x_near_buf = 0, .x_far_buf = 0};
    turn_buf_t turn_buf = {.turn_x_buf = 0, .turn_y_buf = 0};
    road_line_buff_t roadline_buf = {.roadline_buf0 = 0, .roadline_buf1 = 0};
    float radiusThr = 0.0f;
    float radiusAbs = fabsf(pVDY->pVDY_DynamicInfo->vdyCurveRadius);
    bool  stopAlarm = false;
    bool  alarmFlag = false;
    float BSD_ReletiveSpeedThr = 0.0f;
    range_flag_t tmpRangeFlag = {.range_x_flag = 0U, .range_y_flag = 0U, .reserved0 = 0U};

    float radius_threshold = 0.0f;     // 转弯半径阈值

#if (1 == BSD_CURVLIMIT_EN)
    radius_threshold = LCA_TURNING_RADIUS_MIN;
    if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD))  //上次达到报警条件，之后都扩大报警区域
    {
        radius_threshold += LCA_TURNING_RADIUS_MIN_BUF;
    }
#endif

	/* BSD 功能模块的初始化 */
    if (init_flag == 0U)
    {
        ADAS_BSD_Init();
    }

	/* 策略: 计算BSD相对速度门限 */
    BSD_ReletiveSpeedThr = ADAS_BSD_ReletiveSpeedThreshold(pobjAlm, pobjPath, i); 

	/* 策略: 上一帧有报警，spd_buf速度buff为10km/h，否则为0  */
    alarm_param_buff.spd_buf = (pobjPath[i].lastAlarmType == ALARM_ACTIVE_BSD) ? 10.0 : 0.0;

	/* 策略: 之前没报警，大于-4即可，之前有报警，扩大buff至-20m/s */
    if (pobjPath[i].vy > BSD_ReletiveSpeedThr) 
    {
		/* 策略四：上次达到报警条件，之后都扩大报警区域  */
        if ((pobjPath[i].lastAlarmType == ALARM_ACTIVE_BSD) || (pobjPath[i].lastAlarmType == ALARM_ACTIVE_LCA))
        {
            alarm_param_buff = ADAS_BSD_ExpandAlarmBuff(pobjAlm, pobjPath, i);
        }

        /*目标边线buffer，速度越快本车离边线越远，目标车子开的越快，车子要离开边线越大，防止碰边*/
        roadline_buf = ADAS_BSD_RoadLineJudge(pobjAlm, pobjPath, i); 

        /*计算转弯半径阈值*/
        radiusThr = ADAS_BSD_RadiusThreshold(pobjPath, i); 
        ADAS_BSD_CHECKINOUT(pobjPath, i, pobjAlm);

        /* 策略二：转弯收缩报警区域 */
        turn_buf = ADAS_BSD_TurningProcess(pobjAlm, pobjPath, i, radiusThr); 
        stopAlarm = turn_buf.stopAlarm; 

        /* 策略：目标长度length小于1m的认为是异常点 */ 
        // ADAS_BSD_AbnormalLengthFilter(pobjAlm, pobjPath, i, &stopAlarm);

        /* 目标x、y方向距离是否在范围内 */
        tmpRangeFlag = ADAS_BSD_ObjRangeCompliance(pobjAlm, pobjPath, i, alarm_param_buff, turn_buf); 

        /* 假点抑制*/
        ADAS_BSD_suppressFalsePositivesWithoutHistory(pobjAlm, pobjPath, i, tmpRangeFlag, &stopAlarm);

        /* 抑制对向行驶目标 */
        ADAS_BSD_isOncomingVehicle(pobjAlm, pobjPath, i, tmpRangeFlag, &stopAlarm); 

        /* 超车抑制*/
        ADAS_BSD_suppressAdjacentLaneAlarms(pobjAlm, pobjPath, i, &stopAlarm); 

        /*左雷达和右雷达转弯的计算坐标中心点不一样，左转的时候，转弯中心点在左雷达x正方向，在右雷x的负方向，即 centerx 的值*/
        if ((stopAlarm == false) && 
            (fabsf(radiusAbs) > radius_threshold) &&                                                                                                   /*是否暂停报警*/
            (((pobjPath[i].lastAlarmType == ALARM_ACTIVE_BSD) != false) ||                                                                         //
             (pobjPath[i].vyByCarSpeed > (BSD_TARGET_SPEED_MIN - alarm_param_buff.spd_buf))) &&                                                    /*目标速度大于2.0m/s*/
            ((tmpRangeFlag.range_y_flag != 0U) && (tmpRangeFlag.range_x_flag != 0U)) &&                                                            /* Y满足范围 X满足范围 */
            ((RoadLine < MIN_SIDE_VALUE) || ((RoadLine > (MIN_ROAD_SIDE_DISTANCE + roadline_buf.roadline_buf0)) && (pobjPath[i].x < (RoadLine - roadline_buf.roadline_buf1)))) && //边线策略
            (radiusAbs > radiusThr)                                                                                                                /*转弯半径判断*/
        )
        {
            if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA) != false)
            {
                // LCA转BSD不延时
                pobjPath[i].startBsdAlarmDly = BSD_START_ALARM_DELAY; 
                ADAS_setBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD);
                ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA);
                pobjPath[i].alarmPostFlag = 0U; // 20230905_Will+ 经过讨论，先取消这个，退出机制和buff有重叠 
            }
            else if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD) == false)
            {
                pobjPath[i].alarmPostFlag = 0U; // 20230905_Will+ 经过讨论，先取消这个，退出机制和buff有重叠

                /*策略八：边线外鬼点抑制*/
                ADAS_BSD_OutsideLineSuppression(pobjAlm, pobjPath, i); 
            }
            else
            {
                ;   //donothing
            }

            /*策略六：进入报警延迟*/
            alarmFlag = ADAS_BSD_setAlarmManager(pobjAlm, pobjPath, i); 
        }
        else
        { 
            if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD) != false)
            {
                /**
                 * @brief 
                 * 
                 */
                if (pobjPath[i].overBsdAlarmDly == 0)
                { 
                    ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD); 
                }
                else
                {
                    alarmFlag = true;

                    pobjPath[i].overBsdAlarmDly--;

                    ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_BSD);
                }
            }
        }
    }
    else
    { 
        ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_BSD);
    }

    return alarmFlag;
}
