include(../libs.pri)

QT += core gui widgets concurrent

HEADERS += \
    aftersalecalibration.h \
    aftersalecalibrationworker.h \
    dialogcancmds.h \
    functions_global.h \
    radarconfig.h \
    staticcalibration.h \
    staticcalibrationworker.h \
    uds.h

FORMS += \
    aftersalecalibration.ui \
    dialogcancmds.ui \
    radarconfig.ui \
    staticcalibration.ui

SOURCES += \
    aftersalecalibration.cpp \
    aftersalecalibrationworker.cpp \
    dialogcancmds.cpp \
    radarconfig.cpp \
    staticcalibration.cpp \
    staticcalibrationworker.cpp \
    uds.cpp

LIBS += -lutils -ldevices
