{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/ffmpeg-x86-windows-6.1.1#2-e943cfa1-0b47-482d-b847-547f385d36cc", "name": "ffmpeg:x86-windows@6.1.1#2 c94e54fcc44c63e571bf60233ff8fb9385eac24a452384b595091b7b4868868d", "creationInfo": {"creators": ["Tool: vcpkg-7d353e869753e5609a1f1a057df3db8fd356e49d"], "created": "2024-05-28T03:16:41Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-5"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-6"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-7"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-8"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-9"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-10"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-11"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-12"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-13"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-14"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-15"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-16"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-17"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-18"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-19"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-20"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-6", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-7", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-8", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-9", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-10", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-11", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-12", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-13", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-14", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-15", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-16", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-17", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-18", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-19", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-20", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "ffmpeg", "SPDXID": "SPDXRef-port", "versionInfo": "6.1.1#2", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/ffmpeg", "homepage": "https://ffmpeg.org", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "a library to decode, encode, transcode, mux, demux, stream, filter and play pretty much anything that humans and machines have created.\nFFmpeg is the leading multimedia framework, able to decode, encode, transcode, mux, demux, stream, filter and play pretty much anything that humans and machines have created. It supports the most obscure ancient formats up to the cutting edge. No matter if they were designed by some standards committee, the community or a corporation. It is also highly portable: FFmpeg compiles, runs, and passes our testing infrastructure FATE across Linux, Mac OS X, Microsoft Windows, the BSDs, Solaris, etc. under a wide variety of build environments, machine architectures, and configurations.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "ffmpeg:x86-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "c94e54fcc44c63e571bf60233ff8fb9385eac24a452384b595091b7b4868868d", "downloadLocation": "NONE", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-1", "name": "ffmpeg/ffmpeg", "downloadLocation": "git+https://github.com/ffmpeg/ffmpeg@n6.1.1", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "a84209fe36a2a0262ebc34b727e7600b12d4739991a95599d7b4df533791b12e2e43586ccc6ff26aab2f935a3049866204e322ec0c5e49e378fc175ded34e183"}]}], "files": [{"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0001-create-lib-libraries.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "7008c6bfcaedda9a3e9906cb350f4e4acee3c947e8ca357aca23d14d80b57f97"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0002-fix-msvc-link.patch", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "45611935478d66262fde55d2b1dbe122fc0da58c0f3e07682e78734ab2e4fc1f"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0003-fix-windowsinclude.patch", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "9030b03bcc83d9005401b05754d6dae07255b20eda327ae3fe410df94cd1fead"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0004-fix-debug-build.patch", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "b0c0cbbd6dba5d2ee1a9e4a745b7bcba4d83a6b8196253588d97c2adbc2a247c"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0005-fix-nasm.patch", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "9538a85350d46023ac4482abd0f78eb1c75cea5d66f511aead3c233d710995f6"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0006-fix-StaticFeatures.patch", "SPDXID": "SPDXRef-file-5", "checksums": [{"algorithm": "SHA256", "checksumValue": "aa86aa011837a43fb64b5f496c9fa2dfee03c147ec8d2f2f49be50773013dae5"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0007-fix-lib-naming.patch", "SPDXID": "SPDXRef-file-6", "checksums": [{"algorithm": "SHA256", "checksumValue": "1f496b6c998afe63ff20ed15108947d6cd5d81247b0e20dd57c8a75e599a3d3f"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0009-Fix-fdk-detection.patch", "SPDXID": "SPDXRef-file-7", "checksums": [{"algorithm": "SHA256", "checksumValue": "19515fe92d169c2677fc0c4218c0d433df400d937f95e7bc027cd78d07922bf3"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0011-Fix-x265-detection.patch", "SPDXID": "SPDXRef-file-8", "checksums": [{"algorithm": "SHA256", "checksumValue": "1340cfc3d41944f33397fc7bd4377b4451a8f2a56a5d2d1089fdf58eb8bb7f47"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0012-Fix-ssl-110-detection.patch", "SPDXID": "SPDXRef-file-9", "checksums": [{"algorithm": "SHA256", "checksumValue": "6beae0525cac597c25f3d74959a91ac5739338a7c6efbdeb1e4079b51ccaa4d4"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0013-define-WINVER.patch", "SPDXID": "SPDXRef-file-10", "checksums": [{"algorithm": "SHA256", "checksumValue": "dba22ed5a6544fdcd4e85a3ca6b470857ba6343b63d34776fdea1823d689a35a"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0015-Fix-xml2-detection.patch", "SPDXID": "SPDXRef-file-11", "checksums": [{"algorithm": "SHA256", "checksumValue": "451d432d84baea06fda44669b3b85880795e25edfef9c238361b27e6dd12cee2"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0020-fix-aarch64-libswscale.patch", "SPDXID": "SPDXRef-file-12", "checksums": [{"algorithm": "SHA256", "checksumValue": "1106eb674b3ce9ca0413a91a475908758a6cb6c06c2bd4ac99f23160bf1ad580"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0022-fix-iconv.patch", "SPDXID": "SPDXRef-file-13", "checksums": [{"algorithm": "SHA256", "checksumValue": "e2b80ffce6008939b14a510f64195daa1f1113ae8bbffb663070d6d6f430d220"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/0023-fix-qsv-init.patch", "SPDXID": "SPDXRef-file-14", "checksums": [{"algorithm": "SHA256", "checksumValue": "89204ac42780be26b58a9f1cf18483a0c1e546ea601dc9de0fd8fb6bee00d7e0"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/build.sh.in", "SPDXID": "SPDXRef-file-15", "checksums": [{"algorithm": "SHA256", "checksumValue": "7569c86058daf4d556ceb01b862cddc57cf9748f8b0009e3a7107a9ea725b4e5"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/FindFFMPEG.cmake.in", "SPDXID": "SPDXRef-file-16", "checksums": [{"algorithm": "SHA256", "checksumValue": "e47d75e2e99c22b457e05898a9ff2244f3115ac82fc7bfddb99fe18ea2f04e6c"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/portfile.cmake", "SPDXID": "SPDXRef-file-17", "checksums": [{"algorithm": "SHA256", "checksumValue": "b5ae8b8c072455930aa6bf5a7e6fd79ab2ba7e5ceab75e76c816b9616cbb0a0c"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/usage", "SPDXID": "SPDXRef-file-18", "checksums": [{"algorithm": "SHA256", "checksumValue": "be5923d235daf6db434faa8bf089835605420aaf3e4b870dbcf8274e28c4c305"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/vcpkg-cmake-wrapper.cmake", "SPDXID": "SPDXRef-file-19", "checksums": [{"algorithm": "SHA256", "checksumValue": "236c9569e1bb51f66a8e7b6ffd877fe1a08aa43a9b264ed8d30e38dc20228c5e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/ffmpeg/vcpkg.json", "SPDXID": "SPDXRef-file-20", "checksums": [{"algorithm": "SHA256", "checksumValue": "49b528dd14bfabab3a5da574d8819821591eecc5ddeb770ab8158c6fad2e2a22"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}