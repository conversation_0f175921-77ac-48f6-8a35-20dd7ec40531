﻿#include "dataprocessform.h"
#include "ui_dataprocessform.h"

#include "analysis/analysisworker.h"
#include "analysis/calculationworker.h"
#include "views/viewsmanager.h"
#include "utils/settingshandler.h"

#include <QTimer>
#include <QThread>
#include <QFileDialog>
#include <QMessageBox>

#ifndef ALGORITHM_DEBUG_DATA_PROCESS_RAW
DataProcessForm::DataProcessForm(Views::ViewsManager *viewsManager, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::DataProcessForm),
    mViewsManager(viewsManager)
{
    ui->setupUi(this);

    mCTCANDeviceFile = new CTCANDeviceFile;

    if (mViewsManager) {
    connect(this, &DataProcessForm::calculateFinished, mViewsManager, &Views::ViewsManager::calculateFinished);
    }

    connect(mCTCANDeviceFile, &CTCANDeviceFile::calculateFinished, this, &DataProcessForm::calculateFinished);
    connect(mCTCANDeviceFile, &CTCANDeviceFile::currentFrame, this, &DataProcessForm::currentFrame);
}
#else
DataProcessForm::DataProcessForm(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::DataProcessForm)
{
    ui->setupUi(this);

    mCTCANDeviceFile = new CTCANDeviceFile;

    connect(mCTCANDeviceFile, &CTCANDeviceFile::calculateFinished, this, &DataProcessForm::calculateFinished);
    connect(mCTCANDeviceFile, &CTCANDeviceFile::currentFrame, this, &DataProcessForm::currentFrame);
    connect(this, &DataProcessForm::nextFrame, this, &DataProcessForm::on_pushButtonNextFrame_clicked);


    ui->checkBoxGoTo->setChecked(SETTINGS_GET_VALUE(QLatin1String("DataProcess/GoTo"), false).toBool());
    ui->lineEditGoTo->setText(SETTINGS_GET_VALUE(QLatin1String("DataProcess/GoToFrame"), "-1").toString());
}

#endif

DataProcessForm::~DataProcessForm()
{
    delete ui;
}

void DataProcessForm::currentFrame(int frameNo, int firstFrame, int frame)
{
    ui->labelFrameNumber->setText(QString::number(frameNo));
    ui->labelFirstFrame->setText(QString::number(firstFrame));
    ui->labelCurrentFrame->setText(QString::number(frame));
}

void DataProcessForm::readFinished()
{
    mRuning = false;
    ui->pushButtonNextFrame->setEnabled(mRuning);
    ui->pushButtonStartAndStop->setText(QString::fromLocal8Bit(mRuning ? "停止" : "开始") );
    ui->pushButtonPauseAndContionue->setText(QString::fromLocal8Bit(mPaused ? "继续" : "暂停") );
}

void DataProcessForm::on_pushButtonFiles_clicked()
{
    QString filter = tr("File (*.binary *.blf *.asc);; bin (*.binary);; BLF (*.blf);; ASC (*.asc);; CSV (*.csv);; all (*.*)");
    QStringList files = QFileDialog::getOpenFileNames(
                              this,
                              QString::fromLocal8Bit("选择回放文件"),
                              mFilenames.size() ? mFilenames.at(0) : "./",
                              filter);
    if (!files.isEmpty()) {
        ui->lineEditFiles->setText(files.join(";"));
        mFilenames = files;
    }

//    setFiles(ui->lineEditFiles->text().split(";", Qt::SkipEmptyParts));
}

void DataProcessForm::on_pushButtonPauseAndContionue_clicked()
{
    mPaused = !mPaused;
    if (!mPaused) {
        QTimer::singleShot(50, this, &DataProcessForm::on_pushButtonNextFrame_clicked);
    }
    mCTCANDeviceFile->pause(mPaused);

    ui->pushButtonPauseAndContionue->setText(QString::fromLocal8Bit(mPaused ? "继续" : "暂停"));
}

void DataProcessForm::on_pushButtonNextFrame_clicked()
{

    if (!mRuning || !mCTCANDeviceFile->readNextFrame()) {
//        QMessageBox::warning(this, QString::fromLocal8Bit("读取"), QString::fromLocal8Bit("读取失败"));
        readFinished();
        return;
    }

    if (mPaused) { // 单帧
        return;
    }

    quint64 currentDateTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    int time = currentDateTime - mPreviousFrameDateTime;

    qDebug() << __FUNCTION__ << __LINE__ << time;
    if (time > 50) {
        time = 49;
    }
    time = 50 - time;
    mPreviousFrameDateTime = currentDateTime;
    QTimer::singleShot(time, this, &DataProcessForm::on_pushButtonNextFrame_clicked);
}

void DataProcessForm::on_pushButtonStartAndStop_clicked()
{
    if (!mRuning) {
        mFilenames = ui->lineEditFiles->text().split(";");
        if (mFilenames.isEmpty()) {
            QMessageBox::warning(this, QString::fromLocal8Bit("打开"), QString::fromLocal8Bit("未选择文件!"));
            return;
        }
        currentFrame(-1, -1, -1);
        mCTCANDeviceFile->setRadarID(ui->comboBoxRadarID->currentText().toUInt());
        mRuning = mCTCANDeviceFile->open(mFilenames, ui->lineEditGoTo->text().toInt(), ui->checkBoxGoTo->isChecked());
        if (!mRuning) {
            QMessageBox::warning(this, QString::fromLocal8Bit("打开"),
                                 QString::fromLocal8Bit("打开失败！\n%1\n%2")
                                 .arg(mCTCANDeviceFile->errorString().c_str())
                                 .arg(mFilenames.join("\n")));
            return;
        }


        SETTINGS_SET_VALUE(QLatin1String("DataProcess/GoTo"), ui->checkBoxGoTo->isChecked());
        SETTINGS_SET_VALUE(QLatin1String("DataProcess/GoToFrame"), ui->lineEditGoTo->text());

        QTimer::singleShot(50, this, &DataProcessForm::on_pushButtonNextFrame_clicked);

        ui->pushButtonStartAndStop->setText(QString::fromLocal8Bit(mRuning ? "停止" : "开始") );
    } else {
        mRuning = !mCTCANDeviceFile->close();
        if (mRuning) {
            QMessageBox::warning(this, QString::fromLocal8Bit("停止"),
                                 QString::fromLocal8Bit("停止失败！\n%1")
                                 .arg(mCTCANDeviceFile->errorString().c_str()));
            return;
        }

        ui->pushButtonStartAndStop->setText(QString::fromLocal8Bit(mRuning ? "停止" : "开始") );
    }

    ui->pushButtonNextFrame->setEnabled(mRuning);
//    ui->pushButtonPauseAndContionue->setText(QString::fromLocal8Bit(mPaused ? "继续" : "暂停") );
}
