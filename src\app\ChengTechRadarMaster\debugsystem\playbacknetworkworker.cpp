﻿#include "playbacknetworkworker.h"

#include <QTcpServer>
#include <QTcpSocket>
#include <QTimer>
#include <QFileInfo>
#include <QDebug>

#define DCK_BOARD_PACKAGE_SIZE 1028  //1byte[head] 3bytes[index] 1024bytes[data]
#define DCK_BOARD_PACKAGE_HEAD_LEN 4
#define DCK_BOARD_PACKAGE_DATA_SIZE (DCK_BOARD_PACKAGE_SIZE - DCK_BOARD_PACKAGE_HEAD_LEN) //1024

#define GET_VALUE_SPECIFIED_BYTE(value, offset) ((value >> (8 * offset)) & 0xFF)

static uint32_t getNumberOfTcChannel(RadarCfgParams* pSensorCfg) {
    uint32_t nValueTmpTxGroup = 0, antiVelAmbEn = 0, numberOfTx = 0;
    if (NULL == pSensorCfg) return 0;
    antiVelAmbEn = pSensorCfg->antiVelambEn;
    for (int i = 0; i < pSensorCfg->txGroups.size(); i++) {
        nValueTmpTxGroup = nValueTmpTxGroup | pSensorCfg->txGroups[i];
    }
    if ((nValueTmpTxGroup & 0xF000) > 0) {
        numberOfTx = 4;
    }
    else if ((nValueTmpTxGroup & 0xF00) > 0) {
        numberOfTx = 3;
    }
    else if ((nValueTmpTxGroup & 0xF0) > 0) {
        numberOfTx = 2;
    }
    else if ((nValueTmpTxGroup & 0xF) > 0) {
        numberOfTx = 1;
    }
    else {
        return 0;
    }
    if (antiVelAmbEn == 1) {
        numberOfTx++;
    }
    return numberOfTx;
}

PlaybackNetworkWorker::PlaybackNetworkWorker(QObject *parent) : QObject(parent)
{

}

PlaybackNetworkWorker::~PlaybackNetworkWorker()
{
    if (mReadFileData)
    {
        free(mReadFileData);
        mReadFileData = 0;
    }
}

bool PlaybackNetworkWorker::init(int workMode,
                     const QString &filename,
                     int oneFrameProfileCount)
{
    mWorkMode = (WorkMode)workMode;
    mOneFrameProfileCount = oneFrameProfileCount;

    if (mReadFileData)
    {
        free(mReadFileData);
        mReadFileData = 0;
    }
    if (workMode == CollectMode)
    {
        mADCFilename = filename;
        mFileSize = 0;
        mOneReadFileSize = 1024;
    }
    else
    {
        if (!QFileInfo::exists(filename))
        {
            emit initResult(false);
            return false;
        }
        mADCFilename = filename;
        mFileSize = QFileInfo(mADCFilename).size();
        mOneReadFileSize = 1024;

        unsigned int seek = 0;
        for (int i = 0; i < mProfileConfigParams.size(); ++i)
        {
            seek += mHilCfgParams[i].frameSize;
        }
        if (!seek)
        {
            emit initResult(false);
            return false;
        }

        mFrameCount = mFileSize / seek * (mProfileConfigParams.size() / mOneFrameProfileCount);

        int remainSize = mFileSize % seek;
        bool ok = true;
        for (unsigned int i = 0; i < mProfileConfigParams.size() / mOneFrameProfileCount; ++i)
        {
            for (unsigned int j = 0; j < mOneFrameProfileCount; ++j)
            {
                if (remainSize < (mHilCfgParams[(i * j + 1) % mHilCfgParams.size()].frameSize))
                {
                    ok = false;
                    break;
                }
                remainSize -= mHilCfgParams[i * j].frameSize;
            }
            if (!ok)
            {
                break;
            }
            mFrameCount++;
        }
        emit playFrameInfo(0, mFrameCount);
//        qDebug() << __FUNCTION__ << seek << mFileSize << mCurrentFrameIndex << mFrameCount << mProfileConfigParams.size() << mOneFrameProfileCount;
    }
    mReadFileData = (char *)malloc(mOneReadFileSize);

    if (workMode == RechargeMode)
    {
        fillHilFuncTcpCfgProtocolPackage();
    }
    else
    {
        emit initResult(true);
    }

    return true;
}

void PlaybackNetworkWorker::setCollectFrameCount(int count)
{
    mProfilePackageCount.clear();
    mFrameCount = count;
    mADCPackageIndex = 0;
    quint32 packageNum = 0;
    for (int j = 0; j < mHilCfgParams.size(); ++j)
    {
        packageNum += mHilCfgParams[j].packageNum;
        if ((j + 1) % mOneFrameProfileCount == 0)
        {
            mProfilePackageCount << packageNum;
            packageNum = 0;
        }
    }

    int oneFrameCount = mHilCfgParams.size() / mOneFrameProfileCount;
    mADCPackageCount = mFrameCount / oneFrameCount * mAllProfilePackageCount;
    qDebug() << __FUNCTION__ << __LINE__
             << count << mAllProfilePackageCount
             << mHilCfgParams.size() << mOneFrameProfileCount
             << oneFrameCount << mADCPackageCount << mFrameCount % oneFrameCount << mProfilePackageCount;

    for (int i = 0; i < mFrameCount % oneFrameCount; ++i)
    {
        for (int j = 0; j < mOneFrameProfileCount; ++j)
        {
            mADCPackageCount += mHilCfgParams[i * mOneFrameProfileCount + j].packageNum;
        }
    }

    qDebug() << __FUNCTION__ << __LINE__ << mADCPackageCount;
}

bool PlaybackNetworkWorker::parseProfileConfigs(const QStringList &files)
{
    mProfileConfigsLoaded = false;
    mProfileConfigParams.clear();
    mHilCfgParams.clear();
    mAllProfilePackageCount = 0;
    foreach (const QString &filename, files)
    {
        QFile file(filename);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
        {
            return false;
        }

        QString text = file.readAll();
        file.close();

//        qDebug() << __FUNCTION__ << __LINE__ << text;
        RadarCfgParams radarCfgParams;

        QRegExp rx("\\s*\\.(\\w+)\\s*=\\s*((\\{.*\\})|(\\w+)),");
        rx.setMinimal(true);
        QMap<QString, QString> params;
        int pos = 0;
        while ((pos = rx.indexIn(text, pos)) != -1) {
            params[rx.cap(1)] = rx.cap(2);
            pos += rx.matchedLength();
        }

//        qDebug() << __FUNCTION__ << __LINE__ << params;
        radarCfgParams.velNfft = params["vel_nfft"].toUInt();
        radarCfgParams.adcSampleStart = params["adc_sample_start"].toUInt();
        radarCfgParams.adcFreq = params["adc_freq"].toUInt();
        radarCfgParams.decFactor = params["dec_factor"].toUInt();
        radarCfgParams.rngNfft = params["rng_nfft"].toUInt();
        radarCfgParams.antiVelambEn = params["de_vel_amb"].toLower() == "true" ? true : false;
        QString tx_groups = params["tx_groups"];
        tx_groups.replace(QRegExp("\\{|\\}"), "");
        tx_groups.remove(QRegExp("\\s"));

        QStringList groups = tx_groups.split(",");
        foreach (const QString &group, groups)
        {
            radarCfgParams.txGroups << group.toUInt(0, 16);
        }

        mProfileConfigParams << radarCfgParams;

        HilCfgParams hilCfgParams;

        radarCfgParams.numOfTcChannel = getNumberOfTcChannel(&radarCfgParams);
        //velNfft * numOfTcChannel
        hilCfgParams.chirpNmb = radarCfgParams.numOfTcChannel * radarCfgParams.velNfft;
        hilCfgParams.chirpLen = 1600;//1600; //TBD
        hilCfgParams.sampleNmb = radarCfgParams.rngNfft;
        //adcSampleStart * adc_freq * dec_factor
        hilCfgParams.skipNmb = radarCfgParams.adcSampleStart * radarCfgParams.adcFreq * radarCfgParams.decFactor;
        //计算package的size
        hilCfgParams.frameSize = hilCfgParams.chirpNmb * hilCfgParams.sampleNmb * 4 * 2;
        hilCfgParams.packageNum = hilCfgParams.frameSize / DCK_BOARD_PACKAGE_DATA_SIZE;
        if (hilCfgParams.skipNmb % 2) hilCfgParams.skipNmb -= 1;
        hilCfgParams.isValid = 1;

        mAllProfilePackageCount += hilCfgParams.packageNum;

        qDebug() << __FUNCTION__ << __LINE__
                 << radarCfgParams.velNfft
                 << radarCfgParams.adcSampleStart
                 << radarCfgParams.adcFreq
                 << radarCfgParams.decFactor
                 << radarCfgParams.rngNfft
                 << radarCfgParams.antiVelambEn
                 << radarCfgParams.txGroups
                 << hilCfgParams.frameSize
                 << hilCfgParams.packageNum;

        mHilCfgParams << hilCfgParams;
    }

    mProfileConfigsLoaded = true;
    return mProfileConfigsLoaded;
}

void PlaybackNetworkWorker::openTCPServer(const QString &IP, quint16 port, bool analyIP)
{
    if (!mTcpServer)
    {
        mTcpServer = new QTcpServer(this);
        connect(mTcpServer, &QTcpServer::newConnection, this, &PlaybackNetworkWorker::connectNewTCPClient);
    }

    QHostAddress address = QHostAddress::Any;
    if (!analyIP && !IP.isEmpty())
    {
        address.setAddress(IP);
    }

    mTcpServer->listen(address, port);
    if (!mTcpServer->isListening())
    {
        qDebug() << __FUNCTION__ << __LINE__ << mTcpServer->errorString();
        emit tcpServerClosed();
        return;
    }

    emit tcpServerOpened(true);
}

void PlaybackNetworkWorker::closeTCPServer()
{
    if (mTcpServer)
    {
        mTcpServer->close();
    }
    if (mFile.isOpen())
    {
        mFile.close();
    }

    emit tcpServerClosed();
}

void PlaybackNetworkWorker::start(int startFrameIndex, int periodicTime, bool singleStep)
{
    mADCPackageIndex = 0;
    mADCPackageData.clear();

    mFile.setFileName(mADCFilename);
    mCurrentFrameIndex = startFrameIndex;
    mPeriodicTime = periodicTime;
    mSingleStep = singleStep;

    switch (mWorkMode)
    {
    case CollectMode:
        mCurrentFrameIndex = 0;
        emit playFrameInfo(mCurrentFrameIndex, mFrameCount);
        if (!mFile.open(QIODevice::WriteOnly | QIODevice::Truncate))
        {
            emit playState(PlayError, QString::fromLocal8Bit("打开ADC文件失败!\n%1").arg(mADCFilename));
            return;
        }
        qDebug() << __FUNCTION__ << __LINE__ << mADCFilename;
        emit playState(Playing);
        break;
    case RechargeMode:
        if (!mClientConnected)
        {
            emit playState(PlayError, QString::fromLocal8Bit("DCK板未连接!"));
            return;
        }
    case PlaybackMode:
        if (!mFile.open(QIODevice::ReadOnly | QIODevice::Truncate))
        {
            emit playState(PlayError, QString::fromLocal8Bit("打开ADC文件失败!\n%1").arg(mADCFilename));
            return;
        }
        if (!mPlayTimer)
        {
            mPlayTimer = new QTimer(this);
            connect(mPlayTimer, &QTimer::timeout, this, &PlaybackNetworkWorker::nextFrame);
        }

        unsigned int seek = 0;
        for (int i = 0; i < mCurrentFrameIndex * mOneFrameProfileCount; ++i)
        {
            seek += mHilCfgParams[i % mHilCfgParams.size()].frameSize;
        }

        if (seek >= mFileSize)
        {
            emit playFrameInfo(mCurrentFrameIndex, mFrameCount);
            emit playState(PlayStoped);
            return;
        }
        mFile.seek(seek);

//        qDebug() << __FUNCTION__ << __LINE__ << seek << mFileSize << mCurrentFrameIndex << mFrameCount;
        emit playFrameInfo(mCurrentFrameIndex, mFrameCount);
        emit playState(Playing);

        mPlayTimer->setSingleShot(true);
        mPlayTimer->start(mPeriodicTime);
        break;
    }

    mPlayState = Playing;
}

void PlaybackNetworkWorker::stop()
{
    qDebug() << __FUNCTION__ << __LINE__;
    mPlayState = PlayStoped;
    {
        QMutexLocker locker(&mFileMutex);
        if (mFile.isOpen())
        {
            mFile.close();
        }
    }

    if (mPlayTimer)
    {
        mPlayTimer->stop();
    }

    emit playState(PlayStoped);
}

void PlaybackNetworkWorker::nextFrame()
{
    if ((mWorkMode == RechargeMode && !mClientConnected) || !mFile.isOpen() || mFile.atEnd() || mCurrentFrameIndex >= mFrameCount)
    {
        qDebug() << __FUNCTION__ << __LINE__;
        stop();
        return;
    }

    qint64 len = 1024;
    {
        QMutexLocker locker(&mFileMutex);
        for (unsigned int i = 0; len && i < mOneFrameProfileCount; ++i)
        {
            qDebug() << __FUNCTION__ << __LINE__ << mOneFrameProfileCount << i << mHilCfgParams[i].packageNum << mHilCfgParams[i].frameSize;
            for (unsigned int j = 0; len && j < (mHilCfgParams[i].packageNum); j++)
            {
                len = mFile.read(mReadFileData, mOneReadFileSize);
                if (mWorkMode == RechargeMode && len == mOneReadFileSize) // HIL
                {
                    QByteArray data(4, '0');
                    data[0] = 0x06;
                    qint32 index = mADCPackageIndex % 0xFFFFFF;
                    data[1] = GET_VALUE_SPECIFIED_BYTE(index, 2);
                    data[2] = GET_VALUE_SPECIFIED_BYTE(index, 1);
                    data[3] = GET_VALUE_SPECIFIED_BYTE(index, 0);
                    mADCPackageIndex++;
                    data.append(mReadFileData, len);
//                    qDebug() << __FUNCTION__ << __LINE__ << len << data.size() << data.mid(0, 4).toHex();
                    writeData(data);
                }
                else if (len > 0 && mWorkMode == PlaybackMode) // 信号处理
                {
                    qDebug() << __FUNCTION__ << __LINE__ << len;
                }
            }
        }
    }

    emit playFrameInfo(++mCurrentFrameIndex, mFrameCount);

    if (mFile.atEnd())
    {
        qDebug() << __FUNCTION__ << __LINE__;
        stop();
        return;
    }

    if (!mSingleStep)
    {
        mPlayTimer->start(mPeriodicTime);
    }
}

void PlaybackNetworkWorker::pause(bool paused)
{
    if (paused)
    {
        mPlayTimer->stop();
    }
    else
    {
        mPlayTimer->start(mPeriodicTime);
    }
}

void PlaybackNetworkWorker::connectNewTCPClient()
{
    qDebug() << __FUNCTION__ << __LINE__;
    while (mTcpServer->hasPendingConnections())
    {
//        QTcpSocket *socket = mTcpClient;
        mTcpClient = mTcpServer->nextPendingConnection();
//        if (socket)
//        {
//            socket->deleteLater();
//        }
        connect(mTcpClient, SIGNAL(readyRead()), this, SLOT(readTCPClientData()));
        connect(mTcpClient, SIGNAL(disconnected()), this, SLOT(disconnectedTCPClient()));
        connect(mTcpClient, SIGNAL(disconnected()), mTcpClient, SLOT(deleteLater()));

        connect(this, &PlaybackNetworkWorker::write, this, &PlaybackNetworkWorker::writeData);

        emit tcpClientConnected(mTcpClient->peerAddress().toString(), mTcpClient->peerPort());
        mClientConnected = true;

        radeDCKVersion();
    }
}

void PlaybackNetworkWorker::disconnectedTCPClient()
{
    qDebug() << __FUNCTION__ << __LINE__;
    mClientConnected = false;
    emit tcpClientDisonnected();
    stop();
}

void PlaybackNetworkWorker::readTCPClientData()
{
    QMutexLocker locker(&mReadMutex);
    QTcpSocket *tcpSocket = qobject_cast<QTcpSocket *>(sender());
    mADCPackageData.append(tcpSocket->readAll());

    while (mADCPackageData.size() > 8)
    {
//        qDebug() << __FUNCTION__ << __LINE__ << mADCPackageData.size() << QString::number(mADCPackageData[0], 16);
        switch (mADCPackageData[0])
        {
        case 4:
        {
            if (mADCPackageData.size() < 1028)
            {
//                qDebug() << __FUNCTION__ << __LINE__ << mADCPackageData.size() << "----------- Incomplete data.";
                return;
            }

            QByteArray indexData = mADCPackageData.mid(0, 4);
//            std::reverse(indexData.begin(), indexData.end());
            quint32 index = ((*((quint32*)indexData.data())) >> 8);
            if ((index + 1) % 1024 == 0)
            {
                qDebug() << __FUNCTION__ << __LINE__
                         << mADCPackageData.size() << indexData.toHex()
                         << index << mADCPackageCount << mPlayState;
            }
            if (mPlayState != Playing)
            {
                mADCPackageData.remove(0, 1028);
                continue;
            }

            mFile.write(mADCPackageData.data() + 4, 1024);
            mADCPackageData.remove(0, 1028);
            mADCPackageIndex = index + 1;
            quint32 remainIndex = mADCPackageIndex % mAllProfilePackageCount;
            if (remainIndex == 0)
            {
                mCurrentFrameIndex++;
                emit playFrameInfo(mCurrentFrameIndex, mFrameCount);
            }
            else
            {
                for (int i = 0; i < mProfilePackageCount.size(); ++i)
                {
                    remainIndex -= mProfilePackageCount[i];
                    if (remainIndex == 0)
                    {
                        mCurrentFrameIndex++;
                        emit playFrameInfo(mCurrentFrameIndex, mFrameCount);
                    }
                }
            }

            if (index + 1 >= mADCPackageCount)
            {
                qDebug() << __FUNCTION__ << __LINE__ << index << mADCPackageCount;
                stop();
                return;
            }
        }
            break;
        case 8:
            if (mTCPClientDataState == SetProfileConfig)
            {
                emit initResult(true);
                mTCPClientDataState = NoneData;
            }
            mADCPackageData.remove(0, 8);
            break;
        case 9:
            if (mTCPClientDataState == ReadDCKVersion)
            {
                emit dckVersion(QString("%1.%2.%3").arg((short)mADCPackageData[4] << 8 + mADCPackageData[5]).arg((quint8)mADCPackageData[6]).arg((quint8)mADCPackageData[7]));
                mTCPClientDataState = NoneData;
                mADCPackageIndex = 0;
            }
            mADCPackageData.remove(0, 8);
            break;
        default:
            qDebug() << __FUNCTION__ << __LINE__ << mADCPackageData.size();
            mADCPackageData.clear();
            break;
        }
    }
}

void PlaybackNetworkWorker::writeData(const QByteArray &data)
{
//    qDebug() << __FUNCTION__ << __LINE__ << data.mid(0, 4).toHex();
    if (mClientConnected)
    {
        int len = mTcpClient->write(data);
//        qDebug() << __FUNCTION__ << __LINE__ << len << data.mid(0, 4).toHex();
    }
}

void PlaybackNetworkWorker::play()
{

}

void PlaybackNetworkWorker::radeDCKVersion()
{
    QByteArray data(1028, 0);
    data[0] = 0x09;
    data[1] = 0xde;
    data[2] = 0xad;
    data[3] = 0xcc;

    mTCPClientDataState = ReadDCKVersion;

    emit write(data);
}

void PlaybackNetworkWorker::radeDCKVersion1()
{
    QByteArray data(71, 0);
    data[0] = 0x03;
    data[1] = 0xde;
    data[2] = 0xad;
    data[3] = 0xcc;

    emit write(data);
}

void PlaybackNetworkWorker::fillHilFuncTcpCfgProtocolPackage()
{
    QByteArray data = QByteArray(71, 0x00);
    data[0] = 0x00;
    data[1] = 0xde;
    data[2] = 0xad;
    data[3] = 0xcc;

    data[70] = 0x04;	//default param
    uint32_t offset = 0;
    for (int i = 0; i < mHilCfgParams.size(); i++) {
        offset = 16 * i;
        data[4 + offset] = 0x03;//0x03
        data[5 + offset] = 0x00;//src
        data[6 + offset] = 0x00;//type
        data[7 + offset] = GET_VALUE_SPECIFIED_BYTE(mFrameCount, 0);//frame_count
        data[8 + offset] = GET_VALUE_SPECIFIED_BYTE(mFrameCount, 1);//frame_count
        data[9 + offset] = GET_VALUE_SPECIFIED_BYTE(mFrameCount, 2);//frame_count
        data[10 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpNmb, 0);//chirp_num
        data[11 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpNmb, 1);//chirp_num
        data[12 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpLen, 0);//chirp_len
        data[13 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpLen, 1);//chirp_len
        data[14 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].skipNmb, 0);//skip_nmb
        data[15 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].skipNmb, 1);//skip_nmb
        data[16 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].sampleNmb, 0);//sample_nmb
        data[17 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].sampleNmb, 1);//sample_nmb
        qDebug("mHilCfgParams[%d] chirpNmb[%d] chirpLen[%d] skipNmb[%d] sampleNmb[%d]\r\n",
            i, mHilCfgParams[i].chirpNmb, mHilCfgParams[i].chirpLen, mHilCfgParams[i].skipNmb, mHilCfgParams[i].sampleNmb);

    }
    data[68] = mProfileConfigParams.size();	//must be right
    data[69] = mProfileConfigParams.size(); //must be right

    mTCPClientDataState = SetProfileConfig;
    emit write(data);

    return;
}
