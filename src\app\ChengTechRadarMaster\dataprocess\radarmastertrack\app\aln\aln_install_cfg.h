#ifndef PC_DBG_FW
#include "vehicle_cfg.h"
#else
#include "aln_type.h"
#endif


/**
 * @brief 雷达安装信息. 
 */
typedef struct{
    float dir_front_left;
    float dir_front_right;
    float dir_rear_left;
    float dir_rear_right;
    float aln_obj_angle_fcr;    // 前角目标标定位置
    float aln_obj_angle_rcr;    // 后角目标标定位置
    float aln_execpt_angle_fcr; // 前角期望安装角度
    float aln_execpt_angle_rcr; // 后角期望安装角度
    float rear_center_x_fl;     // 左前角雷达与后轴中心在X轴方向的距离
    float rear_center_y_fl;     // 左前角雷达与后轴中心在Y轴方向的距离
    float rear_center_x_rl;     // 左后角雷达与后轴中心在X轴方向的距离
    float rear_center_y_rl;     // 左后角雷达与后轴中心在Y轴方向的距离
    float rear_center_x_fr;
    float rear_center_y_fr;     
    float rear_center_x_rr;     
    float rear_center_y_rr;
    float mount6pos_to_outer_x_fl;
    float mount6pos_to_outer_y_fl;
    float mount4pos_to_outer_x_fl;
    float mount4pos_to_outer_y_fl;
    float mount7pos_to_outer_x_fl;
    float mount7pos_to_outer_y_fl;
    float mount5pos_to_outer_x_fl;
    float mount5pos_to_outer_y_fl;
}INSTALL_MESSAGE_T;

INSTALL_MESSAGE_T *get_install_message(void);

extern INSTALL_MESSAGE_T installmessage;
extern INSTALL_MESSAGE_T installmessageDefault;
// 对应不同车型配置字的安装信息
#if defined VEHICLE_TYPE_BYD_HA5
extern INSTALL_MESSAGE_T installmessage_HA5E;
#elif defined VEHICLE_TYPE_BYD_EM2
extern INSTALL_MESSAGE_T installmessage_EM2EH;
#elif defined VEHICLE_TYPE_BYD_UR
extern INSTALL_MESSAGE_T installmessage_UREC5R12V;
extern INSTALL_MESSAGE_T installmessage_URED5R12V;
#endif
