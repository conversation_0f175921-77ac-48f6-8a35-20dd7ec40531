﻿#ifndef OPENCVREADER_H
#define OPENCVREADER_H

#include <string>

#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/core/core.hpp>
#include <opencv2/objdetect/objdetect.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc/types_c.h>

#include <QImage>

#define     VIDEO_WIDTH     1280
#define     VIDEO_HIGNT     720

class OpenCVReader
{
public:
    OpenCVReader();
    OpenCVReader(std::string url, bool isFile);

    bool open();
    bool open(std::string url, bool isFile);
    bool close();
    bool isOpened() const {return mOpened; }

    cv::Mat *read();

private:
    std::string mURL;
    bool mIsFile{false};
    bool mOpened{false};

    //视频
    cv::VideoCapture mCapture;
    cv::Mat mFrame;
};

#endif // OPENCVREADER_H
