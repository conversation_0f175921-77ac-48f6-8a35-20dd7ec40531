﻿#include "objectcorrdinatesystemconfigdialog.h"
#include "ui_objectcorrdinatesystemconfigdialog.h"

#include <QCheckBox>
#include <QSpinBox>
#include <QColorDialog>

namespace Views {
namespace ObjectView {

ObjectConfig::ObjectConfig(MoveStatus moveStatus, const DrawConfig &drawConfig, QWidget *parent) :
    QWidget(parent), mMoveStatus(moveStatus)
{
    QHBoxLayout *horizontalLayout = new QHBoxLayout(this);
    horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
    horizontalLayout->setContentsMargins(0, 0, 0, 0);

    mCheckBoxShow = new QCheckBox(this);
    mCheckBoxShow->setObjectName(QString::fromUtf8("checkBoxShow"));
    switch (moveStatus) {
    case Motionless:
        mCheckBoxShow->setText("      Motionless:");
        break;
    case InwardMovement:
        mCheckBoxShow->setText(" Inward Movement:");
        break;
    case OutwardMovement:
        mCheckBoxShow->setText("Outward Movement:");
        break;
    default:
        break;
    }

    horizontalLayout->addWidget(mCheckBoxShow);

    QLabel *label_9 = new QLabel(this);
    label_9->setObjectName(QString::fromUtf8("label_9"));
    label_9->setText(QString::fromLocal8Bit("图形"));

    horizontalLayout->addWidget(label_9);

    mComboBoxShape = new QComboBox(this);
    mComboBoxShape->setObjectName(QString::fromUtf8("comboBoxShape"));
    mComboBoxShape->addItems(drawShapeNames());


    horizontalLayout->addWidget(mComboBoxShape);

    QLabel *label_10 = new QLabel(this);
    label_10->setObjectName(QString::fromUtf8("label_10"));
    label_10->setText(QString::fromLocal8Bit("颜色"));

    horizontalLayout->addWidget(label_10);

    mPushButtonColor = new QPushButton(this);
    mPushButtonColor->setObjectName(QString::fromUtf8("pushButtonColor"));

    connect(mPushButtonColor, &QPushButton::clicked, this, [=](){
        mColor = QColorDialog::getColor(mColor, this, tr("Object Color"));

        QPalette pal = mPushButtonColor->palette();
        pal.setColor(QPalette::Button, mColor);
        mPushButtonColor->setPalette(pal);
        mPushButtonColor->setAutoFillBackground(true);
        mPushButtonColor->setFlat(true);
    });

    horizontalLayout->addWidget(mPushButtonColor);

    QLabel *label_11 = new QLabel(this);
    label_11->setObjectName(QString::fromUtf8("label_11"));
    label_11->setText(QString::fromLocal8Bit("大小"));

    horizontalLayout->addWidget(label_11);

    mSpinBoxSize = new QSpinBox(this);
    mSpinBoxSize->setObjectName(QString::fromUtf8("spinBoxSize"));
    mSpinBoxSize->setMinimum(2);

    horizontalLayout->addWidget(mSpinBoxSize);

    QSpacerItem *horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

    horizontalLayout->addItem(horizontalSpacer_4);

    setConfig(drawConfig);
}

void ObjectConfig::setConfig(const DrawConfig &drawConfig)
{
    mCheckBoxShow->setChecked(drawConfig.mShow);
    mComboBoxShape->setCurrentIndex(drawConfig.mDrawShape);
    QPalette pal = mPushButtonColor->palette();
    pal.setColor(QPalette::Button, drawConfig.mColor);
    mPushButtonColor->setPalette(pal);
    mPushButtonColor->setAutoFillBackground(true);
    mPushButtonColor->setFlat(true);
    mColor = drawConfig.mColor;

    mSpinBoxSize->setValue(drawConfig.mSize);
}

DrawConfig ObjectConfig::config()
{
    DrawConfig drawConfig;
    drawConfig.mShow = mCheckBoxShow->isChecked();
    drawConfig.mDrawShape = (DrawShape)mComboBoxShape->currentIndex();
    drawConfig.mColor = mColor;
    drawConfig.mSize = mSpinBoxSize->value();

    return drawConfig;
}

ObjectsConfig::ObjectsConfig(AnalysisFrameType frameType, const ObjectsDrawConfig &drawConfig, QWidget *parent) :
    QGroupBox(parent), mFrameType(frameType)
{
    switch (frameType)
    {
    case FrameRawTarget:
        this->setTitle("Raw Target");
        break;
    case FrameTrackTarget:
        this->setTitle("Track Target");
        break;
    default:
        break;
    }
    QVBoxLayout *layout = new QVBoxLayout(this);

    QHBoxLayout *layoutCalculate = new QHBoxLayout();

    QLabel *label = new QLabel("Conversion Relation: ", this);

    layoutCalculate->addWidget(label);

    mCheckBoxNegateX = new QCheckBox("Negate X", this);
    mCheckBoxNegateX->setChecked(drawConfig.mCalculatorConfig.mNegateX);

    layoutCalculate->addWidget(mCheckBoxNegateX);

    mCheckBoxNegateY = new QCheckBox("Negate Y", this);
    mCheckBoxNegateY->setChecked(drawConfig.mCalculatorConfig.mNegateY);

    layoutCalculate->addWidget(mCheckBoxNegateY);

    QLabel *labelValue = new QLabel("Rotation Angle", this);

    layoutCalculate->addWidget(labelValue);

    mLineEditOffset = new QLineEdit(this);
    mLineEditOffset->setText(QString::number(drawConfig.mCalculatorConfig.mOffsetAngle));

    layoutCalculate->addWidget(mLineEditOffset);

    QLabel *labelAngle = new QLabel(QString::fromLocal8Bit("°"), this);

    layoutCalculate->addWidget(labelAngle);

    QSpacerItem *horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

    layoutCalculate->addItem(horizontalSpacer);

    layout->addLayout(layoutCalculate);

    mLayoutObjects = new QVBoxLayout();
    for (int i = 0; i < UnknowMoveStatus; ++i) {
        mLayoutObjects->addWidget(new ObjectConfig((MoveStatus)i, drawConfig.mDrawConfig[i], this));
    }

    layout->addLayout(mLayoutObjects);
}

void ObjectsConfig::setConfig(const ObjectsDrawConfig &drawConfig)
{
    for (int i = 0; i < UnknowMoveStatus; ++i) {
        ObjectConfig* objectConfig = qobject_cast<ObjectConfig*>(mLayoutObjects->itemAt(i)->widget());
        objectConfig->setConfig(drawConfig.mDrawConfig[i]);
    }

    mCheckBoxNegateX->setChecked(drawConfig.mCalculatorConfig.mNegateX);
    mCheckBoxNegateY->setChecked(drawConfig.mCalculatorConfig.mNegateY);
    mLineEditOffset->setText(QString::number(drawConfig.mCalculatorConfig.mOffsetAngle));
}

void ObjectsConfig::config(ObjectsDrawConfig &drawConfig)
{
    for (int i = 0; i < mLayoutObjects->count(); ++i)
    {
        ObjectConfig* objectConfig = qobject_cast<ObjectConfig*>(mLayoutObjects->itemAt(i)->widget());
        if (objectConfig)
        {
            drawConfig.mDrawConfig[objectConfig->moveStatus()] = objectConfig->config();
            drawConfig.mCalculatorConfig.mNegateX = mCheckBoxNegateX->isChecked();
            drawConfig.mCalculatorConfig.mNegateY = mCheckBoxNegateY->isChecked();
            drawConfig.mCalculatorConfig.mOffsetAngle = mLineEditOffset->text().toDouble();
        }
    }
}

ObjectCorrdinateSystemConfigDialog::ObjectCorrdinateSystemConfigDialog(quint8 currentRadarID, ObjectCoordinateSystem *system, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::ObjectCorrdinateSystemConfigDialog),
    mObjectCoordinateSystem(system)
{
    ui->setupUi(this);

    ObjectShowConfig &showConfig = mObjectCoordinateSystem->coordinateSystemShowConfig(currentRadarID);
    auto drawConfig = mObjectCoordinateSystem->coordinateSystemDrawConfig(currentRadarID);
    for (int i = 0; i < FrameTargetCount; ++i)
    {
        ObjectsConfig *objectsConfig = new ObjectsConfig((AnalysisFrameType)i, drawConfig[i], this);
        objectsConfig->setCheckable(true);
        objectsConfig->setChecked(showConfig.mObjectShow[i]);
        ui->verticalLayoutObjectConfg->addWidget(objectsConfig);
    }

    const ObjectCoordinateSettings &settings = mObjectCoordinateSystem->getObjectCoordinateSettings();

    ui->checkBoxDisplayTargetID->setChecked(settings.mDisplayTargetID);
    ui->checkBoxDisplayVelocityFeversal->setChecked(settings.mDisplayVelocityFeversal);
    ui->checkBoxDisplayTrackFrame->setChecked(settings.mDisplayTrackFrame);
    ui->checkBoxDisplay16TrackTarget->setChecked(settings.mDisplay16TrackTarget);
    ui->checkBoxDisplay16TrackFrame->setChecked(settings.mDisplay16TrackFrame);
    ui->checkBoxDisplay16TrackShowV->setChecked(settings.mDisplay16TrackShowV);
    ui->checkBoxDisplayELKOnly->setChecked(settings.mDisplayELKOnly);
    ui->checkBoxDisplayHeSaiLider->setChecked(settings.mDisplayHeSaiLider);
    ui->checkBoxDisplayVelocityAmbiguity->setChecked(settings.mDisplayVelocityAmbiguity);
    ui->checkBoxHighlightedVelocityAmbiguity->setChecked(settings.mDisplayHighlightedVelocityAmbiguity);
    ui->checkBoxDisplayAcceleration->setChecked(settings.mDisplayAcceleration);
    ui->checkBoxStatusFiltrateRaw->setChecked(settings.mStatusFiltrateRaw);
    ui->spinBoxStatusFiltrateRaw->setValue(settings.mStatusFiltrateValueRaw);

    ui->checkBoxDisplayZeroLine->setChecked(settings.mDisplayZeroLine);

    ui->lineEditHMinRange->setText(QString::number(settings.mHorizontalMinRange));
    ui->lineEditHMaxRange->setText(QString::number(settings.mHorizontalMaxRange));
    ui->lineEditHInterval->setText(QString::number(settings.mHorizontalInterval));
    ui->lineEditVMinRange->setText(QString::number(settings.mVerticalMinRange));
    ui->lineEditVMaxRange->setText(QString::number(settings.mVerticalMaxRange));
    ui->lineEditVInterval->setText(QString::number(settings.mVerticalInterval));

    ui->lineEditLaneWidth->setText(QString::number(settings.mLaneWidth));
    ui->lineEditLocalVehicleWidth->setText(QString::number(settings.mLocalVehicleWidth));
    ui->lineEditLocalVehicleLongth->setText(QString::number(settings.mLocalVehicleLength));
    ui->checkBoxDisplayLane->setChecked(settings.mDisplayLane);
    ui->checkBoxDisplaygridLine->setChecked(settings.mDisplayGridLine);

    ui->checkBoxDOWDisplay->setChecked(settings.mDOWDisplay);
    ui->lineEditDOWDistanceOfHeadway->setText(QString::number(settings.mDOWDistanceOfHeadway));
    ui->lineEditDOWDistanceOfBody->setText(QString::number(settings.mDOWDistanceOfBody));
    ui->lineEditDOWWidth->setText(QString::number(settings.mDOWWidth));
    ui->lineEditDOWLength->setText(QString::number(settings.mDOWLength));

    ui->checkBoxBSDDisplay->setChecked(settings.mBSDDisplay);
    ui->lineEditBSDDistanceOfHeadway->setText(QString::number(settings.mBSDDistanceOfHeadway));
    ui->lineEditBSDDistanceOfBody->setText(QString::number(settings.mBSDDistanceOfBody));
    ui->lineEditBSDWidth->setText(QString::number(settings.mBSDWidth));
    ui->lineEditBSDLength->setText(QString::number(settings.mBSDLength));

    ui->checkBoxLCADisplay->setChecked(settings.mLCADisplay);
    ui->lineEditLCADistanceOfHeadway->setText(QString::number(settings.mLCADistanceOfHeadway ));
    ui->lineEditLCADistanceOfBody->setText(QString::number(settings.mLCADistanceOfBody));
    ui->lineEditLCAWidth->setText(QString::number(settings.mLCAWidth));
    ui->lineEditLCALength->setText(QString::number(settings.mLCALength));

    ui->lineEditRearWheelCenterFrontX->setText(QString::number(settings.mRearWheelCenterFrontX));
    ui->lineEditRearWheelCenterFrontY->setText(QString::number(settings.mRearWheelCenterFrontY));
    ui->lineEditRearWheelCenterRearX->setText(QString::number(settings.mRearWheelCenterRearX));
    ui->lineEditRearWheelCenterRearY->setText(QString::number(settings.mRearWheelCenterRearY));

    for (int i = 0; i < MAX_RADAR_COUNT; ++i)
    {
        ui->comboBoxRadarID->addItem(QString::number(i));
    }
    ui->comboBoxRadarID->setCurrentIndex(currentRadarID);
}

ObjectCorrdinateSystemConfigDialog::~ObjectCorrdinateSystemConfigDialog()
{
    delete ui;
}

void ObjectCorrdinateSystemConfigDialog::on_pushButtonApply_clicked()
{
    on_pushButtonSetAxis_clicked();

    quint8 radarID = ui->comboBoxRadarID->currentIndex();
    ObjectShowConfig &showConfig = mObjectCoordinateSystem->coordinateSystemShowConfig(radarID);
    auto drawConfig = mObjectCoordinateSystem->coordinateSystemDrawConfig(radarID);
    for (int i = 0; i < ui->verticalLayoutObjectConfg->count(); ++i)
    {
        switch (i)
        {
        case FrameTargetCount:
            break;
        default:
            ObjectsConfig* objectsConfig = qobject_cast<ObjectsConfig*>(ui->verticalLayoutObjectConfg->itemAt(i)->widget());
            if (objectsConfig)
            {
                objectsConfig->config(drawConfig[i]);
                showConfig.mObjectShow[i] = objectsConfig->isChecked();
            }

            mObjectCoordinateSystem->calculatorConfigChanged(radarID, i, &(drawConfig[i].mCalculatorConfig));
        }
    }
    mObjectCoordinateSystem->clear();
    mObjectCoordinateSystem->update();
}

void ObjectCorrdinateSystemConfigDialog::on_pushButtonOK_clicked()
{
    on_pushButtonApply_clicked();
    accept();
}

void ObjectCorrdinateSystemConfigDialog::on_comboBoxRadarID_currentIndexChanged(int index)
{
    ObjectShowConfig &showConfig = mObjectCoordinateSystem->coordinateSystemShowConfig(index);
    auto drawConfig = mObjectCoordinateSystem->coordinateSystemDrawConfig(index);
    for (int i = 0; i < ui->verticalLayoutObjectConfg->count(); ++i)
    {
        switch (i)
        {
        case FrameTargetCount:
            break;
        default:
            ObjectsConfig* objectsConfig = qobject_cast<ObjectsConfig*>(ui->verticalLayoutObjectConfg->itemAt(i)->widget());
            objectsConfig->setConfig(drawConfig[i]);
            objectsConfig->setChecked(showConfig.mObjectShow[i]);
        }
    }
}

void ObjectCorrdinateSystemConfigDialog::on_pushButtonSetAxis_clicked()
{
    ObjectCoordinateSettings settings;
    settings.mDisplayTargetID = ui->checkBoxDisplayTargetID->isChecked();
    settings.mDisplayVelocityFeversal = ui->checkBoxDisplayVelocityFeversal->isChecked();
    settings.mDisplayTrackFrame = ui->checkBoxDisplayTrackFrame->isChecked();
    settings.mDisplay16TrackTarget = ui->checkBoxDisplay16TrackTarget->isChecked();
    settings.mDisplay16TrackFrame = ui->checkBoxDisplay16TrackFrame->isChecked();
    settings.mDisplay16TrackShowV = ui->checkBoxDisplay16TrackShowV->isChecked();
    settings.mDisplayELKOnly = ui->checkBoxDisplayELKOnly->isChecked();
    settings.mDisplayHeSaiLider = ui->checkBoxDisplayHeSaiLider->isChecked();
    settings.mDisplayVelocityAmbiguity = ui->checkBoxDisplayVelocityAmbiguity->isChecked();
    settings.mDisplayHighlightedVelocityAmbiguity = ui->checkBoxHighlightedVelocityAmbiguity->isChecked();
    settings.mDisplayAcceleration = ui->checkBoxDisplayAcceleration->isChecked();
    settings.mStatusFiltrateRaw = ui->checkBoxStatusFiltrateRaw->isChecked();
    settings.mStatusFiltrateValueRaw = ui->spinBoxStatusFiltrateRaw->value();

    settings.mDisplayZeroLine = ui->checkBoxDisplayZeroLine->isChecked();

    settings.mHorizontalMinRange = ui->lineEditHMinRange->text().toDouble();
    settings.mHorizontalMaxRange = ui->lineEditHMaxRange->text().toDouble();
    settings.mHorizontalInterval = ui->lineEditHInterval->text().toDouble();
    settings.mHorizontalUnit = "m";
    settings.mHorizontalUnitShow = false;
    settings.mVerticalMinRange = ui->lineEditVMinRange->text().toDouble();
    settings.mVerticalMaxRange = ui->lineEditVMaxRange->text().toDouble();
    settings.mVerticalInterval = ui->lineEditVInterval->text().toDouble();
    settings.mVerticalUnit = "m";
    settings.mVerticalUnitShow = false;

    settings.mLocalVehicleWidth = ui->lineEditLocalVehicleWidth->text().toDouble();
    settings.mLocalVehicleLength = ui->lineEditLocalVehicleLongth->text().toDouble();

    settings.mLaneWidth = ui->lineEditLaneWidth->text().toDouble();
    settings.mDisplayLane = ui->checkBoxDisplayLane->isChecked();
    settings.mDisplayGridLine = ui->checkBoxDisplaygridLine->isChecked();

    settings.mDOWDisplay = ui->checkBoxDOWDisplay->isChecked();
    settings.mDOWDistanceOfHeadway = ui->lineEditDOWDistanceOfHeadway->text().toDouble();
    settings.mDOWDistanceOfBody = ui->lineEditDOWDistanceOfBody->text().toDouble();
    settings.mDOWWidth = ui->lineEditDOWWidth->text().toDouble();
    settings.mDOWLength = ui->lineEditDOWLength->text().toDouble();

    settings.mBSDDisplay = ui->checkBoxBSDDisplay->isChecked();
    settings.mBSDDistanceOfHeadway = ui->lineEditBSDDistanceOfHeadway->text().toDouble();
    settings.mBSDDistanceOfBody = ui->lineEditBSDDistanceOfBody->text().toDouble();
    settings.mBSDWidth = ui->lineEditBSDWidth->text().toDouble();
    settings.mBSDLength = ui->lineEditBSDLength->text().toDouble();

    settings.mLCADisplay = ui->checkBoxLCADisplay->isChecked();
    settings.mLCADistanceOfHeadway = ui->lineEditLCADistanceOfHeadway->text().toDouble();
    settings.mLCADistanceOfBody = ui->lineEditLCADistanceOfBody->text().toDouble();
    settings.mLCAWidth = ui->lineEditLCAWidth->text().toDouble();
    settings.mLCALength = ui->lineEditLCALength->text().toDouble();

    settings.mRearWheelCenterFrontX = ui->lineEditRearWheelCenterFrontX->text().toDouble();
    settings.mRearWheelCenterFrontY = ui->lineEditRearWheelCenterFrontY->text().toDouble();
    settings.mRearWheelCenterRearX = ui->lineEditRearWheelCenterRearX->text().toDouble();
    settings.mRearWheelCenterRearY = ui->lineEditRearWheelCenterRearY->text().toDouble();

    mObjectCoordinateSystem->setObjectCoordinateSettings(settings);
}

} // namespace ObjectView
} // namespace Views
