<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>statisticsDialog</class>
 <widget class="QDialog" name="statisticsDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>312</width>
    <height>371</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>检测点概率统计信息</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0">
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout" rowstretch="0,0,0,0,0,0,0,0,0,0,0,0">
      <item row="8" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>检出概率：</string>
        </property>
       </widget>
      </item>
      <item row="7" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>统计时间计数：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QLineEdit" name="lineEdit_maxLng"/>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>速度范围：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="lineEdit_minVel"/>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>滑窗帧数：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>横向范围：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QLineEdit" name="lineEdit_WinSize"/>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="lineEdit_minLng"/>
      </item>
      <item row="10" column="1">
       <widget class="QLabel" name="label_WinMask">
        <property name="text">
         <string>0</string>
        </property>
       </widget>
      </item>
      <item row="9" column="1">
       <widget class="QLabel" name="label_statisticsCnt">
        <property name="text">
         <string>0</string>
        </property>
       </widget>
      </item>
      <item row="8" column="1">
       <widget class="QLabel" name="label_deteProb">
        <property name="text">
         <string>0</string>
        </property>
       </widget>
      </item>
      <item row="9" column="0">
       <widget class="QLabel" name="label_8">
        <property name="text">
         <string>统计帧数：</string>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="QPushButton" name="pushButton_Set">
        <property name="text">
         <string>应用</string>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLineEdit" name="lineEdit_maxVel"/>
      </item>
      <item row="1" column="2">
       <widget class="QLineEdit" name="lineEdit_maxLat"/>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="lineEdit_minLat"/>
      </item>
      <item row="10" column="0">
       <widget class="QLabel" name="label_WinMask1">
        <property name="text">
         <string>滑窗情况：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>纵向范围：</string>
        </property>
       </widget>
      </item>
      <item row="7" column="1" colspan="2">
       <widget class="QLabel" name="label_statisticsTime">
        <property name="text">
         <string>00.00.000</string>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <widget class="QPushButton" name="pushButtonStartSt">
        <property name="text">
         <string>开始统计</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>最大匹配COST：</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QLineEdit" name="lineEditMaxCost"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>选点情况</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2" rowstretch="1,1,1,1,1,1,1,1" rowminimumheight="1,1,1,1,1,1,1,1">
      <item row="0" column="0">
       <widget class="QLabel" name="label_idx">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_Range">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_Vel">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="7" column="0">
       <widget class="QLabel" name="label_Angle">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLabel" name="label_y">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLabel" name="label_x">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="7" column="1">
       <widget class="QLabel" name="label_SNR">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>lineEdit_minVel</tabstop>
  <tabstop>lineEdit_maxVel</tabstop>
  <tabstop>lineEdit_minLat</tabstop>
  <tabstop>lineEdit_maxLat</tabstop>
  <tabstop>lineEdit_minLng</tabstop>
  <tabstop>lineEdit_maxLng</tabstop>
  <tabstop>lineEdit_WinSize</tabstop>
  <tabstop>lineEditMaxCost</tabstop>
  <tabstop>pushButton_Set</tabstop>
  <tabstop>pushButtonStartSt</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
