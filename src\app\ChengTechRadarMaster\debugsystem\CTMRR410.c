﻿/**
* Copyright (c) 2016-2022 ChengTech All rights reserved.
* @file CTMRR410.c
* @brief brief
* <AUTHOR>
* @date 2023-01-05
* @version 1.0.0
* @note
* description
*/

#include "CTMRR410.h"

static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
    uint32_t mask = ((1 << bits) - 1);
    uint32_t extracted = data & mask;
    int32_t sign_extended = (extracted & (1 << (bits - 1))) ? (int)(extracted | (~mask)) : (int)extracted;
    return sign_extended;
}

static uint32_t encode_sign_bit(int32_t data, uint8_t bits) {
	uint32_t const m = 0x1u << (bits - 1);

	return (data & m) ? (data | m) : data;
}

// 0x3f0 length = 32
bool decode_SRR_VehicleInfo(CT_SRR_VehicleInfo_t *userData, uint8_t *data, int length) {
	if (length != 32) {
		return false;
	}

	SRR_VehicleInfo_t *p = (SRR_VehicleInfo_t*)data;

	userData->Veh_YawRate = p->Veh_YawRate * 0.00024 - 2.093;
	userData->Veh_SwRctaFunc = p->Veh_SwRctaFunc;
	userData->Veh_SwRctbFunc = p->Veh_SwRctbFunc;
	userData->Veh_SwRcwFunc = p->Veh_SwRcwFunc;
	userData->Veh_SwBSDFunc = p->Veh_SwBSDFunc;
	userData->Veh_SwDowFunc = p->Veh_SwDowFunc;
	userData->Veh_SwMainFunc = p->Veh_SwMainFunc;
	userData->Veh_SwFctaFunc = p->Veh_SwFctaFunc;
	userData->Veh_SwFctbFunc = p->Veh_SwFctbFunc;
	userData->Veh_Speed = p->Veh_Speed * 0.05625;
	userData->Veh_DoorFrontLe = p->Veh_DoorFrontLe;
	userData->Veh_DoorFrontRi = p->Veh_DoorFrontRi;
	userData->Veh_DoorRearLe = p->Veh_DoorRearLe;
	userData->Veh_DoorRearRi = p->Veh_DoorRearRi;
	userData->Veh_Gear = p->Veh_Gear;
	userData->Veh_KeyState = p->Veh_KeyState;
	userData->Veh_SecurityLock = p->Veh_SecurityLock;
	userData->Veh_TurnLightLe = p->Veh_TurnLightLe;
	userData->Veh_TurnLightRi = p->Veh_TurnLightRi;
	userData->Veh_BrkPedalSts = p->Veh_BrkPedalSts;
	userData->Veh_WhlSpdDirFrontLe = p->Veh_WhlSpdDirFrontLe;
	userData->Veh_WhlSpdDirFrontRi = p->Veh_WhlSpdDirFrontRi;
	userData->Veh_WhlSpdDirRearLe = p->Veh_WhlSpdDirRearLe;
	userData->Veh_WhlSpdDirRearRi = p->Veh_WhlSpdDirRearRi;
	userData->Veh_AccPedalActLevel = p->Veh_AccPedalActLevel;
	userData->Veh_BrkPedalActLevel = p->Veh_BrkPedalActLevel;
	userData->Veh_TrailerSts = p->Veh_TrailerSts;
	userData->Veh_ESPFailSts = p->Veh_ESPFailSts;
	userData->Veh_LgtAccel = p->Veh_LgtAccel * 0.00098 - 21.592;
	userData->Veh_LatAccel = p->Veh_LatAccel * 0.00098 - 21.592;
	userData->Veh_WhlSpdFrontLe = p->Veh_WhlSpdFrontLe * 0.05625;
	userData->Veh_WhlSpdFrontRi = p->Veh_WhlSpdFrontRi * 0.05625;
	userData->Veh_WhlSpdRearLe = p->Veh_WhlSpdRearLe * 0.05625;
	userData->Veh_WhlSpdRearRi = p->Veh_WhlSpdRearRi * 0.05625;
	userData->Veh_SteerWheelAngle = p->Veh_SteerWheelAngle * 0.2 - 738;
	userData->RollingCntMsgVehInfo01 = p->RollingCntMsgVehInfo01;
	userData->Veh_Radius = p->Veh_Radius * 0.25 - 32767;
	userData->ChecksumMsgVehInfo01 = p->ChecksumMsgVehInfo01;

	return true;
}

// 0x3f0 length = 32
bool encode_SRR_VehicleInfo(CT_SRR_VehicleInfo_t *userData, uint8_t *data, int length) {
	if (length != 32) {
		return false;
	}

	SRR_VehicleInfo_t *p = (SRR_VehicleInfo_t*)data;

	p->Veh_YawRate = (userData->Veh_YawRate + 2.093) / 0.00024;
	p->Veh_SwRctaFunc = userData->Veh_SwRctaFunc;
	p->Veh_SwRctbFunc = userData->Veh_SwRctbFunc;
	p->Veh_SwRcwFunc = userData->Veh_SwRcwFunc;
	p->Veh_SwBSDFunc = userData->Veh_SwBSDFunc;
	p->Veh_SwDowFunc = userData->Veh_SwDowFunc;
	p->Veh_SwMainFunc = userData->Veh_SwMainFunc;
	p->Veh_SwFctaFunc = userData->Veh_SwFctaFunc;
	p->Veh_SwFctbFunc = userData->Veh_SwFctbFunc;
	p->Veh_Speed = userData->Veh_Speed / 0.05625;
	p->Veh_DoorFrontLe = userData->Veh_DoorFrontLe;
	p->Veh_DoorFrontRi = userData->Veh_DoorFrontRi;
	p->Veh_DoorRearLe = userData->Veh_DoorRearLe;
	p->Veh_DoorRearRi = userData->Veh_DoorRearRi;
	p->Veh_Gear = userData->Veh_Gear;
	p->Veh_KeyState = userData->Veh_KeyState;
	p->Veh_SecurityLock = userData->Veh_SecurityLock;
	p->Veh_TurnLightLe = userData->Veh_TurnLightLe;
	p->Veh_TurnLightRi = userData->Veh_TurnLightRi;
	p->Veh_BrkPedalSts = userData->Veh_BrkPedalSts;
	p->Veh_WhlSpdDirFrontLe = userData->Veh_WhlSpdDirFrontLe;
	p->Veh_WhlSpdDirFrontRi = userData->Veh_WhlSpdDirFrontRi;
	p->Veh_WhlSpdDirRearLe = userData->Veh_WhlSpdDirRearLe;
	p->Veh_WhlSpdDirRearRi = userData->Veh_WhlSpdDirRearRi;
	p->Veh_AccPedalActLevel = userData->Veh_AccPedalActLevel;
	p->Veh_BrkPedalActLevel = userData->Veh_BrkPedalActLevel;
	p->Veh_TrailerSts = userData->Veh_TrailerSts;
	p->Veh_ESPFailSts = userData->Veh_ESPFailSts;
	p->Veh_LgtAccel = (userData->Veh_LgtAccel + 21.592) / 0.00098;
	p->Veh_LatAccel = (userData->Veh_LatAccel + 21.592) / 0.00098;
	p->Veh_WhlSpdFrontLe = userData->Veh_WhlSpdFrontLe / 0.05625;
	p->Veh_WhlSpdFrontRi = userData->Veh_WhlSpdFrontRi / 0.05625;
	p->Veh_WhlSpdRearLe = userData->Veh_WhlSpdRearLe / 0.05625;
	p->Veh_WhlSpdRearRi = userData->Veh_WhlSpdRearRi / 0.05625;
	p->Veh_SteerWheelAngle = (userData->Veh_SteerWheelAngle + 738) / 0.2;
	p->RollingCntMsgVehInfo01 = userData->RollingCntMsgVehInfo01;
	p->Veh_Radius = (userData->Veh_Radius + 32767) / 0.25;
	p->ChecksumMsgVehInfo01 = userData->ChecksumMsgVehInfo01;

	return true;
}
// 0x400 length = 16
bool decode_SRR_RawHeader(CT_SRR_RawHeader_t *userData, uint8_t *data, int length) {
	if (length != 16) {
		return false;
	}

	SRR_RawHeader_t *p = (SRR_RawHeader_t*)data;

	userData->RawHeaderProtVer = p->RawHeaderProtVer;
	userData->RawHeaderNoiseCurrent = p->RawHeaderNoiseCurrent * 0.5 - 100;
	userData->RawHeaderNoiseGlobal = p->RawHeaderNoiseGlobal * 0.5 - 100;
	userData->RawHeaderObjNum = p->RawHeaderObjNum;
	userData->RawHeaderFuncCalcTime = p->RawHeaderFuncCalcTime;
	userData->RawHeaderRspTaskCycleTime = p->RawHeaderRspTaskCycleTime;
	userData->RawHeaderDataSource = p->RawHeaderDataSource;
	userData->RawHeaderObjNumAfterCFAR = p->RawHeaderObjNumAfterCFAR;
	userData->RawHeaderObjNumAfterFilter = p->RawHeaderObjNumAfterFilter;
	userData->RawHeaderCurrentFrameModeIdx = p->RawHeaderCurrentFrameModeIdx;
	userData->RawHeaderFrameModeNum = p->RawHeaderFrameModeNum;
	userData->RawHeaderRollingCnt = p->RawHeaderRollingCnt;

	return true;
}

// 0x400 length = 16
bool encode_SRR_RawHeader(CT_SRR_RawHeader_t *userData, uint8_t *data, int length) {
	if (length != 16) {
		return false;
	}

	SRR_RawHeader_t *p = (SRR_RawHeader_t*)data;

	p->RawHeaderProtVer = userData->RawHeaderProtVer;
	p->RawHeaderNoiseCurrent = (userData->RawHeaderNoiseCurrent + 100) / 0.5;
	p->RawHeaderNoiseGlobal = (userData->RawHeaderNoiseGlobal + 100) / 0.5;
	p->RawHeaderObjNum = userData->RawHeaderObjNum;
	p->RawHeaderFuncCalcTime = userData->RawHeaderFuncCalcTime;
	p->RawHeaderRspTaskCycleTime = userData->RawHeaderRspTaskCycleTime;
	p->RawHeaderDataSource = userData->RawHeaderDataSource;
	p->RawHeaderObjNumAfterCFAR = userData->RawHeaderObjNumAfterCFAR;
	p->RawHeaderObjNumAfterFilter = userData->RawHeaderObjNumAfterFilter;
	p->RawHeaderCurrentFrameModeIdx = userData->RawHeaderCurrentFrameModeIdx;
	p->RawHeaderFrameModeNum = userData->RawHeaderFrameModeNum;
	p->RawHeaderRollingCnt = userData->RawHeaderRollingCnt;

	return true;
}
// 0x410 length = 64
bool decode_SRR_RawDetections(CT_SRR_RawDetections_t *userData, uint8_t *data, int length) {
	if (length != 64) {
		return false;
	}

	SRR_RawDetections_t *p = (SRR_RawDetections_t*)data;

	userData->RawObjectRange01 = p->RawObjectRange01 * 0.01;
	userData->RawObjectAzimuth01 = p->RawObjectAzimuth01 * 0.01 - 163.84;
	userData->RawObjectVelocity01 = p->RawObjectVelocity01 * 0.01 - 81.92;
	userData->RawObjectElevationAngle01 = p->RawObjectElevationAngle01 * 0.02 - 81.92;
	userData->RawObjectProbOfExist01 = p->RawObjectProbOfExist01;
	userData->RawObjectRCS01 = p->RawObjectRCS01 * 0.5 - 30;
	userData->RawObjectSNR01 = p->RawObjectSNR01 * 0.5;
	userData->RawObjectStatus01 = p->RawObjectStatus01;
	userData->RawObjectDopplerVelocity01 = p->RawObjectDopplerVelocity01;
	userData->RawObjectSubFrameID01 = p->RawObjectSubFrameID01;
	userData->RawObjectAssociatedTrkId01 = p->RawObjectAssociatedTrkId01;
	userData->RawObjectGroupID01 = p->RawObjectGroupID01;
	userData->RawObjectRange02 = p->RawObjectRange02 * 0.01;
	userData->RawObjectAzimuth02 = p->RawObjectAzimuth02 * 0.01 - 163.84;
	userData->RawObjectVelocity02 = p->RawObjectVelocity02 * 0.01 - 81.92;
	userData->RawObjectElevationAngle02 = p->RawObjectElevationAngle02 * 0.02 - 81.92;
	userData->RawObjectProbOfExist02 = p->RawObjectProbOfExist02;
	userData->RawObjectRCS02 = p->RawObjectRCS02 * 0.5;
	userData->RawObjectSNR02 = p->RawObjectSNR02 * 0.5;
	userData->RawObjectStatus02 = p->RawObjectStatus02;
	userData->RawObjectDopplerVelocity02 = p->RawObjectDopplerVelocity02;
	userData->RawObjectSubFrameID02 = p->RawObjectSubFrameID02;
	userData->RawObjectAssociatedTrkId02 = p->RawObjectAssociatedTrkId02;
	userData->RawObjectGroupID02 = p->RawObjectGroupID02;
	userData->RawObjectRange03 = p->RawObjectRange03 * 0.01;
	userData->RawObjectAzimuth03 = p->RawObjectAzimuth03 * 0.01 - 163.84;
	userData->RawObjectVelocity03 = p->RawObjectVelocity03 * 0.01 - 81.92;
	userData->RawObjectElevationAngle03 = p->RawObjectElevationAngle03 * 0.02 - 81.92;
	userData->RawObjectProbOfExist03 = p->RawObjectProbOfExist03;
	userData->RawObjectRCS03 = p->RawObjectRCS03 * 0.5;
	userData->RawObjectSNR03 = p->RawObjectSNR03 * 0.5;
	userData->RawObjectStatus03 = p->RawObjectStatus03;
	userData->RawObjectDopplerVelocity03 = p->RawObjectDopplerVelocity03;
	userData->RawObjectSubFrameID03 = p->RawObjectSubFrameID03;
	userData->RawObjectAssociatedTrkId03 = p->RawObjectAssociatedTrkId03;
	userData->RawObjectGroupID03 = p->RawObjectGroupID03;
	userData->RawObjectRange04 = p->RawObjectRange04 * 0.01;
	userData->RawObjectAzimuth04 = p->RawObjectAzimuth04 * 0.01 - 163.84;
	userData->RawObjectVelocity04 = p->RawObjectVelocity04 * 0.01 - 81.92;
	userData->RawObjectElevationAngle04 = p->RawObjectElevationAngle04 * 0.02 - 81.92;
	userData->RawObjectProbOfExist04 = p->RawObjectProbOfExist04;
	userData->RawObjectRCS04 = p->RawObjectRCS04 * 0.5;
	userData->RawObjectSNR04 = p->RawObjectSNR04 * 0.5;
	userData->RawObjectStatus04 = p->RawObjectStatus04;
	userData->RawObjectDopplerVelocity04 = p->RawObjectDopplerVelocity04;
	userData->RawObjectSubFrameID04 = p->RawObjectSubFrameID04;
	userData->RawObjectAssociatedTrkId04 = p->RawObjectAssociatedTrkId04;
	userData->RawObjectGroupID04 = p->RawObjectGroupID04;
	userData->RawObjectRollingCnt01 = p->RawObjectRollingCnt01;

	return true;
}

// 0x410 length = 64
bool encode_SRR_RawDetections(CT_SRR_RawDetections_t *userData, uint8_t *data, int length) {
	if (length != 64) {
		return false;
	}

	SRR_RawDetections_t *p = (SRR_RawDetections_t*)data;

	p->RawObjectRange01 = userData->RawObjectRange01 / 0.01;
	p->RawObjectAzimuth01 = (userData->RawObjectAzimuth01 + 163.84) / 0.01;
	p->RawObjectVelocity01 = (userData->RawObjectVelocity01 + 81.92) / 0.01;
	p->RawObjectElevationAngle01 = (userData->RawObjectElevationAngle01 + 81.92) / 0.02;
	p->RawObjectProbOfExist01 = userData->RawObjectProbOfExist01;
	p->RawObjectRCS01 = (userData->RawObjectRCS01 + 30) / 0.5;
	p->RawObjectSNR01 = userData->RawObjectSNR01 / 0.5;
	p->RawObjectStatus01 = userData->RawObjectStatus01;
	p->RawObjectDopplerVelocity01 = userData->RawObjectDopplerVelocity01;
	p->RawObjectSubFrameID01 = userData->RawObjectSubFrameID01;
	p->RawObjectAssociatedTrkId01 = userData->RawObjectAssociatedTrkId01;
	p->RawObjectGroupID01 = userData->RawObjectGroupID01;
	p->RawObjectRange02 = userData->RawObjectRange02 / 0.01;
	p->RawObjectAzimuth02 = (userData->RawObjectAzimuth02 + 163.84) / 0.01;
	p->RawObjectVelocity02 = (userData->RawObjectVelocity02 + 81.92) / 0.01;
	p->RawObjectElevationAngle02 = (userData->RawObjectElevationAngle02 + 81.92) / 0.02;
	p->RawObjectProbOfExist02 = userData->RawObjectProbOfExist02;
	p->RawObjectRCS02 = userData->RawObjectRCS02 / 0.5;
	p->RawObjectSNR02 = userData->RawObjectSNR02 / 0.5;
	p->RawObjectStatus02 = userData->RawObjectStatus02;
	p->RawObjectDopplerVelocity02 = userData->RawObjectDopplerVelocity02;
	p->RawObjectSubFrameID02 = userData->RawObjectSubFrameID02;
	p->RawObjectAssociatedTrkId02 = userData->RawObjectAssociatedTrkId02;
	p->RawObjectGroupID02 = userData->RawObjectGroupID02;
	p->RawObjectRange03 = userData->RawObjectRange03 / 0.01;
	p->RawObjectAzimuth03 = (userData->RawObjectAzimuth03 + 163.84) / 0.01;
	p->RawObjectVelocity03 = (userData->RawObjectVelocity03 + 81.92) / 0.01;
	p->RawObjectElevationAngle03 = (userData->RawObjectElevationAngle03 + 81.92) / 0.02;
	p->RawObjectProbOfExist03 = userData->RawObjectProbOfExist03;
	p->RawObjectRCS03 = userData->RawObjectRCS03 / 0.5;
	p->RawObjectSNR03 = userData->RawObjectSNR03 / 0.5;
	p->RawObjectStatus03 = userData->RawObjectStatus03;
	p->RawObjectDopplerVelocity03 = userData->RawObjectDopplerVelocity03;
	p->RawObjectSubFrameID03 = userData->RawObjectSubFrameID03;
	p->RawObjectAssociatedTrkId03 = userData->RawObjectAssociatedTrkId03;
	p->RawObjectGroupID03 = userData->RawObjectGroupID03;
	p->RawObjectRange04 = userData->RawObjectRange04 / 0.01;
	p->RawObjectAzimuth04 = (userData->RawObjectAzimuth04 + 163.84) / 0.01;
	p->RawObjectVelocity04 = (userData->RawObjectVelocity04 + 81.92) / 0.01;
	p->RawObjectElevationAngle04 = (userData->RawObjectElevationAngle04 + 81.92) / 0.02;
	p->RawObjectProbOfExist04 = userData->RawObjectProbOfExist04;
	p->RawObjectRCS04 = userData->RawObjectRCS04 / 0.5;
	p->RawObjectSNR04 = userData->RawObjectSNR04 / 0.5;
	p->RawObjectStatus04 = userData->RawObjectStatus04;
	p->RawObjectDopplerVelocity04 = userData->RawObjectDopplerVelocity04;
	p->RawObjectSubFrameID04 = userData->RawObjectSubFrameID04;
	p->RawObjectAssociatedTrkId04 = userData->RawObjectAssociatedTrkId04;
	p->RawObjectGroupID04 = userData->RawObjectGroupID04;
	p->RawObjectRollingCnt01 = userData->RawObjectRollingCnt01;

	return true;
}
// 0x430 length = 8
bool decode_SRR_ObjectHeader(CT_SRR_ObjectHeader_t *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	SRR_ObjectHeader_t *p = (SRR_ObjectHeader_t*)data;

	userData->ObjHeaderChecksum = p->ObjHeaderChecksum;
	userData->ObjHeaderObjNum = p->ObjHeaderObjNum;
	userData->ObjHeaderMeasCnt = p->ObjHeaderMeasCnt;
	userData->TrkHeaderFuncCalcTime = p->TrkHeaderFuncCalcTime;
	userData->TrkHeaderTaskCycleTime = p->TrkHeaderTaskCycleTime;
	userData->ObjHeaderProtVer = p->ObjHeaderProtVer;
	userData->ObjHeaderRollingCnt = p->ObjHeaderRollingCnt;

	return true;
}

// 0x430 length = 8
bool encode_SRR_ObjectHeader(CT_SRR_ObjectHeader_t *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	SRR_ObjectHeader_t *p = (SRR_ObjectHeader_t*)data;

	p->ObjHeaderChecksum = userData->ObjHeaderChecksum;
	p->ObjHeaderObjNum = userData->ObjHeaderObjNum;
	p->ObjHeaderMeasCnt = userData->ObjHeaderMeasCnt;
	p->TrkHeaderFuncCalcTime = userData->TrkHeaderFuncCalcTime;
	p->TrkHeaderTaskCycleTime = userData->TrkHeaderTaskCycleTime;
	p->ObjHeaderProtVer = userData->ObjHeaderProtVer;
	p->ObjHeaderRollingCnt = userData->ObjHeaderRollingCnt;

	return true;
}
// 0x440 length = 24
bool decode_SRR_ObjectList(CT_SRR_ObjectList_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	SRR_ObjectList_t *p = (SRR_ObjectList_t*)data;

	userData->ObjectChecksum = p->ObjectChecksum;
	userData->ObjectID = p->ObjectID;
	userData->ObjectDistLong = p->ObjectDistLong * 0.05 - 409.6;
	userData->ObjectDistLat = p->ObjectDistLat * 0.05 - 102.4;
	userData->ObjectVrelLong = p->ObjectVrelLong * 0.04 - 163.84;
	userData->ObjectClass = p->ObjectClass;
	userData->ObjectDistLongRms = p->ObjectDistLongRms * 0.375;
	userData->ObjectVrelLat = p->ObjectVrelLat * 0.04 - 163.84;
	userData->ObjectArelLong = p->ObjectArelLong * 0.01 - 10.24;
	userData->ObjectArelLat = p->ObjectArelLat * 0.01 - 10.24;
	userData->ObjectLength = p->ObjectLength * 0.2;
	userData->ObjectWidth = p->ObjectWidth * 0.2;
	userData->ObjectMeasState = p->ObjectMeasState;
	userData->ObjectRCS = p->ObjectRCS * 0.5 - 128;
	userData->ObjectDynProp = p->ObjectDynProp;
	userData->ObjectHeight = p->ObjectHeight * 0.05;
	userData->ObjectDistLatRms = p->ObjectDistLatRms * 0.375;
	userData->ObjectVrelLongRms = p->ObjectVrelLongRms * 0.375;
	userData->ObjectVrelLatRms = p->ObjectVrelLatRms * 0.375;
	userData->ObjectArelLongRms = p->ObjectArelLongRms * 0.375;
	userData->ObjectArelLatRms = p->ObjectArelLatRms * 0.375;
	userData->ObjectOrientationAngle = p->ObjectOrientationAngle * 0.4 - 180;
	userData->ObjectProbOfExist = p->ObjectProbOfExist * 3.2258;
	userData->ObjectDistAltitude = p->ObjectDistAltitude * 0.05 - 1.75;
	userData->ObjectRollingCnt = p->ObjectRollingCnt;

	return true;
}

// 0x440 length = 24
bool encode_SRR_ObjectList(CT_SRR_ObjectList_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	SRR_ObjectList_t *p = (SRR_ObjectList_t*)data;

	p->ObjectChecksum = userData->ObjectChecksum;
	p->ObjectID = userData->ObjectID;
	p->ObjectDistLong = (userData->ObjectDistLong + 409.6) / 0.05;
	p->ObjectDistLat = (userData->ObjectDistLat + 102.4) / 0.05;
	p->ObjectVrelLong = (userData->ObjectVrelLong + 163.84) / 0.04;
	p->ObjectClass = userData->ObjectClass;
	p->ObjectDistLongRms = userData->ObjectDistLongRms / 0.375;
	p->ObjectVrelLat = (userData->ObjectVrelLat + 163.84) / 0.04;
	p->ObjectArelLong = (userData->ObjectArelLong + 10.24) / 0.01;
	p->ObjectArelLat = (userData->ObjectArelLat + 10.24) / 0.01;
	p->ObjectLength = userData->ObjectLength / 0.2;
	p->ObjectWidth = userData->ObjectWidth / 0.2;
	p->ObjectMeasState = userData->ObjectMeasState;
	p->ObjectRCS = (userData->ObjectRCS + 128) / 0.5;
	p->ObjectDynProp = userData->ObjectDynProp;
	p->ObjectHeight = userData->ObjectHeight / 0.05;
	p->ObjectDistLatRms = userData->ObjectDistLatRms / 0.375;
	p->ObjectVrelLongRms = userData->ObjectVrelLongRms / 0.375;
	p->ObjectVrelLatRms = userData->ObjectVrelLatRms / 0.375;
	p->ObjectArelLongRms = userData->ObjectArelLongRms / 0.375;
	p->ObjectArelLatRms = userData->ObjectArelLatRms / 0.375;
	p->ObjectOrientationAngle = (userData->ObjectOrientationAngle + 180) / 0.4;
	p->ObjectProbOfExist = userData->ObjectProbOfExist / 3.2258;
	p->ObjectDistAltitude = (userData->ObjectDistAltitude + 1.75) / 0.05;
	p->ObjectRollingCnt = userData->ObjectRollingCnt;

	return true;
}
// 0x4c0 length = 24
bool decode_SRR_AlarmObjInfo(CT_SRR_AlarmObjInfo_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	SRR_AlarmObjInfo_t *p = (SRR_AlarmObjInfo_t*)data;

	userData->DrvFunc_AlarmModule = p->DrvFunc_AlarmModule;
	userData->AlarmBsdObjID = p->AlarmBsdObjID;
	userData->AlarmLcaObjID = p->AlarmLcaObjID;
	userData->AlarmLcaObjTtc = p->AlarmLcaObjTtc * 0.02;
	userData->AlarmDowObjID = p->AlarmDowObjID;
	userData->AlarmDowObjTtc = p->AlarmDowObjTtc * 0.02;
	userData->AlarmRcwObjID = p->AlarmRcwObjID;
	userData->AlarmRcwObjTtc = p->AlarmRcwObjTtc * 0.02;
	userData->AlarmRctaObjID = p->AlarmRctaObjID;
	userData->AlarmRctaObjTtc = p->AlarmRctaObjTtc * 0.02;
	userData->AlarmRctbObjID = p->AlarmRctbObjID;
	userData->AlarmRctbObjTtc = p->AlarmRctbObjTtc * 0.02;
	userData->AlarmFctaObjID = p->AlarmFctaObjID;
	userData->AlarmFctaObjTtc = p->AlarmFctaObjTtc * 0.02;
	userData->AlarmFctbObjID = p->AlarmFctbObjID;
	userData->AlarmFctbObjTtc = p->AlarmFctbObjTtc * 0.02;
	userData->AlarmBsdLevel = p->AlarmBsdLevel;
	userData->AlarmLcaLevel = p->AlarmLcaLevel;
	userData->AlarmDowLevelFront = p->AlarmDowLevelFront;
	userData->AlarmDowLevelRear = p->AlarmDowLevelRear;
	userData->AlarmRcwLevel = p->AlarmRcwLevel;
	userData->AlarmRctaLevel = p->AlarmRctaLevel;
	userData->AlarmFctaLevel = p->AlarmFctaLevel;
	userData->AlarmBsdState = p->AlarmBsdState;
	userData->AlarmLCAState = p->AlarmLCAState;
	userData->AlarmDOWState = p->AlarmDOWState;
	userData->AlarmRCTAState = p->AlarmRCTAState;
	userData->AlarmRCTBState = p->AlarmRCTBState;
	userData->AlarmFCTAState = p->AlarmFCTAState;
	userData->AlarmFCTBState = p->AlarmFCTBState;
	userData->AlarmRCWState = p->AlarmRCWState;

	return true;
}

// 0x4c0 length = 24
bool encode_SRR_AlarmObjInfo(CT_SRR_AlarmObjInfo_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	SRR_AlarmObjInfo_t *p = (SRR_AlarmObjInfo_t*)data;

	p->DrvFunc_AlarmModule = userData->DrvFunc_AlarmModule;
	p->AlarmBsdObjID = userData->AlarmBsdObjID;
	p->AlarmLcaObjID = userData->AlarmLcaObjID;
	p->AlarmLcaObjTtc = userData->AlarmLcaObjTtc / 0.02;
	p->AlarmDowObjID = userData->AlarmDowObjID;
	p->AlarmDowObjTtc = userData->AlarmDowObjTtc / 0.02;
	p->AlarmRcwObjID = userData->AlarmRcwObjID;
	p->AlarmRcwObjTtc = userData->AlarmRcwObjTtc / 0.02;
	p->AlarmRctaObjID = userData->AlarmRctaObjID;
	p->AlarmRctaObjTtc = userData->AlarmRctaObjTtc / 0.02;
	p->AlarmRctbObjID = userData->AlarmRctbObjID;
	p->AlarmRctbObjTtc = userData->AlarmRctbObjTtc / 0.02;
	p->AlarmFctaObjID = userData->AlarmFctaObjID;
	p->AlarmFctaObjTtc = userData->AlarmFctaObjTtc / 0.02;
	p->AlarmFctbObjID = userData->AlarmFctbObjID;
	p->AlarmFctbObjTtc = userData->AlarmFctbObjTtc / 0.02;
	p->AlarmBsdLevel = userData->AlarmBsdLevel;
	p->AlarmLcaLevel = userData->AlarmLcaLevel;
	p->AlarmDowLevelFront = userData->AlarmDowLevelFront;
	p->AlarmDowLevelRear = userData->AlarmDowLevelRear;
	p->AlarmRcwLevel = userData->AlarmRcwLevel;
	p->AlarmRctaLevel = userData->AlarmRctaLevel;
	p->AlarmFctaLevel = userData->AlarmFctaLevel;
	p->AlarmBsdState = userData->AlarmBsdState;
	p->AlarmLCAState = userData->AlarmLCAState;
	p->AlarmDOWState = userData->AlarmDOWState;
	p->AlarmRCTAState = userData->AlarmRCTAState;
	p->AlarmRCTBState = userData->AlarmRCTBState;
	p->AlarmFCTAState = userData->AlarmFCTAState;
	p->AlarmFCTBState = userData->AlarmFCTBState;
	p->AlarmRCWState = userData->AlarmRCWState;

	return true;
}
// 0x4f0 length = 24
bool decode_SRR_EndFrame(CT_SRR_EndFrame_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	SRR_EndFrame_t *p = (SRR_EndFrame_t*)data;

	userData->EndFrameCheckSum = p->EndFrameCheckSum;
	userData->EndFrameEOLInstallAngle = p->EndFrameEOLInstallAngle * 0.1 - 102.4;
	userData->EndFrameAutoCalAngleOffset = p->EndFrameAutoCalAngleOffset * 0.1 - 102.4;
	userData->EndFrameInterTime = p->EndFrameInterTime;
	userData->EndFrameFuncCalcTime = p->EndFrameFuncCalcTime;
	userData->EndFrameRoadSideDist = p->EndFrameRoadSideDist * 0.05 - 25;
	userData->EndFrameRollingCnt = p->EndFrameRollingCnt;
	userData->EndFrameTimeTick = p->EndFrameTimeTick;
	userData->EndFrameMeasCnt = p->EndFrameMeasCnt;

	return true;
}

// 0x4f0 length = 24
bool encode_SRR_EndFrame(CT_SRR_EndFrame_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	SRR_EndFrame_t *p = (SRR_EndFrame_t*)data;

	p->EndFrameCheckSum = userData->EndFrameCheckSum;
	p->EndFrameEOLInstallAngle = (userData->EndFrameEOLInstallAngle + 102.4) / 0.1;
	p->EndFrameAutoCalAngleOffset = (userData->EndFrameAutoCalAngleOffset + 102.4) / 0.1;
	p->EndFrameInterTime = userData->EndFrameInterTime;
	p->EndFrameFuncCalcTime = userData->EndFrameFuncCalcTime;
	p->EndFrameRoadSideDist = (userData->EndFrameRoadSideDist + 25) / 0.05;
	p->EndFrameRollingCnt = userData->EndFrameRollingCnt;
	p->EndFrameTimeTick = userData->EndFrameTimeTick;
	p->EndFrameMeasCnt = userData->EndFrameMeasCnt;

	return true;
}

bool proc_CTMRR410(CTMRR410_t *userData, Frame *p) {
	bool ret = false;

	switch (p->id) {
	case MSG_ID3f0_SRR_VEHICLEINFO:
		ret = decode_SRR_VehicleInfo(&userData->_SRR_VehicleInfo, p->data, p->dlc);
		break;
	case MSG_ID400_SRR_RAWHEADER:
		ret = decode_SRR_RawHeader(&userData->_SRR_RawHeader, p->data, p->dlc);
		break;
	case MSG_ID410_SRR_RAWDETECTIONS:
		ret = decode_SRR_RawDetections(&userData->_SRR_RawDetections, p->data, p->dlc);
		break;
	case MSG_ID430_SRR_OBJECTHEADER:
		ret = decode_SRR_ObjectHeader(&userData->_SRR_ObjectHeader, p->data, p->dlc);
		break;
	case MSG_ID440_SRR_OBJECTLIST:
		ret = decode_SRR_ObjectList(&userData->_SRR_ObjectList, p->data, p->dlc);
		break;
	case MSG_ID4c0_SRR_ALARMOBJINFO:
		ret = decode_SRR_AlarmObjInfo(&userData->_SRR_AlarmObjInfo, p->data, p->dlc);
		break;
	case MSG_ID4f0_SRR_ENDFRAME:
		ret = decode_SRR_EndFrame(&userData->_SRR_EndFrame, p->data, p->dlc);
		break;
	default:
		return false;
		break;
	}

	return ret;
}


