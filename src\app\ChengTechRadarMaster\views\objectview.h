﻿#ifndef OBJECTVIEW_H
#define OBJECTVIEW_H

#include <QWidget>

#include "analysis/analysisdata.h"

namespace Ui {
class ObjectView;
}

namespace Views {
namespace ObjectView {

class ObjectCoordinateSystem;

class ObjectView : public QWidget
{
    Q_OBJECT

public:
    explicit ObjectView(QWidget *parent = nullptr);
    ~ObjectView();

    ObjectCoordinateSystem *coordinateSystem();

signals:
    void closeObjectView();
    void draw();

private slots:

    void on_actionShowConfig_triggered();

    void on_actionClearObject_triggered();

    void on_actionPause_triggered(bool checked);

private:
    Ui::ObjectView *ui;
};

} // namespace ObjectView
} // namespace Views

#endif // OBJECTVIEW_H
