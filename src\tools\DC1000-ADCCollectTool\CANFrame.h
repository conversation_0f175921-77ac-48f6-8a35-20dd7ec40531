﻿#ifndef CANFRAME_H
#define CANFRAME_H

#include <memory>

typedef struct CANFrame
{
    CANFrame(){}
    CANFrame(int c, unsigned int i, unsigned char *d, int l, bool f, bool r)
        : channel(c), id(i), len(l), fd(f), rx(r)
    {
        memcpy(data, d, len);
    }
    int channel{0};
    unsigned int id{0};
    unsigned char data[64];
    int len{0};
    bool fd{false};
    bool rx{true};
}CANFrame;

#endif // CANFRAME_H
