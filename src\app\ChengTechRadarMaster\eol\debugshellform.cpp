#include "debugshellform.h"
#include "ui_debugshellform.h"

#include <QDebug>
#include <QKeyEvent>
#include <QTextBlock>
#include <QListWidgetItem>
#include <QMenu>
#include <QInputDialog>

DebugShellForm::DebugShellForm(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::DebugShellForm)
{
    ui->setupUi(this);

    ui->splitter->setStretchFactor(0, 8);
    ui->splitter->setStretchFactor(1, 2);
    ui->pushButtonCmdList->setVisible( false );
    ui->listWidgetCmd->setContextMenuPolicy( Qt::CustomContextMenu );

    connect( ui->shellTextEdit, &ShellTextEdit::shellCmd, this, &DebugShellForm::shellCmd );
    connect( ui->shellTextEdit, &ShellTextEdit::historyCmdChanged, this, &DebugShellForm::refreshHistoryCmdList );
    connect( this, &DebugShellForm::clearHistoryCmd, ui->shellTextEdit, &ShellTextEdit::clearHistoryCmd );
    connect( this, &DebugShellForm::quickCmdChanged, ui->shellTextEdit, &ShellTextEdit::setQuickCmd );
    connect( ui->shellTextEdit, &ShellTextEdit::quickCmdChanged, this, &DebugShellForm::refreshQuickCmdList );

    refreshQuickCmdList();
}

DebugShellForm::~DebugShellForm()
{
    delete ui;
}

void DebugShellForm::showEvent(QShowEvent *event)
{
    ui->shellTextEdit->setFocus();
    QWidget::showEvent( event );
}

void DebugShellForm::showText(const QString &text)
{
    ui->shellTextEdit->showText( text );
}

void DebugShellForm::refreshHistoryCmdList()
{
    if( ui->comboBoxCmdList->currentIndex() != 1 )
        return;
    QStringList cmds;
    ui->shellTextEdit->GetHistoryCmd( cmds );
    ui->listWidgetCmd->clear();
    ui->listWidgetCmd->addItems( cmds );
    ui->listWidgetCmd->scrollToBottom();//滚动至最后一项
}

void DebugShellForm::refreshQuickCmdList()
{
    if( ui->comboBoxCmdList->currentIndex() != 0 )
        return;
    QStringList cmds;
    ui->shellTextEdit->GetQuickCmd( cmds );
    ui->listWidgetCmd->clear();
    ui->listWidgetCmd->addItems( cmds );
    ui->listWidgetCmd->scrollToTop();//滚动至第一项
}

void DebugShellForm::deleteQuickCmd()
{
    int row = ui->listWidgetCmd->currentRow();
    if( row >= 0 ){
        ui->listWidgetCmd->takeItem( row );

        QStringList cmds;
        for( int i=0; i<ui->listWidgetCmd->count(); i++ ){
            cmds << ui->listWidgetCmd->item( i )->text();
        }
        emit quickCmdChanged( cmds );
    }
}

void DebugShellForm::addQuickCmd()
{
    QString text = QInputDialog::getText( this,
                                             QString::fromLocal8Bit( "Add Quick Cmd" ),
                                             QString::fromLocal8Bit( "" ),
                                             QLineEdit::Normal,
                                             "" );
    if( text.size() != 0 ){
        QListWidgetItem* pItem = new QListWidgetItem( ui->listWidgetCmd );
        pItem->setText( text );

        QStringList cmds;
        for( int i=0; i<ui->listWidgetCmd->count(); i++ ){
            cmds << ui->listWidgetCmd->item( i )->text();
        }
        emit quickCmdChanged( cmds );
    }
}

void DebugShellForm::on_comboBoxCmdList_currentIndexChanged(int index)
{
    if( 0 == index ){
        ui->pushButtonCmdList->setVisible( false );
        refreshQuickCmdList();
    }else{
        ui->pushButtonCmdList->setVisible( true );
        //ui->pushButtonCmdList->setText( "Clear History Cmd" );
        refreshHistoryCmdList();
    }
}

void DebugShellForm::on_pushButtonCmdList_clicked()
{
    emit clearHistoryCmd();
}

void DebugShellForm::on_listWidgetCmd_doubleClicked(const QModelIndex &index)
{
    QString cmd = ui->listWidgetCmd->currentItem()->text();
    ui->shellTextEdit->insertCmd( cmd );
}

void DebugShellForm::on_listWidgetCmd_customContextMenuRequested(const QPoint &pos)
{
//    qDebug() << __FUNCTION__ << __LINE__ << ui->comboBoxCmdList->currentIndex()
//             << "lxw debug";
    if( 0 != ui->comboBoxCmdList->currentIndex() )
        return;
    QListWidgetItem* pItem = ui->listWidgetCmd->itemAt( pos );

    QMenu* pMenu = new QMenu( this );
    QAction* pAddQuickCmdAction = new QAction( "Add Quick Cmd", this );
    QAction* pDelQuickCmdAction = new QAction( "Del Quick Cmd", this );
    connect( pAddQuickCmdAction, SIGNAL(triggered() ), this, SLOT( addQuickCmd()) );
    connect( pDelQuickCmdAction, SIGNAL(triggered() ), this, SLOT( deleteQuickCmd()) );

    pMenu->addAction( pAddQuickCmdAction );
    if( pItem ){
        pMenu->addAction( pDelQuickCmdAction );
    }
    pMenu->exec( QCursor::pos() );
    delete pAddQuickCmdAction;
    delete pDelQuickCmdAction;
    delete pMenu;
}
