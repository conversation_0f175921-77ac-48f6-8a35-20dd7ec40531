﻿#ifndef CANSAVEWORKER_H
#define CANSAVEWORKER_H

#include <QObject>
#include <QFile>

#include "CANFrame.h"
#include "SafeQueue.h"
#include "analysisdata.h"

#include "devicefileasc.h"

class CANSaveWorker : public QObject
{
    Q_OBJECT
public:
    explicit CANSaveWorker(QObject *parent = nullptr);

    bool startSave(const QString &canFilename, const QString &objFilename);
    void stopSave();

    bool isSaving() const { return mSaving; }

    void pushFrame(const CANFrame &frame);

signals:

public slots:
    void save(AnalysisData analysisData);

private:
    SafeQueue<CANFrame> mSafeQueue;

    bool mSaving{false};
    DeviceFileASC mDeviceFileASC;
    QFile mFile;
    quint64 mSaveCounts{0};
};

#endif // CANSAVEWORKER_H
