﻿#include "mainwindow.h"

#include "functions/test/wakeuptestform.h"
#include "../views/truetargetsmonitor.h"
#include "../other/frameconvertform.h"
#include "../other/monitor/channelandframemonitorform.h"
#include "../eol/eolview.h"
#include "./daq/daqcalterahctrl.h"
#include "../functions/udstoolboxform.h"
#include "../other/updateECU/ecuupdateform.h"
#include "mastericons.h"
#include "savemanager.h"
#include "shortcutmarkers.h"
#include <app/ChengTechRadarMaster/app_version.h>
#include <analysis/analysismanager.h>
#include <analysis/analysisworker.h>
#include <analysis/calculationworker.h>
#include <devices/devicemanager.h>
#include <devices/deviceconfigdialog.h>
#include <views/viewsmanager.h>
#include <views/analysisdataview.h>
#include <views/objectview.h>
#include <views/truesystemview.h>
#include <functions/radarconfig.h>
#include <functions/dialogcancmds.h>
#include <functions/aftersalecalibration.h>
#include <functions/staticcalibration.h>
#include <functions/calibrationform.h>
#include <functions/ctbinaryfiletools.h>
#include <functions/radarresetworkerform.h>
#include <debugsystem/daqmanager.h>
#include <debugsystem/daqcontrol.h>
#include <debugsystem/debugcontrolform.h>
#include <camera/mulitcamera.h>
#include <utils/settingshandler.h>
#include <utils/cdockwidget.h>
#include "dataprocess/dataprocessform.h"
#include "truevalue/hesailiderworker.h"
#include "truevalue/hesailiderconfigdialog.h"
#include "tools/filebatchparsingform.h"
#include "views/statisticsdialog.h"

#include <QApplication>
#include <QLabel>
#include <QSplitter>
#include <QStatusBar>
#include <QVBoxLayout>
#include <QTimer>
#include <QMenuBar>
#include <QToolBar>
#include <QComboBox>
#include <QMessageBox>
#include <QFileInfo>
#include <QProcess>
#include <QDir>
#include <QDebug>
#include <QInputDialog>
#include <QAction>
#include <QDesktopServices>
#include <QSettings>

using namespace Utils;

static const char windowGeometryKey[] = "MainWindow/WindowGeometry";
static const char windowStateKey[] = "MainWindow/WindowState";
static const char dockSizeKey[] = "MainWindow/DockSize";
static const char deviceSettingsKey[] = "Devices/DeviceSettings";
static const char deviceSettingsKey2[] = "Devices/DeviceSettings2";
static const char outputTargetRaw[] = "Radar/OutputTargetRaw";
static const char outputTargetTrack[] = "Radar/OutputTargetTrack";
static const char output2DFFT[] = "Radar/Output2DFFT";
static const char projectSavePathKey[] = "Project/SavePath";
static const char analysisDataViewsKey[] = "View/AnalysisDataViews";
static const char objectViewsKey[] = "View/ObjectViews";
static const char trueSystemViewsKey[] = "View/TrueSystemViews";
static const char debugSystemViewsKey[] = "View/DebugSystemViews";
static const char dataProcessDebugViewsKey[] = "View/DataProcessDebug";
static const char cameraViewsKey[] = "View/CameraViews";
static const char cameraViewsSizeKey[] = "View/CameraViewsSize";
static const char adcSystemViewsKey[] = "View/AdcSystemViews";

static const char saveRunSaveNow[] = "Save/saveRunSaveNow";
static const char saveAnalysisData[] = "Save/saveAnalysisData";
static const char saveOldFormatKey[] = "Save/saveOldFormat";
static const char saveCanAscKey[] = "Save/saveCanAsc";
static const char saveCanBLFKey[] = "Save/saveCanBLF";
static const char saveVideoKey[] = "Save/saveVideo";
static const char saveHeSaiLiderKey[] = "Save/saveHeSaiLider";

static const char generalSettingsKey[] = "Master/GeneralSettings";
static const char hesaiSettingsKey[] = "HeSai/GeneralSettings";

static const char runStartActionText[] = "开始运行";
static const char runStopActionText[] = "停止运行";

//检测点统计
static const char DeteStatisKey[] = "DeteStatis/DeteStatisSettings";

Q_DECLARE_METATYPE(QCameraInfo)

namespace Core {
namespace Internal {

MainWindow::MainWindow()
    : AppMainWindow(),
      mHeSaiLiderWorker(new HeSaiLiderWorker),
      mAnalysisManager(new Analysis::AnalysisManager(this)),
      mDeviceManager(new Devices::Can::DeviceManager(this)),
      mViewsManager(new Views::ViewsManager(mAnalysisManager->calculationWorker(), this)),
      mDAQManager(new DAQManager(mAnalysisManager)),
      mSaveManager(new SaveManager(mDeviceManager, mAnalysisManager, mHeSaiLiderWorker, &mListCameraInfo)),
      mFrameConvertForm( new FrameConvertForm( mDeviceManager, this ) ),
      mChannelAndFrameMonitorForm( new ChannelAndFrameMonitorForm( mDeviceManager, mAnalysisManager, this )),
      mDeteStatistics (new statisticsDialog(this))
{
    initRole();
    setupUi();

    qRegisterMetaType<Devices::Can::CanFrame>("CanFrame");
    qRegisterMetaType<QList<Devices::Can::CanFrame> >("QList<CanFrame>");
    qRegisterMetaType<AnalysisData>("AnalysisData");
    qRegisterMetaType<Targets>("Targets");
    qRegisterMetaType<AlarmData>("AlarmData");
    qRegisterMetaType<Parser::ParsedDataTypedef::TargetsF>("Parser::ParsedDataTypedef::TargetsF");
    qRegisterMetaType<Parser::ParsedDataTypedef::ParsedData>("Parser::ParsedDataTypedef::ParsedData");

    setWindowTitle( QString("%1 %2 [%3]")
                    .arg(Constants::MASTER_DISPLAY_NAME)
                    .arg(Constants::MASTER_VERSION_DISPLAY)
                    .arg( roleStr() ) );
    QApplication::setWindowIcon(Icons::CHENGTECTRADARMASTER_LOGO.icon());

    connect(mViewsManager, &Views::ViewsManager::trueSystemViewChanged, this, [=](bool openned) {
        mAnalysisManager->analysisWorker()->setTrueSystemOpen(openned);
    });
    connect(mDAQManager, &DAQManager::run, this, &MainWindow::run);

    registerDefaultActions();

    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceOpened, this, [=](){
        mActionRun->setEnabled(true);
        mActionRun->setText(QString::fromLocal8Bit(runStopActionText));
        mActionRun->setIcon(Icons::CHENGTECTRADARMASTER_STOP.icon());
        mActionSelectDevice->setEnabled(false);
        QTimer::singleShot(100, this, &MainWindow::radarOutputMode);
        QTimer::singleShot(1000, this, &MainWindow::getRadarVersion);

        mRunStartTime = QDateTime::currentMSecsSinceEpoch();
    });
    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceClosed, this, [=](){
        mActionRun->setEnabled(true);
        mActionRun->setText(QString::fromLocal8Bit(runStartActionText));
        mActionRun->setIcon(Icons::CHENGTECTRADARMASTER_START.icon());
        mActionSelectDevice->setEnabled(true);
        mRunStartTime = 0;

        if (mHeSaiLiderWorker->isRunning()) {
            mHeSaiLiderWorker->stop();
        }
    });

    if (mRole == ROLE_DRIVE_TEST) {
    connect( mDeviceManager, &Devices::Can::DeviceManager::deviceOpened, mViewsManager, &Views::ViewsManager::deviceOpened  );
    connect( mDeviceManager, &Devices::Can::DeviceManager::deviceClosed, mViewsManager, &Views::ViewsManager::deviceClosed  );
    }
    connect( mAnalysisManager->analysisWorker(), &Analysis::AnalysisWorker::radarResetCountChanged,
             mViewsManager, &Views::ViewsManager::updateRadarResetCount );

    QThread *thread = new QThread;
    mHeSaiLiderWorker->moveToThread(thread);

    connect(this, &MainWindow::sigStartHeSaiRadar, mHeSaiLiderWorker, &HeSaiLiderWorker::run);
    connect(mHeSaiLiderWorker, &HeSaiLiderWorker::showTarget, mViewsManager, &Views::ViewsManager::showHeSaiTarget);
    connect(mHeSaiLiderWorker , &HeSaiLiderWorker::frameNumber, mAnalysisManager->analysisWorker(), &Analysis::AnalysisWorker::heSaiFrameNumber , Qt::DirectConnection);

    thread->start();

    //检测点统计
    connect(mDeteStatistics, &statisticsDialog::sigApply, mAnalysisManager->calculationWorker(), &Analysis::CalculationWorker::setDeteStatistics, Qt::DirectConnection);
    connect(mAnalysisManager->calculationWorker(), &Analysis::CalculationWorker::calculateFinished, mDeteStatistics, &statisticsDialog::calculateFinished, Qt::DirectConnection);

}

void MainWindow::init()
{

}

void MainWindow::initialized()
{
    readSettings();

    QTimer::singleShot(0, this, &MainWindow::restoreWindowState);
}

void MainWindow::aboutToShutdown()
{
    hide();
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (mSaveManager->isSaveing()) {
        mSaveManager->saveStartAndStop();
    }
    if (mDeviceManager->isOpened()) {
        mDeviceManager->closeDevice();
    }

    QMapIterator<QString /*description + deviecname*/, Camera::MulitCamera*> i(mListCameraInfo);
    while (i.hasNext()) {
        i.next();
        Camera::MulitCamera *camera = i.value();
        if (camera->isOpened()) {
            camera->close();
        }
    }


    saveSettings();

    return Utils::AppMainWindow::closeEvent(event);
}

void MainWindow::run()
{
//    mActionSave->setEnabled(false);
    mActionRun->setEnabled(false);
    if (!mDeviceManager->isOpened()) {
        if (!mDeviceManager->openDevice()) {
            mActionRun->setEnabled(true);
            QMessageBox::warning(this, QString::fromLocal8Bit("运行"), QString::fromLocal8Bit("运行失败!\n%1").arg(mDeviceManager->errorString()));
            return;
        }

        if (mRole == ROLE_DRIVE_TEST) {
            emit sigStartHeSaiRadar(-1,
                                    false,
                                    "*************",
                                    2368,
                                    false);

            if (mActionRunSave->isChecked()) {
                mSaveManager->startSave();
            }
        }
    } else {
        if (mRole == ROLE_DRIVE_TEST) {
            mHeSaiLiderWorker->stop();
        }

        if (!mDeviceManager->closeDevice()) {
            mActionRun->setEnabled(true);
            return;
        }
    }
}

void MainWindow::selectDevice()
{
    Devices::Can::DeviceConfigDialog *dialog = mDeviceManager ?
                new Devices::Can::DeviceConfigDialog(mDeviceManager->deviceSettings(),
                                                     mDeviceManager->deviceSettings_2(),
                                                     this) :
                new Devices::Can::DeviceConfigDialog(this);
    connect(dialog, &Devices::Can::DeviceConfigDialog::applied, this, [=](){
        Devices::Can::DeviceSettings deviceSetting = dialog->settings();
        Devices::Can::DeviceSettings deviceSetting_2 = dialog->settings_2();
        deviceChanged(deviceSetting, deviceSetting_2);
    });
    dialog->exec();
}

void MainWindow::radarOutputMode()
{
    typedef struct
    {
    #if IS_BIG_ENDIAN==1
        uint64_t outputMode               : 2;
        uint64_t carVelMode               : 2;
        uint64_t workMode                 : 4;
        uint64_t sendExtInfo              : 2;
        uint64_t estimatedSpeedCfgExpand  : 2;
        uint64_t angleMapperMode                    : 2;
        uint64_t sendYawRateMode                  : 2;
        uint64_t canBaudrateMode                  : 2;
        uint64_t sendExtInfo2                           : 2;
        uint64_t dbgMode                  : 2;
        uint64_t cohesionMode             : 2;
        uint64_t canMode                  : 2;
        uint64_t protVer                  : 4;
        uint64_t resv                                  : 34;
    #else
        uint64_t resv                     : 34;
        uint64_t protVer                  : 4;
        uint64_t canMode                  : 2;
        uint64_t cohesionMode             : 2;
        uint64_t dbgMode                  : 2;
        uint64_t sendExtInfo2                           : 2;
        uint64_t canBaudrateMode                  : 2;
        uint64_t sendYawRateMode                  : 2;
        uint64_t angleMapperMode                    : 2;
        uint64_t estimatedSpeedCfgExpand  : 2;
        uint64_t sendExtInfo              : 2;
        uint64_t workMode                 : 4;
        uint64_t carVelMode               : 2;
        uint64_t outputMode               : 2;
    #endif
    }stWorkModeMsg;

    quint8 mode = 0x0;
    if (mActionDisplayRawTarget->isChecked()) mode |= 0x2;
    if (mActionDisplayTrackTarget->isChecked()) mode |= 0x1;
    int deviceIndex = mDeviceManager->deviceIndex();

    QByteArray data(8, 0);
    stWorkModeMsg *p = (stWorkModeMsg*)data.data();
    p->resv = 0x0;
    p->protVer = 0x0;
    p->canMode = 0x0;
    p->cohesionMode = 0x0;
    p->dbgMode = 0x0;
    p->sendExtInfo2 = 0x0;
    p->canBaudrateMode = 0x0;
    p->sendYawRateMode = 0x0;
    p->angleMapperMode = 0x0;
    p->estimatedSpeedCfgExpand = 0x0;
    p->sendExtInfo = 0x0;
    p->workMode = 0x0;
    p->carVelMode = 0x0;
    p->outputMode = mode;
    std::reverse(data.begin(), data.end());

    for (int i = 0; i < mDeviceManager->channelCount(); ++i)
    {
        QList<Devices::Can::CanFrame> frames;
        for (int radarID = 4; radarID <= 0x07; ++radarID)
        {
            qDebug() << __FUNCTION__ << __LINE__ << QString::number(0x200 | (radarID & 0xF), 16) << data.toHex(' ');

            frames << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x300 | (radarID & 0xF),
                                             QByteArray::fromHex("31 58 AF 80 00 00 00 00"), Devices::Can::CanFrame::microsecondsTimestamp(), false, false )
                   << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x200 | (radarID & 0xF),
                                             data, Devices::Can::CanFrame::microsecondsTimestamp(), false, false )
                   << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x300 | (radarID & 0xF),
                                             QByteArray::fromHex("31 58 AF 00 00 00 00 00"), Devices::Can::CanFrame::microsecondsTimestamp(), false, false );
        }

        int radarID = 0;

        qDebug() << __FUNCTION__ << __LINE__ << QString::number(0x200 | (radarID & 0xF), 16) << data.toHex(' ');
        frames << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x300 | (radarID & 0xF),
                                         QByteArray::fromHex("31 58 AF 80 00 00 00 00"), Devices::Can::CanFrame::microsecondsTimestamp(), false, false )
               << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x200 | (radarID & 0xF),
                                         QByteArray::fromHex("40 00 00 00 00 00 00 00"), Devices::Can::CanFrame::microsecondsTimestamp(), false, false )
//               << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x300 | (radarID & 0xF),
//                                         QByteArray::fromHex("31 58 AF 00 00 00 00 00"), Devices::Can::CanFrame::microsecondsTimestamp(), false, false )
               << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x110,
                                         QByteArray::fromHex("02 00 00 00 00 00 00 00"), Devices::Can::CanFrame::microsecondsTimestamp(), false, true );


        mDeviceManager->sendFrames(frames);
    }

    mRadarOutPutModeLoopTime++;
    if( mRadarOutPutModeLoopTime < 5 ){
        QTimer::singleShot(1000, this, &MainWindow::radarOutputMode);
    }else{
        mRadarOutPutModeLoopTime = 0;
    }
}

void MainWindow::radarOutput2DFFT()
{
    int deviceIndex = mDeviceManager->deviceIndex();
    QByteArray data = QByteArray::fromHex("03 02 00 00 00 00 00 00");
    data[2] = mActionDisplay2DFFT->isChecked() ? 0x02 : 0x01;
    unsigned char *pData = (unsigned char *)data.data();
    unsigned int checkSum{0x00};
    for (int i = 0; i < 7; ++i) {
        checkSum += pData[i];
    }
    data[7] = checkSum ^ 0xFF;
    QList<Devices::Can::CanFrame> frames;
    for (int i = 0; i < mDeviceManager->channelCount(); ++i)
    {
        for (int radarID = 4; radarID <= 0x07; ++radarID)
        {
            frames << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x730 | (radarID & 0xF),
                                             data, Devices::Can::CanFrame::microsecondsTimestamp(), false, false );
        }
    }
    mDeviceManager->sendFrames(frames);
}

void MainWindow::rouseRadarAllTheTime()
{
    QList<Devices::Can::CanFrame> frames;
    int deviceIndex = mDeviceManager->deviceIndex();

    for (int i = 0; i < mDeviceManager->channelCount(); ++i)
    {
        Devices::Can::CanFrame frame;
        switch( mRadarType ){
        case RadarType::BYD:
            frame = Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x12D, QByteArray::fromHex("000000000c00f300"));
            break;
        case RadarType::BAIC:
            frame = Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x411, QByteArray::fromHex("0040000000000000"));
            break;
        case RadarType::GEELY:
            frame = Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x511, QByteArray::fromHex("40FFFFFFFFFFFFFF"));
            frame = Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x501, QByteArray::fromHex("01 40 00 20 00 00 00 00 "));
            break;
        case RadarType::Hozon:
            frame = Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x110, QByteArray::fromHex("02 00 00 00 00 00 00 00"));
            break;
        default:
            return;
        }
//        frames << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x12D, QByteArray::fromHex("000000000c00f300"))
//               << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x12D, QByteArray::fromHex("000000000c00f300"))
//               << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x12D, QByteArray::fromHex("000000000c00f300"));
        frames << frame << frame << frame;
        //每个通道独立发送，避免因为通道0错误导致不发送通道1的数据
        mDeviceManager->sendFrames(frames);
        frames.clear();
    }
    //mDeviceManager->sendFrames(frames);

    if( mSendOutputModeFrame ){
        QThread::sleep( 1 );//等待唤醒
        radarOutputMode();
        mSendOutputModeFrame = false;
    }
}

void MainWindow::wakeupRadar()
{
    mSendOutputModeFrame = true;
    QAction *action = qobject_cast<QAction*>(sender());
    if (!action){
        return;
    }else if( action == mActionRouseRadarAllTheTime ){
        mRadarType = RadarType::BYD;
        mActionRouseRadarAllTheTimeBAIC->setChecked( false );
        mActionRouseRadarAllTheTimeGEELY->setChecked( false );
        mActionRouseRadarAllTheTimeHozon->setChecked( false );
        mTimerWakeupRadar->stop();
        rouseRadarAllTheTime();
    }else if( action == mActionRouseRadarAllTheTimeBAIC ){
        mRadarType = RadarType::BAIC;
        mActionRouseRadarAllTheTime->setChecked( false );
        mActionRouseRadarAllTheTimeGEELY->setChecked( false );
        mActionRouseRadarAllTheTimeHozon->setChecked( false );
        //北汽须定时发送
        if( mTimerWakeupRadar ){
            //if( mTimerWakeupRadar->isActive() ){
            if( !mActionRouseRadarAllTheTimeBAIC->isChecked() ){
                mTimerWakeupRadar->stop();
            }else{
                mTimerWakeupRadar->start( 500 );
            }
        }
    }else if( action == mActionRouseRadarAllTheTimeGEELY ){
        mRadarType = RadarType::GEELY;
        mActionRouseRadarAllTheTime->setChecked( false );
        mActionRouseRadarAllTheTimeBAIC->setChecked( false );
        mActionRouseRadarAllTheTimeHozon->setChecked( false );
        //吉利须定时发送
        if( mTimerWakeupRadar ){
            //if( mTimerWakeupRadar->isActive() ){
            if( !mActionRouseRadarAllTheTimeGEELY->isChecked() ){
                mTimerWakeupRadar->stop();
            }else{
                mTimerWakeupRadar->start( 500 );
            }
        }
    } else if (action == mActionRouseRadarAllTheTimeHozon) {
        mRadarType = Hozon;
        mActionRouseRadarAllTheTime->setChecked( false );
        mActionRouseRadarAllTheTimeBAIC->setChecked( false );
        mActionRouseRadarAllTheTimeGEELY->setChecked( false );
        //合众须定时发送
        if( mTimerWakeupRadar ){
            //if( mTimerWakeupRadar->isActive() ){
            if( !mActionRouseRadarAllTheTimeHozon->isChecked() ){
                mTimerWakeupRadar->stop();
            }else{
                mTimerWakeupRadar->start( 60000 );
            }
        }

    } else {
        return;
    }
}

void MainWindow::generalSettings()
{
    GeneralSettings *form = new GeneralSettings(&mGeneralSettings);
    connect(form, &GeneralSettings::apply, this, &MainWindow::generalSettingsApply);
    form->setWindowTitle(QString::fromLocal8Bit("通用设置"));
    form->setWindowModality(Qt::NonModal);
    form->setAttribute(Qt::WA_ShowModal, true);
    form->setAttribute(Qt::WA_DeleteOnClose,true);

    form->show();
}

void MainWindow::generalSettingsApply()
{
    mAnalysisManager->setSaveCountMax(mGeneralSettings.mFileSaveFrameCount);
    mAnalysisManager->analysisWorker()->alarmCalculate()->setEarlyWarningSettings(mGeneralSettings.mEarlyWarningSettings);
    mAnalysisManager->analysisWorker()->setHozonBreakShort(mGeneralSettings.mHozonBreakShort);

    mAnalysisManager->setAngleCompensation(mGeneralSettings.mAngleCompensation,
                                           sizeof (mGeneralSettings.mAngleCompensation) / sizeof (mGeneralSettings.mAngleCompensation[0]));
    mAnalysisManager->analysisWorker()->setBYDHDChannelRadarID(mGeneralSettings.mBYDRaw600ByChannel, mGeneralSettings.mBYDHDChannelRadarID,
                                             sizeof (mGeneralSettings.mBYDHDChannelRadarID) / sizeof (mGeneralSettings.mBYDHDChannelRadarID[0]));
    if (mDebugControlForm) {
    mDebugControlForm->setBYDHDChannelRadarID(mGeneralSettings.mBYDRaw600ByChannel, mGeneralSettings.mBYDHDChannelRadarID,
                                              sizeof (mGeneralSettings.mBYDHDChannelRadarID) / sizeof (mGeneralSettings.mBYDHDChannelRadarID[0]));
    }
    mAnalysisManager->analysisWorker()->setGEELYChannelRadarID(mGeneralSettings.mGEELYChannelRadarID,
                                             sizeof (mGeneralSettings.mGEELYChannelRadarID) / sizeof (mGeneralSettings.mGEELYChannelRadarID[0]));
}

void MainWindow::heSaiLiderSettings()
{
    HeSaiLiderConfigDialog *dialog = new HeSaiLiderConfigDialog(mHeSaiLiderWorker, this);
    dialog->exec();
}

void MainWindow::deteStatisticalSettings()
{
    mDeteStatistics->show();
}

void MainWindow::calculationSetting()
{

}

void MainWindow::radarConfig()
{
    Functions::RadarConfig *radarConfig = new Functions::RadarConfig(mDeviceManager, this);
    radarConfig->setAttribute(Qt::WA_DeleteOnClose);

    radarConfig->exec();
}

void MainWindow::canCommandLine()
{
    Functions::DialogCanCmds *canCMD = new Functions::DialogCanCmds(mDeviceManager, this);
    canCMD->setAttribute(Qt::WA_DeleteOnClose);

    canCMD->exec();
}

void MainWindow::afterSalecalibration()
{
    Functions::AfterSaleCalibration *dialog = new Functions::AfterSaleCalibration(mDeviceManager, this);
    dialog->setAttribute(Qt::WA_DeleteOnClose);

    dialog->exec();
}

void MainWindow::staticSalecalibration()
{
    Functions::StaticCalibration *dialog = new Functions::StaticCalibration(mDeviceManager, this);
    dialog->setAttribute(Qt::WA_DeleteOnClose);

    dialog->exec();
}

void MainWindow::radarCalibration()
{
    CalibrationForm *form = new CalibrationForm(mDeviceManager, CalibrationForm::CalibrationMode);
    form->setWindowTitle(QString::fromLocal8Bit("前雷达售后标定"));
    form->setWindowModality(Qt::NonModal);
    form->setAttribute(Qt::WA_ShowModal, false);
    form->setAttribute(Qt::WA_DeleteOnClose,true);

    form->show();
}

void MainWindow::radarReset()
{
    Functions::RadarResetWorkerForm *dialog = new Functions::RadarResetWorkerForm(mDeviceManager, this);
    dialog->setAttribute(Qt::WA_DeleteOnClose);

    dialog->exec();
}

void MainWindow::newTrueSystem()
{
    if( mRole != ROLE_DRIVE_TEST ){
        return;
    }

    if (mViewsManager->trueSystemViewExisted())
    {
        return;
    }

    Views::TrueSystemView::TrueSystemView *view = mViewsManager->trueSystemView(this);
    view->setAnalysisManager( mAnalysisManager );
    QDockWidget* trueSystemDockWidget = new QDockWidget(QString::fromLocal8Bit("真值系统"), this);
    trueSystemDockWidget->setAttribute(Qt::WA_DeleteOnClose);
    trueSystemDockWidget->setObjectName("TrueSystem");
    trueSystemDockWidget->setWidget(view);

    addDockWidget(Qt::LeftDockWidgetArea , trueSystemDockWidget);
    SETTINGS_SET_VALUE(QLatin1String(trueSystemViewsKey), true);
}

void MainWindow::newDebugSystem()
{
    if( mRole != ROLE_DRIVE_TEST ){
        return;
    }
    if (mDAQManager->daqControlExisted())
    {
        return;
    }

//    DAQControl *view = mDAQManager->daqControl(mDeviceManager, mAnalysisManager, this);
    mDebugControlForm = mDAQManager->debugControl(mViewsManager, mSaveManager, mDeviceManager, mAnalysisManager, this);
    QDockWidget* daqControlDockWidget = new QDockWidget(QString::fromLocal8Bit("调试系统"), this);
    daqControlDockWidget->setAttribute(Qt::WA_DeleteOnClose);
    daqControlDockWidget->setObjectName("DebugSystem");
    daqControlDockWidget->setWidget(mDebugControlForm);
    mDebugControlForm->setBYDHDChannelRadarID(mGeneralSettings.mBYDRaw600ByChannel, mGeneralSettings.mBYDHDChannelRadarID,
                                              sizeof (mGeneralSettings.mBYDHDChannelRadarID) / sizeof (mGeneralSettings.mBYDHDChannelRadarID[0]));
    connect(mDebugControlForm, &DAQControl::destroyed, this, [=](){
        mDebugControlForm = 0;
    });

    addDockWidget(Qt::BottomDockWidgetArea , daqControlDockWidget);
    SETTINGS_SET_VALUE(QLatin1String(debugSystemViewsKey), true);
}

void MainWindow::newDebugProcess()
{
    if (!mDataProcessForm) {
        mDataProcessForm = new DataProcessForm(mViewsManager, this);

        connect(mDataProcessForm, &DAQControl::destroyed, this, [=](){
            mDataProcessForm = 0;
        });
    }

    QDockWidget* dataProcessDockWidget = new QDockWidget(QString::fromLocal8Bit("数据处理调试"), this);
    dataProcessDockWidget->setAttribute(Qt::WA_DeleteOnClose);
    dataProcessDockWidget->setObjectName("DataProcessDebug");
    dataProcessDockWidget->setWidget(mDataProcessForm);

    addDockWidget(Qt::BottomDockWidgetArea , dataProcessDockWidget);
    SETTINGS_SET_VALUE(QLatin1String(dataProcessDebugViewsKey), true);
}

void MainWindow::refreshCameraInfo()
{
    mMenuCamera->clear();
    const QList<QCameraInfo> availableCameras = QCameraInfo::availableCameras();
    for (const QCameraInfo &cameraInfo : availableCameras)
    {
        QAction *videoDeviceAction = new QAction(cameraInfo.description(), this);
        videoDeviceAction->setCheckable(true);
        videoDeviceAction->setData(QVariant::fromValue(cameraInfo));
        videoDeviceAction->setChecked(mListCameraInfo.contains(cameraInfo.description() + cameraInfo.deviceName()));

        qDebug() << __FUNCTION__ << __LINE__ << cameraInfo.description() + cameraInfo.deviceName();
        connect(videoDeviceAction, SIGNAL(triggered()), this, SLOT(addCamera()));

        mMenuCamera->addAction(videoDeviceAction);
    }
}

void MainWindow::addCamera()
{
    QAction *action = qobject_cast<QAction*>(sender());
    if (!action)
    {
        return;
    }

    const QCameraInfo &cameraInfo = qvariant_cast<QCameraInfo>(action->data());

    if (action->isChecked())
    {
        addCamera(cameraInfo, QSize(300, 400));
    }
}

void MainWindow::addCamera(const QCameraInfo &cameraInfo, QSize cameraSzie)
{
    QDockWidget *dock = new QDockWidget(this);
    dock->setObjectName(cameraInfo.description() + cameraInfo.deviceName());
    dock->setWindowTitle(cameraInfo.description());
    dock->setAttribute(Qt::WA_DeleteOnClose);
    qDebug() << cameraInfo.description() + cameraInfo.deviceName();

    //    Camera *camera = new Camera();
    Camera::MulitCamera *camera = new Camera::MulitCamera(cameraInfo, cameraInfo.description(), this);
    camera->resize(cameraSzie);

    connect(camera, &Camera::MulitCamera::cameraSaveIndex, mDeviceManager->deviceWorker(), &Devices::Can::IDeviceWorker::cameraSaveIndex, Qt::DirectConnection);
    connect(camera, &Camera::MulitCamera::cameraQuit, this, [=](){
        dock->close();
    });

    connect(camera, &Camera::MulitCamera::aliasChenged, this, [=](const QString &cameraAlais){
        dock->setWindowTitle(cameraAlais);
    });

    connect(camera, &Camera::MulitCamera::destroyed, this, [=](){
        mListCameraInfo.remove(cameraInfo.description() + cameraInfo.deviceName());
    });

    dock->setWidget(camera);
    this->addDockWidget(Qt::RightDockWidgetArea, dock);
    dock->resize(400, 500);
    mListCameraInfo[cameraInfo.description() + cameraInfo.deviceName()] = camera;
}

void MainWindow::newShortcutMarkers()
{
    if( mRole != ROLE_DRIVE_TEST ){
        return;
    }

    ShortcutMarkers *view = new ShortcutMarkers(this);
    QDockWidget* shortcutMarkersDockWidget = new QDockWidget(QString::fromLocal8Bit("快捷标记"), this);
    shortcutMarkersDockWidget->setAttribute(Qt::WA_DeleteOnClose);
    shortcutMarkersDockWidget->setObjectName("ShortcutMarkers");
    shortcutMarkersDockWidget->setWidget(view);

    addDockWidget(Qt::RightDockWidgetArea , shortcutMarkersDockWidget);
}

void MainWindow::showBinalryFileTools()
{
    CTBinaryFileTools* pBinaryFileTools = new CTBinaryFileTools(this );
    pBinaryFileTools->setWindowFlag( Qt::Dialog );
    pBinaryFileTools->setAttribute(Qt::WA_DeleteOnClose);
    pBinaryFileTools->show();

}

void MainWindow::fileBatchParsing()
{
    FileBatchParsingForm* pFileBatchParsingForm = new FileBatchParsingForm(this );
    pFileBatchParsingForm->setWindowFlag( Qt::Dialog );
    pFileBatchParsingForm->setAttribute(Qt::WA_DeleteOnClose);
    pFileBatchParsingForm->show();
}

void MainWindow::showEol()
{
//    QString password = QInputDialog::getText( this,
//                                             QString::fromLocal8Bit( "登录EOL" ),
//                                             QString::fromLocal8Bit( "请输入密码:" ),
//                                             QLineEdit::Password,
//                                             "" );
//    if( password != "CTAdmin" ){
//        QMessageBox::warning( this,
//                              QString::fromLocal8Bit( "警告" ),
//                              QString::fromLocal8Bit( "密码错误" ) );
//        return;
//    }


    EOLView* pEolView = new EOLView( mDeviceManager, this );
    pEolView->setWindowFlag( Qt::Dialog );
    pEolView->setAttribute(Qt::WA_DeleteOnClose);
    pEolView->show();
}

void MainWindow::getRadarVersion()
{
    int deviceIndex = mDeviceManager->deviceIndex();
    for (int i = 0; i < mDeviceManager->channelCount(); ++i)
    {
        QList<Devices::Can::CanFrame> frames;
        frames << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x120 | 4, QByteArray::fromHex("F0 00 00 00 00 00 00 00"))
               << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x120 | 5, QByteArray::fromHex("F0 00 00 00 00 00 00 00"))
               << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x120 | 6, QByteArray::fromHex("F0 00 00 00 00 00 00 00"))
               << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x120 | 7, QByteArray::fromHex("F0 00 00 00 00 00 00 00"));
        mDeviceManager->sendFrames(frames);
    }
}

void MainWindow::UDSToolBox()
{
    if( !mUDSBox ){
        mUDSBox = new UDSToolBoxForm( mDeviceManager, this);
        mUDSBox->setWindowFlag( Qt::Dialog );
    }
    mUDSBox->show();
//    this->setVisible( false );

//    UDSToolBoxForm *dialog = new UDSToolBoxForm( mDeviceManager, this);
//    dialog->setWindowFlag( Qt::Dialog );
//    dialog->setAttribute(Qt::WA_DeleteOnClose);
    //    dialog->show();
}


void MainWindow::resolutionTest()
{
    typedef struct
    {
    #if IS_BIG_ENDIAN==1
        uint64_t outputMode               : 2;
        uint64_t carVelMode               : 2;
        uint64_t workMode                 : 4;
        uint64_t sendExtInfo              : 2;
        uint64_t estimatedSpeedCfgExpand  : 2;
        uint64_t angleMapperMode                    : 2;
        uint64_t sendYawRateMode                  : 2;
        uint64_t canBaudrateMode                  : 2;
        uint64_t sendExtInfo2                           : 2;
        uint64_t dbgMode                  : 2;
        uint64_t cohesionMode             : 2;
        uint64_t canMode                  : 2;
        uint64_t protVer                  : 4;
        uint64_t radarTestMode            : 2;
        uint64_t resv                     :32;
    #else
        uint64_t resv                     :32;
        uint64_t radarTestMode            : 2;
        uint64_t protVer                  : 4;
        uint64_t canMode                  : 2;
        uint64_t cohesionMode             : 2;
        uint64_t dbgMode                  : 2;
        uint64_t sendExtInfo2                           : 2;
        uint64_t canBaudrateMode                  : 2;
        uint64_t sendYawRateMode                  : 2;
        uint64_t angleMapperMode                    : 2;
        uint64_t estimatedSpeedCfgExpand  : 2;
        uint64_t sendExtInfo              : 2;
        uint64_t workMode                 : 4;
        uint64_t carVelMode               : 2;
        uint64_t outputMode               : 2;
    #endif
    }stWorkModeMsg;

    if (!mDeviceManager->isOpened())
    {
        mActionResolutionTest->setChecked(false);
        return;
    }

    mActionWeakObjTest->setChecked(false);

    quint8 mode = 0x0;
    if (mActionResolutionTest->isChecked())
    {
        mode = 0x2;
    }
    else
    {
        mode = 0x1;
    }
    int deviceIndex = mDeviceManager->deviceIndex();
    for (int i = 0; i < mDeviceManager->channelCount(); ++i)
    {
        QList<Devices::Can::CanFrame> frames;
        for (int radarID = 4; radarID <= 0x07; ++radarID)
        {
            QByteArray data(8, 0);
            stWorkModeMsg *p = (stWorkModeMsg*)data.data();
            p->resv = 0x0;
            p->protVer = 0x0;
            p->canMode = 0x0;
            p->cohesionMode = 0x0;
            p->dbgMode = 0x0;
            p->sendExtInfo2 = 0x0;
            p->canBaudrateMode = 0x0;
            p->sendYawRateMode = 0x0;
            p->angleMapperMode = 0x0;
            p->estimatedSpeedCfgExpand = 0x0;
            p->sendExtInfo = 0x0;
            p->workMode = 0x0;
            p->carVelMode = 0x0;
            p->outputMode = 0x0;
            p->radarTestMode = mode;
            std::reverse(data.begin(), data.end());


            frames << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x300 | (radarID & 0xF),
                                             QByteArray::fromHex("3158AF8000000000"), Devices::Can::CanFrame::microsecondsTimestamp(), false, false )
                   << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x200 | (radarID & 0xF),
                                             data, Devices::Can::CanFrame::microsecondsTimestamp(), false, false )
                   << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x300 | (radarID & 0xF),
                                             QByteArray::fromHex("3158AF0000000000"), Devices::Can::CanFrame::microsecondsTimestamp(), false, false );
        }

        mDeviceManager->sendFrames(frames);
    }

    mRadarResolutionTestModeLoopTime++;
    if( mRadarResolutionTestModeLoopTime < 5 ){
        QTimer::singleShot(1000, this, &MainWindow::radarOutputMode);
    }else{
        mRadarResolutionTestModeLoopTime = 0;
    }
}


void MainWindow::weakObjTest()
{
    typedef struct
    {
    #if IS_BIG_ENDIAN==1
        uint64_t outputMode               : 2;
        uint64_t carVelMode               : 2;
        uint64_t workMode                 : 4;
        uint64_t sendExtInfo              : 2;
        uint64_t estimatedSpeedCfgExpand  : 2;
        uint64_t angleMapperMode                    : 2;
        uint64_t sendYawRateMode                  : 2;
        uint64_t canBaudrateMode                  : 2;
        uint64_t sendExtInfo2                           : 2;
        uint64_t dbgMode                  : 2;
        uint64_t cohesionMode             : 2;
        uint64_t canMode                  : 2;
        uint64_t protVer                  : 4;
        uint64_t radarTestMode            : 2;
        uint64_t resv                     :32;
    #else
        uint64_t resv                     :32;
        uint64_t radarTestMode            : 2;
        uint64_t protVer                  : 4;
        uint64_t canMode                  : 2;
        uint64_t cohesionMode             : 2;
        uint64_t dbgMode                  : 2;
        uint64_t sendExtInfo2                           : 2;
        uint64_t canBaudrateMode                  : 2;
        uint64_t sendYawRateMode                  : 2;
        uint64_t angleMapperMode                    : 2;
        uint64_t estimatedSpeedCfgExpand  : 2;
        uint64_t sendExtInfo              : 2;
        uint64_t workMode                 : 4;
        uint64_t carVelMode               : 2;
        uint64_t outputMode               : 2;
    #endif
    }stWorkModeMsg;

    if (!mDeviceManager->isOpened())
    {
        mActionWeakObjTest->setChecked(false);
        return;
    }

    mActionResolutionTest->setChecked(false);

    quint8 mode = 0x0;
    if (mActionWeakObjTest->isChecked())
    {
        mode = 0x3;
    }
    else
    {
        mode = 0x1;
    }
    int deviceIndex = mDeviceManager->deviceIndex();
    for (int i = 0; i < mDeviceManager->channelCount(); ++i)
    {
        QList<Devices::Can::CanFrame> frames;
        for (int radarID = 4; radarID <= 0x07; ++radarID)
        {
            QByteArray data(8, 0);
            stWorkModeMsg *p = (stWorkModeMsg*)data.data();
            p->resv = 0x0;
            p->protVer = 0x0;
            p->canMode = 0x0;
            p->cohesionMode = 0x0;
            p->dbgMode = 0x0;
            p->sendExtInfo2 = 0x0;
            p->canBaudrateMode = 0x0;
            p->sendYawRateMode = 0x0;
            p->angleMapperMode = 0x0;
            p->estimatedSpeedCfgExpand = 0x0;
            p->sendExtInfo = 0x0;
            p->workMode = 0x0;
            p->carVelMode = 0x0;
            p->outputMode = 0x0;
            p->radarTestMode = mode;
            std::reverse(data.begin(), data.end());


            frames << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x300 | (radarID & 0xF),
                                             QByteArray::fromHex("3158AF8000000000"), Devices::Can::CanFrame::microsecondsTimestamp(), false, false )
                   << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x200 | (radarID & 0xF),
                                             data, Devices::Can::CanFrame::microsecondsTimestamp(), false, false )
                   << Devices::Can::CanFrame(deviceIndex, i, Devices::Can::CanFrame::TX, 0x300 | (radarID & 0xF),
                                             QByteArray::fromHex("3158AF0000000000"), Devices::Can::CanFrame::microsecondsTimestamp(), false, false );
        }

        mDeviceManager->sendFrames(frames);
    }

    mRadarResolutionTestModeLoopTime++;
    if( mRadarResolutionTestModeLoopTime < 5 ){
        QTimer::singleShot(1000, this, &MainWindow::radarOutputMode);
    }else{
        mRadarResolutionTestModeLoopTime = 0;
    }
}

void MainWindow::wakeUpTest()
{
    if( !mWakeUpTest ){
        mWakeUpTest = new WakeUpTestForm( mDeviceManager, this);
    }
    mWakeUpTest->setWindowFlag( Qt::Dialog );
    mWakeUpTest->show();
}

void MainWindow::ECUUpdate()
{
    ECUUpdateForm* ecuUpdate = new ECUUpdateForm( mDeviceManager, this );
    ecuUpdate->setWindowFlag( Qt::Dialog );
    ecuUpdate->setAttribute(Qt::WA_DeleteOnClose);
    ecuUpdate->show();
    return;
}

void MainWindow::userManual()
{
    QString filePath = QString::fromLocal8Bit("%1/ChengTech Radar Master User's Manual.pdf").arg(QCoreApplication:: applicationDirPath ());

    bool bRet = QDesktopServices::openUrl(QUrl::fromLocalFile(filePath));//尝试直接打开
    if( !bRet ){
        //直接打开失败，则打开文件夹
        const QFileInfo fileInfo(filePath);
        QProcess process;

        QStringList param;
        if (!fileInfo.isDir())
            param += QLatin1String("/select,");
        param += QDir::toNativeSeparators(fileInfo.canonicalFilePath());
        QProcess::startDetached("explorer.exe", param);
    }
}

void MainWindow::about()
{
    QMessageBox::information(this,
                             QString::fromLocal8Bit("About ChengTech Radar Master"),
                             QString::fromLocal8Bit("<h3>%1 %2</h3>\r\nCopyright (c) 2016-2023 Shenzhen ChengTech Technology Co., Ltd")
                             .arg(Constants::MASTER_DISPLAY_NAME)
                             .arg(Constants::MASTER_VERSION_DISPLAY));
}

void MainWindow::showAdcAndHil()
{
    if( mRole != ROLE_DRIVE_TEST ){
        return;
    }
//    DAQCalterahCtrl* pDaq = new DAQCalterahCtrl( mSaveManager, mDeviceManager->deviceWorker(), this );
//    pDaq->setWindowFlag( Qt::Dialog );
//    pDaq->setAttribute(Qt::WA_DeleteOnClose);
//    pDaq->show();
    if( !mDAQCtrl ){
        mDAQCtrl = new DAQCalterahCtrl( mSaveManager, mDeviceManager/*->deviceWorker()*/, this );
//        Qt::WindowFlags flags = Qt::Dialog;
//        flags |= Qt::WindowCloseButtonHint /*| Qt::WindowMaximizeButtonHint | Qt::WindowMinimizeButtonHint*/;
//        mDAQCtrl->setWindowFlags(flags);

        //mDAQCtrl->setWindowFlag( Qt::Dialog );

        QDockWidget* DAQDockWidget = new QDockWidget(QString::fromLocal8Bit("ADC"), this);
        //DAQDockWidget->setAttribute(Qt::WA_DeleteOnClose);
        DAQDockWidget->setObjectName("ADC");
        DAQDockWidget->setWidget(mDAQCtrl);
        addDockWidget(Qt::BottomDockWidgetArea , DAQDockWidget);


    }


    QDockWidget* DAQDockWidget = (QDockWidget*)mDAQCtrl->parent();
    if( !DAQDockWidget->isVisible() ){
        DAQDockWidget->show();
    }

    //mDAQCtrl->show();
    SETTINGS_SET_VALUE(QLatin1String(adcSystemViewsKey), true);
}

void MainWindow::deviceChanged(Devices::Can::DeviceSettings deviceSettings, Devices::Can::DeviceSettings deviceSettings2)
{
    bool newDeviceWorker = false;
    bool newDeviceWorker2 = false;
    mDeviceManager->setDeviceSettings(deviceSettings, newDeviceWorker);
    if (newDeviceWorker)
    {
        Devices::Can::IDeviceWorker *deviceWorker = mDeviceManager->deviceWorker();
        Analysis::AnalysisWorker *analysisWorker = mAnalysisManager->analysisWorker();
        connect(deviceWorker, &Devices::Can::IDeviceWorker::frameRecieved, analysisWorker, &Analysis::AnalysisWorker::canFrame);
        connect( deviceWorker, &Devices::Can::IDeviceWorker::opened, analysisWorker, &Analysis::AnalysisWorker::clearRecv410FrameCount );
        connect( deviceWorker, &Devices::Can::IDeviceWorker::opened, analysisWorker, &Analysis::AnalysisWorker::clearRadarResetCount );

        QMapIterator<QString /*description + deviecname*/, Camera::MulitCamera*> i(mListCameraInfo);
        while (i.hasNext()) {
            i.next();
            Camera::MulitCamera *camera = i.value();
            connect(camera, &Camera::MulitCamera::cameraSaveIndex, deviceWorker, &Devices::Can::IDeviceWorker::cameraSaveIndex, Qt::DirectConnection);
        }
    }
    mDeviceManager->setDeviceSettings_2(deviceSettings2, newDeviceWorker2);
    if (newDeviceWorker2)
    {
//        qDebug() << __FUNCTION__ << __LINE__ << "message";
        Devices::Can::IDeviceWorker *deviceWorker = mDeviceManager->deviceWorker_2();
        Analysis::AnalysisWorker *analysisWorker = mAnalysisManager->analysisWorker();
        connect(deviceWorker, &Devices::Can::IDeviceWorker::frameRecieved, analysisWorker, &Analysis::AnalysisWorker::canFrame);
//        connect( deviceWorker, &Devices::Can::IDeviceWorker::opened, analysisWorker, &Analysis::AnalysisWorker::clearRecv410FrameCount );
//        connect( deviceWorker, &Devices::Can::IDeviceWorker::opened, analysisWorker, &Analysis::AnalysisWorker::clearRadarResetCount );
    }
}

bool MainWindow::newAnalysisDataView(quint8 radarID)
{
    if( mRole != ROLE_DRIVE_TEST ){
        return false;
    }

    if (mViewsManager->analysisDataViewExisted(radarID))
    {
        return false;
    }
    Views::AnalysisView::AnalysisDataViewI *view = mViewsManager->analysisDataView(radarID, this);
    if( mStatusBar ){
        mStatusBar->addWidget(view->labelMeasurementCounter());
    }

    CDockWidget* analysisDataViewDockWidget = new CDockWidget(this);
    analysisDataViewDockWidget->setObjectName(QString("Radar %1").arg(radarID));
    analysisDataViewDockWidget->setWindowTitle(QString::fromLocal8Bit("雷达 %1").arg(radarID));

    analysisDataViewDockWidget->addAction(view->actionOrientation());
    analysisDataViewDockWidget->addAction(view->actionNewAnalysisDataTable());
    analysisDataViewDockWidget->addAction(view->actionViewDataConfig());
    analysisDataViewDockWidget->addAction(view->actionMonitoringTrackTarget());
    analysisDataViewDockWidget->setWidget(view);

    addDockWidget(Qt::LeftDockWidgetArea , analysisDataViewDockWidget);
    mMenuAnalysisDataViews->addAction(analysisDataViewDockWidget->toggleViewAction());

    return true;
}

bool MainWindow::newObjectView(const QVariant &settings)
{
    if( !mObectViewSplitter ){
        return false;
    }

    Views::ObjectView::ObjectView *view = mViewsManager->objectView(settings, this);
    mObectViewSplitter->addWidget(view);

    quint32 index = mObectViewSplitter->count();
    QAction *pAction = mMenuObjectViews->addAction( QString::fromLocal8Bit("绘制视图%1").arg( index ) );
    pAction->setCheckable( true );
    pAction->setChecked( true );

    connect( pAction, &QAction::triggered, [=](){
            if( mObectViewSplitter->widget( index -1 ) ){
                bool bShowSplitter = true;//判断mObectViewSplitter是否需要显示
                if( !pAction->isChecked() ){
                    bShowSplitter = false;
                    mObectViewSplitter->widget( index -1 )->hide();
                    for( int i=0; i<mObectViewSplitter->count(); i++ ){
                        if( mObectViewSplitter->widget( i )->isVisible() ){
                            bShowSplitter = true;
                        }
                    }
                }else{
                    mObectViewSplitter->widget( index -1 )->show();
                    bShowSplitter = true;
                }

                if( bShowSplitter ){
                    this->centralWidget()->show();
                }else{
                    this->centralWidget()->hide();
                }
            }
        }
    );

    return true;
}

void MainWindow::restoreDeviceSettings()
{
#if 1
    Devices::Can::DeviceSettings deviceSettings = Devices::Can::DeviceSettings{false, Devices::Can::DeviceSettings::ZLG, 41, 0};
    deviceSettings.mDeviceChannelSettings << Devices::Can::DeviceChannelSettings{6, 0, 0, 0, true, 500000, 2000000}
                                          << Devices::Can::DeviceChannelSettings{6, 0, 1, 1, true, 500000, 2000000};
#elif 1
    Devices::Can::DeviceSettings deviceSettings = Devices::Can::DeviceSettings{Devices::Can::DeviceSettings::GCAN, 6, 0};
    deviceSettings.mDeviceChannelSettings << Devices::Can::DeviceChannelSettings{6, 0, 0, 500000, 2000000} << Devices::Can::DeviceChannelSettings{6, 0, 1, true, 500000, 2000000};
#else
    // TS
    Devices::Can::DeviceSettings deviceSettings = Devices::Can::DeviceSettings{Devices::Can::DeviceSettings::TongXing, 1014, 0};
    deviceSettings.mDeviceChannelSettings
            << Devices::Can::DeviceChannelSettings{1014, 0, 0, 500000, 2000000}
            << Devices::Can::DeviceChannelSettings{1014, 0, 1, 500000, 2000000}
            << Devices::Can::DeviceChannelSettings{1014, 0, 2, 500000, 2000000}
            << Devices::Can::DeviceChannelSettings{1014, 0, 3, 500000, 2000000};
#endif

    QVariant settings = SETTINGS_GET_VALUE(QLatin1String(deviceSettingsKey));
    if (settings.isValid())
    {
        deviceSettings.setSettings(settings);
    }
    Devices::Can::DeviceSettings deviceSettings2 = deviceSettings;
    QVariant settings2 = SETTINGS_GET_VALUE(QLatin1String(deviceSettingsKey2));
    if (settings2.isValid())
    {
        deviceSettings2.setSettings(settings2);
    }

    deviceChanged(deviceSettings, deviceSettings2);
}

void MainWindow::restoreAnalysisDataViews()
{
    QList<QVariant> views;
    QVariant settings = SETTINGS_GET_VALUE(QLatin1String(analysisDataViewsKey));
    if (settings.type() == QVariant::Map) {
        QMap<QString, QVariant> set = settings.toMap();
        QList<QString> keys = set.keys();
        foreach(const QString &key, keys) {
            views << key;
        }
    } else {
        views = settings.toList();
    }
//    if (views.isEmpty())
    {
        views = QList<QVariant>{0, 4, 5, 6, 7};
    }

    foreach (const QVariant id, views)
    {
        newAnalysisDataView(id.toInt());
    }

    if (settings.type() == QVariant::Map)
    {
        mViewsManager->setAnalysisDataViewsSettings(settings);
    }
}

void MainWindow::restoreObjectViews()
{
    QList<QVariant> views;
    QVariant settings = SETTINGS_GET_VALUE(QLatin1String(objectViewsKey));
    views = settings.toList();
    if (views.isEmpty())
    {
        newObjectView(QVariant());
        newObjectView(QVariant());
    }
    else
    {
        foreach (const QVariant settings, views)
        {
            newObjectView(settings);
        }
    }
}

void MainWindow::restoreTrueSystemViews()
{
    bool show = SETTINGS_GET_VALUE(QLatin1String(trueSystemViewsKey), false).toBool();
    qDebug() << __FUNCTION__ << __LINE__ << show;
    if (show)
    {
        newTrueSystem();
    }
}

void MainWindow::restoreDebugSystemViews()
{
    bool show = SETTINGS_GET_VALUE(QLatin1String(debugSystemViewsKey), false).toBool();
    qDebug() << __FUNCTION__ << __LINE__ << show;
    if (show)
    {
        newDebugSystem();
    }
}

void MainWindow::restoreDataProcessDebugViews()
{
    bool show = SETTINGS_GET_VALUE(QLatin1String(dataProcessDebugViewsKey), false).toBool();
    qDebug() << __FUNCTION__ << __LINE__ << show;
    if (show)
    {
        newDebugProcess();
    }
}

void MainWindow::restoreCamera()
{
    QMap<QString, QVariant> settings = SETTINGS_GET_VALUE(QLatin1String(cameraViewsKey)).toMap();

    const QList<QCameraInfo> availableCameras = QCameraInfo::availableCameras();
    foreach (const QCameraInfo &cameraInfo, availableCameras)
    {
        if (settings.contains(cameraInfo.description() + cameraInfo.deviceName()))
        {
            addCamera(cameraInfo, settings[cameraInfo.description() + cameraInfo.deviceName()].toSize());
        }
    }
}

void MainWindow::restoreShortcutMarkers()
{
    newShortcutMarkers();
}

void MainWindow::restoreWindowState()
{
    if (!restoreGeometry(SETTINGS_GET_VALUE(QLatin1String(windowGeometryKey)).toByteArray())) {
        resize(900, 600); // size without window decoration
    }
    restoreState(SETTINGS_GET_VALUE(QLatin1String(windowStateKey)).toByteArray());

    QMap<QString, QVariant> dockSize = SETTINGS_GET_VALUE(QLatin1String(dockSizeKey)).toMap();
    QList<QDockWidget* > dwList = this->findChildren<QDockWidget*>();
    foreach(QDockWidget* dock,dwList){
        QSize size = dockSize[dock->objectName()].toSize();

        if (size.width()>= 0)
        {
            int nWidth = dock->width();
            if (nWidth <size.width())
                dock->setMinimumWidth(size.width());
            else
                dock->setMaximumWidth(size.width());
        }
        if (size.height()>= 0)
        {
            int nHeight = dock->height();
            if (nHeight <size.height())
                dock->setMinimumHeight(size.height());
            else
                dock->setMaximumHeight(size.height());
        }

        qDebug() << __FUNCTION__ << __LINE__ << dock->objectName() << size << dock->size();
    }

    QTimer::singleShot(0, this, [=](){
        QList<QDockWidget* > dwList = this->findChildren<QDockWidget*>();
        foreach(QDockWidget* dock,dwList){
            //如果只是设定最小宽度等信息会造成QDockWidget调整大小出现问题
            dock->setMaximumSize(QSize(16777215,16777215));
            dock->setMinimumSize(QSize(1,1));
        }
    });

    show();
}

void MainWindow::restoreAdcSystemViews()
{
    bool show = SETTINGS_GET_VALUE(QLatin1String(adcSystemViewsKey), false).toBool();
    if (show){
        showAdcAndHil();
    }
}

void MainWindow::saveSaveSettings()
{
    SETTINGS_SET_VALUE(QLatin1String(saveRunSaveNow), mActionRunSave->isChecked());
    SETTINGS_SET_VALUE(QLatin1String(saveAnalysisData), mSaveManager->actionSaveAnalysisData()->isChecked() );
    SETTINGS_SET_VALUE(QLatin1String(saveOldFormatKey), mSaveManager->actionSaveOldStyle()->isChecked() );
    SETTINGS_SET_VALUE(QLatin1String(saveCanAscKey), mSaveManager->actionSaveCANFrameASC()->isChecked() );
    SETTINGS_SET_VALUE(QLatin1String(saveCanBLFKey), mSaveManager->actionSaveCANFrameBLF()->isChecked() );
    SETTINGS_SET_VALUE(QLatin1String(saveVideoKey), mSaveManager->actionSaveVideo()->isChecked() );
    SETTINGS_SET_VALUE(QLatin1String(saveHeSaiLiderKey), mSaveManager->actionSaveHeSaiLider()->isChecked() );
}

void MainWindow::saveGeneralSetting()
{
    SETTINGS_SET_VALUE(QLatin1String(generalSettingsKey), mGeneralSettings.getSettings());
    HeSaiLiderWorker::Settings settings = mHeSaiLiderWorker->getConfig();
    SETTINGS_SET_VALUE(QLatin1String(hesaiSettingsKey), settings.getSettings());
}

void MainWindow::saveDeteStatisticalSetting()
{
    SETTINGS_SET_VALUE(QLatin1String(DeteStatisKey), mDeteStatistics->getSettings());
}

void MainWindow::restoreSaveSetting()
{
    bool bCheck = SETTINGS_GET_VALUE(QLatin1String(saveRunSaveNow), false).toBool();
    mActionRunSave->setChecked( bCheck );
    bCheck = SETTINGS_GET_VALUE(QLatin1String(saveAnalysisData), true).toBool();
    mSaveManager->actionSaveAnalysisData()->setChecked( bCheck );
    bCheck = SETTINGS_GET_VALUE(QLatin1String(saveOldFormatKey), true).toBool();
    mSaveManager->actionSaveOldStyle()->setChecked( bCheck );
    bCheck = SETTINGS_GET_VALUE(QLatin1String(saveCanAscKey), true).toBool();
    mSaveManager->actionSaveCANFrameASC()->setChecked( bCheck );
    bCheck = SETTINGS_GET_VALUE(QLatin1String(saveCanBLFKey), true).toBool();
    mSaveManager->actionSaveCANFrameBLF()->setChecked( bCheck );
    bCheck = SETTINGS_GET_VALUE(QLatin1String(saveVideoKey), true).toBool();
    mSaveManager->actionSaveVideo()->setChecked( bCheck );
    bCheck = SETTINGS_GET_VALUE(QLatin1String(saveHeSaiLiderKey), true).toBool();
    mSaveManager->actionSaveHeSaiLider()->setChecked( bCheck );
}

void MainWindow::restoreGeneralSetting()
{
    mGeneralSettings.setSettings(SETTINGS_GET_VALUE(QLatin1String(generalSettingsKey)));
    generalSettingsApply();

    HeSaiLiderWorker::Settings settings;
    settings.setSettings(SETTINGS_GET_VALUE(QLatin1String(hesaiSettingsKey)));
    mHeSaiLiderWorker->setConfig(settings);
}

void MainWindow::restoreDeteStatisticalSetting()
{
    mDeteStatistics->setSettings(SETTINGS_GET_VALUE(QLatin1String(DeteStatisKey)));
    mDeteStatistics->settingsApply();
}

void MainWindow::initMenuBar()
{

}

void MainWindow::initFileMenu()
{
    switch( mRole ){
    case ROLE_AFTER_SALES:
        break;
    case ROLE_DRIVE_TEST:
    {
        QMenu *menuFile = menuBar()->addMenu(QString::fromLocal8Bit("文件"));
        menuFile->addAction(mSaveManager->actionSave());
        menuFile->addAction(mSaveManager->actionSelectSavePath());
        menuFile->addAction(mSaveManager->actionOpenSavePath());
        menuFile->addSeparator();
        menuFile->addAction(mActionRunSave);
        menuFile->addSeparator();
        menuFile->addAction(mSaveManager->actionSaveAnalysisData());
        menuFile->addAction(mSaveManager->actionSaveOldStyle());
        menuFile->addAction(mSaveManager->actionSaveCANFrameASC());
        menuFile->addAction(mSaveManager->actionSaveCANFrameBLF());
        menuFile->addAction(mSaveManager->actionSaveVideo());
        menuFile->addAction(mSaveManager->actionSaveHeSaiLider());
    }
        break;
    default:
        break;
    }
}

void MainWindow::initRunMenu()
{
    mActionRun = new QAction( Icons::CHENGTECTRADARMASTER_START.icon(), QString::fromLocal8Bit(runStartActionText) );
    connect(mActionRun, &QAction::triggered, this, &MainWindow::run);

	mActionRunSave = new QAction(QString::fromLocal8Bit("运行即保存"), this);
	mActionRunSave->setCheckable(true);
    //mActionDisplayRawTarget = menuRun->addAction(QString::fromLocal8Bit("输出原始点"));
    mActionDisplayRawTarget = new QAction( QString::fromLocal8Bit("输出原始点") );
    mActionDisplayRawTarget->setCheckable(true);
//    mActionDisplayRawTarget->setChecked(true);
    connect(mActionDisplayRawTarget, &QAction::triggered, this, &MainWindow::radarOutputMode);

    //mActionDisplayTrackTarget = menuRun->addAction(QString::fromLocal8Bit("输出跟踪点"));
    mActionDisplayTrackTarget = new QAction( QString::fromLocal8Bit("输出跟踪点") );
    mActionDisplayTrackTarget->setCheckable(true);
//    mActionDisplayTrackTarget->setChecked(true);
    connect(mActionDisplayTrackTarget, &QAction::triggered, this, &MainWindow::radarOutputMode);

    mActionDisplay2DFFT = new QAction( QString::fromLocal8Bit("输出2DFFT") );
    mActionDisplay2DFFT->setCheckable(true);
    connect(mActionDisplay2DFFT, &QAction::triggered, this, &MainWindow::radarOutput2DFFT);

    //mActionRouseRadarAllTheTime = menuRun->addAction(QString::fromLocal8Bit("长时间唤醒雷达[BYD]"));
    mActionRouseRadarAllTheTime = new QAction( QString::fromLocal8Bit("长时间唤醒雷达[BYD]") );
    connect(mActionRouseRadarAllTheTime, &QAction::triggered, this, &MainWindow::wakeupRadar);

    //mActionRouseRadarAllTheTimeBAIC = menuRun->addAction(QString::fromLocal8Bit("长时间唤醒雷达[BAIC]"));
    mActionRouseRadarAllTheTimeBAIC = new QAction( QString::fromLocal8Bit("长时间唤醒雷达[BAIC]") );
    mActionRouseRadarAllTheTimeBAIC->setCheckable(true);
    connect(mActionRouseRadarAllTheTimeBAIC, &QAction::triggered, this, &MainWindow::wakeupRadar);

    //mActionRouseRadarAllTheTimeGEELY = menuRun->addAction(QString::fromLocal8Bit("长时间唤醒雷达[GEELY]"));
    mActionRouseRadarAllTheTimeGEELY = new QAction( QString::fromLocal8Bit("长时间唤醒雷达[GEELY]") );
    mActionRouseRadarAllTheTimeGEELY->setCheckable(true);
    connect(mActionRouseRadarAllTheTimeGEELY, &QAction::triggered, this, &MainWindow::wakeupRadar);

    mActionRouseRadarAllTheTimeHozon = new QAction( QString::fromLocal8Bit("长时间唤醒雷达[Hozon ]") );
    mActionRouseRadarAllTheTimeHozon->setCheckable(true);
    connect(mActionRouseRadarAllTheTimeHozon, &QAction::triggered, this, &MainWindow::wakeupRadar);

    mActionResolutionTest = new QAction( QString::fromLocal8Bit("保存数据") );
    mActionResolutionTest->setCheckable(true);
    connect(mActionResolutionTest, &QAction::triggered, this, &MainWindow::resolutionTest);

    mActionWeakObjTest = new QAction( QString::fromLocal8Bit("保存屏幕") );
    mActionWeakObjTest->setCheckable(true);
    connect(mActionWeakObjTest, &QAction::triggered, this, &MainWindow::weakObjTest);
    
    switch( mRole ){
    case ROLE_DRIVE_TEST:
//    case ROLE_AFTER_SALES:
    {
        QMenu *menuRun = menuBar()->addMenu(QString::fromLocal8Bit("运行"));
        menuRun->addAction( mActionRun );
        menuRun->addSeparator();
        menuRun->addAction( mActionDisplayRawTarget );
        menuRun->addAction( mActionDisplayTrackTarget );
        menuRun->addAction( mActionDisplay2DFFT );
        menuRun->addSeparator();
        menuRun->addAction( mActionRouseRadarAllTheTime );
        menuRun->addAction( mActionRouseRadarAllTheTimeBAIC );
        menuRun->addAction( mActionRouseRadarAllTheTimeGEELY );
        menuRun->addAction( mActionRouseRadarAllTheTimeHozon );
        menuRun->addAction( mActionResolutionTest );
        menuRun->addAction( mActionWeakObjTest );
    }
        break;
    default:
        break;
    }
}

void MainWindow::initViewMenu()
{
    mMenuViews = new QMenu( QString::fromLocal8Bit("视图"));
    mMenuAnalysisDataViews = mMenuViews->addMenu(QString::fromLocal8Bit("雷达数据视图"));
    mMenuObjectViews = mMenuViews->addMenu(QString::fromLocal8Bit("绘制视图"));

    switch( mRole ){
    case ROLE_DRIVE_TEST:
    {
        menuBar()->addMenu( mMenuViews );
    }
        break;
    default:
        break;
    }
}

void MainWindow::initSetMenu()
{
    //QMenu *menuSettings = menuBar()->addMenu(QString::fromLocal8Bit("设置"));
    QMenu *menuSettings = new QMenu(QString::fromLocal8Bit("设置"));

    QAction *actionGeneralSettings = menuSettings->addAction(QString::fromLocal8Bit("通用设置"));
    connect(actionGeneralSettings, &QAction::triggered, this, &MainWindow::generalSettings);
    QAction *actionHeSaiLiderSettings = menuSettings->addAction(QString::fromLocal8Bit("禾赛激光设置"));
    connect(actionHeSaiLiderSettings, &QAction::triggered, this, &MainWindow::heSaiLiderSettings);

    mActionCalculationSetting = menuSettings->addAction(QString::fromLocal8Bit("计算设置"));
    connect(mActionCalculationSetting, &QAction::triggered, this, &MainWindow::calculationSetting);
    QAction *actionFrameConvert = menuSettings->addAction(QString::fromLocal8Bit("协议转换设置"));
    connect( actionFrameConvert, &QAction::triggered, [=](){
        mFrameConvertForm->show();
    });
    QAction *channelAndFrameMonitor = menuSettings->addAction(QString::fromLocal8Bit("通道监控设置"));
    connect( channelAndFrameMonitor, &QAction::triggered, [=](){
        mChannelAndFrameMonitorForm->show();
    });

    QMenu* radarInfoSetting = menuSettings->addMenu( QString::fromLocal8Bit("雷达信息显示设置"));
    QAction* radarFaultAction = radarInfoSetting->addAction( QString::fromLocal8Bit("显示故障信息") );
    radarFaultAction->setCheckable( true );
    connect( radarFaultAction, &QAction::triggered, mViewsManager, &Views::ViewsManager::showFaultInfo );
    QAction* radarVersionAction = radarInfoSetting->addAction( QString::fromLocal8Bit("显示版本信息") );
    radarVersionAction->setCheckable( true );
    connect( radarVersionAction, &QAction::triggered, mViewsManager, &Views::ViewsManager::showVersionInfo );
    QAction* radarAngleAction = radarInfoSetting->addAction( QString::fromLocal8Bit("显示角度信息") );
    radarAngleAction->setCheckable( true );
    connect( radarAngleAction, &QAction::triggered, mViewsManager, &Views::ViewsManager::showAngleInfo );

    QAction *actionDeteStatisticalSettings = menuSettings->addAction(QString::fromLocal8Bit("检测概率信息"));
    connect(actionDeteStatisticalSettings, &QAction::triggered, this, &MainWindow::deteStatisticalSettings);

    switch( mRole ){
    case ROLE_DRIVE_TEST:
    {
        menuBar()->addMenu( menuSettings );
    }
        break;
    default:
        break;
    }
}

void MainWindow::initDeviceMenu()
{
    //QMenu *menuDevice = menuBar()->addMenu(QString::fromLocal8Bit("设备"));
    QMenu *menuDevice = new QMenu(QString::fromLocal8Bit("设备"));
    mActionSelectDevice = menuDevice->addAction(QString::fromLocal8Bit("选择设备"));
    connect(mActionSelectDevice, &QAction::triggered, this, &MainWindow::selectDevice);

    switch( mRole ){
    case ROLE_DRIVE_TEST:
    case ROLE_AFTER_SALES:
    {
        menuBar()->addMenu( menuDevice );
    }
        break;
    default:
        break;
    }
}

void MainWindow::initCameraMenu()
{
    //mMenuCamera = menuBar()->addMenu(QString::fromLocal8Bit("摄像头"));
    mMenuCamera = new QMenu(QString::fromLocal8Bit("摄像头"));
    connect(mMenuCamera, &QMenu::aboutToShow, this, &MainWindow::refreshCameraInfo);
    switch( mRole ){
    case ROLE_DRIVE_TEST:
    {
        menuBar()->addMenu( mMenuCamera );
    }
        break;
    default:
        break;
    }
}

void MainWindow::initFunctionMenu()
{
    //QMenu *menuFunctions = menuBar()->addMenu(QString::fromLocal8Bit("功能"));
    QMenu *menuFunctions = new QMenu(QString::fromLocal8Bit("功能"));
    /*QAction **/mActionShortcutMarkers = menuFunctions->addAction(QString::fromLocal8Bit("快捷标记"));
    connect(mActionShortcutMarkers, &QAction::triggered, this, &MainWindow::newShortcutMarkers);
    /*QAction **/mActionTrueSystem = menuFunctions->addAction(QString::fromLocal8Bit("真值系统"));
    connect(mActionTrueSystem, &QAction::triggered, this, &MainWindow::newTrueSystem);
    /*QAction **/mActionDebugSystem = menuFunctions->addAction(QString::fromLocal8Bit("调试系统"));
    connect(mActionDebugSystem, &QAction::triggered, this, &MainWindow::newDebugSystem);
    /*QAction **/mActionDebugProcess = menuFunctions->addAction(QString::fromLocal8Bit("数据处理调试"));
    connect(mActionDebugProcess, &QAction::triggered, this, &MainWindow::newDebugProcess);

    /*QAction* */mActionSWVersion = menuFunctions->addAction(QString::fromLocal8Bit("获取版本"));
    connect(mActionSWVersion, &QAction::triggered, this, &MainWindow::getRadarVersion);
    switch( mRole ){
    case ROLE_DRIVE_TEST:
    {
        menuBar()->addMenu( menuFunctions );
    }
        break;
    default:
        break;
    }
}

void MainWindow::initToolMenu()
{
    QMenu *menuTools = new QMenu(QString::fromLocal8Bit("工具"));
    mActionRadarConfig = new QAction(QString::fromLocal8Bit("雷达配置"));
    connect(mActionRadarConfig, &QAction::triggered, this, &MainWindow::radarConfig);
    mActionCanCommandLine = new QAction(QString::fromLocal8Bit("命令行"));
    connect(mActionCanCommandLine, &QAction::triggered, this, &MainWindow::canCommandLine);
    mActionAfterSalecalibration = new QAction(QString::fromLocal8Bit("动态(售后)标定"));
    connect(mActionAfterSalecalibration, &QAction::triggered, this, &MainWindow::afterSalecalibration);
    mActionStaticSalecalibration = new QAction(QString::fromLocal8Bit("静态(下线)标定"));
    connect(mActionStaticSalecalibration, &QAction::triggered, this, &MainWindow::staticSalecalibration);
    mActionRadarCalibration = new QAction(QString::fromLocal8Bit("前雷达标定"));
    connect(mActionRadarCalibration, &QAction::triggered, this, &MainWindow::radarCalibration);
    QAction* actionRadarReset = new QAction(QString::fromLocal8Bit("雷达复位"));
    connect(actionRadarReset, &QAction::triggered, this, &MainWindow::radarReset);
    QAction* actionBinaryFileTools = new QAction(QString::fromLocal8Bit("Binary File Tools"));
    connect(actionBinaryFileTools, &QAction::triggered, this, &MainWindow::showBinalryFileTools);
    QAction* actionFileBatchParsing = new QAction(QString::fromLocal8Bit("数据批量处理"));
    connect(actionFileBatchParsing, &QAction::triggered, this, &MainWindow::fileBatchParsing);
    QAction* actionAdc = new QAction(QString::fromLocal8Bit("ADC"));
    connect(actionAdc, &QAction::triggered, this, &MainWindow::showAdcAndHil);
    QAction* actionEol = new QAction(QString::fromLocal8Bit("EOL"));
    connect(actionEol, &QAction::triggered, this, &MainWindow::showEol);
    QAction* actionUDSToolBox = new QAction(QString::fromLocal8Bit("UDS工具箱"));
    connect(actionUDSToolBox, &QAction::triggered, this, &MainWindow::UDSToolBox);
    QAction* actionECUUpdateToolBox = new QAction(QString::fromLocal8Bit("ECU刷写"));
    connect(actionECUUpdateToolBox, &QAction::triggered, this, &MainWindow::ECUUpdate);
    QAction* actionWakeUpTest = new QAction(QString::fromLocal8Bit("唤醒测试"));
    connect(actionWakeUpTest, &QAction::triggered, this, &MainWindow::wakeUpTest);

    switch( mRole ){
    case ROLE_DRIVE_TEST:
        menuBar()->addMenu( menuTools );
        menuTools->addAction( mActionRadarConfig );
        menuTools->addAction( mActionCanCommandLine );
        menuTools->addAction( mActionAfterSalecalibration );
        menuTools->addAction( mActionStaticSalecalibration );
        menuTools->addAction( mActionRadarCalibration);
        menuTools->addAction( actionRadarReset );
        menuTools->addAction( actionBinaryFileTools );
        menuTools->addAction( actionFileBatchParsing );
        menuTools->addAction( actionAdc );
        menuTools->addAction( actionEol );
        menuTools->addAction( actionUDSToolBox );
        menuTools->addAction( actionECUUpdateToolBox );
        menuTools->addAction( actionWakeUpTest );
        break;
    case ROLE_AFTER_SALES:
        menuBar()->addMenu( menuTools );
        menuTools->addAction( mActionAfterSalecalibration );
        menuTools->addAction( mActionRadarCalibration );
//        menuTools->addAction( actionUDSToolBox );
        break;
    default:
        break;
    }
}

void MainWindow::initHelpMenu()
{
    QMenu *menuHelp = new QMenu(QString::fromLocal8Bit("帮助"));
    QAction *actionUserManual = menuHelp->addAction(QString::fromLocal8Bit("用户手册"));
    connect(actionUserManual, &QAction::triggered, this, &MainWindow::userManual);
    QAction *actionAbout = menuHelp->addAction(QString::fromLocal8Bit("关于%1").arg(Constants::MASTER_DISPLAY_NAME));
    connect(actionAbout, &QAction::triggered, this, &MainWindow::about);

    switch( mRole ){
    case ROLE_DRIVE_TEST:
    case ROLE_AFTER_SALES:
        menuBar()->addMenu( menuHelp );
        break;
    default:
        break;
    }
}


void MainWindow::registerDefaultActions()
{
    initFileMenu();
    initRunMenu();
    initViewMenu();
    initSetMenu();
    initDeviceMenu();
    initCameraMenu();
    initFunctionMenu();
    initToolMenu();
    initHelpMenu();

    initToolBar();
}

void MainWindow::readSettings()
{
    restoreDeviceSettings();
    restoreAnalysisDataViews();
    restoreTrueSystemViews();
    restoreDebugSystemViews();
    restoreDataProcessDebugViews();
    restoreCamera();
    restoreShortcutMarkers();
    restoreObjectViews();
    restoreAdcSystemViews();
    restoreSaveSetting();
    restoreGeneralSetting();
    restoreDeteStatisticalSetting();
    mActionDisplayRawTarget->setChecked(SETTINGS_GET_VALUE(QLatin1String(outputTargetRaw), true).toBool());
    mActionDisplayTrackTarget->setChecked(SETTINGS_GET_VALUE(QLatin1String(outputTargetTrack), true).toBool());
    mActionDisplay2DFFT->setChecked(SETTINGS_GET_VALUE(QLatin1String(output2DFFT), true).toBool());
    mSaveManager->setSavePath(SETTINGS_GET_VALUE(QLatin1String(projectSavePathKey), mSaveManager->savePath()).toString());
    mChannelAndFrameMonitorForm->setLogSavePath( SETTINGS_GET_VALUE(QLatin1String(projectSavePathKey), mSaveManager->savePath()).toString() );
}

void MainWindow::saveSettings()
{
    SETTINGS_SET_VALUE(QLatin1String(outputTargetRaw), mActionDisplayRawTarget->isChecked());
    SETTINGS_SET_VALUE(QLatin1String(outputTargetTrack), mActionDisplayTrackTarget->isChecked());
    SETTINGS_SET_VALUE(QLatin1String(output2DFFT), mActionDisplay2DFFT->isChecked());
    SETTINGS_SET_VALUE(QLatin1String(deviceSettingsKey), mDeviceManager->deviceSettings().getSettings());
    SETTINGS_SET_VALUE(QLatin1String(deviceSettingsKey2), mDeviceManager->deviceSettings_2().getSettings());
    SETTINGS_SET_VALUE(QLatin1String(projectSavePathKey), mSaveManager->savePath());
    SETTINGS_SET_VALUE(QLatin1String(analysisDataViewsKey), mViewsManager->getAnalysisDataViewsSettings());
    SETTINGS_SET_VALUE(QLatin1String(objectViewsKey), mViewsManager->getObjectViewsSettings());
    SETTINGS_SET_VALUE(QLatin1String(trueSystemViewsKey), mViewsManager->trueSystemViewExisted());
    SETTINGS_SET_VALUE(QLatin1String(debugSystemViewsKey), mDAQManager->daqControlExisted());
    SETTINGS_SET_VALUE(QLatin1String(dataProcessDebugViewsKey), mDataProcessForm != 0);
    mDAQManager->saveSettings();
    saveCameraSettings();
    saveWindowSettings();
    saveSaveSettings();
    saveGeneralSetting();
    saveDeteStatisticalSetting();

    if( mDAQCtrl ){
        mDAQCtrl->saveSettings();
    }
}

void MainWindow::saveCameraSettings()
{
    QMap<QString, QVariant> settings;
    QMap<QString /*description + deviecname*/, Camera::MulitCamera*>::iterator it = mListCameraInfo.begin();
    for (; it != mListCameraInfo.end(); ++it)
    {
        settings[it.key()] = it.value()->size();
    }
    SETTINGS_SET_VALUE(QLatin1String(cameraViewsKey), settings);
}

void MainWindow::saveWindowSettings()
{
    SETTINGS_SET_VALUE(QLatin1String(windowGeometryKey), saveGeometry());
    SETTINGS_SET_VALUE(QLatin1String(windowStateKey), saveState());

    QMap<QString, QVariant> dockSize;
    QList<QDockWidget* > dwList = this->findChildren<QDockWidget*>();
    foreach(QDockWidget* dock,dwList){
        dockSize[dock->objectName()] = dock->size();
        qDebug() << __FUNCTION__ << __LINE__ << dock->objectName() << dock->size();
    }
    SETTINGS_SET_VALUE(QLatin1String(dockSizeKey), dockSize);
}

void MainWindow::initCentralWidget()
{
    QWidget *centralwidget = new QWidget(this);
    centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
    setCentralWidget(centralwidget);
    QVBoxLayout *layout = new QVBoxLayout(centralwidget);
    layout->setContentsMargins(0, 0, 0, 0);

    switch( mRole ){
    case ROLE_DRIVE_TEST:
        mObectViewSplitter = new QSplitter(this);
        layout->addWidget(mObectViewSplitter);
        break;
    case ROLE_AFTER_SALES:
        mUDSBox = new UDSToolBoxForm( mDeviceManager, this);
        layout->addWidget( mUDSBox );
        break;
    default:
        break;
    }
}

void MainWindow::initStatusBar()
{
    switch( mRole ){
    case ROLE_DRIVE_TEST:
    {// 初始化状态栏
        mStatusBar = new QStatusBar(this);
        this->setStatusBar(mStatusBar);

        QLabel *labelSystemTime = new QLabel("1970-01-01 00:00:00.000", this);
        labelSystemTime->setMinimumWidth(100);
        // 将初始化的标签添加到底部状态栏上
        mStatusBar->addWidget(labelSystemTime);

        mLabelSaveFilename = new QLabel;
        connect(mSaveManager, &SaveManager::saveFilename, this, [=](const QString &projectPath) {
            mLabelSaveFilename->setText(projectPath);
        });

        mStatusBar->addPermanentWidget(mLabelSaveFilename);

        QLabel *labelRunTime = new QLabel("0h-0m-0s", this);
        mStatusBar->addPermanentWidget(labelRunTime);

        mTimerSystemTime = new QTimer(this);
        connect(mTimerSystemTime, &QTimer::timeout, [=](){
            labelSystemTime->setText(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"));
            if (mRunStartTime) {
                quint64 runTime = QDateTime::currentMSecsSinceEpoch() - mRunStartTime;
                labelRunTime->setText(QString("%1 %2:%3:%4.%5")
                                      .arg(runTime / 3600000 / 60 % 24, 2, 10, QChar('0'))
                                      .arg(runTime / 3600000 % 60, 2, 10, QChar('0'))
                                      .arg(runTime / 60000 % 60, 2, 10, QChar('0'))
                                      .arg(runTime / 1000 % 60, 2, 10, QChar('0'))
                                      .arg(runTime % 1000, 3, 10, QChar('0'))
                                      );
            }
        });
//        mTimerSystemTime->start(50);
        mTimerWakeupRadar = new QTimer( this );
        connect( mTimerWakeupRadar, &QTimer::timeout, this, &MainWindow::rouseRadarAllTheTime );
    }
        break;
    default:
        break;
    }
}

void MainWindow::initToolBar()
{
    QToolBar *toolBar = new QToolBar(this);
    toolBar->setWindowTitle(QString::fromLocal8Bit("工具栏"));
    toolBar->setObjectName(QString::fromUtf8("toolBar"));
    addToolBar(Qt::TopToolBarArea, toolBar);

    switch( mRole ){
    case ROLE_DRIVE_TEST:
        toolBar->addAction(mActionRun);
        toolBar->addAction(mSaveManager->actionSave());
        toolBar->addAction(mSaveManager->actionOpenSavePath());
        toolBar->addSeparator();
        toolBar->addAction(mActionSelectDevice);
        toolBar->addSeparator();
        toolBar->addAction( mActionSWVersion );
        toolBar->addAction(mActionDisplayRawTarget);
        toolBar->addAction(mActionDisplayTrackTarget);
        toolBar->addAction(mActionRouseRadarAllTheTime);
        toolBar->addAction(mActionRouseRadarAllTheTimeBAIC);
        toolBar->addAction(mActionRouseRadarAllTheTimeGEELY);
        toolBar->addAction(mActionRouseRadarAllTheTimeHozon);
        toolBar->addAction(mActionResolutionTest);
        toolBar->addAction(mActionWeakObjTest);
        toolBar->addSeparator();
        toolBar->addAction(mActionShortcutMarkers);
        toolBar->addAction(mActionDebugSystem);
        toolBar->addAction(mActionTrueSystem);
        toolBar->addSeparator();
        toolBar->addWidget(mViewsManager->comboBoxRadarType());
        break;
    case ROLE_AFTER_SALES:
        toolBar->addAction(mActionRun);
        toolBar->addSeparator();
        toolBar->addAction(mActionSelectDevice);
        break;
    default:
        toolBar->addAction(mActionRun);
        toolBar->addAction(mSaveManager->actionSave());
        toolBar->addAction(mSaveManager->actionOpenSavePath());
        toolBar->addAction(mActionSelectDevice);

        toolBar->addAction(mActionAfterSalecalibration);
        toolBar->addAction(mActionRadarCalibration);

        toolBar->addAction(mActionCanCommandLine);
        break;
    }


}

void MainWindow::setupUi()
{
    DockOptions opts;
    opts |= AnimatedDocks
            | AllowNestedDocks
            | AllowTabbedDocks
//            | ForceTabbedDocks
//            | VerticalTabs
//            | GroupedDragging
            ;

    QMainWindow::setDockOptions(opts);

//    QWidget *centralwidget = new QWidget(this);
//    centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
//    setCentralWidget(centralwidget);

//    mObectViewSplitter = new QSplitter(this);
//    QVBoxLayout *layout = new QVBoxLayout(centralwidget);
//    layout->setContentsMargins(0, 0, 0, 0);
//    layout->addWidget(mObectViewSplitter);
    initCentralWidget();

//    // 初始化状态栏
//    mStatusBar = new QStatusBar(this);
//    this->setStatusBar(mStatusBar);
//    QLabel *labelSystemTime = new QLabel("1970-01-01 00:00:00.000", this);
//    labelSystemTime->setMinimumWidth(100);
//    // 将初始化的标签添加到底部状态栏上
//    mStatusBar->addWidget(labelSystemTime);

//    QLabel *labelRunTime = new QLabel("0h-0m-0s", this);
//    mStatusBar->addPermanentWidget(labelRunTime);

//    mTimerSystemTime = new QTimer(this);
//    connect(mTimerSystemTime, &QTimer::timeout, [=](){
//        labelSystemTime->setText(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"));
//        if (mRunStartTime) {
//            quint64 runTime = QDateTime::currentMSecsSinceEpoch() - mRunStartTime;
//            labelRunTime->setText(QString("%1 %2:%3:%4.%5")
//                                  .arg(runTime / 3600000 / 60 % 24, 2, 10, QChar('0'))
//                                  .arg(runTime / 3600000 % 60, 2, 10, QChar('0'))
//                                  .arg(runTime / 60000 % 60, 2, 10, QChar('0'))
//                                  .arg(runTime / 1000 % 60, 2, 10, QChar('0'))
//                                  .arg(runTime % 1000, 3, 10, QChar('0'))
//                                  );
//        }
//    });
//    mTimerSystemTime->start(50);

//    mTimerWakeupRadar = new QTimer( this );
//    connect( mTimerWakeupRadar, &QTimer::timeout, this, &MainWindow::rouseRadarAllTheTime );
    initStatusBar();
}

void MainWindow::initRole()
{
    QSettings setting("config.ini",QSettings::IniFormat);
    QString role = setting.value("USER/ROLE").toString();
    if( role.isEmpty() || role == "ROLE_DRIVE_NULL" ){
        mRole = ROLE_DRIVE_NULL;
    }else if( role == "ROLE_DRIVE_TEST" ){
        mRole = ROLE_DRIVE_TEST;
    }else if( role == "ROLE_AFTER_SALES" ){
        mRole = ROLE_AFTER_SALES;
    }

    if( mRole != ROLE_DRIVE_TEST ){
        mSaveManager->setSaveAnalysisData( false );
    }
}

QString MainWindow::roleStr()
{
    switch( mRole ){
    case ROLE_AFTER_SALES:
        return "ROLE_AFTER_SALES";
    case ROLE_DRIVE_TEST:
        return "ROLE_DRIVE_TEST";
    default:
        return "ROLE_DRIVE_NULL";
    }
}

} // namespace Internal
} // namespace Core
