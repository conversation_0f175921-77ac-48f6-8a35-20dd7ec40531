﻿#ifndef CTANALYSISWORKER_H
#define CTANALYSISWORKER_H

#include <QObject>
#include <QTimer>

#include "devices/canframe.h"

class CTAnalysisWorker : public QObject
{
    Q_OBJECT
public:
    enum RawState {
        Opened,
        Closed
    };

    explicit CTAnalysisWorker(QObject *parent = nullptr);

    void newState();

signals:
    void stateChanged(bool opend);

public slots:
    void canFrame(const Devices::Can::CanFrame &frame);

private slots:
    void isOpened();

private:
    bool mHeader{false};
    bool mTrail{false};
    bool mBody{false};

    QTimer mTimer;
};

#endif // CTANALYSISWORKER_H
