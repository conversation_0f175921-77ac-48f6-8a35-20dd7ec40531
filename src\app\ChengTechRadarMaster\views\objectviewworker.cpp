﻿#include "objectviewworker.h"


#include <QDebug>

#define ALARM_PIXMAP_SIZE 8

namespace Views {
namespace ObjectView {

ObjectViewWorker::ObjectViewWorker(QObject *parent) : QObject(parent)
{
    mPixmapAlarm = QPixmap(":/views/images/alarm.png");
    mPixmapEarlyWarning = QPixmap(":/views/images/earlywarning.png");

    mPixmapAlarm = mPixmapAlarm.scaled(ALARM_PIXMAP_SIZE, ALARM_PIXMAP_SIZE, Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
    mPixmapEarlyWarning = mPixmapEarlyWarning.scaled(ALARM_PIXMAP_SIZE, ALARM_PIXMAP_SIZE, Qt::IgnoreAspectRatio, Qt::SmoothTransformation);

    for (int i = 4; i < 8; ++i)
    {
        ObjectData &objectData = mObjectDatas[i];
        objectData.mObjectShowConfigs.mObjectShow[FrameRawTarget] = true;
        objectData.mObjectShowConfigs.mObjectShow[FrameTrackTarget] = true;
    }
}

void ObjectViewWorker::objectCoordinateSettingsChanged(void *settings)
{
    mObjectCoordinateSettings = *((ObjectCoordinateSettings*)settings);
}

void ObjectViewWorker::calculateFinished(quint8 radarID, const AnalysisData &analysisData)
{
    qDebug() << __FUNCTION__ << __LINE__ << "message";
    // 不能整体赋值，会覆盖真值和其它不同步的目标点
    // mAnalysisDatas[radarID] = *(mCalculationWorker->mAnalysisDatas + radarID);
    for (int i = 0; i < FrameTargetCount; ++i)
    {
        mAnalysisDatas[radarID].mTargets[i] = analysisData.mTargets[i];
    }
    mAnalysisDatas[radarID].mAlarmData = analysisData.mAlarmData;                       // 告警信息
    mAnalysisDatas[radarID].mVehicleData = analysisData.mVehicleData;                   // 汽车数据
    mAnalysisDatas[radarID].mEndFrameData = analysisData.mEndFrameData;
    mAnalysisDatas[radarID].mRadarID = analysisData.mRadarID;
    mAnalysisDatas[radarID].mValid = true; //analysisData.mValid;
}

void ObjectViewWorker::calculateTargetFinished(quint8 radarID, int frameType, const Targets &target)
{
    qDebug() << __FUNCTION__ << __LINE__ << "message";
    switch (frameType)
    {
    case Frame3Track:
        mAnalysisDatas[radarID].mLockTargets = target;
        break;
    case Frame16Track:
        mAnalysisDatas[radarID].m16Targets = target;
        break;
    }
}

} // namespace ObjectView
} // namespace Views
