﻿#include "calibrationsworker.h"

#include "utils/utils.h"

#include "uds.h"
#include <QThread>
#include <QDebug>

CalibrationWorker::CalibrationWorker(Devices::Can::DeviceManager *deviceManager, QObject *parent) : QObject(parent), mUDS(new Functions::UDS(deviceManager, this))
{
    mUDS->canUpdate(true);
}

void CalibrationWorker::setProtocolType(CalibrationWorker::ProtocolType protocol, quint8 radarID)
{
    mProtocolType = protocol;
    mRadarID = radarID;
    mUDS->setResponseID(mResponseAddress[mProtocolType][mRadarID]);
}

void CalibrationWorker::canFrame(const Devices::Can::CanFrame &frame)
{
    if (frame.channelIndex() != mUDS->channelIndex() || mUDS->responseID() != frame.id()) {
        return;
    }
    mUDS->appendCanFrame(frame);
}

#define SEND_DATA_HEX_RESPONSE(ID, DATA, MESSAGE) \
    responseFrame.clear(); \
    emit message(MESSAGE); \
    if (!mUDS->sendData(ID, QString(DATA), &responseFrame)) { \
        emit message(MESSAGE + QString::fromLocal8Bit("失败！") + mUDS->errorString()); \
        emit calibrationFinished(false); \
        return; \
    } \
    emit message(MESSAGE + QString::fromLocal8Bit("成功"));

void CalibrationWorker::start()
{
    mCalibrationCount = 0;
    switch (mCalibrationType) {
    case AfterSale:
        afterSaleCalibration();
        break;
    case OffLineCalibration:
        offlineSaleCalibration();
        break;
    case ExitFactoryMode:
        exitFactoryMode();
        break;
    }
}

void CalibrationWorker::stop()
{
    switch (mCalibrationType) {
    case AfterSale:
        stopAfterSaleCalibration();
        break;
    case OffLineCalibration:
        stopOfflineCalibration();
        break;
    default:
        break;
    }
}

void CalibrationWorker::read()
{
    mCalibrationCount++;
    switch (mCalibrationType) {
    case AfterSale:
        readAfterSaleCalibration();
        break;
    case OffLineCalibration:
        readOfflineCalibration();
        break;
    default:
        break;
    }
}

void CalibrationWorker::afterSaleCalibration()
{
    qDebug() << __FUNCTION__ << __LINE__ << "message";
    UDSFrame responseFrame;
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "10 03", QString::fromLocal8Bit("进入扩展模式【售后】"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "27 01", QString::fromLocal8Bit("请求种子")); // 请求种子
    uint32_t seed{0};
    memcpy(&seed, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);
    uint32_t key = 0;
    switch (mProtocolType) {
    case ProtocolBYD220:
        key = SeedToKey(seed, mMASK[mProtocolType][mRadarID]);
        break;
    case PtotocolHSAE:
        key = SeedToKeyHASE(seed, mMASK[mProtocolType][mRadarID]);
        qDebug() << __FUNCTION__ << __LINE__ << QString::number(mMASK[mProtocolType][mRadarID], 16) << QString::number(seed, 16) << QString::number(key, 16);
        break;
    default:
        break;
    }
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], QString("27 02 %1").arg(key, 8, 16, QLatin1Char('0')), QString::fromLocal8Bit("密钥验证")); // 发送密钥

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "31 01 03 02", QString::fromLocal8Bit("开启SDA校准"));
    emit message(QString::fromLocal8Bit("启动车辆开始标定 ..."));
    emit calibrationStarted();
}

void CalibrationWorker::stopAfterSaleCalibration()
{
    UDSFrame responseFrame;
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "31 02 03 02", QString::fromLocal8Bit("校准终止【售后】"));
}

void CalibrationWorker::readAfterSaleCalibration()
{
    UDSFrame responseFrame;
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "31 03 03 02", QString::fromLocal8Bit("读取校准进度和结果"));
    emit message(responseFrame.mData.toHex(' '));

    quint8 process = responseFrame.mData[6];
    if ((responseFrame.mData[4] & 0xF) == 0x01)
    {
        emit message(QString::fromLocal8Bit("校准正在执行 【%1%】").arg(process));
        return;
    }

//    emit message(index, responseFrame.mData.toHex(' '));
    QString text;
    switch (responseFrame.mData[4] & 0xF)
    {
    case 0x0:
        text.append(QString::fromLocal8Bit("【校准不在执行】"));
        break;
    case 0x1:
        text.append(QString::fromLocal8Bit("【校准正在执行】"));
        break;
    case 0x2:
        text.append(QString::fromLocal8Bit("【校准写入NVM失败】"));
        break;
    case 0x3:
        text.append(QString::fromLocal8Bit("【校准执行超时】"));
        break;
    case 0x4:
        text.append(QString::fromLocal8Bit("【校准正确执行完毕】"));
        break;
    case 0x5:
        text.append(QString::fromLocal8Bit("【校准执行中止】"));
        break;
    }
    switch (responseFrame.mData[4] & 0xF0)
    {
    case 0x00:
        text.append(QString::fromLocal8Bit("【校准结果未出】"));
        break;
    case 0x10:
        text.append(QString::fromLocal8Bit("【校准结果不正确】"));
        break;
    case 0x20:
        text.append(QString::fromLocal8Bit("【校准结果正确】"));
        break;
    }

    text.append(QString::fromLocal8Bit("【"));
    if (responseFrame.mData[5] & 0x1)
    {
        text.append(QString::fromLocal8Bit("速度过慢"));
    }
    if (responseFrame.mData[5] & 0x2)
    {
        text.append(QString::fromLocal8Bit("速度过快"));
    }
    if (responseFrame.mData[5] & 0x4)
    {
        text.append(QString::fromLocal8Bit("横摆角过大"));
    }
    if (responseFrame.mData[5] & 0x8)
    {
        text.append(QString::fromLocal8Bit("加速度过大"));
    }
    if (responseFrame.mData[5] & 0x10)
    {
        text.append(QString::fromLocal8Bit("目标不充分"));
    }
    if (responseFrame.mData[5] & 0x20)
    {
        text.append(QString::fromLocal8Bit("雷达失明"));
    }
    text.append(QString::fromLocal8Bit("】"));

    text.append(QString::fromLocal8Bit("【"));
    switch (responseFrame.mData[11])
    {
    case 0x00:
        text.append(QString::fromLocal8Bit("校准初始化"));
        break;
    case 0x01:
        text.append(QString::fromLocal8Bit("校准中"));
        break;
    case 0x02:
        text.append(QString::fromLocal8Bit("校准数据采集完成"));
        break;
    case 0x03:
        text.append(QString::fromLocal8Bit("预留"));
        break;
    case 0x04:
        text.append(QString::fromLocal8Bit("校准已保存"));
        break;
    case 0x05:
        text.append(QString::fromLocal8Bit("校准超时"));
        break;
    case 0x06:
        text.append(QString::fromLocal8Bit("角度超范围"));
        break;
    case 0x07:
        text.append(QString::fromLocal8Bit("横摆角未接入"));
        break;
    case 0x08:
        text.append(QString::fromLocal8Bit("车速未接入"));
        break;
    case 0x09:
        text.append(QString::fromLocal8Bit("用户退出"));
        break;
    case 0x0A:
        text.append(QString::fromLocal8Bit("其他"));
        break;
    case 0x0B:
        text.append(QString::fromLocal8Bit("相关系数过低"));
        break;
    default:
        break;
    }
    text.append(QString::fromLocal8Bit("】"));

    responseFrame.mData[6];

    double angle = 0.0;
    quint16 _angle = (quint16)((((quint16)responseFrame.mData[7]) << 8) + (quint8)responseFrame.mData[8]);
    if (_angle < 0x8000)
    {
        angle = _angle * 0.01;
    }
    else
    {
        angle = (_angle - 0xFFFF) * 0.01;
    }
    text.append(QString::fromLocal8Bit("【水平偏差角度 %1°】").arg(angle));
    angle = 0.0;
    _angle = (quint16)((((quint16)responseFrame.mData[9]) << 8) + (quint8)responseFrame.mData[10]);
    if (_angle < 0x8000)
    {
        angle = _angle * 0.01;
    }
    else
    {
        angle = (_angle - 0xFFFF) * 0.01;
    }
    text.append(QString::fromLocal8Bit("【俯仰角度 %1°】").arg(angle));

    emit message(text);

    if (responseFrame.mData.data()[4] != 0x24)
    {
//        stop();
        return;
    }

    emit calibrationFinished(responseFrame.mData.data()[4] == 0x24);
}

void CalibrationWorker::offlineSaleCalibration()
{
    qDebug() << __FUNCTION__ << __LINE__ << "message";
    UDSFrame responseFrame;
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "10 03", QString::fromLocal8Bit("进入扩展模式【下线】"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "27 01", QString::fromLocal8Bit("请求种子")); // 请求种子
    uint32_t seed{0};
    memcpy(&seed, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);
    uint32_t key = 0;
    switch (mProtocolType) {
    case ProtocolBYD220:
        key = SeedToKey(seed, mMASK[mProtocolType][mRadarID]);
        break;
    case PtotocolHSAE:
        key = SeedToKeyHASE(seed, mMASK[mProtocolType][mRadarID]);
        break;
    default:
        break;
    }
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], QString("27 02 %1").arg(key, 8, 16, QLatin1Char('0')), QString::fromLocal8Bit("密钥验证")); // 发送密钥

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "22 F1 94", QString::fromLocal8Bit("读取软件编码"));

#if 0
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "2E F1 90", QString::fromLocal8Bit("写入VIN码"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "22 F1 90", QString::fromLocal8Bit("读取VIN码"));
#endif

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "22 F1 95", QString::fromLocal8Bit("读取ECU软件版本"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "14 FF FF FF", QString::fromLocal8Bit("清除DTC"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "19 02 09", QString::fromLocal8Bit("读取DTC"));
    emit message(responseFrame.mData.toHex(' '));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "2E 34 01 01", QString::fromLocal8Bit("打开产线模式"));

    emit calibrationStarted();
}

void CalibrationWorker::stopOfflineCalibration()
{
    UDSFrame responseFrame;
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "31 02 54 DF", QString::fromLocal8Bit("校准终止【下线】"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "2E 34 01 00", QString::fromLocal8Bit("关闭工厂模式"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "14 FF FF FF", QString::fromLocal8Bit("清除DTC"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "19 02 09", QString::fromLocal8Bit("读取DTC"));
    emit message(responseFrame.mData.toHex(' '));
}

void CalibrationWorker::readOfflineCalibration()
{
    UDSFrame responseFrame;
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "22 34 01", QString::fromLocal8Bit("读取产线模式"));
    emit message(responseFrame.mData.toHex(' '));
    if ((responseFrame.mData[3] & 0xFF) != 0x01) {
        emit calibrationFinished(false);
        return;
    }

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "31 01 54 DF", QString::fromLocal8Bit("开始标定"));

    emit message(QString::fromLocal8Bit("标定中(1500ms) ..."));
    QThread::msleep(1500);

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "31 03 54 DF", QString::fromLocal8Bit("读取并写入偏置角度"));
    emit message(responseFrame.mData.toHex(' '));

    if ((responseFrame.mData[4] & 0xF) == 0x01)
    {
        emit message(QString::fromLocal8Bit("标定进行中..."));
        mCalibrationCount--;
        return;
    }

//    emit message(index, responseFrame.mData.toHex(' '));
    QString text;
    switch (responseFrame.mData[4] & 0xF)
    {
    case 0x0:
        text.append(QString::fromLocal8Bit("【标定未开始】"));
        break;
    case 0x1:
        text.append(QString::fromLocal8Bit("【标定进行中】"));
        break;
    case 0x2:
        text.append(QString::fromLocal8Bit("【标定数据写入未完成】"));
        break;
    case 0x3:
        text.append(QString::fromLocal8Bit("【标定超时】"));
        break;
    case 0x4:
        text.append(QString::fromLocal8Bit("【标定结束】"));
        break;
    case 0x5:
        text.append(QString::fromLocal8Bit("【标定异常中断】"));
        break;
    }
    switch (responseFrame.mData[4] & 0xF0)
    {
    case 0x00:
        text.append(QString::fromLocal8Bit("【无有效标定结果】"));
        break;
    case 0x10:
        text.append(QString::fromLocal8Bit("【标定失败】"));
        break;
    case 0x20:
        text.append(QString::fromLocal8Bit("【标定成功】"));
        break;
    }

    double angle = 0.0;
    quint16 _angle = (quint16)((((quint16)responseFrame.mData[5]) << 8) + (quint8)responseFrame.mData[6]);
    if (_angle < 0x8000)
    {
        angle = _angle * 0.01;
    }
    else
    {
        angle = (_angle - 0xFFFF) * 0.01;
    }
    text.append(QString::fromLocal8Bit("【水平角度偏差 %1°】").arg(angle));
    angle = 0.0;
    _angle = (quint16)((((quint16)responseFrame.mData[7]) << 8) + (quint8)responseFrame.mData[8]);
    if (_angle < 0x8000)
    {
        angle = _angle * 0.01;
    }
    else
    {
        angle = (_angle - 0xFFFF) * 0.01;
    }
    text.append(QString::fromLocal8Bit("【垂直角度偏差 %1°】").arg(angle));

    emit message(text);

    if (responseFrame.mData.data()[4] != 0x24)
    {
        if (mCalibrationCount >= 2) {
            stopOfflineCalibration();
            emit calibrationFinished(false);
        }
        return;
    }

    stopOfflineCalibration();
    emit calibrationFinished(true);
}

void CalibrationWorker::exitFactoryMode()
{
    UDSFrame responseFrame;
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "10 03", QString::fromLocal8Bit("进入扩展模式【退出工厂模式】"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "27 01", QString::fromLocal8Bit("请求种子")); // 请求种子
    uint32_t seed{0};
    memcpy(&seed, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);
    uint32_t key = 0;
    switch (mProtocolType) {
    case ProtocolBYD220:
        key = SeedToKey(seed, mMASK[mProtocolType][mRadarID]);
        break;
    case PtotocolHSAE:
        key = SeedToKeyHASE(seed, mMASK[mProtocolType][mRadarID]);
        break;
    default:
        break;
    }
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], QString("27 02 %1").arg(key, 8, 16, QLatin1Char('0')), QString::fromLocal8Bit("密钥验证")); // 发送密钥
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "2E 34 01 00", QString::fromLocal8Bit("关闭工厂模式"));
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "14 FF FF FF", QString::fromLocal8Bit("清除DTC"));
    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "11 01", QString::fromLocal8Bit("硬件重置"));

    emit message(QString::fromLocal8Bit("延时1500ms"));
    Utils::dely(1500);

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolType][mRadarID], "22 34 01", QString::fromLocal8Bit("读取配置字"));
    emit message(responseFrame.mData.toHex(' '));
}

QString CalibrationWorker::verifyDTC(const QByteArray &data)
{
    QString dtc;
    int dtcIndex = data.indexOf(QByteArray::fromHex("ACD488"));
    if (dtcIndex != -1)
    {
        dtc.append(data.mid(dtcIndex, 4).toHex(' ') + " ");
    }
    dtcIndex = data.indexOf(QByteArray::fromHex("ACED00"));
//    dtcIndex = data.indexOf(QByteArray::fromHex("ACD278"));
    qDebug() << __FUNCTION__ << __LINE__ << data << QByteArray::fromHex("ACCE17") << dtcIndex;
    if (dtcIndex != -1)
    {
        dtc.append(data.mid(dtcIndex, 4).toHex(' '));
    }

    return dtc;
}
