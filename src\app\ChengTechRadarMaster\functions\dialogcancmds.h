﻿#ifndef DIALOGCANCMDS_H
#define DIALOGCANCMDS_H

#include "functions_global.h"

#include <QDialog>
#include <QVector>
#include <QString>
#include <QByteArray>
#include <QMap>
#include <QLineEdit>
#include <QFile>
#include <QIODevice>

namespace Ui {
class DialogCanCmds;
}

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Functions {

class FUNCTIONS_EXPORT DialogCanCmds : public QDialog
{
    Q_OBJECT

    enum SAVE_TO_CSV_TYPE{
        AZI_DECAY = 0, //收发方向图
        ANT_POS, // 天线间距
        ANT_COMPS, //天线相位差
        DBF_COEF, //DBF因子
        DBF_COEF_LEN, //DBF因子长度
        UNKNOWN
    };

public:
    explicit DialogCanCmds(Devices::Can::DeviceManager *deviceManager, QWidget *parent = 0);
    ~DialogCanCmds();

signals:
    void sigSendCmds(int rd_id ,const QString &cmds);

 public slots:
    void slot_Receive_CanData(int CanCH, int id , QByteArray datAry );
    void slotCmdAck(int id, const QByteArray msg);
    bool sendData(int channelIndex, int id, QByteArray data);
    void slotSendTextCmd(int rd_id, const QString &cmds);
    void slotCanAck( int id, const QByteArray data );

private slots:
    void slotSendClick(void);
    void slotSetConfig(void);
    void slotSelectProfile(int index);
    void slotReadConfig(void);
    void slotClearFlash(void);
    void slotWriteFlash(void);
    void slotClearCfgText(void);

    void on_pushButton_entryCfg_clicked();

    void on_pushButton_outCfg_clicked();

    void on_pushButton_temp_clicked();

    void on_pushButtonClearBuf_clicked();
    void slotSendCfgCmd();


    void on_pushButton_SR_clicked();

    void on_pushButton_NoSR_clicked();

    void on_pushButton_reset_clicked();

    void on_comboBoxType_currentIndexChanged(int index);

    void on_btnSelAziDecay_clicked();

    void on_ptnSelAntPos_clicked();

    void on_ptnSelAntComps_clicked();

    void on_ptnSelDBFCoef_clicked();

    void on_btnWriteCsv_clicked();

    void on_btnReadCsv_clicked();

private:
    void showAck(const QString &ack);

    void createCfgBotton(int profile, const QString& cfgFileName);

private:
    bool writeFromCsv( const QString& fileName, const QString& cmd_pre );
    bool enterFactoryMode();//进入工厂模式
    bool writeCalVer( quint8 calVerH, quint8 calVerL ); //写入校准版本
    bool writeDbfCsv( const QString& fileName );

    void wait( quint64 ms );
    quint32 getDbfFactorFlashAddr( quint8 profileIndex );
    void generateCrcTable();
    quint32 calCRC( QByteArray data );

    void sendReadCsvCmd( quint8 profileIdx, SAVE_TO_CSV_TYPE csvType );
    void sendReadCsvCmd_DBF( quint8 profileIdx, quint32 dbfLen );
    void saveToCsv( const QString& path, SAVE_TO_CSV_TYPE csvType );
    void saveToCsv_DBF( const QString& path );
    bool saveToCsv( const QString& str );
    void openSaveCsv( const QString& path, SAVE_TO_CSV_TYPE saveType );
    void closeSaveCsv();

    quint32 getDbfLen( quint8 profileIdx );
    quint8 getMaxProfileIdx();

private:
    Ui::DialogCanCmds *ui;
    bool m_recvAckStart = false;
    QByteArray m_ackBuf;
    QMap<QString, QLineEdit*> m_cfg1;
    QByteArray cfgFileAry;
    QStringList cmdlist;
    int curPorfile;

    Devices::Can::DeviceManager *mDeviceManager{0};
    QWidgetList mWidgetList[4];

private:
    quint32 mReqID{0}; //请求帧ID
    quint32 mAckID{0}; //应答帧ID
    QByteArray mAckData; //应答帧数据
    quint32 mCrcTable[256];

private:
    bool mSaveToCsv{false};
    SAVE_TO_CSV_TYPE mSaveToCsvType{UNKNOWN};
    QFile mSaveFile;
    bool mAddCsvSpace{false};
    qint32 mSaveWordLen{-1};

    quint32 mDbfLen{0};  //dbf flash 长度
};

} // namespace Functions

#endif // DIALOGCANCMDS_H
