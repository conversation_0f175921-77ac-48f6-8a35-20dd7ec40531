/**
 * @file     app_common.h
 * @brief    The common structure, macro definition or interface are in defined in this header file.
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2022-12-26
 *
 *
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2022-12-26  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _APP_COMMON_H_
#define _APP_COMMON_H_


/****************************************************************************
  INCLUDE
 ****************************************************************************/
#include <stdint.h>
#include <stddef.h>


/*****************************************************************************
  DEFINE
 *****************************************************************************/
//#define MAX_NB_OF_POINTS   256

#ifndef FALSE
#define FALSE   0U
#endif // !FALSE

#ifndef TRUE
#define TRUE    1U
#endif // !TRUE


typedef struct signalHeader
{
    uint32_t uiTimeStamp;
    uint32_t uiMeasurementCounter;
} SignalHeader_t;

/**
 * @brief Signal header.
 *        It will be used in RSP_DetObjectList_t, RDP_TrkObjectList_t, COM_vehicleSignals_t and other.
 */
typedef struct APP_signalHeader
{
    uint32_t uiTimeStamp;
    uint32_t uiMeasurementCounter;
} APP_signalHeader_t;


/*****************************************************************************
  DECLARATION
 *****************************************************************************/


#endif
