﻿#ifndef ANALYSISMANAGER_H
#define ANALYSISMANAGER_H

#include <QObject>
#include <QThread>
#include <QMap>
#include <QPair>

#include "analysis_global.h"
#include "analysisdata.h"

class ObejectView;

namespace Analysis {

class AnalysisWorker;
class CalculationWorker;
class AnalysisSaveWorker;
class FrameBinarySaveWorker;

class ANALYSIS_EXPORT AnalysisManager : public QObject
{
    Q_OBJECT
public:
    explicit AnalysisManager(QObject *parent = nullptr);

    AnalysisWorker *analysisWorker() const { return mAnalysisWorker; }
    CalculationWorker *calculationWorker() const { return mCalculationWorker; }

    bool startSave(const QString &savePath, const QDateTime &beginTime, bool oldStyle, bool needResponse = false);
    bool stopSave();

    void setSaveCountMax(quint64 max);
    void setAngleCompensation(AngleCompensation angleCompensation[], int count);

signals:
    void saveStarted(const QString &rawFilename, bool ok = true);

private:
    AnalysisWorker *mAnalysisWorker{0};
    CalculationWorker *mCalculationWorker{0};
    AnalysisSaveWorker *mAnalysisSaveWorker{0};
    FrameBinarySaveWorker* mFrameBinarySaveWorker{0};
};

} // namespace Analysis

#endif // ANALYSISMANAGER_H
