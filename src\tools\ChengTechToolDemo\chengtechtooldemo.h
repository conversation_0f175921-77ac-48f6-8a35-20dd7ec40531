﻿#ifndef CHENGTECHTOOLDEMO_H
#define CHENGTECHTOOLDEMO_H

#include <QMainWindow>

#include <devices/ideviceworker.h>

namespace Devices {
namespace Can {
class DeviceManager;
}
}

class CTAnalysisWorker;

QT_BEGIN_NAMESPACE
namespace Ui { class ChengTechToolDemo; }
QT_END_NAMESPACE

class ChengTechToolDemo : public QMainWindow
{
    Q_OBJECT

public:
    ChengTechToolDemo(QWidget *parent = nullptr);
    ~ChengTechToolDemo();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void on_actionOpenDevice_triggered();

    void on_actionSelectDevice_triggered();

private:
    /** @brief 设备发生改变 */
    void deviceChanged(Devices::Can::DeviceSettings deviceSettings);

    void loadSettings();
    void savesettings();

private:
    Ui::ChengTechToolDemo *ui;

    Devices::Can::DeviceManager *mDeviceManager{0};
    CTAnalysisWorker *mCTAnalysisWorker{0};
};
#endif // CHENGTECHTOOLDEMO_H
