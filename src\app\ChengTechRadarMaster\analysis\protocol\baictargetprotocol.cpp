﻿#include "baictargetprotocol.h"

#include "utils/utils.h"

#include <QDebug>

namespace Analysis {
namespace Protocol {

BAICTargetProtocol::BAICTargetProtocol(AnalysisWorker *analysisWorker, QObject *parent)
    : IAnalysisProtocol(analysisWorker, parent)
{

}

bool BAICTargetProtocol::analysisFrame(const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.channelIndex() << frame.length() << frame.idHex() << frame.dataHex();
    if( analysisFrame2( frame ) ){ //尝试解析北汽的车速/报警信息/功能状态
        return true;
    }

    bool bRet = false;
    switch (frame.id() & 0xFF0)
    {
    case 0x200: // 6号雷达 左前
        bRet = baic16TargetParse(6, frame);
        break;
    case 0x240: // 7号雷达 右前
        bRet = baic16TargetParse(7, frame);
        break;
    case 0x280: // 4号雷达 左后
        bRet = baic16TargetParse(4, frame);
        break;
    case 0x2C0: // 5号雷达 右后
        bRet = baic16TargetParse(5, frame);
        break;
    case 0x300: // 后雷达6个目标
        bRet = baic6TargetParse(5, frame);
        break;
    case 0x400: // 前雷达6个目标
        bRet = baic6TargetParse(5, frame);
        break;
    default:
        return false;
    }

    return bRet;
}

qint8 BAICTargetProtocol::radarID(const Devices::Can::CanFrame &frame)
{
    qint8 radarID = -1;
    switch (frame.id() & 0xFF0)
    {
    case 0x200: // 6号雷达 左前
        radarID = 6;
        break;
    case 0x240: // 7号雷达 右前
        radarID = 7;
        break;
    case 0x280: // 4号雷达 左后
        radarID = 4;
        break;
    case 0x2C0: // 5号雷达 右后
        radarID = 5;
        break;
    case 0x300: // 后雷达6个目标
        radarID = 5;
        break;
    case 0x400: // 前雷达6个目标
        radarID = 5;
        break;
    default:
        radarID = -1;
        break;
    }

    return radarID;
}

bool BAICTargetProtocol::baic6TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8)
    {
//        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }
    if (frame.id() == 0x300 || frame.id() == 0x400) {
        mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.clear();
    }

    bool bEndFrame = false;
    // 0x300 0x400 length = 6
    const uint8_t *data = (const uint8_t *)frame.data().data();

    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargetCount; // 注意必须使用引用
    quint8 id = data[0];
    if (id != 0xFF) {
        if (id < MAX_TARGET_COUNT && targetCount < MAX_TARGET_COUNT) {

            Target &target = mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargets[targetCount++];
            target.mID = id;
            //    target.Lock_Target_Abscissa_01 = ((data[1]) - 100);
            //    target.Lock_Target_Ordinate_01 = ((data[2]) - 100);
            //    target.Lock_Target_LonRelative_Spd_01 = (data[3]);
            //    target.Lock_Target_LatRelative_Spd_01 = (data[4]);
            //    target.Lock_Target_MovingState_01 = ((data[5] & 0xC0U) >> 6);
            //    target.Lock_Target_State_01 = ((data[5] & 0x20U) >> 5);
            //    target.Lock_Target_Type_01 = ((data[5] & 0x1EU) >> 1);
        } else {
            qDebug() << __FUNCTION__ << __LINE__ << "BaiC lock Target error!" << id << targetCount << " >= " << MAX_TARGET_COUNT;
        }
    }

    if (frame.id() == 0x306 || frame.id() == 0x406) {
        mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargetHeader.mMeasurementCount = 0;
        mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargetHeader.mProtocolVersion = ProtocolBAIC;
        mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mValid = true;
        mAnalysisWorker->analysisEnd(radarID, Frame3Track);
        bEndFrame = true;
    }

    mAnalysisWorker->analysisLanePointFrameEnd( radarID, frame, bEndFrame );

    return true;
}

bool BAICTargetProtocol::baic16TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 24){
//        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![24]!" << frame.idHex() << frame.channelIndex() << frame.dataHex();
        return false;
    }/*else{
        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.channelIndex();
    }*/

//    qDebug() << __FUNCTION__ << __LINE__ << radarID << frame.channelIndex() << frame.idHex() << frame.dataHex();
    if (frame.id() == 0x200 || frame.id() == 0x240 || frame.id() ==  0x280 || frame.id() ==  0x2C0) {
        mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.clear();
    }

    // 0x200 0x240 0x280 0x2C0 length = 24
    const uint8_t *data = (const uint8_t *)frame.data().data();

    bool bEndFrame = false;
    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount; // 注意必须使用引用
    quint8 id = data[1];
    if (id != 0xFF) {
    if (id < MAX_TARGET_COUNT && targetCount < MAX_TARGET_COUNT) {


    Target &target = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets[targetCount++];
    target.mValid = true;

    target.mChecksum = (data[0]);
    target.mID = id;
    target.mY = (((((data[3] & 0xFCU) >> 2) + (((uint16_t)data[2] & 0xFFU) << 6)) * 0.05) - 409.6);
    target.mX = (((((data[5] & 0xC0U) >> 6) + (((uint32_t)data[4]) << 2) + (((uint32_t)data[3] & 0x3U) << 10)) * 0.05) - 102.4);
    target.mVy = (((((data[6] & 0xFEU) >> 1) + (((uint16_t)data[5] & 0x3FU) << 7)) * 0.04) - 163.84);
//    target.RL_ObjectCntr_01 = (data[7]);
    target.mVx = (((((data[9] & 0xF8U) >> 3) + (((uint16_t)data[8] & 0xFFU) << 5)) * 0.04) - 163.84);
//    target.RL_ObjectArelLong_01 = ((((data[10] & 0xFFU) + (((uint16_t)data[9] & 0x7U) << 8)) * 0.01) - 10.24);
//    target.RL_ObjectArelLat_01 = (((((data[12] & 0xE0U) >> 5) + (((uint16_t)data[11] & 0xFFU) << 3)) * 0.01) - 10.24);
    target.mTrackFrameLength = ((((data[13] & 0xE0U) >> 5) + (((uint16_t)data[12] & 0x1FU) << 3)) * 0.2);
    target.mTrackFrameWidth = ((((data[14] & 0xE0U) >> 5) + (((uint16_t)data[13] & 0x1FU) << 3)) * 0.2);
//    target.RL_ObjectHeadYawAgl_01 = ((((data[15] & 0xFFU) + (((uint16_t)data[14] & 0xFU) << 8)) * 0.1) - 204.8);
    target.mDynamicProperty = ((data[16] & 0xF0U) >> 4);
    target.mExistProbability = (((data[17] & 0xE0U) >> 5) + (((uint16_t)data[16] & 0xFU) << 3));
    target.mRCS = (((((data[18] & 0xF0U) >> 4) + (((uint16_t)data[17] & 0x1FU) << 4)) * 0.5) - 128);
//    target.RL_ObjectClass_01 = (data[18] & 0xFU);
//    target.RL_ObjectDistAltitude_01 = (((data[19]) * 0.05) - 1.75);
//    target.RL_ObjectLink_01 = (data[20]);
    target.mRefencePointPostion = ((data[21] & 0xC0U) >> 6);
    target.mRollingCount = (data[23] & 0xFU);

    //qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << target.mID << target.mX << target.mY << target.mRollingCount
//             << "|||||" << frame.idHex() << frame.dataHex();

//    if( radarID == 5 && target.mID == 15 ){
//        qDebug() <<__FUNCTION__ << __LINE__ << target.mID << target.mX << target.mY << frame.idHex() << frame.dataHex();
//    }

//    target.mBaiCTarget = true;
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << frame.idHex() << frame.dataHex();
//    qDebug() << __FUNCTION__ << __LINE__ << data[1] << target.mY << target.mX;


//    qDebug() << __FUNCTION__ << __LINE__ << frame.channelIndex() << frame.length() << frame.idHex() << frame.dataHex();
//    qDebug() << __FUNCTION__ << __LINE__ << id << target.mX << target.mY;
        } else {
        qDebug() << __FUNCTION__ << __LINE__ << "BaiC 16 Target error!" << id << targetCount << " >= " << MAX_TARGET_COUNT;
    }
    }

    if (frame.id() == 0x20F || frame.id() == 0x24F || frame.id() ==  0x28F || frame.id() ==  0x2CF) {
        mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetHeader.mMeasurementCount = 0;
        mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetHeader.mProtocolVersion = ProtocolBAIC;
        mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mValid = true;
        mAnalysisWorker->analysisEnd(radarID, Frame16Track);
        bEndFrame = true;
    }

    mAnalysisWorker->analysisTargetPoint16FrameEnd( radarID, frame, bEndFrame );
    return true;
}

bool BAICTargetProtocol::analysisFrame2(const Devices::Can::CanFrame &frame)
{
//    if( frame.id() == 0x311 ){
//        qDebug() << __FUNCTION__ << __LINE__ << frame.channelIndex() << frame.dataHex();
//    }

    if( frame.channelIndex() != mBaicChannelIndex ){
        return false;
    }

    switch( frame.id() ){
    case 0x311:
        return baicVehicleSpeedParse( frame );
    case 0x384:
        return baicAlarmParse384( frame );
    case 0x385:
        return baicAlarmParse385( frame );
    case 0x386:
        return baicAlarmParse386( frame );
    case 0x527:
        return baicVehicleParse527( frame );
    default:
        return false;
    }

    return false;
}

bool BAICTargetProtocol::baicVehicleSpeedParse(/*quint8 radarID,*/ const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8) {
        return false;
    }

//    qDebug()<< __FUNCTION__ << __LINE__ << frame.idHex() << frame.channelIndex();

    const uint8_t *data = (const uint8_t *)frame.data().data();

    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[4];
    analysisData->mVehicleData.mVehicleSpeed = ((((data[5] & 0xF8U) >> 3) + (((uint16_t)data[4] & 0xFFU) << 5)) * 0.05625);

    analysisData = &mAnalysisWorker->mAnalysisDatas[5];
    analysisData->mVehicleData.mVehicleSpeed = ((((data[5] & 0xF8U) >> 3) + (((uint16_t)data[4] & 0xFFU) << 5)) * 0.05625);

    analysisData = &mAnalysisWorker->mAnalysisDatas[6];
    analysisData->mVehicleData.mVehicleSpeed = ((((data[5] & 0xF8U) >> 3) + (((uint16_t)data[4] & 0xFFU) << 5)) * 0.05625);

    analysisData = &mAnalysisWorker->mAnalysisDatas[7];
    analysisData->mVehicleData.mVehicleSpeed = ((((data[5] & 0xF8U) >> 3) + (((uint16_t)data[4] & 0xFFU) << 5)) * 0.05625);

    analysisEnd( 4, false);
    analysisEnd( 5, false);
    analysisEnd( 6, false);
    analysisEnd( 7, false);



//    userData->WCBS_ESP5_St_VehicleStandstill = ((data[0] & 0xC0U) >> 6);
//    userData->WCBS_ESP5_RollingCounter = ((data[1] & 0x1EU) >> 1);
//    userData->WCBS_ESP5_F_VehicleSpeed = (data[1] & 0x1U);
//    userData->WCBS_ESP5_St_PrefillActive = ((data[2] & 0x2U) >> 1);
//    userData->WCBS_ESP5_St_PrefillAvailable = (data[2] & 0x1U);
//    userData->WCBS_ESP5_St_AEBdecActive = ((data[3] & 0x80U) >> 7);
//    userData->WCBS_ESP5_St_AEBdecAv = ((data[3] & 0x40U) >> 6);
//    userData->WCBS_ESP5_AWBactive = ((data[3] & 0x8U) >> 3);
//    userData->WCBS_ESP5_AWBavailable = ((data[3] & 0x4U) >> 2);
//    userData->WCBS_ESP5_N_VehicleSpeed = ((((data[5] & 0xF8U) >> 3) + (((uint16_t)data[4] & 0xFFU) << 5)) * 0.05625);
//    userData->WCBS_ESP5_St_RCTBdecActive = ((data[6] & 0x8U) >> 3);
//    userData->WCBS_ESP5_Checksum = (data[7]);

    return true;

}

bool BAICTargetProtocol::baicAlarmParse385(const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8) {
        return false;
    }

//    qDebug()<< __FUNCTION__ << __LINE__ << frame.idHex() << frame.channelIndex();

    const uint8_t *data = (const uint8_t *)frame.data().data();

    //左后雷达
    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[4];
    analysisData->mAlarmData.mAlarmBSDLevel = (data[3] & 0x3U);
    analysisData->mAlarmData.mAlarmLCALevel = ((data[3] & 0xCU) >> 2);
    analysisData->mAlarmData.mAlarmDOWRLevel = ((data[3] & 0x30U) >> 4);
    analysisData->mAlarmData.mAlarmDOWRLevel = ((data[3] & 0x30U) >> 4);
    analysisData->mAlarmData.mAlarmRCTALevel = ((data[3] & 0xC0U) >> 6);

    //右后雷达
    analysisData = &mAnalysisWorker->mAnalysisDatas[5];
    analysisData->mAlarmData.mAlarmRCTBLevel = (data[4] & 0x3U);
    //右前雷达
    analysisData = &mAnalysisWorker->mAnalysisDatas[7];
    analysisData->mAlarmData.mAlarmRCTBLevel = (data[4] & 0x3U);


//    userData->CMRR_RR1_Checksum = (data[0]);
//    userData->CMRR_RR1_RollingCounter = (data[1] & 0xFU);
//    userData->CMRR_RR1_W_LHRCTA = ((data[3] & 0xC0U) >> 6);
//    userData->CMRR_RR1_W_LHDOW = ((data[3] & 0x30U) >> 4);
//    userData->CMRR_RR1_W_LHLCA = ((data[3] & 0xCU) >> 2);
//    userData->CMRR_RR1_W_LHBSD = (data[3] & 0x3U);
//    userData->CMRR_RR1_St_Failure = ((data[4] & 0x70U) >> 4);
//    userData->CMRR_RR1_W_RHRCTB = (data[4] & 0x3U);
//    userData->CMRR_RR1_W_RHRCTBDecel = (((data[7]) * 0.05) - 10);

    return true;
}

bool BAICTargetProtocol::baicAlarmParse386(const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8) {
        return false;
    }

//    qDebug()<< __FUNCTION__ << __LINE__ << frame.idHex() << frame.channelIndex();

    const uint8_t *data = (const uint8_t *)frame.data().data();

   quint8 RCTAState = baicAlarmStateConver( ((data[1] & 0x70U) >> 4) );
   quint8 RCTBState = baicAlarmStateConver( (data[2] & 0x7U) );
   quint8 RCWState = baicAlarmStateConver( ((data[2] & 0x70U) >> 4) );
   quint8 DOWState = baicAlarmStateConver( (data[5] & 0x7U) );
   quint8 BSDState = baicAlarmStateConver( (data[6] & 0x7U) );


    //右后雷达
    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[5];
    analysisData->mAlarmData.mAlarmBSDLevel = (data[3] & 0x3U);
    analysisData->mAlarmData.mAlarmLCALevel = ((data[3] & 0xCU) >> 2);
    analysisData->mAlarmData.mAlarmDOWRLevel = ((data[3] & 0x30U) >> 4);
    analysisData->mAlarmData.mAlarmDOWRLevel = ((data[3] & 0x30U) >> 4);
    analysisData->mAlarmData.mAlarmRCTALevel = ((data[3] & 0xC0U) >> 6);
    analysisData->mAlarmData.mAlarmRCWLevel = ((data[4] & 0xCU) >> 2);

    analysisData->mAlarmData.mAlarmRCTAState = RCTAState;
    analysisData->mAlarmData.mAlarmRCTBState = RCTBState;
    analysisData->mAlarmData.mAlarmRCWState = RCWState;
    analysisData->mAlarmData.mAlarmDOWState = DOWState;
    analysisData->mAlarmData.mAlarmBSDState = BSDState;

    //左后雷达
    analysisData = &mAnalysisWorker->mAnalysisDatas[4];
    analysisData->mAlarmData.mAlarmRCWLevel = ((data[4] & 0xCU) >> 2);
    analysisData->mAlarmData.mAlarmRCTAState = RCTAState;
    analysisData->mAlarmData.mAlarmRCTBState = RCTBState;
    analysisData->mAlarmData.mAlarmRCWState = RCWState;
    analysisData->mAlarmData.mAlarmDOWState = DOWState;
    analysisData->mAlarmData.mAlarmBSDState = BSDState;

    //左前雷达
    analysisData = &mAnalysisWorker->mAnalysisDatas[6];
    analysisData->mAlarmData.mAlarmRCWLevel = ((data[4] & 0xCU) >> 2);
    analysisData->mAlarmData.mAlarmRCTAState = RCTAState;
    analysisData->mAlarmData.mAlarmRCTBState = RCTBState;
    analysisData->mAlarmData.mAlarmRCWState = RCWState;
    analysisData->mAlarmData.mAlarmDOWState = DOWState;
    analysisData->mAlarmData.mAlarmBSDState = BSDState;

    //右前雷达
    analysisData = &mAnalysisWorker->mAnalysisDatas[7];
    analysisData->mAlarmData.mAlarmRCWLevel = ((data[4] & 0xCU) >> 2);
    analysisData->mAlarmData.mAlarmRCTAState = RCTAState;
    analysisData->mAlarmData.mAlarmRCTBState = RCTBState;
    analysisData->mAlarmData.mAlarmRCWState = RCWState;
    analysisData->mAlarmData.mAlarmDOWState = DOWState;
    analysisData->mAlarmData.mAlarmBSDState = BSDState;



//        userData->CMRR_RR2_Checksum = (data[0]);
//        userData->CMRR_RR2_St_RCTA = ((data[1] & 0x70U) >> 4);
//        userData->CMRR_RR2_RollingCounter = (data[1] & 0xFU);
//        userData->CMRR_RR2_SwFb_RCW = ((data[2] & 0x80U) >> 7);
//        userData->CMRR_RR2_St_RCW = ((data[2] & 0x70U) >> 4);
//        userData->CMRR_RR2_St_RCTB = (data[2] & 0x7U);
//        userData->CMRR_RR2_W_RHRCTA = ((data[3] & 0xC0U) >> 6);
//        userData->CMRR_RR2_W_RHDOW = ((data[3] & 0x30U) >> 4);
//        userData->CMRR_RR2_W_RHLCA = ((data[3] & 0xCU) >> 2);
//        userData->CMRR_RR2_W_RHBSD = (data[3] & 0x3U);
//        userData->CMRR_RR2_W_RCW = ((data[4] & 0xCU) >> 2);
//        userData->CMRR_RR2_St_DOW = (data[5] & 0x7U);
//        userData->CMRR_RR2_St_Calibration = ((data[6] & 0xE0U) >> 5);
//        userData->CMRR_RR2_St_Blindness = ((data[6] & 0x18U) >> 3);
//        userData->CMRR_RR2_St_BSD_LCA = (data[6] & 0x7U);
//        userData->CMRR_RR2_SwFb_DOW = ((data[7] & 0xC0U) >> 6);
//        userData->CMRR_RR2_SwFb_BSD_LCA = ((data[7] & 0x30U) >> 4);
//        userData->CMRR_RR2_SwFb_RCTB = ((data[7] & 0xCU) >> 2);
//        userData->CMRR_RR2_SwFb_RCTA = (data[7] & 0x3U);

    return true;
}

bool BAICTargetProtocol::baicAlarmParse384(const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8) {
        return false;
    }

//    qDebug()<< __FUNCTION__ << __LINE__ << frame.idHex() << frame.channelIndex();

    const uint8_t *data = (const uint8_t *)frame.data().data();

    qint8 AlarmFCTAState = baicAlarmStateConver( (data[2] & 0x7U) );
    qint8 AlarmFCTBState = baicAlarmStateConver( ((data[2] & 0xE0U) >> 5) );
    qint8 AlarmJAState = baicAlarmStateConver( ((data[3] & 0x70U) >> 4) );

    //右前雷达
    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[7];
    analysisData->mAlarmData.mAlarmFCTALevel = ((data[1] & 0x30U) >> 4);
    analysisData->mAlarmData.mAlarmFCTBLevel = ((data[1] & 0xC0U) >> 6);
    analysisData->mAlarmData.mAlarmJALevel = (data[3] & 0x3U);
    analysisData->mAlarmData.mAlarmFCTAState = AlarmFCTAState;
    analysisData->mAlarmData.mAlarmFCTBState = AlarmFCTBState;
    analysisData->mAlarmData.mAlarmJAState = AlarmJAState;

    //左前雷达
    analysisData = &mAnalysisWorker->mAnalysisDatas[6];
    analysisData->mAlarmData.mAlarmFCTALevel = ((data[3] & 0xCU) >> 2);
    analysisData->mAlarmData.mAlarmJALevel = (data[6] & 0x3U);
    analysisData->mAlarmData.mAlarmFCTAState = AlarmFCTAState;
    analysisData->mAlarmData.mAlarmFCTBState = AlarmFCTBState;
    analysisData->mAlarmData.mAlarmJAState = AlarmJAState;


//    userData->CMRR_FR_Checksum = (data[0]);
//    userData->CMRR_FR_W_RHFCTB_JA = ((data[1] & 0xC0U) >> 6);
//    userData->CMRR_FR_W_RHFCTA = ((data[1] & 0x30U) >> 4);
//    userData->CMRR_FR_RollingCounter = (data[1] & 0xFU);
//    userData->CMRR_FR_St_FCTB = ((data[2] & 0xE0U) >> 5);
//    userData->CMRR_FR_SwFb_FCTA = ((data[2] & 0x18U) >> 3);
//    userData->CMRR_FR_St_FCTA = (data[2] & 0x7U);
//    userData->CMRR_FR_St_JA = ((data[3] & 0x70U) >> 4);
//    userData->CMRR_FR_W_LHFCTA = ((data[3] & 0xCU) >> 2);
//    userData->CMRR_FR_W_RHJA = (data[3] & 0x3U);
//    userData->CMRR_FR_St_Failure = ((data[4] & 0x70U) >> 4);
//    userData->CMRR_FR_SwFb_FCTB = ((data[4] & 0xCU) >> 2);
//    userData->CMRR_FR_St_Blindness = (data[4] & 0x3U);
//    userData->CMRR_FR_St_Calibration = ((data[6] & 0xE0U) >> 5);
//    userData->CMRR_FR_SwFb_JA = ((data[6] & 0xCU) >> 2);
//    userData->CMRR_FR_W_LHJA = (data[6] & 0x3U);
//    userData->CMRR_FR_W_RHFCTB_JA_Decel = (((data[7]) * 0.05) - 10);

    return true;
}

bool BAICTargetProtocol::baicVehicleParse527(const Devices::Can::CanFrame &frame)
{
//    double mVehicleYear{0.0};
//    double mVehicleMonth{0.0};
//    double mVehicleDay{0.0};
//    double mVehicleHour{0.0};
//    double mVehicleMinute{0.0};
//    double mVehicleSecond{0.0};

    if (frame.length() != 8) {
        return false;
    }
    const uint8_t *data = (const uint8_t *)frame.data().data();

    double year = (((data[0] & 0xFEU) >> 1) + 2000);
    double month = (((data[1] & 0xE0U) >> 5) + (((uint16_t)data[0] & 0x1U) << 3));
    double day = (data[1] & 0x1FU);
    double hour = ((data[2] & 0xF8U) >> 3);
    double minute = ((data[3] & 0xFCU) >> 2);
    double second = (((data[4] & 0xF0U) >> 4) + (((uint16_t)data[3] & 0x3U) << 4));

    //qDebug() << __FUNCTION__ << __LINE__ << year << month << day << hour << minute << second;

    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[4];
    analysisData->mVehicleData.mVehicleYear = year;
    analysisData->mVehicleData.mVehicleMonth = month;
    analysisData->mVehicleData.mVehicleDay = day;
    analysisData->mVehicleData.mVehicleHour = hour;
    analysisData->mVehicleData.mVehicleMinute = minute;
    analysisData->mVehicleData.mVehicleSecond = second;

    analysisData = &mAnalysisWorker->mAnalysisDatas[5];
    analysisData->mVehicleData.mVehicleYear = year;
    analysisData->mVehicleData.mVehicleMonth = month;
    analysisData->mVehicleData.mVehicleDay = day;
    analysisData->mVehicleData.mVehicleHour = hour;
    analysisData->mVehicleData.mVehicleMinute = minute;
    analysisData->mVehicleData.mVehicleSecond = second;

    analysisData = &mAnalysisWorker->mAnalysisDatas[6];
    analysisData->mVehicleData.mVehicleYear = year;
    analysisData->mVehicleData.mVehicleMonth = month;
    analysisData->mVehicleData.mVehicleDay = day;
    analysisData->mVehicleData.mVehicleHour = hour;
    analysisData->mVehicleData.mVehicleMinute = minute;
    analysisData->mVehicleData.mVehicleSecond = second;

    analysisData = &mAnalysisWorker->mAnalysisDatas[7];
    analysisData->mVehicleData.mVehicleYear = year;
    analysisData->mVehicleData.mVehicleMonth = month;
    analysisData->mVehicleData.mVehicleDay = day;
    analysisData->mVehicleData.mVehicleHour = hour;
    analysisData->mVehicleData.mVehicleMinute = minute;
    analysisData->mVehicleData.mVehicleSecond = second;

    analysisEnd( 4, false);
    analysisEnd( 5, false);
    analysisEnd( 6, false);
    analysisEnd( 7, false);



}

quint8 BAICTargetProtocol::baicAlarmStateConver(quint8 baicState)
{
    // 2-active是绿色，3-fault是红色，1-OFF是黑色，0-Passive是灰色
    switch( baicState ){
    case 0: //init
        return 3;
    case 1: //off
        return 1;
    case 2: //standby
        return 0;
    case 3: //active
        return 2;
    case 4:
        return 3;
    default:
        return 3;
    }

    return 3;
}

} // namespace Protocol
} // namespace Analysis
