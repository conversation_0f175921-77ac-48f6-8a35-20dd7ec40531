﻿#include "ctanalysisworker.h"

#include <QDebug>

CTAnalysisWorker::CTAnalysisWorker(QObject *parent) : QObject(parent)
{
    memset(mTimer, 0, sizeof(mTimer));
    memset(mFirst, 1, sizeof(mFirst));
    memset(mRadarFrameReceived, 0, sizeof (mRadarFrameReceived));
}

void CTAnalysisWorker::reset()
{
    memset(mFirst, 1, sizeof(mFirst));
}

void CTAnalysisWorker::canFrame(const Devices::Can::CanFrame &frame)
{
    int channel = frame.channelIndex();
    if (channel > 1) {
        return;
    }

    int radarID = 4;
    switch (frame.id()) {
    case 0x527: // 4
        radarID = 4;
        break;
    case 0x52A: // 5
        radarID = 5;
        break;
    case 0x555: // 6
        radarID = 6;
        break;
    case 0x556: // 7
        radarID = 7;
        break;
        break;
    default:
        return;
    }
    frame.print(__FUNCTION__, __LINE__);
    int radarIndex = radarID - 4;
    if (mFirst[channel][radarIndex]) {
        mFirst[channel][radarIndex] = false;
        emit radarConnected(channel, radarID);

        if (!mTimer[channel][radarIndex]) {
            mTimer[channel][radarIndex] = new QTimer;
            connect(mTimer[channel][radarIndex], &QTimer::timeout, this, [=](){
                this->timeOut(channel, radarID);
            });
        }
    }

    mRadarFrameReceived[channel][radarIndex]++;
    if (mTimer[channel][radarIndex]) {
        mTimer[channel][radarIndex]->start(mOutTime);
    }

}

void CTAnalysisWorker::timeOut(int c, int r)
{
    int radarIndex = r - 4;
    if (!mRadarFrameReceived[c][radarIndex]) {
        emit frameError(c, r);
        mTimer[c][radarIndex]->stop();
    }
    mRadarFrameReceived[c][radarIndex] = 0;
}
