﻿#ifndef ANALYSISDATAVIEWI_H
#define ANALYSISDATAVIEWI_H

#include <QWidget>
#include <QDialog>

#include "analysis/analysisdata.h"

namespace Ui {
class AnalysisDataViewConfigDialog;
}

class QLabel;
class QAction;
class QVBoxLayout;
class QSplitter;

namespace Views {
namespace AnalysisView {

class AnalysisModel;
class AnalysisDataTableView;

class AnalysisDataViewI : public QWidget
{
    Q_OBJECT
public:
    explicit AnalysisDataViewI(quint8 radarID, QWidget *parent = nullptr);

    QVariant getAnalysisDataViewSettings() const;
    void setAnalysisDataViewSettings(QVariant settings);

    QAction *actionOrientation() const { return mActionOrientation; }
    QAction *actionNewAnalysisDataTable() const { return mActionNewAnalysisDataTable; }
    QAction *actionMonitoringTrackTarget() const { return mActionMonitoringTrackTarget; }
    QAction *actionViewDataConfig() const { return mActionViewDataConfig; }
    QLabel *labelMeasurementCounter() const { return mLabelMeasurementCounter; }

    RadarType radarType() const { return mRadarType; }
    void setOrientation(Qt::Orientation);

    virtual void setViewAnalysisTypes(int fType, bool moving, bool continuous, const ViewTypes &types) = 0;

signals:

protected slots:
    virtual void viewTypeChanged() = 0;

    QVariantList getViewsTypes() const;
    void setViewsTypes(QVariantList types);

private slots:
    void newTableView();
    void deleteTableView();
    void orientationChanged();
    void viewDataConfig();

protected:
    void addTopWidget(QWidget *widget);
    void newTableView(int viewType);

    QVector<AnalysisDataTableView *> mAnalysisDataTableViews;

    quint8 mRadarID{0};
    RadarType mRadarType{AngularRadar};


    bool mMovingOnlyRaw;
    bool mMovingOnlyTrack;
    bool mContinuousDisplayRaw;
    bool mContinuousDisplayTrack;
    ViewTypes mAnalysisTypesRaw;
    ViewTypes mAnalysisTypesTrack;

    friend class AnalysisDataViewConfigDialog;

private:
    void setupUi();

    QVBoxLayout *mVLayoutTop{0};
    QSplitter *mSplitter{0};

    QAction *mActionOrientation{0};
    QAction *mActionNewAnalysisDataTable{0};
    QAction *mActionMonitoringTrackTarget{0};
    QAction *mActionViewDataConfig{0};

    QLabel *mLabelMeasurementCounter{0};                ///< 状态栏测量计数
};

class AnalysisDataViewConfigDialog : public QDialog
{
    Q_OBJECT

public:
    AnalysisDataViewConfigDialog(AnalysisDataViewI *view, QWidget *parent = nullptr);

private slots:
    void on_pushButtonOK_clicked();

    void on_pushButtonApply_clicked();

    void on_comboBoxFrameType_currentIndexChanged(int index);

    void on_pushButtonMoveUp_clicked();

    void on_pushButtonMoveDown_clicked();

private:
    void itemMoveInListWidget(bool bUp );

    void initForwardRadar(int fType);
    void initAngularRadar(int fType);

    Ui::AnalysisDataViewConfigDialog *ui;

    AnalysisDataViewI *mAnalysisDataView{0};
};

}
}

#endif // ANALYSISDATAVIEWI_H
