#ifndef ECUUPDATEMODEL_H
#define ECUUPDATEMODEL_H

#include <QObject>
#include <QAbstractTableModel>

class ECUUpdateItem;

class ECUUpdateModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    ECUUpdateModel( QObject *parent = nullptr );
    ~ECUUpdateModel();

public:
    void addData( ECUUpdateItem* item );
    void insertData( ECUUpdateItem* item, const QByteArray& udsCmd, bool before );
    void updateMatchData( const QByteArray& udsCmd, const QString& desc,
                     const QByteArrayList& words, bool bWholeWordMatch, bool bEqual  );
    void clear();
    void updateAddrs( quint32 phyAddr, quint32 funAddr, quint32 dstAddr );
    bool run( quint8 channelIndex );
    bool isAbnormal();
    void initRunResult();

signals:
    void running( int row, quint8 channelIndex );

public:
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    Qt::ItemFlags flags(const QModelIndex &index) const override;

private:

private:
    QStringList mHeader;
    QList< ECUUpdateItem* > mData;
};

#endif // ECUUPDATEMODEL_H
