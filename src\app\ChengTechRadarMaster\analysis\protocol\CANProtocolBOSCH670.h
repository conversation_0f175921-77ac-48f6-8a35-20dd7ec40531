﻿#pragma once

#include "ianalysisprotocol.h"

#include <vector>

namespace Analysis {

namespace Protocol {
        class CANProtocolBOSCH670 :
            public IAnalysisProtocol
		{
		public:
            CANProtocolBOSCH670(AnalysisWorker *analysisWorker, QObject *parent = nullptr);
            ~CANProtocolBOSCH670();

            bool analysisFrame(const Devices::Can::CanFrame &frame) override;

        private:
            bool parse0x64E(const Devices::Can::CanFrame &frame);
            bool parse0x66F(const Devices::Can::CanFrame &frame);
            bool parse0x67N(const Devices::Can::CanFrame &frame);
		};
	}
}

