<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>932</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>数据处理调试</string>
  </property>
  <widget class="Views::ObjectView::ObjectView" name="centralwidget"/>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>932</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QDockWidget" name="dockWidgetLeft">
   <property name="windowTitle">
    <string>雷达信息/跟踪点</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>1</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContentsLeft">
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="leftMargin">
      <number>3</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>3</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <item>
        <widget class="QCheckBox" name="checkBoxRefreshData">
         <property name="text">
          <string>刷写数据</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>文字大小：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSpinBox" name="spinBoxFontSize">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="value">
          <number>5</number>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelRadarResetCount">
         <property name="text">
          <string>复位次数:0</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="labelRadarVersion">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QGridLayout" name="gridLayout">
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="horizontalSpacing">
        <number>0</number>
       </property>
       <property name="verticalSpacing">
        <number>1</number>
       </property>
       <item row="1" column="3" colspan="2">
        <widget class="QLabel" name="labelVehicleSpeedInKilometer">
         <property name="minimumSize">
          <size>
           <width>80</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string>0.0 km/h</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="0" colspan="3">
        <widget class="QLabel" name="labelFrameTime">
         <property name="text">
          <string>hh:mm:ss.zzz</string>
         </property>
        </widget>
       </item>
       <item row="2" column="2" colspan="2">
        <widget class="QLabel" name="label_5">
         <property name="text">
          <string>横摆角速度:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="10" colspan="2">
        <widget class="QLabel" name="labelResponseTaskCycleTime">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>档位:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="11">
        <spacer name="horizontalSpacer_7">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>15</width>
           <height>13</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="7" colspan="2">
        <widget class="QLabel" name="label_6">
         <property name="text">
          <string>T-No.:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="11">
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>车速:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="5">
        <widget class="QLabel" name="labelRawMeasurementCount">
         <property name="text">
          <string>0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="7" colspan="2">
        <widget class="QLabel" name="labelVehicleRadious">
         <property name="text">
          <string>0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="3" colspan="2">
        <widget class="QLabel" name="label_7">
         <property name="text">
          <string>R-No.:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="4">
        <widget class="QLabel" name="labelVehicleYawRate">
         <property name="text">
          <string>0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="6">
        <widget class="QLabel" name="label_BlockagePercent">
         <property name="text">
          <string>0%</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1" colspan="2">
        <widget class="QLabel" name="labelVehicleSpeedInMeter">
         <property name="minimumSize">
          <size>
           <width>80</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string>0.0 m/s</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLabel" name="labelVehicleGear">
         <property name="text">
          <string>P</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="5">
        <widget class="QLabel" name="label_9">
         <property name="text">
          <string>遮挡:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="5" colspan="2">
        <widget class="QLabel" name="label_12">
         <property name="text">
          <string>转弯半径：</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="9">
        <widget class="QLabel" name="labelTrackMeasurementCount">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="7">
        <widget class="QLabel" name="label_10">
         <property name="text">
          <string>干扰:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="8">
        <widget class="QLabel" name="label_InterferencePercent">
         <property name="text">
          <string>0%</string>
         </property>
        </widget>
       </item>
       <item row="2" column="10">
        <widget class="QLabel" name="labelBYD16MeasurementCount">
         <property name="text">
          <string>0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <layout class="QGridLayout" name="gridLayout_2">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="QLabel" name="label_AlarmBSDLevel">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>BSD</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="label_AlarmDOWLevelRear">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>DOW(R)</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QLabel" name="label_AlarmFCTALevel">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>FCTA</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="label_AlarmFCTBLevel">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>FCTB</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QLabel" name="label_AlarmTTC">
           <property name="text">
            <string>TTC</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_AlarmBSDState">
           <property name="text">
            <string>BSD</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QLabel" name="label_AlarmFCTAState">
           <property name="text">
            <string>FCTA</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="4">
          <widget class="QLabel" name="label_AlarmFCTBState">
           <property name="text">
            <string>FCTB</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_AlarmLCALevel">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>LCA</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLabel" name="label_AlarmRCTALevel">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>RCTA</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QLabel" name="label_AlarmRCTBLevel">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>RCTB</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="3">
          <widget class="QLabel" name="label_AlarmRCWLevel">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>RCW</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="4">
          <widget class="QLabel" name="label_AlarmJALevel">
           <property name="text">
            <string>JA</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="label_AlarmLCAState">
           <property name="text">
            <string>LCA</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLabel" name="label_AlarmRCTAState">
           <property name="text">
            <string>RCTA</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="2">
          <widget class="QLabel" name="label_AlarmRCTBState">
           <property name="text">
            <string>RCTB</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QLabel" name="label_AlarmRCWState">
           <property name="text">
            <string>RCW</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="4">
          <widget class="QLabel" name="label_AlarmJAState">
           <property name="text">
            <string>JA</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="label_AlarmDOWLevelFront">
           <property name="text">
            <string>DOW(F)</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1" colspan="2">
          <widget class="QLabel" name="label_AlarmDOWState">
           <property name="text">
            <string>DOW</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="5" rowspan="3">
          <widget class="QLabel" name="label_FrameCount">
           <property name="text">
            <string> 帧数统计</string>
           </property>
          </widget>
         </item>
         <item row="1" column="6" rowspan="3">
          <widget class="QLabel" name="label_VehicleTime">
           <property name="text">
            <string>车身时间</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="horizontalSpacer_5">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>13</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="spacing">
        <number>6</number>
       </property>
       <item>
        <widget class="QLabel" name="labelEOLAngleText">
         <property name="text">
          <string>产线安装角:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelEOLAngle">
         <property name="text">
          <string>0.0</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelEstablishedAngleText">
         <property name="text">
          <string>自标定安装角:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelEstablishedAngle">
         <property name="text">
          <string>0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelAutoALNStsText">
         <property name="text">
          <string>自标定状态:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelAutoALNSts">
         <property name="text">
          <string>UNKNOWN</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QFormLayout" name="formLayout">
       <property name="horizontalSpacing">
        <number>0</number>
       </property>
       <property name="verticalSpacing">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="QLabel" name="label0x4DN">
         <property name="text">
          <string>0x4DN:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLabel" name="labelErrorInformation0x4DN">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label0x4EN">
         <property name="text">
          <string>0x4EN:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLabel" name="labelErrorInformation0x4EN">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QPlainTextEdit" name="plainTextEditTrack">
       <property name="font">
        <font>
         <pointsize>5</pointsize>
        </font>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QDockWidget" name="dockWidget">
   <property name="windowTitle">
    <string>原始点</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>1</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents">
    <layout class="QVBoxLayout" name="verticalLayout_3">
     <property name="leftMargin">
      <number>3</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>3</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QPlainTextEdit" name="plainTextEditRaw">
       <property name="font">
        <font>
         <pointsize>5</pointsize>
        </font>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>Views::ObjectView::ObjectView</class>
   <extends>QWidget</extends>
   <header>views/objectview.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
