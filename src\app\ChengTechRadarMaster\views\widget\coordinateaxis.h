﻿#ifndef COORDINATEAXIS_H
#define COORDINATEAXIS_H

#include <QWidget>

namespace Views {
namespace ObjectView {

class CoordinateAxis : public QWidget
{
    Q_OBJECT
public:
    enum ObjectAxisArea
    {
        LeftObjectAxisArea,
        RightObjectAxisArea,
        TopObjectAxisArea
    };
    explicit CoordinateAxis(ObjectAxisArea objectAxisArea = LeftObjectAxisArea, QWidget *parent = nullptr);

    ~CoordinateAxis();

    int axisWidth() const { return mAxisWidth; }
    int axisLength() const { return mAxisLength; }
    double intervalPixel() const { return mIntervalPixel; }
    double pixelPerMetre() const { return mPixelPerMetre; }

    /** @brief 设置视距范围 */
    void setRange(double minRange, double maxRange, double interval, double offset = 0.0);
    void setUnit(const QString &unit, bool show);
    void setValue(double value);

    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

signals:

private:
    ObjectAxisArea mObjectAxisArea;
    int mAxisWidth{15};        ///< 宽度
    int mAxisLength{0};        ///< 长度
    QString mUnit{"m"};         ///< 单位
    bool mUnitShow{false};      ///< 是否显示单位

    double mZoom{1};            ///< 缩放
    double mMinRange{-50};      ///< 最小值(m)
    double mMaxRange{50};       ///< 最大值(m)
    double mOffset{0.0};        ///< 补偿值
    double mOffsetPixel{1};     ///< 补偿像素(px)
    double mInterval{3};        ///< 间隔(m)
    double mIntervalPixel{1};   ///< 间隔像素(px)
    double mPixelPerMetre{1};   ///< 像素比列(pix/m),一个米需要多少个像素点

    QColor mBackgroundColor;    ///< 背景色
    QColor mScaleColor{Qt::white};         ///< 刻度颜色
    QColor mScaleTextColor{Qt::white};     ///< 刻度文本颜色

    double mValue{0};
};

} // namespace ObjectView
} // namespace Views

#endif // COORDINATEAXIS_H
