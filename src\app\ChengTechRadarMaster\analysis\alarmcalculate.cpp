﻿#include "alarmcalculate.h"

#include "script/earlywarninglua.h"

#include <QFileInfo>
#include <QtMath>

namespace Analysis {

AlarmCalculate::AlarmCalculate(QObject *parent) : QObject(parent)
{

}

AlarmCalculate::~AlarmCalculate()
{
    if (mEarlyWaringScript){
        mEarlyWaringScript->unloadScript();
    }
    delete mEarlyWaringScript;
    mEarlyWaringScript = 0;
}

void AlarmCalculate::setEarlyWarningSettings(const EarlyWarningSettings &settings)
{
    mEarlyWarningSettings = settings;

    if (mEarlyWarningSettings.mScriptFileCalculateEarlyWarning) {
        loadScriptFile();
    } else {
        unloadScriptFile();
    }
}

void AlarmCalculate::earlyWarning(AnalysisData *analysisData)
{
    Targets &targets= analysisData->mTargets[FrameTrackTarget];

    switch (analysisData->mRadarID)
    {
    case 4:
    case 5:
    case 6:
    case 7:
        calculationAlarmAndEarlyWarning(analysisData->mRadarID, targets, analysisData);
        break;
    }
}

quint16 AlarmCalculate::vehicleEarlyWarning(int radarID, quint16 alarmTypes, VehicleData *vehicleData)
{
    quint16 ret = Alarm_None;
    if (mEarlyWarningSettings.mScriptFileCalculateEarlyWarning && mEarlyWaringScript) {
        ret = mEarlyWaringScript->vehicleEarlyWarning(radarID, alarmTypes, vehicleData);
    } else {
        ret = vehicleEarlyWarningCPlusPlus(radarID, alarmTypes, vehicleData);
    }

    return ret;
}

bool AlarmCalculate::calculationEarlyWarning(int radarID, Target *target, quint16 alarmTypes, quint16 vehicleAlarmTypes, VehicleData *vehicleData, QList<EarlyWarningData> &earlyWarningDatas)
{
    bool ret = false;
    if (mEarlyWarningSettings.mScriptFileCalculateEarlyWarning && mEarlyWaringScript) {
        ret = mEarlyWaringScript->earlyWaring(radarID, target, alarmTypes, vehicleAlarmTypes, vehicleData, earlyWarningDatas);
    } else {
        ret = earlyWaringCPlusPlus(target, alarmTypes, vehicleAlarmTypes, vehicleData, earlyWarningDatas);
    }

    return ret;
}

int AlarmCalculate::alarmedID(const EarlyWarningData &warning, Target *targets, int newIndex, int &oldIndex, double &oldLevel, double &oldTTC)
{
    if (!oldLevel || (targets[newIndex].mRange < targets[oldIndex].mRange)) {
        oldIndex = newIndex;
        oldLevel = warning.mLevel;
        oldTTC = warning.mTTC;

    }

    return targets[oldIndex].mID;
}

void AlarmCalculate::calculationDOWEarlyWarning(Target *target)
{
    if (target->mEarlyWarning /*|| vehicleData->mVehicleSpeed != 0*/)
    {
//        qDebug() << __FUNCTION__ << __LINE__;
        return;
    }

    if (target->mX < mEarlyWarningSettings.mDOWDistanceOfBody ||
            target->mX > (mEarlyWarningSettings.mDOWDistanceOfBody + mEarlyWarningSettings.mDOWWidth) ||
            target->mY < (mEarlyWarningSettings.mDOWDistanceOfHeadway) ||
            target->mY > (mEarlyWarningSettings.mDOWDistanceOfHeadway + mEarlyWarningSettings.mDOWLength))
    {
//        qDebug() << __FUNCTION__ << __LINE__;
        return;
    }

    if ((target->mVy >= 0) || (target->mVy != 0 && (qFabs(target->mY) / qFabs(target->mVy)) >= 2.5))
    {
        return;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << target->mEarlyWarning << vehicleData->mVehicleSpeed << target->mX << target->mY << target->mVy << (qFabs(target->mY) / target->mVy);
    target->mEarlyWarning = true;
}

void AlarmCalculate::calculationBSDEarlyWarning(Target *target)
{
    if (target->mEarlyWarning /*|| vehicleData->mVehicleSpeed * 3.6 <= 13.6*/)
    {
        return;
    }

    if (target->mX < mEarlyWarningSettings.mBSDDistanceOfBody ||
            target->mX > (mEarlyWarningSettings.mBSDDistanceOfBody + mEarlyWarningSettings.mBSDWidth) ||
            target->mY < ( mEarlyWarningSettings.mBSDDistanceOfHeadway) ||
            target->mY > ( mEarlyWarningSettings.mBSDDistanceOfHeadway + mEarlyWarningSettings.mBSDLength))
    {
        return;
    }

    if ((target->mVy >= 0) || (target->mVy != 0 && (qFabs(target->mY) / qFabs(target->mVy)) >= 3.5))
    {
        return;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << target->mEarlyWarning << vehicleData->mVehicleSpeed << target->mX << target->mY << target->mVy << (qFabs(target->mY) / target->mVy);
    target->mEarlyWarning = true;
}

void AlarmCalculate::calculationLCAEarlyWarning(Target *target)
{
    if (target->mEarlyWarning /*|| vehicleData->mVehicleSpeed * 3.6 <= 13.6*/)
    {
        return;
    }

    if (target->mX < mEarlyWarningSettings.mLCADistanceOfBody ||
            target->mX > (mEarlyWarningSettings.mLCADistanceOfBody + mEarlyWarningSettings.mLCAWidth) ||
            target->mY < ( mEarlyWarningSettings.mLCADistanceOfHeadway) ||
            target->mY > ( mEarlyWarningSettings.mLCADistanceOfHeadway + mEarlyWarningSettings.mLCALength))
    {
        return;
    }

    if ((target->mVy >= 0) || (target->mVy != 0 && (qFabs(target->mY) / qFabs(target->mVy)) >= 3.5))
    {
        return;
    }

    target->mEarlyWarning = true;
}

void AlarmCalculate::calculationAlarmAndEarlyWarning(int radarID, Targets targets, AnalysisData *analysisData)
{
    double vehicleSpeed = analysisData->mVehicleData.mVehicleSpeed / 3.6;

    AlarmData &earlyAlarmData = analysisData->mEarlyAlarmData;
    AlarmData &alarmData = analysisData->mAlarmData;
    earlyAlarmData.mAlarmBSDState = alarmData.mAlarmBSDState;
    earlyAlarmData.mAlarmDOWState = alarmData.mAlarmDOWState;
    earlyAlarmData.mAlarmFCTAState = alarmData.mAlarmFCTAState;
    earlyAlarmData.mAlarmFCTBState = alarmData.mAlarmFCTBState;
    earlyAlarmData.mAlarmLCAState = alarmData.mAlarmLCAState;
    earlyAlarmData.mAlarmRCTAState = alarmData.mAlarmRCTAState;
    earlyAlarmData.mAlarmRCTBState = alarmData.mAlarmRCTBState;
    earlyAlarmData.mAlarmRCWState = alarmData.mAlarmRCWState;
    earlyAlarmData.mAlarmJAState = alarmData.mAlarmJAState;

    quint16 alarmTypes = earlyAlarmData.alarmTypes();
    memset(&analysisData->mEarlyAlarmData, 0, sizeof (analysisData->mEarlyAlarmData));

    quint16 vehilceAlarmTypes = vehicleEarlyWarning(radarID, alarmTypes, &(analysisData->mVehicleData));
    if (!vehilceAlarmTypes) {
        return;
    }

    Target *target = targets.mTargets;
    int indexBSD, indexLCA, indexRCW, indexDOWF, indexDOWR, indexRCTA, indexRCTB, indexFCTA, indexFCTB, indexJA;
    for (int i = 0; i < targets.mTargetCount; ++i, ++target)
    {
        if (target->mValid && target->mID == 7)
        {
            QList<EarlyWarningData> earlyWarningDatas;

            target->mRange = qSqrt(qPow(target->mX, 2) + qPow(target->mY, 2));
            target->mVsog = qSqrt(qPow(target->mVx, 2) + qPow((analysisData->mRadarID == 6 || analysisData->mRadarID == 7) ?
                                                                  (vehicleSpeed + target->mVy) : (vehicleSpeed - target->mVy), 2));
            calculationEarlyWarning(radarID, target, alarmTypes, vehilceAlarmTypes, &(analysisData->mVehicleData), earlyWarningDatas);

            foreach (const EarlyWarningData &warning, earlyWarningDatas) {
                switch (warning.mType) {
                case Alarm_None:
                    break;
                case Alarm_BSD:
                {
                    double ttc = 0;
                    earlyAlarmData.mAlarmBSDObjectID = alarmedID(warning, targets.mTargets, i, indexBSD,
                                                    earlyAlarmData.mAlarmBSDLevel, ttc);
                }
                    break;
                case Alarm_LCA:
                    earlyAlarmData.mAlarmLCAObjectID = alarmedID(warning, targets.mTargets, i, indexLCA,
                                                    earlyAlarmData.mAlarmLCALevel, earlyAlarmData.mAlarmLCAObjectTTC);
                    break;
                case Alarm_RCW:
                    earlyAlarmData.mAlarmRCWObjectID = alarmedID(warning, targets.mTargets, i, indexRCW,
                                                    earlyAlarmData.mAlarmRCWLevel, earlyAlarmData.mAlarmRCWObjectTTC);
                    break;
                case Alarm_DOWF:
                    earlyAlarmData.mAlarmDOWObjectID = alarmedID(warning, targets.mTargets, i, indexDOWF,
                                                    earlyAlarmData.mAlarmDOWFLevel, earlyAlarmData.mAlarmDOWObjectTTC);
                    break;
                case Alarm_DOWR:
                    earlyAlarmData.mAlarmDOWObjectID = alarmedID(warning, targets.mTargets, i, indexDOWR,
                                                    earlyAlarmData.mAlarmDOWRLevel, earlyAlarmData.mAlarmDOWObjectTTC);
                    break;
                case Alarm_RCTA:
                    earlyAlarmData.mAlarmRCTAObjectID = alarmedID(warning, targets.mTargets, i, indexRCTA,
                                                    earlyAlarmData.mAlarmRCTALevel, earlyAlarmData.mAlarmRCTAObjectTTC);
                    break;
                case Alarm_RCTB:
                    earlyAlarmData.mAlarmRCTBObjectID = alarmedID(warning, targets.mTargets, i, indexRCTB,
                                                    earlyAlarmData.mAlarmRCTBLevel, earlyAlarmData.mAlarmRCTBObjectTTC);
                    break;
                case Alarm_FCTA:
                    earlyAlarmData.mAlarmFCTAObjectID = alarmedID(warning, targets.mTargets, i, indexFCTA,
                                                    earlyAlarmData.mAlarmFCTALevel, earlyAlarmData.mAlarmFCTAObjectTTC);
                    break;
                case Alarm_FCTB:
                    earlyAlarmData.mAlarmFCTBObjectID = alarmedID(warning, targets.mTargets, i, indexFCTB,
                                                    earlyAlarmData.mAlarmFCTBLevel, earlyAlarmData.mAlarmFCTBObjectTTC);
                    break;
                case Alarm_JA:
                    earlyAlarmData.mAlarmJAObjectID = alarmedID(warning, targets.mTargets, i, indexJA,
                                                    earlyAlarmData.mAlarmJALevel, earlyAlarmData.mAlarmJAObjectTTC);
                    break;
                }
            }
        }
    }

    if (alarmData.alarmTypes() || earlyAlarmData.alarmTypes()) {
        qDebug() << __FUNCTION__ << __LINE__ << alarmTypes << vehilceAlarmTypes << alarmData.alarmTypes() << earlyAlarmData.alarmTypes()
//                 << alarmData.mAlarmBSDObjectID << alarmData.mAlarmBSDLevel << earlyAlarmData.mAlarmBSDObjectID << earlyAlarmData.mAlarmBSDLevel
//                 << alarmData.mAlarmLCAObjectID << alarmData.mAlarmLCALevel << alarmData.mAlarmLCAObjectTTC << earlyAlarmData.mAlarmLCAObjectID << earlyAlarmData.mAlarmLCALevel << earlyAlarmData.mAlarmLCAObjectTTC
//                 << alarmData.mAlarmDOWObjectID << alarmData.mAlarmDOWFLevel  << alarmData.mAlarmDOWRLevel << alarmData.mAlarmDOWObjectTTC
//                 << earlyAlarmData.mAlarmDOWObjectID << earlyAlarmData.mAlarmDOWFLevel << earlyAlarmData.mAlarmDOWRLevel << earlyAlarmData.mAlarmDOWObjectTTC
                 << alarmData.mAlarmRCTAObjectID << alarmData.mAlarmRCTALevel << alarmData.mAlarmRCTAObjectTTC << earlyAlarmData.mAlarmRCTAObjectID << earlyAlarmData.mAlarmRCTALevel << earlyAlarmData.mAlarmRCTAObjectTTC
                 << alarmData.mAlarmRCTBObjectID << alarmData.mAlarmRCTBLevel << alarmData.mAlarmRCTBObjectTTC << earlyAlarmData.mAlarmRCTBObjectID << earlyAlarmData.mAlarmRCTBLevel << earlyAlarmData.mAlarmRCTBObjectTTC
//                 << alarmData.mAlarmFCTAObjectID << alarmData.mAlarmFCTALevel << alarmData.mAlarmFCTAObjectTTC << earlyAlarmData.mAlarmFCTAObjectID << earlyAlarmData.mAlarmFCTALevel << earlyAlarmData.mAlarmFCTAObjectTTC
//                 << alarmData.mAlarmFCTBObjectID << alarmData.mAlarmFCTBLevel << alarmData.mAlarmFCTBObjectTTC << earlyAlarmData.mAlarmFCTBObjectID << earlyAlarmData.mAlarmFCTBLevel << earlyAlarmData.mAlarmFCTBObjectTTC
                    ;
    }
}

quint16 AlarmCalculate::vehicleEarlyWarningCPlusPlus(int radarID, quint16 alarmTypes, VehicleData *vehicleData)
{
    return Alarm_None;
}

bool AlarmCalculate::earlyWaringCPlusPlus(Target *target, quint16 alarmTypes, quint16 vehicleAlarmTypes, VehicleData *vehicleData, QList<EarlyWarningData> &earlyWarningDatas)
{
    calculationLCAEarlyWarning(target);
    calculationBSDEarlyWarning(target);
    calculationDOWEarlyWarning(target);

    return true;
}

bool AlarmCalculate::loadScriptFile()
{
    if (mEarlyWarningSettings.mScriptFileCalculateEarlyWarning) {
        QFileInfo fileInfo = QFileInfo(mEarlyWarningSettings.mCalculateEarlyWarningScriptFile);
        if (mEarlyWaringScript) {
            mEarlyWaringScript->unload();
            delete mEarlyWaringScript;
            mEarlyWaringScript = 0;
        }
        if (fileInfo.suffix() == "lua") {
            mEarlyWaringScript = new EarlyWarningLua();
        }
    }

    if (mEarlyWaringScript) {
        return mEarlyWaringScript->load(mEarlyWarningSettings.mCalculateEarlyWarningScriptFile);
    }
    return false;
}

bool AlarmCalculate::unloadScriptFile()
{
    if (!mEarlyWaringScript) {
        return true;
    }
    switch (mScriptType) {
    case ScriptPython:
    case ScriptLua:
        mEarlyWaringScript->unloadScript();
        break;
    default:
        break;
    }

    delete mEarlyWaringScript;
    mEarlyWaringScript = 0;

    return true;
}

} // namespace Analysis
