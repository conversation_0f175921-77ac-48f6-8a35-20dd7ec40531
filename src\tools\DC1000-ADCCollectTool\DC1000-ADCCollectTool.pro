QT       += core gui multimedia multimediawidgets network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

CHENGTECH_TOOL_NAME = "DC1000-ADC Collect Tool(dely 30ms)"
CHENGTECH_TOOL_VERSION = 2.1.7
TARGET = "$${CHENGTECH_TOOL_NAME} $${CHENGTECH_TOOL_VERSION}"

DESTDIR = $$PWD/bin/DC1000-ADCCollectTool

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    CANDeviceZLG.cpp \
    analysisdata.cpp \
    analysisprotocolct410.cpp \
    cameraworker.cpp \
    canparseworker.cpp \
    cansaveworker.cpp \
    dc1000adccollect.cpp \
    dc1000worker.cpp \
    devicefileasc.cpp \
    main.cpp \
    networkudpserver.cpp \
    targetsview.cpp \
    targetsviewconfigdialog.cpp \
    utils/flowlayout.cpp \
    utils/loghandler.cpp \
    utils/settingshandler.cpp \
#    views/analysisdatatableview.cpp \
#    views/analysisdataview.cpp \
#    views/analysisdataviewi.cpp \
#    views/analysismodel.cpp \
#    views/targetmonitor.cpp

HEADERS += \
    CANDeviceZLG.h \
    CANFrame.h \
    NETFrame.h \
    SafeQueue.h \
    analysisdata.h \
    analysisprotocolct410.h \
    cameraworker.h \
    canparseworker.h \
    cansaveworker.h \
    dc1000adccollect.h \
    dc1000worker.h \
    devicefileasc.h \
    networkudpserver.h \
    targetsview.h \
    targetsviewconfigdialog.h \
    utils/flowlayout.h \
    utils/loghandler.h \
    utils/settingshandler.h \
    utils/utils_global.h \
#    views/analysisdatatableview.h \
#    views/analysisdataview.h \
#    views/analysisdataviewi.h \
#    views/analysismodel.h \
#    views/targetmonitor.h \
#    views/views_global.h

FORMS += \
    dc1000adccollect.ui \
    targetsviewconfigdialog.ui \
#    views/analysisdatatableview.ui \
#    views/analysisdataview.ui \
#    views/analysisdataviewconfigdialog.ui \
#    views/targetmonitor.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

DISTFILES += \
    version.h.in

QMAKE_SUBSTITUTES += version.h.in

QMAKE_PROJECT_DEPTH = 0
INCLUDEPATH += $$PWD/../../libs/3rdparty/CAN
OPENCV=$$PWD/../../libs/3rdparty/opencv/opencv_451_VS2017_install/install

INCLUDEPATH += $$OPENCV/include
DEPENDPATH += $$OPENCV/include

contains(QT_ARCH, i386) {
LIBS += \
    -L$$PWD/../../libs/3rdparty/CAN/ZLG_CAN/lib/x86 -lzlgcan
    OPENCV = $$OPENCV/x86/vc15
}else{
LIBS += \
    -L$$PWD/../../libs/3rdparty/CAN/ZLG_CAN/lib/x64 -lzlgcan
}

LIBS += -L$$OPENCV/lib/ -lopencv_world451
LIBS += -lopengl32 -lglu32

RESOURCES += \
    rc.qrc
