#include "adas_dow_client.h"

#ifdef ALPSPRO_ADAS
#include "embARC.h"
#include "embARC_debug.h"
#include "embARC_assert.h"
#include "vdy/vdy_interface.h"
#elif defined(PC_DBG_FW)
#include "app/vehicle/vdy/vdy_interface.h"
#include "other/temp.h"
#else
#include <common/sys_common.h>
#include <mmw/mss/mmw_cli.h>
#include "app/vdy/vdy_interface.h"
#endif

/*!>****** 变量定义 *******/
/*!>  */
static uint32_t closeDow_NowTick = 0;
static uint8_t  enableCloseDOW = 0;


/*!>****** 宏定义 *******/
// 20221121_Will+
#define TIME_OF_CLOSE_DOW   180

/**
 * @brief: 关闭dow这个模块功能使能/失能，close dow
 * @date: 2022-11-23 11:44
 * @author: Will Shi
 * @param: 参数描述
 * @return: 返回描述
 * @details: 细节
 **/
static inline void rstCDOWTimer(void)
{
    closeDow_NowTick = 0;
}
static inline void setCDOWTimer(void)
{
    closeDow_NowTick++;
}
static inline uint32_t getCDOWTimer(void)
{
    return closeDow_NowTick;
}

/**
 * @brief: DOW在IG下电之后3分钟内off
 * @date: 2022-11-23 11:44
 * @author: Will Shi
 * @param: 参数描述
 * @return: 返回描述
 * @details: 细节
 **/
void setCloseDOWStatu(void) //允许关开始计时
{
    enableCloseDOW = 1;
}
static uint8_t getCloseDOWStatu(void)
{
    return enableCloseDOW;
}
void rstCloseDOWStatu(void) // 不允许关dow
{
    enableCloseDOW = 0;
    rstCDOWTimer(); 
}

/**
 * @brief: DOW在IG下电之后3分钟后off
 * @date: 2022-11-23 11:44
 * @author: Will Shi
 * @param: 参数描述
 * @return: 返回描述
 * @details: 细节
 **/
void closeDOWMid(void *param1)
{
    if (!getCloseDOWStatu())
    {
        return;
    }
    
    (void)param1;
#ifndef PC_DBG_FW
    EMBARC_PRINTF("%s, %d\n", __FUNCTION__, getCDOWTimer());
#endif

    setCDOWTimer();
    if (getCDOWTimer() >= TIME_OF_CLOSE_DOW) // 3min=180s
    {
#ifndef PC_DBG_FW
        EMBARC_PRINTF("--------%s, dow close off time %d\n", __FUNCTION__, getCDOWTimer());
#endif
        rstCDOWTimer(); 
        rstCloseDOWStatu();
        // IG下电3分钟之后才能关闭DOW，这里时间到了，在adas_state_machine文件里面会判断
        VDY_setCloseDOWOfIGPDStatu(0); 
    }
}
