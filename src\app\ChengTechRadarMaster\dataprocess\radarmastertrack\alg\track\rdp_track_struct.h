﻿/**
 * @file rdp_track_struct.h
 * @brief 
 * <AUTHOR> <PERSON> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-09-30
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-09-30 <td>1.0     <td>shao<PERSON>     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef _RDP_TRACK_STRUCT_H_
#define _RDP_TRACK_STRUCT_H_

#ifndef PC_DBG_FW
#include "rdp_types.h"
#include "rsp_types.h"
#else
#include "app/system_mgr/typedefs.h"
#include "alg/track/rdp_types.h"
#include "hal/rsp/rsp_types.h"
#endif

#ifdef INCLUDE_HEIGHT_ANGLE
#define MEASURED_VALUE_NUM 5
#else
#define MEASURED_VALUE_NUM 4
#endif

#define <PERSON><PERSON><PERSON>_LANE_CROSS_LIMIT_FLOOR      -2.5f
#define EGO_LANE_CROSS_LIMIT_CEIL   0.5f
#define POSITIVE_SIDE_LANE_CROSS_LIMIT_FLOOR EGO_LANE_CROSS_LIMIT_CEIL
#define POSITIVE_SIDE_LANE_CROSS_LIMIT_CEIL    (EGO_LANE_CROSS_LIMIT_CEIL+3.75f)
#define NEGATIVE_SIDE_LANE_CROSS_LIMIT_FLOOR (EGO_LANE_CROSS_LIMIT_FLOOR-3.75f)
#define NEGATIVE_SIDE_LANE_CROSS_LIMIT_CEIL    EGO_LANE_CROSS_LIMIT_FLOOR
#define TRACK_ID_INVALID    0xffu
#define STORE_POINT_NUM 3
#define STORE_X0_FRAME_NUM  12
#define STORE_X1_FRAME_NUM  12
#define STORE_FRAME_TIME_NUM  20    //must be no less than STORE_X0_FRAME_NUM
#define STORE_RANGE_FRAME_NUM 20    //同存储历史车速帧数保持一致

#define BUFFER_LEN_MAX 18

typedef struct
{
    int8_t     buffer[BUFFER_LEN_MAX];
    uint8_t    len;
    uint8_t    current;
}Buffer_ST;


//通道状态：none（空）；candidate（尚未进入追踪的通道）；track：（已经进入追踪的通道）
typedef enum
{
  NONE = 0,
  CANDI,
  TRACK,
} trk_type_t;

typedef enum TRK_OBJCALSSIFICATION
{
    POINT_CLASSIFICATION = 0,
    MOTORCYCLE_CLASSIFICATION = 1,
    CAR_CLASSIFICATION = 2,
    TRUCK_CLASSIFICATION = 3,
    PEDESTPEDESTRIANRAIN_CLASSIFICATION = 4,
    CYCLIST_CLASSIFICATION = 5,
    WIDE_CLASSIFICATION = 6,
    UNKNOW_CLASSIFICATION = 7,
}objClassfication_t;

typedef enum TRK_OBJDYNPROP
{
    MOVING_DYNPROP = 0,
    STATIONARY_DYNPROP = 1,
    ONCOMING_DYNPROP = 2,
    CROSSING_LEFT2RIGHT_DYNPROP = 3,
    CROSSING_RIGHT2LEFT_DYNPROP = 4,
    UNKNOWN_DYNPROP = 5,
    STOP_DYNPROP = 6,
    RESERVED_DYNPROP = 7,
}objDynProp_t;

typedef enum TRK_OBJMAINTENANCESTATE
{
  OBJ_NONE = 0,
  NEW = 1,
  MEASURED = 2,
  PREDICTED = 3,
}objMaintenanceState_t;

typedef struct
{
	trk_type_t type;
	int8_t hit;		// 每个状态下的连续关联的次数
	int8_t miss;	// 每个状态下的连续丢失的次数
	short idx_1;	// 与该通道配对cost最小的物体的index
	int16_t rcs;	// RCS of the object, unit: 0.1dBsm
	float sigma[2]; // 加速度的方差，x和y方向共两个
	float P[36];	// 状态误差的协方差矩阵，6x6个
	float x[6];		// 当前的状态，6个， //[0]:x, [1]:y, [2]:sim_z[2]（贡献横向速度）, [3]:vdySpeedInmps（共享纵向速度）, [4]:横向加速度, [5]:纵向加速度,
	float sim_z[4]; // 经过滤波后的瞬时值,0-SNR,1-Range,2-Velocity,3-Angle
	float pre_z[4]; // 下一帧的预测值,0-SNR,1-Range,2-Velocity,3-Angle
	float p_x[6];	// //[0]:x, [1]:y, [2]:sim_z[2], [3]:vdySpeedInmps, [4]:横向加速度, [5]:纵向加速度,
	/**  @brief Record the last several frame data in polar: rang, angle, doppler */
	float stored_last_z[STORE_POINT_NUM][MEASURED_VALUE_NUM];
	uint16_t stored_last_status[STORE_POINT_NUM];
	float stored_last_x0[STORE_X0_FRAME_NUM]; // 历史若干帧横向距离
	float stored_last_x1[STORE_X1_FRAME_NUM]; // 历史若干帧纵向距离
	// float stored_last_range[STORE_RANGE_FRAME_NUM];		// 历史若干帧径向距离
	float stored_last_outputX[6];		 // 记录上一帧的输出状态
	float stored_last_16trk_outputXY[4]; // 上一帧输出的16个目标协议
	float stored_last_xy[2];			 // xy[0]:x, xy[1]:y
	// float latestLngVel[5];				 // 保存最近5帧的横向加速度
	float latestSNR[5];					 // 保存最近5帧的SNR和RCS
	float latestRCS[5];
	float snrAvg; // 5帧滑动平均SNR和RCS
	float rcsAvg;
	float startPosition[2]; // 记录起始位置
	float headingAngle;		// 航向角, unit: degree
	float heighAngle;		// 俯仰角
	/* the following four members describe the box of the track */
	float front;		   // the distance from track position to the front side of the box, unit: m
	float back;			   // the distance from track position to the back side of the box, unit: m
	float left;			   // the distance from track position to the left side of the box, unit: m
	float right;		   // the distance from track position to the right side of the box, unit: m
	float inFreeSpaceProb; // 概率越大越可能在自由空间内
	float meaNearest[2];   // 最近的x和y
	float boxCenter[2];
	float rcwoverlapsumx; // RCW的均值X重叠率
	float rcwoverlapx;	  // RCW的均值X重叠率
	float dowoverlapsumx; // DOW的均值重叠率
	float dowoverlapx;	  // DOW的均值重叠率
	float nearestPosition[2];  // 记录航向方向上最近点的横纵向距离
	float rangeStartDiffValue; // 目标航迹绝对位移差值
	uint16_t rcwoverlapxcnt; // rcw目标重叠率统计次数
	uint16_t dowoverlapxcnt; // dow目标重叠率统计次数 和 RCW分开统计
	uint32_t hitBmp;		 // 最近32帧hit情况
	Buffer_ST lengthBuff;
	Buffer_ST widthBuff;
	uint16_t trkCnt;			 // 跟踪计数器
	uint16_t activeTrkCnt;		 // 有效状态航迹计数
	uint16_t groupNonzeroVelCnt; // 关联点组内非0速点统计
	uint16_t groupStaticPointCnt; // 关联点组内静止点统计
	uint16_t vr0_hit;			 // 连续被径向速度为零点关联计数。动态目标数值越大，越可能是假点
	int16_t pidNearest;
	int16_t pidStrongest;
	uint8_t objType;	  // 目标类型，0 = Unknown; 1 = pedestran; 2 = motorcycle; 3 = car; 4 = truck
	uint8_t objReferPosi; // 目标参考点位置，0=左前，1=右前，2=左后，3=右后
	uint8_t probOfExist;  // 目标存在置信度，0~100, unit: percentage
	uint8_t groupPointNumAccum;
	uint32_t status;
	int8_t reverseCnt;
	uint8_t BSDCutInTargetCnt;			// 用于CNCAP目标车切入判断
	uint8_t mergeCnt;
	uint8_t fissionCnt; // 疑似分裂目标计数
	uint16_t bestScore;
	uint16_t suspicious; // 可疑标识
	uint16_t weight;
	uint8_t movingHit; // 连续关联到运动目标计数
	uint8_t crossCnt;
	uint8_t crossAllCnt;
	uint8_t absCrossCnt;
	uint8_t skewCrossCnt;  //靠近斜穿计数（-100°到-135°）
	uint8_t keepInBackCnt;
	uint8_t bsdSuppressCnt;			 // BSD区域超车抑制速度统计
	uint8_t LngSpeedHeadingAngleCnt; // 纵向行驶的历史航向角统计
	uint8_t bumperTimestampCnt;
	uint8_t objFakeDOt;
	uint8_t isTruePoint;
	uint16_t pointTrackingFrameCount;	//
	uint8_t isCPTALFSceneCnt;		//运动的CPTALF计数，用于固定目标的角度
	uint8_t guardrailOutsideCnt;
	uint8_t LngLowSpeedHeadingAngleCnt; // 低速纵向行驶的目标角度统计
	uint8_t objDeleteFlag;				// 大于76°不输出的标志
	uint8_t onComingObjCnt;				// 对向来车目标
	uint8_t keepDowStateCnt;			// DOW维持计数
	uint8_t kepRcwAreaCnt;				// 横向距离小于-0.5m大于-2m计数
	uint8_t bsdTargetMaintainEnd;		// 结束维持
	uint8_t dowMove2Static;				// 运动转静止
	uint8_t dowShelterScene;			// 法规DOW遮挡场景标识
	uint8_t dowguardCrossCnt;			// 用以应对产品走查
	uint8_t isSuspPedestrianCnt;		//
	uint8_t isSuspPedestrian;			// CPTALF场景中疑似行人目标
	int8_t isLngMovingStatusCnt;
	uint8_t assoAbnormalCnt;			// 关联到异常点计数
	uint8_t gridMapFlag;
	uint8_t lngMovingStatus;
	uint8_t movingStatusCnt;			// 目标运动状态计数，用于判断目标起步
	uint8_t staticStatusCnt;			// 目标关联零速点计数，用于判断
	uint8_t isMirrorCnt;				// 疑似镜像假目标统计
	int16_t ObjAssoDetIdx;
	uint8_t LatCutInCnt;			//过去12帧的横向位置是靠近的次数大于6的计数
	uint8_t LatCutOutCnt;			//过去12帧的横向位置是切出的次数大于8的计数
	uint8_t fenceCnt;	//连续判成护栏点的次数
	uint8_t outWeekFenceCnt;	//跟踪点连续在弱侧护栏外的次数
	uint8_t outStrongFenceCnt;	//跟踪点连续在强侧护栏外的次数
} trk_t;

typedef struct
{
  trk_t trk[MAX_NUM_OF_TRACKS];
  float stored_move_range[STORE_X1_FRAME_NUM];  //历史若干帧自车移动的距离
  float stored_frameNumber_time[STORE_X1_FRAME_NUM];  //历史若干帧的帧间隔
  //rcta弱侧雷达目标处理
  int8_t rctaAreaValid;
  int8_t rctaAreabufferCnt;
  uint16_t forwardStaticNum;
  float rctaAreaMinlat;
  float rctaAreaMaxlat;
  float rctaAreaMinlng;
  float rctaAreaMaxlng;
  float rctaAreabufferX[10];         //rcta区域10个目标的x
  float rctaAreabufferV[10];         //rcta区域10个目标的v
  int8_t skewStopSence;			// 斜停场景
  int8_t dowguardrailblocksence;	// DOW护栏遮挡场景.
  sideLine_pkr_t sideLines;     //护栏信息
  uint8_t stableDrvCnt;		// 自车稳定行驶计数器. 
  uint8_t aebsideCarCnt;	//  AEB场景临车道遮挡车鬼探头场景计数器
  uint8_t aebsidecarsence;	// AEB临车道遮挡车鬼探头场景    (车速10-20kph)
  uint8_t aebstandcrosssence;	// AEB标准横穿场景  (车速30-40kph)
  uint8_t aebstandcrossstableDrvCnt;		// 自车稳定行驶计数器. 
  float aebsidecar_x;		// AEB遮挡车可能存在的X位置
  float aebsidecar_y;		// AEB遮挡车可能存在的Y位置
  float runingtime;			// 自车运行的时间.
} trk_pkg_t;

#endif
