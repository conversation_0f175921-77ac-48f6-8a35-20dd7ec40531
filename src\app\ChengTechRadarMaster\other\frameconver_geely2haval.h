#ifndef FRAMECONVER_GEELY2HAVAL_H
#define FRAMECONVER_GEELY2HAVAL_H

#include "iframeconver.h"

#include <QObject>


namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
}

class FrameConver_Geely2Haval : public IFrameConver
{
    Q_OBJECT

    //Geely 0x56 length = 64
    typedef struct CT_SODLSafetyCANFD3Frame39 {
        uint8_t CllsnWarnReIndcn;               // 0 "LcmaIndcn_NoLcmaWarn" 1 "LcmaIndcn_LcmaWarnLvl1" 2 "LcmaIndcn_NotUsed" 3 "LcmaIndcn_LcmaWarnLvl2"
        uint8_t CllsnWarnReOn1;                 // 0 "LcmaCtraOn_Off" 1 "LcmaCtraOn_Passive" 2 "LcmaCtraOn_Active" 3 "LcmaCtraOn_TrlrOff"
        uint8_t CtraOn1;                        // 0 "LcmaCtraOn_Off" 1 "LcmaCtraOn_Passive" 2 "LcmaCtraOn_Active" 3 "LcmaCtraOn_TrlrOff"
        uint8_t DoorOpenwarnLeIndcn;            // 0 "LcmaIndcn_NoLcmaWarn" 1 "LcmaIndcn_LcmaWarnLvl1" 2 "LcmaIndcn_NotUsed" 3 "LcmaIndcn_LcmaWarnLvl2"
        uint8_t DoorOpenwarnOn1;                // 0 "LcmaCtraOn_Off" 1 "LcmaCtraOn_Passive" 2 "LcmaCtraOn_Active" 3 "LcmaCtraOn_TrlrOff"
        uint8_t DoorOpenwarnRiIndcn;            // 0 "LcmaIndcn_NoLcmaWarn" 1 "LcmaIndcn_LcmaWarnLvl1" 2 "LcmaIndcn_NotUsed" 3 "LcmaIndcn_LcmaWarnLvl2"
        uint8_t RsdsSysSts;                     // 0 "RsdsMstSt_Cfg" 1 "RsdsMstSt_StrtUp" 2 "RsdsMstSt_Runng" 3 "RsdsMstSt_Blkd" 4 "RsdsMstSt_Faulty" 5 "RsdsMstSt_Shutdown" 6 "RsdsMstSt_Hot" 7 "RsdsMstSt_Cal"
        uint8_t RsdsSysSts_UB;
        uint8_t CllsnWarnReIndcn_UB;
        uint8_t CllsnWarnReOn1_UB;
        uint8_t CtraOn1_UB;
        uint8_t DoorOpenwarnLeIndcn_UB;
        uint8_t DoorOpenwarnOn1_UB;
        uint8_t DoorOpenwarnRiIndcn_UB;
        uint8_t RctaBrkReq_UB;
        uint8_t RcwmBrkReq_UB;
        uint8_t RctaBrkReqRctaBrkReq;           // 0 "YesNo1_Yes" 1 "YesNo1_No"
        uint8_t RctaBrkReqCntr;
        uint8_t RctaBrkReqChks;
        uint8_t RcwmBrkReqRcwmBrkReq;           // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t RcwmBrkReqCntr;
        uint8_t RcwmBrkReqChks;
    }CT_SODLSafetyCANFD3Frame39_t;

    //Geely 0x127 length = 64
    typedef struct CT_SODLSafetyCANFD3Frame40 {
        uint8_t LcmaAudWarn;                    // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t LcmaAudWarn_UB;
        uint8_t LcmaIndcnLe;                    // 0 "WarnLvl_NoWarn" 1 "WarnLvl_WarnLvl1" 2 "WarnLvl_WarnLvl2_NoAudio" 3 "WarnLvl_WarnLvl3_Audio"
        uint8_t LcmaIndcnRi;                    // 0 "WarnLvl_NoWarn" 1 "WarnLvl_WarnLvl1" 2 "WarnLvl_WarnLvl2_NoAudio" 3 "WarnLvl_WarnLvl3_Audio"
        uint8_t RctaIndcnLe;                    // 0 "LcmaIndcn_NoLcmaWarn" 1 "LcmaIndcn_LcmaWarnLvl1" 2 "LcmaIndcn_NotUsed" 3 "LcmaIndcn_LcmaWarnLvl2"
        uint8_t RctaIndcnRi;                    // 0 "LcmaIndcn_NoLcmaWarn" 1 "LcmaIndcn_LcmaWarnLvl1" 2 "LcmaIndcn_NotUsed" 3 "LcmaIndcn_LcmaWarnLvl2"
        uint8_t LcmaIndcnLe_UB;
        uint8_t LcmaIndcnRi_UB;
        uint8_t RctaIndcnLe_UB;
        uint8_t RctaIndcnRi_UB;
        uint8_t RctaBrkReqQM;                   // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t RctaBrkReqQM_UB;
        uint8_t RcwmBrkReqQM;                   // 0 "OnOff1_Off" 1 "OnOff1_On"
        uint8_t RcwmBrkReqQM_UB;
    }CT_SODLSafetyCANFD3Frame40_t;

    //Geely 0x179 length = 64
    typedef struct CT_SODRSafetyCANFD3Frame37 {
        float ReSideRdrRiObj15RdrObjAx;
        float ReSideRdrRiObj15RdrObjAy;
        float ReSideRdrRiObj15ObjBoxCenterLat;
        float ReSideRdrRiObj15ObjBoxCenterLgt;
        float ReSideRdrRiObj15RdrObjBoxLength;
        float ReSideRdrRiObj15RdrObjBoxWidth;
        uint8_t ReSideRdrRiObj15RdrObjTyp;      // 0 "RdrObjTyp_Ukwn" 1 "RdrObjTyp_Car" 2 "RdrObjTyp_MotorCycle" 3 "RdrObjTyp_Truck" 4 "RdrObjTyp_Ped" 5 "RdrObjTyp_ObstdVert1" 6 "RdrObjTyp_ObstdVert2" 7 "RdrObjTyp_Anim" 8 "RdrObjTyp_ObjGen" 9 "RdrObjTyp_Bicycle" 10 "RdrObjTyp_VehofUkwnClass" 11 "RdrObjTyp_Reserved"
        uint32_t ReSideRdrRiObj15ObservationHist;
        uint8_t ReSideRdrRiObj15RdrObjChks;
        uint8_t ReSideRdrRiObj15RdrObjCntr;
        uint8_t ReSideRdrRiObj15RdrObjConf;
        float ReSideRdrRiObj15RdrObjDxStdDe;
        float ReSideRdrRiObj15RdrObjDy;
        uint8_t ReSideRdrRiObj15RdrObjMtnPat;   // 0 "RdrObjMtnPat_Ukwn" 1 "RdrObjMtnPat_Staty" 2 "RdrObjMtnPat_StatyFromMovg" 3 "RdrObjMtnPat_Movg" 4 "RdrObjMtnPat_Receding" 5 "RdrObjMtnPat_Oncoming" 6 "RdrObjMtnPat_CrossingFromLe" 7 "RdrObjMtnPat_CrossingFromRi"
        float ReSideRdrRiObj15RelVx;
        float ReSideRdrRiObj15RelVy;
        float ReSideRdrRiObj15RdrObjVyStdDe;
        uint8_t ReSideRdrRiObj15RdrObjElevnSts; // 0 "RdrObjElevnSts_Unknown" 1 "RdrObjElevnSts_OverRidable" 2 "RdrObjElevnSts_Rlv" 3 "RdrObjElevnSts_UnderRidable"
        uint8_t ReSideRdrRiObj15RdrObjID;
        uint8_t ReSideRdrRiObj15RdrObjTypConfBik;
        uint8_t ReSideRdrRiObj15RdrObjTypConfPed;
        float ReSideRdrRiObj15RdrObjDyStdDe;
        float ReSideRdrRiObj15RdrObjDx;
        uint8_t ReSideRdrRiObj15RdrObjTrackSts; // 0 "RdrObjTrackSts_Invld" 1 "RdrObjTrackSts_Updated" 2 "RdrObjTrackSts_New" 3 "RdrObjTrackSts_Coasted"
        uint8_t ReSideRdrRiObj15RdrObjIsInFreeSp; // 0 "YesNo2_No" 1 "YesNo2_Yes"
        float ReSideRdrRiObj15RdrObjVy;
        uint8_t ReSideRdrRiObj15RdrObjElevnConf;
        uint8_t ReSideRdrRiObj15RdrObjMirrProblt;
        uint8_t ReSideRdrRiObj15RdrObjNotRealPro;
        uint8_t ReSideRdrRiObj15RdrObjTypConfVeh;
        uint8_t ReSideRdrRiObj15RdrObjStatyCnt;
        uint8_t ReSideRdrRiObj15RdrObjTiAlv;
        float ReSideRdrRiObj15RdrObjVx;
        float ReSideRdrRiObj15RdrObjVxStdDe;
        uint8_t ReSideRdrRiObj15RdrObjCoastCnt;
        float ReSideRdrRiObj15Heading;
        uint8_t ReSideRdrRiObj15RdrObjUsedTracke; // 0 "RdrObjUsedTracker_Stationary" 1 "RdrObjUsedTracker_MovingCVModel" 2 "RdrObjUsedTracker_MovingCTModel" 3 "RdrObjUsedTracker_Undefined"
        uint32_t ReSideRdrRiObj15QualityBits;
        uint8_t ReSideRdrRiObj15_UB;
        uint8_t SODRDoorOpenwarnRiIndcn;        // 0 "LcmaIndcn_NoLcmaWarn" 1 "LcmaIndcn_LcmaWarnLvl1" 2 "LcmaIndcn_NotUsed" 3 "LcmaIndcn_LcmaWarnLvl2"
        uint8_t SODRLcmaIndcnRi;                // 0 "WarnLvl_NoWarn" 1 "WarnLvl_WarnLvl1" 2 "WarnLvl_WarnLvl2_NoAudio" 3 "WarnLvl_WarnLvl3_Audio"
        uint8_t SODRReSideRdrRiSts0;            // 0 "RsdsMstSt_Cfg" 1 "RsdsMstSt_StrtUp" 2 "RsdsMstSt_Runng" 3 "RsdsMstSt_Blkd" 4 "RsdsMstSt_Faulty" 5 "RsdsMstSt_Shutdown" 6 "RsdsMstSt_Hot" 7 "RsdsMstSt_Cal"
        uint8_t SODRRctaIndcnRi;                // 0 "LcmaIndcn_NoLcmaWarn" 1 "LcmaIndcn_LcmaWarnLvl1" 2 "LcmaIndcn_NotUsed" 3 "LcmaIndcn_LcmaWarnLvl2"
        uint8_t SODRRiTTCLv;                    // 0 "WarnLvl_NoWarn" 1 "WarnLvl_WarnLvl1" 2 "WarnLvl_WarnLvl2_NoAudio" 3 "WarnLvl_WarnLvl3_Audio"
        uint8_t SODRDoorOpenwarnRiIndcn_UB;
        uint8_t SODRLcmaIndcnRi_UB;
        uint8_t SODRRctaIndcnRi_UB;
        uint8_t SODRReSideRdrRiSts0_UB;
        uint8_t SODRRiTTCLv_UB;
        uint8_t SODRCllsnWarnReIndcn;           // 0 "LcmaIndcn_NoLcmaWarn" 1 "LcmaIndcn_LcmaWarnLvl1" 2 "LcmaIndcn_NotUsed" 3 "LcmaIndcn_LcmaWarnLvl2"
        uint8_t SODRCllsnWarnReIndcn_UB;
        uint8_t SODRRctaBrkReqChks;
        uint8_t SODRRctaBrkReqCntr;
        uint8_t SODRRctaBrkReqRctaBrkReq;       // 0 "YesNo1_Yes" 1 "YesNo1_No"
        uint8_t SODRRctaBrkReq_UB;
        uint8_t SODRRcwmBrkReq_UB;
        uint8_t SODRRcwmBrkReqChks;
        uint8_t SODRRcwmBrkReqCntr;
        uint8_t SODRRcwmBrkReqRcwmBrkReq;       // 0 "OnOff1_Off" 1 "OnOff1_On"
    }CT_SODRSafetyCANFD3Frame37_t;

    //Geely 0x234 length = 64
    typedef struct CT_SODLSafetyCANFD3Frame41 {
        uint8_t LcmaOn;                         // 0 "LcmaCtraOn_Off" 1 "LcmaCtraOn_Passive" 2 "LcmaCtraOn_Active" 3 "LcmaCtraOn_TrlrOff"
        uint8_t LeTTCLv;                        // 0 "WarnLvl_NoWarn" 1 "WarnLvl_WarnLvl1" 2 "WarnLvl_WarnLvl2_NoAudio" 3 "WarnLvl_WarnLvl3_Audio"
        uint8_t RcwmLiReq;                      // 0 "YesNo1_Yes" 1 "YesNo1_No"
        uint8_t ReSideRdrLeSts0;                // 0 "RsdsMstSt_Cfg" 1 "RsdsMstSt_StrtUp" 2 "RsdsMstSt_Runng" 3 "RsdsMstSt_Blkd" 4 "RsdsMstSt_Faulty" 5 "RsdsMstSt_Shutdown" 6 "RsdsMstSt_Hot" 7 "RsdsMstSt_Cal"
        uint8_t ReSideRdrRiSts0;                // 0 "RsdsMstSt_Cfg" 1 "RsdsMstSt_StrtUp" 2 "RsdsMstSt_Runng" 3 "RsdsMstSt_Blkd" 4 "RsdsMstSt_Faulty" 5 "RsdsMstSt_Shutdown" 6 "RsdsMstSt_Hot" 7 "RsdsMstSt_Cal"
        uint8_t RiTTCLv;                        // 0 "WarnLvl_NoWarn" 1 "WarnLvl_WarnLvl1" 2 "WarnLvl_WarnLvl2_NoAudio" 3 "WarnLvl_WarnLvl3_Audio"
        uint8_t LcmaOn_UB;
        uint8_t LeTTCLv_UB;
        uint8_t RcwmLiReq_UB;
        uint8_t ReSideRdrLeSts0_UB;
        uint8_t ReSideRdrRiSts0_UB;
        uint8_t RiTTCLv_UB;
    }CT_SODLSafetyCANFD3Frame41_t;

    //Haval 0x16f length = 64
    typedef struct CT_RSDS_FD1 {
        float RSDS_ObjRelAccelY_Le;
        uint8_t Checksum_RSDS_R_2;
        uint8_t RSDS_Brk_ErrSts;
        uint8_t RSDS_BrkgReq;
        float RSDS_BrkgReqVal;
        uint8_t RSDS_LEDLightReqLeft;
        uint8_t RSDS_LEDLightReqRight;
        uint8_t RSDS_IPSoundReq;
        uint8_t RSDS_BSD_warningReqRight;
        uint8_t RSDS_BSD_warningReqLeft;
        uint8_t RSDS_DOW_warningReqRight;
        uint8_t RSDS_DOW_warningReqLeft;
        uint8_t RSDS_RCW_Trigger;
        uint8_t RSDS_ErrSts;
        uint8_t RSDS_BrkgTrig;
        uint8_t RSDS_BliSts;
        uint8_t RSDS_TrailerSts;
        uint8_t RSDS_LCAResp;
        uint8_t RSDS_DOWResp;
        uint8_t RSDS_RCWResp;
        uint8_t RSDS_RCTAResp;
        uint8_t RSDS_RCTABrkResp;
        uint8_t RSDS_CTA_Actv;
        uint8_t RSDS_RCTA_warningReqRight;
        uint8_t RSDS_RCTA_warningReqLeft;
        uint8_t RollingCounter_RSDS_R_2;
        uint8_t Checksum_RSDS_R_3;
        uint8_t RSDS_LeTarSts;
        uint8_t RSDS_ObjIDLe;
        float RSDS_ObjLgtPosnCurvLe;
        float RSDS_ObjLatPosnCurvLe;
        float RSDS_ObjLgtSpdCurvLe;
        float RSDS_ObjLatSpdCurvLe;
        float RSDS_ObjTTCLe;
        uint8_t RollingCounter_RSDS_R_3;
        uint8_t Checksum_RSDS_R_4;
        uint8_t RSDS_ObjLenLe;
        float RSDS_ObjWidthLe;
        uint8_t RSDS_ObjStatyLe;
        uint8_t RSDS_ZoneIndLe;
        uint8_t RSDS_ObjStsLe;
        uint8_t RSDS_ObjLenRi;
        float RSDS_ObjWidthRi;
        uint8_t RSDS_ObjStatyRi;
        uint8_t RSDS_ZoneIndRi;
        uint8_t RSDS_ObjStsRi;
        uint8_t RollingCounter_RSDS_R_4;
        uint8_t Checksum_RSDS_R_5;
        uint8_t RSDS_RiTarSts;
        uint8_t RSDS_ObjIDRi;
        float RSDS_ObjLgtPosnCurvRi;
        float RSDS_ObjLatPosnCurvRi;
        float RSDS_ObjLgtSpdCurvRi;
        float RSDS_ObjLatSpdCurvRi;
        float RSDS_ObjTTCRi;
        uint8_t RollingCounter_RSDS_R_5;
        float RSDS_ObjDistXStd_Le;
        float RSDS_ObjDistYStd_Le;
        float RSDS_ObjRelVelXStd_Le;
        float RSDS_ObjRelVelYStd_Le;
        float RSDS_ObjRAccelXStd_Le;
        float RSDS_ObjRAccelYStd_Le;
        float RSDS_ObjDistXStd_Ri;
        float RSDS_ObjDistYStd_Ri;
        float RSDS_ObjRelVelXStd_Ri;
        float RSDS_ObjRelVelYStd_Ri;
        float RSDS_ObjRAccelXStd_Ri;
        float RSDS_ObjRAccelYStd_Ri;
        float RSDS_ObjRelAccelX_Le;
        float RSDS_ObjRelAccelX_Ri;
        float RSDS_ObjRelAccelY_Ri;
    }CT_RSDS_FD1_t;

public:
    FrameConver_Geely2Haval( Devices::Can::DeviceManager* deviceManager, QObject *parent = nullptr );


protected:
    virtual void analysis( const Devices::Can::CanFrame& frame );
    virtual  void conver();
    virtual  void packageFrame();
    virtual void initRecvIDAndEndID();

private://Geely
    // 0x56
    bool decode_SODLSafetyCANFD3Frame39(CT_SODLSafetyCANFD3Frame39_t *userData, uint8_t *data, int length);
    // 0x127
    bool decode_SODLSafetyCANFD3Frame40(CT_SODLSafetyCANFD3Frame40_t *userData, uint8_t *data, int length);
    // 0x179
    bool decode_SODRSafetyCANFD3Frame37(CT_SODRSafetyCANFD3Frame37_t *userData, uint8_t *data, int length);
    // 0x234
    bool decode_SODLSafetyCANFD3Frame41(CT_SODLSafetyCANFD3Frame41_t *userData, uint8_t *data, int length);

private://Haval
    bool encode_RSDS_FD1(CT_RSDS_FD1_t *userData, uint8_t *data, int length);

private:
    CT_SODLSafetyCANFD3Frame39_t mGeelyFrame56;
    CT_SODLSafetyCANFD3Frame40_t mGeelyFrame127;
    CT_SODRSafetyCANFD3Frame37_t mGeelyFrame179;
    CT_SODLSafetyCANFD3Frame41_t mGeelyFrame234;

    CT_RSDS_FD1_t mHavalFrame16F;
};

#endif // FRAMECONVER_GEELY2HAVAL_H
