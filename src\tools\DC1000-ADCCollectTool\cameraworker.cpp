﻿#include "cameraworker.h"

#include <QDateTime>
#include <QDebug>

#define     VIDEO_WIDTH     1280
#define     VIDEO_HIGNT     720

static void cameraReceive(CameraWorker *pCANDevice)
{
    std::cout << "线程结束，开始读取Camera数据..." << std::endl;
    while (pCANDevice->isOpened()) {
        pCANDevice->readFrame();
    }
    pCANDevice->closeDevice();
    std::cout << "线程结束，停止读取Camera数据." << std::endl;
}

CameraWorker::CameraWorker(QObject *parent) : QObject(parent)
{

}

bool CameraWorker::open(int index)
{
    mCameraIndex = index;
    mOpened = mVideoCapture.open(mCameraIndex, cv::CAP_DSHOW) && mVideoCapture.isOpened();
    if (mOpened) {
        mVideoCapture.set(cv::CAP_PROP_FRAME_WIDTH, VIDEO_WIDTH);
        mVideoCapture.set(cv::CAP_PROP_FRAME_HEIGHT, VIDEO_HIGNT);

        std::thread(cameraReceive, this).detach();
    }
    mClosed = false;
    qDebug() << __FUNCTION__ << __LINE__ << index << mCameraIndex << mOpened << mVideoCapture.isOpened();

    return mOpened;
}

void CameraWorker::close()
{
    mOpened = false;
}

bool CameraWorker::startSave(const std::string &filename)
{
    mSaveIndex = 0;

    cv::Size size=cv::Size((int)mVideoCapture.get(cv::CAP_PROP_FRAME_WIDTH),
                        (int)mVideoCapture.get(cv::CAP_PROP_FRAME_HEIGHT));
    mSaving = mVideoWriter.open(filename, cv::VideoWriter::fourcc('H', '2', '6', '4'), 30, size, true);
    return mSaving;
}

void CameraWorker::stopSave()
{
    mSaving = false;
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutexSave);
    mVideoWriter.release();
}

bool CameraWorker::isOpened(int index) const
{
    return mOpened && (index == mCameraIndex);
}

bool CameraWorker::readFrame()
{
    if (!mVideoCapture.read(mFrame)) {
        return false;
    }

    //显示增加文本
    QString text = QString("%1 SI: %2").arg(QDateTime::currentDateTime().toString("yyyy/MM/dd hh:mm:ss:zzz"),24,QLatin1Char(' ')).arg(mSaveIndex);
    cv::putText(mFrame, text.toStdString(), cv::Point(0, 40), cv::FONT_HERSHEY_COMPLEX, 1, cv::Scalar(0, 255, 0),0);

    Mat2QImage();
    emit cameraReady(mSaveIndex);

    if (mSaving) {
        // 加锁(对象释放时自动解锁)
        std::lock_guard<std::mutex> lg(mMutexSave);
        mVideoWriter.write(mFrame);
        mSaveIndex++;
    } else {
        mSaveIndex = 0;
    }

    return true;
}

const QImage &CameraWorker::getFrame()
{
    // 加锁(对象释放时自动解锁)
//    std::lock_guard<std::mutex> lg(mMutex);

    return mImage;
}

void CameraWorker::closeDevice()
{
    if (mSaving) {
        stopSave();
    }
    mVideoCapture.release();
    mClosed = true;
}

bool CameraWorker::Mat2QImage()
{
    if(mFrame.type()==CV_8UC1)
    {
        QVector<QRgb>colorTable;
        for(int i=0;i<256;i++)
            colorTable.push_back(qRgb(i,i,i));
        const uchar*qImageBuffer=(const uchar*)mFrame.data;
        mImage = QImage(qImageBuffer,mFrame.cols,mFrame.rows,mFrame.step,QImage::Format_Indexed8);
        mImage.setColorTable(colorTable);
        return true;
    }
    //8-bitsunsigned,NO.OFCHANNELS=3
    if(mFrame.type()==CV_8UC3)
    {
        const uchar*qImageBuffer=(const uchar*)mFrame.data;
        mImage = QImage(qImageBuffer,mFrame.cols,mFrame.rows,mFrame.step,QImage::Format_RGB888).rgbSwapped();
        return true;
    }
    else
    {
        qDebug()<<"ERROR:MatcouldnotbeconvertedtoQImage.";
        return false;
    }
}
