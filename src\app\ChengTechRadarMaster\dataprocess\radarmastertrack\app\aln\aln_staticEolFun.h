/**
 * @file     aln_staticEolFun.h
 * @brief    The header file of aln_staticEolFun.c
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2023-03-28
 * 
 * 
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2023-03-28  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _ALN_STATICEOLFUN_H_
#define _ALN_STATICEOLFUN_H_


/*****************************************************************************
  INCLUDE
 *****************************************************************************/
#ifndef PC_DBG_FW
#include "rsp_types.h"
#else
#include "hal/rsp/rsp_types.h"
#endif

/****************************************************************************
  DEFINE
 ****************************************************************************/
#define EOL_COLLECT_CNT_MAX         12
#define EOL_COLLECT_CNT_MIN         EOL_COLLECT_CNT_MAX - 3

/****************************************************************************
  STRUCT
 ****************************************************************************/
typedef struct
{
    float r_obj;            //目标距离
    float r_dif;            //目标距离搜索范围
    float spd_obj;          //目标速度，一般都是0
    float v_dif;            //目标速度的搜索范围
    float a_obj;            //真实目标角度，一般都是0度进行校准
    float a_dif;            //目标角度的搜索范围
    uint8_t power;          //发射功率
    uint8_t en_eol;         //使能eol操作
    uint16_t eol_finish_cnt;//eol操作完成操作计数器
    float eol_angle;        //eol计算的结果
    float tx1avg;
    float tx2avg;
    float tx1sqrt;
    float tx2sqrt;
    float tx1per;
    float tx2per;
    uint8_t tx1_s_cnt;
    uint8_t tx2_s_cnt;
    uint8_t tx1_cnt;
    uint8_t tx2_cnt;
    uint32_t eol_time;
    uint8_t eol_run_ok;     //最后是否运行成功状态
    float fixAngleOffset;   //传入安装角度，希望的安装角度是多少
}CT_EOL_OPT_t;

typedef struct
{
    float previous_fix_angle;   //进入标定前的安装角度，即当前正在使用的安装角度
    float new_fix_angle;        //服务标定成功后计算出的安装角度
    uint8_t run_status;         //标定的过程状态
    uint8_t chanle_0_obj_cnt;   //tx1目标计数
    uint8_t chanle_1_obj_cnt;   //tx2目标计数
    uint8_t run_counter;        //运行计数
    uint8_t routine_status;     //例程状态
    uint8_t routine_result;     //例程结果
    uint8_t is_save_to_nvm;     //是否已经保存到nvm，需要存入flash的前提是标定成功
    uint32_t service_count;     //进行了服务多少次
    float hdev_angle;           //横向偏差角
}CT_EOL_DCR_t;

typedef struct
{
    float r;
    float v;
    float a;
    float mag;
}EOL_OBJ_t;

typedef struct
{
    EOL_OBJ_t obj[EOL_COLLECT_CNT_MAX];
    uint8_t objCnt;
}CT_ALN_EOL_t;


/*****************************************************************************
  DECLARATION
 *****************************************************************************/
void ALN_StaticEOL_toStop(void);
CT_EOL_DCR_t *ALN_getStaticEolAlnInfo(void);
void ALN_staticClearSaveFlag(void);
int ALN_StaticEolAngleCalcMainFun(const RSP_DetObjectList_t *ptrDetObjectList);
CT_EOL_DCR_t *Service31Handle_StaticALNAngleCalc(int subServerNumber);
uint8_t ALN_getStaticState();

#endif

