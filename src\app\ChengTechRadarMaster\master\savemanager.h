﻿#ifndef SAVEMANAGER_H
#define SAVEMANAGER_H

#include <QObject>
#include <QDateTime>

class QAction;

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Analysis {
class AnalysisManager;
}

namespace Camera {
class MulitCamera;
}

class ScreenRecordSaveWorker;
class HeSai<PERSON>iderWorker;

namespace Core {

class SaveManager : public QObject
{
    Q_OBJECT
public:
    explicit SaveManager(Devices::Can::DeviceManager *deviceManger,
                         Analysis::AnalysisManager *analysisManger,
                         HeSaiLiderWorker *heSaiLiderWoker,
                         QMap<QString /*description + deviecname*/, Camera::MulitCamera*> *cameras,
                         QObject *parent = nullptr);

    bool startSave(bool needResponse = false);
    void stopSave();

    bool isSaveing() const { return mSaveing; }

    void setSavePath(const QString &path) { mSavePath = path; }
    QString savePath() const { return mSavePath; }
    QString projectSavePath() const { return mProjectSavePath; }
    QString errorString() const { return mErrorString; }

    QAction *actionSelectSavePath() const { return mActionSelectSavePath; }
    QAction *actionOpenSavePath() const { return mActionOpenSavePath; }
    QAction *actionSave() const { return mActionSave; }
    QAction *actionSaveAnalysisData() const { return mActionSaveAnalysisData; }
    QAction *actionSaveOldStyle() const { return mActionSaveOldStyle; }
    QAction *actionSaveCANFrameASC() const { return mActionSaveCANFrameASC; }
    QAction *actionSaveCANFrameBLF() const { return mActionSaveCANFrameBLF; }
    QAction *actionSaveVideo() const { return mActionSaveVideo; }
    QAction *actionSaveHeSaiLider() const { return mActionSaveHeSaiLider; }


    void setSaveAnalysisData( bool bSave );

signals:
    void saveFilename(const QString &filename);

public slots:
    void saveStartAndStop();

private slots:
    void selectSavePath();
    void openSavePath();

private:
    bool startSaveVideo();
    bool stopSaveVideo();
    bool mkSavePath();

    QAction *mActionSelectSavePath;
    QAction *mActionOpenSavePath;
    QAction *mActionSave{0};
    QAction *mActionSaveAnalysisData{0};
    QAction *mActionSaveOldStyle{0};
    QAction *mActionSaveCANFrameASC{0};
    QAction *mActionSaveCANFrameBLF{0};
    QAction *mActionSaveVideo{0};
    QAction *mActionSaveHeSaiLider{0};

    bool mSaveing{false};

    QDateTime mSaveTime;
    QString mSavePath{"./data"};
    QString mProjectSavePath;
    QString mCANSavePath;
    QString mVideoSavePath;
    QString mErrorString;
    quint8 mSaveCanDataType{0};

    Devices::Can::DeviceManager *mDeviceManager{0};
    Analysis::AnalysisManager *mAnalysisManger{0};

    ScreenRecordSaveWorker* mScreenRecordSaveWorker{NULL};
    HeSaiLiderWorker *mHeSaiLiderWorker{NULL};
    QMap<QString /*description + deviecname*/, Camera::MulitCamera*> *mCameras{0};
};

} // namespace Core

#endif // SAVEMANAGER_H
