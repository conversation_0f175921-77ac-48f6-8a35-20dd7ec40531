/**
 * @file aln_install_cfg.c
 * @brief 车型配置字对应的安装信息.
 * <AUTHOR> (wa<PERSON><PERSON>@chengtech.net)
 * @version 1.0
 * @date 2023-04-25
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date     <th>Version <th>Author  <th>Description
 * <tr><td>2023-04-25   <td>1.0     <td>wangjuhua   <td>修改内容
 * <tr><td>2023-09-05   <td>1.0     <td>erthfw      <td>修改文件名
 * </table>
 * @copyright Copyright (c) 2023 Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef PC_DBG_FW
#include "vehicle_cfg.h"
#include "aln_install_cfg.h"
#else
#include "aln_type.h"
#include "aln_install_cfg.h"
#endif

#if ( defined(VEHICLE_TYPE_BYD_HA5) || defined(VEHICLE_TYPE_BYD_EM2) || defined(VEHICLE_TYPE_BYD_UR) )
#ifdef VEHICLE_TYPE_BYD_HA5
/**
 * @brief 默认安装信息,编译时赋默认值. 写车型配置字时copy实际值.
 */
INSTALL_MESSAGE_T installmessage = {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR,          // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR,          // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR,           // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR,           // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL,      // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL,      // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL,      // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL,      // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL,
};

INSTALL_MESSAGE_T installmessageDefault= {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR,          // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR,          // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR,           // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR,           // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL,      // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL,      // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL,      // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL,      // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL,
};

INSTALL_MESSAGE_T installmessage_HA5E = {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT_HA5E,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT_HA5E,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT_HA5E,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT_HA5E,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR_HA5E,         // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR_HA5E,         // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR_HA5E,          // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR_HA5E,          // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL_HA5E,     // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL_HA5E,     // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL_HA5E,     // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL_HA5E,     // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR_HA5E,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR_HA5E,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR_HA5E,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR_HA5E,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL_HA5E,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL_HA5E,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL_HA5E,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL_HA5E,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL_HA5E,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL_HA5E,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL_HA5E,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL_HA5E,
};

#elif defined VEHICLE_TYPE_BYD_EM2

INSTALL_MESSAGE_T installmessage = {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR,          // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR,          // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR,           // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR,           // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL,      // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL,      // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL,      // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL,      // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL,
};

INSTALL_MESSAGE_T installmessageDefault = {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR,          // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR,          // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR,           // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR,           // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL,      // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL,      // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL,      // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL,      // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL,
};

INSTALL_MESSAGE_T installmessage_EM2EH = {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT_STERA,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT_STERA,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT_STERA,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT_STERA,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR_STERA,          // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR_STERA,          // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR_STERA,           // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR_STERA,           // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL_STERA,      // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL_STERA,      // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL_STERA,      // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL_STERA,      // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR_STERA,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR_STERA,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR_STERA,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR_STERA,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL_STERA,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL_STERA,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL_STERA,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL_STERA,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL_STERA,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL_STERA,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL_STERA,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL_STERA,
};

#elif defined VEHICLE_TYPE_BYD_UR

INSTALL_MESSAGE_T installmessage = {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR,          // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR,          // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR,           // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR,           // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL,      // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL,      // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL,      // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL,      // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL,
};

INSTALL_MESSAGE_T installmessageDefault = {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR,          // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR,          // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR,           // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR,           // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL,      // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL,      // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL,      // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL,      // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL,
};

INSTALL_MESSAGE_T installmessage_UREC5R12V = {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT_UREC,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT_UREC,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT_UREC,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT_UREC,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR_UREC,          // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR_UREC,          // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR_UREC,           // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR_UREC,           // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL_UREC,      // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL_UREC,      // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL_UREC,      // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL_UREC,      // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR_UREC,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR_UREC,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR_UREC,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR_UREC,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL_UREC,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL_UREC,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL_UREC,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL_UREC,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL_UREC,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL_UREC,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL_UREC,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL_UREC,
};

INSTALL_MESSAGE_T installmessage_URED5R12V = {
    .dir_front_left = CONNECTOR_DIR_FRONT_LEFT_URED,
    .dir_front_right = CONNECTOR_DIR_FRONT_RIGHT_URED,
    .dir_rear_left = CONNECTOR_DIR_REAR_LEFT_URED,
    .dir_rear_right = CONNECTOR_DIR_REAR_RIGHT_URED,
    .aln_obj_angle_fcr = ALN_OBJ_HORIZONTAL_ANGLE_FCR_URED,          // 前角目标标定位置
    .aln_obj_angle_rcr = ALN_OBJ_HORIZONTAL_ANGLE_RCR_URED,          // 后角目标标定位置
    .aln_execpt_angle_fcr = ALN_EXPECT_FIX_ANGLE_FCR_URED,           // 前角期望安装角度
    .aln_execpt_angle_rcr = ALN_EXPECT_FIX_ANGLE_RCR_URED,           // 后角期望安装角度
    .rear_center_x_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL_URED,      // 左前角雷达与后轴中心在X轴方向的距离
    .rear_center_y_fl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL_URED,      // 左前角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL_URED,      // 左后角雷达与后轴中心在X轴方向的距离
    .rear_center_y_rl = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL_URED,      // 左后角雷达与后轴中心在Y轴方向的距离
    .rear_center_x_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR_URED,
    .rear_center_y_fr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR_URED,     
    .rear_center_x_rr =  MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR_URED,   
    .rear_center_y_rr = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR_URED,
    .mount6pos_to_outer_x_fl = MOUNT6POS_TO_OUTER_EDGE_X_FL_URED,
    .mount6pos_to_outer_y_fl = MOUNT6POS_TO_OUTER_EDGE_Y_FL_URED,
    .mount4pos_to_outer_x_fl = MOUNT4POS_TO_OUTER_EDGE_X_FL_URED,
    .mount4pos_to_outer_y_fl = MOUNT4POS_TO_OUTER_EDGE_Y_FL_URED,
    .mount7pos_to_outer_x_fl = MOUNT7POS_TO_OUTER_EDGE_X_FL_URED,
    .mount7pos_to_outer_y_fl = MOUNT7POS_TO_OUTER_EDGE_Y_FL_URED,
    .mount5pos_to_outer_x_fl = MOUNT5POS_TO_OUTER_EDGE_X_FL_URED,
    .mount5pos_to_outer_y_fl = MOUNT5POS_TO_OUTER_EDGE_Y_FL_URED,
};
#endif

/**
 * @brief Get the install message object
 * @return INSTALL_MESSAGE_T* 
 */
INSTALL_MESSAGE_T *get_install_message(void)
{
    return &installmessage;
}

#endif

