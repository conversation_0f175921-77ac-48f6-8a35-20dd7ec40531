﻿#ifndef TRUEVALUESYSTEMWORKER_H
#define TRUEVALUESYSTEMWORKER_H

#include <QObject>
#include <QMutex>
#include <QFile>
#include <QDataStream>

#include "hesailider.h"

typedef struct HeSaiLiderFileHeader {
    quint64 mTimestemp; // 保存时间时间戳(ms)
    quint64 mVersion;   // 上位机个版本号，每个2byte 1.0.0.0
} HeSaiLiderFileHeader;

typedef struct HeSaiLiderDataHeader {
    quint64 mTimestemp; // 时间戳
    quint32 mSaveNumber;            // 保存序号
    quint64 mHeSaiLiderFrameNumber;
    quint64 mRawFrameNumber{0};     // 原始点帧号
    quint64 mTrackFrameNumber{0};   // 跟踪点帧号
    quint32 mTargetTotal{0};        // 目标数量
} HeSaiLiderDataHeader;

typedef struct HeSaiLiderTargetData {
    float x;
    float y;
    float z;
} HeSaiLiderTargetData;

class HeSaiLiderWorker : public QObject
{
    Q_OBJECT
public:
    typedef struct Settings {
        float mMountingHeight{2.0}; // 安装高度
        float mLongitudinalDistanceCompensation{2.5};  // 纵距补偿
        float mLateralDistanceCompensation{0.0};  // 纵距补偿
        float mUpperFilterX{50.0}; // 过滤横向距离MAX
        float mLowerFilterX{-50.0}; // 过滤横向距离MIN
        float mUpperFilterY{150.0}; // 过滤纵向距离MAX
        float mLowerFilterY{-150.0}; // 过滤纵向距离MIN
        float mUpperFilterZ{0.0}; // 过滤上限高度
        float mLowerFilterZ{-1.6}; // 过滤下限高度
        float mCalibrationAngle{0.0};   // 校准角度


        QVariant getSettings() const;
        bool setSettings(const QVariant &settings);
    }Settings;

    explicit HeSaiLiderWorker(QObject *parent = nullptr);

    bool isRunning() { return !mStoped; }
    bool isSaving() { return mSaved; }

    void setConfig(const Settings &settings) { mSettings = settings; }

    const Settings &getConfig() const { return mSettings; }

signals:
    void state(unsigned int detectionFrameIndex, unsigned int objectFrameIndex);
    void finished(bool ok = true, const QString &msg = "");
    void showTarget(int index, int count, const QString &targetString);
    void frameNumber(int number);

public slots:
    void stop();
    void run(int frameCount, bool startSave, const QString &ip, quint16 port, bool anyIP);
    bool startSave(QString savePath, const QDateTime &beginTime);
    bool stopSave();

    void lockDetection();
    void unlockDetection();
    void lockObject();
    void unlockObject();

    void setRadarFrameNumber(quint64 raw, quint64 track);

public:
    Pandar64_Target *mDetectionList = new Pandar64_Target[PANDAR64_TARGET_MAX];
    uint16_t last_azimuth_{0};
    uint16_t m_iAzimuthRange{36000};
    int start_angle_{0};
    Settings mSettings;

    QString mDetectionString;
    QString mObjecctString;

    quint64 mHeSaiLiderFrameNumber{0};

    QMutex mDetectionMutex;
    QMutex mObjectMutex;

    quint32 mTargetCount{0};
    bool mStoped{false};

    bool mSaved{false};
    quint64 mSaveCount{0};
    QFile mSaveFile;

    quint64 mRawFrameNumber{0};
    quint64 mTrackFrameNumber{0};
};

#endif // TRUEVALUESYSTEMWORKER_H
