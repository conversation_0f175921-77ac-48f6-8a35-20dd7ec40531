﻿/**
 * @file linear_regression.c
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2024-11-26
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-11-26 <td>1.0     <td>Will <PERSON>     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#include <stdbool.h>
#include <math.h>
#include <string.h>

#ifdef ALPSPRO_ADAS
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "rdp/track/data_process/rdp_interface.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/common/linear_regression.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#elif defined(PC_DBG_FW)
#include "alg/track/rdp_interface.h"
#include "app/adas/customizedrequirements/adas_vehicle_ctrls.h"
#include "app/adas/customizedrequirements/adas_signal_integration.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/vehicle/vdy/vdy_interface.h"
#include "app/vehicle/vdy/vdy_types.h"

#include <windows.h>
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/system_mgr/typedefs.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/common/linear_regression.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "other/temp.h"
#else
#include "app/rdp/rdp_clth_radar_lib.h"
#include "app/rdp/rdp_interface.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "common/include/vehicle_cfg.h"
#endif

/**
 * @brief 一阶线性回归
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 */
void ADAS_LinearRegression_FirstOrder(const ALARM_OBJECT_T *pobjAlm,
                                      const OBJ_NODE_STRUCT *pobjPath,
                                      const uint8_t i,
                                      const OBJ_ADAS_TYPE_ENUM type,
                                      const uint8_t n,
                                      float *normalize_x,
                                      float *normalize_y,
                                      float *w,
                                      float *b)
{
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (normalize_x == NULL) || (normalize_y == NULL) ||
        (n > ADAS_HISTORY_NUM) || (w == NULL) || (b == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return;
    }
    (void)pobjAlm;
    float sum_x = 0.0f, sum_y = 0.0f, sum_xy = 0.0f, sum_xx = 0.0f;
    uint8_t num = n;
    int8_t j = 0;

    // 需要考虑到lifecycle. 低生命周期的目标. 历史数据不足 可能会导致错误拟合.
    // 之所以还要减1 是后续的判断下标需要对齐
    if ((pobjPath[i].lifeCycle < num) && (pobjPath[i].lifeCycle > 1))
    {
        num = pobjPath[i].lifeCycle - 1;
    }

    // 遍历5个历史数据点，计算sum_x, sum_y, sum_xy, sum_xx
    for (j = 1; j < (num + 1); j++) // 从第2组开始计算 
    {
        if (j >= ADAS_HISTORY_NUM)
        {
            return; 
        }
        sum_x += normalize_x[j];
        sum_y += normalize_y[j];
        sum_xy += normalize_x[j] * normalize_y[j];
        sum_xx += normalize_x[j] * normalize_x[j];
    }

    // 使用最小二乘法公式计算斜率w和截距b
    *w = (num * sum_xy - sum_x * sum_y) / (num * sum_xx - sum_x * sum_x);
    *b = (sum_y - (*w) * sum_x) / num;
}

/**
 * @brief 滑动平均值
 *
 * @param sample 最新数据
 * @param size 样本数量
 * @return float 均值结果
 */
void ADAS_calculateMovingAverage(const float sample, const uint8_t i, uint8_t n, uint8_t lifecycle, float *avgVel)
{
    if (avgVel == NULL) 
    {
        // 处理错误，例如返回或设置一个默认值
        return;
    }
    
    uint8_t age = lifecycle;
    float avg = *avgVel;

    if (age < n)
    {
        n = age;
    }
    avg = avg + (sample - avg) / n;
    *avgVel = avg; 
}
/**
 * @brief 加权滑动平均值
 *
 * @param sample 最新数据
 * @param size 样本数量
 * @return float 均值结果
 */
void ADAS_calculateWeightedAverage(const float sample, const uint8_t i, uint8_t n, uint8_t lifecycle, float *avgVel)
{
    if ((avgVel == NULL) || (n > ADAS_HISTORY_HEADINGANGLE_NUM))
    {
        return;
    }

    uint8_t age = lifecycle;
//    float avg = *avgVel;
    // 定义权重数组，假设权重递减，您可以根据需要调整权重的计算方式
    float weights[ADAS_HISTORY_HEADINGANGLE_NUM];
    float weightSum = 0;

    if (age < n)
    {
        n = age; 
    }
    for (uint8_t j = 0; j < n; j++)
    {
        weights[j] = (n - j); // 权重递减，最近的样本权重最大
        weightSum += weights[j];
    }
    weights[0] = weights[0] / weightSum;

    // 更新加权平均值
    *avgVel = *avgVel + weights[0] * (sample - *avgVel);
}
/**
 * @brief 计算均值
 *
 * @param arry 角度数组
 * @param size 样本数量
 * @return float 均值结果
 */
float ADAS_CalculateAvgTwo(const float *arry, const uint8_t n)
{
    if ((arry == NULL) || (n > ADAS_HISTORY_NUM))
    {
        return 0;
    }
    float sum = 0.0f, avg = 0.0f;
    uint8_t j = 0;

    for (j = 0U; j < n; j++)
    {
        if (j >= ADAS_HISTORY_NUM)
        {
            continue;
        }
        sum += arry[j];
    }

    avg = (sum / n);

    return avg;
}

/**
 * @brief 对数据标准化，这里是均值标准化
 *  对数据进行标准化处理，以便于线性回归模型的训练和预测
 *  (value - min) / (max - min);
 *  (value - mean)
 *
 * @return
 */
void ADAS_DataNormalizeToMeans(const ALARM_OBJECT_T *pobjAlm,
                               OBJ_NODE_STRUCT *pobjPath,
                               const uint8_t i,
                               const OBJ_ADAS_TYPE_ENUM type,
                               const uint8_t n, 
                               float *normalize_x,
                               float *normalize_y)
{
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (normalize_x == NULL) || (normalize_y == NULL) ||
        (n > ADAS_HISTORY_NUM) || (type >= ADAS_TYPE_MAX))
    {
        return;
    }
    float /*avg = 0.0f,*/ tmp = 0.0f; 
    uint8_t j = 0U;

    if (type == ADAS_TYPE_MAX)
    {
        // pobjPath[i].avgX = ADAS_CalculateAvgTwo(pobjPath[i].stored_last_x, n); // 已在ADAS_covTrackerObjCoordinate()计算 
        // pobjPath[i].avgY = ADAS_CalculateAvgTwo(pobjPath[i].stored_last_y, n); //

        // 数据标准化
        for (j = 0U; j < n; j++)
        {
            if (j >= ADAS_HISTORY_NUM)
            {
                continue;
            }
            tmp = (pobjPath[i].stored_last_x[j] > FLOAT_NEG_EPS) ? pobjPath[i].avgX : -pobjPath[i].avgX;
            normalize_x[j] = pobjPath[i].stored_last_x[j] - tmp; 
        }
        for (j = 0U; j < n; j++)
        {
            if (j >= ADAS_HISTORY_NUM)
            {
                continue;
            }
            tmp = (pobjPath[i].stored_last_y[j] > FLOAT_NEG_EPS) ? pobjPath[i].avgY : -pobjPath[i].avgY;
            normalize_y[j] = pobjPath[i].stored_last_y[j] - tmp;
        }
    }
    else if (type == ADAS_TYPE_DOW)
    {
        /**
         * @brief 结论：当x和y坐标轴对调后，线性回归参数关系如下：
         *
         * 1. 新斜率是原斜率的倒数：w_new = 1/w_old
         * 2. 新截距是原截距的负值除以原斜率：b_new = -b_old/w_old
         *
         * 特殊情况：
         * 1. 当原始斜率w_old接近0时，对调后的斜率w_new将非常大，可能导致数值不稳定
         * 2. 当原始斜率w_old非常大时（接近垂直线），对调后的斜率w_new将接近0
         * 3. 当原始斜率为1或-1时，对调后斜率仍为1或-1，但截距会变化
         */
        // 数据标准化
        for (j = 0U; j < n; j++)
        {
            if (j >= ADAS_HISTORY_NUM)
            {
                continue;
            }
            normalize_y[j] = pobjPath[i].data_stored.filter_x[j]; // 在DOW功能处，对调x和y
        }
        for (j = 0U; j < n; j++)
        {
            if (j >= ADAS_HISTORY_NUM)
            {
                continue;
            }
            normalize_x[j] = pobjPath[i].data_stored.filter_y[j]; // 在DOW功能处，对调x和y
        }
    }
    else
    {
        // 数据标准化
        for (j = 0U; j < n; j++)
        {
            if (j >= ADAS_HISTORY_NUM)
            {
                continue;
            }
            normalize_x[j] = pobjPath[i].stored_last_x[j];
        }
        for (j = 0U; j < n; j++)
        {
            if (j >= ADAS_HISTORY_NUM)
            {
                continue;
            }
            normalize_y[j] = pobjPath[i].stored_last_y[j];
        }
    }
}

/**
 * @brief 使用新的点计算均方误差Mean Squared Error(MSE)
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 * @return
 */
float ADAS_CalculateSinglePointMSE(const float x,
                                   const float y,
                                   const float w,
                                   const float b)
{
    float y_pred = 0;
    float error = 0; 

    // 使用拟合的模型计算新的y值
    y_pred = w * x + b;

    // 计算实际值和预测值之间的差
    error = y - y_pred;

    // 计算误差平方，即均方误差（单点）
    error = error * error;

    return error;
}

/**
 * @brief 计算均方误差 Mean Squared Error(MSE)
 *  如果数据单位是 距离（米），通常希望整体 MSE 在 0.01 ~ 0.1
 *  如果 MSE 表示速度误差（如米每秒），通常会希望控制在 0.1 ~ 0.5 的范围内。
 *  自动驾驶汽车的路径规划、碰撞预测，通常需要 MSE 在 0.001 ~ 0.01 的级别，特别是用于检测车辆周围环境的传感器系统。
 *
 * @return
 */
float ADAS_CalculateTotalMSE(const ALARM_OBJECT_T *pobjAlm,
                             const OBJ_NODE_STRUCT *pobjPath,
                             const uint8_t i,
                             const uint8_t n,
                             const float *normalize_x,
                             const float *normalize_y,
                             const float w,
                             const float b)
{
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (normalize_x == NULL) ||
        (normalize_y == NULL) || (n > ADAS_HISTORY_NUM) || (n < 1U))
    {
        return 1.0f; // 通常希望整体 MSE 在 0.01 ~ 0.1
    }
    (void)pobjAlm;
    float mse = 0.0f, y_pred = 0.0f;
    int8_t j = 0;
    float temp_y_pred = 0.0f;
    float errorMSE = 0.0f;

    for (j = 1; j < (n + 1); j++) // 从第2组开始计算
    {
        if (j >= ADAS_HISTORY_NUM)
        {
            continue;
        }
        y_pred = w * normalize_x[j] + b; // 计算预测值
        temp_y_pred = normalize_y[j] - y_pred;
        mse += (temp_y_pred * temp_y_pred); // 累加平方误差
    }
    errorMSE = mse / n;

    return errorMSE;
}
/**
 * @brief 计算均方误差 Mean Squared Error(MSE)，不包含I点
 *
 * @return
 */
float ADAS_CalculateTotalMSE_NonI(const OBJ_NODE_STRUCT *pobjPath,
                                  const uint8_t n,
                                  const float w,
                                  const float b,
                                  const bool ismove)
{ 
    float mse = 0.0f, y_pred = 0.0f;
    int8_t j = 0;
    float temp_y_pred = 0.0f;
    float errorMSE = 0.0f;
    
    for (j = 0; j < n; j++)
    {
        if (j >= ADAS_HISTORY_NUM)
        {
            continue;
        }
        y_pred = w * pobjPath->stored_last_x[j] + b; // 计算预测值
        if (true == ismove)
        {
            temp_y_pred = pobjPath->stored_last_groundy[j] - y_pred;
        }
        else
        {
            temp_y_pred = pobjPath->stored_last_y[j] - y_pred;
        }

        mse += temp_y_pred * temp_y_pred; // 累加平方误差
    }
    errorMSE = mse / n;

    return errorMSE;
}

/**
 * @brief 计算决定系数R^2 R_squared
 *  在 ADAS 系统的应用中，模型的拟合结果直接影响到系统对周围环境的判断和反应。如果R^2过低，意味着线性回归模型可能无法
 *  准确预测目标的行为轨迹，可能导致不准确的检测和决策。因此，对于涉及到车辆路径预测、碰撞风险评估等场景，确保R^2足够
 *  高（如接近 0.9 以上）是非常重要的，以提高系统的安全性和可靠性。
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 *  决定系数（R²）函数
 *  R² = 1 - (SS_residual / SS_total)
 *
 *  其中：
 *  ∑_(i=1)^(n) (yᵢ - ŷᵢ)² 表示残差平方和（Residual Sum of Squares）。
 *  ∑_(i=1)^(n) (yᵢ - ȳ)² 表示总平方和（Total Sum of Squares）。
 *
 *  yᵢ 是真实的目标值，ŷᵢ 是预测值，ȳ 是目标变量的均值。
 *  R² 表示模型解释目标变量方差的比例，范围为 [0, 1]，值越大，表示拟合效果越好。
 * @return
 */
float ADAS_CalculateRSquared(const ALARM_OBJECT_T *pobjAlm,
                             const OBJ_NODE_STRUCT *pobjPath,
                             const uint8_t i,
                             const uint8_t n,
                             float *normalize_x,
                             float *normalize_y,
                             const float w,
                             const float b)
{
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (normalize_x == NULL) ||
        (normalize_y == NULL) || (n > ADAS_HISTORY_NUM) || (n < 1U))
    {
        return 0.0f; // 范围为 [0, 1]，值越大，表示拟合效果越好。
    }
    (void)pobjAlm;
    float ss_total = 0.0f, ss_residual = 0.0f;
    float y_mean = 0.0f, y_pred = 0.0f;
    float temp_y_mean = 0.0f, temp_y_pred = 0.0f;
    int8_t j = 0;
    float coefficientVal = 0.0f;

    // 计算 y 的均值
    for (j = 1; j < (n + 1); j++) // 从第2组开始计算
    {
        if (j >= ADAS_HISTORY_NUM)
        {
            continue;
        }
        y_mean += normalize_y[j];
    }
    y_mean /= n;

    // 计算总平方和(SS_total)和残差平方和(SS_residual)
    for (j = 1; j < (n + 1); j++) // 从第2组开始计算
    {
        if (j >= ADAS_HISTORY_NUM)
        {
            continue;
        }
        temp_y_mean = normalize_y[j] - y_mean;
        ss_total += temp_y_mean * temp_y_mean; // 总平方和, 计算每个点到均值的平方和

        y_pred = w * normalize_x[j] + b; // 计算预测值
        temp_y_pred = normalize_y[j] - y_pred;
        ss_residual += temp_y_pred * temp_y_pred; // 残差平方和, 计算每个点到预测值的平方和
    }
    /* 防止除以零 */
    if (ss_total < FLOAT_EPS)
    {
        return 0.0f;
    }
    /* 计算R平方 */
    coefficientVal = 1 - (ss_residual / ss_total);

    /* 限制R平方的范围在[0,1]之间 */
    coefficientVal = fmaxf(0.0f, fminf(1.0f, coefficientVal));

    return coefficientVal;
}

/**
 * @brief 计算(0, -2)和(-2, 3)的方差
 *  方差在 0.01 ~ 0.1，那么单点 MSE 也应该在 0.01 ~ 0.1 以内，通常希望单点 MSE ≤ 方差。
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 * @return
 */
float ADAS_CalculateVariance(const ALARM_OBJECT_T *pobjAlm,
                             const OBJ_NODE_STRUCT *pobjPath,
                             const uint8_t i,
                             const uint8_t n,
                             float *normalize_x,
                             float *normalize_y)
{
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (normalize_x == NULL) || (normalize_y == NULL) ||
        (n > ADAS_HISTORY_NUM) || (n < 1U))
    {
        return 1; // 方差在 0.01 ~ 0.1
    }
    (void)pobjAlm; 
    float mean_y = 0.0f, variance = 0.0f;
    float temp_y = 0.0f;
    int8_t j = 0;

    // 遍历n个历史数据点，计算mean_y
    for (j = 1; j < (n + 1); j++) // 从第2组开始计算
    {
        if (j >= ADAS_HISTORY_NUM)
        {
            continue;
        }
        mean_y += normalize_y[j];
    }

    // 计算y的均值
    mean_y = mean_y / n;

    // 计算方差
    for (j = 1; j < (n + 1); j++) // 从第2组开始计算
    {
        if (j >= ADAS_HISTORY_NUM)
        {
            continue;
        }
        temp_y = normalize_y[j] - mean_y;
        variance += temp_y * temp_y;
    }

    // 计算方差
    variance /= n;
    // 计算标准化方差
    // variance /= (mean_y * mean_y);

    return variance;
}

/**
 * @brief FCTAB求极值,判断目标是否满足碰撞点
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 * @return
 */
float ADAS_FCTAB_calculatedExtremum(const ALARM_OBJECT_T *pobjAlm,
                                    const OBJ_NODE_STRUCT *pobjPath,
                                    const uint8_t i,
                                    const VDY_Info_t *pVDY,
                                    const OBJ_ADAS_TYPE_ENUM type,
                                    float *normalize_x,
                                    float *normalize_y,
                                    const float x,
                                    const float y,
                                    const float w,
                                    const float b)
{
    float error = 0;
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (pVDY == NULL) || (normalize_x == NULL) ||
        (normalize_y == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return error;
    }
    float y_pred = 0.f;
    float supplementaryAngles = 0.f; // 定义互补角，不是很准确
    float supplementaryAngles_y = 0.f;
    float longitudinalBuff = FCTB_BREAK_AREA_Y_MAX;
    float warningLongitudinalBuff = 0.f;
    //float break_area_ymax = FCTB_BREAK_AREA_Y_MAX;
    //float warningWidthbuf = 0.f;
    float iaMinRange = FCTA_ACTIVE_MIN_IA;
    float iaMaxRange = FCTA_ACTIVE_MAX_IA;
    float iaMinRange_Buff = FCTA_ACTIVE_MIN_IA_BUF; // FCTA扩一些
    float iaMaxRange_Buff = FCTA_ACTIVE_MAX_IA_BUF;

    if ((type == ADAS_TYPE_FCTA) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps < 2.7f)) // 10kph以下
    {
        iaMinRange = FCTB_ACTIVE_MIN_IA;
        iaMaxRange = FCTB_ACTIVE_MAX_IA;
        iaMinRange_Buff += 0.f; // FCTA 且自车速度小于10kph严格限制，防止低速报了FCTA，扩了FCTB的buff
        iaMaxRange_Buff += 0.f;
    }
    else if (type == ADAS_TYPE_FCTB)
    {
        iaMinRange = FCTB_ACTIVE_MIN_IA;
        iaMaxRange = FCTB_ACTIVE_MAX_IA;
        iaMinRange_Buff += FCTB_ACTIVE_MAX_IA_BUF; // fctb严格限制,符号没错
        iaMaxRange_Buff += FCTB_ACTIVE_MIN_IA_BUF;
    }
    else if (type == ADAS_TYPE_RCTA) // 这里是RCTA
    {
        iaMinRange = RCTA_ACTIVE_MIN_IA;
        iaMaxRange = RCTA_ACTIVE_MAX_IA;
    } 
    else 
    {
    }

    if ((pobjPath[i].x < 3.0f) &&
        (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
         ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)) || 
         (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U)))
    {
        iaMaxRange_Buff += 0.0f;
        iaMinRange_Buff += 0.0f;
    }
    else if (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene != 1U)
    {
        iaMinRange_Buff += 25.0f;   // 符号没错
        iaMaxRange_Buff += -25.0f; 
    }

    // 只计算在IA范围内的
    if ((pobjPath[i].avgheadingAngle <= (iaMaxRange + iaMaxRange_Buff)) && (pobjPath[i].avgheadingAngle >= (iaMinRange + iaMinRange_Buff)))
    {
        // 场景补偿
        if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
            ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO))) // 4s
        {
            longitudinalBuff += 2.0f;
        }
        else if (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U)
        {
            longitudinalBuff += 0.5f;
        }

        if (pobjPath[i].lastAlarmType & ALARM_ACTIVE_FCTA) // 上一帧在报警，则放大区域，左右各扩大0.5m
        { 
            warningLongitudinalBuff += FCTB_WARNING_WIDTH_BUF;
        }

        // 开始计算
        // 0~89.5与180~90.5的值是一样的
        if (pobjPath[i].avgheadingAngle >= 90.0f)
        {
            supplementaryAngles = 180.0f - pobjPath[i].avgheadingAngle;
            if ((supplementaryAngles > 89.5f) && (supplementaryAngles < 90.5f))
            {
                supplementaryAngles = 89.5f; // 无法计算tan(90°)
            }
        }
        else
        {
            supplementaryAngles = pobjPath[i].avgheadingAngle;
            if (supplementaryAngles < 0.5f)
            {
                supplementaryAngles = 0.5f; // tan(0°)=0,除数不能为0
            }
        }
        supplementaryAngles = supplementaryAngles * DEG2RAD; // 这里执行完之后是弧度
        supplementaryAngles_y = VEHICLE_WIDTH_INFO / tanf(supplementaryAngles); //

        /**
         * @brief  大角度高速行驶补偿
         *  大角度，自车6kph，目标高速补偿
         */
        if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
            ((pobjPath[i].avgheadingAngle >= 105.0f) && (pobjPath[i].avgheadingAngle <= 130.0f)) &&
            (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 1.67f) &&
            (pobjPath[i].avgVx > 3.5f)) //
        {
            if (type == ADAS_TYPE_FCTA)
            {
                longitudinalBuff += 4.0f;
            }
            else if (type == ADAS_TYPE_FCTB)
            {
                longitudinalBuff += 1.8f;
            }
        }
        /**
         * @brief  大角度低速二轮车和行人，行驶补偿 
         */
        else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
            ((pobjPath[i].avgheadingAngle >= 100.0f) && (pobjPath[i].avgheadingAngle <= 130.0f)) &&  
            (pobjPath[i].avgVx <= 3.5f)) //
        {
            if (type == ADAS_TYPE_FCTA)
            {
                longitudinalBuff += 3.0f;
            }
            else if (type == ADAS_TYPE_FCTB)
            {
                longitudinalBuff += 1.0f;
            }
        }
        /**
         * @brief  垂直角度范围且大航迹框较高速行驶补偿
         *  同样的制动后距离，四轮车视觉看着比较短，补四轮车
         */
        else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
                 ((pobjPath[i].avgheadingAngle >= 75.0f) && (pobjPath[i].avgheadingAngle <= 105.0f)) &&
                 ((pobjPath[i].boxLength > FCTB_CROSS_OBJBIGTHRESHOLD) || (pobjPath[i].boxWidth > FCTB_CROSS_OBJBIGTHRESHOLD)) &&
                 (pobjPath[i].avgVx > 3.0f)) //
        {
            if (type == ADAS_TYPE_FCTA)
            {
                longitudinalBuff += 2.0f;
            }
            else if (type == ADAS_TYPE_FCTB)
            {
                longitudinalBuff += 1.0f;
            }
        }
        /**
         * @brief  垂直角度范围较高速行驶补偿
         *  较高速目标纵向补偿之后不容易出现漏刹
         */
        else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
                 ((pobjPath[i].avgheadingAngle >= 75.0f) && (pobjPath[i].avgheadingAngle <= 105.0f)) &&
                 ((pobjPath[i].boxLength < 2.5f) && (pobjPath[i].boxWidth < 2.5f)) &&
                 (pobjPath[i].avgVx > 2.18f)) //
        {
            if (type == ADAS_TYPE_FCTA)
            {
                longitudinalBuff += 2.0f;
            }
            else if (type == ADAS_TYPE_FCTB)
            {
                longitudinalBuff += 0.5f;
            }
        } 
        /**
         * @brief  FCTA起步补偿 和FCTB自车大于10补偿，小角度不补？（大角度补偿已经在上面做了，这里不再做了）
         *  较快速目标补偿，
         */
        else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
                 (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 2.8f) &&
                 (pobjPath[i].avgVx > 2.5f))
        {
            if ((pobjPath[i].avgheadingAngle >= 80.0f) && (pobjPath[i].avgheadingAngle <= 100.0f))
            {
                if (type == ADAS_TYPE_FCTA)
                {
                    supplementaryAngles_y += pVDY->pVDY_DynamicInfo->vdySpeedInmps * 3.6f * 0.15f;
                }
                else if (type == ADAS_TYPE_FCTB)
                {
                    supplementaryAngles_y += pVDY->pVDY_DynamicInfo->vdySpeedInmps * 3.6f * 0.05f;
                }
            }
        }/**
         * @brief  全角度范围补偿 
         */
        else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
            ((pobjPath[i].avgheadingAngle >= 75.0f) && (pobjPath[i].avgheadingAngle <= 105.0f)) && 
            (pobjPath[i].avgVx > 1.70f)) //
        {
            if (type == ADAS_TYPE_FCTA)
            {
                longitudinalBuff += 2.0f;
            }
            else if (type == ADAS_TYPE_FCTB)
            {
                longitudinalBuff += 1.0f;
            }
        }
        if (fabsf(supplementaryAngles_y) > (VEHICLE_WIDTH_INFO * 2.0f))
        {
            supplementaryAngles_y = (VEHICLE_WIDTH_INFO * 2.0f);
        }

        // 使用拟合的模型计算新的y值
        y_pred = w * x + b;
        // EMBARC_PRINTF("k : %f b : %f \n", w, b);
        if (pobjPath[i].avgheadingAngle >= 85.0f) // 大角度靠近
        {
            // 预测的y值和实际的y值比较,这里不能合并，y的绝对值大小不一
            if (y < FLOAT_NEG_EPS)
            {
                // EMBARC_PRINTF("--- %d x : %f y : %f 大角度 入点 pre %f\n", i, pobjPath[i].x, pobjPath[i].y, y_pred);
                error = (((y - warningLongitudinalBuff) <= y_pred) && ((1.0f + (supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
            else
            {
                // EMBARC_PRINTF("+++ %d x : %f y : %f 大角度 出点 pre %f\n", i, pobjPath[i].x, pobjPath[i].y, y_pred);
                // 出点的负侧不能卡的太多.  正点不能给到太大.
                error = (((-2.0f + -warningLongitudinalBuff) <= y_pred) && (((supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
        }
        else
        {
            // 预测的y值和实际的y值比较,这里不能合并，y的绝对值大小不一
            if (y < FLOAT_NEG_EPS)
            {
                // EMBARC_PRINTF("--- %d x : %f y : %f 小角度 入点 pre %f\n", i, pobjPath[i].x, pobjPath[i].y, y_pred);
                supplementaryAngles_y = 3 - supplementaryAngles_y + longitudinalBuff + warningLongitudinalBuff;
                error = (((y - warningLongitudinalBuff) <= y_pred) && (supplementaryAngles_y >= y_pred)) ? 1 : 0;
            }
            else
            {
                // EMBARC_PRINTF("+++ %d x : %f y : %f 小角度 出点 pre %f\n", i, pobjPath[i].x, pobjPath[i].y, y_pred);
                error = (((-0.30f + warningLongitudinalBuff) <= y_pred) && ((1.5f + (supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
        }
    }

    return error;
}

/**
 * @brief FCTAB求极值,判断目标是否满足碰撞点
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 * @return
 */
#if 0
float ADAS_FCTB_calculatedExtremum(const ALARM_OBJECT_T *pobjAlm,
                                    const OBJ_NODE_STRUCT *pobjPath,
                                    const uint8_t i,
                                    const VDY_Info_t *pVDY,
                                    const OBJ_ADAS_TYPE_ENUM type,
                                    float *normalize_x,
                                    float *normalize_y,
                                    const float x,
                                    const float y,
                                    const float w,
                                    const float b)
{
    float error = 0;
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (pVDY == NULL) || (normalize_x == NULL) ||
        (normalize_y == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return error;
    }
    float y_pred = 0.f;
    float supplementaryAngles = 0.f; // 定义互补角，不是很准确
    float supplementaryAngles_y = 0.f;
    float warningLongitudinalBuff = 0.f;

    // 只计算在IA范围内的
    if ((pobjPath[i].avgheadingAngle <= (iaMaxRange + iaMaxRange_Buff)) && (pobjPath[i].avgheadingAngle >= (iaMinRange + iaMinRange_Buff)))
    {

#if (FCTAB_FOR_ACCEPTANCE_INSPECTION == 1)
        // if ((pobjPath[i].lastAlarmType & ALARM_ACTIVE_FCTA) || (0 != pobjPath[i].overAlarmStartCnt))
#else
        if (pobjPath[i].lastAlarmType & ALARM_ACTIVE_FCTA) // 上一帧在报警，则放大区域，左右各扩大0.5m
#endif
        {
            warningLongitudinalBuff += FCTB_WARNING_WIDTH_BUF;
        }

        // 开始计算
        // 0~89.5与180~90.5的值是一样的
        if (pobjPath[i].avgheadingAngle >= 90.0f)
        {
            supplementaryAngles = 180.0f - pobjPath[i].avgheadingAngle;
            if ((supplementaryAngles > 89.5f) && (supplementaryAngles < 90.5f))
            {
                supplementaryAngles = 89.5f; // 无法计算tan(90°)
            }
        }
        else
        {
            supplementaryAngles = pobjPath[i].avgheadingAngle;
            if (supplementaryAngles < 0.5f)
            {
                supplementaryAngles = 0.5f; // tan(0°)=0,除数不能为0
            }
        }
        supplementaryAngles = supplementaryAngles * DEG2RAD;                    // 这里执行完之后是弧度
        supplementaryAngles_y = VEHICLE_WIDTH_INFO / tanf(supplementaryAngles); //

        if (fabsf(supplementaryAngles_y) > (VEHICLE_WIDTH_INFO * 2.0f))
        {
            supplementaryAngles_y = (VEHICLE_WIDTH_INFO * 2.0f);
        }

        // 使用拟合的模型计算新的y值
        y_pred = w * x + b;
        // EMBARC_PRINTF("k : %f b : %f \n", w, b);
        if (pobjPath[i].avgheadingAngle >= 85.0f) // 大角度靠近
        {
            // 预测的y值和实际的y值比较,这里不能合并，y的绝对值大小不一
            if (y < FLOAT_NEG_EPS)
            {
                // EMBARC_PRINTF("--- %d x : %f y : %f 大角度 入点 pre %f\n", i, pobjPath[i].x, pobjPath[i].y, y_pred);
                error = (((y - warningLongitudinalBuff) <= y_pred) && ((1.0f + (supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
            else
            {
                // EMBARC_PRINTF("+++ %d x : %f y : %f 大角度 出点 pre %f\n", i, pobjPath[i].x, pobjPath[i].y, y_pred);
                // 出点的负侧不能卡的太多.  正点不能给到太大.
                error = (((-2.0f + -warningLongitudinalBuff) <= y_pred) && (((supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
        }
        else
        {
            // 预测的y值和实际的y值比较,这里不能合并，y的绝对值大小不一
            if (y < FLOAT_NEG_EPS)
            {
                // EMBARC_PRINTF("--- %d x : %f y : %f 小角度 入点 pre %f\n", i, pobjPath[i].x, pobjPath[i].y, y_pred);
                supplementaryAngles_y = 3 - supplementaryAngles_y + longitudinalBuff + warningLongitudinalBuff;
                error = (((y - warningLongitudinalBuff) <= y_pred) && (supplementaryAngles_y >= y_pred)) ? 1 : 0;
            }
            else
            {
                // EMBARC_PRINTF("+++ %d x : %f y : %f 小角度 出点 pre %f\n", i, pobjPath[i].x, pobjPath[i].y, y_pred);
                error = (((-0.30f + warningLongitudinalBuff) <= y_pred) && ((1.5f + (supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
        }
    }

    return error;
}
#endif

/**
 * @brief LCW求极值
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 * @return
 */
float ADAS_RCTAB_calculatedExtremum(const ALARM_OBJECT_T *pobjAlm,
                                    const OBJ_NODE_STRUCT *pobjPath,
                                    const uint8_t i,
                                    const VDY_Info_t *pVDY,
                                    const OBJ_ADAS_TYPE_ENUM type,
                                    float *normalize_x,
                                    float *normalize_y,
                                    const float x,
                                    const float y,
                                    const float w,
                                    const float b,
                                    st_Rctb_pre *rctbPre)
{
    float error = 0;
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (pVDY == NULL) || (normalize_x == NULL) ||
        (normalize_y == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return error;
    }
    float y_pred = 0.f;
    float supplementaryAngles = 0.f; // 定义互补角，不是很准确
    float supplementaryAngles_y = 0.f;
    float longitudinalBuff = RCTB_BREAK_AREA_Y_MAX;     // 另一侧的标准出点.
    float warningLongitudinalBuff = 0.f;
    float iaMinRange = RCTA_ACTIVE_MIN_IA;
    float iaMaxRange = RCTA_ACTIVE_MAX_IA;
    float iaMinRange_Buff = RCTB_ACTIVE_MIN_IA_BUF;
    float iaMaxRange_Buff = RCTB_ACTIVE_MAX_IA_BUF;
    float horizontalthr = 0.0f;     // 横向控制阈值. 在横向多少米以外的时候. 碰撞预测点稍微放开一些.

    if (type == ADAS_TYPE_FCTB)
    {
        iaMinRange = FCTB_ACTIVE_MIN_IA;
        iaMaxRange = FCTB_ACTIVE_MAX_IA;
        iaMinRange_Buff = 0.f; // fctb严格限制
        iaMaxRange_Buff = 0.f;
    }
    else if (type == ADAS_TYPE_RCTA) // 这里是RCTA
    {
        iaMinRange = RCTA_ACTIVE_MIN_IA;
        iaMaxRange = RCTA_ACTIVE_MAX_IA;
        iaMinRange_Buff = RCTB_ACTIVE_MIN_IA_BUF;
        iaMaxRange_Buff = RCTB_ACTIVE_MAX_IA_BUF;
    } 
    else
    {
    }

    // 考虑到自车转到方向盘倒车时, 碰撞区域是一个不断变化的过程. 随着方向盘转角的不断增大. 动态压缩一下IA角度.
    if (fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) > 90.0f)
    {
        iaMinRange_Buff = RCTB_ACTIVE_MAX_IA_BUF;           // 方向盘转角超过一定度数时反向操作. 收紧IA角度.
        iaMaxRange_Buff = RCTB_ACTIVE_MIN_IA_BUF;
    }

    // 只计算在IA范围内的
    if ((pobjPath[i].avgheadingAngle <= (iaMaxRange + iaMaxRange_Buff)) && (pobjPath[i].avgheadingAngle >= (iaMinRange + iaMinRange_Buff)))
    {
        if (pobjPath[i].lastAlarmType & ALARM_ACTIVE_RCTA)
        {
            warningLongitudinalBuff += RCTB_WARNING_WIDTH_BUF;
        }

        // 开始计算
        // 0~89.5与180~90.5的值是一样的
        if (pobjPath[i].avgheadingAngle >= 90.0f)
        {
            supplementaryAngles = 180.0f - pobjPath[i].avgheadingAngle;
            if ((supplementaryAngles > 89.5f) && (supplementaryAngles < 90.5f))
            {
                supplementaryAngles = 89.5f; // 无法计算tan(90°)
            }
        }
        else
        {
            supplementaryAngles = pobjPath[i].avgheadingAngle;
            if (supplementaryAngles < 0.5f)
            {
                supplementaryAngles = 0.5f; // tan(0°)=0,除数不能为0
            }
        }
        // supplementaryAngles = supplementaryAngles * DEG2RAD; // 这里执行完之后是弧度
        // supplementaryAngles_y = VEHICLE_WIDTH_INFO / tanf(supplementaryAngles); //
        // 补偿点尝试根据自车车速进行补偿.  自车车速越慢  对于碰撞点的要求越高. 一般车速越慢  目标越容易逃逸.
        supplementaryAngles_y = (-1.0f) * (1.0f - (fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps) / 1.0f));

        // 横向多少米以外放开预测点.  目标车速越快. 放开的越多.  最小到1米. 最大到2.5米.
        horizontalthr = 1.0f + (pobjPath[i].boxLength * 0.1f) + (pobjPath[i].vx * 0.1f);
        if (horizontalthr > 2.5f)
        {
            horizontalthr = 2.5f;
        }

        // 同时针对航向角也进行一定的补偿. 这个补偿只能在目标横向较远时进行. 目标越近 补偿越小. 避免目标几乎快过去时满足制动.
        if ((pobjPath[i].avgheadingAngle > 100.0f) && (pobjPath[i].x > horizontalthr))         // 大角度近距离收紧碰撞预测点.
        {
            supplementaryAngles_y += (pobjPath[i].avgheadingAngle * 0.005f);
        }
        else if ((pobjPath[i].avgheadingAngle < 80.0f) && (pobjPath[i].x < horizontalthr))      // 小角度近距离收紧碰撞预测点.
        {
            supplementaryAngles_y += (-((180.0f - pobjPath[i].avgheadingAngle) * 0.005f));
        }

        // 方向盘转角大于90度.  收紧碰撞区域.
        if (fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) > 90.0f)
        {
            supplementaryAngles_y += (-fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) * 0.005f);
        }

        if (supplementaryAngles_y > 1.0f)
        {
            supplementaryAngles_y = 1.0f;
        }
        else if (supplementaryAngles_y < -1.0f)
        {
            supplementaryAngles_y = -1.0f;
        }

        // 还存在一个情况是. 自车稍远时可能不满足碰撞预测. 但是刚到车尾满足了碰撞预测. 此时制动可能会显得稍晚.
        // 目标距离越近. 对碰撞预测越收紧? 

        // 使用拟合的模型计算新的y值
        y_pred = w * x + b;

        if (pobjPath[i].avgheadingAngle >= 85.0f) // 大角度靠近  小角度出入点设置的更严格. 这里标准应该是90 设置的宽松些.
        {
            // 预测的y值和实际的y值比较,这里不能合并，y的绝对值大小不一  y < 0是入点检测. >0是出点检测.
            if (y < FLOAT_NEG_EPS)
            {
                //EMBARC_PRINTF("大角度 入点 pre %f\n", y_pred);
                // 入点验证. 对于RCTB 入点的最大值设置的不能太小 否则大角度斜穿行可能会因为入点不满足  不制动.
                rctbPre->rctb_enter_y = y_pred;
                error = (((y - warningLongitudinalBuff) <= y_pred) && ((3.0 + (supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
            else
            {
                // 出点验证. 对于RCTB 出点一般小于最大值即可. 
                //EMBARC_PRINTF("大角度 出点 pre %f\n", y_pred);
                rctbPre->rctb_exit_y = y_pred;
                error = (((-2 - warningLongitudinalBuff) <= y_pred) && (((supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
        }
        else
        {
            // 预测的y值和实际的y值比较,这里不能合并，y的绝对值大小不一 
            if (y < FLOAT_NEG_EPS)
            {
                // 入点验证  这里是小角度的入点验证? 小角度对于入点就是要苛刻一些.  设置太小会不会有问题.  
                // 小角度的入点  需要动态调节. 目标还比较远的时候 入点可以稍微放宽一些. 目标距离比较近的时候  入点压缩一些. 避免一开始没制动. 目标快过去了制动了.
                //EMBARC_PRINTF("小角度 入点 pre %f\n", y_pred);
                rctbPre->rctb_enter_y = y_pred;
                error = (((y - warningLongitudinalBuff) <= y_pred) && (((supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
            else
            {
                // 出点验证. 
                //EMBARC_PRINTF("小角度 出点 pre %f\n", y_pred);
                rctbPre->rctb_exit_y = y_pred;
                error = (((-2 - warningLongitudinalBuff) <= y_pred) && (((supplementaryAngles_y + longitudinalBuff) + warningLongitudinalBuff) >= y_pred)) ? 1 : 0;
            }
        }
    }

    return error;
}


/**
 * @brief
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 * @return
 */
float ADAS_DOW_calculatedExtremum(const ALARM_OBJECT_T *pobjAlm,
                                  OBJ_NODE_STRUCT *pobjPath,
                                  const uint8_t i,
                                  const VDY_Info_t *pVDY,
                                  const OBJ_ADAS_TYPE_ENUM type,
                                  float *normalize_x,
                                  float *normalize_y,
                                  const float x_new,
                                  const float y_new,
                                  const float w,
                                  const float w1,
                                  const float w2,
                                  const float b)
{
    uint8_t dowHitFlag = 0;
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (pVDY == NULL) || (normalize_x == NULL) ||
        (normalize_y == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return dowHitFlag;
    }
    float x_pred = 0.f, /*y_pred = 0.f,*/ x_pred1 = 0.0f;
    float w_old = 0.f, b_old = 0.f;
    float warningWidthMin = 0.f, warningWidthMax = 0.f;
    float warningWidthbuf = 0.f, warningWidthMinBuf = 0.f, warningWidthMaxBuf = 0.f;
    float iaMinRange = -DOW_ACTIVE_MAX_IA;
    float iaMaxRange = DOW_ACTIVE_MAX_IA;
    float iaMinRange_Buff = 0.f;
    float iaMaxRange_Buff = 0.f;
    float sideLengthBuff = 0.f; 
    float dow_InwardAngledParking_Max_Buff = 0.f;    // 车辆雷达所在的部位朝向道路内侧斜停。也可以称为"内斜停"。
    // float dow_InwardAngledParking_Min_Buff = 0.f;
    // float dow_OutwardAngledParking_Max = 0.f;   // 车辆雷达所在的部位朝向道路外侧斜停。也可以称为"外斜停"。
    // float dow_OutwardAngledParking_Min = 0.f; 
    float tmp = 0.f; 
    float theta1 = 0.0f, theta2 = 0.0f, theta3 = 0.0f;
    uint8_t isdoublecheck = 0;                  // 是否进行两次碰撞预测
    uint8_t istriplecheck = 0;                  // 是否进行三次碰撞预测

    // 比较两个斜率  在一定范围内  才计算两次
    // 默认0 认为  w1是无效的
    if (fabsf(w1) > FLOAT_EPS)
    {
        float w_real = 0.0f;
        w_real = 1 / w;
        theta1 = atanf(w_real);
        theta2 = atanf(w1);
        theta3 = atanf(w2);
        theta1 = theta1 * (180.0 / M_PI);
        theta2 = theta2 * (180.0 / M_PI);
        theta3 = theta3 * (180.0 / M_PI);
        // 3度以内的目标  才使用double check
        if (fabsf(theta1 - theta2) < 4.0f)
        {
            isdoublecheck = 1;
        }
        if (fabsf(theta1 - theta3) < 4.0f)          // 拟合出来的轨迹要和护栏相差不大
        {
            istriplecheck = 1;
        }
    }

    // 只计算在IA范围内的
    if ((pobjPath[i].headingAnglerealRCWDOW <= (iaMaxRange + iaMaxRange_Buff)) &&
        (pobjPath[i].headingAnglerealRCWDOW >= (iaMinRange + iaMinRange_Buff)) &&
        (fabsf(pobjPath[i].vx) > FLOAT_EPS))
    {
        if (pobjPath[i].lastAlarmType & ALARM_ACTIVE_DOW) // 上一帧在报警，则放大区域，左右各扩大0.5m
        {
            iaMinRange_Buff -= DOW_ACTIVE_MIN_IA_BUF;
            iaMaxRange_Buff += DOW_ACTIVE_MAX_IA_BUF;
            warningWidthbuf *= 1.1f;
            warningWidthMinBuf += DOW_WARNING_WIDTH_MIN_BUF;
            warningWidthMaxBuf += (3 * DOW_WARNING_WIDTH_MAX_BUF);        // 碰撞预测的差距是比较大的, 这里的buffer是否考虑增大一些.  增大一些
        }
        if ((pobjPath[i].headingAnglerealRCWDOW <= (iaMaxRange + iaMaxRange_Buff)) &&
            (pobjPath[i].headingAnglerealRCWDOW > 5.0f))
        {
            // 横向边界的设定 很大程度上决定了是否会晚报.  设定偏大时横向报警距离会比较大, 偏小了又容易导致路边路测漏报
            // 这里是车头朝外路边斜停的场景.
            sideLengthBuff = DOW_WARNING_WIDTH_MAX / sinf((90.0f - pobjPath[i].headingAnglerealRCWDOW) * degtorad);     // 这里不是很理解? 
            warningWidthMaxBuf += sideLengthBuff; // 
            // dow_InwardAngledParking_Max_Buff 最大到2.5m(按30度最大)
            dow_InwardAngledParking_Max_Buff += VEHICLE_LENGTH_INFO * tanf(pobjPath[i].headingAnglerealRCWDOW * degtorad);  //这里是补偿到车最外边缘? 
            warningWidthMaxBuf += dow_InwardAngledParking_Max_Buff; //  

            // 最终全部补偿完毕理论上应该是横向的长度.  对于X值而言应该是斜向的长度. 所以还需要一个除以cos. 是否是这样?
            // 是否需要限制一个最大值? 
            // 对于车头朝外的场景. 这里计算的是按照顶点输出的跟踪点计算的. 实际效果中. 是否需要再额外补偿一些? 双护栏路测场景很容易由于横向碰撞不满足导致晚报
            // 补偿过多可能会导致远距离报警.  看取舍.
            warningWidthMaxBuf = (warningWidthMaxBuf / cosf((pobjPath[i].headingAnglerealRCWDOW) * degtorad));

            // 针对识别到的产品走查时的护栏路段的目标. 碰撞预测更加宽松一些
            // 需要确保这里是真实识别到的护栏目标.  避免远距离误报.
            if (pobjPath[i].analysisStats.TrkObjdowguardCrossCnt >= DOW_GUESSRAIL_OBJ_CNT)
            {
                warningWidthMaxBuf *= 1.2f;
            }

            warningWidthMin += DOW_WARNING_WIDTH_MIN + warningWidthMinBuf; //  +pobjPath[i].avgX;
            warningWidthMax += warningWidthMaxBuf; //  - pobjPath[i].avgX;
        }
        else if ((pobjPath[i].headingAnglerealRCWDOW <= -5.0f) &&
                 (pobjPath[i].headingAnglerealRCWDOW > (iaMinRange + iaMinRange_Buff)))
        {
            // 车尾朝外场景可能存在的一个是  跟踪点波动对航向角预测影响比较大. 
            // 车尾朝外的场景护栏路段很容易晚报. 目标在远处时, 处于FOV边缘 航向角的抖动一般是相对比较大的.
            // 如果不是很care横向稍远距离误报的话   这里是否可以适当放开一些.   如果care横向远距离误报的话  这里就不能放开.
            sideLengthBuff = DOW_WARNING_WIDTH_MAX / cosf(fabsf(pobjPath[i].headingAnglerealRCWDOW) * degtorad);
            warningWidthMaxBuf += sideLengthBuff; // 
            warningWidthMin += (DOW_WARNING_WIDTH_MIN + warningWidthMinBuf); //  +pobjPath[i].avgX;
            warningWidthMax += warningWidthMaxBuf; //  - pobjPath[i].avgX;

            // 车尾朝外的场景   是否也要稍微放开一些  也只能针对产品走查的护栏场景
            // 护栏侧的目标  左右都放开一些
            if (pobjPath[i].analysisStats.TrkObjdowguardCrossCnt >= DOW_GUESSRAIL_OBJ_CNT)
            {
                warningWidthMin += DOW_WARNING_WIDTH_MIN_BUF;
                warningWidthMax += DOW_WARNING_WIDTH_MAX_BUF; //
            }
        }
#if 0        
        // 这里的碰撞预测,  使用当前目标尝试一次碰撞预测,  如果有历史heading  再使用历史heading尝试计算一次
        x_pred = ((y_new - tmp)-b) / w;
        // 预测的y值和实际的y值比较, 
        dowHitFlag = ((warningWidthMin <= x_pred) && (warningWidthMax >= x_pred)) ? 1 : 0;
#else
        /*
         * 从对调后的参数求原始参数：
         *
         * 已知对调后的参数：w_new 和 b_new
         * 求原始参数：w_old 和 b_old
         *
         * 根据关系：
         * w_new = 1/w_old
         * b_new = -b_old/w_old
         *
         * 解得：
         * w_old = 1/w_new
         * b_old = -b_new * w_old = -b_new/w_new
         *
         * 特殊情况：
         * 1. 当对调后的斜率w_new接近0时（接近水平线），原始斜率w_old将非常大，可能导致数值不稳定
         * 2. 当对调后的斜率w_new非常大时（接近垂直线），原始斜率w_old将接近0
         * 3. 当对调后的斜率为1或-1时，原始斜率也为相同值，但截距会变化
         */
        w_old = 1 / w;
        b_old = -b / w;
        // 这里的碰撞预测,  使用当前目标尝试一次碰撞预测,  如果有历史heading  再使用历史heading尝试计算一次
        x_pred = ((y_new - tmp) - b_old) / w_old;
        // 预测的y值和实际的y值比较,
        dowHitFlag = ((warningWidthMin <= x_pred) && (warningWidthMax >= x_pred)) ? 1 : 0;
#endif
        // 先关注漏报 目标自身拟合的航向角不报警时. 再用推测航向角计算
        if ((1 == isdoublecheck) && ((0 == dowHitFlag)))
        {
            x_pred1 = ((y_new - tmp)-b_old) / w1;
            dowHitFlag = ((warningWidthMin <= x_pred1) && (warningWidthMax >= x_pred1)) ? 1 : 0; 
            if ((1 == istriplecheck) && (0 == dowHitFlag))
            {
                x_pred1 = ((y_new - tmp)-b_old) / w2;
                dowHitFlag = ((warningWidthMin <= x_pred1) && (warningWidthMax >= x_pred1)) ? 1 : 0; 
            }
        }
    }

    return dowHitFlag;
}


