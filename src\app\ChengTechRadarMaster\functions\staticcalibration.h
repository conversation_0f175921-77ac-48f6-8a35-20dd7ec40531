﻿#ifndef STATICCALIBRATION_H
#define STATICCALIBRATION_H

#include "functions_global.h"

#include <QDialog>
#include <QTimer>
#include <QFile>

namespace Ui {
class StaticCalibration;
}

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Functions {

class StaticCalibrationWorker;

class FUNCTIONS_EXPORT StaticCalibration : public QDialog
{
    Q_OBJECT

public:
    explicit StaticCalibration(Devices::Can::DeviceManager *deviceManager, QWidget *parent = nullptr);
    ~StaticCalibration();

private slots:
    void message(int index, const QString &msg);
    void calibrationFinished(int index, bool ok = false);
    void calibrationStarted(int index);
    void sendOrRecvCanFrame( int index, bool bSend, quint64 id, const QString& data );


    void on_pushButtonStopCalibration_clicked();

    void on_pushButtonStartCalibration_clicked();

    void on_pushButtonCalibrationResult_clicked();

    void on_pushButtonFinishedCalibration_clicked();

    void on_pushButtonRepeatCalibration_clicked();

    void on_comboBoxProtocolType_currentIndexChanged(int index);

private:
    void openCanLogFile();
    void closeCanLogFile();

private:
    Ui::StaticCalibration *ui;

    StaticCalibrationWorker *mStaticCalibrationWorker{0};
    QTimer mTimerResult[4];
    quint8 mResult[4];
    QFile mCanLogFile[4];
};

} // namespace Functions

#endif // STATICCALIBRATION_H
