﻿#include "dialogcancmds.h"
#include "ui_dialogcancmds.h"
#include <QDebug>
#include <qmessagebox.h>
#include <QtConcurrent>
#include <QFileDialog>
#include <QThread>

#include "devices/devicemanager.h"

//CAN TX TO RADAR MSG ID
#define FACTORY_CAN_SET_WORK_REQ 0x100
#define FACTORY_DBF_WRITE_REQ 0x154

//CAN RX FROM RADAR MSG ID
#define FACTORY_CAN_SET_WORK_ANS 0x200
#define FACTORY_CAN_OEM_VERSION_REQ 0x103
#define FACTORY_CAN_OEM_VERSION_ANS 0x203
#define FACTORY_BURN_CFG_REQ 0x116
#define FACTORY_BURN_CFG_ANS 0x216

#define FLASH_LOAD_CANID_REQ 0x154
#define FLASH_LOAD_CANID_RESP  0x254


#define CFGVAR_FILENAME_ALPS    "./cfgvar_alps.txt"
#define CFGVAR_FILENAME_ALPSPRO "./cfgvar_alpsPro.txt"

//#define SLEEP_TIME_MS 500
#define SLEEP_TIME_MS 500

#define DBF_FLASH_MAGIC_NUMBER 0x5A5A5A5A

namespace Functions {

DialogCanCmds::DialogCanCmds(Devices::Can::DeviceManager *deviceManager, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::DialogCanCmds),
    mDeviceManager(deviceManager)
{
    ui->setupUi(this);

    connect(deviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameRecieved , this, [=](const Devices::Can::CanFrame &frame){
        this->slot_Receive_CanData(frame.channelIndex(), frame.id(), frame.data());//读取格式
    });

//    connect(this, &DialogCanCmds::sigSendCmds, this, &DialogCanCmds::slotSendTextCmd);
    connect(this, &DialogCanCmds::sigSendCmds, this, [=](int rd_id ,const QString &cmds){
        QFuture<void> res = QtConcurrent::run(this, &DialogCanCmds::slotSendTextCmd, rd_id, cmds);
    });

    connect(ui->btnSendCmd, &QPushButton::clicked, this, &DialogCanCmds::slotSendClick);
    connect(ui->btnConfig, &QPushButton::clicked, this, &DialogCanCmds::slotSetConfig);
    connect(ui->cbProfile, SIGNAL(currentIndexChanged(int)), this, SLOT(slotSelectProfile(int)));
    connect(ui->btnReadCfg, SIGNAL(clicked()), this, SLOT(slotReadConfig()));
    connect(ui->btnWriteFlash, SIGNAL(clicked()), this, SLOT(slotWriteFlash()));
    connect(ui->btnClearFlash, SIGNAL(clicked()), this, SLOT(slotClearFlash()));
    connect(ui->btnClearCfgText, SIGNAL(clicked()), this, SLOT(slotClearCfgText()));

    m_cfg1.insert("cfar_pk_threshold", ui->leCfarPkThrd);
    m_cfg1.insert("cfar_recwin_msk", ui->leCfarRecwinMask);
    m_cfg1.insert("cfar_region_algo_type", ui->leCfarAlgoType);
    m_cfg1.insert("cfar_ca_alpha", ui->leCfarCaAlpha);
    m_cfg1.insert("cfar_ca_n", ui->leCfarCaN);
    m_cfg1.insert("doa_mode", ui->leDoaMode);
    m_cfg1.insert("doa_num_groups", ui->leDoaNumGroups);
    m_cfg1.insert("doa_fft_mux", ui->leDoaFftMux);
    m_cfg1.insert("combined_doa_fft_mux", ui->leDoaCbFftMux);
    m_cfg1.insert("doa_method", ui->leDoaMethod);
    m_cfg1.insert("doa_samp_space", ui->leDoaSmpSpace);
    m_cfg1.insert("doa_max_obj_per_bin", ui->leDoaMaxObj);
    m_cfg1.insert("bfm_az_left", ui->leBfmAzLeft);
    m_cfg1.insert("bfm_az_right", ui->leBfmAzRight);
    m_cfg1.insert("bfm_ev_up", ui->leBfmEvUp);
    m_cfg1.insert("bfm_ev_down", ui->leBfmEvDown);
    m_cfg1.insert("dml_2dsch_start", ui->leDml2dschStart);
    m_cfg1.insert("dml_2dsch_step", ui->leDml2dschStep);
    m_cfg1.insert("dml_2dsch_end", ui->leDml2dschEnd);
    m_cfg1.insert("dml_extra_1d_en", ui->leDmlExtra1d);
    m_cfg1.insert("dml_p1p2_en", ui->leDmlP1P2);
    m_cfg1.insert("dml_respwr_coef", ui->leDmlRespCoef);

    ui->tab_0_layout->setColumnStretch( 0, 3 );
    ui->tab_0_layout->setColumnStretch( 1, 5 );
    ui->tab_0_layout->setColumnStretch( 2, 2 );
    ui->tab_0_layout->setColumnMinimumWidth( 1, 200 );
    ui->tab_1_layout->setColumnStretch( 0, 3 );
    ui->tab_1_layout->setColumnStretch( 1, 5 );
    ui->tab_1_layout->setColumnStretch( 2, 2 );
    ui->tab_1_layout->setColumnMinimumWidth( 1, 200 );
    ui->tab_2_layout->setColumnStretch( 0, 3 );
    ui->tab_2_layout->setColumnStretch( 1, 5 );
    ui->tab_2_layout->setColumnStretch( 2, 2 );
    ui->tab_2_layout->setColumnMinimumWidth( 1, 200 );
    ui->tab_3_layout->setColumnStretch( 0, 3 );
    ui->tab_3_layout->setColumnStretch( 1, 5 );
    ui->tab_3_layout->setColumnStretch( 2, 2 );
    ui->tab_3_layout->setColumnMinimumWidth( 1, 200 );


    curPorfile = 0;
    //createCfgBotton(-1);
    createCfgBotton( -1, CFGVAR_FILENAME_ALPS );
}

DialogCanCmds::~DialogCanCmds()
{
    delete ui;
}

void DialogCanCmds::slot_Receive_CanData(int CanCH, int id, QByteArray datAry)
{
    if ((id & 0xFF0) == (ui->comboBoxProtocol->currentIndex() == 0 ? 0x110 : 0x750))
    {
        qDebug() << __FUNCTION__ << __LINE__ << QString::number(id, 16) << datAry.toHex(' ');
        slotCmdAck(id, datAry);
        return;
    }else if( id == FACTORY_CAN_SET_WORK_ANS
              || id == FACTORY_CAN_OEM_VERSION_ANS
              || id == FACTORY_BURN_CFG_ANS
              || id == FLASH_LOAD_CANID_RESP ){
//        qDebug() << __FUNCTION__ << __LINE__ << QString::number( id, 16 ) << datAry.toHex(' ');
        slotCanAck( id, datAry );
    }
}

void DialogCanCmds::slotSendClick()
{
    QString cmds = ui->textCmds->toPlainText();
    QString totalCmd;

    if (cmds.length() == 0)
    {
        return;
    }

    if (*cmds.cbegin() != '\n')
    {
        cmds.append('\n');
    }

    if (ui->cbSelProf0->isChecked())
    {
        totalCmd += "profile 0\n";
        totalCmd += cmds;
    }

    if (ui->cbSelProf1->isChecked())
    {
        totalCmd += "profile 1\n";
        totalCmd += cmds;
    }

    if (ui->cbSelProf2->isChecked())
    {
        totalCmd += "profile 2\n";
        totalCmd += cmds;
    }

    if (ui->cbSelProf3->isChecked())
    {
        totalCmd += "profile 3\n";
        totalCmd += cmds;
    }

    if (!totalCmd.isEmpty())
    {
        int id = ui->lineEdit_radar_addr->text().toInt();
        emit sigSendCmds(id,totalCmd);
    }
}

void DialogCanCmds::showAck(const QString &ack)
{
    QString val;
    int idx = ack.indexOf(' ');
    int idxall = idx;
    if (idx < 0)
    {
        return;
    }
    QString key = ack.left(idx);

    //根据当前的profile，将数据写入命令列表
    QString editStr = key+"Edit"+QString::number(curPorfile);
    //qDebug()<<"editStr="<<editStr;
    editStr = editStr.trimmed();

    while (ack[idxall] == ' ' || ack[idxall] == '=')
    {
        idxall++;
    }
    val = ack.mid(idxall).replace(QRegExp("\\[|\\]|,|\\(|\\)"), "");

    QLineEdit* editObj =  ui->tab_profile->findChild<QLineEdit *>(editStr);

    if(editObj != NULL)
    {
        editObj->setText(val);
    }

}

void DialogCanCmds::createCfgBotton(int profile, const QString& cfgFileName )
{
    for( int i=0; i<4; i++ ){
        for( int j=0; j<mWidgetList[i].size(); j++ ){
            mWidgetList[i][j]->setParent( NULL );
            mWidgetList[i][j]->deleteLater();
        }
        mWidgetList[i].clear();
    }

    //新增控件
    int i;
    int proIdx = 0;
    if(profile < 0)
    {
        cmdlist.clear();
        //生成4个
        //QFile cfgfile("./cfgvar.txt");
        QFile cfgfile( cfgFileName );
        cfgfile.open(QIODevice::ReadOnly);
        while(true)
        {
        char buf[1024];
        int line = cfgfile.readLine(buf , sizeof(buf));
        if(line <= 0)
        {
            break;
        }
        QString cmdline = QString(buf);
        cmdline=cmdline.trimmed();

        if(cmdline.startsWith(".") == false)
        {
            continue;
        }
        cmdline.remove(0,1);
        cmdlist.append(cmdline);
        }
        cfgfile.close();
        i = 0;

        //qDebug()<<"cmd len = "<<cmdlist.length();
        foreach (QString cmd, cmdlist) {
            int idx = cmd.indexOf("=");
            QString name = cmd.left(idx).trimmed();
            qDebug()<<"name="<<name;
            QString labstr;
            QString editStr;
            QString btStr;
            QLabel *cmdlab;
            QLineEdit *cmdEdit;
            QPushButton *cmdBt;

            for( int j=0; j<4; j++ ){
                labstr = name + "Lab" + QString::number( j );
                editStr = name + "Edit" + QString::number( j );
                btStr = name + "Bt" + QString::number( j );
                cmdlab = new QLabel(labstr);
                cmdlab->setText(name);
                cmdlab->setObjectName(labstr);
                cmdEdit = new QLineEdit(name);
                cmdEdit->setObjectName(editStr);
                cmdBt = new QPushButton("Send");
                cmdBt->setText("Send");
                cmdBt->setObjectName(btStr);
                connect(cmdBt , &QPushButton::clicked , this , &DialogCanCmds::slotSendCfgCmd);

                mWidgetList[j].append( cmdlab );
                mWidgetList[j].append( cmdEdit );
                mWidgetList[j].append( cmdBt );

                switch( j ){
                case 0:
                    ui->tab_0_layout->addWidget(cmdlab , i , 0 , 1 ,1);
                    ui->tab_0_layout->addWidget(cmdEdit , i , 1 , 1 ,1);
                    ui->tab_0_layout->addWidget(cmdBt , i , 2 , 1 ,1);
                    break;
                case 1:
                    ui->tab_1_layout->addWidget(cmdlab , i , 0 , 1 ,1);
                    ui->tab_1_layout->addWidget(cmdEdit , i , 1 , 1 ,1);
                    ui->tab_1_layout->addWidget(cmdBt , i , 2 , 1 ,1);
                    break;
                case 2:
                    ui->tab_2_layout->addWidget(cmdlab , i , 0 , 1 ,1);
                    ui->tab_2_layout->addWidget(cmdEdit , i , 1 , 1 ,1);
                    ui->tab_2_layout->addWidget(cmdBt , i , 2 , 1 ,1);
                    break;
                case 3:
                    ui->tab_3_layout->addWidget(cmdlab , i , 0 , 1 ,1);
                    ui->tab_3_layout->addWidget(cmdEdit , i , 1 , 1 ,1);
                    ui->tab_3_layout->addWidget(cmdBt , i , 2 , 1 ,1);
                    break;
                }
            }



//            labstr = name + "Lab0";
//            editStr = name + "Edit0";
//            btStr = name + "Bt0";
//            cmdlab = new QLabel(labstr);
//            cmdlab->setText(name);
//            cmdlab->setObjectName(labstr);
//            cmdEdit = new QLineEdit(name);
//            cmdEdit->setObjectName(editStr);
//            cmdBt = new QPushButton("Send");
//            cmdBt->setText("Send");
//            cmdBt->setObjectName(btStr);

//            ui->tab_0_layout->addWidget(cmdlab , i , 0 , 1 ,1);
//            ui->tab_0_layout->addWidget(cmdEdit , i , 1 , 1 ,1);
//            ui->tab_0_layout->addWidget(cmdBt , i , 2 , 1 ,1);
//            connect(cmdBt , &QPushButton::clicked , this , &DialogCanCmds::slotSendCfgCmd);



//            labstr = name + "Lab1";
//            editStr = name + "Edit1";
//            btStr = name + "Bt1";
//            cmdlab = new QLabel(labstr);
//            cmdlab->setText(name);
//            cmdlab->setObjectName(labstr);
//            cmdEdit = new QLineEdit(name);
//            cmdEdit->setObjectName(editStr);
//            cmdBt = new QPushButton("Send");
//            cmdBt->setText("Send");
//            cmdBt->setObjectName(btStr);

//            ui->tab_1_layout->addWidget(cmdlab , i , 0 , 1 ,1);
//            ui->tab_1_layout->addWidget(cmdEdit , i , 1 , 1 ,1);
//            ui->tab_1_layout->addWidget(cmdBt , i , 2 , 1 ,1);
//            connect(cmdBt , &QPushButton::clicked , this , &DialogCanCmds::slotSendCfgCmd);


//            labstr = name + "Lab2";
//            editStr = name + "Edit2";
//            btStr = name + "Bt2";
//            cmdlab = new QLabel(labstr);
//            cmdlab->setText(name);
//            cmdlab->setObjectName(labstr);
//            cmdEdit = new QLineEdit(name);
//            cmdEdit->setObjectName(editStr);
//            cmdBt = new QPushButton("Send");
//            cmdBt->setText("Send");
//            cmdBt->setObjectName(btStr);

//            ui->tab_2_layout->addWidget(cmdlab , i , 0 , 1 ,1);
//            ui->tab_2_layout->addWidget(cmdEdit , i , 1 , 1 ,1);
//            ui->tab_2_layout->addWidget(cmdBt , i , 2 , 1 ,1);

//            connect(cmdBt , &QPushButton::clicked , this , &DialogCanCmds::slotSendCfgCmd);


//            labstr = name + "Lab3";
//            editStr = name + "Edit3";
//            btStr = name + "Bt3";
//            cmdlab = new QLabel(labstr);
//            cmdlab->setText(name);
//            cmdlab->setObjectName(labstr);
//            cmdEdit = new QLineEdit(name);
//            cmdEdit->setObjectName(editStr);
//            cmdBt = new QPushButton("Send");
//            cmdBt->setText("Send");
//            cmdBt->setObjectName(btStr);

//            ui->tab_3_layout->addWidget(cmdlab , i , 0 , 1 ,1);
//            ui->tab_3_layout->addWidget(cmdEdit , i , 1 , 1 ,1);
//            ui->tab_3_layout->addWidget(cmdBt , i , 2 , 1 ,1);
//            connect(cmdBt , &QPushButton::clicked , this , &DialogCanCmds::slotSendCfgCmd);

            i++;
        }

    }
    else
    {
        //更新指定一个
    }
    //test
    QLineEdit* editObj =  ui->tab_profile->findChild<QLineEdit *>("fmcw_startfreqEdit0");
    qDebug()<<"edit:"<<editObj;

}

void DialogCanCmds::slotCmdAck(int id , const QByteArray msg)
{
    int rdId = ui->lineEdit_radar_addr->text().toInt();
//    qDebug()<<"-------------------------------------rdid="<<rdId;
//    if(id != (0x130|(rdId&0x000f)))
//    {
//        return;
//    }
    if (m_recvAckStart == false)
    {
        if ((uchar)msg[0] != 0x88)
        {
            return;
        }

        m_recvAckStart = true;
    }
    else if ((uchar)msg[0] == 0xFF)
    {
        int idx = m_ackBuf.indexOf('\n');
        QString ack = QString(m_ackBuf.left(idx));

        ui->textCmdAck->append(ack);

        showAck(ack);
        if( mSaveToCsv ){
            saveToCsv( m_ackBuf.left(idx) );
        }

        m_ackBuf.clear();
        m_recvAckStart = false;
    }
    else
    {
        m_ackBuf.append(msg.right(msg.length() - 1));
    }
}

bool DialogCanCmds::sendData(int channelIndex, int id, QByteArray data)
{
    //qDebug() << __FUNCTION__ << __LINE__ << QString::number( id, 16 ) << data.toHex();
    return mDeviceManager->deviceWorker()->sendFrame(channelIndex, id, data);
}

void DialogCanCmds::slotSendTextCmd(int rd_id, const QString &cmds)
{
    int channelIndex = ui->comboBoxChannelIndex->currentIndex();
    int id = (ui->comboBoxProtocol->currentIndex() == 0 ? 0x110 : 0x750) | (rd_id & 0x0F);
    qDebug() << __FUNCTION__ << __LINE__ << QString::number(id, 16) << cmds;
//    int id = 0x114;
    QByteArray data(8, 0);
    auto cmdlist = cmds.split('\n', QString::SkipEmptyParts);
    for (auto text : cmdlist)
    {
        auto chars = text.toLatin1();
        int cnt = (chars.length() + 6) / 7;
        int idx = 0;

        data[0] = 0x88;
        data[1] = chars.length() >> 8;
        data[2] = chars.length() & 0xFF;
//        emit sigSendData(id, data, channelIndex);
        if (!sendData(channelIndex, id ,data))
        {
            return;
        }
        QThread::msleep(1);

        for (int i = 0; i < cnt; i++)
        {
            data[0] = i & 0xF;

            for (int j = 1; j < 8 && idx < chars.length(); j++)
            {
                data[j] = chars[idx++];
            }

//            emit sigSendData(id, data, channelIndex);
            if (!sendData(channelIndex, id ,data))
            {
                return;
            }
            QThread::msleep(1);
        }

        data[0] = 0xFF;

//        emit sigSendData(id, data, channelIndex);
        sendData(channelIndex, id ,data);

        QThread::msleep(10);
//        qDebug()<<"CanInfo.CH = "<<CanInfo.CH<<id<<data.toHex();
    }
}

void DialogCanCmds::slotSetConfig()
{
    QString cmds;
    QString totalCmd;

    for (auto iter = m_cfg1.begin(); iter != m_cfg1.end(); iter++)
    {
        if (!iter.value()->text().isEmpty())
        {
            cmds += QString("sensor_cfg %1 %2\n").arg(iter.key(), iter.value()->text());
        }
    }

    if (ui->cbSelProf0->isChecked())
    {
        totalCmd += "profile 0\n";
        totalCmd += cmds;
        curPorfile = 0;
    }

    if (ui->cbSelProf1->isChecked())
    {
        totalCmd += "profile 1\n";
        totalCmd += cmds;
        curPorfile = 1;
    }

    if (ui->cbSelProf2->isChecked())
    {
        totalCmd += "profile 2\n";
        totalCmd += cmds;
        curPorfile = 2;
    }

    if (ui->cbSelProf3->isChecked())
    {
        totalCmd += "profile 3\n";
        totalCmd += cmds;
        curPorfile = 3;
    }

    if (!totalCmd.isEmpty())
    {
        int id = ui->lineEdit_radar_addr->text().toInt();
        emit sigSendCmds(id,totalCmd);
    }
}

void DialogCanCmds::slotSelectProfile(int index)
{
    QString cmds("profile ");

    cmds.append(ui->cbProfile->currentText());
    curPorfile = index;
    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds(id,cmds);
}

void DialogCanCmds::slotClearCfgText(void)
{
    for (auto iter = m_cfg1.begin(); iter != m_cfg1.end(); iter++)
    {
        iter.value()->clear();
    }
}

void DialogCanCmds::slotWriteFlash(void)
{
    auto ret = QMessageBox::question(nullptr, "confirm", "Are you sure to write config flash?", QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes)
    {
        int id = ui->lineEdit_radar_addr->text().toInt();
        emit sigSendCmds(id,"cfg_to_flash");
    }
}

void DialogCanCmds::slotClearFlash(void)
{
    auto ret = QMessageBox::question(nullptr, "confirm", "Are you sure to clear config flash?", QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes)
    {
        int id = ui->lineEdit_radar_addr->text().toInt();
        emit sigSendCmds(id,"cfg_to_err");
    }
}

void DialogCanCmds::slotReadConfig(void)
{
    int type = ui->comboBoxType->currentIndex();

    QString cmdsFileCfg;
    cmdlist.clear();
    //生成4个
    //QFile cfgfile("./cfgvar.txt");
    QFile cfgfile;
    if( type == 0 ){ //alps
        cfgfile.setFileName( CFGVAR_FILENAME_ALPS );
    }else{ //alps pro
        cfgfile.setFileName( CFGVAR_FILENAME_ALPSPRO );
    }
    cfgfile.open(QIODevice::ReadOnly);
    while(true)
    {
    char buf[1024];
    int line = cfgfile.readLine(buf , sizeof(buf));
    if(line <= 0)
    {
        break;
    }
    QString cmdline = QString(buf);
    cmdline=cmdline.trimmed();

    if(cmdline.startsWith(".") == false)
    {
        continue;
    }
    cmdline.remove(0,1);
    cmdlist.append(cmdline);
    }
    cfgfile.close();
    foreach (QString cmd, cmdlist) {
        int idx = cmd.indexOf("=");
        QString name = cmd.left(idx).trimmed();
        cmdsFileCfg.append("sensor_cfg ").append(name).append("\n");
    }

    QString cmds("profile ");

    cmds.append(ui->cbProfile->currentText());
    cmds.append("\n");
    curPorfile = ui->cbProfile->currentText().toInt();

    if(cmdsFileCfg.isEmpty() == false)
    {
        cmds.append(cmdsFileCfg);
    }
    else
    {
        if( type == 0 ){ //alps
            cmds.append(
                "sensor_cfg fmcw_startfreq\n"
                "sensor_cfg fmcw_bandwidth\n"
                "sensor_cfg fmcw_chirp_rampup\n"
                "sensor_cfg fmcw_chirp_down\n"
                "sensor_cfg fmcw_chirp_period\n"
                "sensor_cfg tx_groups\n"
                "sensor_cfg nchirp\n"
                "sensor_cfg adc_freq\n"
                "sensor_cfg dec_factor\n"
                "sensor_cfg adc_sample_start\n"
                "sensor_cfg adc_sample_end\n"
                "sensor_cfg rng_win\n"
                "sensor_cfg vel_win\n"
                "sensor_cfg rng_win_params\n"
                "sensor_cfg vel_win_params\n"
                "sensor_cfg rng_nfft\n"
                "sensor_cfg vel_nfft\n"
                "sensor_cfg rng_fft_scalar\n"
                "sensor_cfg vel_fft_scalar\n"
                "sensor_cfg fft_nve_bypass\n"
                "sensor_cfg fft_nve_shift\n"
                "sensor_cfg fft_nve_ch_mask\n"
                "sensor_cfg fft_nve_default_value\n"
                "sensor_cfg cfar_pk_en\n"
                "sensor_cfg cfar_pk_win_size1\n"
                "sensor_cfg cfar_pk_win_size2\n"
                "sensor_cfg cfar_pk_threshold\n"
                "sensor_cfg cfar_recwin_decimate\n"
                "sensor_cfg cfar_recwin_msk\n"
                "sensor_cfg cfar_region_algo_type\n"
                "sensor_cfg cfar_os_rnk_ratio\n"
                "sensor_cfg cfar_os_rnk_sel\n"
                "sensor_cfg cfar_os_tdec\n"
                "sensor_cfg cfar_combine_thetas\n"
                "sensor_cfg cfar_crswin_rng_size\n"
                "sensor_cfg cfar_crswin_rng_skip\n"
                "sensor_cfg cfar_crswin_vel_size\n"
                "sensor_cfg cfar_crswin_vel_skip\n"
                "sensor_cfg cfar_combine_phis\n"
                "sensor_cfg cfar_combine_dirs\n"
                "sensor_cfg cfar_mimo_win\n"
                "sensor_cfg cfar_mimo_win_params\n"
                "sensor_cfg cfar_noise_type\n"
                "sensor_cfg cfar_nr_alpha\n"
                "sensor_cfg cfar_nr_beta1\n"
                "sensor_cfg cfar_nr_beta2\n"
                "sensor_cfg cfar_nr_rnk_ratio\n"
                "sensor_cfg cfar_nr_rnk_sel\n"
                "sensor_cfg cfar_nr_scheme_sel\n"
                "sensor_cfg cfar_nr_tdec\n"
                "sensor_cfg cfar_os_alpha\n"
                "sensor_cfg cfar_region_sep_rng\n"
                "sensor_cfg cfar_region_sep_vel\n"
                "sensor_cfg cfar_sliding_win\n"
                "sensor_cfg cfar_sogo_alpha\n"
                "sensor_cfg cfar_sogo_i\n"
                "sensor_cfg cfar_sogo_mask\n"
                "sensor_cfg cascade_mode\n"
                "sensor_cfg cfar_ca_alpha\n"
                "sensor_cfg cfar_ca_n\n"
                "sensor_cfg doa_mode\n"
                "sensor_cfg doa_num_groups\n"
                "sensor_cfg doa_fft_mux\n"
                "sensor_cfg combined_doa_fft_mux\n"
                "sensor_cfg doa_method\n"
                "sensor_cfg doa_npoint\n"
                "sensor_cfg doa_samp_space\n"
                "sensor_cfg doa_max_obj_per_bin\n"
                "sensor_cfg bfm_peak_scalar\n"
                "sensor_cfg bfm_noise_level_scalar\n"
                "sensor_cfg bfm_snr_thres\n"
                "sensor_cfg bfm_az_left\n"
                "sensor_cfg bfm_az_right\n"
                "sensor_cfg bfm_ev_up\n"
                "sensor_cfg bfm_ev_down\n"
                "sensor_cfg doa_win\n"
                "sensor_cfg doa_win_params\n"
                "sensor_cfg bfm_raw_search_step\n"
                "sensor_cfg bfm_fine_search_range\n"
                "sensor_cfg bfm_iter_search\n"
                "sensor_cfg bfm_mode\n"
                "sensor_cfg bfm_group_idx\n"
                "sensor_cfg ant_info_from_flash\n"
                "sensor_cfg ant_info_flash_addr\n"
                "sensor_cfg ant_pos\n"
                "sensor_cfg ant_comps\n"
                "sensor_cfg bpm_mode\n"
                "sensor_cfg phase_scramble_on\n"
                "sensor_cfg phase_scramble_init_state\n"
                "sensor_cfg phase_scramble_tap\n"
                "sensor_cfg phase_scramble_comp\n"
                "sensor_cfg freq_hopping_on\n"
                "sensor_cfg freq_hopping_init_state\n"
                "sensor_cfg freq_hopping_tap\n"
                "sensor_cfg freq_hopping_deltaf\n"
                "sensor_cfg chirp_shifting_on\n"
                "sensor_cfg chirp_shifting_init_state\n"
                "sensor_cfg chirp_shifting_init_tap\n"
                "sensor_cfg chirp_shifting_delay\n"
                "sensor_cfg fsm_on\n"
                "sensor_cfg agc_mode\n"
                "sensor_cfg agc_code\n"
                "sensor_cfg agc_tia_thres\n"
                "sensor_cfg agc_vga1_thres\n"
                "sensor_cfg agc_vga2_thres\n"
                "sensor_cfg agc_align_en\n"
                "sensor_cfg adc_comp_en\n"
                "sensor_cfg rf_tia_gain\n"
                "sensor_cfg rf_vga1_gain\n"
                "sensor_cfg rf_vga2_gain\n"
                "sensor_cfg rf_hpf1\n"
                "sensor_cfg rf_hpf2\n"
                "sensor_cfg de_vel_amb\n"
                "sensor_cfg track_fps\n"
                "sensor_cfg track_fov_az_left\n"
                "sensor_cfg track_fov_az_right\n"
                "sensor_cfg track_fov_ev_down\n"
                "sensor_cfg track_fov_ev_up\n"
                "sensor_cfg track_near_field_thres\n"
                "sensor_cfg track_capture_delay\n"
                "sensor_cfg track_drop_delay\n"
                "sensor_cfg track_vel_pos_ind_portion\n"
                "sensor_cfg tx_phase_value\n"
                "sensor_cfg spk_en\n"
                "sensor_cfg spk_buf_len\n"
                "sensor_cfg spk_set_zero\n"
                "sensor_cfg spk_ovr_num\n"
                "sensor_cfg spk_thres_dbl\n"
                "sensor_cfg spk_min_max_sel\n"
                "sensor_cfg zero_doppler_cancel\n"
                "sensor_cfg anti_velamb_en\n"
                "sensor_cfg anti_velamb_delay\n"
                "sensor_cfg anti_velamb_qmin\n"
                "sensor_cfg dml_2dsch_start\n"
                "sensor_cfg dml_2dsch_step\n"
                "sensor_cfg dml_2dsch_end\n"
                "sensor_cfg dml_extra_1d_en\n"
                "sensor_cfg dml_p1p2_en\n"
                "sensor_cfg dml_respwr_coef\n"
                "sensor_cfg acc_rng_hw\n"
                "sensor_cfg acc_vel_hw\n"
                "sensor_cfg acc_angle_hw\n"
            );
        }else{ //alps pro
            cmds.append(
                        "sensor_cfg dec_factor\n"
                        "sensor_cfg adc_sample_start\n"
                        "sensor_cfg adc_sample_end\n"
                        "sensor_cfg agc_adc_target_level\n"
                        "sensor_cfg agc_dfe_comp_level\n"
                        "sensor_cfg agc_code\n"
                        "sensor_cfg agc_align_en\n"
                        "sensor_cfg agc_adc_comp_en\n"
                        "sensor_cfg agc_adc_comp_align_en\n"
                        "sensor_cfg agc_tia_thres\n"
                        "sensor_cfg agc_vga1_thres\n"
                        "sensor_cfg agc_vga2_thres\n"
                        "sensor_cfg cqm_en\n"
                        "sensor_cfg cqm_win_size\n"
                        "sensor_cfg cqm_thres_scalar\n"
                        "sensor_cfg cqm_thres_numsample\n"
                        "sensor_cfg cqm_num_section\n"
                        "sensor_cfg cqm_hist_ratio\n"
                        "sensor_cfg rng_win\n"
                        "sensor_cfg rng_win_params\n"
                        "sensor_cfg rng_nfft\n"
                        "sensor_cfg rng_fft_scalar\n"
                        "sensor_cfg fft_general_cancel\n"
                        "sensor_cfg fft_general_cancel_gate_num\n"
                        "sensor_cfg fft_cnc_template\n"
                        "sensor_cfg fft_cnc_scalar\n"
                        "sensor_cfg vel_win\n"
                        "sensor_cfg vel_win_params\n"
                        "sensor_cfg vel_nfft\n"
                        "sensor_cfg vel_fft_scalar\n"
                        "sensor_cfg zero_doppler_cancel\n"
                        "sensor_cfg vel_sva\n"
                        "sensor_cfg rng_sva\n"
                        "sensor_cfg compress\n"
                        "sensor_cfg compress_thres\n"
                        "sensor_cfg hist_en\n"
                        "sensor_cfg hist_auto_range\n"
                        "sensor_cfg hist_tot\n"
                        "sensor_cfg hist_exp\n"
                        "sensor_cfg hist_ratio\n"
                        "sensor_cfg cfar_size_obj\n"
                        "sensor_cfg cfar_combine_mode\n"
                        "sensor_cfg cfar_combine_mask\n"
                        "sensor_cfg cfar_hybrid_combine_mask\n"
                        "sensor_cfg fft_channel_mask \n"
                        "sensor_cfg cfar_combine_velamb_en\n"
                        "sensor_cfg cfar_combine_dirs\n"
                        "sensor_cfg cfar_combine_thetas\n"
                        "sensor_cfg cfar_combine_phis\n"
                        "sensor_cfg cfar_mimo_win\n"
                        "sensor_cfg cfar_mimo_win_params\n"
                        "sensor_cfg cfar_region_sep_rng\n"
                        "sensor_cfg cfar_region_sep_vel\n"
                        "sensor_cfg cfar_pk_en\n"
                        "sensor_cfg cfar_pk_rng_size\n"
                        "sensor_cfg cfar_pk_vel_size\n"
                        "sensor_cfg cfar_pk_threshold\n"
                        "sensor_cfg cfar_region_algo_type\n"
                        "sensor_cfg cfar_local_noise\n"
                        "sensor_cfg cfar_win_mask\n"
                        "sensor_cfg cfar_ca_alpha\n"
                        "sensor_cfg cfar_ca_n\n"
                        "sensor_cfg cfar_os_rnk_ratio\n"
                        "sensor_cfg cfar_os_rnk_sel\n"
                        "sensor_cfg cfar_os_tdec\n"
                        "sensor_cfg cfar_os_alpha\n"
                        "sensor_cfg cfar_sogo_alpha\n"
                        "sensor_cfg cfar_sogo_i\n"
                        "sensor_cfg cfar_nr_alpha\n"
                        "sensor_cfg acc_rng_hw\n"
                        "sensor_cfg acc_vel_hw\n"
                        "sensor_cfg cfar_cand_enable\n"
                        "sensor_cfg cfar_cand_size_obj\n"
                        "sensor_cfg cfar_cand_alpha\n"
                        "sensor_cfg cfar_cand_pk_en\n"
                        "sensor_cfg cfar_cand_vel_skip\n"
                        "sensor_cfg cfar_cand_vel_size\n"
                        "sensor_cfg cfar_cand_rng_skip\n"
                        "sensor_cfg cfar_cand_rng_size\n"
                        "sensor_cfg cfar_cand_pk_rng_size\n"
                        "sensor_cfg cfar_cand_pk_vel_size\n"
                        "sensor_cfg cfar_ddm_cycle\n"
                        "sensor_cfg de_vel_amb_en\n"
                        "sensor_cfg vel_amb_phase_comp_en\n"
                        "sensor_cfg vel_amb_comb_en\n"
                        "sensor_cfg decouple_en\n"
                        "sensor_cfg decouple_mat\n"
                        "sensor_cfg vel_amb_phase_comp_typ\n"
                        "sensor_cfg vel_comp_asym\n"
                        "sensor_cfg vel_amb_qmin\n"
                        "sensor_cfg high_vel_comp_en\n"
                        "sensor_cfg high_vel_comp_method\n"
                        "sensor_cfg vel_comp_usr\n"
                        "sensor_cfg rng_comp_usr\n"
                        "sensor_cfg doa_method\n"
                        "sensor_cfg doa_2d_combine\n"
                        "sensor_cfg doa_num_groups\n"
                        "sensor_cfg doa_fft_mux\n"
                        "sensor_cfg doa_subarray_mux\n"
                        "sensor_cfg doa_az_left\n"
                        "sensor_cfg doa_az_right\n"
                        "sensor_cfg doa_ev_up\n"
                        "sensor_cfg doa_ev_down\n"
                        "sensor_cfg doa_sine_uniform\n"
                        "sensor_cfg doa_npoint\n"
                        "sensor_cfg sv_read_from_flash\n"
                        "sensor_cfg sv_ptr\n"
                        "sensor_cfg doa_sv_share\n"
                        "sensor_cfg ant_info_from_flash\n"
                        "sensor_cfg ant_info_flash_addr\n"
                        "sensor_cfg ant_pos\n"
                        "sensor_cfg ant_comps\n"
                        "sensor_cfg doa_win\n"
                        "sensor_cfg doa_win_params\n"
                        "sensor_cfg doa_noise_from_sw\n"
                        "sensor_cfg doa_noise\n"
                        "sensor_cfg bfm_lite_mode\n"
                        "sensor_cfg bfm_mode\n"
                        "sensor_cfg bfm_early_finish\n"
                        "sensor_cfg bfm_iter_search\n"
                        "sensor_cfg bfm_local_maximum_win\n"
                        "sensor_cfg bfm_raw_search_step\n"
                        "sensor_cfg bfm_fine_search_range\n"
                        "sensor_cfg bfm_max_obj_per_bin\n"
                        "sensor_cfg bfm_snr_thres\n"
                        "sensor_cfg bfm_noise_scalar\n"
                        "sensor_cfg bfm_power_scalar\n"
                        "sensor_cfg dml_single_dim_mask\n"
                        "sensor_cfg dml_double_dim_mask\n"
                        "sensor_cfg dml_min_diff\n"
                        "sensor_cfg dml_search_begin\n"
                        "sensor_cfg dml_search_end\n"
                        "sensor_cfg dml_search_step\n"
                        "sensor_cfg dml_search_fine_range\n"
                        "sensor_cfg dml_suppress_size\n"
                        "sensor_cfg dml_suppress_start\n"
                        "sensor_cfg dml_snr_rne\n"
                        "sensor_cfg dml_rne_coef\n"
                        "sensor_cfg fsm_on\n"
                        "sensor_cfg acc_angle_hw\n"
                        "sensor_cfg track_fps\n"
                        "sensor_cfg track_fov_az_left\n"
                        "sensor_cfg track_fov_az_right\n"
                        "sensor_cfg track_fov_ev_down\n"
                        "sensor_cfg track_fov_ev_up\n"
                        "sensor_cfg track_near_field_thres\n"
                        "sensor_cfg track_capture_delay\n"
                        "sensor_cfg track_drop_delay\n"
                        "sensor_cfg track_vel_pos_ind_portion\n"
                        "sensor_cfg track_obj_snr_sel\n"
                        "sensor_cfg com_params\n"
                        "sensor_cfg ddm_en\n"
                        "sensor_cfg ddm_phase_step\n"
                        "sensor_cfg adc_freq\n"
                        "sensor_cfg agc_mode\n"
                        "sensor_cfg agc_code_init\n"
                        "sensor_cfg agc_code_c0\n"
                        "sensor_cfg agc_code_c1\n"
                        "sensor_cfg anti_velamb_en\n"
                        "sensor_cfg anti_velamb_delay_pre\n"
                        "sensor_cfg anti_velamb_delay_post\n"
                        "sensor_cfg bpm_mode\n"
                        "sensor_cfg fmcw_startfreq\n"
                        "sensor_cfg fmcw_bandwidth\n"
                        "sensor_cfg fmcw_chirp_rampup\n"
                        "sensor_cfg fmcw_chirp_down\n"
                        "sensor_cfg fmcw_chirp_period\n"
                        "sensor_cfg nchirp\n"
                        "sensor_cfg rf_tia_gain\n"
                        "sensor_cfg rf_vga1_gain\n"
                        "sensor_cfg rf_vga2_gain\n"
                        "sensor_cfg rf_hpf0\n"
                        "sensor_cfg rf_hpf1\n"
                        "sensor_cfg rf_hpf2\n"
                        "sensor_cfg tx_groups\n"
                        "sensor_cfg tx_phase_value\n"
                        "sensor_cfg ddm_tx_phase\n"
                        "sensor_cfg ddm_rpt\n"
                        "sensor_cfg ddm_prd\n"
                        "sensor_cfg automode\n"
                        );
        }
    }

    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds(id,cmds);
}

void DialogCanCmds::on_pushButton_entryCfg_clicked()
{
    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds(id,"cfg_mode 1\n");
}

void DialogCanCmds::on_pushButton_outCfg_clicked()
{
    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds(id,"cfg_mode 0\n");
}

void DialogCanCmds::on_pushButton_temp_clicked()
{
    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds(id,"sensor_tmp\n");
}

void DialogCanCmds::on_pushButtonClearBuf_clicked()
{
    int channelIndex = ui->comboBoxChannelIndex->currentIndex();
    QByteArray dat;
    dat.fill(0,8);
    dat[0] = 0xC1;
    int id = ui->lineEdit_radar_addr->text().toInt();
//    emit sigSendData(0x130|(id&0x000F) , dat , channelIndex);
    sendData(channelIndex, 0x130|(id&0x000F) , dat);
}

void DialogCanCmds::slotSendCfgCmd()
{
    QPushButton * btObj = qobject_cast<QPushButton *>(sender());
    QString objStr = btObj->objectName();
    qDebug()<<"obj name="<<btObj->objectName();

    QString profile = objStr.right(1);
    int idx = objStr.indexOf("Bt");
    QString cfgcmd = objStr.left(idx);
    QString editObjName = cfgcmd+"Edit"+profile;
    editObjName = editObjName.trimmed();
    QLineEdit* editObj =  ui->tab_profile->findChild<QLineEdit *>(editObjName);

    QString cmdvalue = editObj->text();

    curPorfile = profile.toInt();

    QString cmds = "";
    cmds.append("profile ").append(profile).append("\n");
    cmds.append("sensor_cfg ").append(cfgcmd).append(" ").append(cmdvalue).append("\n");
    qDebug()<<"cmd:"<<cmds;
    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds(id , cmds);

}



void DialogCanCmds::on_pushButton_SR_clicked()
{
    QString cmds={
        "profile 0\n"
        "sensor_cfg doa_samp_space u\n"
        "sensor_cfg doa_method 2\n"
        "sensor_cfg doa_win rect\n"
        "sensor_cfg doa_max_obj_per_bin 0 1 1\n"
        "sensor_cfg bfm_az_left -30\n"
        "sensor_cfg bfm_az_right 30\n"
        "sensor_cfg dml_extra_1d_en 1\n"
        "sensor_cfg dml_2dsch_step 10\n"
        "sensor_cfg dml_respwr_coef 0.094312 -1 0.007497 -0.0036927 -0.15363 0.094312 -1 0.007497 -0.0036927 -0.15363\n"
        "profile 1\n"
        "sensor_cfg doa_samp_space u\n"
        "sensor_cfg doa_method 2\n"
        "sensor_cfg doa_win rect\n"
        "sensor_cfg doa_max_obj_per_bin 0 1 1\n"
        "sensor_cfg bfm_az_left -30\n"
        "sensor_cfg bfm_az_right 30\n"
        "sensor_cfg dml_extra_1d_en 1\n"
        "sensor_cfg dml_2dsch_step 10\n"
        "sensor_cfg dml_respwr_coef 0.094312 -1 0.007497 -0.0036927 -0.15363 0.094312 -1 0.007497 -0.0036927 -0.15363\n"
        "profile 2\n"
        "sensor_cfg doa_samp_space u\n"
        "sensor_cfg doa_method 2\n"
        "sensor_cfg doa_win rect\n"
        "sensor_cfg doa_max_obj_per_bin 0 1 1\n"
        "sensor_cfg bfm_az_left -30\n"
        "sensor_cfg bfm_az_right 30\n"
        "sensor_cfg dml_extra_1d_en 1\n"
        "sensor_cfg dml_2dsch_step 10\n"
        "sensor_cfg dml_respwr_coef 0.094312 -1 0.007497 -0.0036927 -0.15363 0.094312 -1 0.007497 -0.0036927 -0.15363\n"
        "profile 3\n"
        "sensor_cfg doa_samp_space u\n"
        "sensor_cfg doa_method 2\n"
        "sensor_cfg doa_win rect\n"
        "sensor_cfg doa_max_obj_per_bin 0 1 1\n"
        "sensor_cfg bfm_az_left -30\n"
        "sensor_cfg bfm_az_right 30\n"
        "sensor_cfg dml_extra_1d_en 1\n"
        "sensor_cfg dml_2dsch_step 10\n"
        "sensor_cfg dml_respwr_coef 0.094312 -1 0.007497 -0.0036927 -0.15363 0.094312 -1 0.007497 -0.0036927 -0.15363\n"
    };
    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds(id , cmds);

}

void DialogCanCmds::on_pushButton_NoSR_clicked()
{
    QString cmds={
        "profile 0\n"
        "sensor_cfg doa_samp_space t\n"
        "sensor_cfg doa_method 0\n"
        "sensor_cfg doa_win cheb\n"
        "sensor_cfg doa_max_obj_per_bin 1 1 1\n"
        "sensor_cfg bfm_az_left -80\n"
        "sensor_cfg bfm_az_right 80\n"
        "profile 1\n"
        "sensor_cfg doa_samp_space t\n"
        "sensor_cfg doa_method 0\n"
        "sensor_cfg doa_win cheb\n"
        "sensor_cfg doa_max_obj_per_bin 1 1 1\n"
        "sensor_cfg bfm_az_left -80\n"
        "sensor_cfg bfm_az_right 80\n"
        "profile 2\n"
        "sensor_cfg doa_samp_space t\n"
        "sensor_cfg doa_method 0\n"
        "sensor_cfg doa_win cheb\n"
        "sensor_cfg doa_max_obj_per_bin 1 1 1\n"
        "sensor_cfg bfm_az_left -80\n"
        "sensor_cfg bfm_az_right 80\n"
        "profile 3\n"
        "sensor_cfg doa_samp_space t\n"
        "sensor_cfg doa_method 0\n"
        "sensor_cfg doa_win cheb\n"
        "sensor_cfg doa_max_obj_per_bin 1 1 1\n"
        "sensor_cfg bfm_az_left -80\n"
        "sensor_cfg bfm_az_right 80\n"
    };
    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds(id , cmds);

}

void DialogCanCmds::on_pushButton_reset_clicked()
{
    QString cmds={
        "restart system\n"
    };
    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds(id , cmds);

}

} // namespace Functions

void Functions::DialogCanCmds::on_comboBoxType_currentIndexChanged(int index)
{
    if( index == 0 ){ //alps
        createCfgBotton( -1, CFGVAR_FILENAME_ALPS );
    }else{ //alps pro
        createCfgBotton( -1, CFGVAR_FILENAME_ALPSPRO );
    }
}

void Functions::DialogCanCmds::on_btnSelAziDecay_clicked()
{
    QString filename = QFileDialog::getOpenFileName(this,
                                                    "select file",
                                                    ui->lineEditAziDecay->text(),
                                                    tr("csv (*.csv)" ) );
    if ( !filename.isEmpty() ){
        ui->lineEditAziDecay->setText( filename );
    }
}

void Functions::DialogCanCmds::on_ptnSelAntPos_clicked()
{
    QString filename = QFileDialog::getOpenFileName(this,
                                                    "select file",
                                                    ui->lineEditAntPos->text(),
                                                    tr("csv (*.csv)" ) );
    if ( !filename.isEmpty() ){
        ui->lineEditAntPos->setText( filename );
    }
}

void Functions::DialogCanCmds::on_ptnSelAntComps_clicked()
{
    QString filename = QFileDialog::getOpenFileName(this,
                                                    "select file",
                                                    ui->lineEditAntComps->text(),
                                                    tr("csv (*.csv)" ) );
    if ( !filename.isEmpty() ){
        ui->lineEditAntComps->setText( filename );
    }
}

void Functions::DialogCanCmds::on_ptnSelDBFCoef_clicked()
{
    QString filename = QFileDialog::getOpenFileName(this,
                                                    "select file",
                                                    ui->lineEditDBFCoef->text(),
                                                    tr("csv (*.csv)" ) );
    if ( !filename.isEmpty() ){
        ui->lineEditDBFCoef->setText( filename );
    }
}

bool Functions::DialogCanCmds::writeFromCsv(const QString &fileName, const QString &cmd_pre)
{
    int id = ui->lineEdit_radar_addr->text().toInt();

    if( fileName.length() == 0 ){
        return false;
    }

    QFile file( fileName );
    if( !file.open( QIODevice::ReadOnly ) ){
        return false;
    }

    int lineNum = 0;
    while (!file.atEnd()) {
      QByteArray line = file.readLine();
      QString str = line.replace( ',', ' ' );

      ui->cbProfile->setCurrentIndex( lineNum ); //切换profile index
      //QThread::msleep( SLEEP_TIME_MS );
      wait( SLEEP_TIME_MS );

      QString cmd = cmd_pre + " " + str;
      qDebug() << __FUNCTION__ << __LINE__ << cmd;
      emit sigSendCmds( id, cmd );
      //QThread::msleep( SLEEP_TIME_MS );
      wait( SLEEP_TIME_MS );

      lineNum++;
      if( lineNum > 3 ){
          break;
      }
    }
    file.close();

    return true;
}



void Functions::DialogCanCmds::on_btnWriteCsv_clicked()
{
    int id = ui->lineEdit_radar_addr->text().toInt();
    emit sigSendCmds( id, "cfg_mode 1");
    //QThread::msleep( SLEEP_TIME_MS );
    wait( SLEEP_TIME_MS );

    //收发方向图
    QString file = ui->lineEditAziDecay->text();
    writeFromCsv( file, "sensor_cfg azi_decay 0" );

    //天线间距
    file = ui->lineEditAntPos->text();
    writeFromCsv( file, "sensor_cfg ant_pos" );

    //天线相位差
    file = ui->lineEditAntComps->text();
    writeFromCsv( file, "sensor_cfg ant_comps" );

    //写入角度校准模式[0:一度一校准][1:曲线拟合]
    emit sigSendCmds( id, "ant_cali_mode 0 0 0 0");
    //QThread::msleep( SLEEP_TIME_MS );
    wait( SLEEP_TIME_MS );

    //保存配置 写入Flash
    emit sigSendCmds( id, "cfg_to_flash");
    //QThread::msleep( SLEEP_TIME_MS );
    wait( SLEEP_TIME_MS );

    emit sigSendCmds( id, "cfg_mode 0");
    //QThread::msleep( SLEEP_TIME_MS );
    wait( SLEEP_TIME_MS );

    file = ui->lineEditDBFCoef->text();
    if( file.length() != 0 ){
        //进入生产模式
        if( !enterFactoryMode() ){
            ui->textCmdAck->append( "enter factory mode error!!!!!!!!\n" );
            return;
        }else{
            ui->textCmdAck->append( "enter factory mode success!!!!!!!!\n" );
        }

        //写入校准版本
        if( !writeCalVer( 0x04, 0x04 ) ){
            ui->textCmdAck->append( "write calVer error!!!!!!!!\n" );
            return;
        }else{
            ui->textCmdAck->append( "write calVer success!!!!!!!!\n" );
        }

        //写入dbf因子
        if( !writeDbfCsv( file ) ){
            ui->textCmdAck->append( "write dbf error!!!!!!!!\n" );
            return;
        }else{
            ui->textCmdAck->append( "write dbf success!!!!!!!!\n" );
        }
    }
}


void Functions::DialogCanCmds::slotCanAck(int id, const QByteArray data)
{
    switch ( mReqID ) {
    case FACTORY_CAN_SET_WORK_REQ:
        if( id == FACTORY_CAN_SET_WORK_ANS ){
            mAckID = FACTORY_CAN_SET_WORK_ANS;
            mAckData = data;
        }
        break;
    case FACTORY_CAN_OEM_VERSION_REQ:
        if( id == FACTORY_CAN_OEM_VERSION_ANS ){
            mAckID = FACTORY_CAN_OEM_VERSION_ANS;
            mAckData = data;
        }
        break;
    case FACTORY_BURN_CFG_REQ:
        if( id == FACTORY_BURN_CFG_ANS ){
            mAckID = FACTORY_BURN_CFG_ANS;
            mAckData = data;
        }
        break;
    case FLASH_LOAD_CANID_REQ:
        if( id == FLASH_LOAD_CANID_RESP ){
            mAckID = FLASH_LOAD_CANID_RESP;
            mAckData = data;
        }
        break;
    default:
        break;
    }


//    quint8* pData = (quint8*)data.data();
//    //安全认证响应
//    if( id == FACTORY_CAN_SET_WORK_ANS && (pData[0] == 0x0E)
//            && (pData[1] == 0x31) && (pData[2] == 0x58) && (pData[3] == 0xAF) ){
//        mFactorySafeCode = data;
//        return;
//    }

//    //雷达安全确认响应
//    if( id == FACTORY_CAN_SET_WORK_ANS && (pData[0] == 0x0F)
//            && (pData[1] == 0x31) && (pData[2] == 0x58)
//            && (pData[3] == 0xAF) && (pData[4] == 0x00) ){
//        mFactorySafeOK = true;
//    }

//    //进入生产-普通模式
//    if( id == FACTORY_CAN_SET_WORK_ANS && (pData[0] == 0x01)
//            && (pData[1] == 0x31) && (pData[2] == 0x58) && (pData[3] == 0xAF) ){
//        mFactorySafeOK = true;
//    }

//    if( ( id == FACTORY_CAN_OEM_VERSION_ANS) && (pData[0] == 0x83) ){
//        mFactorySafeOK = true;
//    }

//    if( ( id == FACTORY_BURN_CFG_ANS) && (pData[0] == 0x01) && (pData[1] == 0x11) ){
//        mFactorySafeOK = true;
//    }



}

bool Functions::DialogCanCmds::enterFactoryMode()
{
    int channelIndex = ui->comboBoxChannelIndex->currentIndex();

    //安全认证请求
    QByteArray data = QByteArray::fromHex("0E3158AF0102034C");
    mReqID = FACTORY_CAN_SET_WORK_REQ;
    if( !sendData( channelIndex, mReqID ,data ) ){
        return false;
    }
    //等待
    wait( SLEEP_TIME_MS );
    quint8* pData = (quint8*)mAckData.data();
    if( mAckID == FACTORY_CAN_SET_WORK_ANS && (pData[0] == 0x0E)
                && (pData[1] == 0x31) && (pData[2] == 0x58) && (pData[3] == 0xAF) ){
    }else{
        qDebug() << __FUNCTION__ << __LINE__ << QString::fromLocal8Bit( "安全认证响应 失败!" );
        return false;
    }

    //回复雷达安全确认
    data.clear();
    data.append(0x0F);
    data.append(0x31);
    data.append(0x58);
    data.append(0xAF);
    data.append(pData[4]);
    data.append(pData[5]);
    data.append(pData[6]);
    quint8 dutCheckSum = (0x0F + 0x31 + 0x58 + 0xAF + pData[4] + pData[5] + pData[6]) & 0xFF;
    data.append(dutCheckSum);
    mReqID = FACTORY_CAN_SET_WORK_REQ;
    if( !sendData( channelIndex, mReqID ,data ) ){
        return false;
    }
    //等待 雷达安全确认
    wait( SLEEP_TIME_MS );
    pData = (quint8*)mAckData.data();
    if( mAckID == FACTORY_CAN_SET_WORK_ANS && (pData[0] == 0x0F)
            && (pData[1] == 0x31) && (pData[2] == 0x58)
            && (pData[3] == 0xAF) && (pData[4] == 0x00) ){
    }else{
        qDebug() << __FUNCTION__ << __LINE__ << QString::fromLocal8Bit( "雷达安全确认 失败!" );
        return false;
    }

    //进入生产-普通模式
    data.clear();
    data.resize( 8 );
    data.fill( 0 );
    data[0] = 0x01;
    data[1] = 0x31;
    data[2] = 0x58;
    data[3] = 0xAF;
    mReqID = FACTORY_CAN_SET_WORK_REQ;
    if( !sendData( channelIndex, mReqID ,data ) ){
        return false;
    }
    //等待 进入生产-普通模式
    wait( SLEEP_TIME_MS );
    pData = (quint8*)mAckData.data();
    if( mAckID == FACTORY_CAN_SET_WORK_ANS && (pData[0] == 0x01)
            && (pData[1] == 0x31) && (pData[2] == 0x58) && (pData[3] == 0xAF) ){
    }else{
        qDebug() << __FUNCTION__ << __LINE__ << QString::fromLocal8Bit( "进入生产-普通模式 失败!" );
        return false;
    }

    return true;
}

bool Functions::DialogCanCmds::writeCalVer( quint8 calVerH, quint8 calVerL )
{
    int channelIndex = ui->comboBoxChannelIndex->currentIndex();

    QByteArray data;
    //写入校准版本
    data.resize( 8 );
    data.fill( 0 );
    data[0] = 0x03; //read:1 write:0  0:NUL 1:HW_VER 2:SW_VER 3:CALI_VER
    data[1] = calVerH;
    data[2] = calVerL;
    mReqID = FACTORY_CAN_OEM_VERSION_REQ;
    if( !sendData( channelIndex, mReqID ,data ) ){
        return false;
    }
    //等待
    wait( SLEEP_TIME_MS );
    quint8* pData = (quint8*)mAckData.data();
    if( ( mAckID == FACTORY_CAN_OEM_VERSION_ANS) && (pData[0] == 0x83) ){
    }else{
        qDebug() << __FUNCTION__ << __LINE__ << QString::fromLocal8Bit( "写入校准版本 失败!" );
        return false;
    }

    //校准版本保存至Flash
    data.fill( 0 );
    data[0] = 0xA1; //read:1 write:0  0:NUL 1:HW_VER 2:SW_VER 3:CALI_VER
    mReqID = FACTORY_BURN_CFG_REQ;
    if( !sendData( channelIndex, mReqID ,data ) ){
        return false;
    }
    //等待
    wait( SLEEP_TIME_MS );
    pData = (quint8*)mAckData.data();
    if( ( mAckID == FACTORY_BURN_CFG_ANS) && (pData[0] == 0x01) && (pData[1] == 0x11) ){
    }else{
        qDebug() << __FUNCTION__ << __LINE__ << QString::fromLocal8Bit( "校准版本保存至Flash 失败!" );
        return false;
    }

    return true;
}

bool Functions::DialogCanCmds::writeDbfCsv(const QString &fileName)
{
    int channelIndex = ui->comboBoxChannelIndex->currentIndex();

    if( fileName.length() == 0 ){
        ui->textCmdAck->append( "file name is NULL!!!!!!!!\n" );
        return false;
    }

    generateCrcTable();

    QFile file( fileName );
    if( !file.open( QIODevice::ReadOnly ) ){
        ui->textCmdAck->append( "file open error!!!!!!!!\n" );
        return false;
    }
    //读取csv中的数据
    QByteArrayList dbfFactorDataBytesList; //dbf因子转为字节后的数据
    QByteArrayList dbfFactorDataList; //dbf因子原始数据
    while( !file.atEnd() ){
        QByteArray line = file.readLine();
        QByteArrayList wordList = line.split( ',' );
//        qDebug() << __FUNCTION__ << __LINE__ << line;
//        qDebug() << __FUNCTION__ << __LINE__ << wordList;
        QByteArray dbfFactorDataBytes;
        QByteArray dbfFactorData;
        for( int i=0; i<wordList.size(); i++ ){
            quint32 word = wordList[i].toUInt();
            dbfFactorDataBytes.append( ( (word >> 24) & 0xff ) );
            dbfFactorDataBytes.append( ( (word >> 16) & 0xff) );
            dbfFactorDataBytes.append( ( (word >> 8) & 0xff) );
            dbfFactorDataBytes.append( ( (word) & 0xff) );
            dbfFactorData.append( word );
        }
        dbfFactorDataBytesList << dbfFactorDataBytes;
        dbfFactorDataList << dbfFactorData;
//        qDebug() << __FUNCTION__ << __LINE__ << dbfFactorDataBytes.size() << dbfFactorData.size();
    }
    file.close();

    //写入dbf因子
    for( int i=0; i<dbfFactorDataBytesList.size(); i++ ){
        //获取地址
        quint32 addr = getDbfFactorFlashAddr( i );

        QByteArray& dbfData = dbfFactorDataBytesList[i];
        quint32 dbfFactorDataValidBytes = dbfData.size(); //有效数据长度
        if( dbfData.size() % 7 != 0 ){//补齐7的整数倍
            dbfData.append( 7 - dbfData.size() % 7, 0 );
        }
        quint16 len_1k = dbfData.size() / 1024;
        if( len_1k*1024 < dbfData.size() ){
                len_1k += 1;
        }

        //擦除DBF数据
        QByteArray data;
        data.append( (0x80 | 0xD) );// # cmd
        data.append((addr >> 24) & 0xFF);// #addr start
        data.append((addr >> 16) & 0xFF);
        data.append((addr >> 8) & 0xFF);
        data.append((addr >> 0) & 0xFF); //#addr end
        data.append((len_1k >> 8)&0x00FF); //#length start
        data.append(len_1k & 0x00FF); //#length end
        data.append((char)0); //#none
        if( !sendData( channelIndex, FLASH_LOAD_CANID_REQ ,data ) ){
            return false;
        }
        ui->textCmdAck->append( QString("profileidx=%1 addr=0x%2 len=%3").arg(i).arg(addr,0,16).arg(len_1k) );
        wait( SLEEP_TIME_MS );

        //开始传输
        data.clear();
        data.append((0x80 | 0x1));
        data.append((char)0);
        data.append((char)0);
        data.append((char)0);
        data.append((char)0);
        data.append((char)0);
        data.append((char)0);
        data.append((char)0);
        if( !sendData( channelIndex, FLASH_LOAD_CANID_REQ ,data ) ){
            return false;
        }

        //传输数据
        quint32 frameTotalNum = dbfData.size() / 7;//必然能被7整除
        quint8* pDbfData = (quint8*)dbfData.data();
        quint8 frameCnt = 0;
        for( int j=0; j<frameTotalNum; j++ ){
            data.clear();
            //data.append(0x0 | 0x7);
            data.append( ( frameCnt << 3 ) | 0x7 );
            for( int z=0; z<7; z++ ){
                data.append( pDbfData[j*7+z] );
//                data.append( (char)0 );
            }
            if( !sendData( channelIndex, FLASH_LOAD_CANID_REQ ,data ) ){
                return false;
            }

            frameCnt++;
            if( frameCnt > 0xF ){
                frameCnt = 0;
            }
        }

        //# 写Flash指令
        data.clear();
        data.append((0x80 | 0xC));
        data.append((addr >> 24) & 0xff);
        data.append((addr >> 16) & 0xff);
        data.append((addr >> 8) & 0xff);
        data.append((addr & 0xff));
        data.append((dbfFactorDataValidBytes >> 8) & 0xff);
        data.append(dbfFactorDataValidBytes & 0xff);
        data.append((char)0);
        if( !sendData( channelIndex, FLASH_LOAD_CANID_REQ ,data ) ){
            return false;
        }
        ui->textCmdAck->append( "write to flash success!!!!!!!!\n" );
        wait( 1000 );

        //本地crc计算
        QByteArray flashStoreData; //  #调换与flash内数据一致 用于计算CRC
        for( int j=0; j<dbfFactorDataValidBytes; j++ ){
            if( (j+1) % 4 == 0 ){
                flashStoreData.append( pDbfData[j] );
                flashStoreData.append( pDbfData[j-1] );
                flashStoreData.append( pDbfData[j-2] );
                flashStoreData.append( pDbfData[j-3] );
//                flashStoreData.append( (char) 0 );
            }
        }
//        qDebug() << __FUNCTION__ << __LINE__ << flashStoreData.toHex(' ');
        quint32 sendCrc = calCRC( flashStoreData );
//        qDebug() << __FUNCTION__ << __LINE__ << "sendCrc=0x" << QString::number(sendCrc,16);
        data.clear();
        data.append((0x80 | 0x4));
        data.append(i);
        data.append((sendCrc >> 24) & 0xFF);
        data.append((sendCrc >> 16) & 0xFF);
        data.append((sendCrc >> 8) & 0xFF);
        data.append(sendCrc & 0xFF);
        data.append((char)0);
        data.append((char)0);
        mReqID = FLASH_LOAD_CANID_REQ;
        if( !sendData( channelIndex, FLASH_LOAD_CANID_REQ ,data ) ){
            return false;
        }
        wait( SLEEP_TIME_MS );
        quint8 *pAckData = (quint8*)mAckData.data();
        if( mAckID == FLASH_LOAD_CANID_RESP && pAckData[0] == 0x84) {// #0x84 CRC command
            if( pAckData[1] == 0x00 ){
                ui->textCmdAck->append( "crc check success!!!!!!!!\n" );
            }else{
                return false;
            }
        }

        //结束文件传输
        data.clear();
        data.append( 0x80|0x13 );
        data.append( (char)0 );
        data.append( (char)0 );
        data.append( (char)0 );
        data.append( (char)0 );
        data.append( (char)0 );
        data.append( (char)0 );
        data.append( (char)0 );
        if( !sendData( channelIndex, FLASH_LOAD_CANID_REQ ,data ) ){
            return false;
        }
    }

    return true;
}

void Functions::DialogCanCmds::wait(quint64 ms)
{
    QEventLoop loop;
    QTimer timer;
    timer.setSingleShot( true );
    connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
    timer.start( ms );
    loop.exec();
    timer.stop();
}

quint32 Functions::DialogCanCmds::getDbfFactorFlashAddr(quint8 profileIndex)
{
    quint32 startFlashAddr = 0;
    switch ( profileIndex ) {
    case 0:
        startFlashAddr = 0x1C7000;
        break;
    case 1:
        startFlashAddr = 0x1CF000;
        break;
    case 2:
        startFlashAddr = 0x1D7000;
        break;
    case 3:
        startFlashAddr = 0x1DF000;
        break;

    }
    return startFlashAddr;
}

void Functions::DialogCanCmds::generateCrcTable()
{
    quint32 POLYNOMIAL = 0x04c11db7;
    for( quint32 i=0; i<256; i++ ){
        quint32 crc_accum = i << 24;
        for( quint32 j=0; j<8; j++ ){
            if( crc_accum & 0x80000000 ){
                crc_accum = ((crc_accum << 1) & 0xFFFFFFFF) ^ POLYNOMIAL;
            }else{
                crc_accum = ((crc_accum << 1) & 0xFFFFFFFF);
            }
        }
        mCrcTable[i] = crc_accum;
    }
}

quint32 Functions::DialogCanCmds::calCRC(QByteArray data)
{
    quint32 crc_accum = 0;
    quint32 idx = 0;
    for( quint32 n=0; n<data.size(); n++ ){
        quint8 v = data[n];
        idx += 1;
        quint32 i = ((crc_accum >> 24) ^ v) & 0xFF;
        crc_accum = ((crc_accum << 8) & 0xFFFFFFFF) ^ mCrcTable[i];
    }
//        print(idx)
    return crc_accum;
}

bool Functions::DialogCanCmds::saveToCsv( const QString& str )
{
    if( !mSaveFile.isOpen() )
        return  false;

    QString srcStr = str;
    QStringList wordList;

    QTextStream stream;
    stream.setDevice( &mSaveFile );

    switch( mSaveToCsvType ){
    case AZI_DECAY:
    case ANT_COMPS:
    {
        int headIdx = srcStr.indexOf( '[' );
        int tailIdx = srcStr.indexOf( ']' );
//        qDebug() <<__FUNCTION__<<__LINE__<< srcStr;
        srcStr = srcStr.mid( headIdx+1, tailIdx - headIdx -1 ); //取[]间的文本
//        qDebug() <<__FUNCTION__<<__LINE__<< srcStr;
        srcStr = srcStr.trimmed();//去除前后空格
//        qDebug() <<__FUNCTION__<<__LINE__<< srcStr;
        stream << srcStr;
    }
        break;
    case ANT_POS:
    {
        int headIdx = srcStr.indexOf( '[' );
        int tailIdx = srcStr.indexOf( ']' );
//        qDebug() <<__FUNCTION__<<__LINE__<< srcStr;
        srcStr = srcStr.mid( headIdx+1, tailIdx - headIdx -1 ); //取[]间的文本
        srcStr.replace( " ", "" ); //去除空格
        srcStr.replace( "(", "" ); //去除左括号
        srcStr.replace( ")", "" ); //去除右括号
        stream << srcStr;
    }
        break;
    case DBF_COEF:
    {
        srcStr.replace( "\r", "" ); //去除末尾换行符
        srcStr = srcStr.mid( srcStr.indexOf( ':' )+1 ); //去除前缀 Read Addr 0x001C9940 Size 224Bytes:
        srcStr = srcStr.trimmed();//去除前后空格
        wordList = srcStr.split( ' ' );
        qDebug() <<__FUNCTION__<<__LINE__<< str;
        qDebug() <<__FUNCTION__<<__LINE__<< srcStr;
        qDebug() <<__FUNCTION__<<__LINE__<< wordList;
        for( int i=0; i<wordList.size(); i++ ){
            if( mSaveWordLen != -1 && mSaveWordLen <= i ){
                break;
            }
            if( mAddCsvSpace ){
                stream << ",";
            }
            mAddCsvSpace = true;
            stream << wordList[i].toUInt( NULL, 16 );
            qDebug() <<__FUNCTION__<<__LINE__<< wordList[i] << wordList[i].toUInt( NULL, 16 );
        }
    }
        break;
    case DBF_COEF_LEN:
    {
//        qDebug() << __FUNCTION__ << __LINE__ << srcStr;
        srcStr = srcStr.mid( srcStr.indexOf( ':' )+1 ); //去除前缀 Read Addr 0x001C9940 Size 224Bytes:
//        qDebug() << __FUNCTION__ << __LINE__ << srcStr;
        QStringList wordList = srcStr.split(" ");
//        qDebug() << __FUNCTION__ << __LINE__ << wordList;
        if( wordList.size() > 2 && wordList[0].toUInt( NULL, 16 ) == DBF_FLASH_MAGIC_NUMBER ){
            mDbfLen = wordList[1].toUInt( NULL, 16 );
//            qDebug() << __FUNCTION__ << __LINE__ << wordList[0] << wordList[1] << mDbfLen;
        }
    }
        break;
    default:
        stream << str;
        break;
    }
    return true;
}

void Functions::DialogCanCmds::openSaveCsv( const QString& path, Functions::DialogCanCmds::SAVE_TO_CSV_TYPE saveType)
{
    switch( saveType ){
    case AZI_DECAY :
        mSaveFile.setFileName( path + "/AziDecay.csv" );
        break;
    case ANT_POS:
        mSaveFile.setFileName( path + "/AntPosData.csv" );
        break;
    case ANT_COMPS:
        mSaveFile.setFileName( path + "/AntCompData.csv" );
        break;
    case DBF_COEF:
        mSaveFile.setFileName( path + "/DBFCoefprofileData.csv" );
        break;
    default:
        return;
    }

    mSaveToCsvType = saveType;
    mSaveFile.open( QIODevice::WriteOnly );
    mSaveToCsv = true;
    mSaveWordLen = -1;
    mAddCsvSpace = false;
}

void Functions::DialogCanCmds::closeSaveCsv()
{
    mSaveToCsv = false;
    mSaveFile.close();
}

void Functions::DialogCanCmds::sendReadCsvCmd(quint8 profileIdx, Functions::DialogCanCmds::SAVE_TO_CSV_TYPE csvType)
{
    if( profileIdx > 3 ){
        return;
    }

//    mSaveToCsv = true;
//    mSaveWordLen = -1;
//    mAddCsvSpace = false;

    int id = ui->lineEdit_radar_addr->text().toInt();
    QString pro_cmd = QString( "profile %1\n").arg( profileIdx );
    QString cmd;
    switch( csvType ){
    case AZI_DECAY :
        cmd = "sensor_cfg azi_decay\n";
        break;
    case ANT_POS:
        cmd = "sensor_cfg ant_pos\n";
        break;
    case ANT_COMPS:
        cmd = "sensor_cfg ant_comps\n";
        break;
//    case DBF_COEF:
//        cmd = "./dbf_coef.csv";
//        break;
    default:
        return;
    }

    mSaveToCsv = false;
    emit sigSendCmds(id,pro_cmd);
    wait( SLEEP_TIME_MS );

    mSaveToCsv = true;
    emit sigSendCmds(id,cmd);
    wait( SLEEP_TIME_MS );
}

void Functions::DialogCanCmds::sendReadCsvCmd_DBF(quint8 profileIdx, quint32 dbfLen)
{
    if( profileIdx > 3 ){
        return;
    }

    mAddCsvSpace = false;
    mSaveWordLen = -1;

    int id = ui->lineEdit_radar_addr->text().toInt();
    //quint32 addr = 0x1C7000; //初始地址
    quint32 addr = getDbfFactorFlashAddr( profileIdx );
    addr += 256; //跳过头部
    quint32 step = 224; //步长
    //quint32 len = 0x00002AD0; //长度
    quint32 len = dbfLen;
    QString cmd = "";
    for( quint32 i=0; i+step<len; /*i++*/ ){
        cmd = QString( "cmd dump_flash 0x%1 0 %2\n" ).arg( QString::number(addr+i,16) ).arg( step );
        emit sigSendCmds(id,cmd);
        wait( SLEEP_TIME_MS );
        i += step;
    }
    //结尾部分
    quint32 endLen = len % step;
    if( endLen != 0 ){
        addr += len - endLen;
        mSaveWordLen = endLen / 4;
        cmd = QString( "cmd dump_flash 0x%1 0 %2\n" ).arg( QString::number(addr,16) ).arg( endLen );
        //cmd = "cmd dump_flash 0x1C7100 0 224\n";
        emit sigSendCmds(id,cmd);
        wait( SLEEP_TIME_MS );
    }
}

quint32 Functions::DialogCanCmds::getDbfLen(quint8 profileIdx)
{
    mDbfLen = 0;
    SAVE_TO_CSV_TYPE tmpType = mSaveToCsvType;
    mSaveToCsvType = DBF_COEF_LEN;
    int id = ui->lineEdit_radar_addr->text().toInt();
    quint32 addr = getDbfFactorFlashAddr( profileIdx );
    QString cmd = QString( "cmd dump_flash 0x%1 0 200\n" ).arg( QString::number(addr,16) );
    emit sigSendCmds(id,cmd);
    wait( SLEEP_TIME_MS );
    mSaveToCsvType = tmpType;
    return mDbfLen;
}

quint8 Functions::DialogCanCmds::getMaxProfileIdx()
{
    quint8 maxProfileIdx = 0;
    if( ui->cbSelProf0->checkState() ){
        maxProfileIdx = 0;
    }
    if( ui->cbSelProf1->checkState() ){
        maxProfileIdx = 1;
    }
    if( ui->cbSelProf2->checkState() ){
        maxProfileIdx = 2;
    }
    if( ui->cbSelProf3->checkState() ){
        maxProfileIdx = 3;
    }
    return maxProfileIdx;
}

void Functions::DialogCanCmds::saveToCsv_DBF(const QString &path)
{
    quint8 maxProIdx = getMaxProfileIdx();

    quint32 dbfLen = 0;
    openSaveCsv( path, DBF_COEF );
    if( ui->cbSelProf0->checkState() ){
        dbfLen = getDbfLen( 0 );
        sendReadCsvCmd_DBF( 0, dbfLen );
    }
    if( maxProIdx > 0 ){
        mSaveFile.write( "\r" );
    }
    if( ui->cbSelProf1->checkState() ){
        dbfLen = getDbfLen( 1 );
        sendReadCsvCmd_DBF( 1, dbfLen );
    }
    if( maxProIdx > 1 ){
        mSaveFile.write( "\r" );
    }
    if( ui->cbSelProf2->checkState() ){
        dbfLen = getDbfLen( 2 );
        sendReadCsvCmd_DBF( 2, dbfLen );
    }
    if( maxProIdx > 2 ){
        mSaveFile.write( "\r" );
    }
    if( ui->cbSelProf3->checkState() ){
        dbfLen = getDbfLen( 3 );
        sendReadCsvCmd_DBF( 3, dbfLen );
    }
    closeSaveCsv();
}

void Functions::DialogCanCmds::saveToCsv(const QString &path, Functions::DialogCanCmds::SAVE_TO_CSV_TYPE csvType)
{
    quint8 maxProIdx = getMaxProfileIdx();
    openSaveCsv( path, csvType );
    if( ui->cbSelProf0->checkState() ){
        sendReadCsvCmd( 0, csvType );
    }
    if( maxProIdx > 0 ){
        mSaveFile.write( "\r" );
    }
    if( ui->cbSelProf1->checkState() ){
        sendReadCsvCmd( 1, csvType );
    }
    if( maxProIdx > 1 ){
        mSaveFile.write( "\r" );
    }
    if( ui->cbSelProf2->checkState() ){
        sendReadCsvCmd( 2, csvType );
    }
    if( maxProIdx > 2 ){
        mSaveFile.write( "\r" );
    }
    if( ui->cbSelProf3->checkState() ){
        sendReadCsvCmd( 3, csvType );
    }
    closeSaveCsv();
}

void Functions::DialogCanCmds::on_btnReadCsv_clicked()
{
//    int id = ui->lineEdit_radar_addr->text().toInt();

    QString path = QFileDialog::getExistingDirectory();
    if (path.isEmpty()) {
        QMessageBox::warning( this, "ERROR", "path is NULL!" );
        return;
    }


    saveToCsv( path, AZI_DECAY );
    saveToCsv( path, ANT_POS );
    saveToCsv( path, ANT_COMPS );
    saveToCsv_DBF( path );




//    QString cmd = "profile 0\n";
//    emit sigSendCmds(id,cmd);
//    wait( SLEEP_TIME_MS );

//    openSaveCsv( AZI_DECAY );
//    cmd = "sensor_cfg azi_decay\n";
//    emit sigSendCmds(id,cmd);
//    wait( SLEEP_TIME_MS );
//    closeSaveCsv();

//    openSaveCsv( ANT_POS );
//    cmd = "sensor_cfg ant_pos\n";
//    emit sigSendCmds(id,cmd);
//    wait( SLEEP_TIME_MS );
//    closeSaveCsv();


//    openSaveCsv( ANT_COMPS );
//    cmd = "sensor_cfg ant_comps\n";
//    emit sigSendCmds(id,cmd);
//    wait( SLEEP_TIME_MS );
//    closeSaveCsv();

//    openSaveCsv( path, DBF_COEF );

//    quint32 addr = 0x1C7000; //初始地址
//    addr += 256; //跳过头部
//    quint32 step = 224; //步长
//    quint32 len = 0x00002AD0; //长度
//    for( quint32 i=0; i+step<len; /*i++*/ ){
//        cmd = QString( "cmd dump_flash 0x%1 0 %2\n" ).arg( QString::number(addr+i,16) ).arg( step );
//        //cmd = "cmd dump_flash 0x1C7100 0 224\n";
////        qDebug() << __FUNCTION__ <<__LINE__<< addr << i << QString::number(addr+i,16);
////        qDebug() << __FUNCTION__ <<__LINE__<< cmd;
//        emit sigSendCmds(id,cmd);
//        wait( SLEEP_TIME_MS );
//        i += step;
//    }
//    //结尾部分
//    quint32 endLen = len % step;
//    if( endLen != 0 ){
//        addr += len - endLen;
//        mSaveWordLen = endLen / 4;
//        cmd = QString( "cmd dump_flash 0x%1 0 %2\n" ).arg( QString::number(addr,16) ).arg( endLen );
//        //cmd = "cmd dump_flash 0x1C7100 0 224\n";
//        emit sigSendCmds(id,cmd);
//        wait( SLEEP_TIME_MS );
//    }
//    closeSaveCsv();
}
