﻿#include "daqmanager.h"

#include "daqcontrol.h"
#include "debugcontrolform.h"

#include <QThread>
#include <QDebug>

DAQManager::DAQManager(Analysis::AnalysisManager *analysisManager, QObject *parent)
    : QObject(parent)
{

}

DAQManager::~DAQManager()
{
    if (mDAQControl)
    {
        delete mDAQControl;
    }

    if (mDebugControlForm)
    {
        delete mDebugControlForm;
    }
}

DAQControl *DAQManager::daqControl(Devices::Can::DeviceManager *deviceManager,
                                   Analysis::AnalysisManager *analysisManager,
                                   QWidget *parent)
{
    if (!mDAQControl)
    {
        mDAQControl = new DAQControl(deviceManager, analysisManager, parent);

        connect(mDAQControl, &DAQControl::run, this, &DAQManager::run);
        connect(mDAQControl, &DAQControl::startSave, this, &DAQManager::startSave);
        connect(mDAQControl, &DAQControl::stopSave, this, &DAQManager::stopSave);
        connect(this, &DAQManager::saveStarted, mDAQControl, &DAQControl::saveStarted);

        connect(mDAQControl, &DAQControl::destroyed, this, [=](){
            mDAQControl = 0;
        });
    }

    return mDAQControl;
}

DebugControlForm *DAQManager::debugControl( Views::ViewsManager* pViewManager, Core::SaveManager* saveManager, Devices::Can::DeviceManager *deviceManager, Analysis::AnalysisManager *analysisManager, QWidget *parent)
{
    if (!mDebugControlForm)
    {
        mDebugControlForm = new DebugControlForm( pViewManager, saveManager, deviceManager, analysisManager, parent);

        connect(mDebugControlForm, &DAQControl::destroyed, this, [=](){
            mDebugControlForm = 0;
        });
    }

    return mDebugControlForm;
}

void DAQManager::saveSettings()
{
    if (mDAQControl) {
        mDAQControl->saveSettings();
    }

    if (mDebugControlForm) {
        mDebugControlForm->saveSettings();
    }
}
