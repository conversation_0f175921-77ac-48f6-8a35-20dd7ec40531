﻿/**
 * @file adas_state_machine.c
 * @brief 
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2022-09-30
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-09-30 <td>1.0     <td>shaowei     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifdef ALPSPRO_ADAS
#include <math.h>
#include "adas_state_machine.h"
#include "vdy/vdy_interface.h"
#include "embARC_debug.h"
#include "vehicle_cfg.h"
#include "prot_vcan_byd.h"
#include "dbg/data_hil_det_trk.h"
#include "adas/generalalg/adas_manager.h"
#include "app_vehicle.h"
#include "cfg.h"
#elif defined(PC_DBG_FW)
#include <stdbool.h>
#include "other/temp.h"
// #include "app/protocol/prot_vcan_byd.h"
#include "app/vehicle/vdy/vdy_interface.h"
#include "app/dbg/data_hil_det_trk.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#else
#include <math.h>
#include "adas_state_machine.h"
#include "app/cfg/cfg.h"
#include "app/vdy/vdy_interface.h"
#include "common/include/vehicle_cfg.h"
#include "app/can_protocol/app_vehicle.h"
#include "app/can_protocol/prot_vcan_byd.h"
#include "app/dbg/data_hil_det_trk.h"
#include "app/adas/generalalg/adas_manager.h"
#endif

/*!>****** 宏定义 *******/
// 20221122_Will+ 控制启用代码块,确定之后删除
#define DEBUG_FUNC_CLOSE	(0) 

static uint32_t debugTimeOfPrintf = 0;
static uint32_t debugTimeOfPrintf_Old = 0;
//static uint32_t debugTimeOfPrintfSignal = 0;
#define TO_STR(str)	(#str)
static enum {
	LCA,
	BSD,
	DOW,
	RCW,
	RCTA,
	RCTB,
	FCTA,
	FCTB
} pollingObj = LCA; 

void setTimestampOfASM(void)
{
	debugTimeOfPrintf++;
}

static uint32_t getTimestampOfASM(void)
{
	return debugTimeOfPrintf;
}

static void outputFuncFSM(char str[14], uint8_t objFunc, uint8_t swtReq, float spd, uint8_t gear)
{ 
	if (getTimestampOfASM() != debugTimeOfPrintf_Old)
	{
		debugTimeOfPrintf_Old = getTimestampOfASM(); 

		// EMBARC_PRINTF(">>> %s(), objFunc: %s, %d, %d, %0.2f, %d\n", __FUNCTION__, str,
		// 			  objFunc, swtReq, spd, gear);
		pollingObj++;
		if (pollingObj > RCTA)
		{
			pollingObj = LCA;
		}
	}
}

/*static void outputFuncFSMSignal(char str[14], uint8_t objFunc, uint8_t swtReq, float spd, uint8_t gear)
{
	if (getTimestampOfASM() != debugTimeOfPrintfSignal)
	{
		debugTimeOfPrintfSignal = getTimestampOfASM();

		EMBARC_PRINTF(">>> %s(), objFunc: %s, %d, %d, %0.2f, %d\n", __FUNCTION__, str,
					  objFunc, swtReq, spd, gear);
	}
}*/

/**
 * @brief 相关模式抑制FCTB/RCTB功能
 * @param none
 * @return true:功能被抑制 false:功能不被抑制 
 */
/*static void ADAS_funInhibitionCTB(void)
{
	if (getTimestampOfASM() != debugTimeOfPrintfSignal)
	{
		debugTimeOfPrintfSignal = getTimestampOfASM();

		// EMBARC_PRINTF(">>> %s(), objFunc: %s, %d, %d, %0.2f, %d\n", __FUNCTION__, str,
		// 			  objFunc, swtReq, spd, gear);
	}
}*/

/**
 * @brief LCA状态机
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pVDY->pVDY_ChassisInfo 底盘输入信息地址
 * @param pVDY->pVDY_StaticInfo 车辆静态信息
 * @param pVDY->pVDY_VehicleFuncSwtInfo adas功能开关地址
 */
static void judgeLCAFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY)
{ 
	float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
	uint8_t MrrSystemState = pobjAlm->MrrSystemState; 
	static uint8_t laststate = 0;	// 状态切换标志.
    bool StandbyNotEn = true;        // 智能休眠模式抑制功能. 默认为true 不抑制
    bool UltimateModeNotEn = true;    // 极限续航模式抑制功能. 默认为true 不抑制
    bool DragModeNotEn = true;        // 拖车模式抑制功能, 默认为true 不抑制

    if (ADAS_isStandbyeMode())      // 智能休眠模式
    {
        StandbyNotEn = false;
    }
    if (ADAS_isUltimateEnduranceMode())     // 极限续航模式
    {
        UltimateModeNotEn = false;
    }
    if (1 == Vehicle_getDragMode())         // 进入拖车模式
    {
        DragModeNotEn = false;
    }

	// 现在只有四种状态(不包括START)，四中状态机的优先级：关闭＞故障＞激活=待机
	enum
	{
		START = 0,		  // 跃迁初始态
		LCA_FUNC_OPEN = 1,	  // 1、LCA功能开启
		LCA_MRR_NO_FAULT = 2, // 2、雷达没有故障
		LCA_PASSIVE = 3,	  // 3、Passive待机状态，4、Active状态
	} s_tState = START;

	if (pollingObj == LCA)
	{
		outputFuncFSM(TO_STR(LCA), gadasFunctionState.adasLCAFuncState,
					  pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.LCASwtReq, BSDVelSpeedVal,
					  pVDY->pVDY_DynamicInfo->vdyGearState);
	}

	switch (s_tState)
	{
		case START:
		{
			s_tState++;
		}
		case LCA_FUNC_OPEN:
		{
			if (pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.LCASwtReq == 0U)
			{
				gadasFunctionState.adasLCAFuncState = (uint8_t)LCA_FUNC_STATE_OFF;
				break; 
			}
			else
			{
				s_tState++;
			}
		}
		case LCA_MRR_NO_FAULT:
		{
			if ((uint8_t)DTC_SYSERROR_NULL != MrrSystemState)
			{
				gadasFunctionState.adasLCAFuncState = (uint8_t)LCA_FUNC_STATE_ERROR;
				break;
			}
			else
			{
				s_tState++;
			}
		}
		case LCA_PASSIVE:
		{
            if (
                (StandbyNotEn) && (UltimateModeNotEn) && (DragModeNotEn) &&
                (pVDY->pVDY_DynamicInfo->vdyGearState != (uint8_t)GEAR_SIG_R) && /*非倒挡*/
                //(pVDY->pVDY_ChassisInfo->drivingMode != DRIVING_MODE_TOWING) &&   /* 驾驶模式非拖挂模式 */
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.ESPDiagActv == 0) &&        /*esp 诊断结果正常*/
                (pVDY->pVDY_ChassisInfo->raceModeStatus == 0) &&                  /*赛道模式关闭*/
                (pVDY->pVDY_StaticInfo->vdyKeyState == (uint8_t)vdyKeyStateOn)            /*电源模式ON*/
// #if defined(VEHICLE_TYPE_BYD_SONG_SF_5R5V)       // 这里是从低阶同步过来的代码  高阶按照实际需求确认
//                 && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 1) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 2))  /* 极致转向关闭 0x147  */
//                 && (pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 1 && pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 2) /* 悬架调节抑制刹车 0x1A7 */
// #endif
			){
                /**
                 * @brief 如果上一个状态为Active，那车速在(11.8~151)满足条件下依然是激活状态，
                 *  如果非Active，车速需在(13.6~146)才能进入Active 
                 */
                if (gadasFunctionState.adasLCAFuncState == LCA_FUNC_STATE_ACTIVE)
				{ 
					if ((BSDVelSpeedVal > LCA_MIN_BSD_SPD_LOWER) && (BSDVelSpeedVal < LCA_MAX_BSD_SPD_UPPER))
					{
						gadasFunctionState.adasLCAFuncState = LCA_FUNC_STATE_ACTIVE;
					}
					else
					{
						gadasFunctionState.adasLCAFuncState = LCA_FUNC_STATE_PASSIVE;
					}
				}
				else
				{
					if ((BSDVelSpeedVal >= LCA_MIN_BSD_SPD_UPPER) && (BSDVelSpeedVal <= LCA_MAX_BSD_SPD_LOWER))
					{
						gadasFunctionState.adasLCAFuncState = LCA_FUNC_STATE_ACTIVE;
					}
					else
					{
						gadasFunctionState.adasLCAFuncState = LCA_FUNC_STATE_PASSIVE;
					}
				}
            }
            else
			{
				gadasFunctionState.adasLCAFuncState = LCA_FUNC_STATE_PASSIVE;
			}
			break;
		}
		default:
		{
			break;
		}
	}
    // 状态切换回active时,清理一次数据.避免误报.
    if (((uint8_t)LCA_FUNC_STATE_ACTIVE == gadasFunctionState.adasLCAFuncState) && (laststate != (uint8_t)LCA_FUNC_STATE_ACTIVE))
    {
        ADAS_clrAlmState(pobjAlm->pobjPath, (uint32_t)ALARM_ACTIVE_LCA);
    }
    laststate = gadasFunctionState.adasLCAFuncState;
}


/**
 * @brief BSD状态机
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pVDY->pVDY_ChassisInfo 底盘输入信息地址
 * @param pVDY->pVDY_StaticInfo 车辆静态信息
 * @param pVDY->pVDY_VehicleFuncSwtInfo adas功能开关地址
 */


#define BSD_MULTIPLE_SPEED_JUDEG 1	// 如果为1需要判断速度是否满足区间[11.8~13.6]~[146~151]

static void judgeBSDFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY)
{
    float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
    uint8_t MrrSystemState = pobjAlm->MrrSystemState;
    static uint8_t laststate = 0;	// 状态切换标志.
    bool StandbyNotEn = true;        // 智能休眠模式抑制功能. 默认为true 不抑制
    bool UltimateModeNotEn = true;    // 极限续航模式抑制功能. 默认为true 不抑制
    bool DragModeNotEn = true;        // 拖车模式抑制功能, 默认为true 不抑制

    if (ADAS_isStandbyeMode())      // 智能休眠模式
    {
        StandbyNotEn = false;
    }
    if (ADAS_isUltimateEnduranceMode())     // 极限续航模式
    {
        UltimateModeNotEn = false;
    }
    if (1 == Vehicle_getDragMode())         // 进入拖车模式
    {
        DragModeNotEn = false;
    }
	// 现在只有四种状态(不包括START)
	enum
	{
		START = 0,		  // 跃迁初始态
		BSD_FUNC_OPEN = 1,	  // 1、BSD功能开启
		BSD_MRR_NO_FAULT = 2, // 2、雷达没有故障
		BSD_PASSIVE = 3,	  // 3、Passive待机状态，4、Active状态
	} s_tState = START;

	if (pollingObj == BSD)
	{
			outputFuncFSM(TO_STR(BSD), gadasFunctionState.adasBSDFuncState,
						  pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.BSDSwtReq, BSDVelSpeedVal,
						  pVDY->pVDY_DynamicInfo->vdyGearState);
	}

	switch (s_tState)
	{
        case START:
        {
            s_tState++;
        }
        case BSD_FUNC_OPEN:
        {
            if (pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.BSDSwtReq == 0U)
            {
                gadasFunctionState.adasBSDFuncState = (uint8_t)BSD_FUNC_STATE_OFF;
                break;
            }
            else
            {
                s_tState++;
            }
        }
        case BSD_MRR_NO_FAULT:
        {
            if (MrrSystemState != (uint8_t)DTC_SYSERROR_NULL)
            {
                gadasFunctionState.adasBSDFuncState = (uint8_t)BSD_FUNC_STATE_ERROR;
                break;
            }
            else
            {
                s_tState++;
            }
        }
        case BSD_PASSIVE:
        {
            if (
                (StandbyNotEn) && (UltimateModeNotEn) && (DragModeNotEn) &&
                (pVDY->pVDY_DynamicInfo->vdyGearState != GEAR_SIG_R) && /*非倒挡*/
                //(pVDY->pVDY_ChassisInfo->drivingMode != DRIVING_MODE_TOWING) &&   /* 驾驶模式非拖挂模式 */
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.ESPDiagActv == 0U) &&        /*esp 诊断结果正常*/
                (pVDY->pVDY_ChassisInfo->raceModeStatus == 0) &&                  /*赛道模式关闭*/
                (pVDY->pVDY_StaticInfo->vdyKeyState == (uint8_t)vdyKeyStateOn)            /*电源模式ON*/
// #if defined(VEHICLE_TYPE_BYD_SONG_SF_5R5V)
//                 && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 1) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 2))     /* 极致转向关闭 0x147  */
//                 && (pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 1 && pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 2) /* 悬架调节抑制刹车 0x1A7 */
// #endif
            )
            {
                /**
                 * @brief 如果上一次状态为active，本次判断，速度为11.8~13.6或者146~151也应该是active
                 *  如果上一次状态为非active，那本次速度需要满足大于13.6和小于146
                 */
                if (gadasFunctionState.adasBSDFuncState == BSD_FUNC_STATE_ACTIVE)
                {
                    if ((BSDVelSpeedVal > BSD_MIN_BSD_SPD_LOWER) && (BSDVelSpeedVal < BSD_MAX_BSD_SPD_UPPER)) /*自车车速大于11.8km/h 小于151km/h*/
                    {
                        gadasFunctionState.adasBSDFuncState = BSD_FUNC_STATE_ACTIVE;
                    }
                    else
                    {
                        gadasFunctionState.adasBSDFuncState = BSD_FUNC_STATE_PASSIVE;
                    }
                }
                else
                {
                    if ((BSDVelSpeedVal > BSD_MIN_BSD_SPD_UPPER) && (BSDVelSpeedVal <= BSD_MAX_BSD_SPD_LOWER)) /*自车车速大于13.6km/h 小于146km/h*/
                    {
                        gadasFunctionState.adasBSDFuncState = BSD_FUNC_STATE_ACTIVE;
                    }
                    else
                    {
                        gadasFunctionState.adasBSDFuncState = BSD_FUNC_STATE_PASSIVE;
                    }
                }
            }
            else
            {
                gadasFunctionState.adasBSDFuncState = BSD_FUNC_STATE_PASSIVE;
            }
            break;
        }
        default:
        {
            break;
        }
	}
    // 状态切换回active时,清理一次数据.避免误报.
    if (((uint8_t)BSD_FUNC_STATE_ACTIVE == gadasFunctionState.adasBSDFuncState) && (laststate != (uint8_t)BSD_FUNC_STATE_ACTIVE))
    {
        ADAS_clrAlmState(pobjAlm->pobjPath, (uint32_t)ALARM_ACTIVE_BSD);
    }
    laststate = gadasFunctionState.adasBSDFuncState;
}


/**
 * @brief RCTA状态机
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pVDY->pVDY_ChassisInfo 底盘输入信息地址
 * @param pVDY->pVDY_StaticInfo 车辆静态信息
 * @param pVDY->pVDY_VehicleFuncSwtInfo adas功能开关地址
 */
static void judgeRCTAFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY)
{
	float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
	uint8_t MrrSystemState = pobjAlm->MrrSystemState;
    bool StandbyNotEn = true;        // 智能休眠模式抑制功能. 默认为true 不抑制
    bool UltimateModeNotEn = true;    // 极限续航模式抑制功能. 默认为true 不抑制
    bool DragModeNotEn = true;        // 拖车模式抑制功能, 默认为true 不抑制

    if (ADAS_isStandbyeMode())      // 智能休眠模式
    {
        StandbyNotEn = false;
    }
    if (ADAS_isUltimateEnduranceMode())     // 极限续航模式
    {
        UltimateModeNotEn = false;
    }
    if (1 == Vehicle_getDragMode())         // 进入拖车模式
    {
        DragModeNotEn = false;
    }
	// 现在只有四种状态(不包括START)，四中状态机的优先级：关闭＞故障＞激活=待机
	enum
	{ 
		START = 0,		   // 跃迁初始态
		RCTA_FUNC_OPEN = 1,	   // 1、RCTA功能开启
		RCTA_MRR_NO_FAULT = 2, // 2、雷达没有故障
		RCTA_PASSIVE = 3,	   // 3、Passive待机状态，4、Active状态
	} s_tState = START; 

	if (pollingObj == RCTA)
	{
		outputFuncFSM(TO_STR(RCTA), gadasFunctionState.adasRCTAFuncState,
					  pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.RCTASwtReq, BSDVelSpeedVal,
					  pVDY->pVDY_DynamicInfo->vdyGearState);
	}

	switch (s_tState) 
	{
        case START:
        {
            s_tState++; 
        }
		case RCTA_FUNC_OPEN:
		{ 
			if (pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.RCTASwtReq == 0U)
			{
				gadasFunctionState.adasRCTAFuncState = (uint8_t)RCTA_FUNC_STATE_OFF;
				break;
			}
			else
        	{
                s_tState++;
        	} 
        }
		case RCTA_MRR_NO_FAULT:
		{
			if (MrrSystemState != (uint8_t)DTC_SYSERROR_NULL)
			{
				gadasFunctionState.adasRCTAFuncState = (uint8_t)RCTA_FUNC_STATE_ERROR;
				break;
			}
			else
        	{
                s_tState++;
        	}
        }
        case RCTA_PASSIVE:
        {
            if (
                (StandbyNotEn) && (UltimateModeNotEn) && (DragModeNotEn) &&
                (BSDVelSpeedVal <= 0 && BSDVelSpeedVal > (-RCTA_ACTIVE_MAX_SPEED)) && /*自车车速（-15,0]，这里的车速变为负值*/
                (pVDY->pVDY_DynamicInfo->vdyGearState == (uint8_t)GEAR_SIG_R) &&				  /*挂倒挡*/
                //(pVDY->pVDY_ChassisInfo->drivingMode != DRIVING_MODE_TOWING) &&   /* 驾驶模式非拖挂模式 */
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.ESPDiagActv == 0U) &&					  /*esp 诊断结果正常*/
                (pVDY->pVDY_ChassisInfo->raceModeStatus == 0) &&		/*赛道模式关闭*/
                (pVDY->pVDY_StaticInfo->vdyKeyState == (uint8_t)vdyKeyStateOn)						  /*电源模式ON*/
// #if defined(VEHICLE_TYPE_BYD_SONG_SF_5R5V)
//                 && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 1) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 2))     /* 极致转向关闭 0x147  */
//                 && (pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 1 && pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 2) /* 悬架调节抑制刹车 0x1A7 */
// #endif
            )
            {
                gadasFunctionState.adasRCTAFuncState = (uint8_t)RCTA_FUNC_STATE_ACTIVE;
            }
            else
            {
                gadasFunctionState.adasRCTAFuncState = (uint8_t)RCTA_FUNC_STATE_PASSIVE;
            }
            break;
        }
        default: 
        {
            break;
        }
	}
}


/**
 * @brief RCTB状态机
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pVDY->pVDY_ChassisInfo 底盘输入信息地址
 * @param pVDY->pVDY_StaticInfo 车辆静态信息
 * @param pVDY->pVDY_VehicleFuncSwtInfo adas功能开关地址
 */
static void judgeRCTBFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY)
{
    uint8_t MrrSystemState = pobjAlm->MrrSystemState;
    float   BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal; /* in km/h */
    //bool    rctb_vehicle_flag = false; /* 自车车速是否符合rctb范围标志 */
    bool StandbyNotEn = true;        // 智能休眠模式抑制功能. 默认为true 不抑制
    bool UltimateModeNotEn = true;    // 极限续航模式抑制功能. 默认为true 不抑制
    bool SnowIceNotEn = true;         // 雪地模式抑制功能  默认位true 不抑制
    bool DragModeNotEn = true;        // 拖车模式抑制功能, 默认为true 不抑制
    bool DrivModeNotEn = true;          // 智驾模式抑制功能  默认为true 不抑制

    if (ADAS_isStandbyeMode())      // 智能休眠模式
    {
        StandbyNotEn = false;
    }
    if (ADAS_isUltimateEnduranceMode())     // 极限续航模式
    {
        UltimateModeNotEn = false;
    }
    if (1 == Vehicle_getDragMode())         // 进入拖车模式
    {
        DragModeNotEn = false;
    }
    if (ADAS_isSnowIceGrassGravelMode())    // 雪地模式
    {
        SnowIceNotEn = false;
    }
    if (ADAS_isRestrainRCTB())              // 智驾模式
    {
        DrivModeNotEn = false;          
    }

    enum{
        START = 0,            /* 跃迁初始态 */
        RCTB_FUNC_OPEN = 1,       /* RCTB功能开启 */
        RCTB_MRR_NO_FAULT = 2,    /* 雷达没有故障 */
        RCTB_PASSIVE = 3,          /* RCTB待机状态， 若满足激活条件，RCTB在此状态进入激活状态 */
    } s_tState = START;

    if(pollingObj == RCTB)
    {
        outputFuncFSM(TO_STR(RCTB), gadasFunctionState.adasRCTBFuncState,
        pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.RCTABrkSwtReq, BSDVelSpeedVal,
        pVDY->pVDY_DynamicInfo->vdyGearState); 
    }

    switch(s_tState)
    {
        case START:
        {
            s_tState++;
        }
        case RCTB_FUNC_OPEN:
        {
            if(pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.RCTABrkSwtReq == 0U)
            {
                 gadasFunctionState.adasRCTBFuncState = (uint8_t)RCTB_FUNC_STATE_OFF;
                 break;
            }
            else
            {
                s_tState++;
            }
        }
        case RCTB_MRR_NO_FAULT:
        {
            if(MrrSystemState != (uint8_t)DTC_SYSERROR_NULL)
            {
                gadasFunctionState.adasRCTBFuncState = (uint8_t)RCTB_FUNC_STATE_ERROR;
                break;
            }
            else
            {
                s_tState++;
            }
        }
        case RCTB_PASSIVE:
        {
            if (
                (StandbyNotEn) && (UltimateModeNotEn) && (SnowIceNotEn) && (DragModeNotEn) && (DrivModeNotEn) &&
                (pVDY->pVDY_DynamicInfo->vdyGearState == (uint8_t)GEAR_SIG_R) &&    /* RCTB 功能在R档位 */
                (pVDY->pVDY_ChassisInfo->vdyAccelerograph_Depth <= 85) &&			/* 油门踏板开度小于85% 0x342 */
                ((pVDY->pVDY_StaticInfo->comApaAutoParkingState != 2) &&            /* 0x134 自动泊车  2 3 4时 不激活 */
                (pVDY->pVDY_StaticInfo->comApaAutoParkingState != 3) &&
                (pVDY->pVDY_StaticInfo->comApaAutoParkingState != 4) &&								
                (pVDY->pVDY_StaticInfo->comApaAutoParkingState != 5)) &&            /* 4、APA功能激活判断, ID: 0x134, 信号：APA_Auto_Parking_Status_S， 非0，1，5，6，7抑制 */
                // (pVDY->pVDY_ChassisInfo->AEB_BRK_TQ == 1) &&                                            // 0x10C 制动可用 A3无此描述
                //(pVDY->pVDY_ChassisInfo->vdyEspStatus.TCSActiveSts == 0U) &&							    /* 7、TCS激活判断, ID: 0x222， 信号: TCS_Active_222_S， 1抑制 */  a4规范不判断
                //(pVDY->pVDY_ChassisInfo->vdyEspStatus.VDCActiveSts == 0U) &&							    /* 8、VDC激活判断, ID: 0x222， 信号: VDC_Active_222_S， 1抑制 */  a4规范不判断
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.espOffStatus == 0U) &&								/* 9、ESP 系统开启 0x123 */
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.standStill == 0U) 								/* 6、自车静止判断 , 非静止 0x220 */
                //(pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 1 && pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 2)  /* 悬架调节抑制刹车 0x1A7 待需求确认  */
// #if defined(VEHICLE_TYPE_BYD_SONG_SF_5R5V)
//                 && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 1) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 2))		  /* 极致转向关闭  0x147*/
// #endif				
            )
            {
                /**
                 * @brief 如果上一次是激活状态,车速在(-10， 0)km/h
                 *  如果是第一次报警，车速在[-9, -0.72]km/h才有效
                 */
                if (gadasFunctionState.adasRCTBFuncState == RCTB_FUNC_STATE_ACTIVE)
                {
                    /* 车速在 (-10， 0)km/h仍然有效 */
                    if((BSDVelSpeedVal > -(RCTB_MAX_SPD_UPPER)) && (BSDVelSpeedVal <= (RCTB_MIN_SPD_LOWER)))
                    {
                        gadasFunctionState.adasRCTBFuncState = RCTB_FUNC_STATE_ACTIVE; 
                    }
                    else
                    {
                        gadasFunctionState.adasRCTBFuncState = RCTB_FUNC_STATE_PASSIVE; 
                    }
                }
                else
                {
                    if((BSDVelSpeedVal >= -(RCTB_MAX_SPD_LOWER)) && (BSDVelSpeedVal <= -(RCTB_MIN_SPD_UPPER)))
                    {
                        gadasFunctionState.adasRCTBFuncState = RCTB_FUNC_STATE_ACTIVE; 
                    }
                    else
                    {
                        gadasFunctionState.adasRCTBFuncState = RCTB_FUNC_STATE_PASSIVE; 
                    }
                }
            }
            else
            {
                gadasFunctionState.adasRCTBFuncState = RCTB_FUNC_STATE_PASSIVE; 
            }
            break;
        }
        default:
        {
            break;
        }
    }
}


/**
 * @brief FCTA状态机
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pVDY->pVDY_ChassisInfo 底盘输入信息地址
 * @param pVDY->pVDY_StaticInfo 车辆静态信息
 * @param pVDY->pVDY_VehicleFuncSwtInfo adas功能开关地址
 */
static void judgeFCTAFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY)
{
	float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
	uint8_t MrrSystemState = pobjAlm->MrrSystemState;
    static uint8_t laststate = 0;	// 状态切换标志.
    bool StandbyNotEn = true;        // 智能休眠模式抑制功能. 默认为true 不抑制
    bool UltimateModeNotEn = true;    // 极限续航模式抑制功能. 默认为true 不抑制

    if (ADAS_isStandbyeMode())      // 智能休眠模式
    {
        StandbyNotEn = false;
    }
    if (ADAS_isUltimateEnduranceMode())     // 极限续航模式
    {
        UltimateModeNotEn = false;
    }
	// 现在只有四种状态(不包括START)，四中状态机的优先级：关闭＞故障＞激活=待机
	enum
	{ 
		START = 0,		   // 跃迁初始态
		FCTA_FUNC_OPEN = 1,	   // 1、FCTA功能开启
		FCTA_MRR_NO_FAULT = 2, // 2、雷达没有故障
		FCTA_PASSIVE = 3,	   // 3、Passive待机状态，4、Active状态
	} s_tState = START; 

	if (pollingObj == FCTA)
	{
		// outputFuncFSM(TO_STR(FCTA), gadasFunctionState.adasFCTAFuncState,
		// 			  pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.FCTASwtReq, BSDVelSpeedVal,
		// 			  pVDY->pVDY_DynamicInfo->vdyGearState);
	}

	switch (s_tState) 
	{
        case START:
        {
            s_tState++; 
        }
		case FCTA_FUNC_OPEN:
		{ 
			if (pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.FCTASwtReq == 0U)  //目前没有FCTA和FCTB的使能开关
			{
				gadasFunctionState.adasFCTAFuncState = (uint8_t)FCTA_FUNC_STATE_OFF;
				break;
			}
			else
        	{
                s_tState++;
        	} 
        }
		case FCTA_MRR_NO_FAULT:
		{
			if (MrrSystemState != (uint8_t)DTC_SYSERROR_NULL)
			{
				gadasFunctionState.adasFCTAFuncState = (uint8_t)FCTA_FUNC_STATE_ERROR;
				break;
			}
			else
        	{
                s_tState++;
        	}
        }
		case FCTA_PASSIVE:
		{
            uint8_t doorclose = ((pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorFrontLe |
                                pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorFrontRi |
                                pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorRearLe |
                                pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorRearRi));
            if (
                (StandbyNotEn) && (UltimateModeNotEn) &&
                //(pVDY->pVDY_ChassisInfo->raceModeStatus == 0) &&		/*赛道模式关闭*/
                (pVDY->pVDY_DynamicInfo->vdyGearState == (uint8_t)GEAR_SIG_D) &&				  /*挂D挡*/
                (doorclose == 0U) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.formerHatch == 1U) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorBack == 1U)&&												  /*四个门关闭，前后盖子关闭*/
                (BSDVelSpeedVal <= FCTA_ACTIVE_MAX_SPEED && BSDVelSpeedVal > FCTA_ACTIVE_MIN_SPEED) && /*自车车速[10,25]，*/
                (pVDY->pVDY_StaticInfo->vdyKeyState == (uint8_t)vdyKeyStateOn)
// #if defined(VEHICLE_TYPE_BYD_SONG_SF_5R5V)
//                 && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 1) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 2))     /* 极致转向关闭 0x147  */
//                 && (pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 1 && pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 2) /* 悬架调节抑制刹车 0x1A7 */
// #endif
            )
            {
                gadasFunctionState.adasFCTAFuncState = (uint8_t)FCTA_FUNC_STATE_ACTIVE;
            }
            else
            {
                gadasFunctionState.adasFCTAFuncState = (uint8_t)FCTA_FUNC_STATE_PASSIVE;
            }
            break;
        }
        default: 
        {
            break;
        }
	} 

    // 状态切换回active时,清理一次数据.避免误报.
    if (((uint8_t)FCTA_FUNC_STATE_ACTIVE == gadasFunctionState.adasFCTBFuncState) && (laststate != (uint8_t)FCTA_FUNC_STATE_ACTIVE))
    {
        ADAS_clrAlmState(pobjAlm->pobjPath, (uint32_t)ALARM_ACTIVE_FCTA);
    }
    laststate = gadasFunctionState.adasFCTAFuncState;       
}

/**
 * @brief FCTB状态机
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pVDY->pVDY_ChassisInfo 底盘输入信息地址
 * @param pVDY->pVDY_StaticInfo 车辆静态信息
 * @param pVDY->pVDY_VehicleFuncSwtInfo adas功能开关地址
 */
#define FCTB_MULTIPLE_STREERING_ANGLE_JUDEG 1 // 如果为1需要判断方向盘是否满足区间[0~20)~[0~60)
static void judgeFCTBFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY)
{
	float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
	uint8_t MrrSystemState = pobjAlm->MrrSystemState;
    static uint8_t laststate = 0;	// 状态切换标志.
    bool StandbyNotEn = true;        // 智能休眠模式抑制功能. 默认为true 不抑制
    bool UltimateModeNotEn = true;    // 极限续航模式抑制功能. 默认为true 不抑制
    bool SnowIceNotEn = true;         // 雪地模式抑制功能  默认位true 不抑制
    bool DragModeNotEn = true;        // 拖车模式抑制功能, 默认为true 不抑制
    bool DrivModeNotEn = true;          // 智驾模式抑制功能  默认为true 不抑制

    if (ADAS_isStandbyeMode())      // 智能休眠模式
    {
        StandbyNotEn = false;
    }
    if (ADAS_isUltimateEnduranceMode())     // 极限续航模式
    {
        UltimateModeNotEn = false;
    }
    if (1 == Vehicle_getDragMode())         // 进入拖车模式
    {
        DragModeNotEn = false;
    }
    if (ADAS_isSnowIceGrassGravelMode())    // 雪地模式
    {
        SnowIceNotEn = false;
    }
    if (ADAS_isRestrainFCTB() || ADAS_isInterruptFCTB())              // 智驾模式
    {
        DrivModeNotEn = false;
    }

	// 现在只有四种状态(不包括START)，四中状态机的优先级：关闭＞故障＞激活=待机
	enum
	{ 
		START = 0,		   // 跃迁初始态
		FCTB_FUNC_OPEN = 1,	   // 1、FCTB功能开启
		FCTB_MRR_NO_FAULT = 2, // 2、雷达没有故障
		FCTB_PASSIVE = 3,	   // 3、Passive待机状态，4、Active状态
	} s_tState = START; 

	if (pollingObj == FCTB)
	{
		// outputFuncFSM(TO_STR(FCTB), gadasFunctionState.adasFCTBFuncState,
		// 			  pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.FCTABrkSwtReq, BSDVelSpeedVal,
		// 			  pVDY->pVDY_DynamicInfo->vdyGearState);
	}
	switch (s_tState) 
	{
        case START:
        {
            s_tState++; 
        }
		case FCTB_FUNC_OPEN:
		{ 
			 if (pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.FCTABrkSwtReq == 0U)
			 {
			 	gadasFunctionState.adasFCTBFuncState = (uint8_t)FCTB_FUNC_STATE_OFF;
			 	break;
			 }
			 else
        	{
                s_tState++;
        	} 
        }
		case FCTB_MRR_NO_FAULT:
		{
			if (MrrSystemState != (uint8_t)DTC_SYSERROR_NULL)
			{
				gadasFunctionState.adasFCTBFuncState = (uint8_t)FCTB_FUNC_STATE_ERROR;
				break;
			}
			else
        	{
                s_tState++;
        	}
        }
		case FCTB_PASSIVE:
		{
			uint8_t doorclose = ((pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorFrontLe |
								  pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorFrontRi |
								  pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorRearLe |
								  pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorRearRi));

            if (
                (StandbyNotEn) && (UltimateModeNotEn) && (SnowIceNotEn) && (DragModeNotEn) && (DrivModeNotEn) &&
                (pVDY->pVDY_DynamicInfo->vdyGearState == (uint8_t)GEAR_SIG_D) && /*挂D挡 0x342*/
                (doorclose == 0U) &&
                (pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.formerHatch == 1U) &&
                (pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorBack == 1U) && /*四个门关闭，前后盖子关闭 0x294*/
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.espOffStatus == 0U) &&         /*esp 功能开启 0x123*/
                (((fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngleSpeed) < 100) &&    // 方向盘转动角速度小于100 0X11F
                  (fabs(pVDY->pVDY_DynamicInfo->vdySteeringAngle) < 60)) ||          // 方向盘转角小于60度 0x11F
                 ((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) &&
                  (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME))) &&      /* 检测到CPTA右拐场景，放开限制 */
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.HDCCtrl != 2U) &&              // HDC状态为2 抑制  0X123
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.TCSActiveSts == 0U) &&         // TCS功能未激活 0x222
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.VDCActiveSts == 0U) &&         // vdc功能未激活  0x222
                (pVDY->pVDY_ChassisInfo->vdyAccelerograph_Depth <= 80.0f) &&         /* 加速踏板开度不大于80 0x342 */
                ((BSDVelSpeedVal <= FCTB_ACTIVE_MAX_SPEED) &&
                 (BSDVelSpeedVal > FCTB_ACTIVE_MIN_SPEED)) &&                        /* 自车车速[0,25]，*/
                // ((pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 1) &&
                // (pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 2)) &&            /* 0x1A7 悬架抑制 待需求确认*/
                (pVDY->pVDY_ChassisInfo->AEB_BRK_TQ == 1) && // 0x10C 制动可用
                (pVDY->pVDY_StaticInfo->vdyKeyState == (uint8_t)vdyKeyStateOn) &&
                ((pVDY->pVDY_StaticInfo->comApaAutoParkingState < 0x02) ||
                 (pVDY->pVDY_StaticInfo->comApaAutoParkingState > 0x05))             // 0x134 FCTA、FCTB 监测 APA 自动泊车状态，当 APA_Auto_Parking_Status_S==0x2~0x5时，FCTB 功能抑制。APA_Auto_Parking_Status_S 在任何信号位下，FCTA 功能都不被抑制
                // #if defined(VEHICLE_TYPE_BYD_SONG_SF_5R5V)
                //  && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 1) &&
                //      (pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 2))          /* 极致转向关闭 */
                // #endif
            )
            {
                gadasFunctionState.adasFCTBFuncState = (uint8_t)FCTB_FUNC_STATE_ACTIVE; 
                if (pobjAlm->adasFCTBactiveCnt < MAX_UCHAR){
                    pobjAlm->adasFCTBactiveCnt++;
                }
            }
            else
            {
                pobjAlm->adasFCTBactiveCnt = 0;
                gadasFunctionState.adasFCTBFuncState = (uint8_t)FCTB_FUNC_STATE_PASSIVE;
            }
        }
        default: 
        {
            break;
        }
	}

    // 状态切换回active时,清理一次数据.避免误报.
    if (((uint8_t)FCTB_FUNC_STATE_ACTIVE == gadasFunctionState.adasFCTBFuncState) && (laststate != (uint8_t)FCTB_FUNC_STATE_ACTIVE))
    {
        pobjAlm->adasFCTBactiveCnt = 0;
        ADAS_clrAlmState(pobjAlm->pobjPath, (uint32_t)ALARM_ACTIVE_FCTB);
    }
    laststate = gadasFunctionState.adasFCTBFuncState;    
}


/**
 * @brief DOW状态机
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pVDY->pVDY_ChassisInfo 底盘输入信息地址
 * @param pVDY->pVDY_StaticInfo 车辆静态信息
 * @param pVDY->pVDY_VehicleFuncSwtInfo adas功能开关地址
 */
static void judgeDOWFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY)
{
    float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
    uint8_t MrrSystemState = pobjAlm->MrrSystemState;
    static uint8_t laststate = 0;	// 状态切换标志.
    bool StandbyNotEn = true;        // 智能休眠模式抑制功能. 默认为true 不抑制
    bool UltimateModeNotEn = true;    // 极限续航模式抑制功能. 默认为true 不抑制
    bool DragModeNotEn = true;        // 拖车模式抑制功能, 默认为true 不抑制

    if (ADAS_isStandbyeMode())      // 智能休眠模式
    {
        StandbyNotEn = false;
    }
    if (ADAS_isUltimateEnduranceMode())     // 极限续航模式
    {
        UltimateModeNotEn = false;
    }
    if (1 == Vehicle_getDragMode())         // 进入拖车模式
    {
        DragModeNotEn = false;
    }
	// 现在只有四种状态(不包括START)，四中状态机的优先级：关闭＞故障＞激活=待机
	enum {
        START = 0,          // 跃迁初始态
        DOW_FUNC_OPEN = 1,      // 1、DOW功能开启
        DOW_MRR_NO_FAULT = 2,   ///2、雷达没有故障
        DOW_PASSIVE = 3,        ///3、Passive待机状态，4、Active状态
    } s_tState = START; 

	if (pollingObj == DOW)
	{
		outputFuncFSM(TO_STR(DOW), gadasFunctionState.adasDOWFuncState,
					  pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.DOWSwtReq, MrrSystemState,
					  pVDY->pVDY_DynamicInfo->vdyGearState);
	}

	switch (s_tState) 
	{
        case START:
        {
            s_tState++; 
        }
        case DOW_FUNC_OPEN:
        { 
			if (pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.DOWSwtReq == 0U) // 判断是否开启 0关闭，1打开
			{
        		gadasFunctionState.adasDOWFuncState = (uint8_t)DOW_FUNC_STATE_OFF;
        		break; 
        	}
        	else
        	{
                s_tState++;
        	}
			// EMBARC_PRINTF("%s, %d\n", __FUNCTION__, gadasFunctionState.adasDOWFuncState); 
            
        }
        case DOW_MRR_NO_FAULT: 
        {
            if(MrrSystemState != (uint8_t)DTC_SYSERROR_NULL)  // 1故障
        	{
        		gadasFunctionState.adasDOWFuncState = (uint8_t)DOW_FUNC_STATE_ERROR;
        		break;
        	}
            else
        	{
                s_tState++;
        	}
        }
        case DOW_PASSIVE:
        {
            uint8_t doorlock = 0; 
            uint8_t l_r = (((gADASRadarId % 2) == 0) ? BSD_RADAR_LEFT : BSD_RADAR_RIGHT);
            // 前后角状态融合, 前角故障时抑制后角功能, 后角故障时抑制前角功能

#if (1 == ALARM_TYPE_FDOW_EN)
            uint8_t fusion_state = 0; // 0 融合后不抑制对方    1  融合后抑制对方
            if ((gADASRadarId == RADAR_ID_REAR_LEFT) || (gADASRadarId == RADAR_ID_REAR_RIGHT))
            {
                // 前角雷达故障时, 后角DOW也置为故障
                fusion_state = ((FDOW_FUNC_STATE_ERROR == pVDY->pVDY_VehicleFuncSwtInfo->vdyFcrDowStatus) ? 1 : 0);
            }
            else
            {
                fusion_state = ((DOW_FUNC_STATE_ERROR == pVDY->pVDY_VehicleFuncSwtInfo->vdyRcrDowStatus) ? 1 : 0);
            }
#endif

            // DOW状态机需要关联到对应侧的雷达
             if (l_r == BSD_RADAR_LEFT){
                doorlock = (pVDY->pVDY_VehicleFuncSwtInfo->vdyLockStatus.bit.doorLockFrontLe | 
                            pVDY->pVDY_VehicleFuncSwtInfo->vdyLockStatus.bit.doorLockRearLe) % 2;
             }else{
                doorlock = (pVDY->pVDY_VehicleFuncSwtInfo->vdyLockStatus.bit.doorLockFrontRi | 
                            pVDY->pVDY_VehicleFuncSwtInfo->vdyLockStatus.bit.doorLockRearRi) % 2;                
             }         

            if (
                (StandbyNotEn) && (UltimateModeNotEn) && (DragModeNotEn) &&
#ifndef ADAS_FUNCTION_A3
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.standStill == 1U) && /*自车静止*/
#endif
                //(pVDY->pVDY_ChassisInfo->drivingMode != DRIVING_MODE_TOWING) &&   /* 驾驶模式非拖挂模式 */
                ((BSDVelSpeedVal <= DOW_ACTIVE_MAX_SPEED) && (BSDVelSpeedVal > DOW_ACTIVE_MIN_SPEED)) &&
                ((doorlock % 2U) != 0U) &&						/* 雷达对应侧车门至少有一个解锁 */
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.ESPDiagActv == 0U) &&		  	/*esp 诊断结果正常*/
                (pVDY->pVDY_ChassisInfo->raceModeStatus == 0) &&		/*赛道模式关闭*/
                (pVDY->pVDY_VehicleFuncSwtInfo->commonBit.igPowerDownTimeOfDOW != 0U) && 	/*IG下电3分钟之后*/
                (pVDY->pVDY_VehicleFuncSwtInfo->vdySecurtiyStatus == 0)				  	// 判断锁车 0x0:正常状态, 0x1:防盗设定状态, 0x2:防盗状态
#if (1 == ALARM_TYPE_FDOW_EN)
                && (!fusion_state)
#endif
// #if defined(VEHICLE_TYPE_BYD_SONG_SF_5R5V)
//                 && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 1) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 2))  /* 极致转向关闭 0x147  */
//                 && (pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 1 && pVDY->pVDY_ChassisInfo->vdySuspensionHeight != 2) /* 悬架调节抑制刹车 0x1A7 */
// #endif
            )
			{
        		gadasFunctionState.adasDOWFuncState = (uint8_t)DOW_FUNC_STATE_ACTIVE;
        	}
        	else
        	{
        		gadasFunctionState.adasDOWFuncState = (uint8_t)DOW_FUNC_STATE_PASSIVE;
        	}
			break;
		}
        default: 
        {
            break;
        }
	}
    // 状态切换回active时,清理一次数据.避免误报.
    if (((uint8_t)DOW_FUNC_STATE_ACTIVE == gadasFunctionState.adasDOWFuncState) && (laststate != (uint8_t)DOW_FUNC_STATE_ACTIVE))
    {
        ADAS_clrAlmState(pobjAlm->pobjPath, (uint32_t)ALARM_ACTIVE_DOW);
    }
    laststate = gadasFunctionState.adasDOWFuncState;
}



/**
 * @brief RCW状态机
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pVDY->pVDY_ChassisInfo 底盘输入信息地址
 * @param pVDY->pVDY_StaticInfo 车辆静态信息
 * @param pVDY->pVDY_VehicleFuncSwtInfo adas功能开关地址
 */
static void judgeRCWFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY)
{
    float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
    uint8_t MrrSystemState = pobjAlm->MrrSystemState;
    static uint8_t laststate = 0;	// 状态切换标志.
    float radiusTurningThreshold = RCW_OWN_MIN_RADIUS_TURNING_THRESHOLD;
    uint8_t turnSignalState = 0;	// 转向灯、双闪灯、危险告警灯点亮为点亮为active其一条件
    bool StandbyNotEn = true;        // 智能休眠模式抑制功能. 默认为true 不抑制
    bool UltimateModeNotEn = true;    // 极限续航模式抑制功能. 默认为true 不抑制
    bool DragModeNotEn = true;        // 拖车模式抑制功能, 默认为true 不抑制

    if (ADAS_isStandbyeMode())      // 智能休眠模式
    {
        StandbyNotEn = false;
    }
    if (ADAS_isUltimateEnduranceMode())     // 极限续航模式
    {
        UltimateModeNotEn = false;
    }
    if (1 == Vehicle_getDragMode())         // 进入拖车模式
    {
        DragModeNotEn = false;
    }
#ifdef ADAS_FUNCTION_A3
    static uint32_t radiuscnt = 0;
    if ((pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon == 2) || (pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon == 3) ||
        (pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon == 4) || (pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon == 5) ||
        (pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon == 6) || (pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon == 9))
    {
        turnSignalState = 1;      // 暂时屏蔽
    }
    else
    {
        turnSignalState = 0;
    }
    if (fabsf(pobjAlm->centerx) < radiusTurningThreshold){
        radiuscnt++;
    }else{
        radiuscnt = 0;
    }
#endif
	// 现在只有四种状态(不包括START)，四中状态机的优先级：关闭＞故障＞激活=待机
	enum {
        START = 0,          // 跃迁初始态
        RCW_FUNC_OPEN = 1,      // 1、RCW功能开启
        RCW_MRR_NO_FAULT =2,   ///2、雷达没有故障
        RCW_PASSIVE =3,        ///3、Passive待机状态，4、Active状态
    } s_tState = START; 

	if (pollingObj == RCW)
	{
			outputFuncFSM(TO_STR(RCW), gadasFunctionState.adasRCWFuncState,
						  pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.RCWSwtReq, BSDVelSpeedVal,
						  pVDY->pVDY_DynamicInfo->vdyGearState);
	}

	switch (s_tState) 
	{
        case START:
        {
            s_tState++; 
        }
		case RCW_FUNC_OPEN:
		{
			if (pVDY->pVDY_VehicleFuncSwtInfo->vehFuncSwitchReq.bit.RCWSwtReq == 0U)
			{
				gadasFunctionState.adasRCWFuncState = (uint8_t)RCW_FUNC_STATE_OFF;
				break;
			}
			else
			{
				s_tState++;
			}
		}
		case RCW_MRR_NO_FAULT:
		{
			if (MrrSystemState != (uint8_t)DTC_SYSERROR_NULL)
			{
				gadasFunctionState.adasRCWFuncState = (uint8_t)RCW_FUNC_STATE_ERROR;
				break;
			}
			else
			{
				s_tState++;
			}
        }
		case RCW_PASSIVE:
		{
            /**
             * @brief 条件判断之前的一些参数赋值
             */
            if (RCW_FUNC_STATE_ACTIVE == gadasFunctionState.adasRCWFuncState)
            {
                radiusTurningThreshold += RCW_OWN_MIN_RADIUS_TURNING_THRESHOLD_BUF;
            } 
            if (
                (StandbyNotEn) && (UltimateModeNotEn) && (DragModeNotEn) &&
                (BSDVelSpeedVal <= RCW_MAX_SPD_UPPER && BSDVelSpeedVal >= RCW_MIN_SPD_LOWER) &&
                (pVDY->pVDY_DynamicInfo->vdyGearState != (uint8_t)GEAR_SIG_R) && /*非倒挡*/
                (pVDY->pVDY_ChassisInfo->vdyEspStatus.ESPDiagActv == 0U) && /*esp 诊断结果正常*/
                (pVDY->pVDY_ChassisInfo->raceModeStatus == 0) &&			 /*赛道模式关闭*/
                (pVDY->pVDY_StaticInfo->vdyKeyState == (uint8_t)vdyKeyStateOn) &&	 /*电源模式ON*/
                (turnSignalState == 0) 
#ifdef ADAS_FUNCTION_A3
                && (0 == radiuscnt)                            /* 转弯半径大于阈值 */
#endif                                          /*转向灯、双闪灯、危险告警灯点亮为点亮为active其一条件*/
// #if defined(VEHICLE_TYPE_BYD_SONG_SF_5R5V)
//                 && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 1) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 2))     /* 极致转向关闭 0x147  */
// #endif
            )
            {
                // 速度满足大于等于4.5，小于等于146km/h
                gadasFunctionState.adasRCWFuncState = RCW_FUNC_STATE_ACTIVE;
                break;
            }
            else
            {
                gadasFunctionState.adasRCWFuncState = RCW_FUNC_STATE_PASSIVE;
                break;
            }
		}
        default: 
        {
            break;
        }
	}
    // 状态切换回active时,清理一次数据.避免误报.
    if (((uint8_t)RCW_FUNC_STATE_ACTIVE == gadasFunctionState.adasRCWFuncState) && (laststate != (uint8_t)RCW_FUNC_STATE_ACTIVE))
    {
        ADAS_clrAlmState(pobjAlm->pobjPath, (uint32_t)ALARM_ACTIVE_RCW);
    }
    laststate = gadasFunctionState.adasRCWFuncState;
}



/**
 * @brief 功能状态机主函数，切换并输出具体的功能状态
 * 
 * @param pobjAlm 报警结构体
 * @param pVDY 车身数据
 */
void ADAS_switchFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY, uint8_t hilMode, const ADAS_TimeClase_t timeClass)
{
    ADAS_setclrFunctionState(timeClass);

    if (DATA_HIL_DET_TRK_NONE == hilMode){
        #ifndef PC_DBG_FW   //上位机不执行此函数
            #if (ALARM_TYPE_LCA_EN == 1)
            judgeLCAFuncState(pobjAlm, pVDY);
            #endif

            #if (ALARM_TYPE_BSD_EN == 1)
            judgeBSDFuncState(pobjAlm, pVDY);
            #endif

            #if (ALARM_TYPE_RCTA_EN == 1)
            judgeRCTAFuncState(pobjAlm, pVDY);
            #endif

            #if (ALARM_TYPE_RCTB_EN == 1)
            judgeRCTBFuncState(pobjAlm, pVDY);
            #endif

            #if (ALARM_TYPE_DOW_EN == 1)
            judgeDOWFuncState(pobjAlm, pVDY);
            #endif

            #if (ALARM_TYPE_RCW_EN == 1)
            judgeRCWFuncState(pobjAlm, pVDY);
            #endif

            #if (ALARM_TYPE_FCTA_EN == 1)
            judgeFCTAFuncState(pobjAlm, pVDY);
            #endif

            #if (ALARM_TYPE_FCTB_EN == 1)
            judgeFCTBFuncState(pobjAlm, pVDY);
            #endif
        #endif
    }else{
        // TODO: 回灌模式使用回灌数据中的状态机.
        // gadasFunctionState.adasBSDFuncState = HIL_getHilAdasFunctionState()->adasBSDFuncState;
        // gadasFunctionState.adasLCAFuncState = HIL_getHilAdasFunctionState()->adasLCAFuncState;
        // gadasFunctionState.adasDOWFuncState = HIL_getHilAdasFunctionState()->adasDOWFuncState;
        // gadasFunctionState.adasRCWFuncState = HIL_getHilAdasFunctionState()->adasRCWFuncState;
        // gadasFunctionState.adasRCTAFuncState = HIL_getHilAdasFunctionState()->adasRCTAFuncState;
        // gadasFunctionState.adasRCTBFuncState = HIL_getHilAdasFunctionState()->adasRCTBFuncState;
        // gadasFunctionState.adasFCTAFuncState = HIL_getHilAdasFunctionState()->adasFCTAFuncState;
        // gadasFunctionState.adasFCTBFuncState = HIL_getHilAdasFunctionState()->adasFCTBFuncState;
    }

#ifdef PC_DBG_FW
    if ((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) || (pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1))
    {
        gadasFunctionState.adasFCTAFuncState = FCTA_FUNC_STATE_ACTIVE;
        gadasFunctionState.adasFCTBFuncState = FCTB_FUNC_STATE_ACTIVE;
    }
#endif
}
