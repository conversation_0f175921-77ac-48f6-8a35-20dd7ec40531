﻿/*
 * @Author: your name
 * @Date: 2021-10-14 10:13:18
 * @LastEditTime: 2022-03-14 12:31:58
 * @LastEditors: Please set LastEditors
 * @Description: 根据协议输出目标点数据、周期数据，比如emc的测试，雷达信息等
 * @FilePath: \fw_base_original_sdk\calterah\common\user\can_msg.h
 */
#ifndef _CAN_MSG_H_
#define _CAN_MSG_H_

#ifndef PC_DBG_FW
#include "typedefs.h"
#else
#include "app/system_mgr/typedefs.h"
#endif

#define OBJ_DATA_SEND_CAN_NUMBER CAN_1_ID // CAN_0_ID //发送目标点的can的索引

#define PARAM_DATAMODE_RAW		    0x0
#define PARAM_DATAMODE_TRACK 	    0x1
#define PARAM_DATAMODE_RAW_TRACK 	0x2
#define PARAM_DATAMODE_NONE		 	0x3
#define PARAM_DATAMODE_NOMOVE		0x4
#define PARAM_DATAMODE_MOVE		 	0x5

#define QUERY_INFO_BASE         (0x110)
#define SEND_INFO_BASE          (0x210)
#define RADAR_VERSION_BASE      (0x700)

#define SET_RADAR_CFG_BASE      (0x200)
#define SET_RADAR_MODE_BASE     (0x300)

#define SET_INSTALL_INFO_BASE   (0x400) //配置安装信息

#define RADAR_SNY_INFO_BASE     (0x500) //输出同步的信息-暂时调试用
#define FRAME_END_BASE          (0x5F0)

#define TRK_OBJ_START_BASE      (0x6A0)
#define TRK_OBJ_INFO_BASE       (0x6B0)
#define TRK_OBJ_EXTEND_1_BASE   (0x6C0)
#define TRK_OBJ_EXTEND_2_BASE   (0x6D0)

#define RAW_OBJ_START_BASE      (0x600)
#define RAW_OBJ_INFO_BASE       (0x710)
#define RAW_OBJ_HEIGH_BASE      (0x720)

#define RAW_OBJ_NOISE_BASE      (0x750)

#define FCW_TIME_INFO_1_BASE    (0x6E0)
#define FCW_TIME_INFO_BASE      (0x6F0)
#define FCW_WARNING_INFO_BASE   (0x7F0)

#define RADAR_SPEED_INFO_BASE   (0x7E0)

#define RAW_VEL_OBJ_START_BASE (0x680)
#define RAW_VEL_OBJ_INFO_BASE  (0x690)

#define SYSTEM_TEST_MSG_BASE  (0x7D0)
#define CAN_CMD_CFG_MSG_BASE (0x130)
#define CAN_CMD_DBG_MSG_BASE (0x40)

//与雷达ID不挂钩
#define SET_CAR_INFO 0x3F0       //输入车速信息  -- 大端模式
#define SET_CAR_INFO_2 0x4F5     //输入车速信息  -- 大端模式 -- 》放空，不能使用
#define CMD_CASCADE_ORDAER 0x00F //屏蔽\复位\解锁 雷达操作 -- 级联操作指令
//#define SET_res        		0x3F2 //传感器配置2
//#define SET_res        		0x3F3 //传感器配置3
//#define SET_res        		0x3F4 //传感器配置4
#define SET_CAR_VEL_INTEL 0x3F5     //车辆信息同步及输入
#define SET_CAR_YAWRATE_INTEL 0x3F6 //车辆偏航角信息输入
//#define SET_res        		0x3F7 //寄存器读命令
//#define SET_res        		0x3F8 //寄存器写命令

// #define CAN_MSG_ID(id)   (id + (radar_config_using->compli_para.radarId & 0x0000000F))
#define CAN_MSG_ID(id)   (id + (APAR_getAparRadarId() & 0x0F))

#define QUERY_INFO         CAN_MSG_ID(QUERY_INFO_BASE)
#define SEND_INFO          CAN_MSG_ID(SEND_INFO_BASE)
#define RADAR_VERSION      CAN_MSG_ID(RADAR_VERSION_BASE)

#define SET_RADAR_CFG      CAN_MSG_ID(SET_RADAR_CFG_BASE)
#define SET_RADAR_MODE     CAN_MSG_ID(SET_RADAR_MODE_BASE)

#define SET_INSTALL_INFO   CAN_MSG_ID(SET_INSTALL_INFO_BASE) //配置安装信息

#define RADAR_SNY_INFO     CAN_MSG_ID(RADAR_SNY_INFO_BASE) //输出同步的信息-暂时调试用
#define FRAME_END          CAN_MSG_ID(FRAME_END_BASE)

#define TRK_OBJ_START      CAN_MSG_ID(TRK_OBJ_START_BASE)
#define TRK_OBJ_INFO       CAN_MSG_ID(TRK_OBJ_INFO_BASE)
#define TRK_OBJ_EXTEND_1   CAN_MSG_ID(TRK_OBJ_EXTEND_1_BASE)
#define TRK_OBJ_EXTEND_2   CAN_MSG_ID(TRK_OBJ_EXTEND_2_BASE)

#define RAW_OBJ_START      CAN_MSG_ID(RAW_OBJ_START_BASE)
#define RAW_OBJ_INFO       CAN_MSG_ID(RAW_OBJ_INFO_BASE)
#define RAW_OBJ_HEIGH      CAN_MSG_ID(RAW_OBJ_HEIGH_BASE)

#define RAW_OBJ_NOISE      CAN_MSG_ID(RAW_OBJ_NOISE_BASE)

#define FCW_TIME_INFO_1    CAN_MSG_ID(FCW_TIME_INFO_1_BASE)
#define FCW_TIME_INFO      CAN_MSG_ID(FCW_TIME_INFO_BASE)
#define FCW_WARNING_INFO   CAN_MSG_ID(FCW_WARNING_INFO_BASE)

#define RADAR_SPEED_INFO   CAN_MSG_ID(RADAR_SPEED_INFO_BASE)

#define RAW_VEL_OBJ_START CAN_MSG_ID(RAW_VEL_OBJ_START_BASE)
#define RAW_VEL_OBJ_INFO CAN_MSG_ID(RAW_VEL_OBJ_INFO_BASE)

#define SYSTEM_TEST_MSG CAN_MSG_ID(SYSTEM_TEST_MSG_BASE)
#define CAN_CMD_CFG_MSG CAN_MSG_ID(CAN_CMD_CFG_MSG_BASE)
#define CAN_DBG_ID CAN_MSG_ID(CAN_CMD_DBG_MSG_BASE)

//获取Flash值命令
#define GET_FLASH_VALUE CAN_MSG_ID(0x310)
#define CFG_FLASH_CMD CAN_MSG_ID(0x320)          //写flash命令
#define SET_CALC_INFO CAN_MSG_ID(0x410)          //配置安装校准信息
#define SEND_CALC_INFO CAN_MSG_ID(0x420)         //回复校准信息
#define GET_CALC_INFO SEND_CALC_INFO             //获取校准结果
#define SET_SELF_CALC_INFO CAN_MSG_ID(0x430)     //配置自校准信息
#define SET_CALC_EXT_CFG   CAN_MSG_ID(0x440) //安装校准扩展配置接口
#define SET_ACC_SELF_CALC_INFO CAN_MSG_ID(0x450) //加速度计校准
#define ACC_GYRO_PUT_INFO CAN_MSG_ID(0x460)      //加速度计、陀螺仪输出信息

//UDS command
#define RD_VOLT_STATUS CAN_MSG_ID(0x640)
#define RD_CALI_STATUS CAN_MSG_ID(0x650)
#define RD_ALARM_STATUS CAN_MSG_ID(0x660)
//#define RD_CALI_DIST CAN_MSG_ID(0x670)
//#define RD_CALI_ANGLE CAN_MSG_ID(0x680)
#define RD_DETECT_STATUS CAN_MSG_ID(0x690)
//#define RD_INSTALL_RESULT CAN_MSG_ID(0x730)
#define RD_INPUT_VOLT_STATUS CAN_MSG_ID(0x740)

//与雷达ID不挂钩
#define CMD_CASCADE_ORDAER 0x00F //屏蔽\复位\解锁 雷达操作 -- 级联操作指令
//#define SET_res        		0x3F2 //传感器配置2
//#define SET_res        		0x3F3 //传感器配置3
//#define SET_res        		0x3F4 //传感器配置4
#define SET_CAR_VEL_INTEL       0x3F5 //车辆信息同步及输入
#define SET_CAR_YAWRATE_INTEL   0x3F6 //车辆偏航角信息输入
//#define SET_res        		0x3F7 //寄存器读命令
//#define SET_res        		0x3F8 //寄存器写命令


//error Code
#define RADAR_ERROR_CODE_YAWRATE	  			101		//车速信息错误  曲率输入错误
#define RADAR_ERROR_CODE_CARSPEED	  			102		//车速信息错误 OBD车速输入错误
#define RADAR_ERROR_CODE_BSD_APP	  103		// BSD代码新加入的错误码
#define RADAR_ERROR_CODE_MMIC_TEMPERATURE	  	110		//MMIC芯片温度异常
#define RADAR_ERROR_CODE_MCU_TEMPERATURE	  	111		//MCU 芯片温度异常

#define RADAR_ERROR_CODE_SELF_CALC_ANGLEOFFSET	120		//自标定角度异常

#define CMD_BOOT_MAGIC 0x55504752414445FE
#define CMD_WRITE_FLASH_MAGIC 0x55504752414445FE
#define CMD_READ_CFG 0xFFFFFFFFFFFFFFFF
#define PASSWORD_RADAR_MODE_SW 0x3158AF
#define CMD_CASCADE_ORDAER_PASSWORD 0x3158AF003158

#define CMD_GET_INSTALL_CALC_INFO_PASSWORD 0x0FFFFFFFFFFFFFFF
#define CMD_GET_SELF_CALC_INFO_PASSWORD 0x1FFFFFFFFFFFFFFF


//MAXUS Protocol上汽大通
#define ABS_269h 0x269
#define ESP_249h 0x249
#define GW_BCM_281h 0x281
#define GW_BCM_375h 0x375
#define GW_IPK_363h 0x363
#define GW_TCU_1F5h 0x1F5

//BEIQI
#define BCM_1_4A0h   0x4A0
#define ABS_ESP5_311h    0x311
#define TCM2_3e0h       0x3E0
#define ABS_ESP_6_3f0h   0x3F0

//JAC CAN Signal List
#define BSD_STATUS 0x50A

//MAXUS CAN Signal List
#define CAN_ID_BSD_SYS_STATUS 0x369

#define CAN_ID_SAS_1E5      0x1E5
#define CAN_ID_ESC_1E9      0x1E9
#define CAN_ID_ESC_23C      0x23C
#define CAN_ID_GW_BCM_1F1   0x1F1
#define CAN_ID_GW_BCM_281   0x281
#define CAN_ID_GW_BCM_46A   0x46A
#define CAN_ID_GW_FICM_541  0x541
#define CAN_ID_GW_FICM_562  0x562
#define CAN_ID_GW_IPK_540   0x540
#define CAN_ID_GW_BCM_379   0x379
#define CAN_ID_GW_BCM_375   0x375
#define CAN_ID_SLAVE_RAD    0x645
#define CAN_ID_VEH_INFO_0x3F0 0x3F0

void UpdateMsgGroup(void);
void sendEndFrameInfo(void);

void BSDStatusUpdateBusoff(int CanIndex);

void canSendTest(uint32_t canIndex);
void canSendNowTest(uint32_t canIndex);

void CAN1_sendDumpDataHeader(uint8_t ch_index, uint16_t range_bins, uint16_t doppler_bins);
void CAN1_sendDumpDataEnd(void);
void CAN1_sendDumpDataReqAck(void);
void CAN1_sendDumpDataModeAck(void);
void CAN1_sendDumpDataPayload(uint32_t *src_data, uint32_t len, uint8_t start_frame_flag);

#endif

