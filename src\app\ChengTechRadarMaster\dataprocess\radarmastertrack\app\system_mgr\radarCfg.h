﻿/*
 * @Author: your name
 * @Date: 2021-10-14 12:36:06
 * @LastEditTime: 2022-06-06 14:29:53
 * @LastEditors: moxinqing
 * @Description: In User Settings Edit
 * @FilePath: \fw_base_original_sdk\calterah\common\user\radarCfg.h
 */
#ifndef __RADAR_CFG_H__
#define __RADAR_CFG_H__

#ifdef PC_DBG_FW
#include "common/common_def.h"
#endif

#define TEST_FOR_NO_RECORD_ALL_REG 1   //是否是测试版本，不存对应的大快照  0：非测试版本，会存储 1：测试版本，不存储

//一些雷达计算参数的定义
#define SPEED_LIGTH 299792458 // speed of light
#define DEMO_BORAD_220PRO_EN 0U // 1：适配Demo板子 0：不适配Demo板子

//--------------------------------------------------------
// 天线配置
//--------------------------------------------------------
// #define ANT_INSTALL_REVERSE 0 //雷达倒装放到车型配置里边

//--------------------------------------------------------
// 温度保护配置
//--------------------------------------------------------
#define RADAR_TEMP_RPOTECT 1 /**< 是否支持温度保护功能 */
#define TEMP_MAX 110.0f      /**< 保护温度 */
#define TEMP_MIN 100.0f      /**< 恢复温度 */
#define TEMP_CHIRP_SCALE 4u  /**< 触发温度保护时的chirp数减少倍数 */

//--------------------------------------------------------
// 功能配置
//--------------------------------------------------------
// 看门狗是否启用
#define USE_WATCHDOG 1

#define USE_WATCHDOG_70331

#define UDS_ENABLE

// 跟踪插值功能是否启用
#define TRK_INTERPOLATION 1

// 陀螺仪功能是否启用
#define GYRO_FUNCTION_EN 0

#define DV_TEST_MODE_ENABLE 0 // DV测试模式协议
#define DV_DROP 0             // 瞬时跌落

// 服务校准使能开关
#define RADAR_SERVICE_ALIGNMENT_MODE_DBAA 1 // DBAA算法

#define RADAR_SERVICE_ALIGNMENT_MODE_ASC 1 // ASC算法

// 自标定使能开关
#define ANGLE_SELF_CALIBRATION_BASE_ASC 1 // 自标定-基于ASC算法

// 速度解模糊
#define FIX_VELOCITY_ENABLE 1

// 接模糊回验功能
#define SPEED_VAGUE_CHECK_EN 1

// 输出原始点相位信息使能
#define RADAR_RAWONJ_PHASE_OUTPUT_EN 0

// 距离速度插值提升精度功能
#define RANGE_VELOCITY_INTERPOLATION_FUNEN 1

// 用于原始点数据调试
//  #define RAW_DATA_PROTOCAL_DEBUG

// 是否支持busoff慢恢复
#define CAN_BUSOFF_SLOW_RECOVERY 0

// 暗室测试解速度模糊功能  0：正常模式  1：关闭速度回验，关闭一些不关注点的过滤
#define FIX_VELOCITY_TEST 0

// 是否支持cmd读加密flash
#define CMD_READ_ENC_FLASH_EN 1

// dsp任务是否需要定时执行
#define RSP_TICK_ENABLE 1

// 是否启动网络管理功能
#define CAN_NM 1

// 角雷达BYD高阶项目功能相关
#ifdef ADVANCE_FUNCTION_EN
#define CMD_FRAME_ID 0x750U // cmd id
#define CAN_DBG_ID 0x7A0U   // dbg id
#define EOL_REQ_ID 0X770u   // eol request id
#define EOL_RSP_ID 0x780u   // eol response id

#define BYD_CAN_ID_RL_REQUEST_SN 0x734
/* PCAN BYD request radar SN */
#define CAN_ID_BYD_REQUEST_RADAR_SN (0x730)
#define CAN_ID_BYD_REQUEST_SN CAN_MSG_SUBID(CAN_ID_BYD_REQUEST_RADAR_SN)
/* PCAN BYD response radar SN */
#define CAN_ID_BYD_RESPONSE_RADAR_SN (0x740)
#define CAN_ID_BYD_RESPONSE_SN CAN_MSG_SUBID(CAN_ID_BYD_RESPONSE_RADAR_SN)

#define UOPDATE_MOUNT_ID_REALTIME_EN 1U // 连续检查并判断是否更新角雷达mount ID
#else
// Nothing
#endif // end of ADVANCE_FUNCTION_EN

/****************************************************************************
    插值方案：
    时间定义：
    T0：发波时刻
    T1：回波时刻
    T2：信号处理结束时刻
    T3：数据处理结束时刻
    T4：报文发送时刻
    其中 角雷达：
    1、回波时刻与发波时刻稳定在18.432ms。
    2、信号处理时间受目标数量影响（最大256个目标原始点），在50ms到100ms，是一个不稳定周期。
    3、数据处理耗时相对稳定，在20ms到30ms左右，耗时较短。

    方案一、跟踪点报文对应回波时刻 (不再使用，代码待适配 2024-10)
    为发送目标对应的回波时间，补偿使用的Δt是T4-T3，发送的时间戳为T1-(T1-T0)/2+Δt

    方案二：跟踪点报文对应报文发送时刻（雷达补偿）
    雷达内部补偿从T0与T1的均值到T4时刻的时间差到目标的距离信息上，报文时间戳对应目标报文的发送时间
    补偿使用的Δt是T4-T1-(T1-T0)/2，发送的时间戳为T4
 ****************************************************************************/
#ifdef ADVANCE_TARGETS_TIMESTAMP_EN
#define TRK_INTERPOLATION_SCHEME 2  // 插值方案
#define TRK_INTERPOLATION_DELTA_T 9 //(回波时刻-发波时刻)/2
#endif                              // end of ADVANCE_TARGETS_TIMESTAMP_EN

//--------------------------------------------------------
// 传输和协议配置
//--------------------------------------------------------
#define CAN_TX_EN_MASK ((1U << PCAN) | (1U << VCAN)) // 默认CAN使能发送配置

#if CANBUS_PROTOCOL_VER_USING == CANBUS_PROTOCOL_VER_HW_MDC
#define PROTOCOL_VER_USING PROTOCOL_VER_4 // 目前使用协议
#else
#define PROTOCOL_VER_USING PROTOCOL_VER_8 // 目前使用协议
#endif

// 目标数量限制
#if CANBUS_PROTOCOL_VER_USING == CANBUS_PROTOCOL_VER_Project_410_QZ
#define MAX_TARGET_NUM_SEND 32U
#elif CANBUS_PROTOCOL_VER_USING == CANBUS_PROTOCOL_VER_CONTI
#define MAX_TARGET_NUM_SEND 128U
#elif CANBUS_PROTOCOL_VER_USING == CANBUS_PROTOCOL_VER_CONTI410
#define MAX_TARGET_NUM_SEND 40U
#elif CANBUS_PROTOCOL_VER_USING == CANBUS_PROTOCOL_VER_BoschMrr
#define MAX_TARGET_NUM_SEND 32
#else
#define MAX_TARGET_NUM_SEND 40U
#endif

// CAN TX buffer大小配置
#define PCAN_TX_BUF_SIZE 256u
#define VCAN_TX_BUF_SIZE 64u

//--------------------------------------------------------------------
// 应用场景配置
//--------------------------------------------------------------------
#define NORMAL_MODE_APP 0
#define FCW_MODE 1
#define ACC_MODE 2
#define BSD_MODE 3
#define RCW_MODE 4
#define ETC_MODE 5
#define LDTR_MODE 6 //路达交通雷达

#define RADAR_MODE BSD_MODE //BSD_MODE //NORMAL_MODE_APP

//TX切换定义
enum TX_SWITCH_E
{
    NO_TX_SW,   //不切换
    TX_CH_SW,   //切换天线
    TX_BW_SW,   //切换带宽
    TX_CH_BW_SW //天线和带宽都切换
};

// 总线配置
#define CAN_BAUDRATE_500K 500000//500000   //can波特率 500K
#define CAN_BAUDRATE_1000K 1000000//1000000 //can波特率 1M


#define FIX_VELOCITY_ENABLE 1

// 采样参数配置
//--------------------------------------------------------------------
#define WIDTH_BAND 0
#if TD_MIMO || BPM_MIMO
#define SAMPLE_CNT 512 //采样点数
#else
#define SAMPLE_CNT 1024      //采样点数
#endif                       //end TD_MIMO || BPM_MIMO
#define CHIRPS_PER_FRAME 256 //128	  // number of chirps per frame

//--------------------------------------------------------------------
// 各应用场景对应的参数配置
//--------------------------------------------------------------------
#if RADAR_MODE == LDTR_MODE
#define FMCW_START_FREQ 76.3
#define FMCW_BANDWIDTH 0.400
#define FMCW_TRAMPUP 110.4

#define TX_SW_MODE NO_TX_SW
#define TX_SEL 2

#define TX1_TUNE2_GAIN 0x55
#define TX2_TUNE2_GAIN 0x55

#undef DBF_OUT_ANGLES
#define DBF_OUT_ANGLES 1 //每个点只取一个角度
#elif RADAR_MODE == FCW_MODE
#define TX_SW_MODE NO_TX_SW
#define TX_SEL 2
#elif RADAR_MODE == BSD_MODE
#define FMCW_START_FREQ 76.0
#define FMCW_BANDWIDTH 0.800
#define FMCW_TRAMPUP 110.4

#define TX_SW_MODE TX_BW_SW
#define TX_SEL 3

#define TX1_TUNE2_GAIN 0xff
#define TX2_TUNE2_GAIN 0xff

#undef DBF_OUT_ANGLES
#define DBF_OUT_ANGLES 1 //每个点只取一个角度

#endif

#define FMCW_TDOWN 5.6

#if (TD_MIMO || BPM_MIMO)
#define FMCW_TIDLE 9
#else
#define FMCW_TIDLE 3
#endif

#if TD_MIMO || BPM_MIMO
#define TX_SW_MODE NO_TX_SW
#define TX_SEL 1
#endif

//--------------------------------------------------------
// 默认基本参数配置
//--------------------------------------------------------
#define MAX_RANGE 203 //最大有效距离

//TX天线增益配置
#ifndef TX1_TUNE2_GAIN
#if WIDTH_BAND
#define TX1_TUNE2_GAIN 0x00
#define TX2_TUNE2_GAIN 0x11
#else
#define TX1_TUNE2_GAIN 0x88
#define TX2_TUNE2_GAIN 0x88
#endif
#endif

//RX天线增益配置
#ifndef TIA_LNA_GAIN
#define TIA_LNA_GAIN 0x1F
#endif

#ifndef VGA_GAIN
#define VGA_GAIN 0x26
#endif

#ifndef CT400_TX_POWER
#define CT400_TX_POWER 0xFF
#endif

//默认FMCW参数配置
#if defined(FMCW_START_FREQ)
//使用自定义的
#elif WIDTH_BAND
#define FMCW_START_FREQ 76.0
#define FMCW_BANDWIDTH 1.8
#define FMCW_TRAMPUP 110.4
#elif SAMPLE_CNT == 256
#define FMCW_START_FREQ 76.45
#define FMCW_BANDWIDTH 0.100
#define FMCW_TRAMPUP 32
#elif SAMPLE_CNT == 320
#define FMCW_START_FREQ 76.4
#define FMCW_BANDWIDTH 0.135
#define FMCW_TRAMPUP 39
#elif SAMPLE_CNT == 512
#define FMCW_START_FREQ 76.4
#define FMCW_BANDWIDTH 0.200
#define FMCW_TRAMPUP 58.6
#elif SAMPLE_CNT == 1024
#define FMCW_START_FREQ 76.3
#define FMCW_BANDWIDTH 0.400
#define FMCW_TRAMPUP 110.4
#endif

#define CAN_BAUDRATE_TYPE CAN_BAUDRATE_1000K//CAN_BAUDRATE_1000K

//----------------使用2个profile还是4个profile-----------,注意：只支持2或是4
#define PROFILE_CNT 4

#define ANT_DISTANCE_CAL                                                  \
    {                                                                     \
        0, 3.1415926535, 7.85398163375, 9.4247779605, 0, 3.1415926535, 7.85398163375, 9.4247779605,0, 3.1415926535, 7.85398163375, 9.4247779605,0, 3.1415926535, 7.85398163375, 9.4247779605 \
    } // 这个值表示方法为---：2*pi*d/波长,需要配置到alps的值是   (d/波长)
#define ANT_DISTANCE_CAL_Y     \
    {                          \
        0, 0, 0, 0, 0, 0, 0, 0,0,0,0,0,0,0,0,0 \
    } // 这个值表示方法为---：2*pi*d/波长,需要配置到alps的值是   (d/波长)

#define ANT_PHASE_CAL          \
    {                          \
        0, 6.34, 28.2, 143.57, 0, 6.34, 28.2, 143.57,0, 6.34, 28.2, 143.57,0, 6.34, 28.2, 143.57 \
    }

//硬件版本号
//第 2 字节的 4 根据硬件id变化
#undef HW_VERSION
#define HW_VERSION {4, 0, 1, 0}
#define UDS_HW_VERSION "04.00.01"

#undef CAL_VERSION
#define CAL_VERSION  {0xFF , 0xFF}
#define UDS_CAL_VERSION "02.00.00"

#undef SW_VERSION
#define SW_VERSION  { 2, 7, 250, 88 }
#define UDS_ECU_SW_VER "2.7.250.88"
#define UDS_SW_VERSION "03.00.02"

#undef ALGO_VERSION
#define ALGO_VERSION   {  2, 0, 0, 0 }
#define UDS_ALGO_VERSION "02.00.00"

#undef BSW_VERSION
#define BSW_VERSION  {  3, 0, 0, 0  }
#define UDS_BSW_VERSION "03.00.02"

#undef SDK_VERSION
#define SDK_VERSION   {  2, 0, 1, 0  }
#define UDS_SDK_VERSION "02.00.01"

#undef BOOT_VERSION
#define BOOT_VERSION  { 3, 0, 1, 0  }
#define UDS_BOOT_VERSION "03.00.01"

#undef PRODUCT_VERSION
#define PRODUCT_VERSION  { 2, 0, 0, 0 }
#define UDS_PRODUCT_VERSION "02.00.00"


// APAR默认值
#define APAR_INSTALL_ANGLE_DEFAULT 45.0
#define APAR_FMCW_START_FREQ 76.3

#endif
