# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ChengTechRadarMaster is a comprehensive radar simulation and testing application built with Qt/C++ for automotive radar systems. The application supports multiple radar protocols (Continental ARS410, Bosch 670, ChengTech proprietary), automotive OEM integrations (BYD, Geely, GWM, BAIC), and provides real-time visualization, data analysis, and calibration capabilities.

## Build System

### Primary Build Method
- **Visual Studio Solution**: Use `Sim_130ProAdv.sln` (main solution file)
- **Qt Pro Files**: Alternative build using `Sim_130ProAdv.pro` (Qt qmake)
- **Build Script**: `msvc_make.bat` for automated builds using Qt jom

### Build Commands
```bash
# Visual Studio build (recommended)
msbuild Sim_130ProAdv.sln /p:Configuration=Release /p:Platform=Win32
msbuild Sim_130ProAdv.sln /p:Configuration=Debug /p:Platform=Win32

# Qt qmake build
qmake Sim_130ProAdv.pro
msvc_make.bat release  # or debug

# Manual jom build
"D:\ProgramFiles1\Qt\Qt5.14.2\Tools\QtCreator\bin\jom.exe" release
```

### Build Configuration
- **Static Libraries**: Project uses static linking (MASTER_USE_STATIC_LIBARY defined in chengtech.pri)
- **Output Directories**: 
  - Release: `bin/Radar Master_Release/`
  - Debug: `bin/Radar Master_Debug/`
- **Library Dependencies**: utils, devices, network (built in order)

## Architecture

### Core Components
1. **Master Application** (`src/app/ChengTechRadarMaster/`)
   - Main entry point: `main.cpp` → `Core::Internal::Master`
   - UI framework: Qt Widgets with custom docking system
   - Multi-threaded architecture with worker pattern

2. **Libraries** (`src/libs/`)
   - **utils**: Core utilities, settings, logging, UI components
   - **devices**: CAN device abstraction (ZLG, Vector, GC, TS)
   - **network**: TCP/UDP server/client for remote communication

3. **Communication Protocols** (`src/app/ChengTechRadarMaster/analysis/protocol/`)
   - CAN bus protocols for different radar manufacturers
   - Target-specific protocols for automotive OEMs
   - EOL (End-of-Line) testing protocols

### Key Modules

#### Data Processing Pipeline
- **Analysis Framework**: Real-time radar data processing and target tracking
- **Protocol Handlers**: Multi-vendor radar protocol support (ARS410, Bosch 670, ChengTech 710)
- **File Formats**: DBC, ASC, BLF file parsing and generation
- **Network Streaming**: TCP/UDP data transmission for remote monitoring

#### Visualization System
- **Object View**: Real-time radar target visualization with coordinate system
- **Data Views**: Table-based target data display with filtering
- **QCustomPlot Integration**: Real-time plotting and charting
- **Video Integration**: FFmpeg-based camera overlay system

#### Calibration & Testing
- **Static Calibration**: Radar alignment and configuration
- **EOL Protocol**: Production line testing interface
- **After-sales Calibration**: Field calibration tools
- **Black Box Mode**: Data logging and playback

#### Device Management
- **CAN Interface**: Multi-vendor CAN hardware support
- **Network Interface**: Ethernet-based communication
- **DAQ Systems**: TI DCA1000 integration for raw data collection

## Development Workflow

### Configuration Files
- **Role Configuration**: `config.ini` - Sets application role (ROLE_DRIVE_TEST, ROLE_AFTER_SALES)
- **Application Settings**: `settings.ini` - Runtime configuration (large file, search for specific sections)
- **Version Control**: `chengtech.pri` - Version numbers and build configuration

### DBC Protocol Files
Located in `doc/` directory, organized by vehicle manufacturer:
- **BYD**: 5R1V ADAS CAN matrices for FCR/RCR radars
- **GWM**: B07 project CAN matrices for ADAS1/ADAS2
- **HOZON**: AFD1/AFD2 protocol definitions
- **BAIC**: B41V corner radar specifications

### Testing & Validation
- **Multi-Protocol Testing**: Switch between different radar protocols via UI
- **Network Testing**: TCP/UDP server/client validation
- **Hardware Testing**: CAN bus communication verification
- **Data Playback**: Historical data analysis and regression testing

## Common Development Tasks

### Adding New Radar Protocol
1. Create protocol class inheriting from `IAnalysisProtocol`
2. Implement parsing logic in `analysis/protocol/`
3. Add DBC file to appropriate `doc/` subdirectory
4. Register protocol in analysis manager

### CAN Device Integration
1. Implement device worker class inheriting from `IDeviceWorker`
2. Add device configuration in `devices/` library
3. Update device manager registration
4. Test with physical CAN hardware

### Network Protocol Extension
1. Extend network data structures in `network/networkdata.h`
2. Implement protocol handlers in TCP/UDP servers
3. Update client communication logic
4. Test with external network clients

## Dependencies

### Qt Framework
- **Version**: Qt 5.14.2 (primary), Qt 6.x compatibility
- **Modules**: Core, Widgets, Network, SerialPort, Multimedia
- **Custom Components**: QCustomPlot for visualization

### Third-Party Libraries
- **OpenCV**: Computer vision and image processing
- **FFmpeg**: Video encoding/decoding and camera integration
- **CAN Libraries**: Vendor-specific SDKs (ZLG, Vector, etc.)

### Hardware Interfaces
- **CAN Hardware**: ZLG USBCANFD, Vector CANoe, GC CAN-II-FD
- **Network**: Standard Ethernet interfaces
- **DAQ**: TI DCA1000 for raw ADC data collection

## File Organization

### Source Structure
```
src/
├── app/ChengTechRadarMaster/    # Main application
│   ├── analysis/                # Data processing and protocols
│   ├── views/                   # UI components and visualization
│   ├── camera/                  # Video integration
│   ├── daq/                     # Data acquisition
│   └── dataprocess/             # Network and interpolation
├── libs/                        # Shared libraries
│   ├── utils/                   # Core utilities
│   ├── devices/                 # Hardware abstraction
│   └── network/                 # Network communication
└── tools/                       # Utility applications
```

### Key File Patterns
- **Protocol Files**: `*protocol*.h/cpp` - Communication protocol implementations
- **Worker Classes**: `*worker*.h/cpp` - Threaded background processing
- **UI Forms**: `*.ui` - Qt Designer form files
- **Configuration**: `*.ini` - Settings and configuration files
- **DBC Files**: `*.dbc` - CAN database definitions

## Debug & Testing

### Debug Information
- **Debug/Release Builds**: Use different output directories and linking settings
  - Debug output: `bin/Radar Master_Debug/`
  - Release output: `bin/Radar Master_Release/`
- **Log Files**: Check `USBCANFD.log`, `tcpSend_newVer.log` in main app directory
- **Settings**: Runtime settings stored in `settings.ini` (can be large, search for specific sections)

### Testing & Validation
- **Multi-Protocol Testing**: Switch between radar protocols via UI (ARS410, Bosch 670, ChengTech 710)
- **Network Testing**: TCP/UDP server/client validation using built-in tools
- **Hardware Testing**: CAN bus communication verification with multiple device types
- **Data Playback**: Historical data analysis and regression testing capabilities

## ADAS Algorithm Integration

### Key ADAS Components
Located in the deeply nested structure under `src/app/ChengTechRadarMaster/dataprocess/radarmastertrack/app/adas/`:
- **Alarm Functions**: BSD, DOW, FCTA/B, LCA, RCTA/B, RCW algorithms (in `generalalg/` directory)
- **Common Utilities**: Signal integration, state machines, vehicle controls, detection scenarios
- **Configuration**: Installation configuration and calibration routines

### ADAS File Patterns
- **Algorithm Files**: `adas_alm_*.c/h` - Individual alarm/warning algorithms
- **State Management**: `adas_state_machine.c/h` - Central state coordination
- **Vehicle Integration**: `adas_vehicle_ctrls.c/h` - Vehicle-specific control logic

## Version Information
- **Current Version**: 4.8.18 (defined in chengtech.pri)
- **Display Name**: "Radar Master"
- **Copyright**: 2024 ChengTech
- **Target Platform**: Windows (Win32), MINGW32 compatibility