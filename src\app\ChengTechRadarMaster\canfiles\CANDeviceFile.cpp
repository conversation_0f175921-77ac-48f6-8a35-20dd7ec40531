﻿#include "CANDeviceFile.h"

#include "devicefiles/devicefileasc.h"
#include "devicefiles/devicefileblf.h"

#include <iostream>
#ifdef _MSC_VER
#include <io.h>
#else
#include <unistd.h>
#endif


namespace Devices {
    namespace Can {
		CANDeviceFile::CANDeviceFile()
		{
			mCurFileType = IDeviceFile::UNKNOWN;

			mDeviceFiles[IDeviceFile::ASC] = new DeviceFileASC(this);
            mDeviceFiles[IDeviceFile::BLF] = new DeviceFileBLF(this);
        }

        void CANDeviceFile::setValidID(ValidID validID)
        {
            mDeviceFiles[IDeviceFile::ASC]->setValidID(validID);
            mDeviceFiles[IDeviceFile::BLF]->setValidID(validID);
        }

        void CANDeviceFile::callback(const CanFrame::Ptr pFrame)
        {

        }

		bool CANDeviceFile::openDevice()
		{
			//0 （F_OK）	只判断是否存在
			//2 （R_OK）	判断写入权限
			//4 （W_OK）	判断读取权限
			//6 （X_OK）	判断执行权限
			mFilenames.clear();
			mCurrentFileIndex = 0;
            for (int i = 0; i < mCANDeviceFiles.size(); ++i) {
                std::string &filename = mCANDeviceFiles[i];
				if (_access(filename.c_str(), 0) == -1) {
					mErrorString = "open FILE device error! File does not exist! " + filename;
					return false;
				}
				mFilenames.push_back(filename);
			}
			if (!mFilenames.size()) {
				mErrorString = "open FILE device error! Don't choose file! ";
				return false;
			}
			return openFile(mFilenames[mCurrentFileIndex]);
		}
		bool CANDeviceFile::closeDevice()
		{
			if (mCurFileType == IDeviceFile::UNKNOWN) {
				return true;
			}
			return mDeviceFiles[mCurFileType]->close();
		}
		int CANDeviceFile::receiveFrame(int channelIdx)
		{
			if (mCurFileType == IDeviceFile::UNKNOWN || !mDeviceFiles[mCurFileType]->isOpened()) {
				return 0;
			}

			int len = mDeviceFiles[mCurFileType]->readData();
			if (!len) {
				closeFile();
				if ((mCurrentFileIndex + 1) < mFilenames.size()) { // 文件列表
					if (openFile(mFilenames[++mCurrentFileIndex])) { // 打开下一个文件
						len = mDeviceFiles[mCurFileType]->readData();
					}
                } else { // 文件列表读取结束
//                    receiveFinished();
                    closeDevice();
                }
			}
			return len;
		}
        int CANDeviceFile::sendFrame(CanFrame * pFrame, int len)
		{
			return 0;
		}

		bool CANDeviceFile::openFile(std::string &filename)
		{
			std::cout << __FUNCTION__ << " " << __LINE__ << " FILE: " << filename << std::endl;
			mCurFileType = IDeviceFile::fileType(filename);
			if (mCurFileType == IDeviceFile::UNKNOWN) {
                mErrorString = "open FILE device error! Unknown file type! " + filename;
				return false;
			}
			return mDeviceFiles[mCurFileType]->open(filename);
		}
		bool CANDeviceFile::closeFile()
		{
			if (mCurFileType == IDeviceFile::UNKNOWN) {
				return true;
			}

			std::cout << __FUNCTION__ << " " << __LINE__ << " FILE: " << mDeviceFiles[mCurFileType]->filename() << std::endl;
			return mDeviceFiles[mCurFileType]->close();
		}
	}
}
