﻿#include "debugcontrolform.h"
#include "ui_debugcontrolform.h"

#include "devices/devicemanager.h"
#include "devices/deviceworkerbinfile.h"
#include "analysis/calculationworker.h"
#include "analysis/analysismanager.h"
#include "analysis/analysisworker.h"
#include "analysis/protocol/analysisprotocolct410.h"
#include "master/savemanager.h"
#include "utils/settingshandler.h"
#include "alarmCompare/autorechargeform.h"
#include "views/viewsmanager.h"
#include "media/videoplayerform.h"
#include "dataprocess/iinterpolation.h"

#include <QThread>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>

DebugControlForm::DebugControlForm( Views::ViewsManager* pViewManager, Core::SaveManager *saveManager, Devices::Can::DeviceManager *deviceManager, Analysis::AnalysisManager *analysisManager, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::DebugControlForm),
    CalculationWorker(analysisManager->calculationWorker()),
    mViewManager( pViewManager ),
    mAnalysisManager( analysisManager )
{
    ui->setupUi(this);
    mSaveManager = saveManager;
    mDevManager = deviceManager;
    for (int i = 4; i < 8; ++i) {
        ui->comboBoxMasterRadar->addItem(QString::number(i), i);
        ui->comboBoxSlaveRadar->addItem(QString::number(i), i);
    }

    ui->lineEditSkip->setText("-1");
    ui->lineEditSkip->setValidator(new QIntValidator(ui->lineEditSkip));
    ui->pushButtonNextFrame->setEnabled( false );
    ui->pushButtonPrevFrame->setEnabled( false );

    QThread *thread = new QThread;
    mDeviceWorkerBinFile = new Devices::Can::DeviceWorkerBinFile;
    mDeviceWorkerBinFile->moveToThread(thread);

    connect(thread, &QThread::started, mDeviceWorkerBinFile, &Devices::Can::IDeviceWorker::run);
    connect(mDeviceWorkerBinFile, &Devices::Can::IDeviceWorker::opened, thread, [=]() { thread->start(); });
    connect(mDeviceWorkerBinFile, &Devices::Can::IDeviceWorker::closed, thread, &QThread::quit);
    connect(mDeviceWorkerBinFile, &Devices::Can::IDeviceWorker::destroyed, thread, &QThread::deleteLater);
    connect( ui->checkBoxSingleFrame, &QCheckBox::stateChanged, this, [=](int arg){
        ui->pushButtonNextFrame->setEnabled(arg);
        ui->pushButtonPrevFrame->setEnabled(arg);
        mDeviceWorkerBinFile->setReceiveDirection( true );
        mDeviceWorkerBinFile->setPauseFlag( arg == Qt::Unchecked ? false : true );
    } );
    connect( mDeviceWorkerBinFile, &Devices::Can::DeviceWorkerBinFile::showCurFrameNum, this,  &DebugControlForm::showCurFrameNumber);

    connect( ui->checkBoxHil, &QCheckBox::stateChanged, this, [=](int arg){
        enableHilWidget();
        //setHilMode();
//        if( arg == Qt::Unchecked ){
//            enterHilMode( HilType::HilType_Normal );
//        }else{
//            enterHilMode( ui->comboBoxHilType->currentIndex() == 0 ?
//                              HilType::HilType_Raw : HilType::HilType_Track );
//        }
    } );


    connect(mDeviceWorkerBinFile, &Devices::Can::IDeviceWorker::closed, this, &DebugControlForm::stateChanged);
    connect( mDeviceWorkerBinFile, &Devices::Can::DeviceWorkerBinFile::prevFrameNotReady, this, [=](){
        ui->pushButtonPrevFrame->setEnabled(false);
    } );

    Analysis::AnalysisWorker *analysisWorker = analysisManager->analysisWorker();
    connect(mDeviceWorkerBinFile, &Devices::Can::IDeviceWorker::frameRecieved, analysisWorker, &Analysis::AnalysisWorker::canFrame);
    connect( mDeviceWorkerBinFile, &Devices::Can::DeviceWorkerBinFile::currentFrameTimestemp, this, &DebugControlForm::currentFrameTimeChanged );
    connect( mDeviceWorkerBinFile, &Devices::Can::DeviceWorkerBinFile::fileBeginEndTimestemp, this, &DebugControlForm::fileBeginEndTimestemp );


    connect(this, &DebugControlForm::destroyed, thread, &QThread::deleteLater);

//    CalculationWorker->setDataProcess(true);

    enableHilWidget();
    loadSettings();

    mDeviceWorkerBinFile->setBinaryInterval(50);
}

DebugControlForm::~DebugControlForm()
{
    delete ui;
}

void DebugControlForm::loadSettings()
{
    mFilenames = SETTINGS_GET_VALUE("DebugControl/Filenames").toStringList();
    ui->lineEditFiles->setText(mFilenames.join(";"));
    setFiles(mFilenames);


    ui->lineEditSkip->setText(SETTINGS_GET_VALUE("DebugControl/SkipFrame", "-1").toString());
    ui->spinBoxBinaryInterval->setValue(SETTINGS_GET_VALUE("DebugControl/Interval", 50).toInt());
    ui->lineEditEndFrameID->setText(SETTINGS_GET_VALUE("DebugControl/EndFrameID", "0x4F4,0x4F5,0x4F6,0x4F7").toString());
    ui->checkBoxUseEndFrameID->setChecked(SETTINGS_GET_VALUE("DebugControl/UseEndFrameID", false).toBool());
    on_checkBoxUseEndFrameID_clicked(ui->checkBoxUseEndFrameID->isChecked());

    ui->checkBoxDataProcess->setChecked(SETTINGS_GET_VALUE("DebugControl/DataProcess", false).toBool());
    ui->checkBoxInterpolation->setChecked(SETTINGS_GET_VALUE("DebugControl/Interpolation", false).toBool());
    ui->checkBoxRadarFusion->setChecked(SETTINGS_GET_VALUE("DebugControl/RadarFusion", false).toBool());
    ui->comboBoxMasterRadar->setCurrentText(SETTINGS_GET_VALUE("DebugControl/MasterRadarID", 4).toString());
    ui->comboBoxSlaveRadar->setCurrentText(SETTINGS_GET_VALUE("DebugControl/SlaveRadarID", 5).toString());
    ui->checkBoxorgpoint->setChecked(SETTINGS_GET_VALUE("DebugControl/isorgpoint", false).toBool());

    ui->checkBoxRadar4->setChecked(SETTINGS_GET_VALUE("DebugControl/RadarChecked4", true).toBool());
    ui->checkBoxRadar5->setChecked(SETTINGS_GET_VALUE("DebugControl/RadarChecked5", true).toBool());
    ui->checkBoxRadar6->setChecked(SETTINGS_GET_VALUE("DebugControl/RadarChecked6", true).toBool());
    ui->checkBoxRadar7->setChecked(SETTINGS_GET_VALUE("DebugControl/RadarChecked7", true).toBool());
}

void DebugControlForm::saveSettings()
{
    SETTINGS_SET_VALUE("DebugControl/Filenames", ui->lineEditFiles->text().split(";"));

    SETTINGS_SET_VALUE("DebugControl/SkipFrame", ui->lineEditSkip->text());
    SETTINGS_SET_VALUE("DebugControl/Interval", ui->spinBoxBinaryInterval->value());
    SETTINGS_SET_VALUE("DebugControl/EndFrameID", ui->lineEditEndFrameID->text());
    SETTINGS_SET_VALUE("DebugControl/UseEndFrameID", ui->checkBoxUseEndFrameID->isChecked());

    SETTINGS_SET_VALUE("DebugControl/DataProcess", ui->checkBoxDataProcess->isChecked());
    SETTINGS_SET_VALUE("DebugControl/Interpolation", ui->checkBoxInterpolation->isChecked());
    SETTINGS_SET_VALUE("DebugControl/RadarFusion", ui->checkBoxRadarFusion->isChecked());
    SETTINGS_SET_VALUE("DebugControl/MasterRadarID", ui->comboBoxMasterRadar->currentData());
    SETTINGS_SET_VALUE("DebugControl/SlaveRadarID", ui->comboBoxSlaveRadar->currentData());
    SETTINGS_SET_VALUE("DebugControl/isorgpoint", ui->checkBoxorgpoint->isChecked());

    SETTINGS_SET_VALUE("DebugControl/RadarChecked4", ui->checkBoxRadar4->isChecked());
    SETTINGS_SET_VALUE("DebugControl/RadarChecked5", ui->checkBoxRadar5->isChecked());
    SETTINGS_SET_VALUE("DebugControl/RadarChecked6", ui->checkBoxRadar6->isChecked());
    SETTINGS_SET_VALUE("DebugControl/RadarChecked7", ui->checkBoxRadar7->isChecked());
}

void DebugControlForm::setBYDHDChannelRadarID(bool raw600ByChannel, int *channelRadarID, int size)
{
    qDebug() << __FUNCTION__ << __LINE__ << "message" << mDeviceWorkerBinFile << raw600ByChannel << size;
    qDebug() << __FUNCTION__ << __LINE__ << "message";
    mDeviceWorkerBinFile->setBYDHDChannelRadarID(raw600ByChannel, channelRadarID, size);
//    mAnalysisManager->analysisWorker()->setBYDHDChannelRadarID(raw600ByChannel, channelRadarID, size);
    qDebug() << __FUNCTION__ << __LINE__ << "message";
}

void DebugControlForm::stateChanged()
{
    enableHilWidget();
    if (!mDeviceWorkerBinFile->isOpened()) {
        mAnalysisManager->analysisWorker()->interpolationEnd();
    }
    ui->pushButtonStartAndStop->setText(QString::fromLocal8Bit(mDeviceWorkerBinFile->isOpened() ? "停止" : "开始"));
    ui->lineEditEndFrameID->setEnabled( !mDeviceWorkerBinFile->isOpened() && ui->checkBoxUseEndFrameID->isChecked());
    if( ui->checkBoxHil->checkState() && !mDeviceWorkerBinFile->isOpened() ){
        enterHilMode( HilType::HilType_Normal );//退出回灌模式
    }
    if( ui->checkBoxSaveCsv->checkState() ){
        if( mDeviceWorkerBinFile->isOpened() ){
            mSaveManager->startSave();
        }else{
            mSaveManager->stopSave();
        }
    }

    if( ui->checkBoxHil->checkState() ){
        if( mDeviceWorkerBinFile->isOpened() ){
            emit HILBegin( ui->lineEditFiles->text() );
        }else{
            emit HILEnd( ui->lineEditFiles->text() );
        }
    }

    //qDebug() << __FUNCTION__ << __LINE__ << " open or close file!";
}

void DebugControlForm::on_pushButtonGetOpenFiles_clicked()
{
    if (ui->checkBoxBatchGeneration->isChecked()){
        QString dir = QFileDialog::getExistingDirectory(this, tr("Open Directory"),
                                                          ui->lineEditFiles->text(),
                                                          QFileDialog::ShowDirsOnly
                                                          | QFileDialog::DontResolveSymlinks);
        if (!dir.isEmpty()) {
            ui->lineEditFiles->setText(dir);
        }
    } else {
    QString filter = tr("File (*.binary *.blf *.asc);; bin (*.binary);; BLF (*.blf);; ASC (*.asc);; CSV (*.csv);; all (*.*)");
    QStringList files = QFileDialog::getOpenFileNames(
                              this,
                              QString::fromLocal8Bit("选择回放文件"),
                              mFilenames.size() ? mFilenames.at(0) : "./",
                              filter);
    if (!files.isEmpty()) {
        ui->lineEditFiles->setText(files.join(";"));
        mFilenames = files;
    }

    setFiles(ui->lineEditFiles->text().split(";", Qt::SkipEmptyParts));
    }
}

void DebugControlForm::on_pushButtonStartAndStop_clicked()
{
	mAnalysisManager->analysisWorker()->clearAnalysisData();
    // 分析: 点击开始时进入这里,期望此时关联到ADAS的仿真.
    if (!mDeviceWorkerBinFile->isOpened())
    {
        //生效配置-否则每次跑数据都要配置一次，不然会没有清空trk的buff
        on_pushButtonSettings_clicked();

        //重新读取，清空之前的读取Cnt
        mDeviceWorkerBinFile->setCurFrameNum(0);

        // 分析: 这里决定是否调用回灌. 类似的可以将ADAS的初始化在此处进行
        if( ui->checkBoxHil->checkState() )
        {
            setHilMode();//进入回灌模式
        }
//        qint64 skipNum = ui->lineEditSkip->text().toInt();
        //mDeviceWorkerBinFile->setSkipFrameNum( skipNum < 0 ? skipNum : skipNum * radarCount);

//        if( skipNum != -1 ){
//            mDeviceWorkerBinFile->skipToTime( skipNum*1000 );
//        }

        if (!setEndFrameID()) { // 还会设置插值的雷达ID
            qDebug() << __FUNCTION__ << __LINE__ << "message";
            return;
        }
        setSkipParam();
        if( !setHilParam() )
            return;

        mDeviceWorkerBinFile->open(ui->checkBoxInterpolation->isChecked());
        stateChanged();
//        mSaveManager->startSave();
        mViewManager->deviceOpened();
    } else {
        mDeviceWorkerBinFile->close();
//        mSaveManager->stopSave();
        mViewManager->deviceClosed();
    }

    //stateChanged();
}

void DebugControlForm::setFiles(const QStringList &files)
{
    Devices::Can::DeviceSettings settings = mDeviceWorkerBinFile->deviceSettings();
    settings.mDeviceFileSettings.clear();
    settings.mDeviceFileSettings << Devices::Can::DeviceFileSettings{0, files};
    mDeviceWorkerBinFile->setDeviceChannelSettings(settings);

    mAnalysisManager->analysisWorker()->interpolation()->setFiles(files);
}

void DebugControlForm::enableHilWidget()
{
    bool bEnable = true;
    if( mDeviceWorkerBinFile->isOpened() ){
        bEnable = false;
        ui->checkBoxHil->setEnabled( false );
    }else{
        ui->checkBoxHil->setEnabled( true );
        if( !ui->checkBoxHil->isChecked() ){
            bEnable = false;
        }
    }

//    ui->checkBoxRaw->setEnabled( bEnable );
//    ui->checkBoxTrack->setEnabled( bEnable );
    ui->comboBoxHilType->setEnabled( bEnable );
//    ui->checkBoxRadar4->setEnabled( bEnable );
//    ui->checkBoxRadar5->setEnabled( bEnable );
//    ui->checkBoxRadar6->setEnabled( bEnable );
//    ui->checkBoxRadar7->setEnabled( bEnable );
    ui->comboBoxRadarChannel4->setEnabled( bEnable );
    ui->comboBoxRadarChannel5->setEnabled( bEnable );
    ui->comboBoxRadarChannel6->setEnabled( bEnable );
    ui->comboBoxRadarChannel7->setEnabled( bEnable );
}

void DebugControlForm::on_pushButtonSettings_clicked()
{
    CalculationWorker->setDataProcess(ui->checkBoxDataProcess->isChecked(),
                                      ui->checkBoxInterpolation->isChecked(),
                                      ui->checkBoxRadarFusion->isChecked(),
                                      ui->comboBoxMasterRadar->currentData().toUInt(),
                                      ui->comboBoxSlaveRadar->currentData().toUInt(),
                                      ui->checkBoxorgpoint->isChecked(),
                                      ui->checkBoxShowCandi->isChecked(),
                                      ui->comboBoxEOL->currentData().toUInt());
}

void DebugControlForm::on_pushButtonNextFrame_clicked()
{
    mDeviceWorkerBinFile->setReceiveDirection( true );
    mDeviceWorkerBinFile->setNextFrameNum(1);
    ui->pushButtonPrevFrame->setEnabled( true );
}

void DebugControlForm::on_spinBoxBinaryInterval_valueChanged(int arg1)
{
    mDeviceWorkerBinFile->setBinaryInterval(arg1);
}

bool DebugControlForm::setHilParam()
{
    if( ui->checkBoxHil->checkState() == Qt::Unchecked ){
        mDeviceWorkerBinFile->setHilFlag(  false, mDevManager->deviceWorker() );
        //清空过滤的雷达ID和帧ID，因为回放也会通过这些参数去过滤
        mDeviceWorkerBinFile->clearNoFilterFramID();
        mDeviceWorkerBinFile->clearFilterRatarID();
    }else{
        if( !mDevManager->isOpened() ){
            QMessageBox::warning(this, QString::fromLocal8Bit("提示"), QString::fromLocal8Bit("请打开设备!"));
            emit HILEnd( ui->lineEditFiles->text() );
            return false;
        }

        mDeviceWorkerBinFile->setHilFlag(  true, mDevManager->deviceWorker() );
        mDeviceWorkerBinFile->setNoFilterFramID( 0x3F0, false );
        mDeviceWorkerBinFile->setNoFilterFramID( 0x4F0, false );
        if( ui->comboBoxHilType->currentIndex() == 0 ){
            //不过滤原始点
            mDeviceWorkerBinFile->setNoFilterFramID( 0x400, false );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x410, false );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x600, false );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x710, false );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x250, false );
            //过滤跟踪点
            mDeviceWorkerBinFile->setNoFilterFramID( 0x430, true );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x440, true );
        }else{
            //过滤原始点
            mDeviceWorkerBinFile->setNoFilterFramID( 0x400, true );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x410, true );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x600, true );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x710, true );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x250, true );
            //不过滤跟踪点
            mDeviceWorkerBinFile->setNoFilterFramID( 0x430, false );
            mDeviceWorkerBinFile->setNoFilterFramID( 0x440, false );
        }
        mDeviceWorkerBinFile->setNoFilterFramID( 0x4C0, false );//不过滤报警

        //4号雷达
        mDeviceWorkerBinFile->setHilRadarChannel( 4, ui->comboBoxRadarChannel4->currentIndex() );
        //5号雷达
        mDeviceWorkerBinFile->setHilRadarChannel( 5, ui->comboBoxRadarChannel5->currentIndex() );
        //6号雷达
        mDeviceWorkerBinFile->setHilRadarChannel( 6, ui->comboBoxRadarChannel6->currentIndex() );
        //7号雷达
        mDeviceWorkerBinFile->setHilRadarChannel( 7, ui->comboBoxRadarChannel7->currentIndex() );

        mDeviceWorkerBinFile->filterAllRatarID(); //现将所有雷达ID过滤，防止未过滤小于4，大于7雷达的报文
    }

    //4号雷达
    mDeviceWorkerBinFile->setFilterRatarID( 4, ui->checkBoxRadar4->checkState() == Qt::Unchecked );
    //5号雷达
    mDeviceWorkerBinFile->setFilterRatarID( 5, ui->checkBoxRadar5->checkState() == Qt::Unchecked );
    //6号雷达
    mDeviceWorkerBinFile->setFilterRatarID( 6, ui->checkBoxRadar6->checkState() == Qt::Unchecked );
    //7号雷达
    mDeviceWorkerBinFile->setFilterRatarID( 7, ui->checkBoxRadar7->checkState() == Qt::Unchecked );

    return true;
}

void DebugControlForm::setHilMode()
{
    if( ui->checkBoxHil->checkState() == Qt::Unchecked ){
        enterHilMode( HilType::HilType_Normal );
    }else{
        enterHilMode( ui->comboBoxHilType->currentIndex() == 0 ?
                          HilType::HilType_Raw : HilType::HilType_Track );
    }
}

void DebugControlForm::setSkipParam()
{
    qint64 skipNum = ui->lineEditSkip->text().toInt();
    if( ui->comboBoxSkipType->currentIndex() == 0 )
    {
        //帧跳转,有跳转时默认直接开启单帧调试
        if(skipNum >= 0)
        {
            ui->checkBoxSingleFrame->setCheckState(Qt::Checked);
        }
        mDeviceWorkerBinFile->setSkipFrameNum( skipNum < 0 ? skipNum : skipNum /* radarCount*/);
    }
    else
    {  //秒数跳转
        if( skipNum != -1 )
        {
            mDeviceWorkerBinFile->skipToTime( skipNum*1000 );
        }
    }
}

bool DebugControlForm::setEndFrameID()
{
    int radarID = 4;
    QString text = ui->lineEditEndFrameID->text();
    QStringList strList = text.split(",");
    QList<quint64> radarIDList,endFrameIDList,endFrameIDListOther,beginFrameIDList;
    if (ui->checkBoxUseEndFrameID->isChecked()) {
        for( int i=0; i<strList.size(); i++ )
        {
            endFrameIDListOther.append( strList[i].toUInt( NULL, 16 ) );
        }
    }
    if(ui->checkBoxRadar4->checkState() == Qt::Checked)
    {
        radarID = 4;
        radarIDList.append(4);
        endFrameIDList.append(0x4F4);
        beginFrameIDList.append(0x604);
    }
    if(ui->checkBoxRadar5->checkState() == Qt::Checked)
    {
        radarID = 5;
        radarIDList.append(5);
        endFrameIDList.append(0x4F5);
        beginFrameIDList.append(0x605);
    }
    if(ui->checkBoxRadar6->checkState() == Qt::Checked)
    {
        radarID = 6;
        radarIDList.append(6);
        endFrameIDList.append(0x4F6);
        beginFrameIDList.append(0x606);
    }
    if(ui->checkBoxRadar7->checkState() == Qt::Checked)
    {
        radarID = 7;
        radarIDList.append(7);
        endFrameIDList.append(0x4F7);
        beginFrameIDList.append(0x607);
    }
    qDebug() << __FUNCTION__ << __LINE__ << endFrameIDList << endFrameIDListOther << beginFrameIDList;
    mDeviceWorkerBinFile->setEndFrameIDList( radarIDList, endFrameIDList , endFrameIDListOther, beginFrameIDList);

    return mAnalysisManager->analysisWorker()->interpolationBegin(ui->checkBoxInterpolation->isChecked(), radarID);
}

void DebugControlForm::enterHilMode( HilType type )
{
    //发送回灌指令
    QByteArray data1 = QByteArray::fromHex("3158AF8000000000");
    QByteArray data2 = QByteArray::fromHex("0000000000000000");

    switch( type ){
    case HilType::HilType_Raw:
        data2 = QByteArray::fromHex("0000080000000000");
        break;
    case HilType::HilType_Track:
        data2 = QByteArray::fromHex("00000C0000000000");
        break;
    case HilType::HilType_Normal:
        data2 = QByteArray::fromHex("0000040000000000");
        break;
    default:
        return;
    }

    if( 0 == ui->comboBoxRadarChannel7->currentIndex() ){
        mDevManager->sendFrame( 0, 0x307, data1, true );
        mDevManager->sendFrame( 0, 0x207, data2, true );
    }else{
        mDevManager->sendFrame( 1, 0x307, data1, true );
        mDevManager->sendFrame( 1, 0x207, data2, true );
    }

    if( 0 == ui->comboBoxRadarChannel6->currentIndex() ){
        mDevManager->sendFrame( 0, 0x306, data1, true );
        mDevManager->sendFrame( 0, 0x206, data2, true );
    }else{
        mDevManager->sendFrame( 1, 0x306, data1, true );
        mDevManager->sendFrame( 1, 0x206, data2, true );
    }

    if( 0 == ui->comboBoxRadarChannel5->currentIndex() ){
        mDevManager->sendFrame( 0, 0x305, data1, true );
        mDevManager->sendFrame( 0, 0x205, data2, true );
    }else{
        mDevManager->sendFrame( 1, 0x305, data1, true );
        mDevManager->sendFrame( 1, 0x205, data2, true );
    }

    if( 0 == ui->comboBoxRadarChannel4->currentIndex() ){
        mDevManager->sendFrame( 0, 0x304, data1, true );
        mDevManager->sendFrame( 0, 0x204, data2, true );
    }else{
        mDevManager->sendFrame( 1, 0x304, data1, true );
        mDevManager->sendFrame( 1, 0x204, data2, true );
    }
}

void DebugControlForm::on_comboBoxHilType_currentIndexChanged(int index)
{
    if( ui->checkBoxHil->checkState() == Qt::Unchecked )
        return;

    //setHilMode();
}

void DebugControlForm::on_pushButtonPrevFrame_clicked()
{
    mDeviceWorkerBinFile->setReceiveDirection( false );
    mDeviceWorkerBinFile->setNextFrameNum(1);
}

void DebugControlForm::on_pushButtonBatchHIL_clicked()
{
    if( !mAutoRecharge ){
        mAutoRecharge = new AutoRechargeForm( /*this, */mDevManager, mAnalysisManager->analysisWorker(), this );
        mAutoRecharge->setWindowFlag( Qt::Dialog );
        //mAutoRecharge->setAttribute(Qt::WA_DeleteOnClose);
        mAutoRecharge->setWindowTitle( QString::fromLocal8Bit("批量回灌") );
    }
    mAutoRecharge->show();
}

void DebugControlForm::initBatchParam( quint8 HILMode )
{
    ui->lineEditSkip->setText( "-1" );
    //ui->spinBoxBinaryInterval->setValue( 50 );
    //ui->spinBoxBinaryInterval->setValue( 250 );
    ui->checkBoxSingleFrame->setCheckState( Qt::CheckState::Unchecked );
    ui->checkBoxSaveCsv->setCheckState( Qt::CheckState::Unchecked );
    ui->checkBoxHil->setCheckState( Qt::CheckState::Checked );
    ui->checkBoxRadar4->setCheckState( Qt::CheckState::Checked );
    ui->checkBoxRadar5->setCheckState( Qt::CheckState::Checked );
    ui->checkBoxRadar6->setCheckState( Qt::CheckState::Checked );
    ui->checkBoxRadar7->setCheckState( Qt::CheckState::Checked );
    ui->comboBoxHilType->setCurrentIndex( HILMode );
}

void DebugControlForm::autoHil(const QString &file)
{
//    ui->lineEditFiles->setText( file );
    mFilenames.clear();
    mFilenames << file;
    ui->lineEditFiles->setText(mFilenames.join(";"));
    setFiles(mFilenames);

//    if( ui->checkBoxRadar4->checkState() ){
//        mDevManager->sendFrame( ui->comboBoxRadarChannel4->currentIndex(), 0x204, QByteArray::fromHex("55504752414445FE") );
//    }
//    if( ui->checkBoxRadar5->checkState() ){
//        mDevManager->sendFrame( ui->comboBoxRadarChannel5->currentIndex(), 0x205, QByteArray::fromHex("55504752414445FE") );
//    }
//    if( ui->checkBoxRadar6->checkState() ){
//        mDevManager->sendFrame( ui->comboBoxRadarChannel6->currentIndex(), 0x206, QByteArray::fromHex("55504752414445FE") );
//    }
//    if( ui->checkBoxRadar7->checkState() ){
//        mDevManager->sendFrame( ui->comboBoxRadarChannel7->currentIndex(), 0x207, QByteArray::fromHex("55504752414445FE") );
//    }

//    //雷达重启 等待3S
//    QEventLoop loop;
//    QTimer timer;
//    timer.setSingleShot( true );
//    connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
//    timer.start( 3000 );
//    loop.exec();
//    timer.stop();

    on_pushButtonStartAndStop_clicked();
}

void DebugControlForm::showCurFrameNumber(quint64 num)
{
    ui->lb_CuiFrameCnt->setText(QString::number(num));
}

void DebugControlForm::currentFrameTimeChanged(quint64 curMs)
{
    mCurrentFrameTime = curMs;
    QString curFrameTime = QString::fromLocal8Bit( "当前帧时间:%1")
            .arg( QDateTime::fromMSecsSinceEpoch( mCurrentFrameTime ).toString( "yyyy-MM-dd HH:mm:ss:zzz" ) );
    ui->labelCurrentFrameTime->setText( curFrameTime );
}

void DebugControlForm::fileBeginEndTimestemp(quint64 beginMs, quint64 endMs)
{
    mFileBeginTime = beginMs;
    mFileEndTime = endMs;
    QString beginEndTime = QString::fromLocal8Bit( "文件起止时间:%1至%2")
            .arg( QDateTime::fromMSecsSinceEpoch( mFileBeginTime ).toString( "yyyy-MM-dd HH:mm:ss:zzz" ) )
            .arg( QDateTime::fromMSecsSinceEpoch( mFileEndTime ).toString( "yyyy-MM-dd HH:mm:ss:zzz" ) );
    ui->labelFileBeginEndTime->setText( beginEndTime );
}

void DebugControlForm::on_pushButtonVideoPlayer_clicked()
{
    if( !mVideoPlayser ){
        mVideoPlayser = new VideoPlayerForm( this );
        mVideoPlayser->setWindowFlag( Qt::Dialog );
        //mVideoPlayser->setAttribute(Qt::WA_DeleteOnClose);
        mVideoPlayser->setWindowTitle( QString::fromLocal8Bit("视频播放") );
        //connect( mDeviceWorkerBinFile, &Devices::Can::DeviceWorkerBinFile::currentFrameTimestemp, mVideoPlayser, &VideoPlayerForm::skipToVideoFrame );
        connect( mVideoPlayser, &VideoPlayerForm::skipToTimestamp, this, &DebugControlForm::skipToTimestamp );
    }
    mVideoPlayser->show();
}

void DebugControlForm::on_pushButtonVideoSkip_clicked()
{
    if( mVideoPlayser ){
        mVideoPlayser->skipToVideoFrame( mCurrentFrameTime );
    }
}

void DebugControlForm::skipToTimestamp(quint64 timestamp)
{
    if( !ui->checkBoxSingleFrame->checkState() ){
        QMessageBox::warning( this,
                              QString::fromLocal8Bit("提示"),
                              QString::fromLocal8Bit("仅单帧模式下可跳转!") );
        return;
    }
    if( !mDeviceWorkerBinFile->isOpened() ){
        QMessageBox::warning( this,
                              QString::fromLocal8Bit("提示"),
                              QString::fromLocal8Bit("未打开文件!") );
        return;
    }
    if( timestamp < mFileBeginTime || timestamp > mFileEndTime ){
        QMessageBox::warning( this,
                              QString::fromLocal8Bit("提示"),
                              QString::fromLocal8Bit("请核实文件起止时间及跳转时间!") );
        return;
    }
    if( timestamp < mCurrentFrameTime ){
        QMessageBox::warning( this,
                              QString::fromLocal8Bit("提示"),
                              QString::fromLocal8Bit("跳转时间在当前时间之前，请重新打开文件再跳转!") );
        return;
//        on_pushButtonStartAndStop_clicked();
//        on_pushButtonStartAndStop_clicked();
    }
    mDeviceWorkerBinFile->skipToTime( timestamp - mFileBeginTime );
    return;
}

void DebugControlForm::on_checkBoxShowCandi_stateChanged(int arg1)
{
    if(arg1 == 0)
    {
        CalculationWorker->setIsShowCandiObj(false);
    }
    else
    {
        CalculationWorker->setIsShowCandiObj(true);
    }
}

void DebugControlForm::on_pushButtonBatchGeneration_clicked()
{
    if (ui->pushButtonBatchGeneration->text() == QString::fromLocal8Bit("批量生成")) {
    QString path = ui->lineEditFiles->text();
    if (path.isEmpty() || !QFileInfo(path).isDir()) {
        return;
    }

    QStringList files;
    QDir dir(path);
    QFileInfoList fileInfoList = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot | QDir::NoSymLinks);
    foreach (const QFileInfo info, fileInfoList) {
        QString dataFile = info.absoluteFilePath().append("/CAN/CanData ").append(info.baseName()).append(".asc");
        if (QFileInfo(dataFile).exists()) {
            files.append(dataFile);
        }
    }

    setFiles(files);


    mSaveManager->startSave();
    mDeviceWorkerBinFile->open();
    } else {
        mDeviceWorkerBinFile->close();
        mSaveManager->stopSave();
    }
}

void DebugControlForm::on_checkBoxBatchGeneration_clicked(bool checked)
{
    ui->pushButtonBatchGeneration->setEnabled(checked);
}

void DebugControlForm::on_checkBoxUseEndFrameID_clicked(bool checked)
{
    ui->lineEditEndFrameID->setEnabled( !mDeviceWorkerBinFile->isOpened() && checked);
}

void DebugControlForm::on_comboBoxEOL_currentIndexChanged(int index)
{
    CalculationWorker->setEOLData(index);
    CalculationWorker->setEOLRunStateFunc();
}

void DebugControlForm::on_comboBoxSkipType_activated(int index)
{

}
