﻿#ifndef BYDHOPROTOCOL_H
#define BYDHOPROTOCOL_H

#include "ianalysisprotocol.h"

namespace Analysis {
namespace Protocol {

class BYDHOProtocol : public IAnalysisProtocol
{
    Q_OBJECT
public:
    BYDHOProtocol( AnalysisWorker *analysisWorker, QObject *parent = nullptr );

    void setChannelRadarID(int *channelRadarID, int size);

    bool analysisFrame(const Devices::Can::CanFrame &frame) override;

private:
    bool clearRawTrack(quint8 radarID, const Devices::Can::CanFrame &frame );
    bool clear16Track(quint8 radarID, const Devices::Can::CanFrame &frame );
    bool decode_CR_FL_0x640(quint8 radarID, const Devices::Can::CanFrame &frame );
    bool decode_CR_FL_0x640_MRR(quint8 radarID, const Devices::Can::CanFrame &frame );
    bool decode_CR_FL_0x659(quint8 radarID, const Devices::Can::CanFrame &frame );
    bool decode_CR_FL_0x66D(quint8 radarID, const Devices::Can::CanFrame &frame );
    bool decode_CR_FL_0x66E(quint8 radarID, const Devices::Can::CanFrame &frame );

    int mBYDHOTargetCurrentIndex[MAX_RADAR_COUNT];

    int mBYDHDChannelRadarID[8]{4, 5, 6, 7, 0, 0, 0, 0};                ///< BYD高阶通道-雷达ID设置
};

}
}

#endif // BYDHOPROTOCOL_H
