#ifndef FRAMECONVERTMENU_H
#define FRAMECONVERTMENU_H

#include <QObject>
#include <QMenu>

class FrameConver_Haval2Geely;

namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
}

class FrameConvertMenu : public QMenu
{
    Q_OBJECT
public:
    explicit FrameConvertMenu(Devices::Can::DeviceManager *deviceManager,QWidget *parent = nullptr);


private:
    FrameConver_Haval2Geely* mH2G;
    Devices::Can::DeviceManager *mDeviceManager;
};

#endif // FRAMECONVERTMENU_H
