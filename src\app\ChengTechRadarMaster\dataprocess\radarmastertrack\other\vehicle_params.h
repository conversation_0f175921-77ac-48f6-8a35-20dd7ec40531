﻿#ifndef __VEHICLE_PARAMS_H__
#define __VEHICLE_PARAMS_H__

// #define TangEV
//#define UXE
// #define SONGSF
// #define SONGUF
// #define GWM
// #define UKE
#define MR25

#ifdef TangEV
#define MOUNT6POS_TO_OUTER_EDGE_X_FL 0.3874f												//左前角雷达车身外边缘在X轴方向的距离,D5
#define MOUNT6POS_TO_OUTER_EDGE_Y_FL 0.2601f												//左前角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感

#define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.31088f												//左后角雷达与后轴中心在X轴方向的距离,D6
#define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.263f												//左后角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感
#endif

#ifdef SONGSF
#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL 3.37095f										// 前角雷达连线与后轴中心在Y轴方向的距离,D1
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL 0.88773f										// 左前角雷达到中轴线在X轴方向的距离,左D3

#define MOUNT6POS_TO_OUTER_EDGE_X_FL 0.10088f											// 左前角雷达到车身外边缘在X轴方向的距离,左D5
#define MOUNT6POS_TO_OUTER_EDGE_Y_FL 0.36361f											// 左前角雷达到车身外边缘在Y轴方向的距离,左D9

#define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.09641f											// 左后角雷达到车身外边缘在X轴方向的距离,左D6
#define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.4f												// 左后角雷达到车身外边缘在Y轴方向的距离,左D10
#endif

#ifdef SONGUF
#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL 3.41718f										// 前角雷达连线与后轴中心在Y轴方向的距离,D1
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL 0.86709f										// 左前角雷达到中轴线在X轴方向的距离,左D3

#define MOUNT6POS_TO_OUTER_EDGE_X_FL 0.08639f											// 左前角雷达到车身外边缘在X轴方向的距离,左D5
#define MOUNT6POS_TO_OUTER_EDGE_Y_FL 0.42335f											// 左前角雷达到车身外边缘在Y轴方向的距离,左D9

#define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.25422f											// 左后角雷达到车身外边缘在X轴方向的距离,左D6
#define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.1696f											// 左后角雷达到车身外边缘在Y轴方向的距离,左D10
#endif

#ifdef UXE

#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL 3.43894f										// 前角雷达连线与后轴中心在Y轴方向的距离,D1
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL 0.87392f										// 左前角雷达到中轴线在X轴方向的距离,左D3

#define MOUNT6POS_TO_OUTER_EDGE_X_FL 0.07f												//左前角雷达车身外边缘在X轴方向的距离,D5
#define MOUNT6POS_TO_OUTER_EDGE_Y_FL 0.409f												//左前角雷达到车身外边缘在Y轴方向的距离,左D9

// #define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.179f												//左后角雷达与后轴中心在X轴方向的距离,D6
// #define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.240f												//左后角雷达到车身外边缘在Y轴方向的距离,左D10
// 暂时设为0
#define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.264f												//左后角雷达与后轴中心在X轴方向的距离,D6
#define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.1932f												//左后角雷达到车身外边缘在Y轴方向的距离,左D10
#endif

#ifdef HAIBAO
#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL 3.39631f										// 前角雷达连线与后轴中心在Y轴方向的距离,D1
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL 0.82408f										// 左前角雷达到中轴线在X轴方向的距离,左D3

#define MOUNT6POS_TO_OUTER_EDGE_X_FL 0.10191f											// 左前角雷达到车身外边缘在X轴方向的距离,左D5
#define MOUNT6POS_TO_OUTER_EDGE_Y_FL 0.402745f											// 左前角雷达到车身外边缘在Y轴方向的距离,左D9

#define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.16055f											// 左后角雷达到车身外边缘在X轴方向的距离,左D6
#define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.3067f											// 左后角雷达到车身外边缘在Y轴方向的距离,左D10
#endif

#ifdef GWM
#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL 3.43894f										// 前角雷达连线与后轴中心在Y轴方向的距离,D1
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL 0.82408f										// 左前角雷达到中轴线在X轴方向的距离,左D3

#define MOUNT6POS_TO_OUTER_EDGE_X_FL 0.073f												//左前角雷达车身外边缘在X轴方向的距离,D5
#define MOUNT6POS_TO_OUTER_EDGE_Y_FL 0.0f												//左前角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感

#define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.264f												//左后角雷达与后轴中心在X轴方向的距离,D6
#define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.0f												//左后角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感
#endif

#ifdef UKE
#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL 3.41319f										// 前角雷达连线与后轴中心在Y轴方向的距离,D1
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL 0.86614f										// 左前角雷达到中轴线在X轴方向的距离,左D3

#define MOUNT6POS_TO_OUTER_EDGE_X_FL 0.06615f												//左前角雷达车身外边缘在X轴方向的距离,D5
#define MOUNT6POS_TO_OUTER_EDGE_Y_FL 0.34852f												//左前角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感

#define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.1642f											    //左后角雷达与后轴中心在X轴方向的距离,D6
#define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.23706f												//左后角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感
#endif

#ifdef MR25
#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL 3.8040f										//左前角雷达与后轴中心在X轴方向的距离,D1
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL 0.7640f										//左前角雷达与后轴中心在Y轴方向的距离,D3

#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL -0.91400f									//左后角雷达与后轴中心在X轴方向的距离,D2
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL 0.80400f										//左后角雷达与后轴中心在Y轴方向的距离,D4

#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL				//右前角雷达与后轴中心在X轴方向的距离
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL * (-1)		//右前角雷达与后轴中心在Y轴方向的距离

#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL				//右后角雷达与后轴中心在X轴方向的距离
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL * (-1)		//右后角雷达与后轴中心在Y轴方向的距离

#define MOUNT6POS_TO_OUTER_EDGE_X_FL 0.19600f												//左前角雷达车身外边缘在X轴方向的距离,D5
#define MOUNT6POS_TO_OUTER_EDGE_Y_FL 0.2810f												//左前角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感D9

#define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.15600f												//左后角雷达与后轴中心在X轴方向的距离,D6
#define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.2370f												//左后角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感 D10
#endif

#define MOUNT7POS_TO_OUTER_EDGE_X_FL MOUNT6POS_TO_OUTER_EDGE_X_FL						//右前角雷达车身外边缘在X轴方向的距离,与左前角对称
#define MOUNT7POS_TO_OUTER_EDGE_Y_FL MOUNT6POS_TO_OUTER_EDGE_Y_FL						//右前角雷达与后轴中心在Y轴方向的距离,与左前角对称

#define MOUNT5POS_TO_OUTER_EDGE_X_FL MOUNT4POS_TO_OUTER_EDGE_X_FL						//右后角雷达车身外边缘在X轴方向的距离,与左后角对称
#define MOUNT5POS_TO_OUTER_EDGE_Y_FL MOUNT4POS_TO_OUTER_EDGE_Y_FL						//右后角雷达与后轴中心在Y轴方向的距离,与左后角对称

#define DOW_WARNING_WIDTH_MIN                (0.0) // (0.f)

#define VEHICLE_WIDTH_INFO ((MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL + MOUNT6POS_TO_OUTER_EDGE_X_FL) * 2) // 车宽

#endif // __VEHICLE_PARAMS_H__
