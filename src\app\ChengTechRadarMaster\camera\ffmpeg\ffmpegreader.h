﻿#ifndef FFMPEGREADER_H
#define FFMPEGREADER_H

#ifdef __cplusplus
extern "C" {
#endif
#include <libavdevice/avdevice.h>
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavfilter/buffersink.h>
#include <libavfilter/buffersrc.h>
#include "libavfilter/avfilter.h"
#include "libavutil/avutil.h"
#include "libavutil/pixfmt.h"
#include "libavutil/error.h"
#include "libavutil/imgutils.h"
#include "libswscale/swscale.h"
#ifdef __cplusplus
}
#endif

#include <string>

class FFmpegReader
{
public:
    FFmpegReader();
    FFmpegReader(std::string url, bool isFile);
    ~FFmpegReader();

    bool open();
    bool open(std::string url, bool isFile);
    bool close();
    bool isOpened() const {return mOpened; }
    AVStream *streamIn() { return mpStreamsInput; }

    AVFrame *read();

private:
    std::string mURL;
    bool mIsFile{false};
    bool mOpened{false};
    std::string mErrorString;

    AVFormatContext *mpFormatContextInput{0};    ///< 封装格式上下文
    AVCodecContext  *mpCodecContextInput{0};     ///< 编解码器上下文
    AVStream *mpStreamsInput{0};
    AVPacket *mpPacketInput{0};
    AVFrame *mpFrame{0};
};

#endif // FFMPEGREADER_H
