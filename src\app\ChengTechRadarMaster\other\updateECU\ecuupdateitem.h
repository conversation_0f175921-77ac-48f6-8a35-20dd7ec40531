#ifndef ECUUPDATEITEM_H
#define ECUUPDATEITEM_H

#include "../devices/canframe.h"

#include <QObject>
#include <QLibrary>
#include <QDomElement>

namespace Functions {
class UDS;
};

namespace Devices {
namespace Can {
class DeviceManager;
}
}

class ECUUpdateItem : public QObject
{
    Q_OBJECT
public:
    enum TYPE{
        ERROR = 0,
        Setting,    //设置
        General,    //的普通指令
        Security,   //使用动态库进入安全校验
        Download,   //下载
        Delay,      //延时
        SendCmd    //发送指令
    };

public:
    ECUUpdateItem( Devices::Can::DeviceManager * deviceManager );
    ~ECUUpdateItem();

    virtual bool init( const QDomElement& element );
    bool run( quint8 channelIndex );

    quint32 funAddr() { return mFunAddr; };
    quint32 phyAddr() { return mPhyAddr; };
    quint32 dstAddr() { return mDstAddr; };

    void setFunAddr( quint32 addr ) { mFunAddr = addr; };
    void setPhyAddr( quint32 addr ) { mPhyAddr = addr; };
    void setDstAddr( quint32 addr ) { mDstAddr = addr; };

    QString describeStr() { return mDesc; };
    virtual QString dataStr() = 0;

    bool isAbnormal() { return mAbnormal; };
    QString abnormalStr() { return mAbnormalStr; };

    bool isRuned(){ return mRuned; };
    bool runResult(){ return mRunResult; };
    void initRunResult();

    TYPE type() { return mType; };

protected:
    virtual bool exec( quint8 channelIndex ) = 0;
    bool getChildElement( const QDomElement& element, const QString& tagName, QDomElement& childElement );

private slots:
    void frameRecieved(const Devices::Can::CanFrame &frame);

protected:
    quint32 mFunAddr{0}; //功能地址
    quint32 mPhyAddr{0}; //物理地址
    quint32 mDstAddr{0}; //响应地址

    bool mUseFunAddr{false}; //是否使用功能地址
    QString mDesc;

    bool  mAbnormal{false}; //是否异常
    QString mAbnormalStr; //异常信息

    bool mRuned{false};
    bool mRunResult{false};

    TYPE mType{TYPE::ERROR};

    Functions::UDS* mUDS{NULL};
    Devices::Can::DeviceManager * mDeviceManager{NULL};
};

class ECUUpdateItem_Setting : public ECUUpdateItem
{
    Q_OBJECT
public:
    ECUUpdateItem_Setting( Devices::Can::DeviceManager * deviceManager );

    virtual bool init( const QDomElement& element );
    virtual bool exec( quint8 channelIndex ){ return true; };
    virtual QString dataStr(){ return ""; };
};

class ECUUpdateItem_General : public ECUUpdateItem
{
    Q_OBJECT
public:
    ECUUpdateItem_General( Devices::Can::DeviceManager * deviceManager );

    virtual bool init( const QDomElement& element );
    virtual bool exec( quint8 channelIndex );
    virtual QString dataStr();
    QByteArray cmd();
    void updateMatchData( const QString& desc, const QByteArrayList& words,
                          bool bWholeWordMatch, bool bEqual );

protected:
    bool resultMatch( const QByteArray& data );
    QString dataStr2();

protected:
    quint8 mSid{0};
    QByteArray mData;

    //返回结果匹配参数，用于验证版本，及校验DTC黑名单
    QString mMatchDesc; //匹配说明
    bool mWholeWordMatch{false}; //是否为整词匹配，否则为子串匹配
    QByteArrayList mWords; //用于匹配的字符串
    //整词匹配:true表示相等才匹配成功，否则不相等才匹配成功
    //子串匹配:true表示包含才匹配成功，否则不包含才匹配成功
    bool mEqual{true};
};

class ECUUpdateItem_Security : public ECUUpdateItem
{
    Q_OBJECT
public:
    ECUUpdateItem_Security( Devices::Can::DeviceManager * deviceManager );
    ~ECUUpdateItem_Security();

    virtual bool init( const QDomElement& element );
    virtual bool exec( quint8 channelIndex );
    virtual QString dataStr();

    bool loadDLLFunPtr( const QString& path );

protected:
    typedef int  (*DLL_Seed2Key)(	const unsigned char*	iSeedArray,			/* Array for the seed [in] */
                        const unsigned short	iSeedArraySize,		/* Length of the array for the seed [in] */
                        const unsigned int		iSecurityLevel,		/* Security level [in] */
                        const char*				iVariant, 			/* Name of the active variant [in] */
                        unsigned char*			ioKeyArray, 		/* Array for the key [in, out] */
                        unsigned int			iKeyArraySize,		/* Maximum length of the array for the key [in] */
                        unsigned int*			oSize);

protected:
    quint8 mLevel{0};
    QString mDLL;
    DLL_Seed2Key mDLLFunPtr{ NULL };
    QLibrary mLibrary;
};

class ECUUpdateItem_Download : public ECUUpdateItem
{
    Q_OBJECT
public:
    ECUUpdateItem_Download( Devices::Can::DeviceManager * deviceManager );

    virtual bool init( const QDomElement& element );
    virtual bool exec( quint8 channelIndex );
    virtual QString dataStr();

    bool loadFile( const QString& path );

protected:
    bool readHexFile( const QString &filename );

protected:
    QString mFile;
    bool mErase{false}; //是否需要擦除内存
    QByteArray mData;
    quint32 mAddress{0x00};
};

class ECUUpdateItem_Delay : public ECUUpdateItem
{
    Q_OBJECT
public:
    ECUUpdateItem_Delay( Devices::Can::DeviceManager * deviceManager );

    virtual bool init( const QDomElement& element );
    virtual bool exec( quint8 channelIndex );
    virtual QString dataStr();

protected:
    quint32 mDelayMS; //延时-毫秒
};

class ECUUpdateItem_SendCmd : public ECUUpdateItem
{
    Q_OBJECT
public:
    ECUUpdateItem_SendCmd( Devices::Can::DeviceManager * deviceManager );

    virtual bool init( const QDomElement& element );
    virtual bool exec( quint8 channelIndex );
    virtual QString dataStr();

protected:
    quint32 mID;
    QByteArray mData;
};



#endif // ECUUPDATEITEM_H
