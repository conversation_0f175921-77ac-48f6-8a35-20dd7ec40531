﻿#ifndef _COM_TYPES_H_
#define _COM_TYPES_H_

#ifndef PC_DBG_FW
#include "app_common.h"
#else
#include "app_common.h"
#endif

#define CAN_0 (0)
#define CAN_1 (1)

//比亚迪挡位定义
#define GEAR_SIG_INVALID       0
#define GEAR_SIG_P             1
#define GEAR_SIG_R             2
#define GEAR_SIG_N             3
#define GEAR_SIG_D             4
#define GEAR_SIG_RES           5

/**
 * @brief 报警功能开关信号结构体
 */
typedef union vehicleSwitch
{
	uint16_t word;
	struct {
		uint16_t MainSwitch       : 1;///< 主开关状态，0x0:关闭，0x1:打开
		uint16_t BSDSwtReq        : 1;///< BSD功能开关状态，0x0:关闭，0x1:打开
		uint16_t LCASwtReq        : 1;///< LCA功能开关状态，0x0:关闭，0x1:打开
		uint16_t DOWSwtReq        : 1;///< DOW功能开关状态，0x0:关闭，0x1:打开
		uint16_t RCWSwtReq        : 1;///< RCW功能开关状态，0x0:关闭，0x1:打开
		uint16_t RCTASwtReq       : 1;///< RCTA功能开关状态，0x0:关闭，0x1:打开
		uint16_t RCTABrkSwtReq    : 1;///< RCTB功能开关状态，0x0:关闭，0x1:打开
		uint16_t FCTASwtReq       : 1;///< FCTA功能开关状态，0x0:关闭，0x1:打开
		uint16_t FCTABrkSwtReq    : 1;///< FCTB功能开关状态，0x0:关闭，0x1:打开
        uint16_t BSDLCAAudioStatus: 1;///< BSD(LCA)报警模式状态，0x0:仅视觉，0x1:视觉+声音
        uint16_t reserved         : 6;///< 预留
    }bit;
} vehicleSwitch_t;

/**
 * @brief 车门信号结构体
 */
typedef union vehicleDoorStatus
{
    uint16_t byte;
    struct
    {
    	uint16_t doorFrontLe : 2;
    	uint16_t doorFrontRi : 2;
    	uint16_t doorRearLe  : 2;
    	uint16_t doorRearRi  : 2;
    	uint16_t doorBack    : 2;
    	uint16_t formerHatch : 2;
    } bit;
} vehicleDoorStatus_t;

/**
 * @brief 车门锁信号结构体
 */
typedef union vehicleDoorLockStatus
{
    uint8_t byte;
    struct
    {
    	uint8_t doorLockFrontLe : 2; ///< 左前门锁状态，0x1:解锁状态，0x2:闭锁状态
    	uint8_t doorLockFrontRi : 2; ///< 右前门锁状态，0x1:解锁状态，0x2:闭锁状态
    	uint8_t doorLockRearLe  : 2; ///< 左后锁门锁状态，0x1:解锁状态，0x2:闭锁状态
    	uint8_t doorLockRearRi  : 2; ///< 右后门锁状态，0x1:解锁状态，0x2:闭锁状态
    } bit;
} vehicleDoorLockStatus_t;

/**
 * @brief 转向灯信号结构体
 */
typedef union vehicleTurnLight
{
    uint8_t byte;
    struct
    {
    	uint8_t turnLightLe : 2; ///<左侧转向灯状态，0x0:invalid，0x1:打开，0x2:关闭，0x3:Reserved
    	uint8_t turnLightRi : 2; ///<右侧转向灯状态，0x0:invalid，0x1:打开，0x2:关闭，0x3:Reserved
    	uint8_t reserved    : 4; ///< 预留
    } bit;
} vehicleTurnLight_t;

/**
 * @brief 
 */
typedef union vehicleWhlDir
{
    uint8_t byte;
    struct
    {
        uint8_t whlDirFrLeft  : 2; ///<左前轮方向，0x0:invalid，0x1:向前，0x2:向后，0x3:Reserved
        uint8_t whlDirFrRight : 2; ///<右前轮方向，0x0:invalid，0x1:向前，0x2:向后，0x3:Reserved
        uint8_t whlDirReLeft  : 2; ///<左后轮方向，0x0:invalid，0x1:向前，0x2:向后，0x3:Reserved
        uint8_t whlDirReRight : 2; ///<右后轮方向，0x0:invalid，0x1:向前，0x2:向后，0x3:Reserved
    } bit;
} vehicleWhlDir_t;

/**
 * @brief 
 */
typedef struct
{
    uint8_t hdcCtrlStatus  : 2;     // 陡坡缓降控制
    uint8_t standStill     : 1;     // 0:NotStandStill, 1:StandStill
    uint8_t resv1          : 1;     // uint8_t brakePedalSts   : 2; 
    uint8_t espOffStatus   : 1;
    uint8_t espActive      : 1;
    uint8_t tcsFaultStatus : 1;     // 控制循迹系统
    uint8_t vdcFaultStatus : 1;     // 车辆动态控制(VDC)系统
} espCtrlStatus_t;                  // ESP状态

/**
 * @brief GPS经纬度信息.
 */
typedef struct
{
    uint8_t Latitd_Info_Decimal_Part_S  : 7;
    uint8_t Latitude_Direction_2D4_S    : 1;
    uint8_t Latitd_Info_Integ_Part_S    : 8;
    uint8_t Longitd_Info_Deciml_Part_S  : 7;
    uint8_t Longitd_Direction_2D4_S     : 1;
    uint8_t Longitd_Info_Integr_Part_S  : 8;
} mediaGps_t;                     // GPS位置信息.

typedef struct 
{
    /**
     * @brief 版本号
     */
    uint16_t versionNumber;

    /**
     * @brief header信息
     */
    SignalHeader_t signalHeader;

    /**
     * @brief 车辆速度
     * 
     * @details 单位m/s
     */
    float comVelocity;

    /**
     * @brief 雨刮器开关状态
     * 0x0：off
     * 0x1：on
    */
    uint8_t comWiperSwitch;

    /**
    * @brief 车身垂直位置
    */
    float comVertPosition;

    /**
     * @brief 车身宽度
    */
    float comVehicleWidth;

    /**
     * @brief 车身长度
    */
    float comVehicleLength;

    /**
     * @brief 雷达角度
    */
    float comSensorRot;

    /**
     * @brief 纵向
    */
    float comInstallOffsetX;

    /**
     * @brief 横向
    */
    float comInstallOffsetY;

    /**
     * @brief 接插件朝向
    */
    uint8_t comConnectOrient;

    /**
     * @brief 车速时间戳
     */
    uint32_t comVelocityTimeStamp;

    /**
     * @brief 横摆角速度
     * 
     * @details 单位rad/s
     */
    float comYawRate;

    /**
     * @brief 横摆角速度偏移
     * 
     * @details 单位rad/s
     */
    float comYawRateOffset;

    /**
     * @brief EDR存储的横摆角速度
     * 
     * @details 单位rad/s
     */
    float comEdrYawRate;

    /**
     * @brief EDR存储的横摆角速度偏移
     * 
     * @details 单位rad/s
     */
    float comEdrYawRateOffset;

    /**
     * @brief 横摆角速度偏移获取时间戳
     */
    uint32_t comYawRateOffsetTimeStamp;

    /**
     * @brief 横摆角速度获取时间戳
     */
    uint32_t comYawRateTimeStamp;

    /**
     * @brief 方向盘转角
     * 
     * @details 单位deg
     */
    float comSteerWheelAngle;

    /**
     * @brief 方向盘转角获取的时间戳
     */
    uint32_t comSteerWheelAngleTimeStamp;

    /**
     * @brief 方向盘转角速度
     * 
     * @details 单位deg/s
     */
    float comSteerWheelSpeed;

    /**
     * @brief 方向盘转角速度获取时间戳
     */
    uint32_t comSteeWheelSpeedTimeStamp;

    /**
     * @brief 车辆纵向加速度
     * 
     * @details 单位m/s^2
     */
    float comLongAccel;

    /**
     * @brief 车辆纵向加速度时间戳
     */
    uint32_t comLongAccelTimeStamp;

    /**
     * @brief 车辆纵向加速度偏移
     * 
     * @details 单位m/s^2
     */
    float comLongAccelOffset;

    /**
     * @brief 车辆纵向加速度
     * 
     * @details 单位m/s^2
     */
    float comedrLongAccel;

    /**
     * @brief 车辆纵向加速度偏移
     * 
     * @details 单位m/s^2
     */
    float comedrLongAccelOffset;

    /**
     * @brief IPB仿真压力
     */
    float comIPBSimulatorPressure;

    /**
     * @brief IPB压力
     */
    float comIPBPlungerPressure;

    /**
     * @brief EPS人手扭矩
     */
    float comEPSStrngWhlTorq;

    /**
     * @brief 车辆纵向加速度偏移时间戳
     */
    uint32_t comLongAccelOffsetTimeStamp;

    /**
     * @brief 车辆横向加速度
     * 
     * @details 单位m/s^2
     */
    float comLatAccel;

    /**
     * @brief 车辆横向加速度时间戳
     */
    uint32_t comLatAccelTimeStamp;

    /**
     * @brief 车辆横向加速度偏移
     * 
     * @details 单位m/s^2
     */
    float comLatAccelOffset;

    /**
     * @brief 车辆横向加速度
     * 
     * @details 单位m/s^2
     */
    float comedrLatAccel;

    /**
     * @brief 车辆横向加速度偏移
     * 
     * @details 单位m/s^2
     */
    float comedrLatAccelOffset;

    /**
     * @brief 车辆横向加速度偏移时间戳
     */
    uint32_t comLatAccelOffsetTimeStamp;

    /**
     * @brief 左前轮车速
     * 
     * @details 单位m/s
     */
    float comWhlVelFrLeft;

    /**
     * @brief 左前轮车速时间戳
     */
    uint32_t comWhlVelFrLeftTimeStamp;

    /**
     * @brief 右前轮轮速
     * 
     * @details 单位m/s
     */
    float comWhlVelFrRight;

    /**
     * @brief 右前轮轮速时间戳
     */
    uint32_t comWhlVelFrRightTimeStamp;

    /**
     * @brief 左后轮轮速
     * 
     * @details 单位m/s
     */
    float comWhlVelReLeft;

    /**
     * @brief 左后轮轮速时间戳
     */
    uint32_t comWhlVelReLeftTimeStamp;

    /**
     * @brief 右后轮轮速
     * 
     * @details 档位m/s
     */
    float comWhlVelReRight;

    /**
     * @brief 右后轮轮速时间戳
     */
    uint32_t comWhlVelReRightTimeStamp;

    /**
     * @brief 左前轮轮速脉冲计数
     */
    uint16_t comWhlTicksFrLeft;

    /**
     * @brief 左前轮轮速脉冲时间戳
     */
    uint32_t comWhlTicksFrLeftTimeStamp;

    /**
     * @brief 右前轮轮速脉冲计数
     */
    uint16_t comWhlTicksFrRight;

    /**
     * @brief 右前轮轮速脉冲时间戳
     */
    uint32_t comWhlTicksFrRightTimeStamp;

    /**
     * @brief 左后轮轮速脉冲计数
     */
    uint16_t comWhlTicksReLeft;

    /**
     * @brief 左后轮轮速脉冲时间戳
     */
    uint32_t comWhlTicksReLeftTimeStamp;

    /**
     * @brief 右后轮轮速脉冲计数
     */
    uint16_t comWhlTicksReRight;

    /**
     * @brief 右后轮轮速脉冲时间戳
     */
    uint32_t comWhlTicksReRightTimeStamp;

    /**
     * @brief 发送机转速
     */
    float comEngineSpeed;

    /**
     * @brief 总里程数
     * 
     * @details 用于诊断快照，单位km
     */
    uint32_t comTotalDistance;

    /**
     * @brief 初始收到的总里程数
     * 
     * @details 用于诊断快照，单位km
     */
    uint32_t comInitTotalDistance;

    /**
     * @brief 四轮轮速方向
     */
    vehicleWhlDir_t comWheelDir;

    /**
     * @brief 行驶方向
     */
    uint8_t comDriveDirection;

    /**
     * @brief 档位信号
     * 
     * @details 单位NA，0x0=invalid,0x1=Gear P,0x2=Gear R,0x3=Gear N,0x4=Gear D,0x5~0xFF=Reserved
     */
    uint8_t comActGearPos;

    /**
     * @brief 车辆电源档位
     * 
     * @details 信号值对应为：0x0:OFF,0x1:ACC,0x2:Crunk,0x3:Run 
     */
    uint8_t comSysPowerMod;

    /**
     * @brief 车身状态（车身防盗）
     * 
     * @details 信号值对应为：0x0:正常状态, 0x1:防盗设定状态, 0x2:防盗状态, 0x3-0x7:预留
     */
    uint8_t comVehicleStatusSecurtiy;

    /**
     * @brief 发动机运动状态
     * 
     * @details 0x0:未运转,0x1:运转
     */
    uint8_t comEngineRunning;

    /**
     * @brief 发动机转速错误
     *
     * @details 0x0：No Error   0x1：Error

     */
    uint8_t comEngineRevError; 

    /**
     * @brief 反拖起动命令
     *
     * @details 0x0：Valid   0x1：Invalid
     */
    uint8_t comRevDragStartCmd;

    /**
     * @brief 制动踏板深度
     * 
     * @details 范围：0-100%
     */
    uint8_t comBrakeActLevel;

     /**
     * @brief 制动踏板EDR存储实际值
     * 
     * @details 范围：
     */
    uint16_t comEdrBrakeActLevel;

    /**
     * @brief 制动踏板行程状态  0x0：Valid 0x1：Invalid
     * 
     * @details 范围：0x0：Valid 0x1：Invalid
     */
    uint8_t comImputRodStrokeState;

    /**
     * @brief 制动踏板被踩下状态
     * 
     * @details 0x0:not pressed，0x1：pressed 
     */
    uint8_t BrakePedalPressed;

    /**
     * @brief 驻车制动状态
     * 
     * @details 0x0：inactive，0x1：active
     */
    uint8_t comParkBrakeState;

    /**
     * @brief 拖车状态 
     * 
     * @details 0x0:未拖车,0x1:拖车状态
     */
    uint8_t comTrailerConnection;

    /**
     * @brief 制动系统运行状态
     * 
     * @details 0x0:not available，0x1:available
     */
    uint8_t comBrakeCtrlState;

    /**
     * @brief 油门踏板位置
     * 
     * @details 范围：0-100%
     */
    uint8_t comAccelPedalPosn;

    /**
     * @brief 油门踏板深度
     * 
     * @details 范围：0-100%
     */
    uint8_t comAccelerographDepth;

    /**
     * @brief 车辆安全状态
     * 
     * @details 0x0：invalid，0x1：not armed，0x2：fully armed
     */
    uint8_t comSecurityStatus;

    /**
     * @brief ADS请求方向盘角度
     * 
     * @details
     */
    float comADASAngleReq;    

    /**
     * @brief 当前时间-时
     */
    uint8_t comCurrentTimeHour;

    /**
     * @brief 当前时间-分
     */
    uint8_t comCurrentTimeMinute;

    /**
     * @brief 当前时间-秒
     */
    uint8_t comCurrentTimeSecond;

    /**
     * @brief 当前时间-日
     */
    uint8_t comCurrentTimeDay;

    /**
     * @brief 当前时间-月
     */
    uint8_t comCurrentTimeMonth;

    /**
     * @brief 当前时间-年
     * 
     * @details 基础年份为2000
     */
    uint8_t comCurrentTimeYear;

    /**
     * @brief ESP失效状态
     * 
     * @details 0x0:未失效，0x1:失效
     */
    uint8_t comESPFailSts;

    /**
     * @brief ESP模块激活状态
     * 
     * @details ESP中任何一个功能激活，功能需要被抑制
     */
    uint8_t comESPActiveSts;

    /**
     * @brief ABS失效状态
     * 
     * @details 0x0:No Failure, 0x1:Failure
     */
    uint8_t comABSFailSts;

    /**
     * @brief ABS模块激活状态
     * 
     * @details 0x0:No Active, 0x1:Active
     */
    uint8_t comABSActiveSts;

    /**
     * @brief EBD失效状态
     * 
     * @details 0x0:No Failure, 0x1:Failure
     */
    uint8_t comEBDFailSts;

    /**
     * @brief EBD模块激活状态
     * 
     * @details 0x0:No Active, 0x1:Active
     */
    uint8_t comEBDActiveSts;
    /**
     * @brief TCS模块激活状态
     * 
     * @details 0x0:No Active, 0x1:Active
     */
    uint8_t comTCSActiveSts;

    /**
     * @brief VDC模块激活状态
     * 
     * @details 0x0:No Active, 0x1:Active
     */
    uint8_t comVDCActiveSts;

    /**
     * @brief 自动紧急制动有效状态
     * 
     * @details 0x0:Not Available, 0x1:Available
     */
    uint8_t comAEBNotAvailable;

    /**
     * @brief AEBdecActive
     * 
     * @details 0x0:Not Available, 0x1:Available
     */
    uint8_t comAEBDecActive;

    /**
     * @brief 恢复出厂默认设置
     *
     * @details 0x0:invalid，0x1:确定，0x2:取消，0x3:Reserved
     */
    uint8_t rstFactoryDefaultSe;
    /**
     * @brief 行车辅助设置恢复默认标志位
     *
     * @details 0x0:invalid，0x1:确认
     */
    uint8_t drvAsisSeRstrDfltFlg;

    /**
     * @brief 变道盲区监测按键
     *
     * @details changeLane_DeadZone_Montr_Key_S 0x0:invalid，0x1:确认
     */
    uint8_t chanLaneDeZoneMontKey;

    /**
     * @brief 功能开关状态
     * 
     * @details 0x0:关闭，0x1:打开
     */
    vehicleSwitch_t comHmiFuncSw;

    /**
     * @brief (4)车门状态
     *
     * @details 0x0:invalid，0x1:解锁状态，0x2:闭锁状态，0x3:Reserved
     */
    vehicleDoorStatus_t comDoorStatus;

    /**
     * @brief (4)车门锁状态
     *
     * @details 0x0:invalid，0x1:打开，0x2:关闭，0x3:Reserved
     */
    vehicleDoorLockStatus_t comDoorLockStatus;

    /**
     * @brief 转向灯状态
     * 
     * @details 0x0:invalid，0x1:打开，0x2:关闭，0x3:Reserved
     */
    vehicleTurnLight_t comTurnLight;

    /**
     * @brief esp, hdc, tcs, vdc
     *
     * @details 0x0:invalid，0x1:打开，0x2:关闭，0x3:Reserved
     */
    espCtrlStatus_t espCtrlSts;

    /**
     * @brief 经纬度信息
     *
     * @details 
     */
    mediaGps_t mediaGps;

    /**
     * @brief APA_自动泊车状态
     * 
     * @details 
     * 0x0：Passive待机
     * 0x1：寻车位中
     * 0x2：泊车中
     * 0x3：泊车暂停
     * 0x4：泊车中(终)止（异常退出）
     * 0x5：泊车完成
     * 0x6：自动泊车不可用
     * 0x7：准备就绪状态
     */
    uint8_t comApaAutoParkingState;

    /**
     * @brief OTA模式请求
     * 
     * @details 
     * 
     * 0x0：OTA结束，正常模式
     * 0x1：整车进入OTA模式
     */
    uint8_t comOTAModeReq;

    /**
     * @brief 拖拽模式
     * 
     * @details
     * 
     * 0x0： 无效
     * 0x1： 进入
     * 0x2： 退出
     * 0x3： 预留
     *       初始值/默认值：0x0
     */
    uint8_t comDragMode;

    uint8_t comRctbActive; // 0x0：No active   0x1：active

    uint8_t comRctbAvailable; 

    /**
     * @brief 拖拽模式配置
     * 
     * @details
     * 0x0：无效
     * 0x1：无拖拽模式
     * 0x2：有拖拽模式
     * 0x3：预留
     */
    uint8_t comDragModeConfig;

    /**
     * @brief 空气悬架高度调节设置
     * 
     * @details
     * 0x0：无效
     * 0x1：车身正在上升
     * 0x2：车身正在下降
     * 0x3：调节完成
     * 0x4: 调节中断
     * 0x5-0x7：预留
     * 默认值：0x0
     */
    uint8_t comSuspensionHeight;

    /**
     * @brief 赛道模式状态
     * 
     * @details
     * 0x0：关闭
     * 0x1：开启
     */
    uint8_t raceModeStatus;

    uint8_t keyStatus; // 钥匙状态

        /**
     * @brief 后角雷达DOW状态
     * 
     * 0x0: 0FF, 0x1: Passive, 0x2: Active, 0x3: Fault
     */
    uint8_t comRcrDOWStatus;

    /**
     * @brief 前角雷达DOW状态
     * 
     * 0x0：invalid, 0x1：Off, 0x2：Passive, 0x3：Active, 0x4：Fault, 0x5-0x7：Reserved
     */
    uint8_t comFcrDOWStatus;
    /**
     * @brief 极致转向状态
     * 
     * @details
     * 0x0：关闭
     * 0x1：待命
     * 0x2：激活
     * 0x3：预留
     * 默认值：0x0
     */
    uint8_t comTabStatus;

    /**
     * @brief 转向灯工作工况
     *
     *  0:invalid, 无效
     *  1:NotWorking, 不工作
     *  2: LeftTurnSignalLightNormalFlashing, 左转向信号灯（正常闪烁）
     *  3: LeftTurnSignalLightFlashingQuickly, 左转向信号灯（故障快闪）
     *  4: LeftTurnSignalLightFlashingNormaly, 右转向信号灯（正常闪烁）
     *  5: RightTurnSignalLightFaultyFlashingFastWarningSignal, 右转向信号灯（故障快闪）
     *  6: DangerWarningSicnal, 危险警告信号
     *  7: EmergencyBrakingSignal, 紧急制动信号
     *  8: RearEndCollisionWarningSignal, 追尾警告信号
     *  9: NormalFlashing, 普通快闪
     *  0xA-0xF：预留
     */
    uint8_t comTurnSignalWorkCon;
    
    /**
     * @brief 驾驶模式
     *  0x0:无效
     *  0x1:舒适
     *  0x2:经济
     *  0x3:运动
     *  0x4:雪地/冰面/草地/碎石
     *  0x5:沙地
     *  0x6:泥地/车辙
     *  0x7:山地
     *  0x8:岩石
     *  0x9:涉水
     *  0xA:浮水
     *  0xB:蠕行模式
     *  0xC:原地掉头
     *  0xD:拖挂模式
     *  0xE:长续航模式
     *  0xF:预留
     *  0x10:防滑链模式
     *  0x11:达喀尔模式（拉力模式）
     *  0x12: 跛行模式
     *  0x13: 自定义模式
     *  0x14:超频（狂暴）模式
     *  0x15:自动模式
     *  0x16-0xFF：预留
     */
    uint8_t comDriveMode;
    /**
     * AEB力矩接口可用状态
     * 0-not available
     * 1-availlable
     */
    uint8_t AebBrkTqAvl;  

     /**
     * @brief 极限续航开关
     *  0x0:关闭
     *  0x1:开启
     */
    uint8_t extModeOpenSts;
    /**
     * @brief 极限续航自学习配置
     *  0x0:无效
     *  0x1:无配置
     *  0x2:有配置
     *  0x3:预留
     */
    uint8_t extModeCfgSelfStudy;    

    /**
     * @brief 智驾休眠模式开关请求
     *  0x0:无效
     *  0x1:关闭
     *  0x2:打开
     *  0x3:置灰
     */
    uint8_t adsStandbyEnableS;              
} COM_vehicleSignals_t;

#endif

