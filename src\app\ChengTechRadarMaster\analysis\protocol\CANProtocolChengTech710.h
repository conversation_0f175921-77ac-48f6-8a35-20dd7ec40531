﻿#pragma once

#include "ianalysisprotocol.h"

#include <vector>

namespace Analysis {

namespace Protocol {
		class CANProtocolChengTech710 :
            public IAnalysisProtocol
		{
		public:
            CANProtocolChengTech710(AnalysisWorker *analysisWorker, QObject *parent = nullptr);
			~CANProtocolChengTech710();

            bool analysisFrame(const Devices::Can::CanFrame &frame) override;

		private:
            bool parse0x81(const Devices::Can::CanFrame &frame);
            bool parse0x82(const Devices::Can::CanFrame &frame);
            bool parse0x5F0(const Devices::Can::CanFrame &frame);
            bool parse0x600(const Devices::Can::CanFrame &frame);
            bool parse0x600_VERSION_1_6(const Devices::Can::CanFrame &frame);
            bool parse0x600_VERSION_7_8(const Devices::Can::CanFrame &frame);
            bool parse0x710_8(const Devices::Can::CanFrame &frame);
            bool parse0x710_64_V6(const Devices::Can::CanFrame &frame);
            bool parse0x710_64_V7(const Devices::Can::CanFrame &frame);
            bool parse0x710_64_V8(const Devices::Can::CanFrame &frame);
            bool parse0x710_64(const Devices::Can::CanFrame &frame);
            bool parse0x710(const Devices::Can::CanFrame &frame);
            bool parse0x720(const Devices::Can::CanFrame &frame);
		};
	}
}

