﻿#ifndef CALCULATIONWORKER_H
#define CALCULATIONWORKER_H

#include "analysis_global.h"

#include <QObject>

#include "analysisdataf.h"
#include "analysisdata.h"
#include "truesystemdata.h"

namespace Devices {
namespace Can {
class DeviceWorkerBinFile;
}
}

namespace Analysis {

class AnalysisWorker;

class ANALYSIS_EXPORT CalculationWorker : public QObject
{
    Q_OBJECT
public:
    explicit CalculationWorker(AnalysisWorker *analysisWorker, QObject *parent = nullptr);
    ~CalculationWorker();

    void setDataProcess(bool dataProcess,
                        bool interpolation,
                        bool radarFusion,
                        quint8 masterRadarID,
                        quint8 sloveRadarID,
                        quint8 isorgpoint,
                        quint8 isShowCandiObj,
                        quint8 eolmode);
    void setIsShowCandiObj(bool isShow);
    void setEOLData(quint8 eolType);
    void setEOLRunStateFunc();

signals:
    void calculateFinished(quint8 radarID, const AnalysisData &analysisData);
    void calculateFinishedF(const Parser::ParsedDataTypedef::TargetsF &targets);
    void calculateTargetFinished(quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &target);
    void sigSetIsShowCandiObj(bool isShow);

public slots:
    void calculateSlaveRadar(quint8 radarID);
    void calculate(quint8 radarID, const AnalysisData &analysisData);
    void calculatePlayback(quint8 radarID, const AnalysisData &radarData);
    void calculateTarget(quint8 radarID, /*AnalysisFrameType*/int frameType);
    void calculateTargetF(const Parser::ParsedDataTypedef::TargetsF &targets);
    void calculateTrueSystem();

    void setDeteStatistics(DeteStatisData &StatisData);
public:
    Parser::ParsedDataTypedef::ParsedData mParsedData;  // 前雷达
    AnalysisData mAnalysisDatas[MAX_RADAR_COUNT];       // 角雷达
    stTrueObjInfo mTrueObjectInfo;

    float mTrueTargetLength{5.0};  // 真值从车长
    float mTrueTargetWidth{1.8};   // 真值从车宽

private:
    void calculationAlarm(Targets &targets, AlarmData &alarmData);
    void calculationAlarmAndEarlyWarning(quint8 radarID);

    AnalysisWorker *mAnalysisWorker{0};

    bool mDataProcess{false};   ///< 需要进行数据处理
    bool mInterpolation{false}; ///< 需要进行插值处理
    bool mRadarFusion{false};   ///< 雷达融合
    quint8 mMasterRadarID{4};
    quint8 mSlaveRadarID{5};
    quint8 mIsorgpoint{false};
    quint8 mIsShowCandiObj{false};
    quint8 mEolMode{0};         // 标定模式
};

} // namespace Analysis

#endif // CALCULATIONWORKER_H
