﻿#include "networktcpclient.h"

#include <QTcpSocket>
#include <QNetworkInterface>
#include <QDebug>

NetworkTcpClient::NetworkTcpClient(QObject *parent) : QObject(parent)
{

}

void NetworkTcpClient::connectTCPServer(const QString IP, quint16 port)
{
    if (!mTcpClient) {
        mTcpClient = new QTcpSocket(this);

        connect(mTcpClient, &QTcpSocket::connected, this, [=]{
            emit tcpServerConnected(mTcpClient->localAddress().toString(), mTcpClient->localPort());
            mConnected = true;
            qDebug() << __FUNCTION__ << __LINE__ << mConnected << mTcpClient->localAddress().toString() << mTcpClient->localPort();
        });
        connect(mTcpClient, &QTcpSocket::disconnected, this, &NetworkTcpClient::disconnectTCPServer);

        connect(mTcpClient, &QTcpSocket::readyRead, this, &NetworkTcpClient::readData);
        connect(this, &NetworkTcpClient::write, this, &NetworkTcpClient::writeData);
    }

    qDebug() << __FUNCTION__ << __LINE__ << mTcpClient->state();
    if(mTcpClient->state()==QAbstractSocket::ConnectedState) {
        return;
    } if(mTcpClient->state()==QAbstractSocket::UnconnectedState) {
        const QHostAddress address=QHostAddress(IP);
        mTcpClient->connectToHost(address, port);
    } else {
        disconnectTCPServer();
    }
}

void NetworkTcpClient::disconnectTCPServer()
{
    qDebug() << __FUNCTION__ << __LINE__;
    mConnected =false;
    emit tcpServerDisonnected();
}

void NetworkTcpClient::readData()
{
    if(mTcpClient->bytesAvailable()<=0) {
        return;
    }

    const QString recv_text=QString::fromUtf8(mTcpClient->readAll());

    qDebug() << __FUNCTION__ << __LINE__ << recv_text;

    emit read(recv_text.toUtf8());
}

void NetworkTcpClient::writeData(const QByteArray &data)
{
    mTcpClient->write(data);
}
