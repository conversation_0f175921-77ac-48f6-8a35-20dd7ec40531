<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FileBatchParsingForm</class>
 <widget class="QWidget" name="FileBatchParsingForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>584</width>
    <height>477</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>文件批量生成</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QGridLayout" name="gridLayout">
     <item row="0" column="0">
      <widget class="QLabel" name="label">
       <property name="text">
        <string>数据目录:</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEditDataPath"/>
     </item>
     <item row="0" column="2">
      <widget class="QPushButton" name="pushButtonDataPath">
       <property name="text">
        <string>...</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>保存路径:</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLineEdit" name="lineEditSavePath"/>
     </item>
     <item row="1" column="2">
      <widget class="QPushButton" name="pushButtonSavePath">
       <property name="text">
        <string>...</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QCheckBox" name="checkBoxDeleteOld">
       <property name="text">
        <string>删除旧文件</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxFileType">
       <item>
        <property name="text">
         <string>ASC</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>BLF</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonGenerate">
       <property name="text">
        <string>生成</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QPlainTextEdit" name="plainTextEditLog"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
