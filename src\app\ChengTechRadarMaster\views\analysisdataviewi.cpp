﻿#include "analysisdataviewi.h"
#include "ui_analysisdataviewconfigdialog.h"

#include "analysismodel.h"
#include "analysisdatatableview.h"

#include <QLabel>
#include <QAction>
#include <QCheckBox>
#include <QVBoxLayout>
#include <QSpacerItem>
#include <QSplitter>

namespace Views {
namespace AnalysisView {

AnalysisDataViewI::AnalysisDataViewI(quint8 radarID, QWidget *parent) : QWidget(parent), mRadarID(radarID)
{
    setupUi();
}

QVariant AnalysisDataViewI::getAnalysisDataViewSettings() const
{
    QMap<QString, QVariant> settings;

    QVariantList variantList;
    foreach (const int tp, mAnalysisTypesRaw) {
        variantList << tp;
    }
    settings["RawViewAnalysisTypes"] = variantList;

    variantList.clear();
    foreach (const int tp, mAnalysisTypesTrack) {
        variantList << tp;
    }
    settings["TrackViewAnalysisTypes"] = variantList;

    settings["ViewTypes"] = getViewsTypes();
    settings["ViewOrientation"] = mSplitter->orientation();

    settings["MovingOnlyRaw"] = mMovingOnlyRaw;
    settings["MovingOnlyTrack"] = mMovingOnlyTrack;
    settings["ContinuousDisplayRaw"] = mContinuousDisplayRaw;
    settings["ContinuousDisplayTrack"] = mContinuousDisplayTrack;

    return settings;
}

void AnalysisDataViewI::setAnalysisDataViewSettings(QVariant settings)
{
    if (!settings.isValid() || settings.type() != QVariant::Map) {
        return;
    }
#define VALUE_BOOL(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toBool() : DEFAULT
#define VALUE_UINT(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toUInt() : DEFAULT
#define VALUE_LIST(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toList() : DEFAULT

    QMap<QString, QVariant> config = settings.toMap();

    mAnalysisTypesRaw.clear();
    QVariantList variantList = VALUE_LIST("RawViewAnalysisTypes", QVariantList());
    foreach (const QVariant &tp, variantList) {
        mAnalysisTypesRaw << tp.toUInt();
    }

    mAnalysisTypesTrack.clear();
    variantList = VALUE_LIST("TrackViewAnalysisTypes", QVariantList());
    foreach (const QVariant &tp, variantList) {
        mAnalysisTypesTrack << tp.toUInt();
    }

    mMovingOnlyRaw = VALUE_BOOL("MovingOnlyRaw", false);
    mMovingOnlyTrack = VALUE_BOOL("MovingOnlyTrack", false);
    mContinuousDisplayRaw = VALUE_BOOL("ContinuousDisplayRaw", false);
    mContinuousDisplayTrack = VALUE_BOOL("ContinuousDisplayTrack", false);

    variantList = VALUE_LIST("ViewTypes", QVariantList() << FrameTrackTarget);
    setViewsTypes(variantList);

    setOrientation((Qt::Orientation)(VALUE_UINT("ViewOrientation", Qt::Vertical)));
}

void AnalysisDataViewI::setOrientation(Qt::Orientation ot)
{
    mActionOrientation->setText(QString::fromLocal8Bit(ot == Qt::Vertical ? "左右" : "上下"));
    mSplitter->setOrientation(ot);
}

QVariantList AnalysisDataViewI::getViewsTypes() const
{
    QVariantList variantList;

    foreach (AnalysisDataTableView *view, mAnalysisDataTableViews) {
        variantList << view->viewType();
    }

    return variantList;
}

void AnalysisDataViewI::setViewsTypes(QVariantList types)
{
    foreach (const QVariant &tp, types) {
        newTableView(tp.toUInt());
    }
}

void AnalysisDataViewI::newTableView()
{
    newTableView(0);
}

void AnalysisDataViewI::deleteTableView()
{
    AnalysisDataTableView *tableView = qobject_cast<AnalysisDataTableView *>(sender());
    if (!tableView) {
        return;
    }

    mAnalysisDataTableViews.removeAll(tableView);
    delete tableView;
    tableView = 0;
}

void AnalysisDataViewI::orientationChanged()
{
    setOrientation(mSplitter->orientation() == Qt::Vertical ? Qt::Horizontal : Qt::Vertical);
}

void AnalysisDataViewI::viewDataConfig()
{
    AnalysisDataViewConfigDialog *dialog = new AnalysisDataViewConfigDialog(this, this);
    dialog->exec();
}

void AnalysisDataViewI::addTopWidget(QWidget *widget)
{
    mVLayoutTop->addWidget(widget);
}

void AnalysisDataViewI::newTableView(int viewType)
{
    AnalysisDataTableView *tableView = new AnalysisDataTableView(mRadarID, viewType, this);
    connect(tableView, &AnalysisDataTableView::sigViewTypeChanged, this, &AnalysisDataViewI::viewTypeChanged);
    connect(tableView, &AnalysisDataTableView::sigDelete, this, &AnalysisDataViewI::deleteTableView);
    tableView->sigViewTypeChanged();

    mSplitter->addWidget(tableView);
    mAnalysisDataTableViews << tableView;
}

void AnalysisDataViewI::setupUi()
{
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(3, 3, 3, 3);
    mVLayoutTop = new QVBoxLayout();
    mVLayoutTop->setContentsMargins(0, 0, 0, 0);
    layout->addLayout(mVLayoutTop);

    mSplitter = new QSplitter(this);
    mSplitter->setOrientation(Qt::Vertical);
    QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    sizePolicy.setHorizontalStretch(0);
    sizePolicy.setVerticalStretch(0);
    sizePolicy.setHeightForWidth(mSplitter->sizePolicy().hasHeightForWidth());
    mSplitter->setSizePolicy(sizePolicy);

    layout->addWidget(mSplitter);

    mActionOrientation = new QAction(QString::fromLocal8Bit("左右"), this);
    connect(mActionOrientation, &QAction::triggered, this, &AnalysisDataViewI::orientationChanged);
    mActionNewAnalysisDataTable = new QAction(QString::fromLocal8Bit("新表"), this);
    connect(mActionNewAnalysisDataTable, &QAction::triggered, this, QOverload<>::of(&AnalysisDataViewI::newTableView));
    mActionMonitoringTrackTarget = new QAction(QString::fromLocal8Bit("监控"), this);
    mActionViewDataConfig = new QAction(QString::fromLocal8Bit("设置"), this);
    connect(mActionViewDataConfig, &QAction::triggered, this, &AnalysisDataViewI::viewDataConfig);

    mLabelMeasurementCounter = new QLabel(QString("%1:0/0").arg(mRadarID));
    mLabelMeasurementCounter->setMinimumWidth(25);
}

AnalysisDataViewConfigDialog::AnalysisDataViewConfigDialog(AnalysisDataViewI *view, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::AnalysisDataViewConfigDialog),
    mAnalysisDataView(view)
{
    ui->setupUi(this);
    Qt::WindowFlags flags = Qt::Dialog;
    flags |= Qt::WindowCloseButtonHint | Qt::WindowMaximizeButtonHint | Qt::WindowMinimizeButtonHint;
    setWindowFlags(flags);

    ui->comboBoxFrameType->addItem("Raw");
    ui->comboBoxFrameType->setItemData(0, FrameRawTarget);
    ui->comboBoxFrameType->addItem("Track");
    ui->comboBoxFrameType->setItemData(1, FrameTrackTarget);

    connect(ui->listWidgetTargetAttributes, &QListWidget::itemClicked, this, [=](QListWidgetItem *item) {
        for (int i = 0; i < ui->listWidgetTargetAttributeDisplay->count(); ++i) {
            QListWidgetItem *aItem = ui->listWidgetTargetAttributeDisplay->item(i);
            if (item->data(Qt::UserRole).toUInt() == aItem->data(Qt::UserRole).toUInt()) {
                if (item->checkState() == Qt::Unchecked) {
                    aItem = ui->listWidgetTargetAttributeDisplay->takeItem(i);
                    delete aItem;
                    aItem = nullptr;
                }
                return;
            }
        }
        if (item->checkState() == Qt::Checked)  {
            ui->listWidgetTargetAttributeDisplay->addItem(item->clone());
        }
    });

    connect(ui->listWidgetTargetAttributeDisplay, &QListWidget::itemClicked, this, [=](QListWidgetItem *item) {
        if (item->checkState() == Qt::Unchecked) {
            ui->listWidgetTargetAttributes->item(item->data(Qt::UserRole).toUInt())->setCheckState(Qt::Unchecked);

            int row = ui->listWidgetTargetAttributeDisplay->row(item);
            QListWidgetItem *aItem = ui->listWidgetTargetAttributeDisplay->takeItem(row);
            delete aItem;
            aItem = nullptr;
        }
    });

    on_comboBoxFrameType_currentIndexChanged(ui->comboBoxFrameType->currentIndex());
}

void AnalysisDataViewConfigDialog::on_pushButtonOK_clicked()
{
    on_pushButtonApply_clicked();
    accept();
}

void AnalysisDataViewConfigDialog::on_pushButtonApply_clicked()
{
    ViewTypes viewTypes;
    for (int i = 0; i < ui->listWidgetTargetAttributeDisplay->count(); ++i) {
        int aType = ui->listWidgetTargetAttributeDisplay->item(i)->data(Qt::UserRole).toUInt();
        if (aType == Parser::ParsedDataTypedef::ID) {
            continue;
        }
        viewTypes << aType;
    }

    mAnalysisDataView->setViewAnalysisTypes(ui->comboBoxFrameType->currentIndex(),
                                            ui->checkBoxMovingOnly->isChecked(),
                                            ui->checkBoxContinuousDisplay->isChecked(),
                                            viewTypes);
}

void AnalysisDataViewConfigDialog::on_comboBoxFrameType_currentIndexChanged(int index)
{
    ui->listWidgetTargetAttributes->clear();
    ui->listWidgetTargetAttributeDisplay->clear();
    switch(mAnalysisDataView->mRadarType) {
    case ForwardRadar:
        initForwardRadar(ui->comboBoxFrameType->currentData().toInt());
        break;
    case AngularRadar:
        initAngularRadar(ui->comboBoxFrameType->currentData().toInt());
        break;
    default:
        break;
    }
}

void AnalysisDataViewConfigDialog::on_pushButtonMoveUp_clicked()
{
    itemMoveInListWidget(true);
}

void AnalysisDataViewConfigDialog::on_pushButtonMoveDown_clicked()
{
    itemMoveInListWidget(false);
}

void AnalysisDataViewConfigDialog::itemMoveInListWidget(bool bUp )
{
  // 获得当前选中或者需要移动的item所在的行
  int currentRow = ui->listWidgetTargetAttributeDisplay->currentRow();
  if (currentRow == -1 || (currentRow == 1 && bUp))
    return;

  // 计算上移或者下移之后要到达的位置
  int arriveRow = bUp ? (currentRow - 1 < 0 ? 0 : currentRow - 1) :
                        (currentRow + 1 >= ui->listWidgetTargetAttributeDisplay->count() ?
                             ui->listWidgetTargetAttributeDisplay->count() - 1 :
                             currentRow + 1);
  // 删除行数大的Item，然后插入到行数小的Item前
  ui->listWidgetTargetAttributeDisplay->insertItem(std::min(currentRow, arriveRow),
                                                   ui->listWidgetTargetAttributeDisplay->takeItem(std::max(currentRow, arriveRow)));
  // 然后选中到达的行
  ui->listWidgetTargetAttributeDisplay->setCurrentRow(arriveRow);
  return;
}

QListWidgetItem *attributeItemF(int i) {
    QString text = QString("%1 [%2 - %3] (%4)")
            .arg(Parser::ParsedDataTypedef::sgTargetAttributes[i].mTargetAttributeNameEN)
            .arg(Parser::ParsedDataTypedef::sgTargetAttributes[i].mTargetAttributeNameShort)
            .arg(QString::fromLocal8Bit(Parser::ParsedDataTypedef::sgTargetAttributes[i].mTargetAttributeNameCN))
            .arg(Parser::ParsedDataTypedef::sgTargetAttributes[i].mTargetAttributeUnit);
    QListWidgetItem *item = new QListWidgetItem(text);
    item->setData(Qt::UserRole, i);
    return item;
}

QListWidgetItem *attributeItem(int i) {
    QString text = QString("%1")
            .arg(gAnalysisTypeName((AnalysisType)i));
    QListWidgetItem *item = new QListWidgetItem(text);
    item->setData(Qt::UserRole, i);
    return item;
}

void AnalysisDataViewConfigDialog::initForwardRadar(int fType)
{
    ViewTypes &viewTypes = (fType == Parser::ParsedDataTypedef::TargetRaw) ? mAnalysisDataView->mAnalysisTypesRaw : mAnalysisDataView->mAnalysisTypesTrack;
    for (int i = Parser::ParsedDataTypedef::ID; i < Parser::ParsedDataTypedef::AttributeUnknow; ++i) {
        QListWidgetItem *item = attributeItemF(i);
        if (i == Parser::ParsedDataTypedef::ID) {
            item->setFlags(Qt::NoItemFlags);
            item->setCheckState(Qt::Checked);
        } else {
            item->setCheckState(viewTypes.contains(i) ? Qt::Checked : Qt::Unchecked);
        }
        ui->listWidgetTargetAttributes->addItem(item);
    }

    QListWidgetItem *item = attributeItemF(Parser::ParsedDataTypedef::ID);
    item->setCheckState(Qt::Checked);
    item->setFlags(Qt::NoItemFlags);
    ui->listWidgetTargetAttributeDisplay->addItem(item);
    for (int i = 0; i < viewTypes.size(); ++i) {
        QListWidgetItem *item = attributeItemF(viewTypes[i]);
        item->setCheckState(Qt::Checked);
        ui->listWidgetTargetAttributeDisplay->addItem(item);
    }
}

void AnalysisDataViewConfigDialog::initAngularRadar(int fType)
{
    ViewTypes &viewTypes = (fType == FrameRawTarget) ? mAnalysisDataView->mAnalysisTypesRaw : mAnalysisDataView->mAnalysisTypesTrack;
    for (int i = TARGET_TYPE_BEGIN; i < TARGET_TYPE_END; ++i) {
        QListWidgetItem *item = attributeItem(i);
        if (i == ID) {
            item->setFlags(Qt::NoItemFlags);
            item->setCheckState(Qt::Checked);
        } else {
            item->setCheckState(viewTypes.contains(i) ? Qt::Checked : Qt::Unchecked);
        }
        ui->listWidgetTargetAttributes->addItem(item);
    }

    QListWidgetItem *item = attributeItem(ID);
    item->setCheckState(Qt::Checked);
    item->setFlags(Qt::NoItemFlags);
    ui->listWidgetTargetAttributeDisplay->addItem(item);
    for (int i = 0; i < viewTypes.size(); ++i) {
        QListWidgetItem *item = attributeItem(viewTypes[i]);
        item->setCheckState(Qt::Checked);
        ui->listWidgetTargetAttributeDisplay->addItem(item);
    }

    ui->checkBoxMovingOnly->setChecked((fType == FrameRawTarget) ? mAnalysisDataView->mMovingOnlyRaw : mAnalysisDataView->mMovingOnlyTrack);
    ui->checkBoxContinuousDisplay->setChecked((fType == FrameRawTarget) ? mAnalysisDataView->mContinuousDisplayRaw : mAnalysisDataView->mContinuousDisplayTrack);
}

}
}
