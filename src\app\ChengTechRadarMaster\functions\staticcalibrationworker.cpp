﻿#include "staticcalibrationworker.h"

#include "uds.h"
#include "utils/utils.h"
#include "utils/baicuds/crypto/baciseedtokey.h"
#include "utils/seedtokeybydll.h"
#include "utils/seedtokey.h"

#include <QtConcurrent>
#include <QDebug>

namespace Functions {

StaticCalibrationWorker::StaticCalibrationWorker(Devices::Can::DeviceManager *deviceManager, QObject *parent) : QObject(parent)
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        mUDS[i] = new UDS(deviceManager, this);
        mUDS[i]->setResponseID(mResponseAddress[mProtocolIndex][i]);
    }

    memset(mBAICTargetPosition, 0, sizeof (mBAICTargetPosition));
}

void StaticCalibrationWorker::protocolChanged(int index)
{
    mProtocolIndex = (ProtocolType)index;
    qDebug() << __FUNCTION__ << __LINE__ << mProtocolIndex;
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        mUDS[i]->setResponseID(mResponseAddress[mProtocolIndex][i]);
    }
}

void StaticCalibrationWorker::setBAICTargetPosition(quint16 position[4][2])
{
    memcpy(mBAICTargetPosition, position, sizeof(mBAICTargetPosition));
}

void StaticCalibrationWorker::stop()
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        QFuture<void> res = QtConcurrent::run(this, &StaticCalibrationWorker::calibration, i, true);
    }
}

void StaticCalibrationWorker::start(int channelIndex[], bool sda[])
{
    initFirstReset();
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        mUDS[i]->setChannelIndex(channelIndex[i]);
        if (sda[i])
        {
            QFuture<void> res = QtConcurrent::run(this, &StaticCalibrationWorker::calibration, i, false);
        }
    }
}

void StaticCalibrationWorker::readResult()
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        readResult(i);
    }
}

void StaticCalibrationWorker::readResult(int index)
{
    QFuture<void> res = QtConcurrent::run(this, &StaticCalibrationWorker::result, index);
}

bool StaticCalibrationWorker::seedToKeyByDLL( quint8 index, quint8 level, const QByteArray &seed, QByteArray &key)
{
    QString path;
    switch ( mProtocolIndex ) {
    case ProtocolGWM:
        path = "./secureDLL/GWM_DLL.dll";
        break;
    default:
        break;
    }

    SeedToKeyByDLL dll;
    if( !dll.seedToKey( path, level, seed, key ) ){
        emit message(index, dll.errorMsg() );
    }
    return true;
}


#define SEND_DATA_HEX_RESPONSE(ID, DATA, MESSAGE) \
    responseFrame.clear(); \
    if (!mUDS[index]->sendData(ID, QString(DATA), &responseFrame)) { \
        emit message(index, MESSAGE + mUDS[index]->errorString()); \
        emit calibrationFinished(index); \
        return; \
    }

void StaticCalibrationWorker::calibration(int index, bool stop)
{    UDSFrame responseFrame;

    if (!stop)
    {
        switch (mProtocolIndex) {
        case ProtocolBAIC:
        case ProtocolHozon:
        case ProtocolBAIC_BE12:
        {
            mUDS[index]->sendData(0x411, QString("00 40 00 00 00 00 00 00"));
            Utils::dely(1000);
            mUDS[index]->sendData(0x411, QString("00 40 00 00 00 00 00 00"));
            Utils::dely(500);
            mUDS[index]->sendData(0x411, QString("00 40 00 00 00 00 00 00"));
        }
            break;
        case ProtocolGEELY_E245_J6M:
        {
            mUDS[index]->sendFrame(0x501, QByteArray::fromHex("01 40 00 20 00 00 00 00"));
            Utils::dely(1000);
            mUDS[index]->sendFrame(0x501, QByteArray::fromHex("01 40 00 20 00 00 00 00"));
            Utils::dely(1000);
            mUDS[index]->sendFrame(0x501, QByteArray::fromHex("01 40 00 20 00 00 00 00"));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "10 01", QString::fromLocal8Bit("开启诊断仪失败"));
            emit message(index, QString::fromLocal8Bit("开启诊断仪成功"));
            Utils::dely(20);
        }
        default:
            break;
        }
        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "10 03", QString::fromLocal8Bit("进入扩展模式失败"));
        emit message(index, QString::fromLocal8Bit("进入扩展模式成功"));

        emit message(index, QString::fromLocal8Bit("请求种子"));
        if (ProtocolGEELY_E245_J6M == mProtocolIndex) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "27 05", QString::fromLocal8Bit("请求种子失败")); // 请求种子
        } else if (ProtocolHozon == mProtocolIndex) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "27 03", QString::fromLocal8Bit("请求种子失败")); // 请求种子
        }else {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "27 01", QString::fromLocal8Bit("请求种子失败")); // 请求种子
        }
        emit message(index, QString::fromLocal8Bit("请求种子成功"));
    //    qDebug() << __FUNCTION__ << __LINE__ << mMASK << QString::number(mMASK);if (mProtocolIndex == ProtocolBAIC) {
        switch (mProtocolIndex) {
        case ProtocolBAIC:
        {
            uint32_t seed{0};
            memcpy(&seed, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);
            uint8_t key[16];
            BAICSendToKey(seed, UDSFactor, key);
            qDebug() << __FUNCTION__ << __LINE__ << QByteArray((const char *)key, sizeof (key)).toHex(' ');
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
            break;
        }
        case ProtocolGWM:
        {
            QByteArray key;
            seedToKeyByDLL( index, 0x01, responseFrame.mData.mid(2, 4), key );
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
            emit message(index, QString::fromLocal8Bit("密钥验证成功"));
            break;
        }
        case ProtocolHozon:
        {
            unsigned char iSeedArray[4] = { 0x00 };
            unsigned int iSecurityLevel = 0x01;
            unsigned char ioKeyArray[16]{ 0 };
            memcpy((void*)iSeedArray, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);
            GenerateKeyEx_HOZON_PROJECT(iSecurityLevel, iSeedArray, ioKeyArray);
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 04 %1").arg(QByteArray((const char *)ioKeyArray, sizeof (ioKeyArray)).mid(0,4).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
            emit message(index, QString::fromLocal8Bit("密钥验证成功"));
            break;
        }
        case ProtocolBAIC_BE12:
        {
            unsigned char iSeedArray[16];
            memcpy((void*)iSeedArray, responseFrame.mData.mid(2, 16).data(), 16);
            unsigned short iSeedArraySize = sizeof(iSeedArray);
            const unsigned int iSecurityLevel = 0x01;
            const char* iVariant = 0;
            unsigned char ioKeyArray[32]{ 0 };
            unsigned int iKeyArraySize = sizeof(ioKeyArray);
            unsigned int oSize = 0;
            if (GenerateKeyEx_BEIQI_BE12_PROJECT(iSeedArray,
                                                 iSeedArraySize,
                                                 iSecurityLevel,
                                                 iVariant,
                                                 ioKeyArray,
                                                 iKeyArraySize,
                                                 &oSize) != 0) {
                qDebug() << __FUNCTION__ << __LINE__ << "message";
            }
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(QByteArray((const char *)ioKeyArray, sizeof (ioKeyArray)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥

        }
            break;
        case ProtocolGEELY_E245_J6M:
        {
            unsigned char iSeedArray[16];
            memcpy((void*)iSeedArray, responseFrame.mData.mid(2, 16).data(), 16);
            unsigned short iSeedArraySize = sizeof(iSeedArray);
            const unsigned int iSecurityLevel = 0x01;
            const char* iVariant = 0;
            unsigned char ioKeyArray[16]{ 0 };
            unsigned int iKeyArraySize = sizeof(ioKeyArray);
            unsigned int oSize = 0;
            if (GenerateKeyEx_GEELY_E245_PROJECT(index,
                                                 iSeedArray,
                                                 iSeedArraySize,
                                                 iSecurityLevel,
                                                 iVariant,
                                                 ioKeyArray,
                                                 iKeyArraySize,
                                                 &oSize) != 0) {
                qDebug() << __FUNCTION__ << __LINE__ << "message";
            }
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 06 %1").arg(QByteArray((const char *)ioKeyArray, sizeof (ioKeyArray)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
            Utils::dely(20);
            break;
        }
        default:
        {
            uint32_t seed{0};
            memcpy(&seed, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);
            uint32_t key = SeedToKey(seed, mMASK[mProtocolIndex][index]);
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(key, 8, 16, QLatin1Char('0')), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
            emit message(index, QString::fromLocal8Bit("密钥验证成功"));
            break;
        }
        }

        switch (mProtocolIndex) {
        case ProtocolBAIC:
        case ProtocolBAIC_BE12:
        {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "22 F1 95", QString::fromLocal8Bit("读取软件版本失败"));
            quint16 version = ((quint16)responseFrame.mData[3] << 8) | (quint8)responseFrame.mData[4];
            QString ecuVersion = QString("%1.%2.%3")
                    .arg(version / 100 / 100 % 100).arg(version / 100 % 100, 2, 10, QLatin1Char('0')).arg(version % 100, 2, 10, QLatin1Char('0'));
            QString softwareVersion = QString("%1_%2-%3-%4_%5").arg(ecuVersion)
                    .arg((quint8)responseFrame.mData[5] + 2000).arg((quint8)responseFrame.mData[6])
                    .arg((quint8)responseFrame.mData[7]).arg((quint8)responseFrame.mData[8]);
            emit message(index, QString::fromLocal8Bit("读取软件版本成功 [%1]").arg(softwareVersion));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index],
                                   QString("2E 12 66 %1 %2 %3 %4")
                                   .arg((mBAICTargetPosition[index][0] & 0xFF00) >> 8, 2, 16, QLatin1Char('0'))
                    .arg((mBAICTargetPosition[index][0] & 0xFF), 2, 16, QLatin1Char('0'))
                    .arg((mBAICTargetPosition[index][1] & 0xFF00) >> 8, 2, 16, QLatin1Char('0'))
                    .arg((mBAICTargetPosition[index][1] & 0xFF), 2, 16, QLatin1Char('0')),
                                   QString::fromLocal8Bit("写入标靶位置失败"));
            emit message(index, QString::fromLocal8Bit("写入标靶位置成功").arg(mBAICTargetPosition[index][0] * 0.01 - 180).arg(mBAICTargetPosition[index][1] * 0.01));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "22 12 66", QString::fromLocal8Bit("读取标靶位置失败"));
            float angle = (((quint16)responseFrame.mData[3] << 8) | (quint8)responseFrame.mData[4]) * 0.01 - 180;
            float angle1 = (((quint16)responseFrame.mData[5] << 8) | (quint8)responseFrame.mData[6]) * 0.01;
            emit message(index, QString::fromLocal8Bit("读取标靶位置成功【%1】【%2】").arg(angle).arg(angle1));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "14 FF FF FF", QString::fromLocal8Bit("清除DTC失败"));
            emit message(index, QString::fromLocal8Bit("清除DTC成功"));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 09", QString::fromLocal8Bit("读取DTC失败"));
            emit message(index, QString::fromLocal8Bit("读取DTC成功【%1】").arg(responseFrame.mData.mid(3, responseFrame.mData.length() - 3).toHex(' ').data()));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 12 7A", QString::fromLocal8Bit("开始标定例程失败"));
            emit message(index, QString::fromLocal8Bit("开始标定例程成功"));
            break;

        }
        case ProtocolGWM:
        {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "2E F1 C2 01", QString::fromLocal8Bit("进入工厂模式失败"));
            emit message(index, QString::fromLocal8Bit("进入工厂模式成功"));
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index],
                                   QString("2E FD 85 %1 %2 %3 %4")
                                   .arg( "C0 DD C2 8F" )   //此处需修改，当前未定规则
                                   .arg( "3F 43 53 F8" )
                                   .arg( "C0 DD C2 8F" )
                                   .arg( "BF 40 C4 9C" ),
                                   QString::fromLocal8Bit("写入标靶位置失败") );
            emit message(index, QString::fromLocal8Bit("写入标靶位置成功")/*.arg(mBAICTargetPosition[index][0] * 0.01 - 180).arg(mBAICTargetPosition[index][1] * 0.01)*/);
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 0A 06", QString::fromLocal8Bit("开始标定例程失败"));
            emit message(index, QString::fromLocal8Bit("开始标定例程成功"));
            break;
        }
        case ProtocolHozon:
        {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "14 FF FF FF", QString::fromLocal8Bit("清故障记录失败"));
            emit message(index, QString::fromLocal8Bit("清故障记录成功"));
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 09", QString::fromLocal8Bit("读取障记录失败"));
            emit message(index, QString::fromLocal8Bit("读取障记录成功"));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 35 F0", QString::fromLocal8Bit("读取条件失败"));
            quint8 state = *( (quint8*)responseFrame.mData.mid( 4, 1 ).data() );
            if (state) {
                QString text;
                switch (state) {
                case 0x01:
                    text = QString::fromLocal8Bit("FL 车门未关闭");
                    break;
                case 0x02:
                    text = QString::fromLocal8Bit("FR 车门未关闭");
                    break;
                case 0x03:
                    text = QString::fromLocal8Bit("RL 车门未关闭");
                    break;
                case 0x04:
                    text = QString::fromLocal8Bit("RR 车门未关闭");
                    break;
                case 0x05:
                    text = QString::fromLocal8Bit("雷达未初始化");
                    break;
                case 0x06:
                    text = QString::fromLocal8Bit("雷达电压不满足，不在 9V-16V 范围内");
                    break;
                case 0x07:
                    text = QString::fromLocal8Bit("车辆行驶中，车速 > 0.3m/s，或者车速无效，或者有DTC 故障");
                    break;
                case 0x08:
                    text = QString::fromLocal8Bit("方向盘转角是否小于 10 度");
                    break;
                default:
                    break;
                }
                emit message(index, QString::fromLocal8Bit("条件不满足：%1") .arg(text));
                emit calibrationFinished(index);
                return;
            }
            emit message(index, QString::fromLocal8Bit("条件满足"));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 35 F1", QString::fromLocal8Bit("例程启动失败"));
            state = *( (quint8*)responseFrame.mData.mid( 4, 1 ).data() );
            if (state == 0x05) {
                emit message(index, QString::fromLocal8Bit("例程启动失败或没有运行"));
                emit calibrationFinished(index);
                return;
            }
            emit message(index, QString::fromLocal8Bit("例程启动成功"));
            break;
        }
        case ProtocolGEELY_E245_J6M:
        {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 30 23 00", QString::fromLocal8Bit("执行Lock Control失败"));
            if (responseFrame.mData.data()[4] == 0x11) {
                emit message(index, QString::fromLocal8Bit("上锁失败"));
                emit calibrationFinished(index); \
                return;
            }
            emit message(index, QString::fromLocal8Bit("执行Lock Control成功"));

            Utils::dely(20);
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 01", QString::fromLocal8Bit("查询雷达故障码"));
            QByteArray data = responseFrame.mData
                    .replace(QByteArray::fromHex("af"), QByteArray())
                    .replace(QByteArray::fromHex("59 02"), QByteArray())
                    .replace(QByteArray::fromHex("5A 67 54"), QByteArray())
                    .replace(QByteArray::fromHex("5A 67 55"), QByteArray());
            if (data.size() > 3){
                emit message(index, QString::fromLocal8Bit("雷达存在故障，故障码是 %1, 请修复故障后再校准").arg(data.toHex(' ').data()));
                emit calibrationFinished(index);
                return;
            }
            emit message(index, QString::fromLocal8Bit("读取故障记录成功"));
            Utils::dely(20);

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "2E D0 36 00 99 00 00", QString::fromLocal8Bit("写入目标物坐标信息失败"));
            emit message(index, QString::fromLocal8Bit("写入目标物坐标信息成功"));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 DC 00 5A 5A", QString::fromLocal8Bit("开启静态标定失败"));
            emit message(index, QString::fromLocal8Bit("开启静态标定成功"));

            break;
        }
        default:
        {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "2E F1 C2 01", QString::fromLocal8Bit("进入工厂模式失败"));
            emit message(index, QString::fromLocal8Bit("进入工厂模式成功"));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "22 F1 C2", QString::fromLocal8Bit("读取工作模式失败"));
            emit message(index, QString::fromLocal8Bit("读取工作模式成功"));
            if (responseFrame.mData.data()[3] != 0x01)
            {
                emit message(index, QString("工作模式返回值不为1") + mUDS[index]->errorString());
                emit calibrationFinished(index);
                return;
            }
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 54 DF", QString::fromLocal8Bit("探测参考目标失败"));
            emit message(index, QString::fromLocal8Bit("探测参考目标成功, 开始校准"));
            break;
        }
        }
        emit calibrationStarted( index );
    }
    else
    {
        switch (mProtocolIndex) {
        case ProtocolBAIC:
        case ProtocolBAIC_BE12:
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 02 12 7A", QString::fromLocal8Bit("终止校准失败"));
            break;
        case ProtocolGWM:
            break;
        case ProtocolHozon:
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "11 01", QString::fromLocal8Bit("重启ECU"));
            emit message(index, QString::fromLocal8Bit("重启ECU成功"));
            emit message(index, QString::fromLocal8Bit("延时3s"));
            Utils::dely(3000);
            break;
        case ProtocolGEELY_E245_J6M:
        {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 02 DC 00", QString::fromLocal8Bit("打断标定流程失败"));
            emit message(index, QString::fromLocal8Bit("打断标定流程成功"));
            return;
            break;
        }
        default:
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 02 54 DF", QString::fromLocal8Bit("终止校准失败"));
            break;
        }
        emit message(index, QString::fromLocal8Bit("终止校准成功, 校准结束"));

        if ((mProtocolIndex != ProtocolBAIC) && (mProtocolIndex != ProtocolBAIC_BE12)) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "2E F1 C2 02", QString::fromLocal8Bit("退出工厂模式失败"));
            emit message(index, QString::fromLocal8Bit("退出工厂模式成功"));
        }

        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "14 FF FF FF", QString::fromLocal8Bit("清除DTC失败"));
        emit message(index, QString::fromLocal8Bit("清除DTC成功"));

        if (mProtocolIndex == ProtocolGEELY_E245_J6M) {
            Utils::dely(500);
        }

        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 09", QString::fromLocal8Bit("读取DTC失败"));
        emit message(index, QString::fromLocal8Bit("读取DTC成功【%1】").arg(responseFrame.mData.mid(3, responseFrame.mData.length() - 3).toHex(' ').data()));

        switch (mProtocolIndex) {
        case ProtocolBAIC:
        case ProtocolBAIC_BE12:
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "11 01", QString::fromLocal8Bit("重启ECU"));
            emit message(index, QString::fromLocal8Bit("重启ECU成功"));
            break;
        default:
            break;
        }

        emit calibrationFinished(index);
    }
}

void StaticCalibrationWorker::result_GWM(int index)
{    
    UDSFrame responseFrame;
    responseFrame.clear();

    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString( "22 FC 02" ), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取校准状态失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }


    bool bEnd = false;
    bool bNext = false;
    quint8 stat = *( (quint8*)responseFrame.mData.mid( 3, 1 ).data() );
//    stat = 3;
    switch ( stat ) {
    case 0x1:  //下一步
        bNext = true;
        break;
    case 0x2: //继续获取校准状态
        emit message(index, QString::fromLocal8Bit("校准执行中") );
        break;
    case 0x3:  //重新开始校准
        if( mFirstReset[index] ){
            mFirstReset[index] = false;
            emit message(index, QString::fromLocal8Bit("重新开始校准") );
            calibration( index, false );
        }else{
            emit message(index, QString::fromLocal8Bit("多次重新校准") );
            bEnd = true;
        }
        break;
    default:
        emit message(index, QString::fromLocal8Bit("未知校准状态") );
        bEnd = true;
        break;
    }

    if( bNext ){
        responseFrame.clear();
        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString( "22 FC 01" ), &responseFrame)) {
            emit message(index, QString::fromLocal8Bit("读取校准角度失败") + mUDS[index]->errorString());
            emit calibrationFinished(index);
            return;
        }
        emit message(index, responseFrame.mData.toHex(' ') );
        quint8* data = (quint8*)responseFrame.mData.mid( 3, 3 ).data();
        switch ( data[0] ) {
        case 0:
            emit message(index, QString::fromLocal8Bit("角雷达安装角度偏向不确定") );
            break;
        case 1:
            emit message(index, QString::fromLocal8Bit("角雷达安装角度向车内方向偏") );
            break;
        case 2:
            emit message(index, QString::fromLocal8Bit("角雷达安装角度向车内方向偏") );
            break;
        default:
            emit message(index, QString::fromLocal8Bit("未知校准角度状态") );
            break;
        }
        double angle = ( ( ( (quint16)data[1] ) << 8 ) + data[2] ) * 0.01;
        emit message(index, QString::fromLocal8Bit("偏差角=%1").arg(angle) );
        bEnd = true;
    }

    if( !bEnd ){
        return;
    }

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "2E F1 C2 02", QString::fromLocal8Bit("退出工厂模式失败"));
    emit message(index, QString::fromLocal8Bit("退出工厂模式成功"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "14 FF FF FF", QString::fromLocal8Bit("清除DTC失败"));
    emit message(index, QString::fromLocal8Bit("清除DTC成功"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 09", QString::fromLocal8Bit("读取DTC失败"));
    emit message(index, QString::fromLocal8Bit("读取DTC成功"));

    emit calibrationFinished(index, true);
}

void StaticCalibrationWorker::result_Hozon(int index)
{
    UDSFrame responseFrame;
    responseFrame.clear();

    bool bEnd = false;
    bool bNext = false;

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 03 35 F1", QString::fromLocal8Bit("读取例程状态失败"));
    emit message(index, QString::fromLocal8Bit("读取例程状态成功"));
    quint8 state = *( (quint8*)responseFrame.mData.mid( 4, 1 ).data() );
    quint8 process = *( (quint8*)responseFrame.mData.mid( 5, 1 ).data() );
    switch (state) {
    case 0x00:
        emit message(index, QString::fromLocal8Bit("例程初始化 【%1】").arg(process));
        break;
    case 0x01:
        emit message(index, QString::fromLocal8Bit("标定进行中 【%1】").arg(process));
        break;
    case 0x02:
        emit message(index, QString::fromLocal8Bit("标定成功 【%1】").arg(process));
        bNext = true;
        break;
    case 0x03:
        emit message(index, QString::fromLocal8Bit("标定不成功 【%1】").arg(process));
        bEnd = true;
    default:
        break;
    }

    if( bNext  || bEnd){
        responseFrame.clear();
        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString( "22 F1 04" ), &responseFrame)) {
            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
            emit message(index, QString::fromLocal8Bit("读取校准状态失败") + mUDS[index]->errorString());
            emit calibrationFinished(index);
            return;
        }

        state = *( (quint8*)responseFrame.mData.mid( 3, 1 ).data() );
        //    stat = 3;
        switch ( state ) {
        case 0x0:  //下一步
            bNext = true;
            break;
        default:
        {
            bNext = false;
            QString text;
            if (state & 0x01) {
                text.append(QString::fromLocal8Bit("【标定超时】"));
            }
            if (state & 0x02)  {
                text.append(QString::fromLocal8Bit("【角度超范】"));
            }
            if (state & 0x04)  {
                text.append(QString::fromLocal8Bit("【目标不充分】"));
            }
            if (state & 0x08)  {
                text.append(QString::fromLocal8Bit("【电压超范围】"));
            }
            if (state & 0x10)  {
                text.append(QString::fromLocal8Bit("【未知异常】"));
            }
            emit message(index, QString::fromLocal8Bit("标定失败! %1").arg(text) );

            bEnd = true;
        }
            break;
        }
    }

    if( bNext ){
        responseFrame.clear();
        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString( "22 F1 03" ), &responseFrame)) {
            emit message(index, QString::fromLocal8Bit("读取校准角度失败") + mUDS[index]->errorString());
            emit calibrationFinished(index);
            return;
        }
        emit message(index, responseFrame.mData.toHex(' ') );
        quint8* data = (quint8*)responseFrame.mData.mid( 3, 4 ).data();
        double angle = ( ( ( (quint16)data[0] ) << 8 ) + data[1] ) * 0.01 - 180;
        double eangle = ( ( ( (quint16)data[2] ) << 8 ) + data[3] ) * 0.01 - 180;
        emit message(index, QString::fromLocal8Bit("水平角度=%1, 垂直角度=%2").arg(angle).arg(eangle) );
        bEnd = true;
    }

    if( !bEnd ){
        return;
    }

//    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "11 01", QString::fromLocal8Bit("重启ECU"));
//    emit message(index, QString::fromLocal8Bit("重启ECU成功"));

    emit message(index, QString::fromLocal8Bit("延时3s"));
    Utils::dely(3000);

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "14 FF FF FF", QString::fromLocal8Bit("清除DTC失败"));
    emit message(index, QString::fromLocal8Bit("清除DTC成功"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 09", QString::fromLocal8Bit("读取DTC失败"));
    emit message(index, QString::fromLocal8Bit("读取DTC成功"));

    emit calibrationFinished(index, true);
}

void StaticCalibrationWorker::result_GEELY_E245_J6M(int index)
{
    UDSFrame responseFrame;
    responseFrame.clear();

    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("31 03 DC 00"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("请求标定运行结果失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("请求标定运行结果成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');

    quint16 process = (quint16)((((quint16)responseFrame.mData[4]) << 8) + (quint8)responseFrame.mData[5]);
    switch (process) {
    case 0x2204:
        emit message(index, QString::fromLocal8Bit("标定正在执行"));
        return;
    case 0x2000:
        emit message(index, QString::fromLocal8Bit("标定成功"));
        break;
    case 0x2001:
        emit message(index, QString::fromLocal8Bit("标定失败"));
        break;
    }

    responseFrame.clear();
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("22 D0 5B"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取校准状态失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("读取校准状态成功"));
    emit message(index, responseFrame.mData.toHex(' '));

//    emit message(index, responseFrame.mData.toHex(' '));
    QString text;
    switch (responseFrame.mData[3])
    {
    case 0x01:
        text.append(QString::fromLocal8Bit("【雷达未校准】"));
        break;
    case 0x02:
        text.append(QString::fromLocal8Bit("【雷达已校准】"));
        break;
    case 0x04:
        text.append(QString::fromLocal8Bit("【水平角超差】"));
        break;
    case 0x0D:
        text.append(QString::fromLocal8Bit("【雷达故障】"));
        break;
    case 0x0E:
        text.append(QString::fromLocal8Bit("【射频未启动】"));
        break;
    case 0x0F:
        text.append(QString::fromLocal8Bit("【车辆或目标移动】"));
        break;
    case 0x11:
        text.append(QString::fromLocal8Bit("【标定超时(6s)】"));
        break;
    case 0x16:
        text.append(QString::fromLocal8Bit("【NVM角度写入失败】"));
        break;
    }
    emit message(index, text);

    responseFrame.clear();
    Utils::dely(20);
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("22 D0 39"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取雷达水平角偏差失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("读取雷达水平角偏差成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    emit message(index, QString::fromLocal8Bit("【水平偏差角度 %1°】").arg((int8_t)responseFrame.mData[3] * 0.1f));


    responseFrame.clear();
    Utils::dely(20);
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("19 02 29"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取DTC失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }
    emit message(index, QString::fromLocal8Bit("读取DTC成功"));
    emit message(index, responseFrame.mData.toHex(' '));

    responseFrame.clear();
    Utils::dely(20);
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("14 FF FF FF"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("清除DTC失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }
    emit message(index, QString::fromLocal8Bit("清除DTC成功"));

    emit calibrationFinished(index);
}

void StaticCalibrationWorker::initFirstReset()
{
    for( int i=0; i<(sizeof (mFirstReset) / sizeof (mFirstReset[0])); i++ ){
        mFirstReset[i] = true;
    }
}

void StaticCalibrationWorker::result(int index)
{
    QMutexLocker lock( &mMutex[index] ); //此函数不可重入

    switch( mProtocolIndex) {
    case ProtocolGWM:
    {
        return result_GWM( index );
    }
    case ProtocolHozon:
    {
        return result_Hozon( index );
    }
    case ProtocolGEELY_E245_J6M:
    {
        return result_GEELY_E245_J6M( index );
    }
    default:
        break;
    }

    UDSFrame responseFrame;
    responseFrame.clear();

    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index],
                               QString(((mProtocolIndex == ProtocolBAIC) ||
                                        (mProtocolIndex == ProtocolBAIC_BE12))? "31 03 12 7A" : "31 03 54 DF"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取校准结果失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("读取校准结果成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
    if ((responseFrame.mData[4] & 0xF) == 0x01)
    {
        emit message(index, QString::fromLocal8Bit("校准正在执行..."));
        return;
    }

//    emit message(index, responseFrame.mData.toHex(' '));
    QString text;
    switch (responseFrame.mData[4] & 0xF)
    {
    case 0x0:
        text.append(QString::fromLocal8Bit("【校准不在执行】"));
        break;
    case 0x1:
        text.append(QString::fromLocal8Bit("【校准正在执行】"));
        break;
    case 0x2:
        text.append(QString::fromLocal8Bit("【校准写入NVM失败】"));
        break;
    case 0x3:
        text.append(QString::fromLocal8Bit("【校准执行超时】"));
        break;
    case 0x4:
        text.append(QString::fromLocal8Bit("【校准正确执行完毕】"));
        break;
    case 0x5:
        text.append(QString::fromLocal8Bit("【校准执行中止】"));
        break;
    }
    switch (responseFrame.mData[4] & 0xF0)
    {
    case 0x00:
        text.append(QString::fromLocal8Bit("【校准结果未出】"));
        break;
    case 0x10:
        text.append(QString::fromLocal8Bit("【校准结果不正确】"));
        break;
    case 0x20:
        text.append(QString::fromLocal8Bit("【校准结果正确】"));
        break;
    }
    double angle = 0.0;
    quint16 _angle = (quint16)((((quint16)responseFrame.mData[5]) << 8) + (quint8)responseFrame.mData[6]);
    if (_angle < 0x8000)
    {
        angle = _angle * 0.01;
    }
    else
    {
        angle = (_angle - 0xFFFF) * 0.01;
    }
    text.append(QString::fromLocal8Bit("【水平偏差角度 %1°】").arg(angle));
    if ((mProtocolIndex == ProtocolBAIC) || (mProtocolIndex == ProtocolBAIC_BE12)) {
        angle = 0.0;
        _angle = (quint16)((((quint16)responseFrame.mData[7]) << 8) + (quint8)responseFrame.mData[8]);

        angle = _angle * 0.1;
        text.append(QString::fromLocal8Bit("【垂直偏差角度 %1°】").arg(angle));
    }

    emit message(index, text);

    if (responseFrame.mData.data()[4] != 0x24){
        return;
    }

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "2E F1 C2 02", QString::fromLocal8Bit("退出工厂模式失败"));
    emit message(index, QString::fromLocal8Bit("退出工厂模式成功"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "14 FF FF FF", QString::fromLocal8Bit("清除DTC失败"));
    emit message(index, QString::fromLocal8Bit("清除DTC成功"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 09", QString::fromLocal8Bit("读取DTC失败"));
    emit message(index, QString::fromLocal8Bit("读取DTC成功"));

    emit calibrationFinished(index, true);
}

void StaticCalibrationWorker::canFrame(const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        if (frame.id() == mUDS[i]->responseID())
        {
            mUDS[i]->appendCanFrame(frame);
            emit sendOrRecvCanFrame( i, false, frame.id(), frame.dataHex() );
            break;
        }
    }
}

void StaticCalibrationWorker::frameTransmited(const Devices::Can::CanFrame &frame, bool success)
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        if ( frame.id() == mPhysicalAddress[mProtocolIndex][i] || frame.id() == mFunctionAddress[mProtocolIndex][i] ){
            emit sendOrRecvCanFrame( i, true, frame.id(), frame.dataHex() );
            break;
        }
    }
}


} // namespace Functions
