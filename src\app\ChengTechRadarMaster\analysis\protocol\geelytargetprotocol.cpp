﻿#include "geelytargetprotocol.h"

namespace Analysis {
namespace Protocol {

static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
    uint32_t mask = ((1 << bits) - 1);
    uint32_t extracted = data & mask;
    int32_t sign_extended = (extracted & (1 << (bits - 1))) ? (int)(extracted | (~mask)) : (int)extracted;
    return sign_extended;
}

GEELYTargetProtocol::GEELYTargetProtocol( AnalysisWorker *analysisWorker, QObject *parent )
    : IAnalysisProtocol( analysisWorker, parent )
{
    mProtocolType = ProtocolGEELY;
}

void GEELYTargetProtocol::setChannelRadarID(int *channelRadarID, int size)
{
    for (int i = 0; i < size && i < (sizeof (mGEELYChannelRadarID) / sizeof (mGEELYChannelRadarID[0])); ++i) {
        mGEELYChannelRadarID[i] = channelRadarID[i];
        qDebug() << __FUNCTION__ << __LINE__ << channelRadarID[i] << mGEELYChannelRadarID[i];
    }
}

bool GEELYTargetProtocol::analysisFrame(const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.channelIndex() << frame.idHex() << frame.dataHex();
    //frame.channelIndex();
    int radarID = 4;
    switch (mGEELYChannelRadarID[frame.channelIndex() % 2]) {
    case 0:
        radarID = 6;
        break;
    case 1:
        radarID = 4;
        break;
    }
    bool ret = false;
    switch (frame.id())
    {
    case 0x129:
        ret = GEELY16TargetHeaderParse(radarID, frame);
        break;
    case 0x15C:
    case 0x15E:
    case 0x160:
    case 0x162:
    case 0x164:
    case 0x166:
    case 0x168:
    case 0x16A:
    case 0x16C:
    case 0x16E:
    case 0x170:
    case 0x172:
    case 0x174:
    case 0x176:
    case 0x178:
        ret = GEELY16TargetParse(radarID, frame);
        break;
    case 0x12E:
        ret = GEELY16TargetHeaderParse(radarID + 1, frame);
        break;
    case 0x15D:
    case 0x15F:
    case 0x161:
    case 0x163:
    case 0x165:
    case 0x167:
    case 0x169:
    case 0x16B:
    case 0x16D:
    case 0x16F:
    case 0x171:
    case 0x173:
    case 0x175:
    case 0x177:
    case 0x179:
        ret = GEELY16TargetParse(radarID + 1, frame);
        break;
    default:
        return false;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << frame.channelIndex() << mGEELYChannelRadarID[frame.channelIndex() % 2] << frame.idHex() << frame.dataHex();
    return ret;
}

bool GEELYTargetProtocol::GEELY16TargetHeaderParse(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
        return false;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << radarID;

    mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.clear();
    mGEELY16TargetCurrentIndex[radarID] = 0;

    const uint8_t *data = (const uint8_t *)frame.data().data();

//    userData->ReSideRdrLeStsForDim_UB = ((data[14] & 0x10U) >> 4);
//    userData->ReSideRdrLeStsForDim = ((data[15] & 0xE0U) >> 5);
//    userData->ReSideRdrLeStsRdrStsChks = (data[16]);
//    userData->ReSideRdrLeStsRdrStsCntr = ((data[17] & 0xF0U) >> 4);
//    userData->ReSideRdrLeStsRdrStsIfVersMajor = (data[17] & 0xFU);
//    userData->ReSideRdrLeStsRdrStsIfVersMinor = ((data[18] & 0xF0U) >> 4);
//    userData->ReSideRdrLeStsRdrStsMisAlign = ((data[18] & 0xEU) >> 1);
//    userData->ReSideRdrLeStsRdrStsMissCom = (data[18] & 0x1U);
//    userData->ReSideRdrLeStsRdrStsTiStamp = (((data[22] & 0xFFU) + (((uint32_t)data[21]) << 8) + (((uint32_t)data[20]) << 16) + (((uint32_t)data[19] & 0xFFU) << 24)) * 0.0001);
//    userData->ReSideRdrLeStsRdrStsLatency = ((data[24] & 0xFFU) + (((uint16_t)data[23] & 0xFFU) << 8));
//    userData->ReSideRdrLeStsRdrNrDetn = (data[25]);
    mGEELY16TargetCount[radarID] = (data[26]);
//    userData->ReSideRdrLeStsRdrStsBlkdConf = ((data[27] & 0xFEU) >> 1);
//    userData->ReSideRdrLeStsRdrStsBlkd0bin = (data[27] & 0x1U);
//    userData->ReSideRdrLeStsRdrStsWhlSpdCmpFac = ((((data[28] & 0xF8U) >> 3) * 0.005) + 0.92);
//    userData->ReSideRdrLeStsRdrStsTiStampStsTiOut = ((data[28] & 0x4U) >> 2);
//    userData->ReSideRdrLeStsRdrStsTiStampStsTiLeap = ((data[28] & 0x2U) >> 1);
//    userData->ReSideRdrLeStsRdrStsTiStampStsSyncToGatewy = (data[28] & 0x1U);
//    userData->ReSideRdrLeStsRdrStsDstbc = ((data[29] & 0xFEU) >> 1);
//    userData->ReSideRdrLeStsRdrStsEna = (data[29] & 0x1U);
//    userData->ReSideRdrLeStsRdrStsFaulty = ((data[30] & 0x80U) >> 7);
//    userData->ReSideRdrLeStsRdrStsSftyFlt = ((data[30] & 0x60U) >> 5);
//    userData->ReSideRdrLeStsRdrStsTiStampStsGlbTiBas = ((data[30] & 0x10U) >> 4);
//    userData->ReSideRdrLeSts_UB = ((data[30] & 0x8U) >> 3);

//    qDebug() << __FUNCTION__ << __LINE__ << mGEELY16TargetCount[radarID];

    return true;
}

bool GEELYTargetProtocol::GEELY16TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
            return false;
        }

    const uint8_t *data = (const uint8_t *)frame.data().data();
    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount; // 注意必须使用引用
    if (data[20] != 0x00 && mGEELY16TargetCurrentIndex[radarID]++ < mGEELY16TargetCount[radarID]) {
        Target &target1 = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets[targetCount++];
        target1.mProtocolType = ProtocolGEELY;
        target1.mAx = ((decode_sign_bit(data[0], 8)) * 0.1);
        target1.mAy = ((decode_sign_bit(data[1], 8)) * 0.1);
        target1.mTrackFrameX = (((((data[3] & 0x80U) >> 7) + (((uint16_t)data[2] & 0xFFU) << 1)) * 0.1) - 25);
        target1.mTrackFrameY = (((((data[4] & 0xC0U) >> 6) + (((uint16_t)data[3] & 0x7FU) << 2)) * 0.1) - 25);
        target1.mTrackFrameLength = ((((data[5] & 0xE0U) >> 5) + (((uint16_t)data[4] & 0x3FU) << 3)) * 0.1);
        target1.mTrackFrameWidth = ((((data[6] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0x1FU) << 4)) * 0.1);
        target1.mObjectType = (data[6] & 0xFU);
        target1.mObservationHist = ((data[10] & 0xFFU) + (((uint32_t)data[9]) << 8) + (((uint32_t)data[8]) << 16) + (((uint32_t)data[7] & 0xFFU) << 24));
        target1.mChks = (data[11]);
        target1.mCntr = ((data[12] & 0xF0U) >> 4);
        target1.mConf = ((data[12] & 0xFU) * 7);
        target1.mXStd = ((data[13]) * 0.05);
        target1.mX = (((((data[15] & 0xF8U) >> 3) + (((uint16_t)data[14] & 0xFFU) << 5)) * 0.05) - 200);
        target1.mMtnPat = (data[15] & 0x7U);
        target1.mVy = (((((data[17] & 0xF0U) >> 4) + (((uint16_t)data[16] & 0xFFU) << 4)) * 0.02) - 35.6);
        target1.mVx = ((((data[18] & 0xFFU) + (((uint16_t)data[17] & 0xFU) << 8)) * 0.02) - 35.6);
        target1.mVyStd = (((data[19] & 0xFCU) >> 2) * 0.05);
        target1.mElevnSts = (data[19] & 0x3U);
        target1.mID = (data[20]);
        target1.mTypConfBike = (((data[21] & 0xF0U) >> 4) * 7);
        target1.mTypConfPed = ((data[21] & 0xFU) * 7);
        target1.mYStd = ((data[22]) * 0.05);
        target1.mY = (((((data[24] & 0xF8U) >> 3) + (((uint16_t)data[23] & 0xFFU) << 5)) * 0.05) - 200);
        target1.mTrackSts = ((data[24] & 0x6U) >> 1);
        target1.mIsInFreeSpace = (data[24] & 0x1U);
        target1.mVysog = (((((data[26] & 0xFCU) >> 2) + (((uint16_t)data[25] & 0xFFU) << 6)) * 0.02) - 128);
        target1.mElevnConf = (((data[27] & 0xF0U) >> 4) * 7);
        target1.mMirrProblty = ((data[27] & 0xFU) * 7);
        target1.mNotRealProblty = (((data[28] & 0xF0U) >> 4) * 7);
        target1.mTypConfVeh = ((data[28] & 0xFU) * 7);
        target1.mStatyCnt = ((data[29] & 0xFEU) >> 1);
        target1.mTiAlv = ((data[30] & 0xFEU) >> 1);
        target1.mVxsog = (((((data[32] & 0xFCU) >> 2) + (((uint16_t)data[31] & 0xFFU) << 6)) * 0.02) - 128);
        target1.mVxStd = (((data[33] & 0xFCU) >> 2) * 0.05);
        target1.mCoastCnt = ((data[34] & 0xF8U) >> 3);
        target1.mTrackFrameAngle = (((((data[36] & 0xF0U) >> 4) + (((uint16_t)data[35] & 0xFFU) << 4)) * 0.1) - 180);
////        target1.ReSideRdrLeObj1RdrObjUsedTracker = ((data[36] & 0xCU) >> 2);
////        target1.ReSideRdrLeObj1QualityBits = ((data[38] & 0xFFU) + (((uint32_t)data[37]) << 8) + (((uint32_t)data[36] & 0x3U) << 16));
////        target1.ReSideRdrLeObj1_UB = ((data[39] & 0x80U) >> 7);

        target1.mTrackFrameX += target1.mX;
        target1.mTrackFrameY += target1.mY;

        target1.mValid = true;//(target1.mX != 0 || target1.mY != 0);
        switch ((int)target1.mObjectType) {
        case 0:
            target1.mClass = ObjectUnknown;
            break;
        case 1:
            target1.mClass = ObjectCar;
            break;
        case 2:
            target1.mClass = ObjectMotorcycle;
            break;
        case 3:
            target1.mClass = ObjectTruck;
            break;
        case 4:
            target1.mClass = ObjectPedestran;
            break;
        case 5:
            target1.mClass = OjbectObstdVert_1;
            break;
        case 6:
            target1.mClass = OjbectObstdVert_2;
            break;
        case 7:
            target1.mClass = ObjectAnimal;
            break;
        case 8:
            target1.mClass = ObjectObjGen;
            break;
        case 9:
            target1.mClass = ObjectVehofUnknown;
            break;
        default:
            target1.mClass = ObjectUnknown;
            break;
        }

//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << radarID << target1.mID << target1.mX << target1.mY << target1.mObjectType << target1.mClass;
    }

    bool bEndFrame = false;
    if (frame.id() == 0x178 || frame.id() == 0x179) {
        mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mValid = true;
        mAnalysisWorker->analysisEnd(radarID, Frame16Track);
        bEndFrame = true;
//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << radarID << mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mValid;
    }

    mAnalysisWorker->analysisTargetPoint16FrameEnd( radarID, frame, bEndFrame );
    return true;
}

}
}
