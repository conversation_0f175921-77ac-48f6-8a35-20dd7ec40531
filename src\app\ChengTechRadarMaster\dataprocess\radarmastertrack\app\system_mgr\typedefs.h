﻿/**************************************************************************
 * FILE NAME: $RCSfile: typedefs.h,v $       COPYRIGHT (c) Freescale 2005 *
 * DESCRIPTION:                                     All Rights Reserved   *
 * This file defines all of the data types for the Motorola header file.  *
 *========================================================================*
 * ORIGINAL AUTHOR: r12110                                                *
 * $Log: typedefs.h,v $
 * Revision 1.5 2016/05/23 11:20:19   b47769
 * Diab compiler compatibility
 *
 * Revision 1.4  2008/02/19 11:10:09  b04629
 * Fixed previous comment.
 *
 * Revision 1.3  2008/02/19 11:01:43  b04629
 * Replaced // comments for MISRA.
 *
 * Revision 1.2  2007/11/22 13:18:55  b04629
 * Removed original author name and replaced with author ID.
 *
 * Revision 1.1  2007/03/22 08:55:15  b04629
 * Imported and updated from MPC55xx Headers Rev 1.9
 *                                                                        *
 *                                                                        *
 **************************************************************************/

#ifndef _TYPEDEFS_H_
#define _TYPEDEFS_H_

#ifndef PC_DBG_FW
#include "embARC_toolchain.h"
#include "embARC.h"
#include "embARC_error.h"
#include "embARC_debug.h"
#include "arc_exception.h"
#else
#include <stdio.h>
#include <stdint.h>
#endif

#if defined (__GNUC__)  /* GCC compiler*/
#include <stdint.h>
    /* Short names for volatiles used by header files, based on ISO C standard */
    typedef volatile int8_t vint8_t;
    typedef volatile uint8_t vuint8_t;

    typedef volatile int16_t vint16_t;
    typedef volatile uint16_t vuint16_t;

    typedef volatile int32_t vint32_t;
    typedef volatile uint32_t vuint32_t;    

#elif defined (__MWERKS__)    /* Metrowerk CodeWarrior */
    #include <stdint.h>

    /*  Standard typedefs used by header files, based on ISO C standard */
    typedef volatile int8_t vint8_t;
    typedef volatile uint8_t vuint8_t;

    typedef volatile int16_t vint16_t;
    typedef volatile uint16_t vuint16_t;

    typedef volatile int32_t vint32_t;
    typedef volatile uint32_t vuint32_t;

#elif defined (__ghs__)    /* GreenHills */
    #include <stdint.h>

    /* Standard typedefs used by header files, based on ISO C standard */
    typedef volatile int8_t vint8_t;
    typedef volatile uint8_t vuint8_t;

    typedef volatile int16_t vint16_t;
    typedef volatile uint16_t vuint16_t;

    typedef volatile int32_t vint32_t;
    typedef volatile uint32_t vuint32_t;
#else
/* This is needed for compilers that don't have a stdint.h file i.e. DIAB */

#ifndef PC_DBG_FW
typedef signed char int8_t;
typedef unsigned char uint8_t;
typedef volatile signed char vint8_t;
typedef volatile unsigned char vuint8_t;

typedef signed short int16_t;
typedef unsigned short uint16_t;
typedef volatile signed short vint16_t;
typedef volatile unsigned short vuint16_t;

typedef signed long int32_t;
typedef unsigned long uint32_t;
typedef volatile signed long vint32_t;
typedef volatile unsigned long vuint32_t;

/* 8-byte Extended type, supported by DIAB */
typedef long long int64_t;
typedef unsigned long long uint64_t;
#endif

#endif

typedef uint64_t UINT64;
typedef uint32_t UINT32;
typedef int32_t  INT32;
typedef int32_t  INT;
typedef uint16_t UINT16;
typedef int16_t  INT16;
typedef uint8_t  UINT8;
typedef int8_t   INT8;

typedef uint64_t u64;
typedef uint32_t u32;
typedef int32_t  s32;
typedef uint16_t u16;
typedef int16_t  s16;
typedef uint8_t  u8;
typedef int8_t   s8;


#define IS_BIG_ENDIAN 0

/*********************************************************************
 *
 * Copyright:
 *	Freescale Semiconductor, INC. All Rights Reserved.
 *  You are hereby granted a copyright license to use, modify, and
 *  distribute the SOFTWARE so long as this entire notice is
 *  retained without alteration in any modified and/or redistributed
 *  versions, and that such modified versions are clearly identified
 *  as such. No licenses are granted by implication, estoppel or
 *  otherwise under any patents or trademarks of Freescale
 *  Semiconductor, Inc. This software is provided on an "AS IS"
 *  basis and without warranty.
 *
 *  To the maximum extent permitted by applicable law, Freescale
 *  Semiconductor DISCLAIMS ALL WARRANTIES WHETHER EXPRESS OR IMPLIED,
 *  INCLUDING IMPLIED WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A
 *  PARTICULAR PURPOSE AND ANY WARRANTY AGAINST INFRINGEMENT WITH
 *  REGARD TO THE SOFTWARE (INCLUDING ANY MODIFIED VERSIONS THEREOF)
 *  AND ANY ACCOMPANYING WRITTEN MATERIALS.
 *
 *  To the maximum extent permitted by applicable law, IN NO EVENT
 *  SHALL Freescale Semiconductor BE LIABLE FOR ANY DAMAGES WHATSOEVER
 *  (INCLUDING WITHOUT LIMITATION, DAMAGES FOR LOSS OF BUSINESS PROFITS,
 *  BUSINESS INTERRUPTION, LOSS OF BUSINESS INFORMATION, OR OTHER
 *  PECUNIARY LOSS) ARISING OF THE USE OR INABILITY TO USE THE SOFTWARE.
 *
 *  Freescale Semiconductor assumes no responsibility for the
 *  maintenance and support of this software
 *
 ********************************************************************/

//=============================================================================
// STANDARD ENUMERATIONS
//=============================================================================
#ifndef PC_DBG_FW
typedef enum return_value
{
   	SUCCESS,						// error code 0x00
   	ERROR,							// error code 0x01
   	TIMEOUT,						// error code 0x02
	MODE_CHANGE_ERROR,				// error code 0x03
   	INVALID_PARAMETER,				// error code 0x04
	CHECKSUM_ERROR,					// error code 0x05
	TRIMMING_ERROR,					// error code 0x06
	LIN_TRANSMIT_ERROR,				// error code 0x07
	ENET_SPEED_ERROR,				// error code 0x08
	UTE_ERROR						// error code 0x09
}retval_t;
#endif

#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif

#ifndef true
#define true 1
#endif

#ifndef false
#define false 0
#endif

#ifndef PI
#define PI 3.1415926535f
#endif

//部分APP使用到的类型定义

#define CAN_TX_BUF_SIZE 500
#define CAN_RX_PKG_LEN 8

typedef struct
{
    uint32_t start;
    uint32_t num;
    uint32_t idx;
    uint32_t id[CAN_TX_BUF_SIZE];
    uint32_t len[CAN_TX_BUF_SIZE];  //对每一帧数据的长度进行记录，边长处理
    uint64_t data[CAN_TX_BUF_SIZE][8];  //最大支持64字节的处理
} TxTargets_t;

typedef struct tagCANRxBuf
{
    uint32_t id;
    unsigned char data[CAN_RX_PKG_LEN];
    uint8_t dlc;
} TCANRxBuf;


typedef struct {
    uint32_t swtResetCnt;  //看门狗重启次数
    uint32_t startUpCnt;//雷达重启次数
    uint32_t magicFlag;
    uint32_t excpt_no; //异常号
    uint32_t excpt_ret_reg; //异常地址
    uint32_t excpt_cause_reg; //异常寄存器
    uint32_t excpt_Flag; //上次掉电异常标志
    uint32_t task_can0_tx_err;  //存储任务中函数的输入输出情况
    uint32_t task_can1_tx_err;
    uint32_t task_can0_rx_err;
    uint32_t task_can1_rx_err;
    uint32_t task_baseband_err;
    uint32_t task_data_pro_err;
    uint32_t task_gpio_err;
    uint32_t task_uds_err;
    uint32_t task_sys_det_err;

    uint32_t task_can0_tx_cnt;  //用于存储运行顺序，用于排查错误时候知道函数进入顺序
    uint32_t task_can1_tx_cnt;
    uint32_t task_can0_rx_cnt;
    uint32_t task_can1_rx_cnt;
    uint32_t task_baseband_cnt;
    uint32_t task_data_pro_cnt;
    uint32_t task_gpio_cnt;
    uint32_t task_uds_cnt;
    uint32_t task_sys_det_cnt;

    uint32_t task_can0_tx_st_size;  //存储任务栈大小
    uint32_t task_can1_tx_st_size;
    uint32_t task_can0_rx_st_size;
    uint32_t task_can1_rx_st_size;
    uint32_t task_baseband_st_size;
    uint32_t task_data_pro_st_size;
    uint32_t task_gpio_st_size;
    uint32_t task_uds_st_size;
    uint32_t task_sys_det_st_size;
    char curTskName[12];
    uint8_t cnt_excep;          //进入异常的次数
    uint8_t is_excep_occur;     //上一次是否发送异常的标记
    uint8_t cnt_wdg_reset;      //进入看门狗重启的次数
    uint8_t is_wdg_reset_occur; //上一次是否进入看门狗重启的标记，在第一次读取之后要清除掉
    uint8_t  resv[300];
} startUpInfo_t; //最大只能共预留 512个字节


//函数入口标记
#define TSK_CAN0_TX_ERR_0  0x00000001U
#define TSK_CAN0_TX_ERR_1  0x00000002U
#define TSK_CAN0_TX_ERR_2  0x00000004U
#define TSK_CAN0_TX_ERR_3  0x00000008U
#define TSK_CAN0_TX_ERR_4  0x00000010U
#define TSK_CAN0_TX_ERR_5  0x00000020U
#define TSK_CAN0_TX_ERR_6  0x00000040U
#define TSK_CAN0_TX_ERR_7  0x00000080U
#define TSK_CAN0_TX_ERR_8  0x00000100U
#define TSK_CAN0_TX_ERR_9  0x00000200U
#define TSK_CAN0_TX_ERR_10 0x00000400U
#define TSK_CAN0_TX_ERR_11 0x00000800U
#define TSK_CAN0_TX_ERR_12 0x00001000U
#define TSK_CAN0_TX_ERR_13 0x00002000U
#define TSK_CAN0_TX_ERR_14 0x00004000U
#define TSK_CAN0_TX_ERR_15 0x00008000U
#define TSK_CAN0_TX_ERR_16 0x00010000U
#define TSK_CAN0_TX_ERR_17 0x00020000U
#define TSK_CAN0_TX_ERR_18 0x00040000U
#define TSK_CAN0_TX_ERR_19 0x00080000U
#define TSK_CAN0_TX_ERR_20 0x00100000U
#define TSK_CAN0_TX_ERR_21 0x00200000U
#define TSK_CAN0_TX_ERR_22 0x00400000U
#define TSK_CAN0_TX_ERR_23 0x00800000U
#define TSK_CAN0_TX_ERR_24 0x01000000U
#define TSK_CAN0_TX_ERR_25 0x02000000U
#define TSK_CAN0_TX_ERR_26 0x04000000U
#define TSK_CAN0_TX_ERR_27 0x08000000U
#define TSK_CAN0_TX_ERR_28 0x10000000U
#define TSK_CAN0_TX_ERR_29 0x20000000U
#define TSK_CAN0_TX_ERR_30 0x40000000U
#define TSK_CAN0_TX_ERR_31 0x80000000U

#define TSK_CAN1_TX_ERR_0  0x00000001U
#define TSK_CAN1_TX_ERR_1  0x00000002U
#define TSK_CAN1_TX_ERR_2  0x00000004U
#define TSK_CAN1_TX_ERR_3  0x00000008U
#define TSK_CAN1_TX_ERR_4  0x00000010U
#define TSK_CAN1_TX_ERR_5  0x00000020U
#define TSK_CAN1_TX_ERR_6  0x00000040U
#define TSK_CAN1_TX_ERR_7  0x00000080U
#define TSK_CAN1_TX_ERR_8  0x00000100U
#define TSK_CAN1_TX_ERR_9  0x00000200U
#define TSK_CAN1_TX_ERR_10 0x00000400U
#define TSK_CAN1_TX_ERR_11 0x00000800U
#define TSK_CAN1_TX_ERR_12 0x00001000U
#define TSK_CAN1_TX_ERR_13 0x00002000U
#define TSK_CAN1_TX_ERR_14 0x00004000U
#define TSK_CAN1_TX_ERR_15 0x00008000U
#define TSK_CAN1_TX_ERR_16 0x00010000U
#define TSK_CAN1_TX_ERR_17 0x00020000U
#define TSK_CAN1_TX_ERR_18 0x00040000U
#define TSK_CAN1_TX_ERR_19 0x00080000U
#define TSK_CAN1_TX_ERR_20 0x00100000U
#define TSK_CAN1_TX_ERR_21 0x00200000U
#define TSK_CAN1_TX_ERR_22 0x00400000U
#define TSK_CAN1_TX_ERR_23 0x00800000U
#define TSK_CAN1_TX_ERR_24 0x01000000U
#define TSK_CAN1_TX_ERR_25 0x02000000U
#define TSK_CAN1_TX_ERR_26 0x04000000U
#define TSK_CAN1_TX_ERR_27 0x08000000U
#define TSK_CAN1_TX_ERR_28 0x10000000U
#define TSK_CAN1_TX_ERR_29 0x20000000U
#define TSK_CAN1_TX_ERR_30 0x40000000U
#define TSK_CAN1_TX_ERR_31 0x80000000U

#define TSK_CAN0_RX_ERR_0  0x00000001U
#define TSK_CAN0_RX_ERR_1  0x00000002U
#define TSK_CAN0_RX_ERR_2  0x00000004U
#define TSK_CAN0_RX_ERR_3  0x00000008U
#define TSK_CAN0_RX_ERR_4  0x00000010U
#define TSK_CAN0_RX_ERR_5  0x00000020U
#define TSK_CAN0_RX_ERR_6  0x00000040U
#define TSK_CAN0_RX_ERR_7  0x00000080U
#define TSK_CAN0_RX_ERR_8  0x00000100U
#define TSK_CAN0_RX_ERR_9  0x00000200U
#define TSK_CAN0_RX_ERR_10 0x00000400U
#define TSK_CAN0_RX_ERR_11 0x00000800U
#define TSK_CAN0_RX_ERR_12 0x00001000U
#define TSK_CAN0_RX_ERR_13 0x00002000U
#define TSK_CAN0_RX_ERR_14 0x00004000U
#define TSK_CAN0_RX_ERR_15 0x00008000U
#define TSK_CAN0_RX_ERR_16 0x00010000U
#define TSK_CAN0_RX_ERR_17 0x00020000U
#define TSK_CAN0_RX_ERR_18 0x00040000U
#define TSK_CAN0_RX_ERR_19 0x00080000U
#define TSK_CAN0_RX_ERR_20 0x00100000U
#define TSK_CAN0_RX_ERR_21 0x00200000U
#define TSK_CAN0_RX_ERR_22 0x00400000U
#define TSK_CAN0_RX_ERR_23 0x00800000U
#define TSK_CAN0_RX_ERR_24 0x01000000U
#define TSK_CAN0_RX_ERR_25 0x02000000U
#define TSK_CAN0_RX_ERR_26 0x04000000U
#define TSK_CAN0_RX_ERR_27 0x08000000U
#define TSK_CAN0_RX_ERR_28 0x10000000U
#define TSK_CAN0_RX_ERR_29 0x20000000U
#define TSK_CAN0_RX_ERR_30 0x40000000U
#define TSK_CAN0_RX_ERR_31 0x80000000U

#define TSK_CAN1_RX_ERR_0  0x00000001U
#define TSK_CAN1_RX_ERR_1  0x00000002U
#define TSK_CAN1_RX_ERR_2  0x00000004U
#define TSK_CAN1_RX_ERR_3  0x00000008U
#define TSK_CAN1_RX_ERR_4  0x00000010U
#define TSK_CAN1_RX_ERR_5  0x00000020U
#define TSK_CAN1_RX_ERR_6  0x00000040U
#define TSK_CAN1_RX_ERR_7  0x00000080U
#define TSK_CAN1_RX_ERR_8  0x00000100U
#define TSK_CAN1_RX_ERR_9  0x00000200U
#define TSK_CAN1_RX_ERR_10 0x00000400U
#define TSK_CAN1_RX_ERR_11 0x00000800U
#define TSK_CAN1_RX_ERR_12 0x00001000U
#define TSK_CAN1_RX_ERR_13 0x00002000U
#define TSK_CAN1_RX_ERR_14 0x00004000U
#define TSK_CAN1_RX_ERR_15 0x00008000U
#define TSK_CAN1_RX_ERR_16 0x00010000U
#define TSK_CAN1_RX_ERR_17 0x00020000U
#define TSK_CAN1_RX_ERR_18 0x00040000U
#define TSK_CAN1_RX_ERR_19 0x00080000U
#define TSK_CAN1_RX_ERR_20 0x00100000U
#define TSK_CAN1_RX_ERR_21 0x00200000U
#define TSK_CAN1_RX_ERR_22 0x00400000U
#define TSK_CAN1_RX_ERR_23 0x00800000U
#define TSK_CAN1_RX_ERR_24 0x01000000U
#define TSK_CAN1_RX_ERR_25 0x02000000U
#define TSK_CAN1_RX_ERR_26 0x04000000U
#define TSK_CAN1_RX_ERR_27 0x08000000U
#define TSK_CAN1_RX_ERR_28 0x10000000U
#define TSK_CAN1_RX_ERR_29 0x20000000U
#define TSK_CAN1_RX_ERR_30 0x40000000U
#define TSK_CAN1_RX_ERR_31 0x80000000U

#define TSK_BASEBAAND_ERR_0  0x00000001U
#define TSK_BASEBAAND_ERR_1  0x00000002U
#define TSK_BASEBAAND_ERR_2  0x00000004U
#define TSK_BASEBAAND_ERR_3  0x00000008U
#define TSK_BASEBAAND_ERR_4  0x00000010U
#define TSK_BASEBAAND_ERR_5  0x00000020U
#define TSK_BASEBAAND_ERR_6  0x00000040U
#define TSK_BASEBAAND_ERR_7  0x00000080U
#define TSK_BASEBAAND_ERR_8  0x00000100U
#define TSK_BASEBAAND_ERR_9  0x00000200U
#define TSK_BASEBAAND_ERR_10 0x00000400U
#define TSK_BASEBAAND_ERR_11 0x00000800U
#define TSK_BASEBAAND_ERR_12 0x00001000U
#define TSK_BASEBAAND_ERR_13 0x00002000U
#define TSK_BASEBAAND_ERR_14 0x00004000U
#define TSK_BASEBAAND_ERR_15 0x00008000U
#define TSK_BASEBAAND_ERR_16 0x00010000U
#define TSK_BASEBAAND_ERR_17 0x00020000U
#define TSK_BASEBAAND_ERR_18 0x00040000U
#define TSK_BASEBAAND_ERR_19 0x00080000U
#define TSK_BASEBAAND_ERR_20 0x00100000U
#define TSK_BASEBAAND_ERR_21 0x00200000U
#define TSK_BASEBAAND_ERR_22 0x00400000U
#define TSK_BASEBAAND_ERR_23 0x00800000U
#define TSK_BASEBAAND_ERR_24 0x01000000U
#define TSK_BASEBAAND_ERR_25 0x02000000U
#define TSK_BASEBAAND_ERR_26 0x04000000U
#define TSK_BASEBAAND_ERR_27 0x08000000U
#define TSK_BASEBAAND_ERR_28 0x10000000U
#define TSK_BASEBAAND_ERR_29 0x20000000U
#define TSK_BASEBAAND_ERR_30 0x40000000U
#define TSK_BASEBAAND_ERR_31 0x80000000U

#define TSK_DATA_PRO_ERR_0  0x00000001U
#define TSK_DATA_PRO_ERR_1  0x00000002U
#define TSK_DATA_PRO_ERR_2  0x00000004U
#define TSK_DATA_PRO_ERR_3  0x00000008U
#define TSK_DATA_PRO_ERR_4  0x00000010U
#define TSK_DATA_PRO_ERR_5  0x00000020U
#define TSK_DATA_PRO_ERR_6  0x00000040U
#define TSK_DATA_PRO_ERR_7  0x00000080U
#define TSK_DATA_PRO_ERR_8  0x00000100U
#define TSK_DATA_PRO_ERR_9  0x00000200U
#define TSK_DATA_PRO_ERR_10 0x00000400U
#define TSK_DATA_PRO_ERR_11 0x00000800U
#define TSK_DATA_PRO_ERR_12 0x00001000U
#define TSK_DATA_PRO_ERR_13 0x00002000U
#define TSK_DATA_PRO_ERR_14 0x00004000U
#define TSK_DATA_PRO_ERR_15 0x00008000U
#define TSK_DATA_PRO_ERR_16 0x00010000U
#define TSK_DATA_PRO_ERR_17 0x00020000U
#define TSK_DATA_PRO_ERR_18 0x00040000U
#define TSK_DATA_PRO_ERR_19 0x00080000U
#define TSK_DATA_PRO_ERR_20 0x00100000U
#define TSK_DATA_PRO_ERR_21 0x00200000U
#define TSK_DATA_PRO_ERR_22 0x00400000U
#define TSK_DATA_PRO_ERR_23 0x00800000U
#define TSK_DATA_PRO_ERR_24 0x01000000U
#define TSK_DATA_PRO_ERR_25 0x02000000U
#define TSK_DATA_PRO_ERR_26 0x04000000U
#define TSK_DATA_PRO_ERR_27 0x08000000U
#define TSK_DATA_PRO_ERR_28 0x10000000U
#define TSK_DATA_PRO_ERR_29 0x20000000U
#define TSK_DATA_PRO_ERR_30 0x40000000U
#define TSK_DATA_PRO_ERR_31 0x80000000U

#define TSK_UDS_ERR_0  0x00000001U
#define TSK_UDS_ERR_1  0x00000002U
#define TSK_UDS_ERR_2  0x00000004U
#define TSK_UDS_ERR_3  0x00000008U
#define TSK_UDS_ERR_4  0x00000010U
#define TSK_UDS_ERR_5  0x00000020U
#define TSK_UDS_ERR_6  0x00000040U
#define TSK_UDS_ERR_7  0x00000080U
#define TSK_UDS_ERR_8  0x00000100U
#define TSK_UDS_ERR_9  0x00000200U
#define TSK_UDS_ERR_10 0x00000400U
#define TSK_UDS_ERR_11 0x00000800U
#define TSK_UDS_ERR_12 0x00001000U
#define TSK_UDS_ERR_13 0x00002000U
#define TSK_UDS_ERR_14 0x00004000U
#define TSK_UDS_ERR_15 0x00008000U
#define TSK_UDS_ERR_16 0x00010000U
#define TSK_UDS_ERR_17 0x00020000U
#define TSK_UDS_ERR_18 0x00040000U
#define TSK_UDS_ERR_19 0x00080000U
#define TSK_UDS_ERR_20 0x00100000U
#define TSK_UDS_ERR_21 0x00200000U
#define TSK_UDS_ERR_22 0x00400000U
#define TSK_UDS_ERR_23 0x00800000U
#define TSK_UDS_ERR_24 0x01000000U
#define TSK_UDS_ERR_25 0x02000000U
#define TSK_UDS_ERR_26 0x04000000U
#define TSK_UDS_ERR_27 0x08000000U
#define TSK_UDS_ERR_28 0x10000000U
#define TSK_UDS_ERR_29 0x20000000U
#define TSK_UDS_ERR_30 0x40000000U
#define TSK_UDS_ERR_31 0x80000000U

#define TSK_GPIO_ERR_ERR_0  0x00000001U
#define TSK_GPIO_ERR_ERR_1  0x00000002U
#define TSK_GPIO_ERR_ERR_2  0x00000004U
#define TSK_GPIO_ERR_ERR_3  0x00000008U
#define TSK_GPIO_ERR_ERR_4  0x00000010U
#define TSK_GPIO_ERR_ERR_5  0x00000020U
#define TSK_GPIO_ERR_ERR_6  0x00000040U
#define TSK_GPIO_ERR_ERR_7  0x00000080U
#define TSK_GPIO_ERR_ERR_8  0x00000100U
#define TSK_GPIO_ERR_ERR_9  0x00000200U
#define TSK_GPIO_ERR_ERR_10 0x00000400U
#define TSK_GPIO_ERR_ERR_11 0x00000800U
#define TSK_GPIO_ERR_ERR_12 0x00001000U
#define TSK_GPIO_ERR_ERR_13 0x00002000U
#define TSK_GPIO_ERR_ERR_14 0x00004000U
#define TSK_GPIO_ERR_ERR_15 0x00008000U
#define TSK_GPIO_ERR_ERR_16 0x00010000U
#define TSK_GPIO_ERR_ERR_17 0x00020000U
#define TSK_GPIO_ERR_ERR_18 0x00040000U
#define TSK_GPIO_ERR_ERR_19 0x00080000U
#define TSK_GPIO_ERR_ERR_20 0x00100000U
#define TSK_GPIO_ERR_ERR_21 0x00200000U
#define TSK_GPIO_ERR_ERR_22 0x00400000U
#define TSK_GPIO_ERR_ERR_23 0x00800000U
#define TSK_GPIO_ERR_ERR_24 0x01000000U
#define TSK_GPIO_ERR_ERR_25 0x02000000U
#define TSK_GPIO_ERR_ERR_26 0x04000000U
#define TSK_GPIO_ERR_ERR_27 0x08000000U
#define TSK_GPIO_ERR_ERR_28 0x10000000U
#define TSK_GPIO_ERR_ERR_29 0x20000000U
#define TSK_GPIO_ERR_ERR_30 0x40000000U
#define TSK_GPIO_ERR_ERR_31 0x80000000U

#define TSK_SYS_DET_ERR_0  0x00000001U
#define TSK_SYS_DET_ERR_1  0x00000002U
#define TSK_SYS_DET_ERR_2  0x00000004U
#define TSK_SYS_DET_ERR_3  0x00000008U
#define TSK_SYS_DET_ERR_4  0x00000010U
#define TSK_SYS_DET_ERR_5  0x00000020U
#define TSK_SYS_DET_ERR_6  0x00000040U
#define TSK_SYS_DET_ERR_7  0x00000080U
#define TSK_SYS_DET_ERR_8  0x00000100U
#define TSK_SYS_DET_ERR_9  0x00000200U
#define TSK_SYS_DET_ERR_10 0x00000400U
#define TSK_SYS_DET_ERR_11 0x00000800U
#define TSK_SYS_DET_ERR_12 0x00001000U
#define TSK_SYS_DET_ERR_13 0x00002000U
#define TSK_SYS_DET_ERR_14 0x00004000U
#define TSK_SYS_DET_ERR_15 0x00008000U
#define TSK_SYS_DET_ERR_16 0x00010000U
#define TSK_SYS_DET_ERR_17 0x00020000U
#define TSK_SYS_DET_ERR_18 0x00040000U
#define TSK_SYS_DET_ERR_19 0x00080000U
#define TSK_SYS_DET_ERR_20 0x00100000U
#define TSK_SYS_DET_ERR_21 0x00200000U
#define TSK_SYS_DET_ERR_22 0x00400000U
#define TSK_SYS_DET_ERR_23 0x00800000U
#define TSK_SYS_DET_ERR_24 0x01000000U
#define TSK_SYS_DET_ERR_25 0x02000000U
#define TSK_SYS_DET_ERR_26 0x04000000U
#define TSK_SYS_DET_ERR_27 0x08000000U
#define TSK_SYS_DET_ERR_28 0x10000000U
#define TSK_SYS_DET_ERR_29 0x20000000U
#define TSK_SYS_DET_ERR_30 0x40000000U
#define TSK_SYS_DET_ERR_31 0x80000000U

#ifdef PC_DBG_FW
#define EMBARC_PRINTF(...) printf(__VA_ARGS__);fflush(stdout);
#define EMBARC_PRINTF_FrameNumber(...) printf("#frame=%d#\n",frame_number);printf(__VA_ARGS__);fflush(stdout);
#else
#define EMBARC_PRINTF_FrameNumber(...)
#endif


#endif

