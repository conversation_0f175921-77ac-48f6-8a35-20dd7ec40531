/**
 * @file adas_function.c
 * @brief 
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2022-09-30
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-09-30 <td>1.0     <td>shaowei     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifdef ALPSPRO_ADAS
#include <string.h>
#include <math.h>
#include "cfg.h"
#include "rdp/track/data_process/rdp_interface.h"
#include "vdy/vdy_interface.h"
#include "adas/common/linear_regression.h"
#include "adas/customizedrequirements/adas_signal_integration.h"
#include "adas/customizedrequirements/adas_vehicle_ctrls.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#include "embARC_debug.h"
#include "rdp/track/alignment/aln_install_cfg.h"
#include "dbg/app_dbg.h"
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "rdp/track/data_process/rdp_matrixMath.h"
#elif defined (PC_DBG_FW)
#include <windows.h>
#include <string.h>
#include <math.h>
#include "other/temp.h"
#include "alg/track/rdp_interface.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "alg/track/rdp_matrixMath.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/customizedrequirements/adas_vehicle_ctrls.h"
#include "app/adas/customizedrequirements/adas_signal_integration.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/vehicle/vdy/vdy_interface.h"
#include "app/vehicle/vdy/vdy_types.h"
#else
#include <string.h>
#include <math.h>
#include <mmw/mss/mmw_cli.h>
#include "app/cfg/cfg.h"
#include "app/dbg/app_dbg.h"
#include "app/rdp/rdp_interface.h"
#include "app/rdp/rdp_clth_radar_lib.h"
#include "app/rdp/rdp_matrixMath.h"
#include "app/vdy/vdy_interface.h"
#include "app/aln/aln_install_cfg.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/customizedrequirements/adas_signal_integration.h"
#include "app/adas/customizedrequirements/adas_vehicle_ctrls.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "common/include/vehicle_cfg.h"
#endif

ADAS_FunctionState_t gadasFunctionState;
ADAS_FunctionState_t lastFunctionState;	//上一帧的报警数据
static VDY_vehicleFuncSwt_t gadasfunctionSwitch;    //ADAS冻结数据, 避免ADAS运行期间外部操作更改传入参数的值.
ADAS_RadarConfiguration_t RadarConfiguration;
ADAS_TriggerRecord_T gAdasTriggerRecord;

int gBSDVelSpeedDir = 0;    //0:forward 1:backward
uint8_t gAlarmObjIndex;
float yaw = 0.0f;
//int16_t yawTmpAry[3] = {0, 0, 0}; //平均滤波

static OBJ_NODE_STRUCT gobjPath[ADAS_TRK_OBJNUM];
TVehicleInfo gVehicleInfo;

static ALARM_OBJECT_T gobjAlm;
uint8_t g_MrrSystemState = 0;   //雷达系统状态,0-默认状态，1-系统故障，2-未标定 故障.

uint8_t gADASRadarId = 4;

float gadasInstallAzimuthAngle = 45.0f;
uint8_t gadasAlarmObjIndex;  //报警目标的index // 旧QT版本使用

float gAdasSpeedInkmph;   //ADAS模块所使用的车速，此处单位是km/h

static ADAS_TimeClase_t timeClass = {.adasTimeCnt = 0U, .trackTime = 0U};

#define CTAB_STAND_CROSS(angle, angleThr) (fabsf(angle - 90) < angleThr)      // fctb小角度横穿

#if (1 == VEHICLE_TURN_DETECTION_ENABLED)
static Centerx_t stCenterx; // 弯道计算相关结构体。

/**
 * @brief 
 * @return const Centerx_t 
 */
const Centerx_t *ADAS_getCenterx(void)
{
    return &stCenterx;
}
#endif

/**
 * @brief  获取ADAS功能状态结果
 * 
 * @return const ADAS_FunctionState_t* 
 */
const ADAS_FunctionState_t *ADAS_getAdasDataPointer(void)
{
    return &lastFunctionState;
}

/**
 * @brief 计算 ：本车以恒定加速度进行制动，目标以匀速进行运行，当本车速度为0时，目标与本车得最小距离（当前是y）
 * 
 * @param pobjPath 目标相关结构体地址
 * @param pVDY 车身数据 
 * @param a 车辆加速度
 * @return float 距离
 */
float ADAS_cacRelativedistanceafterbraking(const OBJ_NODE_STRUCT *pobjPath, const VDY_Info_t *pVDY, const float a)
{
    float timeVehicleBrk = fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps)  / a;   //减速至车速为0所需时间, t = v / a;

    float vehicledisplacement = (pVDY->pVDY_DynamicInfo->vdySpeedInmps * pVDY->pVDY_DynamicInfo->vdySpeedInmps) / (2.0f * a);    //减速至车速为0的位移, x = v^2 / (2 * a)

    float targetdisplacement = timeVehicleBrk * pobjPath->absolutevy;   //车辆减速期间，目标的位移

    float distance = pobjPath->y - vehicledisplacement - targetdisplacement; //根据目标和车辆在本车减速期间的位移变化，得出最后的距离（纵向）

    return distance;
}

/**
 * @brief 用于检查报警变量的某一位是否有效
 * 
 * @param type 报警类型
 * @param bit 报警枚举
 * @return true 有效
 * @return false 无效
 */
bool ADAS_checkBit(uint32_t type, uint32_t bit)
{
    if( (type & bit) == 0)
    {
        return false;
    }
    else
    {
        return true;
    }
}

/**
 * @brief 用于置位报警变量的某一位
 * 
 * @param type 报警类型
 * @param bit 报警枚举
 */
void ADAS_setBit(uint32_t *type, const uint32_t bit)
{
    *type |= bit;
}


/**
 * @brief 用于清除报警变量的某一位
 * 
 * @param type 报警类型
 * @param bit 报警枚举
 */
void ADAS_clearBit(uint32_t *type, const uint32_t bit)
{
    *type &= (~ bit);
}


/**
 * @brief 功能报警时的一些标准操作
 * 
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @param type 报警类型
 */
void ADAS_doWarning(const uint8_t i, OBJ_NODE_STRUCT *pobjPath, const uint32_t type)
{
    ADAS_setBit(&pobjPath[i].alarmType, type);
    ADAS_setBit(&pobjPath[i].lastAlarmType, type);

    switch (type)   //根据报警类型，置位相关变量
    {
    case ALARM_ACTIVE_FCTB:
        gadasFunctionState.adasFCTBWarning  = (uint8_t)ADAS_WARNING_LEVEL1;  //一级报警
        break;

    case ALARM_ACTIVE_BSD:
        gadasFunctionState.adasBSDWarning  = (uint8_t)ADAS_WARNING_LEVEL1;  //一级报警
        break;

    case ALARM_ACTIVE_LCA:
        gadasFunctionState.adasLCAWarning  = (uint8_t)ADAS_WARNING_LEVEL1;  //一级报警
        break;

    case ALARM_ACTIVE_RCTA:
        gadasFunctionState.adasRCTAWarning  = (uint8_t)ADAS_WARNING_LEVEL1;  //一级报警
        break;

    case ALARM_ACTIVE_DOW:
        gadasFunctionState.adasDOWWarning  = (uint8_t)ADAS_WARNING_LEVEL1;  //一级报警
        break;

    case ALARM_ACTIVE_RCW:
        gadasFunctionState.adasRCWWarning  = (uint8_t)ADAS_WARNING_LEVEL1;  //一级报警
        break;

    case ALARM_ACTIVE_FCTA:
        gadasFunctionState.adasFCTAWarning  = (uint8_t)ADAS_WARNING_LEVEL1;  //一级报警
        break;

    case ALARM_ACTIVE_RCTB:
        gadasFunctionState.adasRCTBWarning  = (uint8_t)ADAS_WARNING_LEVEL1;  //一级报警
        break;
    
    default:
        break;
    }
}

/**
 * @brief 功能无报警时的变量清除操作
 * 
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @param type 报警类型
 */
void ADAS_clearWarning(const uint8_t i, OBJ_NODE_STRUCT *pobjPath, const uint32_t type)
{
    ADAS_clearBit(&pobjPath[i].alarmType, type);
    ADAS_clearBit(&pobjPath[i].lastAlarmType, type);
}

/**
 * @brief 通过安装角和FOV大小，计算探测区边界方位，通过对比来确定目标是否在雷达盲区，适用于前后横穿功能
 *
 * @param i 跟踪点id
 * @param pobjPath 跟踪点
 * @return uint8_t
 */
uint8_t ADAS_checkBlindArea(uint8_t i, OBJ_NODE_STRUCT *pobjPath)
{
    uint8_t flag = 0;

    float blindAreaAngle = -75 + gadasInstallAzimuthAngle;  //车前方的盲区

    if(pobjPath[i].angle < blindAreaAngle)
    {
        flag = 1;
    }

    return flag;
}

/**
 * @brief 计算纵向的TTC.
 *
 * @param pobjPath 目标相关结构体地址
 * @return float ttcy -1.0表示目标远离
 */
inline float ADAS_cacTTCY(OBJ_NODE_STRUCT *pobjPath, uint8_t i)
{
    float ttcy = -1.0f;

    if( pobjPath[i].vy > FLOAT_EPS) //vy大于0，计算ttcy才有意义
    {
        if(pobjPath[i].y > FLOAT_EPS)
        {
            ttcy = pobjPath[i].y / pobjPath[i].vy;
        }
        else
        {
            ttcy = 0.1f;    //y小于等于0时，默认输出最小值0.1f
        }
    }

    return ttcy;
}

/**
 * @brief 计算纵向的TTC.
 *
 * @param pobjPath 目标相关结构体地址
 * @return float ttcy -1.0表示目标远离
 */
float ADAS_cacTTCY2(OBJ_NODE_STRUCT *pobjPath)
{
    float ttcy = -1.0f;

    if( pobjPath->vy > FLOAT_EPS) //vy大于0，计算ttcy才有意义
    {
        if(pobjPath->y > FLOAT_EPS)
        {
            ttcy = pobjPath->y / pobjPath->vy;
        }
        else
        {
            ttcy = 0.1f;    //y小于等于0时，默认输出最小值0.1f
        }
    }

    return ttcy;
}

/**
 * @brief 计算穿行场景的TTCX,按照X轴的碰撞来测试
 *
 * @param pobjPath 跟踪点
 * @return float ttcx结果
 */
float ADAS_cacCTABTTCX(const ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i)
{
    float ttc = 0.0f;
    float ttc1 = 0.0f;  //计算两个TTC  一个到车身边缘  一个到车辆中心
    float rangettc = 0.0f;

    // 特殊场景：目标越过0m，到自车中轴线距离，算X轴的TTC
    ttc1 = (pobjPath[i].x + (VEHICLE_WIDTH_INFO / 2)) / fabsf(pobjPath[i].vx);  
    ttc = pobjPath[i].x / fabsf(pobjPath[i].vx);      //使用车身边缘, 计算TTC
    // cncap24测试时发现假人横纵项速度分解可能会有问题, 补丁处理
    // 低速靠近目标
    if ((pobjPath[i].v < 2.0f) && (pobjPath[i].v > 1.0f) && (pobjPath[i].y >= 0.5) && (pobjPath[i].y <= 1.5))
    {
        if (pobjAlm->adasRegulationScene.CPTARF_Scene == 1)
        {
            rangettc = (pobjPath[i].range / 2) / fabsf(pobjPath[i].v);
        }
        else
        {
            rangettc = pobjPath[i].range / fabsf(pobjPath[i].v);
        } 
    }
    if (rangettc > FLOAT_EPS)
    {
        ttc = MIN(ttc, rangettc);
    }

    // 跨过车身后维持一小段时间TTC（从0到-1m这一段时间）
    if ((ttc < FLOAT_EPS) && (ttc1 > FLOAT_EPS) && (pobjPath[i].x < FLOAT_EPS))
    {
        ttc = ttc1;
    }

    return ttc;
}

/**
 * @brief 计算穿行场景的TTM,参照BYD功能规范，适用于FCTA/B和RCTA/B
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return float ttm
 */
float ADAS_cacTTM(const ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i)
{
    float ttm = 0.0f, ttm_v = 0.0f;
    float ttm_range = 0.0f;
    float tmp_x = 0.0f, tmp_y = 0.0f;

    // ttm方向，总的碰撞速度
    ttm_v = (pobjPath[i].vx * sinf(fabsf(pobjPath[i].angle)*degtorad)) + (pobjPath[i].vy * cosf(fabsf(pobjPath[i].angle)*degtorad));    

    if(ttm_v < 0) //在碰撞方向上是远离，则无碰撞风险
    {
        ttm = 10;   //输出一个较大的ttm，表示无效
    }
    else
    {
        tmp_x = pobjPath[i].x + (VEHICLE_WIDTH_INFO / 2);
        tmp_y = pobjPath[i].y;
        ttm_range = MagicSqrt(tmp_x * tmp_x + tmp_y * tmp_y);

        // 使ttm更容易满足
        if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)))
        {
            ttm_range /= 2;
        }

        ttm = ttm_range / ttm_v;
    }

    return ttm;
}

/**
 * @brief 计算穿行场景的TTC或TTM，适用于RCTAB
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return float ttm
 */
float ADAS_cacRCTABTTM(ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i)
{
    float ttm;
    float ttx = 0.0f;

    if(fabs(gAdasSpeedInkmph) >= 0.1)    //车速为标量，车静止时算TTCX，车运动时算TTM
    {
        // 斜穿时  快接近自车时, TTM可能形成远离 导致不满足  需要TTCX辅助判断 
        if ((pobjPath[i].x <= RCTB_CROSS_TTX_XDISTANCE_THR) && RCTAB_MIN_IA_CROSS(pobjPath[i].avgheadingAngle, 20))
        {
            ttx = ADAS_cacCTABTTCX(pobjAlm, pobjPath, i);
        }

        ttm = ADAS_cacTTM(pobjAlm, pobjPath, i);

        if (ttx > FLOAT_EPS)
        {
            ttm = MIN(ttx, ttm);
        }

        // 上面计算的可能不满足  需要考虑  目标在近处侧面时. TTX TTY 取一个较小的值.
        // X在一定范围内的横向目标计算TTCX,  
        if ((pobjPath[i].x <= RCTB_CROSS_TTX_XDISTANCE_THR) && (pobjPath[i].x >= 0.0f) && RCTAB_STAND_IA_CROSS(pobjPath[i].avgheadingAngle, 20)
                 && (pobjPath[i].IsRctabNearObj) && (pobjPath[i].vx > 1.0f) && (pobjPath[i].y < 2.0f))
        {
            ttx = ADAS_cacCTABTTCX(pobjAlm, pobjPath, i);
            if ((ttm > RCTB_TTC_MAX_TIME) && (ttx > FLOAT_EPS))
            {
                ttm = MIN(ttx, ttm);
            }
        }
    }
    else
    {
        ttm = ADAS_cacCTABTTCX(pobjAlm, pobjPath, i);
    }

    return ttm;
}

/**
 * @brief 目标识别，简单的识别几类目标，方便后续算法处理
 *
 * @param pobjPath 目标相关结构体地址
 */
void ADAS_identifyTarget(OBJ_NODE_STRUCT *pobjPath)
{
    if (pobjPath->boxWidth > ADAS_OVERLAP_WIDE_WIDTH)
    {
        pobjPath->targetclass = ADAS_OBJ_WIDE;
    }
    else if (pobjPath->boxWidth > ADAS_OBJ_MID_COMPACT_WIDTH || pobjPath->boxLength > ADAS_OBJ_MID_COMPACT_LENGTH)
    {
        pobjPath->targetclass = ADAS_OBJ_MID_COMPACT;
    }
    else  // if ADAS_OBJ_MID_SMALL_WIDTH // 0.4是RDP宽度默认值
    {
        pobjPath->targetclass = ADAS_OBJ_MID_SMALL;
    }
}

/**
 * @brief 计算重叠率
 * 		选取稳定追踪的区域，统计跟踪点，得出重叠率
 *
 * @param pobjPath 目标相关结构体地址
 */
void ADAS_cacOverlap(OBJ_NODE_STRUCT *pobjPath)
{
    float width = 0.0f;
    //float overlay_YMin = 0.0f;
    //float overlay_YMax = 0.0f;
    float theta = 0.0f;
    float k, b, x; // y = k*x + b;

    if (pobjPath->targetclass == ADAS_OBJ_WIDE) // 目标宽度估计
    {
        width = ADAS_OBJ_MID_COMPACT_WIDTH; //大目标宽度暂和中型目标一致
        //overlay_YMin = ADAS_OVERLAP_Y_WIDE_MIN;
        //overlay_YMax = ADAS_OVERLAP_Y_WIDE_MAX;
    }
    else if (pobjPath->targetclass == ADAS_OBJ_MID_COMPACT)
    {
        width = ADAS_OBJ_MID_COMPACT_WIDTH;
        //overlay_YMin = ADAS_OVERLAP_Y_COMPACT_MIN;
        //overlay_YMax = ADAS_OVERLAP_Y_COMPACT_MAX;
    }
    else if(pobjPath->targetclass == ADAS_OBJ_MID_SMALL)
    {
        width = ADAS_OBJ_MID_SMALL_WIDTH;
        //overlay_YMin = ADAS_OVERLAP_Y_SMALL_MIN;
        //overlay_YMax = ADAS_OVERLAP_Y_SMALL_MAX;
    }
    else
    {
        ;   //donothing
    }

    if (pobjPath->y > DOW_DOOR_AREA_Y_MIN) // 是否在车辆后保杠之后
    {
        if((fabsf(pobjPath->vx) > FLOAT_EPS))  //横向速度不为0
        {
            if(pobjPath->lockstatus < ADAS_OBJ_FULLY_LOCKED) //如果未能完全锁定，则算实时夹角
            {
                theta  = asinf((pobjPath->vx / (pobjPath->absolutevabs / 3.6f))) * radtodeg;  //适用于DOW和RCW
            }
            else
            {
                theta = pobjPath->headingAngleRCWDOW;
            }

            k = 1 / tanf(theta * degtorad);   // 先算k
            b = pobjPath->y - (k * ( pobjPath->x - ((width / 2) * cosf(theta * degtorad)) )); // b = y - k*x; 再算出b
            x = (DOW_DOOR_AREA_Y_MIN - b) / k;                                                // x = (y - b) / k; 最后算出 y = 0 时的x
        }
        else
        {
            x = pobjPath->x - (width / 2.0f);
        }
    }
    else
    {
        x = pobjPath->x;    //目标到本车侧面后，X已经是最近点，所以不需要补偿目标宽度
    }

    pobjPath->avgoverlapx = x;

    pobjPath->coverRate = pobjPath->avgoverlapx / ((-1 * VEHICLE_WIDTH_INFO) / 2);
}

/**
 * @brief 锁定目标的横向速度, 仅用于rcta横穿目标的横向速度判断
 *
 * @param pobjPath 目标相关结构体地址
 */
void ADAS_cacheCrossVx(OBJ_NODE_STRUCT *pobjPath)
{   
    // 确保Vx有被赋值, 在横向一定距离外锁定Vx
    if (fabsf(pobjPath->lockCrossVx) < FLOAT_EPS){
        pobjPath->lockCrossVx = pobjPath->vx;
    }
    if (pobjPath->x > RCTA_CROSS_LOCKVX_XDISTANCE){
        // 可能会由于速度突变导致锁定的单帧速度不合理. 
        // 取均值和瞬时值之间的较大的.
        pobjPath->lockCrossVx = pobjPath->vx;
        if (pobjPath->avgVx > pobjPath->lockCrossVx)
        {
            pobjPath->lockCrossVx = pobjPath->avgVx;
        }
    }
}

/**
 * @brief 判断目标是否是标准的正后方来车, 用于场景测试时DOW RCW
 *
 * @param pobjPath 目标相关结构体地址
 */
void ADAS_IsDirBehind(OBJ_NODE_STRUCT *pobjPath, uint8_t size)
{   
    // 思路: 历史的X vx 综合判定
    int i = 0;
    int num = 0; 
    float tty = ADAS_cacTTCY2(pobjPath); 

    // 统计x在正后方的次数
    if ((pobjPath->vy >= DOW_RCW_BEHIND_VY) && (pobjPath->vx <= DOW_RCW_BEHIND_VX) && 
        ((pobjPath->x <= DOW_RCW_BEHIND_X_MAX) && (pobjPath->x >= DOW_RCW_BEHIND_X_MIN)) && 
        (fabsf(pobjPath->headingAnglerealRCWDOW) < 13.0f))
    {
        for (i = 0U; i < size; i++){
            if ((pobjPath->stored_last_x[i] < 0)){
                num++;
            }
        }
    }else{
        if (pobjPath->DirBehindCnt > 5)
        {
            pobjPath->DirBehindCnt = 5;
        }
        if (pobjPath->DirBehindCnt > 0)
        {
            pobjPath->DirBehindCnt--;
        } 
    }
    // 对车辆出现在正后方的次数进行统计, 同时需要兼顾到目标偶发抖动.
    if (num >= (size - 5))
    {
        if ((((pobjPath->boxDot1X < -1.1f) && (pobjPath->boxDot1X > -2.9f)) ||
             ((pobjPath->boxDot2X < 0.5f) && (pobjPath->boxDot2X > -1.0f))) && 
            (pobjPath->DirBehindCnt < MAX_UCHAR)) 
        {
            pobjPath->DirBehindCnt++;
        }
    }else{
        pobjPath->DirBehindCnt = 0;
    }

    // 只针对正后方
    if ((gAdasSpeedInkmph < 0.4f) &&
        ((pobjPath->vy > 0) && (tty < 6.0f)))
    {
        // 可能RCW测切入这里进不了，在点不稳时正后方误报DOW
        // 目标是以一定角度过来时，响应变量会自增，目的是判断目标是否是0°直行靠近
        if ((pobjPath->analysisStats.notDirectlyBehindFlag == 0) && (pobjPath->x < 1.0f) && (pobjPath->x > -2.0f))
        {
            // 当前角度比0°大
            if (pobjPath->vx > 0.f)
            {
                if (pobjPath->analysisStats.angleIncreasingCnt < 255)
                {
                    pobjPath->analysisStats.angleIncreasingCnt++;
                }
            }
            // 当前角度比0°小
            else if (pobjPath->vx < 0.f)
            {
                if (pobjPath->analysisStats.angleDecreasingCnt < 255)
                {
                    pobjPath->analysisStats.angleDecreasingCnt++;
                }
            }
            else
            {
            }
            //// 只记录一次最早的x和y
            //if (((pobjPath->analysisStats.angleIncreasingCnt == 1) || (pobjPath->analysisStats.angleDecreasingCnt == 0)) ||
            //    ((pobjPath->analysisStats.angleIncreasingCnt == 0) || (pobjPath->analysisStats.angleDecreasingCnt == 1)))
            //{
            //    pobjPath->analysisStats.stored_earliest_x = pobjPath->x;
            //    pobjPath->analysisStats.stored_earliest_y = pobjPath->y;
            //}
        }
        else
        {
            pobjPath->analysisStats.notDirectlyBehindFlag = 1;
            pobjPath->analysisStats.angleIncreasingCnt = 0;
            pobjPath->analysisStats.angleDecreasingCnt = 0;
        }
    }
    else 
    {
        pobjPath->analysisStats.notDirectlyBehindFlag = 0;
        pobjPath->analysisStats.angleIncreasingCnt = 0; 
        pobjPath->analysisStats.angleDecreasingCnt = 0;
    }
}

/**
 * @brief 法规测试场景识别, 法规测试项目融入到量产版本
 * @param pobjPath 
 * @param size 
 */
void ADAS_cncapBSDcheck(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath)
{
    static int ic2cCnt = 0; // 车对车
    static int ic2bCnt = 0; // 车对单车

    if (((pobjAlm->BSDVelSpeedVal >= 45) && (pobjAlm->BSDVelSpeedVal <= 53)))       // 法规50速度
    {
        ic2cCnt++;
    }
    else
    {
        pobjPath->cncapBsdcarCheckIn = 0;
        ic2cCnt = 0;
    }
    if (((pobjAlm->BSDVelSpeedVal >= 20) && (pobjAlm->BSDVelSpeedVal <= 28)))   // 法规25速度
    {
        ic2bCnt++;
    }
    else
    {
        pobjPath->cncapBsdmotoCheckIn = 0;
        ic2bCnt = 0;
    }


    // 稳定速度维持一定时间
    if (ic2cCnt >= 35)      // 受帧周期影响
    {
        // 场景内寻找cncap24切入目标
        if ((pobjPath->y > BSD_LENGTH_FRONT) && (pobjPath->y < BSD_LENGTH))
        {
            pobjPath->cncapmaxX  = (pobjPath->x > pobjPath->cncapmaxX) ? pobjPath->x : pobjPath->cncapmaxX;
        }

        if ((pobjPath->y > BSD_LENGTH_FRONT) && (pobjPath->y < BSD_LENGTH) && (pobjPath->cncapmaxX > 5.0))
        {
            pobjPath->cncapBsdcarCheckIn = 1;
        }
    }

    if (ic2bCnt >= 35)  // 受帧周期影响
    {
        // 场景内寻找cncap24切入目标
        if ((pobjPath->y > BSD_LENGTH_FRONT) && (pobjPath->y < BSD_LENGTH))
        {
            pobjPath->cncapmaxX  = (pobjPath->x > pobjPath->cncapmaxX) ? pobjPath->x : pobjPath->cncapmaxX;
        }

        if ((pobjPath->y > BSD_LENGTH_FRONT) && (pobjPath->y < BSD_LENGTH) && (pobjPath->cncapmaxX > 5.0))
        {
            pobjPath->cncapBsdmotoCheckIn = 1;
        }        
    }
    // cncap目标判断X值
    if ((1 == pobjPath->cncapBsdmotoCheckIn) || (1 == pobjPath->cncapBsdcarCheckIn))
    {
        if (pobjPath->x > pobjPath->maxbsdx)
        {
            pobjPath->maxbsdx = pobjPath->x;
        }
        if (pobjPath->x < pobjPath->minbsdx)
        {
            pobjPath->minbsdx = pobjPath->x;
        }
    }else{
        pobjPath->maxbsdx = BSD_CHECKINOUT_MAX_X_DEFAULT;
        pobjPath->minbsdx = BSD_CHECKINOUT_MIN_X_DEFAULT;
    }
}

static void linearFitMatrix(const float x[], const float y[], int n, float *k, float *b) {
    float A[4] = {0};  // 2x2 矩阵 A: A[0][0], A[0][1], A[1][0], A[1][1]
    float B[2] = {0};  // 2x1 向量 B: B[0], B[1]
    float c[2];        // 结果向量 c: [k, b]

    // 构建 A^T * A 和 A^T * b
    for (int i = 0; i < n; i++) {
        A[0] += x[i] * x[i];  // A[0][0] = x^2
        A[1] += x[i];          // A[0][1] = A[1][0] = x
        A[2] += x[i];          // A[1][0] = x
        A[3] += 1;             // A[1][1] = n
        B[0] += x[i] * y[i];   // B[0] = x * y
        B[1] += y[i];          // B[1] = y
    }

    // 求 A^T * A 的逆矩阵
    float invA[4];
    // if (!matrixInv2(A, invA)) {
    //     //printf("矩阵不可逆，无法求解。\n");
    //     return;
    // }
    matrixInv2(A, invA);

    // 计算 (A^T * A)^(-1) * (A^T * b)
    matrixMultiply(2, 2, 1, invA, B, c);

    *k = c[0];
    *b = c[1];
}

bool trackhistoryheading(OBJ_NODE_STRUCT *pobjPath, float *k, float *b, int size)
{
    // float mse_1 = 0.0f;

    if ((fabsf(pobjPath->stored_last_x[size - 1]) > FLOAT_EPS) && (fabsf(pobjPath->stored_last_y[size - 1]) > FLOAT_EPS))
    {
        linearFitMatrix(pobjPath->stored_last_x, pobjPath->stored_last_y, size, k, b);
        // mse_1 = ADAS_CalculateTotalMSE_NonI(pobjPath, ADAS_HISTORY_NUM, *k, *b, false); // 尝试分析均方差 真实目标的方差应该处于一个比较低的值 比如 0.5以下
        return true;
    }
    return false;
}

// 自车运动时刻的目标相对航向
bool trackmovehistoryheading(OBJ_NODE_STRUCT *pobjPath, float *k, float *b, int size, float *mes)
{

    if ((fabsf(pobjPath->stored_last_x[size - 1]) > FLOAT_EPS) && (fabsf(pobjPath->stored_last_groundy[size - 1]) > FLOAT_EPS))
    {
        linearFitMatrix(pobjPath->stored_last_x, pobjPath->stored_last_groundy, size, k, b);
        *mes = ADAS_CalculateTotalMSE_NonI(pobjPath, ADAS_HISTORY_NUM, *k, *b, true); // 尝试分析均方差 真实目标的方差应该处于一个比较低的值 比如 0.5以下
        return true;
    }
    return false;
}

/**
 * @brief 航向角二次校验。 CNCAP假人场景专属补丁
 * @param pobjAlm 
 * @param pobjPath 
 */
void ADAS_cncapRCTAheadingcheck(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, const VDY_Info_t *pVDY)
{
    int iCnt = 0;
    int i = 0;
    float k = 0.0f, b = 0.0f, theta = 0.0f, temp_heading = 0.0f;
    if (1 == pobjAlm->BSDVelSpeedDir)
    {
        if (pobjPath->stored_last_y[ADAS_HISTORY_NUM - 1] > FLOAT_EPS)    // 避免滤除
        {
            if ((pobjPath->v >= 1) && (pobjPath->v <= 2))
            {
                for (i = 0; i < ADAS_HISTORY_NUM; i++)
                {
                    if ((pobjPath->stored_last_y[i] >= 0.5) && (pobjPath->stored_last_y[i] <= 1.5))
                    {
                        iCnt++;
                    }
                }
            }
        }
    }

    // 只对RCTA假人场景生效
    if ((pobjPath->stored_last_y[ADAS_HISTORY_NUM - 1] > FLOAT_EPS) && (pobjPath->stored_last_x[ADAS_HISTORY_NUM - 1] > FLOAT_EPS) && (pobjPath->maxX >= 5.0))
    {
        if ((iCnt >= ADAS_HISTORY_NUM) && ((pobjPath->stored_last_x[ADAS_HISTORY_NUM-1] - pobjPath->stored_last_x[0]) > 1.0f) && 
            (((pobjPath->stored_last_y[ADAS_HISTORY_NUM-1]) - pobjPath->stored_last_y[0]) < 0.5))
        {
            pobjPath->headingAngleCTAB = 90;
        }
    }


    // R挡时， 对横向速度大于1m/s的目标做一个线性拟合 确认航向角 仅在自车静止时使用拟合角度
    if ((!CTAB_STAND_CROSS(pobjPath->headingAngleCTAB, 8)) &&(1 == pobjAlm->BSDVelSpeedDir) && (pobjPath->vx > 1.0f) && (pobjPath->lifeCycle >= ADAS_HISTORY_NUM) &&
        (fabsf(pobjAlm->BSDVelSpeedVal) <= FLOAT_EPS))
    {
        if (true == trackhistoryheading(pobjPath, &k, &b, ADAS_HISTORY_NUM))
        {
            theta = atanf(k);
            // 横向0度 小角度负值  大角度正值
            temp_heading = (90 + theta * (180.0 / M_PI));
            pobjPath->headingAngleCTAB = temp_heading;
            pobjPath->avgheadingAngle = temp_heading;       // rctab里的航向角使用了平均航向角. 如果成功拟合出角度 就优先使用拟合出的航向角.
        }
    }

    // 自车运动时刻, 拟合一个航向角  15秒内也先加上
    // 
    if ((pobjAlm->runingtime <= FCTB_CHECK_L_STRICT_RUNNING_TIME) && (0 == pobjAlm->BSDVelSpeedDir) && (pobjPath->vx > 0.5f) && (pobjPath->lifeCycle >= ADAS_HISTORY_NUM) &&
        (pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) && (fabsf(pobjAlm->BSDVelSpeedVal) > FLOAT_EPS) && (fabsf(pobjAlm->BSDVelSpeedVal) < FCTB_ACTIVE_MAX_SPEED))
    {
        float mes = 0.0f;
        // 位置变化能拟合出航向角的话.  使用位置变化拟合出的航向角
        if (true == trackmovehistoryheading(pobjPath, &k, &b, ADAS_HISTORY_NUM, &mes))
        {
            theta = atanf(k);
            temp_heading = (90 + theta * (180.0 / M_PI));
            // 最主要的目的是确保 小角度不误报.  是否合成的是小角度时, 但是分解的是大角度时, 采用合成的, 宁漏不误.
            if (temp_heading < (FCTB_ACTIVE_MIN_IA + FCTB_ACTIVE_MIN_IA_BUF))
            {
                pobjPath->headingAngleCTAB = temp_heading;
                // avgheadingAngle 正常是平均航向角,  但是这个角度在近车身时经常有问题.  自车启动15秒内, 仿真看通过位置分解的更准确一些.
                pobjPath->avgheadingAngle = temp_heading;     
            }
        }
    }

    if ((1 == pobjAlm->BSDVelSpeedDir) && (pobjPath->vx > 0.5f) && (pobjPath->lifeCycle >= ADAS_HISTORY_NUM) &&
        (pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) && (fabsf(pobjAlm->BSDVelSpeedVal) > FLOAT_EPS) && (fabsf(pobjAlm->BSDVelSpeedVal) < RCTA_ACTIVE_MAX_SPEED)
        && (fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) < 60.0f))
    {
        float mes = 0.0f;
        // 位置变化能拟合出航向角的话.  使用位置变化拟合出的航向角
        if (true == trackmovehistoryheading(pobjPath, &k, &b, ADAS_HISTORY_NUM, &mes))
        {
            theta = atanf(k);
            temp_heading = (90 + theta * (180.0 / M_PI));
            // 近处跟踪点的抖动. 可能会导致这里出问题,  暂时还是使用分解出来的航向角. 如果拟合出来的航向角明显偏小. 使用拟合出来的.
            if (temp_heading < RCTA_ACTIVE_MIN_IA)
            {
                pobjPath->headingAngleCTAB = temp_heading;
                pobjPath->avgheadingAngle = temp_heading;     
            }

        //     这里后续再细调.
        //     pobjPath->headingAngleCTAB = temp_heading;
        //     pobjPath->avgheadingAngle = temp_heading;

        //     EMBARC_PRINTF("MSE %f\n", mes);
        //     if (mes > 0.3)      // 验收时数据仿真  最大的MSE不到0.3.
        //     {
        //         if (pobjPath->avgheadingAngle > 90)
        //         {
        //             pobjPath->avgheadingAngle = RCTA_ACTIVE_MAX_IA + RCTA_ACTIVE_MAX_IA_BUF;
        //         }
        //         else
        //         {
        //             pobjPath->avgheadingAngle = RCTA_ACTIVE_MIN_IA + RCTA_ACTIVE_MIN_IA_BUF;
        //         }
        //     }
         }
    }
}


// 记录抵消自车运动后的Y值
void ADAS_calcgroundY(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath)
{
    int8_t k = 0, j = 0;
    // uint8_t is_smallangle = 0;                  // 是否是小角度
    //float movedistance = 0.0f;
    // float sumY = 0.0f /*, vehiclemove = 0.0f*/;
    float cumulatedMove[ADAS_HISTORY_NUM];      // cache位移 节省算力

    // 计算目标单位时间内运动的Y位移  和 自车一定时间内运动的Y位移. 
    // 目标Y比自车大时,认为是大角度  目标的Y比自车的小时. 认为是小角度
    
    // 节省算力. 只计算自车速度20以内时, 有一定速度的运动目标
    // if ((fabsf(pobjAlm->BSDVelSpeedVal) > FLOAT_EPS) && (fabsf(pobjAlm->BSDVelSpeedVal) < 20.0f) && (pobjPath->v > 1.0f) &&  (pobjPath->attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP))
    // {
    //     // 计算20帧 Y 的总和  一定要超过20帧  才能够计算
    //     if ((fabsf(pobjPath->stored_last_y[ADAS_HISTORY_NUM - 1]) > FLOAT_EPS))
    //     {
    //         sumY = pobjPath->stored_last_y[ADAS_HISTORY_NUM - 1] - pobjPath->stored_last_y[0];
    //         if (pobjAlm->sumvehiclemove > sumY)
    //         {
    //             is_smallangle = 1;
    //         }
    //     }
    // }
    // is_smallangle = 1;

    // 大角度和小角度的计算方式是一样的?
    // 目前先做小角度使用位置变化.  大角度的先不做.  避免有未考虑到的问题.
    //if ((fabsf(pobjAlm->BSDVelSpeedVal) < 20) && (1 == is_smallangle))
    if ((fabsf(pobjAlm->BSDVelSpeedVal) > FLOAT_EPS) && (fabsf(pobjAlm->BSDVelSpeedVal) < 20.0f) && 
        (pobjPath->vx > 1.0f) &&  (pobjPath->attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP))
    {
        // 计算从当前帧到最后一帧的累计自车运动距离
        // 假设数组下标 0 是最新帧，ADAS_HISTORY_NUM-1 是最老帧
        cumulatedMove[ADAS_HISTORY_NUM - 1] = pobjAlm->vehiclemove[ADAS_HISTORY_NUM - 1];
        for (j = ADAS_HISTORY_NUM - 2; j >= 0; j--) {
            cumulatedMove[j] = cumulatedMove[j + 1] + pobjAlm->vehiclemove[j];
        }

        for (k = 0; k < ADAS_HISTORY_NUM; k++)
        {
            pobjPath->stored_last_groundy[k] = pobjPath->stored_last_y[k] + cumulatedMove[k];
            //movedistance = 0.0f;
        }
    }
    else        // 对于不符合小角度和自车速度段的目标,   设置 stored_last_groundy 为 0   避免后续通过位移差计算了航向角.
    {
        memset (pobjPath->stored_last_groundy, 0x00, sizeof (pobjPath->stored_last_groundy));
    }
}


/**
 * @brief FCTB跟随目标检测, 抑制跟随目标的制动
 * @param pobjAlm 
 * @param pobjPath 
 */
void ADAS_FCTB_followcheck(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath)
{
    if (pobjPath->fctbTrigCountdown > 0)
    {
        pobjPath->fctbTrigCountdown--;
    }

    // 与自车保持一定时间跟随的目标, 也认为是会车
    if ((0 == pobjAlm->BSDVelSpeedDir) && (pobjAlm->runingtime >= 0.1) && (pobjPath->range <= 6) && (pobjPath->x <= 3) && ((pobjPath->attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U))
    {
        if (pobjPath->fctb_follow_cnt < MAX_UINT16)
        {
            pobjPath->fctb_follow_cnt++;
        }
    }
    else
    {
        pobjPath->fctb_follow_cnt = 0;
    }

    if ((0 == pobjAlm->BSDVelSpeedDir) && (pobjAlm->runingtime >= 0.1) && ((pobjPath->attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U))
    {
        if (pobjPath->fctb_objrunning_cnt < MAX_UINT16)
        {
            pobjPath->fctb_objrunning_cnt++;
        }
    }
    else
    {
        pobjPath->fctb_objrunning_cnt = 0;
    }

    // 均值航向也先在这里计算
    if ((0 == pobjAlm->BSDVelSpeedDir) && (pobjAlm->runingtime >= 0.1) && ((pobjPath->attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U))
    {
        pobjPath->sumhistoryheadingAngle += pobjPath->headingAngleCTAB;
        pobjPath->avgheadingCnt++;
        pobjPath->avghistoryheadingAngle = pobjPath->sumhistoryheadingAngle / pobjPath->avgheadingCnt;
    }
    else
    {
        pobjPath->sumhistoryheadingAngle = 0;
        pobjPath->avgheadingCnt = 0;
    }

    // 均值横向速度 
    if ((0 == pobjAlm->BSDVelSpeedDir) && (pobjAlm->runingtime >= 0.1) && ((pobjPath->attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U))
    {
        pobjPath->sumhistoryVx += pobjPath->absolutevx;
        pobjPath->avgVxCnt++;
        pobjPath->avghistoryVx = pobjPath->sumhistoryVx / pobjPath->avgVxCnt;
    }
    else
    {
        pobjPath->sumhistoryVx = 0;
        pobjPath->avgVxCnt = 0;
    }
}

/**
 * @brief 
 * 
 * @param arry 角度数组
 * @param size 样本数量
 * @return float 均值结果
 */
float ADAS_cacAvg(float *arry, uint8_t size)
{
    uint8_t i = 0;
    float sum = 0.0f, avg = 0.0f;

    // 存在一个问题是目标的数据还不满足 size 大小. 此情况下 是否不平均?
    // 但是有些场景确实数据是0

    for (i = 0U; i < size; i++){
        sum += arry[i];
    }

    avg = (sum / size);

    return avg;
}

/**
 * @brief 
 * 
 * @param arry 角度数组
 * @param size 样本数量
 * @return float 均值结果
 */
static float ADAS_cacAvgL(float *arry, uint8_t size)
{
    uint8_t i = 0;
    float little = MAX_FLOAT;

    for (i = 0U; i < size; i++){
        if (little > arry[i]){
            little = arry[i];
        }
    }
  
    return little;
}


// // 矩阵乘法函数: C = A * B
// static void matMul(float A[2][2], float B[2][1], float C[2][1]) {
//     for (int i = 0; i < 2; i++) {
//         C[i][0] = 0;
//         for (int j = 0; j < 2; j++) {
//             C[i][0] += A[i][j] * B[j][0];
//         }
//     }
// }

// // 矩阵求逆函数: 求 A 的逆矩阵
// static int matInverse(float A[2][2], float invA[2][2]) {
//     float det = A[0][0] * A[1][1] - A[0][1] * A[1][0];
//     if (det == 0) {
//         return 0; // 不可逆
//     }
//     invA[0][0] = A[1][1] / det;
//     invA[0][1] = -A[0][1] / det;
//     invA[1][0] = -A[1][0] / det;
//     invA[1][1] = A[0][0] / det;
//     return 1;
// }




void ADAS_lockheadingAngleRCWDOW(OBJ_NODE_STRUCT *pobjPath, float angle, bool lock)
{
    angle = ((fabsf(angle) < FLOAT_EPS) ? DOW_MIN_IA_HEADINGANGLE : angle);

    if(lock)
    {
        if(fabsf(angle) < DOW_ACTIVE_MAX_IA)    //当前目标夹角较小时，强制锁定为DOW_ACTIVE_MAX_IA，这样既保证目标不会报RCW，又不会影响DOW
        {
            pobjPath->headingAngleRCWDOW = DOW_ACTIVE_MAX_IA;
        }
        else
        {
            pobjPath->headingAngleRCWDOW = angle;
        }
        pobjPath->headingAnglerealRCWDOW = angle;       //实时角度 实时更新
    }
    else
    {
        float lock_ttc = 0.0f;
        // 根据速度距离比重决定是否更新
        if ((pobjPath->y < 30.0f) && (pobjPath->vy > 3.0f)){
            lock_ttc = pobjPath->y / pobjPath->vy;
            if (lock_ttc < (DOW_TTC_MAX * DOW_SLOPE_TTC_RATE)){
                pobjPath->headingAnglerealRCWDOW = angle;       //实时角度 实时更新
                // 这里的逻辑需要捋一下, 目标变动航向角, 改如何处理， 这里是为了处理目标一开始航向角估计不准,偏差较大， 给纠正回来
                if(fabsf(angle) < DOW_ACTIVE_MAX_IA)    //当前目标夹角较小时，强制锁定为DOW_ACTIVE_MAX_IA，这样既保证目标不会报RCW，又不会影响DOW
                {
                    pobjPath->headingAngleRCWDOW = DOW_ACTIVE_MAX_IA;
                }
                else
                {
                    pobjPath->headingAngleRCWDOW = angle;
                }
            }
        }
        pobjPath->headingAnglerealRCWDOW = angle;       // 整体的锁航向角操作看起来已经没有实际意义了.  这里实时更新角度. 后续锁航向角操作需要再捋一下
    }
}

void ADAS_cacheadingAngleRCWDOW(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath)
{
    float temp_headingAngleRCWDOW, avg_headingAngleRCWDOW;


    float ttcy, k = 0.0f, b = 0.0f, theta = 0.0f;
    ttcy = ADAS_cacTTCY2(pobjPath);

    if(fabsf(pobjPath->absolutevx) > FLOAT_EPS)
    {
        temp_headingAngleRCWDOW = asinf((pobjPath->vx / (pobjPath->absolutevabs / 3.6f))) * radtodeg;  //适用于DOW和RCW
    }
    else
    {
        temp_headingAngleRCWDOW = 0.0f;
    }

    // 航向角滑窗记录
    (void)memmove(&pobjPath->stored_last_headingAngleRCWDOW[1], &pobjPath->stored_last_headingAngleRCWDOW[0], sizeof(float) * (ADAS_HISTORY_HEADINGANGLE_NUM - 1));
    pobjPath->stored_last_headingAngleRCWDOW[0] = temp_headingAngleRCWDOW;  //记录瞬时航向角

    if(pobjPath->lifeCycle >= ADAS_HISTORY_HEADINGANGLE_NUM)
    {
        // 这里的航向计算有个不太友好的是: rdp航向角波动比较大时,导致最终是直行还是斜穿的判定会有一些问题.尤其是小角度车头朝外的斜停场景.
        // 小角度车头朝外斜停尤其需要横向扩充一些buffer 如果此时还是标准横向距离, 极有可能导致横向小角度漏报
        avg_headingAngleRCWDOW = ADAS_cacAvg(pobjPath->stored_last_headingAngleRCWDOW, ADAS_HISTORY_HEADINGANGLE_NUM);    //计算当前的平均航向角
        // 运动目标计算拟合航向角度
        // 如果曲线拟合也计算出了航向角  使用曲线拟合的航向角  只计算来向目标 目前仅供DOW使用
        // 如果是直行场景， 使用拟合反而不太好 跟踪点的微小波动导致的角度  会导致碰撞预测不满足
        // 对于后方而言 车尾朝外时容易报警, 车头朝外时不易报警 对于大角度的目标,更加容易进入曲线拟合
        // 车头朝外斜停时, 斜传目标可能横向速度不准确导致不进入拟合  产生DOW早退中断等情况
        //if ((fabsf(avg_headingAngleRCWDOW) > DOW_LINEAR_MIN_ANGLE) && (pobjPath->lifeCycle >= ADAS_HISTORY_NUM) && (pobjPath->vy > 1.0f) && trackhistoryheading(pobjPath, &k, &b, ADAS_HISTORY_NUM))
        if (((avg_headingAngleRCWDOW > (DOW_LINEAR_MIN_ANGLE / 2)) || (avg_headingAngleRCWDOW < (-DOW_LINEAR_MIN_ANGLE)) || (pobjPath->x > 4.0f)) && (pobjPath->lifeCycle >= ADAS_HISTORY_NUM) && (pobjPath->vy > 1.0f) && trackhistoryheading(pobjPath, &k, &b, ADAS_HISTORY_NUM))
        {
            theta = atanf(k);
            if (theta < FLOAT_EPS)
            {
                avg_headingAngleRCWDOW = -(90 + theta * (180.0 / M_PI));
            }
            else
            {
                avg_headingAngleRCWDOW = (90 - theta * (180.0 / M_PI));
            }
            pobjPath->analysisStats.islinenearheadingresult = 1;
        }
    }
    else
    {
        avg_headingAngleRCWDOW = temp_headingAngleRCWDOW;

        ADAS_lockheadingAngleRCWDOW(pobjPath, avg_headingAngleRCWDOW, true);  //生命周期较短时，锁航向角
    }

    if(pobjPath->maxrange > RCW_LOCK_IA_MAX_RANGE)    //到过远处的目标
    {
        if(pobjPath->lifeCycle == 1)    //刚出现的目标点
        {
            pobjPath->startTTCY = ADAS_cacTTCY2(pobjPath);

            pobjPath->lockx = pobjPath->x;
            pobjPath->locky = pobjPath->y;

            if( (pobjPath->startTTCY) > 0 && (pobjPath->startTTCY < pobjPath->lock_ttcy_thresold1) )   //起始点ttcy小于阈值，则将此ttcy作为第一个点的阈值，第二个点阈值往后推0.5s
            {
                pobjPath->lock_ttcy_thresold1 = pobjPath->startTTCY;
                pobjPath->lock_ttcy_thresold2 = pobjPath->startTTCY - 0.5f;
            }
        }

        if(ttcy >= pobjPath->lock_ttcy_thresold1)
        {
            pobjPath->lockx = pobjPath->x;
            pobjPath->locky = pobjPath->y;
            pobjPath->lockstatus = ADAS_OBJ_HALF_LOCKED;     //锁定第一个点
            pobjPath->locktimedifference = 0.0f;    //累计时间差清零
        }
        else if(ttcy >= pobjPath->lock_ttcy_thresold2 && pobjPath->lockstatus >= ADAS_OBJ_HALF_LOCKED) //已锁定第一个点
        {
            if(fabsf(pobjPath->locktimedifference) > FLOAT_EPS)    //目标尺寸大于人且时间间隔非0
            {
                float /*dx, dy, vx, vy,  vabs, */ k, b, theta/*, headingAngle = 0.0f*/;

                pobjPath->lockstatus = ADAS_OBJ_FULLY_LOCKED;   //锁定第二个点

                //dx = pobjPath->lockx - pobjPath->x;  //计算dx = -1.0f * (pobjPath->x - pobjPath->lockx);
                //dy = pobjPath->locky - pobjPath->y;
                //vx = dx / pobjPath->locktimedifference;
                //vy = dy / pobjPath->locktimedifference;

                //vabs = sqrtf((vx * vx) + (vy * vy)) * 3.6f; //目标绝对速度的大小, 单位转成km/h
                // 跟踪点的抖动极易使这里的计算误差较大
                //temp_headingAngleRCWDOW = asinf((vx / (vabs / 3.6f))) * radtodeg;  //计算根据锁定两个坐标的航向角

                // 如果曲线拟合也计算出了航向角  使用曲线拟合的航向角
                if ((fabsf(avg_headingAngleRCWDOW) > DOW_LINEAR_MIN_ANGLE) && trackhistoryheading(pobjPath, &k, &b, ADAS_HISTORY_NUM))
                {
                    theta = atanf(k);
                    if (theta < FLOAT_EPS)
                    {
                        temp_headingAngleRCWDOW = -(90 + theta * (180.0 / M_PI));
                    }
                    else
                    {
                        temp_headingAngleRCWDOW = (90 - theta * (180.0 / M_PI));
                    }
                    pobjPath->analysisStats.islinenearheadingresult = 1;
                }

                // 一定角度以内的航向角  认为目标属于直行场景 相当于对测量测试误差做冗余
                temp_headingAngleRCWDOW = ((fabsf(temp_headingAngleRCWDOW) < DOW_MIN_IA_INGOREHEADINGANGLE) ? DOW_MIN_IA_HEADINGANGLE : temp_headingAngleRCWDOW);

                pobjPath->headingAngleRCWDOW = temp_headingAngleRCWDOW;
                pobjPath->headingAnglerealRCWDOW = temp_headingAngleRCWDOW;
            }
        }
        else
        {
            ;   //donothing
        }
    }

    if(pobjPath->lockstatus < ADAS_OBJ_FULLY_LOCKED)    //未能成功锁定两个点的目标
    {
        ADAS_lockheadingAngleRCWDOW(pobjPath, avg_headingAngleRCWDOW, true);  //锁航向角
    }
    else
    {
        // 虽然锁定了 但是目标的轨迹也有可能快到车前时改变 已经锁定的目标  航向角变化率过大 解除锁定
        // 为了避免45度斜停误报等场景,  航向角在一定Y轴距离外解锁。
        if ((pobjPath->y <= DOW_TARGET_CLOSING_Y) && (fabsf(fabsf(pobjPath->headingAngleRCWDOW) - fabsf(avg_headingAngleRCWDOW)) >= DOW_HEADINGANGLE_DIFF))
        {
            ADAS_lockheadingAngleRCWDOW(pobjPath, avg_headingAngleRCWDOW, true);  //锁航向角
        }
        else
        {
            ADAS_lockheadingAngleRCWDOW(pobjPath, avg_headingAngleRCWDOW, false);
        }
    }

    // 检测到自车运动时, 清除场景识别的相关信息
    if (fabsf(pobjAlm->BSDVelSpeedVal) > 0.1f)
    {
        DOW_clearSence();
    }
}

/**
 * @brief 策略: 假点判断
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param pfreezedVehDyncData
 * @return true 假点
 * @return false 非假点
 */
static void ADAS_isFakeJudgment(const VDY_DynamicEstimate_t *pfreezedVehDyncData, OBJ_NODE_STRUCT *pobjPath)
{
    float difValue = 0;
    float tmpX = 0;
    float tmpY = 0;
    pobjPath->objFakeDOt = false; // 默认为非假点

    /**
     * @brief 一、起批位置和当前xy的欧氏距离小于阈值或者当前xy的位置和最大xy的欧氏距离不满足3m且小于10帧为假点；
     *      非超车且（当前 Y > 3或起航和报警帧的绝对位移大于 3 m） 
     */
    // 起批位置和当前xy的欧氏距离
    tmpX = (pobjPath->x - pobjPath->startX); // 生成目标的起点和当前目标的xy的差值
    tmpY = (pobjPath->y - pobjPath->startY);
    difValue = sqrtf((tmpX * tmpX) + (tmpY * tmpY)); // 计算欧氏距离
    // 记录离起始点的当前位移
    pobjPath->rangestartdiffvalue = difValue;
    // 记录离起始点的最远位移
    if(pobjPath->rangemaxdiffvalue < difValue)
    {
        pobjPath->rangemaxdiffvalue = difValue; 
    }
    
#if (BUMPER_FALSE_POINT_STRATEGY_ENABLED == 1)    //保杠假点过滤，只作用于RCW和BSD功能
    float BSDVelSpeedVal = 3.6 * pfreezedVehDyncData->vdySpeedInmps; // km/h
    float difValueThd = 3.0f;
    /**
     * @brief 大于3m且记数达到10帧及以上
     *      当前位移大于3m且记数达到10帧及以上，认为是真点，否则认为是假点，这是个累加，不是实时
     */
    if (pobjPath->rangestartdiffvalue >= ADAS_POINT_MAX_MOVEMENT_DIFFERENCE) // 
    {
        if (pobjPath->objFakeDOt_TimestampCnt < ADAS_POINT_MAX_MOVEMENT_DIF_FRAME_CNT)
        {
            pobjPath->objFakeDOt_TimestampCnt++; // 这里是在本次ID生命周期内都有效，如果清除：1切id、2下电； 
        }
        else
        {
            pobjPath->isTruePoint = 1;  // 1，非假点，0 假点
        }
    }
    else
    {
        pobjPath->objFakeDOt_TimestampCnt = 0;
    }

    // 根据不同速度来定假点位移阈值 
    if (BSDVelSpeedVal <= BSD_SCENE_MIN_SPD) 
    {
        difValueThd = BSD_FAKEDOT_MIN_DIF;
    }
    else if (BSDVelSpeedVal <= BSD_SCENE_MID_SPD) // 要看下高峰期这个的影响
    {
        difValueThd = BSD_FAKEDOT_MID_DIF;
    }
    else // if (BSDVelSpeedVal > BSD_SCENE_MID_SPD) // 要看下高峰期这个的影响
    {
        difValueThd = BSD_FAKEDOT_MAX_DIF;
    }

    // 1、当前与起点的位移    2、目标与原点达到的最大位移且10帧以上   3、目标长度大于3的认为非假点
    if ((pobjPath->rangestartdiffvalue >= difValueThd) || (pobjPath->isTruePoint) || pobjPath->boxLength >= ADAS_POINT_BOX_LENGTH_THRESHOLD)
    {
        // pobjPath->objFakeDOt = false;
    }
    else
    {
        pobjPath->objFakeDOt = true; // 0，非假点，1 假点
    }
#endif
}

#if (1 == VEHICLE_TURN_DETECTION_ENABLED)
/**
 * @brief 带符号的统计
 * @param data 
 * @param num 
 * @return float 
 */
static float ADAS_AvrCalc_Symbol(float *data, int num, uint8_t symbol)
{
    int i = 0;
    int calcnum = 0;
    float sum = 0.0f;
    float avr = 0.0f;

    if (1 == symbol){
        for (i = 0; i < num; i++){
            if (data[i] > 0.0f){
                sum += data[i];
                calcnum++;
            }  
        }
    }else{
        for (i = 0; i < num; i++){
            if (data[i] < 0.0f){
                sum += data[i];
                calcnum++;
            }  
        }
    }
    avr = sum / (float)calcnum;

    return avr;
}

/**
 * @brief 弯道检测
 * @return float 
 */
static bool ADAS_turnBufferCheck(float* data, int size)
{
    int i = 0;
    int iCnt = 0;

    for (i = 0; i < size; i++){
        if (fabsf(data[i]) < CENTERX_TURN_THR){
            iCnt++;
        }
    }
    if (iCnt >= (size - CENTERX_BUFFER_NUM_ALLOW_ERROR)){
        return true;
    }else{
        return false;
    }
}

/**
 * @brief 转弯场景识别, 输出经过处理后的转弯半径。
 * @param centerx 
 * @return float 
 */
static float ADAS_centerxCalc(float centerx)
{
    int i = 0;
    float result = 0.0f;

    (void)memmove(&stCenterx.acData[1], &stCenterx.acData[0], sizeof(float) * (CENTERX_BUFFER_NUM - 1U));
    stCenterx.acData[0] = centerx;
    stCenterx.positiveNum = 0;
    stCenterx.negativeNum = 0;
    stCenterx.nearpositiveNum = 0;
    stCenterx.nearnegativeNum = 0;

    result = centerx;
    // 统计全部buffer的正负号
    for (i = 0; i < CENTERX_BUFFER_NUM; i++)
    {
        if (stCenterx.acData[i] > 0.0f){
            stCenterx.positiveNum++;
        }else{
            stCenterx.negativeNum++;
        }
    }
    // 统计最近buffer的正负号
    for (i = 0; i < NEARCENTERX_BUFFER_NUM; i++)
    {
        if (stCenterx.acData[i] > 0.0f){
            stCenterx.nearpositiveNum++;
        }else{
            stCenterx.nearnegativeNum++;
        }
    }
    // 
    if (stCenterx.nearpositiveNum > stCenterx.nearnegativeNum){
        stCenterx.posnegsymbol = 1;
    }else{
        stCenterx.posnegsymbol = 0;
    }

    // 正负数量统计 符号全部一致 且均值小于一定值 才认为是在转弯
    if (((stCenterx.positiveNum >= (CENTERX_BUFFER_NUM-CENTERX_BUFFER_NUM_ALLOW_ERROR)) || (stCenterx.negativeNum >= (CENTERX_BUFFER_NUM-CENTERX_BUFFER_NUM_ALLOW_ERROR))) && (ADAS_turnBufferCheck(&stCenterx.acData[0], CENTERX_BUFFER_NUM))){
        stCenterx.turncnt++;
    }else{
        if (stCenterx.turncnt >= 50){
            stCenterx.turncnt = 50;
        }
        if (stCenterx.turncnt > 0){
            stCenterx.turncnt--;
        }
    }
    // 检测到持续入弯一定帧数后, 判定弯道。
    if (stCenterx.turncnt >= 10){
        stCenterx.turnflag = true;
        stCenterx.turnovercnt = 0;
    }else{
        stCenterx.turnovercnt++;
        if (stCenterx.turnovercnt >= 30){
            stCenterx.turnflag = false;
        }
    }

    // 500以下的转弯半径谨慎输出, 最近N帧的数据符号不一致, 不输出500以下的转弯半径。输出上一次符号相同的。
    if ((false == stCenterx.turnflag) && (centerx < CENTERX_TURN_SHAKE_THR))
    {
        // 转弯时输出均值
        if ((stCenterx.nearpositiveNum >= (NEARCENTERX_BUFFER_NUM-CENTERX_BUFFER_NUM_ALLOW_ERROR)) || (stCenterx.nearnegativeNum >= (NEARCENTERX_BUFFER_NUM-CENTERX_BUFFER_NUM_ALLOW_ERROR))){
            result = ADAS_AvrCalc_Symbol(&stCenterx.acData[0], NEARCENTERX_BUFFER_NUM / 2,stCenterx.posnegsymbol);      // 输出近10帧的均值
        }else{
            // 未识别到转弯场景而产生了较小的转弯半径 输出 CENTERX_TURN_THR
            if ((centerx > 0) && (centerx < CENTERX_TURN_SHAKE_THR)){
                result = CENTERX_TURN_SHAKE_THR;
            }else if ((centerx < 0) && (centerx > (-CENTERX_TURN_SHAKE_THR))){
                result = -CENTERX_TURN_SHAKE_THR;
            }
        }
    }
    // 数组里的数据方向全部一致, 用最近10组的均值.

    return result;
}
#endif

/**
 * @brief RDP跟踪点列表极坐标转换
 * 
 * @param pRDP_TrkObjectList RDP跟踪点列表地址
 * @param pfreezedVehDyncData VDY车身动态数据地址
 * @param pobjPath 目标相关结构体地址
 */
static void ADAS_covTrackerObjCoordinate(const RDP_TrkObjectList_t *pRDP_TrkObjectList,
                                         const VDY_DynamicEstimate_t* pfreezedVehDyncData,
                                         OBJ_NODE_STRUCT *pobjPath, 
                                         ALARM_OBJECT_T *pobjAlm,
                                         const VDY_Info_t* pVDY,
                                         const ADAS_TimeClase_t timeClase_t)
{
    float factor;
    float centerx = 0;
    float x_offset, y_offset;

    uint8_t  l_r = (((gADASRadarId % 2) == 0) ? BSD_RADAR_LEFT : BSD_RADAR_RIGHT);
    uint16_t i;
    const RDP_TrkObjectInfo_t *prdpTrkObject = pRDP_TrkObjectList->rdpTrkObject; 

#ifndef PC_DBG_FW
    switch(gADASRadarId) //根据雷达安装位置，补偿坐标
    {
#if ( defined(VEHICLE_TYPE_BYD_HA5HC_GJ) || defined(VEHICLE_TYPE_BYD_EM2EH_GJ) || defined(VEHICLE_TYPE_BYD_UREC_GJ) || defined(VEHICLE_TYPE_BYD_HTEBY_GJ) || defined(VEHICLE_TYPE_BYD_HTEB_MMT_GJ) )
        case (uint8_t)REAR_LEFT_RADAR_ID:
        {
            x_offset = get_install_message()->mount4pos_to_outer_x_fl; //MOUNT4POS_TO_OUTER_EDGE_X_FL;
            y_offset = get_install_message()->mount4pos_to_outer_y_fl; //MOUNT4POS_TO_OUTER_EDGE_Y_FL;
        } break;
        case (uint8_t)REAR_RIGHT_RADAR_ID:
        {
            x_offset = get_install_message()->mount5pos_to_outer_x_fl;//MOUNT5POS_TO_OUTER_EDGE_X_FL;
            y_offset = get_install_message()->mount5pos_to_outer_y_fl;//MOUNT5POS_TO_OUTER_EDGE_Y_FL;
        } break;
        case (uint8_t)FRONT_LEFT_RADAR_ID:
        {
            x_offset = get_install_message()->mount6pos_to_outer_x_fl;//MOUNT6POS_TO_OUTER_EDGE_X_FL;
            y_offset = get_install_message()->mount6pos_to_outer_y_fl;//MOUNT6POS_TO_OUTER_EDGE_Y_FL;
        } break;
        case (uint8_t)FRONT_RIGHT_RADAR_ID:
        {
            x_offset = get_install_message()->mount7pos_to_outer_x_fl;//MOUNT7POS_TO_OUTER_EDGE_X_FL;
            y_offset = get_install_message()->mount7pos_to_outer_y_fl;//MOUNT7POS_TO_OUTER_EDGE_Y_FL;
        } break;
#else
        case (uint8_t)REAR_LEFT_RADAR_ID:
        {
            x_offset = MOUNT4POS_TO_OUTER_EDGE_X_FL;
            y_offset = MOUNT4POS_TO_OUTER_EDGE_Y_FL;
        } break;
        case (uint8_t)REAR_RIGHT_RADAR_ID:
        {
            x_offset = MOUNT5POS_TO_OUTER_EDGE_X_FL;
            y_offset = MOUNT5POS_TO_OUTER_EDGE_Y_FL;
        } break;
        case (uint8_t)FRONT_LEFT_RADAR_ID:
        {
            x_offset = MOUNT6POS_TO_OUTER_EDGE_X_FL;
            y_offset = MOUNT6POS_TO_OUTER_EDGE_Y_FL;
        } break;
        case (uint8_t)FRONT_RIGHT_RADAR_ID:
        {
            x_offset = MOUNT7POS_TO_OUTER_EDGE_X_FL;
            y_offset = MOUNT7POS_TO_OUTER_EDGE_Y_FL;
        } break;
#endif
        default :
        {
            x_offset = 0;
            y_offset = 0;
        } break;
    }
#else
#ifdef PC_DBG_FW
    gxoffset = MOUNT4POS_TO_OUTER_EDGE_X_FL;
#endif 
    x_offset = gxoffset;
    y_offset = gyoffset;
#endif

#ifndef PC_DBG_FW   //边线补偿
    pobjAlm->RoadLine = RDP_getRoadDescriptionPointer()->coeff[0] - x_offset;
#else
    pobjAlm->RoadLine = RDP_getRoadDescriptionPointer()->coeff[0] - groadlineoffset;
#endif
    centerx = (l_r == BSD_RADAR_LEFT) ? (-pfreezedVehDyncData->vdyCurveRadius) : pfreezedVehDyncData->vdyCurveRadius;
#if (1 == VEHICLE_TURN_DETECTION_ENABLED)
    pobjAlm->filtercenterx = ADAS_centerxCalc(centerx);         // 滤波后的转弯半径
#endif
    pobjAlm->covercnt = RDP_getRoadDescriptionPointer()->covercnt;
	pobjAlm->movedobjnum = 0;

    // 自车速度滑窗记录
    (void)memmove(&pobjAlm->stored_last_SpeedVal[1],
                  &pobjAlm->stored_last_SpeedVal[0],
                  sizeof(float) * (ADAS_HISTORY_NUM - 1));
    pobjAlm->stored_last_SpeedVal[0] = pobjAlm->BSDVelSpeedVal / 3.6f;
    pobjAlm->avgSpeedVal = ADAS_cacAvg(pobjAlm->stored_last_SpeedVal, ADAS_HISTORY_NUM);
    // 自车位移
    (void)memmove(&pobjAlm->vehiclemove[1],
        &pobjAlm->vehiclemove[0],
        sizeof(float) * (ADAS_HISTORY_NUM - 1));
    pobjAlm->vehiclemove[0] = fabsf((pobjAlm->BSDVelSpeedVal / 3.6f) * pobjAlm->avgFramerate);
    pobjAlm->sumvehiclemove = ADAS_cacAvg(pobjAlm->vehiclemove, ADAS_HISTORY_NUM) * ADAS_HISTORY_NUM;  // 减少一个函数定义  复用函数
    pobjAlm->guessstaticguardrailangle = RDP_getRoadDescriptionPointer()->coeff[1];         // 静止护栏的角度  结合目标综合使用.

    for (i = 0U; i < ADAS_TRK_OBJNUM; i++, pobjPath++, prdpTrkObject++)
    {
        if (pRDP_TrkObjectList->rdpTrkObject[i].rdpTrkObjRange <= 0.1f)
        {
            //距离过小，过滤掉
            (void)memset(pobjPath, 0, sizeof(OBJ_NODE_STRUCT)); //清零
        }
        else
        {

            pobjPath->TrkObjMissCnt = prdpTrkObject->rdpTrkObjMissCnt;
            pobjPath->TrkObjHitCnt = prdpTrkObject->rdpTrkObjHitCnt;
            pobjPath->TrkObjRcwOverLapx = prdpTrkObject->rdpTrkObjRcwOverLapx - x_offset;
            pobjPath->TrkObjDowOverLapx = prdpTrkObject->rdpTrkObjDowOverLapx - x_offset;
            pobjPath->TrkObjOverLapxCnt = prdpTrkObject->rdpTrkObjOverLapxCnt;
            pobjPath->locktimedifference += pobjAlm->tracktime;

            // mis hit 同为0时刻,  表示ID进行了切换. 重计生命周期
            if (((0U == pobjPath->TrkObjMissCnt) && (0U == pobjPath->TrkObjHitCnt)) || (1 == prdpTrkObject->rdpLifeCycleCnt))
            {
                (void)memset(pobjPath, 0, sizeof(OBJ_NODE_STRUCT)); //清零

                pobjPath->startX = prdpTrkObject->rdpTrkObjDistX; //记录起批的位置
                pobjPath->startY = prdpTrkObject->rdpTrkObjDistY; //记录起批的位置

                pobjPath->startCandiX = prdpTrkObject->rdpTrkObjStartX; //这里记录的需要是candi航迹的起批位置
                pobjPath->startCandiY = prdpTrkObject->rdpTrkObjStartY; //这里记录的需要是candi航迹的起批位置

                pobjPath->startAngle = atan2f(pobjPath->startX, pobjPath->startY) * radtodeg;   //记录起批方位角，用来判断目标是否是从fov外穿进来的

                pobjPath->headingAngleRCWDOW = 90.0f;   //赋初值，防止因没更新而误报，此夹角作用于纵向功能，所以90°超过了适用范围
                pobjPath->headingAnglerealRCWDOW = 90.0f;

                pobjPath->lock_ttcy_thresold1 = RCW_LOCK_COORDINATE_TTY_MIN;
                pobjPath->lock_ttcy_thresold2 = DOW_TTC_MAX;
            }           
            if (pobjPath->lifeCycle < MAX_UCHAR)
            {
                pobjPath->lifeCycle = prdpTrkObject->rdpLifeCycleCnt;
                //pobjPath->lifeCycle++;
            }

            factor = 0.0f;
            pobjPath->analysisStats.directlyBehind = 0U;

            // 上次的值pObjPath 和 当下的值pTarget 进行加权平均，factor越大，上次值权重越大
            pobjPath->range = (factor * pobjPath->range) + ((1.0 - factor) * prdpTrkObject->rdpTrkObjRange);
            if(pobjPath->maxrange < pobjPath->range)
            {
                pobjPath->maxrange = pobjPath->range;   //记录最远距离
            }
            pobjPath->angle = (factor * pobjPath->angle) + ((1.0 - factor) * prdpTrkObject->rdpTrkObjAzimuthAngle);

            ADAS_isFakeJudgment(pfreezedVehDyncData, pobjPath); //识别假点

            pobjPath->x     = (prdpTrkObject->rdpTrkObjDistX) - x_offset;    //补偿跟踪点坐标
            pobjPath->y     = (prdpTrkObject->rdpTrkObjDistY) - y_offset;
            pobjPath->v     = -(prdpTrkObject->rdpTrkObjVelocity);
            pobjPath->vx    = -(prdpTrkObject->rdpTrkObjVrelX);
            pobjPath->ax    = -(prdpTrkObject->rdpTrkObjArelX);
            pobjPath->vy    = -(prdpTrkObject->rdpTrkObjVrelY);
            pobjPath->ay    = -(prdpTrkObject->rdpTrkObjArelY);
            pobjPath->nreTargetX = prdpTrkObject->nearestTargetX;
            pobjPath->nreTargetY = prdpTrkObject->nearestTargetY;

            int8_t radarFR = (pobjAlm->radarPosition == 0U) ? -1 : 1;
            int8_t radarLR = (pobjAlm->l_r == 0U) ? -1 : 1;
            // pobjPath->absolutevx = prdpTrkObject->rdpTrkObjVrelX * radarLR + pobjAlm->egoVelLateral;
            // pobjPath->absolutevy = prdpTrkObject->rdpTrkObjVrelY * radarFR + pobjAlm->egoVelLongitudinal;
            pobjPath->absolutevx = pobjPath->vx + pobjAlm->vehiclevx;   //目标的x方向速度用车辆的A横向速度去修正，此时车速为负，目标速度靠近为正
            pobjPath->absolutevy = pobjPath->vy + pobjAlm->vehiclevy;   //目标的y方向速度用车辆的向速度去修正  靠近目标的绝对速度   
            pobjPath->absolutevabs = MagicSqrt((pobjPath->absolutevx * pobjPath->absolutevx) + (pobjPath->absolutevy * pobjPath->absolutevy)) * 3.6f; //目标绝对速度的大小, 单位转成km/h

            pobjPath->headingAngleCTAB = (atan2_replacement(pobjPath->absolutevy, pobjPath->absolutevx) * radtodeg) + 90.0f; // 适用于FCTAB和RCTAB

            pobjPath->vxByCarSpeed  = (pfreezedVehDyncData->vdySpeedInmps * cosf(prdpTrkObject->rdpTrkObjAzimuthAngle)) + pobjPath->v;  //此处单位是m/s
            if ((gADASRadarId == 6) || (gADASRadarId == 7))
            {
                pobjPath->vyByCarSpeed = prdpTrkObject->rdpTrkObjVrelY + pfreezedVehDyncData->vdySpeedInmps;  //此处单位是m/s
            }
            else
            {
                pobjPath->vyByCarSpeed = prdpTrkObject->rdpTrkObjVrelY - pfreezedVehDyncData->vdySpeedInmps;  //此处单位是m/s
            }
            pobjPath->vyByCarSpeed = (pfreezedVehDyncData->vdySpeedInmps) - prdpTrkObject->rdpTrkObjVrelY;  //此处单位是m/s
            pobjPath->rcDis = MagicSqrt(((centerx - pobjPath->x) * (centerx - pobjPath->x)) + (pobjPath->y * pobjPath->y));
            pobjPath->maxX  = (pobjPath->x > pobjPath->maxX) ? pobjPath->x : pobjPath->maxX; // 历史最大
            pobjPath->maxY  = (pobjPath->y > pobjPath->maxY) ? pobjPath->y : pobjPath->maxY;
            pobjPath->attrFlag = prdpTrkObject->rdpTrkObjStatus;
            pobjPath->objType = prdpTrkObject->rdpTrkObjType;   // 使用时对应  OBJ_TYPE_ENUM 变量

            //航迹框更新
            pobjPath->boxCenterX = prdpTrkObject->rdpTrkObjBoxCenterX;
            pobjPath->boxCenterY = prdpTrkObject->rdpTrkObjBoxCenterY;
            pobjPath->boxLength = prdpTrkObject->rdpTrkObjBoxLength;
            pobjPath->boxWidth = prdpTrkObject->rdpTrkObjBoxWidth;

            pobjPath->TrkObjReliability = prdpTrkObject->rdpTrkObjReliability;
            pobjPath->TrkObjVr0_hit = prdpTrkObject->rdpTrkObjVr0_hit; 
            pobjPath->analysisStats.TrkObjDowShelterScene = prdpTrkObject->rdpTrkObjDowShelterScene;       // DOW法规场景标识
            pobjPath->analysisStats.TrkObjdowguardCrossCnt = prdpTrkObject->rdpTrkObjdowguardCrossCnt;
            pobjPath->analysisStats.TrkObjdowguardblocksence = prdpTrkObject->rdpTrkObjdowguardblocksence;
            pobjPath->id = prdpTrkObject->id;
            // 记录每个帧周期内运动目标的数量.
            if ((pobjPath->attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U )
            {
                pobjAlm->movedobjnum++;
            }

            /*
                D1:Dot1,距离原点（雷达）最近的点，这个在4个雷达中都是如此

                D3  D4  |   |   D4  D3
                +---+   |   |   +---+
                |obj|   |   |   |obj|
                +---+   |   |   +---+
                D2  D1  |   |   D1  D2
                --6-----+---+-----7---
                        | V |
                --4-----+---+-----5---
                D2  D1  |   |   D1  D2
                +---+   |   |   +---+
                |obj|   |   |   |obj|
                +---+   |   |   +---+
                D3  D4          D4  D3
            */
            float tmpBoxLengthHalf = pobjPath->boxLength / 2;
            float tmpBoxWidthHalf = pobjPath->boxWidth / 2;

            // 当前目标点的航迹框的四个顶点位置,相对于雷达有最近、最远等的点,不用对不同雷达做区分，
            // Dot1
            pobjPath->boxDot1X = pobjPath->boxCenterX - tmpBoxWidthHalf;
            //pobjPath->boxDot1Y = pobjPath->boxCenterY - tmpBoxLengthHalf;
            // Dot2
            pobjPath->boxDot2X = pobjPath->boxCenterX + tmpBoxWidthHalf;
            //pobjPath->boxDot2Y = pobjPath->boxCenterY - tmpBoxLengthHalf;
            // // Dot3
            // pobjPath->boxDot3X = pobjPath->boxCenterX + tmpBoxWidthHalf;
            // pobjPath->boxDot3Y = pobjPath->boxCenterY + tmpBoxLengthHalf;
            // // Dot4
            // pobjPath->boxDot4X = pobjPath->boxCenterX - tmpBoxWidthHalf;
            // pobjPath->boxDot4Y = pobjPath->boxCenterY + tmpBoxLengthHalf;

            (void)memmove(&pobjPath->stored_last_x[1], &pobjPath->stored_last_x[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
            pobjPath->stored_last_x[0] = pobjPath->x;

            (void)memmove(&pobjPath->stored_last_y[1], &pobjPath->stored_last_y[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
            pobjPath->stored_last_y[0] = pobjPath->y;

            (void)memmove(&pobjPath->stored_last_vx[1], &pobjPath->stored_last_vx[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
            pobjPath->stored_last_vx[0] = pobjPath->vx;

            (void)memmove(&pobjPath->stored_last_vy[1], &pobjPath->stored_last_vy[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
            pobjPath->stored_last_vy[0] = pobjPath->vy;
            
            // 航向角滑窗记录
            (void)memmove(&pobjPath->stored_last_headingAngle[1], &pobjPath->stored_last_headingAngle[0], sizeof(float) * (ADAS_HISTORY_NUM - 1));
            pobjPath->stored_last_headingAngle[0] = pobjPath->headingAngleCTAB;

            if (fabsf(pobjPath->stored_last_x[ADAS_HISTORY_NUM - 1]) > FLOAT_EPS)
            {
                pobjPath->avgheadingX = ADAS_cacAvg(pobjPath->stored_last_x, ADAS_HISTORY_NUM);
            }
            else
            {
                pobjPath->avgheadingX = pobjPath->stored_last_x[0];
            }

            if (fabsf(pobjPath->stored_last_y[ADAS_HISTORY_NUM - 1]) > FLOAT_EPS)
            {
                pobjPath->avgheadingY = ADAS_cacAvg(pobjPath->stored_last_y, ADAS_HISTORY_NUM);
            }
            else
            {
                pobjPath->avgheadingY = pobjPath->stored_last_y[0];
            }

            if (fabsf(pobjPath->stored_last_headingAngle[ADAS_HISTORY_LESSER_NUM - 1]) > FLOAT_EPS)
            {
                pobjPath->moreFrameAvgAngle = ADAS_cacAvg(pobjPath->stored_last_headingAngle, ADAS_HISTORY_LESSER_NUM);

            }
            else
            {
                pobjPath->moreFrameAvgAngle = pobjPath->stored_last_headingAngle[0];
            }

            if (fabsf(pobjPath->stored_last_headingAngle[ADAS_HISTORY_HEADINGANGLE_NUM - 1]) > FLOAT_EPS)
            {
                pobjPath->avgheadingAngle = ADAS_cacAvg(pobjPath->stored_last_headingAngle, ADAS_HISTORY_HEADINGANGLE_NUM); // 取5帧
            }
            else
            {
                pobjPath->avgheadingAngle = pobjPath->stored_last_headingAngle[0];
            }

            if (fabsf(pobjPath->stored_last_headingAngle[ADAS_HISTORY_HEADINGANGLE_NUM - 1]) > FLOAT_EPS)
            {
                pobjPath->headingAngleCTAB_L = ADAS_cacAvgL(pobjPath->stored_last_headingAngle, ADAS_HISTORY_HEADINGANGLE_NUM);
            }
            else
            {
                pobjPath->headingAngleCTAB_L = pobjPath->stored_last_headingAngle[0];
            }

            // 移动平均滤波
            ADAS_calculateMovingAverage(pobjPath->stored_last_x[0], i, ADAS_HISTORY_HEADINGANGLE_NUM, pobjPath->lifeCycle, &pobjPath->avgX);
            ADAS_calculateMovingAverage(pobjPath->stored_last_y[0], i, ADAS_HISTORY_HEADINGANGLE_NUM, pobjPath->lifeCycle, &pobjPath->avgY); 
            // 存储ADAS_HISTORY_HEADINGANGLE_NUM帧的滤波值
            (void)memmove(&pobjPath->data_stored.filter_x[1], &pobjPath->data_stored.filter_x[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
            pobjPath->data_stored.filter_x[0] = pobjPath->avgX;
            (void)memmove(&pobjPath->data_stored.filter_y[1], &pobjPath->data_stored.filter_y[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
            pobjPath->data_stored.filter_y[0] = pobjPath->avgY;
            // 加权移动平均滤波
            ADAS_calculateWeightedAverage(pobjPath->stored_last_vx[0], i, ADAS_HISTORY_HEADINGANGLE_NUM, pobjPath->lifeCycle, &pobjPath->avgVx);
            ADAS_calculateWeightedAverage(pobjPath->stored_last_vy[0], i, ADAS_HISTORY_HEADINGANGLE_NUM, pobjPath->lifeCycle, &pobjPath->avgVy);
            if (pobjPath->x > 1.2f) 
            {
                // 最后的位置锁速度
                ADAS_calculateWeightedAverage(pobjPath->stored_last_vx[0], i, ADAS_HISTORY_HEADINGANGLE_NUM, pobjPath->lifeCycle, &pobjPath->avgVxFctb);
            } 
            else
            {
                if (fabsf(pobjPath->avgVy - pobjPath->avgVxFctb) >= 0.5f)
                {
                    // 差异较大，用均值替换掉avgVxFctb
                    pobjPath->avgVxFctb = (pobjPath->avgVy + pobjPath->avgVxFctb) / 2;
                }
            }
            // 存储滤波值
            // (void)memmove(&pobjPath->data_stored.filter_x[1], &pobjPath->data_stored.filter_x[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
            // pobjPath->data_stored.filter_x[0] = pobjPath->avgX;
            // (void)memmove(&pobjPath->data_stored.filter_y[1], &pobjPath->data_stored.filter_y[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
            // pobjPath->data_stored.filter_y[0] = pobjPath->avgY;

            ADAS_calcgroundY(pobjAlm, pobjPath);

            ADAS_identifyTarget(pobjPath);

            ADAS_cacheadingAngleRCWDOW(pobjAlm, pobjPath);

            ADAS_cacOverlap(pobjPath);
            
            ADAS_cacheCrossVx(pobjPath);

            ADAS_IsDirBehind(pobjPath, ADAS_HISTORY_NUM);

            ADAS_cncapBSDcheck(pobjAlm, pobjPath);

            ADAS_cncapRCTAheadingcheck(pobjAlm, pobjPath, pVDY);

            ADAS_FCTB_followcheck(pobjAlm, pobjPath);

            pobjPath->status = OBJ_STATUS_VALID;
            pobjPath->alarmType = (uint32_t)ALARM_NO_ACTIVE;
        }
    }

    ADAS_detectTurnRadiusRapidChange(pobjAlm, pobjPath, pVDY, timeClase_t);
}

/**
 * @brief 初始化目标结构体
 * 
 */
static void ADAS_initObjPath(void)
{
    (void)memset(gobjPath, 0, sizeof(gobjPath));
}

/**
 * @brief 报警预处理，初始化、坐标系转换等
 * 
 * @param pRDP_TrkObjectList RDP跟踪点列表地址
 * @param pVDY VDY车身动态数据地址 
 * @param pobjPath 目标相关结构体地址
 * @param pobjAlm 报警结构体地址  
 */
static void ADAS_runPreAlmProcess(const RDP_TrkObjectList_t *pRDP_TrkObjectList, const VDY_Info_t *pVDY
                            , OBJ_NODE_STRUCT *pobjPath, ALARM_OBJECT_T *pobjAlm, ADAS_TimeClase_t timeClass)
{
    static int dir = 0;

    //更新本车运动方向状态，目标重新处理
    // 方向改变且车速有变化时,才重置目标.
    //if ((dir != pVDY->pVDY_DynamicInfo->vdyDriveDirection) && (fabsf(pobjAlm->BSDVelSpeedVal) > FLOAT_EPS))
    if (dir != pVDY->pVDY_DynamicInfo->vdyDriveDirection)
    {
        //本车方向改变，path点重置
        dir = pVDY->pVDY_DynamicInfo->vdyDriveDirection;

        ADAS_initObjPath();
    }

    ADAS_runRoadLine(pVDY->pVDY_DynamicInfo);

    ADAS_covTrackerObjCoordinate(pRDP_TrkObjectList, pVDY->pVDY_DynamicInfo, pobjPath, pobjAlm, pVDY, timeClass);
}


/**
 * @brief 初始化报警结构体
 * 
 */
static void ADAS_initAlarmObject(void)
{
    gobjAlm.pVehicleInfo = &gVehicleInfo;
    gobjAlm.pobjPath = gobjPath;
    gobjAlm.RawTargets = RDP_getBKTargetsListPointer();
}

/**
 * @brief ADAS各参数初始化
 *
 */
void ADAS_initParams(void)
{
    ADAS_initObjPath();

    ADAS_initAlarmObject();
}

/**
 * @brief 转弯处理
 * 
 * @param pobjAlm 报警结构体地址
 */
static void ADAS_runTurnHandler(ALARM_OBJECT_T *pobjAlm)
{

    float centerx = pobjAlm->centerx;
    TVehicleInfo *pVehicleInfo = pobjAlm->pVehicleInfo;

    //车辆转弯状态判断
    if(fabsf(centerx) < 200)
    {
        //正在转弯
        pVehicleInfo->turn_state.bit.is_turning = 1;
        pVehicleInfo->turn_state.bit.last_turn = 1;
    }
    else
    {
        //直行
        pVehicleInfo->turn_state.bit.is_turning = 0U;
    }

    //转弯延时计时，上次转弯退出置零计数
    if ((pVehicleInfo->turn_state.bit.last_turn == 1U) &&
        (pVehicleInfo->turn_state.bit.is_turning == 0U))
    {
        if (++pVehicleInfo->after_turn_timer >= 30U)
        {
            pVehicleInfo->turn_state.bit.last_turn = 0U;
            pVehicleInfo->after_turn_timer = 0U;
        }
    }
    else
    {
        pVehicleInfo->after_turn_timer = 0U;
    }

}

/**
 * @brief 按最近或最小ttc原则，找到要发送的点
 * 
 * @param pRDP_TrkObjectList RDP跟踪点列表地址
 * @param pobjPath 目标相关结构体地址
 */
static void ADAS_findPointsToSend(ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath)
{
    uint8_t i;
    float TTC;
    float minDis = 1000;
    gadasFunctionState.adasLCATtc = MAX_UCHAR;
    gadasFunctionState.adasDOWTtc = MAX_UCHAR;
    gadasFunctionState.adasRCWTtc = MAX_UCHAR;
    gadasFunctionState.adasRCTATtc = MAX_UCHAR;
    gadasFunctionState.adasRCTBTtc = MAX_UCHAR;
    gadasFunctionState.adasFCTATtc = MAX_UCHAR;
    gadasFunctionState.adasFCTBTtc = MAX_UCHAR;
    gadasFunctionState.adasBSDAlarmObjID = 128U;
    gadasFunctionState.adasLCAAlarmObjID = 128U;
    gadasFunctionState.adasDOWAlarmObjID = 128U;
    gadasFunctionState.adasRCWAlarmObjID = 128U;
    gadasFunctionState.adasRCTAAlarmObjID = 128U;
    gadasFunctionState.adasRCTBAlarmObjID = 128U;
    gadasFunctionState.adasFCTAAlarmObjID = 128U;
    gadasFunctionState.adasFCTBAlarmObjID = 128U;

    for (i = 0U; i < ADAS_TRK_OBJNUM; i++)
    {
        if ((gADASRadarId == (uint8_t)REAR_LEFT_RADAR_ID) || (gADASRadarId == (uint8_t)REAR_RIGHT_RADAR_ID))
        {
            if ((pobjPath[i].alarmType & (uint32_t)ALARM_ACTIVE_BSD) != 0U)
            {
                if (minDis > pobjPath[i].range)
                {
                    minDis = pobjPath[i].range;
                    gadasFunctionState.adasBSDAlarmObjID = i;
                }
            }
            else if ((pobjPath[i].alarmType & (uint32_t)ALARM_ACTIVE_LCA) != 0U)
            {
                TTC = ADAS_cacTTCY(pobjPath, i);

                if (TTC < gadasFunctionState.adasLCATtc)
                {
                    gadasFunctionState.adasLCATtc = TTC;
                    gadasFunctionState.adasLCAAlarmObjID = i;
                }
            }
            else if ((pobjPath[i].alarmType & (uint32_t)ALARM_ACTIVE_DOW) != 0U)
            {

                TTC = ADAS_cacTTCY(pobjPath, i);
                
                if (TTC < gadasFunctionState.adasDOWTtc)
                {
                    gadasFunctionState.adasDOWTtc = TTC;
                    gadasFunctionState.adasDOWAlarmObjID = i;
                }
            }
            else if ((pobjPath[i].alarmType & (uint32_t)ALARM_ACTIVE_RCW) != 0U)
            {

                TTC = ADAS_cacTTCY(pobjPath, i);
                
                if (TTC < gadasFunctionState.adasRCWTtc)
                {
                    gadasFunctionState.adasRCWTtc = TTC;
                    gadasFunctionState.adasRCWAlarmObjID = i;
                }
            }
            else if ((pobjPath[i].alarmType & (uint32_t)ALARM_ACTIVE_RCTA) != 0U)
            {
                TTC = ADAS_cacRCTABTTM(pobjAlm, pobjPath, i);

                if (TTC < gadasFunctionState.adasRCTATtc)
                {
                    gadasFunctionState.adasRCTATtc = TTC;
                    gadasFunctionState.adasRCTAAlarmObjID = i;
                }
            }
            else if((pobjPath[i].alarmType & (uint32_t)ALARM_ACTIVE_RCTB) != 0U)
            {

                TTC = ADAS_cacRCTABTTM(pobjAlm, pobjPath, i);

                if (TTC < gadasFunctionState.adasRCTBTtc)
                {
                    gadasFunctionState.adasRCTBTtc = TTC;
                    gadasFunctionState.adasRCTBAlarmObjID = i;
                }
            }
            else
            {
                ;   //donothing
            }
        }
        else{
            if (((gadasFunctionState.adasFCTAFuncState == (uint8_t)FCTA_FUNC_STATE_ACTIVE) && 
                 ((pobjPath[i].alarmType & (uint32_t)ALARM_ACTIVE_FCTA) != 0U)) != 0U)
            {
                TTC = ADAS_cacTTM(pobjAlm, pobjPath, i);

                if (TTC < gadasFunctionState.adasFCTATtc)
                {
                    gadasFunctionState.adasFCTATtc = TTC;
                    gadasFunctionState.adasFCTAAlarmObjID = i;
                }
            }
            if ((pobjPath[i].alarmType & (uint32_t)ALARM_ACTIVE_FCTB) != 0U)
            {
                TTC = ADAS_cacTTM(pobjAlm, pobjPath, i);

                if (TTC < gadasFunctionState.adasFCTBTtc)
                {
                    gadasFunctionState.adasFCTBTtc = TTC;
                    gadasFunctionState.adasFCTBAlarmObjID = i;
                }
            }
#if (1 == ALARM_TYPE_FDOW_EN)
            if ((pobjPath[i].alarmType & (uint32_t)ALARM_ACTIVE_DOW) != 0U)
            {

                TTC = ADAS_cacTTCY(pobjPath, i);
                
                if (TTC < gadasFunctionState.adasDOWTtc)
                {
                    gadasFunctionState.adasDOWTtc = TTC;
                    gadasFunctionState.adasDOWAlarmObjID = i;
                }
            }
#endif
        }
    }
}

/**
 * @brief 功能算法主函数
 * 
 * @param pobjAlm 报警结构体地址  
 * @param pobjPath 目标相关结构体地址 
 * @param pVDY VDY车身动态数据地址
 * @param timeClase_t 时间戳
 * @param pSlaveRadar 从雷达信息 
 * @return almStsFlag 返回报警标志
 */
static uint32_t ADAS_runAlgorithm(ALARM_OBJECT_T *pobjAlm,
                                  OBJ_NODE_STRUCT *pobjPath,
                                  const VDY_Info_t *pVDY,
                                  const ADAS_TimeClase_t timeClase_t,
                                  const SlaveRadarWarningsStatus *pSlaveRadar)
{
    uint32_t almStsFlag = 0U;

    ADAS_runTurnHandler(pobjAlm);

    if ((gADASRadarId == (uint8_t)FRONT_LEFT_RADAR_ID) || (gADASRadarId == (uint8_t)FRONT_RIGHT_RADAR_ID))
    {
        #if (ALARM_TYPE_FCTA_EN == 1)
        if((gadasFunctionState.adasFCTBFuncState == (uint8_t)FCTB_FUNC_STATE_ACTIVE) || (gadasFunctionState.adasFCTAFuncState == (uint8_t)FCTA_FUNC_STATE_ACTIVE))///<FCTB计算依赖FCTA的结果，且FCTA功能可能比FCTB先激活
        {
            almStsFlag = ADAS_FCTAB_runMain(pobjAlm, pobjPath, pVDY, pSlaveRadar, timeClase_t);
            if(gadasFunctionState.adasFCTAFuncState != (uint8_t)FCTA_FUNC_STATE_ACTIVE)   //FCTA报警条件比FCTB多了一个速度>10所以要判断FCTA的状态机是否激活
            {
                almStsFlag &= ~(uint32_t)FCTA_ALM_FLAG_MARK;
                gadasFunctionState.adasFCTAWarning = 0;
            }

        }
        #endif

        #if (ALARM_TYPE_FDOW_EN == 1)
        for (int i = 0; i < ADAS_TRK_OBJNUM; i++)
        {
            if (pobjPath[i].status == OBJ_STATUS_VALID)
            {
                if ((gadasFunctionState.adasDOWFuncState == DOW_FUNC_STATE_ACTIVE) && (ADAS_DOW_runMain(pobjAlm, pobjPath, i, pVDY) != 0))
                {
                    if (pobjPath[i].alarmType == ALARM_ACTIVE_DOW)
                    {
                        almStsFlag |= DOW_ALM_FLAG_MARK;
                    }
                }
            }
        }
        #endif
    }
    else if ((gADASRadarId == (uint8_t)REAR_LEFT_RADAR_ID) || (gADASRadarId == (uint8_t)REAR_RIGHT_RADAR_ID))
    {
        uint16_t i = 0;
        //报警及类型判断
        for (i = 0; i < ADAS_TRK_OBJNUM; i++)
        {
            //报警判断
            if (pobjPath[i].status == OBJ_STATUS_VALID)
            {
                // 首次if不进入判断  避免开启单个功能时   语法错误.
                if (0)
                {

                }
                #if (ALARM_TYPE_RCW_EN == 1)
                else if ((gadasFunctionState.adasRCWFuncState == RCW_FUNC_STATE_ACTIVE) && (ADAS_RCW_runMain(pobjAlm, pobjPath, i, pVDY) != 0))
                {
                    pobjPath[i].preAlarmType = ALARM_NO_ACTIVE;
                    if (pobjPath[i].alarmType == ALARM_ACTIVE_RCW)
                    {
                        almStsFlag |= RCW_ALM_FLAG_MARK;
                    }
                }
                #endif

                #if (ALARM_TYPE_BSD_EN == 1)
                else if ((gadasFunctionState.adasBSDFuncState == BSD_FUNC_STATE_ACTIVE) && (ADAS_BSD_runMain(pobjAlm, pobjPath, i, pVDY, timeClase_t) != 0))
                {
                    pobjPath[i].preAlarmType = ALARM_NO_ACTIVE;
                    if (pobjPath[i].alarmType == ALARM_ACTIVE_BSD)
                    {
                        almStsFlag |= BSD_ALM_FLAG_MARK;
                    }
                }
                #endif

                #if (ALARM_TYPE_LCA_EN == 1)
                else if ((gadasFunctionState.adasLCAFuncState == LCA_FUNC_STATE_ACTIVE) && (ADAS_LCA_runMain(pobjAlm, pobjPath, i, pVDY) != 0))
                {
                    pobjPath[i].preAlarmType = ALARM_NO_ACTIVE;
                    if (pobjPath[i].alarmType == ALARM_ACTIVE_LCA)
                    {
                        almStsFlag |= LCA_ALM_FLAG_MARK;
                    }
                }
                #endif

                #if (ALARM_TYPE_DOW_EN == 1)
                else if ((gadasFunctionState.adasDOWFuncState == DOW_FUNC_STATE_ACTIVE) && (ADAS_DOW_runMain(pobjAlm, pobjPath, i, pVDY) != 0))
                {
                    if (pobjPath[i].alarmType == ALARM_ACTIVE_DOW)
                    {
                        almStsFlag |= DOW_ALM_FLAG_MARK;
                    }
                }
                #endif

                #if (ALARM_TYPE_ELKA_OT_EN == 1)
                ADAS_ELKA_runMain(pobjAlm, pobjPath, i, pVDY);
                #endif
            }
            else
            {
                pobjPath[i].lastAlarmType = (uint32_t)ALARM_NO_ACTIVE;
                pobjPath[i].preAlarmType = (uint32_t)ALARM_NO_ACTIVE;
            }
        }
    
        #if (ALARM_TYPE_RCTA_EN == 1)
        if ((gadasFunctionState.adasRCTAFuncState == RCTA_FUNC_STATE_ACTIVE) || // RCTA为Off，RCTB依然能工作
            (gadasFunctionState.adasRCTBFuncState == RCTB_FUNC_STATE_ACTIVE))
        {
            almStsFlag = ADAS_RCTAB_runMain(pobjAlm, pobjPath, pVDY, pSlaveRadar);
        }
        else    // 不激活时, 清零变量  避免切换挡位再切回后 持续报警
        {
            getgtempobjPath()->alarmDlyThr = 0;
        }
        #endif

    }
    else
    {
        ; //donothing
    }

    ADAS_findPointsToSend(pobjAlm, gobjPath); //寻找距离最近的点

    return almStsFlag;
}

/**
 * @brief 报警状态数据清理.
 * 
 * @param pobjPath 目标相关结构体地址
 * @param alarmtype 报警类型
 */
void ADAS_clrAlmState(OBJ_NODE_STRUCT *pobjPath, uint32_t alarmtype)
{
    uint8_t i = 0U;

    for (i = 0U; i < ADAS_TRK_OBJNUM; i++)
    {
        pobjPath[i].lastAlarmType &= (~alarmtype);
    }
}

volatile float brake_interval_time;
volatile float fctb_brake_interval_time;
volatile float alarm_last_duration[ADAS_TYPE_MAX];
volatile float alarm_continue_duration[ADAS_TYPE_MAX];

/**
 * @brief 报警持续时间累加
 * 
 * @param trackTime 帧间隔（单位：s） 
 */
static void ADAS_increaseAlarmTime(float trackTime)
{
    uint8_t i = 0U;

    for (i = 0U; i < (uint8_t)ADAS_TYPE_MAX; i++){
        alarm_last_duration[i] += trackTime;
        alarm_last_duration[i] = ((alarm_last_duration[i] > 1.0f) ? 1.0f : alarm_last_duration[i]);
        alarm_continue_duration[i] += trackTime;
        alarm_continue_duration[i] = ((alarm_continue_duration[i] >DOW_DELAY_EXT_TIME) ? DOW_DELAY_EXT_TIME : alarm_continue_duration[i]);
    }
}

/**
 * @brief RCW 报警间隔时间设置
 */
static void ADAS_rcwIntervalCheck(ALARM_OBJECT_T *pobjAlm, float trackTime)
{
    static uint8_t warningflag = 0;
    // 未报警时， 累加时间 
    if (((uint8_t)ADAS_NO_WARNING == lastFunctionState.adasRCWWarning) && ((uint8_t)ADAS_NO_WARNING == gadasFunctionState.adasRCWWarning))
    {
        pobjAlm->rcwntervaltime += trackTime;
    }

    // 触发报警 清除时间 超过一定值才允许报警
    if (((uint8_t)ADAS_NO_WARNING != lastFunctionState.adasRCWWarning) || ((uint8_t)ADAS_NO_WARNING != gadasFunctionState.adasRCWWarning))
    {
        warningflag = 1;
    }

    if (((uint8_t)ADAS_NO_WARNING == lastFunctionState.adasRCWWarning) && ((uint8_t)ADAS_NO_WARNING == gadasFunctionState.adasRCWWarning) && (1 == warningflag))
    {
        warningflag = 0;
        pobjAlm->rcwntervaltime = 0;
    }
}

/**
 * @brief 车辆从静止转为运动后的累计时间
 * @param pobjAlm 
 * @param trackTime 
 */
static void ADAS_vehivleRunningtime(ALARM_OBJECT_T *pobjAlm, float trackTime)
{
    if (pobjAlm->BSDVelSpeedVal <= FLOAT_EPS)
    {
        pobjAlm->runingtime = 0;
    }
    else if ((0 == pobjAlm->BSDVelSpeedDir) && (pobjAlm->BSDVelSpeedVal > FLOAT_EPS))
    {
        pobjAlm->runingtime += trackTime;
    }
}

/**
 * @brief 计算单位时间内的平均帧率
 * @param pobjAlm 
 * @param trackTime 
 */
static void ADAS_avgFramerate(ALARM_OBJECT_T *pobjAlm, float trackTime)
{
    int i = 0;
    float sum = 0.0f;

    (void)memmove(&pobjAlm->historyFramerate[1], &pobjAlm->historyFramerate[0], sizeof(float) * (ADAS_HISTORY_NUM - 1));
    pobjAlm->historyFramerate[0] = trackTime;
    for (i = 0; i < ADAS_HISTORY_NUM; i++)
    {
        sum += pobjAlm->historyFramerate[0];
    }
    pobjAlm->avgFramerate = sum / ADAS_HISTORY_NUM;
}

/**
 * @brief 用于检查报警记录是否需要清除
 *
 * @param type 报警类型
 * @param bit 报警枚举
 * @return true 有效
 * @return false 无效
 */
static void ADAS_checkTriggerRecord(ADAS_TriggerRecord_T *triggerRecord)
{
    float timeThresh = 0.5; // 默认0.5s;

    for (uint8_t adasType = 0; adasType < ADAS_TYPE_MAX; adasType++)
    {
        // 不同功能的时间阈值不同
        if (adasType == (uint8_t)ADAS_TYPE_FCTB)
        {
            timeThresh = 0.5f;
        }
        else
        {
            timeThresh = 0.5f;
        }

        // 对应功能的时间计时是否已经满足
        if ((triggerRecord->simestamp - triggerRecord->adasTriggerTime.funcTimestamp[adasType]) > timeThresh)
        {
            triggerRecord->isActivatedInXms.isActivatedInXms[adasType] = 0;
        }

        // 清除对应功能状态
        gAdasTriggerRecord.adasFuncStateRec[adasType] = FUNC_STATE_OFF;
        gAdasTriggerRecord.adasWarningRec[adasType] = (uint8_t)ADAS_NO_WARNING;
    }
}

/**
 * @brief ADAS切换到不运行的场景下，清除相关信息
 */
void ADAS_clearMainFunction(void)
{
    gadasFunctionState.adasDOWWarning = lastFunctionState.adasDOWWarning = 0;
    gadasFunctionState.adasLCAWarning = lastFunctionState.adasLCAWarning = 0;
    gadasFunctionState.adasBSDWarning = lastFunctionState.adasBSDWarning = 0;
    gadasFunctionState.adasFCTAWarning = lastFunctionState.adasFCTAWarning = 0;
    gadasFunctionState.adasFCTBWarning = lastFunctionState.adasFCTBWarning = 0;
    gadasFunctionState.adasRCTAWarning = lastFunctionState.adasRCTAWarning = 0;
    gadasFunctionState.adasRCTBWarning = lastFunctionState.adasRCTBWarning = 0;
    gadasFunctionState.adasRCWWarning = lastFunctionState.adasRCWWarning = 0;
    gadasFunctionState.adasELKAOTWarning = lastFunctionState.adasELKAOTWarning = 0;

    ADAS_checkTriggerRecord(&gAdasTriggerRecord);
}

/**
 * @brief 清除相关状态
 * @return satic 
 */
static void ADAS_clrFunctionState(void)
{
    gadasFunctionState.adasDOWWarning = (uint8_t)ADAS_NO_WARNING;
    gadasFunctionState.adasLCAWarning = (uint8_t)ADAS_NO_WARNING;
    gadasFunctionState.adasBSDWarning = (uint8_t)ADAS_NO_WARNING;
    gadasFunctionState.adasFCTAWarning = (uint8_t)ADAS_NO_WARNING;
    gadasFunctionState.adasFCTBWarning = (uint8_t)ADAS_NO_WARNING;
    gadasFunctionState.adasRCTAWarning = (uint8_t)ADAS_NO_WARNING;
    gadasFunctionState.adasRCTBWarning = (uint8_t)ADAS_NO_WARNING;
    gadasFunctionState.adasRCWWarning = (uint8_t)ADAS_NO_WARNING;
    gadasFunctionState.adasELKAOTWarning = (uint8_t)ADAS_NO_WARNING; 

    ADAS_checkTriggerRecord(&gAdasTriggerRecord); 
}

/**
 * @brief 清除相关状态
 * @return satic
 */
void ADAS_setclrFunctionState(const ADAS_TimeClase_t timeClass)
{
    ADAS_clrFunctionState();
    gAdasTriggerRecord.simestamp += timeClass.trackTime;
}

/**
 * @brief
 * 
 * @param pCFG 配置信息
 * @param pVDY 车身数据
 * @param trackTime 帧间隔（单位：s）
 * @param pobjAlm 报警结构体地址   
 */
static void ADAS_updateParameters(const ADAS_RadarConfiguration_t *pCFG, const VDY_Info_t *pVDY, ADAS_TimeClase_t timeClass, ALARM_OBJECT_T *pobjAlm, const uint8_t SystemState)
{
    brake_interval_time += timeClass.trackTime;
    fctb_brake_interval_time += timeClass.trackTime;
    int8_t mathPower = 0U;

    if (DBG_getHILDataDbgMode() != 0)
    {
        timeClass.trackTime = ((timeClass.trackTime < FLOAT_EPS) ? ADAS_DEFAULT_FRAME_RATE : timeClass.trackTime);
    }
    //hilMode = pCFG->hilMode;
    ADAS_increaseAlarmTime(timeClass.trackTime);

    if(brake_interval_time > 20)
    {
        brake_interval_time = 20;
    }

    if(fctb_brake_interval_time > 21)
    {
        fctb_brake_interval_time = 21;
    }

    gADASRadarId = pCFG->radarId;
    gadasInstallAzimuthAngle = pCFG->installAzimuthAngle;
    gAdasSpeedInkmph = 3.6 * pVDY->pVDY_DynamicInfo->vdySpeedInmps; //VDY模块传入的车速单位是m/s，在这里转换为km/h，报警模块中的参数都来源于此。

    pobjAlm->MrrSystemState = SystemState;
    pobjAlm->BSDVelSpeedDir = pVDY->pVDY_DynamicInfo->vdyDriveDirection;
    pobjAlm->BSDVelSpeedVal = gAdasSpeedInkmph; //此处单位是km/h
    pobjAlm->l_r = ((gADASRadarId % 2U) == 0) ? BSD_RADAR_LEFT : BSD_RADAR_RIGHT;
    // 获取雷达前后位置
    pobjAlm->radarPosition = (gADASRadarId == REAR_LEFT_RADAR_ID || gADASRadarId == REAR_RIGHT_RADAR_ID)
                                 ? RADAR_POSITION_REAR
                                 : RADAR_POSITION_FRONT;
    pobjAlm->centerx = 0.0 - pVDY->pVDY_DynamicInfo->vdyCurveRadius;
    pobjAlm->tracktime = timeClass.trackTime;
    ADAS_rcwIntervalCheck(pobjAlm, timeClass.trackTime);
    ADAS_vehivleRunningtime(pobjAlm, timeClass.trackTime);
    ADAS_avgFramerate(pobjAlm, timeClass.trackTime);

    // fabs(vdySpeedInmps) * fabs(vdywheelangle) * (向左为正，向右为负)，简化之后为当前，借用了符号
    pobjAlm->egoVelLateral = pVDY->pVDY_DynamicInfo->vdySpeedInmps * sinf(pVDY->pVDY_DynamicInfo->vdywheelangle * degtorad) * mathPower; // 本车对地横向速度，带符号，车辆坐标系向左为y正轴，向左为正，向右为负
    // fabs(vdySpeedInmps) * fabs(vdywheelangle) * (前进为正，倒退为负)，简化之后为当前，借用了符号
    pobjAlm->egoVelLongitudinal = pVDY->pVDY_DynamicInfo->vdySpeedInmps * fabsf(cosf(pVDY->pVDY_DynamicInfo->vdywheelangle * degtorad)); // 本车对地纵向速度，带符号，车辆坐标系向前为x正轴，向前为正，向后为负
    if(pobjAlm->l_r == BSD_RADAR_LEFT)
    {
        pobjAlm->vehiclevx = -fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps) * sinf(pVDY->pVDY_DynamicInfo->vdywheelangle*degtorad);    //横轴速度，左右雷达坐标系X方向相反，倒车工况下，
    }
    else
    {
        pobjAlm->vehiclevx = fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps) * sinf(pVDY->pVDY_DynamicInfo->vdywheelangle*degtorad);
    }

    if ((gADASRadarId == RADAR_ID_FRONT_LEFT) || (gADASRadarId == RADAR_ID_FRONT_RIGHT)){
        pobjAlm->vehiclevy = -pVDY->pVDY_DynamicInfo->vdySpeedInmps * cosf(pVDY->pVDY_DynamicInfo->vdywheelangle*degtorad);    //纵轴速度
    }else{
        pobjAlm->vehiclevy = pVDY->pVDY_DynamicInfo->vdySpeedInmps * cosf(pVDY->pVDY_DynamicInfo->vdywheelangle*degtorad);    //纵轴速度
    }

    // 右雷达转弯半径取反
    if (pobjAlm->l_r == BSD_RADAR_RIGHT)
    {
        pobjAlm->centerx = 0 - pobjAlm->centerx;
    }

    #ifndef PC_DBG_FW
    // ADAS_clrFunctionState();   // 对外接口初始化
    #endif
}


/**
 * @brief 冻结数据
 * @param pVDY 
 */
void ADAS_freezingParam(VDY_Info_t *pVDY, float trackTime)
{
    memcpy (&gadasfunctionSwitch, pVDY->pVDY_VehicleFuncSwtInfo, sizeof (VDY_vehicleFuncSwt_t));
    ADAS_IncShortLightTime(&gadasfunctionSwitch, trackTime);
    pVDY->pVDY_VehicleFuncSwtInfo = &gadasfunctionSwitch;
}

/**
 * @brief 释放已冻结数据
 * @param pVDY 
 */
void ADAS_releaseFreezingParam(VDY_Info_t *pVDY)
{
    pVDY->pVDY_VehicleFuncSwtInfo = VDY_getVehiFuncSwtPointer();
}

ADAS_RadarConfiguration_t *ADAS_getRadarCongiguration(void)
{
    RadarConfiguration.installAzimuthAngle = CFG_getRadarInstallAngle(); //获取雷达安装角
    RadarConfiguration.radarId = CFG_getRadarId(); //获取雷达Id
    //RadarConfiguration.hilMode = DBG_getHILDataDbgMode(); // 获取HIL模式

    return &RadarConfiguration;
}

/**
 * @brief ADAS功能模块初始化
 * 
 */
static void ADAS_initParameters(const ADAS_TimeClase_t timeClass)
{
    static uint8_t adasInitFlag = 0;

    if (adasInitFlag == 0U)
    {
        adasInitFlag = 1U;
        memset(&gadasFunctionState, 0, sizeof(ADAS_FunctionState_t));
        memset(&lastFunctionState, 0, sizeof(ADAS_FunctionState_t)); 
        memset(&gAdasTriggerRecord, 0, sizeof(gAdasTriggerRecord));

        gAdasTriggerRecord.simestamp = timeClass.trackTime;
    }
}
/**
 * @brief ADAS主函数，通过输入各类信息来得出报警结果
 * 
 * @param RDP_TrkObjectListAddr 跟踪点列表 
 * @param pVDY VDY车身动态数据地址 
 * @param pCFG 配置信息
 * @param pSlaveRadar 从雷达信息
 * @param FaultList 故障列表
 * @param trackTime 帧间隔（单位：s）
 */
void ADAS_runMainFunction(const RDP_TrkObjectList_t *RDP_TrkObjectListAddr,
                          const VDY_Info_t *pVDY, 
                          const ADAS_RadarConfiguration_t *pCFG,
                          const SlaveRadarWarningsStatus *pSlaveRadar,
                          const uint8_t SystemState,
                          float trackTime)
{
#ifdef _MSC_VER
    // 调用 openDebugConsole()，重定向 printf
    openDebugConsole();
#endif
    timeClass.trackTime = trackTime;
    timeClass.adasTimeCnt++;            // 不用做溢出操作， 2^32*50/1000/60/60/24/365=6.8 year

    ADAS_initParameters(timeClass);

    ADAS_updateParameters(pCFG, pVDY, timeClass, &gobjAlm, SystemState);   //更新参数

    ADAS_runPreAlmProcess(RDP_TrkObjectListAddr, pVDY, gobjPath, &gobjAlm, timeClass); // 做预处理

    ADAS_switchFuncState(&gobjAlm, pVDY, pCFG->hilMode, timeClass);   //运行状态机

    gadasFunctionState.adasAlmFlag = (uint8_t)ADAS_runAlgorithm(&gobjAlm, gobjPath, pVDY, timeClass, pSlaveRadar);  //运行算法

    ADAS_integrateSignals(pVDY, pCFG, pSlaveRadar);  //融合信号

    ADAS_ctrlVehicle(pVDY, pCFG, pSlaveRadar, timeClass.trackTime);      //控制车辆

    (void)memcpy(&lastFunctionState, &gadasFunctionState, sizeof (ADAS_FunctionState_t)); //备份数据
}
