#ifndef CHANNELANDFRAMEMONITORFORM_H
#define CHANNELANDFRAMEMONITORFORM_H

#include <QWidget>
#include <QFile>

namespace Ui {
class ChannelAndFrameMonitorForm;
}

class ChannelAndFrameMonitorTreeModel;

namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
};

namespace Analysis {
    class AnalysisManager;
}

class ChannelAndFrameMonitorForm : public QWidget
{
    Q_OBJECT

public:
    explicit ChannelAndFrameMonitorForm( Devices::Can::DeviceManager* deviceManager,
                                         Analysis::AnalysisManager *analysisManager,
                                         QWidget *parent = nullptr);
    ~ChannelAndFrameMonitorForm();
    void setLogSavePath( const QString& path );

signals:
    void clearChannelAndFrameFilter();
    void addChannelFilter( quint8 channelIdx );
    void addFrameFilter( quint8 channelIdx, quint64 frameID );

private slots:
    void on_addChannelPushButton_clicked();

    void on_addFramePushButton_clicked();

    void on_delPushButton_clicked();

    void on_startPushButton_clicked();

    void saveLog( quint8 channelIdx, quint64 frameID, quint64 lastTime,
                  quint64 currTime, quint64 diffTime, quint64 checkCycle, const QByteArray& data );

    void updateLastRecvTime( const Devices::Can::CanFrame& frame );

    void on_savePushButton_clicked();

private:
    void refreshFilter(); //刷新analysisworker的过滤器
    void openLog();
    void closeLog();

private:
    Ui::ChannelAndFrameMonitorForm *ui;
    ChannelAndFrameMonitorTreeModel* mModel;
    Devices::Can::DeviceManager* mDeviceManager;
    Analysis::AnalysisManager *mAnalysisManager;
    QString mLogSavePath;
    QFile mLogFile;
};

#endif // CHANNELANDFRAMEMONITORFORM_H
