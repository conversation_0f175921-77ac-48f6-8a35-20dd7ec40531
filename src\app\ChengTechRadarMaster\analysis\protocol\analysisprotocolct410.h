﻿#ifndef ANALYSISPROTOCOLCT410_H
#define ANALYSISPROTOCOLCT410_H

#include "analysisprotocolct.h"

namespace Analysis {
namespace Protocol {

class AnalysisProtocolCT410 : public AnalysisProtocolCT
{
    Q_OBJECT
public:
    static qint8 radarID( const Devices::Can::CanFrame &frame );

    explicit AnalysisProtocolCT410(AnalysisWorker *analysisWorker, QObject *parent = nullptr);

    bool analysisFrame(const Devices::Can::CanFrame &frame) override;

    void setChannelRadarID(bool raw600ByChannel, int *channelRadarID, int size);

signals:

private:
    bool parseFrame(const Devices::Can::CanFrame &frame);

    /** @brief 车身信息 */
    bool parseVehicleInfomation(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);
    bool parseVehicleInfomation_32(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);
    bool parseVehicleInfomation_64(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 原始点头 */
    bool parseRawTargetHeader(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 原始点 */
    bool parseRawTarget(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 跟踪点头 */
    bool parseTrackTargetHeader(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 跟踪点 */
    bool parseTrackTarget(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);
    bool parseTrackTargetVersion0(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);
    bool parseTrackTargetVersion1(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);


    bool parse0x600_VERSION_7(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);
    bool parse0x710_64_V7(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);
    bool parse0x710_64_V8(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 报警信息 */
    bool parseAlarmInfomation(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 故障信息 */
    bool parse0x4DN(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 故障信息 */
    bool parse0x4EN(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 故障信息 */
    bool parse0x604(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 结束帧 */
    bool parseEndFrame(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 雷达版本号 */
    bool parseRadarVersion(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);

    /** @brief 雷达复位 */
    void radarReset( const Devices::Can::CanFrame &frame );

    /** @brief ADC 1DFFT 2DFFT数据 */
    bool parseADC_1DFFT_2DFFT(const Devices::Can::CanFrame &frame, AnalysisData *analysisData);


    quint8 mRawID[MAX_RADAR_COUNT];
    static bool mRaw600ByChannel;
    static int mBYDHDChannelRadarID[8];                ///< BYD高阶通道-雷达ID设置
};

} // namespace Protocol
} // namespace Analysis

#endif // ANALYSISPROTOCOLCT410_H
