#ifndef BLACKBOXSIMULATORFORM_H
#define BLACKBOXSIMULATORFORM_H

#include <QWidget>
#include <QUdpSocket>

namespace Ui {
class BlackBoxSimulatorForm;
}

//暗箱模拟器
class BlackBoxSimulatorForm : public QWidget
{
    Q_OBJECT

public:
    explicit BlackBoxSimulatorForm(QWidget *parent = nullptr);
    ~BlackBoxSimulatorForm();

private slots:
    void on_pushButtonConnectTest_clicked();

private:
    Ui::BlackBoxSimulatorForm *ui;
    QUdpSocket mUdpSocket;
};

#endif // BLACKBOXSIMULATORFORM_H
