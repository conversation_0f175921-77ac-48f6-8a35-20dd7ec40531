﻿#ifndef RADARRESETWORKERFORM_H
#define RADARRESETWORKERFORM_H

#include "functions_global.h"

#include <QDialog>
#include <QTimer>
#include <QFile>

namespace Ui {
class RadarResetWorkerForm;
}

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Functions {

class RadarResetWorkerWorker;

class FUNCTIONS_EXPORT RadarResetWorkerForm : public QDialog
{
    Q_OBJECT

public:
    explicit RadarResetWorkerForm(Devices::Can::DeviceManager *deviceManager, QWidget *parent = nullptr);
    ~RadarResetWorkerForm();

private slots:
    void message(int index, const QString &msg);
    void calibrationFinished(int index);
    void calibrationStarted(int index);
    void sendOrRecvCanFrame( int index, bool bSend, quint64 id, const QString& data );

    void on_pushButtonStopCalibration_clicked();

    void on_pushButtonStartCalibration_clicked();

    void on_pushButtonCalibrationResult_clicked();

    void on_comboBoxProtocol_currentIndexChanged(int index);

private:
    void openCanLogFile();
    void closeCanLogFile();


private:
    Ui::RadarResetWorkerForm *ui;

    RadarResetWorkerWorker *mRadarResetWorkerWorker{0};
    QTimer mTimerResult[4];
    QFile mCanLogFile[4];
};

} // namespace Functions

#endif // RADARRESETWORKERFORM_H
