/**
 * @file adas_signal_integration.h
 * @brief 信号整合模块， 主要负责主从雷达的信号整合、报警等级的整合等功能。
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2023-09-27
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2023-09-27 <td>1.0     <td>shaowei     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifdef ALPSPRO_ADAS
#include "adas/customizedrequirements/adas.h"
#include "adas/generalalg/adas_manager.h"
#include "vdy/vdy_interface.h"
#elif defined(PC_DBG_FW)
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/vehicle/vdy/vdy_interface.h"
#else
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/vdy/vdy_interface.h"
#endif

/**
 * @brief 整合算法、车身和主从雷达的信号
 * 
 * @param pVDY 车身数据
 * @param pCFG 配置信息
 * @param pSlaveRadar 从雷达信息
 */
void ADAS_integrateSignals(const VDY_Info_t *pVDY, const ADAS_RadarConfiguration_t *pCFG, const SlaveRadarWarningsStatus *pSlaveRadar);

/**
 * @brief 提升轻拨转向灯等级
 * @param pfunctionSwitch 
 * @param l_r 
 * @param trackTime 
 */
void ADAS_IncShortLightTime(VDY_vehicleFuncSwt_t *pfunctionSwitch, float trackTime);
