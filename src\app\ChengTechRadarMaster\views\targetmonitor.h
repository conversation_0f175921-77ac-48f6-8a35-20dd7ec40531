﻿#ifndef TARGETMONITOR_H
#define TARGETMONITOR_H

#include <QWidget>
#include <QVector>

#include "analysis/analysisdata.h"

namespace Ui {
class TargetMonitor;
class TargetsMonitor;
}

class QCPGraph;

namespace Views {
namespace AnalysisView {

class TargetMonitor : public QWidget
{
    Q_OBJECT

public:
    explicit TargetMonitor(QWidget *parent = nullptr);
    explicit TargetMonitor(AnalysisType monitorType, QWidget *parent = nullptr);
    ~TargetMonitor();

    AnalysisType monitorType() const { return mMonitorType; }
    void setViewTargetCount(int count) { mViewTargetCount = count; }
    void clear();
    void addData(double value);

private slots:
    void on_comboBoxAnalysisType_currentIndexChanged(int index);

    void on_checkBoxLimitY_stateChanged(int arg1);

    void on_lineEditMinY_textEdited(const QString &arg1);

    void on_lineEditMaxY_textEdited(const QString &arg1);

    void on_lineEditLowerLimitY_textEdited(const QString &arg1);

    void on_lineEditUpperLimitY_textEdited(const QString &arg1);

private:
    void monitorTypeChanged();
    void setupUi();

    Ui::TargetMonitor *ui;
    QCPGraph *mGraph{0};
    QCPGraph *mGraphLowerLimitY{0};
    QCPGraph *mGraphSupperLimitY{0};
    double mLowerLimitY{-10};
    double mSupperLimitY{10};
    int mTargetCount{1};
    int mViewTargetCount{500};
    bool mLimitY{false};

    AnalysisType mMonitorType{UnknowAnalysisType};
};

class TargetsMonitor : public QWidget
{
    Q_OBJECT

public:
    explicit TargetsMonitor(QWidget *parent = nullptr);
    ~TargetsMonitor();

signals:
    void monitorTargetIDChanged(int frameType, quint16 id);

public slots:
    void monitorTarget(/*AnalysisFrameType*/int frameType, const Target &target);

protected:
    void hideEvent(QHideEvent *event) override;

private slots:
    void on_lineEditViewTargetCount_textEdited(const QString &arg1);

    void on_pushButtonClear_clicked();

    void on_comboBoxFrameType_currentIndexChanged(int index);

    void on_pushButtonAddMonitor_clicked();

private:
    Ui::TargetsMonitor *ui;

    AnalysisFrameType mFrameType{FrameTrackTarget};
    QList<TargetMonitor*> mTargetsMonitor;
};

} // namespace AnalysisDataView
} // namespace Views

#endif // TARGETMONITOR_H
