﻿#ifndef FFMPEGWIRTER_H
#define FFMPEGWIRTER_H

#include <string>
#include <mutex>

#ifdef __cplusplus
extern "C" {
#endif
#include <libavdevice/avdevice.h>
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include "libavfilter/avfilter.h"
#include "libavutil/avutil.h"
#include "libavutil/pixfmt.h"
#include "libavutil/error.h"
#include "libavutil/imgutils.h"
#include "libswscale/swscale.h"
#ifdef __cplusplus
}
#endif

class FFmpegWirter
{
public:
    FFmpegWirter();
    ~FFmpegWirter();

    bool save(const char *filename, AVStream *streamIn);
    bool stop();
    bool isSaveing() const { return mSaveing; }

    long long write(AVFrame *frame);

    int saveIndex() const { return mSaveIndex; }

private:
    bool toYUV420P(AVFrame *frame);

    long long mSaveIndex{-1};
    bool mSaveing{false};
    std::mutex mMutexSave;

    AVFormatContext *mpFormatContextOutput{0};    ///< 封装格式上下文
    AVCodecContext  *mpCodecContextOutput{0};     ///< 编解码器上下文
    AVStream *mpStreamsOutput{0};
    AVPacket *mpPacketOutput{0};
    AVFrame *mpFrameYUV420P{0};

    SwsContext* mpSwsContextYUV420P{0};
};

#endif // FFMPEGWIRTER_H
