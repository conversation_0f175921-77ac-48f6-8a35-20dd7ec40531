<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TargetMonitor</class>
 <widget class="QWidget" name="TargetMonitor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>447</width>
    <height>278</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QCustomPlot" name="customPlot" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QComboBox" name="comboBoxAnalysisType"/>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>纵轴范围:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditMinY"/>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditMaxY"/>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxLimitY">
       <property name="text">
        <string>上下限:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditLowerLimitY"/>
     </item>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditUpperLimitY"/>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
