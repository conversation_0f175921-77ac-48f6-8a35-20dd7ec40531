﻿#include "earlywarninglua.h"

#include <QFileInfo>

extern "C"
{
#include "lua-5.4.2/include/lua.h"
#include "lua-5.4.2/include/lauxlib.h"
#include "lua-5.4.2/include/lualib.h"
}

EarlyWarningLua::EarlyWarningLua(QObject *parent) : IEarlyWarningScript(parent)
{

}

bool EarlyWarningLua::loadScript(const QString &filename)
{
    QFileInfo fileInfo = QFileInfo(filename);

    //1.创建一个state
    // luaL_newstate返回一个指向堆栈的指针
    mLuaState = luaL_newstate();
    luaL_openlibs(mLuaState);
//    luaL_requiref(mLuaState, "Target", luaTarget, 0);
//    luaL_requiref(mLuaState, "VehicleData", luaVehicleData, 0);

    if (LUA_OK != luaL_dofile(mLuaState, fileInfo.absoluteFilePath().toLocal8Bit().data())) {
        qDebug() << __FUNCTION__ << __LINE__
                 << "luaL_dofile error!!!"
                 << lua_tostring(mLuaState, -1)
                 << filename;
        return false;
    }

    qDebug() << __FUNCTION__ << __LINE__ << "init Lua environment success.";
    return true;
}

bool EarlyWarningLua::unloadScript()
{
    //4.关闭state
    lua_close(mLuaState);

    return true;
}

quint16 EarlyWarningLua::vehicleEarlyWarning(int radarID, quint16 alarmTypes, VehicleData *vehicleData)
{
    if (!mItialized) {
        return Alarm_None;
    }
    // 读取函数
    lua_getglobal(mLuaState, "vehicle_warning");        // 获取函数，压入栈中 栈底(1)

    lua_pushinteger(mLuaState, radarID);
    lua_pushinteger(mLuaState, alarmTypes);

    lua_newtable(mLuaState);
    lua_pushboolean(mLuaState, vehicleData->mSwitchBSDFunction);
    lua_setfield(mLuaState, -2, "BSD");
    lua_pushboolean(mLuaState, vehicleData->mSwitchDOWFunction);
    lua_setfield(mLuaState, -2, "DOW");
    lua_pushboolean(mLuaState, vehicleData->mSwitchFCTAFunction);
    lua_setfield(mLuaState, -2, "FCTA");
    lua_pushboolean(mLuaState, vehicleData->mSwitchFCTBFunction);
    lua_setfield(mLuaState, -2, "FCTB");
    lua_pushboolean(mLuaState, true);
    lua_setfield(mLuaState, -2, "LCA");
    lua_pushboolean(mLuaState, vehicleData->mSwitchRCTAFunction);
    lua_setfield(mLuaState, -2, "RCTA");
    lua_pushboolean(mLuaState, vehicleData->mSwitchRCTBFunction);
    lua_setfield(mLuaState, -2, "RCTB");
    lua_pushboolean(mLuaState, vehicleData->mSwitchRCWFunction);
    lua_setfield(mLuaState, -2, "RCW");
    lua_pushboolean(mLuaState, vehicleData->mSwitchJAFunction);
    lua_setfield(mLuaState, -2, "JA");

    lua_newtable(mLuaState);
    lua_pushnumber(mLuaState, vehicleData->mVehicleSpeed);
    lua_setfield(mLuaState, -2, "v");
    lua_pushnumber(mLuaState, vehicleData->mGear);
    lua_setfield(mLuaState, -2, "gear");
    lua_pushnumber(mLuaState, vehicleData->mRadius);
    lua_setfield(mLuaState, -2, "radius");
    lua_pushnumber(mLuaState, vehicleData->mAcceleratorPedalActiveLevel);
    lua_setfield(mLuaState, -2, "acc_pedal");
    lua_pushboolean(mLuaState, vehicleData->mTrailerStatus);
    lua_setfield(mLuaState, -2, "trailer");
    lua_pushboolean(mLuaState, vehicleData->mESPFailStatus);
    lua_setfield(mLuaState, -2, "esp");

    if (lua_pcall(mLuaState, 4, 1, NULL)) // 调用函数，调用完成以后，会将返回值压入栈中，2表示参数个数，1表示返回结果个数。                       // 调用出错
    {
        qDebug() << __FUNCTION__ << __LINE__
                 << "lua_pcall error!!!"
                 << lua_tostring(mLuaState, -1);
        lua_pop(mLuaState, 1);
        return Alarm_None;
    }

    quint16 warningTypes = lua_tointeger(mLuaState, -1);
    return warningTypes;
}

bool EarlyWarningLua::earlyWaring(int radarID, Target *target, quint16 alarmTypes, quint16 vehicleAlarmTypes, VehicleData *vehicleData, QList<EarlyWarningData> &earlyWarningDatas)
{
    if (!mItialized) {
        return false;
    }
    // 读取函数
    lua_getglobal(mLuaState, "early_warning");        // 获取函数，压入栈中 栈底(1)

    lua_pushinteger(mLuaState, radarID);
    lua_pushinteger(mLuaState, alarmTypes);
    lua_pushinteger(mLuaState, vehicleAlarmTypes);

    lua_newtable(mLuaState);
    lua_pushnumber(mLuaState, target->mX);
    lua_setfield(mLuaState, -2, "x");
    lua_pushnumber(mLuaState, target->mY);
    lua_setfield(mLuaState, -2, "y");
    lua_pushnumber(mLuaState, target->mZ);
    lua_setfield(mLuaState, -2, "z");
    lua_pushnumber(mLuaState, target->mRange);
    lua_setfield(mLuaState, -2, "r");
    lua_pushnumber(mLuaState, target->mVx);
    lua_setfield(mLuaState, -2, "vx");
    lua_pushnumber(mLuaState, target->mVy);
    lua_setfield(mLuaState, -2, "vy");
    lua_pushnumber(mLuaState, target->mVsog);
    lua_setfield(mLuaState, -2, "vsog");
    lua_pushnumber(mLuaState, target->mTrackFrameAngle);
    lua_setfield(mLuaState, -2, "courseRngle");

    lua_newtable(mLuaState);
    lua_pushboolean(mLuaState, vehicleData->mDoorFrontLeft);
    lua_setfield(mLuaState, -2, "door_f_l");
    lua_pushboolean(mLuaState, vehicleData->mDoorFrontRight);
    lua_setfield(mLuaState, -2, "door_f_r");
    lua_pushboolean(mLuaState, vehicleData->mDoorRearLeft);
    lua_setfield(mLuaState, -2, "door_r_l");
    lua_pushboolean(mLuaState, vehicleData->mDoorRearRight);
    lua_setfield(mLuaState, -2, "door_r_r");

    lua_pushboolean(mLuaState, vehicleData->mTurnLightLeft || vehicleData->mTurnLightRight);


    if (lua_pcall(mLuaState, 6, 1, NULL)) // 调用函数，调用完成以后，会将返回值压入栈中，2表示参数个数，1表示返回结果个数。                       // 调用出错
    {
        qDebug() << __FUNCTION__ << __LINE__
                 << "lua_pcall error!!!"
                 << lua_tostring(mLuaState, -1);
        lua_pop(mLuaState, 1);
        return false;
    }

    EarlyWarningData warning;
    warning.mID = target->mID;
    bool alarmed = false;
    if (lua_istable(mLuaState, -1))        //取值输出
    {
        lua_pushnil(mLuaState);
        while (lua_next(mLuaState, -2)) {
            if (lua_istable(mLuaState, -1)) {
                lua_pushnil(mLuaState);
                warning.mType = Alarm_None;
                while (lua_next(mLuaState, -2)) {
                    switch (lua_tointeger(mLuaState, -2)) {
                    case 1:
                        warning.mType = (AlarmType)lua_tointeger(mLuaState, -1);
                        break;
                    case 2:
                        warning.mLevel = lua_tointeger(mLuaState, -1);
                        break;
                    case 3:
                        warning.mTTC = lua_tonumber(mLuaState, -1);
                        break;
                    default:
                        break;
                    }
                    lua_pop(mLuaState, 1);
                }
//                qDebug() << __FUNCTION__ << __LINE__ << warning.mID << warning.mType << warning.mLevel << warning.mTTC;
                earlyWarningDatas << warning;

                alarmed = true;
            }
            lua_pop(mLuaState, 1);
        }
    }

    return alarmed;
}
