﻿#include "analysisdataf.h"

#include <qmath.h>

namespace Parser {
namespace ParsedDataTypedef {

void TargetsF::calculateTargetMoveStatus()
{
    VehicleInfomation &vehicleInfo = mVehicleInfomation;
    switch (mParsedDataType) {
    case TargetRaw:
    {
        for (uint16_t i = 0; i < mEffectiveNumber; ++i) {
            TargetF &target = mTargets[i];

            float sog = (target.mV / cosf(target.mAngle / 180 * M_PI) + vehicleInfo.mV);
            if(fabsf(sog) < 0.8) {
                target.mMoveStatus = MoveStatusMotionlessSog;
            } else if(target.mV > 0.8) {
                target.mMoveStatus = MoveStatusOutward;
            } else if(target.mV < -0.8) {
                target.mMoveStatus = MoveStatusInward;
            } else {
                target.mMoveStatus = MoveStatusMotorialSog;
            }

//                std::cout << __FUNCTION__ << " " << __LINE__ << " "
//                          << "===:" << target.mID << " " << target.mX << " " << target.mY << " "
//                          << target.mMoveStatus << " " << target.mTAG << std::endl;
        }
    }
        break;
    case TargetTrack:
    {
        switch (mProtocolType) {
        case ARS408:                     ///< 大陆480
        {
            for (uint16_t i = 0; i < mEffectiveNumber; ++i) {
                TargetF &target = mTargets[i];
                //运动属性 0-不详 ，1来向 ，2去向 ，3静止
                if(target.mMoveAttributes == 0x1 || target.mMoveAttributes == 0x7) {
                    target.mMoveStatus = MoveStatusInward;
                } else if(target.mMoveAttributes == 0x0 || target.mMoveAttributes == 0x2) {
                    target.mMoveStatus = MoveStatusOutward;
                } else {
                    float sog = (target.mVy + vehicleInfo.mV);
                    if(fabsf(sog) < 0.8) {
                        target.mMoveStatus = MoveStatusMotionlessSog;
                    } else if(target.mV > 0.8) {
                        target.mMoveStatus = MoveStatusOutward;
                    } else if(target.mV < -0.8) {
                        target.mMoveStatus = MoveStatusInward;
                    } else {
                        target.mMoveStatus = MoveStatusMotorialSog;
                    }
                }
            }
        }
            break;
        case ARS410:                     ///< 大陆410
        {
            for (uint16_t i = 0; i < mEffectiveNumber; ++i) {
                TargetF &target = mTargets[i];
                if(target.mObjectType == 0x1 || target.mObjectType == 0x2 || target.mObjectType == 0x3) {
                    if(target.mMotionPattern == 0x3) {
                        target.mMoveStatus = MoveStatusMotorial;
                    } else if(target.mMotionPattern == 0x2) {
                        target.mMoveStatus = MoveStatusMotorialSog;
                    } else {
                        target.mMoveStatus = MoveStatusUnknow;
                    }
                } else {
                    target.mMoveStatus = MoveStatusUnknow;
                }

//                    std::cout << "===:" << target.mID << " " << target.mObjectType << " "
//                              << target.mMotionPattern << " " << target.mMoveStatus << std::endl;
            }
        }
            break;
        case BOSCH670:                   ///< 博世670
        {
            for (uint16_t i = 0; i < mEffectiveNumber; ++i) {
                TargetF &target = mTargets[i];
//                    std::cout << __FUNCTION__ << " " << __LINE__ << " " << i << " " << target.mID << std::endl;
                target.mObjectType = target.mClassification;
                if (target.mObjectType == 0x04) {
                    target.mObjectType = 0x00;
                } else {
                    target.mObjectType = 0x01;
                }
                target.mMotionPattern = target.mDynamicProperty;
                if (target.mMotionPattern != 0) {
                    target.mMotionPattern = (target.mMotionPattern == 0x4) ? 0x3 : 0x2;
                }
                if(target.mObjectType == 0x1 || target.mObjectType == 0x2 || target.mObjectType == 0x3) {
                    if(target.mMotionPattern == 0x3) {
                        target.mMoveStatus = MoveStatusMotorialSog;
                    } else if(target.mMotionPattern == 0x2) {
                        target.mMoveStatus = MoveStatusMotionlessSog;
                    } else {
                        target.mMoveStatus = MoveStatusUnknow;
                    }
                } else {
                    target.mMoveStatus = MoveStatusUnknow;
                }
            }
        }
            break;
        default:
            break;
        }
    }
        break;
    default:
        break;
    }
}

void TargetsF::calculate()
{
    calculateTargetMoveStatus();
}

}
}
