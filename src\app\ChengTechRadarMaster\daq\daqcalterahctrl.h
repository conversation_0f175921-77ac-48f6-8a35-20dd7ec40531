﻿#ifndef DAQCALTERAHCTRL_H
#define DAQCALTERAHCTRL_H

#include "daqcalterah.h"

#include <QWidget>
#include <QThread>

namespace Ui {
class DAQCalterahCtrl;
}

namespace Devices
{
namespace Can
{
class DeviceManager;
};
};

namespace Core{
    class SaveManager;
};

class DAQCalterahCtrl : public QWidget
{
    Q_OBJECT

public:
    explicit DAQCalterahCtrl( Core::SaveManager* saveMgr, Devices::Can::DeviceManager* devMgr, QWidget *parent = nullptr);
    ~DAQCalterahCtrl();

    void saveSettings();
    void loadSettings();

signals:
    void changeProtocol(int protocol);
    void startCollect(quint8 channel, quint8 collectType, quint64 frameByte, quint32 frameCnt, const QStringList& cfgFiles );
    void stopCollect();
    void startHIL( quint8 calterahProtocol, quint8 channel, quint8 collectType, quint64 frameByte, quint32 frameCnt,
                     const QStringList& cfgFiles,
                     const QString& carFile, bool bBlfFormat,
                     const QString& adcFile, bool bZeerFormat );
    void startOrStopTcpServer( const QString& ip, quint32 port, bool anlyIP);
    void HILNextFrame();

private slots:
    void serverStateChanged(bool opened);

    void clientConnected(unsigned long addr, unsigned short port, bool connected);

    void collectStart();
    void collectFinished(/*CollectState*/int state);

    void showMsg( const QString& msg );

    void on_selCarFilePushButton_clicked();
    void on_serverPushButton_clicked();


    void on_selADCFilePushButton_clicked();

    void on_HILPushButton_clicked();
    void HILFinished();

    void on_selCfgFilepushButton_clicked();

    void on_singleFrameCheckBox_stateChanged(int arg1);

    void on_nextPushButton_clicked();

    void on_protocolComboBox_currentIndexChanged(int index);

private:
    bool checkFrameParam();
    bool checkHILParam();
    bool checkCollectParam();

private:
    Ui::DAQCalterahCtrl *ui;

private:
    DAQ::DAQCalterah* mDAQ;
    QThread* mThread;
    Core::SaveManager* mSaveMgr;
    Devices::Can::DeviceManager* mDeviceManager;
};

#endif // DAQCALTERAHCTRL_H
