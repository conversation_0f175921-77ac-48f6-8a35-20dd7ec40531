﻿#ifndef INTERPOLATIONBYDHO_H
#define INTERPOLATIONBYDHO_H

#include "iinterpolation.h"

namespace Analysis {

class InterpolationBYDHO : public IInterpolation
{
public:
    static bool isTrackFrame(int radarID, int channelRadarID[5], const Devices::Can::CanFrame &frame);
    static void saveTimestamps(Devices::Can::FrameTimestamps *timestamps, const Devices::Can::CanFrame &frame);

    InterpolationBYDHO(AnalysisWorker *analysisWorker);

    int encodeFrame(int radarID, Devices::Can::FrameTimestamp *timestamp, RDP_TrkObjectInfo_t *outputObjList, int16_t trkValidNum, uint8_t msgCounter, Devices::Can::stCanTxMsg *frameArray) override;
    void setChannelRadarIDBYDHO(int *channelRadarID, int size) override;

    bool decode_CR_FL_0x659(quint8 radarID, Devices::Can::stCanTxMsg *frame );

    void canFrame(int radarID, const Devices::Can::CanFrame &frame) override;

    int mBYDHDChannelRadarID[5]{4, 5, 6, 7, 0};                ///< BYD高阶通道-雷达ID设置
};

} // namespace Analysis

#endif // INTERPOLATIONBYDHO_H
