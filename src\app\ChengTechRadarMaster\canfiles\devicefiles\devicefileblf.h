﻿#ifndef DEVICEFILEBLF_H
#define DEVICEFILEBLF_H

#include <string>
#include <vector>
#include <chrono>

#include "idevicefile.h"
#include <CAN/VECTOR_BLF/include/binlog.h>

namespace Devices {
	namespace Can {
		class DeviceFileBLF : 
			public IDeviceFile
		{
		public:
			explicit DeviceFileBLF(CANDeviceFile *device = 0);

			bool openFile() override;
			bool closeFile() override;
			bool readData() override;
            bool writeData(const CanFrame::Ptr pFrame) override;

		private:
			bool writeBLFHead(uint64_t timestamp);

			HANDLE  mHBLF{ INVALID_HANDLE_VALUE };
		};
	}
}

#endif // DEVICEFILEBLF_H
