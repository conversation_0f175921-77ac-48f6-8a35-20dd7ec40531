﻿#include "ffmpeginitializer.h"


#ifdef __cplusplus
extern "C" {
#endif
#include <libavdevice/avdevice.h>
#include <libavformat/avformat.h>
#ifdef __cplusplus
}
#endif
static bool gInitialized{false};
static int  gInitializeCount{0};
void FFmpegInitializer::initFFmpeg()
{
    gInitializeCount++;
    if (gInitialized) {
        return;
    }
    // 1. 注册音视频设备
    avformat_network_init();
    avdevice_register_all();

    gInitialized = true;
}

void FFmpegInitializer::releaseFFmpeg()
{
    gInitializeCount--;
    if (gInitializeCount || gInitialized) {
        return;
    }
}
