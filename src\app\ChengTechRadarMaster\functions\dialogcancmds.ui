<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogCanCmds</class>
 <widget class="QDialog" name="DialogCanCmds">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1018</width>
    <height>860</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>命令行</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <layout class="QGridLayout" name="gridLayout_21">
   <item row="0" column="0">
    <layout class="QGridLayout" name="gridLayout_19">
     <item row="0" column="0">
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <widget class="QLabel" name="label_24">
         <property name="font">
          <font>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Cmd Profile</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QCheckBox" name="cbSelProf0">
         <property name="text">
          <string>Profile0</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QCheckBox" name="cbSelProf1">
         <property name="text">
          <string>Profile1</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QCheckBox" name="cbSelProf2">
         <property name="text">
          <string>Profile2</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="0" column="6">
        <widget class="QLabel" name="label_25">
         <property name="text">
          <string>RD地址:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="7">
        <widget class="QLineEdit" name="lineEdit_radar_addr"/>
       </item>
       <item row="0" column="4">
        <widget class="QCheckBox" name="cbSelProf3">
         <property name="text">
          <string>Profile3</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="0" column="5">
        <widget class="QComboBox" name="comboBoxChannelIndex">
         <item>
          <property name="text">
           <string>CAN 0</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 1</string>
          </property>
         </item>
        </widget>
       </item>
      </layout>
     </item>
     <item row="2" column="0">
      <layout class="QGridLayout" name="gridLayout_18">
       <item row="0" column="0">
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="1">
        <layout class="QGridLayout" name="gridLayout_3">
         <item row="0" column="0">
          <widget class="QPushButton" name="btnSendCmd">
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>发送命令</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QPushButton" name="btnWriteFlash">
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>写FLASH</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QPushButton" name="btnClearFlash">
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>清除flash</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item row="1" column="0">
      <layout class="QGridLayout" name="gridLayout_13">
       <item row="0" column="0">
        <widget class="QTextEdit" name="textCmds">
         <property name="acceptRichText">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QTextEdit" name="textCmdAck">
         <property name="readOnly">
          <bool>false</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item row="0" column="1">
    <layout class="QGridLayout" name="gridLayout_20">
     <item row="1" column="2">
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="1" column="1">
      <widget class="QFrame" name="frame_2">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout_23">
        <item row="2" column="0">
         <widget class="QPushButton" name="pushButton_reset">
          <property name="text">
           <string>  RESET</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QPushButton" name="pushButton_NoSR">
          <property name="text">
           <string>  NoSR</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QPushButton" name="pushButton_SR">
          <property name="text">
           <string>SR</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QFrame" name="frame">
       <property name="frameShape">
        <enum>QFrame::Box</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Plain</enum>
       </property>
       <property name="lineWidth">
        <number>1</number>
       </property>
       <property name="midLineWidth">
        <number>0</number>
       </property>
       <layout class="QGridLayout" name="gridLayout">
        <item row="5" column="0">
         <widget class="QPushButton" name="pushButton_temp">
          <property name="text">
           <string>读取温度</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_26">
          <property name="text">
           <string> 类型</string>
          </property>
         </widget>
        </item>
        <item row="5" column="1">
         <widget class="QPushButton" name="pushButtonClearBuf">
          <property name="text">
           <string>清缓存</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QComboBox" name="comboBoxType">
          <item>
           <property name="text">
            <string>Alps</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>AlpsPro</string>
           </property>
          </item>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QPushButton" name="pushButton_outCfg">
          <property name="text">
           <string>退出命令模式</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QPushButton" name="pushButton_entryCfg">
          <property name="text">
           <string>进入命令模式</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <layout class="QGridLayout" name="gridLayout_14">
          <item row="0" column="0">
           <widget class="QLabel" name="label_3">
            <property name="font">
             <font>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Profile</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QComboBox" name="cbProfile">
            <item>
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>1</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>2</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>3</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
        <item row="2" column="1">
         <widget class="QPushButton" name="btnReadCfg">
          <property name="text">
           <string>读取配置</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QComboBox" name="comboBoxProtocol">
          <item>
           <property name="text">
            <string>0x110</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>0x750</string>
           </property>
          </item>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_31">
          <property name="text">
           <string>协议</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item row="2" column="0" colspan="3">
      <widget class="QTabWidget" name="tab_profile">
       <property name="font">
        <font>
         <weight>50</weight>
         <bold>false</bold>
        </font>
       </property>
       <property name="tabShape">
        <enum>QTabWidget::Triangular</enum>
       </property>
       <property name="currentIndex">
        <number>5</number>
       </property>
       <property name="elideMode">
        <enum>Qt::ElideNone</enum>
       </property>
       <property name="tabBarAutoHide">
        <bool>false</bool>
       </property>
       <widget class="QWidget" name="tab">
        <attribute name="title">
         <string> profile_0</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_5">
         <item row="0" column="0">
          <widget class="QScrollArea" name="scrollArea_2">
           <property name="widgetResizable">
            <bool>true</bool>
           </property>
           <widget class="QWidget" name="scrollAreaWidgetContents_2">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>0</y>
              <width>349</width>
              <height>636</height>
             </rect>
            </property>
            <layout class="QGridLayout" name="gridLayout_9">
             <item row="0" column="0">
              <layout class="QGridLayout" name="tab_0_layout"/>
             </item>
             <item row="1" column="0">
              <spacer name="verticalSpacer">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_2">
        <attribute name="title">
         <string>1</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_6">
         <item row="0" column="0">
          <widget class="QScrollArea" name="scrollArea_3">
           <property name="widgetResizable">
            <bool>true</bool>
           </property>
           <widget class="QWidget" name="scrollAreaWidgetContents_3">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>0</y>
              <width>349</width>
              <height>636</height>
             </rect>
            </property>
            <layout class="QGridLayout" name="gridLayout_10">
             <item row="0" column="0">
              <layout class="QGridLayout" name="tab_1_layout"/>
             </item>
             <item row="1" column="0">
              <spacer name="verticalSpacer_2">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_3">
        <attribute name="title">
         <string>2</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_8">
         <item row="0" column="0">
          <widget class="QScrollArea" name="scrollArea_4">
           <property name="widgetResizable">
            <bool>true</bool>
           </property>
           <widget class="QWidget" name="scrollAreaWidgetContents_4">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>0</y>
              <width>349</width>
              <height>636</height>
             </rect>
            </property>
            <layout class="QGridLayout" name="gridLayout_11">
             <item row="0" column="0">
              <layout class="QGridLayout" name="tab_2_layout"/>
             </item>
             <item row="1" column="0">
              <spacer name="verticalSpacer_3">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_4">
        <attribute name="title">
         <string>3</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_7">
         <item row="0" column="0">
          <widget class="QScrollArea" name="scrollArea">
           <property name="widgetResizable">
            <bool>true</bool>
           </property>
           <widget class="QWidget" name="scrollAreaWidgetContents">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>0</y>
              <width>349</width>
              <height>636</height>
             </rect>
            </property>
            <layout class="QGridLayout" name="gridLayout_12">
             <item row="0" column="0">
              <layout class="QGridLayout" name="tab_3_layout"/>
             </item>
             <item row="1" column="0">
              <spacer name="verticalSpacer_4">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_5">
        <attribute name="title">
         <string>其他</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_15">
         <item row="0" column="0">
          <widget class="QScrollArea" name="scrollArea_5">
           <property name="widgetResizable">
            <bool>true</bool>
           </property>
           <widget class="QWidget" name="scrollAreaWidgetContents_5">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>0</y>
              <width>335</width>
              <height>791</height>
             </rect>
            </property>
            <layout class="QGridLayout" name="gridLayout_16">
             <item row="0" column="0">
              <layout class="QGridLayout" name="gridLayout_4">
               <item row="0" column="0">
                <widget class="QLabel" name="label_2">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>CFAR pk threshold</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="1" column="0" colspan="2">
                <widget class="QLineEdit" name="leCfarPkThrd"/>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_4">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>CFAR recwin mask</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="3" column="0" colspan="2">
                <widget class="QLineEdit" name="leCfarRecwinMask"/>
               </item>
               <item row="4" column="0">
                <widget class="QLabel" name="label_5">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>CFAR algo type</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QLineEdit" name="leCfarAlgoType"/>
               </item>
               <item row="5" column="0">
                <widget class="QLabel" name="label_6">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>CFAR CA Alpha</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="6" column="0" colspan="2">
                <widget class="QLineEdit" name="leCfarCaAlpha"/>
               </item>
               <item row="7" column="0">
                <widget class="QLabel" name="label_7">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>CFAR CA N</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="8" column="0" colspan="2">
                <widget class="QLineEdit" name="leCfarCaN"/>
               </item>
               <item row="9" column="0">
                <widget class="QLabel" name="label">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DOA Mode</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="9" column="1">
                <widget class="QLineEdit" name="leDoaMode"/>
               </item>
               <item row="10" column="0">
                <widget class="QLabel" name="label_8">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DOA Num Groups</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="10" column="1">
                <widget class="QLineEdit" name="leDoaNumGroups"/>
               </item>
               <item row="11" column="0">
                <widget class="QLabel" name="label_9">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DOA FFT Mux</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="12" column="0" colspan="2">
                <widget class="QLineEdit" name="leDoaFftMux"/>
               </item>
               <item row="13" column="0">
                <widget class="QLabel" name="label_10">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>CB DOA FFT Mux</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="14" column="0" colspan="2">
                <widget class="QLineEdit" name="leDoaCbFftMux"/>
               </item>
               <item row="15" column="0">
                <widget class="QLabel" name="label_11">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DOA Method</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="15" column="1">
                <widget class="QLineEdit" name="leDoaMethod"/>
               </item>
               <item row="16" column="0">
                <widget class="QLabel" name="label_12">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DOA Smp Space</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="16" column="1">
                <widget class="QLineEdit" name="leDoaSmpSpace"/>
               </item>
               <item row="17" column="0">
                <widget class="QLabel" name="label_13">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Doa Max Obj</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="17" column="1">
                <widget class="QLineEdit" name="leDoaMaxObj"/>
               </item>
               <item row="18" column="0">
                <widget class="QLabel" name="label_14">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Bfm AZ Left</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="18" column="1">
                <widget class="QLineEdit" name="leBfmAzLeft"/>
               </item>
               <item row="19" column="0">
                <widget class="QLabel" name="label_15">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Bfm AZ Right</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="19" column="1">
                <widget class="QLineEdit" name="leBfmAzRight"/>
               </item>
               <item row="20" column="0">
                <widget class="QLabel" name="label_16">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Bfm Ev UP</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="20" column="1">
                <widget class="QLineEdit" name="leBfmEvUp"/>
               </item>
               <item row="21" column="0">
                <widget class="QLabel" name="label_17">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Bfm Ev Down</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="21" column="1">
                <widget class="QLineEdit" name="leBfmEvDown"/>
               </item>
               <item row="22" column="0">
                <widget class="QLabel" name="label_18">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DML 2dsch Start </string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="22" column="1">
                <widget class="QLineEdit" name="leDml2dschStart"/>
               </item>
               <item row="23" column="0">
                <widget class="QLabel" name="label_19">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DML 2dsch Step</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="23" column="1">
                <widget class="QLineEdit" name="leDml2dschStep"/>
               </item>
               <item row="24" column="0">
                <widget class="QLabel" name="label_20">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DML 2dsch End</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="24" column="1">
                <widget class="QLineEdit" name="leDml2dschEnd"/>
               </item>
               <item row="25" column="0">
                <widget class="QLabel" name="label_21">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DML Extra 1D</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="25" column="1">
                <widget class="QLineEdit" name="leDmlExtra1d"/>
               </item>
               <item row="26" column="0">
                <widget class="QLabel" name="label_22">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DML P1P2</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="26" column="1">
                <widget class="QLineEdit" name="leDmlP1P2"/>
               </item>
               <item row="27" column="0">
                <widget class="QLabel" name="label_23">
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>DML Respwr Coef</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="28" column="0" colspan="2">
                <widget class="QLineEdit" name="leDmlRespCoef"/>
               </item>
              </layout>
             </item>
             <item row="1" column="0">
              <layout class="QGridLayout" name="gridLayout_17">
               <item row="0" column="0">
                <widget class="QPushButton" name="btnClearCfgText">
                 <property name="text">
                  <string>清除配置</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QPushButton" name="btnConfig">
                 <property name="text">
                  <string>发送配置</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_6">
        <attribute name="title">
         <string> DBF因子</string>
        </attribute>
        <widget class="QScrollArea" name="scrollArea_6">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>313</width>
           <height>218</height>
          </rect>
         </property>
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <widget class="QWidget" name="scrollAreaWidgetContents_6">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>311</width>
            <height>216</height>
           </rect>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <item>
              <widget class="QLabel" name="label_27">
               <property name="text">
                <string>收发方向图：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditAziDecay"/>
             </item>
             <item>
              <widget class="QPushButton" name="btnSelAziDecay">
               <property name="text">
                <string>...</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <widget class="QLabel" name="label_28">
               <property name="text">
                <string>天线间距  ：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditAntPos"/>
             </item>
             <item>
              <widget class="QPushButton" name="ptnSelAntPos">
               <property name="text">
                <string>...</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item>
              <widget class="QLabel" name="label_29">
               <property name="text">
                <string>天线相位差：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditAntComps"/>
             </item>
             <item>
              <widget class="QPushButton" name="ptnSelAntComps">
               <property name="text">
                <string>...</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <item>
              <widget class="QLabel" name="label_30">
               <property name="text">
                <string>DBF因子   ：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditDBFCoef"/>
             </item>
             <item>
              <widget class="QPushButton" name="ptnSelDBFCoef">
               <property name="text">
                <string>...</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <item>
              <spacer name="horizontalSpacer_3">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="btnWriteCsv">
               <property name="text">
                <string> 写入</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnReadCsv">
               <property name="text">
                <string>读取</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <spacer name="verticalSpacer_5">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </widget>
       </widget>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
