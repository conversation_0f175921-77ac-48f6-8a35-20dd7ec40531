﻿#include "statisticsdialog.h"
#include "ui_statisticsdialog.h"
#include <QDebug>
#include <QVariant>
#include <qmath.h>
#include <QTime>

statisticsDialog::statisticsDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::statisticsDialog)
{
    ui->setupUi(this);
}

statisticsDialog::~statisticsDialog()
{
    delete ui;
}

QVariant statisticsDialog::getSettings() const
{
    QMap<QString, QVariant> config;
    config["mMaxVel"]   = gStatisData.mMaxVel;
    config["mMinVel"]   = gStatisData.mMinVel;
    config["mMaxLng"]   = gStatisData.mMaxLng;
    config["mMinLng"]   = gStatisData.mMinLng;
    config["mMaxLat"]   = gStatisData.mMaxLat;
    config["mMinLat"]   = gStatisData.mMinLat;
    config["mWinSize"]  = gStatisData.mDeteWinSize;
    config["MatchMaxCost"]  = gStatisData.mMatchMaxCost;
    return config;
}

bool statisticsDialog::setSettings(const QVariant &settings)
{
    QMap<QString, QVariant> config = settings.toMap();
    ui->lineEdit_maxVel->setText(config["mMaxVel"].toString());
    ui->lineEdit_minVel->setText(config["mMinVel"].toString());
    ui->lineEdit_maxLng->setText(config["mMaxLng"].toString());
    ui->lineEdit_minLng->setText(config["mMinLng"].toString());
    ui->lineEdit_maxLat->setText(config["mMaxLat"].toString());
    ui->lineEdit_minLat->setText(config["mMinLat"].toString());
    ui->lineEdit_WinSize->setText(config["mWinSize"].toString());
    ui->lineEditMaxCost->setText(config["MatchMaxCost"].toString());
    return true;
}

void statisticsDialog::settingsApply()
{
    on_pushButton_Set_clicked();
}

void statisticsDialog::calculateFinished(quint8 radarID, const AnalysisData &analysisData)
{
    calcDeteStatis(analysisData,FrameRawTarget);
    mDeteTime = QDateTime::currentDateTime().toMSecsSinceEpoch() - mDeteStartTime;
    ui->label_statisticsTime->setText(QDateTime::fromMSecsSinceEpoch(mDeteTime).toString("mm:ss.zzz"));
    ui->label_deteProb->setText(QString::number(mDeteProb*100,'f',2)+"%");
    ui->label_statisticsCnt->setText(QString::number(mDeteAllCnt));
    ui->label_WinMask->setText(QString::number(mWinMask & (uint32_t)(qPow(2,mWinLength)-1),16).toUpper() + "  " + QString::number(mMatchCnt) +"/"+ QString::number(mWinLength));

    //目标信息 mDeteObjIdx
    ui->label_idx->setText("obj Id = " + QString::number(radarID) + "/" +QString::number(mDeteObjIdx));
    if(mDeteObjIdx >= 0 && mDeteObjIdx < 256)
    {
        ui->label_Range->setText("obj Range = " + QString::number(analysisData.mTargets[FrameRawTarget].mTargets[mDeteObjIdx].mRange));
        ui->label_Vel->setText("obj Speed = " + QString::number(analysisData.mTargets[FrameRawTarget].mTargets[mDeteObjIdx].mV));
        ui->label_Angle->setText("obj Angle = " + QString::number(analysisData.mTargets[FrameRawTarget].mTargets[mDeteObjIdx].mAngle));
        ui->label_x->setText("obj X = " + QString::number(analysisData.mTargets[FrameRawTarget].mTargets[mDeteObjIdx].mX));
        ui->label_y->setText("obj Y = " + QString::number(analysisData.mTargets[FrameRawTarget].mTargets[mDeteObjIdx].mY));
        ui->label_SNR->setText("obj SNR = " + QString::number(analysisData.mTargets[FrameRawTarget].mTargets[mDeteObjIdx].mSNR) + "/"
                                            + QString::number(mMatchCost,'f',2));
    }
}

void statisticsDialog::on_pushButton_Set_clicked()
{
    gStatisData.mMaxVel = ui->lineEdit_maxVel->text().toFloat();
    gStatisData.mMinVel = ui->lineEdit_minVel->text().toFloat();
    gStatisData.mMaxLng = ui->lineEdit_maxLng->text().toFloat();
    gStatisData.mMinLng = ui->lineEdit_minLng->text().toFloat();
    gStatisData.mMaxLat = ui->lineEdit_maxLat->text().toFloat();
    gStatisData.mMinLat = ui->lineEdit_minLat->text().toFloat();
    gStatisData.mDeteWinSize = ui->lineEdit_WinSize->text().toInt();
    gStatisData.mMatchMaxCost = ui->lineEditMaxCost->text().toInt();
    emit sigApply(gStatisData);
}

void statisticsDialog::on_pushButtonStartSt_clicked()
{
    on_pushButton_Set_clicked();
    mDeteProb = {0.0};
    mDeteAllCnt = {0};
    mDeteTime = {0};
    mWinMask = {0};
    mMatchCnt = {0};
    mWinLength = {0};
    mDeteObjIdx = {-1};
    mLastDeteObjIdx = {-1};
    mDeteStartTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    qDebug()<<__FUNCTIONW__<<"==================="<<mDeteStartTime;
}

//统计策略
/*
1、在x、y、v范围内的目标
2、cost在最大值范围内【除非上一帧没有目标】
满足1、2则认为是匹配上的目标
*/
void statisticsDialog::calcDeteStatis(const AnalysisData &analysisData, AnalysisFrameType frameType)
{
    if(FrameRawTarget != frameType)
    {
        return;
    }

    const Targets &targets = analysisData.mTargets[frameType];
    const Target *target = targets.mTargets;
    bool matchFlag = false;

    mDeteAllCnt++;
    mDeteObjIdx = -1;
    for (int j = 0; j < targets.mTargetCount; ++j, ++target)
    {
        if (target->mValid
                && target->mV > analysisData.mDeteStatisData.mMinVel
                && target->mV < analysisData.mDeteStatisData.mMaxVel
                && target->mX > analysisData.mDeteStatisData.mMinLat
                && target->mX < analysisData.mDeteStatisData.mMaxLat
                && target->mY > analysisData.mDeteStatisData.mMinLng
                && target->mY < analysisData.mDeteStatisData.mMaxLng)
        {
            //计算COST,如果上一帧没有值，则忽略
            if(mLastDeteObjIdx == -1)
            {
                mMatchCost = 0;
            }
            else
            {
                mMatchCost = qPow(target->mRange - mLastTarget.mRange, 2)
                           + qPow(target->mY - mLastTarget.mY, 2)
                           + qPow(target->mY - mLastTarget.mY, 2);
            }

            //判断下匹配的COST是否有异常，目的是范围比较大时，剔除异常值
            if(mMatchCost < analysisData.mDeteStatisData.mMatchMaxCost)
            {
                matchFlag = true;
                mDeteObjIdx = j;
                break;
            }
        }
    }
    mWinMask = mWinMask << 1;
    mWinMask = matchFlag ? (mWinMask | 1) : (mWinMask | 0);

    //记录目标
    if(matchFlag)
    {
        memcpy(&mLastTarget,&targets.mTargets[mDeteObjIdx],sizeof(mLastTarget));
    }
    mLastDeteObjIdx = mDeteObjIdx;

    //计算滑窗内的概率
    mWinLength = mDeteAllCnt < analysisData.mDeteStatisData.mDeteWinSize ? mDeteAllCnt : analysisData.mDeteStatisData.mDeteWinSize;
    uint16_t winCalcLength = mWinLength;
    mMatchCnt = 0;
    while(winCalcLength > 0)
    {
        if(mWinMask & (1 << (winCalcLength - 1)))
        {
            mMatchCnt++;
        }
        winCalcLength--;
    }
    mDeteProb = (((double)mMatchCnt)/mWinLength);
}
