﻿/**
 * @file rdp_track_listlib.c
 * @brief 
 * <AUTHOR> Wei (<EMAIL>)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

/**************************************************************************
 *************************** Include Files ********************************
 **************************************************************************/

#include <stdint.h>
#ifndef PC_DBG_FW
#include "rdp_track_listlib.h"
#else
#include "alg/track/rdp_track_listlib.h"
#endif

/**************************************************************************
 ***************************** ListLib Functions **************************
 **************************************************************************/

/** 
 *  @b Description
 *  @n  
 *      The function is called to initialize the list.
 *
 *  @param[in]  list
 *      List to be initialized
 *
 *  @retval
 *      Not Applicable
 */
void gtrack_listInit (GTrack_ListObj* list)
{
    /* Initialize the fields in the list object. */
    list->count = 0;
    list->begin = 0;
    list->end   = 0;
}

/** 
 *  @b Description
 *  @n  
 *      The function is called to check if the list is empty or not
 *
 *  @param[in]  list
 *      List to be checked.
 *
 *  @retval
 *      1	-	List is empty
 *  @retval
 *      0	-	List
 */
int32_t gtrack_isListEmpty (GTrack_ListObj* list)
{
    if (list->count == 0)
        return 1;
    return 0;
}

/** 
 *  @b Description
 *  @n  
 *      The function is called to add an element to the end of the list.
 *
 *  @param[in]  list
 *      List from where the element is to be added
 *  @param[in]  elem
 *      Element to be added to the end of the list
 *
 *  @retval
 *      Not Applicable.
 */
void gtrack_listEnqueue (GTrack_ListObj *list, GTrack_ListElem *elem)
{
	if(list->begin == 0) 
	{
		elem->prev = 0;
		elem->next = 0;

		list->begin = elem;
		list->end = elem;
	}
	else
	{
		elem->prev = list->end;
		elem->next = 0;

		list->end->next = elem;
		list->end = elem;
	}
    list->count++;
}
/** 
 *  @b Description
 *  @n  
 *      The function is called to insert an element to the list by order.
 *
 *  @param[in]  list
 *      List from where the element is to be added
 *  @param[in]  elem
 *      Element to be added to the end of the list
 *
 *  @retval
 *      Not Applicable.
 */
void gtrack_listInsert (GTrack_ListObj *list, GTrack_ListElem *elem)
{
    GTrack_ListElem *elemCurrent;
	if(list->begin == 0) 
	{
		elem->prev = 0;
		elem->next = 0;

		list->begin = elem;
		list->end = elem;
	}
	else if(elem->data < list->begin->data)
    {
        elem->prev = 0;
        elem->next = list->begin;

        list->begin->prev = elem;
        list->begin = elem;
        
    }else   
	{
	    elemCurrent = list->begin;
	    while(elemCurrent->next!=0&&elemCurrent->next->data<elem->data)
            elemCurrent = elemCurrent->next;
        elem->next = elemCurrent->next;
        elem->prev = elemCurrent;

        if(elemCurrent->next)
            elemCurrent->next->prev = elem;
        else
            list->end = elem;
        elemCurrent->next = elem;
	}   
    list->count++;
}

/** 
 *  @b Description
 *  @n  
 *      The function is called to dequeue an element from the head of the list.
 *
 *  @param[in]  list
 *      List from where the element is to be removed
 *
 *  @retval
 *      Head of the list (NULL if the list was empty)
 */
GTrack_ListElem* gtrack_listDequeue (GTrack_ListObj* list)
{
	GTrack_ListElem *elem;

	if(list->begin == 0)
		return 0;
	
	elem = list->begin;
	list->begin = elem->next;
	list->count--;

	if(list->begin == 0)
		list->end = 0;

	elem->next = 0;
	elem->prev = 0;

	return elem;
}

/** 
 *  @b Description
 *  @n  
 *      The function is called to get the number of elements in the list.
 *
 *  @param[in]  list
 *      List for which the number of elements are required.
 *
 *  @retval
 *      Counter
 */
uint32_t gtrack_listGetCount (GTrack_ListObj* list)
{
    return list->count;
}
/** 
 *  @b Description
 *  @n  
 *      The function is called to get the first elements in the list.
 *
 *  @param[in]  list
 *      List for which the number of elements are required.
 *
 *  @retval
 *      First Element
 */
GTrack_ListElem* gtrack_listGetFirst (GTrack_ListObj* list)
{
	return list->begin;
}
/** 
 *  @b Description
 *  @n  
 *      The function is called to get the next elements in the list.
 *
 *  @param[in]  elem
 *      List for which the number of elements are required.
 *
 *  @retval
 *      Next Element
 */
GTrack_ListElem* gtrack_listGetNext (GTrack_ListElem* elem)
{
	return elem->next;
}

/**
 *  @b Description
 *  @n  
 *      The function is called to remove the specific element from 
 *      the list.
 *
 *  @param[in]  list
 *      List from which the element is to be removed.
 *  @param[in]  elem
 *      The element to be removed.
 *
 *  @retval
 *      Success     -   0
 *  @retval
 *      Error       -   <0
 */
int32_t gtrack_listRemoveElement (GTrack_ListObj* list, GTrack_ListElem* elem)
{
	if(elem->prev == 0)
	{
		if(list->begin != elem)
			return -1;

		if(elem->next == 0)
		{
			/* Next could be NULL only if that is the last element */
			if(list->end != elem)
				return -1;

			if(list->count != 1)
				return -1;

			/* That was the only element, list is emty now */
			list->begin = 0;
			list->end = 0;
			list->count = 0;
			return 0;
		}

		if(elem->next->prev != elem)
			return -1;

		/* That was the first element, update the list begin */
		list->begin = elem->next;
		elem->next->prev = 0;
		list->count--;
		return 0;
	}

	if(elem->prev->next != elem)
			return -1;

	if(elem->next == 0)
	{
		/* Next could be NULL only if that is the last element */
		if(list->end != elem)
			return -1;

		/* Yes, it is valid last element */
		elem->prev->next = 0;
		list->end = elem->prev;
		list->count--;
		return  0;
	}
	
	if(elem->next->prev != elem)
		return -1;

	elem->prev->next = elem->next;
	elem->next->prev = elem->prev;
	list->count--;
	return  0;
}

