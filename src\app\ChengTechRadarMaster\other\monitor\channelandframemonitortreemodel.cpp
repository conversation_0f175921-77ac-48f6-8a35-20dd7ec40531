﻿#include "channelandframemonitortreemodel.h"
#include "devices/canframe.h"


#include <QDebug>
#include <QTimerEvent>
#include <QtConcurrent>
#include <QDomDocument>
#include <QMessageBox>

#define XML_FILE_NAME "./ChannelAndFrameMonitor.xml"

ChannelAndFrameMonitorTreeItem::ChannelAndFrameMonitorTreeItem(ChannelAndFrameMonitorTreeItem *parent, ChannelAndFrameMonitorTreeItem::TYPE type)
{
    mParent = parent;
    mType = type;
    if( mParent ){
        mRow = mParent->childCount();
        mParent->addChild( this );
    }
}

ChannelAndFrameMonitorTreeItem::~ChannelAndFrameMonitorTreeItem()
{
    monitorFrameInfo* pFram = (monitorFrameInfo*)mPtr;
    monitorChannelInfo* pChannel = (monitorChannelInfo*)mPtr;
    switch( mType ){
    case TREEITEMTYPE_CHANNEL:
        //delete pFram;
        delete pChannel;
        break;
    case TREEITEMTYPE_FRAME:
        //delete pChannel;
        delete pFram;
        break;
    default:
//            delete mPtr;
        break;
    }

    for( int i=0; i<mChilds.size(); i++ ){
        delete mChilds[i];
    }
    mChilds.clear();
}

void ChannelAndFrameMonitorTreeItem::addChild(ChannelAndFrameMonitorTreeItem *child)
{
    mChilds.push_back( child );
}

ChannelAndFrameMonitorTreeItem *ChannelAndFrameMonitorTreeItem::child(int row)
{
    if( row >= mChilds.size() || row < 0 ){
        return NULL;
    }
    return mChilds[row];
}

void ChannelAndFrameMonitorTreeItem::deleteChild(int row)
{
    ChannelAndFrameMonitorTreeItem* pChild = child( row );
    if( !pChild ){
        return;
    }
    delete pChild;

    //调整行数
    QList<ChannelAndFrameMonitorTreeItem*>::iterator it = mChilds.begin() + row;
    for( ; it!=mChilds.end(); it++ ){
        (*it)->mRow -= 1;
    }

    //删除子节点
    mChilds.erase( mChilds.begin() + row );
}

QVariant ChannelAndFrameMonitorTreeItem::data(int column)
{
    switch ( mType ) {
    case TREEITEMTYPE_CHANNEL:
        {
            monitorChannelInfo* pInfo = (monitorChannelInfo*)mPtr;
            switch ( column ) {
            case 0:
                return QVariant( pInfo->channelIndex );
            case 1:
                return QVariant( pInfo->bContinue );
            default:
                return QVariant();
            }
        }
        break;
    case TREEITEMTYPE_FRAME:
        {
            monitorFrameInfo* pInfo = (monitorFrameInfo*)mPtr;
            switch ( column ) {
            case 0:
                //return QVariant( pInfo->frameID );
                return QVariant( QString("0x%1").arg( QString::number( pInfo->frameID, 16 ) ) );
            case 1:
                return QVariant( pInfo->bContinue );
            case 2:
                return QVariant( pInfo->monitorCycle );
            case 3:
                return QVariant( pInfo->recvCount );
            case 4:
                return QVariant( pInfo->errorCount );
            case 5:
                return QVariant( pInfo->recvCycle );
            }
        }
        break;
    default:
        return QVariant();
    }
    return QVariant();
}

void ChannelAndFrameMonitorTreeItem::setChannelData(quint8 channelIndex, bool bContinue)
{
    if( TREEITEMTYPE_CHANNEL != mType ){
        return;
    }
    if( !mPtr ){
        mPtr = (void*)new monitorChannelInfo;
    }

    monitorChannelInfo* pInfo = (monitorChannelInfo*)mPtr;
    pInfo->channelIndex = channelIndex;
    pInfo->bContinue = bContinue;
}

void ChannelAndFrameMonitorTreeItem::setFrameData(quint32 frameID, quint32 monitorCycle, bool bContinue, quint64 recvCount, quint64 errorCount)
{
    if( TREEITEMTYPE_FRAME != mType ){
        return;
    }
    if( !mPtr ){
        mPtr = (void*)new monitorFrameInfo;
    }

    monitorFrameInfo* pInfo = (monitorFrameInfo*)mPtr;
    pInfo->frameID = frameID;
    pInfo->monitorCycle = monitorCycle;
    pInfo->bContinue = bContinue;
    pInfo->recvCount = recvCount;
    pInfo->errorCount = errorCount;
    pInfo->recvCycle = 0;
}

void ChannelAndFrameMonitorTreeItem::initFrame()
{
    if( TREEITEMTYPE_FRAME != mType ){
        return;
    }
//    if( !mPtr ){
//        mPtr = (void*)new monitorFrameInfo;
//    }

    monitorFrameInfo* pInfo = (monitorFrameInfo*)mPtr;
    pInfo->recvCount = 0;
    pInfo->errorCount = 0;
    pInfo->lastTime = 0;
    pInfo->lastCheckTime = 0;
    pInfo->recvCycle = 0;
    //pInfo->lastTime = QDateTime::currentMSecsSinceEpoch();
    pInfo->lastData.clear();
}

//void ChannelAndFrameMonitorTreeItem::updateFrameCount(quint64 currentTime)
//{
//    if( TREEITEMTYPE_FRAME != mType ){
//        return;
//    }
//    if( !mPtr ){
//        mPtr = (void*)new monitorFrameInfo;
//    }

//    monitorFrameInfo* pInfo = (monitorFrameInfo*)mPtr;
////    pInfo->frameID = frameID;
////    pInfo->monitorCycle = monitorCycle;
////    pInfo->bContinue = bContinue;

//    if( currentTime - pInfo->lastTime > pInfo->recvCount ){
//        pInfo->errorCount++;
//    }else{
//        pInfo->recvCount++;
//    }
//    pInfo->lastTime = currentTime;


//}

//void ChannelAndFrameMonitorTreeItem::recvFrame(quint64 currentTime, const QByteArray& data )
//{
//    return;
//    if( TREEITEMTYPE_FRAME != mType ){
//        return;
//    }
////    if( !mPtr ){
////        mPtr = (void*)new monitorFrameInfo;
////    }

//    monitorFrameInfo* pInfo = (monitorFrameInfo*)mPtr;
////    qDebug() << __FUNCTION__ << __LINE__ << currentTime << pInfo->lastTime << pInfo->errorCount;
////    qDebug() << __FUNCTION__ << __LINE__
////             << QDateTime::fromMSecsSinceEpoch( pInfo->lastTime ).toString( "yyyy-MM-dd hh:mm:ss:zzz" )
////             <<QDateTime::fromMSecsSinceEpoch( currentTime ).toString( "yyyy-MM-dd hh:mm:ss:zzz" )
////            << pInfo->errorCount;

//    pInfo->lastTime = currentTime;
//    pInfo->recvCount++;
//    pInfo->lastData = data;
//}

void ChannelAndFrameMonitorTreeItem::recvFrame(const Devices::Can::CanFrame &frame)
{
    if( TREEITEMTYPE_FRAME != mType || !parent() ){
        return;
    }

//    qDebug() << __FUNCTION__ << __LINE__ <<
//                frame.channelIndex()
//             << parent()->data(0).toUInt() <<
//                frame.idHex() << data(0).toString()
//                << frame.timestemp()/1000;


    bool bEqual = false;
    quint64 curMs = frame.timestemp() / 1000;//us 转换为 ms
    monitorFrameInfo* pInfo = (monitorFrameInfo*)mPtr;
    if( frame.channelIndex() == parent()->data(0).toUInt() && frame.id() == data(0).toString().toUInt(NULL, 16) ){
        bEqual = true;
        pInfo->lastData = frame.data();
        pInfo->recvCount++;

        if( pInfo->lastTime != 0 ){
            pInfo->recvCycle = ( pInfo->recvCycle + curMs - pInfo->lastTime ) / 2;
        }else{
            pInfo->recvCycle = pInfo->monitorCycle;
        }
        pInfo->lastTime = curMs;
    }

    if( pInfo->lastCheckTime == 0 ){
        pInfo->lastCheckTime = curMs;
    }else{
        quint64 lostCnt = (curMs - pInfo->lastCheckTime ) / pInfo->monitorCycle;
        if( lostCnt > 0 ){
//            qDebug() << __FUNCTION__ << __LINE__ << curMs << pInfo->lastCheckTime
//                     << (curMs - pInfo->lastCheckTime ) << pInfo->monitorCycle;
            pInfo->errorCount += lostCnt;
            pInfo->lastCheckTime = curMs;
        }else if( bEqual ){
            pInfo->lastCheckTime = curMs;
        }
    }

//    qDebug() << __FUNCTION__ << __LINE__
//             << pInfo->recvCount << pInfo->errorCount
//             << pInfo->recvCycle
//             << pInfo->lastTime << pInfo->lastCheckTime;
}

//bool ChannelAndFrameMonitorTreeItem::checkFrame(quint64 currentTime)
//{
//    return true;
//    if( TREEITEMTYPE_FRAME != mType ){
//        return true;
//    }
////    if( !mPtr ){
////        mPtr = (void*)new monitorFrameInfo;
////    }

//    monitorFrameInfo* pInfo = (monitorFrameInfo*)mPtr;
//    if( 0 == pInfo->lastTime ){
//        pInfo->lastTime = currentTime;
//    }else if( /*pInfo->lastTime != 0 &&*/ currentTime - pInfo->lastTime > pInfo->monitorCycle ){
//        pInfo->errorCount++;

//        quint8 channelIdx = 0;
//        if( parent() ){
//            channelIdx = parent()->data( 0 ).toUInt();
//        }

//        emit frameCheckAbnormal( channelIdx,
//                                 pInfo->frameID,
//                                 pInfo->lastTime,
//                                 currentTime,
//                                 currentTime - pInfo->lastTime,
//                                 pInfo->monitorCycle,
//                                 pInfo->lastData );


//        qDebug() << __FUNCTION__ << __LINE__
//                 << QDateTime::fromMSecsSinceEpoch( pInfo->lastTime ).toString( "yyyy-MM-dd hh:mm:ss:zzz" )
//                 <<QDateTime::fromMSecsSinceEpoch( currentTime ).toString( "yyyy-MM-dd hh:mm:ss:zzz" );

//        //pInfo->lastTime = currentTime;
//        //qDebug() << __FUNCTION__ << __LINE__ << pInfo->lastTime << currentTime ;
//        return false;
//    }
//    //pInfo->lastTime = currentTime;
//    return true;
//}

////////////////////////////////////////////Model////////////////////////////////////////////////////

ChannelAndFrameMonitorTreeModel::ChannelAndFrameMonitorTreeModel( QObject *parent )
    : QAbstractItemModel( parent )
{
    mHeader << QString::fromLocal8Bit( "通道/帧ID" )
            << QString::fromLocal8Bit( "是否后续流程" )
            << QString::fromLocal8Bit( "监控周期(ms)" )
            << QString::fromLocal8Bit( "已接收数量" )
            << QString::fromLocal8Bit( "丢失数量" )
            << QString::fromLocal8Bit( "接收周期(ms)" );

    mTreeRoot = new ChannelAndFrameMonitorTreeItem( NULL, ChannelAndFrameMonitorTreeItem::TYPE::TREEITEMTYPE_ERROR );
    loadFromXml();

//    connect( this, &ChannelAndFrameMonitorTreeModel::dataChanged, this, &ChannelAndFrameMonitorTreeModel::saveToXml );
    qRegisterMetaType<QVector<int> >("QVector<int>");
}

ChannelAndFrameMonitorTreeModel::~ChannelAndFrameMonitorTreeModel()
{
//    saveToXml();
    clear();
}

void ChannelAndFrameMonitorTreeModel::addChannelMonitor()
{

    beginInsertRows( QModelIndex(), 0/*mTreeRoot->childCount()*/, 0/*mTreeRoot->childCount()*/ );

//    this->beginResetModel();
    ChannelAndFrameMonitorTreeItem* pItem = new ChannelAndFrameMonitorTreeItem( mTreeRoot, ChannelAndFrameMonitorTreeItem::TYPE::TREEITEMTYPE_CHANNEL );
    //qDebug() << __FUNCTION__ << __LINE__ << pItem;
    pItem->setChannelData( 0, true );
//    this->endResetModel();

    endInsertRows();
//    saveToXml();
}

void ChannelAndFrameMonitorTreeModel::addFrameMonitor(const QModelIndex &index)
{
    if( !isChannelIndex( index ) ){
        return;
    }

    ChannelAndFrameMonitorTreeItem* pParent = getItemFromIndex( index );

    if( pParent ){
//        this->beginResetModel();
        //qDebug() << pParent->childCount() << this->rowCount( index );
        QModelIndex index0 = createIndex( pParent->row(), 0, index.internalPointer() ); //转换为第一列的索引，防止展开子节点图标不刷新
        beginInsertRows( index0, pParent->childCount(), pParent->childCount() );

        ChannelAndFrameMonitorTreeItem* pItem = new ChannelAndFrameMonitorTreeItem( pParent, ChannelAndFrameMonitorTreeItem::TYPE::TREEITEMTYPE_FRAME );
        pItem->setFrameData( 0, 65/*50*/, true );
        connect( pItem, &ChannelAndFrameMonitorTreeItem::frameCheckAbnormal, this, &ChannelAndFrameMonitorTreeModel::frameCheckAbnormal );

        endInsertRows();
    }
//    saveToXml();
}

void ChannelAndFrameMonitorTreeModel::deleteMonitor(const QModelIndex &index)
{
    ChannelAndFrameMonitorTreeItem* pItem = getItemFromIndex( index );
    if( pItem && pItem->parent() ){
        //this->beginResetModel();
        QModelIndex parent = createIndex( pItem->parent()->row(), 0, pItem->parent() );
        if( pItem->parent() == mTreeRoot ){
            parent = QModelIndex();
        }
        beginRemoveRows( parent, pItem->row(), pItem->row() );
        pItem->parent()->deleteChild( pItem->row() );
        endRemoveRows();
        //this->endResetModel();
    }
//    saveToXml();
}

bool ChannelAndFrameMonitorTreeModel::isChannelIndex(const QModelIndex &index)
{
    ChannelAndFrameMonitorTreeItem* item = getItemFromIndex( index );
    if( item && item->type() == ChannelAndFrameMonitorTreeItem::TYPE::TREEITEMTYPE_CHANNEL ){
        return true;
    }

    return false;
}

void ChannelAndFrameMonitorTreeModel::getChannelFilter(QList<quint8> &channelIdxList)
{
    channelIdxList.clear();
    for( int i=0; i<mTreeRoot->childCount(); i++ ){
        ChannelAndFrameMonitorTreeItem* pChannelItem = mTreeRoot->child( i );
        quint8 channelIdx = pChannelItem->data( 0 ).toUInt();
        bool bContinue = pChannelItem->data( 1 ).toBool();
        if( !bContinue ){
            channelIdxList.push_back( channelIdx );
        }
    }
}

void ChannelAndFrameMonitorTreeModel::getFrameFilter(QMap<quint8, QList<quint64> > &frameMap)
{
    frameMap.clear();
    for( int i=0; i<mTreeRoot->childCount(); i++ ){
        ChannelAndFrameMonitorTreeItem* pChannelItem = mTreeRoot->child( i );
        quint8 channelIdx = pChannelItem->data( 0 ).toUInt();
        bool bContinueChannel = pChannelItem->data( 1 ).toBool();
        for( int j=0; j<pChannelItem->childCount(); j++ ){
            ChannelAndFrameMonitorTreeItem* pFrameItem = pChannelItem->child( j );
            quint64 frameID = pFrameItem->data( 0 ).toString().toUInt( NULL, 16 );
            bool bContinueFrame = pFrameItem->data( 1 ).toBool();
            if( !bContinueFrame || !bContinueChannel ){
                frameMap[channelIdx].push_back( frameID );
            }
        }
    }
}

void ChannelAndFrameMonitorTreeModel::startOrStop()
{
    if( !mRun ){
        initChannelMap();
//        initTimerMap();
    }else{
//        closeTimer();
    }
    mRun = !mRun;
}

void ChannelAndFrameMonitorTreeModel::recvFrame(const Devices::Can::CanFrame &frame)
{
//    if( frame.id() == 0x436 ){
//        qDebug() << __FUNCTION__ << __LINE__ << frame.timestemp()
//                 << QDateTime::fromMSecsSinceEpoch(frame.timestemp()).toString( "yyyy-MM-dd hh:mm:ss:zzz" );
//    }

    if( !mRun ){
        return;
    }

    QMap< quint8, QList<ChannelAndFrameMonitorTreeItem*> >::iterator it = mChannelMap.begin();
    for( ; it!=mChannelMap.end(); it++ ){
        QList<ChannelAndFrameMonitorTreeItem*>& frameItemList =  it.value();
        for( int i=0; i<frameItemList.size(); i++ ){
            frameItemList[i]->recvFrame( frame );
            dataChange( frameItemList[i] );
        }
    }

    /*
    quint8 channelIdx = frame.channelIndex();
    QMap< quint8, QList<ChannelAndFrameMonitorTreeItem*> >::iterator it = mChannelMap.find( channelIdx );
    if( it == mChannelMap.end() ){
        return;
    }

    QList<ChannelAndFrameMonitorTreeItem*>& frameItemList =  it.value();
    for( int i=0; i<frameItemList.size(); i++ ){
        quint64 frameID = frameItemList[i]->data( 0 ).toString().toUInt( NULL, 16 );
        if( frame.id() == frameID ){
            frameItemList[i]->recvFrame( QDateTime::currentMSecsSinceEpoch(), frame.data() );
            dataChange( frameItemList[i] );
            //break; //不退出，防止设置了相同的监视帧
        }
    }*/
}

QVariant ChannelAndFrameMonitorTreeModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if( orientation == Qt::Horizontal && role == Qt::DisplayRole ){
            return mHeader[section];
    }

    return QAbstractItemModel::headerData(section, orientation, role);
}

int ChannelAndFrameMonitorTreeModel::rowCount(const QModelIndex &parent) const
{
//    if (parent.column() > 0)
//        return 0;

    //return 5;
    ChannelAndFrameMonitorTreeItem* item = getItemFromIndex(parent);
    if( item ){
        return item->childCount();
    }else{
        return 0;
    }
}

int ChannelAndFrameMonitorTreeModel::columnCount(const QModelIndex &parent) const
{
    //return QAbstractItemModel::columnCount( parent );
    return mHeader.size();
}

QVariant ChannelAndFrameMonitorTreeModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid())
        return QVariant();

    ChannelAndFrameMonitorTreeItem *item = getItemFromIndex(index);
    if( item && role == Qt::DisplayRole){
        return item->data( index.column() );
    }
    return QVariant();
}

bool ChannelAndFrameMonitorTreeModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (!index.isValid() || role != Qt::EditRole )
        return false;

    ChannelAndFrameMonitorTreeItem *item = getItemFromIndex(index);
    if( !item ){
        return false;
    }

    int column = index.column();
    switch ( item->type() ) {
    case ChannelAndFrameMonitorTreeItem::TYPE::TREEITEMTYPE_CHANNEL:
        {
            quint8 channelIndex = item->data( 0 ).toUInt();
            bool bContinue = item->data( 1 ).toBool();
            if( column == 0 ){
                channelIndex = value.toUInt();
            }
            if( column == 1 ){
                bContinue = value.toBool();
            }
            item->setChannelData( channelIndex, bContinue );
        }
        break;
    case ChannelAndFrameMonitorTreeItem::TYPE::TREEITEMTYPE_FRAME:
        {
            //quint32 frameID = item->data( 0 ).toUInt();        //帧ID
            quint32 frameID = item->data( 0 ).toString().toUInt( NULL, 16 );        //帧ID
            bool bContinue = item->data( 1 ).toBool();         //是否继续后续流程
            quint32 monitorCycle = item->data( 2 ).toUInt();   //监视周期 单位ms
            quint64 recvCount = item->data( 3 ).toULongLong();      //接收数量
            quint64 errorCount = item->data( 4 ).toULongLong();     //异常数量
            switch ( column ) {
            case 0:
                //frameID = value.toUInt();
                frameID = value.toString().toUInt( NULL, 16 );
                break;
            case 1:
                bContinue = value.toBool();
                break;
            case 2:
                monitorCycle = value.toUInt();
                break;
            case 3:
                recvCount = value.toULongLong();
                break;
            case 4:
                errorCount = value.toULongLong();
                break;
            default:
                break;
            }
            item->setFrameData( frameID, monitorCycle, bContinue, recvCount, errorCount );
        }
        break;
    default:
        break;
    }

    emit dataChanged( index, index );
    return true;
}

Qt::ItemFlags ChannelAndFrameMonitorTreeModel::flags(const QModelIndex &index) const
{
//    return QAbstractItemModel::flags(index);
    if (!index.isValid())
        return QAbstractItemModel::flags(index);

    //int nRow = index.row();
    int nColumn = index.column();

    ChannelAndFrameMonitorTreeItem* item = getItemFromIndex( index );

    Qt::ItemFlags flags =/* Qt::NoItemFlags*/ Qt::ItemIsEnabled | Qt::ItemIsSelectable;
    if( item ){
        if( nColumn == 0 || nColumn == 1 ){
            flags |= Qt::ItemIsEditable;
        }else if( nColumn == 2 && item->type() == ChannelAndFrameMonitorTreeItem::TYPE::TREEITEMTYPE_FRAME ){
            flags |= Qt::ItemIsEditable;
        }else{
            flags = Qt::NoItemFlags;
        }
    }else{
        flags = Qt::NoItemFlags;
    }

    return flags;
}

QModelIndex ChannelAndFrameMonitorTreeModel::index(int row, int column, const QModelIndex &parent) const
{
    if (!hasIndex(row, column, parent))
        return QModelIndex();

    ChannelAndFrameMonitorTreeItem *parentItem = getItemFromIndex( parent );
    if( parentItem ){
        ChannelAndFrameMonitorTreeItem *item = parentItem->child(row);
        if (item){
            return createIndex(row, column, item);
        }
    }
    return QModelIndex();
}

QModelIndex ChannelAndFrameMonitorTreeModel::parent(const QModelIndex &child) const
{
    if (!child.isValid())
        return QModelIndex();

    ChannelAndFrameMonitorTreeItem *item = getItemFromIndex( child );
    if( item ){
        ChannelAndFrameMonitorTreeItem *parentItem = item->parent();
        if( parentItem == mTreeRoot ){
            return QModelIndex();
        }else{
            return createIndex( parentItem->row(), 0, parentItem);
        }
    }
    return QModelIndex();
}

ChannelAndFrameMonitorTreeItem *ChannelAndFrameMonitorTreeModel::getItemFromIndex(const QModelIndex &index) const
{
    void* ptr = index.internalPointer();
    if( !ptr ){
        ptr = mTreeRoot;
    }
    return (ChannelAndFrameMonitorTreeItem*)ptr;
}

void ChannelAndFrameMonitorTreeModel::initChannelMap()
{
    mChannelMap.clear();
    for( int i=0; i<mTreeRoot->childCount(); i++ ){
        ChannelAndFrameMonitorTreeItem* pChannelItem = mTreeRoot->child( i );
        quint8 channelIdx = pChannelItem->data( 0 ).toUInt();
        for( int j=0; j<pChannelItem->childCount(); j++ ){
            ChannelAndFrameMonitorTreeItem* pFrameItem = pChannelItem->child( j );
            pFrameItem->initFrame(); //清空计数
            mChannelMap[channelIdx].push_back( pFrameItem );
            dataChange( pFrameItem );
        }
    }
}

//void ChannelAndFrameMonitorTreeModel::initTimerMap()
//{
////    QMap< int, QList<ChannelAndFrameMonitorTreeItem*> >::iterator it = mTimerMap.begin();
////    for( ; it!=mTimerMap.end(); it++ ){
////        killTimer( it.key() );
////    }

//    mTimerMap.clear();
//    mTimerMap2.clear();
//    for( int i=0; i<mTreeRoot->childCount(); i++ ){
//        ChannelAndFrameMonitorTreeItem* pChannelItem = mTreeRoot->child( i );
//        //quint8 channelIdx = pChannelItem->data( 0 ).toUInt();
//        for( int j=0; j<pChannelItem->childCount(); j++ ){
//            ChannelAndFrameMonitorTreeItem* pFrameItem = pChannelItem->child( j );
//            quint64 monitorCycle = pFrameItem->data(2).toULongLong();
//            int timerID = -1;
//            if( mTimerMap2.find( monitorCycle ) == mTimerMap2.end() ){
//                timerID = startTimer( monitorCycle, Qt::PreciseTimer );
//                //timerID = startTimer( 1000, Qt::PreciseTimer );
//                mTimerMap2[monitorCycle] = timerID;
//            }else{
//                timerID = mTimerMap2.find( monitorCycle ).value();
//            }
//            mTimerMap[timerID].push_back( pFrameItem );
//        }
//    }
//}

//void ChannelAndFrameMonitorTreeModel::closeTimer()
//{
//    QMap< int, QList<ChannelAndFrameMonitorTreeItem*> >::iterator it = mTimerMap.begin();
//    for( ; it!=mTimerMap.end(); it++ ){
//        killTimer( it.key() );
//    }
//}

//void ChannelAndFrameMonitorTreeModel::timerOut(quint32 timerID)
//{
//    QMap< int, QList<ChannelAndFrameMonitorTreeItem*> >::iterator it = mTimerMap.find( timerID );
//    if( it == mTimerMap.end() ){
//        return;
//    }
//    QList<ChannelAndFrameMonitorTreeItem*>& frameItemList = it.value();
//    for( int i=0; i<frameItemList.size(); i++ ){
//        if( !frameItemList[i]->checkFrame( QDateTime::currentMSecsSinceEpoch() ) ){
//            dataChange( frameItemList[i] );
//        }
//    }
//}


//void ChannelAndFrameMonitorTreeModel::timerEvent(QTimerEvent *event)
//{
//    //qDebug() << "Timer ID:" << event->timerId();
//    int timerID = event->timerId();
//    QMap< int, QList<ChannelAndFrameMonitorTreeItem*> >::iterator it = mTimerMap.find( timerID );
//    if( it == mTimerMap.end() ){
//        return;
//    }
//    QList<ChannelAndFrameMonitorTreeItem*>& frameItemList = it.value();
//    for( int i=0; i<frameItemList.size(); i++ ){
//        if( !frameItemList[i]->checkFrame( QDateTime::currentMSecsSinceEpoch() ) ){
//            dataChange( frameItemList[i] );
//        }
//    }

//    //QtConcurrent::run( this, &ChannelAndFrameMonitorTreeModel::timerOut, (quint32)timerID );
//}

void ChannelAndFrameMonitorTreeModel::dataChange(ChannelAndFrameMonitorTreeItem *pItem)
{
    QModelIndex index0 = createIndex( pItem->row(), 0, pItem );
    QModelIndex index1 = createIndex( pItem->row(), 5, pItem );
    emit dataChanged( index0, index1 );
}

void ChannelAndFrameMonitorTreeModel::clear()
{
    if( mTreeRoot ){
        delete mTreeRoot;
        mTreeRoot = NULL;
    }
}

void ChannelAndFrameMonitorTreeModel::saveToXml()
{
//    qDebug() << __FUNCTION__ << __LINE__;
    if( !mTreeRoot ){
        return;
    }
    // 打开或者创建一个XML文件
    QFile file( XML_FILE_NAME );
    // 文件存在则创建，否则创建一个文件
    if (!file.open( QIODevice::WriteOnly ) ) {
        return;
    }
    // 创建一个XML类
    QDomDocument doc;
    // 创建XML处理类，通常用于处理第一行描述信息
    QDomProcessingInstruction instruction;
    // 创建XML头部格式
    instruction = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
    // 添加到XML文件中
    doc.appendChild(instruction);

    // 创建根节点
    QDomElement root = doc.createElement("Root");
    doc.appendChild(root);

    for( int i=0; i<mTreeRoot->childCount(); i++ ){
        ChannelAndFrameMonitorTreeItem* pChannelItem = mTreeRoot->child( i );
        QDomElement channel = doc.createElement("Channel");
        channel.setAttribute("ID", pChannelItem->data( 0 ).toUInt() );
        channel.setAttribute("Continue", pChannelItem->data( 1 ).toBool() );

        for( int j=0; j<pChannelItem->childCount(); j++ ){
            ChannelAndFrameMonitorTreeItem* pFrameItem = pChannelItem->child( j );
            QDomElement frame = doc.createElement("Frame");
            frame.setAttribute("ID", pFrameItem->data( 0 ).toString() );
            frame.setAttribute("Continue", pFrameItem->data( 1 ).toBool() );
            frame.setAttribute("MonitorCycle", pFrameItem->data( 2 ).toUInt() );
            channel.appendChild( frame );
        }
        root.appendChild( channel );
    }

    // 将其写入到xml文件中
    QTextStream stream(&file);
    doc.save(stream, 4);		// 缩进四格
    file.close();
    QMessageBox::information( NULL, QString::fromLocal8Bit("提示"), QString::fromLocal8Bit("保存成功!") );
}

void ChannelAndFrameMonitorTreeModel::loadFromXml()
{
    // 打开文件
    QFile file( XML_FILE_NAME );
    if (!file.open(QFileDevice::ReadOnly)) {
        return;
    }

    QDomDocument doc;
    if (!doc.setContent(&file)) {
        file.close();
        return;
    }



    // 获得根节点
    QDomElement root = doc.documentElement();
    // 获取所有Channel节点
    QDomNodeList list = root.elementsByTagName("Channel");
    /* 获取属性中的值 */
    for (int i = 0; i < list.count(); i++) {
        QDomElement element = list.at(i).toElement();
        this->addChannelMonitor();
        ChannelAndFrameMonitorTreeItem* pChannelItem = mTreeRoot->child( mTreeRoot->childCount() -1 );
        if( !pChannelItem )
            continue;
        QModelIndex channelModeIndex = this->index( mTreeRoot->childCount() -1, 0 );
        pChannelItem->setChannelData( element.attribute("ID").toUInt(), element.attribute("Continue").toUInt() != 0 );
        QDomNodeList frameList = element.elementsByTagName("Frame");
        for( int j=0; j<frameList.count(); j++ ){
            QDomElement frameElement = frameList.at(j).toElement();
            this->addFrameMonitor( channelModeIndex );
            ChannelAndFrameMonitorTreeItem* pFramelItem = pChannelItem->child( pChannelItem->childCount() -1 );
            if( !pFramelItem )
                continue;
            pFramelItem->setFrameData( frameElement.attribute("ID").toUInt(NULL,16), frameElement.attribute("MonitorCycle").toUInt(), frameElement.attribute("Continue").toUInt() != 0 );
        }
    }
    file.close();
}


