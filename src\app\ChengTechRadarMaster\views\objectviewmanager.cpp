﻿#include "objectviewmanager.h"

#include "objectview.h"
#include "objectcoordinatesystem.h"

#include <QDebug>

namespace Views {
namespace ObjectView {

ObjectViewManager::ObjectViewManager(QObject *parent) : QObject(parent)
{

}

ObjectView *ObjectViewManager::newObjectView(QWidget *parent)
{
    ObjectView *objectView = new ObjectView(parent);

    foreach (AnalysisDataView *analysisView, mAnalysisDataViews)
    {
        objectView->addAnalysisDataView(analysisView);
    }

    mObjectViews << objectView;

    return objectView;
}

QList<ObjectView *> ObjectViewManager::newObjectView(const QVariant &config, QWidget *parent)
{
    QList<ObjectView *> objectViews;

    QList<QVariant> viewsConfig = config.toList();
    qDebug() << __FUNCTION__ << __LINE__ << viewsConfig.size();
    foreach (const QVariant &viewConfg, viewsConfig)
    {
        ObjectView *objectView = newObjectView(parent);
        objectView->coordinateSystem()->setCoordinateSystemConfig(viewConfg);

        objectViews << objectView;
    }

    return objectViews;
}

void ObjectViewManager::removeObjectView(ObjectView *view)
{
    mObjectViews.removeAll(view);
}

void ObjectViewManager::addAnalysisDataView(AnalysisDataView *analysisDataView)
{
    if (mAnalysisDataViews.contains(analysisDataView))
    {
        return;
    }

    foreach (ObjectView *objectView, mObjectViews)
    {
        objectView->addAnalysisDataView(analysisDataView);
    }

    mAnalysisDataViews << analysisDataView;
}

QVariant ObjectViewManager::objectViewConfig() const
{
    qDebug() << __FUNCTION__ << __LINE__ << mObjectViews.size();
    QList<QVariant> config;
    foreach (ObjectView *objectView, mObjectViews)
    {
        config << objectView->coordinateSystem()->coordinateSystemConfig();
    }

    return config;
}

} // namespace ObjectView
} // namespace Views
