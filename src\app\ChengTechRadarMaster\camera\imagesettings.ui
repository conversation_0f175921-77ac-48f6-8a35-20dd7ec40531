<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ImageSettingsUi</class>
 <widget class="QDialog" name="ImageSettingsUi">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>332</width>
    <height>270</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Image Settings</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>Image</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0" colspan="2">
       <widget class="QLabel" name="label_8">
        <property name="text">
         <string>Resolution:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="2">
       <widget class="QComboBox" name="imageResolutionBox"/>
      </item>
      <item row="2" column="0" colspan="2">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>Image Format:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0" colspan="2">
       <widget class="QComboBox" name="imageCodecBox"/>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>Quality:</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QSlider" name="imageQualitySlider">
        <property name="maximum">
         <number>4</number>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>14</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="0">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>ImageSettingsUi</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>322</x>
     <y>272</y>
    </hint>
    <hint type="destinationlabel">
     <x>44</x>
     <y>230</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>ImageSettingsUi</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>405</x>
     <y>262</y>
    </hint>
    <hint type="destinationlabel">
     <x>364</x>
     <y>227</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
