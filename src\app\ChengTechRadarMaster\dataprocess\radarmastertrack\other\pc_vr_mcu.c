﻿
#ifdef PC_DBG_FW

#include <string.h>
#include "pc_vr_mcu.h"


int mcu_time = 0;
int frame_number = 0;


TxTargets_t gTxTargets[2] = {
    {
        .start = 0,
        .idx = 0,
        .num = 0,
    },
    {
        .start = 0,
        .idx = 0,
        .num = 0,
    }
};

static void can0_send_frame_now(void *txBufParam, UINT16 ID)
{
    gTxTargets[0].num = 0;
    gTxTargets[0].idx = 0;
    gTxTargets[0].start = 0;
    return;
}

static void can1_send_frame_now(void *txBufParam, UINT16 ID)
{
    gTxTargets[1].num = 0;
    gTxTargets[1].idx = 0;
    gTxTargets[1].start = 0;
    return;
}

static int can0_send(void *txBufParam, UINT16 ID)
{
    gTxTargets[0].num = 0;
    gTxTargets[0].idx = 0;
    gTxTargets[0].start = 0;
    return 0;
}

static int can1_send(void *txBufParam, UINT16 ID)
{
    gTxTargets[1].num = 0;
    gTxTargets[1].idx = 0;
    gTxTargets[1].start = 0;
    return 0;
}

const TCANCtrl gCAN[2] =
    {
        {
            can0_send,
            can0_send_frame_now
        },
        {
            can1_send,
            can1_send_frame_now
        }
    };


int saveRadarCfg(void)
{
    return 0;
}

/*
!!!!!!!!!!  这个需要具体实现 ！！！！！！！！！！！！
*/
int xTaskGetTickCount(void)
{
    return mcu_time;
}

int isCanValid(uint32_t dev_id, int cnt)
{
    return 0;
}

int bzero(void *dat , int len)
{
    memset(dat ,0, len);

    return 0;
}

void system_reset(void)
{
    return;
}

void update_auto_calc_status(uint8_t st)
{
    return;
}

void init_cfg()
{
//    radar_config_using = &radar_config_default;
}

#endif
