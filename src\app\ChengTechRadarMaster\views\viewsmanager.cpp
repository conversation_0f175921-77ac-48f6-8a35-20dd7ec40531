﻿#include "viewsmanager.h"

#include <analysis/protocol/ianalysisprotocol.h>
#include "analysis/calculationworker.h"
#include "analysisdataview.h"
#include "analysisdataviewf.h"
#include "objectview.h"
#include "objectcoordinatesystem.h"
#include "truesystemview.h"
#include "truevalue/hesailiderworker.h"

#include <QComboBox>

namespace Views {

ViewsManager::ViewsManager(Analysis::CalculationWorker *calculationWorker, QObject *parent)
    : QObject(parent),
      mCalculationWorker(calculationWorker)
{
    mComboBoxRadarType = new QComboBox;
    mComboBoxRadarType->addItem(QString::fromLocal8Bit("前雷达"), ForwardRadar);
    mComboBoxRadarType->addItem(QString::fromLocal8Bit("角雷达"), AngularRadar);
    connect(mComboBoxRadarType, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &ViewsManager::radarTypeChanged);

    memset(mAnalysisDataViews, 0, sizeof (mAnalysisDataViews));
    connect(mCalculationWorker, &Analysis::CalculationWorker::calculateFinished, this, &ViewsManager::calculateFinished, Qt::DirectConnection);
    connect(mCalculationWorker, &Analysis::CalculationWorker::calculateFinishedF, this, &ViewsManager::calculateFinishedF, Qt::DirectConnection);
    connect(mCalculationWorker, &Analysis::CalculationWorker::calculateTargetFinished, this, &ViewsManager::calculateTargetFinished);

    mViewTimer.setTimerType(Qt::PreciseTimer);
    mViewTimer.setSingleShot(true);
    connect(&mViewTimer, &QTimer::timeout, this, &ViewsManager::viewData);
}

ViewsManager::~ViewsManager()
{

}

bool ViewsManager::startView()
{
    for (int radarID = 4; radarID < 8; ++radarID) {
        if (mAnalysisDataViews[radarID]) {
            ((AnalysisView::AnalysisDataView*)mAnalysisDataViews[radarID])->startView();
        }
    }

    mViewTimer.start(50);
    mElapsedTimer.start();

    return true;
}

bool ViewsManager::stopView()
{
    for (int radarID = 4; radarID < 8; ++radarID) {
        if (mAnalysisDataViews[radarID]) {
            ((AnalysisView::AnalysisDataView*)mAnalysisDataViews[radarID])->stopView();
        }
    }

    mViewTimer.stop();

    return true;
}

AnalysisView::AnalysisDataViewI *ViewsManager::analysisDataView(quint8 radarID, QWidget *parent)
{
    if (!mAnalysisDataViews[radarID])
    {
        if (radarID == 0) {
            mAnalysisDataViews[radarID] = new AnalysisView::AnalysisDataViewF(radarID, &mParsedData, parent);
        } else {
            mAnalysisDataViews[radarID] = new AnalysisView::AnalysisDataView(radarID, mAnalysisDatas + radarID, parent);
        }
    }

    return mAnalysisDataViews[radarID];
}

ObjectView::ObjectView *ViewsManager::objectView(const QVariant &settings, QWidget *parent)
{
    ObjectView::ObjectView *view = new ObjectView::ObjectView(parent);
    view->coordinateSystem()->setCoordinateSystemConfig(settings);
    mObjectCoordinateSystems << view->coordinateSystem();

    return view;
}

TrueSystemView::TrueSystemView *ViewsManager::trueSystemView(QWidget *parent)
{
    if (!mTrueSystemView)
    {
        mTrueSystemView = new TrueSystemView::TrueSystemView(parent);
        connect(mTrueSystemView, &TrueSystemView::TrueSystemView::destroyed, this, [=](){
            mTrueSystemView = 0;
            emit trueSystemViewChanged(false);
        });
        emit trueSystemViewChanged(true);
    }

    return mTrueSystemView;
}

QVariant ViewsManager::getAnalysisDataViewsSettings() const
{
    QMap<QString, QVariant> settings;

    for (int i = 0; i < sizeof (mAnalysisDataViews) / sizeof (mAnalysisDataViews[0]); ++i) {
        if (mAnalysisDataViews[i]) {
            settings[QString::number(i)] = mAnalysisDataViews[i]->getAnalysisDataViewSettings();
        }
    }

    return settings;
}

void ViewsManager::setAnalysisDataViewsSettings(const QVariant &settings)
{
    QMap<QString, QVariant> set = settings.toMap();
    QList<QString> keys = set.keys();
    foreach(const QString &key, keys) {
        quint8 radarID = key.toUInt();
        if ((radarID >= 0 && radarID < MAX_RADAR_COUNT) && mAnalysisDataViews[radarID]) {
            mAnalysisDataViews[radarID]->setAnalysisDataViewSettings(set[key]);
        }
    }
}

QVariant ViewsManager::getObjectViewsSettings() const
{
    QList<QVariant> settings;

    for (int i = 0; i < mObjectCoordinateSystems.size(); ++i) {
        settings << mObjectCoordinateSystems[i]->coordinateSystemConfig();
    }

    return settings;
}

void ViewsManager::showFaultInfo(bool bShow)
{
    for (int radarID = 4; radarID < 8; ++radarID) {
        if (mAnalysisDataViews[radarID]) {
            ((AnalysisView::AnalysisDataView*)mAnalysisDataViews[radarID])->showFaultInfo( bShow );
        }
    }
}

void ViewsManager::showVersionInfo(bool bShow)
{
    for (int radarID = 4; radarID < 8; ++radarID) {
        if (mAnalysisDataViews[radarID]) {
            ((AnalysisView::AnalysisDataView*)mAnalysisDataViews[radarID])->showVersionInfo( bShow );
        }
    }
}

void ViewsManager::showAngleInfo(bool bShow)
{
    for (int radarID = 4; radarID < 8; ++radarID) {
        if (mAnalysisDataViews[radarID]) {
            ((AnalysisView::AnalysisDataView*)mAnalysisDataViews[radarID])->showAngleInfo( bShow );
        }
    }
}

void ViewsManager::viewData()
{
#if 1
    QMutexLocker locker(&mMutex);
    bool update = false;
    QElapsedTimer elapsedTimer;
    elapsedTimer.start();
//    qDebug() << __FUNCTION__ << __LINE__ << mCalculationWorker->mTrueObjectInfo.validFlag << mTrueSystemView;
    if (mCalculationWorker->mTrueObjectInfo.validFlag && mTrueSystemView)
    {
        mTrueSystemView->slotTrueObjInfo(&mCalculationWorker->mTrueObjectInfo);
        foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
            coordinateSystem->showTrueSystemTarget(mCalculationWorker->mAnalysisDatas[TRUE_VALUE_RADAR_ID_IFS300].mTrueTarget,
                                                   sizeof (mCalculationWorker->mAnalysisDatas[TRUE_VALUE_RADAR_ID_IFS300].mTrueTarget) /
                                                   sizeof (mCalculationWorker->mAnalysisDatas[TRUE_VALUE_RADAR_ID_IFS300].mTrueTarget[0]));
        }
        mCalculationWorker->mTrueObjectInfo.validFlag = false;
    }

    if (mPandar64_Targets->mValid) {
        foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
            coordinateSystem->showHeSaiTargets(mPandar64_Targets);
        }
        mPandar64_Targets->mValid = false;
    }

    for (int radarID = 4; radarID < 8; ++radarID) {
        AnalysisView::AnalysisDataView *analysisDataView = qobject_cast<AnalysisView::AnalysisDataView*>(mAnalysisDataViews[radarID]);
        AnalysisData *analysisData = mAnalysisDatas + radarID;
        if (analysisData->m200Targets.mValid) {
            foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
                coordinateSystem->showTargets(0, radarID, Frame200Raw, analysisData->m200Targets);
            }
            if (!analysisData->mValid) {
                analysisDataView->analysisTargets(radarID, Frame200Raw, analysisData->m200Targets);
            }
            analysisData->m200Targets.mValid = false;
            update = true;
        }
        if (analysisData->mLockTargets.mValid) {
            foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
                coordinateSystem->showTargets(0, radarID, Frame3Track, analysisData->mLockTargets);
            }
            if (!analysisData->mValid) {
                analysisDataView->analysisTargets(radarID, Frame3Track, analysisData->mLockTargets);
            }
            analysisData->mLockTargets.mValid = false;
            update = true;
        }
//        qDebug() << __FUNCTION__ << __LINE__ << radarID << analysisData->m16Targets.mValid;
        if (analysisData->m16Targets.mValid) {
//            qDebug() << __FUNCTION__ << __LINE__ << analysisData->m16Targets.mTargetHeader.mProtocolVersion;
            foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
                coordinateSystem->showTargets(0, radarID, Frame16Track, analysisData->m16Targets);
            }
            if (!analysisData->mValid) {
                analysisDataView->analysisTargets(radarID, Frame16Track, analysisData->m16Targets);
            }
//            qDebug() << __FUNCTION__ << __LINE__ << radarID << analysisData->m16Targets.mTargetHeader.mMeasurementCount;
            analysisData->m16Targets.mValid = false;
            update = true;
        }
        if (analysisData->mELKTargets.mValid) {
            foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
                coordinateSystem->showTargets(0, radarID, FrameELKTrack, analysisData->mELKTargets);
            }
            analysisData->mELKTargets.mValid = false;
            update = true;
        }

        if (analysisData->mValid) {
            if (analysisDataView) {
//                qDebug() << __FUNCTION__ << __LINE__ << radarID << analysisData->m16Targets.mTargetHeader.mMeasurementCount;
                analysisDataView->analysisRadarData(0, radarID, analysisData);
            }

            foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
                coordinateSystem->showRadarData(0, radarID, analysisData);
            }
            analysisData->mValid = false;
            update = true;
//            qDebug() << __FUNCTION__ << __LINE__ << radarID << analysisData->mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount;
        }
    }


    AnalysisView::AnalysisDataViewF *analysisDataViewF = qobject_cast<AnalysisView::AnalysisDataViewF*>(mAnalysisDataViews[0]);
    analysisDataViewF->parsedData(mParsedData);

    for (int i = 0; i < Parser::ParsedDataTypedef::ParsedDataNone; ++i) {
        if (mParsedData.mTargets[i].mValid) {
            foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
                coordinateSystem->showTargetsF(mParsedData.mTargets[i]);
            }
            mParsedData.mTargets[i].mValid = false;
            update = true;
        }
    }

    if (update) {
        foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
            coordinateSystem->draw();
        }
    }
#endif

    qint64 elapsed = elapsedTimer.elapsed();
    qint64 elapsed1 = mElapsedTimer.elapsed();
    if (mDeviceOpened) {
        if (50 > elapsed1) {
            mViewTimer.start(50 - elapsed1);
        } else {
            mViewTimer.start(1);
        }
        mElapsedTimer.start();
    }
#if 0
    qDebug() << __FUNCTION__ << __LINE__ << mDeviceOpened << update << mViewTimer.interval()
             << elapsed1 << elapsed << elapsedTimer.nsecsElapsed()
             << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
#endif
}

void ViewsManager::dataProcessRawViewData(int radarID)
{
    if (mCalculationWorker->mTrueObjectInfo.validFlag && mTrueSystemView)
    {
        mTrueSystemView->slotTrueObjInfo(&mCalculationWorker->mTrueObjectInfo);
        foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
            coordinateSystem->showTrueSystemTarget(mCalculationWorker->mAnalysisDatas[TRUE_VALUE_RADAR_ID_IFS300].mTrueTarget,
                                                   sizeof (mCalculationWorker->mAnalysisDatas[TRUE_VALUE_RADAR_ID_IFS300].mTrueTarget) /
                                                   sizeof (mCalculationWorker->mAnalysisDatas[TRUE_VALUE_RADAR_ID_IFS300].mTrueTarget[0]));
        }
        mCalculationWorker->mTrueObjectInfo.validFlag = false;
    }

    bool update = false;
    AnalysisData *analysisData = mAnalysisDatas + radarID;

    if (mAnalysisDataViews[4]) {
        ((AnalysisView::AnalysisDataView*)mAnalysisDataViews[4])->analysisRadarData(0, radarID, analysisData);
    }
    if (mAnalysisDataViews[5]) {
        ((AnalysisView::AnalysisDataView*)mAnalysisDataViews[5])->analysisRadarData(0, radarID, analysisData);
    }

    if (analysisData->mValid) {
        foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
            coordinateSystem->showRadarData(0, radarID, analysisData);
        }
        analysisData->mValid = false;
        update = true;
    }

    if (update) {
        foreach (ObjectView::ObjectCoordinateSystem* coordinateSystem, mObjectCoordinateSystems) {
            coordinateSystem->draw();
        }
    }
}

void ViewsManager::deviceOpened()
{
    mDeviceOpened = true;
    startView();
}

void ViewsManager::deviceClosed()
{
    mDeviceOpened = false;
    stopView();
}

void ViewsManager::updateRadarResetCount(quint64 count, quint8 channel)
{
    for( int  i = 4; i <= 7; i++ ){
         if( mAnalysisDataViews[i] ){
             ((AnalysisView::AnalysisDataView*)mAnalysisDataViews[i])->updateRadarResetCount( count, channel );
         }
    }
}

void ViewsManager::radarTypeChanged(int index)
{
    switch (index) {
    case ForwardRadar:
        break;
    case AngularRadar:
        break;
    }
}

void ViewsManager::calculateFinished(quint8 radarID, const AnalysisData &analysisData)
{
    QMutexLocker locker(&mMutex);
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << analysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount
//             << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
//    qDebug() << __FUNCTION__ << __LINE__ << radarID;
    // 不能整体赋值，会覆盖真值和其它不同步的目标点
    // mAnalysisDatas[radarID] = *(mCalculationWorker->mAnalysisDatas + radarID);
    for (int i = 0; i < FrameTargetCount; ++i)
    {
        mAnalysisDatas[radarID].mTargets[i] = analysisData.mTargets[i];
    }
    mAnalysisDatas[radarID].mAlarmData = analysisData.mAlarmData;                       // 告警信息
    mAnalysisDatas[radarID].mVehicleData = analysisData.mVehicleData;                   // 汽车数据
    mAnalysisDatas[radarID].mEndFrameData = analysisData.mEndFrameData;
    mAnalysisDatas[radarID].m16Targets = analysisData.m16Targets;
    mAnalysisDatas[radarID].mRadarID = analysisData.mRadarID;
    mAnalysisDatas[radarID].mValid = true; //analysisData.mValid;

    //    qDebug() << __FUNCTION__ << __LINE__ << radarID;
}

void ViewsManager::calculateFinishedF(const Parser::ParsedDataTypedef::TargetsF &targets)
{
    if (targets.mParsedDataType >= Parser::ParsedDataTypedef::ParsedDataNone) {
        return;
    }

    QMutexLocker locker(&mMutex);
    mParsedData.mTargets[targets.mParsedDataType] = targets;
}

void ViewsManager::calculateTargetFinished(quint8 radarID, int frameType, const Targets &target)
{
    QMutexLocker locker(&mMutex);
    switch (frameType)
    {
    case FrameRawTarget:
    case FrameTrackTarget:
        mAnalysisDatas[radarID].mTargets[frameType] = target;
        mAnalysisDatas[radarID].mTargets[frameType].mValid = target.mValid;
        break;
    case Frame200Raw:
        mAnalysisDatas[radarID].m200Targets = target;
        mAnalysisDatas[radarID].m200Targets.mValid = target.mValid;
        break;
    case Frame3Track:
        mAnalysisDatas[radarID].mLockTargets = target;
        mAnalysisDatas[radarID].mLockTargets.mValid = target.mValid;
//        qDebug() << __FUNCTION__ << __LINE__ << radarID << frameType << target.mValid << mAnalysisDatas[radarID].mLockTargets.mValid;
        break;
    case Frame16Track:
        mAnalysisDatas[radarID].m16Targets = target;
        mAnalysisDatas[radarID].m16Targets.mValid = target.mValid;
//        qDebug() << __FUNCTION__ << __LINE__ << radarID << target.mTargetHeader.mMeasurementCount;
//        qDebug() << __FUNCTION__ << __LINE__ << radarID << frameType << target.mValid << mAnalysisDatas[radarID].mLockTargets.mValid;
        break;
    case FrameELKTrack:
        mAnalysisDatas[radarID].mELKTargets = target;
        mAnalysisDatas[radarID].mELKTargets.mValid = target.mValid;
//        qDebug() << __FUNCTION__ << __LINE__ << radarID << frameType << target.mValid << mAnalysisDatas[radarID].mLockTargets.mValid;
        break;
    }
}

void ViewsManager::showHeSaiTarget(int index, int count, const QString &targetString)
{
//    qDebug() << __FUNCTION__ << __LINE__ << count;
    HeSaiLiderWorker *hesaiLiderWorker = qobject_cast<HeSaiLiderWorker*>(sender());
    if (hesaiLiderWorker) {
        mPandar64_Targets->mValid = true;
        mPandar64_Targets->mTargetsCount = count;
        memcpy(mPandar64_Targets->mTargets, hesaiLiderWorker->mDetectionList, count * sizeof (Pandar64_Target));
    }
}

} // namespace Views
