/*
 * @Author: your name
 * @Date: 2020-05-16 14:22:22
 * @LastEditTime: 2021-11-03 15:58:10
 * @LastEditors: fang yongjun
 * @Description: In User Settings Edit
 * @FilePath: sharedDef.h
 */
#ifndef __SHARED_DEF_H__
#define __SHARED_DEF_H__

#ifndef PC_DBG_FW
#include "radarCfg.h"
#else
#include "hal/rsp/rsp_types.h"
#include "app/system_mgr/radarCfg.h"
#include "other/temp.h"
#endif

#ifndef M_PI
#define M_PI 3.1415926f
#endif

#define MAX_OUTPUT_OBJS			(256U)
//====================================================================
#define MAX_NB_OF_TARGETS 		256 // MAX_OUTPUT_OBJS        // Maximum number of targets
#define MAX_NB_OF_CAL_TARGETS 	64u //for 220plus

#if TD_MIMO || BPM_MIMO
#define RX_VIRT_CHAN (RX_ACTIVE * 2)
#else
#define RX_VIRT_CHAN RX_ACTIVE
#endif

#if (SAMPLE_CNT == 1024 || TD_MIMO || BPM_MIMO)
#define FFT_CP_RATE 2
#else
#define FFT_CP_RATE 1
#endif

#define UPLOAD_RADAR_DATA_4CHADC 0x5
#define UPLOAD_RADAR_DATA_TARGETS 0x6
#define UPLOAD_RADAR_DATA_RFFT 0x7
#define UPLOAD_RADAR_DATA_TARGET_1CHMAG 0x8
#define UPLOAD_RADAR_DATA_DFFT 0x9
#define UPLOAD_RADAR_DATA_DBF 0xA
#define DEBUG_TRACK_MODE 0xB
#define NORMAL_TRACK_MODE 0xC
#define UPLOAD_RADAR_DATA_OBJS 0xD
#define UPLOAD_RADAR_RAW_OBJS 0xE

#define UPLOAD_RADAR_DEFAULT_MODE UPLOAD_RADAR_DATA_TARGETS

#define UNVALID_VEL 10000.f //无效速度

//提供给initial_flag变量的宏定义
#define RADAR_INIT_FLAG_NULL 0      //暂时没定义
#define RADAR_INIT_FLAG_START 1     //初始化状态
#define RADAR_INIT_FLAG_OK 2        //完成初始化
#define RADAR_INIT_FLAG_ONE_FRAME 3 //完成一帧处理，但是可能没有完成多帧的处理，没有可以进行跟踪
#define RADAR_INIT_FLAG_TRACK 4     //可以进行track
#define RADAR_INIT_FLAG_TRACK_END 5 //已经完成上一次数据的跟踪

#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif

#define RADAR_SUCCESS (0)
#define RADAR_FAILED (1)

#define RADAR_FALSE (0)
#define RADAR_TRUE (1)

#define TX_CH1 1
#define TX_CH2 2
//下线标定状态码
#define CALC_INIT_VALID			0U    //表示校准初始化
#define CALC_ING_VALID			1U    //表示校准中
#define CALC_OK_NOUSE			2U	 //校准OK但不适用【校准出的角度超过范围】   ---> 标靶未对准【角度偏差超过±2度】
#define CALC_ERROR_RES			3U	 //预留，不使用
#define CALC_USING				4U	 //校准已使用(但还未保存)
#define CALC_SAVED				5U	 //校准已保存
#define CALC_SAVED_ERROR		6U	 //保存失败 --》实际暂时不存在失败
#define CALC_ERROR_VALID		7U	 //校准错误/超时 				   ---> 标定场地有干扰【标定区域内目标太多，需要更换场地】
#define CALC_OK					8U	 //标定数据采集结束
#define CALC_ERROR_NO_OBJ		9U	 //校准错误	 				   ---> 指定位置未放标靶【未检测到3m附近的目标】
#define CALC_USER_QUIT_ERROR	10U   //校准错误-用户退出
#define CALC_OTHER_ERROR		11U   //校准错误-其他错误

//售后服务校准状态码
#define SERVICE_CALC_INIT				            0U    //表示校准初始化
#define SERVICE_CALC_ING				            1U    //表示校准中
#define SERVICE_CALC_CILLECT_OK			            2U	 //校准数据采集完成
#define SERVICE_CALC_SAVED				            4U	 //校准已保存
#define SERVICE_CALC_TIMEOUT			            5U	 //校准错误-超时
#define SERVICE_CALC_OUT_RANGE_ERROR	            6U	 //校准错误-角度超过范围
#define SERVICE_CALC_YAWRATE_ERROR		            7U	 //校准错误-横摆角错误
#define SERVICE_CALC_EGO_VELOCITY_ERROR	            8U	 //校准错误-车速错误
#define SERVICE_CALC_USER_QUIT_ERROR	            9U	 //校准错误-用户退出
#define SERVICE_CALC_OTHER_ERROR		            10U	 //校准错误-其他
#define SERVICE_CALC_COEFFICEIENTLOW                11U   //相关系数太低


//自标定标定状态码
#define SELF_ANGLE_CALIB_STATUE_CALC_INIT				SERVICE_CALC_INIT                //表示水平校准初始化
#define SELF_ANGLE_CALIB_STATUE_CALC_ING				SERVICE_CALC_ING                 //表示水平校准中
#define SELF_ANGLE_CALIB_STATUE_CALC_CILLECT_OK			SERVICE_CALC_CILLECT_OK	        //水平校准数据采集完成
#define SELF_ANGLE_CALIB_STATUE_CALC_SAVED				SERVICE_CALC_SAVED	            //水平校准已保存

#endif
