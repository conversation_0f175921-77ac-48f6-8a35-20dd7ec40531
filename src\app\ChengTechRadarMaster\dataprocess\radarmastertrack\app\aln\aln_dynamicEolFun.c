/**
 * @file     aln_dynamicEolFun.c
 * @brief    This file defines the functions for dynamic EOL alignment.
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2023-02-15
 * 
 * 
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2023-02-15  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

/****************************************************************************
  INCLUDE
 ****************************************************************************/
#include <string.h>
#include <math.h>
#ifndef PC_DBG_FW
#include "arc_timer.h"
#include "cfg.h"
#include "rdp_interface.h"
#include "rdp_clth_radar_lib.h"
#include "app_common.h"
#include "sys_status.h"
#include "rdp/track/alignment/aln_dynamicEolFun.h"
#include "rdp/track/alignment/aln_install_cfg.h"
#include "rdp/track/alignment/aln_type.h"
#include "rdp/track/alignment/alignment.h"
#include "app_diag.h"
#include "adas/customizedrequirements/adas.h"
#else
#include "alg/track/rdp_interface.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "aln_type.h"
#include "aln_dynamicEolFun.h"
#include "app/aln/aln_install_cfg.h"
#include "app/aln/aln_staticEolFun.h"
#include "app/adas/customizedrequirements/adas.h"
#include "other/temp.h"
#endif

/****************************************************************************
  DEFINE
 ****************************************************************************/
#define PRRINT_ALN_DBG_OUTPUT   0

#define ALL_ANGLE_CNT                       (101)   // 0.2 度一个步进，然后从当前设置的安装角度分开两边进行处理 ,z注意，只能是奇数
#define DBAA_ANFLE_RANGE		            (10)    //角度范围
#define DBAA_ANFLE_BIN			            (0.2)   //角度步进

#define MAX_NUMBER_FOR_CALC_OFFSET          (3000) // 15*20*10  = 3000 点数据 20帧1s 一共10s

#define ALN_VDY_CONDITION_MIN_SPEED         (30.0f * 0.277778f)     // 本车速度限制 
#define ALN_VDY_CONDITION_MAX_SPEED         (150.0f * 0.277778f)    // 本车速度限制
#define ALN_VDY_CONDITION_MAX_ACC           (0.5F)  // 纵向加速度限制
#define ALN_VDY_CONDITION_MAX_YAWRATE       (0.5f)  // 横向加速度限制

#define DEFAULT_FIX_ANGLE                   (45)    //默认安装标定角度

#define ALN_DYNAMIC_MAX_SET_OFFSET_ANGLE    (3.0f)

#define ALN_SERVICE_DYNAMIC_EOL_TIMEOUT     (6000)  // (1000 / 50) * 60 * 5  5分钟
// #define ALN_SERVICE_DYNAMIC_EOL_TIMEOUT 10000  // 1000*10  10s

#define MAX_STRAIGHT_ROAD_CNT               (10)    //恢复0.5后才算
#define MAX_OBJ_CNT_FOR_CALC                (25)    // 一帧的最大计算点数

static float gDbaaSaureBuffer[ALL_ANGLE_CNT];
static float gDbaaSaureBufferOneFrame[ALL_ANGLE_CNT];

static uint32_t gDbaaAllObjCnt = 0;
//static float gDbaaSST = 0;
//static float gDbaaSSTPre = 0;

uint16_t gStraightRoadCnt = 0; // 恢复直道的缓冲

static float vBuf[ALL_ANGLE_CNT];
static float agvBuf[ALL_ANGLE_CNT];
static float objV[ALL_ANGLE_CNT][MAX_OBJ_CNT_FOR_CALC];

ALN_CalibType_t ALN_Mode = SERVICE_DIAG_ALN; // 0 自标定，1 服务标定，2：最后一次完成服务标定，会恢复到自标定，但是需要清除一下数据。 默认一直进行自标定，进入服务标定后需要清除对应的数据

static CT_DYNAMIC_EOL_INFO_t mCtDynamicEolInfo;
static uint8_t ALN_routineSt = ROUTINE_ST_STOP;

static int gServiceAngleCalcState = 0;
//static float gCalcInstallAngle = 0;
static float horizontalDeviationAngle = 0.0f;     // 水平安装角度偏差

/*   */
static uint32_t StartALN_DynamicEolTick = 0;
static ADAS_TimeClase_t timeClass = {.adasTimeCnt = 0, .trackTime = 0};
static uint8_t dynamicRunStatus = 0;                                                                // bt0:速度过慢 bit1：速度过快 bit2 横摆角过大 bit3：加速过大 bit4：目标不充分 bit5：雷达失明

static inline void rstALN_DynamicEolTimer(void)
{
    StartALN_DynamicEolTick = 0;
}
static inline void setALN_DynamicEolTimer(void)
{
    StartALN_DynamicEolTick++;
}

static inline uint32_t getStartTime(void)
{
	return StartALN_DynamicEolTick;
}

uint8_t ALN_DynamicEolFun_Flag = 0;
//uint8_t ANGLE_MAX_MIN_OUT = 0;
/*  */

static CT_DYNAMIC_PARA_t mCtDynamicPara=
{
    .fixAngleOffset = DEFAULT_FIX_ANGLE,
};

void ALN_DynamicEOL_toStop(void)
{
    ALN_routineSt = ROUTINE_ST_STOP;
}

/**
 * @brief 
 * 
 * @return CT_DYNAMIC_EOL_INFO_t* 
 */
CT_DYNAMIC_EOL_INFO_t *ALN_getDynamicEolInfo(void)
{
    return &mCtDynamicEolInfo;
}

/**
 * @brief 
 * 
 */
void ALN_clearDynamicEolSaveFlag(void)
{
    mCtDynamicEolInfo.is_save_to_nvm = false;
}

/**
 * @brief 
 * 
 */
void ALN_initVar(void)
{
    gDbaaAllObjCnt = 0;

    memset((void *)gDbaaSaureBuffer, 0, sizeof(gDbaaSaureBuffer));
    memset((void *)gDbaaSaureBufferOneFrame, 0, sizeof(gDbaaSaureBufferOneFrame));
}






/**
 * @brief 道路场景检测
 * @param cdi_pkg 
 * @param trk_pkg 
 * @return int 0：可以正常进行标定
 *              -1：点数不满足
 */
int ALN_roadSceneCheckForCalcAngle(const cdi_pkg_t *cdi_pkg)
{
    int ret = -1;
    int cdi_idx = 0;
    int useCnt = 0;
    int moveCnt = 0;
    rdp_config_t* config = RDP_getTrackConfigPointer();
    for (cdi_idx = 0; cdi_idx < cdi_pkg->number; cdi_idx++)
    {
        if (cdi_pkg->cdi[cdi_idx].mea_z[1] > 5  //筛选大于5m
            && cdi_pkg->cdi[cdi_idx].mea_z[1] < 40  //筛选小于40m内的
            && fabsf(cdi_pkg->cdi[cdi_idx].x) < 15.0
            && fabsf(cdi_pkg->cdi[cdi_idx].mea_z[3] - config->installAngle) < 45
            )
        {
            // 需要为静态点
            if((cdi_pkg->cdi[cdi_idx].status & POINT_STATUS_DYNAMIC_BMP) == 0) /*fabsf(gVehicleInfo.Speed - cdi_pkg->cdi[cdi_idx].vy) < 1.5*/				//
            {
                useCnt++;
            }
            else
            {
                moveCnt++;
            }
        }
    }
    //在判断距离内有5个运动点，场景不满足
    if(moveCnt >= 5)
    {
        ret = -1;
    }
    else if(useCnt >= 8)
    {
        ret = 0;
    }
    return ret;
}

/**
 * @brief 车相关的条件判断
 * @param freezedVehDyncDataAddr 
 * @return int 根据状态返回对应的值
 * 
 */
int ALN_DynamicEolConditionCheck(const VDY_DynamicEstimate_t *freezedVehDyncDataAddr)
{
    //车速限制 // 30 <= speed <= 150
    int DynamicResult = 0;

    if(fabsf(freezedVehDyncDataAddr->vdySpeedInmps) < ALN_VDY_CONDITION_MIN_SPEED)
    {
        dynamicRunStatus |= 0x01;
        DynamicResult = CALC_FAILED_SPEED_OUT;
    }
    else
    {
         dynamicRunStatus &= 0xfE;
    }

    if(fabsf(freezedVehDyncDataAddr->vdySpeedInmps) > ALN_VDY_CONDITION_MAX_SPEED)
    {
        dynamicRunStatus |= 0x02;
        DynamicResult =  CALC_FAILED_SPEED_OUT;
    }
    else
    {
        dynamicRunStatus &= 0xFD;
    }

    // 加速度限制 // a <= 0.5
    if(fabsf(freezedVehDyncDataAddr->vdyAccelLong) > ALN_VDY_CONDITION_MAX_ACC)
    {
        dynamicRunStatus |= 0x08;
        DynamicResult = CALC_FAILED_ACC;
    }
    else
    {
        dynamicRunStatus &= 0xF7;
    }

    //偏航率限制
    if(fabsf(freezedVehDyncDataAddr->vdyYawRate) > ALN_VDY_CONDITION_MAX_YAWRATE)
    {
        DynamicResult = CALC_FAILED_YAWRATE;
        dynamicRunStatus |= 0x04;
    }
    else
    {
         dynamicRunStatus &= 0xFB;
    }
    return DynamicResult;
}
/**
 * @brief 
 * @return int 0:还没有超时，其他：已经超时
 */
static int ALN_checkTimeout(const ADAS_TimeClase_t *pTimeClass)
{
// #ifndef PC_DBG_FW
//     uint32_t curRtcTime = RTC_TO_MS(timer_rtc_count());
// #else
    uint32_t curRtcTime = pTimeClass->adasTimeCnt;
// #endif
    uint32_t timeout = 0;
    if (curRtcTime >= mCtDynamicEolInfo.start_time)
    {
        timeout = curRtcTime - mCtDynamicEolInfo.start_time;
    }
    else
    {
        timeout = (0xFFFFFFFF - mCtDynamicEolInfo.start_time) + curRtcTime;
    }
    if (timeout > ALN_SERVICE_DYNAMIC_EOL_TIMEOUT)
    {
        return -1;
    }
    else
    {
        return 0;
    }
}

/**
 * @brief 动态标定主程序
 *  这段代码的核心思想是利用多个目标的速度数据来找到一个最佳的修正角度，使得在该角度下，速度的方差
 *  最小，从而使传感器的数据更准确。
 *
 * @param cdi_pkg 原始目标数据指针
 * @param freezedVehDyncDataAddr 车身动态数据指针
 * @return int
 */
int ALN_DynamicEolAngleCalcMainFun(const cdi_pkg_t *cdi_pkg, const VDY_DynamicEstimate_t *freezedVehDyncDataAddr, float time)
{
    timeClass.trackTime = time;
    timeClass.adasTimeCnt++; // 不用做溢出操作， 2^32*50/1000/60/60/24/365=6.8 year
    uint8_t objCnt = 0;
    int   i = 0, k = 0;
    int   cdi_idx, flagValue = 0, minLeastIdx = 0;
    float a, aFit;
    rdp_config_t *config = RDP_getTrackConfigPointer();
    int   ret = 0;
    
    //前行的情况下处理
    if (freezedVehDyncDataAddr->vdyDriveDirection == 1)
    {
        return -1;
    }

    mCtDynamicEolInfo.run_step |= 0x01;

    //是否已经进入服务标定
    if (ALN_routineSt == ROUTINE_ST_STOP)
    {
        return CALC_UNCAL_END;
    }

    mCtDynamicEolInfo.run_step |= 0x02;

    ret = ALN_DynamicEolConditionCheck(freezedVehDyncDataAddr);
    if (0 != ret)
    {
        gServiceAngleCalcState = ret;
        return -1;
    }
    mCtDynamicEolInfo.run_step |= 0x04;
    //时间是否超时
// #ifndef PC_DBG_FW
//     if (0 != ALN_checkTimeout(NULL)) // 最大5分钟
// #else
    if (0 != ALN_checkTimeout(&timeClass)) // 最大5分钟
// #endif 
    {
        gServiceAngleCalcState = CALC_FAILED_TIMEOUT;
        ALN_routineSt = ROUTINE_ST_STOP;
        //更新状态
        return -1;
    }
    mCtDynamicEolInfo.run_step |= 0x08;
    //从转弯恢复到直道，增加一点缓冲时间
    if (fabsf(freezedVehDyncDataAddr->vdyCurveRadius) < 2000)
    {
        gStraightRoadCnt = 0;
        return -1;
    }
    else
    {
        gStraightRoadCnt++;
    }

    if (gStraightRoadCnt < MAX_STRAIGHT_ROAD_CNT)
    {
        return -1;
    }
    gStraightRoadCnt = MAX_STRAIGHT_ROAD_CNT;

    mCtDynamicEolInfo.run_step |= 0x10;

    if (cdi_pkg->number < 15) // 0
    {
        return -1;
    }
    mCtDynamicEolInfo.run_step |= 0x20;
    if (0 != ALN_roadSceneCheckForCalcAngle(cdi_pkg)) // 道路场景检测
    {
        return -1;
    }

    mCtDynamicEolInfo.run_step |= 0x40;
    if (ALN_Mode == AUTO_ALN)
    {
        ALN_initVar();
    }
    ALN_Mode = SERVICE_DIAG_ALN;

    for (i = 0; i < ALL_ANGLE_CNT; i++)
    {
        vBuf[i] = 0;
    }
    for (cdi_idx = 0; cdi_idx < cdi_pkg->number; cdi_idx++)
    {
        if (cdi_pkg->cdi[cdi_idx].mea_z[1] < 0.1)
        {
#if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("di_pkg->cdi[cdi_idx].mea_z[1] < 0.1\n");
#endif
            continue;
        }
        flagValue = 0;
        a = cdi_pkg->cdi[cdi_idx].mea_z[3] - config->installAngle;

        if ((cdi_pkg->cdi[cdi_idx].status & POINT_STATUS_DYNAMIC_BMP) == 0                          // 需要为静态点 /*fabsf(gVehicleInfo.Speed - cdi_pkg->cdi[cdi_idx].vy) < 1.5*/                                                                                        //
            && fabsf(cdi_pkg->cdi[cdi_idx].mea_z[2] / freezedVehDyncDataAddr->vdySpeedInmps) > 0.7  // time??
            && fabsf(cdi_pkg->cdi[cdi_idx].mea_z[2] / freezedVehDyncDataAddr->vdySpeedInmps) < 1.05 //
            && cdi_pkg->cdi[cdi_idx].mea_z[1] > 5                                                   // 筛选大于5m
            && cdi_pkg->cdi[cdi_idx].mea_z[1] < 40                                                  // 筛选小于40m内的
            && fabsf(cdi_pkg->cdi[cdi_idx].x) < 15.0 && fabsf(a) < 45)
        {
            flagValue = 1;
        }
        if (flagValue == 0)
        {
            continue;
        }

        //不同速度误差的情况下的累计方差
        //在不同角度上做累计方差,取最小一个
        for (i = 0; i < ALL_ANGLE_CNT; i++)
        {
            aFit = -DBAA_ANFLE_RANGE + DBAA_ANFLE_BIN * i;
            objV[i][objCnt] = cdi_pkg->cdi[cdi_idx].mea_z[2] / cosf((a + mCtDynamicPara.fixAngleOffset + aFit) / 180 * PI);
            vBuf[i] += objV[i][objCnt];
        }
        objCnt++;
        gDbaaAllObjCnt++;

        if (objCnt >= MAX_OBJ_CNT_FOR_CALC)
        {
            break;
        }
    }
    mCtDynamicEolInfo.run_step |= 0x80;

#if PRRINT_ALN_DBG_OUTPUT
    EMBARC_PRINTF("objCnt=%d, gDbaaAllObjCnt=%d\n", objCnt, gDbaaAllObjCnt);
#endif

    if (objCnt > 0)
    {
        for (i = 0; i < ALL_ANGLE_CNT; i++)
        {
            gDbaaSaureBufferOneFrame[i] = 0.0f;
            agvBuf[i] = vBuf[i] / objCnt;
        }

        for (i = 0; i < ALL_ANGLE_CNT; i++)
        {
            for (k = 0; k < objCnt; k++)
            {
                // 内层for遍历结束之后，在i角度上所有的objCnt点的方差和，
                gDbaaSaureBufferOneFrame[i] += (objV[i][k] - agvBuf[i]) * (objV[i][k] - agvBuf[i]);
            }
            gDbaaSaureBufferOneFrame[i] = gDbaaSaureBufferOneFrame[i]/objCnt; // objCnt个点在i角度分量上的方差值

        }
        //判断最小是否满足要求
        float minDbaaS = gDbaaSaureBufferOneFrame[0];
        for (i = 1; i < ALL_ANGLE_CNT; i++)
        {
            if(minDbaaS > gDbaaSaureBufferOneFrame[i])
            {
                minDbaaS = gDbaaSaureBufferOneFrame[i]; // 找到在i角度分量方差值最小的值

            }
        }

        mCtDynamicEolInfo.run_step |= 0x100;
        //这个值可能不一定对，初步统计出来的
        if (minDbaaS > 0.055f) // 在i角度分量最小的方差值要<=0.055
        {
            //认为随机性大，本次校准循环不用
            gDbaaAllObjCnt = gDbaaAllObjCnt - objCnt;
#if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("minDbaaS=%f,objCnt=%d,gDbaaAllObjCnt=%d\r\n",minDbaaS , objCnt ,gDbaaAllObjCnt);
#endif

            return -1;
        }
        mCtDynamicEolInfo.run_step |= 0x200;
        for (i = 0; i < ALL_ANGLE_CNT; i++)
        {
            gDbaaSaureBuffer[i] += gDbaaSaureBufferOneFrame[i];
        }
    }

    /**
     * @brief 多帧数据处理完成 
     * 
     */
    mCtDynamicEolInfo.run_step |= 0x400;
    mCtDynamicEolInfo.obj_cnt = gDbaaAllObjCnt;

    if (gDbaaAllObjCnt >= MAX_NUMBER_FOR_CALC_OFFSET)
    {
         gDbaaAllObjCnt = 0;
         mCtDynamicEolInfo.run_per = 100;
        //查找最小方差时对应的角度分量i
        for (i = 0; i < ALL_ANGLE_CNT; i++)
        {
            minLeastIdx = gDbaaSaureBuffer[i] < gDbaaSaureBuffer[minLeastIdx] ? i : minLeastIdx;
        }

#if PRRINT_ALN_DBG_OUTPUT
        EMBARC_PRINTF("minLeastIdx=%d\n", minLeastIdx);
#endif

        ALN_routineSt = ROUTINE_ST_STOP;

        //计算补偿角度， 获得最有可能的目标角度和当前雷达法线角度的偏差值
        horizontalDeviationAngle = -DBAA_ANFLE_RANGE + DBAA_ANFLE_BIN * minLeastIdx;

        if (horizontalDeviationAngle > ALN_DYNAMIC_MAX_SET_OFFSET_ANGLE)
        {
            gServiceAngleCalcState = CALC_FAILED_ANGLE_MAX_OUT;
        }
        else if (horizontalDeviationAngle < (-ALN_DYNAMIC_MAX_SET_OFFSET_ANGLE))
        {
            gServiceAngleCalcState = CALC_FAILED_ANGLE_MIN_OUT;
        }
        else
        {
            mCtDynamicEolInfo.new_fix_angle = mCtDynamicPara.fixAngleOffset + horizontalDeviationAngle;
            gServiceAngleCalcState = CALC_SUCCESS_END;
        }

        memset((void *)gDbaaSaureBuffer, 0, sizeof(gDbaaSaureBuffer));
    }
    else
    {
        //统计百分数
        mCtDynamicEolInfo.run_per = gDbaaAllObjCnt * 100 / MAX_NUMBER_FOR_CALC_OFFSET;
    }

    return 0;
}

void ALN_initParaForDynamic(void *pParam)
{
// #ifndef PC_DBG_FW
//     (void)pParam;
// #else
    ADAS_TimeClase_t *pTimeClass = (ADAS_TimeClase_t *)pParam;
// #endif
    switch (CFG_getRadarId())
    {
#if ( defined(VEHICLE_TYPE_BYD_HA5) || defined(VEHICLE_TYPE_BYD_EM2) || defined(VEHICLE_TYPE_BYD_UR) )
    case 4:
    {
        mCtDynamicPara.fixAngleOffset = get_install_message()->aln_execpt_angle_rcr; //ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 5:
    {
        mCtDynamicPara.fixAngleOffset = get_install_message()->aln_execpt_angle_rcr; //ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 6:
    {
        mCtDynamicPara.fixAngleOffset = get_install_message()->aln_execpt_angle_fcr; //ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    case 7:
    {
        mCtDynamicPara.fixAngleOffset = get_install_message()->aln_execpt_angle_fcr; //ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    default:
    {
        mCtDynamicPara.fixAngleOffset = get_install_message()->aln_execpt_angle_rcr; //ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
#else
    case 4:
    {
        mCtDynamicPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 5:
    {
        mCtDynamicPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 6:
    {
        mCtDynamicPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    case 7:
    {
        mCtDynamicPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    default:
    {
        mCtDynamicPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
#endif
    }

    gServiceAngleCalcState = CALC_START;
    dynamicRunStatus = 0;
    mCtDynamicEolInfo.routine_status = ROUTINE_STATUS_ACTIVE;
    mCtDynamicEolInfo.hdev_angle = 0;
    mCtDynamicEolInfo.routine_result = ROUTINE_RESULT_NO_RESULT;
    mCtDynamicEolInfo.run_status = CALC_UNCAL_END;
    mCtDynamicEolInfo.run_counter = 0;
    mCtDynamicEolInfo.obj_cnt = 0xffff;
    mCtDynamicEolInfo.run_step = 0;
    mCtDynamicEolInfo.is_save_to_nvm = false;
    mCtDynamicEolInfo.run_per = 0;
// #ifndef PC_DBG_FW
//     mCtDynamicEolInfo.start_time = RTC_TO_MS(timer_rtc_count());
// #else
    mCtDynamicEolInfo.start_time = pTimeClass->adasTimeCnt;
// #endif 
    mCtDynamicEolInfo.new_fix_angle = 0;
    mCtDynamicEolInfo.previous_fix_angle = CFG_getRadarInstallAngle();
  
    ALN_routineSt = ROUTINE_ST_START;
}



/**
 * @brief 
 *  没有调用，在app_nm_client.c中
 * 
 * @param param 
 */
void StartALN_DynamicEolFun(void *param, void *pParam)
{
    ADAS_TimeClase_t *ptimeClass = (ADAS_TimeClase_t *)pParam;
    setALN_DynamicEolTimer();
    if(getStartTime() >= 900)    // 900s 15分钟
	{
		if(ALN_routineSt == ROUTINE_ST_STOP)
		{
            ALN_initParaForDynamic(ptimeClass); // 开始标定
            ALN_initVar();
		}
		if(getStartTime() >= 1080)   //开始标定三分钟内
		{
			rstALN_DynamicEolTimer(); //时间归0重新计算
			ALN_routineSt = ROUTINE_ST_STOP; //结束标定  三分钟内没有标定成功，则结束标定，等下一个15分钟再开始标定 
		}
        if(gServiceAngleCalcState == CALC_FAILED_ANGLE_MAX_OUT || gServiceAngleCalcState == CALC_FAILED_ANGLE_MIN_OUT)  //校准角度过大或者过小则上报问题。
        {
            ALN_DynamicEolFun_Flag = 1;//上报DTC
        }
        else if(gServiceAngleCalcState == CALC_SUCCESS_END)
        {
            ALN_DynamicEolFun_Flag = 0;
        }

	}
}

/**
 * @brief
 *
 * @param subServerNumber
 */

uint8_t getALN_DynamicEolFun_Flag()
{
    return ALN_DynamicEolFun_Flag;
}

/**
 * @brief
 *
 * @param subServerNumber
 */

void Service31Handle_DynamicEol(int subServerNumber)
{
    // EMBARC_PRINTF("%s subFunction=%d\r\n", __FUNCTION__, subServerNumber); 
// #ifndef PC_DBG_FW
//     (void)pParam;
// #else
    ADAS_TimeClase_t *pTimeClass = (ADAS_TimeClase_t *)(&timeClass);
    // #endif
    switch (subServerNumber)
    {
        case 0x01:
        {
            ALN_initParaForDynamic(pTimeClass);
#if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("[uds 31 01] DynamicEol gServiceAngleCalcState=%d\r\n",gServiceAngleCalcState);
            #endif
            rstALN_DynamicEolTimer();  //标定指令进来，则结束15分钟计时
            ALN_initVar();
            break;
        }
        case 0x02:
        {
            if (ALN_routineSt == ROUTINE_ST_START)
            {
                //如果还在运行，用户主动退出，增加记录
                //add here
            }

            // TODO:增加中断状态
            gServiceAngleCalcState = CALC_STOP;
            mCtDynamicEolInfo.routine_status = ROUTINE_STATUS_INACTIVE;
            ALN_routineSt = ROUTINE_ST_STOP;
#if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("[uds 31 02] DynamicEol gServiceAngleCalcState=%d\r\n", gServiceAngleCalcState);
#endif
            break;
        }
        case 0x03:
        {
            switch (gServiceAngleCalcState)
            {
                case CALC_STOP:
                {
                    mCtDynamicEolInfo.routine_status = ROUTINE_STATUS_INACTIVE;
                    break;
                }
                case CALC_START:
                case CALC_UNCAL_END:
                {
                    mCtDynamicEolInfo.routine_status = ROUTINE_STATUS_ACTIVE;
                    break;
                }
                // 0x02存FLASH失败，暂时没有存储功能
                // case CALC_STOP:
                //     service31RespX1Low = 0x02;
                //     break;
                case CALC_FAILED_TIMEOUT:
                {
                    mCtDynamicEolInfo.routine_status = ROUTINE_STATUS_TIMEOUT;
                    mCtDynamicEolInfo.routine_result = ROUTINE_RESULT_NO_RESULT;
                    break;
                }
                case CALC_SUCCESS_END:
                {
                    mCtDynamicEolInfo.routine_status = ROUTINE_STATUS_FINCORRECTLY;
                    //存储flash，需要考虑上报78操作，不确定是否支持78上报
                    mCtDynamicEolInfo.is_save_to_nvm = true;
                    mCtDynamicEolInfo.routine_result = ROUTINE_RESULT_CORRECT;
                    mCtDynamicEolInfo.hdev_angle = horizontalDeviationAngle;        //偏差值
                    break;
                }
                case CALC_FAILED_ANGLE_MAX_OUT:
                case CALC_FAILED_ANGLE_MIN_OUT:
                case CALC_FAILED_MUL_ERR:
                {
                    mCtDynamicEolInfo.routine_status = ROUTINE_STATUS_ABORTED;
                    mCtDynamicEolInfo.routine_result = ROUTINE_RESULT_INCORRECT;
                    mCtDynamicEolInfo.hdev_angle = horizontalDeviationAngle;
                    break;
                }
                case CALC_FAILED_SPEED_OUT:
                case CALC_FAILED_ACC:
                case CALC_FAILED_YAWRATE:
                {
                    mCtDynamicEolInfo.routine_status = ROUTINE_STATUS_ACTIVE;
                    mCtDynamicEolInfo.routine_result = ROUTINE_RESULT_NO_RESULT;
                    break;
                }
                default:
                {
                    mCtDynamicEolInfo.routine_status = ROUTINE_STATUS_INACTIVE;
                    break;
                }
            }
            mCtDynamicEolInfo.run_status = gServiceAngleCalcState;
#if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("[uds 31 03] gServiceAngleCalcState=%d\r\n", gServiceAngleCalcState);
#endif
            break;
        }
        default:
        {
            break;
        }
    }

    return;
}

uint8_t getBYD_DynamicRunStatus()
{
    return dynamicRunStatus;
}
#ifdef PC_DBG_FW
typedef void (* Service31Handle_Eol_CB)(int subServerNumber, void* pParam);
void (*pfALN_EOL_toStop)(void);
Service31Handle_Eol_CB pfService31Handle_Eol = NULL;

ALN_DYNAMIC_EOL_T aln_dynamic_eol = {.last_aln_status = ALN_DYNAMIC_EOL_OK};
uint8_t dynamic_aln_print = 0;

void ALN_initEOLMode(void)
{
    ALN_Mode = SERVICE_DIAG_ALN;
    aln_dynamic_eol.last_aln_status = ALN_DYNAMIC_EOL_OK;
}

void ALN_EOL_toStop(void)
{
    if (pfALN_EOL_toStop != NULL)
    {
        pfALN_EOL_toStop();
    }
}

void ALN_DynamicEOL_setRun(uint8_t mode)
{
    if (aln_dynamic_eol.last_aln_status == ALN_DYNAMIC_EOL_OK)
    {
        ALN_EOL_toStop(); // 先停止，再运行，可能会有数据非0
        aln_dynamic_eol.last_aln_status = ALN_DYNAMIC_EOL_TOSTART; 
    }
}

void ALN_DynamicEOL_setEolType(uint8_t type)
{
    if (aln_dynamic_eol.last_aln_status == ALN_DYNAMIC_EOL_OK)
    {
        if (type == OEM_STATIC_ALN)
        {
            pfService31Handle_Eol = Service31Handle_StaticALNAngleCalc;
            pfALN_EOL_toStop = ALN_StaticEOL_toStop;
        }
        else
        {
            pfService31Handle_Eol = Service31Handle_DynamicEol;
            pfALN_EOL_toStop = ALN_DynamicEOL_toStop;
        }
        ALN_Mode = (ALN_CalibType_t)type;
    }
}

/**
 * @brief 设置ALN 动态标定类型
 * @param type              AUTO_ALN：自动标定
 *                          SERVICE_DIAG_ALN 服务标定
 */
static void ALN_SetDynamicCalibType(ALN_CalibType_t type)
{
    /// 自标定与动态标定切换中，需要清除数据
    if (ALN_Mode != type)
    {
        ALN_Mode = type;
        // EMBARC_PRINTF("%s >> 1 \n", __FUNCTION__);
        ALN_initVar();
    }
}

void Service31Handle_CaltEol(int subServerNumber, void* pParam)
{
    if (pfService31Handle_Eol != NULL)
    {
        pfService31Handle_Eol(subServerNumber, pParam);
    }
}

/**
 * @brief 仅仿真调用 动态标定流程实现，需要周期调用，实际算法实现在ALN_DynamicEolAngleCalcProcess()函数
 *
 */
void ALN_DynamicEolAngleCalc_WorkflowProcess(const ADAS_TimeClase_t *pTimeClass)
{
#if ALN_CAN_DBG_PRINT == 1
    uint8_t radar_id = 0;
    uint16_t print_can_id = 0;
#endif
    ALN_CalibType_t aln_dynamic_type = SERVICE_DIAG_ALN;
    CT_EOL_DCR_t *serviceInfo = NULL;
    CT_DYNAMIC_EOL_INFO_t *dynamicInfo = NULL;

    // EMBARC_PRINTF("%s >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\n", __FUNCTION__);

    ///首先获取当前标定类型
    aln_dynamic_type = SERVICE_DIAG_ALN; // ALN_GetDynamicCalibType();

#if ALN_CAN_DBG_PRINT == 1
    radar_id = CFG_getRadarId();

    if(radar_id == 4)
    {
        print_can_id = 0x566;
    }
    else if(radar_id == 5)
    {
        print_can_id = 0x567;
    }
    else if(radar_id == 7)
    {
        print_can_id = 0x568;
    }
    else
    {
        print_can_id = 0x569;
    }
#endif

    // EMBARC_PRINTF("%s >> 1 aln_dynamic_type=%d\n", __FUNCTION__, aln_dynamic_type);

    ///如果不是自动标定类型，则需要退出
    if (aln_dynamic_type != SERVICE_DIAG_ALN)
    {
        aln_dynamic_eol.state = ALN_DYNAMIC_EOL_START;
        // aln_dynamic_eol.last_aln_tick = RTC_TO_MS(timer_rtc_count());
        aln_dynamic_eol.last_aln_tick = pTimeClass->adasTimeCnt;
        aln_dynamic_eol.last_aln_status = ALN_DYNAMIC_EOL_OK;
#if ALN_CAN_DBG_PRINT == 1
        FAST_PRINT_CAN(print_can_id, "ID:%d:Do Dynamic\r\n", radar_id);
#endif
        dynamic_aln_print = 0;
        return ;
    }
    // EMBARC_PRINTF("%s >> 2 state=%d\n", __FUNCTION__, aln_dynamic_eol.state);

    ///自标定状态判断
    switch(aln_dynamic_eol.state)
    {
        case ALN_DYNAMIC_EOL_START: ///开始服务标定
        {
            if (aln_dynamic_eol.last_aln_status == ALN_DYNAMIC_EOL_OK)
            {
#if ALN_CAN_DBG_PRINT == 1
                FAST_PRINT_CAN(print_can_id, "ID:%d:STANDBY\r\n", radar_id);
#endif
                dynamic_aln_print = 0;
                return;
            }

            // EMBARC_PRINTF("%s >> 4 \n", __FUNCTION__);

            if (ALN_Mode == SERVICE_DIAG_ALN)
            {
                ///设置动态标定模式
                ALN_SetDynamicCalibType(SERVICE_DIAG_ALN);
            }

            ///请求服务
            Service31Handle_CaltEol(0x01, pTimeClass);

            // EMBARC_PRINTF("%s >> 5 START Service31Handle_DynamicEol\n", __FUNCTION__);

            ///设置新的状态
            aln_dynamic_eol.state = ALN_DYNAMIC_EOL_RUNNING;
            aln_dynamic_eol.last_aln_result = ALN_DYNAMIC_RUNNING;

            break;
        }

        case ALN_DYNAMIC_EOL_RUNNING: // 动态标定运行

            ///请求结果
            Service31Handle_CaltEol(0x03, pTimeClass);
            // EMBARC_PRINTF("%s >> 6 RUNNING period\n", __FUNCTION__);

            ///获取标定状态
            uint8_t run_status = 0u;
            float new_fix_angle = 0.0f;
            // pDynamic_eol_ressult = ALN_getDynamicEolInfo();
            if (ALN_Mode == OEM_STATIC_ALN)
            {
                serviceInfo = ALN_getStaticEolAlnInfo();
                run_status = serviceInfo->run_status;
                new_fix_angle = serviceInfo->new_fix_angle;
            }
            else
            {
                dynamicInfo = ALN_getDynamicEolInfo();
                run_status = dynamicInfo->run_status;
                new_fix_angle = dynamicInfo->new_fix_angle;
            }
            // EMBARC_PRINTF("%s >> 7 RUNNING pDynamic_eol_ressult, run_status=%d\n", __FUNCTION__, run_status);

            ///判断标定状态
            // mCtDynamicEolInfo.run_status = gServiceAngleCalcState;
            switch(run_status)
            {
                ///角度偏差大，记录标定失败计数
                case CALC_FAILED_ANGLE_MAX_OUT:
                case CALC_FAILED_ANGLE_MIN_OUT:
                    aln_dynamic_eol.last_aln_status = ALN_DYNAMIC_EOL_ERR;
                    aln_dynamic_eol.state = ALN_DYNAMIC_EOL_END;
                    aln_dynamic_eol.last_aln_result = ALN_DYNAMIC_MAX_MIN;
                    dynamic_aln_print = 2;
#if ALN_CAN_DBG_PRINT == 1
                    FAST_PRINT_CAN(print_can_id, "ID: FAIL\r\n", radar_id);
#endif
                    // EMBARC_PRINTF("%s >> 8 RUNNING MAX_OUT=%d\n", __FUNCTION__);
                    break;

                ///标定成功，清0标定失败计数
                case CALC_SUCCESS_END:
                    aln_dynamic_eol.last_aln_status = ALN_DYNAMIC_EOL_OK;
                    aln_dynamic_eol.state = ALN_DYNAMIC_EOL_END;
                    aln_dynamic_eol.last_aln_result = ALN_DYNAMIC_END;
                    dynamic_aln_print = (uint8_t)(new_fix_angle + 0.5f);
#if ALN_CAN_DBG_PRINT == 1
                    FAST_PRINT_CAN(print_can_id, "ID:%d:SUCCESS,Angle:%.2f\r\n", radar_id, new_fix_angle);
#endif

                    // EMBARC_PRINTF("%s >> 9 RUNNING\n", __FUNCTION__);

#if 1
                    // Alg_setDynamicEOLCaliData(new_fix_angle);
#endif
                    break;

                ///标定超时,标定结束，设置失败，但是不记录失败计数
                case CALC_FAILED_TIMEOUT:
                case CALC_STOP:
                    aln_dynamic_eol.last_aln_status = ALN_DYNAMIC_EOL_ERR;
                    aln_dynamic_eol.state = ALN_DYNAMIC_EOL_END;
                    aln_dynamic_eol.last_aln_result =ALN_DYNAMIC_TIMEOUT;
                    dynamic_aln_print = 2;
#if ALN_CAN_DBG_PRINT == 1
                    FAST_PRINT_CAN(print_can_id, "ID:%d:TIMEOUT\r\n", radar_id);
#endif
                    // EMBARC_PRINTF("%s >> 10 RUNNING\n", __FUNCTION__);
                    break;

                ///开始标定，标定未完成，车身信号失败，目标不达标，则继续标定
                case CALC_START:
                case CALC_UNCAL_END:
                case CALC_FAILED_SPEED_OUT:
                case CALC_FAILED_ACC:
                case CALC_FAILED_YAWRATE:
                case CALC_FAILED_MUL_ERR:
                dynamic_aln_print = 3;
#if ALN_CAN_DBG_PRINT == 1
                    FAST_PRINT_CAN(print_can_id, "ID:%d:DOING\r\n", radar_id);
#endif
                    // EMBARC_PRINTF("%s >> 11 RUNNING\n", __FUNCTION__);
                default:
                    break;
            }
            break;

        case ALN_DYNAMIC_EOL_END: // 自标定结束
            // EMBARC_PRINTF("%s >> 12 RUNNING\n", __FUNCTION__);

            ///请求服务
            Service31Handle_CaltEol(0x02, pTimeClass);
            // EMBARC_PRINTF("%s >> 13 RUNNING\n", __FUNCTION__);

            aln_dynamic_eol.state = ALN_DYNAMIC_EOL_START;
            aln_dynamic_eol.last_aln_result = ALN_DYNAMIC_INACTIVE;
            // EMBARC_PRINTF("%s >> 14 RUNNING state=%d, RUNNING last_aln_result=%d\n", __FUNCTION__, aln_dynamic_eol.state, aln_dynamic_eol.last_aln_result);

        default:
            break;
    }
}

#endif

