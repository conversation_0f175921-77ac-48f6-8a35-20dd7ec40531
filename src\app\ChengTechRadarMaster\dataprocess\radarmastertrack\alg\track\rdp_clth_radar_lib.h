﻿/**
 * @file rdp_clth_radar_lib.h
 * <AUTHOR> (s<PERSON><PERSON>@chengtech.net)
 * @brief 
 * @version 0.1
 * @date 2022-10-09
 * 
 * @copyright Copyright (c) 2022
 * 
 */

#ifndef _CLTH_RADAR_LIB_H_
#define _CLTH_RADAR_LIB_H_

//--- include --------------------------
#ifndef PC_DBG_FW
#include "vdy_types.h"
#include "rdp_track_listlib.h"
#include "rdp_track_struct.h"
#include "sortlib.h"
#else
#include "app/vehicle/vdy/vdy_interface.h"
#include "alg/track/rdp_track_listlib.h"
#include "alg/track/rdp_track_struct.h"
#endif

#define MAX_CHAR  (127)

#define REL_STATIC 0x1                       //相对静止目标
#define NEG_STATIC 0x2                       //与车速相反的目标，为静止目标
#define ZERO_VEL 0x4                         //0速度物体
#define ABS_STATIC (REL_STATIC | NEG_STATIC) //绝对静止目标，与车速相近且速度为0

#define CDI_STATUS_NONE 0   //无效
#define CDI_STATUS_NORMAL 1 //一般正常使用
#define CDI_STATUS_NOT_MAX_MAG 2    //一组中幅值不大的点 
#define CDI_STATUS_CHECKED_FOR_TRK  3 //已经进行过匹配的点
#define CDI_STATUS_PRE_FILTER   4   //预过滤的点，在没有条件下不使用，比如不会进行起批，但是会用于匹配
#define CDI_STATUS_AUTO_CAIL    5   //满足条件用于自校准操作，这个条件的时候必须放在跟踪后，中间的跟踪过程都不会使用到，所以自校准和售后校准需要放在最后
#define CDI_STATUS_SELF_CAIL    6   //售后自标定，这个条件的时候必须放在跟踪后，中间的跟踪过程都不会使用到，所以自校准和售后校准需要放在最后
#define CDI_STATUS_NEAR_FILTER  7   //对前面进入的目标进行匹配和后面来的目标匹配的条件不一样，这个标志只匹配近距离的，速度相对小
#define CDI_STATUS_B_FILTER  8   //对后方点进行匹配限制
#define CDI_STATUS_ONE_ANT  9   //单天线检测到，主要针对近距离 10m内距离
#define CDI_STATUS_TWO_ANT  10   //单天线检测到，主要针对近距离 10m内距离
#define CDI_STATUS_ONE_ANT_S_SPD  11   //单天线检测到，速度小于 1 m/s
#define CDI_STATUS_NOT_FOR_NEW  12   //不用于起批
#define CDI_STATUS_SMALL_SPD  13   //近距离小速度，但是非0的速度点
#define CDI_STATUS_MAG_BIG  14  //与近距离噪声比较后大于 12db 的点 

#define CHECK_PROPERTY(property, dst) ((property & dst) == dst)

#define TRK_VY(trk) ((trk).sim_z[2] * (trk).sim_z[1] / (trk).x[1])

#define SORT_VEL(bit) (0x00 | (1 << (bit)))

#define SET_BIT(x, y) x |= (1 << y)
#define CLR_BIT(x, y) x &= ~(1 << y)
#define GET_BIT(x, y) ((x) >> (y)&1)
#define GET_FLAG(x, y, z) \
  if (GET_BIT(x, y))      \
    SET_BIT(z, y);        \
  else                    \
    CLR_BIT(z, y);
//静止 / 运动，左护栏 / 正常，右护栏 / 正常，井盖 / 正常，天桥 / 正常，正前方车道静止目标 / 正常
#define SORT_UNMOVE_BIT 0  //绝对静止点
#define SORT_LFENCE_BIT 1  //左护栏
#define SORT_RFENCE_BIT 2  //右护栏
#define SORT_COVER_BIT 3   //井盖
#define SORT_BRIDGE_BIT 4  //天桥
#define SORT_MSTATIC_BIT 5 //正前方车道静止目标

#define SORT_UNMOVE_THRESHOLD 1.5f //绝对静止速度门限 ±

#define POINT_ID_INVALID                    (-1)
#define POINT_ID_CLUSTERED                  (-2)

#define POINT_STATUS_DYNAMIC_BMP                0x01    // 动态
#define POINT_STATUS_ASSOCIATED_BMP             0x02    // 关联状态
#define POINT_STATUS_ACTIVE_BMP                 0x04    // 关联到有效Track航迹
#define POINT_STATUS_CLUSTERED_BMP              0x08    // 已聚类
#define POINT_STATUS_DEBLUR_FAILED              0x10    // 速度解模糊异常
#define POINT_STATUS_MULTI_OBJ_PER_BIN          0x20    // 双目标
#define POINT_STATUS_ABNORMAL_BMP               0x40    // 异常点（俯仰异常+信噪比异常）通道一致性相差很大？
#define POINT_STATUS_DYNAMIC_CERTAIN_BMP        0x80    // 动态确信位。抵消自车速度延迟上报后，仍为动态时置位

#define POINT_STATUS_GUARDRAIL_BMP              0x0100  //护栏目标
#define POINT_STATUS_GUARDRAIL_OUTSIDE_BMP      0x0200  //护栏外的目标
#define POINT_STATUS_REVERSE_BMP                0x0400
#define POINT_STATUS_STATIC_UNCERTAIN_BMP       0x0800  //疑似运动目标
#define POINT_STATUS_BIAS_VELOCITY_BMP          0x1000
#define POINT_STATUS_IN_DEADZONE_BMP            0x2000
#define POINT_STATUS_DYNAMIC_KEEP_BMP           0x4000  //当前为运动时，判定为运动的状态。
#define POINT_STATUS_ACTIVE_MOVING_BMP          0x8000


#define DISTANCE_ACCURACY                       (0.2f)    // unit: m
#define DISTANCE_ACCURACY_RECIPROCAL            (1.0f/DISTANCE_ACCURACY)

#define TRACK_STATUS_MOVING_BMP                 0x01
#define TRACK_STATUS_ASSOCIATED_BMP             0x02
#define TRACK_STATUS_WEAK_ASSOCIATED_BMP        0x04    // It's not associated in association period, but has related point in clustering.
#define TRACK_STATUS_GUARDRAIL_BMP              0x08
#define TRACK_STATUS_VELOCITY_CONFIRMED_BMP     0x10
#define TRACK_STATUS_EVER_NONCROSSED_MOVED_BMP  0x20    //track ever moved at noncrossed area
#define TRACK_STATUS_REVERSE_BMP                0x40
#define TRACK_STATUS_MOVED_BMP                  0x80    // the track has moved ever
#define TRACK_STATUS_GUARDRAIL_OUTSIDE_BMP      0x0100
#define TRACK_STATUS_STARTFROM_SIDEBLIND_BMP    0x0200
#define TRACK_STATUS_LOCATION_UNCHANGED_BMP     0x0400
#define TRACK_STATUS_PRE_TRECK_OBJ_BMP          0x0800  // 二轮车/人 提前起批标记，（防止2轮车和行人的目标晚期，标记出来提前起批），注意：只有特殊模式下采用
#define TRACK_STATUS_RFCTA_AREA_OBJ_BMP         0x1000  // 标记为F/RCTA的异侧目标（特殊处理）
#define TRACK_STATUS_IN_DEADZONE_BMP            0x2000
#define TRACK_STATUS_CROSSING_BMP               0x4000  // 横穿
#define TRACK_STATUS_ABS_CROSSING_BMP			0x8000  // 严格横穿
#define TRACK_STATUS_SKEW_CROSSING_BMP			0x10000  // 靠近斜穿-100到-160

#define TARGET_CLASS_NUM 4

#define PEDESTRIAN_LENGTH     0.5f               // unit: m
#define PEDESTRIAN_WIDTH      0.5f               // unit: m
#define CLASS_PEDESTRIAN_DISTANCE_TO_BOX_FRONT_INIT      (PEDESTRIAN_LENGTH/2)    // unit: m
#define CLASS_PEDESTRIAN_DISTANCE_TO_BOX_BACK_INIT       (PEDESTRIAN_LENGTH/2)    // unit: m
#define CLASS_PEDESTRIAN_DISTANCE_TO_BOX_LEFT_INIT       (PEDESTRIAN_WIDTH/2)    // unit: m
#define CLASS_PEDESTRIAN_DISTANCE_TO_BOX_RIGHT_INIT      (PEDESTRIAN_WIDTH/2)    // unit: m

#define BICYCLE_LENGTH 2.f //m
#define BICYCLE_WIDTH    1.f //m
#define CLASS_BICYCLE_DISTANCE_TO_BOX_FRONT_INIT       0.5f    // unit: m
#define CLASS_BICYCLE_DISTANCE_TO_BOX_BACK_INIT       (BICYCLE_LENGTH - CLASS_BICYCLE_DISTANCE_TO_BOX_FRONT_INIT)    // unit: m
#define CLASS_BICYCLE_DISTANCE_TO_BOX_LEFT_INIT       (BICYCLE_WIDTH/2)    // unit: m
#define CLASS_BICYCLE_DISTANCE_TO_BOX_RIGHT_INIT      (BICYCLE_WIDTH/2)    // unit: m
#define BICYCLE_BOX_SIDE_LENGTH    1.f //m

#define CAR_LENGTH 4.2f //m
#define CAR_WIDTH   1.8f //m
#define CAR_WIDTH_MIN   1.5f //m
#define CLASS_CAR_DISTANCE_TO_BOX_FRONT_INIT      0.5f    // unit: m
#define CLASS_CAR_DISTANCE_TO_BOX_BACK_INIT       (CAR_LENGTH - CLASS_CAR_DISTANCE_TO_BOX_FRONT_INIT)    // unit: m
#define CLASS_CAR_DISTANCE_TO_BOX_LEFT_INIT       (CAR_WIDTH/2)    // unit: m
#define CLASS_CAR_DISTANCE_TO_BOX_RIGHT_INIT      (CAR_WIDTH/2)    // unit: m
#define CAR_BOX_SIDE_LENGTH    1.5f //m

#define LARGE_CAR_LENGTH 8.f //m
#define LARGE_CAR_WIDTH   2.3f //m
#define CLASS_LARGE_CAR_DISTANCE_TO_BOX_FRONT_INIT      0.5f    // unit: m
#define CLASS_LARGE_CAR_DISTANCE_TO_BOX_BACK_INIT       (CAR_LENGTH - CLASS_CAR_DISTANCE_TO_BOX_FRONT_INIT)    // unit: m
#define CLASS_LARGE_CAR_DISTANCE_TO_BOX_LEFT_INIT       (CAR_WIDTH/2)    // unit: m
#define CLASS_LARGE_CAR_DISTANCE_TO_BOX_RIGHT_INIT      (CAR_WIDTH/2)    // unit: m


#define AEB_STAND_CROSS_X_MIN   (1.0f)          // aeb标准横穿场景区域限定.
#define AEB_STAND_CROSS_X_MAX   (5.0f)
#define AEB_STAND_CROSS_Y_MIN   (12.0f)
#define AEB_STAND_CROSS_Y_MAX   (45.0f)

#define AEB_STAND_CROSS_SPEED_MIN (7.0f)       // aeb标准横穿场景,  自车最小速度
#define AEB_STAND_CROSS_SPEED_MAX (12.5f)      // aeb标准横穿场景,  自车最大速度

// aeb鬼探头自车最小速度
#define AEB_SIDECAR_SPEED_MIN (2.0f)      
#define AEB_SIDECAR_SPEED_MAX (6.5f)      


//车道宽度
#define LANE_WIDTH		  3.75f
#define LANE_WIDTH_HALF	  1.875f

// 异侧两车道
#define FOVEDGE_THRE	    -55.f
#define FOVEDGE_MAX_THRE	-70.f   // 该角度下认为检测点不稳定，航迹跟踪质量较低
#define FOVEDGE_X_THRE  (-2.f - 0.9f - 2 * LANE_WIDTH)

#define STATIC_R_COEF           0.75f
#define TUNNEL_STATIC_R_COEF    0.95f
#define STATIC_VEL_COEF         0.8f
#define STATIC_ACCVEL_COEF      0.4f

#define SIDE_R_COEF             0.75f
#define SIDE_V_COEF             0.75f
#define SIDE_LAT_V_COEF         0.8f
#define SIDE_LNG_V_COEF         0.9f
#define SIDE_ACC_COEF           0.9f

//加速度异常门限
#define LAT_AB_ACC              5.f
#define LNG_AB_ACC              15.f

// 功能报警车速门限
#define BSD_LNGVEL_THR				15 / 3.6f		// BSD区域超车抑制报警纵向速度门限
#define BSD_DEFAULT_LNGVEL_THR		20 / 3.6f		// BSD区域默认纵向速度赋值（默认不报警）

#define ANGLE_IN_VERTICAL_AREA(angle, angleThr) (fabsf(angle) < angleThr || fabsf(angle - 180) < angleThr || fabsf(angle + 180) < angleThr)
#define ANGLE_IN_CROSS_AREA(angle, angleThr) (fabsf(angle - 90) < angleThr || fabsf(angle + 90) < angleThr)
#define LP_FILTER(pre, coef, meas)  ((pre) * (coef) + (1.f - (coef)) * (meas))
#define SEL_MIN(dst, val) if ((val) < (dst)) { (dst) = (val); }
#define SEL_MAX(dst, val) if ((val) > (dst)) { (dst) = (val); }

#define CAL_BOX_WIDTH(lim) ((lim)[1] - (lim)[0])
#define CAL_BOX_LENGTH(lim) ((lim)[1] - (lim)[0])

// gridMap
#define GRID_LAT					3.75f
#define GRID_LNG					5.f
#define GRIDMAP_VALID_LATRANGE_MIN	(0.9f - GRID_LAT)
#define GRIDMAP_VALID_LATRANGE_MAX	(0.9f + 2 * GRID_LAT)
#define GRIDMAP_VALID_LNGRANGE_MIN	(-5.f)
#define GRIDMAP_VALID_LNGRANGE_MAX	(15.f)
#define IN_GRIDMAP_LAT_AREA(lat)    (lat > GRIDMAP_VALID_LATRANGE_MIN && lat < GRIDMAP_VALID_LATRANGE_MAX)
#define IN_GRIDMAP_LNG_AREA(lng)    (lng > GRIDMAP_VALID_LNGRANGE_MIN && lng < GRIDMAP_VALID_LNGRANGE_MAX)
#define IN_GRIDMAP_AREA(lat, lng)	(lat > GRIDMAP_VALID_LATRANGE_MIN && lat < GRIDMAP_VALID_LATRANGE_MAX && lng > GRIDMAP_VALID_LNGRANGE_MIN && lng < GRIDMAP_VALID_LNGRANGE_MAX)
#define GET_GRID_COL(lat)			((uint8_t)((lat - (0.9f - 3.75f)) / GRID_LAT))
#define GET_GRID_ROW(lng)			((uint8_t)((lng + 5.f) / GRID_LNG))
#define GRIDMAP_NEIGHBOUR_MINUS		1
#define GRIDMAP_NEIGHBOUR_PLUS		2

#define IN_BSD_AREA(x, y)			(x > 0.f && x < 0.9f + LANE_WIDTH && y < 5.f)
#define IN_HONE_LANE(x)             (x > (-0.9f - 2.f) && x < 0.9f)                             // 本车道，设置车身宽度为2m
#define IN_NEIGHBOUR_LANE(x)		(x > 0.9f && x < 0.9f + LANE_WIDTH)                         // 邻车道
#define IN_SEPARATED_LANE(x)		(x > 0.9f + LANE_WIDTH && x < 0.9f + 2 * LANE_WIDTH)        // 隔车道
#define IN_NUMTHREE_LANE(x)		    (x > 0.9f + 2 * LANE_WIDTH && x < 0.9f + 3 * LANE_WIDTH)    // 第三车道
#define IN_NEIGHBOUR_TWO_LANE(x)	(x > 0.9f && x < 0.9f + 2 * LANE_WIDTH)                     // 邻/隔车道
#define IN_HOMEANDNEIGHBOUR_LANE(x)	(x > (-0.9f - 2.f) && x < 0.9f + LANE_WIDTH)                // 本/邻车道
#define IN_THREE_LANES(x)	        (x > (-0.9f - 2.f) && x < 0.9f + 2 * LANE_WIDTH)            // 本/邻/隔三车道
#define IN_OTHER_NEIGHBOUR_LANE(x)	(x > (-0.9f - 2.f - LANE_WIDTH) && x < (-0.9f - 2.f))       // 异侧邻车道

// 保杠假点抑制
#define BSD_FAKEDOT_MIN_DIF (3.0f)
#define BSD_FAKEDOT_MID_DIF (5.0f)
#define BSD_FAKEDOT_MAX_DIF (7.0f)
#define BSD_SCENE_MIN_SPD   (35.0f)
#define BSD_SCENE_MID_SPD   (70.0f)
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_FRAME_CNT_MAX    (600U)  // 
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_STARTY_MIN (-0.5f)	//
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_STARTY_MAX (0.0f)	//
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_MAXY_MIN   (-0.5f)	//
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_MAXY_MAX   (0.0f)	//
#define ADAS_POINT_MAX_MOVEMENT_DIFFERENCE                  (3.0f)
#define ADAS_POINT_MAX_MOVEMENT_DIF_FRAME_CNT               (10U)	// 点移动最大帧数CNT
#define ADAS_POINT_TRACKING_FRAME_MAX                       (600U)
#define ADAS_POINT_BOX_LENGTH_THRESHOLD                     (3.0f)	// 目标长度阈值，大于此阈值认为非假点，一票认定 

//--- typedef --------------------------



typedef enum
{
  SOLVE_OK_WITH_UPDATE = 0,
  SOLVE_FAILURE,
} clthRadarSolveReturnType;

#define VALID_GROUP_NUM     MAX_NUM_OF_POINTS
#define ALL_GROUP_NUM       (VALID_GROUP_NUM+2)
#define GROUP_ID_INVALID    VALID_GROUP_NUM
#define GROUP_ID_NONE       (VALID_GROUP_NUM+1)
#define GROUP_ID_START      0
#define GROUP_ID_END        (GROUP_ID_START+VALID_GROUP_NUM-1)

#define GROUP_STATUS_MOVING_BMP             0x01
#define GROUP_STATUS_ASSOCIATED_BMP         0x02
#define GROUP_STATUS_ACTIVE                 0x04

#define SOLVE_GROUR_SUCCESS_VX              0x01 // 根据Group径向速度拟合Vx是否成功
#define SOLVE_GROUR_SUCCESS_VY              0x02 // 根据Group径向速度拟合Vy是否成功


///* guardrail search macro definition */

#define GUARDRAIL_SEARCH_VERTICAL_LEN_MIN	10.0f	// m
#define CROSS_OUT_X_LIMIT					1.f		// m
#define GUARDRAIL_OUT_RANGE					1.5f	// m

typedef struct
{
    uint16_t parkingMode;
    float x;
    float y;
    float angle;
} BasementWallPoint;

typedef struct
{
    uint16_t parkingMode;
    float x;
    float y;
    float angle;
} SideCarPoint;

extern TrackGroupInfo_t gGroupInfo[ALL_GROUP_NUM];
extern GTrack_ListElem gPointListElem[MAX_NUM_OF_POINTS];  //NUM_CANDIDATE
extern float RList[3][3];

//--- functions ------------------------
// main
clthRadarSolveReturnType RDP_solveRadarTrack(cdi_pkg_t *pCdiPkg, TrackGroupInfo_t *pGroupInfo, VDY_DynamicEstimate_t *pRDP_inVehicleData, float time, rdp_config_t* config, trk_pkg_t *pTrkPkg);

// trk functions
void initGlobalVars(void);
void trk_pkg_init();

// track functions
void RDP_Track_predictState(float time, trk_pkg_t *pTrkPkg, rdp_config_t *config, float EKF_A[36]);
void RDP_Track_associateCdi(cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_pkg_t *pTrkPkg, float time);
void RDP_Track_getBestAssociation(cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_pkg_t *pTrkPkg);
void RDP_Track_manageTracks(cdi_pkg_t* pCdiPkg, VDY_DynamicEstimate_t* RDP_inVehicle, TrackGroupInfo_t* pGroupInfo, trk_pkg_t* pTrkPkg, float time);
void RDP_Track_startTracks(cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *RDP_inVehicle, TrackGroupInfo_t *pGroupInfo, trk_pkg_t *pTrkPkg);
void RDP_Track_updateResults(cdi_pkg_t *pCdiPkg, float time, VDY_DynamicEstimate_t *pRDP_inVehicleData
                            , float EKF_A[36], float EKF_Q[36], float EKF_R[9], trk_pkg_t *pTrkPkg);
float getTrackHeading(float vx, float vy, float* cosAngle, float* sinAngle);
void getTrackBoxCenter(float* X, const VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_t* trk);
void getTrackBoxConner(float* X, const VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_t* trk, float x, float y);


#endif

