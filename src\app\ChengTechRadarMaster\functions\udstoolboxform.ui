<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UDSToolBoxForm</class>
 <widget class="QWidget" name="UDSToolBoxForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>959</width>
    <height>708</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_8">
     <item>
      <widget class="QLabel" name="label_25">
       <property name="text">
        <string>项目:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxProject">
       <item>
        <property name="text">
         <string>BYD-120</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>GWM-140PLUS</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_7">
       <property name="text">
        <string>通道：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxChannel">
       <item>
        <property name="text">
         <string>0</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>1</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>2</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>3</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonReadDTC">
       <property name="text">
        <string>读取故障码</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonClearDTC">
       <property name="text">
        <string>清除故障码</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonExitFactoryMode">
       <property name="text">
        <string> 退出工厂模式</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxCycleReadDTC">
       <property name="text">
        <string>定时读取故障码</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>信息读取</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_8">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_28">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <widget class="QLabel" name="label">
               <property name="text">
                <string>左前雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftVersion_LF">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item>
              <widget class="QLabel" name="label_2">
               <property name="text">
                <string>右前雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftVersion_RF">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <item>
              <widget class="QLabel" name="label_3">
               <property name="text">
                <string>左后雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftVersion_LR">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <item>
              <widget class="QLabel" name="label_4">
               <property name="text">
                <string>右后雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftVersion_RR">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_6">
             <item>
              <widget class="QLabel" name="label_5">
               <property name="text">
                <string>前雷达  ：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftVersion_F">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_7">
             <item>
              <widget class="QLabel" name="label_6">
               <property name="text">
                <string>软件版本号</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonReadSoftVersion">
               <property name="text">
                <string>读取</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonClearSoftVersion">
               <property name="text">
                <string>清除</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_14">
             <item>
              <widget class="QLabel" name="label_13">
               <property name="text">
                <string>左前雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftCode_LF">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_13">
             <item>
              <widget class="QLabel" name="label_9">
               <property name="text">
                <string>右前雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftCode_RF">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_12">
             <item>
              <widget class="QLabel" name="label_12">
               <property name="text">
                <string>左后雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftCode_LR">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_11">
             <item>
              <widget class="QLabel" name="label_10">
               <property name="text">
                <string>右后雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftCode_RR">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_10">
             <item>
              <widget class="QLabel" name="label_8">
               <property name="text">
                <string>前雷达  ：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditSoftCode_F">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_9">
             <item>
              <widget class="QLabel" name="label_11">
               <property name="text">
                <string>软件编码</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonReadSoftCode">
               <property name="text">
                <string>读取</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonClearSoftCode">
               <property name="text">
                <string>清除</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_7">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_15">
             <item>
              <widget class="QLabel" name="label_21">
               <property name="text">
                <string>左前雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditAngle_LF">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_23">
             <item>
              <widget class="QLabel" name="label_22">
               <property name="text">
                <string>右前雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditAngle_RF">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_24">
             <item>
              <widget class="QLabel" name="label_23">
               <property name="text">
                <string>左后雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditAngle_LR">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_25">
             <item>
              <widget class="QLabel" name="label_24">
               <property name="text">
                <string>右后雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditAngle_RR">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_26">
             <item>
              <widget class="QLabel" name="labelAngle_F">
               <property name="text">
                <string>前雷达  ：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditAngle_F">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_27">
             <item>
              <widget class="QLabel" name="label_26">
               <property name="text">
                <string>安装角度</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonAngleCode">
               <property name="text">
                <string>读取</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonClearAngle">
               <property name="text">
                <string>清除</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_4">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>73</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string> 配置字</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_6">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_22">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_16">
             <item>
              <spacer name="horizontalSpacer_2">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_14">
               <property name="text">
                <string> 写入值</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_3">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_15">
               <property name="text">
                <string> 读取值</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_4">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_17">
             <item>
              <widget class="QLabel" name="label_16">
               <property name="text">
                <string>左前雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditWriteCfgCode_LF"/>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonWriteCfgCode_LF">
               <property name="text">
                <string> 写入</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditReadCfgCode_LF">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_18">
             <item>
              <widget class="QLabel" name="label_17">
               <property name="text">
                <string>右前雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditWriteCfgCode_RF"/>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonWriteCfgCode_RF">
               <property name="text">
                <string> 写入</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditReadCfgCode_RF">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_19">
             <item>
              <widget class="QLabel" name="label_20">
               <property name="text">
                <string>左后雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditWriteCfgCode_LR"/>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonWriteCfgCode_LR">
               <property name="text">
                <string> 写入</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditReadCfgCode_LR">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_21">
             <item>
              <widget class="QLabel" name="label_18">
               <property name="text">
                <string>右后雷达：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditWriteCfgCode_RR"/>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonWriteCfgCode_RR">
               <property name="text">
                <string> 写入</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditReadCfgCode_RR">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_20">
             <item>
              <widget class="QLabel" name="label_19">
               <property name="text">
                <string>前雷达  ：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditWriteCfgCode_F"/>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonWriteCfgCode_F">
               <property name="text">
                <string> 写入</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditReadCfgCode_F">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <item>
            <spacer name="verticalSpacer_2">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButtonReadCfgCode">
             <property name="text">
              <string>读取</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButtonClearCfgCode">
             <property name="text">
              <string> 清除</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_3">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>42</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QTextBrowser" name="textBrowserFrame"/>
     </item>
     <item>
      <widget class="QTextBrowser" name="textBrowserMsg"/>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonClearMsg">
     <property name="text">
      <string>清除信息</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
