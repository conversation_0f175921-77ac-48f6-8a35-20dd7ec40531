﻿#include "chengtechradarswitch.h"
#include "ui_chengtechradarswitch.h"

#include <devices/devicemanager.h>
#include <devices/deviceconfigdialog.h>
#include <utils/settingshandler.h>
#include <utils/utils.h>
#include "version.h"
#include "ctanalysisworker.h"

#include <QMessageBox>

static const char deviceSettingsKey[] = "Devices/DeviceSettings";

ChengTechRadarSwitch::ChengTechRadarSwitch(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::ChengTechRadarSwitch),
    mDeviceManager(new Devices::Can::DeviceManager(this)),
    mCTAnalysisWorker(new CTAnalysisWorker)
{
    qRegisterMetaType<Devices::Can::CanFrame>("CanFrame");

    ui->setupUi(this);
    ui->pushButtonRawOpen->setEnabled(false);
    ui->pushButtonRawClose->setEnabled(false);
    this->setWindowTitle( QString("%1 %2")
                    .arg(CHENGTECH_RADAR_SWITCH_NAME)
                    .arg(CHENGTECH_RADAR_SWITCH_VERSION));

    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceOpened, this, [=](){
        ui->actionOpenDevice->setEnabled(true);
        ui->actionOpenDevice->setText(QString::fromLocal8Bit("关闭设备"));
        ui->actionSelectDevice->setEnabled(false);
        ui->pushButtonRawOpen->setEnabled(true);
        ui->pushButtonRawClose->setEnabled(true);
    });
    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceClosed, this, [=](){
        ui->actionOpenDevice->setEnabled(true);
        ui->actionOpenDevice->setText(QString::fromLocal8Bit("打开设备"));
        ui->actionSelectDevice->setEnabled(true);
        ui->pushButtonRawOpen->setEnabled(false);
        ui->pushButtonRawClose->setEnabled(false);
    });

    connect(mCTAnalysisWorker, &CTAnalysisWorker::stateChanged, this, &ChengTechRadarSwitch::stateChanged);

    loadSettings();
}

ChengTechRadarSwitch::~ChengTechRadarSwitch()
{
    delete ui;
}

void ChengTechRadarSwitch::closeEvent(QCloseEvent *event)
{
    if (mDeviceManager->isOpened()) {
        mDeviceManager->closeDevice();
    }

    savesettings();
}

void ChengTechRadarSwitch::stateChanged(bool opened)
{
    ui->labelState->setText(opened ? "OPENED" : "CLOSED");
//    log(QString::fromLocal8Bit(opened ? "【雷达】原始点已开启" : "【雷达】原始点已关闭"));
}

void ChengTechRadarSwitch::log(const QString &text)
{
    ui->plainTextEditLog->appendPlainText(text);
}


void ChengTechRadarSwitch::on_actionSelectDevice_triggered()
{
    Devices::Can::DeviceConfigDialog *dialog = mDeviceManager ?
                new Devices::Can::DeviceConfigDialog(mDeviceManager->deviceSettings(), this) :
                new Devices::Can::DeviceConfigDialog(this);
    connect(dialog, &Devices::Can::DeviceConfigDialog::applied, this, [=](){
        Devices::Can::DeviceSettings deviceSetting = dialog->settings();
        deviceChanged(deviceSetting);
    });
    dialog->exec();
}

void ChengTechRadarSwitch::on_actionOpenDevice_triggered()
{
    ui->actionOpenDevice->setEnabled(false);
    if (!mDeviceManager->isOpened()) {
        ui->labelState->setText("CLOSED");
        if (!mDeviceManager->openDevice()) {
            ui->actionOpenDevice->setEnabled(true);
            QMessageBox::warning(this, QString::fromLocal8Bit("运行"), QString::fromLocal8Bit("运行失败!\n%1").arg(mDeviceManager->errorString()));
            return;
        }
    } else {

        if (!mDeviceManager->closeDevice()) {
            ui->actionOpenDevice->setEnabled(true);
            return;
        }
    }
}

void ChengTechRadarSwitch::deviceChanged(Devices::Can::DeviceSettings deviceSettings)
{
    bool newDeviceWorker = false;
    mDeviceManager->setDeviceSettings(deviceSettings, newDeviceWorker);
    if (newDeviceWorker)
    {
        Devices::Can::IDeviceWorker *deviceWorker = mDeviceManager->deviceWorker();
        connect(deviceWorker, &Devices::Can::IDeviceWorker::frameRecieved, mCTAnalysisWorker, &CTAnalysisWorker::canFrame);
    }
}

void ChengTechRadarSwitch::loadSettings()
{
    Devices::Can::DeviceSettings deviceSettings = Devices::Can::DeviceSettings{Devices::Can::DeviceSettings::ZLG, 41, 0};
    deviceSettings.mDeviceChannelSettings << Devices::Can::DeviceChannelSettings{6, 0, 0, true, 500000, 2000000} << Devices::Can::DeviceChannelSettings{6, 0, 1, true, 500000, 2000000};
    QVariant settings = SETTINGS_GET_VALUE(QLatin1String(deviceSettingsKey));
    if (settings.isValid())
    {
        deviceSettings.setSettings(settings);
    }

    deviceChanged(deviceSettings);
}

void ChengTechRadarSwitch::savesettings()
{
    SETTINGS_SET_VALUE(QLatin1String(deviceSettingsKey), mDeviceManager->deviceSettings().getSettings());
}

void ChengTechRadarSwitch::on_pushButtonRawOpen_clicked()
{
    log(QString::fromLocal8Bit("【操作】开启原始点"));
    ui->labelState->setText("CLOSED");

    Devices::Can::CanFrame frame1(0, ui->comboBoxChannelIndex->currentIndex(), Devices::Can::CanFrame::TX, 0x300, QByteArray::fromHex("31 58 af 80 00 00 00 00"));
    Devices::Can::CanFrame frame2(0, ui->comboBoxChannelIndex->currentIndex(), Devices::Can::CanFrame::TX, 0x200, QByteArray::fromHex("40 00 00 00 00 00 00 00"));
    mDeviceManager->sendFrame(frame1);
    mDeviceManager->sendFrame(frame2);

    mCTAnalysisWorker->newState();
}

void ChengTechRadarSwitch::on_pushButtonRawClose_clicked()
{
    log(QString::fromLocal8Bit("【操作】关闭原始点"));
    ui->labelState->setText("CLOSED");

    Devices::Can::CanFrame frame1(0, ui->comboBoxChannelIndex->currentIndex(), Devices::Can::CanFrame::TX, 0x300, QByteArray::fromHex("31 58 af 80 00 00 00 00"));
    Devices::Can::CanFrame frame2(0, ui->comboBoxChannelIndex->currentIndex(), Devices::Can::CanFrame::TX, 0x200, QByteArray::fromHex("80 00 00 00 00 00 00 00"));
    mDeviceManager->sendFrame(frame1);
    mDeviceManager->sendFrame(frame2);
    Utils::dely(50);
    mDeviceManager->sendFrame(frame1);
    mDeviceManager->sendFrame(frame2);

    mCTAnalysisWorker->newState();
}
