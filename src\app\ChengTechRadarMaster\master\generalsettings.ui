<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GeneralSettings</class>
 <widget class="QWidget" name="GeneralSettings">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>313</width>
    <height>612</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>文件保存帧数(100-100000):</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditFileSaveFrameCount">
       <property name="text">
        <string>48000</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QCheckBox" name="checkBoxAlarmScriptEnable">
       <property name="text">
        <string>使用脚本处理告警</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditAlarmScript"/>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonAlarmScript">
       <property name="text">
        <string>...</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QGridLayout" name="gridLayout_2">
     <item row="0" column="1">
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="0" column="0">
      <widget class="QCheckBox" name="checkBoxHozonBreakShort">
       <property name="text">
        <string>哪吒短协议</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_3">
     <property name="title">
      <string>GEELY通道(%2)-雷达ID设置</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_4">
      <item row="0" column="0">
       <widget class="QLabel" name="label_12">
        <property name="text">
         <string>通道1：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="comboBoxGEELYChannel_1">
        <item>
         <property name="text">
          <string>前角雷达</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>后角雷达</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="0" column="2">
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_13">
        <property name="text">
         <string>通道2：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="comboBoxGEELYChannel_2">
        <property name="currentIndex">
         <number>1</number>
        </property>
        <item>
         <property name="text">
          <string>前角雷达</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>后角雷达</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>BYD高阶通道-雷达ID设置</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="6" column="1">
       <widget class="QSpinBox" name="spinBoxBYDHDChannel_5">
        <property name="maximum">
         <number>7</number>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_10">
        <property name="text">
         <string>通道3：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_9">
        <property name="text">
         <string>通道2：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QSpinBox" name="spinBoxBYDHDChannel_2">
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>7</number>
        </property>
        <property name="value">
         <number>5</number>
        </property>
       </widget>
      </item>
      <item row="0" column="0" colspan="3">
       <widget class="QCheckBox" name="checkBoxBYDRaw600ByChannel">
        <property name="text">
         <string>原始点根据通道区分</string>
        </property>
       </widget>
      </item>
      <item row="7" column="0">
       <widget class="QLabel" name="label_15">
        <property name="text">
         <string>通道6：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1" rowspan="2">
       <widget class="QSpinBox" name="spinBoxBYDHDChannel_3">
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>7</number>
        </property>
        <property name="value">
         <number>6</number>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_8">
        <property name="text">
         <string>通道1：</string>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <widget class="QSpinBox" name="spinBoxBYDHDChannel_4">
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>7</number>
        </property>
        <property name="value">
         <number>7</number>
        </property>
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="QLabel" name="label_14">
        <property name="text">
         <string>通道5：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="9" column="0">
       <widget class="QLabel" name="label_16">
        <property name="text">
         <string>通道8：</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0" rowspan="2">
       <widget class="QLabel" name="label_11">
        <property name="text">
         <string>通道4：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QSpinBox" name="spinBoxBYDHDChannel_1">
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>7</number>
        </property>
       </widget>
      </item>
      <item row="8" column="0">
       <widget class="QLabel" name="label_17">
        <property name="text">
         <string>通道7：</string>
        </property>
       </widget>
      </item>
      <item row="7" column="1">
       <widget class="QSpinBox" name="spinBoxBYDHDChannel_6"/>
      </item>
      <item row="8" column="1">
       <widget class="QSpinBox" name="spinBoxBYDHDChannel_7"/>
      </item>
      <item row="9" column="1">
       <widget class="QSpinBox" name="spinBoxBYDHDChannel_8"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>角度补偿</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="1" column="0">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>4号：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="2">
       <widget class="QLineEdit" name="lineEditAngleCompensationRaw6"/>
      </item>
      <item row="0" column="3">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>跟踪点</string>
        </property>
       </widget>
      </item>
      <item row="4" column="2">
       <widget class="QLineEdit" name="lineEditAngleCompensationRaw7"/>
      </item>
      <item row="4" column="3">
       <widget class="QLineEdit" name="lineEditAngleCompensationTrack7"/>
      </item>
      <item row="2" column="3">
       <widget class="QLineEdit" name="lineEditAngleCompensationTrack5"/>
      </item>
      <item row="3" column="3">
       <widget class="QLineEdit" name="lineEditAngleCompensationTrack6"/>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>原始点</string>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QLineEdit" name="lineEditAngleCompensationRaw4"/>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>5号：</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>7号：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QLineEdit" name="lineEditAngleCompensationRaw5"/>
      </item>
      <item row="1" column="3">
       <widget class="QLineEdit" name="lineEditAngleCompensationTrack4"/>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>6号：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="4">
       <widget class="QCheckBox" name="checkBoxUseRadarOffsetToSide4">
        <property name="text">
         <string>使用雷达边距补偿</string>
        </property>
       </widget>
      </item>
      <item row="2" column="4">
       <widget class="QCheckBox" name="checkBoxUseRadarOffsetToSide5">
        <property name="text">
         <string>使用雷达边距补偿</string>
        </property>
       </widget>
      </item>
      <item row="3" column="4">
       <widget class="QCheckBox" name="checkBoxUseRadarOffsetToSide6">
        <property name="text">
         <string>使用雷达边距补偿</string>
        </property>
       </widget>
      </item>
      <item row="4" column="4">
       <widget class="QCheckBox" name="checkBoxUseRadarOffsetToSide7">
        <property name="text">
         <string>使用雷达边距补偿</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>31</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOK">
       <property name="text">
        <string>确定</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonApply">
       <property name="text">
        <string>应用</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>pushButtonCancel</sender>
   <signal>clicked()</signal>
   <receiver>GeneralSettings</receiver>
   <slot>close()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>232</x>
     <y>89</y>
    </hint>
    <hint type="destinationlabel">
     <x>252</x>
     <y>48</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
