﻿#ifndef INTERPOLATIONGEELY_H
#define INTERPOLATIONGEELY_H

#include "iinterpolation.h"

namespace Analysis {

class InterpolationGEELY : public IInterpolation
{
public:
    static bool isTrackFrame(int radarID, int channelRadarID[2], const Devices::Can::CanFrame &frame);
    static void saveTimestamps(Devices::Can::FrameTimestamps *timestamps, const Devices::Can::CanFrame &frame);

    InterpolationGEELY(AnalysisWorker *analysisWorker);
    bool GEELY16TargetParse(quint8 radarID, Devices::Can::stCanTxMsg *frame );
#ifdef ALGORITHM_GEELY
    int encodeFrame(int radarID, Devices::Can::FrameTimestamp *timestamp, RDP_TrkFrameInfoGeely2_0 *outputObjList, int16_t trkValidNum, uint8_t msgCounter, Devices::Can::stCanTxMsg *frameArray) override;
#endif
    void setChannelRadarIDGEELY(int *channelRadarID, int size) override;

    void canFrame(int radarID, const Devices::Can::CanFrame &frame) override;

    int mGEELYChannelRadarID[2]{0, 1};                ///< GEELY高阶通道-雷达ID设置；0：前角，1：后角

    int mGEELY16TargetCount[MAX_RADAR_COUNT];
    int mGEELY16TargetCurrentIndex[MAX_RADAR_COUNT];
};

} // namespace Analysis

#endif // INTERPOLATIONGEELY_H
