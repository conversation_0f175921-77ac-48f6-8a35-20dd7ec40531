﻿/**
*********************************************************************************************************
*               Copyright (C) SHL. All rights reserved.
**********************************************************************************************************
* 文件名file:concave.h
* 简述brief:
* 作者author:
* 日期date:
* 版本version:
*********************************************************************************************************
*/

#ifndef __RING_QUEUE_STRUCT_BUFF_H__
#define __RING_QUEUE_STRUCT_BUFF_H__

/***********************
* * 宏定义
***********************/
#define RING_QUEUE_LEN	10

/***********************
* * 结构体定义
***********************/
typedef struct asx
{
    uint32_t alarmType;     // 目标报警类型，倒车工况下，功能会同时发生，如RCTA和RCTB
    uint32_t lastAlarmType; // 目标上一次报警类型，倒车工况下，功能会同时发生，如RCTA和RCTB
    uint8_t adasBSDWarning;    // 报警等级
    uint8_t id;
    float x; // x轴距离，m
    float y; // y轴距离，m
} asx;
//环形队列结构体
/*!< 柔性数组实现环形队列 */
typedef struct __Tag_RingQueueFIFO_FlexibleBuffer_TypeDef
{
    int Write;
    int Read;
    int Len;    /*!> 必须至少有一个其它成员 */
    asx Data[];  //!< 使用 asx 类型的柔性数组 /*!> 柔性数组必须是结构体最后一个成员（也可是其它类型，如：int、double、...） */
    /*!> int* Data;*/
} RingQueueFIFO_FlexibleBuffer_TypeDef;

/***********************
* * 函数声明
***********************/
RingQueueFIFO_FlexibleBuffer_TypeDef* RingQueue_FIFO_Init(RingQueueFIFO_FlexibleBuffer_TypeDef* pRingSoftBuff, const unsigned int len);
int RingQueue_GetIsFull_Statue(RingQueueFIFO_FlexibleBuffer_TypeDef* ptr);
int RingQueue_GetIsEmpty_Status(RingQueueFIFO_FlexibleBuffer_TypeDef* ptr);
int RingQueue_Insert(RingQueueFIFO_FlexibleBuffer_TypeDef* ptr, asx* data);
int RingQueue_ReadData(RingQueueFIFO_FlexibleBuffer_TypeDef* ptr, asx* data);
int RingQueue_Destory(RingQueueFIFO_FlexibleBuffer_TypeDef* ptr);

#endif





