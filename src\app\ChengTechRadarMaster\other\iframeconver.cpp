﻿#include "iframeconver.h"
#include "../devices/devicemanager.h"

#include <QDebug>

int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
    uint32_t mask = ((1 << bits) - 1);
    uint32_t extracted = data & mask;
    int32_t sign_extended = (extracted & (1 << (bits - 1))) ? (int)(extracted | (~mask)) : (int)extracted;
    return sign_extended;
}

uint32_t encode_sign_bit(int32_t data, uint8_t bits) {
    uint32_t const m = 0x1u << (bits - 1);

    return (data & m) ? (data | m) : data;
}

IFrameConver::IFrameConver( Devices::Can::DeviceManager* deviceManager, QObject *parent )
    : QObject(parent)
{
    mDeviceManager = deviceManager;
//    connect( &mSendTimer, &QTimer::timeout, this, &IFrameConver::sendResultFrame );
}

void IFrameConver::startOrStop(bool bStart)
{
    initRecvIDAndEndID();
    Devices::Can::IDeviceWorker* pDevice = mDeviceManager->deviceWorker();
    if( bStart ){
        mSendFrameNum = 0;
        mRecvFrameNum = 0;
        connect( pDevice, &Devices::Can::IDeviceWorker::frameRecieved, this, &IFrameConver::recvFrame );
//        mSendTimer.start( mSendTimeMS );
    }else{
        disconnect( pDevice, &Devices::Can::IDeviceWorker::frameRecieved, this, &IFrameConver::recvFrame );
//        mSendTimer.stop();
    }
    mRun = bStart;

    emit dataChanged();
    qDebug() << __FUNCTION__ <<__LINE__ << mRun << mConverName << mRecvChannel << mSendChannel;
}

void IFrameConver::recvFrame(const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.channelIndex() << mRecvChannel;

    if( !isRecvID( frame.id() ) || frame.channelIndex() != mRecvChannel ){
        return;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex();
    analysis( frame );

    mRecvFrameNum++;

//    if( frame.id() == mRecvEndFrameID ){
//        conver();
//        packageFrame();
//        sendFrame();
//    }
    emit dataChanged();
}

bool IFrameConver::isRecvID(quint64 id)
{
    int index = mRecvIDList.indexOf( id );
    return index != -1;
}

void IFrameConver::sendResultFrame()
{
    if( !mRun ){
        return;
    }
    conver();
    packageFrame();
    sendFrame();
}

bool IFrameConver::sendFrame()
{
    bool bRet = true;
    for( int i=0; i<mSendFrames.size(); i++ ){
        bRet &= mDeviceManager->sendFrame( mSendChannel, mSendFrames[i].id(), mSendFrames[i].data(), true );
    }

    if( bRet ){
        mSendFrameNum += mSendFrames.size();
        emit dataChanged();
    }

    mSendFrames.clear();
    return bRet;
}
