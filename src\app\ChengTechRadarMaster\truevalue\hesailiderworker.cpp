﻿#include "hesailiderworker.h"

#include <iostream>

#include <stdio.h>
#include <stdlib.h>
#include <WS2tcpip.h>
#include <time.h>

#include <winsock2.h>

#include <QtMath>
#include <QTextCodec>
#include <QDateTime>
#include <QThread>
#include <QDebug>

#pragma comment(lib, "ws2_32")

#define DEG2RAD 0.017453292519943295f

float _angle_offset[][2] {
    {-1.042, 14.882},
    {-1.042, 11.032},
    {-1.042, 8.059},
    {-1.042, 5.057},
    {-1.042, 3.040},
    {-1.042, 2.028},
    {1.042, 1.860},
    {3.125, 1.688},
    {5.208, 1.522},
    {-5.208, 1.351},
    {-3.125, 1.184},
    {-1.042, 1.013},
    {1.042, 0.846},
    {3.125, 0.675},
    {5.208, 0.508},
    {-5.208, 0.337},
    {-3.125, 0.169},
    {-1.042, 0.000},
    {1.042, -0.169},
    {3.125, -0.337},
    {5.208, -0.508},
    {-5.208, -0.675},
    {-3.125, -0.845},
    {-1.042, -1.013},
    {1.042, -1.184},
    {3.125, -1.351},
    {5.208, -1.522},
    {-5.208, -1.688},
    {-3.125, -1.860},
    {-1.042, -2.028},
    {1.042, -2.198},
    {3.125, -2.365},
    {5.208, -2.536},
    {-5.208, -2.700},
    {-3.125, -2.873},
    {-1.042, -3.040},
    {1.042, -3.210},
    {3.125, -3.375},
    {5.208, -3.548},
    {-5.208, -3.712},
    {-3.125, -3.884},
    {-1.042, -4.050},
    {1.042, -4.221},
    {3.125, -4.385},
    {5.208, -4.558},
    {-5.208, -4.720},
    {-3.125, -4.892},
    {-1.042, -5.057},
    {1.042, -5.229},
    {3.125, -5.391},
    {5.208, -5.565},
    {-5.208, -5.726},
    {-3.125, -5.898},
    {-1.042, -6.061},
    {-1.042, -7.063},
    {-1.042, -8.059},
    {-1.042, -9.060},
    {-1.042, -9.885},
    {-1.042, -11.032},
    {-1.042, -12.006},
    {-1.042, -12.974},
    {-1.042, -13.930},
    {-1.042, -18.889},
    {-1.042, -24.897}
};

SOCKET mSock;

HeSaiLiderWorker::HeSaiLiderWorker(QObject *parent) : QObject(parent)
{
//    mDetectionList = new Detection[1000];
    //    mObjectList = new Object[1000];
}

void HeSaiLiderWorker::stop()
{
    mStoped = true;
    closesocket(mSock);
}

void HeSaiLiderWorker::run(int frameCount, bool save, const QString &ip, quint16 port, bool anyIP)
{
    qDebug() << __FUNCTION__ << __LINE__ << "message";
#if 0
    mTargetCount = 100;
    for (int i = 0; i < mTargetCount; ++i) {
        mDetectionList[i].x = (float)i * (float)i * 0.005f;
        mDetectionList[i].y = (float)i * (float)i * 0.01f;
        mDetectionList[i].z = (float)i * (float)i * 0.1f;
#if 0
        qDebug() << __FUNCTION__ << __LINE__ << i
                 << QByteArray::fromRawData((char*)&mDetectionList[i].x, sizeof (mDetectionList[i].x)).toHex(' ')
                 << QByteArray::fromRawData((char*)&mDetectionList[i].y, sizeof (mDetectionList[i].y)).toHex(' ')
                 << QByteArray::fromRawData((char*)&mDetectionList[i].z, sizeof (mDetectionList[i].z)).toHex(' ');
        qDebug() << __FUNCTION__ << __LINE__ << QString("%1 %2 %3 %4").arg(i).arg(mDetectionList[i].x, 10, 'f').arg(mDetectionList[i].y, 10, 'f').arg(mDetectionList[i].z, 10, 'f');
#endif
    }
    while (!mStoped)
    {

//        qDebug() << __FUNCTION__ << __LINE__ << "message";

        if (mTargetCount > 0) {
            emit showTarget(0, mTargetCount, "");
        }
        if (mSaved) {
            qDebug() << __FUNCTION__ << __LINE__ << "message";
            // 写入块数据头
            quint64 timestamp = QDateTime::currentDateTime().toMSecsSinceEpoch();
            mSaveFile.write((char*)&timestamp, sizeof (timestamp));
            mSaveFile.write((char*)&mSaveCount, sizeof (mSaveCount));
            mSaveFile.write((char*)&mHeSaiLiderFrameNumber, sizeof (mHeSaiLiderFrameNumber));
            mSaveFile.write((char*)&mRawFrameNumber, sizeof (mRawFrameNumber));
            mSaveFile.write((char*)&mTrackFrameNumber, sizeof (mTrackFrameNumber));
            mSaveFile.write((char*)&mTargetCount, sizeof (mTargetCount));
            // 写入目标数据
            for (int i = 0; i < mTargetCount; i++)
            {
                mSaveFile.write((char*)&(mDetectionList[i].x), sizeof (mDetectionList[i].x));
                mSaveFile.write((char*)&(mDetectionList[i].y), sizeof (mDetectionList[i].y));
                mSaveFile.write((char*)&(mDetectionList[i].z), sizeof (mDetectionList[i].z));
            }
        }
        emit frameNumber(mHeSaiLiderFrameNumber++);

        QThread::msleep(100);
        continue;
    }
    return;
#endif
    bool print = false;
    uint32_t object_frame_cnt = 0xFFFFFFFF;
    uint32_t detection_frame_cnt = 0xFFFFFFFF;

    object_frame_cnt = detection_frame_cnt = frameCount;

    qDebug("frame count: %u\n", object_frame_cnt);
    qDebug("      print: %s\n", print ? "true" : "false");


    int iRet = 0;
    WSADATA wsaData;
    WSAStartup(MAKEWORD(2, 2), &wsaData);

    mSock = socket(AF_INET, SOCK_DGRAM, 0);

    sockaddr_in addr;

    addr.sin_family = AF_INET;
    if (anyIP)
    {
        addr.sin_addr.S_un.S_addr = INADDR_ANY;
    }
    else
    {
        addr.sin_addr.S_un.S_addr = inet_addr(ip.toStdString().c_str());
    }
    addr.sin_port = htons(port);

    bool bOptval = true;
    iRet = setsockopt(mSock, SOL_SOCKET, SO_REUSEADDR, (char*)&bOptval, sizeof(bOptval));
    if (iRet != 0) {
        qDebug("setsockopt fail:%d", WSAGetLastError());
        emit finished(false, QString("setsockopt fail:%1").arg(WSAGetLastError()));
        return;
    }

    iRet = bind(mSock, (sockaddr*)&addr, sizeof(addr));
    if (iRet != 0) {
        qDebug("bind fail:%d", WSAGetLastError());
        emit finished(false, QString("bind fail:%1").arg(WSAGetLastError()));
        return;
    }
    qDebug("socket:%d bind success\n", mSock);

    // 加入组播
#if 0
    ip_mreq multiCast;
    if (anyIP)
    {
        multiCast.imr_interface.S_un.S_addr = INADDR_ANY;
    }
    else
    {
        multiCast.imr_interface.S_un.S_addr = inet_addr(ip.toStdString().c_str());
    }
    multiCast.imr_multiaddr.S_un.S_addr = inet_addr("*********");
    iRet = setsockopt(mSock, IPPROTO_IP, IP_ADD_MEMBERSHIP, (char*)&multiCast, sizeof(multiCast));
    if (iRet != 0) {
        qDebug("setsockopt fail:%d", WSAGetLastError());
        emit finished(false, QString("setsockopt 2 fail:%1").arg(WSAGetLastError()));
        return;
    }
#endif
    qDebug("udp group start\n");

    char *targetCString = (char *)malloc(sizeof (char) *1024 * 1024);
    char *detectionHeaderCString = (char *)malloc(sizeof (char) *1024 * 2);
    char *objectHeaderCString = (char *)malloc(sizeof (char) *1024 * 2);
    // %6u, %10f, %14f, %14f
    sprintf(detectionHeaderCString, "\n");

    // %6u, %12f, %12f, %12f
    sprintf(objectHeaderCString, "\n");

    FILE *detection_fd = 0;
    FILE *object_fd = 0;
    if (save)
    {
        time_t t = time(0);
        char detection_file[128] = { 0 };
        char object_file[128] = { 0 };
        tm* pt = localtime(&t);
        strftime(detection_file, sizeof(detection_file), "data/detection_data_%Y%m%d%H%M%S.csv", pt);
        strftime(object_file, sizeof(object_file), "data/object_data_%Y%m%d%H%M%S.csv", pt);

        detection_fd = fopen(detection_file, "wb+");
        if (NULL == detection_fd)
        {
            qDebug("open detection file error! %s\n", detection_file);
            emit finished(false, QString("open detection file error! %1").arg(detection_file));
            return;
        }
        else
        {
            qDebug("open detection file success. %s\n", detection_file);
        }

        object_fd = fopen(object_file, "wb+");
        if (NULL == object_fd)
        {
            qDebug("open object file error! %s\n", object_file);
            emit finished(false, QString("open object file error! %1").arg(object_file));
            return;
        }
        else
        {
            qDebug("open object file success. %s\n", object_file);
        }

        fprintf(detection_fd, "%s", detectionHeaderCString);
        fprintf(object_fd, "%s", objectHeaderCString);
    }

    int len = sizeof(sockaddr);
    char *strRecv = (char *)malloc(1024 * 1024);
    mStoped = false;
    mHeSaiLiderFrameNumber = 0;
    while (!mStoped && (object_frame_cnt != 0 && detection_frame_cnt != 0))
    {
//        qDebug() << __FUNCTION__ << __LINE__;
        //        memset(strRecv, 0, 1024 * 1024);
        iRet = recvfrom(mSock, strRecv, 1024 * 1024, 0, (sockaddr*)&addr, &len);
        if (iRet <= 0) {
            qDebug("recvfrom fail:%d", WSAGetLastError());
            break;
        }
        else
        {
//            qDebug() << __FUNCTION__ << __LINE__ << iRet;
        }

        if (iRet == 1194)
        {
            Pandar64_Header *header = (Pandar64_Header *)strRecv;
            header->EEFF = BIG_LITTLE_SWAP16(header->EEFF);
#if 0
            qDebug() << __FUNCTION__ << __LINE__
                     << QString("0x%1").arg(header->EEFF, 0, 16)
                     << header->LaserN
                     << header->BlockN
                     << header->DisUnit
                     << sizeof (Pandar64_Header)
                     << sizeof (Pandar64_Data)
                     << sizeof (Pandar64_ChannelXX_Data)
                     << sizeof (Pandar64_Tail);
#endif
            float displayUnit = header->DisUnit * 0.001;

            Pandar64_Data *data = (Pandar64_Data*)(strRecv + sizeof (Pandar64_Header));
            for (int i = 0; i < 6; ++i)
            {
                data = data + (i * sizeof (Pandar64_Data));
                if (data->Azimuth == 0)
                {
                    continue;
                }
//                qDebug() << __FUNCTION__ << __LINE__ << data->Azimuth << QByteArray::fromRawData((char*)data, 194).toHex(' ');

                int azimuthGap = 0; /* To do */
                if(last_azimuth_ > data->Azimuth) {
                    azimuthGap = static_cast<int>(data->Azimuth) + (m_iAzimuthRange - static_cast<int>(last_azimuth_));
                    m_iAzimuthRange = last_azimuth_ - data->Azimuth;
                } else {
                    azimuthGap = static_cast<int>(data->Azimuth) - static_cast<int>(last_azimuth_);
                }
                if (last_azimuth_ != data->Azimuth &&
                        azimuthGap < 600 /* 6 degree*/) {
                    /* for all the blocks */
                    if ((last_azimuth_ > data->Azimuth &&
                         start_angle_ <= data->Azimuth) ||
                            (last_azimuth_ < start_angle_ &&
                             start_angle_ <= data->Azimuth)) {
//                        if (mSaved) {
//                            // 写入块数据头
//                            quint64 timestamp = QDateTime::currentDateTime().toMSecsSinceEpoch();
//                            mSaveFile.write((char*)&timestamp, sizeof (timestamp));
//                            mSaveFile.write((char*)&mSaveCount, sizeof (mSaveCount));
//                            mSaveFile.write((char*)&mHeSaiLiderFrameNumber, sizeof (mHeSaiLiderFrameNumber));
//                            mSaveFile.write((char*)&mRawFrameNumber, sizeof (mRawFrameNumber));
//                            mSaveFile.write((char*)&mTrackFrameNumber, sizeof (mTrackFrameNumber));
//                            mSaveFile.write((char*)&mTargetCount, sizeof (mTargetCount));
//                            // 写入目标数据
//                            for (int i = 0; i < mTargetCount; i++)
//                            {
//                                mSaveFile.write((char*)&mDetectionList[i].x, sizeof (mDetectionList[i].x));
//                                mSaveFile.write((char*)&mDetectionList[i].y, sizeof (mDetectionList[i].y));
//                                mSaveFile.write((char*)&mDetectionList[i].z, sizeof (mDetectionList[i].z));
//                            }
//                        }
                        if (mTargetCount > 0) {
//                            qDebug() << __FUNCTION__ << __LINE__ << mTargetCount;
                            emit showTarget(0, mTargetCount, "");
//                            qDebug() << __FUNCTION__ << __LINE__ << mSaved;
//                            if (mSaved) {
//                                mSaveFile.write(QString("End,Total,%1,Time,%2 -------------------\n")
//                                                .arg(mTargetCount)
//                                                .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
//                                                .toLocal8Bit().data());
//                                mSaveFile.flush();
//                            }
                            mTargetCount = 0;
                        }


                        emit frameNumber(mHeSaiLiderFrameNumber++);
                    }
                } else {
//                    printf("last_azimuth_:%d data->Azimuth:%d  *******azimuthGap:%d\n", last_azimuth_, data->Azimuth, azimuthGap);
                }

                float azimuth = data->Azimuth * 0.01 + mSettings.mCalibrationAngle;

//                qDebug() << __FUNCTION__ << __LINE__ << data->Azimuth << azimuth << QString("0x%1").arg(data->Azimuth, 0, 16);
//                qDebug() << __FUNCTION__ << __LINE__ << azimuth;
                Pandar64_ChannelXX_Data *channelData = data->ChannelXXData;
                for (int j = 0; j < 64; ++j, ++channelData) {
                    if (channelData->Distance == 0)
                    {
                        continue;
                    }
//                    qDebug() << __FUNCTION__ << __LINE__ << channelData->Distance << channelData->Reflectivity << sizeof(Pandar64_ChannelXX_Data);
                    float distance = channelData->Distance * displayUnit;
                    float angle = azimuth + _angle_offset[j][0];
                    float range = distance * qCos(_angle_offset[j][1] * DEG2RAD);
                    float x = mSettings.mLateralDistanceCompensation - range * qSin(angle * DEG2RAD);
                    float y = mSettings.mLongitudinalDistanceCompensation - range * qCos(angle * DEG2RAD);
                    float z = distance * qSin(_angle_offset[j][1] * DEG2RAD);

                    if (x < mSettings.mLowerFilterX || x > mSettings.mUpperFilterX ||
                            y < mSettings.mLowerFilterY || y > mSettings.mUpperFilterY ||
                            z < mSettings.mLowerFilterZ || z > mSettings.mUpperFilterZ)
                    {
                        continue;
                    }

                    if (mTargetCount == PANDAR64_TARGET_MAX) {
                        mTargetCount = PANDAR64_TARGET_MAX - 1;
                    }
                    Pandar64_Target *target = mDetectionList + mTargetCount++;
                    target->x = x;//-x + mSettings.mLateralDistanceCompensation;
                    target->y = y;//-y + mSettings.mLongitudinalDistanceCompensation;
                    target->z = z;
                    target->valid = true;

                    if (mSaved) {
//                        qDebug() << __FUNCTION__ << __LINE__ << "message";
                        if (mTargetCount == 1) {
                            mSaveFile.write(QString("Begin,No.,%1,Time,%2 -------------------\n")
                                            .arg(mSaveCount++)
                                            .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                                            .toLocal8Bit().data());
                            mSaveFile.flush();
                        }
                        mSaveFile.write(QString("%1,%2,%3,%4\n")
                                        .arg(mTargetCount)
                                        .arg(target->x)
                                        .arg(target->y)
                                        .arg(target->z)
                                        .toLocal8Bit().data());
                    }


//                    qDebug() << __FUNCTION__ << __LINE__
//                             << QString("0x%1").arg(*((unsigned char *)channelData), 0, 16)
//                             << QString("0x%1").arg(channelData->Distance, 0, 16);
//                    qDebug() << __FUNCTION__ << __LINE__ << azimuth << channelData->Distance << channelData->Reflectivity
//                             << x << y << z;
                }

                last_azimuth_ = data->Azimuth;
            }

            Pandar64_Tail *tail = (Pandar64_Tail*)(strRecv + sizeof (Pandar64_Header) + sizeof (Pandar64_Data) * 6);
//            qDebug() << __FUNCTION__ << __LINE__
//                     << tail->HighTemperatureShutdownFlag
//                     << tail->MotorSpeed
//                     << tail->Timestamp
//                     << tail->ReturnMode
//                     << tail->FactoryInformation
//                     << tail->DateTime
//                     << tail->UDPSequence
//                     << targetCount;


        }

        object_frame_cnt--;
    }

    if (save)
    {
        fclose(object_fd);
        fclose(detection_fd);
    }

    closesocket(mSock);
    WSACleanup();

    free(strRecv);
    free(targetCString);
    free(detectionHeaderCString);
    free(objectHeaderCString);

    emit finished();
}

bool HeSaiLiderWorker::startSave(QString savePath, const QDateTime &beginTime)
{
    qDebug() << __FUNCTION__ << __LINE__ << savePath << "message--------------------------------------------------";
    QString file = QString("%1/%2 %3.dat").arg(savePath).arg("hesailider_Log").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz"));
    qDebug() << __FUNCTION__ << __LINE__ << savePath << file;


    mSaveFile.setFileName(file);
    if (!mSaveFile.open(QIODevice::WriteOnly))
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Open Save File Error!" << file;
        mSaved = false;
        return false;
    }

    qDebug() << __FUNCTION__ << __LINE__ << "Open Save File Success!" << file;
//        mSaveFile.write("No.,x,y,z\n");
//        mSaveFile.flush();
    // 写入文件头
    quint64 teimstamp = QDateTime::currentDateTime().toMSecsSinceEpoch();
    quint64 version = 0;
    mSaveFile.write((char *)&teimstamp, sizeof (teimstamp));
    mSaveFile.write((char *)&version, sizeof (version));
    mSaveCount = 0;
    mSaved = true;

    return true;
}

bool HeSaiLiderWorker::stopSave()
{
    if (mSaved && mSaveFile.isOpen()) {
        mSaved = false;
        mSaveFile.flush();
        mSaveFile.close();
    }

    return true;
}

void HeSaiLiderWorker::lockDetection()
{
    mDetectionMutex.lock();
}

void HeSaiLiderWorker::unlockDetection()
{
    mDetectionMutex.unlock();
}

void HeSaiLiderWorker::lockObject()
{
    mObjectMutex.lock();
}

void HeSaiLiderWorker::unlockObject()
{
    mObjectMutex.unlock();
}

void HeSaiLiderWorker::setRadarFrameNumber(quint64 raw, quint64 track)
{
    mRawFrameNumber = raw;
    mTrackFrameNumber = track;
}

QVariant HeSaiLiderWorker::Settings::getSettings() const
{
    QMap<QString, QVariant> config;
    config["MountingHeight"] = mMountingHeight;
    config["LongitudinalDistanceCompensation"] = mLongitudinalDistanceCompensation;
    config["LateralDistanceCompensation"] = mLateralDistanceCompensation;
    config["UpperFilterX"] = mUpperFilterX;
    config["LowerFilterX"] = mLowerFilterX;
    config["UpperFilterY"] = mUpperFilterY;
    config["LowerFilterY"] = mLowerFilterY;
    config["UpperFilterZ"] = mUpperFilterZ;
    config["LowerFilterZ"] = mLowerFilterZ;
    config["CalibrationAngle"] = mCalibrationAngle;

    qDebug() << __FUNCTION__ << __LINE__ << mLongitudinalDistanceCompensation;

    return config;
}

bool HeSaiLiderWorker::Settings::setSettings(const QVariant &settings)
{
    QMap<QString, QVariant> config = settings.toMap();
#define CONFING_FLOAT(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toFloat() : DEFAULT
    mMountingHeight = CONFING_FLOAT("MountingHeight", 2.00);
    mLongitudinalDistanceCompensation = CONFING_FLOAT("LongitudinalDistanceCompensation", 2.50);
    mLateralDistanceCompensation = CONFING_FLOAT("LateralDistanceCompensation", 0.00);
    mUpperFilterX = CONFING_FLOAT("UpperFilterX", 50.00);
    mLowerFilterX = CONFING_FLOAT("LowerFilterX", -50.00);
    mUpperFilterY = CONFING_FLOAT("UpperFilterY", 150.00);
    mLowerFilterY = CONFING_FLOAT("LowerFilterY", -150.00);
    mUpperFilterZ = CONFING_FLOAT("UpperFilterZ", 0.00);
    mLowerFilterZ = CONFING_FLOAT("LowerFilterZ", -1.60);
    mCalibrationAngle = CONFING_FLOAT("CalibrationAngle", 0.00);

    qDebug() << __FUNCTION__ << __LINE__ << mLongitudinalDistanceCompensation;

    return true;
}
