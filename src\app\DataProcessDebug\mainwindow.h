﻿#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>

#include "analysis/analysisdata.h"
#include <QTimer>

class DataProcessForm;
class QLabel;
class QTextEdit;

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void calculateFinished(quint8 radarID, const AnalysisData &data);

    void on_spinBoxFontSize_valueChanged(int arg1);

protected:
    void closeEvent(QCloseEvent *event) override;

private:
    void analysisRadarData(/*TargetModel*/int targetModel, quint8 radarID, AnalysisData *analysisData);
    void setTarget(AnalysisData *analysisData);

    Ui::MainWindow *ui;

    QLabel *mLabelMeasurementCounter;

    AnalysisData *mAnalysisDatas[MAX_RADAR_COUNT];

    DataProcessForm *mDataProcessForm{0};


    quint64 mRawMeasurementCount{0};
    bool mFrameBreakingFirst{true};
    QTimer mTimerFrameBreaking{0};
};
#endif // MAINWINDOW_H
