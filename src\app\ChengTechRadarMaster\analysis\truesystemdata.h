﻿#ifndef TRUESYSTEMDATA_H
#define TRUESYSTEMDATA_H

#include "analysis_global.h"
#include "analysisdata.h"
#include <QString>

//真值目标信息 20
typedef struct
{
    uint64_t DualAntOfsLenPrg      :  8;
    uint64_t DualAntMntOfs         : 16;
    uint64_t DualAntMntOfsInc      : 16;
    uint64_t res0                  :  24;
}stDualAntMnt;

// 0x600 length = 6 自车时间戳
typedef struct INS_TimeStamp
{
    uint64_t InsYear            :  8;
    uint64_t InsMonth           :  4;
    uint64_t InsDay             :  6;
    uint64_t InsHour            :  6;
    uint64_t InsMinute          :  6;
    uint64_t InsSecond          :  6;
    uint64_t InsMilliSecond     : 10;
    uint64_t res0               :  2;
}INS_TimeStamp_t;

// 0x602 length = 8 自车定位高度和状态
typedef struct INS_AltStatus
{
    uint64_t PosAlt         : 24;
    uint64_t PosEllipsoid   : 24;
    uint64_t PosMode        :  4;
    uint64_t InsSolState    :  4;
    uint64_t InsStasNum     :  8;
}INS_AltStatus_t;

// 0x606 length = 6 自车姿态角
typedef struct INS_Attitude
{
    uint64_t AngleHeading : 16;
    uint64_t AnglePitch   : 16;
    uint64_t AngleRoll    : 16;
}INS_Attitude_t;

// 0x60c length = 6 自车速度速度（车辆坐标系下）
typedef struct INS_VelocityVce {
    uint64_t VelocityVceX : 16;
    uint64_t VelocityVceY : 16;
    uint64_t VelocityVceZ : 16;
}INS_VelocityVce_t;

// 0x6d1 length = 8 学习与监控状态
typedef struct LrnMon {
    uint64_t DualAntOfsLrn          : 8;
    uint64_t DualAntOfsLrnProgress  : 7;
    uint64_t                        : 1;
    uint64_t WsErr                  : 8;
    uint64_t WsErrCalcProgress      : 7;
    uint64_t                        : 1;
    uint64_t ImuExtrinPitchAng      : 8;
    uint64_t ImuExtrinPitchProgress : 7;
    uint64_t                        : 1;
    uint64_t ImuExtrinYawAng        : 8;
    uint64_t ImuExtrinYawProgress   : 7;
    uint64_t                        : 1;
}LrnMon_t;

/** @brief 0x4a0 length = 7 从车状态（主车坐标系下） */
typedef struct RDM_DistStatus
{
    uint64_t RDMStatus             :  4;
    uint64_t RDMDistance           : 20;
    uint64_t RDMAzumith            : 16;
    uint64_t RDMDelayMilliSecond   : 16;
    uint64_t res1                  :  8;
}RDM_DistStatus_t;

// 0x4a1 length = 8 从车位置（主车坐标系下）
typedef struct RDM_Pos
{
    uint64_t RDMPosX           :24;
    uint64_t RDMPosY           :24;
    uint64_t RDMPosZ           :16;
}RDM_Pos_t;

// 0x4a2 length = 8 从车速度（主车坐标系下）
typedef struct RDM_Vel
{
    uint64_t RDMVelX           :16;
    uint64_t RDMVelY           :16;
    uint64_t RDMVelZ           :16;
    uint64_t res1              :16;
}RDM_Vel_t;

// 0x4a3 length = 8 从车加速度
typedef struct RDM_Acc
{
    uint64_t RDMAccX           :16;
    uint64_t RDMAccY           :16;
    uint64_t RDMAccZ           :16;
}RDM_Acc_t;

// 0x4a4 length = 8 AEB
typedef struct RDM1_AEB {
    uint64_t RDM_THW        : 16;
    uint64_t RDM_TTC        : 16;
    uint64_t RDM_ETTC       : 16;
    uint64_t                : 16;
}RDM1_AEB_t;

// 0x7a5 length = 8 目标角速度
typedef struct Target_AngRateVce
{
    uint64_t AngRateVceX        : 20;
    uint64_t AngRateVceY        : 20;
    uint64_t AngRateVceZ        : 20;
    uint64_t res                :  4;
}Target_AngRateVce_t;

// 0x7a6 length = 6 从车姿态角
typedef struct Target_Attitude
{
    uint64_t Heading : 16;
    uint64_t Pitch   : 16;
    uint64_t Roll    : 16;
}Target_Attitude_t;

// 0x7ac length = 6 从车速度
typedef struct Target_VelocityVce1 {
    uint64_t Tgt_VelX : 16;
    uint64_t Tgt_VelY : 16;
    uint64_t Tgt_VelZ : 16;
}Target_VelocityVce1_t;

//真值目标信息
typedef struct ANALYSIS_EXPORT trueObjInfo_t
{
public:
    // 0x600 自车时间戳
    uint8_t mInsYear{0};
    uint8_t mInsMonth{0};
    uint8_t mInsDay{0};
    uint8_t mInsHour{0};
    uint8_t mInsMinute{0};
    uint8_t mInsSecond{0};
    uint8_t mInsMilliSecond{0};

    // 0x602 定位高度和状态
    float   mInsPosAlt{0.0f};                   ///< 位置高度（海拔高度）
    float   mInsPosEllipsoid{0.0f};             ///< 地球椭圆体以上的高度
    uint8_t mInsPosMode{0};                     ///< 定位质量
    uint8_t mInsSolState{0};                    ///< INS解算状态
    uint8_t mInsStasNum{0};                     ///< 卫星数目

    // 0x606 自车姿态角
    float mInsAngleHeading{0.0f};               ///< 航向角(deg)
    float mInsAnglePitch{0.0f};                 ///< 俯仰角(deg)
    float mInsAngleRoll{0.0f};                  ///< 横滚角(deg)

    // 0x60c 自车速度速度（车辆坐标系下）
    float mVelocityVceX{0.0f};                  ///< 纵向速度
    float mVelocityVceY{0.0f};                  ///< 横向速度
    float mVelocityVceZ{0.0f};                  ///< 垂向速度

    // 0x6d1 学习与监控状态
    float   mDualAntOfsLrn{0.0f};               ///< 双天线外参自学习
    uint8_t mDualAntOfsLrnProgress{0};          ///< 双天线外参自学习进度
    float   mWsErr{0.0f};                       ///< 轮速外参自学习
    uint8_t mWsErrCalcProgress{0};              ///< 轮速外参自学习进度
    float   mImuExtrinPitchAng{0.0f};           ///< 俯仰外参自学习
    uint8_t mImuExtrinPitchProgress{0};         ///< 俯仰外参自学习进度
    float   mImuExtrinYawAng{0.0f};             ///< 偏航外参自学习
    uint8_t mImuExtrinYawProgress{0};           ///< 偏航外参自学习进度

    // 0x4A0 从车状态（主车坐标系下）
    uint8_t  mRDMStatus{0};                     ///< 状态质量
    float    mRDMDistance{0.0f};                ///< 主/从车间距离(m)
    float    mRDMAzumith{0.0f};                 ///< 从车方位角(deg)
    uint16_t mRDMDelayMilliSecond{0};           ///< 从车数据延时(ms)

    // 0x4A1 从车位置（主车坐标系下）
    enum TruePoint {
        Origin,
        LeftFront,
        RightFront,
        LeftRear,
        RightRear,
        TruePointCount
    };
    struct
    {
        float mRDMPosX{0.0f};                   ///< 纵向位置
        float mRDMPosY{0.0f};                   ///< 横向位置
        float mRDMPosZ{0.0f};                   ///< 垂向位置
    }mRDMPos[TruePointCount];                   ///< 真值系统实位置;0：原点；1:左前点；2：左后点；3：右前点；4：右后点。

    // 0x4a2 从车速度（主车坐标系下）
    float   mRDMVelX{0.0f};                     ///< 纵向速度
    float   mRDMVelY{0.0f};                     ///< 横向速度
    float   mRDMVelZ{0.0f};                     ///< 垂向速度

    // 0x4a3 从车加速度（主车坐标系下）
    float   mRDMAccX{0.0f};                     ///< 纵向加速度
    float   mRDMAccY{0.0f};                     ///< 横向加速度
    float   mRDMAccZ{0.0f};                     ///< 垂向加速度

    // 0x4a4 length = 8 AEB
    float mRDM_THW{0.0f};                       ///< 车头时距：s
    float mRDM_TTC{0.0f};                       ///< 距离碰撞时间：s
    float mRDM_ETTC{0.0f};                      ///< 强化距离碰撞时间 ：s

    // 0x7a5 目标角速度
    float mTgtAngRateVceX{0.0f};                ///< 纵向角速度
    float mTgtAngRateVceY{0.0f};                ///< 横向角速度
    float mTgtAngRateVceZ{0.0f};                ///< 垂向角速度

    // 0x7a6 目标姿态
    float mTgtAngleHeading{0.0f};               ///< 航向角(deg)
    float mTgtAnglePitch{0.0f};                 ///< 俯仰角(deg)
    float mTgtAngleRoll{0.0f};                  ///< 横滚角(deg)

    // 0x7AC 从车速度
    float mTgtVelX{0.0f};                       ///< 纵向速度
    float mTgtVelY{0.0f};                       ///< 横向速度
    float mTgtVelZ{0.0f};                       ///< 垂向速度

    float mHeading{0.0f};

    bool validFlag = false;//是否有效

    qint64 TimeStamp;
    qint64 TimeUnix;
}stTrueObjInfo;

#endif // TRUESYSTEMDATA_H
