<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BlackBoxSimulatorForm</class>
 <widget class="QWidget" name="BlackBoxSimulatorForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>591</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>894</width>
     <height>30</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QLabel" name="label">
      <property name="text">
       <string>暗箱IP:</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEditBlackBoxIP"/>
    </item>
    <item>
     <widget class="QLabel" name="label_2">
      <property name="text">
       <string>暗箱端口：</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEditBlackBoxPort">
      <property name="maximumSize">
       <size>
        <width>100</width>
        <height>16777215</height>
       </size>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_3">
      <property name="text">
       <string>本机端口：</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEditLocalPort">
      <property name="maximumSize">
       <size>
        <width>100</width>
        <height>16777215</height>
       </size>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pushButtonConnectTest">
      <property name="text">
       <string>连接测试</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
