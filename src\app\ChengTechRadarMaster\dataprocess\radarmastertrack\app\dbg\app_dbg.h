/**
 * @file     app_dbg.h
 * @brief    The header file of app_dbg.c
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2023-03-15
 * 
 * 
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2023-03-15  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef _APP_DBG_H_
#define _APP_DBG_H_

/****************************************************************************
  INCLUDE
 ****************************************************************************/
#include "pcan_protocol.h"


/****************************************************************************
  DEFINE
 ****************************************************************************/
#define PARAM_DATAMODE_NONE         0x0
#define PARAM_DATAMODE_TRACK        0x1
#define PARAM_DATAMODE_RAW          0x2
#define PARAM_DATAMODE_RAW_TRACK    0x3


/*****************************************************************************
  DECLARATION
 *****************************************************************************/
void DBG_setHILDataDbgMode(uint8_t dbgMode);
uint8_t DBG_getHILDataDbgMode(void);
void DBG_setDataOutputMode(uint8_t dataMode);
uint8_t DBG_getDataOutputMode(void);
void DBG_setRadarWorkModeParams(uint8_t *pData);
void DBG_getRadarWorkModeParams(stRadarWorkModeMsg *pMsg);
void DBG_procQueryMsg(uint8_t canIdx, uint8_t *pData);


#endif

