#ifndef WAKEUPTESTFORM_H
#define WAKEUPTESTFORM_H

#include <QWidget>
#include <QFile>

namespace Ui {
class WakeUpTestForm;
}

namespace Functions{
class UDS;
};

namespace Devices {
namespace Can {
class DeviceManager;
class CanFrame;
}
}

class IFrameFileParse;

class WakeUpTestForm : public QWidget
{
    Q_OBJECT

public:
    explicit WakeUpTestForm(Devices::Can::DeviceManager* pDeviceMgr, QWidget *parent = nullptr);
    ~WakeUpTestForm();

signals:
    void testStop();
    void msg( const QString& msg, bool bError );
    void frameRecv( const Devices::Can::CanFrame &frame, bool bTx = false );

private slots:
    void on_pushButtonStart_clicked();

    void on_pushButtonStop_clicked();

    void testEnd();
    void frameRecievedDirect(const Devices::Can::CanFrame &frame);  //直连方式，及时更新uds响应数据以及pcan响应时间，防止流控帧发送不及时
    void frameTransmited(const Devices::Can::CanFrame &frame, bool success);


    void on_pushButtonSelectVCanLog_clicked();

    void showMsg( const QString& msg, bool bError );
    void showFrame( const Devices::Can::CanFrame &frame, bool bSend );


    void on_pushButtonClearMsg_clicked();

private:
    void initUDS();
    bool initTestParam();
    void run();

    bool waitAlive( quint64 beginMS );
    bool waitSleep( quint64 beginMS );
    bool isAlive();

    bool appCheck( quint64 beginMS );
    bool radarAppCheck( quint8 radarID );

    bool sendVCanLogFrame();
    bool sendSleepFrame();

    void initTmpTestResult();
    bool tmpTestResult();

    void wait( quint64 ms );


private:
    Ui::WakeUpTestForm *ui;
    Devices::Can::DeviceManager* mDeviceMgr{NULL};
    IFrameFileParse* mPCanLogParse{NULL};

    quint32 mResponseAddress[4]{0x7dc,0x7ca,0x768,0x769};
    quint32 mPhysicalAddress[4]{0x7d4,0x7c2,0x760,0x761};

    Functions::UDS* mUDS[4];

    bool mRadarTestFlag[4];  //标识4个雷达中哪些需要测试
    QString mVCanLogFileName;  //VCAN log 文件

    //单位 毫秒
    quint64 mVCanLogSendTime{3*1000}; //vcan log发送持续时间
    quint64 mAppResponseTime{3*1000};   //app响应时间
    quint64 mWaitSleepTime{5*60*1000};     //等待休眠时间
    quint64 mWaitAliveTime{3*1000};       //等待唤醒时间
    quint64 mPCanAliveInterval{5*1000};     //判断PCan是否休眠时长


    quint64 mPCan1LastTime{0}; //最后接收到PCAN1报文的时间
    quint64 mPCan2LastTime{0}; //最后接收到PCAN12报文的时间
//    bool mPCan1Alive{false};    //PCAN1是否在线
//    bool mPCan2Alive{false};    //PCAN2是否在线

    quint8 mVCanChannel{0};     //VCan通道
    quint8 mPCan1Channel{1};    //PCan通道1
    quint8 mPCan2Channel{2};    //PCan通道2

    bool mTmpTestResult[4];    //临时测试结果
    bool mTesting{false};
    bool mTestResult{false};    //最终测试结果
    bool mTestResultStr;

    QFile mMsgLog;
    QFile mFrameLog;
};

#endif // WAKEUPTESTFORM_H
