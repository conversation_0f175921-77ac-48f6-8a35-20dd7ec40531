MASTER_LIB_DEPENDS += utils devices network
include(../../../chengtech.pri)
include($$MASTER_LIBS/devices/candevices.pri)

include(dataprocess/dataprocess_code.pri)

include(camera/ffmpeg/ffmpeg.pri)
include(camera/opencv/opencv.pri)

QT += core gui widgets network printsupport multimedia multimediawidgets concurrent serialbus
QT += printsupport
QT += xml
QT += gui-private

CONFIG += c++11
QMAKE_PROJECT_DEPTH = 0

#RC_ICONS = icon.ico

TARGET = $$MASTER_APP_TARGET
DESTDIR = $$MASTER_APP_PATH
INCLUDEPATH += $$PWD/eol

SOURCES += \
    analysis/alarmcalculate.cpp \
    analysis/analysisdata.cpp \
    analysis/analysisdataf.cpp \
    analysis/analysismanager.cpp \
    analysis/analysissaveworker.cpp \
    analysis/analysisworker.cpp \
    analysis/calculationworker.cpp \
    analysis/protocol/CANProtocolARS410.cpp \
    analysis/protocol/CANProtocolBOSCH670.cpp \
    analysis/protocol/CANProtocolBYDHO.cpp \
    analysis/protocol/CANProtocolChengTech710.cpp \
    analysis/protocol/CANProtocolHozonADF.cpp \
    analysis/protocol/CTMRR410.c \
    analysis/protocol/analysisprotocolct.cpp \
    analysis/protocol/analysisprotocolct410.cpp \
    analysis/protocol/baictargetprotocol.cpp \
    analysis/protocol/byd120targetprotocol.cpp \
    analysis/protocol/bydhigherorderprotocol.cpp \
    analysis/protocol/geelytargetprotocol.cpp \
    analysis/protocol/gwmtargetprotocol.cpp \
    analysis/protocol/ianalysisprotocol.cpp \
    analysis/protocol/truesystemprotocol.cpp \
    analysis/framebinarysaveworker.cpp \
    analysis/script/earlywarninglua.cpp \
    analysis/script/iearlywarningscript.cpp \
    camera/camera.cpp \
    camera/cameraimage.cpp \
    camera/imagesettings.cpp \
    camera/mulitcamera.cpp \
    camera/videosettings.cpp \
    canfiles/CANDeviceFile.cpp \
    canfiles/ctcandevicefile.cpp \
    canfiles/devicefiles/devicefileasc.cpp \
    canfiles/devicefiles/devicefileblf.cpp \
    canfiles/devicefiles/idevicefile.cpp \
    daq/daqcalterah.cpp \
    daq/daqcalterahctrl.cpp \
    daq/daqsaveworker.cpp \
    dataprocess/dataprocess.cpp \
    dataprocess/dataprocessform.cpp \
    dataprocess/iinterpolation.cpp \
    dataprocess/interpolationbydho.cpp \
    dataprocess/interpolationgeely.cpp \
    dataprocess/networktcpclient.cpp \
    dataprocess/networktcpserver.cpp \
    debugsystem/CTMRR410.c \
    debugsystem/alarmCompare/alarmcompare.cpp \
    debugsystem/alarmCompare/autorechargeform.cpp \
    debugsystem/daqcontrol.cpp \
    debugsystem/daqmanager.cpp \
    debugsystem/debugcontrolform.cpp \
    debugsystem/playbackcanwoker.cpp \
    debugsystem/playbacknetworkworker.cpp \
    debugsystem/alarmCompare/autorechargerawandtarget.cpp \
    eol/blackboxsimulatorform.cpp \
    eol/blackboxturntableform.cpp \
    eol/eolprotocol.cpp \
    eol/eolview.cpp \
    eol/utility.cpp \
    eol/debugshellform.cpp \
    eol/shelltextedit.cpp \
    eol/eoltablemgr.cpp \
    functions/aftersalecalibration.cpp \
    functions/aftersalecalibrationworker.cpp \
    functions/calibrationform.cpp \
    functions/calibrationsworker.cpp \
    functions/ctbinaryfiletools.cpp \
    functions/dialogcancmds.cpp \
    functions/radarconfig.cpp \
    functions/radarresetworker.cpp \
    functions/radarresetworkerform.cpp \
    functions/sendudsform.cpp \
    functions/staticcalibration.cpp \
    functions/staticcalibrationworker.cpp \
    functions/test/wakeuptestform.cpp \
    functions/uds.cpp \
    functions/udstoolboxform.cpp \
    master/generalsettings.cpp \
    media/videoplayerform.cpp \
    media/screenrecordsaveworker.cpp \
    other/frameconvertform.cpp \
    other/frameconver_haval2geely.cpp \
    other/frameconver_geely2haval.cpp \
    other/iframeconver.cpp \
    other/frameconvertmodel.cpp \
    other/monitor/channelandframemonitorform.cpp \
    other/monitor/channelandframemonitortreemodel.cpp \
    master/mainwindow.cpp \
    master/master.cpp \
    master/mastericons.cpp \
    master/savemanager.cpp \
    master/shortcutmarkers.cpp \
    other/updateECU/ecuupdateform.cpp \
    other/updateECU/ecuupdateitem.cpp \
    other/updateECU/ecuupdatemodel.cpp \
    tools/filebatchparsingform.cpp \
    tools/filebatchparsingworker.cpp \
    truevalue/hesailider.cpp \
    truevalue/hesailiderconfigdialog.cpp \
    truevalue/hesailiderworker.cpp \
    views/analysisdatatableview.cpp \
    views/analysisdataview.cpp \
    views/analysisdataviewf.cpp \
    views/analysisdataviewi.cpp \
    views/analysismodel.cpp \
    views/objectcoordinatesystem.cpp \
    views/objectcorrdinatesystemconfigdialog.cpp \
    views/objectview.cpp \
    #views/objectviewworker.cpp \
    views/qcustomplot.cpp \
    views/statisticsdialog.cpp \
    views/targetmonitor.cpp \
    views/truesystemview.cpp \
    views/truetargetsmonitor.cpp \
    views/viewsicons.cpp \
    views/viewsmanager.cpp \
    views/widget/alarmwidget.cpp \
    views/widget/coordinateaxis.cpp \
    main.cpp

DISTFILES += \
    $$PWD/app_version.h.in \
    release_description.h.in

QMAKE_SUBSTITUTES += $$PWD/app_version.h.in

HEADERS += \
    analysis/alarmcalculate.h \
    analysis/analysis_global.h \
    analysis/analysisdata.h \
    analysis/analysisdata_global.h \
    analysis/analysisdataf.h \
    analysis/analysismanager.h \
    analysis/analysissaveworker.h \
    analysis/analysisworker.h \
    analysis/calculationworker.h \
    analysis/protocol/CANProtocolARS410.h \
    analysis/protocol/CANProtocolBOSCH670.h \
    analysis/protocol/CANProtocolBYDHO.h \
    analysis/protocol/CANProtocolChengTech710.h \
    analysis/protocol/CANProtocolHozonADF.h \
    analysis/protocol/CTMRR410.h \
    analysis/framebinarysaveworker.h \
    analysis/protocol/analysisprotocolct.h \
    analysis/protocol/analysisprotocolct410.h \
    analysis/protocol/baictargetprotocol.h \
    analysis/protocol/byd120targetprotocol.h \
    analysis/protocol/bydhigherorderprotocol.h \
    analysis/protocol/geelytargetprotocol.h \
    analysis/protocol/gwmtargetprotocol.h \
    analysis/protocol/ianalysisprotocol.h \
    analysis/protocol/truesystemprotocol.h \
    analysis/script/earlywarninglua.h \
    analysis/script/iearlywarningscript.h \
    analysis/truesystemdata.h \
    camera/camera.h \
    camera/camera_global.h \
    camera/cameraimage.h \
    camera/imagesettings.h \
    camera/mulitcamera.h \
    camera/videosettings.h \
    canfiles/CANDeviceFile.h \
    canfiles/CANDevicesGlobal.h \
    canfiles/ctcandevicefile.h \
    canfiles/devicefiles/devicefileasc.h \
    canfiles/devicefiles/devicefileblf.h \
    canfiles/devicefiles/idevicefile.h \
    daq/daqcalterah.h \
    daq/daqcalterahctrl.h \
    daq/daqsaveworker.h \
    dataprocess/dataprocess.h \
    dataprocess/dataprocessform.h \
    dataprocess/dataprocess_global.h \
    dataprocess/iinterpolation.h \
    dataprocess/interpolationbydho.h \
    dataprocess/interpolationgeely.h \
    dataprocess/networktcpclient.h \
    dataprocess/networktcpserver.h \
    debugsystem/CTMRR410.h \
    debugsystem/alarmCompare/alarmcompare.h \
    debugsystem/alarmCompare/autorechargeform.h \
    debugsystem/daqcontrol.h \
    debugsystem/daqmanager.h \
    debugsystem/debugcontrolform.h \
    debugsystem/playback_global.h \
    debugsystem/playbackcanwoker.h \
    debugsystem/playbacknetworkworker.h \
    debugsystem/alarmCompare/autorechargerawandtarget.h \
    eol/blackboxsimulatorform.h \
    eol/blackboxturntableform.h \
    eol/eolprotocol.h \
    eol/eolview.h \
    eol/utility.h \
    eol/eoldefine.h \
    eol/debugshellform.h \
    eol/shelltextedit.h \
    eol/eoltablemgr.h \
    functions/aftersalecalibration.h \
    functions/aftersalecalibrationworker.h \
    functions/calibrationform.h \
    functions/calibrationsworker.h \
    functions/ctbinaryfiletools.h \
    functions/dialogcancmds.h \
    functions/functions_global.h \
    functions/radarconfig.h \
    functions/radarresetworker.h \
    functions/radarresetworkerform.h \
    functions/sendudsform.h \
    functions/staticcalibration.h \
    functions/staticcalibrationworker.h \
    functions/test/wakeuptestform.h \
    functions/uds.h \
    functions/udstoolboxform.h \
    master/generalsettings.h \
    media/videoplayerform.h \
    media/screenrecordsaveworker.h \
    other/frameconvertform.h \
    other/frameconver_haval2geely.h \
    other/frameconver_geely2haval.h \
    other/iframeconver.h \
    other/frameconvertmodel.h \
    other/monitor/channelandframemonitorform.h \
    other/monitor/channelandframemonitortreemodel.h \
    master/mainwindow.h \
    master/master.h \
    master/master_global.h \
    master/mastericons.h \
    master/savemanager.h \
    master/shortcutmarkers.h \
    other/updateECU/ecuupdateform.h \
    other/updateECU/ecuupdateitem.h \
    other/updateECU/ecuupdatemodel.h \
    tools/filebatchparsingform.h \
    tools/filebatchparsingworker.h \
    truevalue/hesailider.h \
    truevalue/hesailiderconfigdialog.h \
    truevalue/hesailiderworker.h \
    views/analysisdatatableview.h \
    views/analysisdataview.h \
    views/analysisdataviewf.h \
    views/analysisdataviewi.h \
    views/analysismodel.h \
    views/objectcoordinatesystem.h \
    views/objectcorrdinatesystemconfigdialog.h \
    views/objectview.h \
    #views/objectviewworker.h \
    views/qcustomplot.h \
    views/statisticsdialog.h \
    views/targetmonitor.h \
    views/truesystemview.h \
    views/truetargetsmonitor.h \
    views/views_global.h \
    views/viewsicons.h \
    views/viewsmanager.h \
    views/widget/alarmwidget.h \
    views/widget/coordinateaxis.h

FORMS += \
    camera/camera.ui \
    camera/imagesettings.ui \
    camera/mulitcamera.ui \
    camera/videosettings.ui \
    dataprocess/dataprocessform.ui \
    daq/daqcalterahctrl.ui \
    debugsystem/alarmCompare/alarmcompare.ui \
    debugsystem/alarmCompare/autorechargeform.ui \
    debugsystem/daqcontrol.ui \
    debugsystem/debugcontrolform.ui \
    eol/blackboxsimulatorform.ui \
    eol/blackboxturntableform.ui \
    eol/eolview.ui \
    eol/debugshellform.ui \
    functions/calibrationform.ui \
    functions/ctbinaryfiletools.ui \
    functions/radarresetworkerform.ui \
    functions/sendudsform.ui \
    functions/test/wakeuptestform.ui \
    master/generalsettings.ui \
    media/videoplayerform.ui \
    other/frameconvertform.ui \
    other/monitor/channelandframemonitorform.ui \
    functions/aftersalecalibration.ui \
    functions/dialogcancmds.ui \
    functions/radarconfig.ui \
    functions/staticcalibration.ui \
    functions/udstoolboxform.ui \
    other/updateECU/ecuupdateform.ui \
    tools/filebatchparsingform.ui \
    truevalue/hesailiderconfigdialog.ui \
    views/analysisdatatableview.ui \
    views/analysisdataview.ui \
    views/analysisdataviewconfigdialog.ui \
    views/analysisdataviewf.ui \
    views/objectcorrdinatesystemconfigdialog.ui \
    views/objectview.ui \
    views/statisticsdialog.ui \
    views/targetmonitor.ui \
    views/targetsmonitor.ui \
    views/truesystemview.ui \
    views/truetargetmonitor.ui \
    views/truetargetsmonitor.ui \
    views/widget/alarmwidget.ui

RESOURCES += \
    debugsystem/debugsystem.qrc \
    master/master.qrc \
    views/views.qrc

# 算法调试宏，DEBUG时需要打开这个宏
DEFINES += ALGORITHM_DEBUG
LIBS += \
    -L$$MASTER_3RDPARTY_LIBRARY_PATH/Python312-32/libs -lpython312 \
    -L$$MASTER_3RDPARTY_LIBRARY_PATH/lua-5.4.2/lib -llua54
