﻿#ifndef ANALYSISPROTOCOLCT410_H
#define ANALYSISPROTOCOLCT410_H

#include <QObject>

#include "analysisdata.h"
#include "CANFrame.h"

typedef class CANParseWorker AnalysisWorker;

class AnalysisProtocolCT410 : public QObject
{
    Q_OBJECT
public:
    explicit AnalysisProtocolCT410(AnalysisWorker *analysisWorker, QObject *parent = nullptr);

    bool analysisFrame(const CANFrame &frame);

    static qint8 getRadarID( const CANFrame &frame );

signals:

private:
    void analysisEnd(quint8 radarID, bool assigned);

    bool parseFrame(const CANFrame &frame);

    /** @brief 车身信息 */
    bool parseVehicleInfomation(const CANFrame &frame, AnalysisData *analysisData);
    bool parseVehicleInfomation_32(const CANFrame &frame, AnalysisData *analysisData);
    bool parseVehicleInfomation_64(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 原始点头 */
    bool parseRawTargetHeader(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 原始点 */
    bool parseRawTarget(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 跟踪点头 */
    bool parseTrackTargetHeader(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 跟踪点 */
    bool parseTrackTarget(const CANFrame &frame, AnalysisData *analysisData);
    bool parseTrackTargetVersion0(const CANFrame &frame, AnalysisData *analysisData);
    bool parseTrackTargetVersion1(const CANFrame &frame, AnalysisData *analysisData);


    bool parse0x600_VERSION_7(const CANFrame &frame, AnalysisData *analysisData);
    bool parse0x710_64(const CANFrame &frame, AnalysisData *analysisData);
    bool parse0x710_64_New(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 报警信息 */
    bool parseAlarmInfomation(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 故障信息 */
    bool parse0x4DN(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 故障信息 */
    bool parse0x4EN(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 结束帧 */
    bool parseEndFrame(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 雷达版本号 */
    bool parseRadarVersion(const CANFrame &frame, AnalysisData *analysisData);

    /** @brief 雷达复位 */
    void radarReset( const CANFrame &frame );


    quint8 mRawID[MAX_RADAR_COUNT];
    bool mNewBegin[MAX_RADAR_COUNT];

    AnalysisWorker *mAnalysisWorker;
};

#endif // ANALYSISPROTOCOLCT410_H
