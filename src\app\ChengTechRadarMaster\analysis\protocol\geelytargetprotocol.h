﻿#ifndef GEELYTARGETPROTOCOL_H
#define GEELYTARGETPROTOCOL_H

#include "ianalysisprotocol.h"

namespace Analysis {
namespace Protocol {

class GEELYTargetProtocol : public IAnalysisProtocol
{
    Q_OBJECT
public:
    GEELYTargetProtocol( AnalysisWorker *analysisWorker, QObject *parent = nullptr );

    void setChannelRadarID(int *channelRadarID, int size);

    bool analysisFrame(const Devices::Can::CanFrame &frame) override;

private:
    bool GEELY16TargetHeaderParse(quint8 radarID, const Devices::Can::CanFrame &frame );
    bool GEELY16TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame );

    int mGEELY16TargetCount[MAX_RADAR_COUNT];
    int mGEELY16TargetCurrentIndex[MAX_RADAR_COUNT];

    int mGEELYChannelRadarID[2]{0, 1};                ///< GEELY高阶通道-雷达ID设置；0：前角，1：后角
};

}
}

#endif // GEELYTARGETPROTOCOL_H
