/**
 * @file rdp_kf_types.h
 * <AUTHOR> (s<PERSON><PERSON>@chengtech.net)
 * @brief 
 * @version 0.1
 * @date 2022-10-09
 * 
 * @copyright Copyright (c) 2022
 * 
 */
#ifndef MODULE_BB_KF_TYPES_H_
#define MODULE_BB_KF_TYPES_H_

typedef float        DBL;


/* 0-31 1-bit masks */
#define BIT0    (0x00000001 << 0)
#define BIT1    (0x00000001 << 1)
#define BIT2    (0x00000001 << 2)
#define BIT3    (0x00000001 << 3)
#define BIT4    (0x00000001 << 4)
#define BIT5    (0x00000001 << 5)
#define BIT6    (0x00000001 << 6)
#define BIT7    (0x00000001 << 7)
#define BIT8    (0x00000001 << 8)
#define BIT9    (0x00000001 << 9)
#define BIT10   (0x00000001 << 10)
#define BIT11   (0x00000001 << 11)
#define BIT12   (0x00000001 << 12)
#define BIT13   (0x00000001 << 13)
#define BIT14   (0x00000001 << 14)
#define BIT15   (0x00000001 << 15)
#define BIT16   (0x00000001 << 16)
#define BIT17   (0x00000001 << 17)
#define BIT18   (0x00000001 << 18)
#define BIT19   (0x00000001 << 19)
#define BIT20   (0x00000001 << 20)
#define BIT21   (0x00000001 << 21)
#define BIT22   (0x00000001 << 22)
#define BIT23   (0x00000001 << 23)
#define BIT24   (0x00000001 << 24)
#define BIT25   (0x00000001 << 25)
#define BIT26   (0x00000001 << 26)
#define BIT27   (0x00000001 << 27)
#define BIT28   (0x00000001 << 28)
#define BIT29   (0x00000001 << 29)
#define BIT30   (0x00000001 << 30)
#define BIT31   (0x80000000)

#define BITSL(value, shift)          ((value) << (shift))
#define BITSR(value, shift)          ((value) >> (shift))
#define VALTBITS(value, sbit, nbits) (((value) & ((1 << (nbits)) - 1)) << (sbit))
#define BITSTVAL(value, sbit, nbits) (((value) >> (sbit)) & ((1 << (nbits)) - 1))

#define BITM(shift, nbits)          (((1U << (nbits)) - 1) << shift)
#define BITFEXT(value, sbit, nbits) (((value) >> (sbit)) & ((1U << (nbits)) - 1))
#define BITFINS(dest,src,sbit,nbit) ((u32)(dest) & ~(((1U<<(nbit))-1)<<(sbit))) | (((u32)(src) & ((1U<<(nbit))-1)) << (sbit))
#define BITFUSAT(val,nbit)          ((S32)(val) < 0) ? 0 : ((x) > ((1U<<(nbit))-1) ? ((1U<<(nbit))-1) : (S32)(val))
#define BITFSSAT(val,nbit)          ((S32)(val) < -(1U<<(nbit))) ? -(1U<<(nbit)) : ((x) > ((1U<<(nbit))-1) ? ((1U<<(nbit))-1) : (S32)(val))

/* absolute 16-bit values difference */
#ifndef DIFFU16
#define DIFFU16(a, b) (((U16)(a))>((U16)(b)))?((U16)(((U16)(a))-((U16)(b)))):((U16)((65536-((U16)(b)))+((U16)(a))))
#endif

/* maximum & minimum value macros */
#ifndef MAX
#define MAX(a, b)       ((a) > (b) ? (a) : (b))
#endif

#ifndef MIN
#define MIN(a, b)       ((a) < (b) ? (a) : (b))
#endif

#ifndef ABS
#define ABS(a)          ((a) > 0 ? (a) : (-(a)))
#endif

#ifndef SIGN
#define SIGN(x) ((x)<0?-1:1)
/* maximum & minimum value macros */
#ifndef max
#define max(a, b)       ((a) > (b) ? (a) : (b))
#endif

#ifndef min
#define min(a, b)       ((a) < (b) ? (a) : (b))
#endif
#endif

#ifndef ARRAY_SIZE
#define ARRAY_SIZE(x) (sizeof(x)/sizeof(x[0]))
#endif

#ifndef ALIGN_UP
#define ALIGN_UP(value, size)   (((value)+((size)-1))&(~((size)-1)))
#endif

#ifndef ALIGN_DOWN
#define ALIGN_DOWN(value, size) ((value)&(~((size)-1)))
#endif


#ifndef PARAMETER_NOT_USED
/* Macro to remove compiler warnings for unused function parameters */
#if defined (__BORLANDC__)
#  define PARAMETER_NOT_USED(p) ((void)(p))
#elif defined (__IAR_SYSTEMS_ICC)
#  define PARAMETER_NOT_USED(p) ((p) = (p))
#elif defined(__arm)
#  define PARAMETER_NOT_USED(p) ((void)(p))
#elif defined (_ENTERPRISE_C_)
#  define PARAMETER_NOT_USED(p) ((void)(p))
#elif defined (__GNUC__) /* GCC */
#  define PARAMETER_NOT_USED(p) ((void)(p))
#elif defined(_MSC_VER)
#  define PARAMETER_NOT_USED(p) ((void)(p))
#else
#  define PARAMETER_NOT_USED(p)
#endif
#endif

#define RDP_SUCCESS 0x0
#define RDP_POINTER_NULL 0x1001
#define RDP_PARAM_OUT_OF_RANGE  0x1002

#define NOT_USED(x) ( (void)(x) )

#endif
