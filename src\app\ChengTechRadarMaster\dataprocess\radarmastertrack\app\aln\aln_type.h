/*
 * @Author: m<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-02 00:47:37
 * @LastEditors: moxinqing <EMAIL>
 * @LastEditTime: 2022-12-02 01:36:15
 * @Description: 
 */
#ifndef _ALN_TYPE_H_
#define _ALN_TYPE_H_

#ifndef PC_DBG_FW
#include "vehicle_cfg.h"
#else

#endif

// 标定模式有三种，静态标定，动态标定（服务标定），自标定（比亚迪没有用到）
#define ALN_CALC_TYPE_STATIC    0
#define ALN_CALC_TYPE_DYNAMIC   1
#define ALN_CALC_TYPE_AUTO      2

#define ROUTINE_ST_STOP     0
#define ROUTINE_ST_START    1
#define ROUTINE_ST_RUN      2

#ifdef PC_DBG_FW
// 这里在下位机是通过车型配置文件实现 仿真对应车型时, 需要修改为对应车型的参数
#define ALN_OBJ_HORIZONTAL_ANGLE_FCR		-45.0f  //前角雷达标定目标的位置，需要根据现场环境设定
#define ALN_OBJ_HORIZONTAL_ANGLE_RCR		-45.0f  //后角雷达标定目标的位置，需要根据现场环境设定
#define ALN_EXPECT_FIX_ANGLE_FCR			53.0f   //希望安装角度,即默认安装角度
#define ALN_EXPECT_FIX_ANGLE_RCR			45.0f   //希望安装角度,即默认安装角度
#endif

/**
 * @brief 下面是产线标定相关的变量定义与处理
 */
#define CALC_START                  0x1
#define CALC_STOP                   0x2
#define CALC_UNCAL_END              0x3  //未完成
#define CALC_FAILED_SPEED_OUT       0x4  //相对速度超过限制
#define CALC_FAILED_ANGLE_MAX_OUT   0x5  //角度超过限制，过大
#define CALC_FAILED_ANGLE_MIN_OUT   0x6  //角度过小
#define CALC_FAILED_MUL_ERR         0x7  //多个错误，未收到目标数量直接数量
#define CALC_FAILED_TIMEOUT         0x8  //校准超时
#define CALC_SUCCESS_END            0x9  //校准成功
#define CALC_PRO_END                0xA  //校准结束
#define CALC_FAILED_ACC             0x10 //加速度
#define CALC_FAILED_YAWRATE         0x11 // yaw

#define ROUTINE_RESULT_NO_RESULT    0
#define ROUTINE_RESULT_INCORRECT    1
#define ROUTINE_RESULT_CORRECT      2

#define ROUTINE_STATUS_INACTIVE        0x00 //<Routine inactive
#define ROUTINE_STATUS_ACTIVE          0x01 //<Routine active
#define ROUTINE_STATUS_NVM_FLAG        0x02 //<Routine NVM write not OK
#define ROUTINE_STATUS_TIMEOUT         0x03 //<Routine timeout
#define ROUTINE_STATUS_FINCORRECTLY    0x04 //<Routine finished correctly
#define ROUTINE_STATUS_ABORTED         0x05 //<Routine aborted

// 标定模式
typedef enum
{
    OEM_STATIC_ALN = 0U,
    SERVICE_DIAG_ALN,
    AUTO_ALN,
    MANUAL_SET_BY_TOOL
} ALN_CalibType_t;

#endif

