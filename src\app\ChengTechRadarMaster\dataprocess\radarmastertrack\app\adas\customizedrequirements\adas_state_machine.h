﻿/**
 * @file adas_state_machine.h
 * @brief 
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2022-09-30
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-09-30 <td>1.0     <td>shaowei     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifdef ALPSPRO_ADAS
#include "adas/generalalg/adas_manager.h"
#elif defined(PC_DBG_FW)
#include "app/adas/generalalg/adas_manager.h"
#else
#include "app/adas/generalalg/adas_manager.h"
#endif

void ADAS_switchFuncState(ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY, uint8_t hilMode, const ADAS_TimeClase_t timeClass);


