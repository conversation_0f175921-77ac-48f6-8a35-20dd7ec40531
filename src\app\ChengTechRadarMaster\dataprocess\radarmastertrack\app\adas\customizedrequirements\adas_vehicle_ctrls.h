/**
 * @file adas_vehicle_ctrls.h
 * @brief 控车头文件。
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2023-09-27
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2023-09-27 <td>1.0     <td>shaowei     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifdef ALPSPRO_ADAS
#include "vdy/vdy_interface.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/generalalg/adas_manager.h"
#elif defined(PC_DBG_FW)
#include "app/vehicle/vdy/vdy_interface.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/generalalg/adas_manager.h"
#else
#include "app/vdy/vdy_interface.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/generalalg/adas_manager.h"
#endif

/**
 * @brief 控车模块主函数
 * 
 * @param pVDY 车身数据
 * @param pCFG 配置信息
 * @param pSlaveRadar 从雷达信息
 */
void ADAS_ctrlVehicle(const VDY_Info_t *pVDY, const ADAS_RadarConfiguration_t *pCFG, const SlaveRadarWarningsStatus *pSlaveRadar, float trackTime);



