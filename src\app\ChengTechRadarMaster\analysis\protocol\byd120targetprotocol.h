﻿#ifndef BYD120TARGETPROTOCOL_H
#define BYD120TARGETPROTOCOL_H

#include "ianalysisprotocol.h"

namespace Analysis {
namespace Protocol {

class BYD120TargetProtocol : public IAnalysisProtocol
{
    Q_OBJECT
public:
    explicit BYD120TargetProtocol(AnalysisWorker *analysisWorker, QObject *parent = nullptr);

    bool analysisFrame(const Devices::Can::CanFrame &frame) override;
    static qint8 radarID(const Devices::Can::CanFrame &frame);

signals:

private:
    bool byd3TargetParse_0x291(quint8 radarID, const Devices::Can::CanFrame &frame);
    bool byd3TargetParse_0x35F(quint8 radarID, const Devices::Can::CanFrame &frame);
    bool byd3TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame);
    bool byd16TargetClear(quint8 radarID);
    bool byd16TargetParsePart_1(quint8 radarID, const Devices::Can::CanFrame &frame);
    bool byd16TargetParsePart_2(quint8 radarID, const Devices::Can::CanFrame &frame);
    bool byd16TargetParseFinished(quint8 radarID,const Devices::Can::CanFrame &frame);

    quint8 mTargetsID[MAX_RADAR_COUNT][16];
    int mTargetsIndex_1[MAX_RADAR_COUNT];
    int mTargetsIndex_2[MAX_RADAR_COUNT];
    int m0x291Count{-1};
};

} // namespace Protocol
} // namespace Analysis

#endif // BYD120TARGETPROTOCOL_H
