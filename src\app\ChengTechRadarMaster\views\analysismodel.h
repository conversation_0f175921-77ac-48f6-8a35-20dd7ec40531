﻿#ifndef ANALYSISMODEL_H
#define ANALYSISMODEL_H

#include <QAbstractTableModel>

#include "analysis/analysisdata.h"
#include "analysis/analysisdataf.h"

namespace Views {
namespace AnalysisView {

class AnalysisModelF : public QAbstractTableModel
{
    Q_OBJECT
public:
    explicit AnalysisModelF(Parser::ParsedDataTypedef::ParsedDataType dataType, QObject *parent = nullptr);

    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;

    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    Qt::ItemFlags flags(const QModelIndex& index) const override;

    quint16 monitorTargets() const { return mMonitorTarget; }
    const ViewTypes &viewAnalysisTypes() const { return mViewAnalysisTypes; }
    void setViewAnalysisTypes(bool moving, bool continuous, const ViewTypes &types);

    bool movingOnly() const { return mMovingOnly; }
    bool continuousDisplay() const { return mContinuousDisplay; }

signals:
    void monitorTarget(/*AnalysisFrameType*/int frameType, const Target &target);

public slots:
    void targets(quint8 radarID, /*AnalysisFrameType*/int frameType, const Parser::ParsedDataTypedef::TargetsF &targets);
    void changedMonitorTargetID(const quint16 id) { mMonitorTarget = id; }

private:
    Parser::ParsedDataTypedef::ParsedDataType mAnalysisFrameType{Parser::ParsedDataTypedef::ParsedDataNone};
    Parser::ParsedDataTypedef::TargetsF mTargets;

    quint16 mMonitorTarget{0};
    bool mMovingOnly{false};
    bool mContinuousDisplay{false};
    ViewTypes mViewAnalysisTypes{
        Parser::ParsedDataTypedef::X,
        Parser::ParsedDataTypedef::Y,
        Parser::ParsedDataTypedef::Range,
        Parser::ParsedDataTypedef::Angle,
        Parser::ParsedDataTypedef::PitchAngle,
        Parser::ParsedDataTypedef::V,
        Parser::ParsedDataTypedef::Vx,
        Parser::ParsedDataTypedef::Vy,
        Parser::ParsedDataTypedef::SNR,
        Parser::ParsedDataTypedef::RCS
    };
    //int mAlarmID{-1}; //报警ID
    QList<quint32> mAlarmIDList; //跟踪点报警ID
};

class AnalysisModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    explicit AnalysisModel(AnalysisFrameType dataType, QObject *parent = nullptr);

    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;

    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    Qt::ItemFlags flags(const QModelIndex& index) const override;

    quint16 monitorTargets() const { return mMonitorTarget; }
    const QList<int> &viewAnalysisTypes() const { return mViewAnalysisTypes; }
    void setViewAnalysisTypes(bool moving, bool continuous, const QList<int> &types);

    bool movingOnly() const { return mMovingOnly; }
    bool continuousDisplay() const { return mContinuousDisplay; }

signals:
    void monitorTarget(/*AnalysisFrameType*/int frameType, const Target &target);

public slots:
    void targets(quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &targets);
    void changedMonitorTargetID(int frameType, const quint16 id);

private:
    AnalysisFrameType mAnalysisFrameType{FrameRawTarget};
    Targets mTargets;

    quint16 mMonitorTarget{0};
    bool mMovingOnly{false};
    bool mContinuousDisplay{false};
    QList<int> mViewAnalysisTypes{
        X, Y, Range, Angle, PitchAngle, V, Vx, Vy, SNR, RCS
    };
    //int mAlarmID{-1}; //报警ID
    QList<quint32> mAlarmIDList; //跟踪点报警ID
};

class VehicleDataModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    explicit VehicleDataModel(QObject *parent = nullptr);

    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;

    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    Qt::ItemFlags flags(const QModelIndex& index) const override;

    void setVehicleData(const VehicleData &vehicleData);

private:
    VehicleData mVehicleData;
};

class AlarmDataModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    explicit AlarmDataModel(QObject *parent = nullptr);

    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;

    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    Qt::ItemFlags flags(const QModelIndex& index) const override;

    void setAlarmData(const AlarmData &alarmData);

private:
    AlarmData mAlarmData;
};

class EndFrameModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    explicit EndFrameModel(QObject *parent = nullptr);

    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;

    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    Qt::ItemFlags flags(const QModelIndex& index) const override;

    void setEndFrameData(const EndFrameData &endFrameData);

private:
    EndFrameData mEndFrameData;
};

} // namespace AnalysisDataView
} // namespace Views

#endif // ANALYSISMODEL_H
