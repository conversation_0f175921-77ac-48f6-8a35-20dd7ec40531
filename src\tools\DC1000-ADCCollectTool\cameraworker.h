﻿#ifndef CAMERAWORKER_H
#define CAMERAWORKER_H

#include <QObject>
#include <QImage>

#include <opencv2/opencv.hpp>
#include <iostream>

#include <string>
#include <mutex>

class CameraWorker : public QObject
{
    Q_OBJECT
public:
    explicit CameraWorker(QObject *parent = nullptr);

    bool open(int index);
    void close();

    bool startSave(const std::string &filename);
    void stopSave();

    bool isOpened() const { return mOpened; }
    bool isOpened(int index) const;
    bool isSaving() const { return mSaving; }

    bool readFrame();

    const QImage &getFrame();

signals:
    void cameraReady(unsigned long long saveIndex);

private:
    void closeDevice();
    bool Mat2QImage();

    friend void cameraReceive(CameraWorker *pCANDevice);

    cv::Mat mFrame;
    cv::VideoCapture mVideoCapture;
    cv::VideoWriter mVideoWriter;

    QImage mImage;

    int mCameraIndex{0};
    unsigned long long mSaveIndex{0};
    bool mOpened{false};
    bool mClosed{true};
    bool mSaving{false};

    std::mutex mMutexSave;
};

#endif // CAMERAWORKER_H
