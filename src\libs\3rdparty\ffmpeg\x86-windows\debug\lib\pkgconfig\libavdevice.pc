prefix=${pcfiledir}/../..
exec_prefix=${prefix}
libdir=${prefix}/lib
includedir=${prefix}/../include

Name: libavdevice
Description: FFmpeg device handling library
Version: 60.3.100
Requires: 
Requires.private: libavfilter >= 9.12.100, libswscale >= 7.5.100, libavformat >= 60.16.100, libavcodec >= 60.31.102, libswresample >= 4.12.100, libavutil >= 58.29.100
Conflicts:
Libs: "-L${libdir}" -lavdevice
Libs.private: -lpsapi -lole32 -lstrmiids -luuid -loleaut32 -lshlwapi -lgdi32 -lvfw32
Cflags: "-I${includedir}"

