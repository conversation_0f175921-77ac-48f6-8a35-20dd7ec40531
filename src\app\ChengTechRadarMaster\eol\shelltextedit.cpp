#include "shelltextedit.h"

#include <QKeyEvent>
#include <QDebug>
#include <QCoreApplication>
#include <QSettings>
#include <QTimer>

#define MAX_HISTORY_COUNT 100 //最大历史记录数
#define CFG_FILE_NAME "cmd.ini"

ShellTextEdit::ShellTextEdit(QWidget *parent)
    :QTextEdit( parent )
{
    mClearStr = "\u001B[2J\u001B[1H";

    // 设置QTextEdit背景和字体颜色
    QPalette pl = this->palette();     // 获取textEdit的调色板
    pl.setColor(QPalette::Base, Qt::black);    // 设置背景色为黑色
    pl.setColor(QPalette::Text, Qt::green);    // 设置文本颜色为绿色
    this->setPalette(pl);              // 设置回调色板

    clearText();
    loadQuickCmd();
}

void ShellTextEdit::GetHistoryCmd(QStringList &cmds)
{
    cmds = mHistoryCmd;
}

void ShellTextEdit::GetQuickCmd(QStringList &cmds)
{
    cmds = mQuickCmd;
}

void ShellTextEdit::insertCmd(const QString &cmd)
{
    getCurrentStr();
    this->insertPlainText( cmd );
    this->setFocus();
}

void ShellTextEdit::showText(const QString &text)
{
    quint32 index = 0;
    QTextCursor cursor = this->textCursor();

    for( int i=0; i<text.size(); i++ ){
//        qDebug() << __FUNCTION__ << __LINE__ << text[i];
        if( text[i] == 0x0D ){//回车键
            this->insertPlainText( text.mid( index, i-index ) );
            index = i;
            //判断上一行是否为清屏命令
            QString allStr = this->toPlainText();
            cursor.setPosition( allStr.size() - 8, QTextCursor::MoveAnchor );
            cursor.setPosition( allStr.size(), QTextCursor::KeepAnchor );
            this->setTextCursor( cursor );
            QString lastStr = cursor.selectedText();
//            qDebug() << __FUNCTION__ << __LINE__ << mClearStr<< lastStr;
            if( mClearStr == lastStr ){
                clearText();
            }else{
                //移动光标至末尾
                cursor.movePosition( QTextCursor::End );
                this->setTextCursor( cursor );
            }
        }
    }
    this->insertPlainText( text.mid( index, text.size()-index ) );

    //将光标移动至末尾
    cursor.movePosition( QTextCursor::End );
    this->setTextCursor( cursor );
    mMinCursorPos = cursor.position();

    this->setFocus();
}

void ShellTextEdit::clearHistoryCmd()
{
    mHistoryCmd.clear();
    mHistoryIndex = mHistoryCmd.size();
    emit historyCmdChanged();
}

void ShellTextEdit::setQuickCmd(const QStringList &cmds)
{
    mQuickCmd.clear();
    mQuickCmd = cmds;
    qSort( mQuickCmd.begin(), mQuickCmd.end() );
    emit quickCmdChanged();
    saveQuickCmd();
}

QString ShellTextEdit::getCurrentStr(bool bDelete)
{
    QString allStr = this->toPlainText();
    QTextCursor cursor = this->textCursor();
    cursor.setPosition( mMinCursorPos, QTextCursor::MoveAnchor );
    cursor.setPosition( allStr.size(), QTextCursor::KeepAnchor );
    this->setTextCursor( cursor );

    QString currentStr = cursor.selectedText();
    if( bDelete ){
        cursor.removeSelectedText();
    }
    cursor.movePosition( QTextCursor::End );
    this->setTextCursor( cursor );

    return currentStr;
}

bool ShellTextEdit::clearText()
{
    this->clear();
    //this->moveCursor( QTextCursor::Start );
    this->insertPlainText(logo);
    mMinCursorPos = this->textCursor().position();
    //mMinCursorPos = 0;
    return true;
}

void ShellTextEdit::backHistoryCmd()
{
//    qDebug() << __FUNCTION__ << __LINE__ << mHistoryCmd.size()<< mHistoryIndex;

    if( mHistoryIndex == 0 ){
        return;
    }
    mHistoryIndex--;
//    if( mHistoryIndex > mHistoryCmd.size() -1 ){
//        mHistoryIndex = mHistoryCmd.size() -1;
//    }
//    if( mHistoryIndex < 0 ){
//        mHistoryIndex = 0;
//    }
    getCurrentStr();
    //showText( mHistoryCmd[mHistoryIndex] );
    this->insertPlainText( mHistoryCmd[mHistoryIndex] );
}

void ShellTextEdit::nextHistoryCmd()
{
//    qDebug() << __FUNCTION__ << __LINE__ << mHistoryCmd.size()<< mHistoryIndex;
    if( mHistoryIndex >= mHistoryCmd.size() -1 ){
        return;
    }
    mHistoryIndex++;
    getCurrentStr();
    //showText( mHistoryCmd[mHistoryIndex] );
    this->insertPlainText( mHistoryCmd[mHistoryIndex] );
}

void ShellTextEdit::addHistoryCmd(const QString &cmd)
{
    mHistoryCmd << cmd;
    if( mHistoryCmd.size() > MAX_HISTORY_COUNT ){
        mHistoryCmd.removeAt( 0 );//删除最早的一条
    }
    mHistoryIndex = mHistoryCmd.size();
    emit historyCmdChanged();
}

void ShellTextEdit::saveQuickCmd()
{
    QString path = QCoreApplication::applicationDirPath();//获取exe文件所在目录
    QSettings *configIni = new QSettings (tr("%1/%2").arg(path).arg(CFG_FILE_NAME),
                                          QSettings::IniFormat);
    configIni->beginGroup("Cmd");

    configIni->setValue( "QuickCmd", mQuickCmd );

    configIni->endGroup();
    delete configIni;
}

void ShellTextEdit::loadQuickCmd()
{
    QString path = QCoreApplication::applicationDirPath();//获取exe文件所在目录
    QSettings *configIni = new QSettings (tr("%1/%2").arg(path).arg(CFG_FILE_NAME),
                                          QSettings::IniFormat);

    mQuickCmd = configIni->value("Cmd/QuickCmd",tr("")).toStringList();
    delete configIni;
    emit quickCmdChanged();
}

void ShellTextEdit::completionCmd()
{
    QString prefixStr = getCurrentStr( false );
    if( prefixStr.simplified() == 0 ){
        return;
    }
    QStringList likeCmds;
    for( int i=0; i<mQuickCmd.size(); i++ ){
        if( mQuickCmd[i].indexOf( prefixStr ) == 0 ){
            likeCmds << mQuickCmd[i];
        }
    }

    if( likeCmds.size() == 0 ){
        return;
    }else if( likeCmds.size() == 1 ){//只有一个相似命令
        getCurrentStr();
        insertCmd( likeCmds[0] );
    }else{//多个相似命令
        //另起一行，将所有相似命令都显示
        QString listStr = "\r";
        for( int i=0; i<likeCmds.size(); i++ ){
            listStr.append( likeCmds[i] );
            listStr.append( "\t" );
        }
        showText( listStr );
        //qDebug() << __FUNCTION__ << __LINE__ << listStr << prefixStr;
        //另起一行
        emit shellCmd("");
        //等待上面的信号响应，再保留之前的输入内容
        QEventLoop loop;
        QTimer timer;
        timer.setSingleShot( true );
        connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
        timer.start( 200 );
        loop.exec();
        timer.stop();
        insertCmd( prefixStr );
    }

}

void ShellTextEdit::showEvent(QShowEvent *event)
{
    this->setFocus();
    QTextEdit::showEvent( event );
}

bool ShellTextEdit::isInputKey(QKeyEvent *keyEvent)
{
    if( keyEvent->key() >= Qt::Key_Space && keyEvent->key() <= Qt::Key_yen ){
        return  true;
    }
//    if( keyEvent->key() >= Qt::Key_A && keyEvent->key() <= Qt::Key_Z ){
//        return  true;
//    }

    switch( keyEvent->key() ){
//    case Qt::Key_Backspace:
//    case Qt::Key_Tab:
    case Qt::Key_Return:
    case Qt::Key_Enter:
//    case Qt::Key_Delete:
             return true;
    default:
        return false;
    }
    return false;
}

bool ShellTextEdit::event(QEvent *event)
{
    if( event->type() != QEvent::KeyPress ){
        return QTextEdit::event( event );
    }
    QKeyEvent *k = static_cast<QKeyEvent *>(event);
    if(nullptr == k){
      return QTextEdit::event( event );
    }

    QTextCursor txtcur = this->textCursor();
    if( isInputKey( k ) && txtcur.position() < mMinCursorPos ){//若为输入按键且光标位置不对
        //将光标移动至末尾
        txtcur.movePosition( QTextCursor::End );
        this->setTextCursor( txtcur );
    }

    QString selectStr = txtcur.selectedText();

    switch(k->key()){
    case Qt::Key_Delete:
        if( txtcur.position() < mMinCursorPos || selectStr.size() != 0 ){
            return true; //禁止删除之前的文本
        }
        break;
    case Qt::Key_Backspace:
        if( txtcur.position() <= mMinCursorPos || selectStr.size() != 0 ){
            return true; //禁止删除之前的文本
        }
        break;
    case Qt::Key_Left:
        if( txtcur.position() <= mMinCursorPos ){
            return true; //防止光标向左移动过多
        }
        break;
    case Qt::Key_Return:
    case Qt::Key_Enter:
    {
        QString cmd = getCurrentStr();
        if( cmd.size() != 0 ){
            addHistoryCmd( cmd );
        }
        emit shellCmd( cmd );
        return  true;
    }
    case Qt::Key_Up:
        backHistoryCmd();
        return true;
    case Qt::Key_Down:
        nextHistoryCmd();
        return true;
    case Qt::Key_Tab:
        completionCmd();
        return true;
    default:
        break;
    }

    return QTextEdit::event( event );
}

