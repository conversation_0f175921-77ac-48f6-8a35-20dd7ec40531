﻿#include "screenrecordsaveworker.h"

#include <QDateTime>
#include <QDebug>
#include <QScreen>
#include <QApplication>
#include <QGuiApplication>
#include <QtConcurrent>
#include <QPixmap>
#include <QProcess>


ScreenRecordSaveWorker::ScreenRecordSaveWorker(QObject *parent) : QObject(parent)
{
}

void ScreenRecordSaveWorker::stopSave()
{
    mSaving = false;
    QThread::msleep( 100 );
    mergePictures();
    return;
}

void ScreenRecordSaveWorker::screenRecord()
{
    QMutexLocker lock( &mMutex );
    QScreen *screen = QGuiApplication::primaryScreen();
    QString filePathName;

    QDir logDir(mPicSavePath);
    if (!logDir.exists()) {
        logDir.setPath("");
        logDir.mkpath(mPicSavePath);
    }

    mFrameIndex = 0;
    while( mSaving ){
        mFrameIndex++;
//        filePathName = QString( "%1/%2.jpg" ).arg( mPicSavePath ).arg( mFrameIndex );
        quint64 curMs = QDateTime::currentMSecsSinceEpoch();
        if( mFrameIndex == 1 ){
            mBeginTimes = curMs;
        }

        mVtFile.write( QString::number( mFrameIndex ).toLocal8Bit() + " " + QString::number( curMs ).toLocal8Bit() + " " );
        mVtFile.write( QDateTime::fromMSecsSinceEpoch( curMs ).toString("yyyy-MM-dd hh:mm:ss:zzz").toLocal8Bit() );
        mVtFile.write( "\n" );

        mEndTimes = curMs/*QDateTime::currentMSecsSinceEpoch()*/;

        //screen->grabWindow(0).save(filePathName, "jpg");
        QPixmap pix = screen->grabWindow(0);
        QtConcurrent::run( this, &ScreenRecordSaveWorker::saveImage, pix, mFrameIndex );


        QThread::msleep( 20 );
//        QThread::msleep( 1 );
    }
}

void ScreenRecordSaveWorker::saveImage(const QPixmap &pixmap, quint64 index)
{
    QString filePathName = QString( "%1/%2.jpg" ).arg( mPicSavePath ).arg( index );
    pixmap.save( filePathName, "jpg" );
}

void ScreenRecordSaveWorker::mergePictures()
{
//    mVtFile.write( QDateTime::fromMSecsSinceEpoch( mBeginTimes ).toString("yyyy-MM-dd hh:mm:ss:zzz").toLocal8Bit() );
//    mVtFile.write( "\n" );
//    mVtFile.write( QDateTime::fromMSecsSinceEpoch( mEndTimes ).toString("yyyy-MM-dd hh:mm:ss:zzz").toLocal8Bit() );
//    mVtFile.write( "\n" );
    mVtFile.close();

    //ffmpeg -framerate 10 -i "../2023-11-20 18-11-44-096/Video/%d.jpg" -codec copy output.mkv
    qDebug() << __FUNCTION__ << __LINE__ << mEndTimes - mBeginTimes;
    quint32 framerate =  mFrameIndex / ( ( mEndTimes - mBeginTimes ) / 1000 );
    QStringList params;
    params << "-framerate";
    params << /*"10"*/ QString::number( framerate );
    params << "-i";
    params << mPicSavePath + "%d.jpg";
    params << "-codec";
    params << "copy";
    params << mVideoFileName;
    QProcess::startDetached( "./ffmpeg/ffmpeg.exe", params );
}



bool ScreenRecordSaveWorker::startSave(const QString &savePath, const QDateTime &beginTime )
{
    QString beginTimeStr = QString( "%1" ).arg( beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz") );
    mVideoFileName = QString( "%1.mkv" ).arg( beginTimeStr );
    mVtFileName = QString( "%1.vt" ).arg( beginTimeStr );

    mVtFileName = QString("%1/%2").arg(savePath)
            .arg( mVtFileName );

    mVtFile.setFileName( mVtFileName );
    mVtFile.open( QIODevice::WriteOnly );
    mVtFile.write( mVideoFileName.toLocal8Bit() );
    mVtFile.write( "\n" );

    mVideoFileName = QString("%1/%2").arg(savePath)
            .arg( mVideoFileName );

    QMutexLocker lock( &mMutex );
    mSaving = true;
    mPicSavePath = savePath + "/picture/";
    QtConcurrent::run( this, &ScreenRecordSaveWorker::screenRecord );
    return true;
}
