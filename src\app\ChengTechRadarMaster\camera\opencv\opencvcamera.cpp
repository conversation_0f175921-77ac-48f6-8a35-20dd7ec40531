﻿#include "opencvcamera.h"

#include <opencv2/highgui/highgui_c.h>
#include <windows.h>
#include <QWidget>
#include <QDebug>

#pragma comment  (lib,"User32.lib")
#pragma comment  (lib,"Gdi32.lib")

OpenCVCamera::OpenCVCamera(int index, QObject *parent) : QObject(parent), mCameraIndex(index)
{

}

OpenCVCamera::~OpenCVCamera()
{

}

//图像格式转换
QImage OpenCVCamera::MatToQImage2(const cv::Mat &mat)
{
    //cvtColor(mat, mat, CV_BGR2RGB);     //  OpenCV中Mat读入的图像是BGR格式，要转换为RGB格式

#if (!defined(QT_DEBUG) && !defined(_MSC_VER))
    cv::resize(mat, mat, cv::Size(VIDEO_WIDTH, VIDEO_HIGNT));
#endif
    QImage image((const uchar*)mat.data, mat.cols, mat.rows, QImage::Format_BGR888);
    return image;
}

void OpenCVCamera::rotationAngle(int angle, bool rotation)
{
    mRotationAngle = angle;
    mRotate = rotation;
    qDebug() << __FUNCTION__ << __LINE__ << angle << rotation;
}

bool OpenCVCamera::isOpened() const
{
    return mState == QCamera::ActiveState || isRunning;
}

bool OpenCVCamera::isSaveing() const
{
    return gSaveVideo.isOpened();
}

void OpenCVCamera::namedWindowToWidget(std::string namedWindowTitle, QWidget *widget)
{
    qDebug() << __FUNCTION__ << __LINE__ << namedWindowTitle.c_str();
    // 取 opencv 的 namedWindow窗体 句柄
    HWND hwnd = (HWND)cvGetWindowHandle(namedWindowTitle.c_str()); //转char*

    //取title窗体的父窗体句柄
    HWND hparent =::GetParent(hwnd);

    //改变某个子窗体的父窗口(子窗口句柄,新的父窗口句柄)
    ::SetParent(hwnd, (HWND)widget->winId());  //winId()取win32api要用的窗口句柄

    //显示窗体SW_SHOW  隐藏窗体SW_HIDE
    ::ShowWindow(hparent, SW_HIDE);
    cv::resizeWindow(namedWindowTitle, cv::Size(widget->width(), widget->height()));
}

void OpenCVCamera::run()
{
    /**/
    qDebug()<<"cameraThread thread run..."<<QThread::currentThreadId();
    isRunning = true;
    cv::Mat frame;//用来存放读取的视频序列
    cv::Point origin;
    origin.x = 0;
    origin.y = 40;
    int frameIdx = 0;
    QString logtxt;
    while(isRunning)
    {
        QMutexLocker locker(&mMutex);
        if(gCapture.isOpened())
        {
            if (!gCapture.read(frame))//读取ing
            {
                continue;
            }

            //获取时间等文本信息
            logtxt.clear();
            logtxt.append(QString("%1  I:%2 S:%3  R:%4  T:%5  %6  %7")
                          .arg(QDateTime::currentDateTime().toString("yyyy/MM/dd hh:mm:ss:zzz"),24,QLatin1Char(' ') )
                          .arg(QString::number(frameIdx),6,QLatin1Char(' ') )
                          .arg(QString::number(mSaveIndex + 1),6,QLatin1Char(' ') )
                          .arg(QString::number(gRawFrameCount),4,QLatin1Char(' ') )
                          .arg(QString::number(gTraFrameCount),4,QLatin1Char(' ') )
                          .arg(QString::number(gMeasCounter),6,QLatin1Char(' ') )
                          .arg(gIsRecord,6,QLatin1Char(' ') )
                          );

#if (!defined(QT_DEBUG) && !defined(_MSC_VER))
            if (mRotate) {
                cv::rotate(frame, frame, mRotationAngle); // 0:旋转90度;1:旋转180度;2:旋转270度
            }
            //显示增加文本
            cv::putText(frame, logtxt.toStdString(), origin, cv::FONT_HERSHEY_COMPLEX, 1, cv::Scalar(0, 255, 0),0);
#endif

            //保存视频
            if(gSaveVideo.isOpened())
            {
                gSaveVideo << frame;
                gIsRecord = "Record";
                emit cameraSaveIndex(mCameraIndex, ++mSaveIndex);
            }
            else
            {
                gIsRecord = "No";
            }
#if 0
            imshow(QString("OpenCVCamera_%1").arg(mCameraIndex).toStdString(), frame);//显示当前帧图像
#else
            QImage image = MatToQImage2(frame);

            //发送帧信息
            emit showImage(image, frameIdx++);
#endif
        }
        QCoreApplication::processEvents(QEventLoop::AllEvents , 100);
        QThread::msleep(30);
    }
    close();
    stopSave();
    emit stoped();
    qDebug()<<"cameraThread thread End..."<<QThread::currentThreadId();
}

void OpenCVCamera::open()
{
    close();
    if(mCameraIndex >= 0)
    {
        if (!gCapture.open(mCameraIndex, cv::CAP_DSHOW)) {
            qDebug() << __FUNCTION__ << __LINE__ << mCameraIndex << "message";
        }
        gCapture.set(cv::CAP_PROP_FRAME_WIDTH,VIDEO_WIDTH);
        gCapture.set(cv::CAP_PROP_FRAME_HEIGHT,VIDEO_HIGNT);
    }

    mState = QCamera::ActiveState;
    emit stateChanged(mState);
    emit opened();
}

void OpenCVCamera::close()
{
    QMutexLocker locker(&mMutex);
    isRunning = false;
    stopSave();
    if(gCapture.isOpened())
    {
        gCapture.release();
    }

    mState = QCamera::UnloadedState;
    emit stateChanged(mState);
    emit closed();
}

void OpenCVCamera::startSave(QString pathFileName)
{
    cv::Size S=cv::Size((int)gCapture.get(cv::CAP_PROP_FRAME_WIDTH),(int)gCapture.get(cv::CAP_PROP_FRAME_HEIGHT));
    gSaveVideo.open(pathFileName.toStdString(),cv::VideoWriter::fourcc('D','I','V','X'),30,S);

    qDebug()<<"slotStartSave=="<<pathFileName;
}

void OpenCVCamera::startSaveVideo(const QString &savePath, quint64 saveTime)
{
    mSaveIndex = -1;
    startSave(QString("%1/%2_%3.mp4")
              .arg(savePath)
              .arg(QDateTime::fromMSecsSinceEpoch(saveTime).toString("yyyy-MM-dd hh-mm-ss-zzz"))
              .arg(mCameraIndex).toLocal8Bit());
}

void OpenCVCamera::stopSave()
{
    if(gSaveVideo.isOpened())
    {
        gSaveVideo.release();
    }
    mSaveIndex = -1;
}

void OpenCVCamera::slotSaveFrameCount(int RawFrameCount, int TraFrameCount, int MeasCounter)
{
    if(RawFrameCount != -1)
    {
        gRawFrameCount = RawFrameCount;
    }
    if(TraFrameCount != -1)
    {
        gTraFrameCount = TraFrameCount;
    }
    gMeasCounter = MeasCounter;
    //qDebug()<<__FUNCTION__<<gRawFrameCount<<gTraFrameCount<<gMeasCounter;
}
