﻿#include "alarmwidget.h"
#include "ui_alarmwidget.h"

AlarmWidget::AlarmWidget(const QString &text, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::AlarmWidget)
{
    ui->setupUi(this);
    ui->labelAlarmState->setToolTip(QString::fromLocal8Bit("告警状态:0-(Passive)无颜色;1-(OFF)黑色;2-(Active)绿色;3-(Fault)红色"));
    ui->labelAlarmLevel->setText(text);
    ui->labelAlarmLevel->setToolTip(QString::fromLocal8Bit("告警级别:0-无颜色;1-红色;2-深红色"));
}

AlarmWidget::~AlarmWidget()
{
    delete ui;
}

bool AlarmWidget::alarm(int level, int state)
{
//    2-active是绿色，3-fault是红色，1-OFF是黑色，0-Passive是灰色
    switch (state) {
    case 0x1:
        ui->labelAlarmState->setStyleSheet("background-color: rgb(0, 0, 0);");
        break;
    case 0x2:
        ui->labelAlarmState->setStyleSheet("background-color: rgb(0, 255, 0);");
        break;
    case 0x3:
        ui->labelAlarmState->setStyleSheet("background-color: rgb(255, 0, 0);");
        break;
    case 0x0:
    default:
        ui->labelAlarmState->setStyleSheet("");
        break;
    }

    switch (level) {
    case 1:
        ui->labelAlarmLevel->setStyleSheet("background-color: rgb(255, 0, 0);");
        break;
    case 2:
        ui->labelAlarmLevel->setStyleSheet("background-color: rgb(100, 0, 0);");
        break;
    case 0:
    default:
        ui->labelAlarmLevel->setStyleSheet("");
        return false;
    }

    return true;
}
