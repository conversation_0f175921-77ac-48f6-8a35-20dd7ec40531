﻿/**
 * @file rdp_kf_init.c
 * @brief 
 * <AUTHOR> (s<PERSON><PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef PC_DBG_FW
#include "rdp_main_ekf.h"
#else
#include "alg/track/rdp_main_ekf.h"
#endif


float FAR_A[36];
float FAR_Q[36]; 

float gEKF_A[36];
float gEKF_Q[36];
float gEKF_P[36] = 
{           4.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.25000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            ,10.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            ,2.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            ,2.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 0.00000000000000000000000000000000f
            , 2.00000000000000000000000000000000f
};

float gEKF_R[9] = {
           0.0f
           ,0.0f
           ,0.0f
           ,0.0f
           ,0.0f
           ,0.0f
           ,0.0f
           ,0.0f
           ,0.0f
           };
