<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DebugShellForm</class>
 <widget class="QWidget" name="DebugShellForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>815</width>
    <height>354</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <widget class="ShellTextEdit" name="shellTextEdit"/>
     <widget class="QWidget" name="widget" native="true">
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QComboBox" name="comboBoxCmdList">
         <item>
          <property name="text">
           <string>Quick Cmd List</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>History Cmd List</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <widget class="QListWidget" name="listWidgetCmd"/>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonCmdList">
         <property name="text">
          <string>Clear History Cmd</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ShellTextEdit</class>
   <extends>QTextEdit</extends>
   <header location="global">shelltextedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
