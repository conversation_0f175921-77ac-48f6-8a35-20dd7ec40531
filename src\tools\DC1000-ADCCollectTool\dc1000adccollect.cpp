﻿#include "dc1000adccollect.h"
#include "ui_dc1000adccollect.h"

#include "version.h"
#include "CANDeviceZLG.h"
#include "cansaveworker.h"
#include "canparseworker.h"
#include "cameraworker.h"
#include "dc1000worker.h"
#include "utils/settingshandler.h"

#include <QThread>
#include <QCameraInfo>
#include <QFileInfo>
#include <QProcess>
#include <QFileDialog>
#include <QDebug>

DC1000ADCCollect::DC1000ADCCollect(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::DC1000ADCCollect)
{
    qRegisterMetaType<AnalysisData>("AnalysisData");

    ui->setupUi(this);
    this->setWindowTitle(QString("%1 %2").arg(CHENGTECH_TOOL_NAME).arg(CHENGTECH_TOOL_VERSION));
    mLabelSaveFilename = new QLabel(this);
    ui->statusbar->addWidget(mLabelSaveFilename);

    mCANSaveWorker = new CANSaveWorker();
    mCANParseWorker = new CANParseWorker(mCANSaveWorker);

    connect(mCANParseWorker, &CANParseWorker::targets, ui->targetsView, &TargetsView::targets);

    mCANDeviceZLG = new CANDeviceZLG(mCANParseWorker->frameQueue());

    mCameraWorker = new CameraWorker;
    connect(mCameraWorker, &CameraWorker::cameraReady, this, &DC1000ADCCollect::imShow);

    mDC1000Worker = new DC1000Worker(mCANDeviceZLG, mCANSaveWorker, mCameraWorker);
    QThread *thread = new QThread;
    mDC1000Worker->moveToThread(thread);
//    connect(mDC1000Worker, &DC1000Worker::opened, this, [=](){ thread->start(); });
    connect(mDC1000Worker, &DC1000Worker::showLog, this, &DC1000ADCCollect::appendLog);
    connect(mDC1000Worker, &DC1000Worker::clientConnected, this, &DC1000ADCCollect::clientConnected);
    connect(mDC1000Worker, &DC1000Worker::collectStatusChanged, this, &DC1000ADCCollect::collectStatusChanged);
    connect(mDC1000Worker, &DC1000Worker::saveFilename, this, &DC1000ADCCollect::saveFilename);
    connect(this, &DC1000ADCCollect::startDC1000Worker, mDC1000Worker, &DC1000Worker::start);
    connect(this, &DC1000ADCCollect::closed, thread, &QThread::quit);

    connect(mCANParseWorker, &CANParseWorker::radarReceived, mDC1000Worker, &DC1000Worker::radarReceived);
    connect(mCANParseWorker, &CANParseWorker::radarAlready, mDC1000Worker, &DC1000Worker::radarAlready);
    connect(mCANParseWorker, &CANParseWorker::radarCollectStoped, mDC1000Worker, &DC1000Worker::radarCollectStoped);

    thread->start();

    loadSettings();
}

DC1000ADCCollect::~DC1000ADCCollect()
{
    delete ui;
}

void DC1000ADCCollect::closeEvent(QCloseEvent *event)
{

    if (mDC1000Worker->isCollecting()) {
        mDC1000Worker->stop();
    }
    int i = 10;
    while (i && mDC1000Worker->isCollecting()) {
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
    }

    if (mDC1000Worker->isOpened()) {
        mDC1000Worker->close();
    }
    emit closed();

    if (mCANDeviceZLG->isOpened()) {
        mCANDeviceZLG->close();
    }
    if (mCANParseWorker->isParsing()) {
        mCANParseWorker->stop();
    }

    if (mCameraWorker->isOpened()) {
        mCameraWorker->close();
    }

    i = 10;
    while (i && !mCANDeviceZLG->isClosed()) {
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
    }

    qDebug() << __FUNCTION__ << __LINE__ << mCANDeviceZLG->isClosed() << mCANParseWorker->isParsing();

    saveSttings();
}

void DC1000ADCCollect::clientConnected(unsigned short port, bool ok)
{
    if (ok) {
        appendLog(QString::fromLocal8Bit("Port %1 Connected.").arg(port));
    } else {
        appendLog(QString::fromLocal8Bit("Disconnected."));
    }

    qDebug() << __FUNCTION__ << __LINE__ << mDC1000Worker->isConnected() << mCANDeviceZLG->isOpened();
    ui->pushButtonStart->setEnabled(mDC1000Worker->isConnected() && mCANDeviceZLG->isOpened());
}

void DC1000ADCCollect::collectStatusChanged()
{
    ui->pushButtonStart->setText(mDC1000Worker->isCollecting() ? "Stop" : "Start");
}

void DC1000ADCCollect::imShow(unsigned long long saveIndex)
{
    mCANParseWorker->cameraReady(saveIndex);
    const QImage &image = mCameraWorker->getFrame();
    ui->labelVideo->setPixmap(QPixmap::fromImage(image).scaled(ui->labelVideo->size()));
}

void DC1000ADCCollect::saveFilename(const QString &filename)
{
    mLabelSaveFilename->setText(filename);
}

void DC1000ADCCollect::on_pushButtonOpenCANDevice_clicked()
{
    if (!mCANDeviceZLG->isOpened()) {
        if (!mCANParseWorker->isParsing()) {
            if (!mCANParseWorker->start()) {
                appendLog(QString::fromLocal8Bit("解析线程未启动!!!"));
                return;
            }
        }
        if (!mCANDeviceZLG->open(ui->comboBoxDeviceIndex->currentIndex(),
                            ui->comboBoxBaudRate->currentText().toInt(),
                            ui->comboBoxDataRate->currentText().toInt(),
                            ui->checkBoxDeviceCANFD->isChecked())) {

            appendLog(QString::fromLocal8Bit("CAN 设备打开失败!!! %1").arg(mCANDeviceZLG->errorString().c_str()));
            return;
        }
        appendLog(QString::fromLocal8Bit("CAN 设备打开成功."));
    } else {
        mCANDeviceZLG->close();
        mCANParseWorker->stop();
    }

    ui->pushButtonOpenCANDevice->setText(mCANDeviceZLG->isOpened() ? "Close CAN" : "Open CAN");
    ui->pushButtonStart->setEnabled(mDC1000Worker->isConnected() && mCANDeviceZLG->isOpened());
}

void DC1000ADCCollect::appendLog(const QString &text)
{
    ui->plainTextEditLog->appendPlainText(text);
}

void DC1000ADCCollect::on_pushButtonOpenCamera_clicked()
{
    if (!mCameraWorker->isOpened()) {
        if (!mCameraWorker->open(ui->comboBoxCamera->currentData().toInt())) {
            appendLog(QString::fromLocal8Bit("摄像头打开失败!!!"));
            return;
        }
        appendLog(QString::fromLocal8Bit("摄像头打开成功."));
    } else {
        mCameraWorker->close();
    }

    ui->pushButtonOpenCamera->setText(mCameraWorker->isOpened() ? "Close Camera" : "Open Camera");
}

void DC1000ADCCollect::on_pushButtonCameraRefresh_clicked()
{
    ui->comboBoxCamera->clear();
    const QList<QCameraInfo> availableCameras = QCameraInfo::availableCameras();
    qDebug() << __FUNCTION__ << __LINE__ << "message" << availableCameras.size();
    int i = 0;
    for (const QCameraInfo &cameraInfo : availableCameras)
    {
        ui->comboBoxCamera->addItem(cameraInfo.description(), i++);
    }
}

void DC1000ADCCollect::on_pushButtonConnect_clicked()
{
    if (!mDC1000Worker->isOpened()) {
        if (!mDC1000Worker->open((DC1000Worker::Type)ui->comboBoxType->currentIndex())) {
            appendLog(QString::fromLocal8Bit("网络服务启动失败!!! %1").arg(mDC1000Worker->errorstring().c_str()));
            return;
        }
        appendLog(QString::fromLocal8Bit("网络服务启动成功."));
    } else {
        mDC1000Worker->close();
    }

    ui->pushButtonConnect->setText(mDC1000Worker->isOpened() ? "Disonnect" : "Connect");
}

void DC1000ADCCollect::on_pushButtonStart_clicked()
{
    if (!mDC1000Worker->isCollecting()) {
        ui->plainTextEditLog->clear();
        int frameSize = ui->lineEditSingleFrameSize->text().toInt();
        if (!ui->checkBoxByte->isChecked()) {
            frameSize *= 1024 *1024;
        }
        unsigned long long size = (unsigned long long)frameSize * (unsigned long long)ui->lineEditFrameCount->text().toUInt();
//        qDebug() << __FUNCTION__ << __LINE__ << frameSize << size << ui->lineEditFrameCount->text().toUInt();
        emit startDC1000Worker(frameSize, size, ui->comboBoxCANChannel->currentIndex(),
                               ui->spinBoxChirpCount->value(), ui->comboBoxRadarID->currentText().toUInt(),
                               ui->spinBoxVechileJump->value(),
                               ui->lineEditFileADC->text(), ui->lineEditFileCAN->text());
    } else {
        mDC1000Worker->stop();
    }
}

void DC1000ADCCollect::on_pushButtonOpenSavePath_clicked()
{
    QProcess::startDetached(QString("explorer %1").arg(QFileInfo(mDC1000Worker->savePath()).absoluteFilePath().replace("/", "\\")));
}

void DC1000ADCCollect::on_actionTargetViewSettings_triggered()
{
    ui->targetsView->config();
}

void DC1000ADCCollect::saveSttings()
{
    SETTINGS_SET_VALUE(QLatin1String("TargetViewSettings"), ui->targetsView->getSettings());
    SETTINGS_SET_VALUE(QLatin1String("DC1000Type"), ui->comboBoxType->currentIndex());
    SETTINGS_SET_VALUE(QLatin1String("RadarID"), ui->comboBoxRadarID->currentIndex());
    SETTINGS_SET_VALUE(QLatin1String("ChirpCount"), ui->spinBoxChirpCount->value());
    SETTINGS_SET_VALUE(QLatin1String("UseByte"), ui->checkBoxByte->isChecked());
    SETTINGS_SET_VALUE(QLatin1String("SingleFrameSize"), ui->lineEditSingleFrameSize->text());
    SETTINGS_SET_VALUE(QLatin1String("FrameCount"), ui->lineEditFrameCount->text());
    SETTINGS_SET_VALUE(QLatin1String("ADCFilename"), ui->lineEditFileADC->text());
    SETTINGS_SET_VALUE(QLatin1String("CANFilename"), ui->lineEditFileCAN->text());
}

void DC1000ADCCollect::loadSettings()
{

    ui->targetsView->setSettings(SETTINGS_GET_VALUE(QLatin1String("TargetViewSettings")));
    ui->comboBoxType->setCurrentIndex(SETTINGS_GET_VALUE(QLatin1String("DC1000Type")).toUInt());
    ui->comboBoxRadarID->setCurrentIndex(SETTINGS_GET_VALUE(QLatin1String("RadarID")).toUInt());
    ui->spinBoxChirpCount->setValue(SETTINGS_GET_VALUE(QLatin1String("ChirpCount"), 256).toUInt());
    ui->checkBoxByte->setChecked(SETTINGS_GET_VALUE(QLatin1String("UseByte"), false).toBool());
    ui->lineEditSingleFrameSize->setText(SETTINGS_GET_VALUE(QLatin1String("SingleFrameSize"), 2).toString());
    ui->lineEditFrameCount->setText(SETTINGS_GET_VALUE(QLatin1String("FrameCount"), 100).toString());
    ui->lineEditFileADC->setText(SETTINGS_GET_VALUE(QLatin1String("ADCFilename")).toString());
    ui->lineEditFileCAN->setText(SETTINGS_GET_VALUE(QLatin1String("CANFilename")).toString());
}

void DC1000ADCCollect::on_pushButtonFileADC_clicked()
{
    QString fileName = QFileDialog::getOpenFileName(this, tr("ADC File"), ui->lineEditFileADC->text(), tr("ADC (*.dat);; ADC (*.bin)"));
    if (fileName.isEmpty()) {
        return;
    }

    ui->lineEditFileADC->setText(fileName);
}

void DC1000ADCCollect::on_pushButtonFileCAN_clicked()
{
    QString fileName = QFileDialog::getOpenFileName(this, tr("CAN File"), ui->lineEditFileADC->text(), tr("CAN (*.asc *.blf)"));
    if (fileName.isEmpty()) {
        return;
    }

    ui->lineEditFileCAN->setText(fileName);
}
