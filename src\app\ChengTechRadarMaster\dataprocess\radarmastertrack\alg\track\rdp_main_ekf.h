/**
 * @file rdp_main_ekf.h
 * @brief 
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef __MAIN_EKF_H__
#define __MAIN_EKF_H__

/* Include Files */
#include <math.h>
#include <stddef.h>
#include <stdlib.h>
#include <string.h>

extern float x_[6];
/* Function Declarations */
extern void RDP_runMainEKF(const float EKF_A[36], const float EKF_Q[36], const float old_sigma[2],
              const float old_P[36], const float EKF_R[9], const float old_x[6],
              int miss, const float old_z[3], float T, float new_sigma[2],
              float new_P[36], float new_x[6], float new_z[3], float pre_z[3]);
extern void predictEkf(const float A[36], const float old_x[6], float new_x[6]);
extern void near_efk(const float old_x[6] ,const float cx, const float cy,const float ov ,const float dif_spd, float kp[2],
                    int miss ,float T, float new_x[6] , float new_z[3] , float pre_z[3] , int dir);
#endif

/*
 * File trailer for rdp_main_ekf.h
 *
 * [EOF]
 */
