﻿#include "filebatchparsingform.h"
#include "ui_filebatchparsingform.h"

#include "filebatchparsingworker.h"
#include "utils/settingshandler.h"

#include <QThread>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>

FileBatchParsingForm::FileBatchParsingForm(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::FileBatchParsingForm)
{
    ui->setupUi(this);
    ui->lineEditDataPath->setText(SETTINGS_GET_VALUE("FileBatchParsing/DataPath", "./data").toString());
    ui->lineEditSavePath->setText(SETTINGS_GET_VALUE("FileBatchParsing/SavePath", "./dataBatch").toString());

    QThread *thread = new QThread;
    mFileBatchParsingWorker = new FileBatchParsingWorker;
    mFileBatchParsingWorker->moveToThread(thread);

    connect(mFileBatchParsingWorker, &FileBatchParsingWorker::started,
            this, [=](){ thread->start(); });
    connect(thread, &QThread::started, mFileBatchParsingWorker, &FileBatchParsingWorker::generate);
    connect(mFileBatchParsingWorker, &FileBatchParsingWorker::stoped, thread, &QThread::quit);

    connect(mFileBatchParsingWorker, &FileBatchParsingWorker::message,
            this, &FileBatchParsingForm::message);
    connect(mFileBatchParsingWorker, &FileBatchParsingWorker::stoped,
            this, &FileBatchParsingForm::stoped);
}

FileBatchParsingForm::~FileBatchParsingForm()
{
    delete ui;
}

void FileBatchParsingForm::message(const QString &msg)
{
    ui->plainTextEditLog->appendPlainText(msg);
}

void FileBatchParsingForm::stoped()
{
    ui->pushButtonGenerate->setText(QString::fromLocal8Bit(mFileBatchParsingWorker->isGenerating() ? "停止" : "生成"));
    if (!mFileBatchParsingWorker->isGenerating()) {
        message(QString::fromLocal8Bit("数据生成完成"));
    }
}

void FileBatchParsingForm::on_pushButtonDataPath_clicked()
{
    QString dir = QFileDialog::getExistingDirectory(this, tr("Open Directory"),
                                                      ui->lineEditDataPath->text(),
                                                      QFileDialog::ShowDirsOnly
                                                      | QFileDialog::DontResolveSymlinks);
    if (dir.isEmpty()) {
        return;
    }

    SETTINGS_SET_VALUE("FileBatchParsing/DataPath", dir);
    ui->lineEditDataPath->setText(dir);
}

void FileBatchParsingForm::on_pushButtonSavePath_clicked()
{
    QString dir = QFileDialog::getExistingDirectory(this, tr("Open Directory"),
                                                      ui->lineEditSavePath->text(),
                                                      QFileDialog::ShowDirsOnly
                                                      | QFileDialog::DontResolveSymlinks);
    if (dir.isEmpty()) {
        return;
    }

    SETTINGS_SET_VALUE("FileBatchParsing/SavePath", dir);
    ui->lineEditSavePath->setText(dir);
}

void FileBatchParsingForm::on_pushButtonGenerate_clicked()
{
    if (ui->pushButtonGenerate->text() == QString::fromLocal8Bit("生成")) {
        ui->plainTextEditLog->clear();
        if (!mFileBatchParsingWorker->start(ui->lineEditDataPath->text(),
                                            ui->lineEditSavePath->text(),
                                            ui->comboBoxFileType->currentIndex(),
                                            ui->checkBoxDeleteOld->isChecked())) {
            QMessageBox::warning(this,
                                 QString::fromLocal8Bit("文件批量处理"),
                                 QString::fromLocal8Bit("%1\n%2\n%3")
                                 .arg(ui->lineEditDataPath->text())
                                 .arg(ui->lineEditSavePath->text())
                                 .arg(mFileBatchParsingWorker->errorString()));
        } else {
            ui->plainTextEditLog->appendPlainText("ERROR:" + mFileBatchParsingWorker->errorString() + "\n");
            ui->plainTextEditLog->appendPlainText(mFileBatchParsingWorker->filesText());
        }
    } else {

        mFileBatchParsingWorker->stop();
    }


    ui->pushButtonGenerate->setText(QString::fromLocal8Bit(mFileBatchParsingWorker->isGenerating() ? "停止" : "生成"));
}
