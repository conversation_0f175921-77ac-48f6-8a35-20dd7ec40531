#ifndef ALARMCOMPARE_H
#define ALARMCOMPARE_H

#include <QWidget>
#include <QMap>
#include <QTreeWidgetItem>
#include <QDebug>

class QCPGraph;
class QCPItemTracer;
class QCPItemText;
class QCPItemStraightLine;

class AlarmCompare_Alarm;
class AlarmCompare_AlarmList;
class AlarmCompare_Radar;
class AlarmCompare_File;

class AlarmCompare_TreeItem
{
public:
    enum TreeItemType{
        ERROR = 0,
        FILE,
        RADAR,
        ALARMLIST,
        ALARM
    };

    AlarmCompare_TreeItem( TreeItemType type, AlarmCompare_TreeItem* parent );
    ~AlarmCompare_TreeItem();

    QString text( bool bSrc );

    void buildTree( bool bSrc, QTreeWidget* pTree, QTreeWidgetItem* pItem );
    bool compare( AlarmCompare_File* src, AlarmCompare_File* tar, quint8 offset );
    void rawNo( quint64& rawNoBegin, quint64& rawNoEnd );
    void alarmLevel( quint64 rawNo, quint8& srcLevel, quint8& tarLevel );

private:
    bool compare( AlarmCompare_Radar* src, AlarmCompare_Radar* tar, quint8 offset );
    bool compare( AlarmCompare_AlarmList* src, AlarmCompare_AlarmList* tar, quint8 offset );
    //bool sortAlarm( const AlarmCompare_TreeItem* &item1, const AlarmCompare_TreeItem* &item2 );

public:
    AlarmCompare_TreeItem* mParent{NULL};
    QList< AlarmCompare_TreeItem* > mChilds;

    TreeItemType mType{ ERROR };

    void* mSrcPtr{NULL};
    void* mTarPtr{NULL};
    bool mEqual{false};

    QTreeWidgetItem* mSrcTreeWidgetItem{NULL};
    QTreeWidgetItem* mTarTreeWidgetItem{NULL};
};


//报警段  从某帧开始报警，至某帧结束
class AlarmCompare_Alarm /*: public AlarmCompare_Result*/
{
public:
    AlarmCompare_Alarm( quint8 level, quint64 rawNoBegin );
    //判断是否同一级别报警，且原始点帧ID是否连续（rawNoEnd = mRawNoEnd +1 )
    bool updateRawNoEnd( quint8 level, quint64 rawNoEnd );

    virtual bool compare( AlarmCompare_Alarm* other, quint8 offset );
//    virtual void clearCompareResult();

    quint8 level() { return mLevel; };
    quint64 rawNoBegin() { return mRawNoBegin; };
    quint64 rawNoEnd() { return mRawNoEnd; };

    QString text();

private:
    quint8 mLevel{0}; //报警级别
    quint64 mRawNoBegin{0}; //此次报警的起始 原始点帧ID
    quint64 mRawNoEnd{0}; //此次报警的结束 原始点帧ID

friend class AlarmCompare_TreeItem;
};

//报警段列表
class AlarmCompare_AlarmList /*: public AlarmCompare_Result*/
{
public:
    enum AlarmType
    {
        BSD = 0,
        LCA,
        DOW_F,
        DOW_R,
        RCW,
        RCTA,
        RCTB,
        FCTA,
        FCTB,
        JA,
        AlarmTypeCount  //置于此枚举的最后
    };

public:
    AlarmCompare_AlarmList( AlarmType type );
    void addAlarm( quint8 level, quint64 rawNo );

    void buildTree( QTreeWidgetItem* pParent );

//    bool compare( AlarmCompare_AlarmList* other, quint8 offset, AlarmCompare_TreeItem* treeItem );
//    virtual void clearCompareResult();

    static QString alarmTypeStr(AlarmType);

    QString text();
    quint64 rawNoBegin();
    quint64 rawNoEnd();

    quint8 alarmLevel( quint64 rawNo );


private:
    AlarmType mType{BSD};
    QList<AlarmCompare_Alarm> mData;
    QMap< quint64, quint8 > mRawNoAlarmLevelMap;

friend class AlarmCompare_TreeItem;
};

//雷达报警信息
class AlarmCompare_Radar /*: public AlarmCompare_Result*/
{
public:
    AlarmCompare_Radar( quint8 radarID );
    ~AlarmCompare_Radar();
    void addAlarm( AlarmCompare_AlarmList::AlarmType type, quint8 level, quint64 rawNo );

//    bool compare( AlarmCompare_Radar* other, quint8 offset, AlarmCompare_TreeItem* treeItem );
//    virtual void clearCompareResult();

    void buildTree( QTreeWidgetItem* pParent );

    QString text();

private:
    quint8 mRadarID{0};
    AlarmCompare_AlarmList* mAlarms[AlarmCompare_AlarmList::AlarmType::AlarmTypeCount];
    AlarmCompare_Radar* mOther;

friend class AlarmCompare_TreeItem;
};

//文件
class AlarmCompare_File /*: public AlarmCompare_Result*/
{
public:
    AlarmCompare_File();
    ~AlarmCompare_File();

    void setFileName( const QString& fileName );
    QString fileName() {return mFileName; };

    bool load();
    void buildTree( QTreeWidget* pTree );
    void clear();

//    bool compare( AlarmCompare_File* other, quint8 offset, AlarmCompare_TreeItem* treeItem );
//    virtual void clearCompareResult();

    QString text();

private:
    QString mFileName;
    QMap< quint8, AlarmCompare_Radar* > mData;

friend class AlarmCompare_TreeItem;
};

namespace Ui {
class AlarmCompare;
}
class AlarmCompare : public QWidget
{
    Q_OBJECT

public:
    explicit AlarmCompare(QWidget *parent = nullptr);
    ~AlarmCompare();

    bool compareAlarmFile( const QString& srcFile, const QString& tarFile );

private slots:
    void on_pushButtonSelectSrcFile_clicked();

    void on_pushButtonSelectTarFile_clicked();

    void on_pushButtonLoad_clicked();

    void on_treeWidgetSrc_currentItemChanged(QTreeWidgetItem *current, QTreeWidgetItem *previous);

    void on_treeWidgetTar_currentItemChanged(QTreeWidgetItem *current, QTreeWidgetItem *previous);

    void on_treeWidgetSrc_itemExpanded(QTreeWidgetItem *item);

    void on_treeWidgetTar_itemExpanded(QTreeWidgetItem *item);

    void treeWidgetScrollChanged( int value );

    void on_treeWidgetSrc_itemCollapsed(QTreeWidgetItem *item);

    void on_treeWidgetTar_itemCollapsed(QTreeWidgetItem *item);

    void on_treeWidgetSrc_itemDoubleClicked(QTreeWidgetItem *item, int column);

    void on_treeWidgetTar_itemDoubleClicked(QTreeWidgetItem *item, int column);

    void showInfo(QMouseEvent *e);

private:
    void buildTree();
    bool compare();

    void clearGraph();
    bool addGraphData( AlarmCompare_TreeItem* );
    void addGraphData( quint64 rawNo, quint8 level, bool bSrc );
    void treeWidgetItemDoubleChicked( QTreeWidgetItem *item );

private:
    Ui::AlarmCompare *ui;

    AlarmCompare_File mSrcFile;
    AlarmCompare_File mTarFile;
    bool mFileEqual{false};

    AlarmCompare_TreeItem* mRoot{NULL};

private:
    QCPGraph* mSrcGraph{ NULL };
    QCPGraph* mTarGraph{ NULL };
    QCPItemTracer* mTracer{0};
    QCPItemText* mTracerLabel{0}; //悬浮文本
    QCPItemStraightLine* mLine{NULL};
};

#endif // ALARMCOMPARE_H
