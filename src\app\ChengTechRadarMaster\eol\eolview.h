#ifndef EOLVIEW_H
#define EOLVIEW_H

#include "eolprotocol.h"

#include <QWidget>

namespace Ui {
class EOLView;
}

namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
}

class EOLProtocol;
class BlackBoxSimulatorForm;
class BlackBoxTurnTableForm;
class DebugShellForm;
class ShellTextEdit;

class EOLView : public QWidget
{
    Q_OBJECT

public:
    explicit EOLView( Devices::Can::DeviceManager* deviceManager, QWidget *parent = nullptr );
    ~EOLView();

public slots:
    void canFrameRecv(const Devices::Can::CanFrame &frame);

private slots:
    void showFrame( const Devices::Can::CanFrame &frame );
    void showMsg( const QString& msg, EOL_MSG_TYPE/*quint8*/ type );
    void enableAll( bool bEnable );//启用/禁用视图
    void eolCanFrameSend( const Devices::Can::CanFrame &frame, bool success );


    void on_selModePushButton_clicked();
    void on_rebootPushButton_clicked();

    void on_selFilePushButton_clicked();

    void on_loadFilePushButton_clicked();

    void on_clearMsgPushButton_clicked();

    void on_clearFramePushButton_clicked();

    void on_readTablePushButton_clicked();

signals:
    void eolCanFrameRecv( const Devices::Can::CanFrame &frame );
    void enterMode( quint8 mode );
    void loadTable( const QString& file );
    void readTable( quint8 tableClass, quint8 pro_id, const QString& path );

private:
    Ui::EOLView *ui;
    Devices::Can::DeviceManager *mDeviceManager;
    EOLProtocol* mEOLProtocol;
    BlackBoxSimulatorForm* mBlackBoxSimulator; //暗箱模拟器
    BlackBoxTurnTableForm* mBlackBoxTurnTable; //暗箱转台
    DebugShellForm* mDebugShell;
    //ShellTextEdit* mShellTextEdit;
};

#endif // EOLVIEW_H
