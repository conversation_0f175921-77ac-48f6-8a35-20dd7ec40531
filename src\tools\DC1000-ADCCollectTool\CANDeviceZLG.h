﻿#pragma once


#include <stdint.h>
#include <string>
#include <memory>
#include <vector>

#include "ZLG_CAN/include/zlgcan.h"

#include "CANFrame.h"
#include "SafeQueue.h"

#define ZLG_DEVICE_CHANNEL_MAX 2
#define CAN_DEVICE_MAX_RECEIVE_FRAME 100
#define CAN_DEVICE_WITE_TIME 50

class  CANDeviceZLG
{
public:
    CANDeviceZLG(SafeQueue<CANFrame> *safeQueue);

    bool open(int index, int baudRate, int dataRate, bool fd);
    void close();

    bool isOpened() const { return mOpened; }
    bool isClosed() const { return mClosed; }
    const std::string &errorString() const { return mErrorString; }
    void receive();

    bool sendFrame(CANFrame &frame);

protected:
    bool openDevice();
    bool closeDevice();
    int receiveFrame(int channel);
    int sendFrame(CANFrame *frame, int len);

private:
    bool openCan(int channel);
    bool openCanFD(int channel);
    bool openChannel(int channel);
    bool closeChannel(int channel);
    int receiveCan(int channel);
    int receiveCanFD(int channel);
    bool transmitFrameCan(CANFrame *frame);
    bool transmitFrameCanFD(CANFrame *frame);

    friend void deviceReceive(CANDeviceZLG *pCANDevice);

    DEVICE_HANDLE mDeviceHandle{ 0 };
    CHANNEL_HANDLE mChannelHandle[ZLG_DEVICE_CHANNEL_MAX]{ 0, 0 };
    IProperty* mProperty[ZLG_DEVICE_CHANNEL_MAX]{ 0, 0 };


    ZCAN_Receive_Data mReceiveData[CAN_DEVICE_MAX_RECEIVE_FRAME];
    ZCAN_ReceiveFD_Data mReceiveDataFD[CAN_DEVICE_MAX_RECEIVE_FRAME];

    int mDeviceIndex{0};
    int mBaudRate{500000};
    int mDataRate{2000000};
    bool mCANFD{false};
    bool mOpened{false};
    bool mClosed{true};
    std::string mDeviceName;
    std::string mErrorString;
    std::string mSN;

    SafeQueue<CANFrame> *mSafeQueue;
};

