﻿#include "videoplayerform.h"
#include "ui_videoplayerform.h"

#include <QFileDialog>
#include <QDateTime>
#include <QTimer>
#include <QTextCodec>
#include <QListWidgetItem>
#include <QMessageBox>

VideoPlayerForm::VideoPlayerForm(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::VideoPlayerForm)
{
    ui->setupUi(this);
    mMediaPlayer.setVideoOutput( ui->videoWidget );
    connect( &mMediaPlayer, &QMediaPlayer::positionChanged, this, &VideoPlayerForm::positionChanged );

    connect( &mMediaPlayer, &QMediaPlayer::durationChanged,
          [=](quint64 duration){
            ui->horizontalSlider->setRange( 0, duration );
            positionChanged( 0 );
//            mBeginTimeMs = mEndTimeMs - duration;
//            ui->dateTimeEditBegin->setDateTime( QDateTime::fromMSecsSinceEpoch( mBeginTimeMs ) );
//            initVideoTime();
        }
    );

    connect( ui->horizontalSlider, &QSlider::valueChanged, [=](quint64 value){
            if( mCurrentPos != value ){
                mMediaPlayer.setPosition( value);
            }
        }
    );

    ui->dateTimeEditBegin->setDisplayFormat("yyyy-MM-dd HH:mm:ss:zzz");
    ui->dateTimeEditEnd->setDisplayFormat("yyyy-MM-dd HH:mm:ss:zzz");
}

VideoPlayerForm::~VideoPlayerForm()
{
    delete ui;
}

void VideoPlayerForm::skipToVideoFrame(quint64 ms)
{
    if( ms > mEndTimeMs || ms < mBeginTimeMs ){
        QMessageBox::warning( this, QString::fromLocal8Bit("错误"),
                              QString::fromLocal8Bit("文件起止时间为%1至%2，而跳转时间为%3")
                              .arg( QDateTime::fromMSecsSinceEpoch(mBeginTimeMs).toString( "yyyy-MM-dd hh:mm:ss:zzz" ) )
                              .arg( QDateTime::fromMSecsSinceEpoch(mEndTimeMs).toString( "yyyy-MM-dd hh:mm:ss:zzz" ) )
                              .arg( QDateTime::fromMSecsSinceEpoch(ms).toString( "yyyy-MM-dd hh:mm:ss:zzz" ) ) );
        return;
    }

    //mMediaPlayer.setPosition( ms - mBeginTimeMs );
    mMediaPlayer.setPosition( timestampToPos( ms ) );
}

void VideoPlayerForm::on_pushButtonSelectFile_clicked()
{
    QString fileName = QFileDialog::getOpenFileName(
            this,
            tr("open a file."),
            "./",
            tr("video time(*.vt)"));
    if ( !fileName.isEmpty()) {
        ui->lineEditFileName->setText( fileName );
        initVideoInfo( fileName );
//        initBookMark( fileName );
//        initVideoInfo( fileName );
    }
}

void VideoPlayerForm::on_pushButtonPlay_clicked()
{
    mMediaPlayer.play();
}

void VideoPlayerForm::on_pushButtonPause_clicked()
{
    mMediaPlayer.pause();
}

void VideoPlayerForm::on_pushButtonStop_clicked()
{
    mMediaPlayer.stop();
}

void VideoPlayerForm::positionChanged(qint64 position)
{
    if( mCurrentPos == position ){
        return;
    }
    mCurrentPos = position;
    ui->horizontalSlider->setValue( position );
    ui->labelProccess->setText( QString( "%1/%2" ).arg( timeStr( position ) )
                                .arg( timeStr( mMediaPlayer.duration() ) ) );

    updateCurrentTime();
}

QString VideoPlayerForm::timeStr(quint64 position)
{
    int h,m,s,ms;
    ms = position % 1000;
    position /= 1000;  //获得的时间是以毫秒为单位的
    h = position/3600;
    m = (position-h*3600)/60;
    s = position-h*3600-m*60;

    return QString("%1:%2:%3:%4").arg(h).arg(m).arg(s).arg(ms);
}

bool VideoPlayerForm::initBookMark(const QString &fileName)
{
    qDebug() << __FUNCTION__ << __LINE__ << fileName;

    ui->listWidget->clear();

    QFile bookMark( fileName );
    if( !bookMark.open( QIODevice::ReadOnly ) ){
        ui->listWidget->addItem( QString::fromLocal8Bit("打开%1失败").arg(bookMark.fileName()) );
        return false;
    }

    QTextStream stream(&bookMark);
    stream.setCodec("UTF-16LE");
    stream.setGenerateByteOrderMark(true);
    while ( !stream.atEnd() ) {
        QString lineStr = stream.readLine();
        QStringList tokens = lineStr.split( '=' );
        if( tokens.size() < 2 ){
            continue;
        }
        quint32 id = tokens[0].toUInt();
        QStringList subTokens = tokens[1].split( '*' );
        if( subTokens.size() < 3 ){
            continue;
        }
        quint64 timeMS = subTokens[0].toUInt();
        QListWidgetItem* pItem = new QListWidgetItem( ui->listWidget );
        pItem->setText( subTokens[1] );
        pItem->setData( Qt::UserRole, timeMS );
    }
    bookMark.close();
    return true;
}

bool VideoPlayerForm::initVideoInfo(const QString &fileName)
{
    QFileInfo info( fileName );
    mPath = info.path();
    mFileName = info.baseName();
    qDebug() << __FUNCTION__ << __LINE__ << mPath << mFileName;

    QFile vtFile( fileName );
    if( !vtFile.open( QIODevice::ReadOnly ) ){
        qDebug() << __FUNCTION__ << __LINE__ << fileName;
        return false;
    }

    QString videoFileName = vtFile.readLine();
//    QString beginTime = vtFile.readLine();
//    QString endTime = vtFile.readLine();
    videoFileName = videoFileName.trimmed();
//    beginTime = beginTime.trimmed();
//    endTime = endTime.trimmed();

    mFrameTimestamp.clear();
    mTimestampFrame.clear();
    while ( !vtFile.atEnd() ) {
        QString line = vtFile.readLine();
        QStringList tokens = line.split( " " );
        if( tokens.size() < 3 ){
            continue;
        }
        quint64 index = tokens[0].toULongLong();
        quint64 timestamp = tokens[1].toULongLong();
        mFrameTimestamp[index] = timestamp;
        mTimestampFrame[timestamp] = index;
        mTimestampList.push_back( timestamp );
    }
    vtFile.close();


    mBeginTimeMs = mTimestampList.first();
    mEndTimeMs = mTimestampList.last();
//    qDebug() << __FUNCTION__ << __LINE__ << beginTime << endTime;
//    mBeginTimeMs = QDateTime::fromString( beginTime, "yyyy-MM-dd hh:mm:ss:zzz").toMSecsSinceEpoch();
//    mEndTimeMs = QDateTime::fromString( endTime, "yyyy-MM-dd hh:mm:ss:zzz").toMSecsSinceEpoch();

    ui->dateTimeEditBegin->setDateTime( QDateTime::fromMSecsSinceEpoch( mBeginTimeMs ) );
    ui->dateTimeEditEnd->setDateTime( QDateTime::fromMSecsSinceEpoch( mEndTimeMs ) );

    initBookMark( mPath + "/" + mFileName + ".pbf" );

    mMediaPlayer.setMedia(QUrl::fromLocalFile( mPath + "/" + videoFileName ));
    qDebug() << __FUNCTION__ << __LINE__ << mPath + "/" + videoFileName;
    //先播放一会，缓冲
    mMediaPlayer.play();
    QEventLoop loop;
    QTimer timer;
    timer.setSingleShot( true );
    connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
    //timer.start( 60 );
    timer.start( 1000 );
    loop.exec();
    timer.stop();
    mMediaPlayer.pause();
    mMediaPlayer.setPosition(0);


    return true;
}

//void VideoPlayerForm::initVideoTime()
//{
//    if( mMediaPlayer.duration() != 0 ){
////        mEndTimeMs = mBeginTimeMs + mMediaPlayer.duration();
//    }
//    updateCurrentTime();
//}

void VideoPlayerForm::updateCurrentTime()
{
    if( mMediaPlayer.duration() != 0 ){
        //quint64 curMs = ( (double)( mEndTimeMs - mBeginTimeMs ) ) / mMediaPlayer.duration() * mMediaPlayer.position() + mBeginTimeMs;
        quint64 curMs = posToTimestamp( mMediaPlayer.position() );
        ui->labelCurrentTime->setText( QString::fromLocal8Bit("当前时间：") +
                                       QDateTime::fromMSecsSinceEpoch(curMs).toString( "yyyy-MM-dd hh:mm:ss:zzz" ) );
    }
}

quint64 VideoPlayerForm::timestampToPos(quint64 timestamp)
{
    QList< quint64 >::iterator it = std::lower_bound( mTimestampList.begin(), mTimestampList.end(), timestamp/*, std::greater<quint64>()*/ );
    qDebug() << __FUNCTION__ << __LINE__
             << QDateTime::fromMSecsSinceEpoch(timestamp).toString( "yyyy-MM-dd hh:mm:ss:zzz" );
    if( it == mTimestampList.end() ){
//        qDebug() << __FUNCTION__ << __LINE__;
        return mCurrentPos;
    }else{
//        qDebug() << __FUNCTION__ << __LINE__
//                 << QDateTime::fromMSecsSinceEpoch(*it).toString( "yyyy-MM-dd hh:mm:ss:zzz" );
        return (double)mTimestampFrame.value( *it ) / mTimestampFrame.size() * mMediaPlayer.duration();
    }

    //timestamp
//    return ( (double)( timestamp - mBeginTimeMs ) ) / ( mEndTimeMs - mBeginTimeMs ) * mMediaPlayer.duration();
}

quint64 VideoPlayerForm::posToTimestamp(quint64 pos)
{
//    qDebug() << __FUNCTION__ << __LINE__ << "current frameid="
//             <<   (double) mMediaPlayer.position() / mMediaPlayer.duration() * 995;

    quint64 index = (double)pos / mMediaPlayer.duration() * mFrameTimestamp.size();
    return mFrameTimestamp.value( index );

    //return ( (double)( mEndTimeMs - mBeginTimeMs ) ) / mMediaPlayer.duration() * pos + mBeginTimeMs;
}

void VideoPlayerForm::on_pushButtonPreFrame_clicked()
{
    mMediaPlayer.setPosition( mCurrentPos - mNextStep );
}

void VideoPlayerForm::on_pushButtonNextFrame_clicked()
{
    mMediaPlayer.setPosition( mCurrentPos + mNextStep );
}

void VideoPlayerForm::on_pushButtonPreFrame2_clicked()
{
    mMediaPlayer.setPosition( mCurrentPos - mNextStep*5 );
}

void VideoPlayerForm::on_pushButtonNextFrame2_clicked()
{
    mMediaPlayer.setPosition( mCurrentPos + mNextStep*5 );
}

void VideoPlayerForm::on_pushButtonPreFrame3_clicked()
{
    mMediaPlayer.setPosition( mCurrentPos - mNextStep*20 );
}

void VideoPlayerForm::on_pushButtonNextFrame3_clicked()
{
    mMediaPlayer.setPosition( mCurrentPos + mNextStep*20 );
}

void VideoPlayerForm::on_listWidget_itemDoubleClicked(QListWidgetItem *item)
{
    quint64 timeMs = item->data( Qt::UserRole ).toULongLong();
    mMediaPlayer.setPosition( timeMs );
}

void VideoPlayerForm::on_pushButtonSkipBLF_clicked()
{
    //emit skipToTimestamp( mBeginTimeMs + mCurrentPos );
    emit skipToTimestamp( posToTimestamp( mCurrentPos ) );
}

void VideoPlayerForm::on_dateTimeEditBegin_dateTimeChanged(const QDateTime &dateTime)
{
    mBeginTimeMs = dateTime.toMSecsSinceEpoch();
//    initVideoTime();
}

void VideoPlayerForm::on_dateTimeEditEnd_dateTimeChanged(const QDateTime &dateTime)
{
    mEndTimeMs = dateTime.toMSecsSinceEpoch();
}
