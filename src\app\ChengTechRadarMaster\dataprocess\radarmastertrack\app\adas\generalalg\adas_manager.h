﻿/**
 * @file adas_manager.h
 * @brief adas模块内部头文件
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2022-09-30
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-09-30 <td>1.0     <td>shaowei     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef _ADAS_MANAGER_H_
#define _ADAS_MANAGER_H_

#ifdef ALPSPRO_ADAS
#include "adas/customizedrequirements/adas.h"
#include "rdp/track/data_process/rdp_track_struct.h"
#include "vdy/vdy_types.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/common/adas_common.h"
#include "radar_cfg.h"
#elif defined (PC_DBG_FW)
#include "app/adas/customizedrequirements/adas.h"
#include "alg/track/rdp_track_struct.h"
#include "app/vehicle/vdy/vdy_types.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/common/adas_common.h"
#include <stdbool.h>
#else
#include "app/adas/customizedrequirements/adas.h"
#include "app/rdp/rdp_track_struct.h"
#include "app/vdy/vdy_types.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/common/adas_common.h"
#endif

#define BSD_ALM_FLAG_MARK 0x01
#define LCA_ALM_FLAG_MARK 0X02
#define DOW_ALM_FLAG_MARK 0X04
#define RCW_ALM_FLAG_MARK 0x08
#define RCTA_ALM_FLAG_MARK 0x10
#define RCTB_ALM_FLAG_MARK 0x20
#define FCTA_ALM_FLAG_MARK 0x40
#define FCTB_ALM_FLAG_MARK 0x80

/**
 * @brief 比较两个数大小返回最大值
 *      判断两个数的类型是否相同，如果不相同，会抛出一个警告。因为a和b的类型不一样，
 *      其指针类型也会不一样，两个不一样的指针类型进行比较操作，会抛出一个编译警告。
 */
#ifndef PC_DBG_FW // VS不支持GNU扩展
// 下位机
/**
 * @brief 比较两个数大小返回最大值
 *      判断两个数的类型是否相同，如果不相同，会抛出一个警告。因为a和b的类型不一样，
 *      其指针类型也会不一样，两个不一样的指针类型进行比较操作，会抛出一个编译警告。
 */
#ifndef MAX
#define MAX(a, b) ({    \
    typeof(a) _a = (a); \
    typeof(b) _b = (b); \
    (void)(&_a == &_b); \
    (_a >= _b) ? _a : _b; })
#endif
#ifndef MIN
#define MIN(a, b) ({    \
    typeof(a) _a = (a); \
    typeof(b) _b = (b); \
    (void)(&_a == &_b); \
    (_a <= _b) ? _a : _b; })
#endif
#else
// 上位机
#ifndef MAX
#define MAX(a, b)       ((a) > (b) ? (a) : (b))
#endif
#ifndef MIN
#define MIN(a, b)       ((a) < (b) ? (a) : (b))
#endif
#endif

#define TriangleH 2 //盲区三角形的高

#define FLOAT_EPS           (1e-6f)
#define FLOAT_NEG_EPS       (-1e-6f)

#define MAX_UCHAR           (255U)      //无符号八位整型最大值
#define MAX_UINT16          (65535U)    //无符号八位整型最大值
#define MAX_FLOAT           (3.402823466e+38F)  // FLOAT类型最大值

/* 角度转弧度DEG2RAD = pi/180 */
#define DEG2RAD 0.017453292519943295f

/* 弧度转角度：RAD2DEG = 180/pi */
#define RAD2DEG 57.295779513082323f

typedef enum
{
    ALARM_NO_ACTIVE = 0U,
    ALARM_ACTIVE_FCTB = 1U,
    ALARM_ACTIVE_BSD = 2U,
    ALARM_ACTIVE_LCA = 4U,
    ALARM_ACTIVE_RCTA = 8U,
    ALARM_ACTIVE_DOW = 16U,
    ALARM_ACTIVE_RCW = 32U,
    ALARM_ACTIVE_FCTA = 64U,
    ALARM_ACTIVE_RCTB = 128U,
    ALARM_ACTIVE_FCTA_B = 256U,
    ALARM_ACTIVE_FCTB_B = 512U
} OBJ_ALARM_TYPE_ENUM;

typedef enum
{
    ADAS_TYPE_BSD = 0U,
    ADAS_TYPE_LCA = 1U,
    ADAS_TYPE_RCW = 2U,
    ADAS_TYPE_DOW = 3U,
    ADAS_TYPE_RCTA = 4U,
    ADAS_TYPE_FCTA = 5U,
    ADAS_TYPE_RCTB = 6U,
    ADAS_TYPE_FCTB = 7U,
    ADAS_TYPE_MAX = 8U
} OBJ_ADAS_TYPE_ENUM;

typedef enum
{
    OBJ_STATUS_NULL = 0,
    OBJ_STATUS_BUILDING,
    OBJ_STATUS_VALID,
} OBJ_STATUS_ENUM;

typedef enum
{
    ADAS_OBJ_WIDE = 2U,         //较宽的目标，如大型货车或大型客车。
    ADAS_OBJ_MID_COMPACT = 1U,  //宽度适中的目标，如普通汽车。
    ADAS_OBJ_MID_SMALL= 0U      //较窄的目标，通常是车道上的小车或者自行车等。
} OBJ_CLASS_ENUM;   //目标类别枚举

typedef enum
{
    ADAS_OBJ_UNKNOW = 0U,           // 未知
    ADAS_OBJ_PEDESTRAN = 1U,        // 行人
    ADAS_OBJ_MOTORCYCLE = 2U,       // 二轮车
    ADAS_OBJ_CAR    = 3U,           // 四轮车
    ADAS_OBJ_TRUCK  = 4U,           // 大卡车
}OBJ_TYPE_ENUM;     // 目标类型     // 使用的rdp传入的.  类型需要和rdp中的对应.

typedef enum
{
    ADAS_OBJ_NONE_LOCKED = 0U,
    ADAS_OBJ_HALF_LOCKED = 1U,
    ADAS_OBJ_FULLY_LOCKED = 2U
} OBJ_LOCKSTATUS_ENUM;

typedef enum
{
    ADAS_BSD_CHECKNULL = 0U,
    ADAS_BSD_CHECKIN = 1U,
    ADAS_BSD_CHECKNOUT = 2U,
    ADAS_BSD_CHECKPREOUT = 3U
} OBJ_CHECKSTATUS_ENUM;

typedef struct
{
    float stored_earliest_x;
    float stored_earliest_y; 
    uint8_t notDirectlyBehindFlag; // 允许判断是否是正后方目标，0是允许，1是不允许，默认是0
    uint8_t directlyBehind;        // 判断结果是否是直行过来的目标，0不是，1是
    uint8_t TrkObjDowShelterScene;  // DOW法规遮挡场景类
    uint8_t TrkObjdowguardCrossCnt; // 此类目标放宽横向碰撞
    uint8_t TrkObjdowguardblocksence;

    uint8_t angleIncreasingCnt;
    uint8_t angleDecreasingCnt;
    uint8_t islinenearheadingresult;    // DOW的航向角是否是拟合出来的.
} DataAnalysisStats_T;

typedef struct
{
    float filter_x[ADAS_HISTORY_NUM];
    float filter_y[ADAS_HISTORY_NUM];
    float filter_vx[ADAS_HISTORY_NUM];
    float filter_vy[ADAS_HISTORY_NUM];
    // float normalize_x[ADAS_HISTORY_NUM];
    // float normalize_y[ADAS_HISTORY_NUM];
    // float normalize_vx[ADAS_HISTORY_NUM];
    // float normalize_vy[ADAS_HISTORY_NUM];
} DataStored_Info_T;
typedef struct
{ 
    float fctab_w;
    float fctab_b;
    float rctab_w;
    float rctab_b;
    float dow_w;
    float dow_b;
    uint16_t dow_HitCnt;            // 计算hit的总次数,后续可能删掉
    uint16_t dow_HitValidCnt;       // 40m内hit的次数
    uint16_t dow_HitValidNMCnt;     // 12m内hit的次数
    uint8_t fctab_FitAvailable;     // 0 - 区域外，1 - 区域内；
    uint8_t fctab_FitAvailableCnt;  // 拟合有效次数，
    uint8_t rctab_FitAvailable;
    uint8_t rctab_FitAvailableCnt;
    uint8_t dow_FitAvailable;
    uint8_t dow_FitAvailableCnt;
    uint8_t dow_HitFlag;
} LinearRegressionFitting_Info_T;
typedef struct
{
    float range;                  // 径向距离，m
    float maxrange;               // 最大径向距离，m
    float angle;                  // 方位角，deg
    float x;                      // x轴距离，m
    float y;                      // y轴距离，m
    float lockx;                  // 锁定x轴距离，m
    float locky;                  // 锁定y轴距离，m
    float lockCrossVx;            // 锁定横穿目标的横向速度, 此变量目前仅用于rcta横穿目标在车尾时的横向速度估计
    float locktimedifference;     // 与锁定坐标时的时间差：s
    float lockstatus;             // 锁定状态：0未锁定，1:第一个点已锁定，2：第二点已锁定
    float lock_ttcy_thresold1;    // 锁定第一点ttcy阈值
    float lock_ttcy_thresold2;    // 锁定第二点ttcy阈值
    float startX;                 // 起批的x位置
    float startY;                 // 起批的y位置
    float startCandiX;            // 起批的x位置 candi航迹时的X
    float startCandiY;            // 起批的y位置 candi航迹时的Y
    float startTTCY;              // 起批的ttcy
    float startAngle;             // 起批的角度
    float boxCenterX;             // x-coordinate of the center point of the tracking box, unit:m
    float boxCenterY;             // y-coordinate of the center point of the tracking box, unit:m
    float boxLength;              // length of the tracking box, unit: m
    float boxWidth;               // width of the tracking box, unit: m
    float nreTargetX;              // Nearest 横向最近目标原始点值（相对于自车），x和y对应的不一定是同一个原始点
    float nreTargetY;              // Nearest 纵向最近目标原始点值（相对于自车）
    // float minDistanceSquared;     // 用于计算欧氏距离平方的最小值
    float headingAngleCTAB;       // 以雷达Y轴为0°，向X轴正方向增加，适用于FCTAB和RCTAB ,单位：°
    float headingAngleRCWDOW;     // 计算有问题，暂时不能用，以本车纵轴向车头向前为0°，左右对称，角度逐渐扩大，均为正值，适用于DOW和RCW ,单位：°
    float headingAnglerealRCWDOW; // 实时航向角
    float headingdowwarningangle; // 首次触发dow时刻的航向角. 
    float avgheadingX;            // x轴距离的均值,20帧
    float avgheadingY;            // y轴距离的均值
    float avgheadingVx;           // x轴速度的均值
    float avgheadingVy;           // y轴速度的均值
    float avgX;                   // x轴距离的均值，5帧
    float avgY;                   // y轴距离的均值
    float avgVx;                  // vx的均值，5帧
    float avgVxFctb;              // vx的均值，5帧,只用在FCTB的5帧均值速度
    float avgVy;                  // vy的均值，5帧
    float stored_last_x[ADAS_HISTORY_NUM];
    float stored_last_y[ADAS_HISTORY_NUM];
    float stored_last_groundy[ADAS_HISTORY_NUM];      // 存储抵消自车运动之后的y值, 用于航向角的线性拟合 
    float stored_last_vx[ADAS_HISTORY_NUM];
    float stored_last_vy[ADAS_HISTORY_NUM];
    float stored_last_headingAngle[ADAS_HISTORY_NUM];
    DataStored_Info_T data_stored; // 数据存储 
    float moreFrameAvgAngle;       // ADAS_HISTORY_NUM数量的 航向角均值
    float avgheadingAngle;         // ADAS_HISTORY_HEADINGANGLE_NUM数量的 航向角均值
    float headingAngleCTAB_L;      // 航向角均值滑窗中最小的航向角
    float sumhistoryheadingAngle;  // 历史以来航向总和
    float avghistoryheadingAngle;  // 历史以来的均值航向
    float sumhistoryVx;            // 历史以来横向速度总和
    float avghistoryVx;            // 历史以来平均横向速度
    float stored_last_headingAngleRCWDOW[ADAS_HISTORY_HEADINGANGLE_NUM];
    float v;            // 径向相对速度，来向为正，m/s
    float absolutevabs; // 目标绝对速度的大小，由目标的绝对vx和绝对vy求得，kph
    float vx;           // x轴相对速度，来向为正，m/s
    float ax;           // x轴相对加速度，来向为正，m/s
    float vy;           // y轴相对速度，来向为正，m/s
    float ay;           // x轴相对加速度，来向为正，m/s
    float absolutevx;   // x轴绝对速度，来向为正，m/s
    float absolutevy;   // y轴绝对速度，来向为正，m/s
    float vxByCarSpeed; // x轴相对(对地)速度，比自车快为正，m/s，利用自车车速来计算
    float vyByCarSpeed; // y轴相对(对地)速度，比自车快为正，m/s，利用自车车速来计算
    float rcDis;        // 到转向圆心的距离
    float cncapmaxX;    // cncap24专属
    float maxX;         // 目标生命周期内的最远x轴距离
    float maxY;         // 目标生命周期内的最远y轴距离
    float boxDot1X;     // 航迹框的四个点，这四个点在不同雷达中对应的绝对位置不一样，相对位置是一样的
    //float boxDot1Y;
    float boxDot2X;
    //float boxDot2Y;
    // float boxDot3X;
    // float boxDot3Y;
    // float boxDot4X;
    // float boxDot4Y;
    float avgoverlapx;         // 计算正后方目标X在一段范围内的和
    float coverRate;           // 覆盖率
    float rangestartdiffvalue; // 目标航迹绝对位移差值.
    float rangemaxdiffvalue;   // 目标最大位移差.
    float maxbsdx;
    float minbsdx;
    float TrkObjRcwOverLapx;    // RCW X重叠率
    float TrkObjDowOverLapx;    // DOW X重叠率
	float dowX_AtTtmX;             // 在ttmX时刻的x坐标
    float rctabX_AtTtmX;             // 在ttmX时刻的x坐标

    uint32_t alarmType;           // 当前帧目标报警类型，运算前会初始化，所以具体算法函数内只需要置位，不需要做清除操作
    uint32_t lastAlarmType;       // 目标上一次报警类型，倒车工况下，功能会同时发生，如RCTA和RCTB
    uint32_t preAlarmType;        // 可能会报警的类型
    uint32_t avgheadingCnt;       // 均值航向计数  用于统计历史以来的均值航向
    uint32_t avgVxCnt;            // 横向速度均值  用于统计历史以来的横向速度

    uint16_t TrkObjOverLapxCnt;    // RCW_DOW点云重叠率累计计算帧数 
    uint16_t attrFlag;            // 目标属性
    uint16_t lifeCycle;        // 目标点生命周期
    uint16_t fctb_follow_cnt;     // FCTB跟随计数, 自车运动时,目标在一定范围内跟随一定帧数, 此目标不做制动处理.
    uint16_t fctb_objrunning_cnt; // FCTB自车运动时目标计数, 自车运动时目标运动超过一定时间  不制动
    uint16_t TrkObjVr0_hit;       //  连续被径向速度为零点关联计数。动态目标数值越大，越可能是假点 
    uint16_t pointTrackingFrameCount;   //-0.5<startY<0的点在-0.5<Y<0的情况进行累计记数达到600帧及以上，可能是假点，表示出来， 但Y到达1m后取消标识，有该表示的跟踪点不报警 false，非假点，true 假点 

    uint8_t fctbTrigCountdown;    // FCTB触发倒计时 倒计时内未触发  则不再触发
    uint8_t prefctbAlarmCnt;      // 可能FCTB报警类型已发生的帧数
    uint8_t prefctbplanBAlarmCnt; // 可能FCTB报警类型已发生的帧数
    int8_t startAlarmDly;         // 进入报警延时
    int8_t startrcwAlarmDly;   //进入报警延时
    int8_t startBsdAlarmDly;      // 进入报警延时
    int8_t startLcaAlarmDly;      // 进入报警延时
    int8_t startDowAlarmDly;      // 进入报警延时
    uint8_t overAlarmDly;         // 退出报警延时
    uint8_t overfctaAlarmDly;     // 退出报警延时
    uint8_t overBsdAlarmDly;      // 退出报警延时
    uint8_t overLcaAlarmDly;      // 退出报警延时
    uint8_t overDowAlarmDly;      // 功能退出报警延迟变量分开使用
    uint8_t overRctaAlarmDly;
    uint8_t overRctaBehingCnt; // RCTA在正后方的报警计数
    uint8_t alarmDlyThr;       // 报警延时阈值：报警前可用于进入报警延时阈值，退出报警前可用于退出报警延时阈值
    uint8_t alarmPostFlag;     // 报警后期标记，用于处理报警早退问题，0：无报警或正在报警(进入报警时需清0)，1：报警后期
    uint8_t alarmCnt;          // 在报警区域内连续满足报警条件的次数，为正后方报警维持做逻辑使用
    uint8_t alarmCoastedCnt;   // RCTA工况下，在车辆正后方滑行报警的次数
    uint8_t outRoadLineCnt;    /*目标点在边线之外的计数，用于排除鬼点*/
    uint8_t overtake_cnt;      /*bsd超车抑制计数*/
    uint8_t TrkObjMissCnt;     // 预测丢失的帧数
    uint8_t TrkObjHitCnt;
    uint8_t TrkObjReliability; // 目标的置信度
    uint8_t id;
    uint8_t IsCrossObj;           // 是否具备横穿趋势 FCTB用.对横穿要求相对更严格
    uint8_t LastCrossObjCnt;      // 对于历史轨迹符合横穿的目标, 到车头正前方时, 保持历史横穿趋势一定帧数。车对车场景确保制动
    uint8_t isRearVehicleMarked;  // 后方来车标记，现在(20230818)只在bsd中应用
    uint8_t targetclass;          // 目标类别   此变量先不动   避免影响到其他判断
    uint8_t objType;               // 目标类型, 使用rdp传入.
    uint8_t IsRctabNearObj;       // RCTB判断是否是逼近的目标. 前后角的逼近策略分开处理. 可能会使用不同的参数.
    uint8_t isApproachingX;       // 检查目标在X方向上是否整体在靠近, 1靠近， 0不满足靠近
    uint8_t LastRctabNearObjCnt;  // 对于历史轨迹符合横穿的目标, 到车头正前方时, 保持历史横穿趋势一定帧数
    uint8_t IsFctabNearObj;       // FCTA判断是否是逼近目标, 前后角逼近策略分开处理. 可能会使用不同的参数
    uint8_t LastFctabNearObjCnt;  // 对于历史轨迹符合横穿的目标, 到车头正前方时, 保持历史横穿趋势一定帧数。车对车场景确保制动
    uint8_t DirBehindCnt;         // 正后方来车计数, 用于DOW RCW的筛选
    uint8_t dowpredictedcnt;      // DOW碰撞预测满足帧数
    uint8_t cncapBsdcarCheckIn;   // 目标是否符合cncap24切入目标 汽车
    uint8_t cncapBsdmotoCheckIn;  // 目标是否符合cncap24切入目标 摩托车
    uint8_t bsd_checkinoutflag;   // 切入标记, 使用Vx判断可能会有异常
    uint8_t objFakeDOt;           // 目标是否是假点（maybe fake dot） true 假点，false 非假点
    uint8_t objFakeDOt_TimestampCnt;    //  大于3m且记数达到10帧及以上 
    uint8_t isTruePoint;    // 1,真实点，0，假点
    OBJ_STATUS_ENUM status;       // 目标是否可用
    LinearRegressionFitting_Info_T lrFitting;   // 计算线性回归是否有效 0-区域外，1-区域内;
    // float dowHistoryK[ADAS_HISTORY_HEADINGANGLE_NUM];            // 此变量未使用, 暂时屏蔽 DOW历史k值跳帧存储   0-区域外，1-区域内;
    DataAnalysisStats_T analysisStats;  //  分析统计类
} OBJ_NODE_STRUCT;

typedef union stVehicleTurnStatus
{
    uint8_t byte;
    struct
    {
        uint8_t res : 6;        //预留
        uint8_t last_turn : 1;  //之前的转弯状态，is_turning = 1,且没有超时，则此信号值为1
        uint8_t is_turning : 1; //是否正在转弯，转弯半径大于256m则认为在转弯
    } bit;
} VehTurnSt_t; //车辆转弯状态

typedef struct tagVehicleInfo
{
    VehTurnSt_t turn_state;         //转弯状态
    uint8_t after_turn_timer;       //转弯后的计时
} TVehicleInfo;

extern TVehicleInfo gVehicleInfo;


typedef struct
{
    uint16_t timeToSendRequest; //发送制动指令的时间，要求1s后检查制动系统状态
    uint16_t timeToLastRctbTrigger; //上一次触发rctb的时间
    uint16_t timeToEspHoldState; //上一次触发rctb的时间
    uint16_t startBrkTime;
} rctbInfo_t;

typedef struct
{
    float avgTurnRadiusRight;                            /* 本车转弯半径均值 */
    float avgTurnRadiusLeft;                             /* 本车转弯半径均值 */
    float stored_last_TurnRadiusRight[ADAS_HISTORY_NUM]; /* 本车的右侧转弯半径 */
    float stored_last_TurnRadiusLeft[ADAS_HISTORY_NUM];  /* 本车的左侧转弯半径 */
    
    // ENCAP 2023 CPTA场景
    bool CPTARF_Scene;       // 当前主要是CPTA的右拐, 1识别到此场景，0 未识别到此场景
    uint8_t CPTARF_ScenarioCnt; // CPTA场景，当前主要是CPTA的右拐

    // ENCAP 2023 CCCscp场景
    bool CCCscp8_9_Scene;          // 当前主要是CCCscp 8-9 目标车从左侧高速靠近，且纵向距离为2.9m
    uint8_t CCCscp8_9_ScenarioCnt; // CCCscp场景，当前主要是CCCscp 8-9 目标车从左侧高速靠近，且纵向距离为2.9m

    // 识别快速转弯场景
    bool turnRadiusRapidChange_Scene;                      /* 表示转弯半径在极速变化的标志 */
    // FCTAB场景宽松检测，一定时间内条件放宽
    bool fctab_LooseDetectXs_Scene; 
} ADAS_Regulation_Scene_T;
typedef struct
{
    TVehicleInfo *pVehicleInfo;                           /*本车车辆信息*/
    OBJ_NODE_STRUCT *pobjPath;                            /*报警逻辑使用的跟踪点数组*/
    float BSDVelSpeedVal;                                 /*本车的速度信息*/
    float centerx;                                        /*本车的转弯半径*/
    float filtercenterx;                                  /* 滤波后的转弯半径 */
    float RoadLine;                                       /*边线距离*/
    float egoVelLongitudinal;                             // 本车对地纵向速度，带符号，车辆坐标系向前为x正轴，向前为正，向后为负
    float egoVelLateral;                                  // 本车对地横向速度，带符号，车辆坐标系向左为y正轴，向左为正，向右为负
    float vehiclevy;                                      // 本车纵向速度
    float vehiclevx;                                      // 本车横向速度
    float tracktime;                                      // 当前帧周期：s
    float rcwntervaltime;                                 // RCW报警间隔
    float runingtime;                                     // 车辆从静止转为运动后的累计时间
    float historyFramerate[ADAS_HISTORY_NUM];             /*历史帧率*/
    float vehiclemove[ADAS_HISTORY_NUM];                  /*自车位移*/
    float dowguessheading[ADAS_HISTORY_HEADINGANGLE_NUM]; // DOW 推测当前可能的斜停角度. 通过目标计算
    float guessstaticguardrailangle;                      // 推测静止护栏的角度.
    float avgFramerate;                                   /* 平均帧率，秒 */
    float avgSpeedVal;                                    /* 本车的速度均值  m/s */
    float avgSpeedVal_FCTB_Xs;                            /* 本车在Xs内的速度均值  m/s */
    float stored_last_SpeedVal[ADAS_HISTORY_NUM];         /* 存储本车的速度 m/s */
    float sumvehiclemove;                                 /* 单位时间内自车运动的总距离 */
    int BSDVelSpeedDir;                                   /*本车运行方向*/
    uint32_t covercnt;                                    // 右侧雷达可能存在无法被DOW目标穿越的计数值

    cdi_pkg_t *RawTargets;  /*原始点信息*/
    uint8_t movedobjnum;    // 动态目标数量
    uint8_t l_r;            // 雷达左右位置 1,左，0, 右
    uint8_t radarPosition;  // 雷达前后位置 1,前, 0, 后
    uint8_t *oldAlmFlag;    /*上次报警的状态*/
    uint8_t MrrSystemState; /*雷达系统状态,0-默认状态，1-系统故障，2-系统Standby，3-BSD/LCA active，4-RCTA active，5-DOW active*/
    uint8_t adasLCATtc;
    uint8_t adasDOWTtc;
    uint8_t adasRCWTtc;
    uint8_t adasRCTATtc;
    uint8_t adasFCTATtc;
    uint8_t adasFCTBactiveCnt; // FCTB激活时间.

    /**
     * @brief ADAS Regulation Scene
     */
    ADAS_Regulation_Scene_T adasRegulationScene;
}ALARM_OBJECT_T;

typedef struct
{
    float dowguessheading[ADAS_HISTORY_HEADINGANGLE_NUM]; // 推测DOW可能的航向角. 根据历史目标的航向角推测当前目标的航向角 已报警目标在TTC一定时刻统计?
    float guessheadingresult;                             // 推测出的最终结果
    uint8_t dowguessnum;                                  // 已经推测航向角的目标的数量
    uint8_t dowguessheadingvaild;                         // 推测结果是否有效
} st_Dow_State;

// RCTB碰撞预测 出点 入点.
typedef struct
{
    float rctb_enter_y;      // RCTB碰撞预测入点Y值
    float rctb_exit_y;       // RCTB碰撞预测出点Y值 
} st_Rctb_pre;

// 时间戳使用联合体方式设计，与激活状态类似
typedef union
{
    float funcTimestamp[ADAS_TYPE_MAX]; // 使用uint32_t避免溢出
    struct
    {
        float bsdTimestamp;  // BSD最后触发时间戳
        float lcaTimestamp;  // LCA最后触发时间戳
        float dowTimestamp;  // DOW最后触发时间戳
        float rcwTimestamp;  // RCW最后触发时间戳
        float rctaTimestamp; // RCTA最后触发时间戳
        float rctbTimestamp; // RCTB最后触发时间戳
        float fctaTimestamp; // FCTA最后触发时间戳
        float fctbTimestamp; // FCTB最后触发时间戳
    } name;
} FuncTimestamp_T;
typedef union
{
    uint8_t isActivatedInXms[ADAS_TYPE_MAX]; // 0.5s内是否触发标志
    struct
    {
        uint8_t bsdIsActivatedInXms;  // 0.5s内是否触发标志
        uint8_t lcaIsActivatedInXms;  // 0.5s内是否触发标志
        uint8_t dowIsActivatedInXms;  // 0.5s内是否触发标志
        uint8_t rcwIsActivatedInXms;  // 0.5s内是否触发标志
        uint8_t rctaIsActivatedInXms; // 0.5s内是否触发标志
        uint8_t rctbIsActivatedInXms; // 0.5s内是否触发标志
        uint8_t fctaIsActivatedInXms; // 0.5s内是否触发标志
        uint8_t fctbIsActivatedInXms; // 0.5s内是否触发标志
    } name;
} FuncIsActivation_T;

typedef struct
{
    float simestamp;                                   // 触发时间戳，
    FuncTimestamp_T adasTriggerTime;                   // 时间戳联合体
    FuncIsActivation_T isActivatedInXms;               // 激活状态联合体
    ADAS_FUNC_STATE_T adasFuncStateRec[ADAS_TYPE_MAX]; // 功能激活
    uint8_t adasWarningRec[ADAS_TYPE_MAX];             // 功能报警

    // 可选：保持原有的具名成员以保持代码兼容性
    struct
    {
        uint8_t adasBSDFuncState;
        uint8_t adasBSDWarning;
        uint8_t adasLCAFuncState;
        uint8_t adasLCAWarning;
        uint8_t adasDOWFuncState;
        uint8_t adasDOWWarning;
        uint8_t adasRCWFuncState;
        uint8_t adasRCWWarning;
        uint8_t adasRCTAFuncState;
        uint8_t adasRCTAWarning;
        uint8_t adasRCTBFuncState;
        uint8_t adasRCTBWarning;
        uint8_t adasFCTAFuncState;
        uint8_t adasFCTAWarning;
        uint8_t adasFCTBFuncState;
        uint8_t adasFCTBWarning;
    } specificState;
} ADAS_TriggerRecord_T;

// typedef struct
// {
//     uint32_t simestamp;              // 触发时间戳，
//     ADAS_TriggerFunc_T triggerState; // 最近一次触发的
// } ADAS_TriggerRecord_T;

extern uint8_t gadasAlarmObjIndex;  //报警目标的index

extern uint8_t gADASRadarId;

extern float gadasInstallAzimuthAngle;

extern ADAS_FunctionState_t gadasFunctionState;
extern ADAS_FunctionState_t lastFunctionState;
extern ADAS_TriggerRecord_T gAdasTriggerRecord;

extern float gAdasSpeedInkmph;   //ADAS模块所使用的车速，此处单位是km/h
extern volatile float brake_interval_time;
extern volatile float fctb_brake_interval_time;

extern volatile float alarm_last_duration[ADAS_TYPE_MAX];;  //报警最少持续时间
extern volatile float alarm_continue_duration[ADAS_TYPE_MAX];;  //报警最少持续时间

/**
 * @brief 计算纵向的TTC.
 *
 * @param pobjPath 目标相关结构体地址
 * @return float ttcy -1.0表示目标远离
 */
extern float ADAS_cacTTCY(OBJ_NODE_STRUCT *pobjPath, uint8_t i);

/**
 * @brief 计算穿行场景的TTCX,按照X轴的碰撞来测试
 * 
 * @param pobjPath 跟踪点
 * @return float ttcx结果
 */
float ADAS_cacCTABTTCX(const ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i);

/**
 * @brief 计算穿行场景的TTC或TTM，适用于RCTAB
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return float ttm
 */
float ADAS_cacRCTABTTM(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i);

/**
 * @brief 计算穿行场景的TTM,参照BYD功能规范，适用于FCTA/B和RCTA/B
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return float ttm
 */
float ADAS_cacTTM(const ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i);

/**
 * @brief 求平均值
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return float ttm
 */
float ADAS_cacAvg(float *arry, uint8_t size);

/**
 * @brief 边线模块主函数
 * 
 * @param pfreezedVehDyncData VDY车身动态数据地址 
 */
void ADAS_runRoadLine(const VDY_DynamicEstimate_t *pfreezedVehDyncData);

/**
 * @brief 赋值EDR所需数据
 * 
 * @param pobjPath 目标相关结构体地址
 * @param ttm 详见《IDD_FS_A30-10029》第13页解释
 * @param i 跟踪ID
 */
void ADAS_recordEDRData(OBJ_NODE_STRUCT *pobjPath, float ttm, uint8_t i);


/**
 * @brief 报警状态数据清理.
 * 
 * @param pobjPath 目标相关结构体地址
 * @param alarmtype 报警类型
 */
void ADAS_clrAlmState(OBJ_NODE_STRUCT *pobjPath, uint32_t alarmtype);

/**
 * @brief BSD报警策略主函数
 * 
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param pVDY VDY所有信息
 * @param timeClase_t 时间变量
 * @return alarmFlag
 */
bool ADAS_BSD_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY, const ADAS_TimeClase_t timeClase_t);

/**
 * @brief DOW算法主函数
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID 
 * @param pVDY 车身数据
 * @return true 报警
 * @return false 不报警
 */
bool ADAS_DOW_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY);

/** 
 * @brief LCA报警策略主函数
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param pVDY VDY所有信息 
 * @return alarmFlag
 */
bool ADAS_LCA_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY);

/**
 * @brief RCW报警策略主函数
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param pVDY VDY所有信息 
 * @return alarmFlag 
 */
bool ADAS_RCW_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY);

/**
 * @brief ELKA报警策略主函数
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param pVDY VDY所有信息 
 * @return alarmFlag
 */
bool ADAS_ELKA_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY);

/**
 * @brief RCTAB主函数
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pobjPath 目标相关结构体地址
 * @param pVDY 车身数据 
 * @param pSlaveRadar 从雷达信息 
 * @return uint8_t 报警标志位
 */
uint32_t ADAS_RCTAB_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, const VDY_Info_t *pVDY, const SlaveRadarWarningsStatus *pSlaveRadar);   

/**
 * @brief FCTAB主函数
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pobjPath 目标相关结构体地址
 * @param pVDY 车身数据
 * @param pSlaveRadar 从雷达信息 
 * @return uint8_t 报警标志位
 */
uint32_t ADAS_FCTAB_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, const VDY_Info_t *pVDY, const SlaveRadarWarningsStatus *pSlaveRadar, const ADAS_TimeClase_t timeClase_t);

/**
 * @brief 通过安装角和FOV大小，计算探测区边界方位，通过对比来确定目标是否在雷达盲区，适用于前后横穿功能
 *
 * @param i 跟踪点id
 * @param pobjPath 跟踪点
 * @return uint8_t
 */
uint8_t ADAS_checkBlindArea(uint8_t i, OBJ_NODE_STRUCT *pobjPath);

/**
 * @brief 用于检查报警变量的某一位是否有效
 * 
 * @param type 报警类型
 * @param bit 报警枚举
 * @return true 有效
 * @return false 无效
 */
bool ADAS_checkBit(uint32_t type, uint32_t bit);

/**
 * @brief 用于置位报警变量的某一位
 * 
 * @param type 报警类型
 * @param bit 报警枚举
 */
void ADAS_setBit(uint32_t *type, const uint32_t bit);

/**
 * @brief 用于清除报警变量的某一位
 * 
 * @param type 报警类型
 * @param bit 报警枚举
 */

void ADAS_clearBit(uint32_t *type, const uint32_t bit);

/**
 * @brief 功能报警时的一些标准操作
 * 
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @param type 报警类型
 */
void ADAS_doWarning(const uint8_t i, OBJ_NODE_STRUCT *pobjPath, const uint32_t type);

/**
 * @brief 功能无报警时的变量清除操作
 * 
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @param type 报警类型
 */
void ADAS_clearWarning(const uint8_t i, OBJ_NODE_STRUCT *pobjPath, const uint32_t type);

/**
 * @brief 计算 ：本车以恒定加速度进行制动，目标以匀速进行运行，当本车速度为0时，目标与本车得最小距离（当前是y）
 * 
 * @param pobjPath 目标相关结构体地址
 * @param pVDY 车身数据 
 * @param a 车辆加速度
 * @return float 距离
 */
float ADAS_cacRelativedistanceafterbraking(const OBJ_NODE_STRUCT *pobjPath, const VDY_Info_t *pVDY, const float a);

/**
 * @brief 策略
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param pfreezedVehDyncData
 * @param i
 * @param alarm_param_buff
 * @param pStopFlag
 * @return true 假点
 * @return false 非假点
 */

/**
 * @brief 检测快速方向盘转动
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 道路结构体地址
 * @param pVDY VDY车身数据地址
 * @param timeClase_t 时间类别
 */
void ADAS_detectTurnRadiusRapidChange(ALARM_OBJECT_T *pobjAlm,
                                      OBJ_NODE_STRUCT *pobjPath,
                                      const VDY_Info_t *pVDY,
                                      const ADAS_TimeClase_t timeClase_t);

/**
 * @brief FCTAB场景检测模块主函数
 *
 * @param pfreezedVehDyncData VDY车身动态数据地址
 */
void ADAS_FCTAB_runDetectionScene(ALARM_OBJECT_T *pobjAlm,
                                  OBJ_NODE_STRUCT *pobjPath,
                                  const VDY_Info_t *pVDY,
                                  const ADAS_TimeClase_t timeClase_t);

/**
 * @brief FCTAB场景空松检测一定时间内
 *
 * @param pfreezedVehDyncData VDY车身动态数据地址
 */
void ADAS_FCTAB_StrictDetectXs(ALARM_OBJECT_T *pobjAlm,
                               OBJ_NODE_STRUCT *pobjPath,
                               const VDY_Info_t *pVDY,
                               const ADAS_FunctionState_t adasFuncState,
                               const ADAS_TimeClase_t timeClase_t);

/**
 * @brief 获取RCTAB内部变量
 */
OBJ_NODE_STRUCT *getgtempobjPath(void);

/**
 * @brief DOW 清除场景识别的相关信息
 */
void DOW_clearSence(void);


#endif
