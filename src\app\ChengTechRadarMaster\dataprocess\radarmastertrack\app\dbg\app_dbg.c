/**
 * @file     app_dbg.c
 * @brief    This file defines the functions for radar debug.
 *           Set radar parameters and set work mode.
 * <AUTHOR> (linkang<PERSON>@chengtech.com)
 * @version  1.0
 * @date     2023-03-15
 *
 *
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2023-03-15  <td>1.0      <td>Wison      <td>First Version
 * <tr>  <td>2023-08-17  <td>1.1      <td>Erthfw     <td>Add detection hil and trace hil debug interfaces
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

/****************************************************************************
  INCLUDE
 ****************************************************************************/
#include <string.h>
#include "rsp_interface.h"
#include "pcan_protocol.h"
#include "cfg.h"
#include "vehicle_cfg.h"
#include "app_dbg.h"
#include "data_hil_det_trk.h"
#include "app_vehicle.h"
#include "cfg.h"


/*****************************************************************************
  GLOBAL VARIABLES
 *****************************************************************************/
static uint8_t gHILDataDbgMode = 0;     // 数据回灌调试模式，0:正常模式，1:检测点回灌模式，2:跟踪点回灌模式


/**
 *  @b Description
 *  @n
 *      Set the debug mode of data HIL.
 *
 *  @param[in]  dbgMode
 *      The HIL debug mode. 0:Normal mode, 1:Detection HIL mode, 2:Tracking HIL mode
 */
void DBG_setHILDataDbgMode(uint8_t dbgMode)
{
    gHILDataDbgMode = dbgMode;
}

/**
 *  @b Description
 *  @n
 *      Get the debug mode of data HIL.
 *
 *  @retval
 *      The HIL debug mode.
 */
uint8_t DBG_getHILDataDbgMode(void)
{
    return gHILDataDbgMode;
}

/**
 *  @b Description
 *  @n
 *      Set the data output mode.
 *
 *  @param[in]  dataMode
 *      The data output mode.
 */
void DBG_setDataOutputMode(uint8_t dataMode)
{
    RSP_setDspDataMode(dataMode);
}

/**
 *  @b Description
 *  @n
 *      Get the data output mode.
 *
 *  @retval
 *      The data output mode.
 */
uint8_t DBG_getDataOutputMode(void)
{
    return RSP_getDspDataMode();
}

/**
 *  @b Description
 *  @n
 *      Parse the CAN RX data and set the parameters of radar work mode.
 *
 *  @param[in]  pData
 *      The buffer of CAN RX data.
 */
void DBG_setRadarWorkModeParams(uint8_t *pData)
{
    stRadarWorkModeMsg *pMsg = (stRadarWorkModeMsg *)pData;

    // 设置数据回灌HIL模式
    /* Normal mode */
    if (pMsg->dbgMode == 1)
    {
        if (DBG_getHILDataDbgMode() != 0)  //回灌模式退出时清除车速信息
        {
            /*Clear speed information*/
            Vehicle_setSpeed(0, 0, 0);
        }
        DBG_setHILDataDbgMode(0);
        /* Clear frame index */
        HIL_setDetTrkFrameIdx(0);
    }
    /* Detection HIL mode */
    else if (pMsg->dbgMode == 2)
    {
        DBG_setHILDataDbgMode(1);
        /* Clear frame index */
        HIL_setDetTrkFrameIdx(0);
    }
    /* Tracking HIL mode */
    else if (pMsg->dbgMode == 3)
    {
        DBG_setHILDataDbgMode(2);
        /* Clear frame index */
        HIL_setDetTrkFrameIdx(0);
    }

    // 设置数据输出模式
    if (pMsg->outputMode == 1)
    {
        DBG_setDataOutputMode(PARAM_DATAMODE_TRACK);
    }
    else if (pMsg->outputMode == 2)
    {
        DBG_setDataOutputMode(PARAM_DATAMODE_RAW);
    }
    else if (pMsg->outputMode == 3)
    {
        DBG_setDataOutputMode(PARAM_DATAMODE_RAW_TRACK);
    }
    else
    {
        DBG_setDataOutputMode(PARAM_DATAMODE_NONE);
    }

    // 设置车速选择
    if ((pMsg->carVelMode == 0) && (pMsg->estimatedSpeedCfgExpand == 0)) //不修改
    {
    }
    else if (pMsg->carVelMode == 1) //不用不上传
    {
        CFG_setCarSpeedCfg(1, 0);
    }
    else if (pMsg->carVelMode == 2) //用并不上传
    {
        CFG_setCarSpeedCfg(0, 0);
    }
    else if (pMsg->carVelMode == 3) //用并上传
    {
        CFG_setCarSpeedCfg(0, 1);
    }
    else if (pMsg->estimatedSpeedCfgExpand == 1) //不用并上传
    {
        CFG_setCarSpeedCfg(1, 1);
    }
#if 0 //todo
    // 角度映射方式校准, 1-使能校准, 2-关闭校准
    if (pMsg->angleMapperMode == 1)
    {
        CFG_setAngleMapper(1);
    }
    else if (pMsg->angleMapperMode == 2)
    {
        CFG_setAngleMapper(0);
    }
#endif
    // 发送曲率、曲率半径、温度（同步信息）的使能
    if (pMsg->sendYawRateMode == 1)
    {
        CFG_setSendYawrate(1);
    }
    else if (pMsg->sendYawRateMode == 2)
    {
        CFG_setSendYawrate(0);
    }

    //设置雷达测试的模式，暂定正常模式和精度测试模式
    if (pMsg->radarTestMode == 1)
    {
        CFG_setResolutionTestMode(0);
    }
    else if (pMsg->radarTestMode == 2)
    {
        CFG_setResolutionTestMode(1);
    }
    else if(pMsg->radarTestMode == 3)
    {
        CFG_setResolutionTestMode(2);
    }

    if (pMsg->protVer/* && (pMsg->protVer <= PROTOCOL_VER_MAX)*/)
    {
        CFG_setProtocolVersion(pMsg->protVer - 1);
    }
}

/**
 *  @b Description
 *  @n
 *      Parse the CAN RX data and set the parameters of radar work mode.
 *
 *  @param[in]  pData
 *      The buffer of CAN RX data.
 */
void DBG_getRadarWorkModeParams(stRadarWorkModeMsg *pMsg)
{
    const radar_config_t *pRadarCfg = CFG_getRadarCfg();
    uint8_t dataMode = DBG_getDataOutputMode();

    //输出模式配置
    if (dataMode == PARAM_DATAMODE_RAW_TRACK)
    {
        pMsg->outputMode = 1;
    }
    else if (dataMode == PARAM_DATAMODE_TRACK)
    {
        pMsg->outputMode = 2;
    }
    else if (dataMode == PARAM_DATAMODE_RAW)
    {
        pMsg->outputMode = 3;
    }
    else
    {
        pMsg->outputMode = 0;
    }

    //速度选择配置
    if (pRadarCfg->sendCarVel == 0 && pRadarCfg->speedSource == 1)
    {
        pMsg->carVelMode = 1;
        pMsg->estimatedSpeedCfgExpand = 0;
    }
    else if (pRadarCfg->sendCarVel == 0 && pRadarCfg->speedSource == 0)
    {
        pMsg->carVelMode = 2;
        pMsg->estimatedSpeedCfgExpand = 0;
    }
    else if (pRadarCfg->sendCarVel == 1 && pRadarCfg->speedSource == 0)
    {
        pMsg->carVelMode = 3;
        pMsg->estimatedSpeedCfgExpand = 0;
    }
    else if (pRadarCfg->sendCarVel == 1 && pRadarCfg->speedSource == 1)
    {
        pMsg->estimatedSpeedCfgExpand = 1;
        pMsg->carVelMode = 0;
    }
    else
    {
        pMsg->estimatedSpeedCfgExpand = 0;
        pMsg->carVelMode = 0;
    }

    //pMsg->angleMapperMode = pRadarCfg->angleMapperEn == 1 ? 1 : 2;
    pMsg->sendYawRateMode = pRadarCfg->sendYawrate == 1 ? 1 : 2;
}

/**
 *  @b Description
 *  @n
 *      Process the query message from PCAN and response the message.
 *
 *  @param[in]  pData
 *      The buffer of CAN RX data.
 */
void DBG_procQueryMsg(uint8_t canIdx, uint8_t *pData)
{
    stQueryMsg *pMsg = (stQueryMsg *)pData;
    const radar_config_t *pRadarCfg = CFG_getRadarCfg();
    char respBuf[8];
    char version[6];
    uint16_t i = 0;

    if (pMsg->queryVersion)
    {
        /* SW version info */
        memset((uint8_t *)respBuf, 0, 8);
        memcpy(version, getCtVersionInfo()->version_app, sizeof(getCtVersionInfo()->version_app));
        //CFG_getSwVersion(version);
        respBuf[0] = (1 << 4) | 4;
        for (i = 0; i < 6; i++)
        {
            respBuf[i + 1] = version[i];
        }
        COMLIB_convertBigLittleEndian((uint8_t *)respBuf, eDATA_LEN_8);
        gCAN[canIdx].sendFrame(&respBuf, CAN_ID_RADAR_VERSION);

        /* HW version info */
        memset((uint8_t *)respBuf, 0, 8);
        CFG_getHwVersion(version);
        respBuf[0] = (2 << 4) | 4;
        for (i = 0; i < 4; i++)
        {
            respBuf[i + 1] = version[i];
        }
        COMLIB_convertBigLittleEndian((uint8_t *)respBuf, eDATA_LEN_8);
        gCAN[canIdx].sendFrame(&respBuf, CAN_ID_RADAR_VERSION);

        /* Calibration version info */
        memset((uint8_t *)respBuf, 0, 8);
        respBuf[0] = (3 << 4) | 2;
        for (i = 0; i < 2; i++)
        {
            respBuf[i + 1] = pRadarCfg->radar_info.CalVer[i];
        }
        COMLIB_convertBigLittleEndian((uint8_t *)respBuf, eDATA_LEN_8);
        gCAN[canIdx].sendFrame(&respBuf, CAN_ID_RADAR_VERSION);

        /* Boot version info */
        memset((uint8_t *)respBuf, 0, 8);
        respBuf[0] = (4 << 4) | 4;
        for (i = 0; i < 4; i++)
        {
            respBuf[i + 1] = pRadarCfg->radar_info.bootSwVer[i];
        }
        COMLIB_convertBigLittleEndian((uint8_t *)respBuf, eDATA_LEN_8);
        gCAN[canIdx].sendFrame(&respBuf, CAN_ID_RADAR_VERSION);

        /* DID version info */
        memset((uint8_t *)respBuf, 0, 8);
        respBuf[0] = (5 << 4) | 6;
        respBuf[1] = DID_SW_VERSION_H;
        respBuf[2] = DID_SW_VERSION_M;
        respBuf[3] = DID_SW_VERSION_L;
        respBuf[4] = DID_SW_VERSION_YEAR - 2000;
        respBuf[5] = DID_SW_VERSION_MONTH;
        respBuf[6] = DID_SW_VERSION_DATE;

        COMLIB_convertBigLittleEndian((uint8_t *)respBuf, eDATA_LEN_8);
        gCAN[canIdx].sendFrame(&respBuf, CAN_ID_RADAR_VERSION);
    }

    if (pMsg->queryInstallClacInfo)
    {
        //安装校准信息查询 TODO
//        sendRadarCalcInfo(canIdx,CALC_TYPE_INSTALL_CALC);
    }
}
