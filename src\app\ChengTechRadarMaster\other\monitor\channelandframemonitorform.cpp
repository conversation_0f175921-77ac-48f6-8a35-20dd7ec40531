﻿#include "channelandframemonitorform.h"
#include "ui_channelandframemonitorform.h"
#include "channelandframemonitortreemodel.h"
#include "devices/devicemanager.h"
#include "devices/ideviceworker.h"
#include "../../analysis/analysismanager.h"
#include "../../analysis/analysisworker.h"

#include <QDir>
#include <QDebug>
#include <QMessageBox>

ChannelAndFrameMonitorForm::ChannelAndFrameMonitorForm( Devices::Can::DeviceManager* deviceManager,
                                                        Analysis::AnalysisManager *analysisManager,
                                                        QWidget *parent) :
    QWidget(parent),
    ui(new Ui::ChannelAndFrameMonitorForm)
{
    ui->setupUi(this);

    this->setWindowTitle( "Channel Frame Monitor Set" );
    Qt::WindowFlags flags = Qt::Dialog;
    flags |= Qt::WindowCloseButtonHint /*| Qt::WindowMaximizeButtonHint | Qt::WindowMinimizeButtonHint*/;
    this->setWindowFlags(flags);

    mModel = new ChannelAndFrameMonitorTreeModel( this );
    ui->monitorTreeView->setModel( mModel );
    ui->monitorTreeView->setEditTriggers(QTreeView::DoubleClicked);			//单元格双击编辑

    mDeviceManager = deviceManager;
    mAnalysisManager = analysisManager;

    this->setMinimumSize( QSize(650,300) );

    Analysis::AnalysisWorker* analysisWorker = mAnalysisManager->analysisWorker();
    connect( this, &ChannelAndFrameMonitorForm::clearChannelAndFrameFilter, analysisWorker, &Analysis::AnalysisWorker::clearFilter );
    connect( this, &ChannelAndFrameMonitorForm::addChannelFilter, analysisWorker, &Analysis::AnalysisWorker::addChannelFilter );
    connect( this, &ChannelAndFrameMonitorForm::addFrameFilter, analysisWorker, &Analysis::AnalysisWorker::addFrameFilter );

    connect( mModel, &ChannelAndFrameMonitorTreeModel::frameCheckAbnormal, this, &ChannelAndFrameMonitorForm::saveLog );
}

ChannelAndFrameMonitorForm::~ChannelAndFrameMonitorForm()
{
    delete ui;
}

void ChannelAndFrameMonitorForm::setLogSavePath(const QString &path)
{
    mLogSavePath = path;
}

void ChannelAndFrameMonitorForm::on_addChannelPushButton_clicked()
{
    mModel->addChannelMonitor();
}

void ChannelAndFrameMonitorForm::on_addFramePushButton_clicked()
{
    QModelIndex index = ui->monitorTreeView->currentIndex();
    //qDebug() << __FUNCTION__ << __LINE__ << index.row() << index.column();
    if( !mModel->isChannelIndex( index ) ){
        QMessageBox::information( this, QString::fromLocal8Bit("提示"), QString::fromLocal8Bit("请选中【通道】节点!") );
        return;
    }
    mModel->addFrameMonitor( index );
//    ui->monitorTreeView->expand( index );
}

void ChannelAndFrameMonitorForm::on_delPushButton_clicked()
{
    QModelIndex index = ui->monitorTreeView->currentIndex();
    if( -1 == index.row() || -1 == index.column() ){
        QMessageBox::information( this, QString::fromLocal8Bit("提示"), QString::fromLocal8Bit("请选中需要删除的节点!") );
        return;
    }
    mModel->deleteMonitor( index );
}

void ChannelAndFrameMonitorForm::on_startPushButton_clicked()
{
    Devices::Can::IDeviceWorker* pDeviceWorker = mDeviceManager->deviceWorker();
    if( !mModel->isRun() ){
        if( !mDeviceManager->isOpened() ){
            QMessageBox::information( this, QString::fromLocal8Bit("提示"), QString::fromLocal8Bit("请先打开设备!") );
            return;
        }

        openLog();

        ui->addChannelPushButton->setEnabled( false );
        ui->addFramePushButton->setEnabled( false );
        ui->delPushButton->setEnabled( false );
        ui->monitorTreeView->setEnabled( false );
        ui->startPushButton->setText( QString::fromLocal8Bit("结束") );
        connect( pDeviceWorker, &Devices::Can::IDeviceWorker::frameRecieved, mModel, &ChannelAndFrameMonitorTreeModel::recvFrame/*, Qt::DirectConnection*/ );
        connect( pDeviceWorker, &Devices::Can::IDeviceWorker::frameRecieved, this, &ChannelAndFrameMonitorForm::updateLastRecvTime );

        refreshFilter();
    }else{
        closeLog();

        ui->addChannelPushButton->setEnabled( true );
        ui->addFramePushButton->setEnabled( true );
        ui->delPushButton->setEnabled( true );
        ui->monitorTreeView->setEnabled( true );
        ui->startPushButton->setText( QString::fromLocal8Bit("开始") );
        disconnect( pDeviceWorker, &Devices::Can::IDeviceWorker::frameRecieved, mModel, &ChannelAndFrameMonitorTreeModel::recvFrame );
        disconnect( pDeviceWorker, &Devices::Can::IDeviceWorker::frameRecieved, this, &ChannelAndFrameMonitorForm::updateLastRecvTime );

        emit clearChannelAndFrameFilter();
    }
    mModel->startOrStop();
}

void ChannelAndFrameMonitorForm::saveLog(quint8 channelIdx, quint64 frameID, quint64 lastTime, quint64 currTime, quint64 diffTime, quint64 checkCycle, const QByteArray& data)
{
    if( !mLogFile.isOpen() ){
        return;
    }
//    qDebug()<<__FUNCTION__<<__LINE__<< channelIdx << QString::number( frameID, 16 )
//           << lastTime << currTime << diffTime << checkCycle;
    mLogFile.write( "\n" );
    mLogFile.write( QString::number( channelIdx ).toLocal8Bit() );
    mLogFile.write( "," );
    mLogFile.write( QString::number( frameID, 16 ).toLocal8Bit() );
    mLogFile.write( "," );
    mLogFile.write( QDateTime::fromMSecsSinceEpoch( lastTime ).toString( "yyyy-MM-dd hh:mm:ss:zzz" ).toLocal8Bit() );
    mLogFile.write( "," );
    mLogFile.write( QDateTime::fromMSecsSinceEpoch( currTime ).toString( "yyyy-MM-dd hh:mm:ss:zzz" ).toLocal8Bit() );
    mLogFile.write( "," );
    mLogFile.write( QString::number( diffTime ).toLocal8Bit() );
    mLogFile.write( "," );
    mLogFile.write( QString::number( checkCycle ).toLocal8Bit() );
    mLogFile.write( "," );
    mLogFile.write( data.toHex(' ') );
}

void ChannelAndFrameMonitorForm::refreshFilter()
{
    //清除
    emit clearChannelAndFrameFilter();

    QList<quint8> channelFilter;
    QMap< quint8, QList<quint64> > frameFilter;
    mModel->getChannelFilter( channelFilter );
    mModel->getFrameFilter( frameFilter );

    //添加通道过滤器
    for( int i=0; i<channelFilter.size(); i++ ){
        emit addChannelFilter( channelFilter[i] );
    }

    //添加帧过滤器
    QMap< quint8, QList<quint64> >::iterator it = frameFilter.begin();
    for( ; it!=frameFilter.end(); it++ ){
        quint8 channelIdx = it.key();
        QList<quint64>& frameIDList = it.value();
        for( int i=0; i<frameIDList.size(); i++ ){
            emit addFrameFilter( channelIdx, frameIDList[i] );
        }
    }
}

void ChannelAndFrameMonitorForm::openLog()
{
    closeLog();

    if( mLogSavePath.isEmpty() ){
        mLogSavePath = "./data";
    }

    QString fileName = QString("%1/monitor").arg( mLogSavePath );
    QDir logDir(fileName);
    if (!logDir.exists()) {
        logDir.setPath("");
        if (!logDir.mkpath(fileName)) {
                return;
            }
    }

    QDateTime saveTime = QDateTime::currentDateTime();
    fileName += QString("/monitor%1.csv").arg(saveTime.toString("yyyy-MM-dd hh-mm-ss-zzz"));
    mLogFile.setFileName( fileName );
    if( !mLogFile.open( QIODevice::WriteOnly ) ){
        qDebug() << __FUNCTION__ << __LINE__ << " open log file error!";
    }
    mLogFile.write("Channel ID,Frame ID,Last Time,Check Time,Diff Time(ms),Check Cycle(ms),Frame Data");
}

void ChannelAndFrameMonitorForm::closeLog()
{
    if( mLogFile.isOpen() ){
        mLogFile.close();
    }
}

void ChannelAndFrameMonitorForm::updateLastRecvTime( const Devices::Can::CanFrame& frame )
{
    ui->labelLastRecvTimer->setText( QDateTime::fromMSecsSinceEpoch( frame.systemTimeStemp() ).toString("HH:mm:ss:zzz") );
}

void ChannelAndFrameMonitorForm::on_savePushButton_clicked()
{
    mModel->saveToXml();
}
