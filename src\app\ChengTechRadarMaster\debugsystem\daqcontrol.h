﻿#ifndef DAQCONTROL_H
#define DAQCONTROL_H

#include "playback_global.h"
#include "playbacknetworkworker.h"

#include <QWidget>
#include <QThread>

namespace Ui {
class DAQControl;
}

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Analysis {
class AnalysisManager;
}

class PlaybackCanWoker;
class PlaybackNetworkWorker;

class QThread;

class DEBUGSYSTEM_EXPORT DAQControl : public QWidget
{
    Q_OBJECT

public:
    enum PlayDataType
    {
        PlayRawTarget,
        PlayTrackTarget,
        PlayADC,
        Play1DFFT,
        Play2DFFT
    };

    explicit DAQControl(Devices::Can::DeviceManager *deviceManager, Analysis::AnalysisManager *analysisManager, QWidget *parent = nullptr);
    ~DAQControl();

    void loadSettings();
    void saveSettings();

signals:
    void run();
    void startSave(const QString &adcType);
    void stopSave();

    void openTCPServer(const QString &IP, quint16 port, bool analyIP = false);
    void closeTCPServer();

    void startAlgorithmPlay(int startFrameIndex, int periodicTime, bool singleStep = false);
    void stopAlgorithmPlay();
    void nextAlgorithmPlayFrame();
    void pauseAlgorithmPlay(bool paused);

    void startPlayback(int startFrameIndex, bool recharge, bool single, bool algorithm, int algorithmRadarID);
    void stopPlayback();
    void pausePlayback();
    void playNextPlayback();
    void playPrevPlayback();
    void playPlayback(int index);

public slots:
    void saveStarted(const QString &saveFile);
    void playbackThreadStarted();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void tcpServerOpened(bool opened, const QString &message);
    void tcpServerClosed();
    void tcpClientConnected(const QString &IP, quint16 port);
    void tcpClientDisonnected();
    void dckVersion(const QString &version);

    void initAlgorithmPlayResult(bool ok);
    void playAlgorithmPlayState(/*PlayState*/int state, const QString &message = "");
    void playAlgorithmPlayFrameInfo(int index, int total, const QString &message = "");

    void on_pushButtonStartStopServer_clicked();

    void on_pushButtonProjectFile_clicked();

    void on_pushButtonProfilesConfig_clicked();

    void on_pushButtonStartAndStop_clicked(bool checked);

    void on_pushButtonInit_clicked();

    void on_checkBoxSingleStep_clicked(bool checked);

    void on_comboBoxModel_currentIndexChanged(int index);

    void on_comboBoxCollectMode_currentIndexChanged(int index);

    void on_pushButtonPrevFrame_clicked();

    void on_pushButtonNextFrame_clicked();

    void on_comboBoxCanIndex_currentIndexChanged(int index);

    void on_pushButtonIntoDebugMode_clicked();

    void on_pushButtonExitDebugMode_clicked();

    void on_pushButtonCollectStart_clicked();

    void on_pushButtonCollectStop_clicked();

    void on_pushButtonReboot_clicked();

    void on_comboBoxAlgorithmRadarID_currentIndexChanged(const QString &arg1);

    void on_checkBoxAlgorithm_stateChanged(int arg1);

private:
    bool intoDebugMode();
    bool exitDebugMode();

    Ui::DAQControl *ui;

    PlaybackNetworkWorker::WorkMode mWorkMode{PlaybackNetworkWorker::PlaybackMode};   // 回放模式
    PlayDataType mPlayDataType{PlayADC};                                        // 回放的数据类型
    int mCanChannelIndex{0};

    PlaybackCanWoker *mPlaybackCanWoker{0};
    QThread *mPlaybackCanWorkerThread{0};

    PlaybackNetworkWorker *mPlaybackNetworkWorker{0};
    QThread *mPlaybackNetworkWorkerThread{0};

    Devices::Can::DeviceManager *mDeviceManager{0};
    Analysis::AnalysisManager *mAnalysisManager{0};
};

#endif // DAQCONTROL_H
