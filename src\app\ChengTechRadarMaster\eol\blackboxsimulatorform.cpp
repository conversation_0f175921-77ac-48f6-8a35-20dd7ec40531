#include "blackboxsimulatorform.h"
#include "ui_blackboxsimulatorform.h"

#define BLACKBOX_IP1 "*************"
#define BLACKBOX_IP2 "*************"
#define BLACKBOX_PORT "12001"
#define LOCAL_PORT1 "12000"
#define LOCAL_PORT2 "12003"


BlackBoxSimulatorForm::BlackBoxSimulatorForm(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::BlackBoxSimulatorForm)
{
    ui->setupUi(this);
    ui->lineEditBlackBoxIP->setText( BLACKBOX_IP1 );
    ui->lineEditBlackBoxPort->setText( BLACKBOX_PORT );
    ui->lineEditLocalPort->setText( LOCAL_PORT1 );
    //ui->lin
}

BlackBoxSimulatorForm::~BlackBoxSimulatorForm()
{
    delete ui;
}

void BlackBoxSimulatorForm::on_pushButtonConnectTest_clicked()
{
    //mUdpSocket->
}
