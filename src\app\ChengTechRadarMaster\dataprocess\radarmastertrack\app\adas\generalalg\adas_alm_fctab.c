#include <stdbool.h>
#include <math.h>
#include <string.h>

#ifdef ALPSPRO_ADAS
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "rdp/track/data_process/rdp_interface.h"
#include "adas/common/linear_regression.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/common/linear_regression_application.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#elif defined (PC_DBG_FW)
#include "app/system_mgr/typedefs.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/common/linear_regression_application.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "other/temp.h"
#else
#include "app/rdp/rdp_clth_radar_lib.h"
#include "app/rdp/rdp_interface.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/common/linear_regression_application.h"
#include "common/include/vehicle_cfg.h"
#endif

#if (ALARM_TYPE_EDR_EN == 1)
extern ADAS_EDRData_t EDRDataCTB;
#endif

static float interceptUpperBuffer = 0.0f;
static float posBufYFar = 0.0f;
static float rangeBuf = 0.0f;	   //目标在径向距离上的缓冲
static float ttcBufFctaLower = 0.0f; // Fcta功能ttc的下限buffer
static float ttcBufFctaUpper = 0.0f; // Fcta功能ttc的上限buffer
static float ttcExtendBuf = 0.0f;       // TTC扩展   用于 A3 版本功能规范.  直线行驶时适当扩大TTC.
static uint8_t CTB_EDRTargetI;
static uint8_t CTB_EDRTargetLifeCycle;
//static int fctabBuffhistory[FCTAB_SUM_WIN_SIZE]; // 历史值，其中fctabBuffhistory[FCTAB_SUM_WIN_SIZE-1]为最后的记录
//static int fctabBuffInit = 0;           // 前FCTAB_SUM_WIN_SIZE-1次填充后才能开始输出
//static int fctabIndex = 0; //环形数组可放数据的位置
//static int fctabFactor[FCTAB_SUM_WIN_SIZE] = {1,2,3,4,5,6}; // 加权系数
//static int fctab_K = 21;                                     // 1+2+3+4+5+6=21 

typedef struct
{
    float vxThreshold;            // 相对速度阈值
    float tiggerEarliestPosition; // 最早的位置
    float tiggerLatestPosition;   // 最晚的位置
    float distanceRange;          // 两个位置差值
} PositionProfile_T;
// 全局参数
const PositionProfile_T POSITION_PROFILES[] = {
    {2.0f, 0.0f, -0.8f, 0.8f},  // 自车发出制动最晚横向距离为：2*0.4-1.9 = -1.1m
    {4.0f, 0.5f, -0.3f, 0.8f},  // 自车发出制动最晚横向距离为：4*0.4-1.9 = -0.3m
    {6.0f, 1.5f, 0.0f, 1.5f},   // 自车发出制动最晚横向距离为：6*0.4-1.9 = 0.5m -> 0m
    {8.0f, 2.5f, 0.0f, 2.5f},   // 自车发出制动最晚横向距离为：8*0.4-1.9 = 1.3m -> 0m
    {100.0f, 3.0f, 0.0f, 3.0f}, // 自车发出制动最晚横向距离为：10*0.4-1.9 = 2.1m -> 0m Max
};

// #define FCTAB_MIN_IA_CROSS(angle, angleThr) (fabsf(angle - FCTB_ACTIVE_MIN_IA) < angleThr)      // fctb小角度横穿
// #define FCTAB_MAX_IA_CROSS(angle, angleThr) (fabsf(angle - FCTB_ACTIVE_MAX_IA) < angleThr)      // fctb小角度横穿

static inline uint8_t ADAS_FCTB_isObjBeforeMinTrigDist(const ALARM_OBJECT_T* pobjAlm, const OBJ_NODE_STRUCT* pobjPath, const uint8_t i, const VDY_Info_t* pVDY, const OBJ_ADAS_TYPE_ENUM type);
static bool ADAS_FCTAB_isTrajectoryExceededLateralThr(const ALARM_OBJECT_T *pobjAlm, const OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY, OBJ_ADAS_TYPE_ENUM type);
static uint8_t ADAS_FCTAB_VX_Check(const ALARM_OBJECT_T *pobjAlm, const OBJ_NODE_STRUCT *pobjPath, const uint8_t i, const VDY_Info_t *pVDY, const OBJ_ADAS_TYPE_ENUM type);

/**
 * @brief 做刹车决策，横穿制动场景下，考虑本车对与该目标碰撞风险的贡献度，和刹停时间，来决定是否要刹车
 *
 * @param pobjPath 跟踪点
 * @param i 跟踪点ID
 * @param pCarV 横/纵向速度
 * @param tBrk 刹车时间
 * @param ttm 详见《IDD_FS_A30-10029》第13页解释
 * @return uint8_t ttm结果
 */
static uint8_t ADAS_FCTB_doBrakeDecision(const ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY, float tBrk, float ttm)
{
    uint8_t flag = 0;
    // float ttm_targetv = 0;
    // float ttm_carv = 0; 
    // float ttm_v = 0;
    float ttm_range = 0;
    float ttm_rangeBuff = 0;
    bool exceededLateralThres = false; // 判断目标横向最大位移是否达到过一定阈值  

    /**
     * @brief 碰撞时这几个参数的值符号：ttm-正，ttm_v-正，ttm_range-正，
     *        ttm_carv-负，ttm_targetv-正，ttm_car-正，不一定完全对，待参考
     *
     */
    // ttm_v = (pobjPath[i].vx * sinf(fabsf(pobjPath[i].angle)*degtorad)) + (pobjPath[i].vy * cosf(fabsf(pobjPath[i].angle)*degtorad));    //ttm方向，总的碰撞速度

    // ttm_carv = (carV[0] * sinf(fabsf(pobjPath[i].angle)*degtorad)) + (carV[1] * cosf(fabsf(pobjPath[i].angle)*degtorad));

    ttm_range = MagicSqrt(((pobjPath[i].x + (VEHICLE_WIDTH_INFO / 2)) * (pobjPath[i].x + (VEHICLE_WIDTH_INFO / 2))) + (pobjPath[i].y * pobjPath[i].y));

    if ((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME))
    {
        ttm_rangeBuff += 2.5f; 
    }
    else if ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO))
    {
        ttm_rangeBuff += 22.0f; 
    }

    exceededLateralThres = ADAS_FCTAB_isTrajectoryExceededLateralThr(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTB); 

    if (((ttm_range > ((VEHICLE_WIDTH_INFO / 2) + 0.5 + ttm_rangeBuff)) &&
         ((tBrk + BREAK_RESERVE_TIME) < ttm)) && // 距离不是很近或刹车响应100ms左右
        (exceededLateralThres == false) 
    )
    {
        flag = 0U; // 不制动
    }
    else
    {
        flag = 1U;   //制动
    }
    
    // 针对FCTB车对车场景增加额外补丁
    if ((1 == pobjPath[i].IsCrossObj) &&
        (1 == pobjPath[i].IsFctabNearObj) &&
        (ttm_range < FCTB_BREAK_TTMRANGE_THR) &&
        (exceededLateralThres == true) &&
        (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
    {
        flag = 1; // 制动
    }

    return flag;
}

/**
 * @brief
 *
 * @param current
 * @return int
 */
// static int ADAS_FCTAB_runFilterFunc(int current)
// {
//     int i = 0,j = 0;
//     int sum = 0;

//     if (0 == fctabBuffInit)
//     {
//         fctabBuffhistory[fctabIndex] = current;
//         fctabIndex++;

//         if (fctabIndex >= (FCTAB_SUM_WIN_SIZE - 1))
//         {
//             fctabBuffInit = 1; // fctabIndex有效范围是0-5，前面放到5，下一个就可以输出
//         }

//         return 0xFFFF; // 当前无法输出，做个特殊标记区分
//     }
//     else 
//     {
//         fctabBuffhistory[fctabIndex] = current;
//         fctabIndex++;
//         if (fctabIndex >= FCTAB_SUM_WIN_SIZE)
//         {
//             fctabIndex = 0; // 当前无法输出，做个特殊标记区分
//         }

//         j = fctabIndex;
//         for (i = 0; i < FCTAB_SUM_WIN_SIZE; i++)
//         {
//             // 注意i=0的值并不是最早的值
//             sum += fctabBuffhistory[j] * fctabFactor[i]; // 注意防止数据溢出
//             j++;
//             if (j == FCTAB_SUM_WIN_SIZE)
//             {
//                 j = 0;
//             }
//         }

//         return (sum / fctab_K);
//     }
// }

/**
 * @brief 通过检查目标的置信度，来过滤低质量目标，减少误报
 *
 * @param i 跟踪点id
 * @param pobjPath 目标相关结构体地址
 * @return uint8_t 1：符合要求，0：不符合要求
 */
static uint8_t ADAS_FCTAB_checkTrkObjReliability(const ALARM_OBJECT_T* pobjAlm, uint8_t i, OBJ_NODE_STRUCT *pobjPath)
{
    uint8_t flag = 0;
    uint8_t blindAreaAngleFlag = 0;  //目标是否在车前方盲区的标志位
    int8_t reliability_max = 0U;
    int8_t reliabilityMaxBuff = 0U;

    blindAreaAngleFlag = ADAS_checkBlindArea(i,pobjPath);

    if((blindAreaAngleFlag != 0U) && (pobjPath[i].lifeCycle > FCTB_BLINDAREALIFECYCLE))  //盲区内外对置信度要求不一样
    {
        reliability_max = FCTB_RELIABILITYMININBLINDAREA;
    }
    else
    {
        reliability_max = FCTB_RELIABILITYMININRADARFOV;
    }
    
    if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA))
    {
        reliability_max += FCTB_RELIABILITYBUF;
    }

    if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
        ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)))
    {
        reliabilityMaxBuff = -20;
    }

    if (pobjPath[i].TrkObjReliability >= (reliability_max + reliabilityMaxBuff))
    {
        flag = 1;
    }

    // 连续miss 2帧极其以上的目标不制动  需对过往数据大量仿真验证
    if (pobjPath[i].TrkObjMissCnt > 3)
    {
        flag = 0;
    }

    return flag;
}

/**
 * @brief 通过检查目标的置信度，来过滤低质量目标，减少误报
 *
 * @param i 跟踪点id
 * @param pobjPath 目标相关结构体地址
 * @return uint8_t 1：符合要求，0：不符合要求
 */
static uint8_t ADAS_FCTB_checkTrkObjReliability(const ALARM_OBJECT_T* pobjAlm, uint8_t i, OBJ_NODE_STRUCT *pobjPath)
{
    uint8_t isReliabilityFlag = 0; 
    int8_t reliabilityMaxBuff = 0;

    if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
        ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)))
    {
        reliabilityMaxBuff = -10;
    }

    if (pobjPath[i].TrkObjReliability >= (FCTB_RELIABILITYMININRADARFOV + reliabilityMaxBuff))
    {
        isReliabilityFlag = 1;
    }

    // 连续miss 2帧及其以上的目标不制动  需对过往数据大量仿真验证,miss场景不能用在场景识别上
    if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 0) && (pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 0)) &&
        (pobjPath[i].TrkObjMissCnt > 1))
    {
        isReliabilityFlag = 0;
    }

    return isReliabilityFlag;
}

/**
 * @brief 预测目标时候会进入制动碰撞区
 *
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @return uint8_t
 */
static uint8_t ADAS_FCTAB_judgeCollisionzone(const ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, OBJ_ADAS_TYPE_ENUM type, const VDY_Info_t *pVDY)
{
    uint8_t flag = 0;

    // 线性回归计算
    flag = ADAS_FCTAB_LinearRegression_Flow(pobjAlm, pobjPath, i, pVDY, type);

    return flag;
}

/**
 * @brief 判断目标历史轨迹中横向位移是否达到过一定阈值
 *
 * @param pobjPath 目标相关结构体地址
 * @param size 所统计的帧数量
 * @return uint8_t 1：横向位置满足，可制动 0：横向位置不满足，不可制动
 */
static bool ADAS_FCTAB_isTrajectoryExceededLateralThr(const ALARM_OBJECT_T *pobjAlm,
                                                      const OBJ_NODE_STRUCT *pobjPath,
                                                      const uint8_t i,
                                                      const VDY_Info_t *pVDY,
                                                      const OBJ_ADAS_TYPE_ENUM type)
{
    bool exceededLateralThres = true;
    float vxXThreshold = 0.0f, vxXThrBuff = 0.0f; // 根据速度来定义位置

    // 根据起步时间, 层级收紧FCTB横向距离
    if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
        ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)) ||
        (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
    {
        vxXThreshold = 3.0f; // 5m,  帧数=s/v/avgFramerate
    }
    else if (pobjAlm->runingtime >= FCTB_CHECK_L_STRICT_RUNNING_TIME)
    {
        vxXThreshold = 5.0f;
    }
    else // if (pobjAlm->runingtime >= FCTB_CHECK_M_STRICT_RUNNING_TIME)
    {
        vxXThreshold = 7.0f;
    }
    
    /**
     * @brief FCTA大角度补偿大一些
     */
    if ((type == ADAS_TYPE_FCTA) &&
        (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
        ((pobjPath[i].avgheadingAngle >= 105.0f) && (pobjPath[i].avgheadingAngle <= 130.0f)))
    {
        vxXThrBuff = -1.0;
    }
    /**
     * @brief FCTA直接补偿
     */
    else if (type == ADAS_TYPE_FCTA)
    {
        vxXThrBuff = -1.0f;
    }
    /**
     * @brief FCTB大角度补偿 
     */
    else if ((type == ADAS_TYPE_FCTB) &&
             (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
             ((pobjPath[i].avgheadingAngle >= 105.0f) && (pobjPath[i].avgheadingAngle <= 130.0f)))
    {
        vxXThrBuff = -1.0f;
    }

    // (1)针对从后方切入的场景
    if (pobjPath[i].maxX < (FCTB_CHECK_L_STRICT_X + vxXThrBuff))
    {
        exceededLateralThres = false;
    } 


    return exceededLateralThres;
}

/**
 * @brief 判断目标车到交叉点距离 DDCI 是否小于阈值，满足可触发条件
 *        公式实现 DDCI 阈值：0.3v + v*v/8 + 1
 *      ADAS_FCTAB_checkDdciLateralTriggerCondition
 * @param ddci 当前自车到交点的实际距离（米）
 * @param pVDY 车辆当前动力学信息结构体指针（需包含实时车速 VehSpd，单位km/h）
 * @return bool 1: 横向距离满足阈值（可触发报警/制动） 0: 距离不满足，不可触发
 */
static bool ADAS_FCTAB_checkDdciLateralTrigCond(const ALARM_OBJECT_T *pobjAlm,
                                                const OBJ_NODE_STRUCT *pobjPath,
                                                const uint8_t i,
                                                const VDY_Info_t *pVDY,
                                                const OBJ_ADAS_TYPE_ENUM type,
                                                const uint8_t lngDdciBelowFlag)
{
    (void)pobjAlm;
    bool isLatDdciBelowThr = false;
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (i >= ADAS_TRK_OBJNUM) || (pVDY == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return isLatDdciBelowThr;
    }

    float latDdci = pobjPath[i].x;
    float latPosBuff = 0.0f;

    /* 基于标准公式 DDCI_thr = 0.3*v + v^2/8 + 1参考 */
    float v = 0.0f;
    float latDdciThr = 0.0f, latDdciThrBuff = 0.0f;

    /* 获取目标的相对速度，单位：m/s */
    v = pobjPath[i].avgVxFctb;

    latDdciThrBuff = (v > 3.0) ? 1.0f : v * 0.3;
    /* 公式实现 DDCI 阈值：0.3v + v*v/8 + 1: 目标速度越大，值越大*/
    latDdciThr = 0.3f * v + (v * v) * 0.125f + latDdciThrBuff;
    if ((latDdciThr > 4.5f) && (pobjPath[i].avgVxFctb > 8.0f)) // 横向最大4.5m
    {
        latDdciThr = 4.5f;
    }
    else if ((latDdciThr > 3.0f) && (pobjPath[i].avgVxFctb > 5.55f)) // 5.3->DDCI 6.1m
    {
        latDdciThr = 3.0f;
    }
    else
    {
        latDdciThr = 2.0f;
    }

    // 场景补偿，针对横向距离在8m内的目标,生命周期较大的目标人，容易出现漏刹
    if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
        ((pobjPath[i].avgheadingAngle > 70.0f) && (pobjPath[i].avgheadingAngle < 110.0f)) &&
        (pobjPath[i].avgVxFctb < 2.8f) &&
        (pobjPath[i].maxX < 8.0f) &&
        (pobjPath[i].lifeCycle > 100U))
    {
        latPosBuff += 1.5f;
    }
    // 场景补偿：宽松场景、90度、自车较高速、目标最大Y符合测试场景的y值
    if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
        ((pobjPath[i].avgheadingAngle > 85.0f) && (pobjPath[i].avgheadingAngle < 130.0f)) &&
        (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 2.8f) &&
        ((pobjPath[i].maxY > 0.5f) && (pobjPath[i].maxY < 10.0f)))
    {
        latPosBuff += 1.0f;
    }

    if (lngDdciBelowFlag == 1)
    {
        latPosBuff = 0.0f;
    }

    if ((pobjPath[i].x > -0.8) && (pobjPath[i].x < 4.5f))
    {
        /* 判据：如果ddci < ddciThr，认为满足激活条件 */
        isLatDdciBelowThr = (latDdci < (latDdciThr + latPosBuff));
    }

    return isLatDdciBelowThr;
}

/**
 * @brief 判断自车到交叉点距离 DDCI 是否小于阈值，满足可触发条件
 *        公式实现 DDCI 阈值：0.3v + v*v/8 + 1
 *      ADAS_FCTAB_checkDdciLongitudinalTriggerCondition
 * @param ddci 当前自车到交点的实际距离（米）
 * @param pVDY 车辆当前动力学信息结构体指针（需包含实时车速 VehSpd，单位km/h）
 * @return bool 1: 距离满足阈值（可触发报警/制动） 0: 距离不满足，不可触发
 */
static bool ADAS_FCTAB_checkLngDdciTrigCond(const ALARM_OBJECT_T *pobjAlm,
                                            const OBJ_NODE_STRUCT *pobjPath,
                                            const uint8_t i,
                                            const VDY_Info_t *pVDY,
                                            const OBJ_ADAS_TYPE_ENUM type,
                                            const float ttm)
{
    (void)pobjAlm;
    bool isLngDdciBelowThr = false;
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (i >= ADAS_TRK_OBJNUM) || (pVDY == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return isLngDdciBelowThr;
    }

    float ddci = pobjPath[i].y;
    float lngPosBuff = 0.0f;

    /* 基于标准公式 DDCI_thr = 0.3*v + v^2/8 + 1 */
    float v = 0.0f;
    float ddciThr = 0.0f, ddciThrBuff = 0.0f;

    /*  */

    /**
     * @brief 获取自车当前车速，单位：m/s
     *  这里使用相对纵向速度
     *  如果自车低速，目标横穿，v较低，容易漏，改用径向速度
     */
    if ((type == ADAS_TYPE_FCTA) &&
        (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA) == false) && (ttm < FCTA_TTC_MAX_TIME / 2) &&
        (pVDY->pVDY_DynamicInfo->vdySpeedInmps < 1.66f) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 1.11f))
    {
        v = pobjPath[i].v;
    }
    else if ((type == ADAS_TYPE_FCTB) &&
             (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTB) == false) && (ttm < FCTB_WARNINGTTC / 2) &&
             (pVDY->pVDY_DynamicInfo->vdySpeedInmps < 1.66f) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 1.11f))
    {
        v = pobjPath[i].v;
    }
    else
    {
        v = pobjPath[i].vy; 
    }

    ddciThrBuff = (v > 3.33) ? 1.1f : v * 0.3;
    // 已报警目标增加buff
    if ((type == ADAS_TYPE_FCTA) && (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA) == true))
    {
        ddciThrBuff += 2.0f;
    }
    else if ((type == ADAS_TYPE_FCTB) && (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTB) == true))
    {
        ddciThrBuff += 1.0f;
    }
    /* 公式实现 DDCI 阈值：0.3v + v*v/8 + 1 */
    ddciThr = 0.3f * v + (v * v) * 0.125f + ddciThrBuff;
    if (ddciThr > 6.5f)
    {
        ddciThr = 6.5f;
    }

    if ((type == ADAS_TYPE_FCTB) && 
        (pobjPath[i].boxLength < FCTB_CROSS_OBJBIGTHRESHOLD) && 
        (pobjPath[i].boxWidth < FCTB_CROSS_OBJBIGTHRESHOLD))
    {
        if (ddciThr > 3.0f)
        {
            ddciThr = 3.0f; // 纵向最大4.0m
        }
        lngPosBuff = 0.0f;
    }
    // 场景补偿，针对横向距离在8m内的目标,生命周期较大的目标人，容易出现漏刹
    else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
             ((pobjPath[i].avgheadingAngle > 70.0f) && (pobjPath[i].avgheadingAngle < 110.0f)) &&
             (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 1.6f) &&
             (pobjPath[i].maxX < 8.0f) &&
             (pobjPath[i].lifeCycle > 100U))
    {
        lngPosBuff += 3.0f;
    }
    // 起步补偿
    else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
             ((pobjPath[i].avgheadingAngle > 100.0f) && (pobjPath[i].avgheadingAngle < 125.0f)) &&
             (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 1.6f) &&
             (pobjPath[i].avgVx > 2.0f))
    {
        lngPosBuff += 4.0f;
    }
    // 起步补偿
    else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
             ((pobjPath[i].avgheadingAngle > 65.0f) && (pobjPath[i].avgheadingAngle < 125.0f)) &&
             (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 1.6f) &&
             (pobjPath[i].avgVx > 2.0f))
    {
        lngPosBuff += 2.0f;
    }
    // FCTB纵向触发距离限制，6.65+1.5
    if ((type == ADAS_TYPE_FCTB) && (lngPosBuff > 1.5f))
    {
        lngPosBuff = 0.5f;
    }
    if ((type == ADAS_TYPE_FCTB) && (ddciThr > 4.0f))
    {
        ddciThr = 4.0f;
    }

    if (pobjPath[i].x > -0.8)
    {
        /* 判据：如果ddci < ddciThr，认为满足激活条件 */
        isLngDdciBelowThr = (ddci < (ddciThr + lngPosBuff));
    }

    return isLngDdciBelowThr;
}

/**
 * @brief 检查目标在X方向上是否整体在靠近
 * 
 * @param pobjPath 目标相关结构体地址
 * @param size 所统计的帧数量
 * @return uint8_t 1：在靠近 0：不在靠近
 */
static void ADAS_FCTAB_checkTargetIsApproachingOnX(ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath, const uint8_t i, uint8_t size)
{
    int8_t num = 0U;
    int8_t j = 0;
    //uint8_t flag = 0;
    pobjPath[i].isApproachingX = 0;

    for (j = 0U; j < (size-1U); j++){
        if (pobjPath[i].stored_last_x[j] <= pobjPath[i].stored_last_x[j+1])
        {
            num++;
        }
    }

    const int8_t num_profiles = sizeof(POSITION_PROFILES) / sizeof(PositionProfile_T);
    for (j = 1U; j < num_profiles; j++)
    {
        if (pobjPath[i].avghistoryVx > POSITION_PROFILES[j].vxThreshold) // 根据当前速度找到对应的相对速度阈值
        {
            num += (uint8_t)(j * 2);
            break; 
        }
    }

    // 补充策略：针对帧周期较大的情况，默认num补充2帧
    if ((pobjAlm->avgFramerate > 0.090) && (pobjPath[i].vx < 2.50f))
    {
        num += 2;
    }

    if ((pobjPath[i].boxLength >= FCTB_CROSS_OBJBIGTHRESHOLD) || (pobjPath[i].boxWidth >= FCTB_CROSS_OBJBIGTHRESHOLD))
    {
        if ((num >= (uint8_t)(size * FCTB_CROSS_XOBJBIGNEARPERCENT)) &&
            ((pobjPath[i].stored_last_x[size - 1] - pobjPath[i].stored_last_x[0]) >= (FCTB_CROSS_XDIFFMINPERFRAME * size)))
        {
            pobjPath[i].isApproachingX = 1;
        }
    }
    else if ((pobjPath[i].avgheadingAngle >= FCTB_ACTIVE_MIN_IA) && (pobjPath[i].avgheadingAngle <= (FCTB_ACTIVE_MIN_IA+20)))
    {
        if ((num >= (uint8_t)(size * FCTB_CROSS_XINCLINENEARPERCENT)) &&
            ((pobjPath[i].stored_last_x[size - 1] - pobjPath[i].stored_last_x[0]) >= (FCTB_CROSS_XDIFFMINPERFRAME * size * 0.9)))
        {
            pobjPath[i].isApproachingX = 1; 
        }
    }
    else
    {
        if ((num >= (uint8_t)(size * FCTB_CROSS_XNEARPERCENT)) &&
            ((pobjPath[i].stored_last_x[size - 1] - pobjPath[i].stored_last_x[0]) >= (FCTB_CROSS_XDIFFMINPERFRAME * size)))
        {
            pobjPath[i].isApproachingX = 1;
        }
    }
    // VX与位移差建立关联，判断 1秒内横向位移至少达到“平局速度vx在0.5s的位移”
    if (pobjPath[i].stored_last_x[size - 1] > FLOAT_EPS)    // 避免滤除
    {
        if ((pobjPath[i].stored_last_x[size-1] - pobjPath[i].stored_last_x[0]) <  ((pobjPath[i].avghistoryVx) * 0.5))
        {
            pobjPath[i].isApproachingX = 0;
        }
    } 
}

/**
 * @brief 标准的横穿目标,  且后续测量不准确
 *  横穿场景的控制尽可能严格,接近验收case为主. 尽可能避免路测误刹车.
 *  判断是否是标准的横穿目标
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return uint8_t 1：是横穿，0：不是横穿
 */
static void ADAS_FCTAB_checkTargetIsCrossing(const ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t* pVDY, const OBJ_ADAS_TYPE_ENUM type) 
{
    // 1.历史轨迹具备横穿趋势  2. 航迹框大于一定值. 3.x方向测量不准确. 
    uint8_t isObjxNear = 0;
    int8_t lifeCylceBuff = 0;
    float crossPressminX = 0;
    pobjPath[i].IsCrossObj = 0U;

    isObjxNear = pobjPath[i].isApproachingX;
    if ((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME))
    {
        crossPressminX = 2;
        lifeCylceBuff = -5;
        isObjxNear = 1;
    }
    else if ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO))
    {
        crossPressminX = 20;
        lifeCylceBuff = -5; 
    }
    else
    {
    }

    if ((ALARM_ACTIVE_FCTA == type) && (pobjPath[i].avgVxFctb >= 4.5f))
    {
        lifeCylceBuff = -2;
    }
    else if ((ALARM_ACTIVE_FCTA == type) && (pobjPath[i].avgVxFctb >= 3.4f))
    {
        lifeCylceBuff = -1;
    }
    else
    { }

    if ((pobjPath[i].x <= (FCTB_CROSS_PRESSMINX + crossPressminX)) &&
        (pobjPath[i].lifeCycle >= (ADAS_HISTORY_NUM + lifeCylceBuff)))
    {
        if (isObjxNear != 0U)
        {
            pobjPath[i].IsCrossObj = 1;
            pobjPath[i].LastCrossObjCnt = 1; 
        }
        else if ((0 != pobjPath[i].LastCrossObjCnt) &&
                 (pobjPath[i].x < 0) && (pobjPath[i].x > (-VEHICLE_WIDTH_INFO)) &&
                 (++pobjPath[i].LastCrossObjCnt < FCTA_CROSS_IGNORE_CNT))
        {
            pobjPath[i].IsCrossObj = 1;
        }
        else
        {
            pobjPath[i].IsCrossObj = 0;
            pobjPath[i].LastCrossObjCnt = 0; 
        }
    }
    // 低生命周期不满足的，且在一定时间内的，放宽限制
#if 1
    else if ((pobjPath[i].x <= FCTB_CROSS_PRESSMINX) &&
             (pobjPath[i].lifeCycle < ADAS_HISTORY_NUM) && (pobjPath[i].lifeCycle >= 8) &&
             (pobjPath[i].avghistoryVx > (15 / 3.6f)) &&
             ((pobjPath[i].lifeCycle - pobjPath[i].TrkObjHitCnt) <= 1) &&
             (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
    {
        pobjPath[i].IsCrossObj = 1;
        pobjPath[i].LastCrossObjCnt = 0; 
    }
#endif    
    else
    {
        pobjPath[i].LastCrossObjCnt = 0;
    }

    // 目标首次满足横穿趋势时， 开始计数, 计数一定时间内还未制动,则取消此目标的制动
    if ((pobjPath[i].IsCrossObj == 1U) && (0 == pobjPath[i].fctbTrigCountdown) && (pobjPath[i].x > 0))
    {
        // 速度越慢, 帧数越多 额外补偿一定帧数, 避免目标高速不制动
        pobjPath[i].fctbTrigCountdown = MIN(40U, ((uint8_t)(1.0f / pobjPath[i].avghistoryVx) * 40U)) + 10;
    }
}

/**
 * @brief FCTA限制目标整体趋势一定要是靠近车辆
 *      判断是否是标准的横穿目标
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return uint8_t 1：是横穿，0：不是横穿
 */
static void ADAS_FCTAB_checkTargetIsNear(const ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t* pVDY, const OBJ_ADAS_TYPE_ENUM type) 
{
    // 1.历史轨迹具备横穿趋势  2. 航迹框大于一定值. 3.x方向测量不准确.  
    //uint8_t flag = 0;
    int8_t lifeCylceBuff = -2;
    uint8_t isObjxNear = pobjPath[i].isApproachingX;
    float rangediffvalueBuff = 0.f; // 目标航迹绝对位移差值.
    pobjPath[i].IsFctabNearObj = 0U;

    if ((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME))
    {
        lifeCylceBuff += -5;
        rangediffvalueBuff = -1.0f; // 目标航迹绝对位移差值.
        isObjxNear = 1;
    }

    if ((ALARM_ACTIVE_FCTA == type) && (pobjPath[i].avgVxFctb >= 4.5f))
    {
        lifeCylceBuff += -2;
    }
    else if ((ALARM_ACTIVE_FCTA == type) && (pobjPath[i].avgVxFctb >= 3.4f))
    {
        lifeCylceBuff += -1;
    }
    else
    {
    }

    if (pobjPath[i].lifeCycle >= (ADAS_HISTORY_NUM + lifeCylceBuff))
    {
        if ((isObjxNear != 0) &&
            ((pobjPath[i].rangestartdiffvalue > (FCTA_CROSS_RANGESTARTDIFF + rangediffvalueBuff)) ||
             (pobjPath[i].rangemaxdiffvalue > (FCTA_CROSS_RANGEMAXDIFF + rangediffvalueBuff))))
        {
            pobjPath[i].IsFctabNearObj = 1;
            pobjPath[i].LastFctabNearObjCnt = 1; 
        }
        else if ((0 != pobjPath[i].LastFctabNearObjCnt) &&
                 (pobjPath[i].x < 0) && (pobjPath[i].x > (-VEHICLE_WIDTH_INFO)) &&
                 (++pobjPath[i].LastFctabNearObjCnt < FCTA_CROSS_IGNORE_CNT))
        {
            pobjPath[i].IsFctabNearObj = 1;
        }
        else
        {
            pobjPath[i].IsFctabNearObj = 0;
            pobjPath[i].LastFctabNearObjCnt = 0; 
        }
    }
    // 低生命周期不满足的，且在一定时间内的，放宽限制
#if 1
    else if ((pobjPath[i].lifeCycle < ADAS_HISTORY_NUM) && (pobjPath[i].lifeCycle >= 8) &&
             (pobjPath[i].avghistoryVx > (15 / 3.6f)) &&
             ((pobjPath[i].lifeCycle - pobjPath[i].TrkObjHitCnt) <= 1) &&
             (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
    {
        pobjPath[i].IsFctabNearObj = 1;
        pobjPath[i].LastFctabNearObjCnt = 0; 
    }
#endif
    else
    {   
        pobjPath[i].LastFctabNearObjCnt = 0;
    } 
}

/**
 * @brief 前方穿行预警处理
 *
 * @param i 循环计数
 * @param pobjPath 目标数据
 * @param intercept 碰撞点
 * @param roadline 边线
 * @return float ttm
 */
static float ADAS_FCTA_predictCollision(ALARM_OBJECT_T *pobjAlm,
                                        OBJ_NODE_STRUCT *pobjPath,
                                        const uint8_t i,
                                        const VDY_Info_t *pVDY, 
                                        const float roadline)
{
    // uint8_t flag = 0U; //满足报警标志位：1—满足报警，0-不满足报警
    float intercept = 10.0f; // 碰撞点
    float speedbuf = 0;
    uint8_t speedflag = 0U;
    uint8_t targetCollisionFlag = 1U; // 目标与碰撞区的标志位，0-区域外，1-区域内；
    uint8_t checkVXFlag = 0U;         // 横向速度满足标志：1—满足，0-不满足
    uint8_t objCurveSpeedflag = 1U;   // 弯道场景提高对目标速度的要求,抑制弯道假点误报 0-速度不符合  1-速度符合
    float ttm = -1.0f, tty = 10.0f, ttx = 10.0f;
    float runtimeminIAangle = 0.0f, runtimemaxIAangle = 0.0f; // 起步时刻补偿参数
    float objMinSpeedBuff = 0.f;
    float minAnglebuffer = 0.f, maxAnglebuffer = 0.f;
    bool exceededLateralThres = false; // 判断目标横向最大位移是否达到过一定阈值
    bool isLngDdciBelow = false;
    // 蝴蝶翅膀x对应的y方向宽度
    float Width = FCTA_DISTANCEMAX + (fabsf(pobjPath[i].x) * tanf((FCTA_ACTIVE_MAX_IA - 90) * degtorad));

    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA)) //如果上一次正在报警，则增加报警判断的buff，防抖
    {
        speedbuf = FCTA_SPEED_BUF;
        minAnglebuffer = FCTA_ACTIVE_MIN_IA_BUF;
        maxAnglebuffer = FCTA_ACTIVE_MAX_IA_BUF;
    } 

    // 保证是靠近本车，在车正前方运动，无论什么方向都认为是靠近本车
    if ((pobjPath[i].vx > 0.1)&&(pobjPath[i].x > (-1 * VEHICLE_WIDTH_INFO))) 
    {
        /**
         * @brief 限制自车车速需要大于1.0m/s(3.6kph)
         *  车速为标量，车静止时算TTCX，车运动时算TTM 
         */
        if (pVDY->pVDY_DynamicInfo->vdySpeedInmps >= 1.0f) 
        {
            ADAS_FCTAB_checkTargetIsApproachingOnX(pobjAlm, pobjPath, i, ADAS_HISTORY_NUM);
            ADAS_FCTAB_checkTargetIsNear(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTA); // 判断是否符合逼近趋势, FCTA用.
            ADAS_FCTAB_checkTargetIsCrossing(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTA);

            // 判断是否车辆穿行场景, 车辆穿行场景计算 TTCY TTY计算给出X方向的冗余
            if ((pobjPath[i].IsCrossObj != 0U) &&
                (pobjPath[i].x <= FCTB_CROSS_TTY_XDISTANCE_THR) && (1 == FCTB_TTY_EN))
            {
                tty = ADAS_cacTTCY(pobjPath, i);
            }
            // 如果是小角度横穿场景计算TTX 小角度目标属于远离  TTY没有意义
            if ((1 == FCTB_TTY_EN) && (1 == pobjPath[i].IsCrossObj) &&
                (pobjPath[i].x <= FCTB_CROSS_TTX_XDISTANCE_THR) && FCTAB_MIN_IA_CROSS(pobjPath[i].avgheadingAngle, 15) &&
                (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
            {
                ttx = ADAS_cacCTABTTCX(pobjAlm, pobjPath, i);
            }

            ttm = ADAS_cacTTM(pobjAlm, pobjPath, i);
            if (tty > FLOAT_EPS)
            {
                ttm = MIN(tty, ttm);
            }
            if (ttx > FLOAT_EPS)
            {
                ttm = MIN(ttx, ttm);
            }
        }
        else
        {
            ttm = ADAS_cacCTABTTCX(pobjAlm, pobjPath, i);
        }
    }

    /**
     * @brief 场景补偿，代码位置尽量不要动，有先后顺序
     * 
     */
    if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
        ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO))) // 4s
    {
        objMinSpeedBuff = -3.0f;
    } 

    /**
     * @brief 速度是否满足，绝对速度
     * 
     */
    if ((pobjPath[i].absolutevabs >= FCTA_OBJ_MIN_SPEED - speedbuf + objMinSpeedBuff) && pobjPath[i].absolutevabs <= (FCTA_OBJ_MAX_SPEED + speedbuf))
    {
        speedflag = 1;
    }
    // 相对低速目标横穿时, 到车头正前方横向速度不准导致Vmove偏低. 做响应的补偿。
    if ((pobjPath[i].absolutevabs >= (FCTA_OBJ_MIN_SPEED + FCTA_OBJ_MIN_SPEED_BUF)) &&
        (pobjPath[i].absolutevabs <= (FCTA_OBJ_MAX_SPEED + speedbuf)) &&
        ((pobjPath[i].boxLength >= FCTB_CROSS_OBJBIGTHRESHOLD) || (pobjPath[i].boxWidth >= FCTB_CROSS_OBJBIGTHRESHOLD)) &&
        (pobjPath[i].x < 0) &&
        (pobjPath[i].x > (-VEHICLE_WIDTH_INFO)) && (1 == pobjPath[i].IsFctabNearObj))
    {
        speedflag = 1;
    }

#if (1 == FCTAB_STRICT_EN)
    float objCurveSpeed = 0.0f;
    /**
     * @brief 对于转弯的场景, 根据方向盘转角的大小,动态提高目标的对地速度大小, 减少转弯时动态假点导致的误报。
     *  最多限制到目标对地速度 FCTA_CURV_OBJSPEED_STEERANGLE_H 
     */ 
    if (fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) >= FCTB_CURV_SUPPRESSION_STEERANGLE_LSPEED)
    {
        objCurveSpeed = (FCTA_OBJ_MIN_SPEED + (fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) * FCTA_CURV_OBJSPEED_STEERANGLE_RATE));
        objCurveSpeed = MIN(objCurveSpeed, FCTA_CURV_OBJSPEED_STEERANGLE_H);
        if (pobjPath[i].absolutevabs < objCurveSpeed)
        {
            objCurveSpeedflag = 0;
        }
    }
#endif

    /**
     * @brief 碰撞点计算 
     */
    if (fabsf(pobjPath[i].vx) > 0.1)
    {
        intercept = pobjPath[i].y + (pobjPath[i].x / tanf(pobjPath[i].avgheadingAngle * degtorad));
    }
    else
    {
        intercept = 15.0;
    } 

    /**
     * @brief FCTA碰撞预测
     */
    targetCollisionFlag = ADAS_FCTAB_judgeCollisionzone(pobjAlm, pobjPath, i, ADAS_TYPE_FCTA, pVDY); 
    /**
     * @brief 横向速度
     */
    checkVXFlag = ADAS_FCTAB_VX_Check(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTA);
    /**
     * @brief 横向位置
     */
    exceededLateralThres = ADAS_FCTAB_isTrajectoryExceededLateralThr(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTA);
    isLngDdciBelow = ADAS_FCTAB_checkLngDdciTrigCond(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTA, ttm);

    /**
     * @brief 场景补偿，代码位置尽量不要动，有先后顺序
     */
    if ((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME))
    {
        objCurveSpeedflag = 1;
        minAnglebuffer = -35;
        maxAnglebuffer = 50;
    }
    else if ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)) // 4s
    {
        ttcBufFctaUpper += 0.5f;
    }
    if (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) // 起步补偿
    {
        runtimeminIAangle = FCTA_ACTIVE_MIN_IA_BUF;
        runtimemaxIAangle = FCTA_ACTIVE_MAX_IA_BUF;
    }
    else // if (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 0U)
    {
        runtimeminIAangle = -FCTA_ACTIVE_MIN_IA_BUF; // 符号没错
        runtimemaxIAangle = -FCTA_ACTIVE_MAX_IA_BUF;
    }
    // 起步补偿
    if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
        ((pobjPath[i].avgheadingAngle > 85.0f) && (pobjPath[i].avgheadingAngle < 130.0f)) &&
        (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 2.8f) &&
        (pobjPath[i].vx > 2.5f))
    {
        interceptUpperBuffer += pVDY->pVDY_DynamicInfo->vdySpeedInmps * 3.6f * 0.15f;
        // posBufYFar += 1.0f;
    }
    // 会有概率FCTB只满足一帧，第二帧FCTA不满足，产生漏报，这里考虑的是让FCTA继续满足
    else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
             (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA)) &&
             (pobjPath[i].vx > 2.0f))
    {
        interceptUpperBuffer += 0.8f;
    }
    if (interceptUpperBuffer > 3.0f)
    {
        interceptUpperBuffer = 3.0f;
    }

    if(speedflag != 0U)   //目标速度大小,为了抑制误报误刹，速度一票否决
    {
        if (
            ((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U || ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA)) && // 运动点或上次正在报警的目标
            (ttm <= ((FCTA_CRISISLEVEL * FCTA_TTC_MAX_TIME) + ttcBufFctaUpper + ttcExtendBuf)) &&                                                // ttm
            (ttm >= (FCTA_TTC_MIN_TIME + ttcBufFctaLower)) &&
            (pobjPath[i].x < (FCTA_LENGTH + rangeBuf)) &&                                                 // FCTA报警区域最大宽度
            (isLngDdciBelow == true) &&                                                                   // 动态y范围
            ((pobjPath[i].avgheadingAngle > (FCTA_ACTIVE_MIN_IA + minAnglebuffer + runtimeminIAangle)) && // 轨迹夹角
             (pobjPath[i].avgheadingAngle < (FCTA_ACTIVE_MAX_IA + maxAnglebuffer + runtimemaxIAangle))) &&
            ((roadline < MIN_SIDE_VALUE) || ((roadline >= MIN_SIDE_VALUE) && (pobjPath[i].x <= roadline))) && // 边线外动态点抑制.
            //(intercept > (FCTA_DISTANCEMIN - interceptUpperBuffer)) &&                                        // 碰撞点
            //(intercept < (FCTA_DISTANCEMAX + interceptUpperBuffer)) &&
            (1U == pobjPath[i].IsFctabNearObj) && // 符合逼近趋势
            (1U == targetCollisionFlag) &&        // 满足碰撞预测
            (1U == objCurveSpeedflag) &&          // 满足弯道场景目标最低速度要求
            (1U == checkVXFlag) &&                // 满足横向速度要求
            (1U == exceededLateralThres))         // 满足横向位置要求
        {
            // flag = 1;
        }
        else
        {
            ttm = -1;   //不报警，输出非法ttm
        }
    }
    else
    {
        ttm = -1;   //不报警，输出非法ttm
    }

    return ttm;
}

/**
 * @brief 退出报警前的操作，涉及延迟退出等
 * 
 * @param pobjPath 目标相关结构体地址
 * @param flag 报警标志位
 */
static void ADAS_FCTA_QuitWarning(uint8_t i, OBJ_NODE_STRUCT *pobjPath, uint8_t *flag)
{
    pobjPath[i].startAlarmDly = 0; //不满足报警，直接清零，需要连续满足报警要求才能报警

    // 退出报警前，延迟5帧
    if ((ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA)) && pobjPath[i].overfctaAlarmDly < 5U)
    {
        pobjPath[i].overfctaAlarmDly++;
        *flag = 1;
        ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_FCTA);
    }
    else
    {
        *flag = 0U;

        pobjPath[i].overfctaAlarmDly = 0U;
        ADAS_clearWarning(i, pobjPath, ALARM_ACTIVE_FCTA);
    }
}

/**
 * @brief 计算穿行场景的TTCX,参照BYD功能规范，适用于FCTA/B会车时场景
 *
 * @param pobjPath 目标相关结构体地址
 * @return float ttcx
 */
// static float ADAS_cacFCTABTTCX(OBJ_NODE_STRUCT *pobjPath, uint8_t i)
// {
//     float ttcx;

//     ttcx = (pobjPath[i].x + (VEHICLE_WIDTH_INFO/2))/ fabsf(pobjPath[i].vx);

//     return ttcx;
// }

/**
 * @brief 横向速度是否满足
 * @param pobjPath 
 * @param i 
 * @param BSDVelSpeedVal 
 * @param pobjAlm 
 * @return uint8_t  1 横向速度满足   0  横向速度不满足
 */
static uint8_t ADAS_FCTAB_VX_Check(const ALARM_OBJECT_T *pobjAlm,
                                   const OBJ_NODE_STRUCT *pobjPath,
                                   const uint8_t i,
                                   const VDY_Info_t *pVDY,
                                   const OBJ_ADAS_TYPE_ENUM type)
{
    uint8_t flag = 0; //  默认bu满足   符合一定条件置位满足
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (i >= ADAS_TRK_OBJNUM) || (pVDY == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return flag; // 0  横向速度不满足
    }

    uint8_t k = 0, activeCnt = 0;
    int8_t activeCntBuff = 0;
    float vxthreshold = 0.0f; // 速度阈值
    float vxThrCoefficient = 0.90f;

    if (type == ADAS_TYPE_FCTA) 
    {
        activeCntBuff = -2;
        vxThrCoefficient = 0.8;
    }
    else if ((type == ADAS_TYPE_FCTB) &&
             (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
             ((pobjPath[i].avgheadingAngle >= 105.0f) && (pobjPath[i].avgheadingAngle <= 130.0f)))
    {
        activeCntBuff = -2;
    }
    else
    { }

    // 根据起步时间, 层级收紧FCTB横向速度
    if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
        ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)) ||
        (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
    {
        vxthreshold = FCTB_CHECK_L_STRICT_VX_L;
    }
    else if (pobjAlm->runingtime >= FCTB_CHECK_L_STRICT_RUNNING_TIME)
    {
        vxthreshold = FCTB_CHECK_L_STRICT_VX;
    }
    else // if (pobjAlm->runingtime >= FCTB_CHECK_M_STRICT_RUNNING_TIME)
    {
        vxthreshold = FCTB_CHECK_M_STRICT_VX;
    } 

    // 横向速度需满足一定值
    if (fabsf(pobjPath[i].stored_last_vx[ADAS_HISTORY_NUM / 2]) > FLOAT_EPS) // 中间的数据 
    {
        // 连续20帧的Vx  计算单帧Vx  以及总体Vx
        for (k = 0; k < 12; k++)
        {
            if (pobjPath[i].stored_last_vx[k] >= vxthreshold)
            {
                activeCnt++;
            }
        }
        // 有一定帧数满足横向速度阈值,且最近5帧的速度满足一定阈值
        if ((activeCnt >= (7 + activeCntBuff)) && (pobjPath[i].avgVx >= vxthreshold * vxThrCoefficient))
        {
            flag = 1;
        }
    }
    return flag;
}

/**
 * @brief FCTB会车场景下，目标可能会不稳定有往本车上窜的趋势导致刹车，客户体验不好，所以针对此场景严格制动条件
 *
 * @param pobjPath
 * @param i 跟踪ID
 * @return uint8_t 0：不在会车；1、正在会车
 */
static uint8_t ADAS_FCTB_checkTargetPassingEachOther(const ALARM_OBJECT_T *pobjAlm,
                                                     const OBJ_NODE_STRUCT *pobjPath,
                                                     const uint8_t i,
                                                     const VDY_Info_t *pVDY,
                                                     const OBJ_ADAS_TYPE_ENUM type)
{
    uint8_t flag = 0; 

    // 一定距离内与自车保持一定时间跟随的目标  目标在自车启动后保持长时间运动  全部抑制
#if (1 == FCTAB_STRICT_EN)
    if ((pobjPath[i].fctb_follow_cnt >= FCTB_FOLLOWCNT_MAX_FRAME) ||
        ((pobjPath[i].fctb_objrunning_cnt >= FCTB_OBJ_RUNNING_MAX_FRAME) && (pobjPath[i].vx < (3.0f))))
    {
        flag = 1;
    }

    // 横向速度满足时， 不认为是会车  否则认为是会车
    if (0 == ADAS_FCTAB_VX_Check(pobjAlm, pobjPath, i, pVDY, type))
    {
        flag = 1;
    }
    // 高质量目标可以简单认为是横穿
    else if ((0U == pobjPath[i].IsCrossObj) && (pobjPath[i].lifeCycle < 100))
    {
        flag = 1;
    }
    else
    {}

#endif

    return flag;
}

/**
 * @brief 判断目标是否小于最早制动距离
 * @param i
 * @param pobjPath
 * @return 1，小于最早制动距离（可制动），0，大于最早制动距离（不可制动）
 */
static inline uint8_t ADAS_FCTB_isObjBeforeMinTrigDist(const ALARM_OBJECT_T *pobjAlm, const OBJ_NODE_STRUCT *pobjPath, const uint8_t i, const VDY_Info_t *pVDY, const OBJ_ADAS_TYPE_ENUM type)
{
    // 根据横向速度估计
    uint8_t posFlag = 0; // 默认小于最早制动距离
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (i >= ADAS_TRK_OBJNUM))
    {
        return posFlag;
    }
    float tiggerEarliestPosBuff = 0.0f; // 默认扩1m
    float vx = 0.0f;
    int8_t j = 0;
    const char num_profiles = sizeof(POSITION_PROFILES) / sizeof(PositionProfile_T);

    if (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U)
    {
        tiggerEarliestPosBuff += 0.5;
    }

    vx = MAX(pobjPath[i].vx, pobjPath[i].avgVxFctb);
    vx = MAX(vx, pVDY->pVDY_DynamicInfo->vdySpeedInmps);

    for (j = 0; j < num_profiles; j++)
    {
        if (vx < POSITION_PROFILES[j].vxThreshold) // 根据当前速度找到对应的相对速度阈值
        {
            if (vx < (5.0f / 3.6f)) // 针对低速特殊处理
            {
                tiggerEarliestPosBuff = 0.0f;
            }

            // 报警不能早于最早的位置
            if ((ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTB) == false) &&
                (pobjPath[i].x > (POSITION_PROFILES[j].tiggerEarliestPosition + tiggerEarliestPosBuff)))
            {
                break;
            }
            posFlag = 1; 
            break;
        }
    }

    return posFlag; 
}

/**
 * @brief 判断目标是否在制动范围内,或者是目标是否越过车身
 * @param i
 * @param pobjPath
 * @return 1，没有越过（对应制动），0，越过（对应不制动）
 */
static uint8_t ADAS_FTCB_checkTargetBrakingRange(const ALARM_OBJECT_T *pobjAlm, const OBJ_NODE_STRUCT *pobjPath, const uint8_t i, const VDY_Info_t *pVDY, const OBJ_ADAS_TYPE_ENUM type)
{
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (i >= ADAS_TRK_OBJNUM))
    {
        return 0;
    }
    const char num_profiles = sizeof(POSITION_PROFILES) / sizeof(PositionProfile_T);
    // 根据横向速度估计
    uint8_t tiggerFlag = 0; // 默认没有越过，即制动
    bool apprLatThrFlag = false; // 原始点越过阈值标记,只针对vx小于2m/s
    int j = 0, k = 0, iCnt = 0;
    int totolrate = ADAS_HISTORY_NUM;
    float maxrange = 0.0f;
    float vx = 0.0f; 
    float tiggerEarliestPosBuff = 0;

    if (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U)
    {
        tiggerEarliestPosBuff += 0.5;
    }

    // 不能设置的太小,还要考虑到车身的宽度.
    if (pobjPath[i].boxLength < 2.0f)
    {
        maxrange = 2.0f;
    }
    else if (pobjPath[i].boxLength <= 3.5f)
    {
        maxrange = pobjPath[i].boxLength;
    }
    else 
    {
        maxrange = 3.5f;
    }

    vx = MAX(pobjPath[i].vx, pobjPath[i].avgVxFctb);

    for (j = 0; j < num_profiles; j++)
    {
        if (vx < POSITION_PROFILES[j].vxThreshold) // 根据当前速度找到对应的相对速度阈值
        {
            // 报警不能早于最早的位置
            if (ADAS_FCTB_isObjBeforeMinTrigDist(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTB) == 0)
            {
                break;
            }

            // j对应于制动在自车正前方的目标，针对此种情况，需要判断原始点横向位置是否小于0.0f
            if ((j == 0) && (pobjPath[i].nreTargetX > 0.0f))
            {
                apprLatThrFlag = true; // 原始点越过阈值标记
            }

            // 报警不能晚于最晚的位置
            if (vx > FLOAT_EPS) // 均速，只对靠近目标处理
            {
                totolrate = MIN((uint8_t)((maxrange + POSITION_PROFILES[j].distanceRange) / (vx * pobjAlm->avgFramerate)), (uint8_t)ADAS_HISTORY_LESSER_NUM);
            }
            // 打个补丁,有为1的
            if (totolrate <= 4)
            {
                tiggerEarliestPosBuff = 0;
            }
            for (k = 0; k < ADAS_HISTORY_NUM; k++)
            {
                // 统计历史位置已经超过最晚触发位置的次数 
                if (pobjPath[i].stored_last_x[k] < (POSITION_PROFILES[j].tiggerEarliestPosition + tiggerEarliestPosBuff))
                {
                    iCnt++; 
                }
            } 
            break;
        } 
    } 
    
    if (apprLatThrFlag == true)
    {
        tiggerFlag = 0;
    }
    // 车侧穿行目标已经越过车身
    else if ((iCnt >= totolrate) && (vx >= 1.0f))
    {
        tiggerFlag = 0;
    } 
    else if ((iCnt > 0) && (vx >= 1.0f))
    {
        tiggerFlag = 1;
    }

    return tiggerFlag;
}

/**
 * @brief 是否存在真实碰撞风险, 即x是否跨越或接近车身.
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return uint8_t 1：碰撞；0：不碰撞
 */
static uint8_t ADAS_FCTB_checkTargetIsRealCollision(const ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t* pVDY, const float ttm)
{
    uint8_t flag = 0;
    uint8_t tiggerBrake = 1;    // 1，没有越过（对应制动），0，越过（对应不制动）
    uint8_t isLngDdciBelow = 0; // 0，纵向DDCI不满足；1，满足
    uint8_t isLatDdciBelow = 0; // 0，横向DDCI不满足；1，满足

    tiggerBrake = ADAS_FTCB_checkTargetBrakingRange(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTB);
    isLngDdciBelow = ADAS_FCTAB_checkLngDdciTrigCond(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTB, ttm);
    isLatDdciBelow = ADAS_FCTAB_checkDdciLateralTrigCond(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTB, isLngDdciBelow);

    // 特殊场景：法规场景，默认满足
    if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
        ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO))) // 4s
    {
        flag = 1; // 存在真实碰撞风险.
    }
    else
    {
        if ((isLngDdciBelow == 1) && (isLatDdciBelow == 1))
        {
            tiggerBrake = 1;
        }
        // 目标必须跨过设定的x值才允许制动.  最大程度避免误刹.
        // 横向速度4以下,必须越过车身   横向速度4以上, 允许车身外1米的冗余.
        // 感知到制动的最晚横向距离为：w=-车宽+10cm(假设车宽是2m)，延迟时间最大为400ms，
        if (tiggerBrake == 0) // 在严格场景, // 不制动
        {
            flag = 0U; // 表示不存在真实碰撞风险，或碰撞风险已过
        }
        else
        {
            flag = 1; // 存在真实碰撞风险.
        }

        // 特殊场景：宽松场景
        if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&             // 在宽松（验收等）场景
            ((tiggerBrake == 0) ||                                                        // 不制动
             ((pobjPath[i].lrFitting.fctab_FitAvailableCnt < 18U) && (tiggerBrake == 0))) // 这是针对vx在1.5m内出现异常减小
        )
        {
            flag = 0U; // 表示不存在真实碰撞风险
        }
        else
        {
            flag = 1; // 存在真实碰撞风险.
        }
    }

    return flag;
}

/**
 * @brief FCTB弯道抑制，弯道对象来车引起误刹车，故暂时限制方向盘转角
 * 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param pVDY 车身数据
 * @return uint8_t 1：抑制， 0：不抑制
 */
static uint8_t ADAS_FCTB_doCurveSuppression(OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY, const ALARM_OBJECT_T *pobjAlm)
{
    // 1. 方向盘转角大于一定值 abs
    // 2. 转弯半径小于一定值  abs
    uint8_t flag = 0;

    if (((pVDY->pVDY_DynamicInfo->vdySpeedInmps <= 2.0f) &&
         (fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) > FCTB_CURV_SUPPRESSION_STEERANGLE_LSPEED)) ||
        ((pVDY->pVDY_DynamicInfo->vdySpeedInmps > 2.0f) &&
         (fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) > FCTB_CURV_SUPPRESSION_STEERANGLE_HSPEED)) ||
        (fabsf(pVDY->pVDY_DynamicInfo->vdyCurveRadius) < FCTB_CURV_SUPPRESSION_TURNRADIUS))
    {
        flag = 1;
    }

    // 补充策略：验收场景的额外补充  起步激活的一定时间内（150ms*120=18s）,  放宽方向盘转角
    if ((pobjAlm->adasFCTBactiveCnt < FCTB_CHECK_MAX_CNT) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME) &&
        (fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) < FCTB_CURV_SUPPRESSION_STEERANGLE_MAX) && 
        (pobjAlm->movedobjnum < FCTB_CHECK_MOVED_NUM))
    {
        flag = 0;
    }

    return flag;
}

/**
 * @brief 动态TTM.
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param ttm 详见《【FCTA_B】智驾系统平台功能规范-前方横向碰撞提醒_制动_A7.pdf》
 * @return uint8_t dynamic ttm
 */
static float ADAS_FCTB_calculateDynamicTTM(const ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY)
{
    (void)pVDY;
    float ttm = 10.0f, tty = 10.0f, ttx = 10.0f;

    // 计算TTY：横穿个目标、x小于0.5m
    if ((1 == FCTB_TTY_EN) && (1 == pobjPath[i].IsCrossObj) && (pobjPath[i].x <= FCTB_CROSS_TTY_XDISTANCE_THR))
    {
        tty = ADAS_cacTTCY(pobjPath, i);
    }
    // 如果是小角度横穿场景计算TTX 小角度目标属于远离  TTY没有意义  为了避免误制动， 限制起步时间
    if ((1 == FCTB_TTY_EN) && (1 == pobjPath[i].IsCrossObj) && (pobjPath[i].x <= FCTB_CROSS_TTX_XDISTANCE_THR) &&
        FCTAB_MIN_IA_CROSS(pobjPath[i].avgheadingAngle, 15) &&
        (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
    {
        ttx = ADAS_cacCTABTTCX(pobjAlm, pobjPath, i);
    }

    ttm = ADAS_cacTTM(pobjAlm, pobjPath, i);

    if (tty > FLOAT_EPS)
    {
        ttm = MIN(tty, ttm);
    }
    if (ttx > FLOAT_EPS)
    {
        ttm = MIN(ttx, ttm);
    }

    return ttm;
}

/**
 * @brief 动态TTM.对于一定角度切入的目标, 切入后形成远离趋势, 适当提高TTM.
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param ttm 详见《【FCTA_B】智驾系统平台功能规范-前方横向碰撞提醒_制动_A7.pdf》
 * @return uint8_t 1：满足，0：不满足
 */
static uint8_t ADAS_FCTB_predictCollisionBaseTTM(const ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t* pVDY, const float ttm)
{
    uint8_t flag = 0;
    /* 获取自车当前车速，单位：km/h */
    float BSDVelSpeedVal = pVDY->pVDY_DynamicInfo->vdySpeedInmps * 3.60f; 

    float ttmthr = 0.0f, ttmThrTmp = 0.0f;
    ttmthr = FCTB_CRISISLEVEL * FCTB_WARNINGTTC;
    //uint8_t baseposflag = 0; 

    // 针对考试场景的识别 演示场景放大TTC. 可以不用留？
    if ((BSDVelSpeedVal < 10) && (pobjPath[i].y < FCTA_CROSS_TTC_YREAR) &&
        (pobjPath[i].maxrange > 5.0f) && (ttm <= (ttmthr + FCTB_WARNINGTTC_BUF)))
    {
        flag = 1;
    }

    /**
     * @brief 补充策略
     */
    // 一般不开场景检测
    if ((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME))
    {
        ttmthr += 1.5f;
    }
    else if ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO))
    {
        ttmthr += 1.0f;
    }
    // 补偿策略：宽松场景、90度大附近、目标较高速4m/s以上
    else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
             ((pobjPath[i].avgheadingAngle > 75.0f) && (pobjPath[i].avgheadingAngle < 105.0f)) &&
             (pobjPath[i].vx > 4.0f))
    {
        ttmthr += BSDVelSpeedVal * 0.05f;
    }
    // 补偿策略：宽松场景、90度、自车10kph以上、目标10kph以上
    else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
             ((pobjPath[i].avgheadingAngle > 85.0f) && (pobjPath[i].avgheadingAngle < 95.0f)) &&
             (pVDY->pVDY_DynamicInfo->vdySpeedInmps > 2.8f) &&
             (pobjPath[i].vx > 2.5f))
    {
        ttmthr += BSDVelSpeedVal * 0.01f;
    }
    // 补偿策略：宽松场景、大角度、目标1.3m/s以上
    else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
             ((pobjPath[i].avgheadingAngle > 105.0f) && (pobjPath[i].avgheadingAngle < 135.0f)) && 
             (pobjPath[i].vx > 1.3f))
    {
        ttmThrTmp = BSDVelSpeedVal * 0.1f;
        ttmthr += (ttmThrTmp < 0.3f) ? ttmThrTmp : 0.3f;
    }

    // 限制最大值
    if ((pVDY->pVDY_DynamicInfo->vdySpeedInmps <= 2.8f) && (ttmthr > 2.5f))
    {
        ttmthr = 2.5f;
    }
    else if ((pVDY->pVDY_DynamicInfo->vdySpeedInmps > 2.8f) && (ttmthr > 2.0f))
    {
        ttmthr = 2.0f;
    }
    if ((ttm <= ttmthr))
    {
        flag = 1;
    }

    // 补充策略：目标在x小于0m、y小于2.5m、体目标、横穿，赋为，默认满足ttc
    // 如果走到这一步，会晚刹
    if ((flag == 0) && (pobjPath[i].x < 0) && (pobjPath[i].y < FCTA_CROSS_TTC_YREAR) &&
        ((pobjPath[i].boxLength >= FCTB_CROSS_OBJBIGTHRESHOLD) || (pobjPath[i].boxWidth >= FCTB_CROSS_OBJBIGTHRESHOLD)) &&
        (1 == pobjPath[i].IsCrossObj))
    {
        if (ttm <= (ttmthr + FCTB_WARNINGTTC_BUF))
        { // 车辆横穿场景额外增加TTC.
            flag = 1;
        }
    }

    // 补充策略：起步超过一定时间后,  再次提升横向TTC阈值
    if ((pobjAlm->runingtime >= FCTB_CHECK_L_STRICT_RUNNING_TIME) &&
        ((pobjPath[i].vx < FCTB_CHECK_L_STRICT_VX) || (pobjPath[i].maxX < FCTB_CHECK_L_STRICT_X) || (pobjPath[i].range >= FCTB_CHECK_L_STRICT_RANGE)))
    {
        flag = 0;
    }

    // 补充策略：历史平均航向角不满足的目标  不制动   按自车运动时间分段处理
    if ((pobjAlm->runingtime <= FCTB_CHECK_L_STRICT_RUNNING_TIME) &&
        ((pobjPath[i].avgheadingAngle <= (FCTB_ACTIVE_MIN_IA + FCTB_ACTIVE_MIN_IA_BUF)) || 
         (pobjPath[i].avgheadingAngle >= (FCTB_ACTIVE_MAX_IA + FCTB_ACTIVE_MAX_IA_BUF + 5.0f))))
    {
        flag = 0;
    }

    return flag;
}

/**
 * @brief 结合刹车距离划定一个区域，，目标落入此区域,则认为有碰撞风险
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param timeVehicleBrk 刹停时间
 * @return uint8_t 1：需要制动，0：不需要
 */
// static uint8_t ADAS_FCTB_predictCollisionBasePosition(OBJ_NODE_STRUCT *pobjPath, uint8_t i, float timeVehicleBrk)
// {
//     uint8_t flag = 0; //满足报警标志位：1—满足报警，0-不满足报警

//     if
//     (
//     (pobjPath[i].x <= (FCTB_DISTANCEBRK + (pobjPath[i].vx * timeVehicleBrk))) && //x方向停车距离，注意adas坐标系中，目标靠近为正
//     (pobjPath[i].y <= (FCTB_DISTANCEBRK - (pobjPath[i].vy * timeVehicleBrk)))//y方向停车距离
//     )
//     {
//         flag = 1;
//     }

//     return flag;
// }


/**
 * @brief FCTB主函数
 *
 * @param i 跟踪ID
 * @param pobjPath 道路结构体地址
 * @param BSDVelSpeedVal 本车的速度信息
 * @param pVDY 车身数据
 */
static void ADAS_FCTB_runMain(uint8_t i, OBJ_NODE_STRUCT *pobjPath, const VDY_Info_t *pVDY, ALARM_OBJECT_T *pobjAlm)
{
    uint8_t collisionFlagBaseTTM = 0;
    uint8_t passingFlag = 0;           // 会车标志位,0:不是会车，1：会车
    uint8_t brkflag = 0;               // 制动标志位： 0：不制动，1：制动
    uint8_t targetCollisionFlag = 0;   // 目标与碰撞区的标志位，0-区域外，1-区域内；
    uint8_t curvsuppressionflag = 0;   // 弯道抑制功能 : 0:不抑制  1:抑制
    uint8_t realCollisionFlag = 0;
    float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
    float timeVehicleBrk = 0;
    float ttm = 10.0f;

    if (ADAS_FCTB_checkTrkObjReliability(pobjAlm, i, pobjPath) == false) // 目标置信度低
    {
        return;
    }

    timeVehicleBrk = (fabsf(BSDVelSpeedVal) / 3.6) / fabsf(FCTB_START_DEC_VAL); // 计算刹车时间

    ttm = ADAS_FCTB_calculateDynamicTTM(pobjAlm, pobjPath, i, pVDY); // 计算TTM

    collisionFlagBaseTTM = ADAS_FCTB_predictCollisionBaseTTM(pobjAlm, pobjPath, i, pVDY, ttm);          // 基于TTM的碰撞预测
    passingFlag = ADAS_FCTB_checkTargetPassingEachOther(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_FCTB);    // 会车兼具横穿判断
    curvsuppressionflag = ADAS_FCTB_doCurveSuppression(pobjPath, i, pVDY, pobjAlm);                     // 方向盘转角限制
    targetCollisionFlag = ADAS_FCTAB_judgeCollisionzone(pobjAlm, pobjPath, i, ADAS_TYPE_FCTB, pVDY);    // 碰撞预测
    realCollisionFlag = ADAS_FCTB_checkTargetIsRealCollision(pobjAlm, pobjPath, i, pVDY, ttm);
    if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
        ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)))
    {
        passingFlag = 0;
        curvsuppressionflag = 0;
    }

    // ttm满足，不是错车场景，目标置信度满足条件，则进行制动判断, 真是碰撞
    if ((collisionFlagBaseTTM != 0U) && (passingFlag == 0U) && (targetCollisionFlag != 0U) &&
        (0U == curvsuppressionflag) && (1U == realCollisionFlag) && (pobjPath[i].TrkObjMissCnt == 0U))
    {
        brkflag = ADAS_FCTB_doBrakeDecision(pobjAlm, pobjPath, i, pVDY, timeVehicleBrk, ttm); // 判断是否要制动

        if (brkflag != 0U) // 制动 (pobjPath[i].maxX < FCTB_CHECK_L_STRICT_X)
        {
            ADAS_setBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTB);
            ADAS_setBit(&pobjPath[i].alarmType, ALARM_ACTIVE_FCTB);
            gAdasTriggerRecord.adasTriggerTime.name.fctbTimestamp = gAdasTriggerRecord.simestamp;
            gAdasTriggerRecord.isActivatedInXms.name.fctbIsActivatedInXms = 1; 
        }
        else
        {
            ADAS_clearWarning(i, pobjPath, ALARM_ACTIVE_FCTB);
        }
    }
    else
    {
        ADAS_clearWarning(i, pobjPath, ALARM_ACTIVE_FCTB);
    }
}

/**
 * @brief 检查目标的质量，主要是过滤假点及大部分静态点，与功能规范无关
 * 
 * @param pobjPath 目标相关结构体地址 
 * @param i 跟踪ID
 * @return true 有效
 * @return false 无效
 */
static bool ADAS_FCTA_checkTargetQuality(const ALARM_OBJECT_T* pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i)
{
    bool flag = true;   //默认目标有效

    if(ADAS_FCTAB_checkTrkObjReliability(pobjAlm, i, pobjPath) == false)   //目标置信度低
    {
        return false;
    }

    if((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) == 0U && ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA) == false)   //非动态目标，上次有报警的目标不强制要求为动态
    {
        return false;
    }
    
    return flag;
}

/**
 * @brief 
 * 
 * @param pobjAlm 
 * @param pobjPath 
 * @param i 
 * @param isFctbAvailable 
 * @param pVDY 
 * @return true 
 * @return false 
 */
static bool ADAS_FCTA_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, uint8_t isFctbAvailable, const VDY_Info_t *pVDY)
{
    bool  alarmFlag = 0; 
    float minttm = -1.0f; //用于存储TTM最小目标的信息
    uint8_t isFctaActiv = 0U;
    float roadline = 0.0f;

    roadline = pobjAlm->RoadLine; 

    if( ADAS_FCTA_checkTargetQuality(pobjAlm, pobjPath, i) == false )   //低质量目标直接返回
    {
        return false;
    }

    // 该目标已经报警，增加buffer
    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA))
    {
        posBufYFar = 1.0;
        interceptUpperBuffer = 2.0;
        rangeBuf = FCTA_LENGTH_BUF;
        ttcBufFctaLower = FCTA_TTC_MIN_TIME_BUF;
        ttcBufFctaUpper = FCTA_TTC_MAX_TIME_BUF;
    }
    else
    {
        interceptUpperBuffer = 0.0;
        posBufYFar = 0.0;
        rangeBuf = 0.0;
        ttcBufFctaLower = 0.0;
        ttcBufFctaUpper = 0.0;
    }
    // 基于目标相对直行的场景. 增加TTC. 本质是A3功能规范相比于A1增加了TTC. 为了避免误报在直行场景生效
    if (fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) <= FCTB_CURV_SUPPRESSION_STEERANGLE_LSPEED)
    {
        ttcExtendBuf = FCTA_TTC_MAX_TIME_BUF;       // 相对标准的直线行驶场景 使得FCTA更易触发.
    }

    minttm = ADAS_FCTA_predictCollision(pobjAlm, pobjPath, i, pVDY, roadline);

    if(minttm >= 0) //ttm为正常值，则表示已满足报警条件
    {
        ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_FCTA);
        pobjPath[i].overfctaAlarmDly = 0U;
        alarmFlag = true;
        pobjPath[i].startAlarmDly = 0;
        isFctaActiv = 1;
    }
    else
    {
        pobjPath[i].alarmDlyThr = 5;
        ADAS_FCTA_QuitWarning(i, pobjPath, (uint8_t *)&alarmFlag);
    }

    if (isFctbAvailable == 1U && isFctaActiv == 1U)
    {
        ADAS_FCTB_runMain(i, pobjPath, pVDY, pobjAlm);
    }
    else
    {
        ADAS_clearWarning(i, pobjPath, ALARM_ACTIVE_FCTB);
    }

    return alarmFlag;
}

/**
 * @brief FCTAB主函数
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pobjPath 目标相关结构体地址
 * @param pVDY 车身数据
 * @param pSlaveRadar 从雷达信息 
 * @return uint8_t 报警标志位
 */
uint32_t ADAS_FCTAB_runMain(ALARM_OBJECT_T *pobjAlm,
                            OBJ_NODE_STRUCT *pobjPath,
                            const VDY_Info_t *pVDY,
                            const SlaveRadarWarningsStatus *pSlaveRadar,
                            const ADAS_TimeClase_t timeClase_t)
{
    uint8_t i = 0U;
    uint32_t almStsFlag = 0;
    uint8_t isFctbAvailable = 0; // FCTB功能可用
    float radiusAbs = 999.0f;
    uint8_t prefctbAlarmCnt = 0;

#ifdef PC_DBG_FW
    isFctbAvailable = 1;
#endif
    if(gadasFunctionState.adasFCTBFuncState == (uint8_t)FCTB_FUNC_STATE_ACTIVE)
    {
        isFctbAvailable = 1;
    }

    // FCTAB法规场景检测 
    // 验收情况才会打开
    // ADAS_FCTAB_runDetectionScene(pobjAlm, pobjPath, pVDY, timeClase_t);

    // FCTAB场景时间检测，一定时间内条件放宽，之后把条件收紧
    ADAS_FCTAB_StrictDetectXs(pobjAlm, pobjPath, pVDY, gadasFunctionState, timeClase_t);

    if (pobjAlm->adasRegulationScene.CPTARF_Scene != 1)
    {
        radiusAbs = fabsf(pVDY->pVDY_DynamicInfo->vdyCurveRadius);
    }

    for (i = 0U; i < ADAS_TRK_OBJNUM; i++)
    {
        if (pobjPath[i].status == OBJ_STATUS_VALID)
        {
            if (pobjAlm->adasRegulationScene.CPTARF_Scene != 1)
            {
                // 针对未报警目标做角度限制
                if ((!(pobjPath[i].lastAlarmType & ALARM_ACTIVE_FCTA)) && (radiusAbs < FCTA_MIN_RADIUS_FOR_ADAS))
                {
                    continue;
                }
            }

            if (pobjPath[i].lifeCycle < FCTA_LIFECYCLETHR)
            {
                continue;
            }
            if (ADAS_FCTA_runMain(pobjAlm, pobjPath, i, isFctbAvailable, pVDY) != 0)
            {
                if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA))
                {
                    almStsFlag |= (uint32_t)FCTA_ALM_FLAG_MARK;

                    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTB))
                    {
                        almStsFlag |= (uint32_t)FCTB_ALM_FLAG_MARK;
                    }
                }
            }
        }
        else
        {
            ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTA);
            ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTB);
        }
        
        if (!ADAS_checkBit(pobjPath[i].alarmType, ALARM_ACTIVE_FCTB)){
            pobjPath[i].prefctbAlarmCnt = 0U;
        }else{
            pobjPath[i].prefctbAlarmCnt++;
        } 

        if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
            ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)))
        {
            prefctbAlarmCnt = 1;
        }
        // 起步补偿
        else if ((pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U) &&
                 ((pobjPath[i].avgheadingAngle > 75.0f) && (pobjPath[i].avgheadingAngle < 105.0f)) &&
                 (pobjPath[i].avgVx > 2.5f))
        {
            prefctbAlarmCnt = 1;
        }
        else
        {
            prefctbAlarmCnt = FCTB_PREALARMCNT;
        }

        // 预制动次数从靠近车身开始累计, 跨过车身后开始确认. 
        // 对于制动的判断不用跨过车身, 但是对于实际的制动请求在低速场景下一定要越过车身.
        if ((ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_FCTB) == true) &&
            (ADAS_checkBit(pobjPath[i].alarmType, ALARM_ACTIVE_FCTB) == true) &&
            (gAdasTriggerRecord.isActivatedInXms.name.fctbIsActivatedInXms == 1) &&
            (pobjPath[i].prefctbAlarmCnt >= prefctbAlarmCnt) && (pobjPath[i].fctbTrigCountdown > 0))
        {
            pobjPath[i].prefctbAlarmCnt = 0U; 
            gadasFunctionState.adasFCTBWarning = 1;
            CTB_EDRTargetI = i; // 记录刹车事件发生时的目标id和生命周期
            CTB_EDRTargetLifeCycle = pobjPath[i].lifeCycle;
        }
    }

    #if(ALARM_TYPE_EDR_EN == 1)
    if(gadasFunctionState.adasFCTBWarning)  //功能触发则记录数据
    {
        CTB_EDRTargetLifeCycle = pobjPath[CTB_EDRTargetI].lifeCycle;

        float ttm = ADAS_cacTTM(pobjAlm, pobjPath, CTB_EDRTargetI);
        ADAS_recordEDRData(pobjPath, ttm, CTB_EDRTargetI);
    }
    else if(gadasFunctionState.adasFCTBState)   //已不满足触发条件，但还在刹车退出前，继续记录同一个id的跟踪点
    {
        if((pobjPath[CTB_EDRTargetI].lifeCycle - CTB_EDRTargetLifeCycle) == 1)  //周期连续递增，则认为是同一个目标，否则认为已切id，目标跟丢
        {
            CTB_EDRTargetLifeCycle = pobjPath[CTB_EDRTargetI].lifeCycle;  //更新最后一次记录时目标的生命周期
            float ttm = ADAS_cacTTM(pobjAlm, pobjPath, CTB_EDRTargetI);
            ADAS_recordEDRData(pobjPath, ttm, CTB_EDRTargetI);
        }
        else
        {
            memset(&EDRDataCTB, 0, sizeof(EDRDataCTB));
        }
    }
    else
    {
        memset(&EDRDataCTB, 0, sizeof(EDRDataCTB)); //清楚EDR数据，正常情况下也会周期清楚
    }
    #endif

    return almStsFlag;
}
