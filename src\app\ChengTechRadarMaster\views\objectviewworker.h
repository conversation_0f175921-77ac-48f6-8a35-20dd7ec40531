﻿#ifndef OBJECTVIEWWORKER_H
#define OBJECTVIEWWORKER_H

#include <QObject>

#include "analysis/analysisdata.h"

#include "objectcoordinatesystem.h"

namespace Views {
namespace ObjectView {

class ObjectViewWorker : public QObject
{
    Q_OBJECT
public:
    explicit ObjectViewWorker(QObject *parent = nullptr);

signals:
    void targetImage(quint8 radarID, const QImage &image);

public slots:
    void objectCoordinateSettingsChanged(/*ObjectCoordinateSettings*/void *settings);
    void calculateFinished(quint8 radarID, const AnalysisData &analysisData);
    void calculateTargetFinished(quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &target);

private:
    AnalysisData mAnalysisDatas[MAX_RADAR_COUNT];
    ObjectCoordinateSettings mObjectCoordinateSettings;

    QPixmap mPixmapAlarm;               ///< 雷达报警图片
    QPixmap mPixmapEarlyWarning;          ///< 上位机预警图片

    ObjectData mObjectDatas[MAX_RADAR_COUNT];   ///< 显示目标
};

} // namespace ObjectView
} // namespace Views

#endif // OBJECTVIEWWORKER_H
