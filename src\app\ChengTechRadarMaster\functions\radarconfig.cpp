﻿#include "radarconfig.h"
#include "ui_radarconfig.h"

#include "devices/devicemanager.h"

#include <QtConcurrent>

namespace Functions {

RadarConfig::RadarConfig(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::RadarConfig)
{

}

RadarConfig::RadarConfig(Devices::Can::DeviceManager *deviceManager, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::RadarConfig),
    mDeviceManager(deviceManager)
{
    ui->setupUi(this);
    checkCanId = 0;

    connect(this, &RadarConfig::sigCmdSendData, this, [=](int id, QByteArray data){
        for (int i = 0; i < mDeviceManager->channelCount(); ++i)
        {
            QtConcurrent::run(this, &RadarConfig::sendData, i, id, data);
            QtConcurrent::run(this, &RadarConfig::sendData, i, id, data);
        }
    });
    connect(mDeviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameRecieved , this, [=](const Devices::Can::CanFrame &frame){
        this->slot_Receive_CanData(frame.channelIndex(), frame.id(), frame.data());//读取格式
    });
}

RadarConfig::~RadarConfig()
{
    delete ui;
}

void RadarConfig::slot_Receive_CanData(int CanCH, int id, QByteArray datAry)
{
    if((id & 0xF00) == 0x700 && datAry.length() <= 8)
    {
        qDebug() << __FUNCTION__ << __LINE__ << QString::number(id, 16) << datAry.toHex(' ');
        //qDebug()<<"id == 0x700 ="<<datAry.toHex();
        unsigned char *p = (unsigned char *)(datAry.data());

        if(p[0] == 0x00)
        {
            //报文有问题
            //qDebug()<<"id == error ="<<datAry.toHex();
            return;
        }

        if ((id & 0x0F) != 0)
        {
            switch ((p[0] & 0xF0) >> 4)
            {
            case 1: // 软件版本号
            {
                QString SW = QString("%1.%2.%3.%4.%5.%6").arg(p[1]).arg(p[2]).arg(p[3]).arg(p[4]).arg(p[5]).arg(p[6]);
                ui->label_version->setText(QString("SW Version: %1").arg(SW));
            }
                break;
            case 2: // 硬件版本号
            {
                QString HW = QString("%1.%2.%3.%4").arg(p[1]).arg(p[2]).arg(p[3]).arg(p[4]);
                ui->label_version->setText(ui->label_version->text() + QString("\nHW Version: %1").arg(HW));
            }
                break;
            case 3: // 标定版本号
            {
                QString Cali_Version = QString("%1.%2").arg(p[1]).arg(p[2]);
                ui->label_version->setText(ui->label_version->text() + QString("\nCali Version: %1").arg(Cali_Version));
            }
                break;
            case 4: // Boot版本号
            {
                QString boot = QString("%1.%2.%3.%4").arg(p[1]).arg(p[2]).arg(p[3]).arg(p[4]);
                ui->label_version->setText(ui->label_version->text() + QString("\nBoot Version: %1").arg(boot));
            }
                break;
            case 5:
            {
                QString version = QString("%1.%2.%3_%4-%5-%6").arg(p[1]).arg(p[2]).arg(p[3]).arg(p[4] + 2000).arg(p[5]).arg(p[6]);
                ui->label_version->setText(ui->label_version->text() + QString("\nRelease Version: %1").arg(version));
            }
                break;
            }

            return;
        }
    } else {
        return;
    }

    static int number = 0;
    quint8 * ptr = (quint8*)(datAry.data());
    quint8 code = ptr[0]&0x7F;
    int addr = ui->lineEdit_old_addr->text().toInt();
    //checkCanId = ui->lineEdit_rec_id->text().toInt();
    if(!ui->lineEdit_rec_id->text().isEmpty())
    {
        if(checkCanId == 0)
        {
            QString str = QString("0x%1 %2").arg(id, 3, 16, QLatin1Char('0')).arg(datAry.toHex(' ').data());
            ui->plainTextEdit_log->appendPlainText(str);
            qDebug() << __FUNCTION__ << __LINE__ << checkCanId << str;
        }
    }
    if(id == checkCanId)
    {
        qDebug()<<" id= "<< id <<"  datAry= "<<datAry.toHex();

        ui->lineEdit_rec_data->setText(datAry.toHex());
        ui->label_frame_number->setText(QString::number(number));
        number++;

        switch(code)
        {
            case 0:
        {
            break;
        }
        case 1:
        {
            QString verstr = QString("Version:%1.%2.%3.%4 %5 %6:%7").arg(ptr[1]).arg(ptr[2]).arg(ptr[3]).arg(ptr[4]).arg(ptr[5]).arg(ptr[6]).arg(ptr[7]);
            ui->label_version->setText(verstr);
            break;
        }
        case 2:
        {
            break;
        }
        case 3:
        {
            short angle = ptr[1];
            angle <<= 8;
            angle |= ptr[2];
            float tmp = angle/10.0f;
            ui->lineEdit_fix_angle->setText(QString::number(tmp));
            break;
            break;
        }
        case 4:
        {
            break;
        }
        case 5:
        {
            break;
        }
        case 6:
        {
            break;
        }
        case 7:
        {
            break;
        }
        }

        if(id == (0x500|addr))
        {
            qint16 radius = ptr[0];
            radius <<= 8;
            radius |= ptr[1];

            qint16 yaw = ptr[2];
            yaw <<= 8;
            yaw |= ptr[3];

            QString str = QString("addr=%1 radius=%2 yaw=%3").arg(addr).arg(radius).arg(yaw/100.0);
            ui->plainTextEdit_log->appendPlainText(str);
        }
    }
}

void RadarConfig::sendData(int channelIndex, int id, QByteArray data)
{
    QMutexLocker lock( &mSendDataMutex );
    mDeviceManager->deviceWorker()->sendFrame(channelIndex, id, data);
    qDebug() << __FUNCTION__ << __LINE__;
}

void RadarConfig::on_buttonBox_accepted()
{

}

//进入调试模式
void RadarConfig::CAN_CMD_DebugModeSW(uint8_t sw)
{
    //sw 1--进入debug模式    0---退出debug模式
    if(sw != 0 && sw != 1)
    {
        return;
    }
    int addr = ui->lineEdit_old_addr->text().toInt();
    quint32 id = 0x300 + addr;
    QByteArray Ary ;
    Ary.resize(8);
    Ary.fill(0);
    Ary[0] = 0x31;
    Ary[1] = 0x58;
    Ary[2] = 0xAF;
    if( 0 == sw)
    {
        Ary[3] = 0x00;
    }
    else
    {
        Ary[3] = 0x80;
    }
    Ary[4] = 0x00;
    Ary[5] = 0x00;
    Ary[6] = 0x00;
    Ary[7] = 0x00;
    emit sigCmdSendData(id,Ary);
    emit sigCmdSendData(id,Ary);
    emit sigCmdSendData(id,Ary);
}

void RadarConfig::on_pushButton_clicked()
{
    int id = 0x05B0;
    int addr = ui->lineEdit_old_addr->text().toInt();
    quint8 code = 0;

    QByteArray datAry;
    datAry.resize(8);
    datAry.fill(0 , 8);

    datAry[0] = 0x00|code;
    datAry[1] = 1;
    datAry[2] = 0x16;
    datAry[3] = 0xAC;
    datAry[4] = 0xE9;
    datAry[5] = 0x5B;

    id |= addr;

    setCheckID(id);

    qDebug() << __FUNCTION__ << __LINE__ << QString::number(id, 16) << datAry.toHex(' ');
    emit sigCmdSendData(id , datAry);
}

void RadarConfig::on_pushButton_2_clicked()
{
    int id = 0x05B0;
    int addr = ui->lineEdit_old_addr->text().toInt();
    quint8 newAddr = ui->lineEdit_radarAddr_new->text().toInt();
    quint8 code = 2;

    QByteArray datAry;
    datAry.resize(8);
    datAry.fill(0 , 8);

    datAry[0] = 0x00|code;
    datAry[1] = newAddr;

    id |= addr;

    setCheckID(id);

    emit sigCmdSendData(id , datAry);
}

void RadarConfig::on_pushButton_3_clicked()
{
    int id = 0x05B0;
    int addr = ui->lineEdit_old_addr->text().toInt();
    int spd_mode = ui->comboBox_spd_model->currentIndex();
    quint8 code = 4;

    QByteArray datAry;
    datAry.resize(8);
    datAry.fill(0 , 8);

    datAry[0] = 0x00|code;
    datAry[1] = spd_mode;

    id |= addr;

    setCheckID(id);

    emit sigCmdSendData(id , datAry);
}

void RadarConfig::on_pushButton_4_clicked()
{
    int id = ui->lineEdit_send_id->text().toInt();
    QString datstr = ui->lineEdit_send_data->text();
    QByteArray datAry;
    datAry.resize(8);
    datAry.fill(0 , 8);
    qDebug()<<"datstr org="<<datstr;
    datstr = datstr.replace(" ","");
    qDebug()<<"datstr="<<datstr;
    datAry = QByteArray::fromHex(datstr.toStdString().c_str());

    setCheckID(id);

    emit sigCmdSendData(id , datAry);

}

void RadarConfig::on_pushButton_5_clicked()
{
    qDebug() << __FUNCTION__ << __LINE__;
    CAN_CMD_DebugModeSW(1);

    int id = 0x3E0;
    int addr = ui->lineEdit_old_addr->text().toInt();
    qint16 angle = (qint16)(ui->lineEdit_fix_angle->text().toFloat() * 10);
    id |= addr;
    QByteArray Ary ;
    Ary.resize(8);
    Ary.fill(0);

    //ID -- 雷达id
    Ary[0] = (angle & 0xFF00) >> 8;
    Ary[1] = angle & 0xFF;

    emit sigCmdSendData(id , Ary);
#if 0
    int id = 0x05B0;
    int addr = ui->lineEdit_old_addr->text().toInt();
    qint16 angle = (qint16)(ui->lineEdit_fix_angle->text().toInt());
    quint8 code = 3;

    QByteArray datAry;
    datAry.resize(8);
    datAry.fill(0 , 8);

    datAry[0] = 0x00|code;
    datAry[1] = (angle>>8) & 0x000000FF;
    datAry[2] = angle&0x000000FF;

    id |= addr;

    setCheckID(id);

    emit sigCmdSendData(id , datAry);
#endif
    CAN_CMD_DebugModeSW(0);
}

void RadarConfig::on_pushButton_save_clicked()
{
    int id = 0x05B0;
    int addr = ui->lineEdit_old_addr->text().toInt();
    quint8 code = 0x7F;

    QByteArray datAry;
    datAry.resize(8);
    datAry.fill(0 , 8);

    datAry[0] = 0x00|code;
    datAry[1] = 1;
    datAry[2] = 0x16;
    datAry[3] = 0xAC;
    datAry[4] = 0xE9;
    datAry[5] = 0x50;

    id |= addr;

    setCheckID(id);

    emit sigCmdSendData(id , datAry);
}

void RadarConfig::on_pushButton_getVersion_clicked()
{
    CAN_CMD_DebugModeSW(1);
    int addr = ui->lineEdit_old_addr->text().toInt();
    quint32 id = 0x110 + addr;
    if (addr != 0)
    {
        id = 0x120 + addr;
    }

    QByteArray datAry ;
    datAry.resize(8);
    datAry.fill(0);
    //获取版本号
    datAry[0] = 0xF0;

    setCheckID(0x700 | addr);

    emit sigCmdSendData(id , datAry);
    CAN_CMD_DebugModeSW(0);
}

void RadarConfig::on_lineEdit_rec_id_textChanged(const QString &arg1)
{
    checkCanId = arg1.toInt(0, 16);
//    qDebug() << __FUNCTION__ << __LINE__ << QString::number(checkCanId, 16) << arg1;
}

void RadarConfig::on_pushButton_data_mode_clicked()
{
    int id = 0x05B0;
    int addr = ui->lineEdit_old_addr->text().toInt();
    int mode = ui->comboBox_data_mode->currentIndex();
    quint8 code = 5;

    QByteArray datAry;
    datAry.resize(8);
    datAry.fill(0 , 8);

    datAry[0] = 0x00|code;
    datAry[1] = mode;

    id |= addr;

    setCheckID(id);

    emit sigCmdSendData(id , datAry);
}

void RadarConfig::on_pushButton_6_clicked()
{
    int id = 0x05B0;
    int addr = ui->lineEdit_old_addr->text().toInt();

    //版本
    quint8 code = 0x01;

    QByteArray datAry;
    datAry.resize(8);
    datAry.fill(0 , 8);

    datAry[0] = 0x80|code;

    id |= addr;

    setCheckID(id);

    emit sigCmdSendData(id , datAry);


    //安装角度
    code = 3;
    datAry.resize(8);
    datAry.fill(0 , 8);

    datAry[0] = 0x80|code;

    id |= addr;

    setCheckID(id);

    emit sigCmdSendData(id , datAry);


}

void RadarConfig::setCheckID(int id)
{
    checkCanId = id;
    ui->lineEdit_rec_id->setText(QString::number(checkCanId,16));
}

} // namespace Functions
