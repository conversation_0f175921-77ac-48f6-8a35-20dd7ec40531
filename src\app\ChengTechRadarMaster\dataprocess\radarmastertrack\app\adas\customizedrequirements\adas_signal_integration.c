/**
 * @file adas_signal_integration.c
 * @brief 信号整合模块， 主要负责主从雷达的信号整合、报警等级的整合等功能。
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2023-09-27
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2023-09-27 <td>1.0     <td>shaowei     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifdef ALPSPRO_ADAS
#include "adas/generalalg/adas_manager.h"
#include "adas_signal_integration.h"
#elif defined(PC_DBG_FW)
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_signal_integration.h"
#else
#include "app/adas/generalalg/adas_manager.h"
#include "adas_signal_integration.h"
#endif

/**
 * @brief 根据具体车厂的要求，对DOW进行报警升级
 * 
 * @param pVDY 车身数据
 * @param radarId 雷达id
 */
static void ADAS_confirmDOWAlarmLevel(const VDY_Info_t *pVDY, uint8_t radarId)
{
    uint8_t l_r = (((radarId % 2) == 0) ? BSD_RADAR_LEFT : BSD_RADAR_RIGHT);

    if ((l_r == BSD_RADAR_LEFT && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorFrontLe) != 0U || (pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorRearLe) != 0U)) ||
        (l_r == BSD_RADAR_RIGHT && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorFrontRi) != 0U || (pVDY->pVDY_VehicleFuncSwtInfo->vdyDoorStatus.bit.doorRearRi) != 0U)))
    {
        gadasFunctionState.adasDOWWarning = (uint8_t)ADAS_WARNING_LEVEL2;
    }
}

/**
 * @brief 根据具体车厂的要求，对LCA进行报警升级
 * 
 * @param pVDY 车身数据
 * @param radarId 雷达id
 */
static void ADAS_confirmLCAAlarmLevel(const VDY_Info_t *pVDY, uint8_t radarId)
{
    uint8_t l_r = (((radarId % 2) == 0) ? BSD_RADAR_LEFT : BSD_RADAR_RIGHT);

    //if ((l_r == BSD_RADAR_LEFT && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTurnSignal.bit.turnLightLe) != 0U)) || (l_r == BSD_RADAR_RIGHT && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTurnSignal.bit.turnLightRi) != 0U)))
    if ((l_r == BSD_RADAR_LEFT && ((pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon) == 2U)) || (l_r == BSD_RADAR_RIGHT && ((pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon) == 4U)))
    {
        gadasFunctionState.adasLCAWarning = (uint8_t)ADAS_WARNING_LEVEL2;
    }
}

/**
 * @brief 根据具体车厂的要求，对BSD进行报警升级
 * 
 * @param pVDY 车身数据
 * @param radarId 雷达id
 */
static void ADAS_confirmBSDAlarmLevel(const VDY_Info_t *pVDY, uint8_t radarId)
{
    uint8_t l_r = (((radarId % 2) == 0) ? BSD_RADAR_LEFT : BSD_RADAR_RIGHT);

    //if ((l_r == BSD_RADAR_LEFT && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTurnSignal.bit.turnLightLe) != 0U)) || (l_r == BSD_RADAR_RIGHT && ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTurnSignal.bit.turnLightRi) != 0U)))
    if ((l_r == BSD_RADAR_LEFT && ((pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon) == 2U)) || (l_r == BSD_RADAR_RIGHT && ((pVDY->pVDY_VehicleFuncSwtInfo->turnSignalWorkCon) == 4U)))
    {
        gadasFunctionState.adasBSDWarning = (uint8_t)ADAS_WARNING_LEVEL2;
    }
}

/**
 * @brief 根据具体车厂的要求，对RCW进行报警升级
 * 
 * @param pVDY 车身数据
 * @param radarId 雷达id
 */
static void ADAS_confirmrRCWAlarmLevel(const VDY_Info_t *pVDY, uint8_t radarId)
{
    gadasFunctionState.adasRCWWarning = (uint8_t)ADAS_WARNING_LEVEL1;    // 根据具体车厂定义确认报警等级.
}

/**
 * @brief 根据具体车厂的要求，对RCTA进行报警升级
 * 
 * @param pVDY 车身数据
 * @param radarId 雷达id
 */
static void ADAS_confirmrRCTAAlarmLevel(const VDY_Info_t *pVDY, uint8_t radarId)
{
    gadasFunctionState.adasRCTAWarning = (uint8_t)ADAS_WARNING_LEVEL1;    // 根据具体车厂定义确认报警等级.
}

/**
 * @brief 用于保证报警的最短持续时间
 * 
 * @param lastwarning 上次报警
 * @param thiswarning 当前报警
 * @param type 报警类型
 */
static void ADAS_ensureWarningDuration(uint8_t *lastwarning, uint8_t *thiswarning, uint8_t type)   //报警持续最少1秒
{
    float delaytime = 1.0f;

    if ((type == ADAS_TYPE_FCTB) || (type == ADAS_TYPE_RCTB))     // FCTB最小维持0.3秒 确保报警一定能发出
    {
        delaytime = 0.3f;
    }

    if (type == (uint8_t)ADAS_TYPE_LCA && (gadasFunctionState.adasBSDWarning == (uint8_t)ADAS_WARNING_LEVEL1 || gadasFunctionState.adasBSDWarning == (uint8_t)ADAS_WARNING_LEVEL2))
    {
        ;
    }
    else
    {
        if ((*lastwarning == (uint8_t)ADAS_NO_WARNING) && (*thiswarning != (uint8_t)ADAS_NO_WARNING))
        {
            alarm_last_duration[type] = 0.0f;
        }

        if((*lastwarning != (uint8_t)ADAS_NO_WARNING) && (*thiswarning == (uint8_t)ADAS_NO_WARNING))
        {
            if (alarm_last_duration[type] < delaytime)
            {
                *thiswarning = *lastwarning;
            }
        }
    }
    
}

/**
 * @brief DOW报警结束，目标消失后  继续维持报警一定帧数
 */
static void ADAS_continuWarningDuration(uint8_t *lastwarning, uint8_t *thiswarning, uint8_t type)   //
{
        static uint8_t first = 0;
        // 开始报警时清变量
        if ((*lastwarning == (uint8_t)ADAS_NO_WARNING) && (*thiswarning != (uint8_t)ADAS_NO_WARNING))
        {
            alarm_continue_duration[type] = 0.0f;
            first = 0;
        }

        if((*lastwarning != (uint8_t)ADAS_NO_WARNING) && (*thiswarning == (uint8_t)ADAS_NO_WARNING))
        {
            // 记录退出首帧的特殊时刻
            if (0 == first)
            {
                alarm_continue_duration[type] = 0.0f;
                first = 1;
            }
            if (alarm_continue_duration[type] < DOW_DELAY_EXT_TIME)
            {
                *thiswarning = *lastwarning;
            }
        }
}

/**
 * @brief 根据具体车厂的要求，对不同报警功能设置报警等级
 * 
 * @param pVDY 车身数据
 * @param radarId 雷达id
 */
static void ADAS_confirmAlarmLevel(const VDY_Info_t *pVDY, uint8_t radarId)
{
    if(gadasFunctionState.adasDOWWarning == (uint8_t)ADAS_WARNING_LEVEL1)
    {
        ADAS_confirmDOWAlarmLevel(pVDY, radarId);
    }

    if(gadasFunctionState.adasLCAWarning == (uint8_t)ADAS_WARNING_LEVEL1)
    {
        ADAS_confirmLCAAlarmLevel(pVDY, radarId);
    }

    if(gadasFunctionState.adasBSDWarning == (uint8_t)ADAS_WARNING_LEVEL1)
    {
        ADAS_confirmBSDAlarmLevel(pVDY, radarId);
    }

    if(gadasFunctionState.adasRCWWarning == (uint8_t)ADAS_WARNING_LEVEL1)
    {
        ADAS_confirmrRCWAlarmLevel(pVDY, radarId);
    }

    if(gadasFunctionState.adasRCTAWarning == (uint8_t)ADAS_WARNING_LEVEL1)
    {
        ADAS_confirmrRCTAAlarmLevel(pVDY, radarId);
    }

    ADAS_ensureWarningDuration(&lastFunctionState.adasLCAWarning,&gadasFunctionState.adasLCAWarning, ADAS_TYPE_LCA);
    ADAS_ensureWarningDuration(&lastFunctionState.adasBSDWarning,&gadasFunctionState.adasBSDWarning, ADAS_TYPE_BSD);
    ADAS_ensureWarningDuration(&lastFunctionState.adasDOWWarning,&gadasFunctionState.adasDOWWarning, ADAS_TYPE_DOW);
    ADAS_ensureWarningDuration(&lastFunctionState.adasRCTAWarning,&gadasFunctionState.adasRCTAWarning, ADAS_TYPE_RCTA);
    ADAS_ensureWarningDuration(&lastFunctionState.adasFCTAWarning,&gadasFunctionState.adasFCTAWarning, ADAS_TYPE_FCTA);
    ADAS_ensureWarningDuration(&lastFunctionState.adasRCWWarning,&gadasFunctionState.adasRCWWarning, ADAS_TYPE_RCW);
    ADAS_ensureWarningDuration(&lastFunctionState.adasFCTBWarning,&gadasFunctionState.adasFCTBWarning, ADAS_TYPE_FCTB);
    ADAS_ensureWarningDuration(&lastFunctionState.adasRCTBWarning,&gadasFunctionState.adasRCTBWarning, ADAS_TYPE_RCTB);

    ADAS_continuWarningDuration(&lastFunctionState.adasDOWWarning,&gadasFunctionState.adasDOWWarning, ADAS_TYPE_DOW);
}

/**
 * @brief 根据具体车厂的要求，对左右雷达的信号进行融合，输出主信号
 * 
 * @param pVDY 车身数据
 * @param radarId 雷达id
 */
static void ADAS_mergeSlaveAndMater(const VDY_Info_t *pVDY, uint8_t radarId, const SlaveRadarWarningsStatus *pSlaveRadar)
{
    return;
}

/**
 * @brief 整合算法、车身和主从雷达的信号
 * 
 * @param pVDY 车身数据
 * @param pCFG 配置信息
 * @param pSlaveRadar 从雷达信息
 */
void ADAS_integrateSignals(const VDY_Info_t *pVDY, const ADAS_RadarConfiguration_t *pCFG, const SlaveRadarWarningsStatus *pSlaveRadar)
{
    ADAS_confirmAlarmLevel(pVDY, pCFG->radarId);                //确认报警等级

    ADAS_mergeSlaveAndMater(pVDY, pCFG->radarId, pSlaveRadar);  //主从雷达信号融合
}

/**
 * @brief 短播转向灯提升2.3秒信号维持时间
 *        考虑到会存在连续短拨的情况 策略实现为检测最后一帧短拨信号后 延迟2000ms.
 * @param trackTime 
 */
void ADAS_IncShortLightTime(VDY_vehicleFuncSwt_t *pfunctionSwitch, float trackTime)
{
    static float curtracktime = 0.0f;
    static float rcvshortlighttime = 0.0f;
    curtracktime += trackTime;

    uint8_t l_r = (((gADASRadarId % 2U) == 0) ? BSD_RADAR_LEFT : BSD_RADAR_RIGHT);

    // 接收到短拨信号, 强行维持 2.3 秒
    if ((l_r == BSD_RADAR_LEFT) && (1 == pfunctionSwitch->vdyTurnSignal.bit.turnLightLe))
    {
        rcvshortlighttime = curtracktime;
    }

    if ((l_r == BSD_RADAR_RIGHT) && (1 == pfunctionSwitch->vdyTurnSignal.bit.turnLightRi))
    {
        rcvshortlighttime = curtracktime;
    }

    if ((l_r == BSD_RADAR_LEFT) && ((curtracktime - rcvshortlighttime) <= ADAS_SHORTLIGHT_CONTINUE_TIME))
    {
        pfunctionSwitch->vdyTurnSignal.bit.turnLightLe = 2;     // 短拨信号提升为长拨.
    }
    if ((l_r == BSD_RADAR_RIGHT) && ((curtracktime - rcvshortlighttime) <= ADAS_SHORTLIGHT_CONTINUE_TIME))
    {
        pfunctionSwitch->vdyTurnSignal.bit.turnLightRi = 2;     // 短拨信号提升为长拨.
    }
}