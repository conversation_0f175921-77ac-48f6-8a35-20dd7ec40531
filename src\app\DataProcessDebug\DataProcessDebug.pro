include(../../../chengtech.pri)

QT       += core gui network printsupport

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    ../ChengTechRadarMaster/analysis/analysisdata.cpp \
    ../ChengTechRadarMaster/analysis/analysisworker.cpp \
    ../ChengTechRadarMaster/analysis/calculationworker.cpp \
    ../ChengTechRadarMaster/analysis/protocol/CTMRR410.c \
    ../ChengTechRadarMaster/analysis/protocol/analysisprotocolct.cpp \
    ../ChengTechRadarMaster/analysis/protocol/analysisprotocolct410.cpp \
    ../ChengTechRadarMaster/analysis/protocol/baictargetprotocol.cpp \
    ../ChengTechRadarMaster/analysis/protocol/byd120targetprotocol.cpp \
    ../ChengTechRadarMaster/analysis/protocol/gwmtargetprotocol.cpp \
    ../ChengTechRadarMaster/analysis/protocol/ianalysisprotocol.cpp \
    ../ChengTechRadarMaster/analysis/protocol/truesystemprotocol.cpp \
    ../ChengTechRadarMaster/dataprocess/canfiles/CANDeviceFile.cpp \
    ../ChengTechRadarMaster/dataprocess/canfiles/ctcandevicefile.cpp \
    ../ChengTechRadarMaster/dataprocess/canfiles/devicefiles/devicefileasc.cpp \
    ../ChengTechRadarMaster/dataprocess/canfiles/devicefiles/devicefileblf.cpp \
    ../ChengTechRadarMaster/dataprocess/canfiles/devicefiles/idevicefile.cpp \
    ../ChengTechRadarMaster/dataprocess/dataprocess.cpp \
    ../ChengTechRadarMaster/dataprocess/dataprocessform.cpp \
    ../ChengTechRadarMaster/dataprocess/networktcpclient.cpp \
    ../ChengTechRadarMaster/dataprocess/networktcpserver.cpp \
    ../ChengTechRadarMaster/devices/canframe.cpp \
    ../ChengTechRadarMaster/utils/multiselectcombobox.cpp \
    ../ChengTechRadarMaster/utils/settingshandler.cpp \
    ../ChengTechRadarMaster/utils/utils.cpp \
    ../ChengTechRadarMaster/views/analysisdataview.cpp \
    ../ChengTechRadarMaster/views/analysismodel.cpp \
    ../ChengTechRadarMaster/views/objectcoordinatesystem.cpp \
    ../ChengTechRadarMaster/views/objectcorrdinatesystemconfigdialog.cpp \
    ../ChengTechRadarMaster/views/objectview.cpp \
    ../ChengTechRadarMaster/views/qcustomplot.cpp \
    ../ChengTechRadarMaster/views/targetmonitor.cpp \
    ../ChengTechRadarMaster/views/widget/coordinateaxis.cpp \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    ../ChengTechRadarMaster/analysis/analysisdata.h \
    ../ChengTechRadarMaster/analysis/analysisworker.h \
    ../ChengTechRadarMaster/analysis/calculationworker.h \
    ../ChengTechRadarMaster/analysis/protocol/CTMRR410.h \
    ../ChengTechRadarMaster/analysis/protocol/analysisprotocolct.h \
    ../ChengTechRadarMaster/analysis/protocol/analysisprotocolct410.h \
    ../ChengTechRadarMaster/analysis/protocol/baictargetprotocol.h \
    ../ChengTechRadarMaster/analysis/protocol/byd120targetprotocol.h \
    ../ChengTechRadarMaster/analysis/protocol/gwmtargetprotocol.h \
    ../ChengTechRadarMaster/analysis/protocol/ianalysisprotocol.h \
    ../ChengTechRadarMaster/analysis/protocol/truesystemprotocol.h \
    ../ChengTechRadarMaster/dataprocess/canfiles/CANDeviceFile.h \
    ../ChengTechRadarMaster/dataprocess/canfiles/CANDevicesGlobal.h \
    ../ChengTechRadarMaster/dataprocess/canfiles/ctcandevicefile.h \
    ../ChengTechRadarMaster/dataprocess/canfiles/devicefiles/devicefileasc.h \
    ../ChengTechRadarMaster/dataprocess/canfiles/devicefiles/devicefileblf.h \
    ../ChengTechRadarMaster/dataprocess/canfiles/devicefiles/idevicefile.h \
    ../ChengTechRadarMaster/dataprocess/dataprocess.h \
    ../ChengTechRadarMaster/dataprocess/dataprocess_global.h \
    ../ChengTechRadarMaster/dataprocess/dataprocessform.h \
    ../ChengTechRadarMaster/dataprocess/networktcpclient.h \
    ../ChengTechRadarMaster/dataprocess/networktcpserver.h \
    ../ChengTechRadarMaster/devices/canframe.h \
    ../ChengTechRadarMaster/utils/multiselectcombobox.h \
    ../ChengTechRadarMaster/utils/settingshandler.h \
    ../ChengTechRadarMaster/utils/utils.h \
    ../ChengTechRadarMaster/utils/utils_global.h \
    ../ChengTechRadarMaster/views/analysisdataview.h \
    ../ChengTechRadarMaster/views/analysismodel.h \
    ../ChengTechRadarMaster/views/objectcoordinatesystem.h \
    ../ChengTechRadarMaster/views/objectcorrdinatesystemconfigdialog.h \
    ../ChengTechRadarMaster/views/objectview.h \
    ../ChengTechRadarMaster/views/qcustomplot.h \
    ../ChengTechRadarMaster/views/targetmonitor.h \
    ../ChengTechRadarMaster/views/widget/coordinateaxis.h \
    mainwindow.h

FORMS += \
    ../ChengTechRadarMaster/dataprocess/dataprocessform.ui \
    ../ChengTechRadarMaster/views/analysisdataview.ui \
    ../ChengTechRadarMaster/views/analysisdataviewconfigdialog.ui \
    ../ChengTechRadarMaster/views/objectcorrdinatesystemconfigdialog.ui \
    ../ChengTechRadarMaster/views/objectview.ui \
    ../ChengTechRadarMaster/views/targetmonitor.ui \
    ../ChengTechRadarMaster/views/targetsmonitor.ui \
    mainwindow.ui

# 算法调试宏，DEBUG时需要打开这个宏
DEFINES += ALGORITHM_DEBUG
DEFINES += ALGORITHM_DEBUG_DATA_PROCESS_RAW
DATA_PROCESS_PREFIX_PATH = ../ChengTechRadarMaster/
include(../ChengTEchRadarMaster/dataprocess/dataprocess_code.pri)

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

INCLUDEPATH += \
    ../ChengTechRadarMaster \
    ../ChengTechRadarMaster/dataprocess

LIBS += \
    -L$$MASTER_3RDPARTY_LIBRARY_PATH/CAN/GC_CANFD/lib/x86/ -lECANFDVCI \
    -L$$MASTER_3RDPARTY_LIBRARY_PATH/CAN/ZLG_CAN/lib/x86 -lzlgcan \
    -L$$MASTER_3RDPARTY_LIBRARY_PATH/CAN/TS_CAN/lib/ -lTSMaster \
    -L$$MASTER_3RDPARTY_LIBRARY_PATH/CAN/VECTOR_BLF/lib/x32_Release -lbinlog

DISTFILES += \
    ../ChengTechRadarMaster/dataprocess/dataprocess_code.pri

RESOURCES += \
    ../ChengTechRadarMaster/views/views.qrc
