﻿#ifndef IINTERPOLATION_H
#define IINTERPOLATION_H

#include "devices/canframe.h"
#include "analysis/analysisworker.h"
#include "canfiles/devicefiles/idevicefile.h"
#include "canfiles/devicefiles/devicefileblf.h"
#include "canfiles/devicefiles/devicefileasc.h"

extern "C" {
#include "alg/track/rdp_types.h"
#include "alg/track/rdp_kf_track.h"
}

uint16_t CRC16_CCITT_FALSE(uint8_t *pMsg, uint32_t dataLen);

namespace Analysis {

class IInterpolation
{
public:
    IInterpolation(AnalysisWorker *analysisWorker);

    virtual void canFrame(int radarID, const Devices::Can::CanFrame &frame) = 0;
    virtual int encodeFrame(int radarID, Devices::Can::FrameTimestamp *timestamp, RDP_TrkObjectInfo_t *outputObjList, int16_t trkValidNum, uint8_t msgCounter, Devices::Can::stCanTxMsg *frameArray);
#ifdef ALGORITHM_GEELY
    virtual int encodeFrame(int radarID, Devices::Can::FrameTimestamp *timestamp, RDP_TrkFrameInfoGeely2_0 *outputObjList, int16_t trkValidNum, uint8_t msgCounter, Devices::Can::stCanTxMsg *frameArray);
#endif
    virtual void setChannelRadarIDGEELY(int *channelRadarID, int size);
    virtual void setChannelRadarIDBYDHO(int *channelRadarID, int size);

    void setFiles(const QStringList &files) { mFiles = files; }
    bool start();
    bool stop();
    void setRaw600ByChannel(bool byChannel) { mRaw600ByChannel = byChannel; }
    void injection(int radarID, const Devices::Can::CanFrame &frame);

    AnalysisWorker *analysisWorker() { return mAnalysisWorker; }

protected:
    QStringList mFiles;
    Devices::Can::FrameTimestamps mTimestamps;
    AnalysisWorker *mAnalysisWorker{0};

    Devices::Can::IDeviceFile *mWriter{0};
    Devices::Can::DeviceFileBLF *mWriterBLF{0};
    Devices::Can::DeviceFileASC *mWriterASC{0};
    bool mFirst{true};
    bool mRaw600ByChannel{false};
    quint8 mPointListIsAhead{1};
};

} // namespace Analysis

#endif // IINTERPOLATION_H
