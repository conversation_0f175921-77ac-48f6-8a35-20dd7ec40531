﻿#include "radarresetworker.h"

#include "uds.h"
#include "utils/utils.h"
#include "utils/baicuds/crypto/baciseedtokey.h"
#include "utils/seedtokeybydll.h"

#include <QtConcurrent>
#include <QDebug>


namespace Functions {

RadarResetWorkerWorker::RadarResetWorkerWorker(Devices::Can::DeviceManager *deviceManager, QObject *parent) : QObject(parent)
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        mUDS[i] = new UDS(deviceManager, this);
        mUDS[i]->setResponseID(mResponseAddress[mProtocolIndex][i]);
    }
}

void RadarResetWorkerWorker::protocolChanged(int index)
{
    mProtocolIndex = (ProtocolType)index;
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        mUDS[i]->setResponseID(mResponseAddress[mProtocolIndex][i]);
    }
}

void RadarResetWorkerWorker::stop(bool sda[])
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        if (sda[i])
        {
            QFuture<void> res = QtConcurrent::run(this, &RadarResetWorkerWorker::calibration, i, true);
        }
    }
}

void RadarResetWorkerWorker::start(int channelIndex[], bool sda[])
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        mUDS[i]->setChannelIndex(channelIndex[i]);
        if (sda[i])
        {
            QFuture<void> res = QtConcurrent::run(this, &RadarResetWorkerWorker::calibration, i, false);
        }
    }
}

void RadarResetWorkerWorker::readResult()
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        readResult(i);
    }
}

void RadarResetWorkerWorker::readResult(int index)
{
    QFuture<void> res = QtConcurrent::run(this, &RadarResetWorkerWorker::result, index);
}

bool RadarResetWorkerWorker::seedToKeyByDLL( quint8 index, quint8 level, const QByteArray &seed, QByteArray &key)
{
    QString path;
    switch ( mProtocolIndex ) {
    case ProtocolGWM:
        path = "./secureDLL/GWM_DLL.dll";
        break;
    default:
        break;
    }

    SeedToKeyByDLL dll;
    if( !dll.seedToKey( path, level, seed, key ) ){
        emit message(index, dll.errorMsg() );
    }
    return true;

    /*
    QMutexLocker locker( &mDLLMutex );
    QLibrary library;
    DLL_Seed2Key pDLLFunPtr = NULL;

    QString path;
    switch ( mProtocolIndex ) {
    case ProtocolGWM:
        path = "./secureDLL/GWM_DLL.dll";
        break;
    default:
        break;
    }

    library.setFileName( path );
    if( !library.load() ){
        emit message(index, QString::fromLocal8Bit("加载安全算法库失败！(%1)-(%2)").arg( path ).arg( library.errorString() ) );
        return false;
    }
    const char* funStr = "GenerateKeyEx";
    pDLLFunPtr = (DLL_Seed2Key)library.resolve( funStr );


    if( pDLLFunPtr ){
        unsigned char* iSeedArray = (unsigned char*)seed.data();
        unsigned int  iSize = seed.size();
        unsigned char ioKeyArray[256];
        unsigned int  oSize;
        //0x5754380A  in
        //0x36E91FD6 out
//        iSeedArray[0] = 0xA8;
//        iSeedArray[1] = 0xDA;
//        iSeedArray[2] = 0x5D;
//        iSeedArray[3] = 0x2F;

        pDLLFunPtr( iSeedArray, iSize, level, NULL, ioKeyArray, 256, &oSize );

        key.clear();
        for( int i=0; i<oSize; i++ ){
            key.append( ioKeyArray[i] );
        }

//        qDebug() << __FUNCTION__ << __LINE__
//                 << QByteArray( (const char *)ioKeyArray, sizeof (ioKeyArray)).mid(0,oSize).toHex(' ');
    }else{
        emit message(index, QString::fromLocal8Bit("加载安全算法库失败！(%1)-(%2)").arg( path ).arg( library.errorString() ) );
        return false;
    }

    library.unload();
    return true;*/
}


void RadarResetWorkerWorker::calibration(int index, bool stop)
{
    qDebug() << __FUNCTION__ << __LINE__ << QThread::currentThreadId() << index;
#define SEND_DATA_HEX_RESPONSE(ID, DATA, MESSAGE) \
    responseFrame.clear(); \
    if (!mUDS[index]->sendData(ID, QString(DATA), &responseFrame)) { \
        emit message(index, MESSAGE + mUDS[index]->errorString()); \
        emit calibrationFinished(index); \
        return; \
    }

    UDSFrame responseFrame;

    if (mProtocolIndex == ProtocolBAIC) {
        mUDS[index]->sendData(0x411, QString("00 40 00 00 00 00 00 00"));
        Utils::dely(1000);
        mUDS[index]->sendData(0x411, QString("00 40 00 00 00 00 00 00"));
        Utils::dely(1000);
        mUDS[index]->sendData(0x411, QString("00 40 00 00 00 00 00 00"));
    }

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "10 03", QString::fromLocal8Bit("进入扩展模式失败"));
    emit message(index, QString::fromLocal8Bit("进入扩展模式成功"));

    emit message(index, QString::fromLocal8Bit("请求种子"));
    if (mProtocolIndex == ProtocolGEELY) {
        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "27 05", QString::fromLocal8Bit("请求种子失败")); // 请求种子
    }else{
        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "27 01", QString::fromLocal8Bit("请求种子失败")); // 请求种子
    }
    emit message(index, QString::fromLocal8Bit("请求种子成功"));
    uint32_t seed{0};
    memcpy(&seed, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);
    //    qDebug() << __FUNCTION__ << __LINE__ << mMASK << QString::number(mMASK);
    if (mProtocolIndex == ProtocolBAIC) {
        uint8_t key[16];
        BAICSendToKey(seed, UDSFactor, key);
        qDebug() << __FUNCTION__ << __LINE__ << QString::number(seed, 16);
        qDebug() << __FUNCTION__ << __LINE__ << QByteArray((const char *)key, sizeof (key)).toHex(' ');
        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
    } else if (mProtocolIndex == ProtocolGEELY) {
        quint8 geely_seed[3];
        quint8 key[3];
        memcpy(geely_seed, (responseFrame.mData.mid(2, 3)).data(), 3);

        // seed:35 c7 ce  key:07 f4 de
        //            geely_seed[0] = 0x35;
        //            geely_seed[1] = 0xc7;
        //            geely_seed[2] = 0xce;

        GeelySeedToKey( geely_seed, key );
        //            qDebug() << __FUNCTION__ << __LINE__ << QString::number(geely_seed[0], 16)<< QString::number(geely_seed[1], 16)<< QString::number(geely_seed[2], 16);
        //            qDebug() << __FUNCTION__ << __LINE__ << QByteArray((const char *)key, sizeof (key)).toHex(' ');
        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 06 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
    } else if (mProtocolIndex == ProtocolGWM) {
        QByteArray key;
        seedToKeyByDLL( index, 0x01, responseFrame.mData.mid(2, 4), key );

        //            quint32 gwm_seed;
        //            quint8 key[4];
        //            memcpy(&gwm_seed, (responseFrame.mData.mid(2, 4)).data(), 4);
        //            gwm_seed = 0xA8DA5D2F;
        //            GwmSeedToKey( gwm_seed, key );

        //            qDebug() << __FUNCTION__ << __LINE__ << QString::number(geely_seed[0], 16)<< QString::number(geely_seed[1], 16)<< QString::number(geely_seed[2], 16);
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.mid(2, 4).toHex(' ')
                 << QByteArray((const char *)key, sizeof (key)).toHex(' ');
        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
    }else {
        uint32_t key = SeedToKey(seed, mMASK[mProtocolIndex][index]);
        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(key, 8, 16, QLatin1Char('0')), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
    }
    emit message(index, QString::fromLocal8Bit("密钥验证成功"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "11 01", QString::fromLocal8Bit("复位失败"));
    emit message(index, QString::fromLocal8Bit("复位成功"));

//    emit calibrationStarted(index);
}

void RadarResetWorkerWorker::result_GWM(int index)
{
    UDSFrame responseFrame;
    responseFrame.clear();
    QString data = "22 FC 11";
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取校准结果失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    bool bSuccess = false;
    bool bFail = false;

    emit message(index, QString::fromLocal8Bit("读取校准结果成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
    quint8 process = responseFrame.mData[4];
    switch( (responseFrame.mData[3] & 0xF) ){
    case 0x00:
        emit message(index, QString::fromLocal8Bit("校准未开始") );
        break;
    case 0x01:
        emit message(index, QString::fromLocal8Bit("校准正在执行 【%1%】").arg(process));
        break;
    case 0x02:
        emit message(index, QString::fromLocal8Bit("校准完成") );
        bSuccess = true;
        break;
    case 0x03:
        emit message(index, QString::fromLocal8Bit("校准失败") );
        bFail = true;
        break;
    default:
        emit message(index, QString::fromLocal8Bit("未知校准状态") );
        bFail = true;
        break;
    }

    if( bSuccess || bFail ){
        emit calibrationFinished(index);
    }

    //bSuccess = true;
    if( bSuccess ){
        //读取角度
        responseFrame.clear();
        data = "22 FC 01";
        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame)) {
            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
            emit message(index, QString::fromLocal8Bit("读取校准角度失败") + mUDS[index]->errorString());
            return;
        }
        quint8 stat = responseFrame.mData[3];
        quint16 _angle = (quint16)((((quint16)responseFrame.mData[4]) << 8) + (quint8)responseFrame.mData[8]);
        double angle = _angle * 0.01;
        QString msg = "";
        switch ( stat ) {
        case 0:
            msg = QString::fromLocal8Bit("雷达位置偏向不确定");
            break;
        case 1:
            msg = QString::fromLocal8Bit("左边雷达位置相对车辆推进线向左偏或右边雷达位置相对车辆推进线向右偏");
            break;
        case 2:
            msg = QString::fromLocal8Bit("右边雷达位置相对车辆推进线向左偏或左边雷达位置相对车辆推进线向右偏");
            break;
        default:
            msg = QString::fromLocal8Bit("未知角度状态");
            break;
        }
        emit message(index, QString::fromLocal8Bit("%1 [angle=%2]").arg(msg).arg(angle) );

        //清除故障码
//        responseFrame.clear();
//        data = "14 FF FF FF";
//        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame)) {
//            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
//            emit message(index, QString::fromLocal8Bit("清除故障码失败") + mUDS[index]->errorString());
//            return;
//        }

//        //读取故障码
//        responseFrame.clear();
//        data = "19 0A";
//        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame )) {
//            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
//            emit message(index, QString::fromLocal8Bit("读取故障码失败") + mUDS[index]->errorString());
//            return;
//        }

//        QByteArray dtcArray = responseFrame.mData.mid( 3 );
//        if( dtcArray.size() > 0 ){
//            emit message(index, QString::fromLocal8Bit("故障码读取结果:") );
//        }
//        for( int i=0; i<dtcArray.size(); i+=4 ){
//            QByteArray dtc = dtcArray.mid( i, 4 );
//            quint8* pData = (quint8*)dtc.data();
//            if( pData[3] != 0 ){
//                emit message(index, dtc.toHex(' ') );
//            }
//        }
    }
}

void RadarResetWorkerWorker::result(int index)
{
    QMutexLocker lock( &mMutex[index] ); //此函数不可重入

    if( mProtocolIndex == ProtocolGWM ){
        return result_GWM( index );
    }

    UDSFrame responseFrame;
    responseFrame.clear();

    QString data = "";
    switch ( mProtocolIndex ) {
    case ProtocolBAIC:
        data = "31 03 12 7B";
        break;
    case ProtocolGEELY:
        data = "31 03 DC 01";
        break;
    default:
        data = "31 03 03 02";
        break;
    }

    //if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString((mProtocolIndex == ProtocolBAIC) ? "31 03 12 7B" : "31 03 03 02"), &responseFrame)) {
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取校准结果失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("读取校准结果成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
    quint8 process = responseFrame.mData[6];
    if ((responseFrame.mData[4] & 0xF) == 0x01)
    {
        emit message(index, QString::fromLocal8Bit("校准正在执行 【%1%】").arg(process));
        return;
    }

//    emit message(index, responseFrame.mData.toHex(' '));
    QString text;
    switch (responseFrame.mData[4] & 0xF)
    {
    case 0x0:
        text.append(QString::fromLocal8Bit("【校准不在执行】"));
        break;
    case 0x1:
        text.append(QString::fromLocal8Bit("【校准正在执行】"));
        break;
    case 0x2:
        text.append(QString::fromLocal8Bit("【校准写入NVM失败】"));
        break;
    case 0x3:
        text.append(QString::fromLocal8Bit("【校准执行超时】"));
        break;
    case 0x4:
        text.append(QString::fromLocal8Bit("【校准正确执行完毕】"));
        break;
    case 0x5:
        text.append(QString::fromLocal8Bit("【校准执行中止】"));
        break;
    }
    switch (responseFrame.mData[4] & 0xF0)
    {
    case 0x00:
        text.append(QString::fromLocal8Bit("【校准结果未出】"));
        break;
    case 0x10:
        text.append(QString::fromLocal8Bit("【校准结果不正确】"));
        break;
    case 0x20:
        text.append(QString::fromLocal8Bit("【校准结果正确】"));
        break;
    }

    text.append(QString::fromLocal8Bit("【"));
    if (responseFrame.mData[5] & 0x1)
    {
        text.append(QString::fromLocal8Bit("速度过慢"));
    }
    if (responseFrame.mData[5] & 0x2)
    {
        text.append(QString::fromLocal8Bit("速度过快"));
    }
    if (responseFrame.mData[5] & 0x4)
    {
        text.append(QString::fromLocal8Bit("横摆角过大"));
    }
    if (responseFrame.mData[5] & 0x8)
    {
        text.append(QString::fromLocal8Bit("加速过快"));
    }
    if (responseFrame.mData[5] & 0x10)
    {
        text.append(QString::fromLocal8Bit("目标不充分"));
    }
    if (responseFrame.mData[5] & 0x20)
    {
        text.append(QString::fromLocal8Bit("雷达失明"));
    }
    text.append(QString::fromLocal8Bit("】"));

    responseFrame.mData[6];

    double angle = 0.0;
    quint16 _angle = (quint16)((((quint16)responseFrame.mData[7]) << 8) + (quint8)responseFrame.mData[8]);
    if (_angle < 0x8000)
    {
        angle = _angle * 0.01;
    }
    else
    {
        angle = (_angle - 0xFFFF) * 0.01;
    }
    text.append(QString::fromLocal8Bit("【水平偏差角度 %1°】").arg(angle));
    angle = 0.0;
    _angle = (quint16)((((quint16)responseFrame.mData[11]) << 8) + (quint8)responseFrame.mData[12]);


    if (mProtocolIndex == ProtocolBAIC) {
        angle = _angle * 0.01;
        text.append(QString::fromLocal8Bit("【安装角度 %1°】").arg(angle));
        _angle = (quint16)((((quint16)responseFrame.mData[13]) << 8) + (quint8)responseFrame.mData[14]);
        text.append(QString::fromLocal8Bit("采集的目标总数 %1°】").arg(angle));
        _angle = (quint16)((((quint16)responseFrame.mData[15]) << 8) + (quint8)responseFrame.mData[16]);
        text.append(QString::fromLocal8Bit("运行流程记录 %1°】").arg(angle));
    }else{
        angle = _angle * 0.1;
        text.append(QString::fromLocal8Bit("【安装角度 %1°】").arg(angle));
    }

    emit message(index, text);

    if (responseFrame.mData.data()[4] != 0x24)
    {
        return;
    }

    emit calibrationFinished(index);
}

void RadarResetWorkerWorker::canFrame(const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        if (frame.id() == mUDS[i]->responseID())
        {
            mUDS[i]->appendCanFrame(frame);
            emit sendOrRecvCanFrame( i, false, frame.id(), frame.dataHex() );
            break;
        }
    }
}

void RadarResetWorkerWorker::frameTransmited(const Devices::Can::CanFrame &frame, bool success)
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        if ( frame.id() == mPhysicalAddress[mProtocolIndex][i] || frame.id() == mFunctionAddress[mProtocolIndex][i] ){
            emit sendOrRecvCanFrame( i, true, frame.id(), frame.dataHex() );
            break;
        }
    }
}



} // namespace Functions
