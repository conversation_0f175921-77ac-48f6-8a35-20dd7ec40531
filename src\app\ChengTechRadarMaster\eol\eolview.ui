<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EOLView</class>
 <widget class="QWidget" name="EOLView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1013</width>
    <height>746</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QWidget" name="topWidget" native="true">
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="title">
          <string>模式选择：</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>模式：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="modeComboBox">
           <item>
            <property name="text">
             <string>正常运行</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>生产-普通</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>生产-调试</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="selModePushButton">
           <property name="text">
            <string>进入模式</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="rebootPushButton">
           <property name="text">
            <string>重启雷达</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="clearMsgPushButton">
           <property name="text">
            <string>清除消息</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="clearFramePushButton">
           <property name="text">
            <string> 清除报文</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QTabWidget" name="tabWidget">
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="tableTab">
          <attribute name="title">
           <string>Tab 1</string>
          </attribute>
          <widget class="QWidget" name="layoutWidget">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>10</y>
             <width>781</width>
             <height>30</height>
            </rect>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QLineEdit" name="tableFileLineEdit"/>
            </item>
            <item>
             <widget class="QPushButton" name="selFilePushButton">
              <property name="text">
               <string>选择文件</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="loadFilePushButton">
              <property name="text">
               <string>加载文件</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="writeTablePushButton">
              <property name="text">
               <string> 表写入</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="layoutWidget">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>50</y>
             <width>781</width>
             <height>30</height>
            </rect>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <widget class="QLabel" name="label_2">
              <property name="text">
               <string>表类型：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="tableClassComboBox">
              <item>
               <property name="text">
                <string>方位导向矢量表@ELE+0deg</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>俯仰导向矢量表@AZI+0deg</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>天线间距坐标与初相信息表</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>方向图表</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>俯仰导向矢量表@AZI-45deg</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>俯仰导向矢量表@AZI+45deg</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>通道底噪表</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_3">
              <property name="text">
               <string> profile_id:</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="profileIDComboBox">
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="readTablePushButton">
              <property name="text">
               <string>表读取</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_3">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </widget>
         <widget class="QWidget" name="otherTab">
          <attribute name="title">
           <string>Tab 2</string>
          </attribute>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="downWidget" native="true">
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <widget class="QTextBrowser" name="msgTextBrowser">
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>500</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTextBrowser" name="frameTextBrowser">
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>500</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
