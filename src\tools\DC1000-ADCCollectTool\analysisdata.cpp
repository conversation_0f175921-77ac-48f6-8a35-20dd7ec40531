﻿#include "analysisdata.h"

#include <QtMath>
#include <QDebug>
#include <QThread>

AnalysisFrameType gAnalysisFrameType(const QString &name)
{
     return (AnalysisFrameType)AnalysisFrameTypeNames.indexOf(name);
}

QString gAnalysisFrameTypeNames(AnalysisFrameType type)
{
    return (type >= 0 && type < AnalysisFrameTypeNames.size()) ? AnalysisFrameTypeNames[type] : "Unknow";
}

AnalysisType gAnalysisType(const QString &name)
{
    for (int i = 0; i < AnalysisTypeNames.size(); ++i)
    {
        if (name == AnalysisTypeNames[i].mName)
        {
            return (AnalysisType)i;
        }
    }
    return UnknowAnalysisType;
}

QString gAnalysisTypeName(AnalysisType type)
{
    return (type >= 0 & type < AnalysisTypeNames.size()) ? AnalysisTypeNames[type].mName : "Unknow";
}

QString gAnalysisTypeUnit(AnalysisType type)
{
    return (type >= 0 & type < AnalysisTypeNames.size()) ? AnalysisTypeNames[type].mUnit : "";
}

QStringList gAnalysisTypeNames(int pos, int length)
{
    QStringList names;
    foreach (const AnalysisTypeName &name, AnalysisTypeNames.mid(pos, length))
    {
        names << name.mName;
    }

    return names;
}

void Target::print() const
{
    for (int i = TARGET_TYPE_BEGIN; i < TARGET_TYPE_END; ++i)
    {
        qDebug() << __FUNCTION__ << __LINE__ << gAnalysisTypeName((AnalysisType)i) << value((AnalysisType)i);
    }
}

void Targets::clear()
{
    memset(this, 0, sizeof (Targets));
}

void Targets::print() const
{
    for (int i = 0; i < mTargetCount; ++i)
    {
        if (mTargets[i].mValid)
        {
            mTargets[i].print();
        }
    }
}

quint16 AlarmData::alarmTypes()
{
    quint16 alarmTypes = 0;
    if (mAlarmBSDLevel) {
        alarmTypes |= Alarm_BSD;
    }
    if (mAlarmLCALevel) {
        alarmTypes |= Alarm_LCA;
    }
    if (mAlarmRCWLevel) {
        alarmTypes |= Alarm_RCW;
    }
    if (mAlarmDOWFLevel) {
        alarmTypes |= Alarm_DOWF;
    }
    if (mAlarmDOWRLevel) {
        alarmTypes |= Alarm_DOWR;
    }
    if (mAlarmRCTALevel) {
        alarmTypes |= Alarm_RCTA;
    }
    if (mAlarmRCTBLevel) {
        alarmTypes |= Alarm_RCTB;
    }
    if (mAlarmFCTALevel) {
        alarmTypes |= Alarm_FCTA;
    }
    if (mAlarmFCTBLevel) {
        alarmTypes |= Alarm_FCTB;
    }
    if (mAlarmJALevel) {
        alarmTypes |= Alarm_JA;
    }

    return alarmTypes;
}

void AlarmData::clear()
{
    memset(this, 0, sizeof (AlarmData));
}

void AlarmData::print() const
{
    for (int i = ALARM_TYPE_BEGIN; i < ALARM_TYPE_END; ++i)
    {
        qDebug() << __FUNCTION__ << __LINE__ << gAnalysisTypeName((AnalysisType)i) << value((AnalysisType)i);
    }
}

void VehicleData::clear()
{
    memset(this, 0, sizeof (VehicleData));
}

void VehicleData::print() const
{
    for (int i = VEHICLE_TYPE_BEGIN; i < VEHICLE_TYPE_END; ++i)
    {
        qDebug() << __FUNCTION__ << __LINE__ << gAnalysisTypeName((AnalysisType)i) << value((AnalysisType)i);
    }
}

AnalysisData::AnalysisData()
{

}

void AnalysisData::calculateTarget(AnalysisFrameType frameType)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frameType << mRadarID;
    Targets &targets = mTargets[frameType];
    Target *target = targets.mTargets;
    double vehicleSpeed = mVehicleData.mVehicleSpeed / 3.6;
    if (FrameRawTarget == frameType) {
        switch ((int)mVehicleData.mGear)
        {
        case 1: // P
            break;
        case 2: // R
            vehicleSpeed = -vehicleSpeed;
            break;
        case 3: // N
            break;
        case 4: // D
        default:
            break;
        }

        for (int j = 0; j < targets.mTargetCount; ++j, ++target) {
            if (target->mValid) {

                if (!(target->mRange > 0.0))
                {
//                    qDebug() << __FUNCTION__ << __LINE__ << "Raw Target Range error!" << target->mID << target->mRange;
                    target->mValid = false;
                    continue;
                }

                //速度解模糊的原始点不显示
//                if(((uint8_t)target->mStatus & 0x10) == 0x10)
//                {
//                    qDebug() << __FUNCTION__ << __LINE__ << "Raw Target Range error!" << target->mID << target->mRange <<target->mStatus;
//                    target->mValid = false;
//                    continue;
//                }

//                target->mX = target->mRange * qSin(target->mAngle * M_PI / 180);
//                target->mY = target->mRange * qCos(target->mAngle * M_PI / 180);
//                //安装角度
//                target->mX = target->mX * qCos(mEndFrameData.mSelfCalibrationEstablishedAngle * M_PI / 180)
//                        + target->mY * qSin(mEndFrameData.mSelfCalibrationEstablishedAngle * M_PI / 180);
//                target->mY = target->mY * qCos(mEndFrameData.mSelfCalibrationEstablishedAngle * M_PI / 180)
//                        - target->mX * qSin(mEndFrameData.mSelfCalibrationEstablishedAngle * M_PI / 180);
#if 0
                double angle = target->mAngle + mEndFrameData.mSelfCalibrationEstablishedAngle + mAngleCompensation.mAngleCompensationRaw;
                target->mX = target->mRange * qSin( ( angle)* M_PI / 180);
                target->mY = target->mRange * qCos( ( angle)* M_PI / 180);
#else
                double angle = target->mAngle + mEndFrameData.mEndOfLineEstablishedAngle + mAngleCompensation.mAngleCompensationRaw;
                target->mX = target->mRange * qSin( ( angle)* M_PI / 180);
                target->mY = target->mRange * qCos( ( angle)* M_PI / 180);
#endif

                double vehicleV = vehicleSpeed * qCos((angle) * M_PI / 180);

                target->mVsog = (mRadarID == 6 || mRadarID == 7) ? (vehicleV + target->mV) : (vehicleV - target->mV);
                if (qFabs(target->mVsog) < 1.6) {  // 前向加，后向减
                    target->mDynamicProperty = 0x0; // 静止
                }
                else if (target->mV < 0) {
                    target->mDynamicProperty = 0x1; // 运动
                }
                else if (target->mV > 0) {
                    target->mDynamicProperty = 0x1; // 运动
                }

                targets.mValid = true;
            }
        }
    }
    else if (FrameTrackTarget == frameType) {
        double c = qCos(mAngleCompensation.mAngleCompensationTrack / 180 * M_PI);
        double s = qSin(mAngleCompensation.mAngleCompensationTrack / 180 * M_PI);
        for (int j = 0; j < targets.mTargetCount; ++j, ++target) {
            if (target->mValid) {
                target->mX -= targets.mTargetHeader.mOffsetToSideX;
                target->mY -= targets.mTargetHeader.mOffsetToSideY;
                if (mAngleCompensation.mAngleCompensationTrack != 0.0) {
                    double tempX = target->mX * c - target->mY * s;
                    target->mY = target->mX * s + target->mY * c;
                    target->mX = tempX;

                    tempX = target->mTrackFrameX * c - target->mTrackFrameY * s;
                    target->mTrackFrameY = target->mTrackFrameX * s + target->mTrackFrameY * c;
                    target->mTrackFrameX = tempX;
                    target->mTrackFrameAngle -= mAngleCompensation.mAngleCompensationTrack;
                }
                target->mRange = qSqrt(qPow(target->mX, 2) + qPow(target->mY, 2));
                target->mV = qSqrt(qPow(target->mVx, 2) + qPow(target->mVy, 2));
                target->mVsog = qSqrt(qPow(target->mVx, 2) + qPow((mRadarID == 6 || mRadarID == 7) ? (vehicleSpeed + target->mVy) : (vehicleSpeed - target->mVy), 2));
                if (target->mRange != 0) {
                    target->mAngle = qAtan2(target->mX, target->mY) * 180 / M_PI;
                } else {
                    target->mValid = false;
                    continue;
                    qDebug() << __FUNCTION__ << __LINE__ << "Track Target Range error! Range ="<< target->mRange << target->mID ;
                }

                targets.mValid = true;
            }
        }
    }

    mEndFrameData.mValid = true;//|= targets.mValid;
}

void AnalysisData::calculate(bool assigned)
{
    for (int i = 0; i < FrameTargetCount; ++i)
    {
        calculateTarget((AnalysisFrameType)i);
    }
}

void AnalysisData::clearTarget(AnalysisFrameType frameType)
{
    Targets *targets = 0;
    switch (frameType) {
    case FrameRawTarget:
    case FrameTrackTarget:
        targets = &mTargets[frameType];
        break;
    default:
        break;
    }
    if (!targets)
    {
        return;
    }

    memset(targets, 0, sizeof (Targets));
}

void AnalysisData::clear()
{
    memset(&mTargets, 0, sizeof (mTargets));
    memset( mTrueTarget, 0, sizeof( mTrueTarget ) );
//    memset( &m16Targets, 0, sizeof( m16Targets ) );
//    memset( &mLockTargets, 0, sizeof( mLockTargets ) );
//    memset( &mELKTargets, 0, sizeof( mELKTargets ) );
}

void AnalysisData::clearRecv410FrameCount()
{
    mEndFrameData.mInit410FrameCountFlag = true;
    mEndFrameData.mRecv410FrameCount = 0;
    mEndFrameData.mDiff410FrameCount = 0;
    mEndFrameData.mDiff410FrameCountRate = 0.0;
    //mEndFrameData.mLostFrameFlag = false;
    mEndFrameData.mPreLostFrameFlag = false;
//    qDebug() << __FUNCTION__ << __LINE__ <<QThread::currentThreadId();
}

void AnalysisData::print() const
{
    qDebug() << __FUNCTION__ << __LINE__ << "====================================================";
    qDebug() << __FUNCTION__ << __LINE__ << "mEndRrameIntervalTime : " << mEndFrameData.mRadarRollingCount;

    for (int i = 0; i < FrameTargetCount; ++i)
    {
        if (i == 0)
            qDebug() << __FUNCTION__ << __LINE__ << "------------------------RAW-------------------------";
        else
            qDebug() << __FUNCTION__ << __LINE__ << "-----------------------TRACK------------------------";

        mTargets[i].print();
    }
    mAlarmData.print();
    mVehicleData.print();
}
