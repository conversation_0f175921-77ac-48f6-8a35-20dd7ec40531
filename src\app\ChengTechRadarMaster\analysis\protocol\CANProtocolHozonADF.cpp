﻿#include "CANProtocolHozonADF.h"

#define _USE_MATH_DEFINES //需要放在math前,之后才可以使用M_PI等match定义参数
#include <math.h>
#include <iostream>

namespace Analysis {

namespace Protocol {
        CANProtocolHozonADF::CANProtocolHozonADF(AnalysisWorker *analysisWorker, QObject *parent)
            : IAnalysisProtocol(analysisWorker, parent)
        {
            mProtocolType = ProtocolHonzonADF;
		}
        CANProtocolHozonADF::~CANProtocolHozonADF()
		{
		}
        bool CANProtocolHozonADF::analysisFrame(const Devices::Can::CanFrame &frame)
        {
            if (frame.length() != 64) {
                return false;
            }

            bool ret = false;
            switch (frame.id())
            {
            case 0x300:
                ret = parseHeaderTrack(frame, 4);
                break;
            case 0x301:
            case 0x302:
            case 0x303:
            case 0x304:
            case 0x305:
            case 0x306:
            case 0x307:
                if (mBreakShort) {
                    ret = parseTargetTrack(frame, 4);
                    break;
                }
            case 0x308:
                if (mBreakShort) {
                    ret = parseTargetTrack(frame, 4);
                    if (ret) {
                        analysisEndADF(4);
                    }
                    break;
                }
            case 0x360:
            case 0x361:
            case 0x362:
            case 0x363:
            case 0x364:
            case 0x365:
            case 0x366:
                ret = parseTargetTrack(frame, 4);
                break;
            case 0x367:
                ret = parseTargetTrack(frame, 4);
                if (ret) {
                    analysisEndADF(4);
                }
                break;
            case 0x310:
                ret = parseHeaderTrack(frame, 5);
                break;
            case 0x311:
            case 0x312:
            case 0x313:
            case 0x314:
            case 0x315:
            case 0x316:
            case 0x317:
                if (mBreakShort) {
                    ret = parseTargetTrack(frame, 5);
                    break;
                }
            case 0x318:
                if (mBreakShort) {
                    ret = parseTargetTrack(frame, 5);
                    if (ret) {
                        analysisEndADF(5);
                    }
                    break;
                }
            case 0x378:
            case 0x370:
            case 0x371:
            case 0x372:
            case 0x373:
            case 0x374:
            case 0x375:
            case 0x376:
                ret = parseTargetTrack(frame, 5);
                break;
            case 0x377:
                ret = parseTargetTrack(frame, 5);
                if (ret) {
                    analysisEndADF(5);
                }
                break;
            case 0x350:
                ret = parseHeaderTrack(frame, 6);
                break;
            case 0x351:
            case 0x352:
            case 0x353:
            case 0x354:
            case 0x355:
            case 0x356:
            case 0x357:
                if (mBreakShort) {
                    ret = parseTargetTrack(frame, 6);
                    break;
                }
            case 0x358:
                if (mBreakShort) {
                    ret = parseTargetTrack(frame, 6);
                    if (ret) {
                        analysisEndADF(6);
                    }
                    break;
                }
            case 0x3A0:
            case 0x3A1:
            case 0x3A2:
            case 0x3A3:
            case 0x3A4:
            case 0x3A5:
            case 0x3A6:
                ret = parseTargetTrack(frame, 6);
                break;
            case 0x3A7:
                ret = parseTargetTrack(frame, 6);
                if (ret) {
                    analysisEndADF(6);
                }
                break;
            case 0x330:
                ret = parseHeaderTrack(frame, 7);
                break;
            case 0x331:
            case 0x332:
            case 0x333:
            case 0x334:
            case 0x335:
            case 0x336:
            case 0x337:
                if (mBreakShort) {
                    ret = parseTargetTrack(frame, 7);
                    break;
                }
            case 0x338:
                if (mBreakShort) {
                    ret = parseTargetTrack(frame, 7);
                    if (ret) {
                        analysisEndADF(7);
                    }
                    break;
                }
            case 0x380:
            case 0x381:
            case 0x382:
            case 0x383:
            case 0x384:
            case 0x385:
            case 0x386:
                ret = parseTargetTrack(frame, 7);
                break;
            case 0x387:
                ret = parseTargetTrack(frame, 7);
                if (ret) {
                    analysisEndADF(7);
                }
                break;
            default:
                return false;
            }

//            frame.print(__FUNCTION__, __LINE__);

            return ret;
        }

        bool CANProtocolHozonADF::parseHeaderTrack(const Devices::Can::CanFrame &frame, int radarID)
        {
            if (frame.length() != 64) {
                return false;
            }
            const uint8_t *data = (const uint8_t *)frame.data().data();

            Targets &targets = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets;
            targets.clear();
            TargetHeader &header = targets.mTargetHeader;

//            target.SRR_FL_TargetTI_Status = (data[0]);
//            target.SRR_FL_OBJTi_Stamp = (((data[4] & 0xFFU) + (((uint32_t)data[3]) << 8) + (((uint32_t)data[2]) << 16) + (((uint32_t)data[1] & 0xFFU) << 24)) * 0.1);
            header.mMeasurementCount = (((data[8] & 0xFFU) + (((uint32_t)data[7]) << 8) + (((uint32_t)data[6]) << 16) + (((uint32_t)data[5] & 0xFFU) << 24)) * 0.1);
            header.mTargetCount = (data[9]);
//            target.Radar_Status = ((data[10] & 0xF0U) >> 4);
//            target.Radar_HighVoltError = ((data[11] & 0x80U) >> 7);
//            target.Radar_LowVoltError = ((data[11] & 0x40U) >> 6);
//            target.Radar_BusOffError = ((data[11] & 0x20U) >> 5);
//            target.Radar_HorizontalAngleError = ((data[11] & 0x10U) >> 4);
//            target.Radar_BlockageError = ((data[11] & 0x8U) >> 3);
//            target.Radar_HighTempError = ((data[11] & 0x4U) >> 2);
//            target.Radar_HWError = ((data[11] & 0x2U) >> 1);
//            target.Radar_SWError = (data[11] & 0x1U);
//            target.Radar_ALNfailError = ((data[12] & 0x80U) >> 7);
//            target.Radar_NotALNError = ((data[12] & 0x40U) >> 6);
//            target.Radar_InstallPositionError = ((data[12] & 0x20U) >> 5);
//            target.Radar_AutoALNAngleOffset = (((data[13]) * 0.1) - 12.8);
//            target.Radar_AutoALNStatus = (data[14]);
//            target.MsgCounter = (data[61] & 0xFU);
//            target.CRC16Checksum = ((data[63] & 0xFFU) + (((uint16_t)data[62] & 0xFFU) << 8));

            return true;
        }

        bool CANProtocolHozonADF::parseTargetTrack(const Devices::Can::CanFrame &frame, int radarID)
        {
            if (frame.length() != 64) {
                return false;
            }

            const uint8_t *data = (const uint8_t *)frame.data().data();

            Targets &targets = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets;

            int &targetCount = targets.mTargetCount; // 注意必须使用引用

            if (targetCount < targets.mTargetHeader.mTargetCount) {
                quint8 id = (data[0]);
                if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
                    qDebug() << __FUNCTION__ << __LINE__ << "Track target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
                } else if (id != 0xFF) {

                    Target &target = targets.mTargets[targetCount++];

                    target.mValid = true;
                    target.mID = (data[0]);
                    target.mExistProbability = (((data[1] & 0xFCU) >> 2) * 0.015625);
                    target.mSNR = ((((data[3] & 0x80U) >> 7) + (((uint32_t)data[2]) << 1) + (((uint32_t)data[1] & 0x3U) << 9)) * 0.0625);
                    target.mY = (((((data[4] & 0xF8U) >> 3) + (((uint16_t)data[3] & 0x7FU) << 5)) * 0.0625) - 128);
                    target.mVysog = (((((data[6] & 0x80U) >> 7) + (((uint32_t)data[5]) << 1) + (((uint32_t)data[4] & 0x7U) << 9)) * 0.0625) - 128);
                    target.mX = (((((data[7] & 0xFEU) >> 1) + (((uint16_t)data[6] & 0x7FU) << 7)) * 0.015625) - 128);
                    target.mVxsog = (((((data[9] & 0xE0U) >> 5) + (((uint32_t)data[8]) << 3) + (((uint32_t)data[7] & 0x1U) << 11)) * 0.0625) - 128);
                    target.mAy = (((((data[10] & 0xFEU) >> 1) + (((uint16_t)data[9] & 0x1FU) << 7)) * 0.03125) - 16);
                    target.mAx = (((((data[12] & 0xE0U) >> 5) + (((uint32_t)data[11]) << 3) + (((uint32_t)data[10] & 0x1U) << 11)) * 0.03125) - 16);
                    target.mPitchAngle = (((((data[13] & 0xFEU) >> 1) + (((uint16_t)data[12] & 0x1FU) << 7)) * 0.1) - 180);
                    target.mTrackFrameY = ((((data[14] & 0xFFU) + (((uint16_t)data[13] & 0x1U) << 8)) * 0.1) - 25);
                    target.mTrackFrameX = (((((data[16] & 0x80U) >> 7) + (((uint16_t)data[15] & 0xFFU) << 1)) * 0.1) - 25);
                    target.mTrackFrameLength = ((((data[17] & 0xC0U) >> 6) + (((uint16_t)data[16] & 0x7FU) << 2)) * 0.1);
                    target.mTrackFrameWidth = ((((data[18] & 0xE0U) >> 5) + (((uint16_t)data[17] & 0x3FU) << 3)) * 0.1);
                    target.mXStd = ((((data[19] & 0xE0U) >> 5) + (((uint16_t)data[18] & 0x1FU) << 3)) * 0.05);
                    target.mYStd = ((((data[20] & 0xE0U) >> 5) + (((uint16_t)data[19] & 0x1FU) << 3)) * 0.05);
                    target.mVyStd = ((((data[21] & 0x80U) >> 7) + (((uint16_t)data[20] & 0x1FU) << 1)) * 0.05);
                    target.mVxStd = (((data[21] & 0x7EU) >> 1) * 0.05);
                    target.mObjectType = ((data[22] & 0xE0U) >> 5);
                    target.mDynamicProperty = ((data[22] & 0x1CU) >> 2); // 需要适配

                    target.mAyStd = ((((data[46] & 0xE0U) >> 5) + (((uint16_t)data[45] & 0x3U) << 3)) * 0.375);
                    target.mAxStd = ((data[46] & 0x1FU) * 0.375);
                    target.mTrackLifeCycleCnt = (data[47]);
                    target.mRCS = (((((data[49] & 0x80U) >> 7) + (((uint16_t)data[48] & 0xFFU) << 1)) * 0.5) - 128);
                    target.mStatus = ((data[49] & 0x70U) >> 4);


//                    qDebug() << __FUNCTION__ << __LINE__ << radarID << target.mID << target.mX << target.mY << target.mV << frame.idHex() << frame.dataHex();

                }
            }

            if (targetCount < targets.mTargetHeader.mTargetCount) {
                quint8 id = (data[23]);
                if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
                    qDebug() << __FUNCTION__ << __LINE__ << "Track target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
                } else if (id != 0xFF){
                    Target &target = targets.mTargets[targetCount++];

                    target.mValid = true;
                    target.mID = (data[23]);
                    target.mExistProbability = (((data[24] & 0xFCU) >> 2) * 0.015625);
                    target.mSNR = ((((data[26] & 0x80U) >> 7) + (((uint32_t)data[25]) << 1) + (((uint32_t)data[24] & 0x3U) << 9)) * 0.0625);
                    target.mY = (((((data[27] & 0xF8U) >> 3) + (((uint16_t)data[26] & 0x7FU) << 5)) * 0.0625) - 128);
                    target.mVysog = (((((data[29] & 0x80U) >> 7) + (((uint32_t)data[28]) << 1) + (((uint32_t)data[27] & 0x7U) << 9)) * 0.0625) - 128);
                    target.mX = (((((data[30] & 0xFEU) >> 1) + (((uint16_t)data[29] & 0x7FU) << 7)) * 0.015625) - 128);
                    target.mVxsog = (((((data[32] & 0xE0U) >> 5) + (((uint32_t)data[31]) << 3) + (((uint32_t)data[30] & 0x1U) << 11)) * 0.0625) - 128);
                    target.mAy = (((((data[33] & 0xFEU) >> 1) + (((uint16_t)data[32] & 0x1FU) << 7)) * 0.03125) - 16);
                    target.mAx = (((((data[35] & 0xE0U) >> 5) + (((uint32_t)data[34]) << 3) + (((uint32_t)data[33] & 0x1U) << 11)) * 0.03125) - 16);
                    target.mPitchAngle = (((((data[36] & 0xFEU) >> 1) + (((uint16_t)data[35] & 0x1FU) << 7)) * 0.1) - 180);
                    target.mTrackFrameY = ((((data[37] & 0xFFU) + (((uint16_t)data[36] & 0x1U) << 8)) * 0.1) - 25);
                    target.mTrackFrameX = (((((data[39] & 0x80U) >> 7) + (((uint16_t)data[38] & 0xFFU) << 1)) * 0.1) - 25);
                    target.mTrackFrameLength = ((((data[40] & 0xC0U) >> 6) + (((uint16_t)data[39] & 0x7FU) << 2)) * 0.1);
                    target.mTrackFrameWidth = ((((data[41] & 0xE0U) >> 5) + (((uint16_t)data[40] & 0x3FU) << 3)) * 0.1);
                    target.mXStd = ((((data[42] & 0xE0U) >> 5) + (((uint16_t)data[41] & 0x1FU) << 3)) * 0.05);
                    target.mYStd = ((((data[43] & 0xE0U) >> 5) + (((uint16_t)data[42] & 0x1FU) << 3)) * 0.05);
                    target.mVyStd = ((((data[44] & 0x80U) >> 7) + (((uint16_t)data[43] & 0x1FU) << 1)) * 0.05);
                    target.mVxStd = (((data[44] & 0x7EU) >> 1) * 0.05);
                    target.mObjectType = ((data[45] & 0xE0U) >> 5);
                    target.mDynamicProperty = ((data[45] & 0x1CU) >> 2); // 需要适配

                    target.mStatus = ((data[49] & 0xEU) >> 1);
                    target.mAyStd = (((data[50] & 0xF8U) >> 3) * 0.375);
                    target.mAxStd = (((data[51] & 0xF8U) >> 3) * 0.375);
                    target.mTrackLifeCycleCnt = (data[52]);
                    target.mRCS = (((((data[54] & 0x80U) >> 7) + (((uint16_t)data[53] & 0xFFU) << 1)) * 0.5) - 128);
//                    target.SRR_FRObj1MsgCounter = (data[61] & 0xFU);
//                    target.SRR_FRObj1CRC16Checksum = ((data[63] & 0xFFU) + (((uint16_t)data[62] & 0xFFU) << 8));

//                    qDebug() << __FUNCTION__ << __LINE__ << radarID << target.mID << target.mX << target.mY << frame.idHex() << frame.dataHex();
                }
            }

//            target.SRR_FLObj1MsgCounter = (data[61] & 0xFU);
//            target.SRR_FLObj1CRC16Checksum = ((data[63] & 0xFFU) + (((uint16_t)data[62] & 0xFFU) << 8));

            return true;
        }

        void CANProtocolHozonADF::analysisEndADF(int radarID)
        {

            Targets &targets = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets;
            targets.mValid = true;
            mAnalysisWorker->analysisEnd(radarID, Frame16Track);

//            qDebug() << __FUNCTION__ << __LINE__ << radarID << targets.mTargetCount;
        }
	}
}
