{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/libpng-x86-windows-1.6.43-933c316f-c44c-4c78-9379-213b423b6ed5", "name": "libpng:x86-windows@1.6.43 74a59d475d3149a6d08bc29dce0bd3f696a68008b30ef6cc4d49f973631f5dcf", "creationInfo": {"creators": ["Tool: vcpkg-7d353e869753e5609a1f1a057df3db8fd356e49d"], "created": "2024-05-28T03:04:07Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-5"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-6"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-7"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-8"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-6", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-7", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-8", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "libpng", "SPDXID": "SPDXRef-port", "versionInfo": "1.6.43", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/libpng", "homepage": "https://github.com/glennrp/libpng", "licenseConcluded": "libpng-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "libpng is a library implementing an interface for reading and writing PNG (Portable Network Graphics) format files", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "libpng:x86-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "74a59d475d3149a6d08bc29dce0bd3f696a68008b30ef6cc4d49f973631f5dcf", "downloadLocation": "NONE", "licenseConcluded": "libpng-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-1", "name": "glennrp/libpng", "downloadLocation": "git+https://github.com/glennrp/libpng@v1.6.43", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "3bb2a7b73113be42b09c2116e6c6f5a7ddb4e2ab06e0b13e10b7314acdccc4bb624ff602e16140c0484f6cde80efa190296226be3da195c6926819f07c723c12"}]}, {"SPDXID": "SPDXRef-resource-2", "name": "${LIBPNG_APNG_PATCH_NAME}.gz", "packageFileName": "${LIBPNG_APNG_PATCH_NAME}.gz", "downloadLocation": "https://downloads.sourceforge.net/project/libpng-apng/libpng16/${VERSION}/${LIBPNG_APNG_PATCH_NAME}.gz", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "a724f7de486920cb119818f7172fb589bc2c3c1cc1f81bb5c4da0609ab108ef9ef7406cf689a20bc4e8da69647847f550ed497b3fa99a10539e9a0abf492c053"}]}], "files": [{"fileName": "./D:/Src/vcpkg-master/ports/libpng/cmake.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "7a28556789f7c6b070c782b70f472ad7f668a2565b7f08ac149c16e7fddbdf56"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/libpng/fix-export-targets.patch", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "1142083e86ba780d5bf3e2a2bc5be7a99fe5ffdc043c42a18c1ed2f19b4b4aa3"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/libpng/fix-msa-support-for-mips.patch", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "e4fa568271fbd5dd73ea63b80766fb1791c375a0732eee086bd8e3dc1d8f82b0"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/libpng/libm.patch", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "cc8c2ab1e241a6c951eb46ed59a95aacb8e86c1c2280e0727e06f459bacc570c"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/libpng/pkgconfig.patch", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "0477ba7a169a2f4d61b604c1a4b488228eebec608a8c298ad883c43283a3d3df"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/libpng/portfile.cmake", "SPDXID": "SPDXRef-file-5", "checksums": [{"algorithm": "SHA256", "checksumValue": "16581a3dd5e29d325c78b8073e87cf6725447e395b3696c057636653715717b8"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/libpng/usage", "SPDXID": "SPDXRef-file-6", "checksums": [{"algorithm": "SHA256", "checksumValue": "9671e4ff1a6a1da38797a5c34912b8e5d71bcd582f678bec7a7f2fbb89fdef1a"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/libpng/vcpkg-cmake-wrapper.cmake", "SPDXID": "SPDXRef-file-7", "checksums": [{"algorithm": "SHA256", "checksumValue": "4267e69abf185f2228a4cc012e3fe87d5de21a187c75950b5a7615f41c3f6371"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/libpng/vcpkg.json", "SPDXID": "SPDXRef-file-8", "checksums": [{"algorithm": "SHA256", "checksumValue": "06489fdd30fb7b2ebb8a39131524a00e9e581065d31bc430e469328271308b91"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}