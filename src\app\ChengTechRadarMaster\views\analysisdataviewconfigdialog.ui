<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AnalysisDataViewConfigDialog</class>
 <widget class="QDialog" name="AnalysisDataViewConfigDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1105</width>
    <height>583</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Analysis Data View Config</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QComboBox" name="comboBoxFrameType"/>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxContinuousDisplay">
       <property name="text">
        <string>连续显示</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxMovingOnly">
       <property name="text">
        <string>只显示运动目标</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <widget class="QListWidget" name="listWidgetTargetAttributes"/>
     <widget class="QWidget" name="layoutWidget">
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QListWidget" name="listWidgetTargetAttributeDisplay"/>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonMoveUp">
           <property name="text">
            <string>上移</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonMoveDown">
           <property name="text">
            <string>下移</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOK">
       <property name="text">
        <string>确定</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonApply">
       <property name="text">
        <string>应用</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>pushButtonCancel</sender>
   <signal>clicked()</signal>
   <receiver>AnalysisDataViewConfigDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>1057</x>
     <y>563</y>
    </hint>
    <hint type="destinationlabel">
     <x>1098</x>
     <y>579</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
