<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ChannelAndFrameMonitorForm</class>
 <widget class="QWidget" name="ChannelAndFrameMonitorForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>868</width>
    <height>471</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="font">
        <font>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>最后一帧接收时间：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelLastRecvTimer">
       <property name="font">
        <font>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>00:00:00:000</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTreeView" name="monitorTreeView"/>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="addChannelPushButton">
       <property name="text">
        <string>添加通道监控</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="addFramePushButton">
       <property name="text">
        <string>添加帧监控</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="delPushButton">
       <property name="text">
        <string>删除监控</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="savePushButton">
       <property name="text">
        <string>保存监控参数</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QPushButton" name="startPushButton">
     <property name="text">
      <string>开始</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
