﻿#ifndef DEVICEFILEASC_H
#define DEVICEFILEASC_H

#include <fstream>

#include "CANFrame.h"

class DeviceFileASC
{
public:
    explicit DeviceFileASC();

    bool openFile(std::string filename, bool in =false);
    bool closeFile();
    bool writeData(const CANFrame &frame);
    bool readData(CANFrame &frame);

private:
    bool writeHeader(uint64_t timestamp);
    bool readDataVersion_7(const std::string &line, CANFrame &frame);
    bool readDataVersion_12(const std::string &line, CANFrame &frame);
    bool parseHeader();
    bool parseDate(const std::string &line);
    bool parseVersion(const std::string &line);

    std::fstream mInFileStream;

    uint32_t mMajorVersion{ 0 };
    uint32_t mMinorVersion{ 0 };
    uint32_t mPatchVersion{ 0 };

    std::string mFilename;
    std::string mErrorString;
    uint64_t mSaveTimestamp;			///< 文件保存时间戳（微秒级）

    bool mOpenInMode{ false };
};

#endif // DEVICEFILEASC_H
