﻿/**
 * @file     rdp_interface.h
 * @brief    This file defines the interfaces for RDP module.
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2023-01-10
 *
 *
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2022-12-24  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _RDP_INTERFACE_H_
#define _RDP_INTERFACE_H_

/****************************************************************************
  INCLUDE
 ****************************************************************************/
 #ifndef PC_DBG_FW
#include "rsp_types.h"
#include "vdy_types.h"
#include "rdp_types.h"
#include "rdp_track_struct.h"
#include "trk_cfg.h"
#include "os_hal.h"
#else
#include "hal/rsp/rsp_types.h"
#include "alg/track/rdp_track_struct.h"
#include "app/vehicle/vdy/vdy_types.h"
#endif

/****************************************************************************
  DEFINE
 ****************************************************************************/
#if CUSTOMER_PROTOCOL == CUSTOMER_PROTOCOL_GEELY20_15
#define OUTPUT_OBJ_NUM_MAX      15U
#else
#define OUTPUT_OBJ_NUM_MAX      16U
#endif

typedef struct 
{
    uint32_t tick;              ///RDP 获取16个目标的时刻记录
    uint32_t counter;           ///RDP 发出16个目标的周期计数
    uint64_t rspTimestamp;      // RSP 回波时间戳
    uint64_t lastTimestamp;     // RDP 上一次回波+差值的时刻记录
    // uint64_t interTimestamp;    // 实际插值时间戳
}RDP_TARGET16_TICK_T;

#define SystemTickCount (uint32_t)xTaskGetTickCount()

#ifndef M_PI
#define M_PI 3.1415926f
#endif

#define PI 3.1415926535f

#define RAD2ANG                  57.2957795131


/*****************************************************************************
  DECLARATION
 *****************************************************************************/
/* From rdp_data_process.c */
void RDP_setInstallAngle(float angle);
void RDP_setRadarInstallPosition(uint32_t radarId);
void RDP_setRadarInstallOffset(float x, float y);
int32_t RDP_getMountPosOffsetToRearAxleCenter(float *offsetTorearAxleCenterLgt, float *offsetTorearAxleCenterLat);
void RDP_setRadarUpsidedown(uint8_t upsideDown);
void RDP_vehicleInfoCfgInit();
int32_t RDP_setTrkObjectNumForTrkDbgMode(uint16_t trkObjNum);
int32_t RDP_setTrkObjectForTrkDbgMode(uint16_t objIndex, RDP_TrkObjectInfo_t *trkObjInfo);
int32_t RDP_setSubFramesDopplerVelocityScope(uint8_t subFrameNum, float* rangeRateScope);
void RDP_runMainFunction(const uint8_t radarId, const RSP_DetObjectList_t *RSP_DetObjectListAddr, const VDY_DynamicEstimate_t *freezedVehDyncDataAddr,
                         const float installAzimuthAngle, float time);

RDP_DebugInfo_t* RDP_getDebugDataPtr();
int32_t RDP_getUserTrackInfo(const VDY_DynamicEstimate_t *pRDP_inVehicleData, RDP_TrkObjectInfo_t *pRDP_TrkObject, int16_t* num, int16_t nearTrackId[4]);
rdp_config_t* RDP_getTrackConfigPointer(void);
trk_pkg_t *RDP_getTrackTargetsPointer(void);
const RSP_DetObjectList_t *RDP_getDetObjectListPointer(void);
RDP_TrkObjectList_t *RDP_getTrkObjectListPointer(void);

void RDP_UpdateGeelyObj(trk_pkg_t *pTrackList ,const VDY_DynamicEstimate_t *pVdy, RDP_TrkFrameInfoGeely2_0 *pGeelyTrkObj, const RSP_DetObjectList_t *pDetRawData);

/* From rdp_kf_track.c */
cdi_pkg_t *RDP_getBKTargetsListPointer();

/**
 * @brief 获取RDP冻结16个目标的时刻
 * @return RDP_TARGET16_TICK_T 
 */
const RDP_TARGET16_TICK_T* RDP_GetTarget16Tick(void);
const RDP_TrkFrameInfoGeely2_0* RDP_GetGeely20Target(void);

/**
 * @brief 设置RDP冻结16个目标的时刻
 * @param tick 
 */
void RDP_SetTarget16Tick(const RSP_DetObjectList_t *RSP_DetObjectListAddr, uint32_t tick);

/* From rdp_data_process.c */
RDP_TrkObjectInfo_t* RDP_getBYDOutputObjList(int16_t *validNum);
uint8_t RDP_getBlockFlag(void);
uint8_t RDP_getBlockPercent(void);
uint8_t RDP_getIntFlag(void);
uint8_t RDP_getIntPercent(void);
void RDP_trkInterpolation(float deltaT);

#endif
