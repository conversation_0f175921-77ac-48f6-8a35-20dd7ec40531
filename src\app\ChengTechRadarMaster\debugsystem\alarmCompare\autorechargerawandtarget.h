#ifndef AUTORECHARGERAWANDTARGET_H
#define AUTORECHARGERAWANDTARGET_H

#include "devices/canframe.h"
#include "analysis/analysisdata.h"

#include <QObject>
#include <QMap>
#include <QQueue>
#include <QMutex>
#include <QFile>

namespace Devices {
namespace Can {
class CanFrame;
class IDeviceWorker;
class DeviceManager;
}
}

namespace Analysis {
class AnalysisWorker;
};

class IFrameFileParse;


class AutoRechargeRawAndTarget : public QObject
{
    Q_OBJECT
public:
    enum TYPE{
        RECHARGET_TYPE_NORMAL = 0,
        RECHARGET_TYPE_RAW,
        RECHARGET_TYPE_TARGET
    };

private:
    struct RadarInfo{
        quint8 channel{0};  //通道
        int waitTime{0};    //等待时间
        quint64 waitRawNO{0}; //等待ID
        quint64 lastOnLineTime{0}; //最后在线时间
        QQueue<Devices::Can::CanFrame> frameQueue; //待发送帧队列
    };

public:
    explicit AutoRechargeRawAndTarget(QObject *parent = nullptr);
    ~AutoRechargeRawAndTarget();
    void setRechargeFiles( const QString& file );
    //void setDeviceWorker( Devices::Can::IDeviceWorker* pDeviceWorker );
    void setDeviceManager( Devices::Can::DeviceManager* pDeviceMgr );
    void setAnalysisWorker( Analysis::AnalysisWorker* pAnalysisWorker );
    void setType( TYPE type );

    void clearRadarInfo();
    void setRadarChannel( quint8 radarID, quint8 channel );

    void run();
    void stop();

signals:
    void begin( const QString& file );
    void end( const QString& file );

private slots:
    void receiveEndFrame( quint8 radarID, const AnalysisData &analysisData );


private:
    bool rechargeOneFrame();
    bool loadFrame();
    void enterHilMode( TYPE type );
    void radarPause( quint8 radarID, quint64 rawNO );
    void wait( quint32 ms );
    void init();
    bool onLine( quint8 radarID ); //判断是否在线

private:
    //Devices::Can::IDeviceWorker* mDeviceWorker{NULL};
    Devices::Can::DeviceManager* mDeviceMgr{NULL};
    Analysis::AnalysisWorker* mAnalysisWorker{NULL};

    QString mFileName;
    IFrameFileParse* mBinaryParse{NULL};
    TYPE mType{ RECHARGET_TYPE_NORMAL };

    QMutex mMutex;
    QMap< quint8, RadarInfo > mRadarInfo;

    bool mRun{false};

//    QMap< quint8, int > mRadarPauseTimeMap;
//    QMap< quint8, quint64 > mRadarLastTimeMap; //雷达上一次响应时间，用于判断是否失联
//    QMap< quint8, quint8 > mRadarChannelMap;
//    QMap< quint8, QQueue<Devices::Can::CanFrame> > mRadarFrameMap;

    QFile mDebugFile;
};

#endif // AUTORECHARGERAWANDTARGET_H
