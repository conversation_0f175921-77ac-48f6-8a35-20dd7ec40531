﻿#include "mainwindow.h"
#include "ui_mainwindow.h"

#include "dataprocess/dataprocess.h"

#include <QDebug>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);

    connect(DataProcess::instance(), &DataProcess::message, this, [=](const QString &str){
        ui->plainTextEdit->appendPlainText(str);
    });
}

MainWindow::~MainWindow()
{
    delete ui;
}

bool MainWindow::init(quint8 masterRadarID, quint8 sloveRadarID)
{
    this->setWindowTitle(QString::fromLocal8Bit("主雷达 %1 / 从雷达 %2").arg(masterRadarID).arg(sloveRadarID));
    return DataProcess::instance()->init(false, masterRadarID, sloveRadarID);
}

void MainWindow::on_pushButtonConnectMasterRadar_clicked()
{
    DataProcess::instance()->connectMasterRadar();
}
