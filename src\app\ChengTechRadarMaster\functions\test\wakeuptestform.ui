<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WakeUpTestForm</class>
 <widget class="QWidget" name="WakeUpTestForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>868</width>
    <height>586</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2" stretch="0,3,7,0">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="font">
        <font>
         <pointsize>13</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>提示：所有雷达VCAN接通道0，后角雷达PCAN接通道1，前角雷达PCAN接通道2！</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <item>
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>VCAN发送文件：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="lineEditVCanLog"/>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonSelectVCanLog">
         <property name="text">
          <string>选择</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_6">
       <item>
        <spacer name="horizontalSpacer_5">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QCheckBox" name="checkBoxRadar4">
         <property name="text">
          <string>雷达4</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QCheckBox" name="checkBoxRadar5">
         <property name="text">
          <string>雷达5</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QCheckBox" name="checkBoxRadar6">
         <property name="text">
          <string>雷达6</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QCheckBox" name="checkBoxRadar7">
         <property name="text">
          <string>雷达7</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_4">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>APP若</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSpinBox" name="spinBoxAppResponseTime"/>
       </item>
       <item>
        <widget class="QLabel" name="label_7">
         <property name="text">
          <string>秒未响应，则判断为未进入APP</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label_5">
         <property name="text">
          <string>若PCAN</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSpinBox" name="spinBoxPCanAliveInterval"/>
       </item>
       <item>
        <widget class="QLabel" name="label_8">
         <property name="text">
          <string>秒内无数据，则判断为休眠状态，否则为非休眠状态</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>VCAN Log持续发送</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSpinBox" name="spinBoxVCanLogSendTime"/>
       </item>
       <item>
        <widget class="QLabel" name="label_6">
         <property name="text">
          <string>秒</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label_10">
         <property name="text">
          <string>等待休眠</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSpinBox" name="spinBoxWaitSleepTime"/>
       </item>
       <item>
        <widget class="QLabel" name="label_9">
         <property name="text">
          <string>秒</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label_12">
         <property name="text">
          <string>等待唤醒</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSpinBox" name="spinBoxWaitAliveTime"/>
       </item>
       <item>
        <widget class="QLabel" name="label_11">
         <property name="text">
          <string>秒</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QPushButton" name="pushButtonStart">
         <property name="text">
          <string>开始</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonStop">
         <property name="text">
          <string>结束</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="labelTestResult">
     <property name="font">
      <font>
       <pointsize>35</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>INIT</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <widget class="QTextBrowser" name="textBrowserFrame"/>
     </item>
     <item>
      <widget class="QTextBrowser" name="textBrowserMsg"/>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonClearMsg">
     <property name="text">
      <string>清空消息</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
