﻿#ifndef PLAYBACKNETWORKWORKER_H
#define PLAYBACKNETWORKWORKER_H

#include <QObject>
#include <QMutex>
#include <QFile>

class QTcpServer;
class QTcpSocket;
class QTimer;

typedef struct RadarCfgParams
{
    unsigned int numOfTcChannel;        // 内部计算获取 一般是2
    unsigned int rngNfft;               // range FFT点数
    unsigned int velNfft;               // velocity FFT点数
    unsigned int adcSampleStart;        // chirp采样的起始时间
    unsigned int adcFreq;               // ADC采样率，单位MSPS，支持20,25,40,50  20MSPS
    unsigned int decFactor;             // ADC的抽取滤波器的系数，取值范围0~16
    unsigned int antiVelambEn;          // chirp延时方式的速度解模糊，Baseband集成的，false-禁能，true-使能 FALSE
                                        // 四个元素依次代表Tx0,Tx1,Tx2,Tx3,每个元素由四个半字节组成，
                                        // 不同位置的半字节为1时表示MIMO时当前位置的Tx在发射过程中的次序
    QList<unsigned int> txGroups;       // 例如：0x0000,0x0000,0x0001,0x0000
    unsigned int rxMask;                // 默认是4
}RadarCfgParams;

typedef struct __HIL_CONFIG_PARAMS__ {
    uint32_t chirpNmb; //velNfft * numOfTcChannel 根据sensorCfgParams计算 不需要输入
    uint32_t chirpLen; //1600
    uint32_t skipNmb;  //adcSampleStart * adc_freq * dec_factor 必须偶数 根据sensorCfgParams计算 不需要输入
    uint32_t sampleNmb; //rngNfft sensorCfgParams
    uint32_t packageNum; //一帧分多少帧发送
    uint32_t frameSize; // 一帧数据大小
    bool isValid;
}HilCfgParams;

class PlaybackNetworkWorker : public QObject
{
    Q_OBJECT
public:
    enum WorkMode
    {
        PlaybackMode,      // 回放
        RechargeMode,      // 回灌(HIL)
        CollectMode        // 采集
    };

    enum PlayState
    {
        Playing,
        PlayStoped,
        PlayError
    };

    enum TCPClientDataState
    {
        NoneData,
        ReadDCKVersion,
        SetProfileConfig
    };

    explicit PlaybackNetworkWorker(QObject *parent = nullptr);
    ~PlaybackNetworkWorker();

    bool isPlaying() const { return mPlayState == Playing; }
    bool clientConnected() const { return mClientConnected; }
    bool parseProfileConfigs(const QStringList &files);
    bool init(/*WorkMode*/int workMode, const QString &filename, int oneFrameProfileCount);
    bool profileConfigsLoaded() const { return mProfileConfigsLoaded; }
    int profileSize() const { return mProfileConfigParams.size(); }
    int frameCount() const { return mFrameCount; }
    int profileFrameCount() const { return mFrameCount * mOneFrameProfileCount; }
    void setSingleStep(bool single) { mSingleStep = single; }
    void setCollectFrameCount(int count);
    void setDAQFile(const QString &filename) { mADCFilename = filename; }

signals:
    void tcpServerOpened(bool opened, const QString &message = "");
    void tcpServerClosed();

    void tcpClientConnected(const QString &IP, quint16 port);
    void tcpClientDisonnected();

    void dckVersion(const QString &version);

    void initResult(bool ok);
    void playState(/*PlayState*/int state, const QString &message = "");
    void playFrameInfo(int index, int total, const QString &message = "");

    void write(const QByteArray &data);

public slots:
    void openTCPServer(const QString &IP, quint16 port, bool analyIP = false);
    void closeTCPServer();

    void start(int startFrameIndex, int periodicTime, bool singleStep = false);
    void stop();
    void nextFrame();
    void pause(bool paused);

private slots:
    void connectNewTCPClient();
    void disconnectedTCPClient();
    void readTCPClientData();
    void writeData(const QByteArray &data);

    void play();

private:
    void radeDCKVersion();
    void radeDCKVersion1();
    void fillHilFuncTcpCfgProtocolPackage();

    PlayState mPlayState{PlayStoped};

    WorkMode mWorkMode{RechargeMode};
    TCPClientDataState mTCPClientDataState{NoneData};
    QTcpServer *mTcpServer{0};
    QTcpSocket *mTcpClient{0};
    QMutex mReadMutex;
    QMutex mFileMutex;
    QFile mFile;
    QTimer *mPlayTimer{0};

    bool mClientConnected{false};

    QString mADCFilename;
    QList<RadarCfgParams> mProfileConfigParams;
    QList<HilCfgParams> mHilCfgParams;
    bool mProfileConfigsLoaded{false};
    bool mSingleStep{false};
    unsigned int mOneFrameProfileCount{1};
    unsigned int mPeriodicTime{50};
    unsigned int mFrameCount{0};
    unsigned int mCurrentFrameIndex{0};

    qint64 mFileSize{0};
    qint64 mOneReadFileSize{1024};
    char *mReadFileData{0};
    qint32 mADCPackageIndex{0};
    qint32 mADCPackageCount{0};

    qint32 mAllProfilePackageCount{0};
    QList<qint32> mProfilePackageCount;

    QByteArray mADCPackageData;
};

#endif // PLAYBACKNETWORKWORKER_H
