﻿#ifndef TRUESYSTEMPROTOCOL_H
#define TRUESYSTEM_H

#include "ianalysisprotocol.h"

namespace Analysis {
namespace Protocol {

class TrueSystemProtocol : public IAnalysisProtocol
{
    Q_OBJECT
public:
    /** @brief 原点设置 */
    /**********************************************************
     * LF----YLF-----YRF----RF
     *  |         |         |
     * XLF        |        XRF
     *  |         |         |
     *  __________O__________
     *  |         |         |
     * XRF        |        XRR
     *  |         |         |
     * RF----YRF-----YRR----RR
     **********************************************************/
    typedef struct OriginSettings {
        float mXLF{4.85f};            ///< 左前雷达纵向距离
        float mXRF{4.85f};
        float mXLR{0.0f};
        float mXRR{0.0f};

        float mYLF{0.975f};            ///< 左前雷达横向距离
        float mYRF{0.975f};
        float mYLR{0.975f};
        float mYRR{0.975f};
    } OriginSettings;

    explicit TrueSystemProtocol(AnalysisWorker *analysisWorker, QObject *parent = nullptr);

    bool analysisFrame(const Devices::Can::CanFrame &frame) override;

signals:

private:
    /** @brief INS_ 主车数据 */
    bool parseFrame_IFS300_1_4_1_INS_0x60N(const Devices::Can::CanFrame &frame);

    /** @brief RDMN_ 相对信息 */
    bool parseFrame_IFS300_1_4_1_RDM_0x4AN(const Devices::Can::CanFrame &frame);

    /** @brief TargetN_ 从车数据 */
    bool parseFrame_IFS300_1_4_1_Target_0x7AN(const Devices::Can::CanFrame &frame);

    /** @brief IFS300 1.4.1 注意坐标系， */
    /*
     * x ^
     *   |
     *   |
     * --|-------->
     *   |        y
     */
    bool parseFrame_IFS300_1_4_1(const Devices::Can::CanFrame &frame);

    bool calculatingTheBody();

    float mINSLength{4.90};  // 主车长 3.78 - 4.9 / 2 = 1.33      0.97
    float mINSWidth{1.8};   // 主车宽
    float mTargetLength{4.85};  // 从车长
    float mTargetWidth{1.95};   // 从车宽

    OriginSettings mOriginSettingsINS;
    OriginSettings mOriginSettingsRDM;
};

} // namespace Protocol
} // namespace Analysis

#endif // TRUESYSTEM_H
