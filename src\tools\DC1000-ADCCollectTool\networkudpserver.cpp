﻿#include "networkudpserver.h"

#include <iostream>
#include <thread>

#pragma comment(lib, "ws2_32")

void deviceReceive(NetworkUDPServer *pUDPServer) {
    std::cout << "线程开始，UDP读取数据 ..." << std::endl;
    while (pUDPServer->isOpened())
    {
        pUDPServer->receive();
    }
    std::cout << "线程结束，停止UDP读取." << std::endl;
}

NetworkUDPServer::NetworkUDPServer(NETCallBack callback, void *callbackObj, std::string IP, uint16_t port, std::string rIP, uint16_t rPort, bool anyIP)
    : mNETCallBack(callback), mNETCallBackObj(callbackObj), mLocalIP(IP), mLocalPort(port), mRemoteIP(rIP), mRemotePort(rPort), mAnyIP(anyIP)
{
    mSocket = socket(AF_INET, SOCK_DGRAM, 0);
}

NetworkUDPServer::~NetworkUDPServer()
{
    if (isOpened()) {
        close();
    }
}

bool NetworkUDPServer::open()
{
    if (isOpened()) {
        close();
    }
    mOpened = false;

    WSADATA wsaData;
    int iRet = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (iRet != 0) {
        mErrorString = std::string("WSAStartup fail: ").append(std::to_string(WSAGetLastError()));
        return false;
    }

    mSocket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if(INVALID_SOCKET == mSocket) {
        mErrorString = std::string("socket fail: ").append(std::to_string(WSAGetLastError()));
        return false;
    }

    SOCKADDR_IN addr;
    addr.sin_family = AF_INET;
    if (mBroadcast) { // 广播
        addr.sin_addr.s_addr = 0;
    } else if (mAnyIP) {
        addr.sin_addr.s_addr = INADDR_ANY;
    } else {
        addr.sin_addr.s_addr = inet_addr(mLocalIP.c_str());
    }
    addr.sin_port = htons(mLocalPort);

    bool bOptval = true;
    if (mBroadcast) { // 广播
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "-----------" << std::endl;
        iRet = setsockopt(mSocket, SOL_SOCKET, SO_BROADCAST, (char*)&bOptval, sizeof(bOptval));
        if (iRet != 0) {
            mErrorString = std::string("setsockopt fail: ").append(std::to_string(WSAGetLastError()));
            return false;
        }
    } else {
        iRet = setsockopt(mSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&bOptval, sizeof(bOptval));
        if (iRet != 0) {
            mErrorString = std::string("setsockopt fail: ").append(std::to_string(WSAGetLastError()));
            return false;
        }
    }

    iRet = bind(mSocket, (sockaddr*)&addr, sizeof(addr));
    if (iRet != 0) {
        mErrorString = std::string("udp bind fail: ").append(std::to_string(WSAGetLastError()));
        return false;
    }

    // 加入组播
    std::cout << __FUNCTION__ << " " << __LINE__ << " " << mMulticast << std::endl;
    if (mMulticast) {
        ip_mreq multiCast;
        if (mAnyIP) {
            multiCast.imr_interface.S_un.S_addr = INADDR_ANY;
        } else {
            multiCast.imr_interface.S_un.S_addr = inet_addr(mLocalIP.c_str());
        }
//        multiCast.imr_multiaddr.S_un.S_addr = inet_addr("*********");
        multiCast.imr_multiaddr.S_un.S_addr = inet_addr(mMulticastIP.c_str());
        iRet = setsockopt(mSocket, IPPROTO_IP, IP_ADD_MEMBERSHIP, (char*)&multiCast, sizeof(multiCast));
        if (iRet != 0) {
            mErrorString = std::string("multicast setsockopt fail: ").append(std::to_string(WSAGetLastError()));
            return false;
        }
    }

    // 查看系统默认的socket接收缓冲区大小
        int defRcvBufSize = -1;
        int optlen = sizeof(defRcvBufSize);
        if (getsockopt(mSocket, SOL_SOCKET, SO_RCVBUF, (char*)&defRcvBufSize, &optlen) < 0)
        {
            printf("getsockopt error=%d(%s)!!!\n", errno, strerror(errno));
            goto error;
        }
        printf("OS default udp socket recv buff size is: %d\n", defRcvBufSize);

        // 按照执行参数设置UDP SOCKET接收缓冲区大小
        int rcvBufSize = 1024 * 1024 * 4;
        if (rcvBufSize <= 0)
        {
            printf("rcvBufSize(%d) <= 0, error!!!\n", rcvBufSize);
            goto error;
        }
        printf("you want to set udp socket recv buff size to %d\n", rcvBufSize);
        optlen = sizeof(rcvBufSize);
        if (setsockopt(mSocket, SOL_SOCKET, SO_RCVBUF, (char*)&rcvBufSize, optlen) < 0)
        {
            printf("setsockopt error=%d(%s)!!!\n", errno, strerror(errno));
            goto error;
        }
        printf("set udp socket(%d) recv buff size to %d OK!!!\n", mSocket, rcvBufSize);

        // 查看当前UDP SOCKET接收缓冲区大小
        int curRcvBufSize = -1;
        optlen = sizeof(curRcvBufSize);
        if (getsockopt(mSocket, SOL_SOCKET, SO_RCVBUF, (char*)&curRcvBufSize, &optlen) < 0)
        {
            printf("getsockopt error=%d(%s)!!!\n", errno, strerror(errno));
            goto error;
        }
        printf("OS current udp socket(%d) recv buff size is: %d\n", mSocket, curRcvBufSize);
error:
    mOpened = true;

    std::thread(deviceReceive, this).detach();

    return mOpened;
}

bool NetworkUDPServer::close()
{
    // 加锁(对象释放时自动解锁)
    //! [1] 使用加锁，需要设置SOCKET超时时间，recvfrom默认为阻塞模式
//    std::lock_guard<std::mutex> lg(mMutex);
    mOpened = false;
    closesocket(mSocket);

    return true;
}

int NetworkUDPServer::sendData(const char *data, int len)
{
    SOCKADDR_IN remoteAddress; //远程地址
    remoteAddress.sin_family = AF_INET;
    remoteAddress.sin_port = htons(mRemotePort);
    remoteAddress.sin_addr.s_addr = inet_addr(mRemoteIP.c_str());
    return sendto(mSocket, data, len, 0, (sockaddr*)&remoteAddress, sizeof (SOCKADDR_IN));
}

int NetworkUDPServer::receive()
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);

    SOCKADDR_IN sin_from;
    sin_from.sin_family = AF_INET;
    sin_from.sin_port = htons(mLocalPort);
    sin_from.sin_addr.s_addr = INADDR_BROADCAST;
    int add_len = sizeof(sockaddr);
    int len = recvfrom(mSocket, mRecviveData, NET_FRAME_DATA_MAX, 0, (sockaddr*)&sin_from, &add_len);
    if (len == -1) {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << mLocalPort << " " << len << " " << WSAGetLastError() << std::endl;
        return len;
    } else if (len == 0) {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << mLocalPort << " " << len << " " << WSAGetLastError() << std::endl;
        return len;
    }

    if (mNETCallBack) {
        mNETCallBack(mLocalPort, len, (unsigned char*)mRecviveData, mNETCallBackObj);
    }


//    quint32 sequenceNumber = *((quint32*)(mRecviveData));
//    std::cout << __FUNCTION__ << " " << __LINE__ << " " << len << " " << sequenceNumber << " " << ++msequenceNumber << std::endl;
//    if (mLocalPort == 4098) {
//    return len;
//    }

//    callback(ntohl(sin_from.sin_addr.S_un.S_addr), ntohs(sin_from.sin_port), mLocalIP, mLocalPort, len, mRecviveData, false);

    return len;
}
