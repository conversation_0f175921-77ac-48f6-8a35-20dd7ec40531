﻿#include "ctcandevicefile.h"

#include "analysis/analysisworker.h"
#include "analysis/calculationworker.h"

#ifdef ALGORITHM_DEBUG
#include "dataprocess/dataprocess.h"
#endif

#include <iostream>
#include <string>
#include <thread>
#include <vector>
#include <QThread>
#include <QTimer>
#include <QElapsedTimer>
#include <QDebug>

bool validID(int id) {
    if((id & 0xF) != CTCANDeviceFile::radarID() ) {
        return false;
    }

    switch ( id & 0xFFFFF0 ) {
    case 0x3F0: // 车身信息
    case 0x400: // 原始点头
    case 0x410: // 原始点
//    case 0x430: // 跟踪点头
//    case 0x440: // 跟踪点
//    case 0x4C0: // 报警信息
//    case 0x4D0: // 故障信息
//    case 0x4E0: // 故障信息
    case 0x4F0: // 结束帧
        break;
    default:
        return false;
    }

    return true;
}

unsigned int CTCANDeviceFile::mRadarID{4};

CTCANDeviceFile::CTCANDeviceFile(QObject *parent)
    : QObject(parent)
{
    setValidID(validID);

    mAnalysisWorker = new Analysis::AnalysisWorker;

    connect(mAnalysisWorker, &Analysis::AnalysisWorker::analysisFinished, this, &CTCANDeviceFile::calculate, Qt::DirectConnection);
}

bool CTCANDeviceFile::open(const QStringList &files, int goToFrame, bool goTo)
{
#ifdef ALGORITHM_DEBUG
    DataProcess::instance()->init(false, mRadarID, mRadarID);
#endif

    mGoTo = goTo;
    mGoToFrame = goToFrame;
    mFrameIndex = -1;
    mFirstFrame = -1;

    mCANDeviceFiles.clear();
    foreach (const QString &file, files) {
        mCANDeviceFiles.push_back(file.toStdString());
    }

    mOpened = openDevice();

    if (mOpened) {
        emit opened();
    }

    return mOpened;
}

bool CTCANDeviceFile::close()
{
    mOpened = !closeDevice();
    if (!mOpened) {
        emit closed();
    }
    return !mOpened;
}

bool CTCANDeviceFile::readNextFrame()
{
    mReadOK = false;
    QElapsedTimer elapsedTimer;
    elapsedTimer.start();

    while (isOpened() && !mReadOK) {
        receiveFrame(0);
    }

    qDebug() << __FUNCTION__ << __LINE__ << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz") << elapsedTimer.elapsed() << elapsedTimer.nsecsElapsed();

    return mReadOK;
}

void CTCANDeviceFile::callback(const Devices::Can::CanFrame::Ptr pFrame)
{
    mAnalysisWorker->canFrame(*pFrame);
}

void CTCANDeviceFile::calculate(quint8 radarID, const AnalysisData &analysisData)
{
#ifdef ALGORITHM_DEBUG
    // 不能整体赋值，会覆盖真值和其它不同步的目标点
    AnalysisData data = AnalysisData(analysisData);

    data.mTargets[FrameRawTarget] = analysisData.mTargets[FrameRawTarget];
    data.mRadarID = analysisData.mRadarID;
    data.mAlarmData = analysisData.mAlarmData;                       // 告警信息
    data.mVehicleData = analysisData.mVehicleData;                   // 汽车数据
    data.mEndFrameData = analysisData.mEndFrameData;

    // 走数据处理
    data.calculateTarget(FrameRawTarget);
#endif

    ++mFrameIndex;
    if (mFirstFrame == -1) {
        mFirstFrame = data.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount;
        if (!mGoTo) {
            mGoToFrame = mFirstFrame + mGoToFrame;
        } else {
            if (mGoToFrame < mFirstFrame) {
               mGoToFrame = mFirstFrame;
            }
        }
    }

    // 走数据处理
    if (!DataProcess::instance()->process(data)) {
            qDebug() << __FUNCTION__ << __LINE__ << "master radar data process error!";
    }


    qDebug() << __FUNCTION__ << __LINE__ << mFrameIndex << mFirstFrame << data.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount << mGoToFrame;

    emit currentFrame(mFrameIndex, mFirstFrame, data.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount);
    if (data.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount < mGoToFrame) {
        return;
    }

    emit calculateFinished(radarID, data);

    mReadOK = true;
}
