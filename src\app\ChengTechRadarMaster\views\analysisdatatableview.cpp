﻿#include "analysisdatatableview.h"
#include "ui_analysisdatatableview.h"

#include "analysis/analysisdata.h"
#include "analysis/analysisdataf.h"
#include "analysismodel.h"

namespace Views {
namespace AnalysisView {

AnalysisDataTableView::AnalysisDataTableView(quint8 radarID, int vType, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::AnalysisDataTableView),
    mRadarID(radarID)
{
    ui->setupUi(this);
    switch (mRadarID) {
    case 0:
        ui->comboBoxDataType->addItem("Raw");
        ui->comboBoxDataType->addItem("Track");
        break;
    case 4:
    case 5:
    case 6:
    case 7:
        ui->comboBoxDataType->addItem("Raw");
        ui->comboBoxDataType->addItem("Track");
        ui->comboBoxDataType->addItem("Alarm");
        ui->comboBoxDataType->addItem("EndFrame");
        ui->comboBoxDataType->addItem("Vehicle");
        ui->comboBoxDataType->addItem("200Raw");
        ui->comboBoxDataType->addItem("16Track");
        ui->comboBoxDataType->addItem("3Track");
        break;
    default:
        break;
    }
    ui->comboBoxDataType->addItem("None");

    ui->comboBoxDataType->setCurrentIndex(vType);

    changeTableView();
}

AnalysisDataTableView::~AnalysisDataTableView()
{
    delete ui;
}

void AnalysisDataTableView::changeTableView()
{
    ui->tableView->horizontalHeader()->setStretchLastSection(true);
    QFont font;
    font.setPointSize(7);
    ui->tableView->setFont(font);
    ui->tableView->horizontalHeader()->setDefaultSectionSize(50);
    ui->tableView->verticalHeader()->setMinimumSectionSize(15);
    ui->tableView->verticalHeader()->setDefaultSectionSize(15);
}

int AnalysisDataTableView::viewType()
{
    return ui->comboBoxDataType->currentIndex();
}

void AnalysisDataTableView::on_pushButtonDelete_clicked()
{
    emit sigDelete();
}

void AnalysisDataTableView::on_comboBoxDataType_currentIndexChanged(int index)
{
    switch (mRadarID) {
    case 0:
        modelForwardRadar(index);
        break;
    case 4:
    case 5:
    case 6:
    case 7:
        modelAngularRadar(index);
        break;
    default:
        break;
    }

    emit sigViewTypeChanged();
}

void AnalysisDataTableView::modelForwardRadar(int frameType)
{
    switch (frameType) {
    case Parser::ParsedDataTypedef::TargetRaw:
    case Parser::ParsedDataTypedef::TargetTrack:
    case Parser::ParsedDataTypedef::HeSaiLider:
        mModel = new AnalysisModelF((Parser::ParsedDataTypedef::ParsedDataType)frameType, ui->tableView);
        break;
    default:
        return;
    }

    ui->tableView->setModel(mModel);
}

void AnalysisDataTableView::modelAngularRadar(int frameType)
{
    switch (frameType) {
    case FrameRawTarget:
    case FrameTrackTarget:
    case Frame200Raw:
    case Frame3Track:
        mModel = new AnalysisModel((AnalysisFrameType)frameType, ui->tableView);
        break;
    case Frame16Track:
    {
//        QList<int> viewAnalysisTypes{
//            X, Y, Range, Angle, PitchAngle, V, Vx, Vy, SNR, RCS, Status, AssociatedTrackID, GroupID, RefencePointPostion, ExistProbability, ObstacleProbability
//        };
        mModel = new AnalysisModel((AnalysisFrameType)frameType, ui->tableView);
//        ((AnalysisModel*)mModel)->setViewAnalysisTypes( viewAnalysisTypes );
    }
        break;
    case FrameAlarm:
        mModel = new AlarmDataModel(ui->tableView);
        break;
    case FrameEndFrame:
        mModel = new EndFrameModel(ui->tableView);
        break;
    case FrameVehicle:
        mModel = new VehicleDataModel(ui->tableView);
        break;
    default:
        return;
    }

    ui->tableView->setModel(mModel);
}

}
}
