﻿#include "autorechargeform.h"
#include "ui_autorechargeform.h"
#include "debugsystem/debugcontrolform.h"
#include "analysis/analysissaveworker.h"
#include "alarmcompare.h"
#include "autorechargerawandtarget.h"
#include "devices/devicemanager.h"


#include <QDebug>
#include <QFileDialog>
#include <QStandardItem>
#include <QDateTime>
#include <QTimer>
#include <QtConcurrent>
#include <QMessageBox>

#define RESULT_DIR_STR "HILResult"

AutoRechargeFileModel::AutoRechargeFileModel(QObject *parent)
    :QDirModel( parent )
{

}

void AutoRechargeFileModel::setEqualFlag(const QString &file, bool bEqual)
{
    QModelIndex index = this->index( file );
    if( !index.isValid() ){
        return;
    }
    QFileInfo fileInfo = this->fileInfo( index );
    if( fileInfo.isFile() && fileInfo.suffix() == "binary" ){
        mFileAddInfoMap[ fileInfo.absoluteFilePath() ].opCnt ++;
        mFileAddInfoMap[ fileInfo.absoluteFilePath() ].equalCnt = bEqual ? 1 : 0 ;
        mFileAddInfoMap[ fileInfo.absoluteFilePath() ].notEqualCnt = bEqual ? 0 : 1 ;
    }
    emit dataChanged( index, this->index( index.row(), this->columnCount(index)-1 ) );

    QModelIndex parent = index.parent();
    while ( parent.isValid() ) {
        QFileInfo parentFileInfo = this->fileInfo( parent );
        mFileAddInfoMap[ parentFileInfo.absoluteFilePath() ].opCnt ++;
        if( bEqual ){
            mFileAddInfoMap[ parentFileInfo.absoluteFilePath() ].equalCnt++;
        }else{
            mFileAddInfoMap[ parentFileInfo.absoluteFilePath() ].notEqualCnt++;
        }
        emit dataChanged( parent, this->index( parent.row(), this->columnCount(parent)-1 ) );
        parent = parent.parent();
    }

}

void AutoRechargeFileModel::getCheckedFiles(const QModelIndex& index, QStringList &files)
{
    for( int i=0; i<rowCount( index ); i++ ){
        QModelIndex child = this->index( i, 0, index );
        QFileInfo info = fileInfo( child );
        if( data( child , Qt::CheckStateRole )== Qt::Checked && info.isFile() && info.suffix() == "binary" ){
            files << info.absoluteFilePath();
        }

        getCheckedFiles( child, files );
    }
}

quint32 AutoRechargeFileModel::totalFileAddInfo(const QModelIndex &index)
{
    if( !index.isValid() ){
        return 0;
    }

    QFileInfo info = fileInfo( index );
    FileAddInfo& addInfo = mFileAddInfoMap[ info.absoluteFilePath() ];
    //memset( &addInfo, 0, sizeof(FileAddInfo) );
    addInfo.clearCount();

    if( info.isFile() && info.suffix() == "binary" ){
        addInfo.total = 1;
    }else{//先递归统计子节点
        for( int i=0; i<rowCount(index); i++ ){
            QModelIndex child = this->index( i, 0, index );
            addInfo.total += totalFileAddInfo( child );
        }
    }

    emit dataChanged( index, this->index( index.row(), this->columnCount(index)-1 ) );
    return addInfo.total;
}

void AutoRechargeFileModel::exportResult(QFile *file, const QModelIndex &index, quint32 indent, QFile* csvFile)
{
    if( !index.isValid() ){
        return;
    }

    QFileInfo info = fileInfo( index );
    FileAddInfo& addInfo = mFileAddInfoMap[ info.absoluteFilePath() ];
    QString text = info.fileName();
    QString csvText = info.fileName();

    if( info.isFile() && info.suffix() == "binary" ){
        if( addInfo.opCnt != 0 ){
            text += QString::fromLocal8Bit( " 已回灌");
            csvText += QString::fromLocal8Bit( ",,已回灌");
            if( addInfo.equalCnt != 0 ){
                text += QString::fromLocal8Bit( " 一致");
                csvText += QString::fromLocal8Bit( ",,一致");
            }else{
                text += QString::fromLocal8Bit( " 不一致");
                csvText += QString::fromLocal8Bit( ",,不一致");
            }
        }else{
            text += QString::fromLocal8Bit( " 未回灌" );
            csvText += QString::fromLocal8Bit( ",,未回灌");
        }
    }else if( info.isDir() ){
        if( addInfo.total == 0 ){
            return;
        }else{
            text += QString::fromLocal8Bit( " 总数：%1").arg( addInfo.total );
            text += QString::fromLocal8Bit( " 已回灌数：%1").arg( addInfo.opCnt );
            text += QString::fromLocal8Bit( " 未回灌数：%1").arg( addInfo.total - addInfo.opCnt );
            text += QString::fromLocal8Bit( " 报警一致数：%1").arg( addInfo.equalCnt );
            text += QString::fromLocal8Bit( " 报警不一致数：%1").arg( addInfo.notEqualCnt );
            csvText += "," + QString::number( addInfo.total );
            csvText += "," + QString::number( addInfo.opCnt );
            csvText += "," + QString::number( addInfo.total - addInfo.opCnt );
            csvText += "," + QString::number( addInfo.equalCnt );
            csvText += "," + QString::number( addInfo.notEqualCnt );
        }
    }else{
        return;
    }

    //写入txt文件
    for( quint32 i=0; i<indent; i++ ){ //缩进
        file->write( "\t" );
    }
    file->write( text.toLocal8Bit() );
    file->write( "\n" );
    file->flush();

    //写入csv文件
    for( quint32 i=0; i<indent; i++ ){ //缩进
        csvFile->write( "|----" );
    }
    csvFile->write( csvText.toLocal8Bit() );
    csvFile->write( "\n" );
    csvFile->flush();

    //遍历子节点
    for( int i=0; i<this->rowCount( index ); i++ ){
        QModelIndex child = this->index( i, 0, index );
        exportResult( file, child, indent +1, csvFile );
    }
}

QVariant AutoRechargeFileModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid()){
        return QVariant();
    }

    if (index.column() == 1 && role == Qt::DisplayRole){
        QModelIndex firstColumnIndex = this->index( index.row(), 0, index.parent() );
        if( firstColumnIndex.isValid() ){
            QFileInfo info = fileInfo( firstColumnIndex );
            if( info.isDir() ){
                const FileAddInfo& addInfo = mFileAddInfoMap[ info.absoluteFilePath() ];
                if( addInfo.total != 0 ){
                    return QString::fromLocal8Bit( "已回灌/总数:%1/%2 一致|不一致:%3|%4" )
                            .arg( addInfo.opCnt )
                            .arg( addInfo.total )
                            .arg( addInfo.equalCnt )
                            .arg( addInfo.notEqualCnt );
                }
            }
        }
    }

    if (index.column() == 0 && role == Qt::CheckStateRole){
        QFileInfo info = fileInfo( index );
        if( info.isFile() && info.suffix() != "binary" ){ //只有binary格式的文件，或文件夹可以勾选
            return QVariant();
        }
        const FileAddInfo& addInfo = mFileAddInfoMap[ info.absoluteFilePath() ];
        if( addInfo.checked ){
            return Qt::Checked;
        }else{
            return Qt::Unchecked;
        }
    }

    if( role == Qt::TextColorRole ){
        QFileInfo info = fileInfo( index );
        if( info.isFile() && info.suffix() != "binary" ){ //只有binary格式的文件，或文件夹
            return QVariant();
        }
        const FileAddInfo& addInfo = mFileAddInfoMap[ info.absoluteFilePath() ];
        if( addInfo.notEqualCnt != 0 ){  //存在不相等的子节点
            return QColor( Qt::red );
        }else if( addInfo.equalCnt != 0 ){  //只存在相等的子节点
            return QColor( Qt::green );
        }else{
            return QDirModel::data(index, role);
        }
    }

    return QDirModel::data(index, role);
}

bool AutoRechargeFileModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (role == Qt::CheckStateRole && index.column() == 0){
        QFileInfo info = fileInfo( index );
        FileAddInfo& addInfo = mFileAddInfoMap[ info.absoluteFilePath() ];
        if (value == Qt::Unchecked){
            addInfo.checked = false;
            emit dataChanged(index, index);
        }else{
            addInfo.checked = true;
            emit dataChanged(index, index);
        }

        //设置子节点
        if (hasChildren(index)){
            QString strFilePath = filePath(index);
            int iChildCount = rowCount(index);
            if (iChildCount > 0){
                for (int i = 0; i < iChildCount; i++){
                    QModelIndex _child = this->index(i, 0, index);
                    setData(_child, value,Qt::CheckStateRole);
                }
            }
        }

        //更新父节点
        if( value == Qt::Unchecked ){
            QModelIndex parent = index.parent();
            while( parent.isValid() ){
                QFileInfo parentInfo = fileInfo( parent );
                mFileAddInfoMap[ parentInfo.absoluteFilePath() ].checked = false;
                emit dataChanged(parent, parent);
                parent = parent.parent();
            }
        }

    }

    return QDirModel::setData(index, value, role);
}

Qt::ItemFlags AutoRechargeFileModel::flags(const QModelIndex &index) const
{
    if (!index.isValid())
        return QAbstractItemModel::flags(index);

//    int nRow = index.row();
    int nColumn = index.column();

    Qt::ItemFlags flags = QDirModel::flags( index );
    if ( nColumn == 0){
        flags |= Qt::ItemIsUserCheckable;
    }
    return flags;
}


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

AutoRechargeForm::AutoRechargeForm(/*DebugControlForm* pDebugCtrl,*/ Devices::Can::DeviceManager* pDeviceManager, Analysis::AnalysisWorker* pAnalysisWorker, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::AutoRechargeForm)
{
    ui->setupUi(this);

    mAnalysisWorker = pAnalysisWorker;
    mDeviceManager = pDeviceManager;

    mDirModel = new AutoRechargeFileModel( this );
    mDirModel->setReadOnly( true );
    mDirModel->setSorting(QDir::DirsFirst | QDir::IgnoreCase | QDir::Name);
    QStringList nameFilters;
    nameFilters << "*.binary" << "Alarm *.csv" << "EarlyWarning *.csv";
    mDirModel->setFilter(QDir::AllEntries|QDir::NoDotAndDotDot|QDir::AllDirs);
    mDirModel->setNameFilters( nameFilters );

    ui->treeView->setModel( mDirModel );
    ui->treeView->header()->setSectionResizeMode(0, QHeaderView::ResizeToContents);
    ui->treeView->header()->setSectionResizeMode(1, QHeaderView::ResizeToContents);

    mPath = QCoreApplication::applicationDirPath();
    ui->lineEditPath->setText( mPath );
    ui->treeView->setRootIndex( mDirModel->index(mPath) );
    mDirModel->totalFileAddInfo( ui->treeView->rootIndex() );

    mAnalysisSaveWorker = new Analysis::AnalysisSaveWorker( mAnalysisWorker, this );
    mAlarmCompare = new AlarmCompare( this );
    mAlarmCompare->setWindowFlag( Qt::Dialog );
    mAutoRecharge = new AutoRechargeRawAndTarget( this );

    ui->pushButtonStop->setEnabled( false );
    ui->comboBoxType->setCurrentIndex( 1 );

    initChannelCount();
}

AutoRechargeForm::~AutoRechargeForm()
{
    delete ui;
//    if( mAutoRecharge ){
//        delete mAutoRecharge;
//        mAutoRecharge = NULL;
//    }
}

void AutoRechargeForm::on_pushButtonSelectPath_clicked()
{
    mPath = QFileDialog::getExistingDirectory(
                this,
                QString::fromLocal8Bit("选择文件夹"),
                "./",
                QFileDialog::ShowDirsOnly);
    if (!mPath.isEmpty())
    {
        ui->lineEditPath->setText( mPath );
        ui->treeView->setRootIndex( mDirModel->index(mPath) );
        mDirModel->totalFileAddInfo( ui->treeView->rootIndex() );
    }
}

void AutoRechargeForm::on_pushButtonRecharge_clicked()
{
    mDirModel->totalFileAddInfo( ui->treeView->rootIndex() );
    mFiles.clear();
    mCurrentFilesIndex = 0;    

    QModelIndex rootIndex = ui->treeView->rootIndex();
    mDirModel->getCheckedFiles( rootIndex, mFiles );

    if( mFiles.isEmpty() ){
        return;
    }

    if( !setRechargeParam() ){
        return;
    }

    connect( mAutoRecharge, &AutoRechargeRawAndTarget::begin, this, &AutoRechargeForm::begin );
    connect( mAutoRecharge, &AutoRechargeRawAndTarget::end, this, &AutoRechargeForm::end );
    enablePushButton( false );

    mRun = true;
    run();
}

void AutoRechargeForm::showMsg(const QString &msg, bool bError)
{
    if( bError ){
        ui->textBrowserMsg->setTextColor( Qt::red );
    }else{
        ui->textBrowserMsg->setTextColor( Qt::black );
    }

    QString head = QString( "%1==>").arg( QDateTime::currentDateTime().toLocalTime().toString( "hh:mm:ss:zzz" ) );
    ui->textBrowserMsg->append( head + msg );
}

void AutoRechargeForm::begin(const QString &file)
{
    showMsg( file + QString::fromLocal8Bit(" 回灌开始") );
}

void AutoRechargeForm::end(const QString &file)
{
    showMsg( file + QString::fromLocal8Bit(" 回灌结束") );
    mAnalysisSaveWorker->stopSave();

    mDirModel->refresh();

    //等待1S
    QEventLoop loop;
    QTimer timer;
    timer.setSingleShot( true );
    connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
    timer.start( 1000 );
    loop.exec();
    timer.stop();

    if( !cmpareAlarm( file ) ){
        showMsg( QString::fromLocal8Bit( "报警结果不一致!"), true );
        mDirModel->setEqualFlag( file, false );
    }else{
        mDirModel->setEqualFlag( file, true );
    }

    run();
}

bool AutoRechargeForm::setRechargeParam()
{
    //mAutoRecharge->setType( AutoRechargeRawAndTarget::TYPE::RECHARGET_TYPE_TARGET );
    mAutoRecharge->setType( (AutoRechargeRawAndTarget::TYPE)(ui->comboBoxType->currentIndex() +1) );
    mAutoRecharge->setAnalysisWorker( mAnalysisWorker );
    mAutoRecharge->setDeviceManager( mDeviceManager );
    mAutoRecharge->clearRadarInfo();

    if( ui->checkBoxRadar4->checkState() ){
        mAutoRecharge->setRadarChannel( 4, ui->comboBoxChannel4->currentIndex() );
    }
    if( ui->checkBoxRadar5->checkState() ){
        mAutoRecharge->setRadarChannel( 5, ui->comboBoxChannel5->currentIndex() );
    }
    if( ui->checkBoxRadar6->checkState() ){
        mAutoRecharge->setRadarChannel( 6, ui->comboBoxChannel6->currentIndex() );
    }
    if( ui->checkBoxRadar7->checkState() ){
        mAutoRecharge->setRadarChannel( 7, ui->comboBoxChannel7->currentIndex() );
    }
    if( !ui->checkBoxRadar4->checkState() && !ui->checkBoxRadar5->checkState() &&
            !ui->checkBoxRadar6->checkState() && !ui->checkBoxRadar7->checkState() ){
        QMessageBox::warning( this, QString::fromLocal8Bit("提示"), QString::fromLocal8Bit("请选择需要回灌的雷达ID!") );
        return false;
    }
    return true;
}

void AutoRechargeForm::enablePushButton(bool enable)
{
    ui->lineEditPath->setEnabled( enable );
    ui->pushButtonSelectPath->setEnabled( enable );
    ui->checkBoxRadar4->setEnabled( enable );
    ui->checkBoxRadar5->setEnabled( enable );
    ui->checkBoxRadar6->setEnabled( enable );
    ui->checkBoxRadar7->setEnabled( enable );
    ui->comboBoxChannel4->setEnabled( enable );
    ui->comboBoxChannel5->setEnabled( enable );
    ui->comboBoxChannel6->setEnabled( enable );
    ui->comboBoxChannel7->setEnabled( enable );
    ui->pushButtonRecharge->setEnabled( enable );
    ui->pushButtonStop->setEnabled( !enable );
}

void AutoRechargeForm::run()
{
//    ui->pushButtonRecharge->setEnabled( false );
//    ui->pushButtonStop->setEnabled( true );

    if( mRun && mCurrentFilesIndex < mFiles.size() ){
        QFileInfo info( mFiles[mCurrentFilesIndex] );
        QString savePath = info.absolutePath() + "/"+ RESULT_DIR_STR;
        QDir dir( savePath );
        if( !dir.exists() ){
            dir.mkdir( savePath );
        }

        mAutoRecharge->setRechargeFiles( mFiles[mCurrentFilesIndex] );
        QtConcurrent::run( mAutoRecharge, &AutoRechargeRawAndTarget::run );
        qDebug() << __FUNCTION__ << __LINE__;
        mCurrentFilesIndex++;

        mAnalysisSaveWorker->startSave( savePath, QDateTime::currentDateTime(), false );
    }else{

        disconnect( mAutoRecharge, &AutoRechargeRawAndTarget::begin, this, &AutoRechargeForm::begin );
        disconnect( mAutoRecharge, &AutoRechargeRawAndTarget::end, this, &AutoRechargeForm::end );

//        ui->pushButtonRecharge->setEnabled( true );
//        ui->pushButtonStop->setEnabled( false );
        enablePushButton( true );

        exportResult();
        mRun = false;
    }
}

bool AutoRechargeForm::cmpareAlarm(const QString &file)
{
    QModelIndex index = mDirModel->index( file );
    if( !index.isValid() ){
        return false;
    }

    QFileInfo info = mDirModel->fileInfo( index );
    if( !info.isFile() || info.suffix() != "binary" ){
        showMsg( QString::fromLocal8Bit( "回灌的文件未找到![%1]").arg(file), true );
        return false;
    }
    QModelIndex parent = index.parent();
    if( !parent.isValid() ){
        showMsg( QString::fromLocal8Bit( "回灌的文件无父节点"), true );
        return false;
    }

    QString srcFile, tarFile;
    QModelIndex resultIndex;
    for( int i=0; i<mDirModel->rowCount( parent ); i++ ){
        QModelIndex childIndex = mDirModel->index( i, 0, parent );
        if( childIndex.isValid() ){
            QFileInfo info = mDirModel->fileInfo( childIndex );
            qDebug() << __FUNCTION__ << __LINE__ << info.fileName();
            if( info.isDir() && info.fileName() == RESULT_DIR_STR ){
                resultIndex = childIndex;
                qDebug() << __FUNCTION__ << __LINE__ << info.fileName() << mDirModel->rowCount( resultIndex );
            }else if( info.isFile() && info.suffix() == "csv" ){
                srcFile = info.absoluteFilePath();
                //break;  //不退出  获取最新的那一个
            }
        }
    }
    if( !resultIndex.isValid() ){
        showMsg( QString::fromLocal8Bit( "回灌结果节点未找到!"), true );
        return false;
    }

    for( int i=0; i<mDirModel->rowCount( resultIndex ); i++ ){
        QModelIndex childIndex = mDirModel->index( i, 0, resultIndex );
        if( childIndex.isValid() ){
            QFileInfo info = mDirModel->fileInfo( childIndex );
            if( info.isFile() && info.suffix() == "csv" ){
                tarFile = info.absoluteFilePath();
                //break;  //不退出  获取最新的那一个
            }
        }
    }

    if( srcFile.isEmpty() || tarFile.isEmpty() ){
        showMsg( QString::fromLocal8Bit( "比对源文件或目标文件未找到!"), true );
        qDebug() << __FUNCTION__ << __LINE__ << srcFile << tarFile;
        return false;
    }

    mAlarmCompare->show();
    bool bRet = mAlarmCompare->compareAlarmFile( srcFile, tarFile );
    mAlarmCompare->hide();
    return bRet;
}

bool AutoRechargeForm::exportResult()
{
    QModelIndex root = ui->treeView->rootIndex();
    if( !root.isValid() ){
        return false;
    }
    QFileInfo info = mDirModel->fileInfo( root );
    if( !info.isDir() ){
        return false;
    }

    QDateTime currentTime = QDateTime::currentDateTime();
    QString fileName = QString::fromLocal8Bit( "%1/回灌结果%2.txt" )
            .arg( info.absoluteFilePath() )
            .arg( currentTime.toString("yyyy-MM-dd hh-mm-ss-zzz") );
    QFile file( fileName );
    if( !file.open( QIODevice::WriteOnly ) ){
        qDebug() << __FUNCTION__ << __LINE__ << "open file fail!" << fileName;
        return false;
    }

    QString csvFileName = QString::fromLocal8Bit( "%1/回灌结果%2.csv" )
            .arg( info.absoluteFilePath() )
            .arg( currentTime.toString("yyyy-MM-dd hh-mm-ss-zzz") );
    QFile csvFile( csvFileName );
    if( !csvFile.open( QIODevice::WriteOnly ) ){
        qDebug() << __FUNCTION__ << __LINE__ << "open file fail!" << csvFileName;
        file.close();
        return false;
    }

    QString csvHead = QString::fromLocal8Bit( "文件名,总数,已回灌数,未回灌数,一致数,不一致数\n" );
    csvFile.write( csvHead.toLocal8Bit() );
    csvFile.flush();

    mDirModel->exportResult( &file, root, 0, &csvFile );
    csvFile.close();
    file.close();
    return true;
}

void AutoRechargeForm::initChannelCount()
{
    for( int i=0; i<mDeviceManager->channelCount(); i++ ){
        ui->comboBoxChannel4->addItem( QString::number(i) );
        ui->comboBoxChannel5->addItem( QString::number(i) );
        ui->comboBoxChannel6->addItem( QString::number(i) );
        ui->comboBoxChannel7->addItem( QString::number(i) );
    }
}

void AutoRechargeForm::on_treeView_doubleClicked(const QModelIndex &index)
{
    QFileInfo info = mDirModel->fileInfo( index );
    if( info.isFile() && info.suffix() == "csv" ){
        if( info.absolutePath().indexOf( RESULT_DIR_STR ) == -1 ){ //保证是回灌结果中的alarm xxx.csv
            return;
        }
        QModelIndex parent = index.parent().parent();
        if( !parent.isValid() ){
            return;
        }
        QString srcFile = "";
        for( int i=0; i<mDirModel->rowCount( parent ); i++ ){
            QModelIndex srcIndex = mDirModel->index( i, 0, parent );
            if( srcIndex.isValid() ){
                QFileInfo srcInfo = mDirModel->fileInfo( srcIndex );
                if( srcInfo.isFile() && srcInfo.suffix() == "csv" ){
                    srcFile = srcInfo.absoluteFilePath();
                    //break;  //不退出  获取最新的那一个
                }
            }
        }
        if( srcFile.isEmpty() ){
            return;
        }

        mAlarmCompare->show();
        mAlarmCompare->compareAlarmFile( srcFile, info.absoluteFilePath() );
    }
}

void AutoRechargeForm::on_pushButtonClearMsg_clicked()
{
    ui->textBrowserMsg->clear();
}

void AutoRechargeForm::on_pushButtonStop_clicked()
{
    mRun = false;
    mAutoRecharge->stop();
    ui->pushButtonStop->setEnabled( false );
}
