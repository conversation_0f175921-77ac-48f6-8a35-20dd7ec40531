﻿#include "interpolationgeely.h"


extern "C"{
#include "alg/track/rdp_interface.h"
}

#include <QDebug>

namespace Analysis {


bool InterpolationGEELY::isTrackFrame(int radarID, int channelRadarID[2], const Devices::Can::CanFrame &frame)
{
    int _radarID = 4;
    switch (channelRadarID[frame.channelIndex() % 2]) {
    case 0:
        _radarID = 6;
        break;
    case 1:
        _radarID = 4;
        break;
    }

    switch (frame.id())
    {
    case 0x129:
    case 0x15C:
    case 0x15E:
    case 0x160:
    case 0x162:
    case 0x164:
    case 0x166:
    case 0x168:
    case 0x16A:
    case 0x16C:
    case 0x16E:
    case 0x170:
    case 0x172:
    case 0x174:
    case 0x176:
    case 0x178:
        break;
    case 0x12E:
    case 0x15D:
    case 0x15F:
    case 0x161:
    case 0x163:
    case 0x165:
    case 0x167:
    case 0x169:
    case 0x16B:
    case 0x16D:
    case 0x16F:
    case 0x171:
    case 0x173:
    case 0x175:
    case 0x177:
    case 0x179:
        _radarID = _radarID + 1;
        break;
    default:
        return false;
    }

    return radarID == _radarID;
}

void InterpolationGEELY::saveTimestamps(Devices::Can::FrameTimestamps *timestamps, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
        return;
    }
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.timestemp() << QDateTime::fromMSecsSinceEpoch(frame.timestemp());
//    qDebug() << __FUNCTION__ << __LINE__ << timestamps->mCount;
    const uint8_t *data = (const uint8_t *)frame.data().data();
    Devices::Can::FrameTimestamp *timestamp = timestamps->mTimestamps + timestamps->mCount;
    switch (frame.id())
    {
    case 0x129:
    case 0x12E:
    {
        //    userData->ReSideRdrLeStsRdrStsTiStamp = (((data[22] & 0xFFU) + (((uint32_t)data[21]) << 8) + (((uint32_t)data[20]) << 16) + (((uint32_t)data[19] & 0xFFU) << 24)) * 0.0001);

        timestamp->OD_TimeStampMs = (((data[22] & 0xFFU) + (((uint32_t)data[21]) << 8) + (((uint32_t)data[20]) << 16) + (((uint32_t)data[19] & 0xFFU) << 24)) * 0.0001) * 1000;
        timestamp->OD_InterTimeStampMs = timestamp->OD_TimeStampMs;

        timestamp->_0x129.id = frame.id();
        timestamp->_0x129.dlc = frame.length();
        memcpy(timestamp->_0x129.data, frame.data(), frame.length());
        timestamp->_0x129.timestamp = frame.timestemp();
        timestamp->_0x129.channel = frame.channelIndex();

        timestamp->_0x129Valids = true;
    }
        break;

    case 0x178:

    case 0x179:
        timestamps->mCount++;
//        qDebug() << __FUNCTION__ << __LINE__ << timestamps->mCount << timestamp->OD_InterTimeStampMs << frame.idHex() << frame.dataHex();

    case 0x15C:
    case 0x15E:
    case 0x160:
    case 0x162:
    case 0x164:
    case 0x166:
    case 0x168:
    case 0x16A:
    case 0x16C:
    case 0x16E:
    case 0x170:
    case 0x172:
    case 0x174:
    case 0x176:

    case 0x15D:
    case 0x15F:
    case 0x161:
    case 0x163:
    case 0x165:
    case 0x167:
    case 0x169:
    case 0x16B:
    case 0x16D:
    case 0x16F:
    case 0x171:
    case 0x173:
    case 0x175:
    case 0x177:
    {
        int id = 0x15C + frame.id() % 2;
        timestamp->msgCounter = data[2];
        timestamp->frameTimestamps[(frame.id() - id) / 2] = frame.timestemp();
        timestamp->valids[(frame.id() - id) / 2] = true;
        timestamp->channel = frame.channelIndex();
    }
        break;
    default:
        break;
    }
}

InterpolationGEELY::InterpolationGEELY(AnalysisWorker *analysisWorker)
    : IInterpolation(analysisWorker)
{

}

#define RADAR_ID_REAR_LEFT      4
#define RADAR_ID_REAR_RIGHT     5
#define RADAR_ID_FRONT_LEFT     6
#define RADAR_ID_FRONT_RIGHT    7

#define eDATA_LEN_64 64

#define LIMIT(val, min, max)  ((val) < (min) ? (min) : ((val) > (max) ? (max) : (val)))

static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
    uint32_t mask = ((1 << bits) - 1);
    uint32_t extracted = data & mask;
    int32_t sign_extended = (extracted & (1 << (bits - 1))) ? (int)(extracted | (~mask)) : (int)extracted;
    return sign_extended;
}

bool InterpolationGEELY::GEELY16TargetParse(quint8 radarID, Devices::Can::stCanTxMsg *frame)
{
    if (frame->dlc != 64) {
            return false;
        }

    const uint8_t *data = (const uint8_t *)frame->data;
    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount; // 注意必须使用引用
    if (data[20] != 0x00 && mGEELY16TargetCurrentIndex[radarID]++ < mGEELY16TargetCount[radarID]) {
        Target &target1 = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets[targetCount++];
        target1.mProtocolType = ProtocolGEELY;
        target1.mAx = ((decode_sign_bit(data[0], 8)) * 0.1);
        target1.mAy = ((decode_sign_bit(data[1], 8)) * 0.1);
        target1.mTrackFrameX = (((((data[3] & 0x80U) >> 7) + (((uint16_t)data[2] & 0xFFU) << 1)) * 0.1) - 25);
        target1.mTrackFrameY = (((((data[4] & 0xC0U) >> 6) + (((uint16_t)data[3] & 0x7FU) << 2)) * 0.1) - 25);
        target1.mTrackFrameLength = ((((data[5] & 0xE0U) >> 5) + (((uint16_t)data[4] & 0x3FU) << 3)) * 0.1);
        target1.mTrackFrameWidth = ((((data[6] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0x1FU) << 4)) * 0.1);
        target1.mObjectType = (data[6] & 0xFU);
        target1.mObservationHist = ((data[10] & 0xFFU) + (((uint32_t)data[9]) << 8) + (((uint32_t)data[8]) << 16) + (((uint32_t)data[7] & 0xFFU) << 24));
        target1.mChks = (data[11]);
        target1.mCntr = ((data[12] & 0xF0U) >> 4);
        target1.mConf = ((data[12] & 0xFU) * 7);
        target1.mXStd = ((data[13]) * 0.05);
        target1.mX = (((((data[15] & 0xF8U) >> 3) + (((uint16_t)data[14] & 0xFFU) << 5)) * 0.05) - 200);
        target1.mMtnPat = (data[15] & 0x7U);
        target1.mVy = (((((data[17] & 0xF0U) >> 4) + (((uint16_t)data[16] & 0xFFU) << 4)) * 0.02) - 35.6);
        target1.mVx = ((((data[18] & 0xFFU) + (((uint16_t)data[17] & 0xFU) << 8)) * 0.02) - 35.6);
        target1.mVyStd = (((data[19] & 0xFCU) >> 2) * 0.05);
        target1.mElevnSts = (data[19] & 0x3U);
        target1.mID = (data[20]);
        target1.mTypConfBike = (((data[21] & 0xF0U) >> 4) * 7);
        target1.mTypConfPed = ((data[21] & 0xFU) * 7);
        target1.mYStd = ((data[22]) * 0.05);
        target1.mY = (((((data[24] & 0xF8U) >> 3) + (((uint16_t)data[23] & 0xFFU) << 5)) * 0.05) - 200);
        target1.mTrackSts = ((data[24] & 0x6U) >> 1);
        target1.mIsInFreeSpace = (data[24] & 0x1U);
        target1.mVysog = (((((data[26] & 0xFCU) >> 2) + (((uint16_t)data[25] & 0xFFU) << 6)) * 0.02) - 128);
        target1.mElevnConf = (((data[27] & 0xF0U) >> 4) * 7);
        target1.mMirrProblty = ((data[27] & 0xFU) * 7);
        target1.mNotRealProblty = (((data[28] & 0xF0U) >> 4) * 7);
        target1.mTypConfVeh = ((data[28] & 0xFU) * 7);
        target1.mStatyCnt = ((data[29] & 0xFEU) >> 1);
        target1.mTiAlv = ((data[30] & 0xFEU) >> 1);
        target1.mVxsog = (((((data[32] & 0xFCU) >> 2) + (((uint16_t)data[31] & 0xFFU) << 6)) * 0.02) - 128);
        target1.mVxStd = (((data[33] & 0xFCU) >> 2) * 0.05);
        target1.mCoastCnt = ((data[34] & 0xF8U) >> 3);
        target1.mTrackFrameAngle = (((((data[36] & 0xF0U) >> 4) + (((uint16_t)data[35] & 0xFFU) << 4)) * 0.1) - 180);
////        target1.ReSideRdrLeObj1RdrObjUsedTracker = ((data[36] & 0xCU) >> 2);
////        target1.ReSideRdrLeObj1QualityBits = ((data[38] & 0xFFU) + (((uint32_t)data[37]) << 8) + (((uint32_t)data[36] & 0x3U) << 16));
////        target1.ReSideRdrLeObj1_UB = ((data[39] & 0x80U) >> 7);

        target1.mTrackFrameX += target1.mX;
        target1.mTrackFrameY += target1.mY;

        target1.mValid = true;//(target1.mX != 0 || target1.mY != 0);
        switch ((int)target1.mObjectType) {
        case 0:
            target1.mClass = ObjectUnknown;
            break;
        case 1:
            target1.mClass = ObjectCar;
            break;
        case 2:
            target1.mClass = ObjectMotorcycle;
            break;
        case 3:
            target1.mClass = ObjectTruck;
            break;
        case 4:
            target1.mClass = ObjectPedestran;
            break;
        case 5:
            target1.mClass = OjbectObstdVert_1;
            break;
        case 6:
            target1.mClass = OjbectObstdVert_2;
            break;
        case 7:
            target1.mClass = ObjectAnimal;
            break;
        case 8:
            target1.mClass = ObjectObjGen;
            break;
        case 9:
            target1.mClass = ObjectVehofUnknown;
            break;
        default:
            target1.mClass = ObjectUnknown;
            break;
        }

//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << radarID << target1.mID << target1.mX << target1.mY << target1.mObjectType << target1.mClass;
    }

    bool bEndFrame = false;
    if (frame->id == 0x178 || frame->id == 0x179) {
        mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mValid = true;
        mAnalysisWorker->analysisEnd(radarID, Frame16Track);
        bEndFrame = true;
//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << radarID << mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mValid;
    }

    return true;
}

#ifdef ALGORITHM_GEELY

static uint8_t calCrc8(uint8_t data[], uint8_t dataLen, uint8_t crcStartVal)
{
    uint8_t i,j;
    uint8_t polynomial = 0x1dU;
    uint8_t initValue = 0U;

    initValue = crcStartVal ^ (uint8_t)0x00U;

    for (i = 0; i < dataLen; i++)
    {
        initValue = initValue ^ data[i];
        for (j = 0; j < (uint8_t)8U; j++)
        {
            if((initValue & (uint8_t)0x80U) != (uint8_t)0U)
            {
                initValue = ((initValue << 1U) & (uint8_t)0xffU);
                initValue = initValue ^ polynomial;
            }
            else
            {
                initValue = ((initValue << 1U) & (uint8_t)0xffU);
            }
        }
    }
    initValue = initValue ^ (uint8_t)0x00U;

    return(initValue);
}


// 0x15c length = 64
typedef struct CT_SODLSafetyCANFD3Frame23 {
    uint32_t ReSideRdrLeObj1RdrObjAx;
    uint32_t ReSideRdrLeObj1RdrObjAy;
    uint32_t ReSideRdrLeObj1ObjBoxCenterLat;
    uint32_t ReSideRdrLeObj1ObjBoxCenterLgt;
    uint32_t ReSideRdrLeObj1RdrObjBoxLength;
    uint32_t ReSideRdrLeObj1RdrObjBoxWidth;
    uint32_t ReSideRdrLeObj1RdrObjTyp;
    uint32_t ReSideRdrLeObj1ObservationHist;
    uint32_t ReSideRdrLeObj1RdrObjChks;
    uint32_t ReSideRdrLeObj1RdrObjCntr;
    uint32_t ReSideRdrLeObj1RdrObjConf;
    uint32_t ReSideRdrLeObj1RdrObjDxStdDe;
    uint32_t ReSideRdrLeObj1RdrObjDy;
    uint32_t ReSideRdrLeObj1RdrObjMtnPat;
    uint32_t ReSideRdrLeObj1RelVx;
    uint32_t ReSideRdrLeObj1RelVy;
    uint32_t ReSideRdrLeObj1RdrObjVyStdDe;
    uint32_t ReSideRdrLeObj1RdrObjElevnSts;
    uint32_t ReSideRdrLeObj1RdrObjID;
    uint32_t ReSideRdrLeObj1RdrObjTypConfBike;
    uint32_t ReSideRdrLeObj1RdrObjTypConfPed;
    uint32_t ReSideRdrLeObj1RdrObjDyStdDe;
    uint32_t ReSideRdrLeObj1RdrObjDx;
    uint32_t ReSideRdrLeObj1RdrObjTrackSts;
    uint32_t ReSideRdrLeObj1RdrObjIsInFreeSpace;
    uint32_t ReSideRdrLeObj1RdrObjVy;
    uint32_t ReSideRdrLeObj1RdrObjElevnConf;
    uint32_t ReSideRdrLeObj1RdrObjMirrProblty;
    uint32_t ReSideRdrLeObj1RdrObjNotRealProblty;
    uint32_t ReSideRdrLeObj1RdrObjTypConfVeh;
    uint32_t ReSideRdrLeObj1RdrObjStatyCnt;
    uint32_t ReSideRdrLeObj1RdrObjTiAlv;
    uint32_t ReSideRdrLeObj1RdrObjVx;
    uint32_t ReSideRdrLeObj1RdrObjVxStdDe;
    uint32_t ReSideRdrLeObj1RdrObjCoastCnt;
    uint32_t ReSideRdrLeObj1Heading;
    uint32_t ReSideRdrLeObj1RdrObjUsedTracker;
    uint32_t ReSideRdrLeObj1QualityBits;
    uint32_t ReSideRdrLeObj1_UB;
}CT_SODLSafetyCANFD3Frame23_t;

static uint32_t encode_sign_bit(int32_t data, uint8_t bits) {
    uint32_t const m = 0x1u << (bits - 1);

    return (data & m) ? (data | m) : data;
}

// 0x15c length = 64
bool encode_SODLSafetyCANFD3Frame23(CT_SODLSafetyCANFD3Frame23_t &userData, uint8_t *data, int length) {
    if (length != 64) {
        return false;
    }

    data[0] = (userData.ReSideRdrLeObj1RdrObjAx & 0xFFU);
    data[1] = (userData.ReSideRdrLeObj1RdrObjAy & 0xFFU);
    data[2] = ((userData.ReSideRdrLeObj1ObjBoxCenterLat & 0x1FEU) >> 1);
    data[3] = (((userData.ReSideRdrLeObj1ObjBoxCenterLat & 0x1U) << 7) | ((userData.ReSideRdrLeObj1ObjBoxCenterLgt & 0x1FCU) >> 2));
    data[4] = (((userData.ReSideRdrLeObj1ObjBoxCenterLgt & 0x3U) << 6) | ((userData.ReSideRdrLeObj1RdrObjBoxLength & 0x1F8U) >> 3));
    data[5] = (((userData.ReSideRdrLeObj1RdrObjBoxLength & 0x7U) << 5) | ((userData.ReSideRdrLeObj1RdrObjBoxWidth & 0x1F0U) >> 4));
    data[6] = (((userData.ReSideRdrLeObj1RdrObjBoxWidth & 0xFU) << 4) | (userData.ReSideRdrLeObj1RdrObjTyp & 0xFU));
    data[7] = ((userData.ReSideRdrLeObj1ObservationHist & 0xFF000000U) >> 24);
    data[8] = ((userData.ReSideRdrLeObj1ObservationHist & 0xFF0000U) >> 16);
    data[9] = ((userData.ReSideRdrLeObj1ObservationHist & 0xFF00U) >> 8);
    data[10] = ((userData.ReSideRdrLeObj1ObservationHist & 0xFFU));
    data[11] = (userData.ReSideRdrLeObj1RdrObjChks & 0xFFU);
    data[12] = (((userData.ReSideRdrLeObj1RdrObjCntr & 0xFU) << 4) | (userData.ReSideRdrLeObj1RdrObjConf & 0xFU));
    data[13] = (userData.ReSideRdrLeObj1RdrObjDxStdDe & 0xFFU);
    data[14] = ((userData.ReSideRdrLeObj1RdrObjDy & 0x1FE0U) >> 5);
    data[15] = (((userData.ReSideRdrLeObj1RdrObjDy & 0x1FU) << 3) | (userData.ReSideRdrLeObj1RdrObjMtnPat & 0x7U));
    data[16] = ((userData.ReSideRdrLeObj1RelVx & 0xFF0U) >> 4);
    data[17] = (((userData.ReSideRdrLeObj1RelVx & 0xFU) << 4) | ((userData.ReSideRdrLeObj1RelVy & 0xF00U) >> 8));
    data[18] = ((userData.ReSideRdrLeObj1RelVy & 0xFFU));
    data[19] = (((userData.ReSideRdrLeObj1RdrObjVyStdDe & 0x3FU) << 2) | (userData.ReSideRdrLeObj1RdrObjElevnSts & 0x3U));
    data[20] = (userData.ReSideRdrLeObj1RdrObjID & 0xFFU);
    data[21] = (((userData.ReSideRdrLeObj1RdrObjTypConfBike & 0xFU) << 4) | (userData.ReSideRdrLeObj1RdrObjTypConfPed & 0xFU));
    data[22] = (userData.ReSideRdrLeObj1RdrObjDyStdDe & 0xFFU);
    data[23] = ((userData.ReSideRdrLeObj1RdrObjDx & 0x1FE0U) >> 5);
    data[24] = (((userData.ReSideRdrLeObj1RdrObjDx & 0x1FU) << 3) | ((userData.ReSideRdrLeObj1RdrObjTrackSts & 0x3U) << 1) | (userData.ReSideRdrLeObj1RdrObjIsInFreeSpace & 0x1U));
    data[25] = ((userData.ReSideRdrLeObj1RdrObjVy & 0x3FC0U) >> 6);
    data[26] = ((userData.ReSideRdrLeObj1RdrObjVy & 0x3FU) << 2);
    data[27] = (((userData.ReSideRdrLeObj1RdrObjElevnConf & 0xFU) << 4) | (userData.ReSideRdrLeObj1RdrObjMirrProblty & 0xFU));
    data[28] = (((userData.ReSideRdrLeObj1RdrObjNotRealProblty & 0xFU) << 4) | (userData.ReSideRdrLeObj1RdrObjTypConfVeh & 0xFU));
    data[29] = ((userData.ReSideRdrLeObj1RdrObjStatyCnt & 0x7FU) << 1);
    data[30] = ((userData.ReSideRdrLeObj1RdrObjTiAlv & 0x7FU) << 1);
    data[31] = ((userData.ReSideRdrLeObj1RdrObjVx & 0x3FC0U) >> 6);
    data[32] = ((userData.ReSideRdrLeObj1RdrObjVx & 0x3FU) << 2);
    data[33] = ((userData.ReSideRdrLeObj1RdrObjVxStdDe & 0x3FU) << 2);
    data[34] = ((userData.ReSideRdrLeObj1RdrObjCoastCnt & 0x1FU) << 3);
    data[35] = ((userData.ReSideRdrLeObj1Heading & 0xFF0U) >> 4);
    data[36] = (((userData.ReSideRdrLeObj1Heading & 0xFU) << 4) | ((userData.ReSideRdrLeObj1RdrObjUsedTracker & 0x3U) << 2) | ((userData.ReSideRdrLeObj1QualityBits & 0x30000U) >> 16));
    data[37] = ((userData.ReSideRdrLeObj1QualityBits & 0xFF00U) >> 8);
    data[38] = ((userData.ReSideRdrLeObj1QualityBits & 0xFFU));
    data[39] = ((userData.ReSideRdrLeObj1_UB & 0x1U) << 7);
    data[40] = 0;
    data[41] = 0;
    data[42] = 0;
    data[43] = 0;
    data[44] = 0;
    data[45] = 0;
    data[46] = 0;
    data[47] = 0;
    data[48] = 0;
    data[49] = 0;
    data[50] = 0;
    data[51] = 0;
    data[52] = 0;
    data[53] = 0;
    data[54] = 0;
    data[55] = 0;
    data[56] = 0;
    data[57] = 0;
    data[58] = 0;
    data[59] = 0;
    data[60] = 0;
    data[61] = 0;
    data[62] = 0;
    data[63] = 0;

    return true;
}

uint32_t COMLIB_calcPhyToHexCanData(float physicalValue, float factor, float offset)
{
    uint32_t val;

    if (factor > 0.00001f)
    {
        physicalValue = (physicalValue - offset) / factor;
    }

    val = (uint32_t)((int32_t)(roundf(physicalValue)));

    return val;
}

int InterpolationGEELY::encodeFrame(int radarID, Devices::Can::FrameTimestamp *timestamp, RDP_TrkFrameInfoGeely2_0 *outputObjList, int16_t trkValidNum, uint8_t msgCounter, Devices::Can::stCanTxMsg *frameArray)
{
    uint16_t objIndex = 0;
    uint32_t objCanIdStart;
    uint16_t dataIdHeader;
    uint16_t dataIdObjStart;

    uint8_t CheckArr[55];

    mGEELY16TargetCount[radarID] = trkValidNum;
    mGEELY16TargetCurrentIndex[radarID] = 0;

    AnalysisData &analysisData = mAnalysisWorker->mAnalysisDatas[radarID];
    Targets &_16Targets = analysisData.m16Targets;
    _16Targets.clear();


    int frameCount = 0;
    Devices::Can::stCanTxMsg *frame = frameArray + frameCount++;
    // 0x129
    switch (radarID)
    {
    case RADAR_ID_FRONT_LEFT:   // 6
        frame->id = 0x129U;
        objCanIdStart = 0x15CU;
        dataIdHeader = 2273;
        dataIdObjStart = 2228;
        break;
    case RADAR_ID_FRONT_RIGHT:  // 7
        frame->id = 0x12EU;
        objCanIdStart = 0x15DU;
        dataIdHeader = 2448;
        dataIdObjStart = 2403;
        break;
    case RADAR_ID_REAR_LEFT:    // 4
        frame->id = 0x129U;
        objCanIdStart = 0x15CU;
        dataIdHeader = 2623;
        dataIdObjStart = 2578;
        break;
    case RADAR_ID_REAR_RIGHT:   // 5
        frame->id = 0x12EU;
        objCanIdStart = 0x15DU;
        dataIdHeader = 2798;
        dataIdObjStart = 2753;
        break;
    default:
        frame->id = 0x129U;
        objCanIdStart = 0x15CU;
        dataIdHeader = 2623;
        dataIdObjStart = 2578;
        break;
    }

    memcpy(frame, &(timestamp->_0x129), sizeof (Devices::Can::stCanTxMsg));
    frame->valid = true;

    int rollingCnt = ((timestamp->_0x129.data[17] & 0xF0U) >> 4);
    CheckArr[0] = dataIdHeader & 0xFF;//DATAID LSB
    CheckArr[1] = dataIdHeader >> 8;//DATAID MSB
    CheckArr[2] = rollingCnt;
    CheckArr[3] = frame->data[25];
    CheckArr[4] = trkValidNum;
    CheckArr[5] = (frame->data[27] & 0x1U);
    CheckArr[6] = ((frame->data[27] & 0xFEU) >> 1);
    CheckArr[7] = ((frame->data[29] & 0xFEU) >> 1);
    CheckArr[8] = (frame->data[29] & 0x1U);
    CheckArr[9] = ((frame->data[30] & 0x80U) >> 7);
    CheckArr[10] = (frame->data[17] & 0xFU);
    CheckArr[11] = ((frame->data[18] & 0xF0U) >> 4);
    CheckArr[12] = (frame->data[24] & 0xFFU);
    CheckArr[13] = (frame->data[23] & 0xFFU);
    CheckArr[14] = ((frame->data[18] & 0xEU) >> 1);
    CheckArr[15] = (frame->data[18] & 0x1U);
    CheckArr[16] = ((frame->data[30] & 0x60U) >> 5);
    CheckArr[17] = (frame->data[22] & 0xFFU);
    CheckArr[18] = (frame->data[21] & 0xFFU);
    CheckArr[19] = (frame->data[20] & 0xFFU);
    CheckArr[20] = (frame->data[19] & 0xFFU);
    CheckArr[21] = ((frame->data[30] & 0x10U) >> 4);
    CheckArr[22] = (frame->data[28] & 0x1U);
    CheckArr[23] = ((frame->data[28] & 0x2U) >> 1);
    CheckArr[24] = ((frame->data[28] & 0x4U) >> 2);
    CheckArr[25] = 0;//((((frame->data[28] & 0xF8U) >> 3) * 0.005) + 0.92);
    frame->data[26] = trkValidNum;
    frame->data[16] = calCrc8(CheckArr, 26, 0x00U);

    // frame23
    uint16_t ObjectDistLong = 0u;
    uint16_t ObjectDistLat = 0u;
    uint16_t ObjectVrelLong = 0u;
    uint16_t ObjectVrelLat = 0u;
    uint16_t ObjectLength = 0u;
    uint16_t ObjectWidth = 0u;
    uint16_t ObjectArelLong = 0u;
    uint16_t ObjectArelLat = 0u;

    uint16_t ObjHeading= 0u;
    uint16_t BoxCenterLat= 0u;
    uint16_t ObjBoxCenterLgt= 0u;
    uint32_t ObservationHist= 0u;
    uint32_t QualityBits= 0u;
    uint8_t ObjCoastCnt= 0u;
    uint8_t ObjConf= 0u;
    uint8_t ObjDxStdDe= 0u;
    uint8_t ObjDyStdDe= 0u;
    uint8_t ObjElevnConf= 0u;
    uint8_t ObjElevnSts= 0u;
    uint8_t ObjIsInFreeSpace= 0u;
    uint8_t ObjMirrProblty= 0u;
    uint8_t RdrObjMtnPat= 0u;
    uint8_t ObjNotRealProblty= 0u;
    uint8_t ObjStatyCnt= 0u;
    uint8_t ObjTiAlv= 0u;
    uint8_t ObjTrackSts= 0u;
    uint8_t ObjTyp= 0u;
    uint8_t ObjTypConfBike= 0u;
    uint8_t ObjTypConfPed = 0u;
    uint8_t ObjTypConfVeh= 0u;
    uint8_t ObjUsedTracker= 0u;
    uint8_t ObjVxStdDe= 0u;
    uint8_t ObjVyStdDe= 0u;
    uint16_t RelVx= 0u;
    uint16_t RelVy= 0u;

    uint16_t DataID;

    const rdp_config_t* config = RDP_getTrackConfigPointer();
    for (objIndex = 0; objIndex < trkValidNum; objIndex++)
    {
        Devices::Can::stCanTxMsg *frame = frameArray + frameCount++;
        const ObjInfoGeely2_0 *obj = &outputObjList->obj[objIndex];

        frame->id = objCanIdStart + 2U * objIndex;
        frame->dlc = eDATA_LEN_64;
        frame->valid = true;
        frame->timestamp = timestamp->frameTimestamps[objIndex];
        uint8_t *data = frame->data;
        memset((void *)data, 0, sizeof(frame->dlc));

        CT_SODLSafetyCANFD3Frame23_t target;
        target.ReSideRdrLeObj1_UB = 1;

        DataID = dataIdObjStart + objIndex;
        CheckArr[0] = DataID & 0xFF;
        CheckArr[1] = DataID >> 8;

        CheckArr[2] = rollingCnt;
        target.ReSideRdrLeObj1RdrObjCntr = rollingCnt;

        ObjHeading  = COMLIB_calcPhyToHexCanData(LIMIT(obj->Heading, -180.f, 180.f), 0.1f, -180.0f);
        CheckArr[3] = ObjHeading & 0xFF;
        CheckArr[4] = ObjHeading >> 8 & 0x0F;
        target.ReSideRdrLeObj1Heading = ObjHeading;

        BoxCenterLat  = COMLIB_calcPhyToHexCanData(LIMIT(obj->ObjBoxCenterLat, -25.f, 25.f), 0.1f, -25.0f);
        CheckArr[5] = BoxCenterLat & 0xFF;
        CheckArr[6] = BoxCenterLat >> 8 & 0x01;
        target.ReSideRdrLeObj1ObjBoxCenterLat = BoxCenterLat;

        ObjBoxCenterLgt  = COMLIB_calcPhyToHexCanData(LIMIT(obj->ObjBoxCenterLgt, -25.f, 25.f), 0.1f, -25.0f);
        CheckArr[7] = ObjBoxCenterLgt & 0xFF;
        CheckArr[8] = ObjBoxCenterLgt >> 8 & 0x01;
        target.ReSideRdrLeObj1ObjBoxCenterLgt = ObjBoxCenterLgt;

        ObservationHist  = obj->ObservationHist;
        CheckArr[9] = ObservationHist & 0xFF;
        CheckArr[10] = ObservationHist >> 8 & 0xFF;
        CheckArr[11] = ObservationHist >> 16 & 0xFF;
        CheckArr[12] = ObservationHist >> 24 & 0xFF;
        target.ReSideRdrLeObj1ObservationHist = ObservationHist;

        QualityBits  = 0; // resv
        CheckArr[13] = QualityBits & 0xFF;
        CheckArr[14] = QualityBits >> 8 & 0xFF;
        CheckArr[15] = QualityBits >> 16 & 0x03;
        target.ReSideRdrLeObj1QualityBits = QualityBits;

        ///目标纵向加速度
        ObjectArelLong =  COMLIB_calcPhyToHexCanData(LIMIT(obj->Ax, -12.8f, 12.7f), 0.1f, 0.0f);
        CheckArr[16] = ObjectArelLong & 0xFF;
        target.ReSideRdrLeObj1RdrObjAx = ObjectArelLong;

        ///目标横向加速度
        ObjectArelLat = COMLIB_calcPhyToHexCanData(LIMIT(obj->Ay, -12.8f, 12.7f), 0.1f, 0.0f);
        CheckArr[17] = ObjectArelLat & 0xFF;
        target.ReSideRdrLeObj1RdrObjAy = ObjectArelLat;

        ///目标长度
        ObjectLength = COMLIB_calcPhyToHexCanData(LIMIT(obj->BoxLength, 0.f, 51.f), 0.1f, 0.0f);
        CheckArr[18] = ObjectLength & 0xFF;
        CheckArr[19] = ObjectLength >> 8 & 0x01;
        target.ReSideRdrLeObj1RdrObjBoxLength = ObjectLength;

        ///目标宽度
        ObjectWidth = COMLIB_calcPhyToHexCanData(LIMIT(obj->BoxWidth, 0.f, 51.f), 0.1f, 0.0f);
        CheckArr[20] = ObjectWidth & 0xFF;
        CheckArr[21] = ObjectWidth >>8 & 0x01;
        target.ReSideRdrLeObj1RdrObjBoxWidth = ObjectWidth;

        ObjCoastCnt = obj->CoastCnt > 6 ? 6 : obj->CoastCnt;
        CheckArr[22] = ObjCoastCnt & 0xFF;
        target.ReSideRdrLeObj1RdrObjCoastCnt = ObjCoastCnt;

        ObjConf  = ((uint32_t)obj->Conf * 105) / 700;// 0% ~ 105%
        CheckArr[23] = ObjConf & 0xFF;
        target.ReSideRdrLeObj1RdrObjConf = ObjConf;

        ///目标纵向距离
        ObjectDistLong = COMLIB_calcPhyToHexCanData(LIMIT(obj->Dx + config->installOffsetX, -200.f, 200.f), 0.05f, -200.f);
        CheckArr[24] = ObjectDistLong & 0xFF;
        CheckArr[25] = ObjectDistLong >> 8 & 0x1F;
        target.ReSideRdrLeObj1RdrObjDx = ObjectDistLong;

        ObjDxStdDe  = COMLIB_calcPhyToHexCanData(LIMIT(obj->DxStdDe, 0.f, 12.7f), 0.05f, 0.0f);
        CheckArr[26] = ObjDxStdDe & 0xFF;
        target.ReSideRdrLeObj1RdrObjDxStdDe = ObjDxStdDe;

        ///目标横向距离
        ObjectDistLat = COMLIB_calcPhyToHexCanData(LIMIT(obj->Dy + config->installOffsetY, -200.f, 200.f), 0.05f, -200.f);
        CheckArr[27] = ObjectDistLat & 0xFF;
        CheckArr[28] = ObjectDistLat >> 8 & 0x1F;
        target.ReSideRdrLeObj1RdrObjDy = ObjectDistLat;

        ObjDyStdDe = COMLIB_calcPhyToHexCanData(LIMIT(obj->DyStdDe, 0.f, 12.7f), 0.05f, 0.0f);
        CheckArr[29] = ObjDyStdDe & 0xFF;
        target.ReSideRdrLeObj1RdrObjDyStdDe = ObjDyStdDe;

        ObjElevnConf = ((uint32_t)obj->ElevnConf * 105) / 700;// 0% ~ 105%
        CheckArr[30] = ObjElevnConf & 0xFF;
        target.ReSideRdrLeObj1RdrObjElevnConf = ObjElevnConf;

        ObjElevnSts = obj->ElevnSts; //
        CheckArr[31] = ObjElevnSts & 0xFF;
        target.ReSideRdrLeObj1RdrObjElevnSts = ObjElevnSts;

        CheckArr[32] = obj->ID;
        target.ReSideRdrLeObj1RdrObjID = obj->ID;

        ObjIsInFreeSpace = obj->IsInFreeSpace; //
        CheckArr[33] = ObjIsInFreeSpace & 0xFF;
        target.ReSideRdrLeObj1RdrObjIsInFreeSpace = ObjIsInFreeSpace;

        ObjMirrProblty = 0;// 0% ~ 105%
        CheckArr[34] = ObjMirrProblty & 0xFF;
        target.ReSideRdrLeObj1RdrObjMirrProblty = ObjMirrProblty;

        RdrObjMtnPat = obj->MtnPat;//
        CheckArr[35] = RdrObjMtnPat & 0xFF;
        target.ReSideRdrLeObj1RdrObjMtnPat = RdrObjMtnPat;

        ObjNotRealProblty = ((uint32_t)obj->NotRealProblty * 105) / 700;
        CheckArr[36] = ObjNotRealProblty & 0xFF;
        target.ReSideRdrLeObj1RdrObjNotRealProblty = ObjNotRealProblty;

        ObjStatyCnt = 0; // TODO 静止计数
        CheckArr[37] = ObjStatyCnt & 0xFF;
        target.ReSideRdrLeObj1RdrObjStatyCnt = ObjStatyCnt;

        ObjTiAlv = obj->TiAlv;
        CheckArr[38] = ObjTiAlv & 0xFF;
        target.ReSideRdrLeObj1RdrObjTiAlv = ObjTiAlv;

        ObjTrackSts = obj->TrackSts; //
        CheckArr[39] = ObjTrackSts & 0xFF;
        target.ReSideRdrLeObj1RdrObjTrackSts = ObjTrackSts;

        ObjTyp = obj->Typ;//
        CheckArr[40] = ObjTyp & 0xFF;
        target.ReSideRdrLeObj1RdrObjTyp = ObjTyp;

        ObjTypConfBike = ((uint32_t)obj->TypConfBike * 105) / 700;//
        CheckArr[41] = ObjTypConfBike & 0xFF;
        target.ReSideRdrLeObj1RdrObjTypConfBike = ObjTypConfBike;

        ObjTypConfPed = ((uint32_t)obj->TypConfPed * 105) / 700;//
        CheckArr[42] = ObjTypConfPed & 0xFF;
        target.ReSideRdrLeObj1RdrObjTypConfPed = ObjTypConfPed;

        ObjTypConfVeh = ((uint32_t)obj->TypConfVeh * 105) / 700;//
        CheckArr[43] = ObjTypConfVeh & 0xFF;
        target.ReSideRdrLeObj1RdrObjTypConfVeh = ObjTypConfVeh;

        ObjUsedTracker= 0; // resv?
        CheckArr[44] = ObjUsedTracker & 0xFF;
        target.ReSideRdrLeObj1RdrObjUsedTracker = ObjUsedTracker;

        ObjectVrelLong = COMLIB_calcPhyToHexCanData(LIMIT(obj->Vx, -128.f, 128.f), 0.02f, -128.0f);// Vx非相对速度
        CheckArr[45] = ObjectVrelLong & 0xFF;
        CheckArr[46] = ObjectVrelLong >> 8 & 0x3F;
        target.ReSideRdrLeObj1RdrObjVx = ObjectVrelLong;

        ObjVxStdDe = COMLIB_calcPhyToHexCanData(LIMIT(obj->VxStdDe, 0.f, 2.f), 0.05f, 0.0f);
        CheckArr[47] = ObjVxStdDe & 0xFF;
        target.ReSideRdrLeObj1RdrObjVxStdDe = ObjVxStdDe;

        ObjectVrelLat = COMLIB_calcPhyToHexCanData(LIMIT(obj->Vy, -128.f, 128.f), 0.02f, -128.0f);// Vy非相对速度
        CheckArr[48] = ObjectVrelLat & 0xFF;
        CheckArr[49] = ObjectVrelLat >> 8 & 0x3F;
        target.ReSideRdrLeObj1RdrObjVy = ObjectVrelLat;

        ObjVyStdDe = COMLIB_calcPhyToHexCanData(LIMIT(obj->VyStdDe, 0.f, 2.f), 0.05f, 0.0f);
        CheckArr[50] = ObjVyStdDe & 0xFF;
        target.ReSideRdrLeObj1RdrObjVyStdDe = ObjVyStdDe;

        RelVx = COMLIB_calcPhyToHexCanData(LIMIT(obj->RelVx, -35.6f, 35.6f), 0.02f, -35.6f);
        CheckArr[51] = RelVx & 0xFF;
        CheckArr[52] = RelVx >> 8 & 0x0F;
        target.ReSideRdrLeObj1RelVx = RelVx;

        RelVy = COMLIB_calcPhyToHexCanData(LIMIT(obj->RelVy, -35.6f, 35.6f), 0.02f, -35.6f);
        CheckArr[53] = RelVy & 0xFF;
        CheckArr[54] = RelVy >> 8 & 0x0F;
        target.ReSideRdrLeObj1RelVy = RelVy;

        target.ReSideRdrLeObj1RdrObjChks = calCrc8(CheckArr, 55U, 0x00U);

        encode_SODLSafetyCANFD3Frame23(target, frame->data, frame->dlc);

        GEELY16TargetParse(radarID, frame);
    }

    for (objIndex = trkValidNum; objIndex < OUTPUT_OBJ_NUM_MAX; objIndex++)
    {

        Devices::Can::stCanTxMsg *frame = frameArray + frameCount++;
        const ObjInfoGeely2_0 *obj = &outputObjList->obj[objIndex];

        frame->id = objCanIdStart + 2U * objIndex;
        frame->dlc = eDATA_LEN_64;
        frame->valid = true;
        frame->timestamp = timestamp->frameTimestamps[objIndex];
        uint8_t *data = frame->data;
        memset((void *)data, 0, sizeof(frame->dlc));

        CT_SODLSafetyCANFD3Frame23_t target;
        target.ReSideRdrLeObj1_UB = 1;

        DataID = dataIdObjStart + objIndex;
        CheckArr[0] = DataID & 0xFF;
        CheckArr[1] = DataID >> 8;

        CheckArr[2] = rollingCnt;
        target.ReSideRdrLeObj1RdrObjCntr = rollingCnt;

        ObjHeading  = COMLIB_calcPhyToHexCanData(0.0f, 0.1f, -180.0f);
        CheckArr[3] = ObjHeading & 0xFF;
        CheckArr[4] = ObjHeading >> 8 & 0x0F;
        target.ReSideRdrLeObj1Heading = ObjHeading;

        BoxCenterLat  = COMLIB_calcPhyToHexCanData(0.0f, 0.1f, -25.0f);
        CheckArr[5] = BoxCenterLat & 0xFF;
        CheckArr[6] = BoxCenterLat >> 8 & 0x01;
        target.ReSideRdrLeObj1ObjBoxCenterLat = BoxCenterLat;

        ObjBoxCenterLgt  = COMLIB_calcPhyToHexCanData(0.0f, 0.1f, -25.0f);
        CheckArr[7] = ObjBoxCenterLgt & 0xFF;
        CheckArr[8] = ObjBoxCenterLgt >> 8 & 0x01;
        target.ReSideRdrLeObj1ObjBoxCenterLgt = ObjBoxCenterLgt;

        ObservationHist  = 0;
        CheckArr[9] = ObservationHist & 0xFF;
        CheckArr[10] = ObservationHist >> 8 & 0xFF;
        CheckArr[11] = ObservationHist >> 16 & 0xFF;
        CheckArr[12] = ObservationHist >> 24 & 0xFF;
        target.ReSideRdrLeObj1ObservationHist = ObservationHist;

        QualityBits  = 0; // resv
        CheckArr[13] = QualityBits & 0xFF;
        CheckArr[14] = QualityBits >> 8 & 0xFF;
        CheckArr[15] = QualityBits >> 16 & 0x03;
        target.ReSideRdrLeObj1QualityBits = QualityBits;

        ///目标纵向加速度
        ObjectArelLong =  COMLIB_calcPhyToHexCanData(0.0f, 0.1f, 0.0f);
        CheckArr[16] = ObjectArelLong & 0xFF;
        target.ReSideRdrLeObj1RdrObjAx = ObjectArelLong;

        ///目标横向加速度
        ObjectArelLat = COMLIB_calcPhyToHexCanData(0.0f, 0.1f, 0.0f);
        CheckArr[17] = ObjectArelLat & 0xFF;
        target.ReSideRdrLeObj1RdrObjAy = ObjectArelLat;

        ///目标长度
        ObjectLength = COMLIB_calcPhyToHexCanData(0.0f, 0.1f, 0.0f);
        CheckArr[18] = ObjectLength & 0xFF;
        CheckArr[19] = ObjectLength >> 8 & 0x01;
        target.ReSideRdrLeObj1RdrObjBoxLength = ObjectLength;

        ///目标宽度
        ObjectWidth = COMLIB_calcPhyToHexCanData(0.0f, 0.1f, 0.0f);
        CheckArr[20] = ObjectWidth & 0xFF;
        CheckArr[21] = ObjectWidth >>8 & 0x01;
        target.ReSideRdrLeObj1RdrObjBoxWidth = ObjectWidth;

        ObjCoastCnt = 31;
        CheckArr[22] = ObjCoastCnt & 0xFF;
        target.ReSideRdrLeObj1RdrObjCoastCnt = ObjCoastCnt;

        ObjConf = 0;// 0% ~ 105%
        CheckArr[23] = ObjConf & 0xFF;
        target.ReSideRdrLeObj1RdrObjConf = ObjConf;

        ///目标纵向距离
        ObjectDistLong = COMLIB_calcPhyToHexCanData(-200.f, 0.05f, -200.f);
        CheckArr[24] = ObjectDistLong & 0xFF;
        CheckArr[25] = ObjectDistLong >> 8 & 0x1F;
        target.ReSideRdrLeObj1RdrObjDx = ObjectDistLong;

        ObjDxStdDe  = COMLIB_calcPhyToHexCanData(0.0f, 0.05f, 0.0f);
        CheckArr[26] = ObjDxStdDe & 0xFF;
        target.ReSideRdrLeObj1RdrObjDxStdDe = ObjDxStdDe;

        ///目标横向距离
        ObjectDistLat = COMLIB_calcPhyToHexCanData(0.0f, 0.05f, -200.f);
        CheckArr[27] = ObjectDistLat & 0xFF;
        CheckArr[28] = ObjectDistLat >> 8 & 0x1F;
        target.ReSideRdrLeObj1RdrObjDy = ObjectDistLat;

        ObjDyStdDe = COMLIB_calcPhyToHexCanData(0.0f, 0.05f, 0.0f);
        CheckArr[29] = ObjDyStdDe & 0xFF;
        target.ReSideRdrLeObj1RdrObjDyStdDe = ObjDyStdDe;

        ObjElevnConf = 0;
        CheckArr[30] = ObjElevnConf & 0xFF;
        target.ReSideRdrLeObj1RdrObjElevnConf = ObjElevnConf;

        ObjElevnSts = 0;
        CheckArr[31] = ObjElevnSts & 0xFF;
        target.ReSideRdrLeObj1RdrObjElevnSts = ObjElevnSts;

        CheckArr[32] = 0; // objID init value
        target.ReSideRdrLeObj1RdrObjID = 0;

        ObjIsInFreeSpace = 1;
        CheckArr[33] = ObjIsInFreeSpace & 0xFF;
        target.ReSideRdrLeObj1RdrObjIsInFreeSpace = ObjIsInFreeSpace;

        ObjMirrProblty = 0;
        CheckArr[34] = ObjMirrProblty & 0xFF;
        target.ReSideRdrLeObj1RdrObjMirrProblty = ObjMirrProblty;

        RdrObjMtnPat = 3;
        CheckArr[35] = RdrObjMtnPat & 0xFF;
        target.ReSideRdrLeObj1RdrObjMtnPat = RdrObjMtnPat;

        ObjNotRealProblty = 0;
        CheckArr[36] = ObjNotRealProblty & 0xFF;
        target.ReSideRdrLeObj1RdrObjNotRealProblty = ObjNotRealProblty;

        ObjStatyCnt = 0;
        CheckArr[37] = ObjStatyCnt & 0xFF;
        target.ReSideRdrLeObj1RdrObjStatyCnt = ObjStatyCnt;

        ObjTiAlv = 0;
        CheckArr[38] = ObjTiAlv & 0xFF;
        target.ReSideRdrLeObj1RdrObjTiAlv = ObjTiAlv;

        ObjTrackSts = 0;
        CheckArr[39] = ObjTrackSts & 0xFF;
        target.ReSideRdrLeObj1RdrObjTrackSts = ObjTrackSts;

        ObjTyp = 0;
        CheckArr[40] = ObjTyp & 0xFF;
        target.ReSideRdrLeObj1RdrObjTyp = ObjTyp;

        ObjTypConfBike = 0;
        CheckArr[41] = ObjTypConfBike & 0xFF;
        target.ReSideRdrLeObj1RdrObjTypConfBike = ObjTypConfBike;

        ObjTypConfPed = 0;
        CheckArr[42] = ObjTypConfPed & 0xFF;
        target.ReSideRdrLeObj1RdrObjTypConfPed = ObjTypConfPed;

        ObjTypConfVeh = 0;
        CheckArr[43] = ObjTypConfVeh & 0xFF;
        target.ReSideRdrLeObj1RdrObjTypConfVeh = ObjTypConfVeh;

        ObjUsedTracker = 3;
        CheckArr[44] = ObjUsedTracker & 0xFF;
        target.ReSideRdrLeObj1RdrObjUsedTracker = ObjUsedTracker;

        ObjectVrelLong = COMLIB_calcPhyToHexCanData(0.0f, 0.02f, -128.0f);
        CheckArr[45] = ObjectVrelLong & 0xFF;
        CheckArr[46] = ObjectVrelLong >> 8 & 0x3F;
        target.ReSideRdrLeObj1RdrObjVx = ObjectVrelLong;

        ObjVxStdDe = COMLIB_calcPhyToHexCanData(0.0f, 0.05f, 0.0f);
        CheckArr[47] = ObjVxStdDe & 0xFF;
        target.ReSideRdrLeObj1RdrObjVxStdDe = ObjVxStdDe;

        ObjectVrelLat = COMLIB_calcPhyToHexCanData(0.0f, 0.02f, -128.0f);
        CheckArr[48] = ObjectVrelLat & 0xFF;
        CheckArr[49] = ObjectVrelLat >> 8 & 0x3F;
        target.ReSideRdrLeObj1RdrObjVy = ObjectVrelLat;

        ObjVyStdDe = COMLIB_calcPhyToHexCanData(0.0f, 0.05f, 0.0f);
        CheckArr[50] = ObjVyStdDe & 0xFF;
        target.ReSideRdrLeObj1RdrObjVyStdDe = ObjVyStdDe;

        RelVx = COMLIB_calcPhyToHexCanData(0.0f, 0.02f, -35.6f);
        CheckArr[51] = RelVx & 0xFF;
        CheckArr[52] = RelVx >> 8 & 0x0F;
        target.ReSideRdrLeObj1RelVx = RelVx;

        RelVy = COMLIB_calcPhyToHexCanData(0.0f, 0.02f, -35.6f);
        CheckArr[53] = RelVy & 0xFF;
        CheckArr[54] = RelVy >> 8 & 0x0F;
        target.ReSideRdrLeObj1RelVy = RelVy;

        target.ReSideRdrLeObj1RdrObjChks = calCrc8(CheckArr, 55U, 0x00U);

        encode_SODLSafetyCANFD3Frame23(target, frame->data, frame->dlc);

        GEELY16TargetParse(radarID, frame);
    }

    _16Targets.mValid = true;

    return frameCount;
}
#endif

void InterpolationGEELY::setChannelRadarIDGEELY(int *channelRadarID, int size)
{
    for (int i = 0; i < size && i < (sizeof (mGEELYChannelRadarID) / sizeof (mGEELYChannelRadarID[0])); ++i) {
        mGEELYChannelRadarID[i] = channelRadarID[i];
    }
}

void InterpolationGEELY::canFrame(int radarID, const Devices::Can::CanFrame &frame)
{
    if (isTrackFrame(radarID, mGEELYChannelRadarID, frame)) {
    //        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex();
            mFirst = false;
            saveTimestamps(&mTimestamps, frame);
        } else {
            if (mFirst) {
                mFirst = false;
                mWriter->writeData(Devices::Can::CanFrame::New(frame.deviceIndex(),
                                                               frame.channelIndex(),
                                                               Devices::Can::CanFrame::TX,
                                                               frame.id(),
                                                               (const uint8_t *)frame.data().data(),
                                                               frame.DLC(),
                                                               frame.timestemp(),
                                                               frame.extended(),
                                                               frame.canFD()));
    //            qDebug() << __FUNCTION__ << __LINE__ << frame.timestemp() << QDateTime::fromMSecsSinceEpoch(frame.timestemp());
            }
        }

        injection(radarID, frame);
}

} // namespace Analysis
