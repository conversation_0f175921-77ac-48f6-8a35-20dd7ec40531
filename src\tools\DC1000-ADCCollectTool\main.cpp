﻿#include "dc1000adccollect.h"

#include "utils/loghandler.h"
#include "utils/settingshandler.h"

#include <QApplication>

int main(int argc, char *argv[])
{
//    SETTINGS_SET_FILE("tooldemosettings.ini");
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication a(argc, argv);

#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    a.setAttribute(Qt::AA_UseHighDpiPixmaps);
    a.setAttribute(Qt::AA_DisableWindowContextHelpButton);
#endif

    DC1000ADCCollect w;
    w.show();
    return a.exec();
}
