﻿#include "targetsviewconfigdialog.h"
#include "ui_targetsviewconfigdialog.h"


#include "utils/flowlayout.h"

#include <QHBoxLayout>
#include <QCheckBox>
#include <QPushButton>
#include <QColorDialog>
#include <QDebug>

DisplayWidget::DisplayWidget(const TargetsView::PointDisplay &pointDisplay, QWidget *parent)
    : QWidget(parent)
{
    mPointDisplay = pointDisplay;

    QHBoxLayout *layout = new QHBoxLayout(this);

    mPushButtonColor = new QPushButton(this);
    mPushButtonColor->setObjectName(QString::fromUtf8("pushButtonColor"));
    connect(mPushButtonColor, &QPushButton::clicked, this, [=](){
        QColor colorOld(QColor(ColorR(pointDisplay.mColor),
                               ColorG(pointDisplay.mColor),
                               ColorB(pointDisplay.mColor),
                               ColorA(pointDisplay.mColor)));
        QColor color = QColorDialog::getColor(colorOld, this, tr("Object Color"));
        qDebug() << __FUNCTION__ << __LINE__ << color << color.red() << color.green() << color.blue() << color.alpha();
        mPointDisplay.mColor = ColorRGBA(color.red(), color.green(), color.blue(), color.alpha());

        QPalette pal = mPushButtonColor->palette();
        pal.setColor(QPalette::Button, color);
        mPushButtonColor->setPalette(pal);
        mPushButtonColor->setAutoFillBackground(true);
        mPushButtonColor->setFlat(true);
    });
    layout->addWidget(mPushButtonColor);

    mCheckBoxDisplay = new QCheckBox(QString::fromLocal8Bit(pointDisplay.mName), this);
    mCheckBoxDisplay->setMinimumWidth(100);
    mCheckBoxDisplay->setChecked(pointDisplay.mDisplay);

    connect(mCheckBoxDisplay, &QCheckBox::clicked, this, [=](bool checked){
        mPointDisplay.mDisplay = checked;
    });

    layout->addWidget(mCheckBoxDisplay);


    QColor colorOld(QColor((uint16_t)ColorR(pointDisplay.mColor),
                           (uint16_t)ColorG(pointDisplay.mColor),
                           (uint16_t)ColorB(pointDisplay.mColor),
                           (uint16_t)ColorA(pointDisplay.mColor)));
    QPalette pal = mPushButtonColor->palette();
    pal.setColor(QPalette::Button, colorOld);
    mPushButtonColor->setPalette(pal);
    mPushButtonColor->setAutoFillBackground(true);
    mPushButtonColor->setFlat(true);
}

TargetsViewConfigDialog::TargetsViewConfigDialog(const TargetsView::ViewSettings &settings, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::TargetsViewConfigDialog)
{
    ui->setupUi(this);
    mFlowLayout = new FlowLayout;
    mFlowLayout->setSpacing(0);
    ui->verticalLayoutScrollArea->insertLayout(1, mFlowLayout);

    qDebug() << __FUNCTION__ << __LINE__
             << settings.mXmax << settings.mXmin << settings.mXInterval
             << settings.mYmax << settings.mYmin << settings.mYInterval
             << settings.mPointSizeRaw << settings.mPointSizeRawHalf;
    ui->doubleSpinBoxXmin              ->setValue(settings.mXmin);
    ui->doubleSpinBoxXmax              ->setValue(settings.mXmax);
    ui->doubleSpinBoxXInterval         ->setValue(settings.mXInterval);
    ui->doubleSpinBoxYmin              ->setValue(settings.mYmin);
    ui->doubleSpinBoxYmax              ->setValue(settings.mYmax);
    ui->doubleSpinBoxYInterval         ->setValue(settings.mYInterval);
    ui->doubleSpinBoxLocalVehicleLength->setValue(settings.mLocalVehicleLengthHalf * 2);
    ui->doubleSpinBoxLocalVehicleWidth ->setValue(settings.mLocalVehicleWidthHalf * 2);
    ui->spinBoxPointSizeRaw            ->setValue(settings.mPointSizeRaw);
    ui->spinBoxPointSizeTrack          ->setValue(settings.mPointSizeTrack);
    ui->checkBoxDisplayIDLabelRaw      ->setChecked(settings.mDisplayIDLabelRaw);
    ui->checkBoxDisplayIDLabelTrack    ->setChecked(settings.mDisplayIDLabelTrack);

    for (int i = 0; i < (sizeof (settings.mPointDisplay) / sizeof(settings.mPointDisplay[0])); ++i) {
        mFlowLayout->addWidget(new DisplayWidget(settings.mPointDisplay[i], this));
    }
}

TargetsViewConfigDialog::~TargetsViewConfigDialog()
{
    delete ui;
}

void TargetsViewConfigDialog::on_pushButtonOK_clicked()
{
    on_pushButtonApply_clicked();
    accept();
}

void TargetsViewConfigDialog::on_pushButtonApply_clicked()
{
    TargetsView::ViewSettings settings;

    settings.mXmin =                         ui->doubleSpinBoxXmin              ->value();
    settings.mXmax =                         ui->doubleSpinBoxXmax              ->value();
    settings.mXInterval =                    ui->doubleSpinBoxXInterval         ->value();
    settings.mYmin =                         ui->doubleSpinBoxYmin              ->value();
    settings.mYmax =                         ui->doubleSpinBoxYmax              ->value();
    settings.mYInterval =                    ui->doubleSpinBoxYInterval         ->value();
    settings.mLocalVehicleLengthHalf =       ui->doubleSpinBoxLocalVehicleLength->value() / 2.0f;
    settings.mLocalVehicleWidthHalf =        ui->doubleSpinBoxLocalVehicleWidth ->value() / 2.0f;
    settings.mPointSizeRaw =                 ui->spinBoxPointSizeRaw            ->value();
    settings.mPointSizeRawHalf =             settings.mPointSizeRaw / 2.0f;
    settings.mPointSizeTrack =               ui->spinBoxPointSizeTrack          ->value();
    settings.mPointSizeTrackHalf =           settings.mPointSizeTrack / 2.0f;

    settings.mDisplayIDLabelRaw =            ui->checkBoxDisplayIDLabelRaw      ->isChecked();
    settings.mDisplayIDLabelTrack =          ui->checkBoxDisplayIDLabelTrack    ->isChecked();


    for (int i = 0; i < mFlowLayout->count() && i < (sizeof (settings.mPointDisplay) / sizeof(settings.mPointDisplay[0])); ++i) {
        QLayoutItem *item = mFlowLayout->itemAt(i);
        DisplayWidget *widget = qobject_cast<DisplayWidget*>(item->widget());
        if (widget) {
            settings.mPointDisplay[i] = widget->getPointDisplay();
        }
    }


    qDebug() << __FUNCTION__ << __LINE__
             << settings.mXmax << settings.mXmin << settings.mXInterval
             << settings.mYmax << settings.mYmin << settings.mYInterval
             << settings.mPointSizeRaw << settings.mPointSizeRawHalf;

    emit changeSettings(settings);
}
