﻿#include "app_version.h"

#include <utils/settingshandler.h>
#include <utils/loghandler.h>
#include <master/master.h>

#include <QApplication>
#include <QThread>
#include <QDebug>

int main(int argc, char *argv[])
{
    //HEAP[ChengTech Radar Master 3.1.1.exe]: HEAP: Free Heap block 0CE1C488 modified at 0CE1C4B0 after it was freed
    SETTINGS_SET_FILE("settings.ini");
//    LOG_INSTALL("Log", "Log");
//    //LOG_HANDLER->setLogLevel(Utils::Log::LogHandler::InfoLog);
//    LOG_HANDLER->setLogLevel(Utils::Log::LogHandler::DebugLog);
//    qDebug() << __FUNCTION__ << __LINE__ << QThread::currentThreadId();



    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication app(argc, argv);

#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    app.setAttribute(Qt::AA_UseHighDpiPixmaps);
    app.setAttribute(Qt::AA_DisableWindowContextHelpButton);
#endif
    Core::Internal::Master master;
    master.initialize();

    return app.exec();
}
