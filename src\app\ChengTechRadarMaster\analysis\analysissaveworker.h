﻿#ifndef ANALYSISSAVEWORKER_H
#define ANALYSISSAVEWORKER_H

#include <QObject>
#include <QFile>

#include "analysisdata.h"
#include "analysisdataf.h"

namespace Analysis {

class AnalysisWorker;
class CalculationWorker;

class AnalysisSaveWorker : public QObject
{
    Q_OBJECT
public:
    explicit AnalysisSaveWorker(AnalysisWorker *analysisWorker, QObject *parent = nullptr);

    bool startSave(const QString &savePath, const QDateTime &beginTime, bool oldStyle, bool first = true, bool needResponse = false);
    bool stopSave();

    void setSaveCountMax(quint64 max) { mSaveCountMax = max; }

signals:
    void saveStarted(const QString &rawFilename, bool ok = true);
    void saveFinished();

public slots:
    void analysisRadarData(quint8 radarID, const AnalysisData &analysisData);
    void analysisTargets(quint8 radarID, /*AnalysisFrameType*/int frameType);
    void analysisTargetsF(Parser::ParsedDataTypedef::TargetsF targets);
    void analysisEarlyAlarmData(quint8 radarID, const AlarmData &alarmData);
    void saveResponsed(bool yes) { mResponsed = yes; }

private:
    bool save(quint8 radarID);
    bool saveData(quint8 radarID);
    bool saveAlarm(quint8 radarID, QDateTime &saveTime, QFile &file, AlarmData &alarmData, qint64 &radarAlarmID, quint64 &radarAlarmIDCount, quint64 &radarAlarmCount);
    bool saveAlarm(quint8 radarID, QDateTime &saveTime);
    bool saveAlarm2( quint8 radarID, QDateTime& saveTime);
    bool saveEarlyWarning(quint8 radarID, QDateTime &saveTime);
    bool save200Targets(quint8 radarID, QDateTime &saveTime);
    bool save16Targets(quint8 radarID, QDateTime &saveTime);
    bool saveTargets(quint8 radarID,  AnalysisFrameType frameType, QDateTime &saveTime);
    bool saveOldTargets(quint8 radarID,  AnalysisFrameType frameType, QDateTime &saveTime);
    bool saveLostFrameInfomation( quint8 radarID );
    bool saveTrackInfo( quint8 radarID );

    AnalysisWorker *mAnalysisWorker{0};
    CalculationWorker *mCalculationWorker{0};

    AnalysisData mAnalysisDatas[MAX_RADAR_COUNT];
    AlarmData mEarlyAlarmData[MAX_RADAR_COUNT];
    Parser::ParsedDataTypedef::ParsedData mParsedData;  // 前雷达

    QFile mFileTrackInfo; //跟踪点统计信息
    QString mSavePath;
    QFile mFileEarlyWarning;   // 上位机预警事件日志
    QFile mFileAlarm;   // 告警事件日志
    QFile mFileAlarm2;  //告警事件日志  与mFileAlarm相比格式不一样
    QFile mFileLostFrame; //丢帧日志
    QFile mFile200Targets;   // 200个目标信息
    QFile mFile16Targets;   // 16个目标信息
    QFile mFile[FrameTargetCount];
    QFile mFileOld[FrameTargetCount];
    bool mOldStyle{false};
    bool mResponsed{true};
    quint64 mSaveCounts[MAX_RADAR_COUNT][FrameTargetCount];
    quint64 mSaveCountsOld[MAX_RADAR_COUNT][FrameTargetCount];
    quint64 mSaveCountAll{0};
    quint64 mSave16TargetsCounts[MAX_RADAR_COUNT];
    quint64 mRadarAlarmCount[MAX_RADAR_COUNT];
    quint64 mRadarAlarmIDCount[MAX_RADAR_COUNT];
    qint64 mRadarAlarmID[MAX_RADAR_COUNT];
    quint64 mRadarEarlyWarningCount[MAX_RADAR_COUNT];
    quint64 mRadarEarlyWarningIDCount[MAX_RADAR_COUNT];
    qint64 mRadarEarlyWarningID[MAX_RADAR_COUNT];
    bool mSaving{false};
    quint64 mSaveCountMax{6000};
};


} // namespace Analysis

#endif // ANALYSISSAVEWORKER_H
