#ifndef IFRAMECONVER_H
#define IFRAMECONVER_H

#include <QObject>
#include <QTimer>

#define M_PI (3.1415926)

extern int32_t decode_sign_bit(uint32_t data, uint8_t bits);
extern uint32_t encode_sign_bit(int32_t data, uint8_t bits);

namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
}

class IFrameConver : public QObject
{
    Q_OBJECT
public:
    explicit IFrameConver( Devices::Can::DeviceManager* deviceManager, QObject *parent = nullptr);

    bool run(){ return mRun; };

signals:
    void dataChanged();

public slots:
    void startOrStop( bool bStart );
    void sendResultFrame();

private slots:
    void recvFrame( const Devices::Can::CanFrame& frame );


protected:
    //解析
    virtual void analysis( const Devices::Can::CanFrame& frame ) = 0;
    //转换
    virtual void conver() = 0;
    //组装报文
    virtual void packageFrame() = 0;
    //发送
    bool sendFrame();

    //初始化需要接收的报文ID
    virtual void initRecvIDAndEndID() = 0;
    //判断是否为需要接收的报文ID
    bool isRecvID(quint64 id);



protected:
    Devices::Can::DeviceManager* mDeviceManager;

    QList<quint64> mRecvIDList; //需接收的ID
    quint64 mRecvEndFrameID; //结束帧ID
    QList<Devices::Can::CanFrame> mSendFrames; //待发送的转换结果
    bool mRun{false};
//    QTimer mSendTimer; //定时发送转换结果



public:
    QString mConverName; //转换名
    quint8 mRecvChannel{0}; //接收通道
    quint8 mSendChannel{1}; //发送通道
    quint64 mRecvFrameNum{0}; //已接收的报文数量
    quint64 mSendFrameNum{0}; //已发送的报文数量
//    quint64 mSendTimeMS{20}; //定时发送的周期
};

#endif // IFRAMECONVER_H
