﻿#ifndef DAQMANAGER_H
#define DAQMANAGER_H

#include "playback_global.h"

#include <QObject>

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Analysis {
class AnalysisManager;
}

namespace Core {
class SaveManager;
};

namespace Views{
    class ViewsManager;
}

class DAQControl;
class DebugControlForm;

class DEBUGSYSTEM_EXPORT DAQManager : public QObject
{
    Q_OBJECT
public:
    explicit DAQManager(Analysis::AnalysisManager *analysisManager,
                        QObject *parent = nullptr);
    ~DAQManager();

    DAQControl *daqControl(Devices::Can::DeviceManager *deviceManager,
                           Analysis::AnalysisManager *analysisManager,
                           QWidget *parent = nullptr);
    DebugControlForm *debugControl(Views::ViewsManager* pViewManager, Core::SaveManager* saveManager,Devices::Can::DeviceManager *deviceManager,
                               Analysis::AnalysisManager *analysisManager,
                               QWidget *parent = nullptr);
    bool daqControlExisted() const { return mDAQControl || mDebugControlForm; }

    void setShowDAQControl(bool show) { mShowDAQControl = show; }
    bool showDAQControl() { return mShowDAQControl; }

    void saveSettings();

signals:
    void run();
    void startSave(const QString &adcType);
    void stopSave();
    void saveStarted(const QString &saveFile);

public slots:

private:
    DAQControl *mDAQControl{0};
    DebugControlForm *mDebugControlForm{0};
    bool mShowDAQControl{false};
};

#endif // DAQMANAGER_H
