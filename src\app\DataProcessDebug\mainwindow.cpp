﻿#include "mainwindow.h"
#include "ui_mainwindow.h"

#include "dataprocess/dataprocessform.h"
#include "utils/settingshandler.h"
#include "views/objectcoordinatesystem.h"

#include <QDockWidget>

static const char windowGeometryKey[] = "MainWindow/WindowGeometry";
static const char windowStateKey[] = "MainWindow/WindowState";
static const char objectViewsKey[] = "View/ObjectViews";
static const char dataViewsFontSize[] = "DataView/FontSize";

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    qRegisterMetaType<AnalysisData>("AnalysisData");

    DockOptions opts;
    opts |= AnimatedDocks
            | AllowNestedDocks
            | AllowTabbedDocks
//            | ForceTabbedDocks
//            | VerticalTabs
//            | GroupedDragging
            ;

    QMainWindow::setDockOptions(opts);

    ui->setupUi(this);

    mLabelMeasurementCounter = new QLabel(this);
    ui->statusbar->addPermanentWidget(mLabelMeasurementCounter);

    for (int i = 4; i < 8; ++i) {
        mAnalysisDatas[i] = new AnalysisData;
    }

    mDataProcessForm = new DataProcessForm(this);
    connect(mDataProcessForm, &DataProcessForm::calculateFinished, this, &MainWindow::calculateFinished);

    QDockWidget* dataProcessDockWidget = new QDockWidget(QString::fromLocal8Bit("数据处理调试"), this);
//    dataProcessDockWidget->setAttribute(Qt::WA_DeleteOnClose);
    dataProcessDockWidget->setObjectName("DataProcessDebug");
    dataProcessDockWidget->setWidget(mDataProcessForm);

    addDockWidget(Qt::BottomDockWidgetArea , dataProcessDockWidget);

    QList<QVariant> views;
    QVariant settings = SETTINGS_GET_VALUE(QLatin1String(objectViewsKey));
    views = settings.toList();

    foreach (const QVariant settings, views)
    {
        ui->centralwidget->coordinateSystem()->setCoordinateSystemConfig(settings);
        break;
    }

    ui->checkBoxRefreshData->setChecked(SETTINGS_GET_VALUE(QLatin1String("DataView/RefreshData"), true).toBool());
    ui->spinBoxFontSize->setValue(SETTINGS_GET_VALUE(QLatin1String(dataViewsFontSize), 5).toUInt());

    if (!restoreGeometry(SETTINGS_GET_VALUE(QLatin1String(windowGeometryKey)).toByteArray())) {
        resize(900, 600); // size without window decoration
    }
    restoreState(SETTINGS_GET_VALUE(QLatin1String(windowStateKey)).toByteArray());
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::calculateFinished(quint8 radarID, const AnalysisData &data)
{
    qDebug() << __FUNCTION__ << __LINE__ << radarID;
    *mAnalysisDatas[radarID] = data;
    mAnalysisDatas[radarID]->mValid = true; //analysisData.mValid;

    AnalysisData *analysisData = mAnalysisDatas[radarID];


    analysisRadarData(0, radarID, analysisData);

    qDebug() << __FUNCTION__ << __LINE__ << mAnalysisDatas[radarID]->mValid;
    if (mAnalysisDatas[radarID]->mValid) {
        ui->centralwidget->coordinateSystem()->showRadarData(0, radarID, analysisData);

        ui->centralwidget->draw();

        mAnalysisDatas[radarID]->mValid = false;
    }
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    SETTINGS_SET_VALUE(QLatin1String("DataView/RefreshData"), ui->checkBoxRefreshData->isChecked());
    SETTINGS_SET_VALUE(QLatin1String(dataViewsFontSize),ui->spinBoxFontSize->value());
    SETTINGS_SET_VALUE(QLatin1String(windowGeometryKey), saveGeometry());
    SETTINGS_SET_VALUE(QLatin1String(windowStateKey), saveState());
}

void MainWindow::analysisRadarData(/*TargetModel*/int targetModel, quint8 radarID, AnalysisData *analysisData)
{
    if (mFrameBreakingFirst || mTimerFrameBreaking.isActive()) {
        mTimerFrameBreaking.start(10 * 1000);
        mFrameBreakingFirst = false;
    }
    AlarmData &alarmData = analysisData->mAlarmData;
    EndFrameData &endFrameData = analysisData->mEndFrameData;
    VehicleData &vehicleData = analysisData->mVehicleData;
    for (int i = 0; i < FrameTargetCount; ++i)
    {
//        mAnalysisModel[i]->targets(radarID, i, analysisData->mTargets[i]);

        if (i == FrameRawTarget)
        {
//            if (analysisData->mTargets[i].mTargetHeader.mMeasurementCount % 50 == 0) { analysisData->mTargets[i].mTargetHeader.mMeasurementCount = 0; }
            if (mRawMeasurementCount != 0xFFFFFFFF && analysisData->mTargets[i].mTargetHeader.mMeasurementCount < mRawMeasurementCount)
            {
//                emit rawMeasurementCountError(mRawMeasurementCount, analysisData->mTargets[i].mTargetHeader.mMeasurementCount);
                qWarning() << __FUNCTION__ << __LINE__
                           << QString("Radar[%1] Raw Measurement Count Error! History[%2] -> Current[%3] FAIL TIME: %4")
                              .arg(radarID)
                              .arg(mRawMeasurementCount)
                              .arg(analysisData->mTargets[i].mTargetHeader.mMeasurementCount)
                              .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"));
                ui->labelRawMeasurementCount->setStyleSheet("background-color: rgb(255, 0, 0);");
            }
            mRawMeasurementCount = analysisData->mTargets[i].mTargetHeader.mMeasurementCount;
            ui->labelRawMeasurementCount->setText(QString::number(mRawMeasurementCount));
            ui->labelResponseTaskCycleTime->setText(QString::number(analysisData->mTargets[i].mTargetHeader.mResponseTaskCycleTime));
        }
        else if (i == FrameTrackTarget)
        {
            ui->labelTrackMeasurementCount->setText(QString::number(analysisData->mTargets[i].mTargetHeader.mMeasurementCount));


            ui->label_BlockagePercent->setStyleSheet( analysisData->mTargets[i].mTargetHeader.mBlockageFlag != 0 ? "background-color: rgb(255, 0, 0);" : "");
            ui->label_BlockagePercent->setText( QString::number( analysisData->mTargets[i].mTargetHeader.mBlockagePercent ) + "%" );
            //干扰信息
            ui->label_InterferencePercent->setStyleSheet( analysisData->mTargets[i].mTargetHeader.mInterferenceFlag != 0 ? "background-color: rgb(255, 0, 0);" : "");
            ui->label_InterferencePercent->setText( QString::number( analysisData->mTargets[i].mTargetHeader.mInterferencePercent ) + "%" );
        }
    }


    setTarget(analysisData);


    mLabelMeasurementCounter->setText(QString("%1:%2/%3")
                                      .arg(radarID)
                                      .arg(analysisData->mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount)
                                      .arg(analysisData->mTargets[FrameTrackTarget].mTargetHeader.mMeasurementCount));


    ui->labelFrameTime->setText(QDateTime::fromMSecsSinceEpoch(endFrameData.mSystemFrameTime).toString("hh:mm:ss.zzz"));
    ui->labelEstablishedAngle->setText(QString::number(endFrameData.mSelfCalibrationEstablishedAngle));
    ui->labelEOLAngle->setText(QString::number(endFrameData.mEndOfLineEstablishedAngle));

    bool bAutoALNStsErr = false;
    QString autoALNStsStr = "";
    //qDebug() << __FUNCTION__ << __LINE__ << (int)(endFrameData.mAutoALNSts);
    switch( (int)(endFrameData.mAutoALNSts) ){
    case 0x0:
        autoALNStsStr = "INACTIVE";
        break;
    case 0x1:
        autoALNStsStr = "RUNNING";
        break;
    case 0x2:
        autoALNStsStr = "ABORTING";
        break;
    case 0x3:
        autoALNStsStr = "NORMAL";
        break;
    case 0x4:
        autoALNStsStr = "MINMAXOUT";
        bAutoALNStsErr = true;
        break;
    case 0x5:
        autoALNStsStr = "TIMEOUT";
        bAutoALNStsErr = true;
        break;
    default:
        autoALNStsStr = "UNKNOWN";
        break;
    }
    ui->labelAutoALNSts->setText( autoALNStsStr );
    ui->labelAutoALNSts->setStyleSheet( bAutoALNStsErr ? "background-color: rgb(255, 0, 0);" : "");

    ui->label_VehicleTime->setText( QString( "%1\\%2\\%3\n%4:%5:%6" )
                                    .arg(vehicleData.mVehicleYear)
                                    .arg(vehicleData.mVehicleMonth)
                                    .arg(vehicleData.mVehicleDay)
                                    .arg(vehicleData.mVehicleHour)
                                    .arg(vehicleData.mVehicleMinute)
                                    .arg(vehicleData.mVehicleSecond) );

//    qDebug() << __FUNCTION__ << __LINE__ << radarID << alarmData.mAlarmDOWLevelFront << alarmData.mAlarmDOWLevelRear;
#define SET_ALARM_STYLE_SHEET(valueLevel, valueState) \
    ui->label_##valueLevel->setStyleSheet(alarmData.m##valueLevel != 0 ? "background-color: rgb(255, 0, 0);" : ""); \
    if (alarmData.m##valueLevel == 2) { \
        ui->label_##valueLevel->setStyleSheet("background-color: rgb(100, 0, 0);"); \
    } \
    switch ((quint8)alarmData.m##valueState) { \
    case 0x1: \
        ui->label_##valueState->setStyleSheet("color:rgb(255, 255, 255); background-color: rgb(0, 0, 0);"); \
        break; \
    case 0x2: \
        ui->label_##valueState->setStyleSheet("background-color: rgb(0, 255, 0);"); \
        break; \
    case 0x3: \
        ui->label_##valueState->setStyleSheet("background-color: rgb(255, 0, 0);"); \
        break; \
    case 0x0: \
    default: \
        ui->label_##valueState->setStyleSheet(""); \
        break; \
    }


#define SET_ALARM_TTC(valueLevel, valueState, objectTTC) \
    if (alarmData.m##valueLevel != 0) { \
        alarmTTC = alarmData.m##objectTTC;\
    }\
    SET_ALARM_STYLE_SHEET(valueLevel, valueState)

//    alarmData.mAlarmJALevel = 2;
//    alarmData.mAlarmLCALevel = 2;
//    alarmData.mAlarmJAState = 1;
//    qDebug() << __FUNCTION__ << __LINE__ << alarmData.mAlarmJAState;
    // 2-active是绿色，3-fault是红色，1-OFF是黑色，0-Passive是灰色
    float alarmTTC = -1; //宏函数中赋值
    SET_ALARM_STYLE_SHEET(AlarmBSDLevel, AlarmBSDState)
    SET_ALARM_TTC(AlarmDOWLevelRear, AlarmDOWState, AlarmDOWObjectTTC)
    SET_ALARM_TTC(AlarmDOWLevelFront, AlarmDOWState, AlarmDOWObjectTTC)
    SET_ALARM_TTC(AlarmFCTALevel, AlarmFCTAState, AlarmFCTAObjectTTC)
    SET_ALARM_TTC(AlarmFCTBLevel, AlarmFCTBState, AlarmFCTBObjectTTC)
    SET_ALARM_TTC(AlarmLCALevel, AlarmLCAState, AlarmLCAObjectTTC)
    SET_ALARM_TTC(AlarmRCTALevel, AlarmRCTAState, AlarmRCTAObjectTTC)
    SET_ALARM_TTC(AlarmRCTBLevel, AlarmRCTBState, AlarmRCTBObjectTTC)
    SET_ALARM_TTC(AlarmRCWLevel, AlarmRCWState, AlarmRCWObjectTTC);
    SET_ALARM_TTC(AlarmJALevel, AlarmJAState, AlarmJAObjectTTC);
    ui->label_AlarmTTC->setText( "TTC:" + QString::number( alarmTTC ) );//初始化

    ui->labelVehicleSpeedInMeter->setText(QString("%1 m/s").arg(vehicleData.mVehicleSpeed / 3.6, 0, 'f', 3));
    ui->labelVehicleSpeedInKilometer->setText(QString("%1 km/h").arg(vehicleData.mVehicleSpeed, 0, 'f', 3));
    ui->labelVehicleRadious->setText(QString("%1").arg(vehicleData.mRadius, 0, 'f', 3));
    QString gerText = "N";
    switch ((quint32)vehicleData.mGear)
    {
    case 0:
        break;
    case 1:
        gerText = "P";
        break;
    case 2:
        gerText = "R";
        break;
    case 3:
        gerText = "N";
        break;
    case 4:
        gerText = "D";
        break;
    default:
        break;
    }
    ui->labelVehicleGear->setText(gerText);
    ui->labelVehicleYawRate->setText(QString("%1").arg(vehicleData.mYawRate, 0, 'f', 3));

#define BYTE_ARRAY_CHECK(array,value,err) \
    for( int i=0; i<array.size(); i++ ){\
        if( (quint8)array[i] != value ){ \
            err = true; \
            break; \
        } \
    };

    //故障信息
    QByteArray hex0x4DN = QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4DN, 16);
    QByteArray hex0x4EN = QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4EN, 16);
    bool bError = false;
    BYTE_ARRAY_CHECK(hex0x4DN,0,bError);
    ui->labelErrorInformation0x4DN->setStyleSheet(bError ? "background-color: rgb(255, 0, 0);" : "");
    ui->labelErrorInformation0x4DN->setText( hex0x4DN.toHex(' ') );
    bError = false;
    BYTE_ARRAY_CHECK(hex0x4EN,0,bError);
    ui->labelErrorInformation0x4EN->setStyleSheet(bError ? "background-color: rgb(255, 0, 0);" : "");
    ui->labelErrorInformation0x4EN->setText( hex0x4EN.toHex(' ') );
//    ui->labelErrorInformation0x4DN->setText( QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4DN, 16).toHex(' ') );
//    ui->labelErrorInformation0x4EN->setText( QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4EN, 16).toHex(' ') );

    //版本信息
    quint8* p = (quint8*)analysisData->mEndFrameData.mRadarVersion[0];
    QString versionStr = QString("%1.%2.%3.%4").arg(p[1]).arg(p[2]).arg(p[3]).arg(p[4]);
    ui->labelRadarVersion->setText( versionStr );

    //ui->labelRadarVersion->hide();

//    mAlarmDataModel->setAlarmData(alarmData);
//    mVehicleDataModel->setVehicleData(vehicleData);

    //410协议报文丢帧信息
    quint64 total = analysisData->mEndFrameData.mCurrentRadar410FrameCount - analysisData->mEndFrameData.mInitRadar410FrameCount; //总数
    QString frameCntStr = QString::fromLocal8Bit( "总帧数:%1 \n丢帧数:%2 \n丢帧率:%3%")
                            .arg( total )
                            .arg( analysisData->mEndFrameData.mDiff410FrameCount )
                            .arg( analysisData->mEndFrameData.mDiff410FrameCountRate );
    if( analysisData->mEndFrameData.mDiff410FrameCountRate > 5 ){//丢帧率超过5%，红色警告
        ui->label_FrameCount->setStyleSheet("background-color: rgb(255, 0, 0);");
    }else{
        ui->label_FrameCount->setStyleSheet("");
    }
    ui->label_FrameCount->setText( frameCntStr );
}

void MainWindow::setTarget(AnalysisData *analysisData)
{
    if (!ui->checkBoxRefreshData->isChecked()) {
        return;
    }
    QString textRaw;
    QString textTrack;

    textRaw.append(QString("%1 %2 %3 %4 %5")
                   .arg("ID", 4)
                   .arg("R", 7)
                   .arg("A", 7)
                   .arg("SNR", 7)
                   .arg("RCS", 7));
    textTrack.append(QString("%1 %2 %3 %4 %5")
                     .arg("ID", 4)
                     .arg("R", 7)
                     .arg("A", 7)
                     .arg("SNR", 7)
                     .arg("RCS", 7));

    for (int i = 0; i < MAX_TARGET_COUNT; ++i) {
        const Target &targetRaw = analysisData->mTargets[FrameRawTarget].mTargets[i];
        textRaw.append(QString("\n%1 %2 %3 %4 %5")
                         .arg(i, 4)
                         .arg(targetRaw.mRange, 7, 'f', 2)
                         .arg(targetRaw.mAngle, 7, 'f', 2)
                         .arg(targetRaw.mSNR, 7, 'f', 2)
                         .arg(targetRaw.mRCS, 7, 'f', 2));
        const Target &targetTrack = analysisData->mTargets[FrameTrackTarget].mTargets[i];
        textTrack.append(QString("\n%1 %2 %3 %4 %5")
                         .arg(i, 4)
                         .arg(targetTrack.mRange, 7, 'f', 2)
                         .arg(targetTrack.mAngle, 7, 'f', 2)
                         .arg(targetTrack.mSNR, 7, 'f', 2)
                         .arg(targetTrack.mRCS, 7, 'f', 2));
    }

    ui->plainTextEditRaw->setPlainText(textRaw);
    ui->plainTextEditTrack->setPlainText(textTrack);

}


void MainWindow::on_spinBoxFontSize_valueChanged(int arg1)
{
    QFont font;
    font.setPointSize(arg1);
    ui->plainTextEditTrack->setFont(font);
    ui->plainTextEditRaw->setFont(font);
}
