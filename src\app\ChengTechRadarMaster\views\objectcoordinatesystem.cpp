﻿#include "objectcoordinatesystem.h"

#include <corecrt_math_defines.h>
#include "math.h"

#include "utils/utils.h"
#include "analysis/analysisworker.h"
#include "widget/coordinateaxis.h"
#include "objectcorrdinatesystemconfigdialog.h"

#include <QWheelEvent>
#include <QScrollBar>
#include <QTimer>
#include <QPainter>
#include <QUuid>
#include <QtMath>
#include <QTimer>
#include <QDebug>
#include <QRandomGenerator>

#define DEG2RAD 0.017453292519943295f
#define ALARM_PIXMAP_SIZE 8
#define BYD_OBJECT_SIZE 12
#define RADAR_ID_BEGIN 4
#define RADAR_ID_END 7

namespace Views {
namespace ObjectView {

ObjectCoordinateSystem::ObjectCoordinateSystem(QWidget *parent)
    : QWidget(parent)
{
//    setAttribute(Qt::WA_StyledBackground,true);
//    this->setStyleSheet("ObjectWidget{border:1px solid black;}");
    setupUi();

    mPixmapAlarm = QPixmap(":/views/images/alarm.png");
    mPixmapEarlyWarning = QPixmap(":/views/images/earlywarning.png");

    mPixmapAlarm = mPixmapAlarm.scaled(ALARM_PIXMAP_SIZE, ALARM_PIXMAP_SIZE, Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
    mPixmapEarlyWarning = mPixmapEarlyWarning.scaled(ALARM_PIXMAP_SIZE, ALARM_PIXMAP_SIZE, Qt::IgnoreAspectRatio, Qt::SmoothTransformation);

    for (int i = RADAR_ID_BEGIN; i <= RADAR_ID_END; ++i)
    {
        ObjectData &objectData = mObjectDatas[i];
        objectData.mObjectShowConfigs.mObjectShow[ObjectData::ObjectRaw] = true;
        objectData.mObjectShowConfigs.mObjectShow[ObjectData::ObjectTrack] = true;
    }
}

ObjectCoordinateSystem::~ObjectCoordinateSystem()
{
    delete mLeftCustomAxis;
    mLeftCustomAxis = nullptr;

    delete mRightCustomAxis;
    mRightCustomAxis = nullptr;

    delete mTopCustomAxis;
    mTopCustomAxis = nullptr;
}

void ObjectCoordinateSystem::setObjectCoordinateSettings(const ObjectCoordinateSettings &settings)
{
    mObjectCoordinateSettings = settings;

    setLaneWidth(mObjectCoordinateSettings.mLaneWidth, mObjectCoordinateSettings.mDisplayLane, mObjectCoordinateSettings.mDisplayGridLine);
    setLocalVehicleData(mObjectCoordinateSettings.mLocalVehicleWidth, mObjectCoordinateSettings.mLocalVehicleLength);
    setLineOfSightRange(mObjectCoordinateSettings.mHorizontalMinRange, mObjectCoordinateSettings.mHorizontalMaxRange, mObjectCoordinateSettings.mHorizontalInterval,
                        mObjectCoordinateSettings.mVerticalMinRange, mObjectCoordinateSettings.mVerticalMaxRange, mObjectCoordinateSettings.mVerticalInterval);

    mPixmapBackground = QPixmap(this->width(), this->height());
    drawBackground(&mPixmapBackground);
    mPixmapTargets = mPixmapBackground;

    update();
}

const ObjectCoordinateSettings &ObjectCoordinateSystem::getObjectCoordinateSettings()
{
    return mObjectCoordinateSettings;
}

void ObjectCoordinateSystem::setLineOfSightRange(double hMinRange, double hMaxRange, double hInterval,
                                       double vMinRange, double vMaxRange, double vInterval)
{
    mObjectCoordinateSettings.mHorizontalInterval = hInterval;
    mObjectCoordinateSettings.mHorizontalMinRange = hMinRange;
    mObjectCoordinateSettings.mHorizontalMaxRange = hMaxRange;

    mObjectCoordinateSettings.mVerticalInterval = vInterval;
    mObjectCoordinateSettings.mVerticalMinRange = vMinRange;
    mObjectCoordinateSettings.mVerticalMaxRange = vMaxRange;               // 一个间隔需要多少个像素点

    mTopCustomAxis->setRange(mObjectCoordinateSettings.mHorizontalMinRange,
                             mObjectCoordinateSettings.mHorizontalMaxRange,
                             mObjectCoordinateSettings.mHorizontalInterval);
    mTopCustomAxis->setUnit(mObjectCoordinateSettings.mHorizontalUnit,
                            mObjectCoordinateSettings.mHorizontalUnitShow);

    mLeftCustomAxis->setRange(mObjectCoordinateSettings.mVerticalMinRange,
                              mObjectCoordinateSettings.mVerticalMaxRange,
                              mObjectCoordinateSettings.mVerticalInterval);
    mLeftCustomAxis->setUnit(mObjectCoordinateSettings.mVerticalUnit,
                             mObjectCoordinateSettings.mVerticalUnitShow);

    mRightCustomAxis->setRange(mObjectCoordinateSettings.mVerticalMinRange,
                               mObjectCoordinateSettings.mVerticalMaxRange,
                               mObjectCoordinateSettings.mVerticalInterval,
                               mObjectCoordinateSettings.mLocalVehicleLength / 2);
    mRightCustomAxis->setUnit(mObjectCoordinateSettings.mVerticalUnit,
                              mObjectCoordinateSettings.mVerticalUnitShow);

    mHorizontalPixelPerMetre = mTopCustomAxis->pixelPerMetre();
    mVerticalPixelPerMetre = mLeftCustomAxis->pixelPerMetre();
    double x = mLeftCustomAxis->axisWidth() - mObjectCoordinateSettings.mHorizontalMinRange * mHorizontalPixelPerMetre ;
    double y = mLeftCustomAxis->axisLength() + mObjectCoordinateSettings.mVerticalMinRange * mVerticalPixelPerMetre;
    mZeroPoint.setX(x);
    mZeroPoint.setY(y);
    mZeroPointF.setX(x);
    mZeroPointF.setY(y - mObjectCoordinateSettings.mLocalVehicleLength * mVerticalPixelPerMetre / 2);

    QPixmap pixmap = QPixmap(":/views/images/car.png");
    mPixmapCar = pixmap.scaled(mObjectCoordinateSettings.mLocalVehicleWidth * mHorizontalPixelPerMetre,
                           mObjectCoordinateSettings.mLocalVehicleLength * mVerticalPixelPerMetre,
                      Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
}

void ObjectCoordinateSystem::getLineOfSightRange(double &hMinRange, double &hMaxRange, double &hInterval,
                                       double &vMinRange, double &vMaxRange, double &vInterval)
{
    hInterval = mObjectCoordinateSettings.mHorizontalInterval;
    hMinRange = mObjectCoordinateSettings.mHorizontalMinRange;
    hMaxRange = mObjectCoordinateSettings.mHorizontalMaxRange;

    vInterval = mObjectCoordinateSettings.mVerticalInterval;
    vMinRange = mObjectCoordinateSettings.mVerticalMinRange;
    vMaxRange = mObjectCoordinateSettings.mVerticalMaxRange;
}

void ObjectCoordinateSystem::setLocalVehicleData(double width, double length)
{
    mObjectCoordinateSettings.mLocalVehicleLength = length;
    mObjectCoordinateSettings.mLocalVehicleWidth = width;
}

void ObjectCoordinateSystem::getLocalVehicleData(double &width, double &length)
{
    length = mObjectCoordinateSettings.mLocalVehicleLength;
    width = mObjectCoordinateSettings.mLocalVehicleWidth;
}

void ObjectCoordinateSystem::setLaneWidth(double width, bool displayLane, bool displayGridLine)
{
    mObjectCoordinateSettings.mLaneWidth = width;
    mObjectCoordinateSettings.mDisplayLane = displayLane;
    mObjectCoordinateSettings.mDisplayGridLine = displayGridLine;
}

void ObjectCoordinateSystem::getLaneWidth(double &width, bool &displayLane, bool &displayGridLine)
{
    width = mObjectCoordinateSettings.mLaneWidth;
    displayLane = mObjectCoordinateSettings.mDisplayLane;
    displayGridLine = mObjectCoordinateSettings.mDisplayGridLine;
}

void ObjectCoordinateSystem::setUnit(const QString &hUnit, bool &hShow, const QString &vUnit, bool &vShow)
{
    mObjectCoordinateSettings.mHorizontalUnit = hUnit;
    mObjectCoordinateSettings.mHorizontalUnitShow = hShow;
    mObjectCoordinateSettings.mVerticalUnit = vUnit;
    mObjectCoordinateSettings.mVerticalUnitShow = vShow;

    mLeftCustomAxis->setUnit(vUnit, vShow);

    mTopCustomAxis->setUnit(hUnit, hShow);
}

void ObjectCoordinateSystem::getUnit(QString &hUnit, bool &hShow, QString &vUnit, bool &vShow)
{
    hUnit = mObjectCoordinateSettings.mHorizontalUnit;
    hShow = mObjectCoordinateSettings.mHorizontalUnitShow;
    vUnit = mObjectCoordinateSettings.mVerticalUnit;
    vShow = mObjectCoordinateSettings.mVerticalUnitShow;
}

void ObjectCoordinateSystem::setRadarType(RadarType radarType)
{

}

void ObjectCoordinateSystem::setShowTarget(QMap<quint8, QVector<bool> > show)
{
    QMapIterator<quint8, QVector<bool> >it(show);
    while (it.hasNext()) {
        it.next();
        quint8 radarID = it.key();
        ObjectData &objectData = mObjectDatas[radarID];
        for (int i = 0; i < it.value().size(); ++i)
        {
            qDebug() << __FUNCTION__ << __LINE__ << radarID << i << it.value()[i];
            switch (i) {
            case FrameTargetCount:
                objectData.mObjectShowConfigs.mRadarDataShow = it.value()[i];
                break;
            default:
                objectData.mObjectShowConfigs.mObjectShow[i] = it.value()[i];
                if (!objectData.mObjectShowConfigs.mObjectShow[i])
                {
                    clearTargets(radarID, i);
                }
            }
        }
    }
}

void ObjectCoordinateSystem::setTargetVeiwAnalysisTypes(QList<AnalysisType> &targetVeiwAnalysisTypes)
{
    qDebug() << __FUNCTION__ << __LINE__ << targetVeiwAnalysisTypes;
    mObjectCoordinateSettings.mTargetVeiwAnalysisTypes = targetVeiwAnalysisTypes;
}

void ObjectCoordinateSystem::clear()
{
    qDebug() << __FUNCTION__ << __LINE__;
    for (int i = 0; i < MAX_RADAR_COUNT; ++i) {
        for (int j = 0; j < ObjectData::ObjectTypeCount; ++j)
        {
            clearTargets(i, j);
        }
    }

    draw();
}

void ObjectCoordinateSystem::clearTargets(quint8 radarID, int frameType)
{
    mNewData = true;
    ObjectData &objectData = mObjectDatas[radarID];
    Objects &objects = objectData.mObjects[frameType];
    objects.clear();
}

QVariant ObjectCoordinateSystem::coordinateSystemConfig() const
{
    QMap<QString, QVariant> systemConfig;

    QMap<QString, QVariant> coordinateConfig;
    coordinateConfig["DisplayTargetID"] = mObjectCoordinateSettings.mDisplayTargetID;
    coordinateConfig["DisplayVelocityFeversal"] = mObjectCoordinateSettings.mDisplayVelocityFeversal;
    coordinateConfig["DisplayTrackFrame"] = mObjectCoordinateSettings.mDisplayTrackFrame;
    coordinateConfig["Display16TrackTarget"] = mObjectCoordinateSettings.mDisplay16TrackTarget;
    coordinateConfig["Display16TrackFrame"] = mObjectCoordinateSettings.mDisplay16TrackFrame;
    coordinateConfig["Display16TrackShowV"] = mObjectCoordinateSettings.mDisplay16TrackShowV;
    coordinateConfig["DisplayELKOnly"] = mObjectCoordinateSettings.mDisplayELKOnly;
    coordinateConfig["DisplayHeSaiLider"] = mObjectCoordinateSettings.mDisplayHeSaiLider;
    coordinateConfig["DisplayVelocityAmbiguity"] = mObjectCoordinateSettings.mDisplayVelocityAmbiguity;
    coordinateConfig["DisplayHighlightedVelocityAmbiguity"] = mObjectCoordinateSettings.mDisplayHighlightedVelocityAmbiguity;
    coordinateConfig["DisplayAcceleration"] = mObjectCoordinateSettings.mDisplayAcceleration;

    coordinateConfig["StatusFiltrateRaw"] = mObjectCoordinateSettings.mStatusFiltrateRaw;
    coordinateConfig["StatusFiltrateValueRaw"] = mObjectCoordinateSettings.mStatusFiltrateValueRaw;

    coordinateConfig["DisplayZeroLine"] = mObjectCoordinateSettings.mDisplayZeroLine;

    coordinateConfig["HorizontalInterval"] = mObjectCoordinateSettings.mHorizontalInterval;
    coordinateConfig["HorizontalMinRange"] = mObjectCoordinateSettings.mHorizontalMinRange;
    coordinateConfig["HorizontalMaxRange"] = mObjectCoordinateSettings.mHorizontalMaxRange;

    coordinateConfig["VerticalInterval"] = mObjectCoordinateSettings.mVerticalInterval;
    coordinateConfig["VerticalMinRange"] = mObjectCoordinateSettings.mVerticalMinRange;
    coordinateConfig["VerticalMaxRange"] = mObjectCoordinateSettings.mVerticalMaxRange;

    coordinateConfig["LaneWidth"] = mObjectCoordinateSettings.mLaneWidth;
    coordinateConfig["LocalVehicleWidth"] = mObjectCoordinateSettings.mLocalVehicleWidth;
    coordinateConfig["LocalVehicleLength"] = mObjectCoordinateSettings.mLocalVehicleLength;
    coordinateConfig["DisplayLane"] = mObjectCoordinateSettings.mDisplayLane;
    coordinateConfig["DisplayGridLine"] = mObjectCoordinateSettings.mDisplayGridLine;

    coordinateConfig["DOWDisplay"] = mObjectCoordinateSettings.mDOWDisplay;
    coordinateConfig["DOWDistanceOfHeadway"] = mObjectCoordinateSettings.mDOWDistanceOfHeadway;
    coordinateConfig["DOWDistanceOfBody"] = mObjectCoordinateSettings.mDOWDistanceOfBody;
    coordinateConfig["DOWWidth"] = mObjectCoordinateSettings.mDOWWidth;
    coordinateConfig["DOWLength"] = mObjectCoordinateSettings.mDOWLength;

    coordinateConfig["BSDDisplay"] = mObjectCoordinateSettings.mBSDDisplay;
    coordinateConfig["BSDDistanceOfHeadway"] = mObjectCoordinateSettings.mBSDDistanceOfHeadway;
    coordinateConfig["BSDDistanceOfBody"] = mObjectCoordinateSettings.mBSDDistanceOfBody;
    coordinateConfig["BSDWidth"] = mObjectCoordinateSettings.mBSDWidth;
    coordinateConfig["BSDLength"] = mObjectCoordinateSettings.mBSDLength;

    coordinateConfig["LCADisplay"] = mObjectCoordinateSettings.mLCADisplay;
    coordinateConfig["LCADistanceOfHeadway"] = mObjectCoordinateSettings.mLCADistanceOfHeadway;
    coordinateConfig["LCADistanceOfBody"] = mObjectCoordinateSettings.mLCADistanceOfBody;
    coordinateConfig["LCAWidth"] = mObjectCoordinateSettings.mLCAWidth;
    coordinateConfig["LCALength"] = mObjectCoordinateSettings.mLCALength;

    coordinateConfig["RearWheelCenterFrontX"] = mObjectCoordinateSettings.mRearWheelCenterFrontX;
    coordinateConfig["RearWheelCenterFrontY"] = mObjectCoordinateSettings.mRearWheelCenterFrontY;
    coordinateConfig["RearWheelCenterRearX"] = mObjectCoordinateSettings.mRearWheelCenterRearX;
    coordinateConfig["RearWheelCenterRearY"] = mObjectCoordinateSettings.mRearWheelCenterRearY;

    systemConfig["CoordinateConfig"] = coordinateConfig;

    QMap<QString, QVariant> radarConfig;
    for (int  i = 0; i < MAX_RADAR_COUNT; ++i)
    {
        QMap<QString, QVariant> targetsConfig;
        for (int j = 0; j < FrameTargetCount; ++j) {
            QMap<QString, QVariant> targetConfig;
            QList<QVariant> moveConfig;
            for (int k = 0; k < UnknowMoveStatus; ++k)
            {
                QMap<QString, QVariant> config;
                config["Show"] = mObjectDatas[i].mObjectsDrawConfig[j].mDrawConfig[k].mShow;
                config["DrawShape"] = mObjectDatas[i].mObjectsDrawConfig[j].mDrawConfig[k].mDrawShape;
                config["Color"] = mObjectDatas[i].mObjectsDrawConfig[j].mDrawConfig[k].mColor;
                config["Size"] = mObjectDatas[i].mObjectsDrawConfig[j].mDrawConfig[k].mSize;
                moveConfig.append(config);
            }
            targetConfig["MotionDrawConfig"] = moveConfig;
            targetConfig["Show"] = mObjectDatas[i].mObjectShowConfigs.mObjectShow[j];
            targetConfig["NegateX"] = mObjectDatas[i].mObjectsDrawConfig[j].mCalculatorConfig.mNegateX;
            targetConfig["NegateY"] = mObjectDatas[i].mObjectsDrawConfig[j].mCalculatorConfig.mNegateY;
            targetConfig["OffsetAngle"] = mObjectDatas[i].mObjectsDrawConfig[j].mCalculatorConfig.mOffsetAngle;


            targetsConfig[QString::number(j)] = targetConfig;
        }
        radarConfig[QString::number(i)] = targetsConfig;
    }

    systemConfig["RadarConfig"] = radarConfig;

    return systemConfig;
}

void ObjectCoordinateSystem::setCoordinateSystemConfig(const QVariant &config)
{
    if (config.isNull() || !config.isValid())
    {
        return;
    }
    QMap<QString, QVariant> systemConfig = config.toMap();

    QMap<QString, QVariant> coordinateConfig = systemConfig["CoordinateConfig"].toMap();
    if (coordinateConfig.size())
    {
        mObjectCoordinateSettings.mDisplayTargetID = coordinateConfig["DisplayTargetID"].toBool();
        mObjectCoordinateSettings.mDisplayVelocityFeversal = coordinateConfig["DisplayVelocityFeversal"].toBool();
        mObjectCoordinateSettings.mDisplayTrackFrame = coordinateConfig["DisplayTrackFrame"].toBool();
        mObjectCoordinateSettings.mDisplay16TrackTarget = coordinateConfig["Display16TrackTarget"].toBool();
        mObjectCoordinateSettings.mDisplay16TrackFrame = coordinateConfig["Display16TrackFrame"].toBool();
        mObjectCoordinateSettings.mDisplay16TrackShowV = coordinateConfig["Display16TrackShowV"].toBool();
        mObjectCoordinateSettings.mDisplayELKOnly = coordinateConfig["DisplayELKOnly"].toBool();
        mObjectCoordinateSettings.mDisplayHeSaiLider = coordinateConfig["DisplayHeSaiLider"].toBool();
        if (coordinateConfig.find("DisplayVelocityAmbiguity") != coordinateConfig.end()) {
            mObjectCoordinateSettings.mDisplayVelocityAmbiguity = coordinateConfig["DisplayVelocityAmbiguity"].toBool();
        } else {
            mObjectCoordinateSettings.mDisplayVelocityAmbiguity = true;
        }
        mObjectCoordinateSettings.mDisplayHighlightedVelocityAmbiguity = coordinateConfig["DisplayHighlightedVelocityAmbiguity"].toBool();
        mObjectCoordinateSettings.mDisplayAcceleration = coordinateConfig["DisplayAcceleration"].toBool();

        mObjectCoordinateSettings.mDisplayZeroLine = coordinateConfig["DisplayZeroLine"].toBool();

        mObjectCoordinateSettings.mStatusFiltrateRaw = coordinateConfig["StatusFiltrateRaw"].toBool();
        mObjectCoordinateSettings.mStatusFiltrateValueRaw = coordinateConfig["StatusFiltrateValueRaw"].toInt();

        mObjectCoordinateSettings.mHorizontalInterval = coordinateConfig["HorizontalInterval"].toDouble();
        if (!mObjectCoordinateSettings.mHorizontalInterval) mObjectCoordinateSettings.mHorizontalInterval = 3;
        mObjectCoordinateSettings.mHorizontalMinRange = coordinateConfig["HorizontalMinRange"].toDouble();
        if (!mObjectCoordinateSettings.mHorizontalMinRange) mObjectCoordinateSettings.mHorizontalMinRange = -30;
        mObjectCoordinateSettings.mHorizontalMaxRange = coordinateConfig["HorizontalMaxRange"].toDouble();
        if (!mObjectCoordinateSettings.mHorizontalMaxRange) mObjectCoordinateSettings.mHorizontalMaxRange = 30;

        mObjectCoordinateSettings.mVerticalInterval = coordinateConfig["VerticalInterval"].toDouble();
        if (!mObjectCoordinateSettings.mVerticalInterval) mObjectCoordinateSettings.mVerticalInterval = 3;
        mObjectCoordinateSettings.mVerticalMinRange = coordinateConfig["VerticalMinRange"].toDouble();
        if (!mObjectCoordinateSettings.mVerticalMinRange) mObjectCoordinateSettings.mVerticalMinRange = -80;
        mObjectCoordinateSettings.mVerticalMaxRange = coordinateConfig["VerticalMaxRange"].toDouble();
        if (!mObjectCoordinateSettings.mVerticalMaxRange) mObjectCoordinateSettings.mVerticalMaxRange = 80;

        mObjectCoordinateSettings.mLaneWidth = coordinateConfig["LaneWidth"].toDouble();
        if (!mObjectCoordinateSettings.mLaneWidth) mObjectCoordinateSettings.mLaneWidth = 2.5;
        mObjectCoordinateSettings.mLocalVehicleWidth = coordinateConfig["LocalVehicleWidth"].toDouble();
        if (!mObjectCoordinateSettings.mLocalVehicleWidth) mObjectCoordinateSettings.mLocalVehicleWidth = 1.8;
        mObjectCoordinateSettings.mLocalVehicleLength = coordinateConfig["LocalVehicleLength"].toDouble();
        if (!mObjectCoordinateSettings.mLocalVehicleLength) mObjectCoordinateSettings.mLocalVehicleLength = 5.0;
        mObjectCoordinateSettings.mDisplayLane = coordinateConfig["DisplayLane"].toBool();
        mObjectCoordinateSettings.mDisplayGridLine = coordinateConfig["DisplayGridLine"].toBool();

        mObjectCoordinateSettings.mDOWDisplay = coordinateConfig["DOWDisplay"].toBool();
        mObjectCoordinateSettings.mDOWDistanceOfHeadway = coordinateConfig["DOWDistanceOfHeadway"].toDouble();
        mObjectCoordinateSettings.mDOWDistanceOfBody = coordinateConfig["DOWDistanceOfBody"].toDouble();
        mObjectCoordinateSettings.mDOWWidth = coordinateConfig["DOWWidth"].toDouble();
        if (!mObjectCoordinateSettings.mDOWWidth) mObjectCoordinateSettings.mDOWWidth = 2.25;
        mObjectCoordinateSettings.mDOWLength = coordinateConfig["DOWLength"].toDouble();
        if (!mObjectCoordinateSettings.mDOWLength) mObjectCoordinateSettings.mDOWLength = 45;

        mObjectCoordinateSettings.mBSDDisplay = coordinateConfig["BSDDisplay"].toBool();
        mObjectCoordinateSettings.mBSDDistanceOfHeadway = coordinateConfig["BSDDistanceOfHeadway"].toDouble();
        mObjectCoordinateSettings.mBSDDistanceOfBody = coordinateConfig["BSDDistanceOfBody"].toDouble();
        mObjectCoordinateSettings.mBSDWidth = coordinateConfig["BSDWidth"].toDouble();
        if (!mObjectCoordinateSettings.mBSDWidth) mObjectCoordinateSettings.mBSDWidth = 3.75;
        mObjectCoordinateSettings.mBSDLength = coordinateConfig["BSDLength"].toDouble();
        if (!mObjectCoordinateSettings.mBSDLength) mObjectCoordinateSettings.mBSDLength = 5.0;

        mObjectCoordinateSettings.mLCADisplay = coordinateConfig["LCADisplay"].toBool();
        mObjectCoordinateSettings.mLCADistanceOfHeadway = coordinateConfig["LCADistanceOfHeadway"].toDouble();
        mObjectCoordinateSettings.mLCADistanceOfBody = coordinateConfig["LCADistanceOfBody"].toDouble();
        mObjectCoordinateSettings.mLCAWidth = coordinateConfig["LCAWidth"].toDouble();
        if (!mObjectCoordinateSettings.mLCAWidth) mObjectCoordinateSettings.mLCAWidth = 3.75;
        mObjectCoordinateSettings.mLCALength = coordinateConfig["LCALength"].toDouble();
        if (!mObjectCoordinateSettings.mLCALength) mObjectCoordinateSettings.mLCALength = 75;

#define VALUE_DOUBLE(KEY, DEFAULT) coordinateConfig.find(KEY) != coordinateConfig.end() ? coordinateConfig[KEY].toDouble() : DEFAULT
        mObjectCoordinateSettings.mRearWheelCenterFrontX = VALUE_DOUBLE("RearWheelCenterFrontX", 0.85927);
        mObjectCoordinateSettings.mRearWheelCenterFrontY = VALUE_DOUBLE("RearWheelCenterFrontY", 3.472542);
        mObjectCoordinateSettings.mRearWheelCenterRearX = VALUE_DOUBLE("RearWheelCenterRearX", 0.67762);
        mObjectCoordinateSettings.mRearWheelCenterRearY = VALUE_DOUBLE("RearWheelCenterRearY", 0.896058);
    }

    QMap<QString, QVariant> radarConfig = systemConfig["RadarConfig"].toMap();
    if (radarConfig.size())
    {
        QMapIterator<QString, QVariant> it(radarConfig); // 雷达

        while (it.hasNext()) {
            it.next();
            quint8 radarId = it.key().toUInt();
            QMap<QString, QVariant> targetsConfig = it.value().toMap();
            QMapIterator<QString, QVariant> targetsIterator(targetsConfig); // Targets
            while (targetsIterator.hasNext()) {
                targetsIterator.next();
                AnalysisFrameType frameType = (AnalysisFrameType)targetsIterator.key().toUInt();
                QMap<QString, QVariant> targetConfig = targetsIterator.value().toMap();


                mObjectDatas[radarId].mObjectShowConfigs.mObjectShow[frameType] = targetConfig["Show"].isNull() ? true : targetConfig["Show"].toBool();
                mObjectDatas[radarId].mObjectsDrawConfig[frameType].mCalculatorConfig.mNegateX = targetConfig["NegateX"].toBool();
                mObjectDatas[radarId].mObjectsDrawConfig[frameType].mCalculatorConfig.mNegateY = targetConfig["NegateY"].toBool();
                mObjectDatas[radarId].mObjectsDrawConfig[frameType].mCalculatorConfig.mOffsetAngle = targetConfig["OffsetAngle"].toDouble();
                emit calculatorConfigChanged(radarId, frameType, &(mObjectDatas[radarId].mObjectsDrawConfig[frameType].mCalculatorConfig));

                QList<QVariant> moveConfig = targetConfig["MotionDrawConfig"].toList();
                for (int i = 0; i < moveConfig.size(); ++i)
                {
                    QMap<QString, QVariant> config = moveConfig[i].toMap();
                    mObjectDatas[radarId].mObjectsDrawConfig[frameType].mDrawConfig[i].mShow = config["Show"].toBool();
                    mObjectDatas[radarId].mObjectsDrawConfig[frameType].mDrawConfig[i].mDrawShape = (DrawShape)config["DrawShape"].toUInt();
                    mObjectDatas[radarId].mObjectsDrawConfig[frameType].mDrawConfig[i].mColor = config["Color"].value<QColor>();
                    mObjectDatas[radarId].mObjectsDrawConfig[frameType].mDrawConfig[i].mSize = config["Size"].toUInt();
                }
            }
        }
    }
}

void ObjectCoordinateSystem::showTargetsF(const Parser::ParsedDataTypedef::TargetsF &targets)
{
    ObjectData::ObjectType objectType = ObjectData::ObjectRaw;
    switch (targets.mParsedDataType) {
    case Parser::ParsedDataTypedef::TargetRaw:
        objectType = ObjectData::ObjectRaw;
        break;
    case Parser::ParsedDataTypedef::TargetTrack:
        objectType = ObjectData::ObjectTrack;
        break;
    case Parser::ParsedDataTypedef::HeSaiLider:
        objectType = ObjectData::ObjectTrueObject;
        break;
    default:
        break;
    }
    clearTargets(0, objectType);
    ObjectData &objectData = mObjectDatas[0];
    Objects &objects = objectData.mObjects[objectType];

    for (uint i = 0; i < targets.mEffectiveNumber; ++i) {
        const Parser::ParsedDataTypedef::TargetF &target = targets.mTargets[i];
        if (!within(target.mX, target.mY)) {
            continue;
        }

        Object object = calculationObject(objectType, target);

        if (!object.mShow)
        {
            continue;
        }

        objects << object;
    }
    if (Parser::ParsedDataTypedef::TargetTrack == targets.mParsedDataType) {
        objectData.mValid = true;
    }
}

void ObjectCoordinateSystem::showTargets(/*TargetModel*/int targetModel, quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &targets)
{
    switch (frameType) {
    case Frame200Raw:
    case Frame16Track:
    case Frame3Track:
    case FrameELKTrack:
        showBYDTargetsData(radarID, frameType, targets);
        break;
    default:
        break;
    }
}

void ObjectCoordinateSystem::showTargets(int targetModel, quint8 radarID, int frameType, const Targets &targets, const EndFrameData &endFrameData)
{
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << frameType;
    ObjectData &objectData = mObjectDatas[radarID];

    clearTargets(radarID, frameType);

    if (!objectData.mObjectShowConfigs.mObjectShow[frameType])
    {
        return;
    }

    // 道路模型
    if (frameType == FrameTrackTarget) {
        objectData.mPolynomial[0].d =     endFrameData.mGuardrail01_c0;
        objectData.mPolynomial[0].c =     endFrameData.mGuardrail01_c1;
        objectData.mPolynomial[0].b =     endFrameData.mGuardrail01_c2;
        objectData.mPolynomial[0].a =     endFrameData.mGuardrail01_c3;
        objectData.mPolynomial[0].begin = endFrameData.mGuardrail01_IngStart;
        objectData.mPolynomial[0].end =   endFrameData.mGuardrail01_IngEnd;
        objectData.mPolynomial[0].vaild = endFrameData.mGuardrail01_vaild;

        objectData.mPolynomial[1].d =     endFrameData.mGuardrail02_c0;
        objectData.mPolynomial[1].c =     endFrameData.mGuardrail02_c1;
        objectData.mPolynomial[1].b =     endFrameData.mGuardrail02_c2;
        objectData.mPolynomial[1].a =     endFrameData.mGuardrail02_c3;
        objectData.mPolynomial[1].begin = endFrameData.mGuardrail02_IngStart;
        objectData.mPolynomial[1].end =   endFrameData.mGuardrail02_IngEnd;
        objectData.mPolynomial[1].vaild = endFrameData.mGuardrail02_vaild;


        Objects &objects = objectData.mObjects[ObjectData::ObjectRoadMode];
        clearTargets(radarID, ObjectData::ObjectRoadMode);
        Object object;
        for (int j = 0; j < 2; ++j) {
            Polynomial &p = objectData.mPolynomial[j];
//            p.a = 0.1;
//            p.b = 0.1;
//            p.c = 1;
//            p.d = 0;
//            qDebug() << __FUNCTION__ << __LINE__ << p.a << p.b << p.c << p.d;
            if (!p.vaild || (p.a == 0.0 && p.b == 0.0 && p.c == 0.0 && p.d == 0.0)) {
                continue;
            }

            for (float i = p.begin; i < p.end; i += 0.2)
            {
                object.mY = i;
                object.mX = p.a * i * i * i + p.b * i * i + p.c * i + p.d;
                object.mX = object.mX + mObjectCoordinateSettings.mLocalVehicleWidth / 2;
                object.mY = object.mY + mObjectCoordinateSettings.mLocalVehicleLength / 2;
                object.mX = object.mX * mHorizontalPixelPerMetre;
                object.mY = object.mY * mVerticalPixelPerMetre;

                switch (radarID) {
                case 4:
                    object.mX = -object.mX;
                    object.mVx = -object.mVx;
                    break;
                case 5:
                    break;
                case 6:
                    object.mX = -object.mX;
                    object.mY = -object.mY;
                    break;
                case 7:
                    object.mY = -object.mY;
                    break;
                }

                objects.append(object);
            }
        }
    }

    Objects &objects = objectData.mObjects[frameType];

    const Target *target = targets.mTargets;
    for (int i = 0; i < targets.mTargetCount; ++i, ++target)
    {
        if (!target->mValid || (mObjectCoordinateSettings.mDisplayELKOnly && !targets.mTargets[i].mAlarmed))
        {
            continue;
        }

        // 状态位筛选
        if (frameType == ObjectData::ObjectRaw &&
                mObjectCoordinateSettings.mStatusFiltrateValueRaw &&
                (uint8_t)target->mStatus > mObjectCoordinateSettings.mStatusFiltrateValueRaw) {
            continue;
        }

        //速度解模糊的原始点不显示
        if(frameType == ObjectData::ObjectRaw && !mObjectCoordinateSettings.mDisplayVelocityAmbiguity &&
                ((((uint8_t)target->mStatus & 0x10) == 0x10) ||
                 (((uint8_t)target->mStatus & 0x40) == 0x40)/* ||
                 ((uint8_t)target->mMatchFlag < 0x3)*/))
        {
            continue;
        }

        Object object = calculationObject(radarID, frameType, target);
//        qDebug() << __FUNCTION__ << __LINE__ << object.mX << object.mY;

        if (!object.mShow)
        {
            continue;
        }
        objects.append(object);
    }
}

/**
* <AUTHOR>
* @date date
* @param
* @return void
* @note
* description
* @remarks
*/
void ObjectCoordinateSystem::showRadarData(/*TargetModel*/int targetModel, quint8 radarID, AnalysisData *analysisData)
{
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << analysisData->mEndFrameData.mValid;
    EndFrameData &endFrameData = analysisData->mEndFrameData;
    if (!endFrameData.mValid) {
        return;
    }
    VehicleData &vehicleData = analysisData->mVehicleData;

    ObjectData &objectData = mObjectDatas[radarID];
    objectData.mValid = endFrameData.mValid;

    for (int frameType = ObjectData::ObjectRaw; frameType <= ObjectData::ObjectTrack; ++frameType)
    {
        showTargets(targetModel, radarID, frameType, analysisData->mTargets[frameType], analysisData->mEndFrameData);
    }

    mVehicleGear = vehicleData.mGear;
    mRadius = vehicleData.mRadius;
    objectData.mObjectShowConfigs.mShowRoadSideLine = endFrameData.mRoadSideDistance != 0;
    if (objectData.mObjectShowConfigs.mShowRoadSideLine)
    {
        double x = endFrameData.mRoadSideDistance + mObjectCoordinateSettings.mLocalVehicleWidth / 2;
        x = (radarID == 6 || radarID == 4) ? -x : x;
        objectData.mRoadSideLineX = x * mHorizontalPixelPerMetre;
    }

    mNewData = true;

//    draw();
}
// float x, float y, float vx, float vy
void ObjectCoordinateSystem::showTrueSystemTarget(const Target *targets, int length)
{
    mNewData = true;
    ObjectData &objectData = mObjectDatas[TRUE_VALUE_RADAR_ID_IFS300];
    clearTargets(TRUE_VALUE_RADAR_ID_IFS300, ObjectData::ObjectTrueObject);
    Objects &objects = objectData.mObjects[ObjectData::ObjectTrueObject];
    const Target *target = targets;

    for (int i = 0; i < length; ++i, ++target)
    {
        Object object;
        object.mID = i;

        object.mX = target->mX;
        object.mY = target->mY;

        object.mX += mObjectCoordinateSettings.mLocalVehicleWidth / 2;
        object.mY += mObjectCoordinateSettings.mLocalVehicleLength / 2;
        object.mY -= mObjectCoordinateSettings.mRearWheelCenterFrontY;
        object.mX -= mObjectCoordinateSettings.mRearWheelCenterFrontX;

        object.mY = -object.mY;
        object.mVy = -object.mVy;
        object.mX = -object.mX;
        object.mVx = -object.mVx;

        object.mX = object.mX * mHorizontalPixelPerMetre;
        object.mY = object.mY * mVerticalPixelPerMetre;
        object.mVx = object.mVx * mHorizontalPixelPerMetre;
        object.mVy = object.mVy * mVerticalPixelPerMetre;

        objects << object;
    }
    objectData.mValid = objects.size();
}

void ObjectCoordinateSystem::showHeSaiTargets(Pandar64_Targets *targets)
{
    mNewData = true;
    clearTargets(TRUE_VALUE_RADAR_ID_Pandar64, ObjectData::ObjectTrueObject);
    if (!mObjectCoordinateSettings.mDisplayHeSaiLider) {
        return;
    }

    ObjectData &objectData = mObjectDatas[TRUE_VALUE_RADAR_ID_Pandar64];
    Objects &objects = objectData.mObjects[ObjectData::ObjectTrueObject];
    for (int i = 0; i < targets->mTargetsCount; ++i) {
        Object object;
        object.mID = i;

        object.mX = targets->mTargets[i].x * mHorizontalPixelPerMetre;
        object.mY = -targets->mTargets[i].y * mVerticalPixelPerMetre;

        objects.append(object);
    }

    objectData.mValid = objects.size();
}

void ObjectCoordinateSystem::showBYDTargetsData(quint8 radarID, int frameType, const Targets &targets)
{
//    if (frameType == Frame3Track)
//        qDebug() << __FUNCTION__ << __LINE__ << radarID;
    ObjectData &objectData = mObjectDatas[radarID];
    objectData.mValid = true;
    ObjectData::ObjectType objectType = ObjectData::ObjectBYD16Track;
    switch (frameType) {
    case Frame200Raw:
    {
        objectType = ObjectData::ObjectBYD200Raw;
        clearTargets(radarID, objectType);
        Objects &objects = objectData.mObjects[objectType];
        const Target *target = targets.mTargets;
        for (int i = 0; i < targets.mTargetCount; ++i, ++target)
        {
            if (!target->mValid)
            {
                continue;
            }

            Object object = calculationObject(radarID, frameType, target);

            if (!object.mShow)
            {
                continue;
            }
            objects.append(object);
        }
    }
        return;
    case Frame3Track:
        objectType = ObjectData::ObjectBYD3Track;
        break;
    case Frame16Track:
        objectType = ObjectData::ObjectBYD16Track;
        break;
    case FrameELKTrack:
        objectType = ObjectData::ObjectELKTrack;
        break;
    }
    clearTargets(radarID, objectType);
    Objects &objects = objectData.mObjects[objectType];
    const Target *target = targets.mTargets;
    for (int i = 0; i < targets.mTargetCount; ++i, ++target)
    {
        if (!target->mValid)
        {
            continue;
        }

        Object object;

        object.mObjectClass = (ObjectClass)(int)target->mClass;

        double azumith = target->mTrackFrameAngle;
        object.mID = target->mID;

        object.mX = target->mX;
        object.mY = target->mY;
        object.mVx = target->mVx;
        object.mVy = target->mVy;

        object.mX += mObjectCoordinateSettings.mLocalVehicleWidth / 2;
        object.mY += mObjectCoordinateSettings.mLocalVehicleLength / 2;

        if (target->mProtocolType != ProtocolBAIC) {
        switch (radarID)
        {
        case 4: // OK
            object.mY += mObjectCoordinateSettings.mRearWheelCenterRearY;
            object.mX -= mObjectCoordinateSettings.mRearWheelCenterRearX;
            object.mY -= mObjectCoordinateSettings.mLocalVehicleLength;
            break;
        case 6: // OK
            object.mY -= mObjectCoordinateSettings.mRearWheelCenterFrontY;
            object.mX -= mObjectCoordinateSettings.mRearWheelCenterFrontX;
            break;
        case 7: // OK
            object.mY -= mObjectCoordinateSettings.mRearWheelCenterFrontY;
            object.mX += mObjectCoordinateSettings.mRearWheelCenterFrontX;
            object.mX -= mObjectCoordinateSettings.mLocalVehicleWidth;
            break;
        case 5: // OK
            object.mY += mObjectCoordinateSettings.mRearWheelCenterRearY;
            object.mX += mObjectCoordinateSettings.mRearWheelCenterRearX;
            object.mX -= mObjectCoordinateSettings.mLocalVehicleWidth;
            object.mY -= mObjectCoordinateSettings.mLocalVehicleLength;
            break;
        }
//            qDebug() << __FUNCTION__ << __LINE__ << radarID << target->mID << target->mX << target->mY << object.mX << object.mY;
        } else {
            switch (radarID)
            {
            case 4: // OK
                object.mY -= mObjectCoordinateSettings.mLocalVehicleLength;
                break;
            case 6: // OK
                break;
            case 7: // OK
                object.mX -= mObjectCoordinateSettings.mLocalVehicleWidth;
                break;
            case 5: // OK
                object.mX -= mObjectCoordinateSettings.mLocalVehicleWidth;
                object.mY -= mObjectCoordinateSettings.mLocalVehicleLength;
                break;
            }
        }

        object.mY = -object.mY;
        object.mVy = -object.mVy;
        object.mX = -object.mX;
        object.mVx = -object.mVx;

        object.mShowV = mObjectCoordinateSettings.mDisplay16TrackShowV;
        object.mShowTrackFrame = mObjectCoordinateSettings.mDisplay16TrackFrame;
        if (object.mShowTrackFrame) {
//        if (target->mProtocolType == ProtocolGEELY || target->mProtocolType == ProtocolBYDHO) {
            object.mTrackFrameX = target->mTrackFrameX;
            object.mTrackFrameY = target->mTrackFrameY;
            azumith = 180 + azumith;

            object.mTrackFrameX += mObjectCoordinateSettings.mLocalVehicleWidth / 2;
            object.mTrackFrameY += mObjectCoordinateSettings.mLocalVehicleLength / 2;
            switch (radarID)
            {
            case 4: // OK
                object.mTrackFrameY += mObjectCoordinateSettings.mRearWheelCenterRearY;
                object.mTrackFrameX -= mObjectCoordinateSettings.mRearWheelCenterRearX;
                object.mTrackFrameY -= mObjectCoordinateSettings.mLocalVehicleLength;
                break;
            case 6: // OK
                object.mTrackFrameY -= mObjectCoordinateSettings.mRearWheelCenterFrontY;
                object.mTrackFrameX -= mObjectCoordinateSettings.mRearWheelCenterFrontX;
                break;
            case 7: // OK
                object.mTrackFrameY -= mObjectCoordinateSettings.mRearWheelCenterFrontY;
                object.mTrackFrameX += mObjectCoordinateSettings.mRearWheelCenterFrontX;
                object.mTrackFrameX -= mObjectCoordinateSettings.mLocalVehicleWidth;
                break;
            case 5: // OK
                object.mTrackFrameY += mObjectCoordinateSettings.mRearWheelCenterRearY;
                object.mTrackFrameX += mObjectCoordinateSettings.mRearWheelCenterRearX;
                object.mTrackFrameX -= mObjectCoordinateSettings.mLocalVehicleWidth;
                object.mTrackFrameY -= mObjectCoordinateSettings.mLocalVehicleLength;
                break;
            }


            object.mTrackFrameX = -object.mTrackFrameX;
            object.mTrackFrameY = -object.mTrackFrameY;
        }

        object.mX = object.mX * mHorizontalPixelPerMetre;
        object.mY = object.mY * mVerticalPixelPerMetre;
        object.mVx = object.mVx * mHorizontalPixelPerMetre;
        object.mVy = object.mVy * mVerticalPixelPerMetre;

//        qDebug() << __FUNCTION__ << __LINE__ << radarID << object.mShowV << object.mShowTrackFrame;
        // 速度方向
        if (object.mShowV) {
            object.mVx = object.mX + (object.mVx * 0.5) * mHorizontalPixelPerMetre;
            object.mVy = object.mY + (object.mVy * 0.5) * mVerticalPixelPerMetre;
        }

        // 航迹框
        if (object.mShowTrackFrame)
        {
            double widthHalf = mHorizontalPixelPerMetre * target->mTrackFrameWidth / 2;
            double lengthHalf = mVerticalPixelPerMetre * target->mTrackFrameLength / 2;
            object.mTrackFrameX = object.mTrackFrameX * mHorizontalPixelPerMetre;
            object.mTrackFrameY = object.mTrackFrameY * mVerticalPixelPerMetre;

            azumith = -azumith;
            float s = qSin(azumith * M_PI / 180);
            float c = qCos(azumith * M_PI / 180);
            /**************************************
             * 1 - 4
             * |   |
             * 2 - 3
             * 先旋转，后计算像素点
             **************************************/
    //        targets[i].mX = (x - targets[0].mX) * c - (y - targets[0].mY) * s + targets[0].mX;
    //        targets[i].mY = (x - targets[0].mX) * s + (y - targets[0].mY) * c + targets[0].mY;
            double x1 = (-widthHalf) * c - (-lengthHalf) * s + object.mTrackFrameX;
            double y1 = (-widthHalf) * s + (-lengthHalf) * c + object.mTrackFrameY;
            double x2 = (-widthHalf) * c - (lengthHalf) * s + object.mTrackFrameX;
            double y2 = (-widthHalf) * s + (lengthHalf) * c + object.mTrackFrameY;
            double x3 = (widthHalf) * c - (lengthHalf) * s + object.mTrackFrameX;
            double y3 = (widthHalf) * s + (lengthHalf) * c + object.mTrackFrameY;
            double x4 = (widthHalf) * c - (-lengthHalf) * s + object.mTrackFrameX;
            double y4 = (widthHalf) * s + (-lengthHalf) * c + object.mTrackFrameY;

            double vx = -(2 * lengthHalf) * s + object.mTrackFrameX;
            double vy = (2 * lengthHalf) * c + object.mTrackFrameY;

            // 航迹框
            object.mTrackFrameLines << QLineF(x1, y1, x2, y2) << QLineF(x2, y2, x3, y3)
                                    << QLineF(x3, y3, x4, y4) << QLineF(x4, y4, x1, y1);

            // 箭头的两点坐标
            double xA1, yA1, xA2, yA2;

            // 求得箭头两点坐标
            Utils::CalcVertexes(object.mTrackFrameX, object.mTrackFrameY, vx, vy, xA1, yA1, xA2, yA2);
            // 航迹框箭头
            object.mTrackFrameLines << QLineF(object.mTrackFrameX, object.mTrackFrameY, vx, vy)
                                    << QLineF(vx, vy, xA1, yA1)
                                    << QLineF(vx, vy, xA2, yA2);

    //        qDebug() << __FUNCTION__ << __LINE__ << target->mID
    //                 << target->mX << target->mY << target->mTrackFrameX << target->mTrackFrameY
    //                 << target->mTrackFrameWidth << target->mTrackFrameLength << target->mTrackFrameAngle << azumith
    //                 << object.mX << object.mY << object.mTrackFrameX << object.mTrackFrameY << object.mTrackFrameLines;
        }

//        Object object = calculationObject(radarID, FrameBYD16Track, target16, radarData);
        if (!object.mShow)
        {
            continue;
        }
        objects.append(object);
//        qDebug() << __FUNCTION__ << __LINE__ << target16->mID << target16->mX << target16->mY << object.mX << object.mY;
    }
    mNewData = true;
}

void ObjectCoordinateSystem::zoom(double scaleFactor)
{

}

void ObjectCoordinateSystem::showConfig(quint8 radarID)
{
    ObjectCorrdinateSystemConfigDialog *dialog = new ObjectCorrdinateSystemConfigDialog(radarID, this);
    dialog->exec();
}

void ObjectCoordinateSystem::paintEvent(QPaintEvent *event)
{
    QPainter painter;
    painter.begin(this);

    painter.drawPixmap(0, 0, mPixmapTargets);
    painter.end();
    QWidget::paintEvent(event);
}

void ObjectCoordinateSystem::wheelEvent(QWheelEvent *event)
{
    if (event->modifiers() & Qt::ControlModifier) {
        if (event->angleDelta().y() > 0)
            zoom(1.1);
        else
            zoom(0.9);
        event->accept();
    } else {
        QWidget::wheelEvent(event);
    }
}

void ObjectCoordinateSystem::resizeEvent(QResizeEvent *event)
{
    int thisWidth = this->width();
    int thisHeight = this->height();
    mLeftCustomAxis->setFixedSize(mLeftCustomAxis->axisWidth(), thisHeight);
    mLeftCustomAxis->move(0, 0);
    mRightCustomAxis->setFixedSize(mRightCustomAxis->axisWidth(), thisHeight);
    mRightCustomAxis->move(thisWidth - mRightCustomAxis->axisWidth(), 0);
    mTopCustomAxis->setFixedSize(thisWidth, mTopCustomAxis->axisWidth());
    mTopCustomAxis->move(0, 0);

    QTimer::singleShot(10, this, [=](){
        setObjectCoordinateSettings(mObjectCoordinateSettings);
    });
}

void ObjectCoordinateSystem::mouseMoveEvent(QMouseEvent *event)
{

}

void ObjectCoordinateSystem::mousePressEvent(QMouseEvent *event)
{
    mLeftCustomAxis->setValue(mLeftCustomAxis->axisLength() - event->pos().y());
    mRightCustomAxis->setValue(mRightCustomAxis->axisLength() - event->pos().y());
    mTopCustomAxis->setValue(event->pos().x());
}

void ObjectCoordinateSystem::draw()
{
    if (!mNewData)
    {
        return;
    }
    QPixmap pixmap(mPixmapBackground);
    drawTargets(&pixmap);
    mPixmapTargets = pixmap;
    update();
    mNewData = false;
}

Object ObjectCoordinateSystem::calculationObject(quint8 radarID, int frameType, const Target *target)
{
    Object object;
    object.mID = target->mID;
    object.mAlarm = target->mAlarmed;
    object.mEarlyWarning = target->mEarlyWarning;
    object.mStatus = target->mStatus;
    object.mMatchFlag = target->mMatchFlag;

    object.mX = target->mX;
    object.mY = target->mY;
    object.mVx = target->mVxsog;
    object.mVy = target->mVysog;
    object.mAx = target->mAx;
    object.mAy = target->mAy;
    object.mGroupId = target->mGroupID;
#if 1
    object.mTrackFrameX = target->mTrackFrameX;
    object.mTrackFrameY = target->mTrackFrameY;
    double azumith = target->mTrackFrameAngle;
#else
    object.mTrackFrameX = target->mX;
    object.mTrackFrameY = target->mY;
    double azumith = 10 * target->mID;
#endif

    // 动静属性
    switch ((int)target->mDynamicProperty)
    {
    case 0x1: // moving
        object.mStateOfMotion = OutwardMovement;
        //            object.mStateOfMotion = InwardMovement;
        break;
    default:
        object.mStateOfMotion = Motionless;
        break;
    }

    object.mObjectClass = (ObjectClass)(int)target->mClass;

    if (frameType == FrameRawTarget || frameType == FrameTrackTarget) {
        // 显示配置
        DrawConfig &drawConfig = mObjectDatas[radarID].mObjectsDrawConfig[frameType].mDrawConfig[object.mStateOfMotion];
        object.mShow = drawConfig.mShow;
        if (!object.mShow) {
            return object;
        }
    }

    // 车身长宽的一半
    object.mX = object.mX + mObjectCoordinateSettings.mLocalVehicleWidth / 2;
    object.mY = object.mY + mObjectCoordinateSettings.mLocalVehicleLength / 2;
    object.mTrackFrameX = object.mTrackFrameX + mObjectCoordinateSettings.mLocalVehicleWidth / 2;
    object.mTrackFrameY = object.mTrackFrameY + mObjectCoordinateSettings.mLocalVehicleLength / 2;

    if (frameType == Frame16Track || frameType == Frame3Track || frameType == FrameELKTrack)
    {
//        qDebug() << __FUNCTION__ << __LINE__ << frameType << radarID << object.mID << object.mX << object.mY;
        switch (radarID)
        {
        case 4: // OK
            object.mY -= mObjectCoordinateSettings.mLocalVehicleLength;
            object.mVy -= mObjectCoordinateSettings.mLocalVehicleLength;
            break;
        case 6: // OK
            break;
        case 7: // OK
            object.mX -= mObjectCoordinateSettings.mLocalVehicleWidth;
            break;
        case 5: // OK
            object.mX -= mObjectCoordinateSettings.mLocalVehicleWidth;
            object.mY -= mObjectCoordinateSettings.mLocalVehicleLength;
            break;
        }

        object.mX = -object.mX;
        object.mVx = -object.mVx;
        object.mY = -object.mY;
        object.mVy = -object.mVy;

//        qDebug() << __FUNCTION__ << __LINE__ << object.mID << object.mX << object.mY;
    }
    else
    {
        switch (radarID) {
        case 4:
            object.mX = -object.mX;
            object.mVx = -object.mVx;
            object.mAx = -object.mAx;
            object.mTrackFrameX = -object.mTrackFrameX;
            azumith = -azumith;
            break;
        case 5:
            break;
        case 6:
            object.mX = -object.mX;
            object.mY = -object.mY;
            object.mVx = -object.mVx;
            object.mVy = -object.mVy;
            object.mAx = -object.mAx;
            object.mAy = -object.mAy;
            object.mTrackFrameX = -object.mTrackFrameX;
            object.mTrackFrameY = -object.mTrackFrameY;
            azumith = 180 + azumith;
            break;
        case 7:
            object.mY = -object.mY;
            object.mVy = -object.mVy;
            object.mAy = -object.mAy;
            object.mTrackFrameY = -object.mTrackFrameY;
            azumith = 180 - azumith;
            break;
        }
    }

    object.mX = object.mX * mHorizontalPixelPerMetre;
    object.mY = object.mY * mVerticalPixelPerMetre;
    object.mShowV = (mObjectCoordinateSettings.mDisplayVelocityFeversal && (FrameTrackTarget == frameType));
    object.mShowAcc = (mObjectCoordinateSettings.mDisplayAcceleration && (FrameTrackTarget == frameType));
    object.mShowTrackFrame = (mObjectCoordinateSettings.mDisplayTrackFrame && (object.mStateOfMotion != Motionless) && (FrameTrackTarget == frameType));

    if ((FrameTrackTarget == frameType) && (object.mStateOfMotion != Motionless)) {
        if (mObjectCoordinateSettings.mTargetVeiwAnalysisTypes.size()) {
            object.mText = QString("%1: %2").arg(gAnalysisTypeName(ID)).arg(target->mID);
        }
        foreach (AnalysisType t, mObjectCoordinateSettings.mTargetVeiwAnalysisTypes)
        {
            object.mText.append(QString("\r\n%1: %2")
                                .arg(gAnalysisTypeName((AnalysisType)t))
                                .arg(target->value((AnalysisType)t)));
        }
    }

    // 速度方向
    if (object.mShowV) {
        object.mVx = object.mX + (object.mVx * 0.5) * mHorizontalPixelPerMetre;
        object.mVy = object.mY + (object.mVy * 0.5) * mVerticalPixelPerMetre;
    }

    // 加速度方向
    if (object.mShowAcc) {
        object.mAx = object.mX + (object.mAx * 0.5) * mHorizontalPixelPerMetre;
        object.mAy = object.mY + (object.mAy * 0.5) * mVerticalPixelPerMetre;
    }

    // 航迹框
    if (object.mShowTrackFrame)
    {
        double widthHalf = mHorizontalPixelPerMetre * target->mTrackFrameWidth / 2;
        double lengthHalf = mVerticalPixelPerMetre * target->mTrackFrameLength / 2;
        object.mTrackFrameX = object.mTrackFrameX * mHorizontalPixelPerMetre;
        object.mTrackFrameY = object.mTrackFrameY * mVerticalPixelPerMetre;

        azumith = -azumith;
        float s = qSin(azumith * M_PI / 180);
        float c = qCos(azumith * M_PI / 180);
        /**************************************
         * 1 - 4
         * |   |
         * 2 - 3
         * 先旋转，后计算像素点
         **************************************/
//        targets[i].mX = (x - targets[0].mX) * c - (y - targets[0].mY) * s + targets[0].mX;
//        targets[i].mY = (x - targets[0].mX) * s + (y - targets[0].mY) * c + targets[0].mY;
        double x1 = (-widthHalf) * c - (-lengthHalf) * s + object.mTrackFrameX;
        double y1 = (-widthHalf) * s + (-lengthHalf) * c + object.mTrackFrameY;
        double x2 = (-widthHalf) * c - (lengthHalf) * s + object.mTrackFrameX;
        double y2 = (-widthHalf) * s + (lengthHalf) * c + object.mTrackFrameY;
        double x3 = (widthHalf) * c - (lengthHalf) * s + object.mTrackFrameX;
        double y3 = (widthHalf) * s + (lengthHalf) * c + object.mTrackFrameY;
        double x4 = (widthHalf) * c - (-lengthHalf) * s + object.mTrackFrameX;
        double y4 = (widthHalf) * s + (-lengthHalf) * c + object.mTrackFrameY;

        double vx = -(2 * lengthHalf) * s + object.mTrackFrameX;
        double vy = (2 * lengthHalf) * c + object.mTrackFrameY;

        // 航迹框
        object.mTrackFrameLines << QLineF(x1, y1, x2, y2) << QLineF(x2, y2, x3, y3)
                                << QLineF(x3, y3, x4, y4) << QLineF(x4, y4, x1, y1);

        // 箭头的两点坐标
        double xA1, yA1, xA2, yA2;

        // 求得箭头两点坐标
        Utils::CalcVertexes(object.mTrackFrameX, object.mTrackFrameY, vx, vy, xA1, yA1, xA2, yA2);
        // 航迹框箭头
        object.mTrackFrameLines << QLineF(object.mTrackFrameX, object.mTrackFrameY, vx, vy)
                                << QLineF(vx, vy, xA1, yA1)
                                << QLineF(vx, vy, xA2, yA2);

//        qDebug() << __FUNCTION__ << __LINE__ << target->mID
//                 << target->mX << target->mY << target->mTrackFrameX << target->mTrackFrameY
//                 << target->mTrackFrameWidth << target->mTrackFrameLength << target->mTrackFrameAngle << azumith
//                 << object.mX << object.mY << object.mTrackFrameX << object.mTrackFrameY << object.mTrackFrameLines;
    }

    return object;
}

Object ObjectCoordinateSystem::calculationObject(ObjectData::ObjectType objectType, const Parser::ParsedDataTypedef::TargetF &target)
{

    Object object;

    object.mX = target.mX;
    object.mY = target.mY;
    object.mVx = target.mVx;
    object.mVy = target.mVy;
    object.mAx = target.mAx;
    object.mAy = target.mAy;
    object.mY = object.mY + mObjectCoordinateSettings.mLocalVehicleLength / 2;

    object.mY = -object.mY;
    object.mVy = -object.mVy;
    object.mAy = -object.mAy;

    object.mX = object.mX * mHorizontalPixelPerMetre;
    object.mY = object.mY * mVerticalPixelPerMetre;

    object.mText = QString::number(target.mID);

    // 动静属性
    switch ((int)target.mDynamicProperty)
    {
    case 0x1: // moving
        object.mStateOfMotion = OutwardMovement;
        //            object.mStateOfMotion = InwardMovement;
        break;
    default:
        object.mStateOfMotion = Motionless;
        break;
    }

    switch (objectType) {
    case ObjectData::ObjectRaw:                ///< 原始点目标
    case ObjectData::ObjectTrack:              ///< 跟踪点目标
    {
        DrawConfig &drawConfig = mObjectDatas[0].mObjectsDrawConfig[objectType].mDrawConfig[object.mStateOfMotion];
        object.mShow = drawConfig.mShow;
        if (!object.mShow) {
            return object;
        }
    }
        break;
    case ObjectData::ObjectTrueObject:         ///< 禾赛激光雷达点
        break;
    default:
        break;
    }

    return object;
}

void ObjectCoordinateSystem::drawBackground(QPixmap *pixmapGackground)
{
//    qDebug() << __FUNCTION__ << __LINE__ << "Draw Background!";
    pixmapGackground->fill(Qt::black);
    QPainter painter;
    painter.begin(pixmapGackground);

    painter.setPen(QPen(Qt::darkRed, 1, Qt::DashLine));

    if (mObjectCoordinateSettings.mDisplayZeroLine)
    {
        painter.drawLine(mZeroPoint.x(), 0, mZeroPoint.x(), this->height());
        painter.drawLine( 0, mZeroPoint.y(), this->width(), mZeroPoint.y());
        painter.drawLine( 0, mZeroPointF.y(), this->width(), mZeroPointF.y());
    }

    double width = this->width();
    double height = this->height();

    if (mObjectCoordinateSettings.mDOWDisplay)
    {
        painter.setPen(QColor(0, 0, 0, 0));
        painter.setBrush(QColor(239, 255, 204, 50));
        painter.drawRect(mZeroPoint.x() - ((mObjectCoordinateSettings.mLocalVehicleWidth / 2 + mObjectCoordinateSettings.mDOWDistanceOfBody + mObjectCoordinateSettings.mDOWWidth) * mHorizontalPixelPerMetre),
                         mZeroPoint.y() + ((-mObjectCoordinateSettings.mLocalVehicleLength / 2 + mObjectCoordinateSettings.mDOWDistanceOfHeadway) * mVerticalPixelPerMetre),
                         mObjectCoordinateSettings.mDOWWidth * mHorizontalPixelPerMetre,
                         mObjectCoordinateSettings.mDOWLength * mVerticalPixelPerMetre);
        painter.setBrush(QColor(239, 255, 204, 50));
        painter.drawRect(mZeroPoint.x() + ((mObjectCoordinateSettings.mLocalVehicleWidth / 2 + mObjectCoordinateSettings.mDOWDistanceOfBody) * mHorizontalPixelPerMetre),
                         mZeroPoint.y() + ((-mObjectCoordinateSettings.mLocalVehicleLength / 2 + mObjectCoordinateSettings.mDOWDistanceOfHeadway) * mVerticalPixelPerMetre),
                         mObjectCoordinateSettings.mDOWWidth * mHorizontalPixelPerMetre,
                         mObjectCoordinateSettings.mDOWLength * mVerticalPixelPerMetre);
    }

    if (mObjectCoordinateSettings.mBSDDisplay)
    {
        painter.setPen(QColor(0, 0, 0, 0));
        painter.setBrush(QColor(239, 255, 204, 50));
        painter.drawRect(mZeroPoint.x() - ((mObjectCoordinateSettings.mLocalVehicleWidth / 2 + mObjectCoordinateSettings.mBSDDistanceOfBody + mObjectCoordinateSettings.mBSDWidth) * mHorizontalPixelPerMetre),
                         mZeroPoint.y() + ((-mObjectCoordinateSettings.mLocalVehicleLength / 2 + mObjectCoordinateSettings.mBSDDistanceOfHeadway) * mVerticalPixelPerMetre),
                         mObjectCoordinateSettings.mBSDWidth * mHorizontalPixelPerMetre,
                         mObjectCoordinateSettings.mBSDLength * mVerticalPixelPerMetre);
        painter.setBrush(QColor(239, 255, 204, 50));
        painter.drawRect(mZeroPoint.x() + ((mObjectCoordinateSettings.mLocalVehicleWidth / 2 + mObjectCoordinateSettings.mBSDDistanceOfBody) * mHorizontalPixelPerMetre),
                         mZeroPoint.y() + ((-mObjectCoordinateSettings.mLocalVehicleLength / 2 + mObjectCoordinateSettings.mBSDDistanceOfHeadway) * mVerticalPixelPerMetre),
                         mObjectCoordinateSettings.mBSDWidth * mHorizontalPixelPerMetre,
                         mObjectCoordinateSettings.mBSDLength * mVerticalPixelPerMetre);
    }

    if (mObjectCoordinateSettings.mLCADisplay)
    {
        painter.setPen(QColor(0, 0, 0, 0));
        painter.setBrush(QColor(239, 255, 204, 50));
        painter.drawRect(mZeroPoint.x() - ((mObjectCoordinateSettings.mLocalVehicleWidth / 2 + mObjectCoordinateSettings.mLCADistanceOfBody + mObjectCoordinateSettings.mLCAWidth) * mHorizontalPixelPerMetre),
                         mZeroPoint.y() + ((-mObjectCoordinateSettings.mLocalVehicleLength / 2 + mObjectCoordinateSettings.mLCADistanceOfHeadway) * mVerticalPixelPerMetre),
                         mObjectCoordinateSettings.mLCAWidth * mHorizontalPixelPerMetre,
                         mObjectCoordinateSettings.mLCALength * mVerticalPixelPerMetre);
        painter.setBrush(QColor(239, 255, 204, 50));
        painter.drawRect(mZeroPoint.x() + ((mObjectCoordinateSettings.mLocalVehicleWidth / 2 + mObjectCoordinateSettings.mLCADistanceOfBody) * mHorizontalPixelPerMetre),
                         mZeroPoint.y() + ((-mObjectCoordinateSettings.mLocalVehicleLength / 2 + mObjectCoordinateSettings.mLCADistanceOfHeadway) * mVerticalPixelPerMetre),
                         mObjectCoordinateSettings.mLCAWidth * mHorizontalPixelPerMetre,
                         mObjectCoordinateSettings.mLCALength * mVerticalPixelPerMetre);
    }

    double drawLeft  = mLeftCustomAxis->axisWidth();
    double drawTop = mTopCustomAxis->axisWidth();
    double value = mObjectCoordinateSettings.mHorizontalMinRange;
    if (mObjectCoordinateSettings.mDisplayGridLine)
    {
        while (value <= mObjectCoordinateSettings.mHorizontalMaxRange)
        {
            value ? painter.setPen(QPen(Qt::darkGray, 1, Qt::DashLine)) : painter.setPen(QPen(Qt::darkRed, 1, Qt::DashLine));
            if (value != 0) painter.drawLine(QPointF(drawLeft, drawTop), QPointF(drawLeft, height));
            value += mObjectCoordinateSettings.mHorizontalInterval;
            drawLeft += mObjectCoordinateSettings.mHorizontalInterval * mHorizontalPixelPerMetre;
        }

        drawLeft  = mLeftCustomAxis->axisWidth();
        value = mObjectCoordinateSettings.mVerticalMinRange;
        while (value <= mObjectCoordinateSettings.mVerticalMaxRange)
        {
            value ? painter.setPen(QPen(Qt::darkGray, 1, Qt::DashLine)) : painter.setPen(QPen(Qt::darkRed, 1, Qt::DashLine));
            painter.drawLine(QPointF(drawLeft, drawTop), QPointF(width, drawTop));
            value += mObjectCoordinateSettings.mVerticalInterval;
            drawTop += mObjectCoordinateSettings.mVerticalInterval * mVerticalPixelPerMetre;
        }
    }

    if (mObjectCoordinateSettings.mDisplayLane)
    {
        painter.setPen(QPen(Qt::darkYellow, 1, Qt::DashLine));
        value = mObjectCoordinateSettings.mLaneWidth / 2;
        drawLeft = mZeroPoint.x() + value * mHorizontalPixelPerMetre;
        drawTop = mTopCustomAxis->axisWidth();
        while (value < mObjectCoordinateSettings.mHorizontalMaxRange)
        {
            painter.drawLine(QPointF(drawLeft, drawTop), QPointF(drawLeft, height));
            value += mObjectCoordinateSettings.mLaneWidth;
            drawLeft = mZeroPoint.x() + value * mHorizontalPixelPerMetre;
        }

        value = -mObjectCoordinateSettings.mLaneWidth / 2;
        drawLeft = mZeroPoint.x() + value * mHorizontalPixelPerMetre;
        drawTop = mTopCustomAxis->axisWidth();
        while (value > mObjectCoordinateSettings.mHorizontalMinRange)
        {
            painter.drawLine(QPointF(drawLeft, drawTop), QPointF(drawLeft, height));
            value -= mObjectCoordinateSettings.mLaneWidth;
            drawLeft = mZeroPoint.x() + value * mHorizontalPixelPerMetre;
        }
    }

    if (!mPixmapCar.isNull())
    {
        painter.drawPixmap(mZeroPoint.x() - mHorizontalPixelPerMetre * mObjectCoordinateSettings.mLocalVehicleWidth / 2,
                           mZeroPoint.y() - mVerticalPixelPerMetre * mObjectCoordinateSettings.mLocalVehicleLength / 2,
                           mPixmapCar);
    }

    painter.end();
}

void ObjectCoordinateSystem::drawTargets(QPixmap *pixmapTargets)
{
    QPainter painter;
    painter.begin(pixmapTargets);

    painter.drawPixmap(0, 0, mPixmapBackground);

    // 绘制转弯车道线
    if (mObjectCoordinateSettings.mDisplayLane)
    {
        drawLane(painter);
    }

    painter.translate(mZeroPoint);


    // 绘制禾赛激光雷达目标
//    if (radarID == TRUE_VALUE_RADAR_ID_Pandar64) {
    ObjectData &hsObjectData = mObjectDatas[TRUE_VALUE_RADAR_ID_Pandar64];
        drawHeSaiLiderTrueTarget(painter, TRUE_VALUE_RADAR_ID_Pandar64, hsObjectData);
//    }

    // 绘制角雷达点信息
    for (int radarID = 0; radarID <= RADAR_ID_END; !radarID ? radarID = RADAR_ID_BEGIN : ++radarID)
    {
        ObjectData &objectData = mObjectDatas[radarID];

        if (!objectData.mValid)
        {
            continue;
        }

        // 绘制路旁线
        if (objectData.mObjectShowConfigs.mShowRoadSideLine) {
            drawRoadSideLine(painter, radarID, objectData);
        }

        // 绘制道路模型
        drawRoadModel(painter, radarID, objectData);

        // 绘制目标点
        drawTargets(painter, radarID, objectData);

        // 绘制航迹框
        if (mObjectCoordinateSettings.mDisplayTrackFrame) {
            drawTrackFrame(painter, radarID, objectData.mObjects[ObjectData::ObjectTrack]);
        }

        // 绘制告警信息
//        drawAlarmInfomation(painter, radarID, objectData);

        // 绘制16个点目标
        if (mObjectCoordinateSettings.mDisplay16TrackTarget) {
            draw16TrackTarget(painter, radarID, objectData);
        }

        // 绘制速度方向
        if (mObjectCoordinateSettings.mDisplayVelocityFeversal) {
            drawSpeedDirection(painter, radarID, objectData);
        }

        // 绘制加速度方向
        if (mObjectCoordinateSettings.mDisplayAcceleration) {
            drawAccDirection(painter, radarID, objectData);
        }

        // 绘制真值目标
        if (radarID == TRUE_VALUE_RADAR_ID_IFS300) {
            drawIFS300TrueTarget(painter, objectData);
        }

        // 绘制目标数据
        drawTargetData(painter, radarID, objectData);
    }

    painter.end();
}
static QColor groupBuf[128];
void ObjectCoordinateSystem::drawTargets(QPainter &painter, quint8 radarID, ObjectData &objectData)
{
    painter.setBrush(Qt::transparent);

    for (int j = 0; j <= 128; j++)
    {
        uint8 r = QRandomGenerator::global()->bounded(0x255);
        uint8 g = QRandomGenerator::global()->bounded(0x255);
        uint8 b = QRandomGenerator::global()->bounded(0x255);
        groupBuf[j] = QColor(r,g,b);
    }

    for (int j = ObjectData::ObjectRaw; j <= ObjectData::ObjectTrack; ++j)
    {
        Objects &objects = objectData.mObjects[j];
        foreach (const Object &object, objects)
        {
            DrawConfig &drawConfig = objectData.mObjectsDrawConfig[j].mDrawConfig[object.mStateOfMotion];
            // 绘制目标点
            if (j == ObjectData::ObjectRaw && mObjectCoordinateSettings.mDisplayHighlightedVelocityAmbiguity) {
                /*if (object.mMatchFlag < 0x3) {
                    painter.setPen(QColor(255, 204, 153)); // 淡黄色
                } else */if (object.mStatus == 8 || object.mStatus == 24 || object.mStatus == 40 || object.mStatus == 56) {
                    painter.setPen(QColor(231, 139, 68)); // 橘色
                } else if ((object.mStatus & 0x40) == 0x40) {
                    painter.setPen(QColor(255, 102, 102)); // 橘红色
                } else if ((object.mStatus & 0x10) == 0x10) {
                    painter.setPen(QColor(255, 204, 153)); // 淡黄色
                } else {
                    painter.setPen(drawConfig.mColor);
                }
            }
            else
            {
                painter.setPen(drawConfig.mColor);

                //聚类调试时，使用group设置颜色
                if(1 && j == ObjectData::ObjectRaw)
                {
                    uint8 groupid = (uint8)object.mGroupId;
                    painter.setPen(groupBuf[groupid]);
                }
            }
            switch (drawConfig.mDrawShape)
            {
            case HollowCircle:
                painter.drawEllipse(object.mX - drawConfig.mSize / 2, object.mY - drawConfig.mSize / 2, drawConfig.mSize, drawConfig.mSize);
                break;
            case SolidCircle:
                painter.setBrush(drawConfig.mColor);
                painter.drawEllipse(object.mX - drawConfig.mSize / 2, object.mY - drawConfig.mSize / 2, drawConfig.mSize, drawConfig.mSize);
                break;
            case OutlinedRectangle:
                painter.drawRect(object.mX - drawConfig.mSize / 2, object.mY - drawConfig.mSize / 2, drawConfig.mSize, drawConfig.mSize);
                break;
            case FilledRectangle:
                painter.setBrush(drawConfig.mColor);
                painter.drawRect(object.mX - drawConfig.mSize / 2, object.mY - drawConfig.mSize / 2, drawConfig.mSize, drawConfig.mSize);
                break;
            }

            // 绘制告警信息
            if (object.mAlarm)
            {
                painter.drawPixmap(object.mX + drawConfig.mSize / 2, object.mY, mPixmapAlarm);
            }

            if (object.mEarlyWarning)
            {
                painter.drawPixmap(object.mX - drawConfig.mSize / 2 - ALARM_PIXMAP_SIZE, object.mY, mPixmapEarlyWarning);
            }
        }
    }
}

void ObjectCoordinateSystem::drawLane(QPainter &painter)
{
    bool drawmRadius = (qAbs(mRadius) < 32767.0) && (qAbs(mRadius) > 6);
    if(drawmRadius)
    {
//            mRadius = 100;
//            mVehicleGear = 4;
        double x = qSqrt(mRadius * mRadius - mObjectCoordinateSettings.mLocalVehicleLength * mObjectCoordinateSettings.mLocalVehicleLength);
        double angle = 180 * qAcos(x / qFabs(mRadius)) / M_PI;

        QRectF rectOne(mHorizontalPixelPerMetre * (((mRadius < 0) ? -1 : 1) * (x + mObjectCoordinateSettings.mLocalVehicleWidth / 2) - mRadius) - 1,
                       mVerticalPixelPerMetre * (-mRadius + mObjectCoordinateSettings.mLocalVehicleLength / 2),
                       mHorizontalPixelPerMetre * 2 * mRadius,
                       mVerticalPixelPerMetre * 2 * mRadius);
        QRectF rectTwo(mHorizontalPixelPerMetre * (((mRadius < 0) ? -1 : 1) * (x - mObjectCoordinateSettings.mLocalVehicleWidth / 2) - mRadius) - 1,
                       mVerticalPixelPerMetre * (-mRadius + mObjectCoordinateSettings.mLocalVehicleLength / 2),
                       mHorizontalPixelPerMetre * 2 * mRadius,
                       mVerticalPixelPerMetre * 2 * mRadius);
        int startAngle = ((mRadius < 0) ? angle : (180 - angle )) * 16;
        int spanAngle = ((mVehicleGear == 3) ? -90 : 90) * 16;
        spanAngle = (mRadius < 0) ? spanAngle : -spanAngle;
        QTransform transform;
        transform.translate(mZeroPoint.x(), mZeroPoint.y());
        painter.save();
        painter.setTransform(transform);
        painter.setPen(QPen(QColor(255, 255, 0, 50), 2, Qt::DashLine));
        painter.drawArc(rectOne, startAngle, spanAngle);
        painter.drawArc(rectTwo, startAngle, spanAngle);
        painter.restore();
    }
}

void ObjectCoordinateSystem::drawRoadSideLine(QPainter &painter, quint8 radarID, ObjectData &objectData)
{
    // 绘制路旁线
    painter.setPen(QPen(objectData.mObjectsDrawConfig[ObjectData::ObjectTrack].mDrawConfig[Motionless].mColor, 1, Qt::DashLine));
    painter.drawLine(objectData.mRoadSideLineX, -mZeroPoint.y() + mTopCustomAxis->axisWidth(), objectData.mRoadSideLineX, this->height() - mZeroPoint.y());
}

void ObjectCoordinateSystem::drawRoadModel(QPainter &painter, quint8 radarID, ObjectData &objectData)
{
    // 绘制道路模型

    painter.save();
    painter.setBrush(Qt::white);
    painter.setPen(Qt::white);
    Objects &objects = objectData.mObjects[ObjectData::ObjectRoadMode];
    foreach (const Object &object, objects)
    {
        painter.drawEllipse(object.mX - 1, object.mY - 1, 2, 2);
    }
    painter.restore();
}

void ObjectCoordinateSystem::drawAlarmInfomation(QPainter &painter, quint8 radarID, ObjectData &objectData)
{
    painter.setBrush(Qt::transparent);
    Objects &objects = objectData.mObjects[ObjectData::ObjectTrack];
    foreach (const Object &object, objects)
    {
        DrawConfig &drawConfig = objectData.mObjectsDrawConfig[ObjectData::ObjectTrack].mDrawConfig[object.mStateOfMotion];
        // 绘制告警信息
        if (object.mAlarm)
        {
            painter.drawPixmap(object.mX + drawConfig.mSize / 2, object.mY, mPixmapAlarm);
        }

        if (object.mEarlyWarning)
        {
            painter.drawPixmap(object.mX - drawConfig.mSize / 2 - ALARM_PIXMAP_SIZE, object.mY, mPixmapEarlyWarning);
        }
    }
}

void ObjectCoordinateSystem::drawSpeedDirection(QPainter &painter, quint8 radarID, ObjectData &objectData)
{
    painter.setBrush(Qt::transparent);
    painter.setPen(Qt::yellow);
    Objects &objects = objectData.mObjects[ObjectData::ObjectTrack];
    foreach (const Object &object, objects)
    {
        // 绘制速度方向
        if (object.mShowV) {
            painter.drawLine(object.mX, object.mY, object.mVx, object.mVy);
        }
    }
}

void ObjectCoordinateSystem::drawAccDirection(QPainter &painter, quint8 radarID, ObjectData &objectData)
{
    painter.setBrush(Qt::transparent);
    painter.setPen(Qt::magenta);
    Objects &objects = objectData.mObjects[ObjectData::ObjectTrack];
    foreach (const Object &object, objects)
    {
        // 绘制加速度方向
        if (object.mShowV) {
            painter.drawLine(object.mX, object.mY, object.mAx, object.mAy);
        }
    }
}

void ObjectCoordinateSystem::drawTrackFrame(QPainter &painter, quint8 radarID, Objects &objects)
{
    painter.setBrush(Qt::transparent);
    QPen trackPen(Qt::magenta);
    trackPen.setWidthF(0.5);
//    trackPen.setStyle(Qt::DotLine);
//    painter.setPen(trackPen);
//    Objects &objects = objectData.mObjects[ObjectData::ObjectTrack];
    foreach (const Object &object, objects)
    {
        // 绘制航迹框
        if (object.mShowTrackFrame) {
//            qDebug() << __FUNCTION__ << __LINE__ << object.mObjectClass;
            switch (object.mObjectClass) {
            case ObjectCar:                         // 轿车-黄色；卡车-红色;行人-绿色;两轮车-洋红;自行车-深洋红;其它-白色
                trackPen.setColor(Qt::yellow);      // 黄色
                break;
            case ObjectTruck:                       // 卡车
                trackPen.setColor(Qt::red);         // 红色
                break;
            case ObjectPedestran:                   // 行人
                trackPen.setColor(Qt::green);       // 绿色
                break;
            case ObjectMotorcycle:                  // 两轮车
                trackPen.setColor(Qt::magenta);     // 洋红
                break;
            case ObjectBicycle:                     // 自行车
                trackPen.setColor(Qt::darkMagenta); // 深洋红
                break;
            case ObjectWide:
            case ObjectReserved:
            default:
            case ObjectUnknown:                     // 未知
                trackPen.setColor(Qt::white);       // 白色
                break;
            }
            painter.setPen(trackPen);
            painter.drawLines(object.mTrackFrameLines);
        }
    }
}

void ObjectCoordinateSystem::drawTargetData(QPainter &painter, quint8 radarID, ObjectData &objectData)
{
    painter.setBrush(Qt::transparent);
    for (int j = ObjectData::ObjectRaw; j <= ObjectData::ObjectTrack; ++j)
    {
        if (j == ObjectData::ObjectRaw && !mObjectCoordinateSettings.mDisplayTargetID) {
            continue;
        }
        Objects &objects = objectData.mObjects[j];
        foreach (const Object &object, objects)
        {
            DrawConfig &drawConfig = objectData.mObjectsDrawConfig[j].mDrawConfig[object.mStateOfMotion];

            // 绘制目标数据
            painter.setPen(Qt::white);

            double textX = object.mX + drawConfig.mSize;
            double textY = object.mY - drawConfig.mSize;
            if (object.mText.isEmpty()) {
                QString idText = QString::number(object.mID);
                switch (radarID) {
                case 4:
                    textX = object.mX - drawConfig.mSize - QFontMetrics(this->font()).boundingRect(idText).width();
                    textY = object.mY + drawConfig.mSize + QFontMetrics(this->font()).boundingRect(idText).height() / 2;
                    break;
                case 5:
                    textY = object.mY + drawConfig.mSize + QFontMetrics(this->font()).boundingRect(idText).height() / 2;
                    break;
                case 6:
                    textX = object.mX - drawConfig.mSize - QFontMetrics(this->font()).boundingRect(idText).width();
                    break;
                case 7:
                    break;
                default:
                    break;
                }
                painter.drawText(textX, textY, idText);
            } else {
                painter.drawText(textX, textY, 100, 100, Qt::TextWordWrap, object.mText);
            }
        }
    }
}

void ObjectCoordinateSystem::draw16TrackTarget(QPainter &painter, quint8 radarID, ObjectData &objectData)
{
    for (int j = ObjectData::ObjectBYD200Raw; j <= (ObjectData::ObjectELKTrack); ++j)
    {
        int object_size = BYD_OBJECT_SIZE;
        Qt::PenStyle penStyle = Qt::SolidLine;
        int front_size = 15;
        painter.save();
        switch (j) {
        case ObjectData::ObjectBYD200Raw:
            object_size = BYD_OBJECT_SIZE * 0.8;
            penStyle = Qt::DotLine;
            front_size = 10;
            break;
        case ObjectData::ObjectBYD3Track:
        case ObjectData::ObjectELKTrack:
            object_size = BYD_OBJECT_SIZE * 1.5;
            break;
        }
        QColor color = Qt::white;
        switch (radarID)
        {
        case 4:
            color = Qt::magenta; // 洋红
            break;
        case 5:
            switch (j) {
            case ObjectData::ObjectBYD16Track:
                color = Qt::yellow; // 黄色
                break;
            case ObjectData::ObjectBYD3Track:
                color = Qt::white; // 白色
                break;
            case ObjectData::ObjectELKTrack:
                color = QColor(255, 192, 0); // 橘黄
                break;
            }
            break;
        case 6:
            color = QColor(36, 169, 225); // 天蓝
            break;
        case 7:
            color = Qt::red; // 红色
            break;
        }
        QFont font = this->font();
        font.setPointSize(front_size);
        painter.setFont(font);
        painter.setBrush(Qt::transparent);
        Objects &objects = objectData.mObjects[j];
        foreach (const Object &object, objects)
        {
            if (object.mStateOfMotion == Motionless) {
                painter.setPen(QPen(color, 1, penStyle));
            } else {
                painter.setPen(QPen(Qt::green, 1, penStyle));
            }
            painter.drawRect(object.mX - object_size / 2, object.mY - object_size / 2, object_size, object_size);
            if (!(radarID % 2))
            {
                QString text = QString::number(object.mID);
                int textWidth = QFontMetrics(this->font()).boundingRect(text).width();
                painter.drawText(object.mX - object_size - textWidth, object.mY - object_size, text);
            }
            else
            {
                painter.drawText(object.mX + object_size, object.mY - object_size, QString::number(object.mID));
            }
        }

        if (j == ObjectData::ObjectBYD16Track) {
            drawTrackFrame(painter, radarID, objects);
        }
        painter.restore();
    }
}

void ObjectCoordinateSystem::drawIFS300TrueTarget(QPainter &painter, ObjectData &objectData)
{
    Objects &objects = objectData.mObjects[ObjectData::ObjectTrueObject];
    painter.setPen(Qt::yellow);
    painter.setBrush(Qt::yellow);
    foreach (const Object &object, objects)
    {
        painter.drawEllipse(object.mX - 2, object.mY - 2, 4, 4);
        QString text = QString::number(object.mID);
        int textWidth = QFontMetrics(this->font()).boundingRect(text).width();
        painter.drawText(object.mX - textWidth, object.mY, text);
    }
}

void ObjectCoordinateSystem::drawHeSaiLiderTrueTarget(QPainter &painter, quint8 radarID, ObjectData &objectData)
{
    Objects &objects = objectData.mObjects[ObjectData::ObjectTrueObject];
    painter.setPen(Qt::cyan);
    foreach (const Object &object, objects)
    {
        painter.drawPoint(object.mX, object.mY);
    }
}

bool ObjectCoordinateSystem::within(double x, double y)
{
    return !(x < mObjectCoordinateSettings.mHorizontalMinRange ||
             x > mObjectCoordinateSettings.mHorizontalMaxRange ||
             y < mObjectCoordinateSettings.mVerticalMinRange ||
             y > mObjectCoordinateSettings.mVerticalMaxRange);
}

void ObjectCoordinateSystem::setupUi()
{
    mTopCustomAxis = new CoordinateAxis(CoordinateAxis::TopObjectAxisArea, this);
    mLeftCustomAxis = new CoordinateAxis(CoordinateAxis::LeftObjectAxisArea, this);
    mRightCustomAxis = new CoordinateAxis(CoordinateAxis::RightObjectAxisArea, this);

    setObjectCoordinateSettings(mObjectCoordinateSettings);

//    setMouseTracking(true);
}

QStringList drawShapeNames()
{
    return QStringList() << "Hollow Circle"  << "Solid Circle"  << "Outlined Rectangle"  << "Filled Rectangle";
}

} // namespace ObjectView
} // namespace Views
