﻿/**
 * @file rdp_kf_track.h
 * @brief 
 * <AUTHOR> (s<PERSON><PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef _RDP_KF_TRACK_H_
#define _RDP_KF_TRACK_H_

#ifndef PC_DBG_FW
#include "calterah_constants.h"
#include "vdy_types.h"
#include "rdp_types.h"
#include "rsp_types.h"
#include "rdp_track_struct.h"
#else
#include "app/vehicle/vdy/vdy_interface.h"
#include "hal/rsp/rsp_types.h"
#include "alg/track/rdp_types.h"
#include "alg/track/rdp_track_struct.h"
#endif

//速度解模糊
#define TX_SW_RANGE_FLAG 1024
#ifndef M_PI
#define M_PI (3.1415926535f)
#endif

#ifndef PI
#define PI 3.1415926535f
#endif

#ifndef RAD2ANG
#define RAD2ANG                  57.2957795131
#endif

#ifndef MAX
#define MAX(a, b)       ((a) > (b) ? (a) : (b))
#endif

#ifndef MIN
#define MIN(a, b)       ((a) < (b) ? (a) : (b))
#endif

#ifndef DEG2RAD
/* DEG2RAD = pi/180 */
#define DEG2RAD 0.017453292519943295f
#endif
#ifndef RAD2DEG
/* RAD2DEG = 180/pi */
#define RAD2DEG 57.295779513082323f
#endif

#define MAX_Q_IDX 15
#define MAX_Q_NUM 32

#define DELTA_RANGE_BIN 5 //速度解模糊两次采样的距离差异
#define DELTA_VEL_BIN 3   //速度解模糊两次采样的速度差异
#define DELTA_ANGLE_BIN 5 //速度解模糊两次采样的角度差异


static inline float POWF_I_0(float x)
{
	return 1;
}

static inline float POWF_I_1(float x)
{
	return x;
}

static inline float POWF_I_2(float x)
{
	return x * x;
}

static inline float POWF_I_3(float x)
{
	return x * x * x;
}

static inline float POWF_I_4(float x)
{
	return x * x * x * x;
}

static inline float POWF_I_5(float x)
{
	return x * x * x * x * x;
}

static inline float POWF_I_6(float x)
{
	return x * x * x * x * x * x;
}

#define POWF_I(x, n) POWF_I_##n(x)

#define FENCE_PARAM_COEF 0.8f
#define VEHICLE_MIN_SPEED 0.1f
#define NONE_SIDE_LINE 0
#define WEEK_SIDE_LINE 1
#define STRONG_SIDE_LINE 2

#define acceRMSEx   1.0f         /* acceleration of x, unit: m/s2 */
#define acceRMSEy   3.0f         /* acceleration of y, unit: m/s2 */
#define maxVx       (75/3.6f)    /* max velocity of x, unit: m/s */
#define maxVy       (150/3.6f)   /* max velocity of y, unit: m/s */

#define acceRMSEx2  (acceRMSEx*acceRMSEx)
#define acceRMSEy2  (acceRMSEy*acceRMSEy)
#define maxVx2      (maxVx*maxVx)
#define maxVy2      (maxVy*maxVy)

#define NOISE2_R (0.4f*0.4f)      // m
#define NOISE2_A (2*DEG2RAD*2*DEG2RAD)          // rad


//#define dT 0.05f //s

#define DELTA_RAD_NORM(dRad)   \
                if((dRad)>(M_PI))   \
                    (dRad) = (M_PI*2-dRad); 
typedef float (*Fun_angleTransform)(float, float);

typedef struct
{
    float rdpTrkObjDistX;
    float rdpTrkObjDistY;
    float rdpTrkObjDistZ;
    float rdpTrkObjVrelX;
    float rdpTrkObjVrelY;
    float rdpTrkObjVrelZ;
    float rdpTrkObjArelX;
    float rdpTrkObjArelY;
    float rdpTrkObjArelZ;
    float rdpTrkObjDistStd[9];
    float rdpTrkObjRange;
    float rdpTrkObjVelocity;
    float rdpTrkObjAzimuthAngle;
	float rdpTrkObjElevetionAngle;
    float rdpTrkObjHeadingAngle;
    float rdpTrkObjsnr;
    float rdpTrkObjcenterOffset;
    float rdpTrkObjLength;
    float rdpTrkObjWidth;
    float rdpTrkObjBoxCenterX;
    float rdpTrkObjBoxCenterY;
    float rdpTrkObjBoxLength;
    float rdpTrkObjBoxWidth;
    /**
     * @brief 跟踪目标的属性
     * bit0-动静属性，0-静态，1=动态
     */
    uint16_t rdpTrkObjStatus;
    uint8_t rdpTrkObjHitCnt;
    uint8_t rdpTrkObjMissCnt;
    uint8_t rdpTrkObjReliability;         // 目标存在置信度，0~100, unit: percentage
    uint8_t rdpTrkObjType;
    uint8_t id;
} trkObjectInfo_t;

extern float grdpInstallAzimuthAngle;
extern uint8_t isCPTALFScene;
extern uint8_t fenceCntInBlindArea;
extern float fencePointLatRange;

int32_t RDP_initTrack(uint8_t subFrameNum, float* rangeRateScope);
void RDP_frameCycleManage(float *time, float *storeTime);
void RDP_getDetections(const RSP_DetObjectList_t *pRSP_DetObjectList_t, const VDY_DynamicEstimate_t *pfreezedVehDyncData,
                              RDP_TrkObjectList_t *pRDP_TrkObjectList, VDY_DynamicEstimate_t *pRDP_DynamicEstimate,
                              RSP_DetObjectList_t *pRDP_DetObjectList);
//int32_t RDP_getUserTrackInfo(const VDY_DynamicEstimate_t *pRDP_inVehicleData, RDP_TrkObjectInfo_t *pRDP_TrkObject, int16_t* num, int16_t nearTrackId[4]);
void RDP_runKFTrack(RSP_DetObjectList_t *pRSP_DetObjectList, VDY_DynamicEstimate_t *pRDP_inVehicleData, float time, trk_pkg_t *pRDP_TrackTargets);
void RDP_updateTrackerList(trk_pkg_t *pRDP_TrackTargets, VDY_DynamicEstimate_t *pRDP_inVehicleData, RDP_TrkObjectList_t *pRDP_TrkObjectList, float time);
void setEstVelocity(int type, float velocity);
float getEstVelocity(int type);
void initGlobalVars(void);
uint8_t LowSpeedManInDOWArea(trk_t* trk);
uint8_t HighSpeedCarInRCTAArea(trk_t* trk);
float getSideLineDis(float y, sideLine_pkr_t* pSideLinePkg);
static void fitFence(cdi_pkg_t *pCdiPkg, int weekCnt, int strongCnt, sideLine_pkr_t *pSideLinePkg);
static void filterFence(cdi_pkg_t *pCdiPkg, trk_pkg_t* pRDP_TrackTargets, sideLine_pkr_t *pSideLinePkg);
void cdiProcess(cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_pkg_t* pRDP_TrackTargets);
static void fit_fence_coefficient(cdi_pkg_t* pCdiPkg, int weekCnt, int strongCnt, float weekParam[DEGREE + 1], float strongParam[DEGREE + 1], float RMSE[2]);
void gaussJordanElimination(float augmentedMatrix[][DEGREE + 2], int n);
static void statFence(cdi_pkg_t *pCdiPkg, int *weekCnt, int *strongCnt, sideLine_pkr_t *pSideLinePkg);

#ifndef PC_DBG_FW
int32_t RDP_createTrackerListSemaphore(void);
void RDP_takeTrackerListSemaphore(void);
void RDP_giveTrackerListSemaphore(void);
#endif
uint64_t RDP_getSynTimeStamp(void);

#endif
