﻿/**
 * @file     rdp_data_process.c
 * @brief    This file defines the functions for data process in RDP module.
 *           Process the track objects from the detection objects and provide interfaces to get RDP data.
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2023-01-10
 * 
 * 
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2023-01-10  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

/****************************************************************************
  INCLUDE
 ****************************************************************************/
#include <string.h>
#ifndef PC_DBG_FW
#include "target_proc.h"
#include "rdp_interface.h"
#include "aln_staticEolFun.h"
#include "pcan_protocol_byd.h"
#include "cfg.h"
#include "rdp_kf_track.h"
#include "rdp_kf_init.h"
#include "rdp_kf_config.h"
#include "rdp_clth_radar_lib.h"
#include "rdp_kf_types.h"
#include "rdp_interface.h"
#include "sys_status.h"
#include "rsp_interface.h"
#include "vdy.h"
#include "aln_install_cfg.h"
#else
#include "alg/track/rdp_interface.h"
#include "alg/track/rdp_kf_track.h"
#include "alg/track/rdp_kf_config.h"
#include "alg/track/rdp_kf_init.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "app/app_common/app_pcan_protocol.h"
#include "hal/rsp/rsp_types.h"
#include "alg/vdy/vdy.h"
#include "other/temp.h"
#include "math.h"
#endif

/****************************************************************************
  Global Variables
 ****************************************************************************/
#ifndef PC_DBG_FW
SHARED_MEM(010) static RSP_DetObjectList_t gRDP_privateDetObjectList;   // RDP自己维护的原始数据列表，包含R、V、A
SHARED_MEM(011) static RDP_TrkObjectList_t gRDP_TrkObjectList;          // 跟踪目标列表，只包含Track类型的目标
SHARED_MEM(012) static trk_pkg_t gRDP_TrackTargets;                     // 卡尔曼滤波后的跟踪目标，包含Track和Candi类型
static VDY_DynamicEstimate_t gRDP_DynamicEstimate;                      // 车身动态数据，RDP模块自维护的数据
SHARED_MEM(013) static RDP_TrkFrameInfoGeely2_0 gGeely20ObjFrameInfo;   // BYD协议输出16个目标
#else
static RSP_DetObjectList_t gRDP_privateDetObjectList;   // RDP自己维护的原始数据列表，包含R、V、A
static RDP_TrkObjectList_t gRDP_TrkObjectList;          // 跟踪目标列表，只包含Track类型的目标
static trk_pkg_t gRDP_TrackTargets;                     // 卡尔曼滤波后的跟踪目标，包含Track和Candi类型
static VDY_DynamicEstimate_t gRDP_DynamicEstimate;                      // 车身动态数据，RDP模块自维护的数据
static RDP_TrkFrameInfoGeely2_0 gGeely20ObjFrameInfo;   // BYD协议输出16个目标
#endif
static RDP_DebugInfo_t gRDP_DebugData;

float gRDP_storedFrameTime[STORE_FRAME_TIME_NUM];              //存储过去若干帧帧周期
static RDP_TrkObjectInfo_t gBYDOuputTrkObjList[16];     // BYD协议输出16个目标
static int16_t gBYDOutputVaildNum;
static int16_t gBYDNearTrackId[4];     //in order: 本1, 邻1, 本2, 邻2
static RDP_TARGET16_TICK_T rdp_target16_tick = {0}; ///记录RDP筛选16个目标的时刻

/**
 * @brief 获取RDP冻结16个目标的时刻
 * @return uint32_t 
 */
const RDP_TARGET16_TICK_T* RDP_GetTarget16Tick(void)
{
    return &rdp_target16_tick;
}

const RDP_TrkFrameInfoGeely2_0* RDP_GetGeely20Target(void)
{
    return &gGeely20ObjFrameInfo;
}

void RDP_trkInterpolation(float deltaT)
{

    static float s_curvature = 0;
    const stVehicleStatus *pVdy = getVdyStatus();
    float sinYaw, cosYaw, alpha, yaw, curvature;
	int16_t trkValidNum;
#ifndef PC_DBG_FW
    RDP_TrkObjectInfo_t *bydFrameInfo = RSP_getObjectBufferPointer();
    trkValidNum = RSP_getObjectBufferVaildNum();
#else
    #ifdef ALGORITHM_GEELY
        RDP_TrkFrameInfoGeely2_0* bydFrameInfo = RSP_getGeelyObjectBufferPointer(&trkValidNum);
    #else
        RDP_TrkObjectInfo_t* bydFrameInfo = RDP_getBYDOutputObjList(&trkValidNum);
    #endif
#endif
    if (trkValidNum > 0 && bydFrameInfo[0].trkUpdataFlag)
    {
        s_curvature = pVdy->curvature;
    }
    
    if (pVdy->compSpeed < pVdy->minSpeed)
    {
        sinYaw = 0.f;
        cosYaw = 1.f;
        s_curvature = 0.f;
    }
    else
    {
        curvature = pVdy->yawrate / pVdy->compSpeed;    // 注：yawrate未滤波，车速估计未开放（当前speed==compSpeed）
        alpha = expf(-deltaT * pVdy->compSpeed / pVdy->fastDisCons);
        s_curvature += (1.0f - alpha) * (curvature - s_curvature);
        yaw = s_curvature * pVdy->compSpeed * deltaT;
        sinYaw = sinf(yaw);
        cosYaw = cosf(yaw);
    }
    
    //update
    float x, y, interX, interY;
    for (uint32_t j = 0; j < trkValidNum; j++)
    {
        // 使用相对速度进行插值处理
        y = bydFrameInfo[j].rdpTrkObjDistY + (bydFrameInfo[j].rdpTrkObjRelVy * deltaT);
        x = bydFrameInfo[j].rdpTrkObjDistX + (bydFrameInfo[j].rdpTrkObjRelVx * deltaT);
        
        //补偿由于自车转弯导致的目标横向和纵向位移
        interY = y * cosYaw - x * sinYaw;
        interX = x * cosYaw + y * sinYaw;
        
        // if (interX < 0.01f)
        // {
        //     //防止插值插出负值-插出反向的不使用插值
        //     continue;
        // }
        
        bydFrameInfo[j].rdpTrkObjDistY = interY;
        bydFrameInfo[j].rdpTrkObjDistX = interX; 
        rdp_config_t*config = RDP_getTrackConfigPointer();
        if ((config->installPosition == SENSOR_POSITION_REAR_LEFT) || (config->installPosition == SENSOR_POSITION_REAR_RIGHT))
        {
            if (bydFrameInfo[j].rdpTrkObjDistX <= -99.f) // DBC要求后角限制在100m内
            {
                bydFrameInfo[j].rdpTrkObjDistX = -99.f;
            }
        }
    }
}

/**
 * @brief 设置RDP冻结16个目标的时刻
 * @param tick 
 */
void RDP_SetTarget16Tick(const RSP_DetObjectList_t *RSP_DetObjectListAddr, uint32_t tick)
{
    rdp_target16_tick.tick = tick;
    rdp_target16_tick.counter++;
#ifdef ADVANCE_TARGETS_TIMESTAMP_EN
    rdp_target16_tick.rspTimestamp = RSP_DetObjectListAddr->rspTimestamp;
#endif
}

static rdp_config_t gRDP_config = {
    .installPosition = SENSOR_POSITION_REAR_LEFT,
    .installAngle    = 45,  // unit: degree
    .upsideDown      = 0,   // 0:normal, 1:upsidedown
    .installOffsetX  = 0,   // unit: m
    .installOffsetY  = 0,   // unit: m
};


void RDP_vehicleInfoCfgInit()
{
    gRDP_config.speed = 0;
    gRDP_config.yawRate = 0;
    gRDP_config.speedDiff = 0;
    gRDP_config.yawRateDiff = 0;
}

int32_t RDP_setSubFramesDopplerVelocityScope(uint8_t subFrameNum, float* rangeRateScope)
{
    if(rangeRateScope == NULL)
        return RDP_POINTER_NULL; // NULL pointer
    if(subFrameNum > NUM_SUBFRAME_MAX)
        return RDP_PARAM_OUT_OF_RANGE; // out of range
    gRDP_config.numSubFrame = subFrameNum;
    memcpy(gRDP_config.dopplerVelocityScope, rangeRateScope, subFrameNum*sizeof(float));
    return RDP_SUCCESS;
}

void trk_pkg_init()
{
    memset((void *)&gRDP_TrackTargets, 0, sizeof(trk_pkg_t));

    gEKF_R[0] = FILTER_NOISE_STD_RNG * FILTER_NOISE_STD_RNG;
    gEKF_R[4] = FILTER_NOISE_STD_VEL * FILTER_NOISE_STD_VEL;
    gEKF_R[8] = (FILTER_NOISE_STD_ANG) * (FILTER_NOISE_STD_ANG) * (PI / 180.f) * (PI / 180.f);

    //gEKF_R[0] = binParam[0] * binParam[0] * FILTER_NOISE_STD_RNG * FILTER_NOISE_STD_RNG;
    //gEKF_R[4] = binParam[1] * binParam[1] * FILTER_NOISE_STD_VEL * FILTER_NOISE_STD_VEL;
    //gEKF_R[8] = binParam[2] * binParam[2] * FILTER_NOISE_STD_ANG * FILTER_NOISE_STD_ANG;

    RList[0][0] = gEKF_R[0] * 2;
    RList[0][1] = gEKF_R[4];
    RList[0][2] = gEKF_R[8];

    RList[1][0] = gEKF_R[0];
    RList[1][1] = gEKF_R[4];
    RList[1][2] = gEKF_R[8];

    RList[2][0] = gEKF_R[0];
    RList[2][1] = gEKF_R[4];
    RList[2][2] = gEKF_R[8];
}

void RDP_setInstallAngle(float angle)
{
    gRDP_config.installAngle = angle;
    return;
}

void RDP_setResolutionTestMode(uint8_t mode)
{
    gRDP_config.radarResolutionTestMode = mode;
    return;
}

void RDP_setRadarInstallPosition(uint32_t radarId)
{
    gRDP_config.installPosition = (rdp_sensorPosition_e)radarId;
    return;
}

/* \brief
* Function Name       :    RDP_setRadarInstallOffset
*
* \par
* <b>Description</b>  :    set the x&y offset basing on vehicle coordinate, where x direction is the front of the vehicle, 
     * y direction is the left of the vehicle, origin is the center of the back shaft of the vehicle
*
* @param        [in]       x : x-coordinate, unit: m
* @param        [in]       y : y-coordinate, unit: m
* @return                  none
*/
void RDP_setRadarInstallOffset(float x, float y)
{
    gRDP_config.installOffsetX = x;
    gRDP_config.installOffsetY = y;
}

/**
 * @brief 获得4个雷达到后桥的距离
 * 
 * @param offsetTorearAxleCenterLgt 
 * @param offsetTorearAxleCenterLat 
 * @return int32_t 
 */
int32_t RDP_getMountPosOffsetToRearAxleCenter(float *offsetTorearAxleCenterLgt, float *offsetTorearAxleCenterLat)
{
    int32_t ret = 0;

    switch (CFG_getRadarId())
    {
#if ( defined(VEHICLE_TYPE_BYD_HA5HC_GJ) || defined(VEHICLE_TYPE_BYD_EM2EH_GJ) || defined(VEHICLE_TYPE_BYD_UREC_GJ) || defined(VEHICLE_TYPE_BYD_HTEBY_GJ) || defined(VEHICLE_TYPE_BYD_HTEB_MMT_GJ) )
    case RADAR_ID_REAR_LEFT:   // 4
        *offsetTorearAxleCenterLgt = get_install_message()->rear_center_x_rl; //MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL;
        *offsetTorearAxleCenterLat = get_install_message()->rear_center_y_rl; //MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL;
        break;

    case RADAR_ID_REAR_RIGHT:  // 5
        *offsetTorearAxleCenterLgt = get_install_message()->rear_center_x_rr; //MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR;
        *offsetTorearAxleCenterLat = get_install_message()->rear_center_y_rr; //MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR;
        break;

    case RADAR_ID_FRONT_LEFT: // 6
        *offsetTorearAxleCenterLgt = get_install_message()->rear_center_x_fl; //MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL;
        *offsetTorearAxleCenterLat = get_install_message()->rear_center_y_fl; //MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL;
        break;

    case RADAR_ID_FRONT_RIGHT: // 7
        *offsetTorearAxleCenterLgt = get_install_message()->rear_center_x_fr; //MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR;
        *offsetTorearAxleCenterLat = get_install_message()->rear_center_y_fr; //MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR;
        break;
#else
    case RADAR_ID_REAR_LEFT: // 4
        *offsetTorearAxleCenterLgt = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL;
        *offsetTorearAxleCenterLat = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL;
        break;

    case RADAR_ID_REAR_RIGHT: // 5
        *offsetTorearAxleCenterLgt = MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR;
        *offsetTorearAxleCenterLat = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR;
        break;

    case RADAR_ID_FRONT_LEFT: // 6
        *offsetTorearAxleCenterLgt = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL;
        *offsetTorearAxleCenterLat = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL;
        break;

    case RADAR_ID_FRONT_RIGHT: // 7
        *offsetTorearAxleCenterLgt = MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR;
        *offsetTorearAxleCenterLat = MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR;
        break;
#endif
    default:
        ret = -1;
        break;
    }

    return ret;
}

void RDP_setRadarUpsidedown(uint8_t upsideDown)
{
    gRDP_config.upsideDown = upsideDown;
}

RDP_DebugInfo_t* RDP_getDebugDataPtr()
{
    return &gRDP_DebugData;
}

rdp_config_t* RDP_getTrackConfigPointer(void)
{
    return &gRDP_config;
}

// 外部模块获取gRDP_TrackTargets地址
trk_pkg_t *RDP_getTrackTargetsPointer(void)
{
    return (trk_pkg_t *)&gRDP_TrackTargets;
}

/*
 * @Description Get BYD output object list.
 */
RDP_TrkObjectInfo_t* RDP_getBYDOutputObjList(int16_t *validNum)
{
    *validNum = gBYDOutputVaildNum;

    return &gBYDOuputTrkObjList[0];
}

/*
 * @Description Output 2 nearTrackId
 */
int16_t* RDP_getBYDOutputNearTrackId(void)
{
    return &gBYDNearTrackId[0];
}

#ifndef PC_DBG_FW
void RDP_updateRadarTwoNearObjs(CAN_msg_TwoObj_32D_t *nearTwoObj)
{
    int16_t trkValidNum = 0;
    int16_t *nearTrackId = RDP_getBYDOutputNearTrackId();
    RDP_TrkObjectInfo_t *outputObjList = RDP_getBYDOutputObjList(&trkValidNum);

    /* The first near obj */
    if ((nearTrackId[0] >= 0) && (nearTrackId[0] != TRACK_ID_INVALID))
    {
        nearTwoObj->adjacentLaneTarget_1    = 0x2;
        nearTwoObj->lockTargetAbscissa_1    = (uint8_t)(outputObjList[nearTrackId[0]].rdpTrkObjDistX + 100);
        nearTwoObj->lockTargetOrdinate_1    = (uint8_t)(outputObjList[nearTrackId[0]].rdpTrkObjDistY + 100);
        nearTwoObj->goalLengthRelativeSpd_1 = (uint8_t)(3.6f * outputObjList[nearTrackId[0]].rdpTrkObjVrelY);    //与DBC统一单位
        nearTwoObj->goalLateRelativeSpd_1   = (uint8_t)(3.6f * outputObjList[nearTrackId[0]].rdpTrkObjVrelX);    //与DBC统一单位
        nearTwoObj->targetStateB6_1         = (outputObjList[nearTrackId[0]].rdpTrkObjStatus & 0x40) ? 0x2 : 0x1;   // TRACK_STATUS_REVERSE_BMP
        nearTwoObj->targetStateB1_1         = 0x1;
        nearTwoObj->targetType_1            = outputObjList[nearTrackId[0]].rdpTrkObjType & 0xF;
    }
    else
    {
        nearTwoObj->adjacentLaneTarget_1    = 0x2;
        nearTwoObj->lockTargetAbscissa_1    = 0xFF;
        nearTwoObj->lockTargetOrdinate_1    = 0xFF;
        nearTwoObj->goalLengthRelativeSpd_1 = 0xFF;
        nearTwoObj->goalLateRelativeSpd_1   = 0xFF;
        nearTwoObj->targetStateB6_1         = 0x0;
        nearTwoObj->targetStateB1_1         = 0x0;
        nearTwoObj->targetType_1            = 0x0;
    }

    /* The second near obj */
    if ((nearTrackId[1] >= 0) && (nearTrackId[1] != TRACK_ID_INVALID))
    {
        nearTwoObj->adjacentLaneTarget_2    = (CFG_getRadarId() == 4) ? 0x1 : 0x3;
        nearTwoObj->lockTargetAbscissa_2    = (uint8_t)(outputObjList[nearTrackId[1]].rdpTrkObjDistX + 100);
        nearTwoObj->lockTargetOrdinate_2    = (uint8_t)(outputObjList[nearTrackId[1]].rdpTrkObjDistY + 100);
        nearTwoObj->goalLengthRelativeSpd_2 = (uint8_t)(3.6f * outputObjList[nearTrackId[1]].rdpTrkObjVrelY);    //与DBC统一单位
        nearTwoObj->goalLateRelativeSpd_2   = (uint8_t)(3.6f * outputObjList[nearTrackId[1]].rdpTrkObjVrelX);    //与DBC统一单位
        nearTwoObj->targetStateB6_2         = (outputObjList[nearTrackId[1]].rdpTrkObjStatus & 0x40) ? 0x2 : 0x1;   // TRACK_STATUS_REVERSE_BMP
        nearTwoObj->targetStateB1_2         = 0x1;
        nearTwoObj->targetType_2            = outputObjList[nearTrackId[1]].rdpTrkObjType & 0xF;
    }
    else
    {
        nearTwoObj->adjacentLaneTarget_2    = (CFG_getRadarId() == 4) ? 0x1 : 0x3;
        nearTwoObj->lockTargetAbscissa_2    = 0xFF;
        nearTwoObj->lockTargetOrdinate_2    = 0xFF;
        nearTwoObj->goalLengthRelativeSpd_2 = 0xFF;
        nearTwoObj->goalLateRelativeSpd_2   = 0xFF;
        nearTwoObj->targetStateB6_2         = 0x0;
        nearTwoObj->targetStateB1_2         = 0x0;
        nearTwoObj->targetType_2            = 0x0;
    }
}
#endif

/**
 *  @b Description
 *  @n
 *      Set the track object numbers.
 *
 *  @param[in]  trkObjNum
 *      The trace numbers.
 *
 *  @retval
 *     0   -   Success
 *  @retval
 *     <0  -   Fail
 */
int32_t RDP_setTrkObjectNumForTrkDbgMode(uint16_t trkObjNum)
{
    if (trkObjNum > MAX_NUM_OF_TRACKS)
    {
        return -1;
    }

    gRDP_TrkObjectList.rdpDetObjectNum = trkObjNum;

    return 0;
}

/**
 *  @b Description
 *  @n
 *      Update the track object list information externally
 *
 *  @param[in]  objIndex
 *      The track object index.
 *  @param[in]  ObjInfo
 *      The trace hit data input.
 *
 *  @retval
 *     0   -   Success
 *  @retval
 *     <0  -   Fail
 */
int32_t RDP_setTrkObjectForTrkDbgMode(uint16_t objIndex, RDP_TrkObjectInfo_t *trkObjInfo)
{
    if (objIndex > MAX_NUM_OF_TRACKS)
    {
        return -1;
    }

    memcpy((void *)&gRDP_TrkObjectList.rdpTrkObject[objIndex], (void *)trkObjInfo, sizeof(RDP_TrkObjectInfo_t));

    return 0;
}

const RSP_DetObjectList_t *RDP_getDetObjectListPointer(void)
{
    return &gRDP_privateDetObjectList;
}

/**
 *  @b Description
 *  @n
 *      Get the pointer of track object list.
 *
 *  @retval
 *      RDP_TrkObjectList_t - Pointer of track object list
 */
RDP_TrkObjectList_t *RDP_getTrkObjectListPointer(void)
{
    return &gRDP_TrkObjectList;
}

/**
 * @brief RDP模块主函数
 */
void RDP_runMainFunction(const uint8_t radarId,
                         const RSP_DetObjectList_t *RSP_DetObjectListAddr,
                         const VDY_DynamicEstimate_t *freezedVehDyncDataAddr,
                         const float installAzimuthAngle,
                         float time)
{
    grdpInstallAzimuthAngle = installAzimuthAngle;
    RDP_setInstallAngle(installAzimuthAngle);
    RDP_setRadarInstallPosition(radarId);
    RDP_setResolutionTestMode(RSP_DetObjectListAddr->radarResolutionTestMode);

#ifdef PC_DBG_FW
    // 调试时补偿
    switch (radarId)
    {
    case SENSOR_POSITION_REAR_LEFT:
        RDP_setRadarInstallOffset(-0.65f, 0.73);
        break;
    case SENSOR_POSITION_REAR_RIGHT:
        RDP_setRadarInstallOffset(-0.65f, -0.73f);
        break;
    case SENSOR_POSITION_FRONT_LEFT:
        RDP_setRadarInstallOffset(3.30f, 0.85f);
        break;
    case SENSOR_POSITION_FRONT_RIGHT:
        RDP_setRadarInstallOffset(3.30f, -0.85f);
        break;
    default:
        break;
    }
#endif


#ifndef PC_DBG_FW
    //静态标定服务
    ALN_StaticEolAngleCalcMainFun(RSP_DetObjectListAddr);
#endif

    //帧周期管理
    RDP_frameCycleManage(&time, &gRDP_storedFrameTime[0]);

    //将VDY输出与RSP输出分别拷贝至RDP内部维护的全局变量
    RDP_getDetections(RSP_DetObjectListAddr, freezedVehDyncDataAddr, &gRDP_TrkObjectList, &gRDP_DynamicEstimate, &gRDP_privateDetObjectList);

    //输入航迹列表和帧间隔
    RDP_runKFTrack(&gRDP_privateDetObjectList, &gRDP_DynamicEstimate, time, (trk_pkg_t *)&gRDP_TrackTargets);

#ifndef PC_DBG_FW
    /* Take the semaphore */
    RDP_takeTrackerListSemaphore();
#endif

    RDP_updateTrackerList((trk_pkg_t *)&gRDP_TrackTargets, &gRDP_DynamicEstimate, &gRDP_TrkObjectList, time);

#if CUSTOMER_PROTOCOL == CUSTOMER_PROTOCOL_BYD_16
    // 在此获取16个输出目标以满足BYD协议输出
    RDP_getUserTrackInfo(freezedVehDyncDataAddr, &gBYDOuputTrkObjList[0], &gBYDOutputVaildNum, &gBYDNearTrackId[0]);
    #ifndef PC_DBG_FW
    // 更新16个目标点时间戳
    RDP_SetTarget16Tick(RSP_DetObjectListAddr, SystemTickCount);
    // For debug
    // EMBARC_PRINTF("%s()%d Target16Tick_rspTimestamp %d ms\n", __func__, __LINE__ , RSP_DetObjectListAddr->rspTimestamp);

    // 更新雷达(左后或右后)的邻车道目标
    if (CFG_getRadarId() == 4)
    {
        RDP_updateRadarTwoNearObjs(APP_getLeftRadarTwoNearObjs());
    }
    else if (CFG_getRadarId() == 5)
    {
        RDP_updateRadarTwoNearObjs(APP_getRightRadarTwoNearObjs());
    }

    /* Give the semaphore */
    //RDP_giveTrackerListSemaphore();
    #endif
#elif CUSTOMER_PROTOCOL == CUSTOMER_PROTOCOL_GEELY20_15
    //吉利2.0/3.0输出目标
    RDP_UpdateGeelyObj(&gRDP_TrackTargets, freezedVehDyncDataAddr, &gGeely20ObjFrameInfo, RSP_DetObjectListAddr);
#elif CUSTOMER_PROTOCOL == CUSTOMER_PROTOCOL_GEELY10_4
    //吉利1.0输出目标
    //RDP_UpdateGeeltObj(&gRDP_TrackTargets, freezedVehDyncDataAddr, &gGeely20ObjFrameInfo, RSP_DetObjectListAddr);
#endif

#ifndef PC_DBG_FW
    /* Give the semaphore */
    RDP_giveTrackerListSemaphore();
#endif
}


/* 获取遮挡标志 */
uint8_t RDP_getBlockFlag(void)
{
    return gRDP_TrkObjectList.blockFlag;
}
/* 获取遮挡程度 */
uint8_t RDP_getBlockPercent(void)
{
    return gRDP_TrkObjectList.blockPercent;
}
/* 获取干扰标志 */
uint8_t RDP_getIntFlag(void)
{
    return gRDP_TrkObjectList.interferedFlag;
}
/* 获取干扰程度 */
uint8_t RDP_getIntPercent(void)
{
    return gRDP_TrkObjectList.intPercent;
}

