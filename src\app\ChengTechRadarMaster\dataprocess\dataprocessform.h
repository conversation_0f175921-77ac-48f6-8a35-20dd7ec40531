﻿#ifndef DATAPROCESSFORM_H
#define DATAPROCESSFORM_H

#include <QWidget>

#include "canfiles/ctcandevicefile.h"

#ifndef ALGORITHM_DEBUG_DATA_PROCESS_RAW
namespace Views {
class ViewsManager;
}
#endif

namespace Ui {
class DataProcessForm;
}

class DataProcessForm : public QWidget
{
    Q_OBJECT

public:
#ifndef ALGORITHM_DEBUG_DATA_PROCESS_RAW
    explicit DataProcessForm(Views::ViewsManager *viewsManager = 0, QWidget *parent = nullptr);
#else
    explicit DataProcessForm(QWidget *parent = nullptr);
#endif
    ~DataProcessForm();

signals:
    void nextFrame();
    void calculateFinished(quint8 radarID, const AnalysisData &analysisData);

private slots:
    void currentFrame(int frameNo, int firstFrame, int frame);
    void readFinished();

    void on_pushButtonFiles_clicked();

    void on_pushButtonPauseAndContionue_clicked();

    void on_pushButtonNextFrame_clicked();

    void on_pushButtonStartAndStop_clicked();

private:
    Ui::DataProcessForm *ui;

#ifndef ALGORITHM_DEBUG_DATA_PROCESS_RAW
    Views::ViewsManager *mViewsManager{0};
#endif

    CTCANDeviceFile *mCTCANDeviceFile{0};

    QStringList mFilenames;

    quint64 mPreviousFrameDateTime{0};

    bool mRuning{ false };
    bool mPaused{ false };
};

#endif // DATAPROCESSFORM_H
