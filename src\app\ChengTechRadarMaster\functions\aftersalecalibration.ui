<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AfterSaleCalibration</class>
 <widget class="QDialog" name="AfterSaleCalibration">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>720</width>
    <height>646</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>售后标定</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_5">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="9,0,1">
     <item>
      <widget class="QComboBox" name="comboBoxProtocol">
       <item>
        <property name="text">
         <string>BYD120</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>MRR410</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>BAIC</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>GEELY</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>GEELY 180Pro</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>GEELY 180Pro KX11</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>GEELY 180Pro E245</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>GWM</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>HOZON</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>BAIC_BE12</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxCAN">
       <property name="text">
        <string>CAN通信</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxSaveCanLog">
       <property name="text">
        <string>保存CanLog</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0,6,0">
       <item>
        <widget class="QCheckBox" name="checkBoxRadar4">
         <property name="text">
          <string>雷达 4</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="comboBoxRadar4">
         <item>
          <property name="text">
           <string>CAN 0</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 1</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 2</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 3</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="plainTextEditRadar4"/>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="plainTextEditCanLog4"/>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2" stretch="0,0,6,0">
       <item>
        <widget class="QCheckBox" name="checkBoxRadar5">
         <property name="text">
          <string>雷达 5</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="comboBoxRadar5">
         <item>
          <property name="text">
           <string>CAN 0</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 1</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 2</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 3</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="plainTextEditRadar5"/>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="plainTextEditCanLog5"/>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_3" stretch="0,0,6,0">
       <item>
        <widget class="QCheckBox" name="checkBoxRadar6">
         <property name="text">
          <string>雷达 6</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="comboBoxRadar6">
         <item>
          <property name="text">
           <string>CAN 0</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 1</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 2</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 3</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="plainTextEditRadar6"/>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="plainTextEditCanLog6"/>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_4" stretch="0,0,6,0">
       <item>
        <widget class="QCheckBox" name="checkBoxRadar7">
         <property name="text">
          <string>雷达 7</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="comboBoxRadar7">
         <item>
          <property name="text">
           <string>CAN 0</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 1</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 2</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CAN 3</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="plainTextEditRadar7"/>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="plainTextEditCanLog7"/>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonStartCalibration">
     <property name="text">
      <string>开始校准</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonStopCalibration">
     <property name="text">
      <string>停止校准</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonCalibrationResult">
     <property name="text">
      <string>读取校准结果</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
