﻿#ifndef DAQCALTERAH_H
#define DAQCALTERAH_H

#include "../3rdparty/CAN/VECTOR_BLF/include/binlog.h"

#include "network/networkdata.h"

#include <QObject>
#include <QString>
#include <QFile>
#include <QDataStream>
#include <QByteArray>
#include <QMutex>

namespace Network {
class INetworkDeveice;
}

namespace Devices {
namespace Can {
class IDeviceWorker;
};
};

namespace Core {
class SaveManager;
}

namespace DAQ{

typedef struct RadarCfgParams
{
    unsigned int numOfTcChannel;        // 内部计算获取 一般是2
    unsigned int rngNfft;               // range FFT点数
    unsigned int velNfft;               // velocity FFT点数
    unsigned int adcSampleStart;        // chirp采样的起始时间
    unsigned int adcFreq;               // ADC采样率，单位MSPS，支持20,25,40,50  20MSPS
    unsigned int decFactor;             // ADC的抽取滤波器的系数，取值范围0~16
    unsigned int antiVelambEn;          // chirp延时方式的速度解模糊，Baseband集成的，false-禁能，true-使能 FALSE
                                        // 四个元素依次代表Tx0,Tx1,Tx2,Tx3,每个元素由四个半字节组成，
                                        // 不同位置的半字节为1时表示MIMO时当前位置的Tx在发射过程中的次序
    unsigned int ena;
    unsigned int enb;
    unsigned int rx_time;
    unsigned int ddm;
    unsigned int chirp_delay;

    float adc_sample_start;
    float adc_sample_end;

    QList<unsigned int> txGroups;       // 例如：0x0000,0x0000,0x0001,0x0000
    unsigned int rxMask;                // 默认是4
}RadarCfgParams;

typedef struct __HIL_CONFIG_PARAMS__ {
    uint32_t chirpNmb; //velNfft * numOfTcChannel 根据sensorCfgParams计算 不需要输入 OK
    uint32_t chirpLen; //1600 OK
    uint32_t skipNmb;  //adcSampleStart * adc_freq * dec_factor 必须偶数 根据sensorCfgParams计算 不需要输入 Ok
    uint32_t sampleNmb; //rngNfft sensorCfgParams OK
    quint8 ena; // ?
    quint8 enb; // ?
    quint8 sam_freq;
    quint16 rx_time; // rx_time * 10(us) ?
    quint16 rng_fft;
    quint16 vel_fft;
    quint8 ddm; // ?
    quint8 chirp_delay; // ?

    float adc_sample_start;
    float adc_sample_end;

    float data_len{1024};


    uint32_t packageNum; //一帧分多少帧发送
    uint32_t frameSize; // 一帧数据大小

    //alps pro

    bool isValid;
}HilCfgParams;

enum DAQProtocol{
    Alps = 0,
    AlpsPro,
    DC1000
};

enum DAQCollectType {
    ADC,
    _1DFFT,
    _2DFFT
};

enum DC1000Commands {
    RESET_FPGA_CMD_CODE              = 0x01,
    RESET_AR_DEV_CMD_CODE            = 0x02,
    CONFIG_FPGA_GEN_CMD_CODE         = 0x03,
    CONFIG_EEPROM_CMD_CODE           = 0x04,
    RECORD_START_CMD_CODE            = 0x05,
    RECORD_STOP_CMD_CODE             = 0x06,
    PLAYBACK_START_CMD_CODE          = 0x07,
    PLAYBACK_STOP_CMD_CODE           = 0x08,
    SYSTEM_CONNECT_CMD_CODE          = 0x09,
    SYSTEM_ERROR_CMD_CODE            = 0x0A,
    CONFIG_PACKET_DATA_CMD_CODE      = 0x0B,
    CONFIG_DATA_MODE_AR_DEV_CMD_CODE = 0x0C,
    INIT_FPGA_PLAYBACK_CMD_CODE      = 0x0D,
    READ_FPGA_VERSION_CMD_CODE       = 0x0E,
};

class DAQSaveWorker;

//加特兰-数据采集、回放
class DAQCalterah : public QObject
{
    Q_OBJECT
public:
    enum CollectState {
        Running,
        Stoped,
        Success,
        Failed
    };

    DAQCalterah( Core::SaveManager* saveMgr, Devices::Can::IDeviceWorker* device, QObject *parent=nullptr );
    virtual ~DAQCalterah();

    bool isRunning() const { return mCollectState == Running; }

signals:
    void    serverStateChanged(bool opened);
    void    clientConnected( unsigned long addr, unsigned short port, bool connected);
    void    showMsg( const QString& msg );

    void    collectStateChanged( /*CollectState*/int state );
    void    HILFinished( bool bSuccess );

public slots:
    void    changeProtocol(/*DAQProtocol*/int protocl);
    void    openOrCloseServer( const QString& ip, quint32 port, bool anlyIP);
    void    startCollect( quint8 channel, quint8 collectType, quint64 frameByte, quint32 frameCnt, const QStringList& cfgFiles );
    void    stopCollect();
    void    startHIL( quint8 calterahProtocol, quint8 channel, quint8 collectType, quint64 frameByte, quint32 frameCnt,
                      const QStringList& cfgFiles,
                      const QString& carFile, bool bBlfFormat,
                      const QString& adcFile, bool bZeerFormat );
    void    setHilPauseFramNum( int frameNum );
    void    HILData();//回灌数据
    void    setDevice( Devices::Can::IDeviceWorker* device ){ mDevice = device;};

    void    recvData(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp);//接收并保存数据


private:
    void parseAlpsData(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp);
    void parseDC1000Data(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp);
    void parseDC1000_4096(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp);
    void parseDC1000_4098(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp);

    //加载tcp下发给DCK板配置参数
    bool    loadRadarCfgParam_Alps( /*const QStringList &files*/ );
    bool    loadRadarCfgParam_AlpsPro( /*const QStringList &files*/ );
    bool    loadHILConfigParam();
    //拼装tcp下发给DCK板的配置数据包
    QByteArray    getTcpConfig( bool bHIL/*, DAQCalterahType type, QStringList cfgFiles*/ );
    QByteArray    getDC1000Data(quint16 command);

    bool    sendCollectRequest( quint32 frameCnt );//发送开始采集指令
    bool    sendStopCollectRequest();//发送结束采集指令

    //打开/关闭采集文件
    bool    openSaveFile();
    bool    closeSaveFile();

    bool    sendHILRequest();//发送开始回灌指令

    /** @brief 回灌一个周期大小的ADC数据 */
    bool    HILData_ADC();
    //回灌车身数据
    bool    HILData_Car();
    bool    HILData_Car_Binary();
    bool    HILData_Car_Blf();
    void    finishHIL( bool bsuccess, const QString& msg );

    void    setCfgFiles( const QStringList& files );

private:
    QFile mTcpSendLog;

    float mFrameSize;
    quint64 mFrameByte{2 * 1024 * 1024};
    quint32 mFrameCnt;
    quint64 mTotalByte;
    quint64 mSequenceNumber{0};
    quint64 mPackageCount{0};
    quint64 mByteCount{0};
    quint64 mDataLength{0};
    quint64 mCurrentByte;
    quint32 mCurrentFrame{0};
    quint8 mChannel;

    QByteArray mRecvBuf;
    QByteArray mDC1000Data_4096;

private:
    Devices::Can::IDeviceWorker* mDevice;
    Core::SaveManager* mSaveMgr;
    CollectState mCollectState{Stoped};
    bool mBeginSave;

    //回灌的车身文件
    QFile mHILCarFile;
    QDataStream mHILCarStream;
    HANDLE          mHBLF;
    bool mBlfFormat;
    //回灌的adc文件
    QFile mHILAdcFile;
    QDataStream mHILAdcStream;
    bool mZeerFormat;//标识adc文件是否为泽耳格式

    quint32 mAdcIndex;

    //回灌参数
    QList<HilCfgParams> mHilCfgParams;
    QList<RadarCfgParams> mProfileConfigParams;

    int mHilPauseFrameNum{-1};    //回灌多少帧后暂停

    DAQProtocol mProtocol{Alps};
    DAQCollectType mCollectType{ADC};
    QStringList mCfgFiles;

    DAQSaveWorker *mDAQSaveWorker{0};

    Network::INetworkDeveice *mNetworkDevice{0};
    Network::INetworkDeveice *mNetworkDeviceConfig{0};

    QMutex mMutex;
};

};
#endif // DAQCALTERAH_H
