/**
 * @file rdp_kf_config.h
 * @brief 
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangju<PERSON>     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef MODULE_BB_KF_CONFIG_H_
#define MODULE_BB_KF_CONFIG_H_


//车道宽
#define FCW_MAX_WIDTH        6

#define MIN_RADIUS          20
#define MAX_RADIUS          0x7FFE

enum OBJ_TYPE_E
{
    NORMAL_OBJ,         //普通目标
    CROSS_OBJ,          //横穿目标
    FORWARD_STATIC_OBJ, //正前方车道静止目标
    FENCE_OBJ,          //护栏
    STATIC_OBJ,         //静止目标
    COVER_OBJ,          //井盖
    OVERBRIDGE_OBJ,     //天桥/广告牌等高空目标
    TUNNEL_OBJ,         //隧道放射目标
};

enum RADAR_RES_TEST_MODE_E
{
    TEST_MODE_NORMAL    = 0U,         //普通路测
    TEST_MODE_RESOLUTION= 1U,     //距离分辨率测试
    TEST_MODE_WEAK_OBJ  = 2U,     //远离靠近测试，弱目标【二轮车和行人，检测点不好，需要特殊处理的模式】
};

//#define ALPHA_FILTER                       1
// #define E_KAL_FILTER                       1

#define CDI2TRACK_CNT_THR              3
#define CDI2TRACK_CNT_STATIC_THR       5
#define CDI2TRACK_CNT_MAX_THR           10
#define TRACK2NONE_CNT_THR             5

#define E_KAL_ACC_CDI                      0.2f
#define E_KAL_ACC_TRK                      0.2f
#define E_KAL_ACC_STB                      0.2f

#define FILTER_BOUNDARY_RNG_ON             1
//#define FILTER_BOUNDARY_RNG        4
//#define FILTER_BOUNDARY_VEL_ON             1
        #define FILTER_BOUNDARY_VEL        1
#define FILTER_BOUNDARY_ANG_ON             1
#if 0//ANT_DISTANCE_5 || FURCAL_TX_ANT
	#define FILTER_BOUNDARY_ANG            90
#else
	#define FILTER_BOUNDARY_ANG            18
#endif

//#define FILTER_BOUNDARY_NUM_ON             1
        #define FILTER_BOUNDARY_NUM        10

#define FILTER_RESOLUTION_ANG              1.0f

#if 0//SAMPLE_CNT == 1024
#define STATIC_VEL_DIFF                    10    //静止目标与车速的速度差
#define FILTER_THRESHOLD_RNG               16.0f
#define FILTER_THRESHOLD_VEL               10.0f
#define FILTER_THRESHOLD_ANG               5.0f
#elif 0//SAMPLE_CNT == 512
#define STATIC_VEL_DIFF                    2    //静止目标与车速的速度差
#define FILTER_THRESHOLD_RNG               8.0f
#define FILTER_THRESHOLD_VEL               6.0f
#define FILTER_THRESHOLD_ANG               5.0f
#else
#define STATIC_VEL_DIFF                    2    //静止目标与车速的速度差
#define FILTER_THRESHOLD_RNG               8.0f
#define FILTER_THRESHOLD_VEL               3.0f
#define FILTER_THRESHOLD_ANG               5.0f
#endif

#define FILTER_NOISE_STD_RNG               0.4f
#define FILTER_NOISE_STD_VEL               1.f
#define FILTER_NOISE_STD_ANG               1.5f

#define COHESION_STD_RNG_Y                 5
#define COHESION_STD_RNG_X                 3
//#define COHESION_STD_RNG_Y                 4
//#define COHESION_STD_RNG_X                 3.5
#define COHESION_STD_VEL_Y				   5  //3
#define COHESION_STD_VEL_X				   1
#define COHESION_STD_RNG_A                 20

#define FILTER_NON_ASSOC                   -1

#endif
