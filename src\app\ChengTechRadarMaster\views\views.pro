include(../libs.pri)

QT += core gui widgets printsupport

FORMS += \
    analysisdataview.ui \
    analysisdataviewconfigdialog.ui \
    objectcorrdinatesystemconfigdialog.ui \
    objectview.ui \
    targetmonitor.ui \
    targetsmonitor.ui \
    truesystemview.ui

HEADERS += \
    analysisdataview.h \
    analysismodel.h \
    objectcoordinatesystem.h \
    objectcorrdinatesystemconfigdialog.h \
    objectview.h \
    qcustomplot.h \
    targetmonitor.h \
    truesystemview.h \
    views_global.h \
    viewsicons.h \
    viewsmanager.h \
    widget/coordinateaxis.h

SOURCES += \
    analysisdataview.cpp \
    analysismodel.cpp \
    objectcoordinatesystem.cpp \
    objectcorrdinatesystemconfigdialog.cpp \
    objectview.cpp \
    qcustomplot.cpp \
    targetmonitor.cpp \
    truesystemview.cpp \
    viewsicons.cpp \
    viewsmanager.cpp \
    widget/coordinateaxis.cpp

LIBS += -lutils -lanalysis

RESOURCES += \
    views.qrc
