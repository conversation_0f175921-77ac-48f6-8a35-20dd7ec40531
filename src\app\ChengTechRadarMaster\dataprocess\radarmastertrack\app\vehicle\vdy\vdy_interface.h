﻿/**
 * @file vdy_interface.h
 * @brief VDY模块对外的头文件，包含了对外提供的函数接口
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2022-09-24
 * 
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-09-24 <td>1.0     <td>wangjuhua     <td>初始版本
 * </table>
 */
#ifndef _VDY_INTERFACE_H_
#define _VDY_INTERFACE_H_

/*****************************************************************************
  INCLUDES
*****************************************************************************/
#ifndef PC_DBG_FW
#include "vdy_types.h"
#else
#include "app/vehicle/vdy/vdy_types.h"
#endif

/** @defgroup VDY Vehicle Dynamics
  * @{
  */ 

/*****************************************************************************
                                    FUNCTIONS
*****************************************************************************/
/**
 * @brief 车辆下电3分钟后才能关闭DOW，这里满足条件设置状态，上电初始为1，暂时把数据放到这里。
 *
 *
 * @param [in] None
 * @param [out] None
 * @return igPowerDownTimeOfDOW 获得时间是否到3分钟
 */
void VDY_setCloseDOWOfIGPDStatu(uint8_t dowSta);
uint8_t VDY_getCloseDOWOfIGPDStatu(void);

/**
 * @brief VDY模块向外部模块提供的获取车辆动态数据冻结后的接口。
 *
 * 其他模块调用此函数后，会获取到车辆动态数据冻结后的参数，包括 @c VDY_DynamicEstimate_t 结构体中的参数。
 *
 * @param [in] None
 * @param [out] None
 * @return VDY_DynamicEstimate_t* 车身数据冻结后的数据存储地址
 */
VDY_DynamicEstimate_t *VDY_getFreezedVehDyncDataPointer();

/**
 * @brief VDY模块向外部模块提供的车辆状态参数，参数的类型见 @see VDY_StaticState_t,
 * 
 * @return VDY_StaticState_t* 车身状态参数的指针，不可通过指针更改内部的值
 * 
 * @param [in] None
 * @param [out] None
 * 
 * @todo 返回值需要加上写权限限制
 */
VDY_StaticState_t *VDY_getVehStaticDataPointer();

/**
 * @brief 获取驾驶员操作开关的接口
 * 
 * @details 功能开关包括各个功能开关、转向灯开关、四门开关
 * 
 * @param [in] None
 * @param [out] None
 * 
 * @return VDY_vehicleFuncSwt_t* 开关数据指针，调用者不可通过指针对数据进行更改
 */
VDY_vehicleFuncSwt_t *VDY_getVehFuncSwtPointer();

/**
 * @brief 获取冻结后的车辆档位。
 * 
 * @details 档位数据较为常用，使用此接口可以获得车辆的档位信息。
 * 
 * @param [in] None
 * @param [out] None
 * 
 * @return uint8_t 车辆的行驶方向，档位的详细信息见 @see VDY_DynamicEstimate_t 中档位的定义。
 * @retval 0x0:Forward
 * @retval 0x1:Backward
 */
uint8_t VDY_getFreezedVehDrvDir();

/**
 * @brief 获取冻结后的车辆速度
 * 
 * @details 返回模块内部车辆动态参数中的车速
 * 
 * @param [in] None
 * @param [out] None
 * 
 * @return float 车速，单位为mps
 */
float VDY_getFreezedVehSpdmps();

/**
 * @brief 获取车辆里程计信息
 * 
 * @param [in] None
 * @param [out] None
 * 
 * @return uint32_t 里程数据，单位为km
 */
uint32_t VDY_getOdometer();

/**
 * @brief VDY模块计算的主入口，其他模块可以调用此接口将当前的车身动态信息固定下来。
 * 
 * @details 由于数据处理模块使用了车身的动态数据，因此需要在读取完Baseband内存中的
 * 目标信息后调用此函数，将车身数据冻结，后续的计算都使用冻结后的车身动态数据.
 * 
 * @return float 计算的耗时
 */
float VDY_freezeVehDynData();

/**
 * @brief 获取VDY模块中底盘（Chassis）数据的接口
 * 
 * @param [in] None
 * 
 * @param [out] None
 * 
 * @return const VDY_ChassisInfo_t* 底盘数据的指针
 */
const VDY_ChassisInfo_t* VDY_getChassisData();

/**
 * @brief 设置底盘数据的接口
 * 
 * @param [in] None
 * 
 * @param [out] None
 * 
 * @return uint32_t 返回的状态
 */
uint32_t VDY_setChassisData();


/**
 * @brief 初始化vdy中的全局变量
 * @return int32_t 0-正常，其他-非正常
 */
int32_t VDY_initViarable();

/**
 * @brief 设置功能开关的状态

 * @param [in] switchType 开关的类型,0-主开关（Main Switch）\
 *                                  1-BSD功能开关（BSD Switch）
 *                                  2-LCA功能开关（LCA Switch）
 *                                  3-DOW功能开关（DOW Switch）
 *                                  4-RCWSwtReq
 *                                  5-RCTASwtReq
 *                                  6-RCTABrkSwtReq
 *                                  7-FCTASwtReq
 *                                  8-FCTABrkSwtReq
 * @param [in] switchState 开关状态0-关，1-开
 * @return int32_t 返回状态，0-正常，其他-非正常
 */
int32_t VDY_setFuncSwitch(uint8_t switchType, uint8_t switchState);

/**
  * @}
  */ 

#endif

