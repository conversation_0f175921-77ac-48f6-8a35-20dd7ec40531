/*
 * @Author: m<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-01 08:30:45
 * @LastEditors: <PERSON>
 * @LastEditTime: 2025-03-21 18:00:09
 * @FilePath: \ctmrr120_platform\calterah\common\custom\aln\aln_autoEolFun.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */


#include <string.h>
#include <math.h>
#include "aln_autoEolFun.h"
#ifndef PC_DBG_FW
#include "arc_timer.h"
#include "cfg.h"
#include "rdp_interface.h"
#include "rdp_clth_radar_lib.h"
#include "app_common.h"
#include "sys_status.h"
#include "rdp/track/alignment/aln_type.h"
#include "rdp/track/alignment/alignment.h"
#include "rdp/track/alignment/aln_dynamicEolFun.h"
#include "rdp/track/alignment/aln_autoEolFun.h"
#include "rdp/track/alignment/aln_install_cfg.h"
#include "app_diag.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/generalalg/adas_manager.h"
#else
#include "alg/track/rdp_clth_radar_lib.h"
#include "alg/track/rdp_interface.h"
#include "app/aln/alignment.h"
#include "app/apar/apar_manager.h"
#include "app/aln/ALN_DynamicEolFun.h"
#include "app/aln/aln_autoEolFun.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/generalalg/adas_manager.h"
#include "other/temp.h"
#endif

#if ALN_CAN_DBG_PRINT == 1
//#include "app_diag.h"
//#include "can_port.h"
#endif

#define ALL_ANGLE_CNT                   (101) // 0.2 度一个步进，然后从当前设置的安装角度分开两边进行处理 ,z注意，只能是奇数
#define DBAA_ANFLE_RANGE		        (10) //角度范围
#define DBAA_ANFLE_BIN			        (0.2) //角度步进
static float gDbaaSaureBuffer[ALL_ANGLE_CNT];
static float gDbaaSaureBufferOneFrame[ALL_ANGLE_CNT];

#define MAX_NUMBER_FOR_CALC_OFFSET      (3000) // 15*20*10  = 3000 点数据 20帧1s 一共10s

#define PRRINT_ALN_DBG_OUTPUT           (0)

static uint32_t gDbaaAllObjCnt = 0;
//static uint32_t gDbaaHorizontalObjCnt = 0;

//static float gDbaaSST = 0;
//static float gDbaaSSTPre = 0;

#define MAX_STRAIGHT_ROAD_CNT           (10)    //恢复0.5后才算
static uint16_t gStraightRoadCnt = 0;  //恢复直道的缓冲

static float vBuf[ALL_ANGLE_CNT];
static float agvBuf[ALL_ANGLE_CNT];
#define MAX_OBJ_CNT_FOR_CALC            (25) //一帧的最大计算点数
static float objV[ALL_ANGLE_CNT][MAX_OBJ_CNT_FOR_CALC];
static ALN_CalibType_t ALN_Mode = AUTO_ALN;  // AUTO_ALN 自标定，1 服务标定，2：最后一次完成服务标定，会恢复到自标定，但是需要清除一下数据。 默认一直进行自标定，进入服务标定后需要清除对应的数据
#define DEFAULT_FIX_ANGLE               (45)    //默认安装标定角度

#define ALN_AUTO_MAX_SET_OFFSET_ANGLE   (5.0f)

#define ALN_SERVICE_AUTO_EOL_TIMEOUT    (6000)  // 次 1000*60*5/50  5分钟
// #define ALN_SERVICE_AUTO_EOL_TIMEOUT 10000  // 1000*10  10s

static CT_AUTO_EOL_INFO_t mCtAutoEolInfo = {.one_succeed = CALC_UNCAL_END};
static uint8_t ALN_routineSt = ROUTINE_ST_STOP;

static int gServiceAngleCalcState = 0;
//static float gCalcInstallAngle = 0;
static float horizontalDeviationAngle = 0.0f;     // 水平安装角度偏差

uint8_t ALN_AutoEolFun_Flag = 0;
//static uint8_t ANGLE_MAX_MIN_OUT = 0;
ALN_AUTO_EOL_T aln_self_eol = {
    .state = ALN_AUTO_EOL_START,     // 假设这是你枚举中的某一项
    .last_aln_status = ALN_AUTO_EOL_OK,
    .last_aln_result = ALN_AUTO_INACTIVE,
    .last_aln_tick = 0U,
    .err_cnt = 0U
};

static uint8_t ginitAutoStatus = 0U;

static CT_AUTO_PARA_t mCtAutoPara=
{
    .fixAngleOffset = DEFAULT_FIX_ANGLE,
};

static CT_AutoEOL_ResultInfo_t resultAutoEolResult = {.auto_angle = DEFAULT_FIX_ANGLE}; // ！！！赋值不能放在自标定init里面

static ADAS_SelfCalibrationCtrl autoEOLCtrl = // 单次上电周期自标定控制
{
    .travlledDistance = 0U,
    .calibAngles[0] = 0.f,
    .calibAngles[1] = 0.f,
    .calibAngles[2] = 0.f,
    .calibAngles[3] = 0.f,
    .calibAngles[4] = 0.f,
    .calibAngles[CALIB_RUN_MAX] = 0.f,
    .singleAngleMax = 0.f,
    .singleAngleMin = 999.f,
    .storageCaliInfo.storageCaliStatus = -1,   // 编译阶段赋值
    .runAllCount = 0U,
    .calibrationDone = false,
    .autoEolSuccesFalg = 0,
    .calibSuccesAngles = 0.f,
    .singletakeTime = 0U,
    .totalTakeTime = 0U,
    .caliStatus = -1
};

/**
 * 函数声明
 */
static void ALN_AutoEolCtrlStatusChange(int gServiceAngleCalcState, const ADAS_TimeClase_t *pTimeClass);
static void ADAS_clearAutoEolCtrlStatus(void);

static void ALN_initVar()
{
    // EMBARC_PRINTF("%s >> 1 \n", __FUNCTION__);
    gDbaaAllObjCnt = 0;
    //gDbaaSSTPre = 0;
    //gDbaaSST = 0;
    memset((void *)gDbaaSaureBuffer, 0, sizeof(gDbaaSaureBuffer));
    memset((void *)gDbaaSaureBufferOneFrame, 0, sizeof(gDbaaSaureBufferOneFrame));
}

/**
 * @brief 获取ALN 动态标定类型
 * @return ALN_CalibType_t AUTO_ALN：自动标定
 *                          SERVICE_DIAG_ALN 服务标定
 */
static ALN_CalibType_t ALN_GetAutoCalibType(void)
{
    return ALN_Mode;
}

/**
 * @brief 设置ALN 动态标定类型
 * @param type              AUTO_ALN：自动标定
 *                          SERVICE_DIAG_ALN 服务标定
 */
static void ALN_SetAutoCalibType(ALN_CalibType_t type)
{
    /// 自标定与动态标定切换中，需要清除数据
    if (ALN_Mode != type)
    {
        ALN_Mode = type;
        // EMBARC_PRINTF("%s >> 1 \n", __FUNCTION__);
        ALN_initVar();
    }
}

/**
 * @brief 道路场景检测
 * @param cdi_pkg 
 * @param trk_pkg 
 * @return int 0：可以正常进行标定
 *              -1：点数不满足
 */
#if 0
static int ALN_AutoEol_RoadSceneCheckForCalcAngle(const cdi_pkg_t *cdi_pkg)
{
    int ret = -1;
    int cdi_idx = 0;
    // int flagValue = 0;
    int useCnt = 0;
    int moveCnt = 0;
    rdp_config_t* config = RDP_getTrackConfigPointer();
    for (cdi_idx = 0; cdi_idx < cdi_pkg->number; cdi_idx++) // 判断场景内所有的点数量
    {
        if (cdi_pkg->cdi[cdi_idx].mea_z[1] > 5       // 筛选大于5m
            && cdi_pkg->cdi[cdi_idx].mea_z[1] < 40   // 筛选小于40m内的
            && fabsf(cdi_pkg->cdi[cdi_idx].x) < 15.0 //
            && fabsf(cdi_pkg->cdi[cdi_idx].mea_z[3] - config->installAngle) < 45)
        {
            // flagValue = 1;
            // 需要为静态点
            if ((cdi_pkg->cdi[cdi_idx].status & POINT_STATUS_DYNAMIC_BMP) == 0) /*fabsf(gVehicleInfo.Speed - cdi_pkg->cdi[cdi_idx].vy) < 1.5*/ //
            {
                useCnt++;
            }
            else
            {
                moveCnt++;
            }
        }
    }
    //在判断距离内有5个运动点，场景不满足
    if(moveCnt >= 5)
    {
        ret = -1;
    }
    else if(useCnt >= 8)
    {
        ret = 0;
    }
    return ret;
}
#endif

/**
 * @brief 车相关的条件判断
 * @param freezedVehDyncDataAddr 
 * @return int 根据状态返回对应的值
 * 
 */

#define ALN_VDY_CONDITION_MIN_SPEED             (30.0f / 3.6f)
#define ALN_VDY_CONDITION_MAX_SPEED             (150.0f / 3.6f)
#define ALN_VDY_CONDITION_MAX_ACC               (0.5F)
#define ALN_VDY_CONDITION_MAX_YAWRATE           (0.65f)

int ALN_AutoEolConditionCheck(const VDY_DynamicEstimate_t *freezedVehDyncDataAddr)
{
    //车速限制 // 30 <= speed <= 150
    if(fabsf(freezedVehDyncDataAddr->vdySpeedInmps) < ALN_VDY_CONDITION_MIN_SPEED)
    {
        // EMBARC_PRINTF("%s >> 1 MIN_SPEED=%0.2f\n", __FUNCTION__, fabsf(freezedVehDyncDataAddr->vdySpeedInmps));
        return CALC_FAILED_SPEED_OUT;
    }
    else if(fabsf(freezedVehDyncDataAddr->vdySpeedInmps) > ALN_VDY_CONDITION_MAX_SPEED)
    {
        // EMBARC_PRINTF("%s >> 2 MAX_SPEED=%0.2f\n", __FUNCTION__, fabsf(freezedVehDyncDataAddr->vdySpeedInmps));
        return CALC_FAILED_SPEED_OUT;
    }

    // 加速度限制 // a <= 0.5
    if(fabsf(freezedVehDyncDataAddr->vdyAccelLong) > ALN_VDY_CONDITION_MAX_ACC)
    {
        // EMBARC_PRINTF("%s >> 3 MAX_ACC=%0.4f\n", __FUNCTION__, fabsf(freezedVehDyncDataAddr->vdyAccelLong));
        return CALC_FAILED_ACC;
    }

    //偏航率限制
    if(fabsf(freezedVehDyncDataAddr->vdyYawRate) > ALN_VDY_CONDITION_MAX_YAWRATE)
    {
        // EMBARC_PRINTF("%s >> 4 MAX_YAWRATE=%0.4f\n", __FUNCTION__, fabsf(freezedVehDyncDataAddr->vdyYawRate));
        return CALC_FAILED_YAWRATE;
    }
    return 0;
}

/**
 * @brief 
 * @return int 0:还没有超时 ， 其他：已经超时
 */
#if 0
static int ALN_AutoEol_checkTimeout(const ADAS_TimeClase_t *pTimeClass)
{
    // uint32_t curRtcTime = RTC_TO_MS(timer_rtc_count());
    uint32_t curRtcTime = pTimeClass->adasTimeCnt;
    uint32_t timeout = 0;
    if(curRtcTime >= mCtAutoEolInfo.start_time)
    {
        timeout = curRtcTime - mCtAutoEolInfo.start_time;
    }
    else
    {
        timeout = (0xFFFFFFFF - mCtAutoEolInfo.start_time) + curRtcTime;
    }

    if(timeout > ALN_SERVICE_AUTO_EOL_TIMEOUT)
    {
        return -1;
    }
    else
    {
        return 0;
    }
}
#endif

/**
 * @brief 自标定主程序
 *  这段代码的核心思想是利用多个目标的速度数据来找到一个最佳的修正角度，使得在该角度下，速度的方差
 *  最小，从而使传感器的数据更准确。
 *
 * @param cdi_pkg 原始目标数据指针
 * @param freezedVehDyncDataAddr 车身动态数据指针
 * @return int
 */
static int ALN_AutoEolAngleCalcProcess(const cdi_pkg_t *cdi_pkg, const VDY_DynamicEstimate_t *freezedVehDyncDataAddr, const ADAS_TimeClase_t *pTimeClass)
{
    uint8_t objCnt= 0;
    int i = 0, k = 0;
    int cdi_idx, flagValue = 0, minLeastIdx = 0;
    float a, aFit;
    rdp_config_t* config = RDP_getTrackConfigPointer();
    int ret = 0;

    //EMBARC_PRINTF("%s >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\n", __FUNCTION__);

    //EMBARC_PRINTF("%s >> 1 vdyDriveDirection=%d\n", __FUNCTION__, freezedVehDyncDataAddr->vdyDriveDirection);
    //前行的情况下处理
    if (freezedVehDyncDataAddr->vdyDriveDirection == 1) // 0前行，1倒车
    {
        return -1;
    } 
    mCtAutoEolInfo.run_step |= 0x01;

    //EMBARC_PRINTF("%s >> 2 ALN_routineSt=%d\n", __FUNCTION__, ALN_routineSt);
    //是否已经进入服务标定
    if (ALN_routineSt == ROUTINE_ST_STOP) // ALN_routineSt此变量31服务函数Service31Handle_AutoEol()会查询
    {
        return CALC_UNCAL_END;
    }

    //EMBARC_PRINTF("%s >> 3 ret=%d\n", __FUNCTION__, ret);
    mCtAutoEolInfo.run_step |= 0x02;
    ret = ALN_AutoEolConditionCheck(freezedVehDyncDataAddr);
    if(0 != ret)
    {
        gServiceAngleCalcState = ret;
        return -1;
    }

    //EMBARC_PRINTF(">> 4 checkTimeout=%d\n", ALN_AutoEol_checkTimeout(pTimeClass));
    mCtAutoEolInfo.run_step |= 0x04;
    //时间是否超时,自标定暂时不用
    //if (0 != ALN_AutoEol_checkTimeout(pTimeClass)) // 最大5分钟
    //{
    //    gServiceAngleCalcState = CALC_FAILED_TIMEOUT;
    //    ALN_routineSt = ROUTINE_ST_STOP;
    //    //更新状态
    //    return -1;
    //}

    //EMBARC_PRINTF(">> 5 fabsf(gStraightRoadCnt)=%d\n",(fabsf(freezedVehDyncDataAddr->vdyCurveRadius) < 2000));
    mCtAutoEolInfo.run_step |= 0x08;
    //从转弯恢复到直道，增加一点缓冲时间
    if (fabsf(freezedVehDyncDataAddr->vdyCurveRadius) < 2000)
    {
        gStraightRoadCnt = 0;
        return -1;
    }
    else
    {
        gStraightRoadCnt++;
    }

    // EMBARC_PRINTF("%s >> 6 gStraightRoadCnt=%d\n", __FUNCTION__, gStraightRoadCnt);
    if(gStraightRoadCnt < MAX_STRAIGHT_ROAD_CNT)
    {
        return -1;
    }
    gStraightRoadCnt = MAX_STRAIGHT_ROAD_CNT;

    // EMBARC_PRINTF("%s >> 7 cdi_pkg->number=%d\n", __FUNCTION__, cdi_pkg->number);
    mCtAutoEolInfo.run_step |= 0x10;
    if(cdi_pkg->number < 15) // 0
    {
        return -1;
    }
    // EMBARC_PRINTF("%s >> 8 RoadSceneCheck=%d\n", __FUNCTION__, (0 != ALN_AutoEol_RoadSceneCheckForCalcAngle(cdi_pkg)));
    mCtAutoEolInfo.run_step |= 0x20;
    //if (0 != ALN_AutoEol_RoadSceneCheckForCalcAngle(cdi_pkg)) // 道路场景检测
    //{
    //    return -1;
    //}
     
    mCtAutoEolInfo.run_step |= 0x40;
    // if(ALN_Mode == 0)
    {
        // ALN_initVar(); // 自标定要清除这里
    }
    // ALN_Mode = 1;

    for(int i = 0 ; i < ALL_ANGLE_CNT ; i++)
    {
        vBuf[i] = 0;
    }
    for (cdi_idx = 0; cdi_idx < cdi_pkg->number; cdi_idx++)
    {
        if (cdi_pkg->cdi[cdi_idx].mea_z[1] < 0.1) // range要大于0.1 
        {
            // EMBARC_PRINTF("di_pkg->cdi[cdi_idx].mea_z[1] < 0.1\n");
            continue;
        }
        flagValue = 0;
        a = cdi_pkg->cdi[cdi_idx].mea_z[3] - config->installAngle;

        if ((cdi_pkg->cdi[cdi_idx].status & POINT_STATUS_DYNAMIC_BMP) == 0                          // 需要为静态点 /*fabsf(gVehicleInfo.Speed - cdi_pkg->cdi[cdi_idx].vy) < 1.5*/                                                                                        //
            && fabsf(cdi_pkg->cdi[cdi_idx].mea_z[2] / freezedVehDyncDataAddr->vdySpeedInmps) > 0.7  // time??
            && fabsf(cdi_pkg->cdi[cdi_idx].mea_z[2] / freezedVehDyncDataAddr->vdySpeedInmps) < 1.05 //
            && cdi_pkg->cdi[cdi_idx].mea_z[1] > 5                                                   // 筛选大于5m
            && cdi_pkg->cdi[cdi_idx].mea_z[1] < 40                                                  // 筛选小于40m内的
            && fabsf(cdi_pkg->cdi[cdi_idx].x) < 15.0 && fabsf(a) < 45)
        {
            flagValue = 1;
        }
        if (flagValue == 0 )
        {
            continue;
        }

        // 不同速度误差的情况下的累计方差
        // 在不同角度上做累计方差,取最小一个
        for (i = 0; i < ALL_ANGLE_CNT; i++)
        {
            aFit = -DBAA_ANFLE_RANGE + DBAA_ANFLE_BIN * i; 
            // 每个点的速度  转换为绝对速度
            objV[i][objCnt] = cdi_pkg->cdi[cdi_idx].mea_z[2] / cosf((a + mCtAutoPara.fixAngleOffset + aFit) / 180 * PI);
            vBuf[i] += objV[i][objCnt];
        }
        objCnt++;
        gDbaaAllObjCnt++;

        if (objCnt >= MAX_OBJ_CNT_FOR_CALC)
        {
            break;
        }
    }
    mCtAutoEolInfo.run_step |= 0x80;
    // EMBARC_PRINTF("%s >> 9 objCnt=%d, gDbaaAllObjCnt=%d\n", __FUNCTION__, objCnt, gDbaaAllObjCnt);
#ifdef PC_DBG_FW
    // EMBARC_PRINTF("objCnt=%d ,gDbaaAllObjCnt=%d\n", objCnt,gDbaaAllObjCnt);
#endif
    if(objCnt > 0)
    {
         for(i = 0; i < ALL_ANGLE_CNT; i++)
        {
            gDbaaSaureBufferOneFrame[i] = 0.0f;
            agvBuf[i] = vBuf[i] / objCnt; // 
        }
        //EMBARC_PRINTF("agvBuf[i] = %f , objCnt = %d\r\n" , agvBuf[0] , objCnt );

        for(i = 0; i < ALL_ANGLE_CNT; i++)
        {
            for(k = 0 ; k < objCnt ; k++)
            {
                // 内层for遍历结束之后，在i角度上所有的objCnt点的方差和，
                gDbaaSaureBufferOneFrame[i] += (objV[i][k] - agvBuf[i])*(objV[i][k] - agvBuf[i]);
            }
            gDbaaSaureBufferOneFrame[i] = gDbaaSaureBufferOneFrame[i] / objCnt; // objCnt个点在i角度分量上的方差值
        }
        //判断最小是否满足要求
        float minDbaaS = gDbaaSaureBufferOneFrame[0];
        for(i = 1; i < ALL_ANGLE_CNT; i++)
        {
            if(minDbaaS > gDbaaSaureBufferOneFrame[i])
            {
                minDbaaS = gDbaaSaureBufferOneFrame[i]; // 找到在i角度分量方差值最小的值
            }
        }
        // EMBARC_PRINTF("%s >> 10 minDbaaS=%0.4f\n", __FUNCTION__, minDbaaS);

        mCtAutoEolInfo.run_step |= 0x100;
        //这个值可能不一定对，初步统计出来的
        if (minDbaaS > 0.055f) // 在i角度分量最小的方差值要<=0.055
        {
            //认为随机性大，本次校准循环不用
            gDbaaAllObjCnt = gDbaaAllObjCnt - objCnt;
            // EMBARC_PRINTF("###################### minDbaaS=%0.4f,objCnt=%d,gDbaaAllObjCnt=%d\r\n", minDbaaS, objCnt, gDbaaAllObjCnt);
            return -1;
        }

        mCtAutoEolInfo.run_step |= 0x200;
        for(int i = 0; i < ALL_ANGLE_CNT; i++)
        {
            gDbaaSaureBuffer[i] += gDbaaSaureBufferOneFrame[i];
        }
    }
    // EMBARC_PRINTF("%s >> 11 \n", __FUNCTION__);

    /**
     * @brief 多帧数据处理完成 
     * 
     */
    mCtAutoEolInfo.run_step |= 0x400;
    mCtAutoEolInfo.obj_cnt = gDbaaAllObjCnt;
    if(gDbaaAllObjCnt >= MAX_NUMBER_FOR_CALC_OFFSET)
    {
        // EMBARC_PRINTF("%s >> 11-12 gDbaaAllObjCnt=%d \n", __FUNCTION__, gDbaaAllObjCnt);
        gDbaaAllObjCnt = 0;
        mCtAutoEolInfo.run_per = 100;
        //查找最小方差时所对应的角度分量i
        for (i = 0; i < ALL_ANGLE_CNT; i++)
        {
            minLeastIdx = gDbaaSaureBuffer[i] < gDbaaSaureBuffer[minLeastIdx] ? i : minLeastIdx;
        }
        // EMBARC_PRINTF(" minLeastIdx=%d\n",minLeastIdx);

        ALN_routineSt = ROUTINE_ST_STOP;
        // EMBARC_PRINTF("%s >> 12 ALN_routineSt=%d\n", __FUNCTION__, ALN_routineSt);

        //计算补偿角度， 获得最有可能的目标角度和当前雷达法线角度的偏差值
        horizontalDeviationAngle = -DBAA_ANFLE_RANGE + DBAA_ANFLE_BIN * minLeastIdx;
        // EMBARC_PRINTF("%s >> 13 horizontalDeviationAngle=%0.4f\n", __FUNCTION__, horizontalDeviationAngle);

        if(horizontalDeviationAngle > ALN_AUTO_MAX_SET_OFFSET_ANGLE)
        {
            gServiceAngleCalcState = CALC_FAILED_ANGLE_MAX_OUT;
        }
        else if(horizontalDeviationAngle < (-ALN_AUTO_MAX_SET_OFFSET_ANGLE))
        {
            gServiceAngleCalcState = CALC_FAILED_ANGLE_MIN_OUT;
        }
        else
        {
            mCtAutoEolInfo.new_fix_angle = mCtAutoPara.fixAngleOffset + horizontalDeviationAngle;
            gServiceAngleCalcState = CALC_SUCCESS_END;
        } 
        // EMBARC_PRINTF("%s >> 14 gServiceAngleCalcState=%d\n", __FUNCTION__, gServiceAngleCalcState);

        // 保存每次运行结果
        mCtAutoEolInfo.new_auto_angle = mCtAutoPara.fixAngleOffset + horizontalDeviationAngle;
        mCtAutoEolInfo.one_succeed = gServiceAngleCalcState;

        //gDbaaSSTPre = 0;
        //gDbaaSST = 0;
        memset((void *)gDbaaSaureBuffer, 0, sizeof(gDbaaSaureBuffer));
    }
    else
    {
        //统计百分数
        mCtAutoEolInfo.run_per = gDbaaAllObjCnt * 100 / MAX_NUMBER_FOR_CALC_OFFSET;
    }
    // EMBARC_PRINTF("%s >> 15\n", __FUNCTION__);

    return 0;

}

static void ALN_initParaForAuto(const ADAS_TimeClase_t *pTimeClass)
{
    switch (CFG_getRadarId())
    {
#if ( defined(VEHICLE_TYPE_BYD_HA5) || defined(VEHICLE_TYPE_BYD_EM2) || defined(VEHICLE_TYPE_BYD_UR) )
    case 4:
    {
        mCtAutoPara.fixAngleOffset = get_install_message()->aln_execpt_angle_rcr; //ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 5:
    {
        mCtAutoPara.fixAngleOffset = get_install_message()->aln_execpt_angle_rcr; //ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 6:
    {
        mCtAutoPara.fixAngleOffset = get_install_message()->aln_execpt_angle_fcr; //ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    case 7:
    {
        mCtAutoPara.fixAngleOffset = get_install_message()->aln_execpt_angle_fcr; //ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    default:
    {
        mCtAutoPara.fixAngleOffset = get_install_message()->aln_execpt_angle_rcr; //ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
#else
    case 4:
    {
        mCtAutoPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 5:
    {
        mCtAutoPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 6:
    {
        mCtAutoPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    case 7:
    {
        mCtAutoPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    default:
    {
        mCtAutoPara.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
#endif
    }
    gServiceAngleCalcState = CALC_START;
    mCtAutoEolInfo.routine_status = ROUTINE_STATUS_ACTIVE;
    mCtAutoEolInfo.hdev_angle = 0;
    mCtAutoEolInfo.routine_result = ROUTINE_RESULT_NO_RESULT;
    mCtAutoEolInfo.run_status = CALC_UNCAL_END;
    mCtAutoEolInfo.run_counter = 0;
    mCtAutoEolInfo.obj_cnt = 0xffff;
    mCtAutoEolInfo.run_step = 0;
    mCtAutoEolInfo.is_save_to_nvm = false;
    mCtAutoEolInfo.run_per = 0;
    // mCtAutoEolInfo.start_time = RTC_TO_MS(timer_rtc_count());
    mCtAutoEolInfo.start_time = pTimeClass->adasTimeCnt;
    mCtAutoEolInfo.new_fix_angle = 0;
    mCtAutoEolInfo.previous_fix_angle = CFG_getRadarInstallAngle();
    if (autoEOLCtrl.totalTakeTime == 0)
    {
        autoEOLCtrl.totalTakeTime = pTimeClass->adasTimeCnt; // 统计总用时
    }
    autoEOLCtrl.singletakeTime = pTimeClass->adasTimeCnt; // 统计单次用时

    ALN_routineSt = ROUTINE_ST_START;
}

static uint32_t StartALN_AutoEolTick = 0;

static inline void rstALN_AutoEolTimer(void)
{
    StartALN_AutoEolTick = 0;
}
static inline void setALN_AutoEolTimer(void)
{
    StartALN_AutoEolTick++;
}

static inline uint32_t getStartTime(void)
{
	return StartALN_AutoEolTick;
}

/**
 * @brief 没有调用 
 * 
 * @param param 
 */
void StartALN_AutoEolFun(void *param, void *pParam)
{
    (void)param;
    ADAS_TimeClase_t *pTimeClass = (ADAS_TimeClase_t *)pParam;
	setALN_AutoEolTimer();
	if(getStartTime() >= 900)    // 900s 15分钟
	{
		if(ALN_routineSt == ROUTINE_ST_STOP)
		{
            ALN_initParaForAuto(pTimeClass);  //开始标定
			ALN_initVar();
		}
		if(getStartTime() >= 1080)   //开始标定三分钟内
		{
			rstALN_AutoEolTimer(); //时间归0重新计算
			ALN_routineSt = ROUTINE_ST_STOP; //结束标定  三分钟内没有标定成功，则结束标定，等下一个15分钟再开始标定
		}
        if(gServiceAngleCalcState == CALC_FAILED_ANGLE_MAX_OUT || gServiceAngleCalcState == CALC_FAILED_ANGLE_MIN_OUT)  //校准角度过大或者过小则上报问题。
        {
            ALN_AutoEolFun_Flag = 1;//上报DTC
        }
        else if(gServiceAngleCalcState == CALC_SUCCESS_END)
        {
            ALN_AutoEolFun_Flag = 0;
        }

	}
}

uint8_t getALN_AutoEolFun_Flag()
{
    return ALN_AutoEolFun_Flag;
}


static void Service31Handle_AutoEol(int subServerNumber, const ADAS_TimeClase_t *pTimeClass)
{
    
    switch (subServerNumber)
    {
        case 01:
        {
            ALN_initParaForAuto(pTimeClass);
            #if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("[uds 31 01] AutoEol gServiceAngleCalcState=%d\r\n",gServiceAngleCalcState);
            #endif
            rstALN_AutoEolTimer();  //标定指令进来，则结束15分钟计时
            ALN_initVar();
            // EMBARC_PRINTF("%s >> 1 subServerNumber=%d\r\n", __FUNCTION__, subServerNumber);
            break;
        }
        case 02:
        {
            if(ALN_routineSt == ROUTINE_ST_START)
            {
                //如果还在运行，用户主动退出，增加记录
                //add here
            }

            // TODO:增加中断状态
            gServiceAngleCalcState = CALC_STOP;
            mCtAutoEolInfo.routine_status = ROUTINE_STATUS_INACTIVE;
            ALN_routineSt = ROUTINE_ST_STOP;
            #if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("[uds 31 02] AutoEol gServiceAngleCalcState=%d\r\n",gServiceAngleCalcState);
            #endif
            // EMBARC_PRINTF("%s >> 2 subServerNumber=%d\r\n", __FUNCTION__, subServerNumber);
            break;
        }
        case 03:
        {
            switch (gServiceAngleCalcState)
            {
                case CALC_STOP:
                {
                    mCtAutoEolInfo.routine_status = ROUTINE_STATUS_INACTIVE;
                    break;
                }
                
                case CALC_START:
                case CALC_UNCAL_END:
                {
                    mCtAutoEolInfo.routine_status = ROUTINE_STATUS_ACTIVE;
                    break;
                }
                // 0x02存FLASH失败，暂时没有存储功能
                // case CALC_STOP:
                //     service31RespX1Low = 0x02;
                //     break;
                case CALC_FAILED_TIMEOUT:
                {
                    mCtAutoEolInfo.routine_status = ROUTINE_STATUS_TIMEOUT;
                    mCtAutoEolInfo.routine_result = ROUTINE_RESULT_NO_RESULT;
                    break;
                }

                case CALC_SUCCESS_END:
                {
                    mCtAutoEolInfo.routine_status = ROUTINE_STATUS_FINCORRECTLY;
                    //存储flash，需要考虑上报78操作，不确定是否支持78上报
                    mCtAutoEolInfo.is_save_to_nvm = true;
                
                    mCtAutoEolInfo.routine_result = ROUTINE_RESULT_CORRECT;
                    mCtAutoEolInfo.hdev_angle = horizontalDeviationAngle;        //偏差值
                    break;
                }

                case CALC_FAILED_ANGLE_MAX_OUT:
                case CALC_FAILED_ANGLE_MIN_OUT:
                case CALC_FAILED_MUL_ERR:
                {
                    mCtAutoEolInfo.routine_status = ROUTINE_STATUS_ABORTED;
                    mCtAutoEolInfo.routine_result = ROUTINE_RESULT_INCORRECT;
                    mCtAutoEolInfo.hdev_angle = horizontalDeviationAngle;
                    break;
                }
                
                case CALC_FAILED_SPEED_OUT:
                case CALC_FAILED_ACC:
                case CALC_FAILED_YAWRATE:
                {
                    mCtAutoEolInfo.routine_status = ROUTINE_STATUS_ACTIVE;
                    mCtAutoEolInfo.routine_result = ROUTINE_RESULT_NO_RESULT;
                    break;
                }

                default:
                {
                    mCtAutoEolInfo.routine_status = ROUTINE_STATUS_INACTIVE;
                    break;
                }
            }
            mCtAutoEolInfo.run_status = gServiceAngleCalcState;
            #if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("[uds 31 03] gServiceAngleCalcState=%d , gCalcInstallAngle=%f\r\n",gServiceAngleCalcState,gCalcInstallAngle);
            #endif
            // EMBARC_PRINTF("%s >> 3 routine_status=%d, routine_result=%d\r\n",
                        //   __FUNCTION__, mCtAutoEolInfo.routine_status, mCtAutoEolInfo.routine_result);

            ALN_AutoEolCtrlStatusChange(gServiceAngleCalcState, pTimeClass);
            break;
        }
        default:
        {
            break;
        }
    }

    return;
}

CT_AUTO_EOL_INFO_t *ALN_getAutoEolInfo(void)
{
    return &mCtAutoEolInfo;
}
 
/**
 * @brief 获取自标定运行结果
 *
 * @return float
 */
CT_AutoEOL_ResultInfo_t ALN_getAutoEolRunResult(void)
{ 
    // 第一次运行结果都没有成功，
    if (mCtAutoEolInfo.one_succeed != CALC_UNCAL_END)
    {
        resultAutoEolResult.auto_angle = mCtAutoEolInfo.new_auto_angle; // 自标定角度
        resultAutoEolResult.one_succeed = mCtAutoEolInfo.one_succeed;   // CALC_FAILED_ANGLE_MIN_OUT 等标定结果
        resultAutoEolResult.last_aln_result = mCtAutoEolInfo.routine_result; // 标定结果    ROUTINE_RESULT_NO_RESULT等结果    
        resultAutoEolResult.err_cnt = aln_self_eol.err_cnt;             // 标定失败的次数.
#if 0
        uint8_t radar_id = 0;
        uint16_t print_can_id = 0;
        uint8_t acData[8] = {0};

        radar_id = CFG_getRadarId();

        if(radar_id == 4)
        {
            print_can_id = 0x566;
        }
        else if(radar_id == 5)
        {
            print_can_id = 0x567;
        }
        else if(radar_id == 7)
        {
            print_can_id = 0x568;
        }
        else
        {
            print_can_id = 0x569;
        }

        acData[0] = 0;
        acData[1] = 0;
        acData[2] = ALN_Mode;
        acData[3] = 0;
        acData[4] = mCtAutoEolInfo.one_succeed;
        acData[5] = mCtAutoEolInfo.routine_result;
        acData[6] = 8;
        CAN_sendFrameNow(1, print_can_id, acData, eDATA_LEN_8);
#endif
    }

    return resultAutoEolResult;
}

void ALN_clearAutoEolSaveFlag()
{
    mCtAutoEolInfo.is_save_to_nvm = false;
}

#define ALN_AUTO_MAX_ERR_CNT    (20)
#ifndef PC_DBG_FW
#define ALN_AUTO_PERIOD         (20 * 2 * 1)        // 自标定周期,cnt计数,50ms一个cnt  20代表1秒 10s进行一次
#define ALN_AUTO_ERR_PERIOD     (20 * 1 * 1)        // 上次自标定失败后的标定周期,失败后暂定2s再次标定一次
#else
#define ALN_AUTO_PERIOD         (20 * 2 * 1)        // 自标定周期,cnt计数,50ms一个cnt  20代表1秒 调试时2s进行一次
#define ALN_AUTO_ERR_PERIOD     (20 * 1 * 1)        // 上次自标定失败后的标定周期,失败后调试时暂定1s再次标定一次
#endif

uint8_t self_aln_print = 0;

/**
 * @brief 获取自标定状态信息
 * 
 * @return 自标定状态信息
 */
ALN_AUTO_EOL_T * ALN_GetAutoInfo(void)
{
    return &aln_self_eol;
}

/**
 * @brief 自标定流程实现，需要周期调用，实际算法实现在ALN_AutoEolAngleCalcProcess()函数
 *
 */
static void ALN_AutoEolAngleCalc_WorkflowProcess(const VDY_Info_t *pVDY, const ADAS_TimeClase_t *pTimeClass)
{  
#if ALN_CAN_DBG_PRINT == 1
    uint8_t radar_id = 0;
    uint16_t print_can_id = 0;
    uint8_t acData[8] = {0};
#endif
    ALN_CalibType_t aln_self_type = AUTO_ALN;
    uint32_t tick_now = 0U;
    uint32_t delta_tick = 0U;
    uint32_t period    = 0U;
    CT_AUTO_EOL_INFO_t *pAuto_eol_ressult = NULL;
    uint32_t nowTravlledDistance = 0U;

    //EMBARC_PRINTF("%s >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\n", __FUNCTION__);

    ///首先获取当前标定类型
    aln_self_type = ALN_GetAutoCalibType();

#if ALN_CAN_DBG_PRINT == 1
    radar_id = CFG_getRadarId();

    if(radar_id == 4)
    {
        print_can_id = 0x566;
    }
    else if(radar_id == 5)
    {
        print_can_id = 0x567;
    }
    else if(radar_id == 7)
    {
        print_can_id = 0x568;
    }
    else
    {
        print_can_id = 0x569;
    }
#endif

    ADAS_initAutoEolCtrlStatus();
    
#ifdef PC_DBG_FW
    // 如果当前上电周期内已经执行完5次[单次自标定]，即一整个自标定流程已经执行完毕，则需要经过3km再进行自标定完成流程
    if ((autoEOLCtrl.calibrationDone == true) && (autoEOLCtrl.travlledDistance == 0U))
    {
        autoEOLCtrl.travlledDistance = pTimeClass->adasTimeCnt;
    }
    else if ((autoEOLCtrl.calibrationDone == true) && (autoEOLCtrl.travlledDistance != 0U))
    {
        nowTravlledDistance = pTimeClass->adasTimeCnt;
        if ((nowTravlledDistance - autoEOLCtrl.travlledDistance) >= 10U) // 0.5s
        {
            ADAS_clearAutoEolCtrlStatus();
        }
    }
    // 如果当前上电周期内已经执行完5次自标定，则不再执行 
    if ((autoEOLCtrl.calibrationDone == true) /* || (ALN_isEverAlignment() == false)*/)
#else
    // 如果当前上电周期内已经执行完5次[单次自标定]，即一整个自标定流程已经执行完毕，则需要经过3km再进行自标定完成流程
    if ((autoEOLCtrl.calibrationDone == true) && (autoEOLCtrl.travlledDistance == 0U))
    {
        autoEOLCtrl.travlledDistance = pVDY->pVDY_StaticInfo->vdyTotalOdometer;
    }
    else if ((autoEOLCtrl.calibrationDone == true) && (autoEOLCtrl.travlledDistance != 0U))
    {
        nowTravlledDistance = pVDY->pVDY_StaticInfo->vdyTotalOdometer;
        if ((nowTravlledDistance - autoEOLCtrl.travlledDistance) >= 3)
        {
            ADAS_clearAutoEolCtrlStatus();
        }
    }
    // 如果没有经历过下线标定或者动态标定，则不能执行自标定
    if ((autoEOLCtrl.calibrationDone == true) || (ALN_isAlignDone() == false))
#endif 
    {
        return ;
    }

    ///如果不是自动标定类型，则需要退出
    if(aln_self_type != AUTO_ALN)
    {
        aln_self_eol.state = ALN_AUTO_EOL_START;
        // aln_self_eol.last_aln_tick = RTC_TO_MS(timer_rtc_count());
        aln_self_eol.last_aln_tick = pTimeClass->adasTimeCnt;
        aln_self_eol.last_aln_status = ALN_AUTO_EOL_OK;
#if ALN_CAN_DBG_PRINT == 1
        acData[0] = aln_self_eol.state;
        acData[1] = aln_self_eol.last_aln_result;
        acData[2] = ALN_Mode;
        acData[3] = aln_self_eol.last_aln_status;
        acData[4] = ALN_getAutoEolInfo()->run_status;
        acData[5] = pAuto_eol_ressult->new_fix_angle;
        acData[6] = 1;
        CAN_sendFrameNow(1, print_can_id, acData, eDATA_LEN_8);
        //FAST_PRINT_CAN(print_can_id, "ID:%d:Do Auto\r\n", radar_id);
#endif
        self_aln_print = 0;
        return ;
    }
    //EMBARC_PRINTF("%s >> 2 state=%d\n", __FUNCTION__, aln_self_eol.state);

    ///自标定状态判断
    switch(aln_self_eol.state)
    {
        case ALN_AUTO_EOL_START: ///开始自动标定

            // tick_now = RTC_TO_MS(timer_rtc_count());
            tick_now = pTimeClass->adasTimeCnt;

            delta_tick = tick_now - aln_self_eol.last_aln_tick;

            // 自标定周期控制, 根据客户的具体需求实现.
            period = aln_self_eol.last_aln_status == ALN_AUTO_EOL_OK ? ALN_AUTO_PERIOD : ALN_AUTO_ERR_PERIOD;
            //EMBARC_PRINTF("%s >> 3 START period=%d %d %d \n", __FUNCTION__, period, tick_now, delta_tick);

            ///未达到两次间隔时间，退出
            if(delta_tick < period)
            {
                aln_self_eol.last_aln_result = ALN_AUTO_INACTIVE;
#if ALN_CAN_DBG_PRINT == 1
                acData[0] = aln_self_eol.state;
                acData[1] = aln_self_eol.last_aln_result;
                acData[2] = ALN_Mode;
                acData[3] = aln_self_eol.last_aln_status;
                acData[4] = ALN_getAutoEolInfo()->run_status;
                acData[5] = pAuto_eol_ressult->new_fix_angle;
                acData[6] = 2;
                CAN_sendFrameNow(1, print_can_id, acData, eDATA_LEN_8);
                //FAST_PRINT_CAN(print_can_id, "ID:%d:STANDBY\r\n", radar_id);
                //EMBARC_PRINTF("%s >> 4 interval period=%d %d %d \n", __FUNCTION__, period, tick_now, delta_tick);
#endif
                self_aln_print = 0;
                return;
            }
            //EMBARC_PRINTF("%s >> 4 START (delta_tick < period)=%d\n", __FUNCTION__, (delta_tick < period));

            ///设置自标定模式
            ALN_SetAutoCalibType(AUTO_ALN);

            ///请求服务
            Service31Handle_AutoEol(0x01, pTimeClass);
            //EMBARC_PRINTF("%s >> 5 START Service31Handle_AutoEol\n", __FUNCTION__);

            ///设置新的状态
            aln_self_eol.state = ALN_AUTO_EOL_RUNNING;
            aln_self_eol.last_aln_result = ALN_AUTO_RUNNING;
            break;

        case ALN_AUTO_EOL_RUNNING:///自标定运行

            ///请求结果
            Service31Handle_AutoEol(0x03, pTimeClass);
            //EMBARC_PRINTF("%s >> 6 RUNNING period\n", __FUNCTION__);

            ///获取标定状态
            pAuto_eol_ressult = ALN_getAutoEolInfo();
            //EMBARC_PRINTF("%s >> 7 RUNNING pAuto_eol_ressult, run_status=%d\n", __FUNCTION__, pAuto_eol_ressult->run_status);

            ///判断标定状态
            switch(pAuto_eol_ressult->run_status)
            {
                ///角度偏差大，记录标定失败计数
                case CALC_FAILED_ANGLE_MAX_OUT:
                case CALC_FAILED_ANGLE_MIN_OUT:
                //case CALC_FAILED_MUL_ERR:
                    aln_self_eol.err_cnt++;
                    // aln_self_eol.last_aln_tick = RTC_TO_MS(timer_rtc_count());
                    aln_self_eol.last_aln_tick = pTimeClass->adasTimeCnt;
                    aln_self_eol.last_aln_status = ALN_AUTO_EOL_ERR;
                    aln_self_eol.state = ALN_AUTO_EOL_END;
                    aln_self_eol.last_aln_result = ALN_AUTO_MAX_MIN;
                    self_aln_print = 2;
#if ALN_CAN_DBG_PRINT == 1
                    acData[0] = aln_self_eol.state;
                    acData[1] = aln_self_eol.last_aln_result;
                    acData[2] = ALN_Mode;
                    acData[3] = aln_self_eol.last_aln_status;
                    acData[4] = pAuto_eol_ressult->run_status;
                    acData[5] = aln_self_eol.err_cnt;
                    acData[6] = 3;
                    CAN_sendFrameNow(1, print_can_id, acData, eDATA_LEN_8);
                    //FAST_PRINT_CAN(print_can_id, "ID: FAIL\r\n", radar_id);
#endif
                    //EMBARC_PRINTF("%s >> 8 RUNNING MAX_OUT=%d\n", __FUNCTION__);
                    break;

                ///标定成功，清0标定失败计数
                case CALC_SUCCESS_END:
                    aln_self_eol.err_cnt = 0;
                    // aln_self_eol.last_aln_tick = RTC_TO_MS(timer_rtc_count());
                    aln_self_eol.last_aln_tick = pTimeClass->adasTimeCnt;
                    aln_self_eol.last_aln_status = ALN_AUTO_EOL_OK;
                    aln_self_eol.state = ALN_AUTO_EOL_END;
                    aln_self_eol.last_aln_result = ALN_AUTO_END;
                    self_aln_print = (uint8_t)(pAuto_eol_ressult->new_fix_angle + 0.5f);
#if ALN_CAN_DBG_PRINT == 1
                    acData[0] = aln_self_eol.state;
                    acData[1] = aln_self_eol.last_aln_result;
                    acData[2] = ALN_Mode;
                    acData[3] = aln_self_eol.last_aln_status;
                    acData[4] = pAuto_eol_ressult->run_status;
                    acData[5] = aln_self_eol.err_cnt;
                    acData[6] = 4;
                    CAN_sendFrameNow(1, print_can_id, acData, eDATA_LEN_8);
                    //FAST_PRINT_CAN(print_can_id, "ID:%d:SUCCESS,Angle:%.2f\r\n", radar_id, pAuto_eol_ressult->new_fix_angle);
#endif
                    
                     //EMBARC_PRINTF("%s >> 9 RUNNING\n", __FUNCTION__);

#ifndef PC_DBG_FW // 下位机使用 
                    // Alg_setAutoEOLCaliData(pAuto_eol_ressult);
#endif
                    break;

                ///标定超时,标定结束，设置失败，但是不记录失败计数
                case CALC_FAILED_TIMEOUT:
                case CALC_STOP:
                    // aln_self_eol.last_aln_tick = RTC_TO_MS(timer_rtc_count());
                    aln_self_eol.last_aln_tick = pTimeClass->adasTimeCnt;
                    aln_self_eol.last_aln_status = ALN_AUTO_EOL_ERR;
                    aln_self_eol.state = ALN_AUTO_EOL_END;
                    aln_self_eol.last_aln_result =ALN_AUTO_TIMEOUT;
                    self_aln_print = 2;
#if ALN_CAN_DBG_PRINT == 1
                    acData[0] = aln_self_eol.state;
                    acData[1] = aln_self_eol.last_aln_result;
                    acData[2] = ALN_Mode;
                    acData[3] = aln_self_eol.last_aln_status;
                    acData[4] = pAuto_eol_ressult->run_status;
                    acData[5] = pAuto_eol_ressult->new_fix_angle;
                    acData[6] = 5;
                    CAN_sendFrameNow(1, print_can_id, acData, eDATA_LEN_8);
                    //FAST_PRINT_CAN(print_can_id, "ID:%d:TIMEOUT\r\n", radar_id);
#endif
                    //EMBARC_PRINTF("%s >> 10 RUNNING\n", __FUNCTION__);
                    break;

                ///开始标定，标定未完成，车身信号失败，目标不达标，则继续标定
                case CALC_START:
                case CALC_UNCAL_END:
                case CALC_FAILED_SPEED_OUT:
                case CALC_FAILED_ACC:
                case CALC_FAILED_YAWRATE:
                case CALC_FAILED_MUL_ERR:
                self_aln_print = 3;
#if ALN_CAN_DBG_PRINT == 1
                acData[0] = aln_self_eol.state;
                acData[1] = aln_self_eol.last_aln_result;
                acData[2] = ALN_Mode;
                acData[3] = aln_self_eol.last_aln_status;
                acData[4] = pAuto_eol_ressult->run_status;
                acData[5] = pAuto_eol_ressult->new_fix_angle;
                acData[6] = 6;
                CAN_sendFrameNow(1, print_can_id, acData, eDATA_LEN_8);
                //FAST_PRINT_CAN(print_can_id, "ID:%d:DOING\r\n", radar_id);
#endif
                    //EMBARC_PRINTF("%s >> 11 RUNNING\n", __FUNCTION__);
                default:
                    break;
            }
            break;

        case ALN_AUTO_EOL_END:///自标定结束
            if(aln_self_eol.err_cnt >= ALN_AUTO_MAX_ERR_CNT)
            {
                aln_self_eol.err_cnt = 0;

                self_aln_print = 4;

#if ALN_CAN_DBG_PRINT == 1
                acData[0] = aln_self_eol.state;
                acData[1] = aln_self_eol.last_aln_result;
                acData[2] = ALN_Mode;
                acData[3] = aln_self_eol.last_aln_status;
                acData[4] = pAuto_eol_ressult->run_status;
                acData[5] = aln_self_eol.err_cnt;
                acData[6] = 7;
                CAN_sendFrameNow(1, print_can_id, acData, eDATA_LEN_8);
                //FAST_PRINT_CAN(aln_print_buf, print_can_id, "ID:%d:ERROR\r\n", radar_id);
#endif
            }
            else
            {
                ;///Do nothing
            }
            //EMBARC_PRINTF("%s >> 12 RUNNING\n", __FUNCTION__);

            Service31Handle_AutoEol(0x02, pTimeClass);
            //EMBARC_PRINTF("%s >> 13 RUNNING\n", __FUNCTION__);

            aln_self_eol.state = ALN_AUTO_EOL_START;
            aln_self_eol.last_aln_result = ALN_AUTO_INACTIVE;
            //EMBARC_PRINTF("%s >> 14 RUNNING state=%d, RUNNING last_aln_result=%d\n", __FUNCTION__, aln_self_eol.state, aln_self_eol.last_aln_result);

        default:
            break;
    }
}

/**
 * @brief 当前上电周期自标定大的次数
 *
 * @param return 自标定大的次数
 */
uint8_t ADAS_getAutoEolCtrlCnt(void)
{
    int16_t AutoEolCtrlCnt = -1;
    if (autoEOLCtrl.storageCaliInfo.storageRunCount < MAX_UCHAR)
    {
        AutoEolCtrlCnt = autoEOLCtrl.storageCaliInfo.storageRunCount; // 自标定大次数
    }
    return (uint8_t)AutoEolCtrlCnt;
}
/**
 * @brief 标定状态
 *
 * @param return -1,每次上电默认数值，CALIB_RUN_MAX次标定完成之前都是此数值。
 *               0, not error，存角度。
 *               1, 有问题，上报DTC错误，但是仍然存储结果。
 *               2, error，上报DTC错误，自标定异常，不存储结果。
 */
int8_t ADAS_getAutoEolCtrlStatus(void)
{
    return autoEOLCtrl.storageCaliInfo.storageCaliStatus;
}
/**
 * @brief 标定角度（度）
 *
 * @param return autoEOLCtrl.caliStatus：0, not error，存角度; 1, 有问题，但是仍然存储结果; 2, error，不存储结果
 *               为0或1时存储结果
 */
float ADAS_getAutoEolCtrlAngle(void)
{
    return autoEOLCtrl.storageCaliInfo.calibAvgAngles;
}
/**
 * @brief 标定状态，是否上报DTC错误
 *
 * @param return 0, not error，存角度;
 *               1, 有问题，上报DTC错误，但是仍然存储结果;
 *               2, error，上报DTC错误，自标定异常，不存储结果，
 */
int8_t ADAS_getAutoEolCtrl_IsErr(void)
{
    return autoEOLCtrl.storageCaliInfo.storageCaliStatus;
}

/**
 * @brief 
 * 
 * @return float 
 */
float ADAS_getAutoEolCtrl_SimpleAngle(void)
{
    float calibAngles = -1.0f;
    if (autoEOLCtrl.runAllCount >= 1)
    {
        calibAngles = autoEOLCtrl.calibAngles[autoEOLCtrl.runAllCount - 1];
    }
    return calibAngles;
}

/**
 * @brief 
 * 
 * @param count 
 * @return float 
 */
float ADAS_getAutoEolCtrl_AppointSimpleAngle(uint8_t count)
{
    float calibAngles = -1.0f;
    if (autoEOLCtrl.runAllCount >= 1)
    {
        calibAngles = autoEOLCtrl.calibAngles[count - 1];
    }
    return calibAngles;
}

/**
 * @brief 
 * 
 * @return float 
 */
float ADAS_getAutoEolCtrl_MaxAngle(void)
{
    float calibMaxAngles = -1.0f;
    if (autoEOLCtrl.runAllCount >= 1)
    {
        calibMaxAngles = autoEOLCtrl.singleAngleMax;
    }
    return calibMaxAngles;
}

/**
 * @brief 
 * 
 * @return float 
 */
float ADAS_getAutoEolCtrl_MinAngle(void)
{
    float calibMinAngles = -1.0f;
    if (autoEOLCtrl.runAllCount >= 1)
    {
        calibMinAngles = autoEOLCtrl.singleAngleMin;
    }
    return calibMinAngles;
}

/**
 * @brief 
 * 
 * @return uint16_t 
 */
uint16_t ADAS_getAutoEolCtrl_simpleTakeTime(void)
{
    uint16_t simpleTakeTime = 0U;
    if (autoEOLCtrl.runAllCount >= 1)
    {
        simpleTakeTime = autoEOLCtrl.singletakeTime;
    }
    return simpleTakeTime;
}

/**
 * @brief 
 * 
 * @return uint16_t 
 */
uint16_t ADAS_getAutoEolCtrl_totalTakeTime(void)
{
    uint16_t totalTakeTime = 0U;
    if (autoEOLCtrl.runAllCount >= 5)
    {
        totalTakeTime = autoEOLCtrl.totalTakeTime;
    }
    return totalTakeTime;
}

/**
 * @brief 
 * 
 * @return float 
 */
float ADAS_getAutoEolCtrl_SimpleCount(void)
{
    return autoEOLCtrl.runAllCount;
}

/**
 * @brief 
 * 
 */
static void ADAS_clearAutoEolCtrlStatus(void)
{
    uint8_t travlledDistance = 0U;
    uint8_t totalRunCount = 0U;
    int8_t storageCaliStatus = -1;
    travlledDistance = autoEOLCtrl.travlledDistance;
    totalRunCount = autoEOLCtrl.storageCaliInfo.storageRunCount;
    storageCaliStatus = autoEOLCtrl.storageCaliInfo.storageCaliStatus;
    memset(&autoEOLCtrl, 0x0, sizeof(ADAS_SelfCalibrationCtrl));
    autoEOLCtrl.travlledDistance = travlledDistance;
    autoEOLCtrl.storageCaliInfo.storageRunCount = totalRunCount;
    autoEOLCtrl.storageCaliInfo.storageCaliStatus = storageCaliStatus;
    autoEOLCtrl.singleAngleMin = 999.f;
    autoEOLCtrl.caliStatus = -1;
    // autoEOLCtrl.storageCaliInfo.storageCaliStatus = -1; // 不清
    ALN_clearAutoAlignCnt();
}

/**
 * @brief 自标定下电清除信息
 *
 */
void ADAS_clearAutoEolCtrlStatus_PowerOnOff(void)
{
    ADAS_clearAutoEolCtrlStatus();
    autoEOLCtrl.travlledDistance = 0U;
    autoEOLCtrl.storageCaliInfo.storageRunCount = 0;
    autoEOLCtrl.storageCaliInfo.storageCaliStatus = -1;
}

void ADAS_resetAutoEolCtrlInitStatus(void)
{
    ginitAutoStatus = 0U;
}

/**
 * @brief 自标定上电初始化
 * 
 */
void ADAS_initAutoEolCtrlStatus(void)
{
    if (ginitAutoStatus == 0U)
    {
        ginitAutoStatus = 1U;
        ADAS_clearAutoEolCtrlStatus_PowerOnOff();
    }
}

/**
 * @brief 
 *
 * @param cdi_pkg
 * @param freezedVehDyncDataAddr
 */
static void ALN_AutoEolCtrlStatusChange(int gServiceAngleCalcState, const ADAS_TimeClase_t *pTimeClass)
{
    if ((autoEOLCtrl.runAllCount > CALIB_RUN_MAX) || (autoEOLCtrl.calibrationDone == true)) // 最大运行CALIB_RUN_MAX次
    {
        return;
    }
    // float singleAngleMax = 0.f, singleAngleMin = 999.f;
    if ((gServiceAngleCalcState == CALC_SUCCESS_END) ||
        (gServiceAngleCalcState == CALC_FAILED_ANGLE_MAX_OUT) || (gServiceAngleCalcState == CALC_FAILED_ANGLE_MIN_OUT))
    {
        autoEOLCtrl.calibAngles[autoEOLCtrl.runAllCount] = mCtAutoPara.fixAngleOffset + horizontalDeviationAngle;
        if ((gServiceAngleCalcState == CALC_SUCCESS_END) &&
            (fabsf(autoEOLCtrl.calibAngles[autoEOLCtrl.runAllCount] - mCtAutoPara.fixAngleOffset) < 3.0f))
        {
            autoEOLCtrl.autoEolSuccesFalg = autoEOLCtrl.runAllCount; // 此标志既是标定角度在3度内的标志，又是最后一次标定角度在3度内的提示
            autoEOLCtrl.calibSuccesAngles = autoEOLCtrl.calibAngles[autoEOLCtrl.runAllCount]; // 标定角度在3度内的角度记录
        } 
        autoEOLCtrl.runAllCount++;

        // 统计自标定单次流程用时
        autoEOLCtrl.singletakeTime = pTimeClass->adasTimeCnt - autoEOLCtrl.singletakeTime;

        // 最大最小值查找
        if ((autoEOLCtrl.runAllCount >= 1) && (autoEOLCtrl.runAllCount <= 5))
        {
            if (autoEOLCtrl.singleAngleMax <= autoEOLCtrl.calibAngles[autoEOLCtrl.runAllCount - 1])
            {
                autoEOLCtrl.singleAngleMax = autoEOLCtrl.calibAngles[autoEOLCtrl.runAllCount - 1];
            }
            if (autoEOLCtrl.singleAngleMin >= autoEOLCtrl.calibAngles[autoEOLCtrl.runAllCount - 1])
            {
                autoEOLCtrl.singleAngleMin = autoEOLCtrl.calibAngles[autoEOLCtrl.runAllCount - 1];
            }
        }

        if (autoEOLCtrl.runAllCount >= CALIB_RUN_MAX)
        {
            autoEOLCtrl.calibrationDone = true;
            for (uint8_t j = 0; j < CALIB_RUN_MAX; j++)
            {
                autoEOLCtrl.calibAngles[CALIB_RUN_MAX] += autoEOLCtrl.calibAngles[j]; 
            }
            // 去掉最大/最小值
            autoEOLCtrl.calibAngles[CALIB_RUN_MAX] = autoEOLCtrl.calibAngles[CALIB_RUN_MAX] -
                                                     (autoEOLCtrl.singleAngleMax + autoEOLCtrl.singleAngleMin);
            autoEOLCtrl.calibAngles[CALIB_RUN_MAX] /= (CALIB_RUN_MAX - 2);  // 求去掉最大/最小值之后的平均角度

            // 如果平均值在3度内，用平均值
            if (fabsf(autoEOLCtrl.calibAngles[CALIB_RUN_MAX] - mCtAutoPara.fixAngleOffset) < 3.0f)
            {
                autoEOLCtrl.caliStatus = 0; // 结果正常;
            }
            // 如果平均值在3度外，但有一次自标定角度在3度内，用此次自标定角度在3度内的结果值
            else if (autoEOLCtrl.autoEolSuccesFalg != 0)
            {
                autoEOLCtrl.caliStatus = 0; // 结果正常;
                autoEOLCtrl.calibAngles[CALIB_RUN_MAX] = autoEOLCtrl.calibSuccesAngles;
            }
            // 平均值大于等于3且小于5，仍然存储，
            else if (fabsf(autoEOLCtrl.calibAngles[CALIB_RUN_MAX] - mCtAutoPara.fixAngleOffset) < 5.0f)
            {
                autoEOLCtrl.caliStatus = 1; // 结果有一些异常，需要上报故障，但是仍需要保存;
            }
            // 平均值大于5，不存储，要上报故障
            else
            {
                autoEOLCtrl.caliStatus = 2; // 结果异常，需要上报故障; 
            }

            // 统计自标定整个流程总用时
            autoEOLCtrl.totalTakeTime = pTimeClass->adasTimeCnt - autoEOLCtrl.totalTakeTime;

            /**
             * @brief 把当前自标定流程结束结果存储 
             */
            autoEOLCtrl.storageCaliInfo.storageCaliStatus = autoEOLCtrl.caliStatus;
            autoEOLCtrl.storageCaliInfo.calibAvgAngles = autoEOLCtrl.calibAngles[CALIB_RUN_MAX];
            autoEOLCtrl.storageCaliInfo.storageAngleMax = autoEOLCtrl.singleAngleMax;
            autoEOLCtrl.storageCaliInfo.storageAngleMin = autoEOLCtrl.singleAngleMin;
            autoEOLCtrl.storageCaliInfo.storageTotalTakeTime = autoEOLCtrl.totalTakeTime;
            if (autoEOLCtrl.storageCaliInfo.storageRunCount < MAX_UCHAR)
            {
                autoEOLCtrl.storageCaliInfo.storageRunCount++; // 自标定大次数
            } 

            if ((autoEOLCtrl.caliStatus == 0) || (autoEOLCtrl.caliStatus == 1))
            {
                ALN_setAutoHrzAngle(autoEOLCtrl.storageCaliInfo.calibAvgAngles);
            }

#ifndef PC_DBG_FW
            // 存储标定相关参数
            if (autoEOLCtrl.storageCaliInfo.storageCaliStatus >= 0)
            {
                setSelfCalcErrorFlag(autoEOLCtrl.storageCaliInfo.storageCaliStatus); // 不能调用函数
            }
            setCfgSelfCaliHrzAngleOffset((ADAS_getAutoEolCtrlAngle() - mCtAutoPara.fixAngleOffset));
#endif
        }

        if (autoEOLCtrl.runAllCount >= 1)
        {
            if (fabsf(autoEOLCtrl.calibAngles[autoEOLCtrl.runAllCount - 1] - mCtAutoPara.fixAngleOffset) < ALN_AUTO_MAX_SET_OFFSET_ANGLE)
            {
                ALN_updateAutoAlignCnt(1);
            }
            else
            {
                ALN_updateAutoAlignCnt(0);
            }
        }

#ifdef PC_DBG_FW
        EMBARC_PRINTF("Aln auto eol func: runAllCount=%d\n", ADAS_getAutoEolCtrl_SimpleCount());
        EMBARC_PRINTF("calibAngles[0]=%.4f, calibAngles[1]=%.4f, calibAngles[2]=%.4f, calibAngles[3]=%.4f, calibAngles[4]=%.4f\n",
                      ADAS_getAutoEolCtrl_AppointSimpleAngle(1), ADAS_getAutoEolCtrl_AppointSimpleAngle(2), ADAS_getAutoEolCtrl_AppointSimpleAngle(3), 
                      ADAS_getAutoEolCtrl_AppointSimpleAngle(4), ADAS_getAutoEolCtrl_AppointSimpleAngle(5));
        EMBARC_PRINTF("singleAngleMax=%.4f, singleAngleMin=%.4f, Max-Min=%.4f\n",
                      ADAS_getAutoEolCtrl_MaxAngle(), ADAS_getAutoEolCtrl_MinAngle(), ((ADAS_getAutoEolCtrl_MaxAngle()) - (ADAS_getAutoEolCtrl_MinAngle())));
        EMBARC_PRINTF("autoEolSuccesFalg=%d, calibSuccesAngles=%.4f\n", autoEOLCtrl.autoEolSuccesFalg, autoEOLCtrl.calibSuccesAngles);
        EMBARC_PRINTF("singletakeTime=%d, totalTakeTime=%d\n",
                      ADAS_getAutoEolCtrl_simpleTakeTime(), ADAS_getAutoEolCtrl_totalTakeTime());
        EMBARC_PRINTF("caliAvgbAngles=%.4f\n", ADAS_getAutoEolCtrlAngle());
#endif
    }
}

/**
 * @brief 自标定处理主函数
 * 
 * @param cdi_pkg 
 * @param freezedVehDyncDataAddr 
 */
void ALN_AutoEolAngleCalcMainFun(const cdi_pkg_t *cdi_pkg, const VDY_Info_t *pVDY, const VDY_DynamicEstimate_t *freezedVehDyncDataAddr, float time)
{
    static ADAS_TimeClase_t timeClass = {.adasTimeCnt = 0, .trackTime = 0};
    timeClass.trackTime = time;
    timeClass.adasTimeCnt++; // 不用做溢出操作， 2^32*50/1000/60/60/24/365=6.8 year

    ALN_AutoEolAngleCalc_WorkflowProcess(pVDY, &timeClass); // 查看状态,类比到诊断
    ALN_AutoEolAngleCalcProcess(cdi_pkg, freezedVehDyncDataAddr, &timeClass); // 实现函数

#ifdef PC_DBG_FW
    ALN_DynamicEolAngleCalc_WorkflowProcess(&timeClass);                         // 实现函数
#endif
}



