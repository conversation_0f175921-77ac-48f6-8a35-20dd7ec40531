﻿/**
 * @file rdp_main_ekf.c
 * @brief 
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef PC_DBG_FW
#include <math.h>
#include <string.h>
#else
#include <math.h>
#include <string.h>
#endif
#include "rdp_track_common.h"

/* Function Definitions */

/*
 * % debug
 *  fprintf('-------\n');
 *  format = '| %8.5f ';
 *
 *  fprintf('A\n');
 *  for i = 1:numel(A)
 *      fprintf(format, A(i));
 *  end
 *  fprintf('|\n');
 *
 *  fprintf('Q\n');
 *  for i = 1:numel(Q)
 *      fprintf(format, Q(i));
 *  end
 *  fprintf('|\n');
 *
 *  fprintf('old_sigma\n');
 *  for i = 1:numel(old_sigma)
 *      fprintf(format, old_sigma(i));
 *  end
 *  fprintf('|\n');
 *
 *  fprintf('old_P\n');
 *  for i = 1:numel(old_P)
 *      fprintf(format, old_P(i));
 *  end
 *  fprintf('|\n');
 *
 *  fprintf('R\n');
 *  for i = 1:numel(R)
 *      fprintf(format, R(i));
 *  end
 *  fprintf('|\n');
 *
 *  fprintf('old_x\n');
 *  for i = 1:numel(old_x)
 *      fprintf(format, old_x(i));
 *  end
 *  fprintf('|\n');
 *
 *  fprintf('miss\n');
 *  for i = 1:numel(miss)
 *      fprintf(format, miss(i));
 *  end
 *  fprintf('|\n');
 *
 *  fprintf('old_z\n');
 *  for i = 1:numel(old_z)
 *      fprintf(format, old_z(i));
 *  end
 *  fprintf('|\n');
 *
 *  fprintf('T\n');
 *  for i = 1:numel(T)
 *      fprintf(format, T(i));
 *  end
 *  fprintf('|\n');
 * Arguments    : const float A[36]
 *                const float Q[36]
 *                const float old_sigma[2]
 *                const float old_P[36]
 *                const float R[9]
 *                const float old_x[6]
 *                int miss
 *                const float old_z[3]
 *                float T
 *                float new_sigma[2]
 *                float new_P[36]
 *                float new_x[6]
 *                float new_z[3]
 *                float pre_z[3]
 * Return Type  : void
 */

void predictEkf(const float A[36], const float old_x[6], float new_x[6])
{
	for (int rtemp = 0; rtemp < 6; rtemp++)
	{
		new_x[rtemp] = 0.0f;
		for (int k = 0; k < 6; k++)
		{
			new_x[rtemp] += (A[rtemp + (6 * k)] * old_x[k]);
		}
	}
}

float x_[6];

float b_A[36];
int rtemp;
int k;
int r1;
float b_old_sigma[36];
float P_[36];
float maxval;
float r;
float v;
float a;
float H[18];
float y[18];
float Kg[18];
float B[9];
int r2;
int r3;
float a21;
float b_r[3];
float b_old_z[3];
signed char I[36];


void RDP_runMainEKF(const float EKF_A[36], const float EKF_Q[36], const float old_sigma[2],
              const float old_P[36], const float EKF_R[9], const float old_x[6],
              int miss, const float old_z[3], float T, float new_sigma[2],
              float new_P[36], float new_x[6], float new_z[3], float pre_z[3])
{

  /* ------------------------------------------------------------------- */
  /*  */
  /*   Filename      : rdp_main_ekf.m */
  /*   Author        : Huang Lei Lei */
  /*   Created       : 2016-12-27 */
  /*   Description   : main of extended kalman filter */
  /*  */
  /* ------------------------------------------------------------------- */
  /*  */
  /*   Modified      : 2017-02-09 by HLL */
  /*   Description   : dbug log added */
  /*   Modified      : 2017-02-22 by HLL */
  /*   Description   : more dbug log added */
  /*  */
  /* ------------------------------------------------------------------- */
  /* % update immediate x_ & P_ */
  for (rtemp = 0; rtemp < 6; rtemp++)
  {
    x_[rtemp] = 0.0f;
    for (k = 0; k < 6; k++)
    {
      x_[rtemp] += EKF_A[rtemp + 6 * k] * old_x[k];
    }

    for (k = 0; k < 6; k++)
    {
      b_A[rtemp + 6 * k] = 0.0f;
      for (r1 = 0; r1 < 6; r1++)
      {
        b_A[rtemp + 6 * k] += EKF_A[rtemp + 6 * r1] * old_P[r1 + 6 * k];
      }
    }
  }

  b_old_sigma[0] = old_sigma[0];
  b_old_sigma[6] = 0.0f;
  b_old_sigma[12] = old_sigma[0];
  b_old_sigma[18] = 0.0f;
  b_old_sigma[24] = old_sigma[0];
  b_old_sigma[30] = 0.0f;
  b_old_sigma[1] = 0.0f;
  b_old_sigma[7] = old_sigma[1];
  b_old_sigma[13] = 0.0f;
  b_old_sigma[19] = old_sigma[1];
  b_old_sigma[25] = 0.0f;
  b_old_sigma[31] = old_sigma[1];
  b_old_sigma[2] = old_sigma[0];
  b_old_sigma[8] = 0.0f;
  b_old_sigma[14] = old_sigma[0];
  b_old_sigma[20] = 0.0f;
  b_old_sigma[26] = old_sigma[0];
  b_old_sigma[32] = 0.0f;
  b_old_sigma[3] = 0.0f;
  b_old_sigma[9] = old_sigma[1];
  b_old_sigma[15] = 0.0f;
  b_old_sigma[21] = old_sigma[1];
  b_old_sigma[27] = 0.0f;
  b_old_sigma[33] = old_sigma[1];
  b_old_sigma[4] = old_sigma[0];
  b_old_sigma[10] = 0.0f;
  b_old_sigma[16] = old_sigma[0];
  b_old_sigma[22] = 0.0f;
  b_old_sigma[28] = old_sigma[0];
  b_old_sigma[34] = 0.0f;
  b_old_sigma[5] = 0.0f;
  b_old_sigma[11] = old_sigma[1];
  b_old_sigma[17] = 0.0f;
  b_old_sigma[23] = old_sigma[1];
  b_old_sigma[29] = 0.0f;
  b_old_sigma[35] = old_sigma[1];
  for (rtemp = 0; rtemp < 6; rtemp++)
  {
    for (k = 0; k < 6; k++)
    {
      maxval = 0.0f;
      for (r1 = 0; r1 < 6; r1++)
      {
        maxval += b_A[rtemp + 6 * r1] * EKF_A[k + 6 * r1];
      }

      P_[rtemp + 6 * k] = maxval + EKF_Q[rtemp + 6 * k] * b_old_sigma[rtemp + 6 * k];
    }
  }

  /* % update new_x & new_P */
  if (miss != 0)
  {
    /*  get new_x & new_P */
    for (rtemp = 0; rtemp < 6; rtemp++)
    {
      new_x[rtemp] = x_[rtemp];
    }

    memcpy(&new_P[0], &P_[0], 36 * sizeof(float));
  }
  else
  {
    /*  get H */
    r = sqrtf(x_[0] * x_[0] + x_[1] * x_[1]);
    v = x_[2] * x_[0] / r + x_[3] * x_[1] / r;
    //a = atanf(x_[0] / x_[1]) / 3.1415926f * 180.0f;
    a = atan2f(x_[0], x_[1]) / 3.1415926f * 180.0f;
    H[0] = x_[0] / r;
    H[3] = x_[1] / r;
    H[6] = 0.0f;
    H[9] = 0.0f;
    H[12] = 0.0f;
    H[15] = 0.0f;
    H[1] = x_[2] / r - x_[0] * v / (r * r);
    H[4] = x_[3] / r - x_[1] * v / (r * r);
    H[7] = x_[0] / r;
    H[10] = x_[1] / r;
    H[13] = 0.0f;
    H[16] = 0.0f;
    H[2] = x_[1] / (r * r);
    H[5] = -x_[0] / (r * r);
    H[8] = 0.0f;
    H[11] = 0.0f;
    H[14] = 0.0f;
    H[17] = 0.0f;
    // for (rtemp = 0; rtemp < 6; rtemp++)
    // {
    //   H[2 + 3 * rtemp] = H[2 + 3 * rtemp] / 3.1415926f * 180.0f;
    // }

    if (a - old_z[2] > 135.0f)
    {
      a -= 180.0f;
    }
    else
    {
      if (a - old_z[2] < -135.0f)
      {
        a += 180.0f;
      }
    }

    /*  get Kg */
    for (rtemp = 0; rtemp < 6; rtemp++)
    {
      for (k = 0; k < 3; k++)
      {
        y[rtemp + 6 * k] = 0.0f;
        for (r1 = 0; r1 < 6; r1++)
        {
          y[rtemp + 6 * k] += P_[rtemp + 6 * r1] * H[k + 3 * r1];
        }
      }
    }

    for (rtemp = 0; rtemp < 3; rtemp++)
    {
      for (k = 0; k < 6; k++)
      {
        Kg[rtemp + 3 * k] = 0.0f;
        for (r1 = 0; r1 < 6; r1++)
        {
          Kg[rtemp + 3 * k] += H[rtemp + 3 * r1] * P_[r1 + 6 * k];
        }
      }
    }

    for (rtemp = 0; rtemp < 3; rtemp++)
    {
      for (k = 0; k < 3; k++)
      {
        maxval = 0.0f;
        for (r1 = 0; r1 < 6; r1++)
        {
          maxval += Kg[rtemp + 3 * r1] * H[k + 3 * r1];
        }

        B[rtemp + 3 * k] = maxval + EKF_R[rtemp + 3 * k];
      }
    }

    r1 = 0;
    r2 = 1;
    r3 = 2;
    maxval = fabsf(B[0]);
    a21 = fabsf(B[1]);
    if (a21 > maxval)
    {
      maxval = a21;
      r1 = 1;
      r2 = 0;
    }

    if (fabsf(B[2]) > maxval)
    {
      r1 = 2;
      r2 = 1;
      r3 = 0;
    }

    B[r2] /= B[r1];
    B[r3] /= B[r1];
    B[3 + r2] -= B[r2] * B[3 + r1];
    B[3 + r3] -= B[r3] * B[3 + r1];
    B[6 + r2] -= B[r2] * B[6 + r1];
    B[6 + r3] -= B[r3] * B[6 + r1];
    if (fabsf(B[3 + r3]) > fabsf(B[3 + r2]))
    {
      rtemp = r2;
      r2 = r3;
      r3 = rtemp;
    }

    B[3 + r3] /= B[3 + r2];
    B[6 + r3] -= B[3 + r3] * B[6 + r2];
    for (k = 0; k < 6; k++)
    {
      Kg[k + 6 * r1] = y[k] / B[r1];
      Kg[k + 6 * r2] = y[6 + k] - Kg[k + 6 * r1] * B[3 + r1];
      Kg[k + 6 * r3] = y[12 + k] - Kg[k + 6 * r1] * B[6 + r1];
      Kg[k + 6 * r2] /= B[3 + r2];
      Kg[k + 6 * r3] -= Kg[k + 6 * r2] * B[6 + r2];
      Kg[k + 6 * r3] /= B[6 + r3];
      Kg[k + 6 * r2] -= Kg[k + 6 * r3] * B[3 + r3];
      Kg[k + 6 * r1] -= Kg[k + 6 * r3] * B[r3];
      Kg[k + 6 * r1] -= Kg[k + 6 * r2] * B[r2];
    }

    /*  get new_x & new_P */
    b_r[0] = r;
    b_r[1] = v;
    b_r[2] = a;
    for (rtemp = 0; rtemp < 3; rtemp++)
    {
      b_old_z[rtemp] = old_z[rtemp] - b_r[rtemp];
    }

    b_old_z[2] = ANG2RAD * (b_old_z[2]);

    for (rtemp = 0; rtemp < 6; rtemp++)
    {
      maxval = 0.0f;
      for (k = 0; k < 3; k++)
      {
        maxval += Kg[rtemp + 6 * k] * b_old_z[k];
      }

      new_x[rtemp] = x_[rtemp] + maxval;
    }

    for (rtemp = 0; rtemp < 36; rtemp++)
    {
      I[rtemp] = 0;
    }

    for (k = 0; k < 6; k++)
    {
      I[k + 6 * k] = 1;
    }

    for (rtemp = 0; rtemp < 6; rtemp++)
    {
      for (k = 0; k < 6; k++)
      {
        maxval = 0.0f;
        for (r1 = 0; r1 < 3; r1++)
        {
          maxval += Kg[rtemp + 6 * r1] * H[r1 + 3 * k];
        }

        b_A[rtemp + 6 * k] = (float)I[rtemp + 6 * k] - maxval;
      }
    }

    for (rtemp = 0; rtemp < 6; rtemp++)
    {
      for (k = 0; k < 6; k++)
      {
        new_P[rtemp + 6 * k] = 0.0f;
        for (r1 = 0; r1 < 6; r1++)
        {
          new_P[rtemp + 6 * k] += b_A[rtemp + 6 * r1] * P_[r1 + 6 * k];
        }
      }
    }
  }

  /* % get new_z */
  r = sqrtf(new_x[0] * new_x[0] + new_x[1] * new_x[1]);
  if(r < 0.1f)
    r = 0.1f;
  new_z[0] = r;
  new_z[1] = new_x[2] * new_x[0] / r + new_x[3] * new_x[1] / r;
  //new_z[2] = atanf(new_x[0] / new_x[1]) / 3.1415926f * 180.0f;
  new_z[2] = atan2f(new_x[0], new_x[1]) / 3.1415926f * 180.0f;


  /* % get new_sigma */
  /*  declare */
  /*  q_x */
  if (miss != 0)
  {
    maxval = new_x[4];
  }
  else
  {
    maxval = ((new_x[2] - old_x[2]) / T + new_x[4]) / 2.0f;
  }

  new_sigma[0] = maxval * maxval;

  /*  q_y */
  if (miss != 0)
  {
    maxval = new_x[5];
  }
  else
  {
    maxval = ((new_x[3] - old_x[3]) / T + new_x[5]) / 2.0f;
  }

  new_sigma[1] = new_x[0] * new_x[0] * (0.04f*0.04f);
  new_sigma[0] = new_x[1] * new_x[1] * (0.04f*0.04f);
  if(new_sigma[0] < 0.5f)
    new_sigma[0] = 0.5f;
  if(new_sigma[1] < 0.5f)
    new_sigma[1] = 0.5f;

}


//对近距离的目标跟踪处理，使用直线匀速运动处理，直接对 x 和 y 方向单独处理，分档固定增益
//dif_spd 表示相对速度,如果是前进的时候，表示纵向速度差，倒车的时候暂时不通过这个方法过滤
void near_efk(const float old_x[6] ,const float cx, const float cy,const float ov , const float dif_spd, float kp[2],
                    int miss ,float T, float new_x[6] , float new_z[3] , float pre_z[3] , int dir)
{
	float r,v,a;
	float div_x;
	float div_y;
	//dir = dir;    //方向目前只用于处理 x y 方向的速度，根据不同的运动方向处理
	//更新状态预测
	x_[0] = old_x[0];
	x_[1] = old_x[1] + dif_spd*T;

	r = sqrtf(x_[0] * x_[0] + x_[1] * x_[1]);
	v = ov;
	a = atan2f(x_[0], x_[1]) / 3.1415926f * 180.0f;

	if(miss != 0)
	{
		//没有匹配，只有预测
	}
	else
	{
		//最优估计
		div_x = cx-x_[0];
		div_y = cy-x_[1];

		//根据差值更新增益
		if(fabsf(div_x) > 2)
		{
			kp[0] = kp[0] * 1.1f;
		}
		else if(fabsf(div_x) > 1)
		{
			kp[0] = kp[0] * 1.05f;
		}
		else
		{
			kp[0] = 0.3f;
		}

		if(fabsf(div_y) > 2)
		{
			kp[1] = kp[1] * 1.08f;
		}
		else if(fabsf(div_y) > 1)
		{
			kp[1] = kp[1] * 1.01f;
		}
		else
		{
			kp[1] = 0.18f;
		}

		x_[0] = x_[0]+kp[0] * div_x;
		x_[1] = x_[1]+kp[1] * div_y;

	}

	new_z[0] = r;
	new_z[1] = v;
	new_z[2] = a;

  //更正状态量
  new_x[0] = x_[0];
  new_x[1] = x_[1];
  //方向控制 x y 的速度
  if(dir == 0)
  {
    new_x[2] = 0;
    new_x[3] = v;
  }
  else
  {
    new_x[2] = v;
    new_x[3] = 0;
  }
  new_x[4] = 0;
  new_x[5] = 0;

	//下一步预测
	x_[0] = x_[0];
	x_[1] = x_[1] + dif_spd*T;

	r = sqrtf(x_[0] * x_[0] + x_[1] * x_[1]);
	v = ov;
	a = atan2f(x_[0], x_[1]) / 3.1415926f * 180.0f;

	pre_z[0] = r;
	pre_z[1] = v;
	pre_z[2] = a;

}
/*
 * File trailer for rdp_main_ekf.c
 *
 * [EOF]
 */
