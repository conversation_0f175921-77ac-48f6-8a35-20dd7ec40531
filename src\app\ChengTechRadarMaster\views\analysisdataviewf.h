﻿#ifndef ANALYSISDATAVIEWF_H
#define ANALYSISDATAVIEWF_H

#include "analysisdataviewi.h"
#include "analysis/analysisdataf.h"

namespace Ui {
class AnalysisDataViewF;
}

namespace Views {
namespace AnalysisView {

class AnalysisDataViewF : public AnalysisDataViewI
{
    Q_OBJECT

public:
    explicit AnalysisDataViewF(quint8 radarID, Parser::ParsedDataTypedef::ParsedData *parsedData, QWidget *parent = nullptr);
    ~AnalysisDataViewF();

    void parsedData(const Parser::ParsedDataTypedef::ParsedData &parsedData);

    void setViewAnalysisTypes(int fType, bool moving, bool continuous, const ViewTypes &types) override;

private slots:
    void viewTypeChanged() override;

private:
    Ui::AnalysisDataViewF *ui;

    Parser::ParsedDataTypedef::ParsedData *mParsedData{0};  // 前雷达
};

}
}

#endif // ANALYSISDATAVIEWF_H
