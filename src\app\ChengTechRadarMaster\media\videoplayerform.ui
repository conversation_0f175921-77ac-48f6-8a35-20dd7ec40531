<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>VideoPlayerForm</class>
 <widget class="QWidget" name="VideoPlayerForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>925</width>
    <height>530</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>文件:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditFileName"/>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonSelectFile">
       <property name="text">
        <string>选择</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="8,2">
     <item>
      <widget class="QVideoWidget" name="videoWidget" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QListWidget" name="listWidget"/>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QPushButton" name="pushButtonPlay">
           <property name="text">
            <string>播放</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonPause">
           <property name="text">
            <string>暂停</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_8">
         <item>
          <widget class="QPushButton" name="pushButtonStop">
           <property name="text">
            <string>停止</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonSkipBLF">
           <property name="text">
            <string>BLF跳至此时</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <widget class="QPushButton" name="pushButtonPreFrame">
           <property name="text">
            <string>&lt;</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonNextFrame">
           <property name="text">
            <string>&gt;</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <widget class="QPushButton" name="pushButtonPreFrame2">
           <property name="text">
            <string>&lt;&lt;</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonNextFrame2">
           <property name="text">
            <string>&gt;&gt;</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <item>
          <widget class="QPushButton" name="pushButtonPreFrame3">
           <property name="text">
            <string>&lt;&lt;&lt;</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonNextFrame3">
           <property name="text">
            <string>&gt;&gt;&gt;</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <widget class="QLabel" name="labelBeginTime">
       <property name="text">
        <string>视频起始时间：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDateTimeEdit" name="dateTimeEditBegin"/>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>视频结束时间</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDateTimeEdit" name="dateTimeEditEnd"/>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="labelCurrentTime">
       <property name="text">
        <string>当前时间：</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QSlider" name="horizontalSlider">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelProccess">
       <property name="text">
        <string>N/N</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QVideoWidget</class>
   <extends>QWidget</extends>
   <header location="global">QVideoWidget</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
