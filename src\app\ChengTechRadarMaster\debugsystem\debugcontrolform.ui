<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DebugControlForm</class>
 <widget class="QWidget" name="DebugControlForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1066</width>
    <height>250</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="leftMargin">
    <number>9</number>
   </property>
   <property name="topMargin">
    <number>9</number>
   </property>
   <property name="rightMargin">
    <number>9</number>
   </property>
   <property name="bottomMargin">
    <number>9</number>
   </property>
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>1086</width>
        <height>216</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>文件：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditFiles"/>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonGetOpenFiles">
           <property name="text">
            <string>...</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxBatchGeneration">
           <property name="text">
            <string>批量生成</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonBatchGeneration">
           <property name="text">
            <string>批量生成</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QPushButton" name="pushButtonStartAndStop">
           <property name="text">
            <string>开始</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxSkipType">
           <property name="minimumSize">
            <size>
             <width>75</width>
             <height>0</height>
            </size>
           </property>
           <item>
            <property name="text">
             <string>跳转到(时间)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>跳转到(帧)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>跳转秒数</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QDateTimeEdit" name="dateTimeEdit">
           <property name="displayFormat">
            <string>yyyy/MM/dd hh:mm:ss.zzz</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditSkip">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="maximumSize">
            <size>
             <width>80</width>
             <height>16777215</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="lb_CuiFrameCnt">
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>0</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>回放间隔(ms)：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="spinBoxBinaryInterval">
           <property name="minimum">
            <number>1</number>
           </property>
           <property name="maximum">
            <number>10000</number>
           </property>
           <property name="value">
            <number>50</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxSingleFrame">
           <property name="text">
            <string>单帧调试</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonPrevFrame">
           <property name="text">
            <string>上一帧</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonNextFrame">
           <property name="text">
            <string>下一帧</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxUseEndFrameID">
           <property name="text">
            <string>使用结束帧ID:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditEndFrameID">
           <property name="maximumSize">
            <size>
             <width>200</width>
             <height>16777215</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxSaveCsv">
           <property name="text">
            <string>保存跟踪点统计数据</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QCheckBox" name="checkBoxInterpolation">
           <property name="text">
            <string>插值</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxHil">
           <property name="text">
            <string>是否回灌</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxHilType">
           <item>
            <property name="text">
             <string>回灌原始点</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>回灌跟踪点</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxRadar4">
           <property name="text">
            <string>4号雷达</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxRadarChannel4">
           <item>
            <property name="text">
             <string>CAN 0</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>CAN 1</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxRadar5">
           <property name="text">
            <string>5号雷达</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxRadarChannel5">
           <item>
            <property name="text">
             <string>CAN 0</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>CAN 1</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxRadar6">
           <property name="text">
            <string>6号雷达</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxRadarChannel6">
           <item>
            <property name="text">
             <string>CAN 0</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>CAN 1</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxRadar7">
           <property name="text">
            <string>7号雷达</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxRadarChannel7">
           <item>
            <property name="text">
             <string>CAN 0</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>CAN 1</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxShowCandi">
           <property name="text">
            <string>显示候选点</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonBatchHIL">
           <property name="text">
            <string>批量回灌</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <widget class="QCheckBox" name="checkBoxDataProcess">
           <property name="text">
            <string>数据处理</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxRadarFusion">
           <property name="text">
            <string>雷达融合</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxorgpoint">
           <property name="text">
            <string>原始点仿真</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>主雷达：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxMasterRadar"/>
         </item>
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>从雷达:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxSlaveRadar"/>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonSettings">
           <property name="text">
            <string>设置</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxEOL">
           <item>
            <property name="text">
             <string>STATIC_EOL</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>DYNA_EOL</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="labelFileBeginEndTime">
           <property name="text">
            <string>文件起止时间:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelCurrentFrameTime">
           <property name="text">
            <string>当前帧时间:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonVideoSkip">
           <property name="text">
            <string>视频跳转至此时</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonVideoPlayer">
           <property name="text">
            <string>视频回放</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>comboBoxEOL</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>comboBoxEOL</receiver>
   <slot>setCurrentIndex(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>628</x>
     <y>248</y>
    </hint>
    <hint type="destinationlabel">
     <x>628</x>
     <y>250</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
