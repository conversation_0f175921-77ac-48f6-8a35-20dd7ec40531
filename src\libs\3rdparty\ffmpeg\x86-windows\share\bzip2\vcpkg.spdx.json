{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/bzip2-x86-windows-1.0.8#5-8deb56b9-b557-4964-a451-1ae92461bbc5", "name": "bzip2:x86-windows@1.0.8#5 c364229a151ed0b338db76e4ae594bb817f2e0d00a0b0244a26c3d21146a1988", "creationInfo": {"creators": ["Tool: vcpkg-7d353e869753e5609a1f1a057df3db8fd356e49d"], "created": "2024-05-28T03:03:45Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-5"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "bzip2", "SPDXID": "SPDXRef-port", "versionInfo": "1.0.8#5", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/bzip2", "homepage": "https://sourceware.org/bzip2/", "licenseConcluded": "bzip2-1.0.6", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "bzip2 is a freely available, patent free, high-quality data compressor. It typically compresses files to within 10% to 15% of the best available techniques (the PPM family of statistical compressors), whilst being around twice as fast at compression and six times faster at decompression.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "bzip2:x86-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "c364229a151ed0b338db76e4ae594bb817f2e0d00a0b0244a26c3d21146a1988", "downloadLocation": "NONE", "licenseConcluded": "bzip2-1.0.6", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-1", "name": "bzip2-${VERSION}.tar.gz", "packageFileName": "bzip2-${VERSION}.tar.gz", "downloadLocation": "https://sourceware.org/pub/bzip2/bzip2-${VERSION}.tar.gz", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "083f5e675d73f3233c7930ebe20425a533feedeaaa9d8cc86831312a6581cefbe6ed0d08d2fa89be81082f2a5abdabca8b3c080bf97218a1bd59dc118a30b9f3"}]}], "files": [{"fileName": "./D:/Src/vcpkg-master/ports/bzip2/bzip2.pc.in", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "bba1241c5dd9e86e9dc81915816edc32bd32bcef148715372b82ed62838ea740"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/bzip2/CMakeLists.txt", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "8935f858d0308fccf551559696797f36170e1404eefbb0bbb60f255e296d10e3"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/bzip2/fix-import-export-macros.patch", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "38918c1257c884cc5cde39d9f4235d0e71574f20c7e5ec686a506612f647495e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/bzip2/portfile.cmake", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "0581cada7dfe9bfac7e24badd7543b0593350727c3f2242faf80212ec37fce03"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/bzip2/usage", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "75d339011fc11a43ee5093142ca1e8cb5e54db2ac93822abc5de694cda5a9881"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/Src/vcpkg-master/ports/bzip2/vcpkg.json", "SPDXID": "SPDXRef-file-5", "checksums": [{"algorithm": "SHA256", "checksumValue": "96594ea06b5c6bc85d1e041e80d1f2988c08ecb92ec05d9df888a1f22fad8427"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}