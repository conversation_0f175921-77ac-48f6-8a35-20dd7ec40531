#ifndef SHARED_VAR_H__
#define SHARED_VAR_H__

#ifdef ALPSPRO_ADAS
#include "typedefs.h"
#include "sharedDef.h"
#include "trk_cfg.h"
#include "data_proc.h"
#elif defined(PC_DBG_FW)
#include "app/system_mgr/typedefs.h"
#include "app/include/sharedDef.h" 
#include "other/temp.h"
#else
#include "typedefs.h"
#include "sharedDef.h"
#include "trk_cfg.h"
#include "data_proc.h"
#endif

//数值越低  优先级越低
#define TSK_PRIOR_HI (configMAX_PRIORITIES - 1)
#define TSK_PRIOR_MID (configMAX_PRIORITIES - 2)
#define TSK_PRIOR_LO (configMAX_PRIORITIES - 3)

#define MAX_PEAK_NUM 10240

#if RADAR_SERVICE_ALIGNMENT_MODE_DBAA == 1
#define DBAA_HORIZONTAL_PARAM_BUFFER_SIZE                  1    //水平角度检测边界
#define DBAA_HORIZONTAL_ANGLE_RANGE		                   8   //水平角度范围
#define DBAA_HORIZONTAL_ANGLE_BIN			               0.1f  //水平角度步进
#define DBAA_HORIZONTAL_SAUARE_BUFFER_SIZE	               161  //水平角度size(unsigned int)(2 * (DBAA_ANGLE_RANGE / DBAA_ANGLE_BIN) + 1)  //方差Buffer大小

#define DBAA_VERTICAL_PARAM_BUFFER_SIZE                    1    //俯仰角度检测边界
#define DBAA_VERTICAL_ANGLE_RANGE                          5   //俯仰角度范围
#define DBAA_VERTICAL_ANGLE_BIN                            0.1f  //俯仰角度步进
#define DBAA_VERTICAL_SAUARE_BUFFER_SIZE                   101  //俯仰角度size

#define DBAA_HORIZONTAL_OBJ_CNT_BUFFER_SIZE                8U	//水平校准的刻度
#define DBAA_VERTICAL_OBJ_CNT_BUFFER_SIZE	               4	//俯仰校准的刻度
#endif

#if RADAR_SERVICE_ALIGNMENT_MODE_ASC == 1
#define ASC_USE_TARGET_NUM                                  100U //每个角度选取目标的数量
#define ASC_ANGLE_RANGE                                     8U   //选取目标的角度范围
#endif

#if ANGLE_SELF_CALIBRATION_BASE_ASC == 1
#define ANGLE_SELF_CALI_BUFFER_SIZE		10
#define ANGLE_SELF_CALI_DIFF_MAX		(UINT8)(ANGLE_SELF_CALI_BUFFER_SIZE / 3)						//波动误差计算的取值,取1/3的位置
#define ANGLE_SELF_CALI_DIFF_MIN		(ANGLE_SELF_CALI_BUFFER_SIZE - ANGLE_SELF_CALI_DIFF_MAX)		//波动误差计算的取值,取1/3的位置
#define ANGLE_SELF_CALI_ODOMETER_MIN	(uint8_t)(3) 	//0.1Km
#endif 

//雷达标定成功后，标定目标的位置记录
typedef struct radarInstallCalcObj_t
{
    float x;
    float y;
    float range;
    float speed;
    float angle;
    float snr;
    int idx;
    UINT8 histMaxCnt;//实际最大直方图个数
} radarInstalledCalcObj;


/// Track List
typedef union tagTrackState {
    UINT64 word;
    struct
    {
        UINT64 GROUPING_CHANGED : 1;
        UINT64 moveFlag : 1;
        UINT64 STATUS : 3;
        UINT64 TRACK_STATUS : 4;
        UINT64 SORT_FLAG : 32;
        UINT64 ObjOutPutFlag : 1;
        UINT64 objType : 4; //另一个目标分类
        UINT64 DynProp : 6;
        UINT64 idx_1 : 8;
        UINT64 MeasFlag : 1;
        UINT64 UpdateFlag : 1;  //用于指示 CAN 总线中新创建的对象的标志。 0x0：循环中的新对象;  0x1：对象在上一个周期中存在
        UINT64 bridge : 1;
        UINT64 ars410ObjType : 1;
    } bit_big;
    struct
    {
        UINT64 ars410ObjType : 1;
        UINT64 bridge : 1;
        UINT64 UpdateFlag : 1;  //用于指示 CAN 总线中新创建的对象的标志。 0x0：循环中的新对象;  0x1：对象在上一个周期中存在
        UINT64 MeasFlag : 1;
        UINT64 idx_1 : 8;
        UINT64 DynProp : 6;
        UINT64 objType : 4; //另一个目标分类
        UINT64 ObjOutPutFlag : 1;
        UINT64 SORT_FLAG : 32;
        UINT64 TRACK_STATUS : 4;
        UINT64 STATUS : 3;
        UINT64 moveFlag : 1;
        UINT64 GROUPING_CHANGED : 1;
    } bit;
} TTrackState;

typedef union tagTrackValue {
    UINT32 word;
    struct
    {
        UINT32 missCnt : 8;
        UINT32 hitCnt : 8;
        UINT32 trkReiability : 8;
        UINT32 trkCnt : 8;
    } bit_big;
    struct
    {
        UINT32 trkCnt : 8;
        UINT32 trkReiability : 8;
        UINT32 hitCnt : 8;
        UINT32 missCnt : 8;
    } bit;
} TTrackValue;

typedef struct tagTrackRVA
{
    TTrackState state;
    TTrackValue traValue;
    float distance;
    float speed;
    float angle;
    float heighAngle;
    float snr;
    float centerOffset;
    float objLength;
    float objWidth;
    float acc_y;
    float acc_x;
    float rcs;
    float vx;
    float vy;
    float handingAngle;
    float  x;
    float  y;
    float  objObstacleProb;
    float  objExistProb;
    float rms[7];
    uint16_t weight;
    UINT8 motionPattern;
    UINT8 objType;
    UINT8 idx;
    UINT8  sendId;
} TTrackRVA;

typedef struct tagRawRVA
{
    float distance;
    float speed;
    float angle;
    float angleFixed;
    float heighAngle;
    float snr;
    float rcs;
    uint8_t mag;
    uint8_t objTxCh;//远距离目标还是近距离目标
    int8_t tag;
    uint8_t use;
    int     cdiIdx; //对应的cdi, radarTarget使用
    float amb_speed;
    int16_t clusterID;
    int16_t rawIdx;//对应的原始点, radarTarget使用
    int16_t clusterTailIdx;
	uint8_t velMatchFlag;
    float SSL0;
    float SSL1;
    float SSL2;
    uint8_t pow_diff;
} TRawRVA;

typedef struct tagCaliObj
{
    float distance;
    float speed;
    float angle;
    float heighAngle;
    float snr;
} TCaliObj;

typedef struct tagRawRVAList
{
    float time;
    int32_t targetNum;
    float angleOffset;
    int32_t speedAssocErrNum;
    TRawRVA target[MAX_CDI_NUM];
} TRawRVAList;

typedef union tagTrackSence {
    UINT16 word;
    struct
    {
        UINT16 ruralScene : 1;  //乡村道路
        UINT16 barnScene : 1;   //地下车库
        UINT16 fenceScene : 1;  //靠栏杆场景
        UINT16 tunnelFence : 1; //隧道场景
        UINT16 ObjMaxCntAbove : 2;    //清智目标超过标志 0没有超，1，2，3都分别在不同策略下还超过
        UINT16 jam : 1;        //拥堵场景
        UINT16 REV : 9;        //其他
    } bit_big;
    struct
    {
        UINT16 REV : 9;        //其他
		UINT16 jam : 1;        //拥堵场景
        UINT16 ObjMaxCntAbove : 2;    //清智目标超过标志 0没有超，1，2，3都分别在不同策略下还超过
        UINT16 tunnelFence : 1; //隧道场景
        UINT16 fenceScene : 1;  //靠栏杆场景
        UINT16 barnScene : 1;   //地下车库
        UINT16 ruralScene : 1;  //乡村道路
    } bit;
} TTrackSenceFlag;

typedef union tagTrackObjFilter {
    UINT8 word;
    struct
    {
    	UINT8 unMoveFlag : 1;  //±3m外的，静止目标
    	UINT8 Fence : 1;   	//护栏过滤
    	UINT8 bridght : 1;  //天桥
    	UINT8 REV : 5;        //其他
    } bit_big;
    struct
    {
    	UINT8 REV : 4;        //其他
    	UINT8 harmonic : 1;        //其他
    	UINT8 bridght : 1;  //天桥
    	UINT8 Fence : 1;   	//护栏过滤
    	UINT8 unMoveFlag : 1;  //±3m外的，静止目标
    } bit;
} TTrackObjFilterFlag;

//目标需要输出的内容/flag：静止/运动，左护栏/正常，右护栏/正常，井盖/正常，天桥/正常，正前方车道静止目标/正常，，，自信度
typedef union tagSortFlag {
	UINT32 word;
	struct
	{
		UINT32 resv : 16;
		UINT32 ConObjHighV : 1;	   //伴随性大速度假目标
		UINT32 caliObjectFlag : 1; //留
		UINT32 cross : 1;		   //横穿目标
		UINT32 multipathObj : 1;   //后方来的目标、多径目标
		UINT32 moveObjectFlag : 1; //移动物体，或是有移动过的物体 标记
		UINT32 moveObjectType : 3; //0无效 1人 2自行车，电动车，3小车 4大车
		UINT32 moveAttributes : 2; //运动属性 0-不详 ，1来向 ，2去向 ，3静止
		UINT32 midStatic : 1;	   //中间测到绝对静止目标标记
		UINT32 bridght : 1;		   //跳桥/路牌标记
		UINT32 cover : 1;		   //井盖标记
		UINT32 rightFrence : 1;	   //右护栏标记
		UINT32 leftFence : 1;	   //左护栏标记
		UINT32 unMoveFlag : 1;	   //绝对静止标记
	} bit_big;
	struct
	{
		UINT32 unMoveFlag : 1;	   //绝对静止标记
		UINT32 leftFence : 1;	   //左护栏标记
		UINT32 rightFrence : 1;	   //右护栏标记
		UINT32 cover : 1;		   //井盖标记
		UINT32 bridght : 1;		   //跳桥/路牌标记
		UINT32 multipathObj : 1;   //后方来的目标、多径目标
		UINT32 moveAttributes : 2; //运动属性 0-不详 ，1来向 ，2去向 ，3静止
		UINT32 moveObjectType : 3; //0无效 1人 2自行车，电动车，3小车 4大车
        UINT32 moveObjectFlag : 1; //移动物体，或是有移动过的物体 标记
		UINT32 harmonicFlag : 1; //谐波目标
        UINT32 cross : 1;		   //横穿目标
        UINT32 longVehicle : 1;  //大车，横向
		UINT32 forwardTruck : 1;	//前方大车
        UINT32 maintain : 1;        //不使用匹配目标更新，采用Miss的方式跟踪
        UINT32 bigVehicle : 1;      //大车，纵向
        UINT32 IsNoLaneCaseObj : 1;//被遮挡不关注且不稳定的目标
        UINT32 fission : 1; //大车分裂目标
		UINT32 stopCar : 1; //疑似停止的车辆判断
        UINT32 laneNum : 3;			//车道编号
        UINT32 laneNumOld : 3;			//上一次车道车道编号
        UINT32 moveFlag : 1;		//运动目标标记  ，0-未运动、1-运动
		UINT32 keep : 1;        //目标未进跟踪
        UINT32 caliObjectFlag : 1; //留
		UINT32 resolution : 1;
		UINT32 ConObjHighV : 1;	   //伴随性大速度假目标
	} bit;
} TSortFlag;

typedef struct tagTrackRVAList
{
    uint16_t targetNum;
    TTrackSenceFlag senceFlag;
    int forwardObjId;  //正前方最近非护栏目标ID
    float angleOffset; //角度偏移补偿
    float estSpeed[3]; //估计的车速, 0是用mag值估算的车速，1是用目标点估算的车速
    TTrackRVA target[MAX_TRK_NUM];
    uint8_t trkObjNum;
    uint8_t outPutNum;
    uint8_t canPutNum;
    uint8_t TimeStampStatus; //时间戳同步状态
    uint8_t trkUpdataFlag;//跟踪点更新标志，在发送1次后清零，跟踪填充后置1
    uint8_t swMrrFlag;    //切换MRR波形标志
    uint64_t TimeStampMs; //时间戳
} TTrackRVAList;

typedef union SendTrkObjTypeFlag {
	UINT8 word;
	struct {
		UINT8 REV 				: 6; //其他
		UINT8 CANDI				: 1; //CANDI目标
		UINT8 TRACK				: 1; //跟踪后目标
	}bit;
}SendTrkObjType;

typedef union tagradarOperate {
	UINT8 word;
	struct {
		UINT8 res 			: 7; //
		UINT8 trkInitFlag 	: 1; //是否需要清除跟踪状态
	}bit;
}trkOperate_t; //雷达操作模式

typedef enum CFAR_value
{
    OSCA,
    OS2D,
    CA2D,
    CA_SOCA,
} CFAR_t;

// typedef struct
// {
//     int8_t valid;     //是否有效
//     int8_t cIdx;        //关联到的跟踪点，radartarget调试用
//     int8_t cdiIdx;      //对应的cdi idx
//     int8_t using;     //是否使用
//     int8_t F;         //DDMIMO中间参数
//     int8_t ambIdx;    //解速度模糊的模糊数

//     int8_t angleNum;  //有几个角度，超分辨时可能有2个
//     float range;      //距离
//     float velocity;   //速度
//     float realVel;    //解速度模糊后的结果
//     float angle;      //水平角度
//     float heighAngle; //高度角
//     float sig_elv;
//     uint16_t rangeBin;    //距离bin
//     int16_t velocityBin; //速度bin
//     float fftMag;       //fft mag值
//     float doaMag;    //doa mag值
//     float mag1;        //当前fftMag与前和后一个距离bin的fftMag值平方和开根号
//     float threshold; //底噪mag值
//     float phase[4]; //相位值
//     float firstvelocity; 

//     float snr;
//     float x;
//     float y;
// 	float rcs; //DDM track_read_hv cali
// 	uint8_t realProfileIdx; //实际radar内部的frameType
// 	// #if DDM_RAW_DATA_EXT_INFO
// 	uint16_t  threshold3T; //3T阈值调试用
// 	int8_t  deltaRange;  //距离
// 	int8_t  deltaVel;    //速度
// 	// #endif
//     int8_t staticFlag;

//     uint8_t peakNum[2];
//     float sideLobe[3];
// } Target_Thres_t;

typedef struct 
{
    float farRange;
    float leftRange;
    float leftAngle;
    float rightRange;
    float rightAngle;
}antArea_t;

typedef struct
{
    uint16_t cohesionEn; //是否是能聚类
    uint16_t filterType; //护栏过滤类型
    uint16_t radarMode;  //雷达工作模式，FCW/AEB/NORMAL
    uint16_t debugMode;  //debug模式开关
    antArea_t antArea;   //天线的负责范围
    uint8_t selfCali;    //自标定安装角度开关
    SendTrkObjType  ObjSendType; //发送的目标类型
    trkOperate_t	trkOperate; //跟踪操作
    TTrackObjFilterFlag trkOutFilter;  //输出过滤
    uint8_t isChanged;    //结构体内容已改变
    uint8_t resved[11];
    uint32_t crc;
} radarTrkCfg_t;

typedef struct
{
    float x0;
    float y0;
    float x1;
    float y1;
    u8 IsValid;
} Area;

typedef struct
{
	Area turnArea; //掉头区域的 (x0,y0)   (x1,y1) 配置时候需要注意，规定 x0y0要在 x1y1的左下方， x0<=x1 y0<=y1
	Area stopArea;
    Area sidewalkArea1;	//人行道区域1，与车辆掉头区域说明一样
	Area sidewalkArea2; //人行道区域2，与车辆掉头区域说明一样
	Area sidewalkArea3; //人行道区域3，与车辆掉头区域说明一样
	Area sidewalkArea4; //人行道区域4，与车辆掉头区域说明一样
    u8 laneNumLeft;  //左车道数量,0时候表示不用
    u8 laneNumRight; //右车道数量。0时候表示不用
    float laneWide;
    float laneWideX[8]; //最大支持7个车道

    //目标过滤相关参数
	float gRangeThr_Y_A;
	float gRangeThr_Y_B;
	float gRangeThr_X_C;
	float gRangeThr_X_D;
	float gRangeThr_Tx1_E;
	float gRangeThr_Tx2_F;

} hwBaseCfgInfo;

typedef struct
{
    uint8_t led : 1;
    uint8_t volt : 1;
    uint8_t rf : 1;
    uint8_t temp : 1;
    uint8_t flash_error : 1;
    uint8_t angleagv : 1;
    uint8_t resv : 1;
    uint8_t is_1st_update : 1;
} DetectStatus_st;

void initVar(void);

#endif
