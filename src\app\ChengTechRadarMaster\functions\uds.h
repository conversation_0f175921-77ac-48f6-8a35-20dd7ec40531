﻿#ifndef UDS_H
#define UDS_H

#include <QObject>
#include <QQueue>
#include <QMutex>

#include "devices/canframe.h"

#define WAIT_TIME 5000

enum UDSFrameType
{
    SingleFrame = 0x0,
    FirstFrame = 0x1,
    SuccessiveFrame = 0x2,
    FlowControlFrame = 0x3,
    UnknowFrame
};

enum UDSFSType
{
    FS_Successive = 0,
    FS_Wait = 1,
    FS_OverFlow = 2
};

typedef struct UDSFrame
{
    quint8 mServiceID{0x0};
    QByteArray mData;
    quint32 mIntactDataLength{0};
    quint8 mIndex{0};

    UDSFSType mFS{FS_Successive};
    quint8 mBS{0};
    quint8 mSTmin{0};

    void clear() {
        mServiceID = 0x0;
        mData.clear();
        mIntactDataLength = 0;
        mIndex = 0;
        mFS = FS_Successive;
        mBS = 0;
        mSTmin = 0;
    }
}UDSFrame;

uint32_t SeedToKey (uint32_t seed, uint32_t MASK);
uint32_t SeedToKeyHASE (uint32_t SEED, uint32_t MASK);
quint8 GeelySeedToKey(quint8 seed[3u], /*quint8 data[5u],*/ quint8 *key);
quint8 GeelySeedToKey180Pro(quint8 seed[3u], /*quint8 data[5u],*/ quint8 *key);
quint8 GeelySeedToKey180ProNew(quint8 seed[3u], /*quint8 data[5u],*/ quint8 *key, int index);
void GwmSeedToKey(quint32 seed ,const quint8 *key);
int GenerateKeyEx_BEIQI_BE12_PROJECT(
        const unsigned char* iSeedArray,
        unsigned short iSeedArraySize,
        const unsigned int iSecurityLevel,
        const char* iVariant,
        unsigned char* ioKeyArray,
        unsigned int iKeyArraySize,
        unsigned int* oSize
    );
int GenerateKeyEx_GEELY_E245_PROJECT(
        int type,
        const unsigned char* iSeedArray,
        unsigned short iSeedArraySize,
        const unsigned int iSecurityLevel,
        const char* iVariant,
        unsigned char* ioKeyArray,
        unsigned int iKeyArraySize,
        unsigned int* oSize
    );


namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Functions {

class UDS : public QObject
{
    Q_OBJECT
public:
    explicit UDS(Devices::Can::DeviceManager *deviceManager, QObject *parent = nullptr);

    void appendCanFrame(const Devices::Can::CanFrame &frame);
    Devices::Can::CanFrame takeFirstFrame(bool &ok);
    void clearReceive();


    const QString &errorString() { return mErrorString; }

    void set8BitUpdate(bool _8Bit) { m8BitUpdate = _8Bit; }
    void canUpdate(bool can) { mCanUpdate = can; }
    void setResponseID(quint32 id) { mResponseID = id; }
    quint32 responseID() const { return  mResponseID; }
    int channelIndex() const { return mChannelIndex; };
    void setChannelIndex(int channelIndex) { mChannelIndex = channelIndex; }

    bool sendFrame(quint16 frameID, const QByteArray &data);

    /** @brief 发送数据，不需要长度字节 */
    bool sendData(quint16 frameID, const QString &dataHex);

    /** @brief 发送数据，不需要长度字节 */
    bool sendData(quint16 frameID, const QByteArray &data);

    /** @brief 发送数据，不需要长度字节 */
    bool sendData(quint16 frameID, quint8 serviceID, const QByteArray &subID, const QByteArray &data);

    /** @brief 发送数据，不需要长度字节 */
    bool sendData(quint16 frameID, const QString &dataHex, UDSFrame *responseFrame, qint64 timeOut = WAIT_TIME);

    /** @brief 发送数据，不需要长度字节 */
    bool sendData(quint16 frameID, quint8 serviceID, const QByteArray &subID, const QByteArray &data, UDSFrame *responseFrame, qint64 timeOut = WAIT_TIME);

    /** @brief 发送数据，不需要长度字节 */
    bool sendData(quint16 frameID, const QByteArray &data, UDSFrame *responseFrame, qint64 timeOut = WAIT_TIME);

    UDSFrameType parseFrame(const Devices::Can::CanFrame &canFrame, UDSFrame *udcFrame);

signals:

private:
    /** @brief 发送单帧数据，不需要长度字节 */
    quint8 sendDataSingleFrame(quint16 frameID, const QByteArray &data);

    /** @brief 发送多帧，不需要长度字节 */
    quint8 sendDataMultiFrame(quint16 frameID, const QByteArray &data, UDSFrame *responseFrame, qint64 timeOut = WAIT_TIME);

    /** @brief 发送连续帧 */
    bool sendSuccessiveFrame(quint16 frameID, const QByteArray &data, const qint8 beginIndex = 0);

    /** @brief 发送流控制 */
    bool sendFlowControlFrame(quint16 frameID, quint8 fs = 0, quint8 bs = 0x0, quint8 sTmin = 0x0);

    Devices::Can::DeviceManager *mDeviceManager{0};

    QString mErrorString;
    QMutex mMutex;

    quint32 mResponseID{0x7FA};

    bool m8BitUpdate{false};
    bool mCanUpdate{false};

    int mChannelIndex{0};
    QQueue<Devices::Can::CanFrame> mReceiveCanFrames;
    int mMaxKeepNumber{200};
};

} // namespace Functions

#endif // UDS_H
