﻿#include "analysismodel.h"

#include <QColor>
#include <QFont>
#include <QSize>
#include <QDateTime>
#include <QtMath>
#include <QDebug>

namespace Views {
namespace AnalysisView {

/*************************************************************************************/
AnalysisModelF::AnalysisModelF(Parser::ParsedDataTypedef::ParsedDataType dataType, QObject *parent)
    : QAbstractTableModel(parent),
      mAnalysisFrameType(dataType)
{
}

QVariant AnalysisModelF::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal)
    {
        switch (role)
        {
        case Qt::DisplayRole:
            return Parser::ParsedDataTypedef::sgTargetAttributes[mViewAnalysisTypes.at(section)].mTargetAttributeNameEN;
        case Qt::TextAlignmentRole:
            return  Qt::AlignLeft;
        case Qt::SizeHintRole:
            break;
        case Qt::FontRole:
//        {
//            QFont font;
//            font.setPixelSize(7);
//            return font;
//        }
            break;
        }
    }
    else
    {
        switch (role)
        {
        case Qt::DisplayRole:
        {
            if( mAnalysisFrameType == Parser::ParsedDataTypedef::TargetTrack){
                if( section < mAlarmIDList.size() ){
                    return mAlarmIDList[section];
                }else{
                    return section - mAlarmIDList.size();
                }
                //return  section == 0 ? mAlarmID : (section -1) ;
            }else{
                return section;
            }
        }
        case Qt::SizeHintRole:
            break;
        case Qt::FontRole:
        {
            QFont font;
            font.setPixelSize(7);
            font.setBold(true);
            return font;
        }
            break;
        }
    }

    return QAbstractTableModel::headerData(section, orientation, role);
}

int AnalysisModelF::rowCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }

    if( mAnalysisFrameType == Parser::ParsedDataTypedef::TargetTrack){
        return sizeof(mTargets.mTargets) / sizeof (mTargets.mTargets[0]) + mAlarmIDList.size();
    }else{
        return sizeof(mTargets.mTargets) / sizeof (mTargets.mTargets[0]);
    }
}

int AnalysisModelF::columnCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }
    return mViewAnalysisTypes.size();
}

QVariant AnalysisModelF::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() > rowCount( index.parent() )/*index.row() >= sizeof(mTargets.mTargets) / sizeof (mTargets.mTargets[0])*/ )
    {
        return QVariant();
    }

    const Parser::ParsedDataTypedef::TargetF &target = mTargets.mTargets[index.row()];
    int cloumn = index.column();
    switch(role)  {
    case Qt::DisplayRole:          // 6
        if (!target.mValid) {
            return "--.--";
        }
        return QString::number(target.value((Parser::ParsedDataTypedef::TargetAttributeType)mViewAnalysisTypes[cloumn]), 'f', 2);
        break;
    }

    return QVariant();

    return QVariant();
}

Qt::ItemFlags AnalysisModelF::flags(const QModelIndex &index) const
{
//    if (!index.isValid())
    return QAbstractItemModel::flags(index);
}

void AnalysisModelF::setViewAnalysisTypes(bool moving, bool continuous, const ViewTypes &types)
{
    if (!types.size())
    {
        return;
    }
    this->beginResetModel();
    mMovingOnly = moving;
    mContinuousDisplay = continuous;
    mViewAnalysisTypes = types;
    this->endResetModel();
}

void AnalysisModelF::targets(quint8 radarID, /*AnalysisFrameType*/int frameType, const Parser::ParsedDataTypedef::TargetsF &targets)
{
    if (frameType != mAnalysisFrameType)
    {
        return;
    }

    beginResetModel();

    mAlarmIDList.clear();
    bool monitor = false;
    //mAlarmID = -1;

    mTargets.clear();
    for (uint i = 0; i <= targets.mEffectiveNumber; ++i) {
        const Parser::ParsedDataTypedef::TargetF &target = targets.mTargets[i];
        if (target.mID < TARGET_MAX_COUNT) {
            mTargets.mTargets[target.mID] = target;
        }
    }
    endResetModel();
}

AnalysisModel::AnalysisModel(AnalysisFrameType dataType, QObject *parent)
    : QAbstractTableModel(parent),
      mAnalysisFrameType(dataType)
{
}

QVariant AnalysisModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal)
    {
        switch (role)
        {
        case Qt::DisplayRole:
            return gAnalysisTypeName((AnalysisType)mViewAnalysisTypes.at(section));
        case Qt::TextAlignmentRole:
            return  Qt::AlignLeft;
        case Qt::SizeHintRole:
            break;
        case Qt::FontRole:
//        {
//            QFont font;
//            font.setPixelSize(7);
//            return font;
//        }
            break;
        }
    }
    else
    {
        switch (role)
        {
        case Qt::DisplayRole:
        {
            if( mAnalysisFrameType == FrameTrackTarget){
                if( section < mAlarmIDList.size() ){
                    return mAlarmIDList[section];
                }else{
                    if (mContinuousDisplay) {
                        const Target &target = mTargets.mTargets[section - mAlarmIDList.size()];
                        return (target.mValid ? QString::number(target.mID) : "---");
                    } else {
                        return section - mAlarmIDList.size();
                    }
                }
                //return  section == 0 ? mAlarmID : (section -1) ;
            }else{
                return section;
            }
        }
        case Qt::SizeHintRole:
            break;
        case Qt::FontRole:
        {
            QFont font;
            font.setPixelSize(7);
            font.setBold(true);
            return font;
        }
            break;
        }
    }

    return QAbstractTableModel::headerData(section, orientation, role);
}

int AnalysisModel::rowCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }

    if( mAnalysisFrameType == FrameTrackTarget){
        return sizeof(mTargets.mTargets) / sizeof (mTargets.mTargets[0]) + mAlarmIDList.size();
    }else{
        return sizeof(mTargets.mTargets) / sizeof (mTargets.mTargets[0]);
    }
}

int AnalysisModel::columnCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }
    return mViewAnalysisTypes.size();
}

QVariant AnalysisModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() > rowCount( index.parent() )/*index.row() >= sizeof(mTargets.mTargets) / sizeof (mTargets.mTargets[0])*/ )
    {
        return QVariant();
    }

    int targetIndex = index.row();
    if( mAnalysisFrameType == FrameTrackTarget){
        //获取行表头  作为下标
        targetIndex = index.row() - mAlarmIDList.size();//headerData( targetIndex, Qt::Vertical, Qt::DisplayRole ).toInt();
    }


    const Target &target = mTargets.mTargets[targetIndex/*index.row()*/];
    int status = target.mStatus;
    int nColumn = index.column();
    switch(role)
    {
    case Qt::DisplayRole:          // 6
        if ( targetIndex<0 || !target.mValid)
        {
            return "--.--";
        }

        if( nColumn == mViewAnalysisTypes.indexOf(RefencePointPostion) && mAnalysisFrameType == Frame16Track ){
            return QString::number(target.value((AnalysisType)mViewAnalysisTypes[nColumn]) );
        }else{
            return QString::number(target.value((AnalysisType)mViewAnalysisTypes[nColumn]), 'f', 2);
        }
    case Qt::FontRole:
//        return QFont("song", 8);
    case Qt::SizeHintRole:
        break;
    case Qt::BackgroundRole:
        {
            //if( mAnalysisFrameType == FrameTrackTarget && index.row() == 0 && targetIndex >= 0  ){

        switch (mAnalysisFrameType) {
        case FrameRawTarget:
            /*if (target.mMatchFlag < 0x3 && target.mValid) {
                return QColor(255, 204, 153); // 淡黄色
            } else */if (status == 8 || status == 24 || status == 40 || status == 56) {
                return QColor(231, 139, 68); // 橘色
            } else if (status & 0x40) {
                return QColor(255, 102, 102); // 橘红色
            } else if (status & 0x10) {
                return QColor(255, 204, 153); // 淡黄色
            }
            break;
        case FrameTrackTarget:
            if(index.row() < mAlarmIDList.size() ) {
                return QColor( 255, 0, 0 ); //首行报警数据非空，则红色背景
            }
            break;
        default:
            break;
        }

        }
        break;
    }

    return QVariant();
}

Qt::ItemFlags AnalysisModel::flags(const QModelIndex &index) const
{
//    if (!index.isValid())
    return QAbstractItemModel::flags(index);
}

void AnalysisModel::setViewAnalysisTypes(bool moving, bool continuous, const QList<int> &types)
{
    if (!types.size())
    {
        return;
    }
    this->beginResetModel();
    mMovingOnly = moving;
    mContinuousDisplay = continuous;
    mViewAnalysisTypes = types;
    this->endResetModel();
}

void AnalysisModel::targets(quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &targets)
{
    if (frameType != mAnalysisFrameType)
    {
        return;
    }

    beginResetModel();

    mAlarmIDList.clear();
    bool monitor = false;
    //mAlarmID = -1;

    mTargets.clear();
//    qDebug() << __FUNCTION__ << __LINE__ << targets.mTargetCount << frameType << mMonitorTarget;
    int index = 0;
    for (int i = 0; i < targets.mTargetCount; ++i)
    {
        if (targets.mTargets[i].mValid)
        {
            if (mMovingOnly && targets.mTargets[i].mDynamicProperty != 0x01) {
                continue;
            }
//            qDebug() << __FUNCTION__ << __LINE__ << i << index
//                     << targets.mTargets[i].mValid << targets.mTargets[i].mID
//                     << targets.mTargets[i].mX << targets.mTargets[i].mY << targets.mTargets[i].mDynamicProperty;
            Target &target = mTargets.mTargets[mContinuousDisplay ? index : targets.mTargets[i].mID];
            target = targets.mTargets[i];
            if (mAnalysisFrameType == Frame200Raw) {
                target.mAngle = target.mAngle * 180 / M_PI;
            }
            if( targets.mTargets[i].mAlarmed ){
                //mAlarmID = i;
                mAlarmIDList.push_back( mContinuousDisplay ? index : targets.mTargets[i].mID );
            }
            if (mMonitorTarget == targets.mTargets[i].mID)
            {
                emit monitorTarget(mAnalysisFrameType, targets.mTargets[i]);
                monitor = true;
            }

            mTargets.mTargetCount++;
            index++;
        }
    }
    if (!monitor)
    {
        emit monitorTarget(mAnalysisFrameType, Target{mMonitorTarget});
    }
    endResetModel();
}

void AnalysisModel::changedMonitorTargetID(int frameType, const quint16 id)
{
    qDebug() << __FUNCTION__ << __LINE__ << mAnalysisFrameType << frameType << id;
    mMonitorTarget = (frameType == mAnalysisFrameType) ? id : -1;
}

/*************************************************************************************/
VehicleDataModel::VehicleDataModel(QObject *parent)
    : QAbstractTableModel(parent)
{

}

QVariant VehicleDataModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal)
    {
        switch (role)
        {
        case Qt::DisplayRole:
            return section ? "Value" : "Attribute";
        case Qt::TextAlignmentRole:
            return  Qt::AlignLeft;
        case Qt::SizeHintRole:
            break;
        }
    }
    else
    {
        switch (role)
        {
        case Qt::DisplayRole:
            return section;
        case Qt::SizeHintRole:
            break;
        }
    }

    return QAbstractTableModel::headerData(section, orientation, role);
}

int VehicleDataModel::rowCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }

    return VEHICLE_TYPE_END - VEHICLE_TYPE_BEGIN;
}

int VehicleDataModel::columnCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }
    return 2;
}

QVariant VehicleDataModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid())
    {
        return QVariant();
    }

    int row = index.row();
    switch(role)
    {
    case Qt::DisplayRole:
        return index.column() == 0 ? gAnalysisTypeName((AnalysisType)(row + VEHICLE_TYPE_BEGIN)) : QString::number(mVehicleData.value((AnalysisType)(row + VEHICLE_TYPE_BEGIN)));
    case Qt::SizeHintRole:
        break;
    }

    return QVariant();
}

Qt::ItemFlags VehicleDataModel::flags(const QModelIndex &index) const
{
//    if (!index.isValid())
    return QAbstractItemModel::flags(index);
}

void VehicleDataModel::setVehicleData(const VehicleData &vehicleData)
{
    this->beginResetModel();
    mVehicleData = vehicleData;
    this->endResetModel();
}

/*************************************************************************************/
AlarmDataModel::AlarmDataModel(QObject *parent)
    : QAbstractTableModel(parent)
{

}

QVariant AlarmDataModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal)
    {
        switch (role)
        {
        case Qt::DisplayRole:
            return section ? "Value" : "Attribute";
        case Qt::TextAlignmentRole:
            return  Qt::AlignLeft;
        case Qt::SizeHintRole:
            break;
        }
    }
    else
    {
        switch (role)
        {
        case Qt::DisplayRole:
            return section;
        case Qt::SizeHintRole:
            break;
        }
    }

    return QAbstractTableModel::headerData(section, orientation, role);
}

int AlarmDataModel::rowCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }

    return ALARM_TYPE_END - ALARM_TYPE_BEGIN;
}

int AlarmDataModel::columnCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }
    return 2;
}

QVariant AlarmDataModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid())
    {
        return QVariant();
    }

    int row = index.row();
    switch(role)
    {
    case Qt::DisplayRole:
        return index.column() == 0 ? gAnalysisTypeName((AnalysisType)(row + ALARM_TYPE_BEGIN)) : QString::number(mAlarmData.value((AnalysisType)(row + ALARM_TYPE_BEGIN)));
    case Qt::SizeHintRole:
        break;
    }

    return QVariant();
}

Qt::ItemFlags AlarmDataModel::flags(const QModelIndex &index) const
{
//    if (!index.isValid())
    return QAbstractItemModel::flags(index);
}

void AlarmDataModel::setAlarmData(const AlarmData &alarmData)
{
    this->beginResetModel();
    mAlarmData = alarmData;
    this->endResetModel();
}

/*************************************************************************************/
EndFrameModel::EndFrameModel(QObject *parent)
    : QAbstractTableModel(parent)
{

}

QVariant EndFrameModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal)
    {
        switch (role)
        {
        case Qt::DisplayRole:
            return section ? "Value" : "Attribute";
        case Qt::TextAlignmentRole:
            return  Qt::AlignLeft;
        case Qt::SizeHintRole:
            break;
        }
    }
    else
    {
        switch (role)
        {
        case Qt::DisplayRole:
            return section;
        case Qt::SizeHintRole:
            break;
        }
    }

    return QAbstractTableModel::headerData(section, orientation, role);
}

int EndFrameModel::rowCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }

    return ALARM_TYPE_END - ALARM_TYPE_BEGIN;
}

int EndFrameModel::columnCount(const QModelIndex &parent) const
{
    if (parent.isValid())
    {
        return 0;
    }
    return 2;
}

QVariant EndFrameModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid())
    {
        return QVariant();
    }

    int row = index.row();
    switch(role)
    {
    case Qt::DisplayRole:
        return index.column() == 0 ? gAnalysisTypeName((AnalysisType)(row + END_FRAME_TYPE_BEGIN)) : QString::number(mEndFrameData.value((AnalysisType)(row + END_FRAME_TYPE_END)));
    case Qt::SizeHintRole:
        break;
    }

    return QVariant();
}

Qt::ItemFlags EndFrameModel::flags(const QModelIndex &index) const
{
//    if (!index.isValid())
    return QAbstractItemModel::flags(index);
}

void EndFrameModel::setEndFrameData(const EndFrameData &endFrameData)
{
    this->beginResetModel();
    mEndFrameData = endFrameData;
    this->endResetModel();
}

} // namespace AnalysisDataView
} // namespace Views

