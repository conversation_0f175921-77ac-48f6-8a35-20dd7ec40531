﻿#ifndef PC_VR_CAN_H
#define PC_VR_CAN_H

#ifdef PC_DBG_FW

#include "app/system_mgr/typedefs.h"

#define CAN_0 (0)
#define CAN_1 (1)
//end

//为了兼容77G代码
typedef struct tagCANCtrl
{
    int (*sendFrame)(void *buf, UINT16 ID);
    void (*sendFrameNow)(void *buf, UINT16 ID);
} TCANCtrl;

extern const TCANCtrl gCAN[];
extern TxTargets_t gTxTargets[];


#define USE_GPIO_PWM 0
#define LED_ON 1
#define LED_OFF 0

extern int saveRadarCfg(void);
extern int xTaskGetTickCount(void);
extern int isCanValid(uint32_t dev_id, int cnt);
extern int bzero(void *dat , int len);
extern void system_reset(void);
extern void update_auto_calc_status(uint8_t st);

extern void init_cfg();

extern int mcu_time;
extern int frame_number;

#endif

#endif
