<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BlackBoxTurnTableForm</class>
 <widget class="QWidget" name="BlackBoxTurnTableForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>833</width>
    <height>561</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>10</y>
     <width>791</width>
     <height>30</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QLabel" name="label">
      <property name="text">
       <string>转台IP：</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="turnTableIPLineEdit">
      <property name="maximumSize">
       <size>
        <width>200</width>
        <height>16777215</height>
       </size>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="connectPushButton">
      <property name="text">
       <string>连接转台</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>50</y>
     <width>101</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>转台旋转电机:</string>
   </property>
  </widget>
  <widget class="QPushButton" name="rotateOpenPushButton">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>80</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="text">
    <string>打开</string>
   </property>
  </widget>
  <widget class="QPushButton" name="rotateClosePushButton">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>80</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="text">
    <string>关闭</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
