﻿#pragma once

#include <list>
#include <queue>
#include <mutex>

template <typename T>
class SafeQueue
{
public:
    SafeQueue(unsigned int maxSize = 2000);
    ~SafeQueue();

    void setMaxSize(unsigned int maxSize);
    void clear();

    bool empty() const;
    void push(const T &data);
    void push(const T &&data);

    std::shared_ptr<T> tryPop();
    std::shared_ptr<T> pop();

    int size();

private:
    unsigned int mMaxSize{ 2000 };
    std::queue<T> mDataQueue;
    std::mutex mMutex;
    std::condition_variable mConditionVariable;
};

template<typename T>
inline SafeQueue<T>::SafeQueue(unsigned int maxSize) : mMaxSize(maxSize)
{
}

template<typename T>
inline SafeQueue<T>::~SafeQueue()
{
}

template<typename T>
inline void SafeQueue<T>::setMaxSize(unsigned int maxSize)
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    mMaxSize = maxSize;
}

template<typename T>
inline void SafeQueue<T>::clear()
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    std::queue<T> empty;
    std::swap(empty, mDataQueue);
}

template<typename T>
inline bool SafeQueue<T>::empty() const
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    return mDataQueue.empty();
}

template<typename T>
inline void SafeQueue<T>::push(const T & data)
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    if (mDataQueue.size() >= mMaxSize) {
        mDataQueue.pop();
    }
    mDataQueue.push(data);
    mConditionVariable.notify_one();
}

template<typename T>
inline void SafeQueue<T>::push(const T && data)
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    if (mDataQueue.size() >= mMaxSize) {
        mDataQueue.pop();
    }
    mDataQueue.push(std::move(data));
    mConditionVariable.notify_one();
}

template<typename T>
inline std::shared_ptr<T> SafeQueue<T>::tryPop()
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    // 一直等待到有数据
    mConditionVariable.wait(lg, [this]{ return !mDataQueue.empty(); });
    auto res = std::make_shared<T>(std::move(mDataQueue.front()));
    mDataQueue.pop();

    return res;
}

template<typename T>
inline std::shared_ptr<T> SafeQueue<T>::pop()
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    if (mDataQueue.empty()) {
        return {};
    }
    auto res = std::make_shared<T>(mDataQueue.front());
    mDataQueue.pop();

    return res;
}

template<typename T>
inline int SafeQueue<T>::size()
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    return mDataQueue.size();
}
