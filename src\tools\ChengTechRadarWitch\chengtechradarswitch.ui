<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ChengTechRadarSwitch</class>
 <widget class="QMainWindow" name="ChengTechRadarSwitch">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ChengTechRadarSwitch</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QLabel" name="label">
        <property name="text">
         <string>CAN</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="comboBoxChannelIndex">
        <item>
         <property name="text">
          <string>1</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>2</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>3</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>4</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QPushButton" name="pushButtonRawOpen">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>100</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>25</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>开启原始点</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButtonRawClose">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>100</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>25</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>关闭原始点</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QLabel" name="labelState">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>100</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>25</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>CLOSED</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPlainTextEdit" name="plainTextEditLog"/>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu">
    <property name="title">
     <string>设备</string>
    </property>
    <addaction name="actionOpenDevice"/>
    <addaction name="actionSelectDevice"/>
   </widget>
   <addaction name="menu"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionOpenDevice"/>
   <addaction name="actionSelectDevice"/>
  </widget>
  <action name="actionSelectDevice">
   <property name="text">
    <string>选择设备</string>
   </property>
  </action>
  <action name="actionOpenDevice">
   <property name="text">
    <string>打开设备</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
