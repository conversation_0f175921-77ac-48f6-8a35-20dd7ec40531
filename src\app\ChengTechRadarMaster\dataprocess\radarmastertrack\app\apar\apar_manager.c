﻿/**
 * @file apar_manager.c
 * @brief APAR模块接口实现
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2022-10-02
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-02 <td>1.0     <td><PERSON>     <td>初始版本
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef PC_DBG_FW
#include "apar_manager.h"
#include "flash_mmap_user.h"
#include "crc32.h"
#include "cfg_mgr.h"
#include "radarCfg.h"
#include "queue.h"
#include "string.h"
#include "system_misc.h"

static APAR_CfgData_t radarAparData;

/*
static APAR_CfgData_t radarAparDataDefault =
    {
        .aparInstallAzimuthAngle = APAR_INSTALL_ANGLE_DEFAULT,
        .aparInstallAzimuthAngleOffset = 0,
        .aparStaticALNAzimuthAngle = APAR_INSTALL_ANGLE_DEFAULT,
        .aparStaticALNElevationAngle = 0,
        .aparFmcwStartFreq = APAR_FMCW_START_FREQ,
        .aparRadarInfo = {
            .macaddr = {0x00, 0x04, 0x9F, 0x11, 0x22, 0x33},
            .ipaddr = {192, 168, 1, 60},
            .HwVer = HW_VERSION,
            .SwVer = SW_VERSION,
            // .pcbaBBSN = {"001010063C1002070000x"},
            // .radarSN = {"001010063C1002070000x"},
            // .imuName = {"LSM_6DS33_NULL"},
            .CalVer = {0, 0, 0, 0},
        },
        .aparRadarId = 4,
        .aparSpeedSource = 1,
        .aparTxSelect = 1,
        .aparTxPattern = 0,
        .aparObjExtendInfoSW = 0,
        .aparObjExtendInfoSW2 = 0,
        .aparProtocolVersion = 0,
        .aparRandomId = 0,
        .aparAgingTest = 0,
};
*/
static APAR_SystemParameter_t aparSystemParam;

#define CONFIG_MAGIC_NUM_NEW 0xC5C5C5C5
#define MAX_CFG_AREA_SIZE 0x8000

typedef struct
{
    uint16_t type;
    uint16_t item;
    uint16_t size;
    uint16_t resv;
} APAR_cfgItemInfo_t;



typedef struct
{
    uint8_t followVer; //是否跟随版本
    uint8_t type;
    uint16_t item;
    void *pVar;
    uint16_t offSet;
    uint16_t size;
    uint8_t initok; //是否正常从flash初始化，新加入的数据flash中并没有，需要使用默认值的判断
}radarCfgItem_t;


static int32_t APAR_readAparDataFromFlash(void);
static int32_t APAR_setAparDataDefault();


const APAR_CfgData_t *APAR_getAparDataPtr()
{
    return &radarAparData;
}

int32_t APAR_initAparData(void)
{
    int32_t ret = 0;

    gen_crc_table();

    ret = APAR_readAparDataFromFlash();

    //读取芯片序列号
    /* Read the chip serial number */
    getChipSerialNumber(&aparSystemParam.aparChipSN);

    return ret;
}

uint8_t APAR_getAparTxSelectMode()
{
    return radarAparData.aparTxSelect;
}

float APAR_getAparInstallAzimuthAngle()
{
    return radarAparData.aparInstallAzimuthAngle;
}

uint8_t APAR_getAparRadarId()
{
    return radarAparData.aparRadarId;
}

int32_t APAR_setAparRadarId(uint8_t radarId)
{
    int32_t ret = 0;
    radarAparData.aparRadarId = radarId;
    return ret;
}

static int32_t APAR_readAparDataFromFlash(void)
{
    //计算使用存储长度，使用freeRTOS的内存申请

    int j, offset = 0;
    int i, len, hlen = 0;
    int32_t ret = 0;

    do
    {
        // Step 1 读取header内容
        APAR_commonHeader_t header;
        hlen = sizeof(APAR_commonHeader_t);
        hlen += (hlen % 4); //对齐4byte

        // 读取Header信息
        // ret = flash_memory_readw( CONFIG_ADDR ,  (uint32_t*)(&header) , hlen);
        ret = flash_memory_readb(CONFIG_ADDR, (uint8_t *)(&header), hlen);

        if (ret != 0) 
        {
            //读取失败，将APAR参数设置为默认值
            APAR_setAparDataDefault();
            EMBARC_PRINTF("[%s] [%d]\n", __func__,__LINE__);
            break;
        }

        // Step 2 校验header中的magic num和size信息
        if ((header.magicNum != CONFIG_MAGIC_NUM_NEW) || (header.size >= MAX_CFG_AREA_SIZE))
        {
            // setSmFailed(IDX_ITEM_NO_CFG);

            //magicNum或者长度校验失败，恢复为默认值
            // memcpy((uint8_t *)&radarAparData, (uint8_t *)&radarAparDataDefault, sizeof(radarAparData));
            APAR_setAparDataDefault();
            ret = -1;
            break;
        }
        
        // Step 3 动态申请内存
        len = header.size + 8;
        len += len % 4; // 字为单位长度
        len &= 0xFFFF;  // 最大长度不能大于64k
        EMBARC_PRINTF("loadradarcfg step 1 , len=%d\n", len);
        char *pBuf = (char *)pvPortMalloc(len);

        if (pBuf == NULL)//申请内存失败
        {
            //申请内存失败，使用默认值
            // memcpy((uint8_t *)&radarAparData, (uint8_t *)&radarAparDataDefault, sizeof(radarAparData));
            APAR_setAparDataDefault();
            ret = -2;
            break;
        }

        // Step 4 从Flash中读取所有的信息        
        EMBARC_PRINTF("loadradarcfg step 2\n");
        ret = flash_memory_readb(CONFIG_ADDR, (uint8_t *)pBuf, len); // 全部读完
        if (ret != 0)
        {
            vPortFree(pBuf);
            EMBARC_PRINTF("flash_memory_read error 2\n");
            // 读取失败，使用默认值
            // memcpy((uint8_t *)&radarAparData, (uint8_t *)&radarAparDataDefault, sizeof(radarAparData));
            APAR_setAparDataDefault();
            break;
        }

        // Step 5 对所有数据进行CRC校验
        // header中的size包含了header中的itemNum, size字段以及剩余的eeprom中的配置
        uint32_t crc = update_crc(0, (char *)pBuf + 8, (header.size - 8));
        EMBARC_PRINTF("header.crc=0x%x  crc=0x%x\n", header.crc, crc);
        if (header.crc != crc)
        {
            vPortFree(pBuf);
            // flashCrcErrorFlag = 1;
            // flashHeadErrorFlag = 0; //CRC错误
            // setSmFailed(IDX_ITEM_CFG_CRC);

            // CRC校验失败，使用默认值
            // memcpy((uint8_t *)&radarAparData, (uint8_t *)&radarAparDataDefault, sizeof(radarAparData));
            APAR_setAparDataDefault();
            ret = -3;

            // 重新写一遍Flash
            APAR_writeAparDataToFlash();

            break;
        }

        //只有到这里了说明Flash的内容可以用---否则认为Flash中的内容不可用
        // flashHeadErrorFlag = 0;
        // flashCrcErrorFlag = 0;

        // Step 6 使用数据
        memcpy((uint8_t *)&radarAparData, pBuf, sizeof(radarAparData));
        vPortFree(pBuf);

    } while (0);
    
    // len = 0;
    // for (i = 0; i < CFG_ITEM_NUM; i++)
    // {
    //     len += gCfgItems[i].size;
    // }
    // len = sizeof(APAR_cfgItemInfo_t) * CFG_ITEM_NUM + len + hlen; // 存储数据长度+item的总量+header头数据
 
#if 0 //暂时不需要复杂的逻辑
    APAR_cfgItemInfo_t *item = (APAR_cfgItemInfo_t *)((char *)pBuf + sizeof(APAR_commonHeader_t));
    char *cfgData = (char *)(item + header.itemNum);

    for (i = 0; i < header.itemNum; i++)
    {
        if (item[i].type >= CFG_TYPE_NUM)
        {
            offset += item[i].size;
            continue;
        }

        for (j = 0; j < CFG_ITEM_NUM; j++)
        {
            //type按顺序排列
            if (gCfgItems[j].type > item[i].type)
            {
                break;
            }

            if (item[i].type == gCfgItems[j].type && item[i].item == gCfgItems[j].item && item[i].size == gCfgItems[j].size)
            {
                memcpy((char *)gCfgItems[j].pVar + gCfgItems[j].offSet, cfgData + offset, item[i].size);
                break;
            }
        }

        offset += item[i].size;
    }

    vPortFree(pBuf);

    //跟随版本走的配置要使用版本的默认值
    if (memcmp(radar_config_user.radar_info.SwVer, radar_config_default.radar_info.SwVer, sizeof(radar_config_default.radar_info.SwVer)))
    {
        memcpy(radar_config_user.radar_info.SwVer, radar_config_default.radar_info.SwVer, sizeof(radar_config_default.radar_info.SwVer));

        for (i = 0; i < CFG_ITEM_NUM; i++)
        {
            if (gCfgItems[i].followVer && gCfgItems[i].type < CFG_TYPE_NUM && gDefaultCfgs[gCfgItems[i].type][0])
            {
                memcpy((char *)gCfgItems[i].pVar + gCfgItems[i].offSet,
                       (char *)gDefaultCfgs[gCfgItems[i].type][1] + gCfgItems[i].offSet,
                       gCfgItems[i].size);
            }
        }
    }
#endif
    return ret;
}

int32_t APAR_writeAparDataToFlash(void)
{
    //计算使用存储长度，使用freeRTOS的内存申请
    int i, len = 0;
    int32_t ret = 0;

    do
    {
        // Step 0 计算数据长度，申请内存
        len = sizeof(radarAparData);
        len += len % 4; // 字为单位长度
        len &= 0xFFFF;  // 最大长度不能大于64k
        EMBARC_PRINTF("save --\n");
        char *gBuf = (char *)pvPortMalloc(len);

        if (gBuf == NULL)
        {
            ret = -1;
            break;
        }
        
        // Step 1 拷贝数据至buffer中
        APAR_commonHeader_t *header = (APAR_commonHeader_t *)gBuf;
        header->magicNum = CONFIG_MAGIC_NUM_NEW;
        header->versionNumber = 0;
        header->size = len;
        memcpy(gBuf, (char *)&radarAparData, len);

        // Step 2 计算CRC
        //header中的magic num和crc不计算在内
        header->crc = update_crc(0, ((char *)gBuf) + 8, (header->size - 8));

        //擦除、写入
        eraseConfig(CONFIG_ADDR, 0x4000);

        ret = flash_memory_writeb(CONFIG_ADDR, (const uint8_t *)gBuf, len);
        EMBARC_PRINTF("cfg write addr=0x%x len=0x%x header->crc=0x%x\r\n", (int)gBuf, len, header->crc);
        
        vPortFree(gBuf);

    } while (0);

    return ret;
}

uint32_t APAR_getChipSerialNum()
{
    return aparSystemParam.aparChipSN;
}


static int32_t APAR_setAparDataDefault()
{
    int32_t ret = 0;
    uint8_t macAddr[6] = {0x00, 0x04, 0x9F, 0x11, 0x22, 0x33};
    uint8_t ipAddr[4] = {192, 168, 1, 60};
    uint8_t hardWare[4] = HW_VERSION;
    uint8_t softWare[4] = SW_VERSION;
    uint8_t calibVer[4] = CALIB_VERSION;

    radarAparData.aparInstallAzimuthAngle = APAR_INSTALL_ANGLE_DEFAULT;
    radarAparData.aparInstallAzimuthAngleOffset = 0;
    radarAparData.aparStaticALNAzimuthAngle = APAR_INSTALL_ANGLE_DEFAULT;
    radarAparData.aparStaticALNElevationAngle = 0;
    radarAparData.aparFmcwStartFreq = APAR_FMCW_START_FREQ;
    
    radarAparData.aparRadarId = 4;
    radarAparData.aparSpeedSource = 1;
    radarAparData.aparTxSelect = 1;
    radarAparData.aparTxPattern = 0;
    radarAparData.aparObjExtendInfoSW = 0;
    radarAparData.aparObjExtendInfoSW2 = 0;
    radarAparData.aparProtocolVersion = 4;
    radarAparData.aparRandomId = 0;
    radarAparData.aparAgingTest = 0;

    memcpy((uint8_t*)radarAparData.aparRadarInfo.macaddr,macAddr,sizeof(radarAparData.aparRadarInfo.macaddr));
    memcpy((uint8_t*)radarAparData.aparRadarInfo.ipaddr,ipAddr,sizeof(radarAparData.aparRadarInfo.ipaddr));
    memcpy((uint8_t*)radarAparData.aparRadarInfo.HwVer,hardWare,sizeof(radarAparData.aparRadarInfo.HwVer));
    memcpy((uint8_t*)radarAparData.aparRadarInfo.SwVer,softWare,sizeof(radarAparData.aparRadarInfo.SwVer));
    memcpy((uint8_t*)radarAparData.aparRadarInfo.CalVer,calibVer,sizeof(radarAparData.aparRadarInfo.CalVer));

    return ret;
}

#else
#include "app/apar/apar_manager.h"

static APAR_CfgData_t radarAparData;

APAR_CfgData_t *APAR_getAparDataPtr()
{
    return &radarAparData;
}

// float APAR_getAparInstallAzimuthAngle()
// {
//     return radarAparData.aparInstallAzimuthAngle;
// }
#endif
