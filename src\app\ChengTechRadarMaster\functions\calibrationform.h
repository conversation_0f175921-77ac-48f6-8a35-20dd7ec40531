﻿#ifndef AFTERSALECALIBRATIONFORM_H
#define AFTERSALECALIBRATIONFORM_H

#include <QWidget>
#include <QTimer>

#include "calibrationsworker.h"

class ICanBusInterface;

namespace Ui {
class CalibrationForm;
}

class CalibrationForm : public QWidget
{
    Q_OBJECT

public:
    enum ShowMode{
        CalibrationMode,
        ExitFactoryMode
    };
    explicit CalibrationForm(Devices::Can::DeviceManager *deviceManager, ShowMode m = CalibrationMode, QWidget *parent = nullptr);
    ~CalibrationForm();

signals:
    void start();
    void stop();
//    void read();

private slots:
    void message(const QString message);
    void calibrationStarted();
    void calibrationFinished(bool ok);

    void on_pushButtonStart_clicked();

    void on_pushButtonStop_clicked();

    void on_comboBoxChannelIndex_currentIndexChanged(int index);

    void on_checkBoxCAN_stateChanged(int arg1);

    void on_comboBoxCalibrationType_currentIndexChanged(int index);

private:
    Ui::CalibrationForm *ui;

    ICanBusInterface *mICanBusInterface{0};
    CalibrationWorker *mAfterSaleCalibrationWorker{0};

    ShowMode mShowMode{CalibrationMode};

    QTimer mTimerRunning;

};

#endif // AFTERSALECALIBRATIONFORM_H
