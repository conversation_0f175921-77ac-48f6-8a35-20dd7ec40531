﻿#ifndef CAMERATHREAD_H
#define CAMERATHREAD_H

#include <QObject>
#include <QThread>
#include <QDebug>
#include <QTimer>
#include <QCoreApplication>
#include <QDateTime>
#include <QImage>
#include <QCamera>
#include <QMutex>

#include "opencv2/imgproc/imgproc.hpp"
#include "opencv2/core/core.hpp"
#include "opencv2/objdetect/objdetect.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/opencv.hpp"
#include "opencv2/imgproc/types_c.h"

#define     VIDEO_WIDTH     1280
#define     VIDEO_HIGNT     720

// error:“ACCESS_MASK”: 不明确的符号
// using namespace cv;

class OpenCVCamera : public QObject
{
    Q_OBJECT
public:
    explicit OpenCVCamera(int index, QObject *parent = 0);
    ~OpenCVCamera();
    QImage MatToQImage2(const cv::Mat &mat);

    void rotationAngle(int angle, bool rotation);

    bool isOpened() const;
    bool isSaveing() const;

    void namedWindowToWidget(std::string namedWindowTitle, QWidget * widget);

signals:
    void opened();
    void closed();
    void stoped();
    void stateChanged(QCamera::State state);
    void showImage(QImage frame, int idx);
    void cameraSaveIndex(int cIndex, long long sIndex);

public slots:
    void run();
    void open();
    void close();
    void startSaveVideo(const QString &savePath, quint64 saveTime);
    void startSave(QString pathFileName);
    void stopSave();
    void slotSaveFrameCount(int RawFrameCount, int TraFrameCount, int MeasCounter);

    QCamera::State state() const { return mState; }

public:
    QMutex mMutex;
    int mCameraIndex{-1};
    long long mSaveIndex{-1};
    bool isRunning = false;
    cv::VideoCapture gCapture;
    cv::VideoWriter gSaveVideo;
    int gRawFrameCount = 0;
    int gTraFrameCount = 0;
    int gMeasCounter = 0;
    QDateTime gTime;
    QString gIsRecord = "";
    bool mRotate{false};
    int mRotationAngle{1};
    QCamera::State mState{QCamera::UnloadedState};
};

#endif // CAMERATHREAD_H
