﻿/**
 * @file rdp_kf_track.c
 * @brief 
 * <AUTHOR> (s<PERSON><PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#ifndef PC_DBG_FW
#include <string.h>
#include <math.h>
#include "embARC_debug.h"
#include "rdp_kf_config.h"
#include "rdp_kf_track.h"
#include "rdp_track_config.h"
#include "rdp_clth_radar_lib.h"
#include "rdp_interface.h"
#include "aln_dynamicEolFun.h"
#include "aln_autoEolFun.h"
#include "app_common.h"
#include "vdy.h"
#include "vdy_interface.h"
#ifndef RADAR_AUTOSAR
#include "portmacro.h"
#include "FreeRTOSConfig.h"
#include "uds_network_layer.h"
#else
#include "os_hal.h"
#endif
#include "embARC_error.h"
#include "radar_status.h"
#define STATIC static
#else
#include <string.h>
#include <math.h>
#include "rdp_kf_config.h"
#include "alg/track/rdp_kf_track.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "app/apar/apar_types.h"
#include "alg/track/rdp_interface.h"
#include "../vdy/vdy.h"
#endif

#ifndef M_PI
#define M_PI 3.1415926
#endif
#ifndef RAD2ANG
#define RAD2ANG                  57.2957795131
#endif

extern float gRDP_storedFrameTime[STORE_FRAME_TIME_NUM]; 
extern float binParam[3];
extern float gRangeRes[2];
extern float gVelocityRes[2];
#ifndef PC_DBG_FW
extern float *ptrFFT[4];
#endif
extern UINT16 gRadarFrenceFlag; //bit 0代表全部有效还是无效  bit1-地库flag   bit2-乡村道路 。。。
extern float gAngleOffset;
extern volatile unsigned char pTestCanCli;

float gEstVelocity[2] = {0, 0}; //估算的车速

#ifndef PC_DBG_FW
SHARED_MEM(020) STATIC cdi_pkg_t gRDP_BKTargetsList;	 //RDP内部用于跟踪的原始点全局变量
#else
static cdi_pkg_t gRDP_BKTargetsList;	 //RDP内部用于跟踪的原始点全局变量
#endif

#ifndef PC_DBG_FW
#ifndef RADAR_AUTOSAR
static SemaphoreHandle_t gMutexTrackerList = NULL;  //用于tracking后tracker list数据拷贝过程
#endif
#endif

float grdpInstallAzimuthAngle = 45.0f;
uint8_t isCPTALFScene = 0;
uint8_t fenceCntInBlindArea = 0;
float fencePointLatRange = 0.f;

void setEstVelocity(int type, float velocity)
{
    gEstVelocity[type] = velocity;
}

float getEstVelocity(int type)
{
    return gEstVelocity[type];
}

#ifndef PC_DBG_FW
/*
 * @Description Create the semaphore for tracker list copy.
 *
 * @retval
 *    0   -  Success
 *    <0  -  Error
 */
int32_t RDP_createTrackerListSemaphore(void)
{
#ifndef RADAR_AUTOSAR
    gMutexTrackerList = xSemaphoreCreateMutex();
    if (gMutexTrackerList == NULL)
    {
        EMBARC_PRINTF("Create gMutexTrackerList error!\r\n");
        return E_SYS;
    }

    xSemaphoreGive(gMutexTrackerList);
#endif
    return 0;
}

/*
 * @Description Take the semaphore of tracker list.
 */
void RDP_takeTrackerListSemaphore(void)
{
#ifndef RADAR_AUTOSAR
    xSemaphoreTake(gMutexTrackerList, portMAX_DELAY);
#else
    GetResource(OsResource_3);
#endif
}

/*
 * @Description Give the semaphore of tracker list.
 */
void RDP_giveTrackerListSemaphore(void)
{
#ifndef RADAR_AUTOSAR
    xSemaphoreGive(gMutexTrackerList);
#else
    ReleaseResource(OsResource_3);
#endif
}

#endif

//外部模块获取gRDP_BKTargetsList地址
cdi_pkg_t* RDP_getBKTargetsListPointer()
{
    return &gRDP_BKTargetsList;
}

/**
 * @brief 冒泡排序算法 ->升序
 * @param length 排序样本个数
 * @param prspDetObject 排序样本指针
 */
void RDP_bubblesortRawTargets(int32_t length, RSP_DetObjectInfo_t *prspDetObject)
{
    RSP_DetObjectInfo_t temp; //临时变量
    int32_t i, j, flag = 1, cnt = 5;
    //针对高度有序的检测点序列，采取先至多五遍轮排，后自检的方式，降低运行时耗
    while(cnt && flag)
    {
        flag = 0;
        for (i = 0; i < length - 1; i++)    //轮排
        {
            if (prspDetObject[i].rspDetRange > prspDetObject[i + 1].rspDetRange)
            {
                temp = prspDetObject[i];
                prspDetObject[i] = prspDetObject[i + 1];
                prspDetObject[i + 1] = temp;
                flag = 1;   //存在需要重排的情形
            }
        }
        cnt--;
    }
    if(flag)    //五次轮排仍存在需要重排的情形，执行完整冒泡排序
    {
        for (i = 0; i < length; i++)
        {   //表示趟数，一共length次。
            for (j = length - 1; j > i; j--)
            {
                if (prspDetObject[j].rspDetRange < prspDetObject[j - 1].rspDetRange)
                {
                    temp = prspDetObject[j];
                    prspDetObject[j] = prspDetObject[j - 1];
                    prspDetObject[j - 1] = temp;
                }
            }
        }
    }
}

/**
 * @brief pre-cluster
 * Zhang Jian
 *
 */
 
#define MAX_OBJ_PER_GROUP       8
#ifndef MAX_GROUP_NUM
#define MAX_GROUP_NUM           256 // must less than 256
#endif
/**
 * @brief 原始点小范围聚类，降低关联点的个数
 * @param detNum 聚类前的个数
 * @param prspDetObject 原始点指针
 * @return uint16_t 聚类后的个数
 */
static uint16_t RDP_preclusterRawTargets(uint32_t detNum, RSP_DetObjectInfo_t *prspDetObject)
{
    uint16_t detIdx, detIdx1, groupNum, buffIdx, buffLen, maxValDetIdx;
    uint16_t groupBuff[MAX_OBJ_PER_GROUP], groupBuffDetIdx;
    float deltaRange, deltaAngle, deltaDoppler;
    float maxVal;
    uint16_t groupId, targetGroupId[MAX_GROUP_NUM] = {0};
    rdp_config_t* config = RDP_getTrackConfigPointer();  //获取安装角进行坐标系转换

    for (detIdx = 0, groupId = 1; detIdx < detNum&& groupId <= MAX_GROUP_NUM; detIdx++)
    {
        if (targetGroupId[detIdx] != 0)
        {
            continue;
        }
        targetGroupId[detIdx] = groupId;
        groupBuff[0] = detIdx;
        maxVal = prspDetObject[detIdx].rspDetSNR;
        maxValDetIdx = detIdx;
        for (buffIdx = 0, buffLen = 1; buffIdx < buffLen; buffIdx++)
        {
            if (buffLen >= MAX_OBJ_PER_GROUP)
            {
                break;
            }
            groupBuffDetIdx = groupBuff[buffIdx];
            if (prspDetObject[groupBuffDetIdx].rspDetStatus & POINT_STATUS_ABNORMAL_BMP)
			{
				continue;
			}
            for (detIdx1 = detIdx + 1; detIdx1 < detNum; detIdx1++)
            {
				if (prspDetObject[detIdx1].rspDetStatus & POINT_STATUS_ABNORMAL_BMP)
				{
					continue;
				}
                float tmpAngle = (prspDetObject[detIdx1].rspDetAzimuthAngle + config->installAngle)/ 180 * M_PI;
                float tmpCdi_X = prspDetObject[detIdx1].rspDetRange * sinf(tmpAngle);
                float tmpCdi_Y = prspDetObject[detIdx1].rspDetRange * cosf(tmpAngle);
                if(tmpCdi_X > -0.5f && tmpCdi_X < 1.0f && tmpCdi_Y > 0 && tmpCdi_Y < 5.0f)   //位于雷达近处点，不进行预聚类，让近处的原始点信息更充分
                {
                    continue;
                }
                if (buffLen >= MAX_OBJ_PER_GROUP)
                {
                    break;
                }
                if (targetGroupId[detIdx1] != 0)
                {
                    continue;
                }
                deltaDoppler = fabsf(prspDetObject[groupBuffDetIdx].rspDetVelocity - prspDetObject[detIdx1].rspDetVelocity);
                if (deltaDoppler > 0.5f)
                {
                    continue;
                }
                deltaRange = fabsf(prspDetObject[groupBuffDetIdx].rspDetRange - prspDetObject[detIdx1].rspDetRange);
                if (deltaRange > 0.3f)
                {
                    if (detIdx1 > groupBuffDetIdx)
                    {
                        break;
                    }
                    else
                    {
                        continue;
                    }
                }
                deltaAngle = fabsf(prspDetObject[groupBuffDetIdx].rspDetAzimuthAngle - prspDetObject[detIdx1].rspDetAzimuthAngle)*PI/180;
                DELTA_RAD_NORM(deltaAngle);
                if (deltaAngle < DEG2RAD*2.51f)
                {
                    targetGroupId[detIdx1] = groupId;
                    groupBuff[buffLen] = detIdx1;
                    buffLen++;
                    if (prspDetObject[detIdx1].rspDetSNR > maxVal)
                    {
                        maxValDetIdx = detIdx1;
                        maxVal = prspDetObject[detIdx1].rspDetSNR;
                    }
                }
            }
        }

        memcpy(&prspDetObject[groupId-1], &prspDetObject[maxValDetIdx], sizeof(RSP_DetObjectInfo_t));
        groupId++;
    }
    groupNum = groupId - 1;
    return groupNum;
}

/**
 * @brief 对gRawTargets的原始点进行谐波处理，滤除疑似谐波的假点
 * @param pRDP_RawTargets RDP内部用于跟踪的原始点全局变量指针
 */
static void detectHarmonic(cdi_pkg_t *pRDP_RawTargets, VDY_DynamicEstimate_t *pRDP_inVehicleData, uint8_t isFront)
{
    const float multi[3] = { 2.f, 3.f, 4.f };
    const float rangeCoef[3] = { 0.1f, 0.1f, 0.15f };
    float velTol = 2.f, angleTol = 1.f;
    float rangeTol;
    float rangeUp[3], rangeDown[3];
    float velDouble;
    cdi_t *pCdi, *pCdi2;

    for (uint32_t i = 0; i < pRDP_RawTargets->number; i++)
    {
        pCdi = &pRDP_RawTargets->cdi[i];
        if (pCdi->mea_z[1] > 50)    // 只处理50m内的谐波假点
        {
            break;
        }

        //自车低速运动时，近处异侧的与车速成两倍速度关系的点，大概率为地库的谐波假点
        if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 3.f
            && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
            && fabsf(pRDP_inVehicleData->vdyCurveRadius) > 800.f
            && (pCdi->status & POINT_STATUS_DYNAMIC_BMP)
            && pCdi->mea_z[1] < 10 && pCdi->x < 0.f
            && fabsf(pCdi->mea_z[2] - 2 * pRDP_inVehicleData->vdySpeedInmps) < 0.2f)
        {
            pCdi->valid = false;
            continue;
        }
        #ifdef ALGORITHM_GEELY
        //自车低速运动时，与车速成两倍/三倍速度关系的点且snr低于45，大概率为地库/墙壁的谐波假点
        else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 3.f
            && fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
            && (pCdi->status & POINT_STATUS_DYNAMIC_BMP)
            && pCdi->mea_z[1] < 20.f && pCdi->mea_z[0] < 45.f
            && fabsf(pCdi->mea_z[2]) > fabsf(pRDP_inVehicleData->vdySpeedInmps)
            && ((fabsf(pCdi->mea_z[2] - 2 * pRDP_inVehicleData->vdySpeedInmps) < 0.5f)
                || (fabsf(pCdi->mea_z[2] - 3 * pRDP_inVehicleData->vdySpeedInmps) < 0.5f)))
        {
            pCdi->valid = false;
            continue;
        }
        #endif // ALGORITHM_GEELY
        // 近距离谐波处理放大角度门限
        if (pCdi->mea_z[1] < 10)
        {
            angleTol = 8.f;
        }
        else if (pCdi->mea_z[1] < 20)
        {
            angleTol = 4.f;
        }
        else if (pCdi->mea_z[1] < 30)
        {
            angleTol = 3.f;
        }

        // 低速目标调整速度门限
        if (fabsf(pCdi->mea_z[2]) < 1.f)
        {
            velTol = isFront ? 0.1f : 0.25;	// 地库场景下容易出现速度超0.1门限的情况，适当扩大（并非只在R档下出现）
        }
        else if (fabsf(pCdi->mea_z[2]) < 4.f)
        {
            velTol = fabsf(pCdi->mea_z[2]) / 4;
        }

        for (int k = 0; k < 3; k++)
        {
            rangeTol = pCdi->mea_z[1] * multi[k] * rangeCoef[k];
            rangeUp[k] = pCdi->mea_z[1] * multi[k] + rangeTol;
            rangeDown[k] = pCdi->mea_z[1] * multi[k] - rangeTol;
        }

        velDouble = pCdi->mea_z[2] * 2.f;

        for (uint32_t j = i + 1; j <= pRDP_RawTargets->number; j++)
        {
            pCdi2 = &pRDP_RawTargets->cdi[j];
            if (!pCdi2->valid)
            {
                continue;
            }

            if (pCdi2->mea_z[1] > rangeUp[2])
            {
                break;
            }

            if ((!(pCdi->status & POINT_STATUS_DYNAMIC_BMP) && fabsf(pCdi->mea_z[2]) < 0.1f) || fabsf(pCdi2->mea_z[2]) < fabsf(pCdi->mea_z[2]))
            {
                continue;
            }
            else if (fabsf(pCdi->mea_z[2]) > 0.2f && pCdi2->mea_z[2] / pCdi->mea_z[2] < 1.7f)
            {
                continue;
            }

            for (int k = 0; k < 3; k++)
            {
                if (pCdi2->mea_z[1] < rangeDown[k])
                {
                    break;
                }

                if (pCdi2->mea_z[1] > rangeUp[k])
                {
                    continue;
                }

                // 相对速度小且远距离的目标暂时只考虑二次谐波
                if (fabsf(pCdi->mea_z[2]) < 2.f && pCdi->mea_z[1] > 10.f)
                {
                    if (fabsf(pCdi->mea_z[3] - pCdi2->mea_z[3]) < angleTol
                            && fabsf(velDouble - pCdi2->mea_z[2]) < velTol
                            && pCdi2->mea_z[1] > rangeDown[0]
                            && pCdi2->mea_z[1] < rangeUp[0])
                    {
                        pCdi2->valid = false;
                        break;
                    }
                }
                else
                {
                    if (fabsf(pCdi->mea_z[3] - pCdi2->mea_z[3]) < angleTol && fabsf(pCdi->mea_z[2] * multi[k] - pCdi2->mea_z[2]) < velTol)
                    {
                        pCdi2->valid = false;
                        break;
                    }
                }
            }
        }
    }
}
/**
 * @brief 从前端检测到的R、V、A信息转换到笛卡尔坐标系（根据安装角度进行了旋转），
 * 存储到gRawTargets数据结构中；
 * 变换后数据的坐标系：x轴指向车辆左侧，y轴指向后方
 *
 */
#define DYNAMIC_RADIAL_VELOCITY_THR_LOW		(float)(2.f / 3.6)
#define DYNAMIC_RADIAL_VELOCITY_THR_HIGH	(float)(8.f / 3.6)
#define DYNAMIC_RADIAL_VELOCITY_THR_CPTALF	(float)(3.f / 3.6)

static float angleTransformNormal(float radarAngle, float installAngle)
{
    float vehicleAngle;
    vehicleAngle = radarAngle + installAngle;
    return vehicleAngle;
}
static float angleTransformReversed(float radarAngle, float installAngle)
{
    float vehicleAngle;
    vehicleAngle = -radarAngle + installAngle;
    return vehicleAngle;
}

/**
 * 判断点迹是否位于AEB鬼探头区域.
 */
static bool RDP_aebAreaCheck(trk_pkg_t* pRDP_TrackTargets, cdi_t *pCdi)
{
    bool rst = false;

    if ((pCdi->x > 0.0f) && fabsf(pCdi->x - pRDP_TrackTargets->aebsidecar_x) < 1.0f && 
        (pCdi->y > 2.0f) && (pCdi->y - pRDP_TrackTargets->aebsidecar_y > 1.0f) && (pCdi->y - pRDP_TrackTargets->aebsidecar_y < 6.0f))
    {
        rst = true;
    }
    return rst;
}

/***
 * AEB 标准横穿区域检测. 检测原始点是否位于自车侧前方一定区域.
 */
static bool RDP_aebstandcrossAreaCheck(trk_pkg_t* pRDP_TrackTargets, cdi_t *pCdi)
{
    bool rst = false;
    if ((pCdi->x > 1.0f) && (pCdi->x < 5.0f) && (pCdi->y > 15.0f) && (pCdi->y < 40.0f))
    {
        rst = true;
    }
    return rst;    
}

#define SELF_VELOCITY_DELAY_COMP    0.5f    //a = 2.5fm/s2, delaytime = 200ms
/**
 * @brief 对TargetsList的原始点处理包括坐标系转换、过滤后，拷贝入gRawTargets，作为跟踪处理用的原始点
 * @param pRDP_DetObjectList RDP内部维护的检测目标全局变量指针
 * @param pGroupInfo 组指针
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针
 * @param pRDP_RawTargets RDP内部用于跟踪的原始点全局变量指针
 */
#define SNR_FLITER_THRESHOLD    20.f
#define RCS_FLITER_THRESHOLD    -15.f // RCS优化后的门限//#define RCS_FLITER_THRESHOLD    -25
#define ANG_FLITER_THRESHOLD    10.f
static void RDP_getRawTargets(trk_pkg_t* pRDP_TrackTargets, RSP_DetObjectList_t *pRDP_DetObjectList, TrackGroupInfo_t *pGroupInfo
                            , VDY_DynamicEstimate_t *pRDP_inVehicleData, cdi_pkg_t *pRDP_RawTargets)
{
    //将数据从gTargetsList拷贝到gRawTargets
    //gTargetsList包含的是雷达检测到的数据
    //gRawTargets是后续计算用的数据，已经将极坐标转换为笛卡尔坐标系，也包含了原始数据信息
    int j = 0;
    float angle;
    cdi_t *pCdi = pRDP_RawTargets->cdi;
    int i = 0;
    float cosAngle, sinAngle;
    float radialVelocityThr, radialVelocityThrCur, radialVelocityThrLow, radialVelocityDiff;
    float turningRadius = fabsf(pRDP_inVehicleData->vdyCurveRadius);
    float steeringangle = fabsf(pRDP_inVehicleData->vdySteeringAngle);

	const stVehicleStatus *pVdy = getVdyStatus();
    GTrack_ListObj* groupNoneList = &pGroupInfo[GROUP_ID_NONE].pointList;
    rdp_config_t* config = RDP_getTrackConfigPointer();
    Fun_angleTransform angleTransform;
    float rangeRateScopeCur = 0, rangeRateScopeCurRecip = 0;
    float SNRthr;
    float rcsThr = (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (20 / 3.6f)) ? (-24.5f) : (-19.5f);
	uint8_t isFencePoint = 0;   // 是否存在护栏点

	// 判断是否为CPTALF场景
	isCPTALFScene = 0;
	if (config->installPosition == SENSOR_POSITION_FRONT_LEFT
		&& turningRadius < 50.f
		&& pRDP_inVehicleData->vdyGearState == 4
		&& fabsf(pRDP_inVehicleData->vdySpeedInmps) > (float)(8 / 3.6f)) // FCTA/FCTB的启动速度是10kph，留有余量
	{
		isCPTALFScene = 1;
	}

	// 获取车身的横纵向速度
	float vcsLngVel = 0.f, vcsLatVel = 0.f;
	if (config->isFront)
	{
		vcsLngVel = pVdy->vcsLngVel;
		vcsLatVel = (config->installPosition == SENSOR_POSITION_FRONT_RIGHT) ? -pVdy->vcsLatVel : pVdy->vcsLatVel;
	}
	else
	{
		vcsLngVel = -pVdy->vcsLngVel;
		vcsLatVel = (config->installPosition == SENSOR_POSITION_REAR_RIGHT) ? -pVdy->vcsLatVel : pVdy->vcsLatVel;
	}

    /*
    if(config->upsideDown == 0)
    {
        if(config->isFront)
        {
            angleTransform = angleTransformReversed;
        }
        else
        {
            angleTransform = angleTransformNormal;
        }
    }else
    {
        if(config->isFront)
            angleTransform = angleTransformNormal;
        else
        {
            angleTransform = angleTransformReversed;
        }
    }
    */
    angleTransform = angleTransformNormal;
    //EMBARC_PRINTF("start match...\n");
    // qDebug() << QString("test");
    if(pRDP_DetObjectList->rspCurFrameMode < config->numSubFrame)
    {
        rangeRateScopeCur = config->dopplerVelocityScope[pRDP_DetObjectList->rspCurFrameMode];
        if(rangeRateScopeCur > 0.1f)
            rangeRateScopeCurRecip = 1.f/rangeRateScopeCur;
    }else
    {
#ifndef PC_DBG_FW
        EMBARC_PRINTF("rspCurFrameMode out of range\n");
#endif
        return;
    }

    //计算检测点运动最低门限
    if(fabsf(pRDP_inVehicleData->vdySpeedInmps) > (10.f/3.6f))
    {
        radialVelocityThrLow = fabsf(pRDP_inVehicleData->vdySpeedInmps)*0.05f + 0.1f;
    }
    else
    {
        radialVelocityThrLow = 0.1f;
    }

    //判断目标运动门限动态计算：自车静止时,最小，自车运动时，动态计算，在转弯时放大
    if(fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f)
    {
        //自车起步时，放大动静门限，避免速度延迟导致动静判断出错
        radialVelocityThr = fabsf(pRDP_inVehicleData->vdyAccelLong) > 0.3f ? 0.5f : 0.2f;
    }
    else
    {
        //转弯时速度会有误差，针对转弯放大门限,10以下和25m以下但车速大于10kph 认为基本是在极限调头了，暂不考虑功能，最大门限抑制点变运动，TODO:需要关注下是否影响FCTA/FCTB功能
        if(turningRadius < 10)
        {
            radialVelocityThr = DYNAMIC_RADIAL_VELOCITY_THR_HIGH;
            radialVelocityThrLow = DYNAMIC_RADIAL_VELOCITY_THR_LOW;
        }
        else if(turningRadius < 25.f)
        {
            if(fabsf(pRDP_inVehicleData->vdySpeedInmps) > (10/3.6f) ) //FCTA/FCTB的启动速度是10kph
            {
                radialVelocityThr = DYNAMIC_RADIAL_VELOCITY_THR_HIGH;
            }
            else
            {
                radialVelocityThr = (fabsf(pRDP_inVehicleData->vdySpeedInmps))*(1+1.f/turningRadius)*0.15f + DYNAMIC_RADIAL_VELOCITY_THR_LOW*2 + fabsf(config->speedDiff);
            }
            radialVelocityThrLow = DYNAMIC_RADIAL_VELOCITY_THR_LOW;
        }
        else if(turningRadius < 100.0f)
        {
            radialVelocityThr = (fabsf(pRDP_inVehicleData->vdySpeedInmps))*0.05f + DYNAMIC_RADIAL_VELOCITY_THR_LOW/2 + fabsf(config->speedDiff);
            radialVelocityThr *= 1.5f;
        }
        else
        {
            if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (30 / 3.6f))
            {
                radialVelocityThr = (fabsf(pRDP_inVehicleData->vdySpeedInmps)) * 0.05f + DYNAMIC_RADIAL_VELOCITY_THR_LOW / 2 + fabsf(config->speedDiff);
            }
            else
            {
                radialVelocityThr = 0.4f;           // 基础阈值保持不变.  场景识别时特殊修改
            }
        }
    }

    //最大门限限定
    if(radialVelocityThr > DYNAMIC_RADIAL_VELOCITY_THR_HIGH)
    {
        radialVelocityThr = DYNAMIC_RADIAL_VELOCITY_THR_HIGH;
    }

    for (i = 0; i < pRDP_DetObjectList->rspDetObjectNum ; i++)
    {

        if (pRDP_DetObjectList->rspDetObject[i].rspDetRange < 0.3f || fabsf(pRDP_DetObjectList->rspDetObject[i].rspDetVelocity) > 55.6f )
        {
            continue;
        }
        //根据实测数据低径向速度假点位置及SNR分析，微调上述假点剔除门限：①r=0~10m，SNR=39-1.4r；②r=10~12m，SNR=25
        if(fabsf(pRDP_DetObjectList->rspDetObject[i].rspDetVelocity) < 0.1f)
        {
            if(pRDP_DetObjectList->rspDetObject[i].rspDetRange < 10.0f)
            {
                //距离分辨率模式下需要降低下SNR过滤门限，否则角反有时候容易被滤掉
                if(RDP_getTrackConfigPointer()->radarResolutionTestMode == TEST_MODE_RESOLUTION)
                {
                   SNRthr = 20.0f - 1.4f * pRDP_DetObjectList->rspDetObject[i].rspDetRange; 
                }
                else
                {
                    SNRthr = 39.0f - 1.4f * pRDP_DetObjectList->rspDetObject[i].rspDetRange;
                }
                if(pRDP_DetObjectList->rspDetObject[i].rspDetSNR < SNRthr)
                {
                    continue;
                }
            }
            else if(pRDP_DetObjectList->rspDetObject[i].rspDetRange < 12.0f && pRDP_DetObjectList->rspDetObject[i].rspDetSNR < 25.0f)
            {
                continue;
            }
        }
        else if(pRDP_DetObjectList->rspDetObject[i].rspDetRange < 2.0f && pRDP_DetObjectList->rspDetObject[i].rspDetSNR < 25.0f)
        {
            continue;
        }
        else if(pRDP_DetObjectList->rspDetObject[i].rspDetRange < 10.f
                && pRDP_DetObjectList->rspDetObject[i].rspDetSNR < SNR_FLITER_THRESHOLD
                && pRDP_DetObjectList->rspDetObject[i].rspDetRCS < RCS_FLITER_THRESHOLD
                && fabsf(pRDP_DetObjectList->rspDetObject[i].rspDetElevationAngle) > ANG_FLITER_THRESHOLD)
        {
            continue;
        }
        // 自车运动，近处低SNR标记为0x40的非0速点直接滤除
        if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.1f
            && pRDP_DetObjectList->rspDetObject[i].rspDetRange < 1.f
            && pRDP_DetObjectList->rspDetObject[i].rspDetStatus & POINT_STATUS_ABNORMAL_BMP
            && pRDP_DetObjectList->rspDetObject[i].rspDetSNR < 50.f)
        {
            continue;
        }
        // 自车运动，满足近距离、低RCS、相对0速、0x40标识的检测点（多为保杠假点）直接滤除---暂不考虑自车运动状态
        // TODO：需要测试行人等弱目标是否会被误删
        if (pRDP_DetObjectList->rspDetObject[i].rspDetRange < 7.5f
            && pRDP_DetObjectList->rspDetObject[i].rspDetRCS < rcsThr
            && pRDP_DetObjectList->rspDetObject[i].rspDetSNR < 50.f
            && fabsf(pRDP_DetObjectList->rspDetObject[i].rspDetVelocity) < 0.1f
            && pRDP_DetObjectList->rspDetObject[i].rspDetStatus & POINT_STATUS_ABNORMAL_BMP)
        {
            continue;
        }

        pCdi->mea_z[3] = angleTransform(pRDP_DetObjectList->rspDetObject[i].rspDetAzimuthAngle, config->installAngle);
        angle = pCdi->mea_z[3] / 180 * M_PI;  // 角度转弧度
        pCdi->x = pRDP_DetObjectList->rspDetObject[i].rspDetRange * sinf(angle);
        cosAngle = cosf(angle);
		sinAngle = sinf(angle);
        pCdi->y = pRDP_DetObjectList->rspDetObject[i].rspDetRange * cosAngle;

        // 自车静止时，0x40过滤；邻车道20m内的近距离且相对静止的点（保杆假点），0x40过滤，其余情况不过滤；剩下其余情况过滤
        /*if (pRDP_DetObjectList->rspDetObject[i].rspDetStatus & POINT_STATUS_ABNORMAL_BMP)
        {
            if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f)
            {
                continue;
            }
            else if (pCdi->x > 0.9f && pCdi->x < 0.9f + LANE_WIDTH && pCdi->y < 20)
            {
                if (pCdi->y < 1.f && fabsf(pRDP_DetObjectList->rspDetObject[i].rspDetVelocity) < 0.1f)
                {
                    continue;
                }
                else
                {
                    ;
                }
            }
        }
        else */if (pRDP_DetObjectList->rspDetObject[i].rspDetSNR < 30.f
            && fabsf(pRDP_DetObjectList->rspDetObject[i].rspDetVelocity) < 0.3f
            && pRDP_DetObjectList->rspDetObject[i].rspDetRange < 2.5f)
        {
            continue;
        }
		else if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > 10 / 3.6f
			&& pRDP_DetObjectList->rspDetObject[i].rspDetSNR < 40.f
			&& fabsf(pRDP_DetObjectList->rspDetObject[i].rspDetVelocity) > 1.f
			&& pCdi->x > 0.f && pCdi->x < 0.5f
			&& pCdi->y > 0.f && pCdi->y < 0.5f)
		{
			// 自车运动，近处低SNR的较大速度点直接删除
			continue;
		}

        pCdi->valid = 1;
        pCdi->property = 0;
        pCdi->index = FILTER_NON_ASSOC;
        pCdi->index2 = FILTER_NON_ASSOC;
        pCdi->index3 = FILTER_NON_ASSOC;
        pCdi->status = pRDP_DetObjectList->rspDetObject[i].rspDetStatus;
        pCdi->mea_z[0] = pRDP_DetObjectList->rspDetObject[i].rspDetSNR;
        pCdi->mea_z[1] = pRDP_DetObjectList->rspDetObject[i].rspDetRange;
        pCdi->mea_z[2] = pRDP_DetObjectList->rspDetObject[i].rspDetVelocity;
        pCdi->groupId = pCdi->groupId2 = GROUP_ID_NONE;
        pCdi->rcs = (int16_t)(pRDP_DetObjectList->rspDetObject[i].rspDetRCS * RCS_SCALE);
        pCdi->DetIdx = pRDP_DetObjectList->rspDetObject[i].rspRawIdx;
        pCdi->shelterFlag = 0;
        pCdi->shelterAreaAngle[0] = 0.0f;
        pCdi->shelterAreaAngle[1] = 0.0f;
		pCdi->fence = 0;

        for (int i = 0; i < 2; i++)
        {
            pCdi->latLim[i] = pCdi->x;
            pCdi->lngLim[i] = pCdi->y;
        }
        
        /* judge the point is moving or not */
		// 暂时只针对CPTALF场景
		if (isCPTALFScene && pCdi->x > 0.f && pCdi->x < 15.f && pCdi->y < 50.f)
		{
			radialVelocityDiff = (vcsLngVel * cosAngle + vcsLatVel * sinAngle + pCdi->mea_z[2]);
			radialVelocityThrCur = DYNAMIC_RADIAL_VELOCITY_THR_CPTALF;
		}
		else
		{
			radialVelocityDiff = (pRDP_inVehicleData->vdySpeedInmps * cosAngle - pCdi->mea_z[2]);
            // 动静门限阈值针对AEB遮挡场景特殊处理. 
            if ((1 == config->isFront) && (pRDP_TrackTargets->aebsidecarsence))
            {
                if ((fabsf(pRDP_inVehicleData->vdySpeedInmps) > 2.0f) && (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 4.0f) 
                    && (RDP_aebAreaCheck(pRDP_TrackTargets, pCdi)) && (radialVelocityDiff > 0.25f))
                {
                    radialVelocityThrCur = 0.25f;
                }
                else
                {
                    radialVelocityThrCur = radialVelocityThr;
                }
            }
            // AEB无遮挡标准横穿.  降低动静门限. 需辅助位移判断.  只针对特定区域降低动静门限. 
            else if ((1 == config->isFront) && (pRDP_TrackTargets->aebstandcrosssence))
            {
                if (RDP_aebstandcrossAreaCheck(pRDP_TrackTargets, pCdi) && (radialVelocityDiff > (radialVelocityThr * 0.6f)))
                {
                    radialVelocityThrCur = (radialVelocityThr * 0.6f);
                }
                else
                {
                    radialVelocityThrCur = radialVelocityThr;
                }
            }
            else
            {
			    radialVelocityThrCur = radialVelocityThr;
            }
			pCdi->otgVel = radialVelocityDiff;
		}

        float tempAngle = fabsf(pCdi->mea_z[3]) > 180.0f? 180.0f : fabsf(pCdi->mea_z[3]);
        tempAngle = tempAngle > 90.0f? 180.0f - tempAngle : tempAngle;  //set tempAngle to [0,90]
        if(tempAngle > 45.0f)       //横向位置检测点速度估计偏差较大，门限值略提高
        {
            float AngleComp = (tempAngle - 45.0f) / 45.0f;   //补偿系数0~1
            radialVelocityThrCur += fabsf(pRDP_inVehicleData->vdySpeedInmps) * 0.05f * AngleComp;
        }

        //如果是转弯半径很小，且20m内小SNR的目标，门限继续放大,原因是在掉头的时候发现有些角度测错的目标，改善可以去掉该策略
        if(turningRadius < 10 && pCdi->mea_z[0] < 18 && pCdi->mea_z[1] < 20)
        {
            radialVelocityThrCur = 1.5f * radialVelocityThrCur;
        }

        if(pCdi->status&POINT_STATUS_DEBLUR_FAILED)
        {
            int16_t q = roundf(radialVelocityDiff*rangeRateScopeCurRecip);
            radialVelocityDiff -= q*rangeRateScopeCur;  // uniformed to [-Vmax/2:Vmax/2]
            if((config->isFront && cosAngle > 0) || (!config->isFront && cosAngle < 0))
                radialVelocityDiff = -radialVelocityDiff;
            if(radialVelocityDiff > radialVelocityThrCur)
            {
                pCdi->status |= POINT_STATUS_DYNAMIC_BMP;
            }
            else if(radialVelocityDiff < -radialVelocityThrCur)
            {
                pCdi->status |= POINT_STATUS_DYNAMIC_BMP;
                pCdi->status |= POINT_STATUS_REVERSE_BMP;
            }
        }
        else
        {
            if((config->isFront && cosAngle > 0) || (!config->isFront && cosAngle < 0))
            {
                radialVelocityDiff = -radialVelocityDiff;
            }
            if(radialVelocityDiff > radialVelocityThrCur)
            {
                pCdi->status |= POINT_STATUS_DYNAMIC_BMP;
            }
            else if(radialVelocityDiff < -radialVelocityThrCur)
            {
                pCdi->status |= POINT_STATUS_DYNAMIC_BMP;
                pCdi->status |= POINT_STATUS_REVERSE_BMP;
            }
        }

        /*
        * DEADZONE：该区域静态目标与运动目标在速度维不易区分
        */
		if (fabsf(pCdi->mea_z[3] - 90.f) < 15.f && pCdi->x < 0.9f + 3.75f * 2.f)
        {
            pCdi->status |= POINT_STATUS_IN_DEADZONE_BMP;
        }
        
        if(radialVelocityDiff > radialVelocityThrLow)
        {
            pCdi->status |= POINT_STATUS_DYNAMIC_KEEP_BMP;
        }
        else if(radialVelocityDiff < -radialVelocityThrLow)
        {
            pCdi->status |= POINT_STATUS_DYNAMIC_KEEP_BMP;
        }

        if(fabsf(radialVelocityDiff) > radialVelocityThrCur + SELF_VELOCITY_DELAY_COMP)
        {
            pCdi->status |= POINT_STATUS_DYNAMIC_CERTAIN_BMP;
        }
        
        // 针对护栏谐波场景，统计护栏点
        if (!isFencePoint)
        {
            if (fabsf(pRDP_inVehicleData->vdySpeedInmps) > (10 / 3.6f)
                //&& fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
                && pRDP_inVehicleData->vdyGearState == GEAR_SIG_D
                && !(pCdi->status & POINT_STATUS_DYNAMIC_BMP)
                && fabsf(pCdi->mea_z[3] - 90.f) < 5.f
                && pCdi->x > 0.5f && pCdi->x < 3.75f && fabsf(pCdi->y) < 0.5f
                && pCdi->otgVel < 0.3f
                && pCdi->mea_z[0] > 40.f)
            {
                fencePointLatRange = pCdi->x;
                isFencePoint = 1;
            }
        }

        /* 转移到笛卡尔坐标处理，左雷达转到第一象限，右雷达转第二象限，
        后面在进行处理的时候，需统一转移到第3象限处理(后期可能还需要统一整个坐标) */
        pCdi->heighAngle = pRDP_DetObjectList->rspDetObject[i].rspDetElevationAngle;
        //pCdi->centerOffset = pCdi->x;
        //TODO:&gPointListElem[j] 定义为静态全局变量，封装一层函数用于访问
        gtrack_listEnqueue (groupNoneList, &gPointListElem[j]);
        j++;
        pCdi++;

        #ifdef PC_DBG_FW
//        EMBARC_PRINTF("index=%d,x=%.2f,y=%.3f,vd=%.2f\n", j, pCdi->x, pCdi->y, pCdi->mea_z[2]);
        #endif
    }

    if (isFencePoint)
    {
        fenceCntInBlindArea = (fenceCntInBlindArea < 20) ? (fenceCntInBlindArea + 1) : fenceCntInBlindArea;
    }
    else
    {
        fenceCntInBlindArea = (fenceCntInBlindArea > 0) ? (fenceCntInBlindArea - 1) : fenceCntInBlindArea;
    }

    //剩下的pCdi初始化
    for (i = j; i < MAX_NUM_OF_POINTS; i++)
    {
        pCdi->valid = 0;
        pCdi++;
    }

    pRDP_RawTargets->number = j;
    pRDP_RawTargets->raw_number = j;

    //谐波判断
    detectHarmonic(pRDP_RawTargets, pRDP_inVehicleData, config->isFront);

    // #if DEBUG_RD_TARGET
    // EMBARC_PRINTF("pRDP_RawTargets->number = %d \n",pRDP_RawTargets->number);
    // #endif
    return;
}

void MarkTargetType(trk_t *pObj)
{
    #if 0
    float carVel = gEstVelocity[1];
    float velDiff = STATIC_VEL_DIFF * gVelocityRes[0];
    float trkVy = TRK_VY(*pObj);

    if (carVel < gVelocityRes[0])
    {
        velDiff = gVelocityRes[0];
    }

    if (pObj->idx_1 != FILTER_NON_ASSOC)
    {
        if (CHECK_PROPERTY(gRDP_BKTargetsList.cdi[pObj->idx_1].property, ZERO_VEL))
        {
            if (pObj->zeroVelCnt < 255)
            {
                pObj->zeroVelCnt++;
            }
        }
        else if (pObj->zeroVelCnt > 0)
        {
            pObj->zeroVelCnt--;
        }

        if (fabsf(trkVy + carVel) > SORT_UNMOVE_THRESHOLD)
        {
            pObj->SortFlag.bit.unMoveFlag = 0;
        }
    }

    //判断是否为横向运动目标
    if (pObj->zeroVelCnt <= 3 && fabsf(pObj->x[2]) > 0.6f && fabsf(pObj->x[2]) > fabsf(pObj->x[3]) * 3)
    {
        pObj->objType = CROSS_OBJ;
    }
    //标记静止目标类型，判断是本车道的还是隔壁车道的
    else if (pObj->zeroVelCnt >= 4 || (pObj->sim_z[2] < gVelocityRes[0] && fabsf(trkVy + carVel) < velDiff))
    {
        if (pObj->objType == NORMAL_OBJ || pObj->objType == CROSS_OBJ)
        {
            pObj->objType = STATIC_OBJ;
        }

        if (pObj->idx_1 != FILTER_NON_ASSOC)
        {
            if (fabsf(gRDP_BKTargetsList.cdi[pObj->idx_1].centerOffset) < 1.2f)
            {
                pObj->forwardStaticCnt++;
            }
            else
            {
                pObj->fenceCnt++;
            }

            if (pObj->type == TRACK && (pObj->objType == STATIC_OBJ || pObj->objType == FORWARD_STATIC_OBJ || pObj->objType == FENCE_OBJ))
            {
                if (pObj->fenceCnt >= 6)
                {
                    pObj->objType = FENCE_OBJ;
                    pObj->forwardStaticCnt = 0;
                }
                else if (pObj->forwardStaticCnt >= 4)
                {
                    pObj->objType = FORWARD_STATIC_OBJ;
                    pObj->fenceCnt = 0;
                }
            }
        }
    }
    else if (pObj->objType <= STATIC_OBJ)
    {
        pObj->objType = NORMAL_OBJ;
        pObj->forwardStaticCnt = 0;
        pObj->fenceCnt = 0;
    }

    //过滤非本车道静止目标
    if ((gTrkCfg.filterType & FILTER_STATIC_OBJ) && pObj->SortFlag.bit.unMoveFlag && pObj->objType == FENCE_OBJ)
    {
        pObj->type = CANDI;
    }
    #endif
}

/**
 * @brief //帧周期异常检测，存储过去20帧帧周期
 * @param time 当前帧帧周期地址
 * @param storeTime 帧周期存储地址
 */
void RDP_frameCycleManage(float *time, float *storeTime)
{
    if(*time < 0.005f)      //帧周期下限检测，低于下限则赋值50ms
    {
        *time = 0.05f;          
    }    
    memmove((storeTime + 1), storeTime, sizeof(float) * (STORE_FRAME_TIME_NUM - 1));
    *storeTime = *time;
}

/**
 * @brief //将VDY输出与RSP输出分别拷贝至RDP内部维护的全局变量
 * @param pRSP_DetObjectList_t RSP输出地址
 * @param pfreezedVehDyncData VDY车身动态数据地址
 * @param pRDP_TrkObjectList RDP跟踪列表地址 
 * @param pRDP_DynamicEstimate RDP内部维护的车身信息全局变量
 * @param pRDP_DetObjectList RDP内部维护的检测目标全局变量
 */
void RDP_getDetections(const RSP_DetObjectList_t *pRSP_DetObjectList_t,
                       const VDY_DynamicEstimate_t *pfreezedVehDyncData,
                       RDP_TrkObjectList_t *pRDP_TrkObjectList,
                       VDY_DynamicEstimate_t *pRDP_DynamicEstimate,
                       RSP_DetObjectList_t *pRDP_DetObjectList)
{
    int i, j;
    int tx1_cnt = 0, tx2_cnt = 0;
    int * p_cnt = &tx1_cnt;
    u8 nomoveflag = 0;
    u8 tx = 0;

    memcpy(pRDP_DynamicEstimate, pfreezedVehDyncData, sizeof(VDY_DynamicEstimate_t));

    // 将RDP模块输出目标的帧计数与输入的检测值帧计数统一
    pRDP_TrkObjectList->signalHeader.uiMeasurementCounter = pRSP_DetObjectList_t->signalHeader.uiMeasurementCounter;
    pRDP_DetObjectList->rspCurFrameMode = pRSP_DetObjectList_t->rspCurFrameMode;

    for (i = 0; i < MAX_NUM_OF_POINTS; i++)
    {
        pRDP_DetObjectList->rspDetObject[i].rspDetRange = 0;
    }
    for (i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        pRDP_TrkObjectList->rdpTrkObject[i].rdpTrkObjRange = 0;
    }
    for (i = 0, j = 0; i < pRSP_DetObjectList_t->rspDetObjectNum; i++)
    {
        if (pRSP_DetObjectList_t->rspDetObject[i].rspDetRange >= 1000)
        {
            tx = 1; // 切换tx标识，正常处理只有一次
            p_cnt = &tx2_cnt;
        }

        //TODO:is_using在此处取消
        if (pRSP_DetObjectList_t->rspDetObject[i].rspDetRange >= MAX_RANGE)
        {
            continue;
        }

        pRDP_DetObjectList->rspDetObject[j].rspDetRange = pRSP_DetObjectList_t->rspDetObject[i].rspDetRange; // m
        pRDP_DetObjectList->rspDetObject[j].rspDetVelocity = pRSP_DetObjectList_t->rspDetObject[i].rspDetVelocity; // m/s
        pRDP_DetObjectList->rspDetObject[j].rspDetAzimuthAngle= pRSP_DetObjectList_t->rspDetObject[i].rspDetAzimuthAngle;    // Degree
        pRDP_DetObjectList->rspDetObject[j].rspDetElevationAngle = pRSP_DetObjectList_t->rspDetObject[i].rspDetElevationAngle;
        pRDP_DetObjectList->rspDetObject[j].rspDetSNR = pRSP_DetObjectList_t->rspDetObject[i].rspDetSNR;
        pRDP_DetObjectList->rspDetObject[j].rspDetStatus = pRSP_DetObjectList_t->rspDetObject[i].rspDetStatus;
        pRDP_DetObjectList->rspDetObject[j].rspDetValid = 1;

        pRDP_DetObjectList->rspDetObject[j].rspDetRCS = pRSP_DetObjectList_t->rspDetObject[i].rspDetRCS;
        pRDP_DetObjectList->rspDetObject[j].rspRawIdx = i;
        
        (*p_cnt) = (*p_cnt) + 1;
        j++;
        // 速度为0时候的静止目标数量
        if (fabsf(pRDP_DynamicEstimate->vdySpeedInmps) < 0.1f)
        {
            if ((pRSP_DetObjectList_t->rspDetObject[i].rspDetVelocity < 0.1f) && (nomoveflag == 0))
            {
                nomoveflag = 1;
            }
        }
    }
    pRDP_DetObjectList->rspDetObjectNum = j;

    // 将干扰和遮挡检测结果拷贝至RDP全局变量
    pRDP_TrkObjectList->interferedFlag  = pRSP_DetObjectList_t->interferedFlag;
    pRDP_TrkObjectList->intPercent      = pRSP_DetObjectList_t->intPercent;
    pRDP_TrkObjectList->blockFlag       = pRSP_DetObjectList_t->blockFlag;
    pRDP_TrkObjectList->blockPercent    = pRSP_DetObjectList_t->blockPercent;
#ifndef RADAR_AUTOSAR
#ifndef PC_DBG_FW   //注意这里是否有影响
    pRDP_DetObjectList->signalHeader.uiTimeStamp = (uint64_t)(1.0f * xTaskGetTickCount() * portTICK_PERIOD_MS);
#endif
#else
//autosar todo
#endif
}
void RDP_simTrackTargetList(trk_pkg_t *pRDP_TrackTargets)
{
    trk_t* trk;

    for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pRDP_TrackTargets->trk[i];
        trk->type = TRACK;
        if(i < MAX_NUM_OF_TRACKS/2)
        {
            trk->sim_z[1] = i*0.5f;
            trk->sim_z[2] = i - MAX_NUM_OF_TRACKS/4;
            trk->sim_z[3] = i*2 - 45;
            trk->x[0] = (i - MAX_NUM_OF_TRACKS/4)*0.3f;
            trk->x[1] = i + 5;
            trk->x[2] = 0.5*i - 10;
            trk->x[3] = i -10;
        }else
        {
            trk->sim_z[1] = i*0.5f + 50;
            trk->sim_z[2] = i - MAX_NUM_OF_TRACKS/2;
            trk->sim_z[3] = i*2 - 45;
            trk->x[0] = (i-MAX_NUM_OF_TRACKS*3/4)*0.3f;
            trk->x[1] = i+5;
            trk->x[2] = 0.5f*i - 10;
            trk->x[3] = i -10;
        }
        trk->x[4] = i;
        trk->x[5] = i;
        trk->activeTrkCnt = 100;
        trk->headingAngle = i*0.03f;
        trk->objType = i%6;
        trk->probOfExist = i;
        trk->status = 0xff;
    }
}

void updateTrackAccelerate(trk_t *trk, float prevx, float prevy)
{
    float accx = 0.0f;
    float accy = 0.0f;
    float alpha = 0.2f;     //决定收敛速度
    if(trk->activeTrkCnt == 1)      //首帧track,直接使用卡尔曼滤波结果
    {
        trk->stored_last_outputX[4] = trk->x[4];
        trk->stored_last_outputX[5] = trk->x[5];
        return;
    }
    else
    {
        accx = (trk->stored_last_outputX[2] - prevx) / gRDP_storedFrameTime[0];
        accy = (trk->stored_last_outputX[3] - prevy) / gRDP_storedFrameTime[0];
    }
    trk->stored_last_outputX[4] = accx * alpha + trk->stored_last_outputX[4] * (1.0f - alpha);
    trk->stored_last_outputX[5] = accy * alpha + trk->stored_last_outputX[5] * (1.0f - alpha);

    return;
}

float getSameGroupXClose2MidAxis(trk_t *trk)                    //获取目标关联点所有群组点的近中轴线x最小值，且不得大于上一帧上报值
{
    if(trk->idx_1 < 0)      //missed
    {
        if(trk->x[0] < trk->stored_last_outputX[0])
        {
            return trk->x[0];
        }
        else
        {
            return trk->stored_last_outputX[0];
        }
    }
    else
    {
        cdi_t* cdi = gRDP_BKTargetsList.cdi;
        u8 group = cdi[trk->idx_1].groupId2;
        float xtemp = trk->x[0] < trk->stored_last_outputX[0] ? trk->x[0] : trk->stored_last_outputX[0];
        for(int16_t i = 0; i < gRDP_BKTargetsList.number; i++)
        {
            if(cdi[i].groupId2 == group)
            {
                if(cdi[i].x < xtemp)
                {
                    if(cdi[i].x < -0.9f)
                    {
                        xtemp = -0.9f;
                        break;
                    }
                    else
                    {
                        xtemp = cdi[i].x;
                    }
                }
            }
            else if(cdi[i].mea_z[1] > 15.0f)    //too faraway
            {
                break;
            }
        }
        return xtemp;
    }
}
// 将航迹框按象限移动到跟踪点
void RDP_updateBoxCenter(float* boxCenter, const VDY_DynamicEstimate_t *vehicleInfo, trk_t* trk)
{
	float A[2];
	float B[2];
	if ((trk->x[0] > 0 && trk->x[1] > -3.0f) || (trk->x[0] > -2.0f && trk->x[0] < 0 && trk->x[1] < 7.0f && (trk->status & TRACK_STATUS_CROSSING_BMP)))
	{
		if ((trk->headingAngle < -45 && trk->headingAngle >= -135))
		{
			//左前 右前
			getTrackBoxConner(A, vehicleInfo, trk, -(trk->left + trk->right) / 2, -(trk->front + trk->back) / 2);
			//boxNearestPoint[0] = leftFront[0];
			//boxNearestPoint[1] = leftFront[1];
		}
		else if (trk->headingAngle > 135 || trk->headingAngle < -135 \
			|| ((trk->headingAngle > 45 && trk->headingAngle < 135) && trk->x[2] > 0.f && trk->x[0] < 2))
		{
			//右前 左前
			getTrackBoxConner(A, vehicleInfo, trk, (trk->left + trk->right) / 2, -(trk->front + trk->back) / 2);
			//boxNearestPoint[0] = rightFront[0];
			//boxNearestPoint[1] = rightFront[1];
		}
		else if (trk->headingAngle > -45 && trk->headingAngle < 45)
		{
			//右后 左后
			getTrackBoxConner(A, vehicleInfo, trk, -(trk->left + trk->right) / 2, (trk->front + trk->back) / 2);
			//boxNearestPoint[0] = leftBack[0];
			//boxNearestPoint[1] = leftBack[1];
		}
		else if (trk->headingAngle > 45 && trk->headingAngle < 135)
		{
			//右后 左后
			getTrackBoxConner(A, vehicleInfo, trk, (trk->left + trk->right) / 2, (trk->front + trk->back) / 2);
			//boxNearestPoint[0] = rightBack[0];
			//boxNearestPoint[1] = rightBack[1];
		}
	}
	else if (trk->x[0] > 0 && trk->x[1] < -3.0f)
	{
		if (trk->headingAngle < -45 && trk->headingAngle >= -135)
		{
			//右前
			getTrackBoxConner(A, vehicleInfo, trk, (trk->left + trk->right) / 2, -(trk->front + trk->back) / 2);
			//boxNearestPoint[0] = rightFront[0];
			//boxNearestPoint[1] = rightFront[1];
		}
		else if (trk->headingAngle > 135 || trk->headingAngle < -135)
		{
			//右后
			getTrackBoxConner(A, vehicleInfo, trk, (trk->left + trk->right) / 2, (trk->front + trk->back) / 2);
			//boxNearestPoint[0] = rightBack[0];
			//boxNearestPoint[1] = rightBack[1];
		}
		else if (trk->headingAngle > -45 && trk->headingAngle < 45)
		{
			//左前
			getTrackBoxConner(A, vehicleInfo, trk, -(trk->left + trk->right) / 2, -(trk->front + trk->back) / 2);
			//boxNearestPoint[0] = leftFront[0];
			//boxNearestPoint[1] = leftFront[1];
		}
		else if (trk->headingAngle > 45 && trk->headingAngle < 135)
		{
			//左后
			getTrackBoxConner(A, vehicleInfo, trk, -(trk->left + trk->right) / 2, (trk->front + trk->back) / 2);
			//boxNearestPoint[0] = leftBack[0];
			//boxNearestPoint[1] = leftBack[1];
		}
	}
	else if (trk->x[0] < 0 && trk->x[1] > -0.5)
	{
		if (trk->headingAngle < -45 && trk->headingAngle >= -135)
		{
			getTrackBoxConner(A, vehicleInfo, trk, -(trk->left + trk->right) / 2, (trk->front + trk->back) / 2);
			/*	boxNearestPoint[0] = leftBack[0];
				boxNearestPoint[1] = leftBack[1];*/
		}
		else if (trk->headingAngle > 135 || trk->headingAngle < -135)
		{
			getTrackBoxConner(A, vehicleInfo, trk, -(trk->left + trk->right) / 2, -(trk->front + trk->back) / 2);
			/*	boxNearestPoint[0] = leftFront[0];
				boxNearestPoint[1] = leftFront[1];*/
		}
		else if (trk->headingAngle > -45 && trk->headingAngle < 45)
		{
			getTrackBoxConner(A, vehicleInfo, trk, (trk->left + trk->right) / 2, (trk->front + trk->back) / 2);
			//boxNearestPoint[0] = rightBack[0];
			//boxNearestPoint[1] = rightBack[1];
		}
		else if (trk->headingAngle > 45 && trk->headingAngle < 135)
		{
			getTrackBoxConner(A, vehicleInfo, trk, (trk->left + trk->right) / 2, -(trk->front + trk->back) / 2);
			//boxNearestPoint[0] = rightFront[0];
			//boxNearestPoint[1] = rightFront[1];
		}
	}
	boxCenter[0] = A[0];
	boxCenter[1] = A[1];

}

//void trackingBoxAdjustToCustomer(trk_t* trk, VDY_DynamicEstimate_t *pRDP_inVehicleData, float *X, float x, float y)
//{
//	//直行的航迹框中心计算
//	float left = trk->left, right = trk->right, front = trk->front, back = trk->back;
//	float distX = trk->x[0], distY = trk->x[1];
//	trk->x[0] = x;
//	trk->x[1] = y;
//	trk->front = 0;
//	trk->back = (front + back);
//	trk->right = 0;
//	trk->left = (left + right);
//	getTrackBoxCenter(X, pRDP_inVehicleData, trk);
//	trk->left = left;
//	trk->right = right;
//	trk->front = front;
//	trk->back = back;
//	trk->x[0] = distX;
//	trk->x[1] = distY;
//	trk->boxCenter[0] = X[0];
//	trk->boxCenter[1] = X[1];
//}

/*
*低速行人在正后方来回走动，一开始没有识别行人，类型为unknow，导致DOW误报
*/
uint8_t LowSpeedManInDOWArea(trk_t* trk)
{
	if (trk->objType == 0 && trk->activeTrkCnt < 100 \
		&& trk->startPosition[0] < 4.f  && trk->startPosition[0] > -3.f \
		&& trk->startPosition[1] < 5.f && trk->startPosition[1] > -2.f \
		&& trk->x[0] < 3.f && trk->x[0] > 0.f && trk->x[1] < 5.f && trk->x[1] > -2.f)
	{
		return 1;
	}
	return 0;
}

#define RCTA_RDP_OBJ_MAX_SPEED      (60.0f / 3.6f)     //目标车最大车速 60kph

/**
 * @brief 高速60+ kph目标 在近处由于速度检测降低可能导致RCTA误报 识别此类case场景
 * @param trk 
 * @return uint8_t 
 */
uint8_t HighSpeedCarInRCTAArea(trk_t* trk)
{
    //   R挡位时, 近处还未报警目标, 横向速度在一定范围内, 强行将横向速度拉上去
    // rdp无法获取到功能的报警状态, 
    if ((trk->x[2] < (-RCTA_RDP_OBJ_MAX_SPEED + 1.0f)) && (trk->x[2] > (-RCTA_RDP_OBJ_MAX_SPEED - 1.0f)) && (trk->x[0] <= 5.0f) && (trk->x[0] >= -2.0f))
	{
		return 1;
	}
    return 0;
}



#define VALID_OPERATE_LAT_MAX_RANGE		0.75f + 3.75f * 2	// 有效横向作用距离，相邻两车道
#define VALID_OPERATE_LAT_MIN_RANGE		-2.0f				// 最小值门限考虑RCW场景
#define VALID_OPERATE_LNG_MAX_RANGE		6.f					// 有效纵向作用距离，考虑RCTA横穿斜穿属性
#define VALID_OPERATE_LNG_MIN_RANGE		0.5f
#define VALID_OPERATE_LAT_PLUS_ANGLE1	60.f
#define VALID_OPERATE_LAT_PLUS_ANGLE2	120.f
#define VALID_OPERATE_LNG_PLUS_ANGLE	150.f
#define VALID_OPERATE_LNG_MINUS_ANGLE	-150.f
void RDP_updateTrackerList(trk_pkg_t *pRDP_TrackTargets, VDY_DynamicEstimate_t *pRDP_inVehicleData, RDP_TrkObjectList_t *pRDP_TrkObjectList, float time)
{
    float x, y, r = pRDP_inVehicleData->vdyCurveRadius;
    RDP_TrkObjectInfo_t *prdpTrkObject = pRDP_TrkObjectList->rdpTrkObject;
    int usedCnt = 0;
    float boxCenter[2];
    VDY_DynamicEstimate_t vehicleInfo;
    memcpy(&vehicleInfo, pRDP_inVehicleData,sizeof(VDY_DynamicEstimate_t));
    rdp_config_t *config = RDP_getTrackConfigPointer();
    cdi_t *cdi = gRDP_BKTargetsList.cdi; 

    bool movePointFlag = false;

    //180项目根据航迹框移动跟踪点，130项目只在自车静止时根据航迹框移动跟踪点
#ifdef ALGORITHM_GEELY
    movePointFlag = true;
#else
    /*if (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f)
    {
        movePointFlag = true;
    }*/
#endif // ALGORITHM_GEELY

    if(config->isFront)
    {
        vehicleInfo.vdySpeedInmps = -vehicleInfo.vdySpeedInmps;
    }

    pRDP_TrkObjectList->rdpDetObjectNum = 0;

    for (int i = 0; i < MAX_NUM_OF_TRACKS; i++, prdpTrkObject++)
    {
        if (pRDP_TrackTargets->trk[i].type != NONE)
        {
            usedCnt++;
        }
        prdpTrkObject->rdpTrackType = pRDP_TrackTargets->trk[i].type;
#ifdef PC_TRACK_DBG
        //调试模式下，CANDI目标和TRACK目标都输出显示，用于快速直观在界面上看到CANDI的信息
        if(pRDP_TrackTargets->trk[i].type != NONE
			&& (fabsf(pRDP_TrackTargets->trk[i].sim_z[3] - config->installAngle) < 90.f
				||fabsf(vehicleInfo.vdySpeedInmps) > 0.1f
				|| (pRDP_TrackTargets->trk[i].x[2] < -2.5f && pRDP_TrackTargets->trk[i].x[0] > -8.0f))
			&& pRDP_TrackTargets->trk[i].objFakeDOt == 0)
#else
        //FOV ±76deg内的目标输出，超过不输出，主要是1、担心是假点目标不稳定。2、超过FOV较大客户(吉利)会提出质疑
        if (pRDP_TrackTargets->trk[i].type == TRACK
			&& (fabsf(pRDP_TrackTargets->trk[i].sim_z[3] - config->installAngle) < 90.f
				|| fabsf(vehicleInfo.vdySpeedInmps) > 0.1f
				|| (pRDP_TrackTargets->trk[i].x[2] < -2.5f && pRDP_TrackTargets->trk[i].x[0] > -8.0f))
			&& pRDP_TrackTargets->trk[i].objFakeDOt == 0)
#endif
        {
			// 不输出标志清0
			pRDP_TrackTargets->trk[i].objDeleteFlag = 0;
            prdpTrkObject->rdpTrkObjsnr             = (float)pRDP_TrackTargets->trk[i].sim_z[0];
            prdpTrkObject->rdpTrkObjRange           = (float)pRDP_TrackTargets->trk[i].sim_z[1];
            prdpTrkObject->rdpTrkObjVelocity        = (float)pRDP_TrackTargets->trk[i].sim_z[2];
            prdpTrkObject->rdpTrkObjAzimuthAngle    = (float)pRDP_TrackTargets->trk[i].sim_z[3];
            prdpTrkObject->rdpTrkObjAssoDetIdx      = pRDP_TrackTargets->trk[i].ObjAssoDetIdx;
            
            //太大了协议会翻转
            prdpTrkObject->rdpTrkObjRcs             = pRDP_TrackTargets->trk[i].rcs;
            prdpTrkObject->rdpTrkObjRcs             = prdpTrkObject->rdpTrkObjRcs < (-30 * RCS_SCALE) ? (-30 * RCS_SCALE) : prdpTrkObject->rdpTrkObjRcs; //范围固定在-30~90之间
            prdpTrkObject->rdpTrkObjRcs             = prdpTrkObject->rdpTrkObjRcs > ( 90 * RCS_SCALE) ? ( 90 * RCS_SCALE) : prdpTrkObject->rdpTrkObjRcs;
            prdpTrkObject->rdpTrkObjElevetionAngle  = pRDP_TrackTargets->trk[i].heighAngle;
            prdpTrkObject->rdpTrkObjDistX           = pRDP_TrackTargets->trk[i].x[0];
            prdpTrkObject->rdpTrkObjDistY           = pRDP_TrackTargets->trk[i].x[1];
            prdpTrkObject->rdpTrkObjStartX          = pRDP_TrackTargets->trk[i].startPosition[0];       // 记录起始位置跟到功能使用
            prdpTrkObject->rdpTrkObjStartY          = pRDP_TrackTargets->trk[i].startPosition[1];

			//if (pRDP_TrackTargets->trk[i].headingAngle < -80.f && pRDP_TrackTargets->trk[i].headingAngle > -100.f
			//	&& (pRDP_TrackTargets->trk[i].status & TRACK_STATUS_CROSSING_BMP)
			//	&& pRDP_TrackTargets->trk[i].x[0] > 3// && pRDP_TrackTargets->trk[i].startPosition[0] > 30.f
			//	&& pRDP_TrackTargets->trk[i].x[2] < -20.0f / 3.6
			//	&& fabsf(pRDP_TrackTargets->trk[i].x[3]) < 2)
			//{
			//	prdpTrkObject->rdpTrkObjVrelY = pRDP_TrackTargets->trk[i].x[3] * 0.1;
			//}
			//else
			{
				prdpTrkObject->rdpTrkObjVrelY = pRDP_TrackTargets->trk[i].x[3];
			}
            prdpTrkObject->rdpTrkObjVrelX           = pRDP_TrackTargets->trk[i].x[2];
            //prdpTrkObject->rdpTrkObjVrelY           = pRDP_TrackTargets->trk[i].x[3];
            prdpTrkObject->rdpTrkObjStatus          = pRDP_TrackTargets->trk[i].status;
			prdpTrkObject->rdpTrkObjHeadingAngle = pRDP_TrackTargets->trk[i].headingAngle;//atan2f(prdpTrkObject->rdpTrkObjVrelX, prdpTrkObject->rdpTrkObjVrelY - vehicleInfo.vdySpeedInmps) * 180.f / M_PI;

            prdpTrkObject->rdpTrkObjBoxLength       = pRDP_TrackTargets->trk[i].front + pRDP_TrackTargets->trk[i].back;
            prdpTrkObject->rdpTrkObjBoxWidth        = pRDP_TrackTargets->trk[i].left + pRDP_TrackTargets->trk[i].right;

            if (movePointFlag)
            {
                RDP_updateBoxCenter(boxCenter, &vehicleInfo, &pRDP_TrackTargets->trk[i]);
                pRDP_TrackTargets->trk[i].boxCenter[0] = boxCenter[0];
                pRDP_TrackTargets->trk[i].boxCenter[1] = boxCenter[1];
                prdpTrkObject->rdpTrkObjBoxCenterX = boxCenter[0];
                prdpTrkObject->rdpTrkObjBoxCenterY = boxCenter[1];

                /*
                直行的目标航向角在150°以上的，在邻车道和邻邻车道的目标，向左移动一个left或者一个right
                为了限制斜停8°的隔车道误报，限制起批位置在横向大于-4m的目标才移动
                为了RCW能够重合，在横向位置大于-2m的时候也移动跟踪点位置
                */
                if ((pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LNG_PLUS_ANGLE || pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LNG_MINUS_ANGLE)
                    && pRDP_TrackTargets->trk[i].startPosition[0] > -4.0f
                    && (pRDP_TrackTargets->trk[i].x[0] < VALID_OPERATE_LAT_MAX_RANGE \
                        && ((pRDP_TrackTargets->trk[i].x[0] > VALID_OPERATE_LAT_MIN_RANGE && pRDP_TrackTargets->trk[i].startPosition[0] > -2.5f && pRDP_TrackTargets->trk[i].startPosition[0] < 2) \
                            || (pRDP_TrackTargets->trk[i].x[0] > 0))))
                {
                    /*float A[2];
                    getTrackXoffset(A, &vehicleInfo, &pRDP_TrackTargets->trk[i], pRDP_TrackTargets->trk[i].left, (pRDP_TrackTargets->trk[i].front + pRDP_TrackTargets->trk[i].back) / 2);*/
                    switch (config->installPosition)
                    {
                        //左雷达减right，但是怀疑这里的left值才是right，right的值才是left；从几组数据上表现看是这样的
                    case SENSOR_POSITION_REAR_LEFT:
                    case SENSOR_POSITION_FRONT_LEFT:
                        prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].x[0] - (pRDP_TrackTargets->trk[i].left * cosf((180 - fabsf(pRDP_TrackTargets->trk[i].headingAngle)) / 180.f * 3.1415927));
                        break;

                    case SENSOR_POSITION_REAR_RIGHT:
                    case SENSOR_POSITION_FRONT_RIGHT:
                        prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].x[0] - (pRDP_TrackTargets->trk[i].right * cosf((180 - fabsf(pRDP_TrackTargets->trk[i].headingAngle)) / 180.f * 3.1415927));
                        break;
                    default:
                        break;
                    }
                    //移动的比最近点还要靠里面，则使用最近点的横向位置
                    if (prdpTrkObject->rdpTrkObjDistX < pRDP_TrackTargets->trk[i].nearestPosition[0] && pRDP_TrackTargets->trk[i].nearestPosition[0] < 100.f && pRDP_TrackTargets->trk[i].activeTrkCnt != 1)
                    {
                        prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].nearestPosition[0];
                    }
                    else
                    {
                        prdpTrkObject->rdpTrkObjDistX = LP_FILTER(prdpTrkObject->rdpTrkObjDistX, 0.5, pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[0]);
                    }

                    if (config->isFront == 0)
                    {
                        float installOffsetY = -0.89f; //后轴中心纵向最近点默认-0.89，后面根据不同车型调整
                        if (fabsf(config->installOffsetX) > 0.5f && fabsf(config->installOffsetX) < 1.f)
                        {
                            installOffsetY = -fabsf(config->installOffsetX);
                        }
                        if (pRDP_TrackTargets->trk[i].x[1] < 0.f && prdpTrkObject->rdpTrkObjDistY < (installOffsetY + 0.1f) && prdpTrkObject->rdpTrkObjVrelY < 0.f)
                        {
                            prdpTrkObject->rdpTrkObjDistY = installOffsetY;
                        }
                        else
                        {
                            prdpTrkObject->rdpTrkObjDistY = pRDP_TrackTargets->trk[i].x[1];
                        }
                    }
                    else
                    {
                        prdpTrkObject->rdpTrkObjDistY = pRDP_TrackTargets->trk[i].x[1];
                    }

                    //航迹框中心的横向位置更新：跟踪点移动多少，航迹框中心跟着移动多少。
                    prdpTrkObject->rdpTrkObjBoxCenterX = pRDP_TrackTargets->trk[i].boxCenter[0] - (pRDP_TrackTargets->trk[i].x[0] - prdpTrkObject->rdpTrkObjDistX);
                    //在目标纵行到90°附近的时候，航迹框中心的y位置，靠速度预测
                    if (prdpTrkObject->rdpTrkObjDistY < 0.5f \
                        && pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[1] < 0.5f \
                        && pRDP_TrackTargets->trk[i].x[3] < 0.f)
                    {
                        if (pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[3] < (prdpTrkObject->rdpTrkObjDistY - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f))
                        {
                            prdpTrkObject->rdpTrkObjBoxCenterY = prdpTrkObject->rdpTrkObjDistY - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f;
                            pRDP_TrackTargets->trk[i].bsdTargetMaintainEnd = 1;
                        }
                        else
                        {
                            prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[3] + pRDP_TrackTargets->trk[i].x[3] * 0.05f;
                            if (prdpTrkObject->rdpTrkObjBoxCenterY > pRDP_TrackTargets->trk[i].boxCenter[1] \
                                && (pRDP_TrackTargets->trk[i].boxCenter[1] < (prdpTrkObject->rdpTrkObjDistY - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f)))
                            {
                                prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].boxCenter[1];
                            }
                            //当前结果航迹框中，没有包括跟踪点，则限制航迹框中心
                            if (prdpTrkObject->rdpTrkObjBoxCenterY < (prdpTrkObject->rdpTrkObjDistY - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f))
                            {
                                prdpTrkObject->rdpTrkObjBoxCenterY = prdpTrkObject->rdpTrkObjDistY - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f;
                                pRDP_TrackTargets->trk[i].bsdTargetMaintainEnd = 1;
                            }
                            else
                            {
                                pRDP_TrackTargets->trk[i].bsdTargetMaintainEnd = 0;
                            }
                        }
                    }
                    else
                    {
                        prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].boxCenter[1];
                    }
                }
                /*
                直行的目标航向角在150°以上的，在邻车道和邻邻车道的目标，向左移动一个left或者一个right
                为了限制斜停8°的隔车道误报，限制起批位置在横向大于-4m的目标才移动
                横向位置小于0的，需要改变符号
                */
                else if ((pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LNG_PLUS_ANGLE || pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LNG_MINUS_ANGLE)
                    && pRDP_TrackTargets->trk[i].startPosition[0] > -4.0f \
                    && (pRDP_TrackTargets->trk[i].x[0] < 0) && pRDP_TrackTargets->trk[i].x[0] > -2.f)
                {
                    switch (config->installPosition)
                    {
                        //左雷达减right，但是怀疑这里的left值才是right，right的值才是left；从几组数据上表现看是这样的
                    case SENSOR_POSITION_REAR_LEFT:
                    case SENSOR_POSITION_FRONT_LEFT:
                        prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].x[0] + (pRDP_TrackTargets->trk[i].left * cosf((180 - fabsf(pRDP_TrackTargets->trk[i].headingAngle)) / 180.f * 3.1415927));
                        break;

                    case SENSOR_POSITION_REAR_RIGHT:
                    case SENSOR_POSITION_FRONT_RIGHT:
                        prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].x[0] + (pRDP_TrackTargets->trk[i].right * cosf((180 - fabsf(pRDP_TrackTargets->trk[i].headingAngle)) / 180.f * 3.1415927));
                        break;
                    default:
                        break;
                    }
                    if (prdpTrkObject->rdpTrkObjDistX < pRDP_TrackTargets->trk[i].nearestPosition[0] && pRDP_TrackTargets->trk[i].nearestPosition[0] < 100.f && pRDP_TrackTargets->trk[i].activeTrkCnt != 1)
                    {
                        prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].nearestPosition[0];
                    }
                    else if (pRDP_TrackTargets->skewStopSence == 0x1 && config->installPosition == SENSOR_POSITION_REAR_LEFT)
                    {
                        prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].x[0];
                    }
                    // 内部跟踪点小于0，往左移动或者往右移动的时候，导致左右雷达不重合
                    else if (pRDP_TrackTargets->trk[i].x[0] < 0.f \
                        && (pRDP_TrackTargets->trk[i].right > (prdpTrkObject->rdpTrkObjDistX - pRDP_TrackTargets->trk[i].x[0])) \
                        && pRDP_inVehicleData->vdySpeedInmps > 20 / 3.6f)
                    {
                        prdpTrkObject->rdpTrkObjDistX = LP_FILTER(prdpTrkObject->rdpTrkObjDistX, 0.5, pRDP_TrackTargets->trk[i].x[0]);
                    }
                    prdpTrkObject->rdpTrkObjDistY = pRDP_TrackTargets->trk[i].x[1];
                    prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].boxCenter[1];//boxCenter[1];
                    prdpTrkObject->rdpTrkObjDistX = LP_FILTER(prdpTrkObject->rdpTrkObjDistX, 0.5, pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[0]);
                    prdpTrkObject->rdpTrkObjBoxCenterX = pRDP_TrackTargets->trk[i].boxCenter[0] - (pRDP_TrackTargets->trk[i].x[0] - prdpTrkObject->rdpTrkObjDistX);
                    //trackingBoxAdjustToCustomer(&pRDP_TrackTargets->trk[i], &vehicleInfo, boxCenter_1, prdpTrkObject->rdpTrkObjDistX, prdpTrkObject->rdpTrkObjDistY);
                    //prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].boxCenter[1];//boxCenter[1];
                    //prdpTrkObject->rdpTrkObjBoxCenterX = pRDP_TrackTargets->trk[i].boxCenter[0];// boxCenter[0];
                }
                else if (((pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LAT_PLUS_ANGLE1 && pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LAT_PLUS_ANGLE2) || (pRDP_TrackTargets->trk[i].headingAngle > -VALID_OPERATE_LAT_PLUS_ANGLE2 && pRDP_TrackTargets->trk[i].headingAngle < -VALID_OPERATE_LAT_PLUS_ANGLE1))
                    && (pRDP_TrackTargets->trk[i].x[1] < VALID_OPERATE_LNG_MAX_RANGE && pRDP_TrackTargets->trk[i].x[1] > VALID_OPERATE_LNG_MIN_RANGE))
                {

                    switch (config->installPosition)
                    {
                    case SENSOR_POSITION_REAR_LEFT:
                    case SENSOR_POSITION_FRONT_LEFT:
                        prdpTrkObject->rdpTrkObjDistY = pRDP_TrackTargets->trk[i].x[1] - (pRDP_TrackTargets->trk[i].right * sinf((180 - fabsf(pRDP_TrackTargets->trk[i].headingAngle)) / 180.f * 3.1415927));
                        break;

                    case SENSOR_POSITION_REAR_RIGHT:
                    case SENSOR_POSITION_FRONT_RIGHT:
                        prdpTrkObject->rdpTrkObjDistY = pRDP_TrackTargets->trk[i].x[1] - (pRDP_TrackTargets->trk[i].left * sinf((180 - fabsf(pRDP_TrackTargets->trk[i].headingAngle)) / 180.f * 3.1415927));
                        break;
                    default:
                        break;
                    }
                    if (prdpTrkObject->rdpTrkObjDistY < pRDP_TrackTargets->trk[i].nearestPosition[1] && pRDP_TrackTargets->trk[i].nearestPosition[1] < 100.f)
                    {
                        if (pRDP_TrackTargets->trk[i].nearestPosition[1] > pRDP_TrackTargets->trk[i].x[1])
                        {
                            prdpTrkObject->rdpTrkObjDistY = pRDP_TrackTargets->trk[i].x[1];
                        }
                        else
                        {
                            prdpTrkObject->rdpTrkObjDistY = pRDP_TrackTargets->trk[i].nearestPosition[1];
                        }
                    }
                    prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].x[0];
                    //横穿目标保持在正后方的时候，航迹框中心预测着往前走
                    if (pRDP_TrackTargets->trk[i].keepInBackCnt > 0 && pRDP_TrackTargets->trk[i].keepInBackCnt < 79 && pRDP_TrackTargets->trk[i].x[2] < 0.f)
                    {
                        if (pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[2] < (prdpTrkObject->rdpTrkObjDistX - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f) \
                            && prdpTrkObject->rdpTrkObjDistX < 0)
                        {
                            prdpTrkObject->rdpTrkObjBoxCenterX = (prdpTrkObject->rdpTrkObjDistX - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f);
                        }
                        else
                        {
                            prdpTrkObject->rdpTrkObjBoxCenterX = pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[2] - fabsf(pRDP_TrackTargets->trk[i].x[2] * 0.025f);
                        }
                        if (pRDP_TrackTargets->trk[i].x[2] > -15 / 3.6f && pRDP_TrackTargets->trk[i].x[2] < -10 / 3.6f)
                        {
                            prdpTrkObject->rdpTrkObjVrelX = LP_FILTER(pRDP_TrackTargets->trk[i].x[2], 0.25, -1.2f);
                        }
                        else if (pRDP_TrackTargets->trk[i].x[2] > -10 / 3.6f && pRDP_TrackTargets->trk[i].x[2] < -1.2f)
                        {
                            prdpTrkObject->rdpTrkObjVrelX = LP_FILTER(pRDP_TrackTargets->trk[i].x[2], 0.5, -1.2f);
                        }
                        //防止目标回跳
                        if (prdpTrkObject->rdpTrkObjDistX > pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[0] && prdpTrkObject->rdpTrkObjVrelX < 0.f)
                        {
                            prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[0];
                        }
                    }
                    else
                    {
                        prdpTrkObject->rdpTrkObjBoxCenterX = pRDP_TrackTargets->trk[i].boxCenter[0];// boxCenter[0];
                    }

                    prdpTrkObject->rdpTrkObjDistY = LP_FILTER(prdpTrkObject->rdpTrkObjDistY, 0.5, pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[1]);
                    prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].boxCenter[1] - (pRDP_TrackTargets->trk[i].x[1] - prdpTrkObject->rdpTrkObjDistY);//boxCenter[1];
                }
                else
                {
                    //其它位置不移动跟踪点位置。但是移动航迹框位置，（斜停8°隔车道误报问题）
                    //getTrackBoxCenter(boxCenter, &vehicleInfo, &pRDP_TrackTargets->trk[i]);
                    pRDP_TrackTargets->trk[i].boxCenter[0] = boxCenter[0];
                    pRDP_TrackTargets->trk[i].boxCenter[1] = boxCenter[1];
                    prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].x[0]; //boxNearestPoint[0];//
                    prdpTrkObject->rdpTrkObjDistY = pRDP_TrackTargets->trk[i].x[1]; //boxNearestPoint[1];//
                    prdpTrkObject->rdpTrkObjBoxCenterX = pRDP_TrackTargets->trk[i].boxCenter[0];// boxCenter[0];


                    if (prdpTrkObject->rdpTrkObjDistY < 0.5f \
                        && pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[1] < 0.5f \
                        && pRDP_TrackTargets->trk[i].x[3] < 0.f \
                        && pRDP_TrackTargets->trk[i].x[0] < VALID_OPERATE_LAT_MAX_RANGE \
                        && pRDP_TrackTargets->trk[i].x[0] > VALID_OPERATE_LAT_MIN_RANGE
                        && (pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LNG_PLUS_ANGLE || pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LNG_MINUS_ANGLE))
                    {
                        if (pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[3] < (prdpTrkObject->rdpTrkObjDistY - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f) && prdpTrkObject->rdpTrkObjDistY < 0)
                        {
                            prdpTrkObject->rdpTrkObjBoxCenterY = prdpTrkObject->rdpTrkObjDistY - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f;
                        }
                        else
                        {
                            prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[3] + pRDP_TrackTargets->trk[i].x[3] * 0.05f;
                            if (prdpTrkObject->rdpTrkObjBoxCenterY > pRDP_TrackTargets->trk[i].boxCenter[1] \
                                && (prdpTrkObject->rdpTrkObjBoxCenterY < (prdpTrkObject->rdpTrkObjDistY - (pRDP_TrackTargets->trk[i].back + pRDP_TrackTargets->trk[i].front) / 2.f)))
                            {
                                prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].boxCenter[1];
                            }
                        }
                    }
                    else
                    {
                        prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].boxCenter[1];//boxCenter[1];
                    }
                }
                //纵向行驶的目标，输出的横向位置，与上一帧的横向位置差值大于0.6，则取与上一帧的横向位置滤波处理
                if (fabsf(prdpTrkObject->rdpTrkObjDistX - pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[0]) > 0.6 \
                    && (pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LNG_PLUS_ANGLE || pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LNG_MINUS_ANGLE) \
                    && pRDP_TrackTargets->trk[i].x[0] > VALID_OPERATE_LAT_MIN_RANGE && pRDP_TrackTargets->trk[i].x[0] < VALID_OPERATE_LAT_MAX_RANGE)
                {
                    prdpTrkObject->rdpTrkObjDistX = LP_FILTER(prdpTrkObject->rdpTrkObjDistX, 0.5, pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[0]);
                    prdpTrkObject->rdpTrkObjBoxCenterX = pRDP_TrackTargets->trk[i].boxCenter[0] - (pRDP_TrackTargets->trk[i].x[0] - prdpTrkObject->rdpTrkObjDistX);
                }
                //横穿目标，纵向距离相差0.6m以上的，取与上一帧的滤波
                else if (fabsf(prdpTrkObject->rdpTrkObjDistY - pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[1]) > 0.6 \
                    && ((pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LAT_PLUS_ANGLE1 && pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LAT_PLUS_ANGLE2) || (pRDP_TrackTargets->trk[i].headingAngle > -VALID_OPERATE_LAT_PLUS_ANGLE2 && pRDP_TrackTargets->trk[i].headingAngle < -VALID_OPERATE_LAT_PLUS_ANGLE1)) \
                    && pRDP_TrackTargets->trk[i].x[1] > VALID_OPERATE_LNG_MIN_RANGE && pRDP_TrackTargets->trk[i].x[1] < VALID_OPERATE_LNG_MAX_RANGE)
                {
                    prdpTrkObject->rdpTrkObjDistY = LP_FILTER(prdpTrkObject->rdpTrkObjDistY, 0.5, pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[1]);
                    prdpTrkObject->rdpTrkObjBoxCenterY = pRDP_TrackTargets->trk[i].boxCenter[1] - (pRDP_TrackTargets->trk[i].x[1] - prdpTrkObject->rdpTrkObjDistY);
                }
                /*
                正后方目标，航迹框中心与跟踪点的横向位置一致
                正后方RCW目标，纵向速度在20kph以下的，横向位置往中轴靠近滤波
                正后方RCW目标，满足TTC之后，限制横向速度
                */
                if (prdpTrkObject->rdpTrkObjDistX < 0.5f && prdpTrkObject->rdpTrkObjDistX > -2.f \
                    && prdpTrkObject->rdpTrkObjDistY > 0.5f /*&& fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f*/\
                    && fabsf(pRDP_inVehicleData->vdyCurveRadius) > 200.f
                    && pRDP_TrackTargets->trk[i].startPosition[0] > -6.0f \
                    && (pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LNG_PLUS_ANGLE + 10.f || pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LNG_MINUS_ANGLE - 10.f) \
                    )
                {
                    if (prdpTrkObject->rdpTrkObjDistX < -0.5f && prdpTrkObject->rdpTrkObjDistX > -2.f)
                    {
                        pRDP_TrackTargets->trk[i].kepRcwAreaCnt = pRDP_TrackTargets->trk[i].kepRcwAreaCnt >= 127 ? pRDP_TrackTargets->trk[i].kepRcwAreaCnt : pRDP_TrackTargets->trk[i].kepRcwAreaCnt + 1;
                    }
					else if (pRDP_TrackTargets->trk[i].x[3] - fabsf(pRDP_inVehicleData->vdySpeedInmps) > 0.f)
					{
						// 目标远离自车重置计数
						pRDP_TrackTargets->trk[i].kepRcwAreaCnt = 0;
					}
				//正后方目标，移动过多导致左右雷达目标不重合
				if (prdpTrkObject->rdpTrkObjDistX < -1.f && pRDP_TrackTargets->trk[i].x[0] < -0.5f)
                    {
                        prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].x[0];
                    }
                    if ((pRDP_TrackTargets->trk[i].startPosition[0] > -2.5f && pRDP_TrackTargets->trk[i].startPosition[0] < 2) && fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f)
                    {
                        if (fabsf(prdpTrkObject->rdpTrkObjVrelY) < 20 / 3.6f && (pRDP_TrackTargets->trk[i].objType == 3 || pRDP_TrackTargets->trk[i].objType == 4) \
                            && prdpTrkObject->rdpTrkObjDistY < 5.5f && prdpTrkObject->rdpTrkObjDistY > 0.5f && pRDP_TrackTargets->trk[i].kepRcwAreaCnt > 100)
                        {
                            prdpTrkObject->rdpTrkObjDistX = LP_FILTER(prdpTrkObject->rdpTrkObjDistX, 0.25, -0.8);
                        }
                        if (fabsf(prdpTrkObject->rdpTrkObjDistY / prdpTrkObject->rdpTrkObjVrelY) < 1.8f)
                        {
                            prdpTrkObject->rdpTrkObjVrelX *= 0.3f;
                        }
                        if ((pRDP_TrackTargets->trk[i].objType == 3 || pRDP_TrackTargets->trk[i].objType == 4) && prdpTrkObject->rdpTrkObjBoxWidth < 1.5f)
                        {
                            prdpTrkObject->rdpTrkObjBoxWidth = 1.5f;
                        }
                    }
                    if (pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[0] > 0.5f && prdpTrkObject->rdpTrkObjDistX < 0.5f)
                    {
                        prdpTrkObject->rdpTrkObjBoxCenterX = LP_FILTER(prdpTrkObject->rdpTrkObjDistX, 0.5, pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[2]);
                    }
                    else
                    {
                        if ((prdpTrkObject->rdpTrkObjDistX - prdpTrkObject->rdpTrkObjBoxWidth / 2.f) < pRDP_TrackTargets->trk[i].nearestPosition[0] - 0.2f && pRDP_TrackTargets->trk[i].x[0] > -0.3f)
                        {
                            prdpTrkObject->rdpTrkObjBoxCenterX = (prdpTrkObject->rdpTrkObjDistX + prdpTrkObject->rdpTrkObjBoxWidth / 2.f - 0.3f);
                        }
                        else
                        {
                            prdpTrkObject->rdpTrkObjBoxCenterX = prdpTrkObject->rdpTrkObjDistX;
                        }       
                    }

                }

                //航迹框中心一帧内变化大的，与上一帧滤波
                if (fabsf(prdpTrkObject->rdpTrkObjBoxCenterX - pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[2]) > (pRDP_TrackTargets->trk[i].left + pRDP_TrackTargets->trk[i].right) / 1.1f && pRDP_TrackTargets->trk[i].activeTrkCnt > 1)
                {
                    prdpTrkObject->rdpTrkObjBoxCenterX = LP_FILTER(prdpTrkObject->rdpTrkObjBoxCenterX, 0.5, pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[2]);
                }
                //目标航迹框中心一帧内变化大的，与上一帧滤波
                if (fabsf(prdpTrkObject->rdpTrkObjBoxCenterY - pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[3]) > (pRDP_TrackTargets->trk[i].front + pRDP_TrackTargets->trk[i].back) / 1.1f && pRDP_TrackTargets->trk[i].activeTrkCnt > 1)
                {
                    prdpTrkObject->rdpTrkObjBoxCenterY = LP_FILTER(prdpTrkObject->rdpTrkObjBoxCenterY, 0.5, pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[3]);
                }

                //航迹框横向偏出跟踪点，限制其航迹框中心
                if (prdpTrkObject->rdpTrkObjBoxCenterX < (prdpTrkObject->rdpTrkObjDistX - (pRDP_TrackTargets->trk[i].left + pRDP_TrackTargets->trk[i].right) / 2.f) \
                    && (pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LNG_PLUS_ANGLE || pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LNG_MINUS_ANGLE) \
                    && pRDP_TrackTargets->trk[i].x[0] > VALID_OPERATE_LAT_MIN_RANGE && pRDP_TrackTargets->trk[i].x[0] < VALID_OPERATE_LAT_MAX_RANGE)
                {
                    prdpTrkObject->rdpTrkObjBoxCenterX = prdpTrkObject->rdpTrkObjDistX - (pRDP_TrackTargets->trk[i].left + pRDP_TrackTargets->trk[i].right) / 2.f;
                }
                else if (prdpTrkObject->rdpTrkObjBoxCenterX > (prdpTrkObject->rdpTrkObjDistX + (pRDP_TrackTargets->trk[i].left + pRDP_TrackTargets->trk[i].right) / 2.f) \
                    && (pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LNG_PLUS_ANGLE || pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LNG_MINUS_ANGLE) \
                    && pRDP_TrackTargets->trk[i].x[0] > VALID_OPERATE_LAT_MIN_RANGE && pRDP_TrackTargets->trk[i].x[0] < VALID_OPERATE_LAT_MAX_RANGE)
                {
                    prdpTrkObject->rdpTrkObjBoxCenterX = prdpTrkObject->rdpTrkObjDistX + (pRDP_TrackTargets->trk[i].left + pRDP_TrackTargets->trk[i].right) / 2.f;
                }

                //if (prdpTrkObject->rdpTrkObjBoxCenterX < (prdpTrkObject->rdpTrkObjDistX - (pRDP_TrackTargets->trk[i].front + pRDP_TrackTargets->trk[i].back) / 2.f) \
    			//	&& ((pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LAT_PLUS_ANGLE1 && pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LAT_PLUS_ANGLE2) || (pRDP_TrackTargets->trk[i].headingAngle > -VALID_OPERATE_LAT_PLUS_ANGLE2 && pRDP_TrackTargets->trk[i].headingAngle < -VALID_OPERATE_LAT_PLUS_ANGLE1)) \
    			//	&& pRDP_TrackTargets->trk[i].x[1] > VALID_OPERATE_LNG_MIN_RANGE && pRDP_TrackTargets->trk[i].x[1] < VALID_OPERATE_LNG_MAX_RANGE)
                //{
                //	prdpTrkObject->rdpTrkObjBoxCenterX = prdpTrkObject->rdpTrkObjDistX - (pRDP_TrackTargets->trk[i].front+ pRDP_TrackTargets->trk[i].back) / 2.f;
                //}
                //else if (prdpTrkObject->rdpTrkObjBoxCenterX > (prdpTrkObject->rdpTrkObjDistX + (pRDP_TrackTargets->trk[i].front + pRDP_TrackTargets->trk[i].back) / 2.f) \
    			//	&& ((pRDP_TrackTargets->trk[i].headingAngle > VALID_OPERATE_LAT_PLUS_ANGLE1 && pRDP_TrackTargets->trk[i].headingAngle < VALID_OPERATE_LAT_PLUS_ANGLE2) || (pRDP_TrackTargets->trk[i].headingAngle > -VALID_OPERATE_LAT_PLUS_ANGLE2 && pRDP_TrackTargets->trk[i].headingAngle < -VALID_OPERATE_LAT_PLUS_ANGLE1)))
                //{
                //	prdpTrkObject->rdpTrkObjBoxCenterX = prdpTrkObject->rdpTrkObjDistX + (pRDP_TrackTargets->trk[i].front + pRDP_TrackTargets->trk[i].back) / 2.f;
                //}

                if (prdpTrkObject->rdpTrkObjDistX * pRDP_TrackTargets->trk[i].x[0] < 0.f)
                {
                    prdpTrkObject->rdpTrkObjDistX = pRDP_TrackTargets->trk[i].x[0];
                }
                //防止低速行人DOW误报
                if ((pRDP_TrackTargets->trk[i].objType == 1 || LowSpeedManInDOWArea(&pRDP_TrackTargets->trk[i])) && fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f \
                    && fabsf(prdpTrkObject->rdpTrkObjVrelY) > 1.85f && fabsf(prdpTrkObject->rdpTrkObjVrelY) < 3.8f && prdpTrkObject->rdpTrkObjDistY < 2.f)
                {
                    prdpTrkObject->rdpTrkObjVrelY *= 0.5f;
                }
                // 防止高速测试场景,  RCTA误报
                if ((0x02 == pRDP_inVehicleData->vdyGearState) && (HighSpeedCarInRCTAArea(&pRDP_TrackTargets->trk[i])) && (fabsf(pRDP_inVehicleData->vdySpeedInmps) < 0.1f))
                {
                    prdpTrkObject->rdpTrkObjVrelX -= (1.0f);   // 相当于强行将 57kph速度加到 60以上  不触发RCTA
                }
            }
            else
            {
                getTrackBoxCenter(boxCenter, &vehicleInfo, &pRDP_TrackTargets->trk[i]);
                prdpTrkObject->rdpTrkObjBoxCenterX = boxCenter[0];
                prdpTrkObject->rdpTrkObjBoxCenterY = boxCenter[1];
            }

            pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[0] = prdpTrkObject->rdpTrkObjDistX;
			pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[1] = prdpTrkObject->rdpTrkObjDistY;
			pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[2] = prdpTrkObject->rdpTrkObjBoxCenterX;
			pRDP_TrackTargets->trk[i].stored_last_16trk_outputXY[3] = prdpTrkObject->rdpTrkObjBoxCenterY;
            // 记录当前帧的输出状态
            float prevx = pRDP_TrackTargets->trk[i].stored_last_outputX[2], prevy = pRDP_TrackTargets->trk[i].stored_last_outputX[3];
			pRDP_TrackTargets->trk[i].stored_last_outputX[0] = pRDP_TrackTargets->trk[i].x[0];//prdpTrkObject->rdpTrkObjDistX;
			pRDP_TrackTargets->trk[i].stored_last_outputX[1] = pRDP_TrackTargets->trk[i].x[1];//prdpTrkObject->rdpTrkObjDistY;
            pRDP_TrackTargets->trk[i].stored_last_outputX[2]    = prdpTrkObject->rdpTrkObjVrelX;
            pRDP_TrackTargets->trk[i].stored_last_outputX[3]    = prdpTrkObject->rdpTrkObjVrelY;
            updateTrackAccelerate(&pRDP_TrackTargets->trk[i], prevx, prevy);
            prdpTrkObject->rdpTrkObjArelX = pRDP_TrackTargets->trk[i].stored_last_outputX[4];
            prdpTrkObject->rdpTrkObjArelY = pRDP_TrackTargets->trk[i].stored_last_outputX[5];

            //目标属性
            prdpTrkObject->id = i;
            prdpTrkObject->rdpTrkObjReliability     = pRDP_TrackTargets->trk[i].probOfExist;
            prdpTrkObject->rdpTrkObjHitCnt          = pRDP_TrackTargets->trk[i].hit;
            prdpTrkObject->rdpTrkObjMissCnt         = pRDP_TrackTargets->trk[i].miss;
            prdpTrkObject->rdpTrkObjVr0_hit         = pRDP_TrackTargets->trk[i].vr0_hit;
            prdpTrkObject->rdpTrkObjType            = pRDP_TrackTargets->trk[i].objType;
            prdpTrkObject->rdpTrkObjReferPosi       = pRDP_TrackTargets->trk[i].objReferPosi;
            prdpTrkObject->rdpTrkObjRcwOverLapx     = pRDP_TrackTargets->trk[i].rcwoverlapx;        // 主要用于RCW正后方目标重叠率的测试.
            prdpTrkObject->rdpTrkObjDowOverLapx     = pRDP_TrackTargets->trk[i].dowoverlapx;        // 主要用于DOW正后方目标重叠率的测试.
            prdpTrkObject->rdpTrkObjOverLapxCnt     = pRDP_TrackTargets->trk[i].rcwoverlapxcnt;        // 主要用于RCW_DO正后方目标的测试
			prdpTrkObject->rdpTrkObjDowShelterScene = pRDP_TrackTargets->trk[i].dowShelterScene;
            prdpTrkObject->rdpTrkObjdowguardCrossCnt = pRDP_TrackTargets->trk[i].dowguardCrossCnt;    // 应对产品护栏路段DOW走查
            prdpTrkObject->rdpTrkObjdowguardblocksence = pRDP_TrackTargets->dowguardrailblocksence;    // 实际不应该负值给具体目标  但是目前只有目标传输接口.无场景传输接口
            
            // 获取跟踪点所在的点云中横向和纵向的最近原始点值，此两值不一定对应同一个原始点
            if (pRDP_TrackTargets->trk[i].idx_1 >= 0)
            {
                prdpTrkObject->nearestTargetX = cdi[pRDP_TrackTargets->trk[i].idx_1].latLim[0];
                prdpTrkObject->nearestTargetY = cdi[pRDP_TrackTargets->trk[i].idx_1].lngLim[0];
            } 
            else
            {
                prdpTrkObject->nearestTargetX = 999.0f;
                prdpTrkObject->nearestTargetY = 999.0f;
            }

            x = pRDP_TrackTargets->trk[i].x[0];
            y = pRDP_TrackTargets->trk[i].x[1];
            if (fabsf(r) < MAX_RADIUS)
            {
                prdpTrkObject->rdpTrkObjcenterOffset = (r > 0) ? (r - sqrtf((r - x) * (r - x) + y * y)) : (r + sqrtf((r - x) * (r - x) + y * y));
            }
            else
            {
                prdpTrkObject->rdpTrkObjcenterOffset = x;
            }

            if(pRDP_TrackTargets->trk[i].activeTrkCnt < 0xFFFF)
            {
            	prdpTrkObject->rdpLifeCycleCnt = pRDP_TrackTargets->trk[i].activeTrkCnt;
            }
            else
            {
                prdpTrkObject->rdpLifeCycleCnt = 0xFFFF;
            }

            pRDP_TrkObjectList->rdpDetObjectNum++;
        }
        else if(pRDP_TrackTargets->trk[i].type == TRACK)    //不被显示的目标，补充x及vx历史值更新
        {
			pRDP_TrackTargets->trk[i].objDeleteFlag = 1;
            pRDP_TrackTargets->trk[i].stored_last_outputX[0]    = pRDP_TrackTargets->trk[i].x[0];
            pRDP_TrackTargets->trk[i].stored_last_outputX[1]    = pRDP_TrackTargets->trk[i].x[1];
            pRDP_TrackTargets->trk[i].stored_last_outputX[2]    = pRDP_TrackTargets->trk[i].x[2];
            pRDP_TrackTargets->trk[i].stored_last_outputX[3]    = pRDP_TrackTargets->trk[i].x[3];
            pRDP_TrackTargets->trk[i].stored_last_outputX[4]    = pRDP_TrackTargets->trk[i].x[4];
            pRDP_TrackTargets->trk[i].stored_last_outputX[5]    = pRDP_TrackTargets->trk[i].x[5];
        }
        else
        {
            prdpTrkObject->rdpTrkObjRange           = 0;
            prdpTrkObject->rdpTrkObjVelocity        = 0;
            prdpTrkObject->rdpTrkObjAzimuthAngle    = 0;
            prdpTrkObject->rdpTrkObjsnr             = 0;
            prdpTrkObject->rdpTrkObjReliability     = 0;
            prdpTrkObject->rdpTrkObjHitCnt          = 0;
            prdpTrkObject->rdpTrkObjMissCnt         = 0;
            prdpTrkObject->rdpTrkObjArelX           = 0;
            prdpTrkObject->rdpTrkObjArelY           = 0;
        }
    }
}

/**
 * @brief 初始化跟踪相关变量
 * 
 */
int32_t RDP_initTrack(uint8_t subFrameNum, float* rangeRateScope)
{
    int32_t retVal;

    gRDP_BKTargetsList.number     = 0;
    gRDP_BKTargetsList.raw_number = 0;
	fenceCntInBlindArea = 0;
	fencePointLatRange = 0;
	isCPTALFScene = 0;
    trk_pkg_init();
    initGlobalVars();
    initVdyStatus();

    /* Creat a new semaphore for track list copy */
#ifndef PC_DBG_FW
    RDP_createTrackerListSemaphore();
#endif
    /* Initialize vehicle info of RDP config */
    RDP_vehicleInfoCfgInit();
    retVal = RDP_setSubFramesDopplerVelocityScope(subFrameNum, rangeRateScope);

#ifdef PC_DBG_FW
    // trkDbgMode = 1;
#endif

    return retVal;
}

/**
 * @brief 
 * 
 * @param pRDP_inVehicleData 
 * @param pVehicleInfo 
 * @param config 
 */
void cfgUpdate(VDY_DynamicEstimate_t *pRDP_inVehicleData, VDY_DynamicEstimate_t* pVehicleInfo, rdp_config_t* config)
{
    float speed, speedDiff, yawRate;
    if(config->installPosition == SENSOR_POSITION_FRONT_LEFT || config->installPosition == SENSOR_POSITION_FRONT_RIGHT)
    {
        config->isFront = 1;
        speed = -pRDP_inVehicleData->vdySpeedInmps;
    }else if(config->installPosition == SENSOR_POSITION_REAR_LEFT || config->installPosition == SENSOR_POSITION_REAR_RIGHT)
    {
        config->isFront = 0;
        speed = pRDP_inVehicleData->vdySpeedInmps;
    }else
    {
        config->isFront = 0;
        speed = pRDP_inVehicleData->vdySpeedInmps;
#ifndef PC_DBG_FW
    EMBARC_PRINTF("invalid install position: %d\n", config->installPosition);
#endif
    }

    ////左右雷达rawRate正负适配
    //if (config->installPosition == SENSOR_POSITION_REAR_LEFT || config->installPosition == SENSOR_POSITION_FRONT_RIGHT)
    //{
    //    yawRate = (pVehicleInfo->vdyYawRate);
    //}
    //else
    //{
    //    yawRate = -(pVehicleInfo->vdyYawRate);
    //}

    speedDiff = speed - config->speed;
    if(speed == 0 && fabsf(speedDiff) > 1)
    {
        pVehicleInfo->vdySpeedInmps = config->speed;
        pVehicleInfo->vdyYawRate = config->yawRate;
        config->speedDiff = 0;
        //config->yawRateDiff = 0;
    }else
    {
        pVehicleInfo->vdySpeedInmps = speed;
        //pVehicleInfo->vdyYawRate = yawRate;
        config->speed = speed;
        config->yawRate = pVehicleInfo->vdyYawRate;
        config->speedDiff = speedDiff;
        //config->yawRateDiff = pVehicleInfo->vdyYawRate - config->yawRate;
    }

    config->vdyGearState = pVehicleInfo->vdyGearState;
}

uint8_t RDP_getObjMotionDirection(trk_t* trk, const VDY_DynamicEstimate_t *pRDP_inVehicleData, rdp_sensorPosition_e installPosition)
{
    uint8_t objMotionDirection = 0; 
    float AbsHeadingAngle = atan2f(trk->x[2], trk->x[3] - pRDP_inVehicleData->vdySpeedInmps)*RAD2ANG;
    switch(installPosition)
    {
        case SENSOR_POSITION_REAR_LEFT:
            if(AbsHeadingAngle > 0)
            {
                AbsHeadingAngle = 180 - AbsHeadingAngle;
            }
            else
            {
                AbsHeadingAngle = -180 - AbsHeadingAngle;
            }
            break;
        case SENSOR_POSITION_REAR_RIGHT:
            if(AbsHeadingAngle > 0)
            {
                AbsHeadingAngle = AbsHeadingAngle - 180;
            }
            else
            {
                AbsHeadingAngle = 180 + AbsHeadingAngle;
            }
            break;
        case SENSOR_POSITION_FRONT_LEFT:
            break;
        case SENSOR_POSITION_FRONT_RIGHT:
            AbsHeadingAngle = - AbsHeadingAngle;
            break;
        default:
            break;
    }
    if(fabsf(trk->x[2]) < 0.5f && fabsf(trk->x[3] - pRDP_inVehicleData->vdySpeedInmps) < 0.5f)
    {
        objMotionDirection = UNKNOWN_DYNPROP;     //微小的对地速度，视为方向未知
    }
    else if(fabsf(AbsHeadingAngle) >= 135.0f)
    {
        objMotionDirection = ONCOMING_DYNPROP;     //Oncoming
    }
    else if(fabsf(AbsHeadingAngle) <= 45.0f)
    {
        objMotionDirection = MOVING_DYNPROP;     //Ongoing
    }
    else
    {
        objMotionDirection = CROSSING_LEFT2RIGHT_DYNPROP;     //Crossing
    }
    return objMotionDirection;
}

static void RDP_getTrackUnit(rdp_config_t* config, RDP_TrkObjectInfo_t* pTrkObject, trk_t* trk, const VDY_DynamicEstimate_t *pRDP_inVehicleData)
{
    const stVehicleStatus *pVdy = getVdyStatus();
    float boxCenter[2];
    float powerVrelX, powerVrelY, powerVrelXStd, powerVrelYStd;

    #ifdef BYD_ORIN_OTGVEL_PLATFORM
    float otgVelx, otgVely;
    #endif

    pTrkObject->rdpTrkObjRange = trk->sim_z[1];
    pTrkObject->rdpTrkObjVelocity = trk->sim_z[2];
    pTrkObject->rdpTrkObjsnr = trk->sim_z[0];
	pTrkObject->rdpTrkObjElevetionAngle = trk->heighAngle;
    pTrkObject->rdpTrkObjStatus= trk->status;
    pTrkObject->rdpTrkObjMirrorProb = 0.f;      //默认输出0
    pTrkObject->rdpTrkObjStableFlag = 1;     //默认输出1

    pTrkObject->rdpTrkObjBoxLength = trk->front + trk->back;
    pTrkObject->rdpTrkObjBoxWidth = trk->left + trk->right;
    pTrkObject->rdpTrkObjReliability = trk->probOfExist;
    pTrkObject->rdpTrkObjType = trk->objType;
    if (trk->objType == 0)
    {
        pTrkObject->rdpTrkObjClassification = UNKNOW_CLASSIFICATION;
    }
    else if (trk->objType == 1)
    {
        pTrkObject->rdpTrkObjClassification = PEDESTPEDESTRIANRAIN_CLASSIFICATION;
    }
    else if (trk->objType == 2)
    {
        pTrkObject->rdpTrkObjClassification = CYCLIST_CLASSIFICATION;
    }
    else if (trk->objType == 3)
    {
        pTrkObject->rdpTrkObjClassification = CAR_CLASSIFICATION;
    }
    else if (trk->objType == 4)
    {
        pTrkObject->rdpTrkObjClassification = TRUCK_CLASSIFICATION;
    }

    pTrkObject->rdpTrkObjObstacleProb = trk->probOfExist;       //障碍物概率先以存在概率为值
    
    pTrkObject->rdpTrkObjReferPosi = trk->objReferPosi;
    pTrkObject->rdpTrkObjRcs = trk->rcs;

    pTrkObject->rdpTrkObjHitCnt = trk->hit;
    pTrkObject->rdpTrkObjMissCnt = trk->miss;

    if(trk->activeTrkCnt < 0xFFFF)
    {
        pTrkObject->rdpLifeCycleCnt = trk->activeTrkCnt;
    }
    else
    {
        pTrkObject->rdpLifeCycleCnt = 0xFFFF;
    }

    pTrkObject->rdpTrkObjRCSHighLevel = (uint16_t)(((float)trk->rcs / RCS_SCALE + 128.0f) * RCS_SCALE_FOR_130PRO);   //for 130pro output
    pTrkObject->rdpTrkObjMotionStat = 1;    //for 130pro output. default moving
    pTrkObject->rdpTrkObjMotionDir = RDP_getObjMotionDirection(trk, pRDP_inVehicleData, config->installPosition);     //for 130pro output. default unknown
    pTrkObject->rdpTrkObjDynProp = RDP_getObjMotionDirection(trk, pRDP_inVehicleData, config->installPosition);       //J6M协议mapping
    if(trk->activeTrkCnt == 1)
    {
        pTrkObject->rdpTrkObjDetectionStatus = 1;   //for 130pro output
        pTrkObject->rdpTrkObjMaintenanceState = NEW;  //J6M协议mapping
    }
    else if(trk->miss == 0)
    {
        pTrkObject->rdpTrkObjDetectionStatus = 2;   //for 130pro output
        pTrkObject->rdpTrkObjMaintenanceState = MEASURED;  //J6M协议mapping
    }
    else
    {
        pTrkObject->rdpTrkObjDetectionStatus = 3;   //for 130pro output
        pTrkObject->rdpTrkObjMaintenanceState = PREDICTED;  //J6M协议mapping
    }

    //加速度
    switch(config->installPosition)
    {
        case SENSOR_POSITION_REAR_LEFT:
            if(trk->sim_z[3] > 0)
                pTrkObject->rdpTrkObjAzimuthAngle = 180-trk->sim_z[3];
            else
                pTrkObject->rdpTrkObjAzimuthAngle = -180-trk->sim_z[3];
            pTrkObject->rdpTrkObjDistX = -trk->stored_last_16trk_outputXY[1];
            pTrkObject->rdpTrkObjDistY = trk->stored_last_16trk_outputXY[0];
            pTrkObject->rdpTrkObjVrelX = -trk->x[3];
            pTrkObject->rdpTrkObjVrelY = trk->x[2];
            pTrkObject->rdpTrkObjArelX = -trk->stored_last_outputX[5];
            pTrkObject->rdpTrkObjArelY = trk->stored_last_outputX[4];
            #ifdef BYD_ORIN_OTGVEL_PLATFORM
            //对地航向角
            otgVelx = trk->x[2] + pVdy->vcsLatVel;
            otgVely = -trk->x[3] + pVdy->vcsLngVel;

            pTrkObject->rdpTrkObjHeadingAngle = atan2f(otgVelx, otgVely) * RAD2ANG;
            #else
            //将航向角转换至车身坐标系下
            if(trk->headingAngle > 0)
            {
                pTrkObject->rdpTrkObjHeadingAngle = 180 - trk->headingAngle;
            }
            else
            {
                pTrkObject->rdpTrkObjHeadingAngle =  -180 - trk->headingAngle;
            }
            #endif

            pTrkObject->rdpTrkObjBoxCenterX = -trk->stored_last_16trk_outputXY[3];
            pTrkObject->rdpTrkObjBoxCenterY = trk->stored_last_16trk_outputXY[2];
            break;
        case SENSOR_POSITION_REAR_RIGHT:
            if(trk->sim_z[3] > 0)
                pTrkObject->rdpTrkObjAzimuthAngle = trk->sim_z[3]-180;
            else
                pTrkObject->rdpTrkObjAzimuthAngle = trk->sim_z[3]+180;
            pTrkObject->rdpTrkObjDistX = -trk->stored_last_16trk_outputXY[1];
            pTrkObject->rdpTrkObjDistY = -trk->stored_last_16trk_outputXY[0];
            pTrkObject->rdpTrkObjVrelX = -trk->x[3];
            pTrkObject->rdpTrkObjVrelY = -trk->x[2];
            pTrkObject->rdpTrkObjArelX = -trk->stored_last_outputX[5];
            pTrkObject->rdpTrkObjArelY = -trk->stored_last_outputX[4];
            #ifdef BYD_ORIN_OTGVEL_PLATFORM
            //对地航向角
            otgVelx = -trk->x[2] + pVdy->vcsLatVel;
            otgVely = -trk->x[3] + pVdy->vcsLngVel;

            pTrkObject->rdpTrkObjHeadingAngle = atan2f(otgVelx, otgVely) * RAD2ANG;
            #else
            //将航向角转换至车身坐标系下
            if(trk->headingAngle > 0)
            {
                 pTrkObject->rdpTrkObjHeadingAngle = trk->headingAngle - 180;
            }
            else
            {
                pTrkObject->rdpTrkObjHeadingAngle =  180 + trk->headingAngle;
            }
            
            #endif

            pTrkObject->rdpTrkObjBoxCenterX = -trk->stored_last_16trk_outputXY[3];
            pTrkObject->rdpTrkObjBoxCenterY = -trk->stored_last_16trk_outputXY[2];
            break;
        case SENSOR_POSITION_FRONT_LEFT:
            pTrkObject->rdpTrkObjAzimuthAngle = trk->sim_z[3];
            pTrkObject->rdpTrkObjDistX = trk->stored_last_16trk_outputXY[1];
            pTrkObject->rdpTrkObjDistY = trk->stored_last_16trk_outputXY[0];
            pTrkObject->rdpTrkObjVrelX = trk->x[3];
            pTrkObject->rdpTrkObjVrelY = trk->x[2];
            pTrkObject->rdpTrkObjArelX = trk->stored_last_outputX[5];
            pTrkObject->rdpTrkObjArelY = trk->stored_last_outputX[4];
            #ifdef BYD_ORIN_OTGVEL_PLATFORM
            //对地航向角
            otgVelx = trk->x[2] + pVdy->vcsLatVel;
            otgVely = trk->x[3] + pVdy->vcsLngVel;

            pTrkObject->rdpTrkObjHeadingAngle = atan2f(otgVelx, otgVely) * RAD2ANG;
            #else
            //将航向角转换至车身坐标系下
            pTrkObject->rdpTrkObjHeadingAngle = trk->headingAngle;
            #endif

            pTrkObject->rdpTrkObjBoxCenterX = trk->stored_last_16trk_outputXY[3];
            pTrkObject->rdpTrkObjBoxCenterY = trk->stored_last_16trk_outputXY[2];
            break;
        case SENSOR_POSITION_FRONT_RIGHT:
            pTrkObject->rdpTrkObjAzimuthAngle = -trk->sim_z[3];
            pTrkObject->rdpTrkObjDistX = trk->stored_last_16trk_outputXY[1];
            pTrkObject->rdpTrkObjDistY = -trk->stored_last_16trk_outputXY[0];
            pTrkObject->rdpTrkObjVrelX = trk->x[3];
            pTrkObject->rdpTrkObjVrelY = -trk->x[2];
            pTrkObject->rdpTrkObjArelX = trk->stored_last_outputX[5];
            pTrkObject->rdpTrkObjArelY = -trk->stored_last_outputX[4];
            #ifdef BYD_ORIN_OTGVEL_PLATFORM
            //对地航向角
            otgVelx = -trk->x[2] + pVdy->vcsLatVel;
            otgVely = trk->x[3]  + pVdy->vcsLngVel;

            pTrkObject->rdpTrkObjHeadingAngle = atan2f(otgVelx, otgVely) * RAD2ANG;
            #else
            //将航向角转换至车身坐标系下
            pTrkObject->rdpTrkObjHeadingAngle = - trk->headingAngle;
            #endif

            pTrkObject->rdpTrkObjBoxCenterX = trk->stored_last_16trk_outputXY[3];
            pTrkObject->rdpTrkObjBoxCenterY = -trk->stored_last_16trk_outputXY[2];
            break;
        default:
            pTrkObject->rdpTrkObjAzimuthAngle = trk->sim_z[3];
            pTrkObject->rdpTrkObjDistX = trk->stored_last_16trk_outputXY[1];
            pTrkObject->rdpTrkObjDistY = trk->stored_last_16trk_outputXY[0];
            pTrkObject->rdpTrkObjVrelX = trk->x[3];
            pTrkObject->rdpTrkObjVrelY = trk->x[2];
            pTrkObject->rdpTrkObjArelX = trk->stored_last_outputX[5];
            pTrkObject->rdpTrkObjArelY = trk->stored_last_outputX[4];
            #ifdef BYD_ORIN_OTGVEL_PLATFORM
            //对地航向角
            otgVelx = trk->x[2] + pVdy->vcsLatVel;
            otgVely = trk->x[3] + pVdy->vcsLngVel;
            
            pTrkObject->rdpTrkObjHeadingAngle = atan2f(otgVelx, otgVely) * RAD2ANG;
            #else
            //将航向角转换至车身坐标系下
            pTrkObject->rdpTrkObjHeadingAngle = trk->headingAngle;
            #endif

            pTrkObject->rdpTrkObjBoxCenterX = trk->stored_last_16trk_outputXY[3];
            pTrkObject->rdpTrkObjBoxCenterY = trk->stored_last_16trk_outputXY[2];
#ifndef PC_DBG_FW
//            EMBARC_PRINTF("invalid radar id: %d\n", aparRadarId);
#endif
            break;
    }

    pTrkObject->rdpTrkObjHeadingAngle = pTrkObject->rdpTrkObjHeadingAngle / RAD2ANG;

    // 赋值相对速度
    pTrkObject->rdpTrkObjRelVx = pTrkObject->rdpTrkObjVrelX;
    pTrkObject->rdpTrkObjRelVy = pTrkObject->rdpTrkObjVrelY;
    pTrkObject->rdpTrkObjRelVz = pTrkObject->rdpTrkObjVrelZ;

    #ifdef ADVANCE_MOMENTA_ALG_EN
    // Do nothing: For Compatibility is not in the case of ground speed, and nor is it the case of momenta's algorithm.
    #elif defined(BYD_ORIN_OTGVEL_PLATFORM)
    //转换为对地速度/加速度
    pTrkObject->rdpTrkObjArelX = pTrkObject->rdpTrkObjArelX + pRDP_inVehicleData->vdyAccelLong;
    pTrkObject->rdpTrkObjArelY = pTrkObject->rdpTrkObjArelY + pRDP_inVehicleData->vdyAccelLat;
    if (config->installPosition == SENSOR_POSITION_REAR_LEFT || config->installPosition == SENSOR_POSITION_REAR_RIGHT)
    {
        pTrkObject->rdpTrkObjVrelX = pTrkObject->rdpTrkObjVrelX + pVdy->vcsLngVel;
        pTrkObject->rdpTrkObjVrelY = pTrkObject->rdpTrkObjVrelY + pVdy->vcsLatVel;
    }
    else if (config->installPosition == SENSOR_POSITION_FRONT_LEFT || config->installPosition == SENSOR_POSITION_FRONT_RIGHT)
    {
        pTrkObject->rdpTrkObjVrelX = pTrkObject->rdpTrkObjVrelX + pVdy->vcsLngVel;
        pTrkObject->rdpTrkObjVrelY = pTrkObject->rdpTrkObjVrelY + pVdy->vcsLatVel;
    }
    #endif

    pTrkObject->objDistXStd = sqrtf(trk->P[7]);        //输出标准差
    pTrkObject->objDistYStd = sqrtf(trk->P[0]);        //输出标准差
    pTrkObject->objRelVelXStd = sqrtf(trk->P[21]);     //输出标准差
    pTrkObject->objRelVelYStd = sqrtf(trk->P[14]);     //输出标准差
    pTrkObject->objAccelXStd = sqrtf(trk->P[35]);      //输出标准差
    pTrkObject->objAccelYStd = sqrtf(trk->P[28]);      //输出标准差

    powerVrelX = pTrkObject->rdpTrkObjVrelX * pTrkObject->rdpTrkObjVrelX;
    powerVrelY = pTrkObject->rdpTrkObjVrelY * pTrkObject->rdpTrkObjVrelY;
    powerVrelXStd = pTrkObject->objRelVelXStd * pTrkObject->objRelVelXStd;
    powerVrelYStd = pTrkObject->objRelVelYStd * pTrkObject->objRelVelYStd;
    //根据误差传递公式，根据vx，vy的标准差计算航向角的标准差
    pTrkObject->rdpTrkObjHeadingAngleStd = sqrtf((powerVrelY * powerVrelXStd + powerVrelX + powerVrelYStd) / ((powerVrelY + powerVrelX) * (powerVrelY + powerVrelX)));
    pTrkObject->rdpTrkObjDistX += config->installOffsetX;
    pTrkObject->rdpTrkObjDistY += config->installOffsetY;
    pTrkObject->rdpTrkObjStartX = trk->startPosition[0];
    pTrkObject->rdpTrkObjStartY = trk->startPosition[1];
    pTrkObject->rdpTrkObjBoxCenterX += config->installOffsetX;
    pTrkObject->rdpTrkObjBoxCenterY += config->installOffsetY;

    pTrkObject->trkUpdataFlag = 0x01;

    return;
}

static void RDP_getTrackUnitGeely(rdp_config_t* config, trk_t *trk, ObjInfoGeely2_0 *obj, const VDY_DynamicEstimate_t *pVdy, uint8_t id)
{
    const stVehicleStatus* pVdyInfo = getVdyStatus();

    
    obj->DyStdDe = trk->P[0];
    obj->DxStdDe = trk->P[7];
    obj->VyStdDe = trk->P[14];
    obj->VxStdDe = trk->P[21];
    obj->NotRealProblty = 0x0;
    obj->Conf = trk->probOfExist;
    obj->BoxWidth = trk->left + trk->right;
    obj->BoxLength = trk->front + trk->back;
    obj->ObservationHist = trk->hitBmp;
    obj->TiAlv = (trk->activeTrkCnt > 127) ? 127 : trk->activeTrkCnt;
    obj->CoastCnt = (trk->miss > 6) ? 6 : trk->miss;
    obj->ID = id + 1;   //从1开始
    obj->TypConfVeh = 10;
    obj->TypConfBike = 10; 
    obj->TypConfPed = 10;

    //目标分类 0-未知，4-行人，9-两轮车，10-四轮车
    if(trk->objType == 1)
    {
        obj->Typ = 4;
        obj->TypConfPed = 80;
    }
    else if(trk->objType == 2)
    {
        obj->Typ = 9;
        obj->TypConfBike = 80; 
    }
    else if(trk->objType == 3 || trk->objType == 4)
    {
        obj->Typ = 10;
        obj->TypConfVeh = 80;
		if (obj->BoxWidth < 1.5f)
		{
			obj->BoxWidth = 1.5f;
		}
    }
    else
    {
        obj->Typ = 0;
    }

    //跟踪状态 0-未知  1-updata    2-newTrack  3-预测
    if(trk->activeTrkCnt == 1)
    {
        obj->TrackSts = 2;
    }
    else if(trk->miss == 0)
    {
        obj->TrackSts = 1;
    }
    else
    {
        obj->TrackSts = 3;
    }

	obj->Dy = trk->stored_last_16trk_outputXY[0]; //boxNearestPoint[0];//
	obj->Dx = trk->stored_last_16trk_outputXY[1]; //boxNearestPoint[1];//
	obj->ObjBoxCenterLgt = trk->stored_last_16trk_outputXY[3];//boxCenter[1];
	obj->ObjBoxCenterLat = trk->stored_last_16trk_outputXY[2];// boxCenter[0];
    //位置、速度、加速度、航向角--转换到车身坐标系  TODO:速度和加速度要做成对地的

    obj->Vy = trk->x[2];
	obj->Vx = trk->x[3];
    obj->Ay = trk->stored_last_outputX[4];
    obj->Ax = trk->stored_last_outputX[5];
    obj->RelVy = trk->x[2];
    obj->RelVx = trk->x[3];

	//低速在正后方保持的目标横向速度输出的时候减小一点，防止早退
	if (trk->keepInBackCnt > 0 && trk->keepInBackCnt < 79 && trk->crossAllCnt > 20)
	{
		if ((trk->x[2] > -15 / 3.6f && trk->x[2] < -10 / 3.6f))
		{
			obj->Vy = LP_FILTER(trk->x[2], 0.25, -1.2f);
			obj->RelVy = LP_FILTER(trk->x[2], 0.25, -1.2f);
		}
		else if (trk->x[2] > -10 / 3.6f && trk->x[2] < -1.2f)
		{
			obj->Vy = LP_FILTER(trk->x[2], 0.5, -1.2f);
			obj->RelVy = LP_FILTER(trk->x[2], 0.5, -1.2f);
		}
	}
	//RCW 正后方限制横向速度
	if (obj->Dy < 0.f && obj->Dy > -2.f \
		&& obj->Dx > 0.5f && fabsf(pVdyInfo->speed) < 0.1f\
		&& (trk->headingAngle > VALID_OPERATE_LNG_PLUS_ANGLE  || trk->headingAngle < VALID_OPERATE_LNG_MINUS_ANGLE) \
		&& (trk->startPosition[0] > -2.5 && trk->startPosition[0] < 0.5) \
		&& fabsf(obj->Dx / obj->RelVx) < 1.8f)
	{
		obj->RelVy *= 0.3;
		obj->Vy *= 0.3;
	}
	
    switch(config->installPosition)
    {
        case SENSOR_POSITION_REAR_LEFT:
        obj->Dx = -obj->Dx;
        obj->Vx = -obj->Vx;
        obj->Ax = -obj->Ax;               //纵向加速度
        obj->RelVx = -obj->RelVx;
        obj->ObjBoxCenterLgt = -obj->ObjBoxCenterLgt;
		if (config->installOffsetY < 0)
			config->installOffsetY = -config->installOffsetY;
        break;

        case SENSOR_POSITION_REAR_RIGHT:
        obj->Dy = -obj->Dy;
        obj->Dx = -obj->Dx;
        obj->Vy = -obj->Vy;
        obj->Vx = -obj->Vx;
        obj->Ay = -obj->Ay;               //横向加速度
        obj->Ax = -obj->Ax;               //纵向加速度
        obj->RelVy = -obj->RelVy;
        obj->RelVx = -obj->RelVx;
        obj->ObjBoxCenterLgt = -obj->ObjBoxCenterLgt;
        obj->ObjBoxCenterLat = -obj->ObjBoxCenterLat;
		if(config->installOffsetY > 0)
			config->installOffsetY = -config->installOffsetY;
        break;

        case SENSOR_POSITION_FRONT_LEFT:
			if (config->installOffsetY < 0)
				config->installOffsetY = -config->installOffsetY;
        break;

        case SENSOR_POSITION_FRONT_RIGHT:
			obj->Dy = -obj->Dy;
			obj->Vy = -obj->Vy;
			obj->Ay = -obj->Ay;               //横向加速度
			obj->RelVy = -obj->RelVy;
			obj->ObjBoxCenterLat = -obj->ObjBoxCenterLat;
			if (config->installOffsetY > 0)
				config->installOffsetY = -config->installOffsetY;
        break;

        default:
        break;
    }
    //绝对信息转换
    obj->Vy = obj->Vy - pVdyInfo->vcsLatVel;
    obj->Vx = obj->Vx + pVdyInfo->vcsLngVel;
    obj->Ay = obj->Ay + pVdyInfo->latAcceleration;
    obj->Ax = obj->Ax + pVdyInfo->lngAcceleration;
    //航向角
    obj->Heading = atan2f(obj->Vy, obj->Vx) * 180.f / M_PI;

    //补偿到后轴中心
    obj->ObjBoxCenterLgt -= obj->Dx;    //需要的是基于最近点的
    obj->ObjBoxCenterLat -= obj->Dy;
    obj->Dx += config->installOffsetX;
    obj->Dy += config->installOffsetY;
	//防止低速行人DOW误报
	if (((trk->objType == 1) || LowSpeedManInDOWArea(trk)) && fabsf(pVdyInfo->speed) < 0.1f\
		&& fabsf(obj->RelVx) > 1.85f && fabsf(obj->RelVx) < 3.8f && trk->stored_last_16trk_outputXY[1] < 2.f)
	{
		obj->RelVx *= 0.5f;
		obj->Vx *= 0.5f;
	}
    //目前输出的都是运动目标
    obj->MtnPat = 0x3;

    //俯仰暂时默认值，等待数据验证
    obj->ElevnSts = 0x2;
    obj->ElevnConf = 10;
    
    //是否可达区域
    obj->IsInFreeSpace = trk->inFreeSpaceProb > 60.0f ? 0x1 : 0x0;
}

//吉利2.0/3.0 帧目标信息更新
void RDP_UpdateGeelyObj(trk_pkg_t *pTrackList ,const VDY_DynamicEstimate_t *pVdy, RDP_TrkFrameInfoGeely2_0 *pGeelyTrkObj, const RSP_DetObjectList_t *pDetRawData)
{
    //思路：
    //1、直接过滤横向±N米外的目标。
    //2、计算权重,根据直接根据径向距离来。
    //3、排序。
    //4、过滤输出优先级较高的目标。

    static uint16_t objWeight[MAX_NUM_OF_TRACKS];
    uint16_t maxWeight = 0xFFFF;
    memset(&(objWeight[0]), 0xFFFF, sizeof(objWeight));

    //计算权重值
    //过滤超范围的目标 ,并计算权重值
    for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        //无效目标 
        if (pTrackList->trk[i].sim_z[1] < 0.1f 
                || (pTrackList->trk[i].type != TRACK)
                || ((pTrackList->trk[i].status & TRACK_STATUS_MOVING_BMP) == 0) )
        {
            pTrackList->trk[i].weight = 0xFFFF;
            objWeight[i] = pTrackList->trk[i].weight;
            continue;
        }

        //计算权重值--- 客户没有特殊要求，直接按照径向距离来输出
        pTrackList->trk[i].weight = (uint16_t)roundf(pTrackList->trk[i].sim_z[1]);
        objWeight[i] = pTrackList->trk[i].weight;
    }

    //排序
    do_BubbleSort_Uint16(&(objWeight[0]), MAX_NUM_OF_TRACKS);
    maxWeight = (objWeight[GEELY2_0_SEND_OBJECT_NUM - 1] < objWeight[GEELY2_0_SEND_OBJECT_NUM]) ? objWeight[GEELY2_0_SEND_OBJECT_NUM - 1] : (objWeight[GEELY2_0_SEND_OBJECT_NUM - 1] - 1);

    //得到输出优先级较高的目标
    memset((void*)pGeelyTrkObj, 0, sizeof(RDP_TrkFrameInfoGeely2_0));
    uint8_t putObjNum = 0;
#if 1
    for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        if (pTrackList->trk[i].weight <= maxWeight && pTrackList->trk[i].objFakeDOt == 0)
        {
			// BSD超车区域延迟两帧输出
			//if (pTrackList->trk[i].x[1] < 0 && pTrackList->trk[i].activeTrkCnt < 3 \
			//	&& pTrackList->trk[i].x[3] < 15.f / 3.6f && (pVdy->vdySpeedInmps > 30.f/3.6f) \
			//	&& !(pTrackList->trk[i].status & TRACK_STATUS_CROSSING_BMP))
			//{
			//	continue;
			//}
			//异侧横穿低速目标限制输出
			if ((pTrackList->trk[i].activeTrkCnt < 10 || (pTrackList->trk[i].activeTrkCnt < 50 && pTrackList->trk[i].status & TRACK_STATUS_CROSSING_BMP)) \
				&& pTrackList->trk[i].x[2] > 0.f && pTrackList->trk[i].x[2] < 4.f \
				&& fabsf(pTrackList->trk[i].x[3]) < 2.f
				&& pTrackList->trk[i].x[0] < 3.f && pTrackList->trk[i].x[1] < 5.f \
				&& pTrackList->trk[i].startPosition[0] < -1.f && pTrackList->trk[i].startPosition[0] > -3.5f \
				&& pTrackList->trk[i].startPosition[1] < 5.f && fabsf(pVdy->vdySpeedInmps) < 0.1f )
			{
				continue;
			}
			// 异侧超过76°的目标不输出
			if (pTrackList->trk[i].objDeleteFlag == 1 && pTrackList->trk[i].x[0] < 0.f && pTrackList->trk[i].x[1] > 0.f)
			{
				continue;
			}
			// 对向来车在0到5m内不显示
			//if ((pTrackList->trk[i].status & TRACK_STATUS_REVERSE_BMP) && \
			//	pTrackList->trk[i].activeTrkCnt < 10 && pTrackList->trk[i].x[1] > 0.f && pTrackList->trk[i].x[1] < 5.f \
			//	&& pTrackList->trk[i].startPosition[1] > 0.f)
			//{
			//	continue;
			//}

            RDP_getTrackUnitGeely(RDP_getTrackConfigPointer(), &pTrackList->trk[i], &pGeelyTrkObj->obj[putObjNum], pVdy, i);
            putObjNum++;
        }
    }

    pGeelyTrkObj->ReSideRdrLeStsRdrNrObj = putObjNum;
    pGeelyTrkObj->ReSideRdrLeStsRdrStsBlkd0bin = pDetRawData->blockFlag;
    pGeelyTrkObj->ReSideRdrLeStsRdrStsBlkdConf = pDetRawData->blockPercent;
    pGeelyTrkObj->ReSideRdrLeStsRdrNrDetn = pDetRawData->rspDetObjectNum;
    pGeelyTrkObj->ReSideRdrLeStsRdrStsDstbc = pDetRawData->intPercent;
    pGeelyTrkObj->trkUpdataFlag = 0x01;
#else
    for(int i = 0; i < 15; i++)
    {
        ObjInfoGeely2_0 *obj = &pGeelyTrkObj->obj[i];
        //基本指标
        obj->Dy                = 3.2;         //横向位置-最近点
        obj->Dx                = -15.2;         //纵向位置-最近点
        obj->Vy                = -3.2;        //横向速度-对地
        obj->Vx                = -1.1;        //纵向速度-对地
        obj->RelVy             = -1.2;        //横向速度-相对
        obj->RelVx             = -1.3;        //纵向速度-相对
        obj->Ay                = -1.4;        //横向加速度
        obj->Ax                = -1.5;        //纵向加速度
        obj->DyStdDe           = -1.6;       //横向位置的标准差
        obj->DxStdDe           = -1.7;        //纵向位置的标准差
        obj->VyStdDe           = -1.8;        //横向速度标准差
        obj->VxStdDe           = -1.9;        //纵向速度标准差
        obj->Heading           = 2.0;            //航向角-对地
        //航迹框
        obj->BoxWidth          = 2;          //box的宽度
        obj->BoxLength         = 3;          //box的长度
        obj->ObjBoxCenterLgt   = 1.1;        //box中心相对于最近点的纵向位置
        obj->ObjBoxCenterLat   = -1.2;  //box中心相对于最近点的横向位置
        //跟踪状态
        obj->ObservationHist=0xFFFF0000;  //每个周期中目标关联的结果,最近32帧
        obj->TrackSts      = 2;         //跟踪状态  0-未知  1-updata    2-newTrack  3-预测
        obj->TiAlv         = 120;            //生命周期 从0到127,到达127后保持
        obj->CoastCnt      = 4;         //未关联的周期数    最大循环数应该是6.
        obj->Typ           = 9;              //目标分类  0-未知，4-行人，9-两轮车，10-四轮车
        obj->ID            = 5;               //航迹ID
        //概率
        obj->NotRealProblty = 90;   //假目标跟踪的概率
        obj->Conf           = 91;             //置信度
        obj->TypConfVeh      = 92;       //分类为车的置信度
        obj->TypConfBike     = 93;      //分类自行车的置信度    
        obj->TypConfPed     =94;       //分类行人的置信度 
        obj->ElevnConf       =95;        //俯仰可信度
        
        //增加
        obj->ElevnSts        =2;         //是否可跨越 0-未知  1-可跨越  2-同一个水平面上  3-可从下方开过
        obj->IsInFreeSpace   =1;      //是否自由空间内的目标 0-NO 1-Yes
        obj->MtnPat          =6;           //目标运动模式 0-未知 1-静态 2-运动到静止 3-运动中 4-远离 5-靠近 6-左到右横穿  7-右到左横穿
    }
    pGeelyTrkObj->ReSideRdrLeStsRdrNrObj = 15;//putObjNum;
    pGeelyTrkObj->ReSideRdrLeStsRdrStsBlkd0bin = 1;//pDetRawData->blockFlag;
    pGeelyTrkObj->ReSideRdrLeStsRdrStsBlkdConf = 95;//pDetRawData->blockPercent;
    pGeelyTrkObj->ReSideRdrLeStsRdrNrDetn = 125;//pDetRawData->rspDetObjectNum;
    pGeelyTrkObj->ReSideRdrLeStsRdrStsDstbc = 96;//pDetRawData->intPercent;
#endif
}

/*!*****************************************************************************************************************
 * \brief
 * Function Name       :    RDP_getTrackInfo
 *
 * \par
 * <b>Description</b>  :    
 *
 * @param        [in]       *pRDP_inVehicleData :  pointer of vehicle status info
 * @param        [out]      *pRDP_TrkObjectList : header of track object array
 * @param        [out]      *num : pointer of the number of the objects
 * @return                  -1: pointer none; 0: ok;
 *
 * @pre
 * @
 *
 *******************************************************************************************************************
 */

#define TRACK_SECOND_LEVEL_NUM_MAX OUTPUT_OBJ_NUM_MAX
#define TRACK_THIRD_LEVEL_NUM_MAX OUTPUT_OBJ_NUM_MAX
#define CROSS_RANGE_LIMIT_THIS_SIDE_FOR_OUT     6.0f
#define CROSS_RANGE_LIMIT_OTHER_SIDE_FOR_OUT    5.0f

int32_t RDP_getUserTrackInfo(const VDY_DynamicEstimate_t *pRDP_inVehicleData, RDP_TrkObjectInfo_t *pRDP_TrkObject, int16_t* num, int16_t nearTrackId[4])
{
    uint16_t outputTrackNum = 0, i;
    trk_pkg_t * pTrackPkg = RDP_getTrackTargetsPointer();
    trk_t* trk = NULL;
    uint8_t trackIdSecondLevel[TRACK_SECOND_LEVEL_NUM_MAX];
    uint8_t trackIdThirdLevel[TRACK_THIRD_LEVEL_NUM_MAX];
    uint8_t trackNumSecondLevel = 0;
    uint8_t trackNumThirdLevel = 0;
    uint8_t overFlowFlag = 0;
    uint8_t overFlowFlagSecondLevel = 0;
    uint8_t overFlowFlagThirdLevel = 0;
//    uint8_t trackIdEgoLane[OUTPUT_OBJ_NUM_MAX];
//    uint8_t trackIdSideLane[OUTPUT_OBJ_NUM_MAX];
//    uint8_t trackNumEgoLane = 0;
//    uint8_t trackNumSideLane = 0;
    uint8_t trackIdEgoNearest = TRACK_ID_INVALID;
    uint8_t scdtrackIdEgoNearest = TRACK_ID_INVALID;
    uint8_t trackIdSideLaneNearest = TRACK_ID_INVALID;
    uint8_t scdtrackIdSideLaneNearest = TRACK_ID_INVALID;
    float  nearestRangeEgoLane = 1e3;
    float  scdnearestRangeEgoLane = 1e3;
    float  nearestRangeSideLane = 1e3;
    float  scdnearestRangeSideLane = 1e3;
    if(pRDP_inVehicleData == NULL || pRDP_TrkObject == NULL || num == NULL)
    {
        return -1; // 空指针
    }
    VDY_DynamicEstimate_t vehicleInfo;
    memcpy(&vehicleInfo, pRDP_inVehicleData,sizeof(VDY_DynamicEstimate_t));
    /*
        第1优先级：非倒档: 本车道和旁边车道(横向-3m~4m)，倒档: 距离10m以内
        第2优先级：非倒档: 横向-5m~6m, 倒档:  距离40m以内
        第3优先级：其他运动目标
    */
//    APAR_CfgData_t* paparData = APAR_getAparDataPtr();  //获取apar参数指针
    rdp_config_t*config = RDP_getTrackConfigPointer();
    if(config->isFront)
        vehicleInfo.vdySpeedInmps = -vehicleInfo.vdySpeedInmps;
    for(i = 0; i < MAX_NUM_OF_TRACKS; i++)
    {
        trk = &pTrackPkg->trk[i];
        if(trk->type == TRACK && trk->status&TRACK_STATUS_MOVING_BMP)
        { 
            if ((config->installPosition == SENSOR_POSITION_REAR_LEFT) || (config->installPosition == SENSOR_POSITION_REAR_RIGHT))
            {
                if (fabsf(trk->stored_last_16trk_outputXY[1]) >= 99.f) // DBC要求后角限制在100m内
                {
                    continue;
                }
            } 
            if(pRDP_inVehicleData->vdyDriveDirection) // 倒档
            {
                if(trk->sim_z[1] < 10)
                {
                    if(outputTrackNum < OUTPUT_OBJ_NUM_MAX)
                    {
                        RDP_getTrackUnit(config, &pRDP_TrkObject[outputTrackNum], trk, &vehicleInfo);
                        pRDP_TrkObject[outputTrackNum].id = i;
                        outputTrackNum++;
                    }else
                    {
                        overFlowFlag = 1;
                        break;
                    }
                }else if(trk->sim_z[1] < 40)
                {
                    if(trackNumSecondLevel < TRACK_SECOND_LEVEL_NUM_MAX)
                    {
                        trackIdSecondLevel[trackNumSecondLevel] = i;
                        trackNumSecondLevel++;
                    }else if(trackNumThirdLevel < TRACK_THIRD_LEVEL_NUM_MAX)
                    {
                        trackIdThirdLevel[trackNumThirdLevel] = i;
                        trackNumThirdLevel++;
                        overFlowFlagSecondLevel = 1;
                    }else
                    {
                        overFlowFlagThirdLevel = 1;
                    }
                    
                }else
                {
                    if(trackNumThirdLevel < TRACK_THIRD_LEVEL_NUM_MAX)
                    {
                        trackIdThirdLevel[trackNumThirdLevel] = i;
                        trackNumThirdLevel++;
                    }else
                    {
                        overFlowFlagThirdLevel = 1;
                    }
                }
            }else
            {
                if(trk->x[0] >= EGO_LANE_CROSS_LIMIT_FLOOR && trk->x[0] < POSITIVE_SIDE_LANE_CROSS_LIMIT_CEIL)
                {
                    if(outputTrackNum < OUTPUT_OBJ_NUM_MAX)
                    {
                        RDP_getTrackUnit(config, &pRDP_TrkObject[outputTrackNum], trk, &vehicleInfo);
                        pRDP_TrkObject[outputTrackNum].id = i;
                        if(trk->x[0] >= EGO_LANE_CROSS_LIMIT_FLOOR && trk->x[0] < EGO_LANE_CROSS_LIMIT_CEIL)
                        {
                            if(trk->sim_z[1] < nearestRangeEgoLane)     //nearest
                            {
                                scdnearestRangeEgoLane = nearestRangeEgoLane;  //1st->2nd
                                scdtrackIdEgoNearest = trackIdEgoNearest;      //1st->2nd
                                nearestRangeEgoLane = trk->sim_z[1];
                                trackIdEgoNearest = i;
                            }
                            else if(trk->sim_z[1] < scdnearestRangeEgoLane)     //second nearest
                            {
                                scdnearestRangeEgoLane = trk->sim_z[1];
                                scdtrackIdEgoNearest = i;
                            }
                        }
                        if(trk->x[0] >= POSITIVE_SIDE_LANE_CROSS_LIMIT_FLOOR && trk->x[0] < POSITIVE_SIDE_LANE_CROSS_LIMIT_CEIL)
                        {
                            if(trk->sim_z[1] < nearestRangeSideLane)    //nearest
                            {
                                scdnearestRangeSideLane = nearestRangeSideLane;         //1st->2nd
                                scdtrackIdSideLaneNearest = trackIdSideLaneNearest;     //1st->2nd
                                nearestRangeSideLane = trk->sim_z[1];
                                trackIdSideLaneNearest = i;
                            }
                            else if(trk->sim_z[1] < scdnearestRangeSideLane)     //second nearest
                            {
                                scdnearestRangeSideLane = trk->sim_z[1];
                                scdtrackIdSideLaneNearest = i;
                            }
                        }
                        outputTrackNum++;
                    }else
                    {
                        overFlowFlag = 1;
                        break;
                    }
                }else if(fabsf(vehicleInfo.vdySpeedInmps) > 1.f && trk->x[0] < EGO_LANE_CROSS_LIMIT_FLOOR && trk->x[1] < 20.f)
                {
                    continue;
                }else if(trk->x[0] > -5 && trk->x[0] < 6)
                {
                    if(trackNumSecondLevel < TRACK_SECOND_LEVEL_NUM_MAX)
                    {
                        trackIdSecondLevel[trackNumSecondLevel] = i;
                        trackNumSecondLevel++;
                    }else if(trackNumThirdLevel < TRACK_THIRD_LEVEL_NUM_MAX)
                    {
                        trackIdThirdLevel[trackNumThirdLevel] = i;
                        trackNumThirdLevel++;
                        overFlowFlagSecondLevel = 1;
                    }else
                    {
                        overFlowFlagThirdLevel = 1;
                    }
                    
                }else
                {
                    if(trackNumThirdLevel < TRACK_THIRD_LEVEL_NUM_MAX)
                    {
                        trackIdThirdLevel[trackNumThirdLevel] = i;
                        trackNumThirdLevel++;
                    }else
                    {
                        overFlowFlagThirdLevel = 1;
                    }
                }
            }
        }
    }
    if(overFlowFlag)
    {
        *num = outputTrackNum;
    }else
    {
        for(i = 0; i < trackNumSecondLevel && outputTrackNum < OUTPUT_OBJ_NUM_MAX; i++)
        {
            RDP_getTrackUnit(config, &pRDP_TrkObject[outputTrackNum], &pTrackPkg->trk[trackIdSecondLevel[i]], &vehicleInfo);
            pRDP_TrkObject[outputTrackNum].id = trackIdSecondLevel[i];
            outputTrackNum++;
        }
        for(i = 0; i < trackNumThirdLevel && outputTrackNum < OUTPUT_OBJ_NUM_MAX; i++)
        {
            RDP_getTrackUnit(config, &pRDP_TrkObject[outputTrackNum], &pTrackPkg->trk[trackIdThirdLevel[i]], &vehicleInfo);
            pRDP_TrkObject[outputTrackNum].id = trackIdThirdLevel[i];
            outputTrackNum++;
        }
        *num = outputTrackNum;
    }
    nearTrackId[0] = trackIdEgoNearest;         //in order: 本1, 邻1, 本2, 邻2
    nearTrackId[1] = trackIdSideLaneNearest;
    nearTrackId[2] = scdtrackIdEgoNearest;
    nearTrackId[3] = scdtrackIdSideLaneNearest;
    return 0;
}

// 护栏拟合
static void statFence(cdi_pkg_t *pCdiPkg, int *weekCnt, int *strongCnt, sideLine_pkr_t *pSideLinePkg)
{
	const stVehicleStatus *pVdy = getVdyStatus();
	float yawrate = pVdy->yawrate * 180.0f / M_PI;
	float carSpeed = pVdy->compSpeed;
	float lat;
	float latDiff = 0.0f, lngDiff = 0.0f;
	int j = 0;
	int k = 0;
	cdi_t *pCdi = NULL, *pChosen = NULL;
	int idx = -1;
	*weekCnt = 0;
	*strongCnt = 0;

	//初步统计强弱侧护栏点
	for (int i = 0; i < pCdiPkg->number; i++)
	{
		if ((!(pCdiPkg->cdi[i].status & POINT_STATUS_DYNAMIC_BMP) && carSpeed > 0.1f)
			|| (fabsf(pCdiPkg->cdi[i].mea_z[2]) < 0.1f && carSpeed < 0.1f))
		{
			lat = pCdiPkg->cdi[i].x;
			if (lat > 0)
			{
				pCdiPkg->cdi[i].fence = 1;
				(*strongCnt)++;
			}
			//else // 屏蔽弱侧护栏
			//{
			//	pCdiPkg->cdi[i].fence = -1;
			//	(*weekCnt)++;
			//}
		}
	}

	//float rlng = 1000.f, llng = 1000.f;
	//查找
	//for (int i = 0; i < pCdiPkg->number; i++)
	//{
	//	if (pCdiPkg->cdi[i].fence == -1 && pCdiPkg->cdi[i].x < -2.5f)
	//	{
	//		pChosen = &pCdiPkg->cdi[i];
	//		idx = i;
	//		pSideLinePkg->weekFenceRange[0] = pChosen->y; // 记录纵向最近点
	//		break;
	//	}
	//}
	//查找一个纵向最近点
	/*for (int i = 0; i < pCdiPkg->number; i++)
	{
		if (idx == -1)
		{
			break;
		}
		if (pCdiPkg->cdi[i].fence == -1 && pCdiPkg->cdi[i].x < -2.5f)
		{
			if (pCdiPkg->cdi[i].y < pChosen->y && fabsf(pCdiPkg->cdi[i].mea_z[1] - pChosen->mea_z[1]) < 2.f)
			{
				pChosen = &pCdiPkg->cdi[i];
				idx = i;
			}
		}
	}*/

	//for (int i = 0; i < pCdiPkg->number; i++)
	//{
	//	if (idx == -1)
	//	{
	//		break;
	//	}
	//	if (pCdiPkg->cdi[i].fence == -1)//左
	//	{
	//		if (idx == i)
	//		{
	//			continue;
	//		}
	//		pCdi = &pCdiPkg->cdi[i];
	//		if (pChosen->y < pCdi->y || (pChosen->y < 3.f && pCdi->y < 3.f))
	//		{
	//			latDiff = pCdi->x - pChosen->x;
	//			lngDiff = pCdi->y - pChosen->y;
	//			//缓行时，减小纵向门限，避免横穿的径向速度小目标被误判护栏
	//			//转弯时，关联的下一护栏点和当前护栏点的横向距离差要与yawrate同向
	//			//起步变道时，不做要求
	//			if (1/*gTrkPkg.gTunnelFlag == 0*/)
	//			{
	//				if (((fabsf(lngDiff) > 20.f || fabsf(latDiff) > 1.0f) && fabsf(yawrate) < 3.f && carSpeed > 8.f)
	//					|| ((fabsf(lngDiff) > 5.f || fabsf(latDiff) > 0.5f) && fabsf(yawrate) < 3.f && carSpeed < 8.f)
	//					|| ((fabsf(lngDiff) > 10.f || fabsf(latDiff) > 3.0f || latDiff * yawrate < 0) && fabsf(yawrate) > 3.f && carSpeed > 8.f)
	//					|| ((fabsf(lngDiff) > 10.f || fabsf(latDiff) > 3.0f) && fabsf(yawrate) > 3.f && carSpeed < 8.f))
	//					//|| (fabsf(lngDiff) > 5.f && fabsf(yawrate) > 8.f))
	//				{
	//					pCdiPkg->cdi[i].fence = -2;		//横向距离小于0，但有可能为右护栏点
	//					(*weekCnt)--;
	//				}
	//				else
	//				{
	//					pChosen = pCdi;
	//					idx = i;
	//				}
	//			}
	//			else
	//			{
	//				if (((fabsf(lngDiff) > 60.f || fabsf(latDiff) > 0.4f) && fabsf(yawrate) < 1.f)
	//					|| ((fabsf(lngDiff) > 20.f || fabsf(latDiff) > 0.8f) && fabsf(yawrate) > 1.f))
	//				{
	//					pCdiPkg->cdi[i].fence = 0;		//隧道模式下
	//					(*weekCnt)--;
	//				}
	//				else
	//				{
	//					pChosen = pCdi;
	//					idx = i;
	//				}
	//			}
	//		}
	//		else
	//		{
	//			pCdiPkg->cdi[i].fence = 0;
	//			(*weekCnt)--;
	//		}
	//	}
	//}

	//强侧护栏
	idx = -1;
	pCdi = NULL, pChosen = NULL;
	uint8_t sideLineLat[20] = { 0 };		//统计横向距离在0到10m的护栏点数量
	float sideLineLngMin[20];
	for (int i = 0; i < 20; i++)
	{
		sideLineLngMin[i] = 100.f;
	}
	for (int i = 0; i < pCdiPkg->number; i++)
	{
		if (pCdiPkg->cdi[i].fence == 1 && pCdiPkg->cdi[i].x > 0.5f)
		{
			int index = (int)(pCdiPkg->cdi[i].x / 0.5);
			if (index < 14 && pCdiPkg->cdi[i].y < 30.f)
			{
				sideLineLat[index]++;
				if (pCdiPkg->cdi[i].y < sideLineLngMin[index])
				{
					sideLineLngMin[index] = pCdiPkg->cdi[i].y;
				}
			}
		}
	}
	uint8_t tempSideLineLat_max = sideLineLat[0];
	uint8_t tempSideLineLat_second = 0;
	float latThrold_max = 0, latThrold_second = 0, latThrold = 0;//在这个范围内找一个横向距离点数最多的区间
	float LngThrold_Min = 0, LngThrold_second = 0;
	for (int i = 0; i < 20; i++)
	{
		if (tempSideLineLat_max < sideLineLat[i])
		{
			latThrold_max = i / 2.f;
			tempSideLineLat_max = sideLineLat[i];
			LngThrold_Min = sideLineLngMin[i];
		}
	}

	for (int i = 0; i < 20; i++)  //在这个范围内找一个横向距离点数第二多的区间
	{
		if (tempSideLineLat_second < sideLineLat[i] && sideLineLat[i] < tempSideLineLat_max)
		{
			latThrold_second = i / 2.f;
			tempSideLineLat_second = sideLineLat[i];
			LngThrold_second = sideLineLngMin[i];
		}
	}
	/*如果第二多的区间点数大于8个，且区间范围在3m内，且最多的区间的横向距离在5m以上，
	则选择第二多的区间,目的是防止镜像护栏点比真实的点数更多导致镜像护栏形成，而证实护栏没有形成*/
	if (tempSideLineLat_second >= 6 && latThrold_second < 3.f && latThrold_max > 3.f)
	{
		latThrold = latThrold_second;
	}
	else if (tempSideLineLat_second > 8 && tempSideLineLat_max > 8 && LngThrold_Min > 10.f && LngThrold_second < 5.f)
	{
		latThrold = latThrold_second;
	}
	else
	{
		latThrold = latThrold_max;
	}
	for (int i = 0; i < pCdiPkg->number; i++)
	{
		if (pCdiPkg->cdi[i].fence == 1 && pCdiPkg->cdi[i].x > 0.5f && ((fabsf(pCdiPkg->cdi[i].x - latThrold) < 0.5f) || latThrold < 0.1f))
		{
			if (pChosen == NULL)
			{
				pChosen = &pCdiPkg->cdi[i];
				idx = i;
			}
			else
			{
				if (pCdiPkg->cdi[i].y < pChosen->y && fabsf(pCdiPkg->cdi[i].mea_z[1] - pChosen->mea_z[1]) < 2.f && fabsf(pCdiPkg->cdi[i].x - latThrold) < 0.5f)
				{
					pChosen = &pCdiPkg->cdi[i];
					idx = i;
				}
			}
		}
	}


	for (int i = 0; i < pCdiPkg->number; i++)
	{
		if (idx == -1)
		{
			break;
		}
		if (pCdiPkg->cdi[i].fence == 1)
		{
			if (idx == i)
			{
				continue;
			}
			pCdi = &pCdiPkg->cdi[i];
			if ((pChosen->y < pCdi->y) || (pChosen->y < 3.0f && pCdi->y < 3.0f))
			{
				latDiff = pCdi->x - pChosen->x;
				lngDiff = pCdi->y - pChosen->y;
				if (1/*gTrkPkg.gTunnelFlag == 0*/)
				{
					//缓行时，减小纵向门限，避免横穿的径向速度小目标被误判护栏
					//转弯时，关联的下一护栏点和当前护栏点的横向距离差要与yawrate同向
					//起步变道时，不做要求
					if (((fabsf(lngDiff) > 20.f || fabsf(latDiff) > 1.0f) && fabsf(yawrate) < 3.f && carSpeed > 8.f)
						|| ((fabsf(lngDiff) > 10.f || fabsf(latDiff) > 0.8f) && fabsf(yawrate) < 3.f && carSpeed < 8.f)
						|| ((fabsf(lngDiff) > 10.f || fabsf(latDiff) > 3.0f || latDiff * yawrate < 0.f) && fabsf(yawrate) > 3.f && carSpeed > 8.f)
						|| ((fabsf(lngDiff) > 10.f || fabsf(latDiff) > 3.0f) && fabsf(yawrate) > 3.f && carSpeed < 8.f))
						//|| (fabsf(lngDiff) > 5.f && fabsf(yawrate) > 8.f))
					{
						pCdiPkg->cdi[i].fence = 2;		//横向距离大于0，但有可能是左护栏点
						(*strongCnt)--;
					}
					else
					{
						pChosen = pCdi;
						idx = i;
					}
				}
				else
				{
					if (((fabsf(lngDiff) > 60.f || fabsf(latDiff) > 0.4f) && fabsf(yawrate) < 1.f)
						|| ((fabsf(lngDiff) > 20.f || fabsf(latDiff) > 0.8f) && fabsf(yawrate) > 1.f))
					{
						pCdiPkg->cdi[i].fence = 0;
						(*strongCnt)--;
					}
					else
					{
						pChosen = pCdi;
						idx = i;
					}
				}
			}
			else
			{
				pCdiPkg->cdi[i].fence = 0;
				(*strongCnt)--;
			}
		}
	}


	//增加原始点护栏矫正
	//idx = -1;
	//pCdi = NULL, pChosen = NULL;
	/*for (int i = pCdiPkg->number - 1; i >= 0; i--)
	{
		if (pCdiPkg->cdi[i].fence == -1)
		{
			pChosen = &pCdiPkg->cdi[i];
			idx = i;
			break;
		}
	}*/
	//for (int i = 0; i < pCdiPkg->number; i++)
	//{
	//	if (idx == -1)
	//	{
	//		break;
	//	}
	//	if (pCdiPkg->cdi[i].fence == 2)
	//	{
	//		pCdi = &pCdiPkg->cdi[i];
	//		latDiff = pCdi->x - pChosen->x;
	//		lngDiff = pCdi->y - pChosen->y;
	//		if (fabsf(lngDiff) < 5 && latDiff > 0 && latDiff < 3 && yawrate > 3)		//只有右转弯时，右护栏点才能转换为左护栏点
	//		{
	//			pCdiPkg->cdi[i].fence = -1;
	//			pChosen = pCdi;
	//			(*weekCnt)++;
	//		}
	//		else
	//		{
	//			pCdi->fence = 0;
	//		}
	//	}
	//	//当没有真实右护栏点时，将lat > 0 的护栏点归于左护栏
	//	else if (pCdiPkg->cdi[i].fence == 1)
	//	{
	//		pCdi = &pCdiPkg->cdi[i];
	//		latDiff = pCdi->x - pChosen->x;
	//		lngDiff = pCdi->y - pChosen->y;
	//		if (fabsf(lngDiff) < 5 && fabsf(latDiff) < 3 && yawrate > 3)		//只有右转弯时，右护栏点才能转换为左护栏点
	//		{
	//			pCdiPkg->cdi[i].fence = -1;
	//			pChosen = pCdi;
	//			(*weekCnt)++;
	//			(*strongCnt)--;
	//		}
	//		else
	//		{
	//			;//若两护栏不连贯，不做处理
	//		}
	//	}
	//}


	idx = -1;
	pCdi = NULL, pChosen = NULL;
	for (int i = pCdiPkg->number - 1; i >= 0; i--)
	{
		if (pCdiPkg->cdi[i].fence == 1)
		{
			pChosen = &pCdiPkg->cdi[i];
			idx = i;
			break;
		}
	}
	for (int i = 0; i < pCdiPkg->number; i++)
	{
		if (idx == -1)
		{
			break;
		}
		if (pCdiPkg->cdi[i].fence == -2)
		{
			pCdi = &pCdiPkg->cdi[i];
			latDiff = pCdi->x - pChosen->x;
			lngDiff = pCdi->y - pChosen->y;
			if (fabsf(lngDiff) < 5 && latDiff < 0 && latDiff > -3 && yawrate < -3)	//只有左转弯时，左护栏点才能转换为右护栏点
			{
				pCdiPkg->cdi[i].fence = 1;
				pChosen = pCdi;
				(*strongCnt)++;
			}
			else
			{
				pCdi->fence = 0;
			}
		}
		//当没有真实左护栏点时，将lat < 0 的护栏点归于右护栏
		else if (pCdiPkg->cdi[i].fence == -1)
		{
			pCdi = &pCdiPkg->cdi[i];
			latDiff = pCdi->x - pChosen->x;
			lngDiff = pCdi->y - pChosen->y;
			if (fabsf(lngDiff) < 5 && fabsf(latDiff) < 3 && yawrate < -3)	//只有左转弯时，左护栏点才能转换为右护栏点
			{
				pCdiPkg->cdi[i].fence = 1;
				pChosen = pCdi;
				(*strongCnt)++;
				(*weekCnt)--;
			}
			else
			{
				;//若两护栏不连贯，不做处理
			}
		}
	}
}
// Function to perform Gauss-Jordan elimination to solve linear equations
void gaussJordanElimination(float augmentedMatrix[][DEGREE + 2], int n)
{
	for (int i = 0; i < n; i++)
	{
		// Make diagonal element 1
		if (augmentedMatrix[i][i] == 0.f)
		{
			return;
		}
		for (int j = 0; j < n; j++)
		{
			if (i != j)
			{
				float ratio = augmentedMatrix[j][i] / augmentedMatrix[i][i];

				for (int k = 0; k <= n; k++)
				{
					augmentedMatrix[j][k] -= ratio * augmentedMatrix[i][k];
				}
			}
		}
	}

	// Row operation to make principal diagonal element 1
	for (int i = 0; i < n; i++)
	{
		float divisor = augmentedMatrix[i][i];
		for (int j = 0; j <= n; j++)
		{
			augmentedMatrix[i][j] /= divisor;
		}
	}
}
//三阶拟合
static void fit_fence_coefficient(cdi_pkg_t* pCdiPkg, int weekCnt, int strongCnt, float weekParam[DEGREE + 1], float strongParam[DEGREE + 1], float RMSE[2])
{
	float strong_sum_x[2 * DEGREE + 1] = { 0.f };
	float strong_sum_yx[DEGREE + 1] = { 0.f };
	float week_sum_x[2 * DEGREE + 1] = { 0.f };
	float week_sum_yx[DEGREE + 1] = { 0.f };

	float res[2] = { 0 };
	for (int i = 0; i < pCdiPkg->number; i++)
	{
		if (pCdiPkg->cdi[i].fence == -1)
		{
			week_sum_yx[0] += pCdiPkg->cdi[i].x;
			for (int j = 0; j <= 2 * DEGREE; j++)
			{
				week_sum_x[j] += powf(pCdiPkg->cdi[i].y, j);
			}
			for (int j = 1; j <= DEGREE; j++)
			{
				week_sum_yx[j] += pCdiPkg->cdi[i].x * powf(pCdiPkg->cdi[i].y, j);
			}
		}
		if (pCdiPkg->cdi[i].fence == 1)
		{

			strong_sum_yx[0] += pCdiPkg->cdi[i].x;
			for (int j = 0; j <= 2 * DEGREE; j++)
			{
				strong_sum_x[j] += powf(pCdiPkg->cdi[i].y, j);
			}
			for (int j = 1; j <= DEGREE; j++)
			{
				strong_sum_yx[j] += pCdiPkg->cdi[i].x * powf(pCdiPkg->cdi[i].y, j);
			}
		}
	}

	if (weekCnt > 8)
	{
		float augmentedMatrixWeek[DEGREE + 1][DEGREE + 2];
		for (int i = 0; i <= DEGREE; i++) {
			for (int j = 0; j <= DEGREE; j++) {
				augmentedMatrixWeek[i][j] = week_sum_x[i + j];
			}
			augmentedMatrixWeek[i][DEGREE + 1] = week_sum_yx[i];
		}
		gaussJordanElimination(augmentedMatrixWeek, DEGREE + 1);
		for (int i = 0; i <= DEGREE; i++) {
			weekParam[i] = augmentedMatrixWeek[i][DEGREE + 1];
		}
	}
	else
	{
		memset(weekParam, 0, (DEGREE + 1) * sizeof(float));
	}

	if (strongCnt > 8)
	{
		//三阶矩阵
		//float augmentedMatrixStrong[DEGREE + 1][DEGREE + 2];
		//for (int i = 0; i <= DEGREE; i++) {
		//	for (int j = 0; j <= DEGREE; j++) {
		//		augmentedMatrixStrong[i][j] = strong_sum_x[i + j];
		//	}
		//	augmentedMatrixStrong[i][DEGREE + 1] = strong_sum_yx[i];
		//}
		//gaussJordanElimination(augmentedMatrixStrong, DEGREE + 1);
		//for (int i = 0; i <= DEGREE; i++) {
		//	strongParam[i] = augmentedMatrixStrong[i][DEGREE + 1];
		//}
		//二阶矩阵
		float augmentedMatrix[3][4] = {
		{strong_sum_x[4], strong_sum_x[3], strong_sum_x[2], strong_sum_yx[2]},
		{strong_sum_x[3], strong_sum_x[2], strong_sum_x[1], strong_sum_yx[1]},
		{strong_sum_x[2], strong_sum_x[1], strongCnt, strong_sum_yx[0]}
		};
		gaussJordanElimination(augmentedMatrix, 3);
		strongParam[2] = augmentedMatrix[0][3];
		strongParam[1] = augmentedMatrix[1][3];
		strongParam[0] = augmentedMatrix[2][3];
	}
	else
	{
		memset(strongParam, 0, (DEGREE + 1) * sizeof(float));
	}

	for (int i = 0; i < pCdiPkg->number; i++) {
		float lat = pCdiPkg->cdi[i].y;
		float lng = pCdiPkg->cdi[i].x;
		if (pCdiPkg->cdi[i].fence == -1) {
			res[0] += powf((lng - (/*weekParam[3] * POWF_I(lat, 3) +*/ weekParam[2] * POWF_I(lat, 2) + weekParam[1] * lat + weekParam[0])), 2.f);
		}
		if (pCdiPkg->cdi[i].fence == 1) {
			res[1] += powf((lng - (/*strongParam[3] * POWF_I(lat, 3) + */strongParam[2] * POWF_I(lat, 2) + strongParam[1] * lat + strongParam[0])), 2.f);
		}
	}
	RMSE[0] = weekCnt > 8 ? sqrtf(res[0] / weekCnt) : 0;
	RMSE[1] = strongCnt > 8 ? sqrtf(res[1] / strongCnt) : 0;
}
//二阶拟合不要b
static void fit_linear(cdi_pkg_t* pCdiPkg, int leftCnt, int rightCnt, float lParam[3], float rParam[3], float RMSE[2])
{
	float lysum = 0.0f, rysum = 0.0f;
	float lxsum = 0.0f, rxsum = 0.0f;
	float lxysum = 0.0f, rxysum = 0.0f;
	float lysum_2 = 0.0f, rysum_2 = 0.0f;
	float res[2] = { 0 };

	for (int i = 0; i < pCdiPkg->number; i++)
	{
		if (pCdiPkg->cdi[i].fence == -1)
		{
			lysum += pCdiPkg->cdi[i].y * pCdiPkg->cdi[i].y;
			lysum_2 += POWF_I(pCdiPkg->cdi[i].y * pCdiPkg->cdi[i].y, 2);
			lxsum += pCdiPkg->cdi[i].x;
			lxysum += (pCdiPkg->cdi[i].x * pCdiPkg->cdi[i].y * pCdiPkg->cdi[i].y);
		}
		if (pCdiPkg->cdi[i].fence == 1)
		{
			rysum += pCdiPkg->cdi[i].y * pCdiPkg->cdi[i].y;
			rysum_2 += POWF_I(pCdiPkg->cdi[i].y * pCdiPkg->cdi[i].y, 2);
			rxsum += pCdiPkg->cdi[i].x;
			rxysum += (pCdiPkg->cdi[i].x * pCdiPkg->cdi[i].y * pCdiPkg->cdi[i].y);
		}
	}

	float denom_left = leftCnt * lysum_2 - lysum * lysum;
	if (fabsf(denom_left - 0) > 1e-6)
	{
		lParam[2] = (leftCnt * lxysum - lxsum * lysum) / denom_left;
		lParam[0] = (lxsum - lParam[1] * lysum) / leftCnt;
		rParam[1] = 0.f;
	}

	float denom_right = rightCnt * rysum_2 - rysum * rysum;
	if (fabsf(denom_right - 0) > 1e-6)
	{
		rParam[2] = (rightCnt * rxysum - rxsum * rysum) / denom_right;
		rParam[0] = (rxsum - rParam[1] * rysum) / rightCnt;
		rParam[1] = 0.f;
	}
	for (int i = 0; i < pCdiPkg->number; i++)
	{
		float lat = pCdiPkg->cdi[i].x;
		float lng = pCdiPkg->cdi[i].y;
		if (pCdiPkg->cdi[i].fence == -1)
		{
			res[0] += powf((lat - (lParam[1] * lng * lng + lParam[0])), 2.f);
		}
		if (pCdiPkg->cdi[i].fence == 1)
		{
			res[1] += powf((lat - (rParam[1] * lng * lng + rParam[0])), 2.f);
		}
	}
	RMSE[0] = leftCnt > 8 ? sqrtf(res[0] / leftCnt) : 0;
	RMSE[1] = rightCnt > 8 ? sqrtf(res[1] / rightCnt) : 0;
}



static void fitFence(cdi_pkg_t *pCdiPkg, int weekCnt, int strongCnt, sideLine_pkr_t *pSideLinePkg)		//护栏拟合
{
	const stVehicleStatus *pVdy = getVdyStatus();
	float carSpeed = pVdy->compSpeed;
	float strongParam[DEGREE + 1] = { 0 }, weekParam[DEGREE + 1] = { 0 }, RMSE[2] = { 0 };
	float strongFenceRange[2] = { 0 }, weekFenceRange[2] = { 0 };
	bool leftFind = false, rightFind = false;
	memset(weekParam, 0, (DEGREE + 1) * sizeof(float));
	memset(strongParam, 0, (DEGREE + 1) * sizeof(float));
	float fit_x_data[200] = { 0.f };
	float fit_y_data[200] = { 0.f };
	int fit_index_data[200] = { 0 };
	int j = 0;
	rdp_config_t *config = RDP_getTrackConfigPointer();

	for (int i = pCdiPkg->number - 1; i >= 0; i--)
	{
		if (pCdiPkg->cdi[i].fence == -1)
		{
			if (!leftFind)
			{
				pSideLinePkg->weekFenceLat = pCdiPkg->cdi[i].x;
				leftFind = true;
			}
		}
		if (pCdiPkg->cdi[i].fence == 1)
		{
			if (!rightFind)
			{
				pSideLinePkg->strongFenceLat = pCdiPkg->cdi[i].x;
				rightFind = true;
			}
			fit_x_data[j] = pCdiPkg->cdi[i].x;
			fit_y_data[j] = pCdiPkg->cdi[i].y;
			fit_index_data[j++] = i;
		}
	}
	fit_fence_coefficient(pCdiPkg, weekCnt, strongCnt, weekParam, strongParam, RMSE);
	//fit_linear(pCdiPkg, weekCnt, strongCnt, weekParam, strongParam, RMSE);
	if (weekCnt > 8 && weekParam[0] < -1 && RMSE[0] < 1.f)
	{
		float weekParamtemp[DEGREE + 1] = { 0.f };
		pSideLinePkg->weekFenceCnt++;
		pSideLinePkg->weekFenceMiss = 0;
		//平滑处理
		if (pSideLinePkg->weekParam[0] < -1.f)
		{
			if (pSideLinePkg->weekFenceCnt < 10)
			{
				if (fabsf(pSideLinePkg->weekParam[0] - weekParam[0]) < 2)		//前后时刻护栏与x轴交点间距不超过2m，才进行平滑处理
				{
					//weekParamtemp[3] = LP_FILTER(pSideLinePkg->weekParam[3], FENCE_PARAM_COEF, weekParam[3]);
					weekParamtemp[2] = LP_FILTER(pSideLinePkg->weekParam[2], FENCE_PARAM_COEF, weekParam[2]);
					weekParamtemp[1] = LP_FILTER(pSideLinePkg->weekParam[1], FENCE_PARAM_COEF, weekParam[1]);
					weekParamtemp[0] = LP_FILTER(pSideLinePkg->weekParam[0], FENCE_PARAM_COEF, weekParam[0]);
				}
			}
			else
			{
				if (fabsf(pSideLinePkg->weekParam[0] - weekParam[0]) < 2)		//前后时刻护栏与x轴交点间距不超过2m，才进行平滑处理
				{
					//weekParamtemp[3] = LP_FILTER(pSideLinePkg->weekParam[3], FENCE_PARAM_COEF, weekParam[3]);
					weekParamtemp[2] = LP_FILTER(pSideLinePkg->weekParam[2], FENCE_PARAM_COEF, weekParam[2]);
					weekParamtemp[1] = LP_FILTER(pSideLinePkg->weekParam[1], FENCE_PARAM_COEF, weekParam[1]);
					weekParamtemp[0] = LP_FILTER(pSideLinePkg->weekParam[0], FENCE_PARAM_COEF, weekParam[0]);
				}
			}
		}
		//对滤波后的护栏系数矫正；即直行时，不出现左护栏顶端在右车道的情况
		//if (fabsf(pVdy->radius) > 500.f)
		//{
		//	float fenceTop = lParamtemp[1] * weekFenceRange[1] * weekFenceRange[1] * 100 + lParamtemp[0];
		//	if (fenceTop < 0 && pCdiPkg->lFenceCnt != 3)
		//	{
		//		memcpy(lParam, lParamtemp, 3 * sizeof(float));
		//	}
		//}

		//查找护栏最近最远点
		float lastWeekFenceRange[2];
		memcpy(lastWeekFenceRange, pSideLinePkg->weekFenceRange, (2) * sizeof(float));
		pSideLinePkg->weekFenceRange[0] = 1000.f;
		pSideLinePkg->weekFenceRange[1] = -1000.f;
		for (int i = 0; i < pCdiPkg->number; i++)
		{
			if (pCdiPkg->cdi[i].fence == -1)
			{
				if (pCdiPkg->cdi[i].y < pSideLinePkg->weekFenceRange[0])
				{
					pSideLinePkg->weekFenceRange[0] = pCdiPkg->cdi[i].y;
				}
				if (pCdiPkg->cdi[i].y > pSideLinePkg->weekFenceRange[1])
				{
					pSideLinePkg->weekFenceRange[1] = pCdiPkg->cdi[i].y;
				}
			}
		}

		if (pSideLinePkg->weekFenceCnt > 2 && weekParam[0] < -1.f)
		{
			memcpy(pSideLinePkg->weekParam, weekParam, (DEGREE + 1) * sizeof(float));
		}
		else
		{
			memset(pSideLinePkg->weekFenceRange, 0, 2 * sizeof(float));
		}
	}
	else if (weekCnt <= 8 || RMSE[0] > 1.f)
	{
		pSideLinePkg->weekFenceMiss = pSideLinePkg->weekFenceMiss > 100 ? 100 : pSideLinePkg->weekFenceMiss + 1;
	}
	if (pSideLinePkg->weekFenceMiss > 5 /*|| RMSE[0] > 2.f*/)
	{
		pSideLinePkg->weekFenceCnt = 0;
		memset(pSideLinePkg->weekParam, 0, (DEGREE + 1) * sizeof(float));
		memset(pSideLinePkg->weekFenceRange, 0, 2 * sizeof(float));
	}

	if (strongCnt > 8 && strongParam[0] > 0.5f && RMSE[1] < 1.f)
	{
		float strongParamtemp[DEGREE + 1] = { 0.f };
		pSideLinePkg->strongFenceCnt++;
		pSideLinePkg->strongFenceMiss = 0;

		//平滑处理
		if (pSideLinePkg->strongParam[0] > 0.5f)
		{
			if (pSideLinePkg->strongFenceCnt < 10)
			{
				if (fabsf(pSideLinePkg->strongParam[0] - strongParam[0]) < 2)	//前后时刻护栏与x轴交点间距不超过2m，才进行平滑处理
				{
					//strongParamtemp[3] = LP_FILTER(pSideLinePkg->strongParam[3], FENCE_PARAM_COEF, strongParam[3]);
					strongParamtemp[2] = LP_FILTER(pSideLinePkg->strongParam[2], FENCE_PARAM_COEF, strongParam[2]);
					strongParamtemp[1] = LP_FILTER(pSideLinePkg->strongParam[1], FENCE_PARAM_COEF, strongParam[1]);
					strongParamtemp[0] = LP_FILTER(pSideLinePkg->strongParam[0], FENCE_PARAM_COEF, strongParam[0]);
				}
			}
			else
			{
				if (fabsf(pSideLinePkg->strongParam[0] - strongParam[0]) < 2)	//前后时刻护栏与x轴交点间距不超过2m，才进行平滑处理
				{
					//strongParamtemp[3] = LP_FILTER(pSideLinePkg->strongParam[3], FENCE_PARAM_COEF, strongParam[3]);
					strongParamtemp[2] = LP_FILTER(pSideLinePkg->strongParam[2], FENCE_PARAM_COEF, strongParam[2]);
					strongParamtemp[1] = LP_FILTER(pSideLinePkg->strongParam[1], FENCE_PARAM_COEF, strongParam[1]);
					strongParamtemp[0] = LP_FILTER(pSideLinePkg->strongParam[0], FENCE_PARAM_COEF, strongParam[0]);
				}
			}
		}

		//对滤波后的护栏系数矫正；即直行时，不出现右护栏顶端在左车道的情况
		//if (fabsf(pVdy->radius) > 500.f)
		//{
		//	float fenceTop = strongParamtemp[1] * strongFenceRange[1] * strongFenceRange[1] * 100 + strongParamtemp[0];
		//	if (fenceTop > 0 && pSideLinePkg->strongFenceCnt != 3)
		//	{
		//		memcpy(strongParam, strongParamtemp, 3 * sizeof(float));
		//	}
		//}

		//查找护栏最近最远点
		float laststrongFenceRange[2];
		memcpy(laststrongFenceRange, pSideLinePkg->strongFenceRange, (2) * sizeof(float));
		pSideLinePkg->strongFenceRange[0] = 1000.f;
		pSideLinePkg->strongFenceRange[1] = -1000.f;
		for (int i = 0; i < pCdiPkg->number; i++)
		{
			if (pCdiPkg->cdi[i].fence == 1)
			{
				if (pCdiPkg->cdi[i].y < pSideLinePkg->strongFenceRange[0])
				{
					pSideLinePkg->strongFenceRange[0] = pCdiPkg->cdi[i].y;
				}
				if (pCdiPkg->cdi[i].y > pSideLinePkg->strongFenceRange[1])
				{
					pSideLinePkg->strongFenceRange[1] = pCdiPkg->cdi[i].y;
				}
			}
		}
		//护栏90度附近的时候，经常不够长导致一些点没有过滤
		if (pSideLinePkg->strongFenceRange[0] < 0.5f && (pSideLinePkg->strongFenceRange[1] - pSideLinePkg->strongFenceRange[0] > 5.f) && strongParam[1] < 0.1)
		{
			if (fabsf(pSideLinePkg->strongParam[1]) < 0.05f && fabsf(pSideLinePkg->strongParam[2]) < 0.001f)
			{
				pSideLinePkg->strongFenceRange[0] = -3.5f;
			}
			else if (config->isFront && pSideLinePkg->strongFenceRange[0] > -2.f)
			{
				pSideLinePkg->strongFenceRange[0] = -2.0f;
			}
			else if(config->isFront == 0)
			{
				pSideLinePkg->strongFenceRange[0] = -3.0f;
			}
		}
		if (pSideLinePkg->strongFenceCnt > 2 && strongParam[0] > 0.5f)
		{
			memcpy(pSideLinePkg->strongParam, strongParam, (DEGREE + 1) * sizeof(float));
		}
		else
		{
			memset(pSideLinePkg->strongFenceRange, 0, 2 * sizeof(float));
		}
	}
	else if (strongCnt <= 8 || RMSE[1] > 1.f)
	{
		pSideLinePkg->strongFenceMiss = pSideLinePkg->strongFenceMiss > 100 ? 100 : pSideLinePkg->strongFenceMiss + 1;
	}
	if (pSideLinePkg->strongFenceMiss > 5 || fabsf(pVdy->yawrate) > 0.1f/*|| RMSE[1] > 2.f*/) // 连续5帧没有护栏，才取消护栏，直接使用上一帧的护栏系数
	{
		pSideLinePkg->strongFenceCnt = 0;
		memset(pSideLinePkg->strongParam, 0, (DEGREE + 1) * sizeof(float));
		memset(pSideLinePkg->strongFenceRange, 0, 2 * sizeof(float));
	}

	if (pSideLinePkg->strongFenceMiss <= 5 \
		&& (pSideLinePkg->strongFenceRange[1] - pSideLinePkg->strongFenceRange[0]) > 5.f\
		&& (fabsf(pSideLinePkg->strongParam[0]) > 1e-6 \
			|| fabsf(pSideLinePkg->strongParam[1]) > 1e-6 \
			|| fabsf(pSideLinePkg->strongParam[2]) > 1e-6))
	{
		if (strongCnt < 12 && pSideLinePkg->strongFenceRange[1] - pSideLinePkg->strongFenceRange[0] > strongCnt)
		{
			pSideLinePkg->strongFenceMiss = pSideLinePkg->strongFenceMiss > 100 ? 100 : pSideLinePkg->strongFenceMiss + 2;
		}
		else
		{
			pSideLinePkg->strongSildLineValid = 1;
		}
		
	}
	else
	{
		pSideLinePkg->strongSildLineValid = 0;
	}
	if (pSideLinePkg->weekFenceMiss <= 5 \
		&& (pSideLinePkg->weekFenceRange[1] - pSideLinePkg->weekFenceRange[0]) > 5.f \
		&& (fabsf(pSideLinePkg->weekParam[0]) > 1e-6 \
			|| fabsf(pSideLinePkg->weekParam[1]) > 1e-6 \
			|| fabsf(pSideLinePkg->weekParam[1]) > 1e-6))
	{
		pSideLinePkg->weekSildLineValid = 1;
	}
	else
	{
		pSideLinePkg->weekSildLineValid = 0;
	}

	//重新统计护栏标识
	int leftnum = 0, num1 = 0;
	int rightnum = 0, num2 = 0;
	float lFence_rcs = 0.f;
	float rFence_rcs = 0.f;
	for (int i = 0; i < pCdiPkg->number; i++)
	{
		float lat = pCdiPkg->cdi[i].x;
		float lng = pCdiPkg->cdi[i].y;
		float temp_sideLineLat = getSideLineDis(lng, pSideLinePkg);

		if (fabsf(lat - (pSideLinePkg->weekParam[1] * lng *lng + pSideLinePkg->weekParam[0])) < 1.0f && pSideLinePkg->weekParam[0] < -1 && pSideLinePkg->weekFenceRange[1] > pSideLinePkg->weekFenceRange[0]
			&& ((!(pCdiPkg->cdi[i].status & POINT_STATUS_DYNAMIC_BMP) && carSpeed > VEHICLE_MIN_SPEED) || (fabsf(pCdiPkg->cdi[i].mea_z[2]) < 0.1f && carSpeed < VEHICLE_MIN_SPEED)))
		{
			if ((weekFenceRange[1] - weekFenceRange[0]) <= 20.f && fabs(RAD2ANG * pVdy->yawrate) < 2.f
				&& fabsf(pCdiPkg->cdi[i].latVel) > 1.f)
			{
				continue;
			}
			if (pCdiPkg->cdi[i].y - weekFenceRange[1] < 20)
			{
				pCdiPkg->cdi[i].fence = -1;
				pCdiPkg->cdi[i].weekStrongFence = WEEK_SIDE_LINE;
			}
			if (fabsf(lat - (pSideLinePkg->weekParam[1] * lng *lng + pSideLinePkg->weekParam[0])) < 0.5f)
			{
				leftnum++;
				if (lng > 10.f)
				{
					num1++;
					lFence_rcs = (lFence_rcs + pCdiPkg->cdi[i].rcs) / num1;
				}
			}
		}
		else if (fabsf(lat - temp_sideLineLat) < 1.0f && pSideLinePkg->strongParam[0] > 0.5f && pSideLinePkg->strongFenceRange[1] > pSideLinePkg->strongFenceRange[0]
			&& ((!(pCdiPkg->cdi[i].status & POINT_STATUS_DYNAMIC_BMP) && carSpeed > VEHICLE_MIN_SPEED) || (fabsf(pCdiPkg->cdi[i].mea_z[2]) < 0.1f && carSpeed < VEHICLE_MIN_SPEED)))
		{
			if (strongFenceRange[1] - strongFenceRange[0] <= 20.f && fabs(RAD2ANG * pVdy->yawrate) < 2.f
				&& fabsf(pCdiPkg->cdi[i].latVel) > 1.f)
			{
				continue;
			}
			if (pCdiPkg->cdi[i].y - strongFenceRange[1] < 20)
			{
				pCdiPkg->cdi[i].fence = 1;
				pCdiPkg->cdi[i].weekStrongFence = STRONG_SIDE_LINE;
			}
			if (fabsf(lat - temp_sideLineLat) < 0.5f)
			{
				rightnum++;
				if (lng > 10.f)
				{
					num2++;
					rFence_rcs = (rFence_rcs + pCdiPkg->cdi[i].rcs) / num2;
				}
			}
		}
		else
		{
			pCdiPkg->cdi[i].fence = 0;
			pCdiPkg->cdi[i].weekStrongFence = NONE_SIDE_LINE;
		}
	}
}

static void filterFence(cdi_pkg_t *pCdiPkg, trk_pkg_t* pRDP_TrackTargets, sideLine_pkr_t *pSideLinePkg)
{
	cdi_t *pCdi;
	trk_t *pTrk, *pTrk1;
	bool leftFilterFlag = false, rightFilterFlag = false;

	int lFenceHighCnt = 0, rFenceHighCnt = 0;
	if (1/*gTrkPkg.gTunnelFlag == 0x0*/)
	{
		leftFilterFlag = rightFilterFlag = true;
	}
	else
	{
		/*
		针对隧道中灯带误判成护栏导致误删点的情况：
		根据护栏原始点的俯仰角判断，若高度超过4m的护栏点超过10个，则判断为灯带，不进行护栏过滤；
		同时灯带作为高空目标，其最近检测距离会大于20m；
		*/
		/*for (int i = 0; i < pCdiPkg->num; i++)
		{
			if (pCdiPkg->cdi[i].fence == -1)
			{
				if (pCdiPkg->cdi[i].meas[0] * sinf(ANG2RAD(pCdiPkg->cdi[i].heighAngle)) > TUNNELOBJ_HEIGHT_THRD && pCdiPkg->cdi[i].meas[0] < 100)
				{
					lFenceHighCnt++;
				}
			}
			if (pCdiPkg->cdi[i].fence == 1)
			{
				if (pCdiPkg->cdi[i].meas[0] * sinf(ANG2RAD(pCdiPkg->cdi[i].heighAngle)) > TUNNELOBJ_HEIGHT_THRD && pCdiPkg->cdi[i].meas[0] < 100)
				{
					rFenceHighCnt++;
				}
			}
		}
		leftFilterFlag = (lFenceHighCnt >= 10 || pSideLinePkg->weekFenceRange[0] >= 2) ? false : true;
		rightFilterFlag = (rFenceHighCnt >= 10 || pSideLinePkg->strongFenceRange[0] >= 2) ? false : true;*/
	}

	for (int i = 0; i < pCdiPkg->number; i++)
	{
		pCdi = &pCdiPkg->cdi[i];
		//兼容之前的护栏标记
		if ((pSideLinePkg->strongSildLineValid == 1 && pCdi->fence == 1)  \
			|| (pSideLinePkg->weekSildLineValid == 1 && pCdi->fence == -1))
		{
			pCdi->status |= POINT_STATUS_GUARDRAIL_BMP;
		}
		if (!(pCdiPkg->cdi[i].status & POINT_STATUS_DYNAMIC_BMP))
		{
			continue;
		}
		if (pSideLinePkg->weekSildLineValid == 1)
		{
			if (IS_OUTSIDE_WEEKFENCE_SECOND(pCdi->x, pCdi->y, pSideLinePkg->weekParam[2], pSideLinePkg->weekParam[1], pSideLinePkg->weekParam[0])
				&& (leftFilterFlag /*|| (gTrkPkg.gTunnelFlag == 0x1 && pCdi->otgVel < 5.f)*/)
				&& pCdi->y < pSideLinePkg->weekFenceRange[1] && pCdi->y > pSideLinePkg->weekFenceRange[0])
			{
				pCdi->valid = 0;
			}
		}
		if (pSideLinePkg->strongSildLineValid == 1)
		{
			if (IS_OUTSIDE_STRONGFENCE_SECOND(pCdi->x, pCdi->y, pSideLinePkg->strongParam[2], pSideLinePkg->strongParam[1], pSideLinePkg->strongParam[0])
				&& (rightFilterFlag /*|| (gTrkPkg.gTunnelFlag == 0x1 && pCdi->otgVel < 5.f)*/)
				&& pCdi->y < pSideLinePkg->strongFenceRange[1] && pCdi->y > pSideLinePkg->strongFenceRange[0])
			{
				pCdi->valid = 0;
			}
		}
	}
	for (int i = 0; i < MAX_NUM_OF_TRACKS; i++)
	{
		pTrk = &pRDP_TrackTargets->trk[i];

		if (!(pTrk->status & TRACK_STATUS_MOVING_BMP))
		{
			continue;
		}

		if (pSideLinePkg->weekSildLineValid == 1)
		{
			if (IS_OUTSIDE_WEEKFENCE_SECOND(pTrk->x[0], pTrk->x[1], pSideLinePkg->weekParam[2], pSideLinePkg->weekParam[1], pSideLinePkg->weekParam[0])
				&& pTrk->x[1] < pSideLinePkg->weekFenceRange[1] && pTrk->x[1] > pSideLinePkg->weekFenceRange[0])
			{
				if (/*(gTrkPkg.gTunnelFlag == 0x1 && pTrk->x[3] < 5.f) || */pTrk->type == CANDI)
				{
					pTrk->type = NONE;
				}
				else if (leftFilterFlag)
				{
					pTrk->outWeekFenceCnt++;
				}
			}
			else
			{
				pTrk->outWeekFenceCnt = 0;
			}
		}
		if (pSideLinePkg->strongSildLineValid == 1)
		{
			if (IS_OUTSIDE_STRONGFENCE_SECOND(pTrk->x[0], pTrk->x[1], pSideLinePkg->strongParam[2], pSideLinePkg->strongParam[1], pSideLinePkg->strongParam[0])
				&& pTrk->x[1] < pSideLinePkg->strongFenceRange[1] && pTrk->x[1] > pSideLinePkg->strongFenceRange[0])
			{
				if (/*(gTrkPkg.gTunnelFlag == 0x1 && pTrk->x[3] < 5.f) ||*/ pTrk->type == CANDI)
				{
					pTrk->type = NONE;
				}
				else if (rightFilterFlag)
				{
					pTrk->outStrongFenceCnt++;
				}
			}
			else
			{
				pTrk->outStrongFenceCnt = 0;
			}
		}
		if ((pTrk->outWeekFenceCnt >= 5 || pTrk->outStrongFenceCnt >= 5))
		{
			pTrk->type = NONE;
		}
	}
}

void cdiProcess(cdi_pkg_t *pCdiPkg, VDY_DynamicEstimate_t *pRDP_inVehicleData, trk_pkg_t* pRDP_TrackTargets)
{
	int weekCnt = 0, strongCnt = 0;
	float cdiHeight = 0;
	sideLine_pkr_t *pSideLinePkg = &pRDP_TrackTargets->sideLines;

	statFence(pCdiPkg, &weekCnt, &strongCnt, pSideLinePkg);

	fitFence(pCdiPkg, weekCnt, strongCnt, pSideLinePkg);

	filterFence(pCdiPkg, pRDP_TrackTargets, pSideLinePkg);
	pSideLinePkg->weekParam[0] = fabsf(pSideLinePkg->weekParam[0]); //定义成无符号的，直接取绝对值外发
}

float getSideLineDis(float y, sideLine_pkr_t* pSideLinePkg)
{
	float sideLineDis = 0;
	if (pSideLinePkg->strongSildLineValid)
	{
		sideLineDis = pSideLinePkg->strongParam[0] + pSideLinePkg->strongParam[1] * y + pSideLinePkg->strongParam[2] * y * y /*+ pSideLinePkg->strongParam[3] * y * y * y*/;
	}
	return sideLineDis;
}

/**
 * @brief 卡尔曼跟踪主流程，实际上滤波器是扩展卡尔曼，实际输出为Track属性的目标
 * @param pRDP_DetObjectList  RDP内部维护的检测目标全局变量指针
 * @param pRDP_inVehicleData RDP内部维护的车身信息全局变量指针
 * @param time 帧间隔
 * @param pRDP_TrackTargets RDP内部卡尔曼滤波后的航迹列表指针，包含多种航迹状态的目标
 */
void RDP_runKFTrack(RSP_DetObjectList_t* pRDP_DetObjectList, VDY_DynamicEstimate_t* pRDP_inVehicleData, float time, trk_pkg_t* pRDP_TrackTargets)
{
    VDY_DynamicEstimate_t vehicleInfo;
    memcpy(&vehicleInfo, pRDP_inVehicleData, sizeof(VDY_DynamicEstimate_t));

    const VDY_Info_t *pVDY = VDY_getVDY_Info_Pointer();

    rdp_config_t* config = RDP_getTrackConfigPointer();
    cfgUpdate(pRDP_inVehicleData, &vehicleInfo, config);

    //vdy
    vdyProcess(pRDP_DetObjectList, &vehicleInfo, time, config);

    //按距离对点进行冒泡排序
    RDP_bubblesortRawTargets(pRDP_DetObjectList->rspDetObjectNum, pRDP_DetObjectList->rspDetObject);

    //组初始化
    memset(&gGroupInfo[0], 0, sizeof(gGroupInfo));

    //获得跟踪用的原始点
    RDP_getRawTargets(pRDP_TrackTargets, pRDP_DetObjectList, gGroupInfo, &vehicleInfo, &gRDP_BKTargetsList);

	cdiProcess(&gRDP_BKTargetsList, &vehicleInfo, pRDP_TrackTargets);
    //服务标定
    ALN_DynamicEolAngleCalcMainFun(&gRDP_BKTargetsList, &vehicleInfo, time);
    //自标定
    ALN_AutoEolAngleCalcMainFun(&gRDP_BKTargetsList, pVDY, &vehicleInfo, time);

#ifndef PC_DBG_FW
#ifndef RADAR_AUTOSAR
    // 非UDS诊断处理模式下，才进行跟踪处理主流程
    if (UDS_getDiagProcMode() == 0)
#endif
#endif
    {
    //跟踪处理主流程，pRDP_TrackTargets为跟踪结果的地址
        RDP_solveRadarTrack(&gRDP_BKTargetsList, gGroupInfo, &vehicleInfo, time, config, pRDP_TrackTargets);
    }
}
