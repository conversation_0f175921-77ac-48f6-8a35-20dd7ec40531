/**
 * @file adas_alg_params.h
 * @brief 内部算法使用参数
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2023-09-27
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2023-09-27 <td>1.0     <td>shaowei     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _ADAS_ALG_PARAMS_H_
#define _ADAS_ALG_PARAMS_H_

#ifdef ALPSPRO_ADAS
#include "adas_standard_params.h"
#include "vehicle_cfg.h"
#include "rsp/rsp_types.h"
#elif defined(PC_DBG_FW)
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/vehicle_cfg.h"
#include "hal/rsp/rsp_types.h"
#else
#include "adas_standard_params.h"
#include "common/include/vehicle_cfg.h"
#include "app/rsp/rsp_types.h"
#endif

#define BUMPER_FALSE_POINT_STRATEGY_ENABLED 1   //宏开关 ，用于控制RCW和BSD是否要启用保杠假点过滤策略
#define VEHICLE_TURN_DETECTION_ENABLED  0       // 检测车辆是否在转弯 调试阶段

#define RCTAB_MIN_IA_CROSS(angle, angleThr) (fabsf(angle - (RCTA_ACTIVE_MIN_IA + 10)) < angleThr)      // RCTAB小角度横穿  50度为基准 一般正负20附近
#define RCTAB_MAX_IA_CROSS(angle, angleThr) (fabsf(angle - (RCTA_ACTIVE_MAX_IA - 10)) < angleThr)      // RCTAB大角度横穿 130度作为基准 一般正负20附近
#define RCTAB_STAND_IA_CROSS(angle, angleThr) (fabsf(angle - (90)) < angleThr)                     // RCTAB横穿角度
#define FCTAB_MIN_IA_CROSS(angle, angleThr) (fabsf(angle - FCTB_ACTIVE_MIN_IA) < angleThr)      // fctb小角度横穿
#define FCTAB_MAX_IA_CROSS(angle, angleThr) (fabsf(angle - FCTB_ACTIVE_MAX_IA) < angleThr)      // fctb大角度横穿

// FCTAB自定义参数
#define FCTA_MIN_RADIUS_FOR_ADAS                    (100.0f) 
#define FCTA_LIFECYCLETHR                           (3U)        //FCTA 目标最小生命周期 
#define FCTA_SPEED_BUF                              (5)         //Fcta速度缓冲，只在限制高速目标时使用
#define FCTA_CURV_OBJSPEED_STEERANGLE_RATE          (0.12f)     // 根据方向盘转角动态提升目标速度检测阈值  3.6km/h  /  30 度
#define FCTA_CURV_OBJSPEED_STEERANGLE_H             (10.0f)     // 根据方向盘转角动态提升目标速度检测阈值  km/h
#define FCTB_CURV_SUPPRESSION_STEERANGLE_MAX        (40.0f)     // 方向盘转角大于一定值  抑制FCTB
#define FCTB_CURV_SUPPRESSION_STEERANGLE_LSPEED     (30.0f)     // 方向盘转角大于一定值  抑制FCTB
#define FCTB_CURV_SUPPRESSION_STEERANGLE_HSPEED     (20.0f)     // 方向盘转角大于一定值  抑制FCTB
#define FCTB_CURV_SUPPRESSION_TURNRADIUS            (100.0f)      // 转弯半径小于一定值, 抑制FCTB
#define FCTB_PREALARMCNT                            (2U)        // FCTB预警一定次数一定次数才触发制动
#define FCTB_CROSS_XVAGUEAREA                       (0.0f)       // 大车横穿场景目标测不准时X坐标区域.
#define FCTB_CROSS_XPERSONVAGUEAREA                 (-0.2f)      // 对于行人等小目标, 越过车身0.2米后启用横穿判断策略.
#define FCTB_CROSS_XDIFFMINPERFRAME                 (0.04f)
#define FCTB_CROSS_PRESSMINX                        (3.0f)       // 车身外2米开始判断是否符合横穿场景
#define FCTB_CROSS_TTY_XDISTANCE_THR                (0.5f)       // X距离车身多远时刻计算TTY.
#define FCTB_CROSS_TTX_XDISTANCE_THR                (3.0f)       // X距离车身多远时刻计算TTX.
#define FCTB_CROSS_XNEARPERCENT                     (0.70f)      // 横穿场景目标x轴符合递减规律的百分比
#define FCTB_CROSS_XINCLINENEARPERCENT              (0.75f)      // 斜穿场景目标x轴符合递减规律的百分比
#define FCTB_CROSS_XOBJBIGNEARPERCENT               (0.55f)      // 横穿场景目标x轴符合递减规律的百分比
#define FCTB_CROSS_OBJBIGTHRESHOLD                  (3.0)       // 横穿场景判断大目标的航迹框阈值.
#define FCTB_FRAME_RATE                             (50)        // 帧间隔
#define FCTAB_SUM_WIN_SIZE                          (6)
#define FCTB_RELIABILITYMININBLINDAREA              (60.0f)     //盲区内的目标置信度要求降低
#define FCTB_BLINDAREALIFECYCLE                     (10U)       //盲区内的目标对生命周期有要求
#define FCTB_RELIABILITYMININRADARFOV               (80.0f)     //雷达FOV内要求相对较高
#define FCTB_RELIABILITYBUF                         (-10.0f)    //雷达FOV内要求相对较高
#define FCTB_TTY_EN                                 (1)         // 是否使能TTY计算
#define FCTB_JUDGE_SPAN_CAR                         (1)         // 目标是否必须过车身.
#define FCTB_BREAK_TTMRANGE_THR                     (3.0f)      // 车辆横穿场景FCTB制动range限制.
#define FCTA_CROSS_TTC_YREAR                        (2.5f)      // 横穿补偿TTC的Y距离限制
#define FCTA_CROSS_IGNORE_CNT                       (20)        // 忽略横穿计数次数, 对于已经符合横穿趋势的目标, 到车头正前方时忽略一定帧数的横穿计数.
#define FCTB_CHECK_MAX_CNT                          (120)       // FCTB验收时刻, 起步内的一定帧数策略稍微宽松
#define FCTB_CHECK_RUNING_TIME                      (10.0f)      // FCTB验收时刻, 起步 N 秒内内适当放宽参数  适当放大一些
#define FCTA_CHECK_RUNING_TIME                      (10.0f)      // FCTA验收时刻, 起步 N 秒内内适当放宽参数
#define FCTB_CHECK_L_STRICT_RUNNING_TIME            (15.0f)     // 起步超过一定时间   策略一级收紧
#define FCTB_CHECK_M_STRICT_RUNNING_TIME            (30.0f)     // 起步超过一定时间   策略二级收紧
#define FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO  (4.0f)      // FCTB 检测为CCCscp 8-9场景, 起步 N 秒内内适当放宽参数
#define FCTB_CHECK_L_STRICT_VX_L                    (1.0f)      // 一级收紧策略时， 横向VX限制 起步时限制
#define FCTB_CHECK_L_STRICT_VX                      (3.0f)      // 一级收紧策略时， 横向VX限制
#define FCTB_CHECK_M_STRICT_VX                      (5.0f)      // 二级收紧策略时， 横向VX限制
#define FCTB_CHECK_L_STRICT_X						(4.0f)      // 一级收紧策略时， 横向最大位置限制
#define FCTB_CHECK_L_STRICT_RANGE					(2.5f)      // 一级收紧策略时， 最小位置限制
#define FCTB_FOLLOWCNT_MAX_FRAME                    (60)        // 一定距离内跟随自车一定帧数的目标, 不制动
#define FCTB_OBJ_RUNNING_MAX_FRAME                  (150)       // 从自车运动开始检测目标运动 超过一定帧数不制动
#define FCTB_CHECK_MOVED_NUM                        (3.0f)      // FCTA验收时刻, 一定目标数量适当放宽参数
#define FCTB_CHECK_MOVED_STRAIGHT_SCENARIO_NUM      (5U)        // FCTA验收时刻, 一定目标数量适当放宽参数
#define FCTB_SCENE_EN                               (0)         // 场景识别, 验收时适当放开策略.


#define FCTA_CROSS_RANGESTARTDIFF                   (3.0)       // 目标当前位置和航迹起始位置的差值
#define FCTA_CROSS_RANGEMAXDIFF                     (2.0)       // 目标当前位置和历史最大位置的差值.(未考虑符合, 和起航迹的差值结合使用)
#define FCTAB_STRICT_EN                              (1)        // FCTA使用更加严格的策略

// FCTAB planB自定义参数
#define FCTA_B_COLLISIO_NHAZARD_ZONE_Y                (1.0f)      //FCTA不判断航向角的区域Y轴距离
#define FCTA_B_COLLISIO_NHAZARD_ZONE_X                (0.5f)      //FCTA不判断航向角的区域X轴距离
#define FCTA_B_LIFECYCLETHR                           (3U)        //FCTA 目标最小生命周期
#define FCTA_B_MULTIPOINTS_MINR                       (10)        //计算航迹框上多个点目标时，目标距离最小阈值
#define FCTA_B_SPEED_BUF                              (5)         //Fcta速度缓冲，只在限制高速目标时使用
#define FCTB_B_CURV_SUPPRESSION_STEERANGLE            (60.0f)     // 方向盘转角大于一定值  抑制FCTB
#define FCTB_B_CURV_SUPPRESSION_TURNRADIUS            (0.0f)      // 转弯半径小于一定值, 抑制FCTB
#define FCTB_B_PREALARMCNT                            (1U)        // FCTB预警一定次数一定次数才触发制动
#define FCTB_B_CROSS_XVAGUEAREA                       (0.0)       // 大车横穿场景目标测不准时X坐标区域.
#define FCTB_B_CROSS_XPERSONVAGUEAREA                 (-0.2)      // 对于行人等小目标, 越过车身0.2米后启用横穿判断策略.
#define FCTB_B_CROSS_XDIFFMINPERFRAME                 (0.02)      // 值越小  越易触发FCTB
#define FCTB_B_CROSS_PRESSMINX                        (2.0)       // 车身外2米开始判断是否符合横穿场景
#define FCTB_B_CROSS_TTY_XDISTANCE_THR                (0.5)       // X距离车身多远时刻计算TTY.
#define FCTB_B_CROSS_XNEARPERCENT                     (0.85)      // 横穿场景目标x轴符合递减规律的百分比
#define FCTB_B_CROSS_XINCLINENEARPERCENT              (0.75)      // 斜穿场景目标x轴符合递减规律的百分比
#define FCTB_B_CROSS_XOBJBIGNEARPERCENT               (0.65)      // 横穿场景目标x轴符合递减规律的百分比
#define FCTB_B_CROSS_OBJBIGTHRESHOLD                  (3.0)       // 横穿场景判断大目标的航迹框阈值.
#define FCTB_B_FRAME_RATE                             (50)        // 帧间隔
#define FCTAB_B_SUM_WIN_SIZE                          (6)
#define FCTB_B_RELIABILITYMININBLINDAREA              (60.0f)     //盲区内的目标置信度要求降低
#define FCTB_B_BLINDAREALIFECYCLE                     (10U)       //盲区内的目标对生命周期有要求
#define FCTB_B_RELIABILITYMININRADARFOV               (80.0f)     //雷达FOV内要求相对较高
#define FCTB_B_RELIABILITYBUF                         (-10.0f)    //雷达FOV内要求相对较高

//RCTAB自定义参数
#define RCTA_DELAY_MAX                  (40U)       //最大延迟报警周期
#define RCTA_BEHIND_DELAY_MAX           (80U)       //目标在正后方的最大报警延迟
#define RCTA_LIFECYCLETHR               (3U)        //RCTA报警对目标生命周期数的最低要求
#define RCTA_SPEED_BUF                  (5)         //rcta速度缓冲，只在限制高速目标时使用
#define RCTA_MULTIPOINTS_MINR           (10)        //计算航迹框上多个点目标时，目标距离最小阈值
#define BREAK_RESERVE_TIME              (1.5f)
#define RCTB_BREAK_RESERVE_TIME              (0.5f)      // RCTB刹停控制变量
#define RCTB_BLINDAREALIFECYCLE         (10U)       //盲区内的目标对生命周期有要求
#define RCTB_RELIABILITYBUF             (-20.0f)    //置信度缓冲
#define RCTB_RELIABILITYMININBLINDAREA  (60.0f)     //盲区内的目标置信度要求降低
#define RCTB_RELIABILITYMININRADARFOV   (80.0f)     //雷达FOV内要求相对较高
#define RCTB_CROSS_RANGESTARTDIFF                   (3.0)       // 目标当前位置和航迹起始位置的差值
#define RCTB_CROSS_RANGEMAXDIFF                     (2.0)       // 目标当前位置和历史最大位置的差值.(未考虑符合, 和起航迹的差值结合使用)
#define RCTB_CROSS_XNEARPERCENT                     (0.85)      // 横穿场景目标x轴符合递减规律的百分比
#define RCTB_CROSS_XINCLINENEARPERCENT              (0.75)      // 斜穿场景目标x轴符合递减规律的百分比
#define RCTB_CROSS_XOBJBIGNEARPERCENT               (0.65)      // 横穿场景目标x轴符合递减规律的百分比
#define RCTB_CROSS_OBJBIGTHRESHOLD                  (3.0)       // 横穿场景判断大目标的航迹框阈值.
#define RCTB_CROSS_XDIFFMINPERFRAME                 (0.04)
#define RCTB_CROSS_LOW_SPEED                        (3)         // RCTB目标低速定义  m/s
#define RCTA_CROSS_IGNORE_CNT                       (20)        // 忽略横穿计数次数, 对于已经符合横穿趋势的目标, 到车头正前方时忽略一定帧数的横穿计数.
#define RCTA_CROSS_LOCKVX_XDISTANCE                 (1.0f)      // 横穿目标距离车身还有多远时锁定横向速度.
#define ADAS_RCTB_IADOUBLECHECK_EN                   1          // 小角度斜穿策略启用开关
#define RCTA_BEHIND_MIN_X              (0.5)                    // 正后方维持报警, 正后方设置为x 0.5米内.
#define RCTB_CROSS_TTX_XDISTANCE_THR                (2.0f)       // X距离车身多远时刻计算TTX.
#define RCTB_RESPONSE_DELAY                         (0.3f)      // 帧周期高. RCTB的时延也高. 单位 秒
#define RCTB_Y_PRE_SAFEDISTANCE                     (1.0f)      // RCTB纵向TTC计算的时候  预留一个安全距离.


// BSD自定义参数
#define BSD_OVERTAKE_SUPPRESS_START_X_THRESHOLD     (ALARM_ROAD_WIDTH + MIN_ROAD_SIDE_DISTANCE)     //超车抑制跟踪点起批x坐标阈值
#define BSD_OVERTAKE_SUPPRESS_START_Y_THRESHOLD     (0.5f)									        //超车抑制跟踪点起批y坐标阈值
#define BSD_SUPPRESS_START_Y_THRESHOLD              (1.0f)                                          // 对向来车，抑制跟踪点起批y坐标阈值
#define BSD_SUPPRESS_REVERSE_RANGE_Y_MIN            (5.0)
#define BSD_SUPPRESS_REVERSE_RANGE_Y_MAX            (7.0)
#define BSD_OVERTAKE_SUPPRESS_LIFE_CYCLE_THRESHOLD  (15U) 									        //超车抑制跟踪点已经存在帧数阈值
#define BSD_OVERTAKE_SUPPRESS_ALARM_DELAY	        (15U)									        //超车抑制跟踪点延迟多少帧数后报警
#define BSD_OVERTAKE_SUPPRESS_ALARM_DELAY_MIN	    (5U)									        //超车抑制跟踪点延迟多少帧数后报警最小值
#define BSD_MIN_SPEED       (0.0f)
#define BSD_CAR_LENGTH_MIN  (5.2f)
#define BSD_CARSPEED_ROADLINEBUF_HIGH               (0.5f)   //自车速度较高时的边线buffer.    边线策略
#define BSD_CARSPEED_ROADLINEBUF_MID                (0.3f)   //自车速度适中时的边线buffer.
#define BSD_CARSPEED_ROADLINEBUF_LOW                (0.2f)   //自车速度较低时的边线buffer.
#define BSD_OBJSPEED_ROADLINEBUF_HIGH               (0.8f)   //目标相对速度较高时的边线buffer.
#define BSD_OBJSPEED_ROADLINEBUF_MID                (0.5f)   //目标相对速度适中时的边线buffer.
#define BSD_OBJSPEED_ROADLINEBUF_LOW                (0.3f)   //目标相对速度较低时的边线buffer.
#define BSD_TURNING_RADIUS_MIN                      (10.0f)  //BSD最小转弯半径阈值
#define BSD_TURNING_RADIUS_MIN_BUF                  (5.0f)   //转弯半径缓冲
#define BSD_TURNING_X_BUF                           (0.5f)   //转弯过程中弯道补偿X            弯道补偿策略
#define BSD_TURNING_Y_BUF                           (-1.5f)  //转弯过程中弯道补偿Y
#define BSD_TURNDELAY_X_BUF                         (0.5f)   //转弯延时阶段弯道补偿X
#define BSD_TURNDELAY_Y_BUF                         (-1.0f)  //转弯延时阶段弯道补偿Y
#define BSD_REDUCED_WIDTH_MARGIN                    (-0.75f)    // 对道路缩减
#define BSD_ABNORMAL_TURN_XBUF_LOW                  (0.5f)   //转弯时抑制横向一定距离外的异常点. 转弯半径越小 距离越远
#define BSD_ABNORMAL_TURN_XBUF_MID                  (1.0f)   //转弯时抑制横向一定距离外的异常点. 转弯半径越小 距离越远
#define BSD_ABNORMAL_TURN_XBUF_HIGH                 (1.5f)   //转弯时抑制横向一定距离外的异常点. 转弯半径越小 距离越远
#define BSD_OBJ_RELIABLITY_MIN                      (75U)    //bsd目标最小置信度
#define BSD_OBJ_MISSCNT_MAX                         (7U)     //bsd目标最大丢失次数
#define BSD_OUT_ROAD_LINE_TARGET_SUPPRESS_CNT_TRESHOLD (5U)
#define BSD_OUT_ROAD_LINE_TARGET_SUPPRESS_ALARM_DELAY  (6)
#define BSD_CURVLIMIT_EN                            (0)     // BSD曲率限制是否启用.
/* 针对后保假点过滤策略的参数 */
#define BSD_FAKEDOT_MIN_DIF (3.0f)
#define BSD_FAKEDOT_MID_DIF (5.0f)
#define BSD_FAKEDOT_MAX_DIF (7.0f)
#define BSD_SCENE_MIN_SPD   (35.0f)
#define BSD_SCENE_MID_SPD   (70.0f)
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_FRAME_CNT_MAX    (600U)  // 
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_STARTY_MIN (-0.5f) //
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_STARTY_MAX (0.0f)  //
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_MAXY_MIN   (-0.5f) //
#define ADAS_POTENTIA_FALSE_POINT_TRACKING_RANGE_MAXY_MAX   (0.0f)  //
#define BSD_CHECKINOUT_MIN_X_DEFAULT        (4.0)
#define BSD_CHECKINOUT_MAX_X_DEFAULT        (0.0)
#define BSD_CHECKIN_MAX_X           (4.0)
#define BSD_CHECKOUT_MIN_X          (2.3)
#define BSD_CHECKINOUT_MAXDIFF      (0.15)
#define BSD_CHECKINOUT_MIN_Y        (-2.5)
#define BSD_CHECKINOUT_MAX_Y        (8.0)

// LCA 自定义参数
#define LCA_ABNORMAL_VX_THRESHOLD                       (3.0f)   //异常跟踪点x方向相对速度判断阈值
#define LCA_ABNORMAL_VX_THRESHOLD_BUF                   (1.0f)   //异常跟踪点x方向相对速度已报警目标增加阈值
#define LCA_LOW_VY_ALM_QUIT_PROTECTION_VY               (3.0f)
#define LCA_LOW_VY_ALM_QUIT_PROTECTION_FRAME_NUM        (16U)
#define LCA_LOW_VY_ALM_QUIT_PROTECTION_FRAME_NUM_BASE   (100U)
#define LCA_CURVE_COMPENSATE_X                          (0.3f)   // 转弯半径小于一定值时, 内侧X向外扩 之前是0.5  现在改为0.3 
#define LCA_RELIABILITY_BUF                             (-10.0f) // 置信度缓冲LCA_ReliabilityBuf             
#define LCA_RELIABILITY_MIN                             (50.0f)  // LCA自车低速,目标高速场景。目标需在较远距离报警.降低置信度需求.  
#define LCA_CARSPEED_ROADLINEBUF_HIGH                   (0.5f)   //自车速度较高时的边线buffer.    边线策略
#define LCA_CARSPEED_ROADLINEBUF_MID                    (0.3f)   //自车速度适中时的边线buffer.
#define LCA_CARSPEED_ROADLINEBUF_LOW                    (0.2f)   //自车速度较低时的边线buffer.
#define LCA_OBJSPEED_ROADLINEBUF_HIGH                   (0.8f)   //目标相对速度较高时的边线buffer.
#define LCA_OBJSPEED_ROADLINEBUF_MID                    (0.5f)   //目标相对速度适中时的边线buffer.
#define LCA_OBJSPEED_ROADLINEBUF_LOW                    (0.3f)   //目标相对速度较低时的边线buffer.
#define LCA_TURNING_RADIUS_MIN                          (125.0f) //最小转弯半径                      转弯半径限制策略
#define LCA_TURNING_RADIUS_MIN_BUF                      (-125.0f)  //最小转弯半径缓冲
#define LCA_VYSPEED_HIGH_X_DYN_BUF                      (0.3f)   //相对速度对应的X动态buf.            相对速度大小X动态补偿策略
#define LCA_VYSPEED_RATE_X_DYN_BUF                      (0.03f)  //相对速度对应的X动态buf的比例系数
#define LCA_AREA_X_LIMIT_BUF                            (1)      // lca横向区域限制buffer

// RCW自定义参数
#define RCW_OWN_HOLDUP_SPEED                            (5.0f)  //自车速度buff，km/h
#define RCW_ALM_ROAD_WIDTH_SIZE                         (0.0f)  //0.5横向距离有点宽，为了减小误报，减小到0.0
#define RCW_TTC_DELTA_10                                (0.12f) //相对车速在(0,10]km/h时，ttc增量 ttc_Delta_10
#define RCW_TTC_DELTA_20                                (0.08f) //相对车速在(10,20]km/h时，ttc增量
#define RCW_TTC_DELTA_30                                (0.15f) //相对车速在(20,30]km/h时，ttc增量
#define RCW_TTC_DELTA_40                                (0.3f) //相对车速在>30km/h时，ttc增量
#define RCW_ALM_ROAD_WIDTH_10_MAX                       (0.1f)  //相对车速在(0,10]km/h时，横向报警区域宽度max rcw_alm_road_width_10_max
#define RCW_ALM_ROAD_WIDTH_10_MIN                       (1.3f)  //相对车速在(0,10]km/h时，横向报警区域宽度min(处理时取反)
#define RCW_ALM_ROAD_WIDTH_20_MAX                       (0.1f)  //相对车速在(10,20]km/h时，横向报警区域宽度max
#define RCW_ALM_ROAD_WIDTH_20_MIN                       (1.3f)  //相对车速在(10,20]km/h时，横向报警区域宽度min(处理时取反)
#define RCW_ALM_ROAD_WIDTH_30_MAX                       (0.1f)  //相对车速在(20,30]km/h时，横向报警区域宽度max
#define RCW_ALM_ROAD_WIDTH_30_MIN                       (1.3f)  //相对车速在(20,30]km/h时，横向报警区域宽度min(处理时取反)
#define RCW_ALM_ROAD_WIDTH_40_MAX                       (0.1f)  //相对车速在>30km/h时，横向报警区域宽度max
#define RCW_ALM_ROAD_WIDTH_40_MIN                       (1.3f)  //相对车速在>30km/h时，横向报警区域宽度min(处理时取反)
#define RCW_OBJ_OVERLAP_RANGE_RATE_LOW_THRESHOLD        (0.25f)    // RCW 目标最小重叠率，低于这个不报警
#define RCW_OBJ_OVERLAP_RANGE_RATE_LOW_THRESHOLD_BUFF   (0.2f)     // RCW 目标最小重叠率buff
#define RCW_OBJ_OVERLAP_RANGE_RATE_HIGH_THRESHOLD       (2.0f - RCW_OBJ_OVERLAP_RANGE_RATE_LOW_THRESHOLD - RCW_OBJ_OVERLAP_RANGE_RATE_LOW_THRESHOLD_BUFF)    // RCW 目标最大重叠率，高于这个不报警
#define RCW_OBJ_OVERLAP_RANGE_RATE_HIGH_THRESHOLD_BUFF  (-0.1f)     // RCW 目标最小重叠率buff
#define RCW_RELIABILITYBUF                              (-20U)    //置信度缓冲
#define RCW_RELIABILITYMININBLINDAREA  					(50U)     //盲区内的目标置信度要求降低
#define RCW_RELIABILITYMININRADARFOV   					(60U)     //雷达FOV内要求相对较高
#define RCW_OBJ_X_NEGATIVE_DIRT_MIN_BUFF                (-0.40f)    // RCW 目标在负方向时的x限制，不能超过车身的buff
#define RCW_LOCK_IA_MAX_RANGE                           (5.0f)  //RCW锁定目标航向角的最近距离
#define RCW_LOCK_COORDINATE_TTY_MIN                     (3.5f)  //锁定目标坐标的最小TTCY,用于估算整体的航向，解决TTC较小是航向角抖动的问题
#define RCW_LOCK_X_MAX                                  (-2.0f)  //锁定目标坐标的最大横坐标，小于此坐标，则不会触发RCW
#define RCW_OBJ_MIN_SIZE                                ADAS_OBJ_MID_SMALL
#define RCW_ACTIVE_MAX_IA                               (10.0f)
#define RCW_ACTIVE_MAX_IA_BUF                           (5.0f)     
#define RCW_MAX_STEERINGANGLE                           (60.0f)  
#define RCW_OWN_MIN_RADIUS_TURNING_THRESHOLD            (150.0f)    // 本车最小转弯半径为150m
#define RCW_OWN_MIN_RADIUS_TURNING_THRESHOLD_BUF        (150.0f)   // 因为转弯时浮动比较大，所以持续active时转弯半径扩大

// RCW正后方确保报警相关参数
#define RCW_BEHIND_ACTIVE_JUDGE_Y_MIN                   (2.0f)
#define RCW_BEHIND_ACTIVE_JUDGE_MOVE_Y_MAX  			(6.0f)         // 自车运动时,在一个小区间内符合正后方  也认为符合重叠率
#define RCW_BEHIND_ACTIVE_JUDGE_Y_MAX                   (20.0f)         // 放宽距离
#define RCW_BEHIND_ACTIVE_JUDGE_X_LEFT                  (-0.35f)
#define RCW_BEHIND_ACTIVE_JUDGE_Y_RIGHT                 (-1.3f)
#define RCW_BEHIND_ACTIVE_VX_LIMIT                      (1.2f)
#define RCW_BEHIND_CUT_IN_ACTIVE_VX_LIMIT   			(1.5f)         // 切入类场景横向速度最大限制
// 高重叠率触发场景
#define RCW_OVRELAP_H_ACTIVE_JUDGE_X_LEFT      			(0.25f)
#define RCW_OVRELAP_H_ACTIVE_JUDGE_Y_RIGHT     			(-1.2f)
#define RCW_OVRELAP_M_ACTIVE_JUDGE_X_LEFT      			(0.2f)

#define RCW_BEHIND_PASSIVE_JUDGE_Y_MIN                  (1.0f)
#define RCW_BEHIND_PASSIVE_JUDGE_Y_MAX                  (20.0f)      // 原来是10  调整到20
#define RCW_WARNING_INTERVAL                            (5.0f)      // 避免RCW中断  增加RCW报警间隔概念

//DOW自定义参数
#define DOW_DELAY_MAX                           (40U)       //最大延迟报警周期
#define DOW_WARNING_TRACKBOX_WIDTH_MAX          (2.2f)     //航迹框顶点辅助跟踪点综合判断 航迹框一般估计的偏大. 横向距离比规范的小一些
#define DOW_WARNING_SINGLE_TRACKBOX_WIDTH_MAX   (2.0f)     //单独航迹框顶点判断 阈值设置的小一些
#define DOW_HEADINGANGLE_CAR_SET                (25.0f)    //航向角设定值  DOW_HeadingAngle_Car_Set
#define DOW_HEADINGANGLE_THRESH                 (20.0f)    //航向角阈值(deg)  碰撞位置确定由于TTC时间较大，碰撞位置波动较大
#define DOW_SPEED_BUF                           (1.0f)     //速度缓冲区
#define DOW_SPEED_15                            (15.0f)     //15km/h
#define DOW_SPEED_25                            (25.0f)     //25km/h
#define DOW_SPEED_35                            (35.0f)     //35km/h
#define DOW_SPEED_45                            (45.0f)     //45km/h
#define DOW_SPEED_55                            (55.0f)     //55km/h
#define DOW_SPEED_65                            (65.0f)     //65km/h
#define TARGET_CENTERX_EST_MAX                  (1.5f)      //Target_CenterX_Est_Max
#define TARGET_CENTERX_EST_MIN				    (0.5f)
#define TARGET_CENTERX_EST_BUF                  (0.8f)
#define DOW_VY_OFFSET                           (1.5f)      //DOW_vy_Offset
#define DOW_Y_OFFSET                            (4.0f)
#define DOW_X_OFFSET                            (2.0f)
#define DOW_LIFECYCLETHR                        (3U)        //DOW报警对目标生命周期数的最低要求
#define DOW_DOOR_AREA_Y_MIN                     (0.1f)      //单位：m，表示门碰撞区域的底线
#define DOW_DOOR_AREA_Y_MAX                     (1.0f)      //单位：m，表示门碰撞区域的底线
#define DOW_MIN_Y                               (6.0f)      //使用正后方3到5米内的目标计算覆盖率 DOW_MIN_Y
#define DOW_MIN_Y_BUF                           (0.0f)      //使用正后方3到5米内的目标计算覆盖率 DOW_MIN_Y
#define DOW_MAX_Y                               (10.0f)     //  DOW_MAX_Y
#define DOW_MAX_Y_BUF                           (0.0f)      //  DOW_MAX_Y
#define DOW_COVERAGERATE_Y                      (1.0f)      //在自车正后方1m以上的目标才使用覆盖率 DOW_Coveragerate_Y
#define DOW_STARTY                              (5.0f)      //对起皮位置纵轴在5m以上的目标进行DOW报警抑制 DOW_STARTY
#define DOW_OBJ_RELIABLITY_MIN                  (50U)       //dow目标最小置信度
#define DOW_OBJ_OVERLAP_MAX                     (0.2f)      //dow目标最大重叠率
#define DOW_INVALIDATEOVERLAP_MIN_X             (0.1f)      //当x坐标超过此阈值，DOW不参考历史重叠率，对于后方斜切除得目标可以快速响应
#define DOW_TARGET_CLOSING_Y                    (2.0f)      //目标靠近车后方Ym范围内增加较大的夹角缓冲
#define DOW_HEADINGANGLE_DIFF                   (15)        // 航向角差值阈值
#define DOW_CHECKY_LIFECYCLE                    (30)        // 多少帧之内的目标检测Y方向距离
#define DOW_CHECKY_DISTANCE                     (2.0f)      // Y方向距离变化最小值      
#define DOW_SLOPE_MIN_ANGLE                     (6.0f)      // 最小斜穿角度  太小会导致直行远距离误报
#define DOW_SLOPE_MIN_ANGLE_BUF                 (-3.0f)     // 最小斜穿角度
#define DOW_SLOPE_X_MIN_RATE                    (0.5f)      // 斜穿角度对应外扩比例 (0.12f) 默认 0.12
#define DOW_SLOPE_X_MAX_RATE                    (0.5f)      // 斜穿角度对应外扩比例 (0.2f) 默认 0.2
#define DOW_SLOPE_PRE_X_NEAR_MAX                (1.2f)      // 碰撞预测X最多外扩1.2米.
#define DOW_SLOPE_PRE_X_FAR_MAX                 (3.5f)      // 碰撞预测X最多外扩1.2米. 理论上30度车尾朝内斜停时, 车尾到车头纵向边缘是车长的一半 约2.5米 理论上横向需外扩至4.5 X方向要 4.5/cos(30)  5.6米
#define DOW_MIN_IA_INGOREHEADINGANGLE           (3.0f)      // 3.0以内的航向角  忽略
#define DOW_MIN_IA_HEADINGANGLE                 (0.1f)      // 航向角作为除数, 最小限制其为0.1
#define DOW_SLOPE_X_RATE                        (1.5f)      // 自车斜停时， 对X外扩的比例系数 此值越大，外扩的比例越多 (1.5)
#define DOW_SLOPE_TTC_RATE                      (0.75f)     // 一定倾斜角度时,压缩TTC
#define DOW_DELAY_EXT_TIME                      (0.3f)      // dow延迟退出帧数  后续按照车型来， 车越长 延迟越多
#define DOW_DELAY_EXT_TIME_MAX                  (1.0f)      // dow最大延迟退出帧数   最大延迟1秒
#define DOW_LINEAR_MIN_ANGLE                    (8.0f)      // DOW使用线性拟合的最小角度
#define DOW_SLOPE_CROSS_MIN_ANGLE               (5.0f)      // DOW斜穿行最小判断角度.
#define DOW_COLLISION_MIN_ANGLE                 (10.0f)     // DOW拟合碰撞的最小角度
#define DOW_PREDICTED_MIN_CNT                   (3U)        // DOW最少满足多少帧预测才能触发报警
#define DOW_GUESSRAIL_OBJ_CNT                   (10U)       // 多少帧检测到临近护栏时, 认为是护栏场景的测试
#define DOW_RIGHTRADAR_COVERCNT                 (100U)       // 右侧雷达遮挡计数. 超过50帧 认为是可能存在遮挡  压TTC.
#define DOW_WARNING_WIDTH_MAX_LIMIT             (3.3f)     // 不同场景下动态调整横向距离  但是横向距离要有一个最大值限制

 
#define DOW_RCW_BEHIND_X_MIN                    (-2.0)      // 正后方来车的最小X限制
#define DOW_RCW_BEHIND_X_MAX                    (1.0)       // 正后方来车的最大X限制
#define DOW_RCW_BEHIND_VX                       (1.0)       // 正后方来车最大Vx限制
#define DOW_RCW_BEHIND_VY                       (1.0)       // 正后方来车最小Vy限制


// MISC 定义参数
#define ADAS_DET_POINTS                 MAX_NUM_OF_POINTS       // 最大原始点数量
#define ADAS_TRK_OBJNUM                 MAX_NUM_OF_TRACKS       // 最大报警目标数量
#define BSD_RADAR_LEFT                  (1U)
#define BSD_RADAR_RIGHT                 (0U)
#define RADAR_POSITION_FRONT            (1U)
#define RADAR_POSITION_REAR             (0U)
#define VEHICLE_LENGTH_INFO             (MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL - MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL + \
                                         MOUNT6POS_TO_OUTER_EDGE_Y_FL + MOUNT4POS_TO_OUTER_EDGE_Y_FL) // 车长
#define VEHICLE_WIDTH_INFO              ((MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL + MOUNT6POS_TO_OUTER_EDGE_X_FL) * 2) // 车宽
#define MIN_ROAD_SIDE_DISTANCE          ( 0.5f)              //车身到绿化带的距离
#define ORIGIN_BSD_ALARM_WIDTH_SIZE     (0.5f)               //报警忽略区域
#define MIN_SIDE_VALUE                  (0.3f)               //最近的边线值

/* 角度转弧度DEG2RAD = pi/180 */
#define degtorad 0.017453292519943295f
/* 弧度转角度：RAD2DEG = 180/pi */
#define radtodeg 57.295779513082323f

//报警延迟，表示n帧后的下一帧开始报警
#define RCW_START_ALARM_DELAY       1U
#define RCW_OVER_ALARM_DELAY        3U
#define BSD_START_ALARM_DELAY       2U
#define BSD_OVER_ALARM_DELAY        0U // 20230905_Will+ 经过讨论，先取消这个，退出机制和buff有重叠
#define LCA_START_ALARM_DELAY       2U
#define LCA_OVER_ALARM_DELAY        3U
#define DOW_START_ALARM_DELAY       10U
#define DOW_OVER_ALARM_DELAY        1U  // 真正需要延迟的一般都是跟踪点没有了. 跟踪点还存在的一般不需要延迟. 当前报警后的buffer也设置的比较大. 一般不容易中断
#define RCTA_START_ALARM_DELAY      2U
#define RCTA_OVER_ALARM_DELAY       3U

#define ADAS_HISTORY_NUM                    20U     // ADAS保留历史坐标帧数.
#define ADAS_HISTORY_LESSER_NUM             10U     // ADAS保留历史坐标帧数.
#define ADAS_HISTORY_LINEAR_REGRESSION_NUM  18U     // ADAS保留历史坐标帧数. 注意: 最大值不能超过ADAS_HISTORY_NUM.
#define ADAS_HISTORY_HEADINGANGLE_NUM       5U      // ADAS保留历史坐标帧数. 注意: 最大值不能超过ADAS_HISTORY_NUM.
#define ADAS_HISTORY_OVERLAP_NUM            5U      // 重叠率均值统计帧数.
#define ADAS_SHORTLIGHT_CONTINUE_TIME       (2.0f)  // 短拨信号维持2.3秒 拨杆信号自带300ms 所以只需2000ms

#define ADAS_OVERLAP_WIDE_WIDTH             (2.2f)  // 此变量主要用于过滤窄目标，窄目标接近于点目标，在统计重叠率时不需要补偿X坐标, 用于描述较宽的目标，如大型货车或大型客车。
#define ADAS_OBJ_MID_COMPACT_WIDTH          (1.4f)  // 此变量主要用于过滤窄目标，窄目标接近于点目标，在统计重叠率时不需要补偿X坐标,用于描述宽度适中的目标，如普通汽车。
#define ADAS_OBJ_MID_SMALL_WIDTH            (0.4f)  // 此变量主要用于过滤窄目标，窄目标接近于点目标，在统计重叠率时不需要补偿X坐标,用于描述较窄的目标，通常是车道上的小车或者自行车等。
#define ADAS_OBJ_MID_COMPACT_LENGTH         (2.5f)  // 此变量主要用于过滤窄目标，窄目标接近于点目标，在统计重叠率时不需要补偿X坐标,用于描述宽度适中的目标，如普通汽车。
#define ADAS_OVERLAP_X_THOD                 (0.0f)  // 统计重叠率目标X坐标的补偿值
#define ADAS_OVERLAP_Y_THOD                 (0.0f)  // 统计重叠率目标Y坐标的补偿值
#define ADAS_OVERLAP_VX_THOD                (0.3f)  // 统计重叠率目标VX坐标的补偿值
#define ADAS_OVERLAP_VY_THOD                (-1.0f) // 统计重叠率目标VY坐标的补偿值
#define ADAS_OVERLAP_Y_WIDE_MAX             (18.0f) // 重叠率目标Y值的最大值
#define ADAS_OVERLAP_Y_WIDE_MIN             (1.0f)  // 重叠率目标Y值的最小值
#define ADAS_OVERLAP_Y_COMPACT_MAX          (18.0f) // 重叠率目标Y值的最大值
#define ADAS_OVERLAP_Y_COMPACT_MIN          (0.5f)  // 重叠率目标Y值的最小值
#define ADAS_OVERLAP_Y_SMALL_MAX            (9.0f)  // 重叠率目标Y值的最大值
#define ADAS_OVERLAP_Y_SMALL_MIN            (1.0f)  // 重叠率目标Y值的最小值

#define ADAS_POINT_MAX_MOVEMENT_DIFFERENCE                  (3.0f)
#define ADAS_POINT_MAX_MOVEMENT_DIF_FRAME_CNT               (10U) // 点移动最大帧数CNT
#define ADAS_POINT_TRACKING_FRAME_MAX                       (600U)
#define ADAS_POINT_BOX_LENGTH_THRESHOLD                     (3.0f) // 目标长度阈值，大于此阈值认为非假点，一票认定 

#define CENTERX_BUFFER_NUM  60
#define CENTERX_BUFFER_NUM_ALLOW_ERROR  3       // BUFFER中对转弯半径的允许误差
#define NEARCENTERX_BUFFER_NUM  20              //(CENTERX_BUFFER_NUM / 3)
#define CENTERX_TURN_SHAKE_THR  500             // 抖动数据阈值
#define CENTERX_TURN_THR  300                   // 弯道的临界阈值

#define ADAS_DEFAULT_FRAME_RATE            (0.06f)       // 帧周期设置默认值0.06 为了兼容北汽的跟踪点回灌.

#if (ADAS_HISTORY_HEADINGANGLE_NUM > ADAS_HISTORY_NUM)
 #error "error ADAS_HISTORY_HEADINGANGLE_NUM is big than ADAS_HISTORY_NUM"
#endif

#endif
