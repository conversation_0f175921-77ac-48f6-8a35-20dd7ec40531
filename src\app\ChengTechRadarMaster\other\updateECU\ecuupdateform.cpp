#include "ecuupdateform.h"
#include "ui_ecuupdateform.h"
#include "ecuupdateitem.h"
#include "ecuupdatemodel.h"
#include "../devices/devicemanager.h"

#include <QDebug>
#include <QFile>
#include <QMessageBox>
#include <QFileDialog>
#include <QtConcurrent>
#include "QtGui/private/qzipreader_p.h"
#include <QHeaderView>
#include <QMessageBox>


#define UNZIP_DIR "./UnZip"
#define SH_EX_FILE_NAME "flowExpand.xml"

ECUUpdateForm::ECUUpdateForm(Devices::Can::DeviceManager * deviceManager,QWidget *parent) :
    QWidget(parent),
    ui(new Ui::ECUUpdateForm),
    mDeviceManager( deviceManager )
{
    ui->setupUi(this);
    //mModel = new ECUUpdateModel( this );
    //ui->tableView->setModel( mModel );

    this->setWindowTitle( QString::fromLocal8Bit( "ECU Update") );

    ui->lineEditFunAddr->setEnabled( false );
    ui->lineEditPhyAddr->setEnabled( false );
    ui->lineEditResponseAddr->setEnabled( false );
    ui->pushButtonRun->setEnabled( false );

    connect( mDeviceManager, &Devices::Can::DeviceManager::deviceOpened, this, &ECUUpdateForm::clear );
}

ECUUpdateForm::~ECUUpdateForm()
{
    delete ui;
    clear();
    qDebug() << __FUNCTION__ << __LINE__;
}

void ECUUpdateForm::on_pushButtonSelect_clicked()
{
    QString filename = ui->lineEditUpdatePack->text();
    QString filter = tr("zip (*.zip)");
    filename = QFileDialog::getOpenFileName(
                              this,
                              QString("Select File"),
                              filename.isEmpty() ? "./" : filename,
                              filter);
    if (!filename.isEmpty()) {
        ui->lineEditUpdatePack->setText( filename );
        clear();
//        on_pushButtonLoad_clicked();
    }
}

void ECUUpdateForm::on_pushButtonLoad_clicked()
{
    clear();
    createTables();

    QString fileName = ui->lineEditUpdatePack->text();


    //解压文件
    //先删除，再创建，确保该目录下只有最新的解压文件
    QDir delDir( UNZIP_DIR );
    delDir.removeRecursively(); //删除
    QDir dir;
    if( !dir.exists( UNZIP_DIR ) ){
        dir.mkpath( UNZIP_DIR ); //创建
    }
    QZipReader reader( fileName );
    if( !reader.extractAll( UNZIP_DIR ) ){ //解压
        QMessageBox::warning( this, "ERROR", QString( "解压失败![%1]" ).arg( fileName ) );
        return;
    }

    //搜寻zflash文件
    QDir unZipDir( UNZIP_DIR );
    QString shFileName = "";
    QString shExFileName = "";
    unZipDir.setFilter( QDir::Files );
    QFileInfoList list = unZipDir.entryInfoList();
    if( list.size() <= 0 ){
        QMessageBox::warning( this, "ERROR", QString( "压缩包内无文件![%1]" ).arg( fileName ) );
        return;
    }
    for(int i=0; i<list.size(); i++){
        QFileInfo file_info = list.at(i);
        QString suffix = file_info.suffix();
        qDebug()<< __FUNCTION__ << __LINE__ << file_info.fileName();
        if(QString::compare(suffix, QString("zflash"), Qt::CaseInsensitive) == 0){
            shFileName = file_info.absoluteFilePath();
            //break;
        }else if( QString::compare(file_info.fileName(), QString(SH_EX_FILE_NAME), Qt::CaseInsensitive) == 0 ){
            shExFileName = file_info.absoluteFilePath();
        }
    }
    if( shFileName.isEmpty() ){
        QMessageBox::warning( this, "ERROR", QString( "压缩包内未找到zflash文件![%1]" ).arg( fileName ) );
        return;
    }


    //解析zflash文件
    if( analysisShFile( shFileName ) ){
        ui->pushButtonRun->setEnabled( true );
    }

    //解析拓展文件
    if( !shExFileName.isEmpty() ){
        analysisShExFile( shExFileName );
    }
}

bool ECUUpdateForm::analysisShFile(const QString &fileName)
{
    //clear();
    QDomDocument doc;
    QFile file;
    file.setFileName( fileName );
    if (!file.open(QIODevice::ReadOnly)){
        //qDebug() << __FUNCTION__ << __LINE__ << "open error" << file.errorString() << file.fileName();
        QMessageBox::information( this, "ERROR", file.errorString() );
        return false;
    }
    if (!doc.setContent(&file)){
        file.close();
        qDebug() << __FUNCTION__ << __LINE__ << "setContent error";
        return false;
    }
    file.close();

    //返回根元素的第一个子节点
    QDomNode n=doc.firstChild ();
    while( !n.isNull() ) {
        if( n.isElement() ) {            //将其转换为元素
            QDomElement e = n.toElement ();
            //获得元素e的所有子节点列表
            QDomNodeList list = e.childNodes ();
            for (int i = 0; i< list.count ();i++) {
                QDomNode node = list.at(i);
                if (node.isElement ()) {//使用text()来获取其中的文本内容
                    createECUUpdateItem( node.toElement() );
                }
            }
        }
        n = n.nextSibling ();//nextSibling()获取下一个兄弟节点
    }
    if( !updateAddrs() ){
        return false;
    }

    return true;
}

bool ECUUpdateForm::getChildElement(const QDomElement &element, const QString &tagName, QDomElement& childElement)
{
    QDomNodeList list = element.elementsByTagName( tagName );
    if( list.isEmpty() ){
        return false;
    }
    if( !list.at( 0 ).isElement() ){
        return false;
    }
    childElement = list.at( 0 ).toElement();
    return true;
}

void ECUUpdateForm::insertSendCmdItem(const QDomElement &element)
{
    QDomElement child;
    QByteArray udsCmd;
    bool before = false;
    if( getChildElement( element, "UdsCmd", child ) ){
        udsCmd = QByteArray::fromHex( child.text().remove(QRegExp("\\s")).toLocal8Bit() );
    }
    if( getChildElement( element, "Before", child ) ){
        before = QVariant( child.text() ).toBool();
    }

    for( int i=0; i<mModels.size(); i++ ){
        ECUUpdateItem_SendCmd* item = new ECUUpdateItem_SendCmd( mDeviceManager );
        if( !item->init( element ) ){
            continue;
        }
        mModels[i]->insertData( item, udsCmd, before );
    }
}

void ECUUpdateForm::updateWholeWordMatchForGeneralItem(const QDomElement &element)
{
    QDomElement child;
    QByteArray udsCmd;
    QString desc;
    QByteArray word;
    bool bEqual = true;
    if( getChildElement( element, "UdsCmd", child ) ){
        udsCmd = QByteArray::fromHex( child.text().remove(QRegExp("\\s")).toLocal8Bit() );
    }
    if( getChildElement( element, "Desc", child ) ){
        desc = child.text();
    }
    if( getChildElement( element, "Word", child ) ){
        word = QByteArray::fromHex( child.text().remove(QRegExp("\\s")).toLocal8Bit() );
    }
    if( getChildElement( element, "Equal", child ) ){
        bEqual = QVariant( child.text() ).toBool();
    }

    QByteArrayList words;
    words << word;
    qDebug() << __FUNCTION__ << __LINE__ << udsCmd.toHex(' ')
             << desc << words << bEqual;
    for( int i=0; i<mModels.size(); i++ ){
        mModels[i]->updateMatchData( udsCmd, desc, words, true, bEqual );
    }
}

void ECUUpdateForm::updateSubStrMatchForGeneralItem(const QDomElement &element)
{
    QDomElement child;
    QByteArray udsCmd;
    QString desc;
    bool bContain = true;
    QByteArrayList words;
    if( getChildElement( element, "UdsCmd", child ) ){
        udsCmd = QByteArray::fromHex( child.text().remove(QRegExp("\\s")).toLocal8Bit() );
    }
    if( getChildElement( element, "Desc", child ) ){
        desc = child.text();
    }
//    if( getChildElement( element, "Word", child ) ){
//        word = QByteArray::fromHex( child.text().remove(QRegExp("\\s")).toLocal8Bit() );
//    }
    if( getChildElement( element, "contain", child ) ){
        bContain = QVariant( child.text() ).toBool();
    }

    //子串列表
    QDomNodeList list = element.elementsByTagName( "SubStr" );
    for( int i=0; i<list.size(); i++ ){
        QDomElement e = list.at( i ).toElement();
        QByteArray word = QByteArray::fromHex( e.text().remove(QRegExp("\\s")).toLocal8Bit() );
        words << word;
    }

    qDebug() << __FUNCTION__ << __LINE__ << udsCmd.toHex(' ')
             << desc << words << bContain;
    for( int i=0; i<mModels.size(); i++ ){
        mModels[i]->updateMatchData( udsCmd, desc, words, false, bContain );
    }
}

bool ECUUpdateForm::analysisShExFile(const QString &fileName)
{
    /*
    <?xml version="1.0" encoding="GB2312"?>
    <!--UDS 流程拓展 zflash的补充内容-->
    <FlowExpand>
      <SendCmd>								<!--发送指令-->
        <Desc>唤醒</Desc>
        <UdsCmd></UdsCmd> 					<!--zflash中指令 为空则表示最前或最后-->
        <Before>true</Before> 				<!--true表示在zflash中指令的前面执行，否则为后面执行-->
        <ID>0x411</ID> 						<!--发送的指令ID-->
        <Cmd>00 40 00 00 00 00 00 00</Cmd> 	<!--发送的指令-->
      </SendCmd>
      <WholeWordMatch>						<!--整词匹配-->
        <Desc>版本验证</Desc>
        <UdsCmd>22 F1 95</UdsCmd>			<!--zflash中的指令-->
        <Word>FF FF</Word>					<!--匹配数据，与zflash中指令的响应结果比较-->
        <Equal>true</Equal>					<!--true表示相等才匹配成功，否则不相等才匹配成功-->
      </WholeWordMatch>
      <SubStrMatch>							<!--子串匹配-->
        <Desc>DTC黑名单</Desc>
        <UdsCmd>19 02 09</UdsCmd>			<!--zflash中的指令-->
        <SubStr>FF FF FF FF</SubStr>		<!--匹配数据，在zflash中指令的响应结果中查找-->
        <SubStr>FF FF FF FF</SubStr>		<!--匹配数据，在zflash中指令的响应结果中查找-->
        <SubStr>FF FF FF FF</SubStr>		<!--匹配数据，在zflash中指令的响应结果中查找-->
        <SubStr>FF FF FF FF</SubStr>		<!--匹配数据，在zflash中指令的响应结果中查找-->
        <contain>false</contain>			<!--true表示包含才匹配成功，否则不包含才匹配成功-->
      </SubStrMatch>
    </FlowExpand>
    */

//    qDebug() << __FUNCTION__ << __LINE__ << fileName;
    QDomDocument doc;
    QFile file;
    file.setFileName( fileName );
    if (!file.open(QIODevice::ReadOnly)){
        qDebug() << __FUNCTION__ << __LINE__ << fileName;
        QMessageBox::information( this, "ERROR", file.errorString() );
        return false;
    }

    QString errorMsg;
    if (!doc.setContent(&file,&errorMsg)){
        file.close();
        qDebug() << __FUNCTION__ << __LINE__ << "setContent error" << errorMsg;
        return false;
    }
    file.close();

    QDomNodeList sendCmdList;
    QDomNodeList wholeWordMatchList;
    QDomNodeList subStrMatchList;
    //返回根元素的第一个子节点
    QDomNodeList flowExpandList = doc.elementsByTagName( "FlowExpand" );
    for( int i=0; i<flowExpandList.size(); i++ ){
        QDomNode n = flowExpandList.at(i);
        if( n.isElement() ){
            QDomElement e = n.toElement();
            sendCmdList = e.elementsByTagName( "SendCmd" );
            wholeWordMatchList = e.elementsByTagName( "WholeWordMatch" );
            subStrMatchList = e.elementsByTagName( "SubStrMatch" );
            break;
        }
    }

    for( int i=0; i<sendCmdList.size(); i++ ){
        QDomElement e = sendCmdList.at(i).toElement();
        insertSendCmdItem( e );
    }
    for( int i=0; i<wholeWordMatchList.size(); i++ ){
        //qDebug() << __FUNCTION__ << __LINE__ << wholeWordMatchList.at(i).toElement().tagName();
        QDomElement e = wholeWordMatchList.at(i).toElement();
        updateWholeWordMatchForGeneralItem( e );
    }
    for( int i=0; i<subStrMatchList.size(); i++ ){
        QDomElement e = subStrMatchList.at(i).toElement();
        updateSubStrMatchForGeneralItem( e );
    }


    return true;
}

bool ECUUpdateForm::createECUUpdateItem( const QDomElement& element )
{
    for( int i=0; i<mModels.size(); i++ ){
        ECUUpdateItem* pItem = NULL;
        if( element.tagName() != "item" ){
            return NULL;
        }

        QString type = element.attribute( "type" );
        if( type == "setting" ){
            pItem = new ECUUpdateItem_Setting( mDeviceManager );
        }else if( type == "general" ){
            pItem = new ECUUpdateItem_General( mDeviceManager );
        }else if( type == "security" ){
            pItem = new ECUUpdateItem_Security( mDeviceManager );
        }else if( type == "download" ){
            pItem = new ECUUpdateItem_Download( mDeviceManager );
        }else if( type == "delay" ){
            pItem = new ECUUpdateItem_Delay( mDeviceManager );
        }

        if( pItem && !pItem->init( element ) ){
            delete pItem;
            pItem = NULL;
        }

        if( pItem && type != "setting" ){
            mModels[i]->addData( pItem );
            if( type == "security" ){
                ( (ECUUpdateItem_Security*)pItem )->loadDLLFunPtr( UNZIP_DIR );
            }else if( type == "download" ){
                ( (ECUUpdateItem_Download*)pItem )->loadFile( UNZIP_DIR );
            }
        }else{
            if( !mSettingItem ){
                mSettingItem = pItem;
            }else{ //防止有多个setting
                delete pItem;
                pItem = NULL;
            }
        }
    }

    return true;
}

bool ECUUpdateForm::updateAddrs()
{
    if( !mSettingItem ){
        QMessageBox::warning( this, "ERROR", QString( "zflash文件中未找到地址设置!请检查该文件" )  );
        return false;
    }

    ui->lineEditFunAddr->setText( QString::number( mSettingItem->funAddr(), 16 ) );
    ui->lineEditPhyAddr->setText( QString::number( mSettingItem->phyAddr(), 16 ) );
    ui->lineEditResponseAddr->setText( QString::number( mSettingItem->dstAddr(), 16 ) );

    for( int i=0; i<mModels.size(); i++ ){
        mModels[i]->updateAddrs( mSettingItem->phyAddr(), mSettingItem->funAddr(), mSettingItem->dstAddr() );
    }
    return  true;
}

void ECUUpdateForm::clear()
{
    disconnect( mDeviceManager->deviceWorker(), &Devices::Can::IDeviceWorker::frameRecieved, this, &ECUUpdateForm::showCanFrame );
    disconnect( mDeviceManager->deviceWorker(), &Devices::Can::IDeviceWorker::frameTransmited, this, &ECUUpdateForm::showCanFrame );

    ui->lineEditFunAddr->setText( "" );
    ui->lineEditPhyAddr->setText( "" );
    ui->lineEditResponseAddr->setText( "" );
    ui->pushButtonRun->setEnabled( false );

    if( mSettingItem ){
        qDebug() << __FUNCTION__ << __LINE__ << mSettingItem->funAddr();
        delete mSettingItem;
        mSettingItem = NULL;
    }
    qDebug() << __FUNCTION__ << __LINE__ << "delete mSettingItem success";

//    if( mModel ){
//        mModel->clear();
//    }

    for( int i=0; i<mModels.size(); i++ ){
        disconnect( mModels[i], &ECUUpdateModel::running, this, &ECUUpdateForm::scrollTo );
        delete mModels[i];
    }
    mModels.clear();

    qDebug() << __FUNCTION__ << __LINE__ << "delete mModels success";

    for( int i=0; i<mCmdTables.size(); i++ ){
        delete mCmdTables[i];
    }
    mCmdTables.clear();

    qDebug() << __FUNCTION__ << __LINE__ << "delete mCmdTables success";

    for( int i=0; i<mCanFrameLogs.size(); i++ ){
        delete mCanFrameLogs[i];
    }
    mCanFrameLogs.clear();

    qDebug() << __FUNCTION__ << __LINE__ << "delete mCanFrameLogs success";
}

void ECUUpdateForm::createTables()
{
    //return;
    for( int i=0; i<mDeviceManager->channelCount(); i++ ){
        QTableView* pCmdTable = new QTableView( this );
        QTextBrowser* pCanFrameLog = new QTextBrowser( this );
        ui->horizontalLayout_cmd->addWidget( pCmdTable );
        ui->horizontalLayout_canFrame->addWidget( pCanFrameLog );


        //设置列宽自适应
        pCmdTable->horizontalHeader()->setSectionResizeMode( QHeaderView::ResizeToContents );
        pCmdTable->verticalHeader()->setSectionResizeMode( QHeaderView::ResizeToContents );

        ECUUpdateModel* model = new ECUUpdateModel( this );
        pCmdTable->setModel( model );

        mModels.append( model );
        mCmdTables.append( pCmdTable );
        mCanFrameLogs.append( pCanFrameLog );

        connect( model, &ECUUpdateModel::running, this, &ECUUpdateForm::scrollTo );
    }
}

void ECUUpdateForm::on_pushButtonRun_clicked()
{
    if( !mDeviceManager->isOpened() ){
        QMessageBox::warning( this, "waring", "Please open the device!!" );
        return;
    }

    connect( mDeviceManager->deviceWorker(), &Devices::Can::IDeviceWorker::frameRecieved, this, &ECUUpdateForm::showCanFrame, Qt::UniqueConnection );
    connect( mDeviceManager->deviceWorker(), &Devices::Can::IDeviceWorker::frameTransmited, this, &ECUUpdateForm::showCanFrame, Qt::UniqueConnection );

    for( int i=0; i<mModels.size(); i++ ){
        if( mModels[i]->isAbnormal() ){
            QMessageBox::warning( this, "ERROR", QString( "zflash脚本存在异常!" ) );
            return;
        }
        mCanFrameLogs[i]->clear();
        mModels[i]->initRunResult();
        QtConcurrent::run( mModels[i], &ECUUpdateModel::run, i );
    }
}

void ECUUpdateForm::showCanFrame(const Devices::Can::CanFrame &frame)
{
    if( !mSettingItem ){
        return;
    }

    if( frame.id() != mSettingItem->dstAddr() && frame.id() != mSettingItem->phyAddr() && frame.id() != mSettingItem->funAddr() ){
        return;
    }

    quint32 channel = frame.channelIndex();
    QString head = QString( "%1==>").arg( QDateTime::currentDateTime().toLocalTime().toString( "hh:mm:ss:zzz" ) );
    QString text = QString("%1\t%2\t%3")
            .arg( frame.recieveOrTransmit() == Devices::Can::CanFrame::RecieveOrTransmit::RX ? "RX" : "TX")
            .arg( frame.idHex() )
            .arg( frame.dataHex() );

    if( frame.recieveOrTransmit() == Devices::Can::CanFrame::RecieveOrTransmit::RX ){
        mCanFrameLogs[ channel ]->setTextColor( Qt::blue );
    }else{
        mCanFrameLogs[ channel ]->setTextColor( Qt::green );
    }

    mCanFrameLogs[ channel ]->append( head + text );
    mCanFrameLogs[ channel ]->moveCursor(QTextCursor::End);
}

void ECUUpdateForm::scrollTo(int row, quint8 channelIndex)
{
    QModelIndex modelIndex = mCmdTables[ channelIndex ]->model()->index( row, 0);
    mCmdTables[ channelIndex ]->scrollTo(modelIndex); // 滚动到最后一行
}













