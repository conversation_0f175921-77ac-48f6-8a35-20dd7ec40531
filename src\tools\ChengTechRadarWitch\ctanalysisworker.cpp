﻿#include "ctanalysisworker.h"

CTAnalysisWorker::CTAnalysisWorker(QObject *parent) : QObject(parent)
{
    connect(&mTimer, &QTimer::timeout, this, &CTAnalysisWorker::isOpened);
    mTimer.start(100);
}

void CTAnalysisWorker::newState()
{
    mHeader = mBody = mTrail = false;
}

void CTAnalysisWorker::canFrame(const Devices::Can::CanFrame &frame)
{
//    frame.print(__FUNCTION__, __LINE__);

    switch (frame.id()) {
    case 0x600:
        mHeader = true;
    case 0x710:
        mBody = true;
    case 0x5F0:
        mTrail = true;
        break;
    default:
        break;
    }
}

void CTAnalysisWorker::isOpened()
{
    emit stateChanged(mHeader || mBody || mTrail);
    newState();
}
