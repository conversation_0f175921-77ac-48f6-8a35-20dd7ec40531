#ifndef EOLPROTOCOL_H
#define EOLPROTOCOL_H

#include "eoldefine.h"

#include <QObject>
#include <QTimer>

//CAN ID
#define EOL_PROTOCOL_MASTER_CAN_ID    0x157U  /**< 上位机CAN ID */
#define EOL_PROTOCOL_REPLY_CAN_ID     0x257U  /**< 回复上位机CAN ID */

//功能码
#define EOL_PROTOCOL_WRITE_CMD 0x00 //写入
#define EOL_PROTOCOL_READ_CMD 0x01 //读取
#define EOL_PROTOCOL_META_CMD 0xFF //数据裸露发送

//寄存器地址
#define EOL_R_ACCESS_CODE_REG          0x00U  /**< 读访问码 */
#define EOL_W_RUN_MODE_REG             0x01U  /**< 写运行模式 */
#define EOL_R_PROFILE_NUM_CH_NUM       0x02U  /**< 读EOL下配置数和通道数 */

/* 读写表 */
#define EOL_RW_TABLE_DATA_REG          0x07U  /**< 读写表数据，帧计数为0时传输表信息，其他为数据帧 */
#define EOL_W_TABLE_SEL_REG            0x08U  /**< 设置需要读取的表 */
#define EOL_W_TABLE_DATA_SEL_REG       0x09U  /**< 设置需要特定帧的表数据（用于丢帧重传） */


#define EOL_W_DEVICE_REBOOT_REG        0x0AU  /**< 设置设备重启 */

#define EOL_RW_SHELL_REG               0x1DU  /**< 读写shell，当读命令时持续监听eol协议数据 */



#define EOL_ERROR_REG                   0xFFU //错误寄存器地址


//帧头相关
#define MASTER_EOL_FRAME_HEADER       0x7AU   /**< 上位机帧头 */
#define SLAVE_EOL_FRAME_HEADER        0x75U   /**< 下位机帧头 */
#define EOL_DEVICE_COM_ADDR           0x55U   /**< 下位机通讯地址 */

//响应超时-毫秒数
#define RESPONSE_TIME_OUT_MS          50
#define RESPONES_TIME_OUT_CNT         10

namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
}

enum EOL_MODE_TYPE{
    EOL_NORMAL_RUN = 0, //正常运行模式
    EOL_PRODUCE_NORMAL = 1, //生产普通模式
    EOL_PRODUCE_DEBUG = 2 //生产调试模式
};

//enum EOL_MSG_TYPE{
//    EOL_MSG_NORMAL = 0,
//    EOL_MSG_REQUEST,
//    EOL_MSG_RESPONSE,
//    EOL_MSG_ERROR
//};

class EOLTableMgr;
class EOLProtocol : public QObject
{
    Q_OBJECT
public:
    explicit EOLProtocol( Devices::Can::DeviceManager *deviceManager, QObject *parent = nullptr);
    ~EOLProtocol();

    //判断是否为EOL协议中上位机的请求帧ID
    static bool isEolAskCanID( quint64 can_id );
    //判断是否为EOL协议下位机响应的帧ID
    static bool isEOLReplyCanID( quint64 can_id );

public slots:
    void enterMode( quint8 type );//进入生产模式
    void rebootRadar();//重启雷达
    void loadTable( const QString& fileName );
    void writeTable();
    void readTable( quint8 tableClass, quint8 pro_id, const QString& path );
    void sendShellCmd( const QString& cmd );

    void recvFrame( const Devices::Can::CanFrame& frame );

signals:
    void showMsg( const QString& msg, EOL_MSG_TYPE/*quint8*/ type/* = 0*/ );
    void ResponseTimeOut();

    void enableUI( bool bEnable );
    void shellResult( const QString& result );

private slots:
    void decodeShellTimerSlot();

private:
    //写请求
    bool request_enMode( EOL_MODE_TYPE run_mode );//运行模式

    //读请求
    bool request_safeCode();//请求安全码
    bool request_param1(); //读取配置数、通道数等参数

    //解析
    bool decode_safeCode( const QByteArray& data );
    bool decode_param1( const QByteArray& data );
    bool decode_table( const QByteArray& data );
    bool decode_shell();

private:
    bool request_read( quint8 reg_addr );//读请求
    bool request_write( quint8 reg_addr, const QByteArray& data ); //写请求

    void request_msg( quint8 reg_addr, bool bRead );
    bool request_checkRunMode( quint8 reg_addr ); //判断在当前运行模式下，是否使用该地址

    bool sendFrame( const QByteArray& data, quint8 reg_addr, quint8 cmds ); //发送数据

private:
    bool decodeRecvData(); //解析数据
    bool decodeAckData(); //解析写入的响应数据
    bool decodeReadData(); //解析读取的响应数据

    bool wait( quint64 ms ); //等待
    void responsetimeOut(); //响应超时

    void clearTables();
private:
    Devices::Can::DeviceManager *mDeviceManager;

//    quint8 mCurrentRegAddr;
//    quint8 mCurrentCmd;
    QByteArray mCurrentSafeCode;//安全码

    QByteArray mRecvData;
    quint32 mTimeOutCnt;

    QList<EOLTableMgr*> mTables;
    bool mReadTable{false};

    QTimer mDecodeShellTimer;
    EOL_MODE_TYPE mCurrentRunMode{EOL_NORMAL_RUN};
};

#endif // EOLPROTOCOL_H
