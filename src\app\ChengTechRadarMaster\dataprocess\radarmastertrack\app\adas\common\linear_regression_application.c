﻿/**
 * @file linear_regression_application.c
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2024-11-26
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-11-26 <td>1.0     <td>Will <PERSON>     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#include <stdbool.h>
#include <math.h>
#include <string.h>

#ifdef ALPSPRO_ADAS
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "rdp/track/data_process/rdp_interface.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/common/linear_regression.h"
#include "adas/common/linear_regression_application.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#elif defined(PC_DBG_FW)
#include "alg/track/rdp_interface.h"
#include "app/adas/customizedrequirements/adas_vehicle_ctrls.h"
#include "app/adas/customizedrequirements/adas_signal_integration.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/vehicle/vdy/vdy_interface.h"
#include "app/vehicle/vdy/vdy_types.h"
#include <windows.h>
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/system_mgr/typedefs.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/common/linear_regression_application.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "other/temp.h"
#else
#include "app/rdp/rdp_clth_radar_lib.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/common/linear_regression_application.h"
#endif

static float stored_x[ADAS_HISTORY_NUM];
static float stored_y[ADAS_HISTORY_NUM];

/* 归一化参数 */
#define MSE_MAX_VALUE           (1.0f)    // MSE最大参考值
#define VARIANCE_MAX_VALUE      (1.0f)    // 方差最大参考值
#define THRESHOLD_MIN           (0.1f)    // 阈值最小值
#define THRESHOLD_MAX           (2.0f)    // 阈值最大值

// 状态
#define ADAS_DECISION_POSITIVE  (1U)
#define ADAS_DECISION_NEGATIVE  (0U)
/* 置信度最大值，可以是100.0表示百分比，或者1.0表示归一化值 */
#define CONFIDENCE_MAX          (100.0f)  

/* ------------------配置常量定义------------------ */
/* 默认配置参数 */
static const ADAS_LR_EvalConfig LR_DEFAULT_CONFIG = {
    .r2_weight = 0.4f,
    .mse_weight = 0.35f,
    .var_weight = 0.25f,
    .sigmoid_temp = 0.45f,
    .sigmoid_offset = 0.8f,
    .decision_threshold = 0.90f
};

/* 场景特定配置 */
static const ADAS_LR_EvalConfig SCENE_CONFIGS[7] = {
    /* 高速公路场景 - 更注重R²值 */
    {
        .r2_weight = 0.5f,
        .mse_weight = 0.3f,
        .var_weight = 0.2f,
        .sigmoid_temp = 0.4f,
        .sigmoid_offset = 0.65f,
        .decision_threshold = 0.7f
    },
    /* 城市道路场景 - 均衡配置 */
    {
        .r2_weight = 0.4f,
        .mse_weight = 0.3f,
        .var_weight = 0.3f,
        .sigmoid_temp = 0.5f,
        .sigmoid_offset = 0.7f,
        // .decision_threshold = 0.65f
        .decision_threshold = 0.85f
    },
    /* 弯道场景 - 更注重方差 */
    {
        .r2_weight = 0.35f,
        .mse_weight = 0.25f,
        .var_weight = 0.4f,
        .sigmoid_temp = 0.6f,
        .sigmoid_offset = 0.75f,
        .decision_threshold = 0.6f
    },
    /* 复杂场景 - 更严格的标准 */
    {
        .r2_weight = 0.4f,
        .mse_weight = 0.35f,
        .var_weight = 0.25f,
        .sigmoid_temp = 0.45f,
        .sigmoid_offset = 0.8f,
        .decision_threshold = 0.75f
    },
    /* 严格场景 - 15s后 */
    {
        .r2_weight = 0.4f,
        .mse_weight = 0.35f,
        .var_weight = 0.25f,
        .sigmoid_temp = 0.45f,
        .sigmoid_offset = 0.8f,
        .decision_threshold = 0.93f
    },
    /* 宽松场景 - 15s内 */
    {
        .r2_weight = 0.50f,
        .mse_weight = 0.30f,
        .var_weight = 0.20f,
        .sigmoid_temp = 0.45f,
        .sigmoid_offset = 0.8f,
        .decision_threshold = 0.82f
    },
    /* 默认场景 */
    {
        .r2_weight = 0.4f,
        .mse_weight = 0.35f,
        .var_weight = 0.25f,
        .sigmoid_temp = 0.45f,
        .sigmoid_offset = 0.8f,
        .decision_threshold = 0.90f
    }
};

/* ------------------模块全局变量------------------ */
static ADAS_LR_EvalConfig g_lr_current_config;                // 当前使用的配置
static ADAS_SceneType g_lr_current_scene = ADAS_SCENE_STRICT; // 当前场景类型
static int g_lr_module_initialized = 0;                       // 模块初始化标志
/* Sigmoid查找表 */
// static float g_lr_sigmoid_lut[256];

/* ------------------函数声明------------------ */
static void ADAS_LR_updateConfigForScene(ADAS_SceneType scene);
static ADAS_SceneType ADAS_getSceneType(void);

/**
 * @brief 初始化Sigmoid查找表
 *
 * 为常用的Sigmoid函数值创建查找表以提高性能
 */
static void ADAS_initSigmoidLUT(void)
{
    static uint8_t g_lr_sigmoid_lut_initialized = 0U;
    if (g_lr_sigmoid_lut_initialized)
    {
        return;
    }
    uint16_t j = 0U;

    const float range = 10.0f; // 查找表覆盖[-5,5]的范围
    for (j = 0U; j < 256U; j++)
    {
        float x = (j / 255.0f) * range - range / 2.0f;
        // g_lr_sigmoid_lut[j] = 1.0f / (1.0f + expf(-x)); 
    }

    g_lr_sigmoid_lut_initialized = 1U;
}

/**
 * @brief 初始化评估模块
 *
 * 初始化全局配置和Sigmoid查找表
 *
 * @return ADAS_EVAL_OK表示成功，其他值表示错误
 */
ADAS_LR_EvalStatus ADAS_LR_initEvaluationModule(void)
{
    /* 初始化默认配置 */
    memcpy(&g_lr_current_config, &LR_DEFAULT_CONFIG, sizeof(ADAS_LR_EvalConfig));

    /* 初始化Sigmoid查找表 */
    ADAS_initSigmoidLUT();

    g_lr_module_initialized = 1;
    return ADAS_EVAL_OK;
}
/**
 * @brief 设置评估配置
 *
 * @param config 配置参数指针
 * @return ADAS_EVAL_OK表示成功，其他值表示错误
 */
ADAS_LR_EvalStatus ADAS_setEvalConfig(const ADAS_LR_EvalConfig *config)
{
    if (config == NULL)
    {
        return ADAS_EVAL_NULL_PTR;
    }

    /* 参数有效性检查 */
    ADAS_SceneType scene= ADAS_getSceneType(); 
    if ((scene > ADAS_SCENE_INVALID && scene < ADAS_SCENE_MAX) && (ADAS_SCENE_LENIENT != scene)) // 宽松场景不需要
    {
        float total_weight = config->r2_weight + config->mse_weight + config->var_weight;
        if (fabsf(total_weight - 1.0f) > FLOAT_EPS ||
            config->sigmoid_temp <= 0.0f)
        {
            return ADAS_EVAL_INVALID_PARAM;
        }
    }

    memcpy(&g_lr_current_config, config, sizeof(ADAS_LR_EvalConfig));
    return ADAS_EVAL_OK;
}
/**
 * @brief 获取当前评估配置
 *
 * @param config [出参] 配置参数结构体指针
 * @return ADAS_EVAL_OK表示成功，其他值表示错误
 */
ADAS_LR_EvalStatus ADAS_getEvalConfig(ADAS_LR_EvalConfig *config)
{
    if (config == NULL)
    {
        return ADAS_EVAL_NULL_PTR;
    }

    if (!g_lr_module_initialized)
    {
        return ADAS_EVAL_INVALID_PARAM;
    }

    memcpy(config, &g_lr_current_config, sizeof(ADAS_LR_EvalConfig));
    return ADAS_EVAL_OK;
}

/**
 * @brief 设置场景类型
 *
 * @param scene 场景类型
 * @return ADAS_EVAL_OK表示成功，其他值表示错误
 */
ADAS_LR_EvalStatus ADAS_setSceneType(ADAS_SceneType scene)
{
    if (scene < ADAS_SCENE_HIGHWAY || scene > ADAS_SCENE_AUTO)
    {
        return ADAS_EVAL_INVALID_PARAM;
    }

    g_lr_current_scene = scene;

    /* 如果不是自动模式，则立即更新配置 */
    if (scene != ADAS_SCENE_AUTO)
    {
        ADAS_LR_updateConfigForScene(scene);
    }

    return ADAS_EVAL_OK;
}

/**
 * @brief 设置场景类型
 *
 * @param scene 场景类型
 * @return ADAS_EVAL_OK表示成功，其他值表示错误
 */
static ADAS_SceneType ADAS_getSceneType(void)
{
    if (g_lr_current_scene < ADAS_SCENE_HIGHWAY || g_lr_current_scene > ADAS_SCENE_AUTO)
    {
        return ADAS_SCENE_INVALID;
    }

    ADAS_SceneType scene = g_lr_current_scene;

    return scene;
}

/**
 * @brief 快速Sigmoid函数实现
 *
 * 使用查找表加速Sigmoid计算，对于查找表范围外的值使用直接计算
 *
 * @param x 输入值
 * @param temperature 温度参数，控制Sigmoid的陡峭程度
 * @return Sigmoid函数值
 */
#if 0
static float ADAS_fastSigmoid(float x, float temperature)
{
    const float range = 10.0f;
    float sigmoidValue = 0.0f;

    /* 应用温度参数 */
    x = x / temperature;

    /* 使用查找表加速计算 */
    if (x >= -range / 2.0f && x <= range / 2.0f)
    {
        float normalized_x = (x + range / 2.0f) / range;
        int index = (int)(normalized_x * 255.0f);
        /* 边界检查 */
        index = index < 0 ? 0 : (index > 255 ? 255 : index);

        return g_lr_sigmoid_lut[index];
    }

    /* 查找表范围外使用直接计算 */
    sigmoidValue =  1.0f / (1.0f + expf(-x));
    return sigmoidValue;
}
#endif

/**
 * @brief 检测当前场景类型
 *
 * 根据路径形状、车速、道路曲率等特征自动检测场景类型
 *
 * @param pobjAlm 算法对象指针
 * @param pobjPath 路径对象指针
 * @param index 当前索引
 * @return 检测到的场景类型
 */
static ADAS_SceneType ADAS_detectSceneType(const ALARM_OBJECT_T *pobjAlm,
                                           const OBJ_NODE_STRUCT *pobjPath,
                                           const uint8_t i,
                                           const VDY_Info_t *pVDY)
{
#if 0
    /* 计算道路曲率 */
    float curvature = 0.0f;
    float max_curvature = 0.0f;
    float avg_curvature = 0.0f;
    int valid_samples = 0;

    /* 使用前5个点计算曲率 */
    int sample_count = fminf(5, index + 1);
    for (int i = 0; i < sample_count; i++)
    {
        if (index - i >= 0)
        {
            /* 简化的曲率计算 */
            float dx = pobjPath->stored_x[index - i];
            float dy = pobjPath->stored_y[index - i];
            float curr_curvature = fabsf(dx * dy) / (powf(dx * dx + dy * dy, 1.5f) + FLOAT_EPS);

            if (!isnan(curr_curvature) && !isinf(curr_curvature))
            {
                curvature += curr_curvature;
                max_curvature = fmaxf(max_curvature, curr_curvature);
                valid_samples++;
            }
        }
    }

    if (valid_samples > 0)
    {
        avg_curvature = curvature / valid_samples;
    }
#endif

    ADAS_SceneType scene = ADAS_SCENE_AUTO;
    /* 获取车速、yawrate */
    float vehicle_speed = pobjAlm->BSDVelSpeedVal;
    float vehicle_curvature = pVDY->pVDY_DynamicInfo->vdyYawRate;

    /* 根据曲率和车速确定场景类型 */
    if (vehicle_curvature > 0.05f)
    {
        /* 高曲率表示弯道场景 */
        scene = ADAS_SCENE_CURVE;
    }
    else if (vehicle_speed > 80.0f && vehicle_curvature < 0.01f)
    {
        /* 高速且低曲率表示高速公路场景 */
        scene = ADAS_SCENE_HIGHWAY;
    }
    //else if (vehicle_speed < 60.0f && pobjAlm->obstacle_count > 2)
    //{
    //    /* 低速且有多个障碍物表示复杂场景 */
    //    scene = ADAS_SCENE_COMPLEX;
    //}
    else
    {
        /* 默认为城市道路场景 */
        // scene = ADAS_SCENE_URBAN;
        /* 默认为严格场景 */
        scene = ADAS_SCENE_STRICT; 
    }

    if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
       ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)) || // 4s
       (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
    {
        /* 宽松评估场景（容忍更大误差） */
        scene = ADAS_SCENE_LENIENT;
    }

    return scene;
}

/**
 * @brief 根据场景类型更新配置
 *
 * @param scene 场景类型
 */
static void ADAS_LR_updateConfigForScene(ADAS_SceneType scene)
{
    if (scene < ADAS_SCENE_HIGHWAY || scene >= ADAS_SCENE_MAX)
    {
        /* 无效场景类型，使用默认配置 */
        memcpy(&g_lr_current_config, &LR_DEFAULT_CONFIG, sizeof(ADAS_LR_EvalConfig));
        return;
    }

    /* 更新为场景特定配置 */
    memcpy(&g_lr_current_config, &SCENE_CONFIGS[scene], sizeof(ADAS_LR_EvalConfig));
}

/**
 * @brief 模型评估核心函数
 *
 * 该函数计算模型的R²值、MSE和方差，并通过加权组合生成一个综合评分。
 * 评分计算公式: score = w1*R² + w2*(1-MSE) + w3*(1-VAR)
 * 权重值根据当前场景类型动态调整
 *
 * @param pobjAlm 算法对象指针
 * @param pobjPath 路径对象指针
 * @param index 当前索引
 * @param result [出参] 评估结果
 *
 * @return ADAS_EVAL_OK表示成功，其他值表示错误
 *
 * @note R²值范围为[0,1]，越大越好；MSE和方差为[0,+∞)，越小越好
 **/
ADAS_LR_EvalStatus ADAS_LR_EvaluateModel(const ALARM_OBJECT_T *pobjAlm,
                                         OBJ_NODE_STRUCT *pobjPath,
                                         const uint8_t i,
                                         const VDY_Info_t *pVDY,
                                         const OBJ_ADAS_TYPE_ENUM type,
                                         const uint8_t n,
                                         float *normalize_x,
                                         float *normalize_y,
                                         float w,
                                         float b)
{
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (i >= ADAS_TRK_OBJNUM) || (pVDY == NULL) || 
        (type >= ADAS_TYPE_MAX) || (n > ADAS_HISTORY_NUM) || (normalize_x == NULL) || (normalize_y == NULL))
    {
        return ADAS_EVAL_NULL_PTR;
    }

    ADAS_LR_EvaluationResult pResult;
    // float sigmoid_input = 0.0f;
    float x_new = 0.0f, y_new = 0.0f;
    float variance_threshold = 0.0f;
    float mse_threshold = 0.0f;

    float r2_expected_buff = 0.0f;
    float mse_good_buff = 0.0f, mse_bad_buff = 0.0f;
    float var_good_buff = 0.0f, var_bad_buff = 0.0f;

    if (!g_lr_module_initialized)
    {
        ADAS_LR_initEvaluationModule();
    }

    /* 清除结果结构体 */
    memset(&pResult, 0x0, sizeof(ADAS_LR_EvaluationResult));
    pResult.status = ADAS_EVAL_INVALID_PARAM;

    /* 检测场景类型并更新配置（仅自动模式） */
    ADAS_SceneType cur_scene = ADAS_detectSceneType(pobjAlm, pobjPath, i, pVDY); // 当前场景类型
    if (g_lr_current_scene != cur_scene)
    {
        pResult.scene_type = cur_scene;    // ADAS_detectSceneType(pobjAlm, pobjPath, i, pVDY);
        ADAS_setSceneType(cur_scene);
        ADAS_LR_updateConfigForScene(pResult.scene_type);
    }
    else
    {
        pResult.scene_type = g_lr_current_scene;
    }

    /* 从算法对象中获取模型参数 */
    /* 计算评估指标 */
    // 使用新点 (当前点) 验证均方误差, 调用均方误差计算函数
    x_new = normalize_x[0];
    y_new = normalize_y[0];
    pResult.mse_var = ADAS_CalculateSinglePointMSE(x_new, y_new, w, b);
    pResult.r_squared = ADAS_CalculateRSquared(pobjAlm, pobjPath, i, n, normalize_x, normalize_y, w, b);
    pResult.total_mse = ADAS_CalculateTotalMSE(pobjAlm, pobjPath, i, n, stored_x, normalize_y, w, b);
    pResult.variance = ADAS_CalculateVariance(pobjAlm, pobjPath, i, n, normalize_x, normalize_y);

    //if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
    //    ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)) || // 4s
    //    (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
    if (0)
    {
        /* 异常值检查和处理 */
        if ((isnan(pResult.r_squared) && (pResult.r_squared > 1.0f)) && isnan(pResult.total_mse) && isnan(pResult.variance))
        {
            pResult.status = ADAS_EVAL_CALC_ERROR;
            return ADAS_EVAL_CALC_ERROR; 
        }
        /* 归一化MSE和方差，使它们的取值范围为[0,1]，并且越小越好 */ 
        // pResult.normalized_mse = fminf(pResult.mse_var / MSE_MAX_VALUE, 1.0f);
        // pResult.normalized_var = fminf((1.0f - pResult.variance) / VARIANCE_MAX_VALUE, 1.0f);

		mse_threshold = pResult.total_mse * 2.0f;
        variance_threshold = pResult.variance * 1.5;
        if ((pResult.r_squared > 0.70f) ||                                              // R2阈值
            (((pResult.mse_var < mse_threshold) && (pResult.total_mse < 0.15)) ||       // 方差作为阈值和单点mse比较
             ((pResult.mse_var < 0.05) && (pResult.total_mse < 0.05))) ||
            (((variance_threshold > pResult.mse_var) && (pResult.variance < 0.15f)) ||  // 总残差方差作为阈值和单点mse比较
             ((0.05 > pResult.mse_var) && (pResult.variance < 0.15f)))
        )
        {
            pResult.status = ADAS_EVAL_OK;
            pResult.decision_satus = ADAS_DECISION_POSITIVE;
            /* 根据超过阈值的程度确定置信度 */
            pResult.confidence = CONFIDENCE_MAX;
        }
        else
        {
            pResult.status = ADAS_EVAL_CALC_ERROR;
        }
    }
    else
    {
        /* 异常值检查和处理 */
        if (isnan(pResult.r_squared) || isnan(pResult.total_mse) || isnan(pResult.variance))
        {
            pResult.status = ADAS_EVAL_CALC_ERROR;
            return ADAS_EVAL_CALC_ERROR; 
        }

        /* 归一化MSE和方差，使它们的取值范围为[0,1]，并且越小越好 */
        // pResult.normalized_mse = fminf(pResult.total_mse / MSE_MAX_VALUE, 1.0f);
        // pResult.normalized_var = fminf(pResult.variance / VARIANCE_MAX_VALUE, 1.0f);

        // 补充策略
        if (((pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_M_STRICT_RUNNING_TIME)) ||
            ((pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->runingtime < FCTB_CHECK_RUNING_TIME_CCCSCP_8_9_SCENARIO)) ||
            (pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene == 1U))
        {
            r2_expected_buff = -0.15f; // 和为0.8

            mse_good_buff = 0.075f; // 和
            mse_bad_buff = 0.125f;

            var_good_buff = 0.075f;
            var_bad_buff = 0.125f;
        }
        // 补充策略
        if ((type == ADAS_TYPE_FCTA) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps >= 2.7f)) // 10kph以上
        {
            r2_expected_buff += -0.10f; // 和为0.8

            mse_good_buff += 0.10f; // 和为0.125
            mse_bad_buff += 0.155f;

            var_good_buff += 0.10f;
            var_bad_buff += 0.155f;
        }
        else if ((type == ADAS_TYPE_DOW) && (fabsf(pobjPath[i].headingAnglerealRCWDOW) >= DOW_SLOPE_MIN_ANGLE)) // 10kph以上
        {
            r2_expected_buff += -0.10f; // 和为0.8

            mse_good_buff += 0.10f; // 和为0.125
            mse_bad_buff += 0.155f;

            var_good_buff += 0.10f;
            var_bad_buff += 0.155f;
        }
        // 补充策略
        if ((pobjPath[i].lifeCycle > 130U && type == ADAS_TYPE_FCTB) || 
            (pobjPath[i].lifeCycle > 100U && (type == ADAS_TYPE_FCTA || type == ADAS_TYPE_DOW)))
        {
            r2_expected_buff *= 1.1f; // 

            mse_good_buff *= 1.1f; // 
            mse_bad_buff *= 1.1f;

            var_good_buff *= 1.1f;
            var_bad_buff *= 1.1f;
        }

        /* 1. R²得分归一化 - R²本身已在[0,1]范围，但可以设置期望阈值 */
        float r2_expected = 0.95f + r2_expected_buff; // 期望的良好R²值
        float r2_score = 0.0f;
        if (pResult.r_squared >= r2_expected)
        {
            // 高于期望阈值时，线性映射到[0.9, 1.0]区间
            r2_score = 0.9f + 0.1f * (pResult.r_squared - r2_expected) / (1.0f - r2_expected);

            if (r2_score > 0.95)
            {
                pResult.lr_eval_best_mask += 1; // pResult.lr_eval_best_mask | 0x01;
            }
        }
        else
        {
            // 低于期望阈值时，线性映射到[0, 0.9]区间
            r2_score = pResult.r_squared / r2_expected * 0.9f;
        }

        /* 2. MSE得分归一化 - 越低越好 */
        float mse_good = 0.025f + mse_good_buff; // 良好的MSE值
        float mse_bad = 0.075f + mse_bad_buff;   // 较差的MSE值
        float mse_score = 0.0f;
        if (pResult.total_mse <= mse_good)
        {
            // MSE低于良好阈值，得分映射到[0.9, 1.0]
            mse_score = 1.0f - 0.1f * (pResult.total_mse / mse_good);
            pResult.lr_eval_best_mask += 1; // pResult.lr_eval_best_mask | 0x02;
        }
        else if (pResult.total_mse >= mse_bad)
        {
            // MSE高于较差阈值，得分为0
            mse_score = 0.0f;
        }
        else
        {
            // MSE在中间范围，线性映射到[0, 0.9]
            mse_score = 0.9f * (mse_bad - pResult.total_mse) / (mse_bad - mse_good);
        }

        /* 3. 方差得分归一化 - 较低的方差更好 */
        //if (stored_y[n-1] < FLOAT_EPS) // 针对小角度，补偿一下方差，
        //{
        //    pResult.variance = 0.05;
        //}
        float var_good = 0.025f + var_good_buff; // 良好的方差值
        float var_bad = 0.075f + var_bad_buff;   // 较差的方差值
        float var_score = 0.0f;
        if (pResult.variance <= var_good)
        {
            // 方差低于良好阈值，得分映射到[0.9, 1.0]
            var_score = 1.0f - 0.1f * (pResult.variance / var_good);
            pResult.lr_eval_best_mask += 1; // pResult.lr_eval_best_mask | 0x04;
        }
        else if (pResult.variance >= var_bad)
        {
            // 方差高于较差阈值，得分为0
            var_score = 0.0f;
        }
        else
        {
            // 方差在中间范围，线性映射到[0, 0.9]
            var_score = 0.9f * (var_bad - pResult.variance) / (var_bad - var_good);
        }

        /* 存储各项归一化得分，便于调试和诊断 */
        pResult.r2_score = r2_score;
        pResult.mse_score = mse_score;
        pResult.var_score = var_score;

        /* 计算加权总分 */
        pResult.weighted_score = g_lr_current_config.r2_weight * r2_score +
                                  g_lr_current_config.mse_weight * mse_score +
                                  g_lr_current_config.var_weight * var_score;

        /* 确保加权总分在[0,1]范围内 */
        float weight_sum = g_lr_current_config.r2_weight +
                           g_lr_current_config.mse_weight +
                           g_lr_current_config.var_weight;

        if (weight_sum > 0.0f)
        {
            pResult.weighted_score /= weight_sum;
        }
        else
        {
            pResult.weighted_score = 0.0f;
        }

        /* 补充策略 */
        // DOW功能：有1个最优，可以认为是能通过的
        if ((type == ADAS_TYPE_DOW) && (pResult.lr_eval_best_mask >= 1))
        {
            pResult.lr_eval_best_mask = 1;
        }
        // FCTA功能：有1个最优，可以认为是能通过的
        else if ((type == ADAS_TYPE_FCTA) && (pResult.lr_eval_best_mask >= 1))
        {
            pResult.lr_eval_best_mask = 1;
        }
        // FCTB功能：有两个最优，可以认为是能通过的
        else if ((type == ADAS_TYPE_FCTB) && (pResult.lr_eval_best_mask >= 2))
        {
            pResult.lr_eval_best_mask = 1;
        }
        else
        {
            pResult.lr_eval_best_mask = 0;
        }

#if 0
        /* 应用Sigmoid函数获得控制输出 */
        sigmoid_input = (pResult.weighted_score - g_lr_current_config.sigmoid_offset) / g_lr_current_config.sigmoid_temp;
        pResult.control_output = ADAS_fastSigmoid(sigmoid_input, g_lr_current_config.sigmoid_temp);

        /* 根据场景动态调整阈值 */
        pResult.threshold = g_lr_current_config.decision_threshold;

        if (pResult.control_output >= pResult.threshold)
#endif

        /**
         * @brief 阈值调整
         */  
        /* 根据场景动态调整阈值 */
        pResult.threshold = g_lr_current_config.decision_threshold;
        if (type == ADAS_TYPE_FCTA)
        {
            pResult.threshold -= 0.10; // 宽松场景到0.70；
        }
        else if (type == ADAS_TYPE_DOW)
        {
            pResult.threshold -= 0.10; // 严格场景到0.83；
        }

        /**
         * @brief 阈值判断
         * 
         */
        if ((pResult.weighted_score >= pResult.threshold) || (pResult.lr_eval_best_mask))
        {
            pResult.status = ADAS_EVAL_OK;
            pResult.decision_satus = ADAS_DECISION_POSITIVE;

            /* 根据超过阈值的程度确定置信度 */
            pResult.confidence = fminf((pResult.control_output - pResult.threshold) /
                                           (1.0f - pResult.threshold) * CONFIDENCE_MAX,
                                       CONFIDENCE_MAX);
        }
        else
        {
            pResult.status = ADAS_EVAL_CALC_ERROR;
            pResult.decision_satus = ADAS_DECISION_NEGATIVE;

            /* 根据低于阈值的程度确定置信度 */
            pResult.confidence = fminf((pResult.threshold - pResult.control_output) /
                                           pResult.threshold * CONFIDENCE_MAX,
                                       CONFIDENCE_MAX);
        }
    }
    
    return pResult.status;
}


/**
 * @brief 线性回归调用流程
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 */ 
int8_t ADAS_FCTAB_LinearRegression_Flow(const ALARM_OBJECT_T *pobjAlm,
                                        OBJ_NODE_STRUCT *pobjPath,
                                        const uint8_t i,
                                        const VDY_Info_t *pVDY,
                                        const OBJ_ADAS_TYPE_ENUM type)
{
    int8_t fctabCollisionFlag = 0;
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (i >= ADAS_TRK_OBJNUM) || (pVDY == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return 0;
    }

    // 定义 w 和 b，用于存储线性回归模型的斜率和截距
    float w = 0, b = 0; 
    float x_new = 0, y_new = 0; 
    float mse_1 = 0, mse_2 = 0; // r_squared = 0;
    // float mse_2_buff = 1.0f;
    uint8_t linearRegFitSampleNum = ADAS_HISTORY_LESSER_NUM; // 线性拟合使用的数据量
    float headingAngleMinBuff = 0.0f, headingAngleMaxBuff = 0.0f;
    ADAS_LR_EvalStatus lr_evalStatus = ADAS_EVAL_LR_ERR;

    /**
     * @brief 根据不同情况使用不同的拟合数据量
     * 
     */
    if (type == ADAS_TYPE_FCTA)
    {
        // FCTA用5帧
        linearRegFitSampleNum = ADAS_HISTORY_HEADINGANGLE_NUM;
    }
    else if ((type == ADAS_TYPE_FCTB) && (pobjPath[i].vx >= 5.3f))
    {
        // FCTB且目标高速用10帧
        linearRegFitSampleNum = ADAS_HISTORY_LESSER_NUM + 3;
    }

    /**
     * @brief 
     * 
     */
    if (pobjPath[i].x > -0.8f)
    {
        // 数据标准化, 对stored_dow_x、stored_dow_y赋值
        ADAS_DataNormalizeToMeans(pobjAlm, pobjPath, i, type, ADAS_HISTORY_NUM, stored_x, stored_y);
        // 调用线性回归函数，计算斜率和截距
        ADAS_LinearRegression_FirstOrder(pobjAlm, pobjPath, i, type, linearRegFitSampleNum,
                                         stored_x, stored_y, &w, &b);
        pobjPath[i].lrFitting.fctab_w = w;
        pobjPath[i].lrFitting.fctab_b = b;
    }
    else
    {
        w = pobjPath[i].lrFitting.fctab_w;
        b = pobjPath[i].lrFitting.fctab_b;
    }

#if (FCTAB_FOR_ACCEPTANCE_INSPECTION == 1)
    if ((pobjPath[i].lastAlarmType & ALARM_ACTIVE_FCTA) || (0 != pobjPath[i].overAlarmStartCnt))
    {
        mse_2_buff *= 0.9;
    }
#endif
    

    lr_evalStatus = ADAS_LR_EvaluateModel(pobjAlm, pobjPath, i, pVDY, type, linearRegFitSampleNum,
                                          stored_x, stored_y, w, b);

    if (ADAS_EVAL_OK == lr_evalStatus)
    {
        pobjPath[i].lrFitting.fctab_FitAvailable = 1; // 线性回归拟合有效
        if (pobjPath[i].lrFitting.fctab_FitAvailableCnt < 200)
        {
            pobjPath[i].lrFitting.fctab_FitAvailableCnt++;
        }
    }
    else
    {
        if (pobjPath[i].lrFitting.fctab_FitAvailableCnt > 8) // FCTAB拟合会出现前面拟合正常，后面抖动,这里设个临时措施
        {
            pobjPath[i].lrFitting.fctab_FitAvailableCnt = 8;
        }
        if (pobjPath[i].lrFitting.fctab_FitAvailableCnt > 0)
        {
            pobjPath[i].lrFitting.fctab_FitAvailableCnt--;
        }
        if (pobjPath[i].lrFitting.fctab_FitAvailableCnt <= 1)
        {
            pobjPath[i].lrFitting.fctab_FitAvailable = 0;
        }
    }

    //
    if (pobjPath[i].lrFitting.fctab_FitAvailable == 1)
    {
        /**
         * @brief  针对FCTA补偿
         */
        if ((type == ADAS_TYPE_FCTA) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps < 2.7f)) // 10kph以下
        {
            headingAngleMinBuff -= 10.0f;
            headingAngleMaxBuff += 15.0f;
        }
        else if ((type == ADAS_TYPE_FCTA) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps >= 2.7f)) // 10kph以下
        {
            headingAngleMinBuff -= 15.0f;
            headingAngleMaxBuff += 15.0f;
        }
        else if (type == ADAS_TYPE_FCTB) // FCTB小角度定义为55~75°
        {
            headingAngleMinBuff += FCTB_ACTIVE_MIN_IA_BUF;
            headingAngleMaxBuff += FCTB_ACTIVE_MAX_IA_BUF * 3;
        }

        // 使用新点 (x_new, y_new) 验证均方误差, 调用均方误差计算函数
        x_new = -2.0f, y_new = 3.0f; // y最大为3
        mse_2 = ADAS_FCTAB_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_x, stored_y, x_new, y_new, w, b);
        x_new = 0, y_new = 0.0f; // y最小为0
        if ((pobjPath[i].avgheadingAngle >= (FCTB_ACTIVE_MIN_IA + headingAngleMinBuff)) && 
            ((pobjPath[i].avgheadingAngle <= FCTB_ACTIVE_MIN_IA + headingAngleMaxBuff)))
        {
            // 小角度只判单侧即可
            x_new = 2.0f, y_new = 3.0f;
            mse_1 = ADAS_FCTAB_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_x, stored_y, x_new, y_new, w, b);
            mse_2 = 1;
        }
        else
        {
            mse_1 = ADAS_FCTAB_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_x, stored_y, x_new, y_new, w, b);
        }
        if ((mse_1 == 1) && (mse_2 == 1))
        {
            fctabCollisionFlag = 1;
        }
    }

    return fctabCollisionFlag;
}

/**
 * @brief DOW 线性回归调用流程
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 */
#define DOW_LINEAR_REGRESSION_EVALUATE_MODEL 3
int8_t ADAS_DOW_LinearRegression_Flow(const ALARM_OBJECT_T *pobjAlm,
                                      OBJ_NODE_STRUCT *pobjPath,
                                      const uint8_t i,
                                      const VDY_Info_t *pVDY,
                                      const OBJ_ADAS_TYPE_ENUM type,
                                      const st_Dow_State dowState)
{
    int8_t dowLRFlag = 0;
    if ((pobjAlm == NULL) || (pobjPath == NULL) || (i >= ADAS_TRK_OBJNUM) || (pVDY == NULL) || (type >= ADAS_TYPE_MAX))
    {
        return dowLRFlag;
    }

    // uint8_t l_r = (((gADASRadarId % 2U) == 0) ? BSD_RADAR_LEFT : BSD_RADAR_RIGHT);

    if ((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) == 0U) // 过滤静态点
    {
        return dowLRFlag;
    }
    if ((pobjPath[i].headingAnglerealRCWDOW < (-30.0f - 10.0f)) || (pobjPath[i].y < DOW_DOOR_AREA_Y_MIN))
    {
        return dowLRFlag;
    }
    // if(fabsf(pobjPath[i].vx) > FLOAT_EPS)

    // 定义 w 和 b，用于存储线性回归模型的斜率和截距
    float w = 0, b = 0, w1 = 0, w2 = 0;
    // float variance = 0;
    float x_new = 0, y_new = 0;
    // float threshold = 0;
    float mse_1 = 0, mse_2 = 0; // r_squared = 0;
    // float mse_2_buff = 1.0f;
    // uint8_t modelType = 0;
    ADAS_LR_EvalStatus lr_dow_evalStatus = ADAS_EVAL_LR_ERR;

    if (pobjPath[i].y > 0.6f)
    {
        // 数据标准化, 对stored_x、stored_y赋值
        ADAS_DataNormalizeToMeans(pobjAlm, pobjPath, i, type, ADAS_HISTORY_NUM, stored_x, stored_y);
        // 调用线性回归函数，计算斜率和截距
        ADAS_LinearRegression_FirstOrder(pobjAlm, pobjPath, i, type, ADAS_HISTORY_LINEAR_REGRESSION_NUM,
                                         stored_x, stored_y, &w, &b);
        pobjPath[i].lrFitting.dow_w = w;
        pobjPath[i].lrFitting.dow_b = b;
    }
    else
    {
        w = pobjPath[i].lrFitting.dow_w;
        b = pobjPath[i].lrFitting.dow_b;
    } 

    // 再次计算出一个斜率. 斜率默认是 0  0 设置为一个无效值
    // 自学习只对左雷达生效
    if ((dowState.dowguessheadingvaild) && ((gADASRadarId == (uint8_t)REAR_LEFT_RADAR_ID) || (gADASRadarId == (uint8_t)FRONT_RIGHT_RADAR_ID)))
    {
        w1 = 1 / tanf(dowState.guessheadingresult * degtorad);
        w2 = 1 / tanf(pobjAlm->guessstaticguardrailangle * degtorad); 
    }

    lr_dow_evalStatus = ADAS_LR_EvaluateModel(pobjAlm, pobjPath, i, pVDY, type, ADAS_HISTORY_HEADINGANGLE_NUM,
                                              stored_x, stored_y, w, b); 

    if (ADAS_EVAL_OK == lr_dow_evalStatus)
    {
        pobjPath[i].lrFitting.dow_FitAvailable = 1; // 线性回归拟合有效
        if (pobjPath[i].lrFitting.dow_FitAvailableCnt < 200)
        {
            pobjPath[i].lrFitting.dow_FitAvailableCnt++;
        }
    }
    else
    {
        if (pobjPath[i].lrFitting.dow_FitAvailableCnt > 6)
        {
            pobjPath[i].lrFitting.dow_FitAvailableCnt = 6;
        }
        if (pobjPath[i].lrFitting.dow_FitAvailableCnt > 0)
        {
            pobjPath[i].lrFitting.dow_FitAvailableCnt--;
        }
        if (pobjPath[i].lrFitting.dow_FitAvailableCnt <= 1)
        {
            pobjPath[i].lrFitting.dow_FitAvailable = 0;
        }
    }

    //
    if (pobjPath[i].lrFitting.dow_FitAvailable == 1)
    {
        // 使用新点 (x_new, y_new) 验证均方误差, 调用均方误差计算函数
        x_new = DOW_WARNING_WIDTH_MAX, y_new = 1.0f;
        if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_DOW))
        {
            x_new += DOW_WARNING_WIDTH_MAX_BUF;
        }
        mse_1 = ADAS_DOW_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_x, stored_y, x_new, y_new, w, w1, w2, b);
        y_new = 0.0f;
        mse_2 = ADAS_DOW_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_x, stored_y, x_new, y_new, w, w1, w2, b);
        //
        if ((mse_1 == 1) || (mse_2 == 1))
        {
            // pobjPath[i].dowpredictedcnt = MIN(++pobjPath[i].dowpredictedcnt, MAX_CHAR);
            dowLRFlag = 1;
        }
    } 

    return dowLRFlag;
}
