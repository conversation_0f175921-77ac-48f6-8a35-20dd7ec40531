/*
 * @Author: m<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-01 08:32:17
 * @LastEditors: Mo Yican
 * @LastEditTime: 2025-03-19 10:46:16
 * @FilePath: \ctmrr120_platform\calterah\common\custom\aln\aln_autoEolFun.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef __ALN_AUTO_EOL_FUN_H__
#define __ALN_AUTO_EOL_FUN_H__

#include <stdint.h>
#include "aln_type.h"
#ifndef PC_DBG_FW
#include "rdp_types.h"
#include "vdy_types.h"
#else
#include "hal/rsp/rsp_types.h"
#include "app/vehicle/vdy/vdy_types.h"
#include "alg/track/rdp_types.h"
#include "other/temp.h"
#endif

#define ALN_CAN_DBG_PRINT   0
#define CALIB_RUN_MAX       5    // 最大自标定次数

typedef struct{
    float fixAngleOffset;   //传入安装角度，希望的安装角度是多少
}CT_AUTO_PARA_t;

typedef struct
{
    float previous_fix_angle; //进入标定前的安装角度，即当前正在使用的安装角度
    float new_fix_angle;      //自标定成功后计算出的安装角度
    float new_auto_angle;     //自标定运行完成计算出的安装角度，这个角度和自标定成功与否无关，在第一次没有运行结果之前，数据是默认值，不正确
    uint8_t run_status;       //标定的过程状态
    uint16_t obj_cnt;         //目标总数
    uint16_t run_step;         //运行过的位置
    uint8_t run_per;          //运行百分比
    uint8_t run_counter;      //运行计数,周期数量
    uint8_t routine_status;   //例程状态
    uint8_t routine_result;   //例程结果
    uint8_t is_save_to_nvm;   //是否已经保存到nvm，需要存入flash的前提是标定成功
    uint32_t service_count;   //进行了服务多少次
    uint32_t start_time;      //每次服务启动的时间
    float hdev_angle;         //水平角度偏差角
    uint8_t one_succeed;      // 运行成功记录，CALC_FAILED_ANGLE_MIN_OUT 等标定结果
} CT_AUTO_EOL_INFO_t;

struct __CT_AutoEOL_ResultInfo_t
{
    float auto_angle;        // 自标定角度
    uint8_t one_succeed;     // 运行成功记录，CALC_FAILED_ANGLE_MIN_OUT 等标定结果
    uint8_t last_aln_result; // 最后的运行结果状态，是个枚举值
    uint32_t err_cnt;        // 单次上电周期标定失败的次数
} ;
typedef struct __CT_AutoEOL_ResultInfo_t CT_AutoEOL_ResultInfo_t;

typedef enum
{
    ALN_AUTO_EOL_START = 0U,
    ALN_AUTO_EOL_RUNNING = 1,
    ALN_AUTO_EOL_ABORTING = 2,
    ALN_AUTO_EOL_END = 3
} ALN_AUTO_EOL_STATE_T;

typedef enum
{
    ALN_AUTO_INACTIVE = 0U,
    ALN_AUTO_RUNNING = 1,
    ALN_AUTO_ABORTING = 2,
    ALN_AUTO_END = 3,
    ALN_AUTO_MAX_MIN = 4,
    ALN_AUTO_TIMEOUT = 5
} ALN_AUTO_RESULT_T;

typedef enum
{
    ALN_AUTO_EOL_OK = 0U,
    ALN_AUTO_EOL_ERR = 1
} ALN_AUTO_STATUS_T;

typedef struct
{
    ALN_AUTO_EOL_STATE_T state; 
    ALN_AUTO_STATUS_T last_aln_status;
    ALN_AUTO_RESULT_T last_aln_result;
    uint32_t last_aln_tick;
    uint32_t err_cnt; 
} ALN_AUTO_EOL_T;

/**
 * @brief 存储已经自标定流程执行完的数据
 *  1、自标定流程这个执行结束，存储记录结果；
 *  2、如果在新的自标定流程还没有执行结束，此时下电，使用的是上次自标定流程的结果
 */
typedef struct {
    uint32_t storageRunCount;                // 当前已执行的自标定完整流程的次数
    float calibAvgAngles;                   // 存储平均角度
    float storageAngleMax;                  // 标定流程中最大角度，每次标定结束进行统计，在进行到当前次数时的最大最小值
    float storageAngleMin;                  // 标定流程中最大角度，
    int16_t storageTotalTakeTime;           // 总标定用时 
    int8_t storageCalibrationDone;          // 当前上电周期内是否已经完成了自标定（达到5次后置true）
    int8_t storageCaliStatus;               // 若5次自标定平均角度不在允许范围内，则置为true表示故障
} ADAS_SelfCaliStorage;
typedef struct {
    uint32_t travlledDistance;              // 里程读取 Km 精度1km/h，如果当前上电周期内已经执行完5次[单次自标定]，即一整个自标定流程已经执行完毕，则需要经过3km再进行自标定完成流程
    float calibAngles[CALIB_RUN_MAX + 1];   // 存储连续5次自标定获得的标定角度, [CALIB_RUN_MAX]存储平均角度
    float calibSuccesAngles;                // 有在3度内的自标定成功就存储此角度，有一次即可，不需要多次
    float singleAngleMax;                   // 标定流程中最大角度，每次标定结束进行统计，在进行到当前次数时的最大最小值
    float singleAngleMin;                   // 标定流程中最大角度，
    ADAS_SelfCaliStorage storageCaliInfo;   // 存储已经自标定流程执行完的数据
    int16_t singletakeTime;                 // 单次标定用时
    int16_t totalTakeTime;                  // 总标定用时
    int8_t autoEolSuccesFalg;               // 有在3度内的自标定成功就存储此角度的标志
    uint8_t runAllCount;                    // 当前已执行的自标定小次数（取值范围0~CALIB_RUN_MAX）
    int8_t calibrationDone;                 // 当前上电周期内是否已经完成了自标定（达到5次后置true）
    int8_t caliStatus;                      // 若5次自标定平均角度不在允许范围内，则置为true表示故障 
} ADAS_SelfCalibrationCtrl;


CT_AUTO_EOL_INFO_t* ALN_getAutoEolInfo(void);
CT_AutoEOL_ResultInfo_t ALN_getAutoEolRunResult(void);

void ADAS_resetAutoEolCtrlInitStatus(void);
void ADAS_initAutoEolCtrlStatus(void);

void ALN_clearAutoEolSaveFlag();

void StartALN_AutoEolFun(void* param, void* pParam);

extern uint8_t ALN_AutoEolFun_Flag;
uint8_t getALN_AutoEolFun_Flag();

/**
 * @brief 当前上电周期自标定大的次数
 *
 * @param return 自标定大的次数
 */
uint8_t ADAS_getAutoEolCtrlCnt(void);
/**
 * @brief 标定状态
 *
 * @param return 0, not error，存角度;
 *               1, 有问题，上报DTC错误，但是仍然存储结果;
 *               2, error，上报DTC错误，自标定异常，不存储结果，
 */
int8_t ADAS_getAutoEolCtrlStatus(void);

/**
 * @brief 标定角度（度）
 *
 * @param return autoEOLCtrl.caliStatus：0, not error，存角度; 1, 有问题，但是仍然存储结果; 2, error，不存储结果
 *               为0或1时存储结果
 */
float ADAS_getAutoEolCtrlAngle(void);

/**
 * @brief 标定状态，是否上报DTC错误
 *
 * @param return 0, not error，存角度;
 *               1, 有问题，上报DTC错误，但是仍然存储结果;
 *               2, error，上报DTC错误，自标定异常，不存储结果，
 */
int8_t ADAS_getAutoEolCtrl_IsErr(void);

float ADAS_getAutoEolCtrl_SimpleAngle(void);
float ADAS_getAutoEolCtrl_AppointSimpleAngle(uint8_t count);
float ADAS_getAutoEolCtrl_MaxAngle(void);
float ADAS_getAutoEolCtrl_MinAngle(void);
uint16_t ADAS_getAutoEolCtrl_simpleTakeTime(void);
uint16_t ADAS_getAutoEolCtrl_totalTakeTime(void);
float ADAS_getAutoEolCtrl_SimpleCount(void);
// void ADAS_clearAutoEolCtrlStatus(void);

void ADAS_clearAutoEolCtrlStatus_PowerOnOff(void);

/**
 * @brief 自标定主函数
 */
void ALN_AutoEolAngleCalcMainFun(const cdi_pkg_t *cdi_pkg, const VDY_Info_t *pVDY, const VDY_DynamicEstimate_t *freezedVehDyncDataAddr, float time);

/**
 * @brief 获取自标定状态信息
 *
 * @return 自标定状态信息
 */
ALN_AUTO_EOL_T *ALN_GetAutoInfo(void);

#endif
