﻿#include "ffmpegreader.h"

#include "ffmpeginitializer.h"

#include <iostream>

#include <QDateTime>

FFmpegReader::FFmpegReader()
{

}

FFmpegReader::FFmpegReader(std::string url, bool isFile)
    : mURL(url), mIsFile(isFile)
{
    FFmpegInitializer::initFFmpeg();
}

FFmpegReader::~FFmpegReader()
{
    FFmpegInitializer::releaseFFmpeg();
}

bool FFmpegReader::open()
{
    if (mURL.empty()) {
            return false;
        }

        std::cout << __FUNCTION__ << " " << __LINE__ << " " << mURL << std::endl;

        if (!mIsFile) {
            // 2. 参数设置，释放：av_dict_free(AVDictionary **m);
            AVDictionary* pOptions = 0;
            // 设置解码器（Linux下打开本地摄像头默认为rawvideo解码器，输入图像为YUYV420，不方便显示，有两种解决办法，1：使用sws_scale把YUYV422转为YUVJ422P；2：指定mjpeg解码器输出YUVJ422P图像）
            av_dict_set(&pOptions, "input_format", "mjpeg", AV_OPT_SEARCH_CHILDREN);
            av_dict_set(&pOptions, "framerate", "30", 0);                           // 设置帧率
            av_dict_set(&pOptions, "video_size", "1280x720", 0);                    // 设置视频分辨率（如果该分辨率摄像头不支持则会报错）
            //    av_dict_set(&pOptions, "pixel_format", "yuvj422p", 0);                      // 设置像素格式

            // 3. 查找输入格式， 不需要释放，格式上下文释放时，会一起释放
            const AVInputFormat *inputFormat = av_find_input_format("dshow"); // dshow方式打开
            if (!inputFormat) {
                std::cout << __FUNCTION__ << " " << __LINE__ << " " << "av_find_input_format(\"dshow\") error!" << std::endl;
                close();
                return false;
            }

            // 4. 格式上下文 分配内存空间，释放：avformat_close_input(AVFormatContext **s);
            if (!mpFormatContextInput) {
                mpFormatContextInput = avformat_alloc_context();
            }

            // 5. 以输入格式打开，填充格式上下文
            if(avformat_open_input(&mpFormatContextInput, mURL.c_str(), inputFormat, &pOptions) != 0)
            {
                close();
                return false;
            }

            if (pOptions) {
                av_dict_free(&pOptions);
                pOptions = 0;
            }
        } else {
            if (avformat_open_input(&mpFormatContextInput, mURL.c_str(), NULL, NULL) != 0)
                {
                    std::cout << __FUNCTION__ << " " << __LINE__ << " " << "open input error" << std::endl;
                    close();
                    return false;
                }
        }

        // 6. 根据格式上下文，查找输入流信息
        if(avformat_find_stream_info(mpFormatContextInput, NULL)<0)
        {
            std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avformat_find_stream_info" << std::endl;
            close();
            return false;
        }

        // 7. 查找视频流, 不需要释放
        mpStreamsInput = 0;
        for(unsigned int i=0; i < mpFormatContextInput->nb_streams; i++)
        {
            if(mpFormatContextInput->streams[i]->codecpar->codec_type==AVMEDIA_TYPE_VIDEO)
            {
                mpStreamsInput = mpFormatContextInput->streams[i];
                break;
            }
        }
        if(!mpStreamsInput)
        {
            std::cout << __FUNCTION__ << " " << __LINE__ << " " << "mpStreamsInput" << std::endl;
            close();
            return false;
        }

        // 8. 根据视频流解码器ID，获取解码器, 不需要释放，解码器上下文释放时，会一起释放
        const AVCodec *pCodec = avcodec_find_decoder(mpStreamsInput->codecpar->codec_id);
        if(pCodec == NULL)
        {
            std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avcodec_find_decoder" << std::endl;
            close();
            return false;
        }
        // 9. 解码器上下文，分配内存空间
        if (!mpCodecContextInput) {
            mpCodecContextInput = avcodec_alloc_context3(pCodec);
        }
        if(!mpCodecContextInput)
        {
            std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avcodec_alloc_context3" << std::endl;
            close();
            return false;
        }

        // 10. 根据视频流参数，设置解码器上下文
        avcodec_parameters_to_context(mpCodecContextInput, mpStreamsInput->codecpar);

        // 11. 根据解码器上下文，打开解码器
        if(avcodec_open2(mpCodecContextInput, pCodec, NULL)<0)
        {
            std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avcodec_open2" << std::endl;
            close();
            return false;
        }

        // 12. 数据帧，释放av_frame_free();
        if (!mpFrame) {
            mpFrame = av_frame_alloc();
        }

        // 16. 创建缓存包，分配内存空间。释放：av_packet_free(AVPacket **pkt);
        if (!mpPacketInput) {
            mpPacketInput = av_packet_alloc();
        }

        mOpened = true;

        return true;
}

bool FFmpegReader::open(std::string url, bool isFile)
{
    mURL = url;
    mIsFile = isFile;
    return open();
}

bool FFmpegReader::close()
{
    // 释放frame、packet，地址会被置为0
    av_frame_free(&mpFrame);
    av_packet_free(&mpPacketInput);

    // 关闭解码器上下文，mpCodecContext需要手动释放
    avcodec_close(mpCodecContextInput);
    // 释放解码器上下文, mpCodecContext会被置为0
    avcodec_free_context(&mpCodecContextInput);

    // 关闭输入，释放封装上下文，mpFormatContext会被置0，不需要手动释放
    //（1）调用AVInputFormat的read_close()方法关闭输入流
    //（2）调用avformat_free_context()释放AVFormatContext
    //（3）调用avio_close()关闭并且释放AVIOContext
    avformat_close_input(&mpFormatContextInput);

    mOpened = false;

    return true;
}

static qreal rationalToDouble(AVRational* rational)
{
    qreal frameRate = (rational->den == 0) ? 0 : (qreal(rational->num) / rational->den);
    return frameRate;
}

AVFrame *FFmpegReader::read()
{
//    std::cout << __FUNCTION__ << " " << __LINE__ << " " << std::endl;
    if (!mOpened) {
        return 0;
    }
    int ret = 0;
    if(av_read_frame(mpFormatContextInput, mpPacketInput) < 0){
        return 0;
//        avcodec_send_packet(mpCodecContextInput, mpPacketInput);   // 读取完成后向解码器中传如空AVPacket，否则无法读取出最后几帧
    } else {
        if(mpPacketInput->stream_index == mpStreamsInput->index){
            // 计算当前帧时间（毫秒）
            if (!mIsFile) {
                mpPacketInput->pts = qRound64(mpPacketInput->pts * (1000 * rationalToDouble(&(mpStreamsInput->time_base))));
                mpPacketInput->dts = qRound64(mpPacketInput->dts * (1000 * rationalToDouble(&(mpStreamsInput->time_base))));
            } else {
//                std::cout << __FUNCTION__ << " " << __LINE__ << " " << mpPacketInput->pts << " " << mpPacketInput->dts << std::endl;
            }
            ret = avcodec_send_packet(mpCodecContextInput, mpPacketInput);
            if(ret < 0)
            {
                std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avcodec_send_packet error!" << std::endl;
                mErrorString = "Decode Error";
            }
        }
    }

    av_packet_unref(mpPacketInput);
    av_frame_unref(mpFrame);
    if (avcodec_receive_frame(mpCodecContextInput, mpFrame) < 0)
    {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avcodec_receive_frame error!" << std::endl;
        av_frame_unref(mpFrame);
        return 0;
    }
    if (mIsFile) {
    AVDictionaryEntry *t = av_dict_get(mpFrame->metadata, "system_timestamp", NULL, AV_DICT_MATCH_CASE);
    if (t) {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << t->key << " " << t->value << " "
                  << QDateTime::fromMSecsSinceEpoch(QString(t->value).toULongLong()).toString("yyyy-MM-dd hh:mm:ss.zzz").toStdString() << std::endl;
    }
    }

//    std::cout << __FUNCTION__ << " " << __LINE__ << " " << std::endl;
    return  mpFrame;

    return 0;
}
