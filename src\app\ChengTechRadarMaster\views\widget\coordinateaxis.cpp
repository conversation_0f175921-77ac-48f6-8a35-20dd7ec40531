﻿#include "coordinateaxis.h"

#include <QPainter>
#include <QDebug>

namespace Views {
namespace ObjectView {

CoordinateAxis::CoordinateAxis(ObjectAxisArea objectAxisArea, QWidget *parent)
    : QWidget(parent),
      mObjectAxisArea(objectAxisArea),
      mBackgroundColor(220, 220, 220, 10)
{
    setWindowFlags(Qt::FramelessWindowHint);  //屏蔽窗口，只看widget部分的效果
    setWindowOpacity(0.7);
    QPalette pal = palette();
    pal.setColor(QPalette::Background, QColor(85, 170, 255));
    setPalette(pal);
}

CoordinateAxis::~CoordinateAxis()
{

}

/**
* <AUTHOR>
* @date 2022-05-29
* @param
* @return void
* @note
* 设置视距范围
* @remarks
*/
void CoordinateAxis::setRange(double minRange, double maxRange, double interval, double offset)
{
    if (maxRange <= minRange)
    {
        return;
    }

    mMinRange = minRange;
    mMaxRange = maxRange;
    mInterval = interval;
    mOffset = offset;

    mPixelPerMetre = (mAxisLength  - mAxisWidth) / (mMaxRange - mMinRange);    // 像素比列(pix/m),一个米需要多少个像素点
    mIntervalPixel = mInterval * mPixelPerMetre;                // 一个间隔需要多少个像素点
    mOffsetPixel = mOffset * mPixelPerMetre;
}

void CoordinateAxis::setUnit(const QString &unit, bool show)
{
    mUnit = unit;
    mUnitShow = show;
}

void CoordinateAxis::setValue(double value)
{
    mValue = value;
    update();
}

void CoordinateAxis::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);

    painter.fillRect(this->rect(), mBackgroundColor);

    double drawLeft = mAxisWidth;
    bool bRight = false;
    switch (mObjectAxisArea) {
    case TopObjectAxisArea:
        break;
    case RightObjectAxisArea:
        bRight = true;
    case LeftObjectAxisArea:
        painter.translate(0, mAxisLength - mOffsetPixel);
        painter.rotate(-90);
        drawLeft = 0;
        break;
    }

    painter.setPen(QPen(mScaleColor, 1));
    if (!bRight) {
        painter.drawLine(0,  mAxisWidth, mAxisLength, mAxisWidth);
    }

    double value = mMinRange;
    while (value <= mMaxRange) {
        value ? painter.setPen(QPen(mScaleColor, 1)) : painter.setPen(QPen(Qt::blue, 1));
        painter.drawLine(QPointF(drawLeft, 0), QPointF(drawLeft, mAxisWidth));
        if (value == 0) painter.setPen(QPen(mScaleColor, 1));
        if (value < mMaxRange && mIntervalPixel > 20) {
            for (int i = 1; i < 10; ++i) {
                painter.drawLine(QPointF(drawLeft + mIntervalPixel / 10 * i,
                                         (i == 5 ? mAxisWidth / 2 : mAxisWidth / 4  * 3) + 2),
                                 QPointF(drawLeft + mIntervalPixel / 10 * i,
                                         mAxisWidth));
            }
        } else if (value < mMaxRange && mIntervalPixel > 3) {
            painter.drawLine(QPointF(drawLeft + mIntervalPixel / 2, mAxisWidth / 2),
                             QPointF(drawLeft + mIntervalPixel / 2, mAxisWidth));
        }

        QString scaleText = QString::number(value < 0 ? -value : value) + (mUnitShow ? mUnit : "");
        painter.drawText(QPointF(value < 0 ? (drawLeft + 2) : (drawLeft - 2 - fontMetrics().horizontalAdvance(scaleText, scaleText.length())),
                                     mAxisWidth - mAxisWidth / 3), scaleText);

        value += mInterval;
        drawLeft += mIntervalPixel;
    }

    painter.setPen(QPen(Qt::red, 1));
    painter.drawLine(mValue - mOffsetPixel, 0, mValue - mOffsetPixel, mAxisWidth);
}

void CoordinateAxis::resizeEvent(QResizeEvent *event)
{
    switch(mObjectAxisArea) {
    case LeftObjectAxisArea:
    case RightObjectAxisArea:
        mAxisLength = this->height();
        break;
    case TopObjectAxisArea:
        mAxisLength = this->width();
        break;
    }

    mAxisWidth = fontMetrics().height() / 2 * 3;

    setRange(mMinRange, mMaxRange, mInterval);

    QWidget::resizeEvent(event);
}

} // namespace ObjectView
} // namespace Views
