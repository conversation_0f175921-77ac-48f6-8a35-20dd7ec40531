﻿#ifndef TRUESYSTEMVIEW_H
#define TRUESYSTEMVIEW_H

#include "views_global.h"

#include <QWidget>

#include "analysis/truesystemdata.h"

namespace Ui {
class TrueSystemView;
}

namespace Analysis {
class AnalysisManager;
}

class trueTargetsMonitor;

namespace Views {
namespace TrueSystemView {

class VIEWS_EXPORT TrueSystemView : public QWidget
{
    Q_OBJECT

public:
    explicit TrueSystemView(QWidget *parent = nullptr);
    ~TrueSystemView();
    void setAnalysisManager( Analysis::AnalysisManager* analysisMgr );

public slots:
    void slotTrueObjInfo(stTrueObjInfo * trueObjInfo);

private slots:
    void on_pushButtonCompare_clicked();

    void on_pushButtonSet_clicked();

private:
    Ui::TrueSystemView *ui;
    trueTargetsMonitor* mMonitor{NULL};
    Analysis::AnalysisManager* mAnalysisManager{NULL};
};

} // namespace TrueSystemView
} // namespace Views


#endif // TRUESYSTEMVIEW_H
