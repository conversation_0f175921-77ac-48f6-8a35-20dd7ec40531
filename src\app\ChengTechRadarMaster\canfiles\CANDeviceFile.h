﻿#pragma once

#include <string>
#include <vector>

#include "devicefiles/devicefileblf.h"

namespace Devices {
	namespace Can {
        class CANDeviceFile
		{
		public:
			CANDeviceFile();

            virtual void callback(const CanFrame::Ptr pFrame) = 0;

            const std::string &errorString() const { return mErrorString; }

            void setValidID(ValidID validID);

		protected:
            bool openDevice();
            bool closeDevice();
            int receiveFrame(int channelIdx);
            int sendFrame(CanFrame *pFrame, int len);

            std::vector<std::string> mCANDeviceFiles;

		private:
			bool openFile(std::string &filename);
			bool closeFile();

			std::vector<std::string> mFilenames;
			uint16_t mCurrentFileIndex{ 0 };
			IDeviceFile::FileType mCurFileType;
			std::vector<IDeviceFile::FileTypeInfo>  mFileTypeList;
			IDeviceFile *mDeviceFiles[IDeviceFile::UNKNOWN];

            std::string mErrorString;
		};
	}
}

