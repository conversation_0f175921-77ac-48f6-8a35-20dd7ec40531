﻿#ifndef NETWORKTCPSERVER_H
#define NETWORKTCPSERVER_H

#include <QObject>
#include <QMutex>

class QTcpServer;
class QTcpSocket;

class NetworkTcpServer : public QObject
{
    Q_OBJECT
public:
    explicit NetworkTcpServer(QObject *parent = nullptr);

    bool isOpened() const { return mOpened; }

signals:
    void tcpServerOpened(bool opened, const QString &message = "");
    void tcpServerClosed();

    void tcpClientConnected(const QString &IP, quint16 port);
    void tcpClientDisonnected();

    void write(const QByteArray &data);
    void read(const QByteArray &data);

public slots:
    void openTCPServer(const QString &IP, quint16 port, bool analyIP = false);
    void closeTCPServer();

private slots:
    void connectNewTCPClient();
    void disconnectedTCPClient();
    void readTCPClientData();
    void writeData(const QByteArray &data);

private:
    QMutex mReadMutex;
    QTcpServer *mTcpServer{0};
    QTcpSocket *mTcpClient{0};

    bool mOpened{false};
    bool mClientConnected{false};

};

#endif // NETWORKTCPSERVER_H
