#include "autorechargerawandtarget.h"
#include "devices/FrameFileDevices/framefileparse_binary.h"
#include "devices/ideviceworker.h"
#include "analysis/analysisworker.h"
#include "devices/devicemanager.h"

#include <QEventLoop>
#include <QTimer>

#define TIME_OUT_MS (1000)
#define DISCONNECT_TIME_MS (1000*10) //10秒

AutoRechargeRawAndTarget::AutoRechargeRawAndTarget(QObject *parent) : QObject(parent)
{

}

AutoRechargeRawAndTarget::~AutoRechargeRawAndTarget()
{
    if( mBinaryParse ){
        delete mBinaryParse;
        mBinaryParse = NULL;
    }
}

void AutoRechargeRawAndTarget::setRechargeFiles(const QString &file)
{
    mFileName = file;
}

void AutoRechargeRawAndTarget::setRadarChannel(quint8 radarID, quint8 channel)
{
    QMutexLocker locker(&mMutex);
    mRadarInfo[radarID].channel = channel;
//    qDebug() << __FUNCTION__ << __LINE__  << radarID << channel;
}

//void AutoRechargeRawAndTarget::setDeviceWorker(Devices::Can::IDeviceWorker *pDeviceWorker)
//{
//    mDeviceWorker = pDeviceWorker;
//}

void AutoRechargeRawAndTarget::setDeviceManager(Devices::Can::DeviceManager *pDeviceMgr)
{
    mDeviceMgr = pDeviceMgr;
}

void AutoRechargeRawAndTarget::setAnalysisWorker(Analysis::AnalysisWorker *pAnalysisWorker)
{
//    if( mAnalysisWorker ){
//        disconnect( mAnalysisWorker, &Analysis::AnalysisWorker::analysisFinished, this, &AutoRechargeRawAndTarget::receiveEndFrame );
//    }
    mAnalysisWorker = pAnalysisWorker;
//    connect( mAnalysisWorker, &Analysis::AnalysisWorker::analysisFinished, this, &AutoRechargeRawAndTarget::receiveEndFrame, Qt::UniqueConnection );
}

void AutoRechargeRawAndTarget::setType(AutoRechargeRawAndTarget::TYPE type)
{
    mType = type;
}

void AutoRechargeRawAndTarget::clearRadarInfo()
{
    QMutexLocker locker(&mMutex);
    mRadarInfo.clear();
}

void AutoRechargeRawAndTarget::enterHilMode( TYPE type )
{

    //发送回灌指令
    QByteArray data1 = QByteArray::fromHex("3158AF8000000000");
    QByteArray data2 = QByteArray::fromHex("0000000000000000");

    switch( type ){
    case RECHARGET_TYPE_RAW:
        data2 = QByteArray::fromHex("0000080000000000");
        break;
    case RECHARGET_TYPE_TARGET:
        data2 = QByteArray::fromHex("00000C0000000000");
        break;
    case RECHARGET_TYPE_NORMAL:
        data2 = QByteArray::fromHex("0000040000000000");
        break;
    default:
        return;
    }

    if( mRadarInfo.contains( 7 ) ){
        mDeviceMgr->sendFrame( mRadarInfo[7].channel, 0x307, data1, false );
        mDeviceMgr->sendFrame( mRadarInfo[7].channel, 0x207, data2, false );
    }

    if( mRadarInfo.contains( 6 ) ){
        mDeviceMgr->sendFrame( mRadarInfo[6].channel, 0x306, data1, false );
        mDeviceMgr->sendFrame( mRadarInfo[6].channel, 0x206, data2, false );
//        qDebug() << __FUNCTION__ << __LINE__ << mRadarInfo[6].channel;
    }

    if( mRadarInfo.contains( 5 ) ){
        mDeviceMgr->sendFrame( mRadarInfo[5].channel, 0x305, data1, false );
        mDeviceMgr->sendFrame( mRadarInfo[5].channel, 0x205, data2, false );
    }

    if( mRadarInfo.contains( 4 ) ){
        mDeviceMgr->sendFrame( mRadarInfo[4].channel, 0x304, data1, false );
        mDeviceMgr->sendFrame( mRadarInfo[4].channel, 0x204, data2, false );
    }
}

void AutoRechargeRawAndTarget::run()
{
//    qDebug() << __FUNCTION__ << __LINE__;
    enterHilMode( mType );
    mDebugFile.setFileName( "./AutoRechargeRawAndTarget.log" );
    mDebugFile.open( QIODevice::WriteOnly );

    connect( mAnalysisWorker, &Analysis::AnalysisWorker::analysisFinished, this, &AutoRechargeRawAndTarget::receiveEndFrame, Qt::UniqueConnection );
    emit begin( mFileName );

    init();

    if( mBinaryParse ){
        delete mBinaryParse;
        mBinaryParse = NULL;
    }
    mBinaryParse = new FrameFileParse_Binary( mFileName );
    mRun = true;
    while( mRun && rechargeOneFrame() ){
    }

    emit end( mFileName );
    disconnect( mAnalysisWorker, &Analysis::AnalysisWorker::analysisFinished, this, &AutoRechargeRawAndTarget::receiveEndFrame );
    enterHilMode( RECHARGET_TYPE_NORMAL ); //退出回灌模式

    mDebugFile.close();
}

void AutoRechargeRawAndTarget::stop()
{
    mRun = false;
}

void AutoRechargeRawAndTarget::receiveEndFrame(quint8 radarID, const AnalysisData &analysisData)
{
    QMutexLocker locker(&mMutex);
    if( !mRadarInfo.contains(radarID) ){
        return;
    }
    mRadarInfo[radarID].lastOnLineTime = QDateTime::currentMSecsSinceEpoch();
    if( mRadarInfo[radarID].waitRawNO == analysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount ){
        mRadarInfo[radarID].waitTime = 0;
//        qDebug() <<QDateTime::currentDateTime().toLocalTime() << __FUNCTION__ << __LINE__ << "lxw" << radarID << analysisData.mEndFrameData.mRadarRollingCount
//                 << analysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount;
    }

    mDebugFile.write( "recv " );
    mDebugFile.write( QString::number(analysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount).toLocal8Bit() );
    mDebugFile.write( "\n" );

}

void AutoRechargeRawAndTarget::radarPause(quint8 radarID, quint64 rawNO)
{
    if( !mRadarInfo.contains(radarID) || !onLine( radarID ) ){
        return; //若掉线，则不等待，否则死等太耗时间
    }

    QMutexLocker locker(&mMutex);
    //mRadarNextMap[radarID] = true;
    mRadarInfo[radarID].waitTime = TIME_OUT_MS;    //ms
    mRadarInfo[radarID].waitRawNO = rawNO;
}

void AutoRechargeRawAndTarget::wait(quint32 ms)
{
    QEventLoop loop;
    QTimer timer;
    timer.setSingleShot( true );
    connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
    timer.start( ms );
    loop.exec();
    timer.stop();

    QMutexLocker locker(&mMutex);
    QMap< quint8, RadarInfo >::iterator it = mRadarInfo.begin();
    for( ; it!=mRadarInfo.end(); it++ ){
        mRadarInfo[it.key()].waitTime -= ms;
    }
}

void AutoRechargeRawAndTarget::init()
{
    QMutexLocker locker(&mMutex);
    QMap< quint8, RadarInfo >::iterator it = mRadarInfo.begin();
    for( ; it!=mRadarInfo.end(); it++ ){
        mRadarInfo[it.key()].lastOnLineTime = QDateTime::currentMSecsSinceEpoch();
    }
}

bool AutoRechargeRawAndTarget::onLine(quint8 radarID)
{
    if( !mRadarInfo.contains(radarID) ){
        return false;
    }

    quint64 diffMS = QDateTime::currentMSecsSinceEpoch() -  mRadarInfo[radarID].lastOnLineTime;
    if( diffMS < DISCONNECT_TIME_MS ){
        return true;
    }else{
        qDebug() << __FUNCTION__ << __LINE__ << radarID << "out line" << diffMS;
        return false;
    }
}

bool AutoRechargeRawAndTarget::rechargeOneFrame()
{
//    quint64 beginMS = QDateTime::currentMSecsSinceEpoch();

    //发送
    bool bWait = false;
    bool bSend = false;
    QMap< quint8, RadarInfo >::iterator it = mRadarInfo.begin();
    for( ; it!=mRadarInfo.end(); it++ ){
//        quint8 radarID = it.key();
        quint8 channel = it.value().channel;

        if( it.value().waitTime > 0 ){
            bWait = true;
            bSend = true;
            continue;  //等待雷达响应结束帧，或超时
        }

        while( it.value().frameQueue.isEmpty() && loadFrame() ){
            //一直加载，直到加载到文件末尾，或有此雷达的数据
        }
//        qDebug() << __FUNCTION__ << __LINE__ << mRadarFrameMap[radarID].size();
        if( !it.value().frameQueue.isEmpty() ){
            Devices::Can::CanFrame frame = it.value().frameQueue.dequeue();
//            qDebug()<<QDateTime::currentDateTime().toLocalTime() << __FUNCTION__ << __LINE__ << "lxw" << frame.idHex() << frame.dataHex();
            mDeviceMgr->sendFrame( channel, frame.id(), frame.data(), false );

            mDebugFile.write( frame.idHex().toLocal8Bit() );
            mDebugFile.write( frame.dataHex().toLocal8Bit() );
            mDebugFile.write( "\n" );

            //////////////北汽雷达不能发太快，下位机要求发一帧数据，等待/////////////////////
//            QEventLoop loop;
//            QTimer timer;
//            timer.setSingleShot( true );
//            connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
//            timer.start( 5 );
//            loop.exec();
//            timer.stop();
            ///////////////////////////////////

            bSend = true;
            if( frame.idN() == 0x4F0 ){ //发送了结束帧，暂停发送该雷达的数据，等待雷达响应
                const uint8_t *data = (const uint8_t *)frame.data().data();
                quint64 rawNO = ((data[15] & 0xFFU) +
                                (((uint32_t)data[14]) << 8) +
                                (((uint32_t)data[13]) << 16) +
                                (((uint32_t)data[12] & 0xFFU) << 24));
                radarPause( frame.radarID(), rawNO );
//                qDebug() << __FUNCTION__ << __LINE__ << "wait" << frame.radarID() << rawNO;
                mDebugFile.write( "wait " );
                mDebugFile.write( QString::number(rawNO).toLocal8Bit() );
                mDebugFile.write( "\n" );

                //////////////北汽雷达不能发太快，下位机要求发一帧数据，等待/////////////////////
//                QEventLoop loop;
//                QTimer timer;
//                timer.setSingleShot( true );
//                connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
//                timer.start( 20 );
//                loop.exec();
//                timer.stop();
                ///////////////////////////////////
            }
        }
    }

//    quint64 endMS = QDateTime::currentMSecsSinceEpoch();

    if( bWait ){
        wait( 20 );
    }

    return bSend;
}

bool AutoRechargeRawAndTarget::loadFrame()
{

    if( !mBinaryParse ){
        return false;
    }

    if( !mBinaryParse->isOpen() ){
        mBinaryParse->open();
    }

    QList<Devices::Can::CanFrame> frameList;
    if( !mBinaryParse->next( frameList ) ){
        return false;
    }

    for( int i=0; i<frameList.size(); i++ ){
        quint8 radarID = frameList[i].radarID();
        quint64 IDN = frameList[i].idN();

//        qDebug() << __FUNCTION__ << __LINE__ << radarID
//                 << frameList[i].idHex() << frameList[i].dataHex();

        if( !mRadarInfo.contains( radarID ) ){
            continue;
        }

        //只要原始点 跟踪点 车身 结束帧
        switch ( IDN ) {
        case 0x4C0: //报警数据
        case 0x3F0: // 车身信息
        case 0x4F0: // 结束帧
            {
                mRadarInfo[radarID].frameQueue.enqueue( frameList[i] );
            }
            break;
        case 0x400: // 原始点头
        case 0x410: // 原始点
            {
                if( mType==RECHARGET_TYPE_RAW ){
                    mRadarInfo[radarID].frameQueue.enqueue( frameList[i] );
                }
            }
            break;
        case 0x430: // 跟踪点头
        case 0x440: // 跟踪点
            {
                if( mType==RECHARGET_TYPE_TARGET ){
                    mRadarInfo[radarID].frameQueue.enqueue( frameList[i] );
                }
            }
            break;
        default:
            break;
        }
    }
//    qDebug() << __FUNCTION__ << __LINE__;
    return true;
}
