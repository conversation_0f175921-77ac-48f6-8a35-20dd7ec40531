/*
 * @Author: fan<PERSON> <PERSON><PERSON><PERSON>
 * @Date: 2022-04-15 14:47:59
 * @LastEditTime: 2025-02-14 18:58:06
 * @LastEditors: <PERSON>can
 * @Description: 雷达标定校准
 * @FilePath: alignment.h
 * Copyright (C) 2021 Chengtech Ltd.
 */
#ifndef ALIGNMENT_H__
#define ALIGNMENT_H__
#ifdef ALPSPRO_ADAS
#include "typedefs.h"
#include "sharedVar.h"
#include "prot_pcan_app.h"
#include "aln_staticEolFun.h"
#include "aln_dynamicEolFun.h"
#include "aln_type.h"
#elif defined(PC_DBG_FW)
#include "app/system_mgr/typedefs.h"
#include "app/include/sharedVar.h"
#include "app/protocol/prot_pcan_app.h"
#include "app/system_mgr/cfg.h"
#include "app/aln/aln_staticEolFun.h"
#include "app/aln/aln_dynamicEolFun.h"
#include "app/aln/aln_type.h"
#else
#include "typedefs.h"
#include "sharedVar.h"
#include "prot_pcan_app.h"
#include "aln_staticEolFun.h"
#include "aln_dynamicEolFun.h"
#include "aln_type.h"
#endif


typedef union tagCalcResultSendFlag {
	uint8_t word;
	struct {
		uint8_t REV 				: 5; //其他
		uint8_t snSendFlag		: 1; //其他
		uint8_t ExtCfgSendFlag	: 1; //扩展配置上报
		uint8_t resultSendFlag	: 1; //结果上报
	}bit_big;
	struct {
		uint8_t resultSendFlag	: 1; //结果上报
		uint8_t ExtCfgSendFlag	: 1; //扩展配置上报
		uint8_t snSendFlag		: 1; //其他
		uint8_t REV 				: 5; //其他
	}bit;
}CalcResultSendFlag;

typedef struct {
    /*
    uint8_t leastSquareThr; //拟合直线的相关性门限 --- //默认93 --0.93
    uint8_t cycleThr;       //周期门限 //默认10
    uint8_t caliThr;        //使用校准门限 //默认 6次
    uint8_t FrameAngleThr;  //相邻两帧角度跳动的门限跳动太大表示场景不可靠
    uint8_t cycleAngleThr;  //周期间的角度跳动的门限跳动太大表示场景不可靠
    CalcResultSendFlag sendFlag;
    */
    uint8_t stdThr;             //均方差门限 1
    uint8_t cycleWaitThr;       //计算等待门限 10 一个计算周期间隔帧 1
    uint8_t caliCntThr;         //几个计算周期算一次校准 //默认 10次 1
    uint8_t caliMode;           //相邻两帧角度跳动的门限跳动太大表示场景不可靠 1
    uint8_t caliObjNumberThr;   //参与计算的目标数量门限
    uint8_t caliAngleBeatThr;   //角度跳动门限  1
    uint8_t canCaliMinAngleThr; //允许标定的最小角度门限
    CalcResultSendFlag sendFlag;//1
} selfCaliCfg;

//自校准结构体
#define AUTO_CALIB_DATA_MAGIC 0xC5C5C5C5U
typedef struct 
{
	uint32_t magicFlag;
	float HrzAngle;
	float ElvAngle;
	uint32_t calcOdometer;	//标定使用的里程数
	uint32_t Odometer;		//上次标定时刻的里程数
	uint16_t curAccObjCnt;
	uint8_t enable;
	uint8_t calcState;
	uint8_t calcAngleDiff;
	uint8_t leastRFactor;
    uint8_t calcCorrelationCoefficient;
	uint8_t calcBufCnt;
	uint8_t SSE;
	uint8_t SST;
	uint8_t currentAccEn;	//用于记录当前环境是否满足积累条件
	uint8_t saveFlag;
	uint8_t Time[6];		//记录最后一次标定的时间
	uint32_t resv[53];
	uint32_t crc;
	uint64_t timestamp;
} selfCaliInfo;

typedef struct {
    uint8_t calcState;
    uint8_t gCalcCycleCnt;
    uint8_t gCalcValueCnt;
    uint8_t gCalcErrorCnt;
    uint8_t gCalcSycleInterval;
    uint8_t gcaliA;
    uint8_t leastSquareThr;
    selfCaliCfg cfg;
    selfCaliInfo info;
    float calcHorizontalAngle;
} radarCaliInfo;

typedef struct
{
    float height;
    float horizontalAngle;
	float verticalAngle;
    float verticalAngle_y; //垂直角度，不应该输入，用于陀螺仪实时监测,y代表前后方向
    float verticalAngle_x; //垂直角度，不应该输入，用于陀螺仪实时监测,x代表左右方向
	float verticalOffset;
    float horizontalOffset;
} radarInstallInfo;

typedef union tagCalcDebugFlag {
	uint8_t word;
	struct {
		uint8_t loseTxObjErr		: 1; //其他
		uint8_t multipleObjErr	: 1; //其他
		uint8_t angleDiffErr		: 1; //目标波动较大
		uint8_t SwTargetPos		: 1; //标靶目标切换标志
		uint8_t AngleOverflowErr	: 1; //角度溢出标志
		uint8_t carSpeedErr 		: 1; //车速错误
		uint8_t timeOutErr		: 1; //超时
		uint8_t invalidObjErr		: 1; //无效目标
	}bit;
}CalcDebugFlag;

typedef struct radarInstallCalcCfg_t {
    float calcObjRange;				//目标所在的真实距离
    float objRangSearchThreshold;	//目标距离搜索范围
    float calcObjAngle;				//目标的真实角度，一般0度
    float objAngleSearchThreshold; 	//目标角度搜索范围
    //float histAngleThreshold;	//直方图角度分辨
    float calcMaxAngle;			//标定的最大角度，偏差超过calcMaxAngle，认为标定角度不可用
    uint32_t timeOutThr; //超时门限
	uint64_t startTime;
    CalcResultSendFlag sendFlag;	//结果是否上报标志
    uint8_t waitFrame; 			//使能后等待到帧数，才开始标定
    //uint8_t timeOutFrame;			//超时帧数，在waitFrame + timeOutFrame内标定完成，否则标定失败
    //uint8_t invalidObjMax;		//无效目标最大门限，超过这么多帧没有检测到目标，则报错。
    //uint8_t histMaxNumber;		//直方图最大的有效个数，大于则认为角度有效
    uint8_t calcEn;
    //uint8_t cohesionEn;					//是否使能聚类，1使能 0不使能
    //uint8_t TxSel;				//校准Tx选择，0-使用Tx1+Tx2  1-使用Tx1 2-使用Tx2 ...
    //uint8_t UsingTxSel;			//校准Tx选择,正在使用的tx
}radarInstallCalcCfg;

typedef struct radarInstallCalcInfo_t {
    float calcInstallAngle; 		    //安装标定的水平结果角度
    float calcInstallPitchAngle; 		//安装标定的俯仰结果角度
	radarInstalledCalcObj obj;
	CalcDebugFlag debugFlag;			//debug标志
	uint8_t calcState;
	uint8_t calcCycleCnt; 				//本次校准的周期，用于计算超时
	uint8_t traClearCnt; 					//清空trk
	uint8_t HAdiff; 						//水平波动
	uint8_t VADiff; 						//俯仰波动
}radarInstallCalcInfo;

typedef struct factor
{
    float caliAngle;
    float angleOffset;
    float leastRFactor;
    float calcCorrelationCoefficient;
    float SSE;
    float SST;
	float A;
} ServiceCaliParam_t;

typedef union tagCaliWarningInfo {
	uint8_t word;
	struct {
        uint8_t timeOut                            : 1;       //已经超时
        uint8_t coefficientLow                     : 1;       //相关系数过低
        uint8_t sensorIsBlind                      : 1;       //雷达失明
        uint8_t locationNumberInsufficient         : 1;       //目标点太少 场景不满足
        uint8_t longitudinalAccelerationLarge      : 1;       //纵向加速度过大
        uint8_t yawVelocityLarge                   : 1;       //横摆角角过大
        uint8_t egoVelocityFast                    : 1;       //车速过快
        uint8_t egoVelocitySlow                    : 1;       //车速过慢
	}bit;
}caliWarningInfo;

typedef struct {
	uint64_t startTime;
	float HrzAngle;
	float ElvAngle;
	float leastRFactor;
    float calcCorrelationCoefficient;
    float SSE;
    float SST;
	uint32_t curAccObjCnt;
	uint32_t timeOutThr; //超时门限
	uint16_t elapsedTime;
	uint8_t calcState;
	uint8_t enable;
	uint8_t caliProgressBar;
	uint8_t calcAngleDiff;
	uint8_t currentAccEn;	//用于记录当前环境是否满足积累条件
	caliWarningInfo warningInfo;
}serviceCaliInfo;

//标定数据结构体
typedef struct 
{
	uint16_t installAlignFlag;  
	uint16_t serviceAlignFlag;
	uint16_t installAlignCnt;
	uint16_t serviceAlignCnt;
	float installHrzAngle;
	float installElvAngle;
	float serviceHrzAngle;
	float serviceElvAngle;
	uint32_t resv[56];
	uint32_t flag;
	uint32_t crc;
} stAlignInfo;

//自标定数据结构体 128byte
typedef struct 
{
    uint16_t autoAlignFlag;     //自标定是否需要产生DTC标志
    uint16_t autoAlignCntSucc;  //自标定成功次数
    uint16_t autoAlignCntFail;  //自标定失败次数
    uint16_t autoAlignCntFlash; //自标定写入flash次数
    float autoHrzAngle;         //自标定水平角度 deg
    float autoElvAngle;
    uint32_t resv2[26];
    uint32_t flag;
    uint32_t crc;
} stAutoAlignInfo;

typedef struct 
{
    uint8_t autoAlignCntSucc : 4;
    uint8_t autoAlignCntFail : 4;
} stAutoAlignCntCurrent;

typedef struct
{
    uint32_t timeOutThr; //超时门限
} serviceCaliCfg;

typedef struct
{
    uint32_t magicFlag;
    float calcHorizontalAngle;
    float calcVerticalAngle;
    serviceCaliCfg  cfg;
    serviceCaliInfo info;
    caliWarningInfo warningInfo;
    uint8_t  calcState;
    uint8_t  enable;
    uint8_t  calcHorizontalState;
    uint8_t  calcVerticalState;
    uint32_t crc;
} radarServiceCaliInfo;

//标定模式
typedef enum
{
	INSTALL_CALI,
	SERVICE_CALI,
	AUTO_CALI
} E_CALI_TYPE;

enum INSTALL_CFG_E
{
    HRZ_ANGLE_CFG,
    HRZ_OFFSET_CFG
};

enum INSTALL_CFG
{
    HRZ_OFFSET_ANGLE_CFG,
    HRZ_OFFSET_PITCH_ANGLE_CFG
};

extern void initSelfCali(void);

extern const selfCaliInfo *getSelfCaliInfo(void);
extern void resetInstallCaliStatus(void);

extern void initInstallInfo(void);

extern void initInstallCali(void);

extern void initSrvCali(void);

extern bool isInstallCaliError(void);

extern void setInstallCaliEnable(uint8_t en);

extern uint8_t getInstallCaliEnable(void);

// extern float getTotalAngleOffset(void);

extern void setInstallInfo(enum INSTALL_CFG_E type, float value);

// extern float getInstallInfo(enum INSTALL_CFG_E type);

extern const radarInstallCalcInfo *getInstallCaliInfo(void);

extern void setInstallCaliStatus(uint8_t status);

extern const radarInstallCalcCfg *getInstallCaliCfg(void);

extern const serviceCaliInfo *getSrvCaliInfo(void);

extern void setSrvCaliStatus(uint8_t status);

extern uint8_t getServiceCaliEnabled(void);

extern void setServiceCaliEnabled(uint8_t enable);

extern void resetSrvCaliStatus(void);

extern uint8_t getSrvCaliDepthStatus(void);

extern void setInstallCalcAngle(enum INSTALL_CFG type, float angle);

// extern void serviceAngleCalc(TRawRVAList *rawData);

// extern void serviceAngleCaliASC(TRawRVAList *rawData);

extern void initAlignment(void);

extern bool isEverAlignment(void);

extern bool isAlignDone(void);

extern bool isInstallAlignOK(void);

extern bool isServiceAlignOK(void);

extern bool loadSelfCaliData(void);

// MRR
void ALN_syncAlign2ALNServiceInfo(CT_EOL_DCR_t *ptrALNServiceInfo);
void ALN_syncStaticALNService2AlignInfo(const CT_EOL_DCR_t *ptrALNServiceInfo);
void ALN_syncDynamicALNService2AlignInfo(const CT_DYNAMIC_EOL_INFO_t *ptrALNServiceInfo);
void ALN_syncClearAlignInfo(void);
bool ALN_isInstallAlignOK(void);
bool ALN_isServiceAlignOK(void);
bool ALN_isAlignDone(void);
bool ALN_isEverAlignment(void);
float ALN_getAutoHrzAngle(void); 
void ALN_setAutoHrzAngle(const float autoElvAngle);
bool ALN_getAutoAlignFlag(void);
uint16_t ALN_getAutoAlignCntOfFlash(uint8_t whichCnt);
uint8_t ALN_getAutoAlignCntOfReadTime(uint8_t whichCnt);
void ALN_updateAutoAlignCnt(uint8_t autoAlignFlag);
int32_t ALN_clearAlignFlag(void);
const radarCaliInfo *ALN_getSelfCaliInfo(void);
const radarInstallInfo *ALN_getInstallInfo(void);
const radarInstallCalcInfo *ALN_getInstallCaliInfo(void);
const radarInstallCalcCfg *ALN_getInstallCaliCfg(void);
void ALN_resetInstallCaliStatus(void);
const radarServiceCaliInfo *ALN_getSrvCaliInfo(void);
void ALN_resetSrvCaliStatus(void);
int32_t ALN_loadAlignmentData(void);
void ALN_clearAutoAlignmentData(void);
void ALN_clearAutoAlignCnt(void);
int32_t ALN_saveAutoAlignmentData(void);
int32_t ALN_saveAlignmentData(E_CALI_TYPE type);
int32_t ALN_initAlignment(void);
int32_t setUnAlignInstallAngle(void);
#endif

