﻿/**
 * @file adas_alm_rctab.c
 * @brief
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#include <stdbool.h>
#include <math.h>
#include <string.h>
#ifdef ALPSPRO_ADAS
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "rdp/track/data_process/rdp_interface.h"
#include "adas/common/linear_regression.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#elif defined (PC_DBG_FW)
#include "app/system_mgr/typedefs.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "alg/track/rdp_interface.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "other/temp.h"
#else
#include "app/rdp/rdp_clth_radar_lib.h"
#include "app/rdp/rdp_interface.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "common/include/vehicle_cfg.h"
#endif

static float interceptUpperBuffer = 0.0f;
static float interceptMaxAngleUpperBuffer = 0.0f;
static float posBufYFar = 0.0f;
static float rangeBuf = 0.0f;	   //目标在径向距离上的缓冲
static float ttcBufRctaLower = 0.0f; // rcta功能ttc的下限buffer
static float ttcBufRctaUpper = 0.0f; // rcta功能ttc的上限buffer
static OBJ_NODE_STRUCT gtempobjPath[1];
static uint8_t CTB_EDRTargetI;
static uint8_t CTB_EDRTargetLifeCycle;

static float stored_rctab_x[ADAS_HISTORY_NUM];
static float stored_rctab_y[ADAS_HISTORY_NUM];
static st_Rctb_pre rctbPre;     // RCTB碰撞预测

//#define RCTAB_MIN_IA_CROSS(angle, angleThr) (fabsf(angle - 60) < angleThr)      // fctb小角度横穿

#if (ALARM_TYPE_EDR_EN == 1)
ADAS_EDRData_t EDRDataCTB;

/**
 * @brief EDR所需数据结果
 *
 * @return const ADAS_EDRData_t*
 */
const ADAS_EDRData_t *ADAS_getEDRData()
{
    return &EDRDataCTB;
}
#endif


static float ADAS_cacDDCi(OBJ_NODE_STRUCT *pobjPath, uint8_t i)    //详见《IDD_FS_A30-10020》
{
    float k, b, DDCi;    // y = k*x + b;

    k = tanf((pobjPath[i].avgheadingAngle - 90) * degtorad); // 先算k
    b = pobjPath[i].y - (k * pobjPath[i].x);  // b = y - k*x; 再算出b
    DDCi = (k * (- VEHICLE_WIDTH_INFO/2)) + b;  //y = k*x + b，算出x = - VEHICLE_WIDTH_INFO/2;

    return DDCi;
}

/**
 * @brief 赋值EDR所需数据
 *
 * @param pobjPath 目标相关结构体地址
 * @param ttm 详见《IDD_FS_A30-10029》第13页解释
 * @param i 跟踪ID
 */
void ADAS_recordEDRData(OBJ_NODE_STRUCT *pobjPath, float ttm, uint8_t i)
{
#if (ALARM_TYPE_EDR_EN == 1)
    EDRDataCTB.M_TTM = ttm;
    EDRDataCTB.M_DDCI = ADAS_cacDDCi(pobjPath, i);
    EDRDataCTB.ObjAx = RDP_getTrkObjectListPointer()->rdpTrkObject[i].rdpTrkObjArelX;
    EDRDataCTB.ObjAy = RDP_getTrkObjectListPointer()->rdpTrkObject[i].rdpTrkObjArelY;
    EDRDataCTB.ObjDx = pobjPath[i].x;
    EDRDataCTB.ObjDy = pobjPath[i].y;
    EDRDataCTB.ObjVx = pobjPath[i].vx;
    EDRDataCTB.ObjVy = pobjPath[i].vy;
    EDRDataCTB.ObjExist = RDP_getTrkObjectListPointer()->rdpTrkObject[i].rdpTrkObjReliability;
    EDRDataCTB.ObjObstcl = 0;   //未实现
    EDRDataCTB.ObjCntAlive = pobjPath[i].lifeCycle;
    EDRDataCTB.ObjMotionPattern = 3;    //默认给3表示运动
    EDRDataCTB.M_IntersectionAngle = pobjPath[i].avgheadingAngle;

    if(pobjPath[i].boxLength >= 4 && pobjPath[i].boxWidth >= 1.5)   //尺寸较大的为4轮车
        EDRDataCTB.FCTBObjClassification = 0x1;
    else
        EDRDataCTB.FCTBObjClassification = 0;
#endif
}

/**
 * @brief 此跟踪点的可信度判断
 *
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @return bool 1：可信，0：不可信
 */
static bool ADAS_RCTA_checkTrkObjReliability(uint8_t i, OBJ_NODE_STRUCT *pobjPath)
{
    bool flag = false;
    uint8_t blindAreaAngleFlag = 0;  //目标是否在车前方盲区的标志位
    float reliability_max = 0.0f;

    blindAreaAngleFlag = ADAS_checkBlindArea(i,pobjPath);

    if((blindAreaAngleFlag == 1U) && (pobjPath[i].lifeCycle > RCTB_BLINDAREALIFECYCLE))  //盲区内外对置信度要求不一样
    {
        reliability_max = RCTB_RELIABILITYMININBLINDAREA;
    }
    else
    {
        reliability_max = RCTB_RELIABILITYMININRADARFOV;
    }

    if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA))
    {
        reliability_max += RCTB_RELIABILITYBUF;
    }

    if(pobjPath[i].TrkObjReliability >= reliability_max)
    {
        flag = true;
    }

    return flag;
}

/**
 * @brief 记录报警之前目标对应TTM所在的x位置
 *
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @param timeVehicleBrk 刹停时间
 * @return uint8_t 1:有碰撞风险，0：无风险
 */
void ADAS_RCTA_recordX_AtTtmX(OBJ_NODE_STRUCT* pobjPath, const uint8_t i,const float ttm)
{
    if ((ttm <= RCTA_TTC_MAX_TIME + 1.0f) && (ttm >= RCTA_TTC_MAX_TIME + 0.5f))
    {
		pobjPath[i].rctabX_AtTtmX = pobjPath[i].x;
    }
}


/**
 * @brief 结合刹车距离划定一个区域，，目标落入此区域,则认为有碰撞风险
 *
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @param timeVehicleBrk 刹停时间
 * @return uint8_t 1:有碰撞风险，0：无风险
 */
#if 0
static uint8_t ADAS_RCTB_predictCollisionBasePosition(uint8_t i, OBJ_NODE_STRUCT *pobjPath, float timeVehicleBrk)
{
    uint8_t flag = 0; //满足报警标志位：1—满足报警，0-不满足报警

    if ((pobjPath[i].x <= RCTB_COLLISIO_NHAZARD_ZONE_X) && // x方向停车距离，注意adas坐标系中，目标靠近为正
        (pobjPath[i].y <= RCTB_COLLISIO_NHAZARD_ZONE_Y)    // y方向停车距离     由于碰撞TTM时间过短，在车辆和目标互相靠近追逐的时候可能距离很近了，ttm还没有满足，反应时间不足，所以设置了一个很小的必刹区域
    )
    {
        //flag = 1;     // 暂时取消必报区  必报区可能会导致偶发假点误报. 
    }

    return flag;
}
#endif

/**
 * @brief 控制一个刹停距离, 制动后尽量减小刹停距离.
 *
 * @param i
 * @param pobjPath
 * @param timeVehicleBrk
 * @return uint8_t
 */
static uint8_t ADAS_RCTB_controlBrkDistance(uint8_t i, OBJ_NODE_STRUCT *pobjPath, float timeVehicleBrk, float distanceBrk, float ttm, ALARM_OBJECT_T *pobjAlm)
{
    // 考虑目标能够安全通过碰撞区域  不会触发制动.  主要是处理晚制动问题.
    // ttm_range 超过一定距离时. 且X在外侧时. 考虑刹停控制
    // ttm_range 超过一定距离 但是X已经在内侧. 此时不能再做刹停控制. 此时再控制会导致报警晚.
    uint8_t flag = 0;               // 满足报警标志位：1—满足报警，0-不满足报警
    float ttm_range = 0.0f;         // 到车辆侧边缘的距离.
    float ttc = 0.0f;               // 纵向TTC. 自车与目标纵向相对位置上的TTC.
    // float vehiclemovedistance = 0.0f;           // 考虑自车在纵向方向上的运动距离
    float longitudinalthr = 0.0f;       // 纵向控制的阈值.  需要有一个最小值. 

    // 在侧边时. X横向小于1就立即不使用刹停控制. 此时如果未制动. 极有可能是被纵向TTC控制. 需要细化的点 就是 再过多久就不要再制动了. 

    ttm_range = sqrt((pobjPath[i].x)*(pobjPath[i].x) + (pobjPath[i].y)*(pobjPath[i].y));

    // 目标在自车外侧 且 距离大于一定值时. 才使用刹停控制.
    if ((ttm_range > 2.0f) && (pobjPath[i].x > 1.0f) && ((timeVehicleBrk + RCTB_BREAK_RESERVE_TIME) < ttm))
    {
        flag = 0;
    }
    else
    {
        flag = 1;
    }

    // 小角度斜穿目标. 如果跟踪点已经跨过车身.  且自车车速与相对距离碰撞上目标的TTC还大于1秒. 则不制动.
    // 避免小角度一开始没制动. 目标越过之后制动了.
    // 刹停控制分为两类. 前面是总体距离控制.  这里需要纵向细节控制.
    if (pobjPath[i].avgheadingAngle < 95.0f)
    {
        ttc = ((pobjPath[i].y - RCTB_Y_PRE_SAFEDISTANCE) / (fabsf(pobjAlm->BSDVelSpeedVal) / 3.6f));
    }
    else
    {
        ttc = ((pobjPath[i].y - RCTB_Y_PRE_SAFEDISTANCE) / pobjPath[i].vy);
    }

    // 纵向方向上的距离控制  避免制动过于灵敏. 
    longitudinalthr = 1.8f + (pobjPath[i].boxLength * 0.1f) + (pobjPath[i].vx * 0.1f);
    if (longitudinalthr > 2.5f)
    {
        longitudinalthr = 2.5f;
    }

    // 主要考虑纵向远距离目标不制动.  不同速度 不同类型的目标. 可控制的纵向距离是不一样的. 行人可以控制的小一些  二轮车可以控制的大一些.
    // 纵向TTC的Y方向控制.   目标速度越快  Y控制的越大一些. 
    // 目标如果没有跟踪到车头  纵向的Y可能会很不准确. 导致纵向TTC偏大  一直不满足.   这里考虑使用预测的Y值. 
    // 这里本质上是不让制动那么早  如果偶发没进来. 也没什么问题.
    if ((ttc > RCTB_TTC_MAX_TIME) && (rctbPre.rctb_enter_y > longitudinalthr))
    {
        flag = 0;
    }

    // 到自车后方还没制动. 此时的制动要更加谨慎
    if ((pobjPath[i].x < 0.0f) && (ttc > RCTB_TTC_MAX_TIME))
    {
        flag = 0;
    }

    return flag;
}

/**
 * @brief RCTAB 线性回归调用流程
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪目标ID
 */
#define RCTAB_LINEAR_REGRESSION_EVALUATE_MODEL 2
static int8_t ADAS_RCTAB_LinearRegression_Flow(const ALARM_OBJECT_T *pobjAlm,
                                               OBJ_NODE_STRUCT *pobjPath,
                                               const uint8_t i,
                                               const VDY_Info_t *pVDY,
                                               const OBJ_ADAS_TYPE_ENUM type)
{
    int8_t rctabCollisionFlag = 0;
    // 定义 w 和 b，用于存储线性回归模型的斜率和截距
    float w = 0, b = 0;
    //float variance = 0;
    float x_new = 0, y_new = 0;
    float threshold = 0;
    float mse_1 = 0, mse_2 = 0;
    // float r_squared = 0;
    float mse_2_buff = 1.0f;

    // 数据标准化, 对stored_dow_x、stored_dow_y赋值
    ADAS_DataNormalizeToMeans(pobjAlm, pobjPath, i, type, ADAS_HISTORY_NUM, stored_rctab_x, stored_rctab_y);
    // 调用线性回归函数，计算斜率和截距
    ADAS_LinearRegression_FirstOrder(pobjAlm, pobjPath, i, type, ADAS_HISTORY_HEADINGANGLE_NUM, stored_rctab_x, stored_rctab_y, &w, &b);
    pobjPath[i].lrFitting.rctab_w = w;
    pobjPath[i].lrFitting.rctab_b = b;

    if (pobjPath[i].lastAlarmType & ALARM_ACTIVE_RCTA) // 上一帧在报警，则放宽条件
    {
        mse_2_buff *= 0.9;
    }

    // 三种评估模型性能的方法
#if (RCTAB_LINEAR_REGRESSION_EVALUATE_MODEL == 1)
    // 动态阈值 使用方差作为阈值来确定误差的容忍范围
    // 计算 y 的方差
    variance = ADAS_CalculateVariance(pobjAlm, pobjPath, i, ADAS_HISTORY_HEADINGANGLE_NUM, stored_rctab_x, stored_rctab_y);
    // float threshold = variance;  // 可以调整阈值，比如使用方差的某个倍数
    threshold = 1.5f * variance; // 使用方差的 1.5f 倍作为阈值
    // 使用新点 (x_new, y_new) 验证均方误差, 调用均方误差计算函数
    x_new = pobjPath[i].stored_last_x[ADAS_HISTORY_HEADINGANGLE_NUM];
    y_new = pobjPath[i].stored_last_y[ADAS_HISTORY_HEADINGANGLE_NUM]; // 这里使用之前的点做验证
    mse_2 = ADAS_CalculateSinglePointMSE(x_new, y_new, w, b);
    mse_2 *= mse_2_buff;
    if (((mse_2 < threshold) && (variance < 0.15)) ||
        ((mse_2 < 0.05) && (variance < 0.15)))
    {
        mse_2 = 0.5f; // 统一到下面做冗余处理
        threshold = 1.0f;

        // printf("%s: This should show in the console.\n", __FUNCTION__);

#ifdef _MSC_VER
        // ADAS_Sim_runPythonScript(pobjAlm, pobjPath, i, type, gFrameNb); 后续评估再用
#endif
    }
    else
    {
        mse_2 = 1.0f; // 统一到下面做冗余处理
        threshold = 0.5f;
    }
#elif (RCTAB_LINEAR_REGRESSION_EVALUATE_MODEL == 2)
    // 动态阈值 使用均方误差的滑动窗口均值作为动态阈值,和单个mse做对比。
    // 计算均方误差
    mse_2 = ADAS_CalculateTotalMSE(pobjAlm, pobjPath, i, ADAS_HISTORY_HEADINGANGLE_NUM, stored_rctab_x, stored_rctab_y, w, b);
    //EMBARC_PRINTF ("MSE_2 : %f\n", mse_2);
    threshold = 0.3f;        // 暂时仅考虑拟合质量  MSE 越小越好.
    //threshold = mse_1 * 2.0f;                                                                                                           // 使用均方误差的滑动窗口均值的1.5f 倍座位动态阈值
    // x_new = pobjPath[i].stored_last_x[ADAS_HISTORY_HEADINGANGLE_NUM];
    // y_new = pobjPath[i].stored_last_y[ADAS_HISTORY_HEADINGANGLE_NUM]; // 这里使用之前的点做验证
    // mse_2 = ADAS_CalculateSinglePointMSE(x_new, y_new, w, b);
    // EMBARC_PRINTF ("mse_2 : %f\n", mse_2);

    // mse_2 *= mse_2_buff;

    // if (((mse_2 < threshold) && (mse_1 < 0.15)) ||
    //     ((mse_2 < 0.05) && (mse_1 < 0.05)))
    // {
    //     mse_2 = 0.5f; // 统一到下面做冗余处理
    //     threshold = 1.0f;
    // }

#else
    // 静态阈值 使用决定系数（R²）作为动态阈值
    // 计算决定系数 R^2
    r_squared = ADAS_CalculateRSquared(pobjAlm, pobjPath, i, ADAS_HISTORY_HEADINGANGLE_NUM, stored_rctab_x, stored_rctab_y, w, b);
    threshold = 0.7f; // 使用决定系数（R²）作为动态阈值
    if ((r_squared > FLOAT_EPS) && (r_squared < 1.0f))
    {
        mse_2 = r_squared;
        mse_2 = 1 - mse_2;
        threshold = 1 - threshold;
    }
    else
    {
        mse_2 = 1;
    }
    mse_2 *= mse_2_buff;
#endif
    if (mse_2 < threshold)
    {
        pobjPath[i].lrFitting.rctab_FitAvailable = 1; // 线性回归拟合有效
        if (pobjPath[i].lrFitting.rctab_FitAvailableCnt < 200)
        {
            pobjPath[i].lrFitting.rctab_FitAvailableCnt++;
        }
    }
    else
    {
        if (pobjPath[i].lrFitting.rctab_FitAvailableCnt > 4)
        {
            pobjPath[i].lrFitting.rctab_FitAvailableCnt = 4;
        }
        if (pobjPath[i].lrFitting.rctab_FitAvailableCnt > 0)
        {
            pobjPath[i].lrFitting.rctab_FitAvailableCnt--;
        }
        if (pobjPath[i].lrFitting.rctab_FitAvailableCnt <= 1)
        {
            pobjPath[i].lrFitting.rctab_FitAvailable = 0;
        }
    }

    //
    if (pobjPath[i].lrFitting.rctab_FitAvailable == 1)
    {
        // 使用新点 (x_new, y_new) 验证均方误差, 调用均方误差计算函数
        x_new = -2.0f, y_new = 3.0f;        // 出点验证. 
        mse_2 = ADAS_RCTAB_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_rctab_x, stored_rctab_y, x_new, y_new, w, b, &rctbPre);
        x_new = 0, y_new = -0.1f;   // 压缩入点 车身侧 Y要给个负值  便于函数内部计算.
        if (RCTAB_MIN_IA_CROSS(pobjPath[i].avgheadingAngle, 20))
        {
            // 小角度只判单侧即可
            x_new = 0.0f, y_new = -0.1f;    // 压缩入点 车身侧
            mse_1 = ADAS_RCTAB_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_rctab_x, stored_rctab_y, x_new, y_new, w, b, &rctbPre);
            mse_2 = 1;
        }
        else
        {
            mse_1 = ADAS_RCTAB_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_rctab_x, stored_rctab_y, x_new, y_new, w, b, &rctbPre);
        }
        if ((mse_1 == 1) && (mse_2 == 1))
        {
            rctabCollisionFlag = 1;
        }
        // 使用新点 (x_new, y_new) 验证均方误差, 调用均方误差计算函数
        // x_new = 0, y_new = -2.0f;
        // mse_1 = ADAS_RCTAB_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_rctab_x, stored_rctab_y, x_new, y_new, w, b);
        // x_new = -2.0f, y_new = 3.0f;
        // mse_2 = ADAS_RCTAB_calculatedExtremum(pobjAlm, pobjPath, i, pVDY, type, stored_rctab_x, stored_rctab_y, x_new, y_new, w, b);
        // if ((mse_1 == 1) && (mse_2 == 1))
        // {
        //     rctabCollisionFlag = 1;
        // }
    }

    return rctabCollisionFlag;
}

/**
 * @brief 预测目标与本车是否有碰撞风险
 * @param i
 * @param pobjPath
 * @return uint8_t
 */
static uint8_t ADAS_RTCB_judgeCollisionzone(ALARM_OBJECT_T *pobjAlm,
                                            OBJ_NODE_STRUCT *pobjPath,
                                            uint8_t i,
                                            const VDY_Info_t *pVDY,
                                            OBJ_ADAS_TYPE_ENUM type) // 判断目标的预计轨迹会不会闯进门碰撞区
{
    uint8_t flag = 0;

#if 0
    float k, b;                  // y = k*x + b;
    float y1 = 0, y2 = 0;       // y = k*x + b;

    k = pobjPath[i].vy / pobjPath[i].vx;   // 先算k
    b = pobjPath[i].y - k * pobjPath[i].x; // b = y - k*x; 再算出b

    if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA))
    {
        y1 = k * (RCTB_BREAK_AREA_X_MAX + RCTB_WARNING_WIDTH_BUF) + b; // y = k*x + b; 最后算出 x = 0 时的y
        y2 = k * (RCTB_BREAK_AREA_X_MIN - RCTB_WARNING_WIDTH_BUF) + b; // y = k*x + b; 最后算出 x = -2 时的y

        /**
         * @brief y1是入点，y2是出点，入点y1小于y最小值RCTB_Break_Area_Y_Min不会相交，出点y2
         *      大于RCTB_Break_Area_Y_Max不会相交
         *
         */
        if ((y1 < RCTB_BREAK_AREA_Y_MIN) || (y2 > RCTB_BREAK_AREA_Y_MAX))
        {
        }
        else
        {
            flag = 1;
        }
        // 入点满足一定的位置关系也要制动 应对斜穿场景  主要是小角度斜穿
        if ((y1 >= (-0.5)) && (y1 <= 2) && (RCTAB_MIN_IA_CROSS(pobjPath[i].avgheadingAngle, 20)))
        {
            flag = 1;
        }

        // 是否针对小角度,目标已经在车后开始远离了,   就不再制动.

    }
    else
    {
        y1 = k * RCTB_BREAK_AREA_X_MAX + b; // y = k*x + b; 最后算出 x = 0 时的y
        y2 = k * RCTB_BREAK_AREA_X_MIN + b; // y = k*x + b; 最后算出 x = -2 时的y

        //
        if ((y1 < RCTB_BREAK_AREA_Y_MIN) || (y2 > RCTB_BREAK_AREA_Y_MAX))
        {
        }
        else
        {
            flag = 1;
        }
    }
#else // 线性回归计算
    flag = ADAS_RCTAB_LinearRegression_Flow(pobjAlm, pobjPath, i, pVDY, type);
#endif

    return flag;
}


/**
 * @brief 符合制动的目标. 通过各种限制条件加严  抑制其制动
 * @param i
 * @param pobjPath
 * @return uint8_t
 */
static uint8_t ADAS_RTCB_brakeinhibition(uint8_t i, OBJ_NODE_STRUCT *pobjPath, ALARM_OBJECT_T *pobjAlm, const VDY_Info_t *pVDY)    //目标是否还未越过车身
{
    // 根据横向速度估计
    uint8_t flag = 1;
    int k = 0, iCnt = 0;
    int totolrate = ADAS_HISTORY_NUM;
    float maxrange = 0.0f;
    float xescapettc = 0.0f;        // 横向逃离的TTC. 
    float ycollisionenterttc = 0.0f, ycollisionexitttc = 0.0f, ycollisionttc = 0.0f;     // 纵向碰撞的TTC.

    // 不能设置的太小,还要考虑到车身的宽度.
    if (pobjPath[i].boxLength < 2.0f)
    {
        maxrange = 2.0f;
    }
    else
    {
        maxrange = pobjPath[i].boxLength;
    }

    // 限制一下航迹框最大长度  避免航迹框被过大的估算.
    if (maxrange > 5.0f)
    {
        maxrange = 5.0f;
    }

    if (pobjPath[i].lockCrossVx > FLOAT_EPS)
    {
        totolrate = MIN(((uint8_t)(maxrange / (pobjPath[i].lockCrossVx * pobjAlm->avgFramerate))), (uint8_t)ADAS_HISTORY_NUM);
    }
    
    for (k = 0; k < ADAS_HISTORY_NUM; k++)
    {
        if (pobjPath[i].stored_last_x[k] < 1.0f)
        {
            iCnt++;
        }
    }
    // 车侧穿行目标已经越过车身
    if ((iCnt >= totolrate) && (pobjPath[i].lockCrossVx >= 1.0f))
    {
        flag = 0;
    }

    xescapettc = ((totolrate - iCnt) * pobjAlm->avgFramerate);      // 横向距离碰撞的TTC. 目标越过之后会变成负值
    if (rctbPre.rctb_enter_y > FLOAT_EPS)
    {
        ycollisionenterttc = ((rctbPre.rctb_enter_y - RCTB_Y_PRE_SAFEDISTANCE) / fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps));    // 碰撞点预留1米的安全距离.
    }
    if (rctbPre.rctb_exit_y > FLOAT_EPS)
    {
        ycollisionexitttc = ((rctbPre.rctb_exit_y - RCTB_Y_PRE_SAFEDISTANCE) / fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps));
    }
    ycollisionttc = MIN(ycollisionenterttc, ycollisionexitttc);

    // miss 大于一定值不制动.
    if (pobjPath[i].TrkObjMissCnt > 0)
    {
        flag = 0;
    }

    // 自车低速场景下, 要求目标的最大X距离要大于一定值.  避免近处偶发假点误报.
    if ((fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps) < 1.0f) && (pobjPath[i].maxrange < 3.0f))
    {
        flag = 0;
    }

    // 自车车速小于一定值时, 自车后方稍远的较快目标. 不制动. 
    if ((fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps) < 0.65f) && (pobjPath[i].x < 0.0f) && (pobjPath[i].y > 2.0f) && (pobjPath[i].vx > 2.0f))
    {
        flag = 0;
    }

    // 目标在正后方位置已经持续超过一定时间了.  也不制动.
    iCnt = 0;
    for (k = 0; k < ADAS_HISTORY_NUM; k++)
    {
        if (pobjPath[i].stored_last_x[k] < 1.0f)
        {
            iCnt++;
        }
    }
    // 历史跟踪点已经20帧以上后方区域了. 如果此时Y距离也比较远. 不制动. 
    if ((iCnt >= ADAS_HISTORY_NUM) && (pobjPath[i].y > 3.0f))
    {
        flag = 0;
    }

    // 目标历史的横向速度要满足一定的值  否则也不制动.
    // 对于低速目标, 最近10帧的横向速度都要达到一定的值才制动.
    iCnt = 0;
    if (pobjPath[i].stored_last_vx[ADAS_HISTORY_NUM / 2] > FLOAT_EPS)
    {
        for (k = 0; k < (ADAS_HISTORY_NUM / 2); k++)
        {
            if (pobjPath[i].stored_last_vx[k] < 1.0f)
            {
                iCnt++;
            }
        }
        // 先针对转动方向盘的场景控制多帧横向速度  避免把ENCAP的法规场景卡掉了.
        // 法规场景自车最小5kph.
        // 还存在一个问题是帧周期太高时, 行人横向距离比较近横穿时, 数组内横向速度可能会不满足.
        if (((fabsf(pVDY->pVDY_DynamicInfo->vdySteeringAngle) > 15.0f) || (fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps) < 1.0f)) && (iCnt > 2))
        {
            flag = 0;
        }
    }

    // 
    // 在 X 小于 横向一定距离时, 开始计算横向逃逸TTC 和 纵向 碰撞TTC   横向一定距离外没制动.  到后方时就尽量抑制其制动.
    // 这可能会容易导致漏制动. 尤其是横向高速目标. 横向的TTC都是非常小的. 纵向距离稍微检测的远一点. 纵向TTC就会偏大. 是否设置一个小范围内. 不严格要求TTC. 否则极易漏制动.
    if (pobjPath[i].x < RCTA_CROSS_LOCKVX_XDISTANCE)
    {
        if ((xescapettc > FLOAT_EPS) && (ycollisionttc > FLOAT_EPS))
        {
            if (ycollisionttc > (2.0f * xescapettc))
            {
                flag = 0;
            }
        }
    }

    return flag;
}

/**
 * @brief 判断ttm是否满足制动条件
 *
 * @param ttm 详见《IDD_FS_A30-10029》第13页解释
 * @return uint8_t 1：满足，0：不满足
 */
static uint8_t ADAS_RCTB_predictCollisionBaseTTM(float ttm)
{
    uint8_t flag = 0;

    if(ttm <= RCTB_TTC_MAX_TIME)
    {
        flag = 1;
    }

    return flag;
}

/**
 * @brief 检查目标是否在敏感区域，在此区域内的目标一定会发出报警
 * 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return true 目标在敏感区域
 * @return false 不在
 */
static bool ADAS_RCTA_checkTargetIsInSensitiveAreas(OBJ_NODE_STRUCT *pobjPath, uint8_t i)
{
    /**
     * @brief  通过起始航向角 以及目前起始位置确认是否启用必报区策略. 为抑制假点误报, 增加目标径向速度限制.
     */
    //  限制目标起始位置.  
    // if ((pobjPath[i].startX > RCTA_SENSITIVEAREAS_MIN_START_X) && (pobjPath[i].x >= RCTA_COLLISIO_NHAZARD_ZONE_MIN_X) && 
    //     (pobjPath[i].x <= 0.0) && (pobjPath[i].y >= - RCTA_DDCI_MAX) && (pobjPath[i].y <= RCTA_COLLISIO_NHAZARD_ZONE_Y) &&
    //     (pobjPath[i].v >= 1.0) && ((pobjPath[i].attrFlag & TRACK_STATUS_MOVING_BMP) != 0) && (1 == pobjPath[i].IsRctabNearObj))
    // {
    //     return true;
    // }
    // else
    // {
    //     return false;
    // }
    return false;       // RCTA必报区先返回false 不使用必报区. 可能会导致后方假点偶发误报
}

/**
 * @brief 检查目标在X方向上是否整体在靠近
 * 
 * @param pobjPath 目标相关结构体地址
 * @param size 所统计的帧数量
 * @return uint8_t 1：在靠近 0：不在靠近
 */
static uint8_t ADAS_RCTAB_checkTargetIsApproachingOnX(OBJ_NODE_STRUCT *pobjPath, uint8_t size)
{
    uint8_t i = 0U, num = 0U;
    uint8_t flag = 0;

    // 20帧存在切挡位等情况 stored_last_x 被清零 先确认数组中是有值的
    if (fabsf(pobjPath->stored_last_x[size - 1]) < FLOAT_EPS)
    {
        if ((pobjPath->vx >= (2.0f)) && (pobjPath->rangemaxdiffvalue > RCTB_CROSS_RANGEMAXDIFF)){
            flag = 1;
        }
    }

    for (i = 0U; i < (size-1U); i++){
        if (pobjPath->stored_last_x[i] <= pobjPath->stored_last_x[i+1])
        {
            num++;
        }
    }
    if ((pobjPath->boxLength >= RCTB_CROSS_OBJBIGTHRESHOLD) || (pobjPath->boxWidth >= RCTB_CROSS_OBJBIGTHRESHOLD)){
        if ((pobjPath->vx < RCTB_CROSS_LOW_SPEED) && (num >= (size * RCTB_CROSS_XOBJBIGNEARPERCENT)))      // 低速横穿车辆目标不判断最大位移差, 抑制低速横穿车辆目标中断.
        {
            flag = 1;
        }else if ((num >= (size * RCTB_CROSS_XOBJBIGNEARPERCENT)) && ((pobjPath->stored_last_x[size-1] - pobjPath->stored_last_x[0]) >= (RCTB_CROSS_XDIFFMINPERFRAME * size))){
            flag = 1;
        }
    }else if ((pobjPath->avgheadingAngle >= RCTA_ACTIVE_MIN_IA) && (pobjPath->avgheadingAngle <= (RCTA_ACTIVE_MIN_IA+30))){
        if ((num >= (size * RCTB_CROSS_XINCLINENEARPERCENT)) && ((pobjPath->stored_last_x[size-1] - pobjPath->stored_last_x[0]) >= (RCTB_CROSS_XDIFFMINPERFRAME * size * 0.9)))
        {
            flag = 1;
        }
    }else{
        if ((num >= (size * RCTB_CROSS_XNEARPERCENT)) && ((pobjPath->stored_last_x[size-1] - pobjPath->stored_last_x[0]) >= (RCTB_CROSS_XDIFFMINPERFRAME * size)))
        {
            flag = 1;
        }
    }

    return flag;
}

/**
 * @brief RCTA限制目标整体趋势一定要是靠近车辆
 * 
 * 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return uint8_t 1：是横穿，0：不是横穿
 */
static uint8_t ADAS_RCTAB_checkTargetIsNear(OBJ_NODE_STRUCT *pobjPath, uint8_t i)    //判断是否是标准的横穿目标
{
    // 1.历史轨迹具备横穿趋势  2. 航迹框大于一定值. 3.x方向测量不准确.  
    uint8_t flag = 0;
    pobjPath[i].IsRctabNearObj = 0U;
    if (pobjPath[i].lifeCycle >= ADAS_HISTORY_NUM){
        if ((ADAS_RCTAB_checkTargetIsApproachingOnX(&pobjPath[i], ADAS_HISTORY_NUM) != 0U) && 
            ((pobjPath[i].rangestartdiffvalue > RCTB_CROSS_RANGESTARTDIFF) || (pobjPath[i].rangemaxdiffvalue > RCTB_CROSS_RANGEMAXDIFF))){
            pobjPath[i].IsRctabNearObj = 1;
            pobjPath[i].LastRctabNearObjCnt = 1;
            flag = 1;
        }
        else if ((0 != pobjPath[i].LastRctabNearObjCnt) && (pobjPath[i].x < 0.2) && (pobjPath[i].x > (-VEHICLE_WIDTH_INFO)) && (++pobjPath[i].LastRctabNearObjCnt < RCTA_CROSS_IGNORE_CNT))
        {
            pobjPath[i].IsRctabNearObj = 1;
            flag = 1;
        }
        else
        {
            pobjPath[i].IsRctabNearObj = 0;
            pobjPath[i].LastRctabNearObjCnt = 0;
            flag = 0;
        }
    }
    else
    {   
        pobjPath[i].LastRctabNearObjCnt = 0;
    }
    // 补丁策略: 对于某些场景目标速度较高但起批非常晚的情况, 20帧之后目标就越过车身了,会导致完全不报RCTA.
    // 行人起步切ID时, 也会导致帧数不满足可能导致RCTA晚报或漏报
    if (pobjPath[i].lifeCycle < ADAS_HISTORY_NUM){
        if ((pobjPath[i].vx >= (2.0f)) && (pobjPath[i].rangemaxdiffvalue > RCTB_CROSS_RANGEMAXDIFF)){
            pobjPath[i].IsRctabNearObj = 1;
        }
    }
    // 当前有个情况是远距离目标, 刚起批时候横纵向速度可能不准 触发斜穿RCTA. 还未来得及20帧拟合曲线
    // 纵向距离较远 TTC大于 1.5的目标, 先不认为满足横穿趋势
    if (pobjPath[i].lifeCycle < ADAS_HISTORY_NUM){
        if ((pobjPath[i].y > (-RCTA_DDCI_MIN + 1)) && (fabsf(pobjPath[i].x / pobjPath[i].vx) >= ((float)RCTA_TTC_MAX_TIME / 2.0f)))
        {
            pobjPath[i].IsRctabNearObj = 0;
        }
    }

    // 同理 对于自车前方的目标也有可能会因为航向角不准确误报RCTA. 暂时分开处理 便于细节调参.
    if (pobjPath[i].lifeCycle < ADAS_HISTORY_NUM){
        if ((pobjPath[i].y < (-RCTA_DDCI_MAX - 1)) && (fabsf(pobjPath[i].x / pobjPath[i].vx) >= ((float)RCTA_TTC_MAX_TIME / 2.0f)))
        {
            pobjPath[i].IsRctabNearObj = 0;
        }
    }    

    return flag;
}

/**
 * @brief IA角度二次检测, 抑制小角度报警
 */
static uint8_t ADAS_RCTB_IADoubleCheck(OBJ_NODE_STRUCT *pobjPath, uint8_t i)
{
    uint8_t checkokflag = 1;

    #if (ADAS_RCTB_IADOUBLECHECK_EN == 1)
    // 上次未报警目标, 到车身正后方后, 非必报区策略下, 增加IA角限制 抑制小角度目标一开始由于航向角不准导致误报
    if((gAdasSpeedInkmph < 0.1) && (false == ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA))){
        if ((pobjPath[i].x < 0.5) && (pobjPath[i].headingAngleCTAB_L < 50)){
            checkokflag = 0;
        }
    }
    #endif

    return checkokflag;
}

/**
 * @brief 后方穿行预警处理
 *
 * @param i 循环计数
 * @param pobjPath 目标相关结构体地址
 * @param intercept 碰撞点
 * @param CarV 本车横/纵向速度
 * @return float ttm
 */
static float ADAS_RCTA_predictCollision(ALARM_OBJECT_T *pobjAlm, uint8_t i, OBJ_NODE_STRUCT *pobjPath, float intercept, float roadline)
{
    //uint8_t flag = 0U; //满足报警标志位：1—满足报警，0-不满足报警
    uint8_t speedflag = 0U;
    float speedbuf = 0;
    float minAnglebuffer = 0;
    float maxAnglebuffer = 0;
    float Width = - RCTA_DDCI_MIN + (fabsf(pobjPath[i].x) * tanf((RCTA_ACTIVE_MAX_IA - 90)*degtorad)); //蝴蝶翅膀x对应的y方向宽度
    float ttm = -1.0f; 
    //bool rctabAtTtmXFlag = true;

    if ((pobjPath[i].vx > 0.05) && (pobjPath[i].x > (RCTA_X_MIN))) //保证是靠近本车。注意：在车正后方运动，向左或向右都认为是靠近本车。
    {
        ADAS_RCTAB_checkTargetIsNear(pobjPath, i);

        ttm = ADAS_cacRCTABTTM(pobjAlm, pobjPath, i);

        ADAS_RCTA_recordX_AtTtmX(pobjPath, i, ttm);
    }

    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA)) //如果上一次正在报警，则增加报警判断的buff，防抖
    {
        speedbuf = RCTA_SPEED_BUF;
        minAnglebuffer = RCTA_ACTIVE_MIN_IA_BUF;
        maxAnglebuffer = RCTA_ACTIVE_MAX_IA_BUF;
    }
    else // 针对未报警目标
    {
        if (pobjPath[i].rctabX_AtTtmX < (-FLOAT_EPS))
        {
            //rctabAtTtmXFlag = false;
        }
    }

    if((pobjPath[i].absolutevabs >= RCTA_OBJ_MIN_SPEED - speedbuf) && pobjPath[i].absolutevabs <= (RCTA_OBJ_MAX_SPEED + speedbuf))
    {
        speedflag = 1;
    }

    if (fabsf(pobjPath[i].vx) > 0.1)
    {
        intercept = pobjPath[i].y + (pobjPath[i].x / tanf(pobjPath[i].avgheadingAngle * degtorad));
    }
    else
    {
        intercept = 15.0;
    }

    // 针对大角度的场景,需要补偿一定的碰撞点 穿行角度在一定角度的正负 20度内 补偿碰撞点
    // 标准场景最大是 135度.  大角度一般覆盖到 110度以上  150以下即可 所以针对RCTA的大角度可以设置为 130为基准
    if (RCTAB_MAX_IA_CROSS(pobjPath[i].avgheadingAngle, 20))
    {
        interceptMaxAngleUpperBuffer = (pobjPath[i].avgheadingAngle * 0.01); // 比如 120度 就补偿 1.2米  粗略补偿
    }
    else
    {
        interceptMaxAngleUpperBuffer = 0.0f;
    }

    // 针对小角度场景  碰撞点使用双倍buffer  小角度碰撞点波动大 可能触发中断
    if ((RCTAB_MIN_IA_CROSS(pobjPath[i].avgheadingAngle, 20) || (RCTAB_MAX_IA_CROSS(pobjPath[i].avgheadingAngle, 20))) && ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA))
    {
        interceptUpperBuffer = RCTA_DDCI_MIN_BUF *2;    //interceptUpperBuffer * 2;
    }

    if(speedflag != 0U)   //目标速度大小,为了抑制误报误刹，速度一票否决
    {
        if (
            (ttm <= (RCTA_TTC_MAX_TIME + ttcBufRctaUpper)) &&                         //ttm
            (ttm >= (RCTA_TTC_MIN_TIME + ttcBufRctaLower)) &&
			(pobjPath[i].rctabX_AtTtmX > (-FLOAT_EPS)) &&                                                         // 报警目标需在车身外，不考虑车身内报警，抑制正后方保rcta
            /*((roadline < MIN_SIDE_VALUE) ||
               ((roadline >= MIN_SIDE_VALUE) && (pobjPath[i].x <= roadline))) && */                 // 边线外动态点抑制.
            (pobjPath[i].range < (RCTA_LENGTH + rangeBuf)) &&                                       // RCTA报警区域最大宽度
            (pobjPath[i].y <= (Width + posBufYFar)) &&                                              // 动态y范围
            (intercept < (-RCTA_DDCI_MIN + interceptUpperBuffer + interceptMaxAngleUpperBuffer)) && // 碰撞点
            (intercept > (-RCTA_DDCI_MAX - interceptUpperBuffer)) &&
            (pobjPath[i].avgheadingAngle >= (RCTA_ACTIVE_MIN_IA + minAnglebuffer)) &&               // 轨迹夹角
            (pobjPath[i].avgheadingAngle <= (RCTA_ACTIVE_MAX_IA + maxAnglebuffer)) &&
            (1 == pobjPath[i].IsRctabNearObj))
        {
            if (ADAS_RCTB_IADoubleCheck(pobjPath, i))
            {
                //flag = 1;
            }
            else
            {
                ttm = -1;
            }
        }
        else if(ADAS_RCTA_checkTargetIsInSensitiveAreas(pobjPath, i))   //在敏感区的目标一定会报警
        {
            //flag = 1;
        }
        else
        {
            ttm = -1;   //不报警，输出非法ttm
        }
    }
    else
    {
        ttm = -1;   //不报警，输出非法ttm
    }

    return ttm;
}

/**
 * @brief 延迟一定的帧数退出
 */
static void ADAS_RCTA_OverAlmDelay(uint8_t i, OBJ_NODE_STRUCT *pobjPath, uint8_t *flag)
{
    // 退出报警前，延迟5帧
    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA) && pobjPath[i].overRctaAlarmDly < RCTA_OVER_ALARM_DELAY)
    {
        ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_RCTA);
        pobjPath[i].overRctaAlarmDly++;
        *flag = 1U;
    }
    else
    {
        ADAS_clearWarning(i, pobjPath, ALARM_ACTIVE_RCTA);
        pobjPath[i].overRctaAlarmDly = 0;
        *flag = 0U;
    }
}

/**
 * @brief 退出报警前的操作，涉及延迟退出等
 *
 * @param pobjPath 目标相关结构体地址
 * @param flag 报警标志位
 */
static void ADAS_RCTA_QuitWarning(uint8_t i, OBJ_NODE_STRUCT *pobjPath, uint8_t *flag)
{
    float time = 0.0f;

    // 雷达延迟退出策略: 避免跟踪波动导致报警中断.
    ADAS_RCTA_OverAlmDelay(i, pobjPath, flag);

    // 雷达盲区延迟退出策略.
    if(((ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA))) && (pobjPath[i].angle < 0) && (pobjPath[i].y < TriangleH))  //落入雷达盲区，会比理论盲区大一些
    {
        (void)memcpy(gtempobjPath, &pobjPath[i], sizeof (OBJ_NODE_STRUCT));
        // 自车正后方可能存在横向速度骤降的问题, VX偏小会导致预测退出帧数偏多, 导致RCTA退出报警晚, 取目标过车身之前的Vx, 会缓解此现象
        if (((fabsf(gtempobjPath[0].lockCrossVx) > FLOAT_EPS) && (fabsf(gtempobjPath[0].vx) > FLOAT_EPS)))
        {
            time = (-RCTA_X_MIN + gtempobjPath[0].x) / gtempobjPath[0].lockCrossVx;   //到达退出线预计时间， BYD需求RCTA在目标完全离开车辆正后方后再结束报警，会有探测不到的区域，所以需要预测

            if(time > 0)
            {
                gtempobjPath[0].alarmDlyThr = floor(time / 0.05) + 1;

                if(gtempobjPath[0].alarmDlyThr > RCTA_DELAY_MAX)
                {
                    gtempobjPath[0].alarmDlyThr = RCTA_DELAY_MAX;
                }

                gadasFunctionState.adasRCTAWarning = (uint8_t)ADAS_WARNING_LEVEL1;
            }
        }
    }
}

/**
 * @brief 获取RCTAB内部变量
 * @return OBJ_NODE_STRUCT* 
 */
OBJ_NODE_STRUCT *getgtempobjPath(void)
{
    return &gtempobjPath[0];
}

/**
 * @brief
 *
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @param BSDVelSpeedVal 本车车速（kph）
 */
static void ADAS_RCTB_runMain(ALARM_OBJECT_T *pobjAlm, uint8_t i, OBJ_NODE_STRUCT *pobjPath, const VDY_Info_t *pVDY)
{
    uint8_t CollisionFlagBaseTTM = 0;
    //uint8_t CollisionFlagBasePosition = 0;
    uint8_t CollisionFlagBaseDistance = 0;      // 控制刹停距离
    uint8_t CollisionFlagBaseRisk = 0;          // 是否会碰撞预测
    uint8_t CollisionFlagBaseSpan = 0;          // 是否已经越过车身, 已越过车身的不制动

    float ttm = ADAS_cacRCTABTTM(pobjAlm, pobjPath, i); // 计算穿行场景的TTM

    float timeVehicleBrk = fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps) / (-RCTB_START_DEC_VAL) + RCTB_RESPONSE_DELAY;                //计算刹车时间 
    float distanceBrk = ((pVDY->pVDY_DynamicInfo->vdySpeedInmps * pVDY->pVDY_DynamicInfo->vdySpeedInmps) / (2 * (-RCTB_START_DEC_VAL))) + (RCTB_RESPONSE_DELAY * fabsf(pVDY->pVDY_DynamicInfo->vdySpeedInmps));    // 计算制动距离

    CollisionFlagBaseTTM = ADAS_RCTB_predictCollisionBaseTTM(ttm) ;    //基于TTM的碰撞预测
    //CollisionFlagBasePosition = ADAS_RCTB_predictCollisionBasePosition(i, pobjPath, timeVehicleBrk);   //基于相对位置的碰撞预测,保证一个刹停后的安全距离
    CollisionFlagBaseRisk = ADAS_RTCB_judgeCollisionzone(pobjAlm, pobjPath, i, pVDY, ADAS_TYPE_RCTB);             // 判断是否会碰撞
    CollisionFlagBaseDistance = ADAS_RCTB_controlBrkDistance(i, pobjPath, timeVehicleBrk, distanceBrk, ttm, pobjAlm);      // 控制一个较小的刹停距离
    CollisionFlagBaseSpan = ADAS_RTCB_brakeinhibition(i, pobjPath, pobjAlm, pVDY);                                         // 是否触发制动抑制策略.

    if ((1U == CollisionFlagBaseRisk) && (1U == CollisionFlagBaseTTM) && (1U == CollisionFlagBaseDistance) && (1U == CollisionFlagBaseSpan))
    {
        ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_RCTB);

        CTB_EDRTargetI = i;    //记录刹车事件发生时的目标id和生命周期
        CTB_EDRTargetLifeCycle = pobjPath[i].lifeCycle;
    }
    else
    {
        ADAS_clearWarning(i, pobjPath, ALARM_ACTIVE_RCTB);
    }
}

/**
 * @brief 检查目标的质量，主要是过滤假点及大部分静态点，与功能规范无关
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return true 有效
 * @return false 无效
 */
static bool ADAS_RCTA_checkTargetQuality(OBJ_NODE_STRUCT *pobjPath, uint8_t i, ALARM_OBJECT_T *pobjAlm)
{
    bool flag = true;   //默认目标有效
    uint8_t activeCnt = 0, k = 0;


    if((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) == 0U)   //只保留动态点
    {
        return false;
    }

    // 未报警目标, 具备TRACK_STATUS_MOVED_BMP属性 但是不具备TRACK_STATUS_MOVING_BMP 属性 
    // 主要是防止假点运动了以下停止了 但是在车身近处又有横向速度误报
    // 针对未报警目标是因为低速目标在自车后方可能转静态  导致不报警
    if (((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVED_BMP) != 0U)  &&
        ((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) == 0U) &&
        (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA) == false))
    {
        return false;
    }

    if(ADAS_RCTA_checkTrkObjReliability(i, pobjPath) == false)   //目标置信度低
    {
        if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA) == false) //上次不在报警
        {

        }
        else
        {
            ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA);
        }

        return false;
    }

    // 均值径向速度不满足最低标准, 也认为不满足
    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA) == false)
    {
        activeCnt = 0;
        // 对于低速目标, 最近20帧内至少有一半时间达到1m/s才报警.
        if (pobjPath[i].stored_last_vx[ADAS_HISTORY_NUM - 1] > FLOAT_EPS)
        {
            if (pobjPath[i].vx < 1.0f)
            {
                for (k = 0; k < ADAS_HISTORY_NUM; k++)
                {
                    if (pobjPath[i].stored_last_vx[k] < 1.0f)
                    {
                        activeCnt++;
                    }
                }
                if (activeCnt >= (ADAS_HISTORY_NUM / 2))
                {
                    flag = false;
                }
            }
        }
    }
    
    // R挡位正后方来车触发RCTA  根据正后方特征抑制
    // 仅限自车静止时判断
    if ((ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA) == false) && (fabsf(pobjAlm->BSDVelSpeedVal) < FLOAT_EPS))
    {
        activeCnt = 0;
        // 目标的20帧X一直在自车后方   还未报警时  抑制报警
        if (fabsf(pobjPath[i].stored_last_x[ADAS_HISTORY_NUM - 1]) > FLOAT_EPS)
        {
            if ((pobjPath[i].x < 1.0f) && (pobjPath[i].x > -2.0f))
            {
                for (k = 0; k < ADAS_HISTORY_NUM; k++)
                {
                    if ((pobjPath[i].stored_last_x[k] <= 1.0f) && (pobjPath[i].stored_last_x[k] >= -2.0f))
                    {
                        activeCnt++;
                    }
                }
                if (activeCnt >= (ADAS_HISTORY_NUM * 0.9))
                {
                    flag = false;
                }
            }
        }
    }    

    return flag;
}

/**
 * @brief RCTA主函数
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param i 循环计数
 * @param isRctbAvailable rctb使能位
 * @return true
 * @return false
 */
static bool ADAS_RCTA_runMain(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, uint8_t i, const VDY_Info_t *pVDY)
{
    bool  alarmFlag = 0;
    float intercept = 10.0f; //碰撞点
    float r, theta; //计算航迹框上某点位置所使用的变量
    // float tempx = 0, tempy = 0;
    float minttm = -1.0f; //用于存储TTM最小目标的信息
    float pointTTM = 0;   //跟踪点的TTM
    float targetHeadTTM = 0;
    float targetCenterTTM = 0;
    float targetTailTTM = 0;
    float tmporgx = 0.0, tmporgy = 0.0;

    float roadline = 0.0f;
    roadline = pobjAlm->RoadLine;
    tmporgx = pobjPath[i].x;
    tmporgy = pobjPath[i].y;

    // tempx = pobjPath[i].x;
    // tempy = pobjPath[i].y;

    if( ADAS_RCTA_checkTargetQuality(pobjPath, i, pobjAlm) == false )   //低质量目标直接返回
    {
        return false;
    }

    // 该目标已经报警，增加buffer
    if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA))
    {
        posBufYFar = RCTA_LENGTH_Y_BUF;
        interceptUpperBuffer = RCTA_DDCI_MIN_BUF;
        rangeBuf = RCTA_LENGTH_BUF;
        ttcBufRctaLower = RCTA_TTC_MIN_TIME_BUF;
        ttcBufRctaUpper = RCTA_TTC_MAX_TIME_BUF;
    }
    else
    {
        interceptUpperBuffer = 0.0;
        posBufYFar = 0.0;
        rangeBuf = 0.0;
        ttcBufRctaLower = 0.0;
        ttcBufRctaUpper = 0.0;
    }

    //如果该id上一次在报警，且距离较近，则根据航迹框，来替代当前目标位置，去做报警预测，防止车身上未起跟踪点而影响报警
    if(((ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA)))&&(pobjPath[i].range <= RCTA_MULTIPOINTS_MINR))
    {
        pointTTM = ADAS_RCTA_predictCollision(pobjAlm, i, pobjPath, intercept, roadline); // 算原始的跟踪点

        if(pointTTM >= 0)
        {
            // tempx = pobjPath[i].x;
            // tempy = pobjPath[i].y;
            minttm = pointTTM;
        }

        r = pobjPath[i].boxLength / 2;
        theta = atanf(pobjPath[i].vy / pobjPath[i].vx);

        pobjPath[i].x = pobjPath[i].boxCenterX - (r * cosf(theta)); //航迹框头的中心点
        pobjPath[i].y = pobjPath[i].boxCenterY + (r * sinf(theta));

        targetHeadTTM = ADAS_RCTA_predictCollision(pobjAlm, i, pobjPath, intercept, roadline);

        if((targetHeadTTM >= 0) && (((targetHeadTTM < minttm) && (minttm > FLOAT_EPS)) || (minttm < FLOAT_EPS)))
        {
            // tempx = pobjPath[i].x;
            // tempy = pobjPath[i].y;
            minttm = targetHeadTTM;
        }

        pobjPath[i].x = pobjPath[i].boxCenterX; //航迹框中心
        pobjPath[i].y = pobjPath[i].boxCenterY;

        targetCenterTTM = ADAS_RCTA_predictCollision(pobjAlm, i, pobjPath, intercept, roadline);

        if((targetCenterTTM >= 0) && (((targetCenterTTM < minttm) && (minttm > FLOAT_EPS))  || (minttm < FLOAT_EPS)))
        {
            // tempx = pobjPath[i].x;
            // tempy = pobjPath[i].y;
            minttm = targetCenterTTM;
        }

        pobjPath[i].x = pobjPath[i].boxCenterX + (r * cosf(theta)); //航迹框尾巴的中心点
        pobjPath[i].y = pobjPath[i].boxCenterY - (r * sinf(theta));

        targetTailTTM = ADAS_RCTA_predictCollision(pobjAlm, i, pobjPath, intercept, roadline);

        if((targetTailTTM >= 0) && (((targetTailTTM < minttm) && (minttm > FLOAT_EPS))  || (minttm < FLOAT_EPS)))
        {
            // tempx = pobjPath[i].x;
            // tempy = pobjPath[i].y;
            minttm = targetTailTTM;
        }
        // 对于已报警目标在车辆正后方时, 航迹框不准, 测速偏差等原因都可能导致ttm不满足. 此时临界的TTM可能导致报警中断后再次触发.
        // 对车辆后方特殊处理. 同时给出最大抖动次数
        if (minttm < FLOAT_EPS)
        {
            // 已报警目标, 跟踪点坐落在车后方区域, 维持报警. 防止目标在车尾中断. 同时给与最大次数限制
            if ((tmporgx < RCTA_BEHIND_MIN_X) && (tmporgx > (-VEHICLE_WIDTH_INFO)) && (tmporgy < RCTA_NCALC_TTM_ZONE_Y))
            {   
                if (pobjPath[i].overRctaBehingCnt < MAX_CHAR){
                    pobjPath[i].overRctaBehingCnt++;
                }
                if (pobjPath[i].overRctaBehingCnt < RCTA_BEHIND_DELAY_MAX){
                    minttm = RCTA_TTC_MIN_TIME;
                }
            }
        }
    }
    else
    {
        // tempx = pobjPath[i].x;
        // tempy = pobjPath[i].y;

        minttm = ADAS_RCTA_predictCollision(pobjAlm, i, pobjPath, intercept, roadline);
    }

    if(minttm >= 0) //ttm为正常值，则表示已满足报警条件
    {
        alarmFlag = true;

        ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_RCTA);
    }
    else
    {
        ADAS_RCTA_QuitWarning(i, pobjPath, (uint8_t *)&alarmFlag);
    }

    if (ADAS_checkBit(pobjPath[i].alarmType, ALARM_ACTIVE_RCTA) && (gadasFunctionState.adasRCTBFuncState == (uint8_t)RCTB_FUNC_STATE_ACTIVE))
    {
		pobjPath[i].x = tmporgx;  //还原跟踪点的x，y
        pobjPath[i].y = tmporgy;

        ADAS_RCTB_runMain(pobjAlm, i, pobjPath, pVDY);
    }
    else
    {
        ADAS_clearWarning(i, pobjPath, ALARM_ACTIVE_RCTB);
    }

    return alarmFlag;
}


/**
 * @brief RCTAB主函数
 *
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 目标相关结构体地址
 * @param pVDY 车身数据
 * @param pSlaveRadar 从雷达信息
 * @return uint8_t 报警标志位
 */
uint32_t ADAS_RCTAB_runMain(ALARM_OBJECT_T *pobjAlm
                                  , OBJ_NODE_STRUCT *pobjPath
                                  , const VDY_Info_t *pVDY
                                  , const SlaveRadarWarningsStatus *pSlaveRadar)
{
    uint8_t i = 0U;
    uint32_t almStsFlag = 0;

    for (i = 0U; i < ADAS_TRK_OBJNUM; i++)
    {
        if (pobjPath[i].status == OBJ_STATUS_VALID)
        {
            if (pobjPath[i].lifeCycle < RCTA_LIFECYCLETHR)
            {
                continue;
            }

            if (ADAS_RCTA_runMain(pobjAlm, pobjPath, i, pVDY) != 0U)
            {
                if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA))
                {
                    almStsFlag |= (uint32_t)RCTA_ALM_FLAG_MARK;

                    if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTB))
                    {
                        almStsFlag |= (uint32_t)RCTB_ALM_FLAG_MARK;
                    }
                }
            }
        }
        else
        {
            ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTA);
            ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCTB);
        }
    }

    if ((almStsFlag == 0) && (gtempobjPath[0].alarmDlyThr > 0)) //需要延迟报警，例如比亚迪要求行人到另一侧车尾才退出报警
    {
        gadasFunctionState.adasRCTAWarning = (uint8_t)ADAS_WARNING_LEVEL1;
        gtempobjPath[0].alarmDlyThr --;
        gadasFunctionState.adasRCTAAlarmObjID = 128U;    //与上位机协定，报警ID为128时，为特殊情况
    }

    #if(ALARM_TYPE_EDR_EN == 1)
    if(gadasFunctionState.adasRCTBWarning)  //功能触发则记录数据
    {
        CTB_EDRTargetLifeCycle = pobjPath[CTB_EDRTargetI].lifeCycle;

        float ttm = ADAS_cacTTM(pobjAlm, pobjPath, CTB_EDRTargetI);
        ADAS_recordEDRData(pobjPath, ttm, CTB_EDRTargetI);
    }
    else if(gadasFunctionState.adasRCTBState)   //已不满足触发条件，但还在刹车退出前，继续记录同一个id的跟踪点
    {
        if((pobjPath[CTB_EDRTargetI].lifeCycle - CTB_EDRTargetLifeCycle) == 1)  //周期连续递增，则认为是同一个目标，否则认为已切id，目标跟丢
        {
            CTB_EDRTargetLifeCycle = pobjPath[CTB_EDRTargetI].lifeCycle;  //更新最后一次记录时目标的生命周期
            float ttm = ADAS_cacTTM(pobjAlm, pobjPath, CTB_EDRTargetI);
            ADAS_recordEDRData(pobjPath, ttm, CTB_EDRTargetI);
        }
        else
        {
            memset(&EDRDataCTB, 0, sizeof(EDRDataCTB));
        }
    }
    else
    {
        memset(&EDRDataCTB, 0, sizeof(EDRDataCTB)); //清楚EDR数据，正常情况下也会周期清楚
    }
    #endif

    return almStsFlag;
}
