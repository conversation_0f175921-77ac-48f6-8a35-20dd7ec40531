#ifndef Py_INTERNAL_UNICODEOBJECT_GENERATED_H
#define Py_INTERNAL_UNICODEOBJECT_GENERATED_H
#ifdef __cplusplus
extern "C" {
#endif

#ifndef Py_BUILD_CORE
#  error "this header requires Py_BUILD_CORE define"
#endif

/* The following is auto-generated by Tools/build/generate_global_objects.py. */
static inline void
_PyUnicode_InitStaticStrings(PyInterpreterState *interp) {
    PyObject *string;
    string = &_Py_ID(CANCELLED);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(FINISHED);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(False);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(JSONDecodeError);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(PENDING);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(Py_Repr);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(TextIOWrapper);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(True);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(WarningMessage);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_WindowsConsoleIO);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__IOBase_closed);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__abc_tpflags__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__abs__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__abstractmethods__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__add__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__aenter__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__aexit__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__aiter__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__all__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__and__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__anext__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__annotations__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__args__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__asyncio_running_event_loop__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__await__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__bases__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__bool__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__buffer__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__build_class__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__builtins__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__bytes__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__call__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__cantrace__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__class__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__class_getitem__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__classcell__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__classdict__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__classdictcell__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__complex__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__contains__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__copy__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__ctypes_from_outparam__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__del__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__delattr__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__delete__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__delitem__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__dict__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__dictoffset__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__dir__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__divmod__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__doc__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__enter__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__eq__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__exit__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__file__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__float__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__floordiv__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__format__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__fspath__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__ge__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__get__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__getattr__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__getattribute__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__getinitargs__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__getitem__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__getnewargs__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__getnewargs_ex__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__getstate__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__gt__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__hash__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__iadd__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__iand__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__ifloordiv__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__ilshift__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__imatmul__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__imod__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__import__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__imul__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__index__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__init__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__init_subclass__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__instancecheck__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__int__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__invert__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__ior__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__ipow__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__irshift__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__isabstractmethod__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__isub__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__iter__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__itruediv__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__ixor__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__le__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__len__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__length_hint__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__lltrace__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__loader__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__lshift__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__lt__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__main__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__matmul__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__missing__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__mod__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__module__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__mro_entries__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__mul__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__name__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__ne__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__neg__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__new__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__newobj__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__newobj_ex__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__next__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__notes__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__or__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__orig_class__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__origin__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__package__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__parameters__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__path__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__pos__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__pow__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__prepare__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__qualname__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__radd__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rand__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rdivmod__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__reduce__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__reduce_ex__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__release_buffer__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__repr__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__reversed__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rfloordiv__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rlshift__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rmatmul__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rmod__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rmul__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__ror__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__round__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rpow__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rrshift__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rshift__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rsub__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rtruediv__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__rxor__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__set__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__set_name__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__setattr__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__setitem__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__setstate__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__sizeof__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__slotnames__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__slots__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__spec__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__str__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__sub__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__subclasscheck__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__subclasshook__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__truediv__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__trunc__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__type_params__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__typing_is_unpacked_typevartuple__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__typing_prepare_subst__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__typing_subst__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__typing_unpacked_tuple_args__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__warningregistry__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__weaklistoffset__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__weakref__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(__xor__);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_abc_impl);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_abstract_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_active);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_annotation);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_anonymous_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_argtypes_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_as_parameter_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_asyncio_future_blocking);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_blksize);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_bootstrap);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_check_retval_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_dealloc_warn);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_feature_version);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_fields_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_finalizing);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_find_and_load);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_fix_up_module);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_flags_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_get_sourcefile);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_handle_fromlist);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_initializing);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_io);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_is_text_encoding);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_length_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_limbo);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_lock_unlock_module);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_loop);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_needs_com_addref_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_pack_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_restype_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_showwarnmsg);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_shutdown);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_slotnames);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_strptime_datetime);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_swappedbytes_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_type_);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_uninitialized_submodules);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_warn_unawaited_coroutine);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(_xoptions);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(a);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(abs_tol);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(access);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(add);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(add_done_callback);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(after_in_child);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(after_in_parent);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(aggregate_class);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(alias);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(append);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(arg);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(argdefs);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(args);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(arguments);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(argv);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(as_integer_ratio);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(ast);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(attribute);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(authorizer_callback);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(autocommit);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(b);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(backtick);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(base);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(before);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(big);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(binary_form);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(block);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(bound);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(buffer);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(buffer_callback);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(buffer_size);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(buffering);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(buffers);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(bufsize);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(builtins);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(byteorder);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(bytes);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(bytes_per_sep);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(c);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(c_call);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(c_exception);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(c_return);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(cached_statements);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(cadata);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(cafile);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(call);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(call_exception_handler);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(call_soon);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(cancel);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(capath);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(category);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(cb_type);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(certfile);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(check_same_thread);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(clear);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(close);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(closed);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(closefd);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(closure);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_argcount);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_cellvars);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_code);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_consts);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_exceptiontable);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_filename);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_firstlineno);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_flags);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_freevars);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_kwonlyargcount);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_linetable);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_name);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_names);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_nlocals);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_posonlyargcount);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_qualname);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_stacksize);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(co_varnames);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(code);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(command);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(comment_factory);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(compile_mode);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(consts);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(context);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(contravariant);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(cookie);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(copy);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(copyreg);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(coro);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(count);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(covariant);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(cwd);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(d);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(data);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(database);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(decode);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(decoder);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(default);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(defaultaction);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(delete);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(depth);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(detect_types);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(deterministic);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(device);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(dict);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(dictcomp);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(difference_update);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(digest);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(digest_size);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(digestmod);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(dir_fd);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(discard);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(dispatch_table);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(displayhook);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(dklen);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(doc);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(dont_inherit);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(dst);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(dst_dir_fd);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(duration);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(e);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(eager_start);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(effective_ids);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(element_factory);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(encode);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(encoding);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(end);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(end_lineno);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(end_offset);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(endpos);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(entrypoint);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(env);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(errors);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(event);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(eventmask);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(exc_type);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(exc_value);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(excepthook);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(exception);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(existing_file_name);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(exp);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(extend);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(extra_tokens);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(facility);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(factory);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(false);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(family);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fanout);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fd);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fd2);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fdel);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fget);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(file);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(file_actions);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(filename);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fileno);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(filepath);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fillvalue);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(filters);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(final);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(find_class);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fix_imports);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(flags);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(flush);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(follow_symlinks);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(format);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(frequency);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(from_param);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fromlist);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fromtimestamp);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fromutc);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(fset);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(func);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(future);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(generation);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(genexpr);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(get);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(get_debug);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(get_event_loop);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(get_loop);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(get_source);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(getattr);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(getstate);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(gid);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(globals);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(groupindex);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(groups);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(handle);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(hash_name);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(header);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(headers);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(hi);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(hook);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(id);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(ident);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(ignore);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(imag);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(importlib);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(in_fd);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(incoming);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(indexgroup);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(inf);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(infer_variance);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(inheritable);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(initial);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(initial_bytes);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(initial_value);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(initval);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(inner_size);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(input);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(insert_comments);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(insert_pis);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(instructions);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(intern);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(intersection);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(is_running);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(isatty);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(isinstance);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(isoformat);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(isolation_level);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(istext);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(item);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(items);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(iter);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(iterable);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(iterations);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(join);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(jump);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(keepends);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(key);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(keyfile);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(keys);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(kind);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(kw);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(kw1);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(kw2);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(lambda);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(last);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(last_exc);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(last_node);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(last_traceback);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(last_type);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(last_value);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(latin1);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(leaf_size);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(len);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(length);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(level);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(limit);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(line);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(line_buffering);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(lineno);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(listcomp);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(little);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(lo);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(locale);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(locals);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(logoption);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(loop);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(mapping);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(match);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(max_length);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(maxdigits);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(maxevents);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(maxmem);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(maxsplit);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(maxvalue);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(memLevel);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(memlimit);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(message);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(metaclass);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(metadata);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(method);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(mod);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(mode);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(module);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(module_globals);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(modules);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(mro);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(msg);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(mycmp);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(n);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(n_arg);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(n_fields);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(n_sequence_fields);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(n_unnamed_fields);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(name);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(name_from);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(namespace_separator);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(namespaces);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(narg);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(ndigits);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(new_file_name);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(new_limit);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(newline);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(newlines);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(next);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(nlocals);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(node_depth);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(node_offset);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(ns);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(nstype);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(nt);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(null);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(number);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(obj);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(object);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(offset);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(offset_dst);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(offset_src);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(on_type_read);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(onceregistry);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(only_keys);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(oparg);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(opcode);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(open);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(opener);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(operation);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(optimize);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(options);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(order);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(origin);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(out_fd);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(outgoing);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(overlapped);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(owner);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(p);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(pages);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(parent);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(password);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(path);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(pattern);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(peek);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(persistent_id);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(persistent_load);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(person);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(pi_factory);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(pid);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(policy);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(pos);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(pos1);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(pos2);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(posix);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(print_file_and_line);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(priority);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(progress);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(progress_handler);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(progress_routine);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(proto);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(protocol);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(ps1);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(ps2);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(query);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(quotetabs);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(r);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(raw);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(read);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(read1);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(readable);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(readall);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(readinto);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(readinto1);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(readline);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(readonly);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(real);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(reducer_override);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(registry);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(rel_tol);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(release);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(reload);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(repl);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(replace);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(reserved);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(reset);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(resetids);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(return);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(reverse);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(reversed);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(s);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(salt);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(sched_priority);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(scheduler);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(seek);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(seekable);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(selectors);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(self);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(send);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(sep);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(sequence);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(server_hostname);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(server_side);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(session);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(setcomp);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(setpgroup);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(setsid);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(setsigdef);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(setsigmask);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(setstate);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(shape);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(show_cmd);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(signed);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(size);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(sizehint);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(skip_file_prefixes);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(sleep);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(sock);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(sort);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(sound);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(source);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(source_traceback);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(src);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(src_dir_fd);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(stacklevel);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(start);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(statement);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(status);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(stderr);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(stdin);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(stdout);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(step);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(steps);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(store_name);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(strategy);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(strftime);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(strict);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(strict_mode);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(sub_key);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(symmetric_difference_update);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(tabsize);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(tag);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(target);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(target_is_directory);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(task);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(tb_frame);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(tb_lasti);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(tb_lineno);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(tb_next);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(tell);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(template);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(term);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(text);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(threading);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(throw);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(timeout);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(times);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(timetuple);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(top);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(trace_callback);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(traceback);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(trailers);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(translate);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(true);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(truncate);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(twice);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(txt);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(type);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(type_params);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(tz);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(tzname);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(uid);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(unlink);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(unraisablehook);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(uri);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(usedforsecurity);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(value);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(values);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(version);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(volume);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(warnings);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(warnoptions);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(wbits);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(week);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(weekday);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(which);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(who);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(withdata);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(writable);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(write);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(write_through);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(x);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(year);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
    string = &_Py_ID(zdict);
    assert(_PyUnicode_CheckConsistency(string, 1));
    _PyUnicode_InternInPlace(interp, &string);
}
/* End auto-generated code */
#ifdef __cplusplus
}
#endif
#endif /* !Py_INTERNAL_UNICODEOBJECT_GENERATED_H */
