VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: SRR_FR SRR_FL ADAS SRR_RR SRR_RL
VAL_TABLE_ ObjectLaneID 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_TABLE_ ObjectRefPointPos 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;


BO_ 1029 SRR_Front_LockTarget06: 6 SRR_FR
 SG_ Lock_Target_Type_06 : 44|4@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_State_06 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_06 : 47|2@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_06 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_06 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_06 : 23|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Abscissa_06 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Lane_ID_06 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 1028 SRR_Front_LockTarget05: 6 SRR_FR
 SG_ Lock_Target_Type_05 : 44|4@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_State_05 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_05 : 47|2@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_05 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_05 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_05 : 23|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Abscissa_05 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Lane_ID_05 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 1027 SRR_Front_LockTarget04: 6 SRR_FR
 SG_ Lock_Target_Type_04 : 44|4@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_State_04 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_04 : 47|2@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_04 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_04 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_04 : 23|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Abscissa_04 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Lane_ID_04 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 1026 SRR_Front_LockTarget03: 6 SRR_FR
 SG_ Lock_Target_Type_03 : 44|4@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_State_03 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_03 : 47|2@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_03 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_03 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_03 : 23|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Abscissa_03 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Lane_ID_03 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 1025 SRR_Front_LockTarget02: 6 SRR_FR
 SG_ Lock_Target_Type_02 : 44|4@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_State_02 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_02 : 47|2@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_02 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_02 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_02 : 23|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Abscissa_02 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Lane_ID_02 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 1024 SRR_Front_LockTarget01: 6 SRR_FR
 SG_ Lock_Target_Type_01 : 44|4@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_State_01 : 45|1@0+ (1,0) [0|1] ""  ADAS
 SG_ Lock_Target_MovingState_01 : 47|2@0+ (1,0) [0|0] ""  ADAS
 SG_ Lock_Target_LatRelative_Spd_01 : 39|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_LonRelative_Spd_01 : 31|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Ordinate_01 : 23|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Abscissa_01 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ Lock_Target_Lane_ID_01 : 7|8@0+ (1,0) [0|255] ""  ADAS

BO_ 576 SRR_FR_ObjectList01: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_01 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_01 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_01 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_01 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_01 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_01 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_01 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_01 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_01 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_01 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_01 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_01 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_01 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_01 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_01 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_01 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_01 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_01 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_01 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_01 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 577 SRR_FR_ObjectList02: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_02 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_02 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_02 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_02 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_02 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_02 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_02 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_02 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_02 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_02 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_02 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_02 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_02 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_02 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_02 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_02 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_02 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_02 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_02 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_02 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 578 SRR_FR_ObjectList03: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_03 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_03 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_03 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_03 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_03 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_03 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_03 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_03 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_03 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_03 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_03 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_03 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_03 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_03 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_03 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_03 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_03 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_03 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_03 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_03 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 579 SRR_FR_ObjectList04: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_04 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_04 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_04 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_04 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_04 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_04 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_04 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_04 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_04 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_04 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_04 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_04 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_04 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_04 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_04 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_04 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_04 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_04 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_04 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_04 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 580 SRR_FR_ObjectList05: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_05 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_05 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_05 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_05 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_05 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_05 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_05 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_05 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_05 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_05 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_05 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_05 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_05 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_05 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_05 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_05 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_05 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_05 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_05 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_05 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 581 SRR_FR_ObjectList06: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_06 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_06 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_06 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_06 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_06 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_06 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_06 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_06 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_06 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_06 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_06 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_06 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_06 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_06 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_06 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_06 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_06 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_06 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_06 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_06 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 582 SRR_FR_ObjectList07: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_07 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_07 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_07 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_07 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_07 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_07 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_07 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_07 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_07 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_07 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_07 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_07 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_07 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_07 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_07 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_07 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_07 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_07 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_07 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_07 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 583 SRR_FR_ObjectList08: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_08 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_08 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_08 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_08 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_08 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_08 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_08 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_08 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_08 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_08 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_08 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_08 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_08 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_08 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_08 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_08 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_08 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_08 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_08 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_08 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 584 SRR_FR_ObjectList09: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_09 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_09 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_09 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_09 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_09 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_09 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_09 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_09 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_09 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_09 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_09 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_09 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_09 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_09 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_09 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_09 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_09 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_09 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_09 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_09 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 585 SRR_FR_ObjectList10: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_10 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_10 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_10 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_10 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_10 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_10 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_10 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_10 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_10 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_10 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_10 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_10 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_10 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_10 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_10 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_10 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_10 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_10 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_10 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_10 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 586 SRR_FR_ObjectList11: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_11 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_11 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_11 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_11 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_11 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_11 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_11 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_11 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_11 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_11 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_11 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_11 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_11 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_11 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_11 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_11 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_11 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_11 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_11 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_11 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 587 SRR_FR_ObjectList12: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_12 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_12 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_12 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_12 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_12 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_12 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_12 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_12 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_12 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_12 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_12 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_12 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_12 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_12 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_12 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_12 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_12 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_12 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_12 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_12 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 588 SRR_FR_ObjectList13: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_13 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_13 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_13 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_13 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_13 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_13 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_13 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_13 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_13 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_13 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_13 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_13 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_13 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_13 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_13 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_13 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_13 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_13 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_13 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_13 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 589 SRR_FR_ObjectList14: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_14 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_14 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_14 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_14 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_14 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_14 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_14 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_14 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_14 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_14 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_14 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_14 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_14 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_14 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_14 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_14 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_14 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_14 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_14 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_14 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 590 SRR_FR_ObjectList15: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_15 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_15 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_15 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_15 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_15 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_15 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_15 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_15 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_15 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_15 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_15 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_15 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_15 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_15 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_15 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_15 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_15 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_15 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_15 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_15 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 591 SRR_FR_ObjectList16: 24 SRR_FR
 SG_ FR_ObjectRefPointPos_16 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FR_ObjectLink_16 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectCntr_16 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectHeadYawAgl_16 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FR_ObjectExistnc_16 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FR_ObjectDistAltitude_16 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FR_ObjectRollingCnt_16 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FR_ObjectDynProp_16 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FR_ObjectClass_16 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FR_ObjectRCS_16 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FR_ObjectWidth_16 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectLength_16 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FR_ObjectArelLat_16 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectArelLong_16 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FR_ObjectVrelLat_16 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectVrelLong_16 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FR_ObjectDistLat_16 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FR_ObjectDistLong_16 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FR_ObjectID_16 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FR_ObjectChecksum_16 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 512 SRR_FL_ObjectList01: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_01 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_01 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_01 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_01 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_01 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_01 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_01 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_01 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_01 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_01 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_01 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_01 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_01 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_01 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_01 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_01 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_01 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_01 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_01 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_01 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 513 SRR_FL_ObjectList02: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_02 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_02 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_02 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_02 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_02 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_02 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_02 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_02 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_02 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_02 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_02 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_02 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_02 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_02 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_02 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_02 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_02 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_02 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_02 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_02 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 514 SRR_FL_ObjectList03: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_03 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_03 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_03 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_03 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_03 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_03 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_03 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_03 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_03 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_03 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_03 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_03 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_03 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_03 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_03 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_03 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_03 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_03 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_03 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_03 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 515 SRR_FL_ObjectList04: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_04 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_04 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_04 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_04 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_04 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_04 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_04 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_04 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_04 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_04 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_04 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_04 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_04 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_04 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_04 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_04 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_04 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_04 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_04 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_04 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 516 SRR_FL_ObjectList05: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_05 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_05 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_05 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_05 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_05 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_05 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_05 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_05 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_05 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_05 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_05 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_05 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_05 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_05 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_05 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_05 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_05 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_05 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_05 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_05 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 517 SRR_FL_ObjectList06: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_06 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_06 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_06 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_06 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_06 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_06 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_06 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_06 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_06 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_06 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_06 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_06 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_06 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_06 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_06 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_06 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_06 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_06 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_06 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_06 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 518 SRR_FL_ObjectList07: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_07 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_07 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_07 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_07 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_07 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_07 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_07 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_07 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_07 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_07 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_07 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_07 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_07 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_07 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_07 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_07 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_07 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_07 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_07 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_07 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 519 SRR_FL_ObjectList08: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_08 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_08 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_08 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_08 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_08 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_08 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_08 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_08 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_08 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_08 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_08 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_08 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_08 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_08 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_08 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_08 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_08 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_08 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_08 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_08 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 520 SRR_FL_ObjectList09: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_09 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_09 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_09 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_09 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_09 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_09 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_09 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_09 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_09 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_09 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_09 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_09 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_09 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_09 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_09 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_09 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_09 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_09 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_09 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_09 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 521 SRR_FL_ObjectList10: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_10 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_10 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_10 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_10 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_10 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_10 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_10 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_10 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_10 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_10 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_10 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_10 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_10 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_10 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_10 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_10 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_10 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_10 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_10 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_10 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 522 SRR_FL_ObjectList11: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_11 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_11 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_11 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_11 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_11 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_11 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_11 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_11 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_11 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_11 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_11 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_11 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_11 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_11 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_11 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_11 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_11 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_11 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_11 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_11 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 523 SRR_FL_ObjectList12: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_12 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_12 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_12 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_12 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_12 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_12 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_12 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_12 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_12 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_12 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_12 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_12 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_12 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_12 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_12 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_12 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_12 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_12 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_12 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_12 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 524 SRR_FL_ObjectList13: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_13 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_13 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_13 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_13 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_13 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_13 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_13 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_13 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_13 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_13 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_13 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_13 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_13 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_13 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_13 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_13 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_13 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_13 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_13 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_13 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 525 SRR_FL_ObjectList14: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_14 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_14 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_14 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_14 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_14 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_14 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_14 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_14 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_14 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_14 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_14 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_14 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_14 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_14 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_14 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_14 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_14 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_14 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_14 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_14 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 526 SRR_FL_ObjectList15: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_15 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_15 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_15 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_15 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_15 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_15 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_15 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_15 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_15 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_15 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_15 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_15 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_15 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_15 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_15 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_15 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_15 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_15 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_15 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_15 : 7|8@0+ (1,0) [0|15] ""  ADAS

BO_ 527 SRR_FL_ObjectList16: 24 SRR_FL
 SG_ FL_ObjectRefPointPos_16 : 175|2@0+ (1,0) [0|3] ""  ADAS
 SG_ FL_ObjectLink_16 : 167|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectCntr_16 : 63|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectHeadYawAgl_16 : 115|12@0+ (0.1,-204.8) [-204.8|204.7] ""  ADAS
 SG_ FL_ObjectExistnc_16 : 131|7@0+ (1,0) [0|127] ""  ADAS
 SG_ FL_ObjectDistAltitude_16 : 159|8@0+ (0.05,-1.75) [-1.75|11] "m"  ADAS
 SG_ FL_ObjectRollingCnt_16 : 187|4@0+ (1,0) [0|7] ""  ADAS
 SG_ FL_ObjectDynProp_16 : 135|4@0+ (1,0) [0|15] "dbm^2"  ADAS
 SG_ FL_ObjectClass_16 : 147|4@0+ (1,0) [0|15] ""  ADAS
 SG_ FL_ObjectRCS_16 : 140|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ADAS
 SG_ FL_ObjectWidth_16 : 108|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectLength_16 : 100|8@0+ (0.2,0) [0|51] "m"  ADAS
 SG_ FL_ObjectArelLat_16 : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectArelLong_16 : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ADAS
 SG_ FL_ObjectVrelLat_16 : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectVrelLong_16 : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ADAS
 SG_ FL_ObjectDistLat_16 : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ADAS
 SG_ FL_ObjectDistLong_16 : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ADAS
 SG_ FL_ObjectID_16 : 15|8@0+ (1,0) [0|255] ""  ADAS
 SG_ FL_ObjectChecksum_16 : 7|8@0+ (1,0) [0|15] ""  ADAS



CM_ SG_ 576 FR_ObjectDynProp_01 "0x00: Unknown
0x01: Stationary
0x02: Reserved
0x03: Moving
0x04-0F: Reserved";
CM_ SG_ 576 FR_ObjectClass_01 "0x0:Unknown
0x1:Car
0x2:Truck
0x3:2 wheeler
0x4:Pedestrian
0x5-0xF:Reserved";
CM_ SG_ 512 FL_ObjectDynProp_01 "0x00: Unknown
0x01: Stationary
0x02: Reserved
0x03: Moving
0x04-0F: Reserved";
CM_ SG_ 512 FL_ObjectClass_01 "0x0:Unknown
0x1:Car
0x2:Truck
0x3:2 wheeler
0x4:Pedestrian
0x5-0xF:Reserved";
BA_DEF_ BO_  "GenericFrameRequirementNb" STRING ;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","OnEvent","Cyclic_And_OnEvent";
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 65535;
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_  "DBName" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_DEF_  "GenericFrameRequirementNb" "";
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenMsgCycleTime" 50;
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_ "BusType" "CAN FD";
BA_ "DBName" "CTMRR410_PCANFD";
BA_ "VFrameFormat" BO_ 576 14;
BA_ "VFrameFormat" BO_ 577 14;
BA_ "VFrameFormat" BO_ 578 14;
BA_ "VFrameFormat" BO_ 579 14;
BA_ "VFrameFormat" BO_ 580 14;
BA_ "VFrameFormat" BO_ 581 14;
BA_ "VFrameFormat" BO_ 582 14;
BA_ "VFrameFormat" BO_ 583 14;
BA_ "VFrameFormat" BO_ 584 14;
BA_ "VFrameFormat" BO_ 585 14;
BA_ "VFrameFormat" BO_ 586 14;
BA_ "VFrameFormat" BO_ 587 14;
BA_ "VFrameFormat" BO_ 588 14;
BA_ "VFrameFormat" BO_ 589 14;
BA_ "VFrameFormat" BO_ 590 14;
BA_ "VFrameFormat" BO_ 591 14;
BA_ "VFrameFormat" BO_ 512 14;
BA_ "VFrameFormat" BO_ 513 14;
BA_ "VFrameFormat" BO_ 514 14;
BA_ "VFrameFormat" BO_ 515 14;
BA_ "VFrameFormat" BO_ 516 14;
BA_ "VFrameFormat" BO_ 517 14;
BA_ "VFrameFormat" BO_ 518 14;
BA_ "VFrameFormat" BO_ 519 14;
BA_ "VFrameFormat" BO_ 520 14;
BA_ "VFrameFormat" BO_ 521 14;
BA_ "VFrameFormat" BO_ 522 14;
BA_ "VFrameFormat" BO_ 523 14;
BA_ "VFrameFormat" BO_ 524 14;
BA_ "VFrameFormat" BO_ 525 14;
BA_ "VFrameFormat" BO_ 526 14;
BA_ "VFrameFormat" BO_ 527 14;
VAL_ 1029 Lock_Target_Lane_ID_06 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 1028 Lock_Target_Lane_ID_05 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 1027 Lock_Target_Lane_ID_04 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 1026 Lock_Target_Lane_ID_03 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 1025 Lock_Target_Lane_ID_02 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 1024 Lock_Target_Lane_ID_01 3 "Right" 2 "Behind/Front" 1 "Left" 0 "NA" ;
VAL_ 576 FR_ObjectRefPointPos_01 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 577 FR_ObjectRefPointPos_02 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 578 FR_ObjectRefPointPos_03 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 579 FR_ObjectRefPointPos_04 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 580 FR_ObjectRefPointPos_05 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 581 FR_ObjectRefPointPos_06 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 582 FR_ObjectRefPointPos_07 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 583 FR_ObjectRefPointPos_08 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 584 FR_ObjectRefPointPos_09 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 585 FR_ObjectRefPointPos_10 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 586 FR_ObjectRefPointPos_11 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 587 FR_ObjectRefPointPos_12 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 588 FR_ObjectRefPointPos_13 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 589 FR_ObjectRefPointPos_14 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 590 FR_ObjectRefPointPos_15 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 591 FR_ObjectRefPointPos_16 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 512 FL_ObjectRefPointPos_01 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 513 FL_ObjectRefPointPos_02 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 514 FL_ObjectRefPointPos_03 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 515 FL_ObjectRefPointPos_04 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 516 FL_ObjectRefPointPos_05 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 517 FL_ObjectRefPointPos_06 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 518 FL_ObjectRefPointPos_07 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 519 FL_ObjectRefPointPos_08 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 520 FL_ObjectRefPointPos_09 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 521 FL_ObjectRefPointPos_10 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 522 FL_ObjectRefPointPos_11 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 523 FL_ObjectRefPointPos_12 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 524 FL_ObjectRefPointPos_13 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 525 FL_ObjectRefPointPos_14 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 526 FL_ObjectRefPointPos_15 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;
VAL_ 527 FL_ObjectRefPointPos_16 3 "RR" 2 "RL" 1 "FR" 0 "FL" ;

