<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TargetsViewConfigDialog</class>
 <widget class="QDialog" name="TargetsViewConfigDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>371</width>
    <height>339</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>2D显示设置</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>371</width>
        <height>277</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayoutScrollArea">
       <item>
        <layout class="QGridLayout" name="gridLayout">
         <item row="2" column="1">
          <widget class="QDoubleSpinBox" name="doubleSpinBoxLocalVehicleLength"/>
         </item>
         <item row="1" column="1">
          <widget class="QDoubleSpinBox" name="doubleSpinBoxYmin">
           <property name="minimum">
            <double>-500.000000000000000</double>
           </property>
           <property name="maximum">
            <double>500.000000000000000</double>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QDoubleSpinBox" name="doubleSpinBoxXmax">
           <property name="minimum">
            <double>-100.000000000000000</double>
           </property>
           <property name="maximum">
            <double>100.000000000000000</double>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>-</string>
           </property>
          </widget>
         </item>
         <item row="1" column="5">
          <widget class="QDoubleSpinBox" name="doubleSpinBoxYInterval">
           <property name="minimum">
            <double>1.000000000000000</double>
           </property>
           <property name="maximum">
            <double>100.000000000000000</double>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QDoubleSpinBox" name="doubleSpinBoxYmax">
           <property name="minimum">
            <double>-500.000000000000000</double>
           </property>
           <property name="maximum">
            <double>500.000000000000000</double>
           </property>
          </widget>
         </item>
         <item row="1" column="4">
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>/</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QSpinBox" name="spinBoxPointSizeRaw">
           <property name="minimum">
            <number>1</number>
           </property>
           <property name="maximum">
            <number>100</number>
           </property>
          </widget>
         </item>
         <item row="2" column="3">
          <widget class="QDoubleSpinBox" name="doubleSpinBoxLocalVehicleWidth"/>
         </item>
         <item row="0" column="1">
          <widget class="QDoubleSpinBox" name="doubleSpinBoxXmin">
           <property name="minimum">
            <double>-100.000000000000000</double>
           </property>
           <property name="maximum">
            <double>100.000000000000000</double>
           </property>
          </widget>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>/</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="label_9">
           <property name="text">
            <string>原始点大小:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label">
           <property name="text">
            <string>横坐标:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>-</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>纵坐标:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QDoubleSpinBox" name="doubleSpinBoxXInterval">
           <property name="minimum">
            <double>1.000000000000000</double>
           </property>
           <property name="maximum">
            <double>100.000000000000000</double>
           </property>
          </widget>
         </item>
         <item row="0" column="6">
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>自车(长/宽):</string>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QLabel" name="label_8">
           <property name="text">
            <string>-</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="label_10">
           <property name="text">
            <string>跟踪点大小:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QSpinBox" name="spinBoxPointSizeTrack">
           <property name="minimum">
            <number>1</number>
           </property>
           <property name="maximum">
            <number>100</number>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QCheckBox" name="checkBoxDisplayIDLabelRaw">
           <property name="text">
            <string>显示原始点ID</string>
           </property>
          </widget>
         </item>
         <item row="4" column="3">
          <widget class="QCheckBox" name="checkBoxDisplayIDLabelTrack">
           <property name="text">
            <string>显示跟踪点ID</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>19</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOK">
       <property name="text">
        <string>确定</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonApply">
       <property name="text">
        <string>应用</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>pushButtonCancel</sender>
   <signal>clicked()</signal>
   <receiver>TargetsViewConfigDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>266</x>
     <y>108</y>
    </hint>
    <hint type="destinationlabel">
     <x>296</x>
     <y>70</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
