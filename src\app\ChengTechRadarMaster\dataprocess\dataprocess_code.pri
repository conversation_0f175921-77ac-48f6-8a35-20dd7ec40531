DEFINES += PC_DBG_FW
DEFINES += PC_TRACK_DBG

DATA_PROCESS_PREFIX_PATH = ./dataprocess/radarmastertrack
HEADERS += \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_main_ekf.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_matrixMath.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_pre_ekf.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_track_common.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_track_config.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_main_ekf.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_matrixMath.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_pre_ekf.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_track_common.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_track_config.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_clth_radar_lib.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_interface.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_kf_config.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_kf_init.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_kf_track.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_kf_types.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_track_listlib.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_track_struct.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_types.h \
    $$DATA_PROCESS_PREFIX_PATH/app/apar/apar_types.h \
    $$DATA_PROCESS_PREFIX_PATH/app/app_common/app_common.h \
    $$DATA_PROCESS_PREFIX_PATH/app/app_common/app_pcan_protocol.h \
    $$DATA_PROCESS_PREFIX_PATH/app/can_com/can_msg.h \
    $$DATA_PROCESS_PREFIX_PATH/app/can_com/msg_struct_typedef.h \
    $$DATA_PROCESS_PREFIX_PATH/app/include/sharedVar.h \
    $$DATA_PROCESS_PREFIX_PATH/app/protocol/com_interface.h \
    $$DATA_PROCESS_PREFIX_PATH/app/protocol/com_types.h \
    $$DATA_PROCESS_PREFIX_PATH/app/system_mgr/typedefs.h \
    $$DATA_PROCESS_PREFIX_PATH/app/vehicle/vdy/vdy_interface.h \
    $$DATA_PROCESS_PREFIX_PATH/app/vehicle/vdy/vdy_types.h \
    $$DATA_PROCESS_PREFIX_PATH/other/pc_vr_mcu.h \
    $$DATA_PROCESS_PREFIX_PATH/other/temp.h \
    $$DATA_PROCESS_PREFIX_PATH/alg/vdy/vdy.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/adas_common.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/moving_average_filter.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/ring_queue_struct_buff.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/linear_regression.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/linear_regression_application.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/customizedrequirements/adas.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/customizedrequirements/adas_alg_params.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/customizedrequirements/adas_signal_integration.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/customizedrequirements/adas_standard_params.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/customizedrequirements/adas_state_machine.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/customizedrequirements/adas_vehicle_ctrls.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_manager.h \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/misc/adas_dow_client.h \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/alignment.h \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/aln_autoEolFun.h \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/aln_dynamicEolFun.h \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/aln_install_cfg.h \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/aln_staticEolFun.h \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/aln_type.h \
    $$DATA_PROCESS_PREFIX_PATH/app/apar/apar_manager.h \
#    $$DATA_PROCESS_PREFIX_PATH/app/hmi_types.h \
    $$DATA_PROCESS_PREFIX_PATH/app/system_mgr/cfg.h \
    $$DATA_PROCESS_PREFIX_PATH/app/system_mgr/radarCfg.h \
#    $$DATA_PROCESS_PREFIX_PATH/hal/algo/crclib.h \
#    $$DATA_PROCESS_PREFIX_PATH/hal/algo/pow_fast.h \
#    $$DATA_PROCESS_PREFIX_PATH/hal/algo/sha256.h \
    $$DATA_PROCESS_PREFIX_PATH/hal/algo/sortlib.h \
    $$DATA_PROCESS_PREFIX_PATH/hal/rsp/rsp_types.h

SOURCES += \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_main_ekf.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_matrixMath.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_pre_ekf.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_main_ekf.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_matrixMath.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_pre_ekf.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_clth_radar_lib.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_kf_init.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_kf_track.c \
#    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_Main.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_data_process.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/track/rdp_track_listlib.c \
    $$DATA_PROCESS_PREFIX_PATH/alg/vdy/vdy.c \
#    $$DATA_PROCESS_PREFIX_PATH/app/vehicle/vdy/vdy_dataFunc.c \
#    $$DATA_PROCESS_PREFIX_PATH/app/vehicle/app_vehicle.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/adas_common.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/moving_average_filter.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/ring_queue_struct_buff.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/linear_regression.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/common/linear_regression_application.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/customizedrequirements/adas_signal_integration.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/customizedrequirements/adas_state_machine.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/customizedrequirements/adas_vehicle_ctrls.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_alm_bsd.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_alm_dow.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_alm_fctab.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_alm_lca.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_alm_rctab.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_alm_rcw.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_function.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_sideLine.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/generalalg/adas_detection_scenario.c \
    $$DATA_PROCESS_PREFIX_PATH/app/adas/misc/adas_dow_client.c \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/alignment.c \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/aln_autoEolFun.c \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/aln_dynamicEolFun.c \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/aln_install_cfg.c \
    $$DATA_PROCESS_PREFIX_PATH/app/aln/aln_staticEolFun.c \
    $$DATA_PROCESS_PREFIX_PATH/other/pc_vr_mcu.c \
    $$DATA_PROCESS_PREFIX_PATH/other/temp.c \
#    $$DATA_PROCESS_PREFIX_PATH/hal/algo/crclib.c \
#    $$DATA_PROCESS_PREFIX_PATH/hal/algo/sha256.c \
    $$DATA_PROCESS_PREFIX_PATH/hal/algo/sortlib.c

INCLUDEPATH += $$DATA_PROCESS_PREFIX_PATH
