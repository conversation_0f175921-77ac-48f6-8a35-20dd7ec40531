<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DC1000ADCCollect</class>
 <widget class="QMainWindow" name="DC1000ADCCollect">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>904</width>
    <height>609</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_2">
    <property name="leftMargin">
     <number>3</number>
    </property>
    <property name="topMargin">
     <number>3</number>
    </property>
    <property name="rightMargin">
     <number>3</number>
    </property>
    <property name="bottomMargin">
     <number>3</number>
    </property>
    <item>
     <widget class="QSplitter" name="splitter_2">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QWidget" name="widget" native="true"/>
      <widget class="TargetsView" name="targetsView" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>300</width>
         <height>0</height>
        </size>
       </property>
      </widget>
      <widget class="QSplitter" name="splitter">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <widget class="QLabel" name="labelVideo">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>300</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QWidget" name="layoutWidget">
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <layout class="QGridLayout" name="gridLayout">
           <item row="5" column="0">
            <widget class="QCheckBox" name="checkBox">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="text">
              <string>回灌ADC</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <widget class="QCheckBox" name="checkBox_2">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="text">
              <string>回灌CAN</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QComboBox" name="comboBoxDataRate">
             <item>
              <property name="text">
               <string>2000000</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="4" column="5">
            <widget class="QLabel" name="label_7">
             <property name="text">
              <string>Chirp数:</string>
             </property>
            </widget>
           </item>
           <item row="5" column="8">
            <widget class="QPushButton" name="pushButtonOpenSavePath">
             <property name="text">
              <string>打开路径</string>
             </property>
            </widget>
           </item>
           <item row="3" column="8">
            <widget class="QPushButton" name="pushButtonConnect">
             <property name="text">
              <string>Connect</string>
             </property>
            </widget>
           </item>
           <item row="6" column="5">
            <widget class="QPushButton" name="pushButtonFileCAN">
             <property name="text">
              <string>...</string>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>单帧大小(M)</string>
             </property>
            </widget>
           </item>
           <item row="6" column="8">
            <widget class="QPushButton" name="pushButtonClear">
             <property name="text">
              <string>清空</string>
             </property>
            </widget>
           </item>
           <item row="4" column="2">
            <widget class="QLineEdit" name="lineEditSingleFrameSize">
             <property name="text">
              <string>2</string>
             </property>
            </widget>
           </item>
           <item row="5" column="1" colspan="4">
            <widget class="QLineEdit" name="lineEditFileADC"/>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label">
             <property name="text">
              <string>设备</string>
             </property>
            </widget>
           </item>
           <item row="0" column="8">
            <widget class="QPushButton" name="pushButtonOpenCamera">
             <property name="text">
              <string>Open Camera</string>
             </property>
            </widget>
           </item>
           <item row="3" column="4">
            <widget class="QLabel" name="label_4">
             <property name="text">
              <string>Port:</string>
             </property>
            </widget>
           </item>
           <item row="4" column="3">
            <widget class="QLabel" name="label_6">
             <property name="text">
              <string>采集帧数</string>
             </property>
            </widget>
           </item>
           <item row="1" column="9">
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="1" column="1">
            <widget class="QComboBox" name="comboBoxDeviceIndex">
             <item>
              <property name="text">
               <string>0</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QComboBox" name="comboBoxBaudRate">
             <item>
              <property name="text">
               <string>500000</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="0" column="1" colspan="5">
            <widget class="QComboBox" name="comboBoxCamera"/>
           </item>
           <item row="4" column="4">
            <widget class="QLineEdit" name="lineEditFrameCount">
             <property name="text">
              <string>100</string>
             </property>
            </widget>
           </item>
           <item row="6" column="1" colspan="4">
            <widget class="QLineEdit" name="lineEditFileCAN"/>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>IP:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="8">
            <widget class="QPushButton" name="pushButtonOpenCANDevice">
             <property name="text">
              <string>Open CAN</string>
             </property>
            </widget>
           </item>
           <item row="5" column="5">
            <widget class="QPushButton" name="pushButtonFileADC">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="text">
              <string>...</string>
             </property>
            </widget>
           </item>
           <item row="5" column="6">
            <widget class="QLabel" name="label_8">
             <property name="text">
              <string>雷达ID:</string>
             </property>
            </widget>
           </item>
           <item row="3" column="5">
            <widget class="QLineEdit" name="lineEditPort">
             <property name="minimumSize">
              <size>
               <width>30</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>4098</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="4" colspan="2">
            <widget class="QCheckBox" name="checkBoxDeviceCANFD">
             <property name="text">
              <string>CANFD</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QCheckBox" name="checkBoxByte">
             <property name="text">
              <string>Byte</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>摄像头</string>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QCheckBox" name="checkBoxAnyIP">
             <property name="text">
              <string>Any</string>
             </property>
            </widget>
           </item>
           <item row="3" column="2" colspan="2">
            <widget class="QLineEdit" name="lineEditIP">
             <property name="text">
              <string>*************</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="8">
            <widget class="QPushButton" name="pushButtonStart">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="text">
              <string>Start</string>
             </property>
            </widget>
           </item>
           <item row="5" column="7">
            <widget class="QComboBox" name="comboBoxRadarID">
             <property name="editable">
              <bool>true</bool>
             </property>
             <item>
              <property name="text">
               <string>0</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>4</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>5</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>6</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>7</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="4" column="6" colspan="2">
            <widget class="QSpinBox" name="spinBoxChirpCount">
             <property name="minimum">
              <number>1</number>
             </property>
             <property name="maximum">
              <number>102400</number>
             </property>
            </widget>
           </item>
           <item row="3" column="6" colspan="2">
            <widget class="QComboBox" name="comboBoxType">
             <item>
              <property name="text">
               <string>采集</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>回灌</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="1" column="6" colspan="2">
            <widget class="QComboBox" name="comboBoxCANChannel">
             <item>
              <property name="text">
               <string>CAN 0</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>CAN 1</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="0" column="6" colspan="2">
            <widget class="QPushButton" name="pushButtonCameraRefresh">
             <property name="text">
              <string>刷新</string>
             </property>
            </widget>
           </item>
           <item row="6" column="6">
            <widget class="QLabel" name="label_9">
             <property name="text">
              <string>跳过:</string>
             </property>
            </widget>
           </item>
           <item row="6" column="7">
            <widget class="QSpinBox" name="spinBoxVechileJump"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QPlainTextEdit" name="plainTextEditLog"/>
         </item>
        </layout>
       </widget>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>904</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuSettings">
    <property name="title">
     <string>设置</string>
    </property>
    <addaction name="actionTargetViewSettings"/>
   </widget>
   <addaction name="menuSettings"/>
  </widget>
  <action name="actionTargetViewSettings">
   <property name="text">
    <string>目标显示设置</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>TargetsView</class>
   <extends>QWidget</extends>
   <header>targetsview.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>pushButtonClear</sender>
   <signal>clicked()</signal>
   <receiver>plainTextEditLog</receiver>
   <slot>clear()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>826</x>
     <y>483</y>
    </hint>
    <hint type="destinationlabel">
     <x>817</x>
     <y>539</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
