﻿#ifndef FRAMEBINARYSAVEWORKER_H
#define FRAMEBINARYSAVEWORKER_H

#include "analysisdata.h"
#include "../devices/canframe.h"

#include <QObject>
#include <QtEndian>
#include <QDebug>
#include <QDataStream>
#include <QFile>


namespace Analysis {

static bool isLittleEndian()
{
    short a=0x0061;
    if((char)a=='a')
        return true;
    else
        return false;
}

//帧2进制保存格式-文件头
struct FrameBinarySaveFormat_FHead
{
    quint32 version;//上位机版本
    quint64 ct; //创建时间
    quint8 fileNum;//文件计数
    quint8 reservedNum;//预留字节数量
    quint8 protocolNum;//协议数量
    QByteArray protocols;//协议
    QByteArray reservedByte;//预留字节

    void    saveToStream( QDataStream& stream )
    {
        quint32 version_tmp = qToBigEndian( version );
        stream.writeRawData( (const char*)&version_tmp, sizeof(version_tmp) );
        quint64 ct_tmp = qToBigEndian( ct );
        stream.writeRawData( (const char*)&ct_tmp, sizeof(ct_tmp) );
        stream.writeRawData( (const char*)&fileNum, sizeof(fileNum) );
        stream.writeRawData( (const char*)&reservedNum, sizeof(reservedNum) );
        stream.writeRawData( (const char*)&protocolNum, sizeof(protocolNum) );
        stream.writeRawData( protocols.data(), protocols.size() );
        stream.writeRawData( reservedByte.data(), reservedByte.size() );
    };

    void    loadFromStream( QDataStream& stream )
    {
        stream.readRawData( (char*)&version, sizeof( version ) );
        stream.readRawData( (char*)&ct, sizeof( ct ) );
        stream.readRawData( (char*)&fileNum, sizeof( fileNum ) );
        stream.readRawData( (char*)&reservedNum, sizeof( reservedNum ) );
        stream.readRawData( (char*)&protocolNum, sizeof( protocolNum ) );
        if( isLittleEndian() ){
            version = qFromBigEndian( version );
            ct = qFromBigEndian( ct );
        }
        protocols.resize( protocolNum );
        stream.readRawData( protocols.data(), protocolNum );
        reservedByte.resize( reservedNum );
        stream.readRawData( reservedByte.data(), reservedNum );
    };

    void    Printf( QTextStream* stream = NULL)
    {
        if( !stream ){
            qDebug() <<"file head"
                    << "\n\t version" << ((version&0xFF000000)>>24) << "."<< ((version&0x00FF0000)>>16) << "."<< ((version&0x0000FF00)>>8) << "."<< (version&0x000000FF)
                    <<"\n\t ct"<< QDateTime::fromMSecsSinceEpoch( ct ).toString()
                    <<"\n\t filenum"<< fileNum
                    <<"\n\t reservedNum"<< reservedNum
                    <<"\n\t protocolnum"<< protocolNum
                    <<"\n\t protocols"<< protocols.toHex()
                    <<"\n\t reservedByte"<< reservedByte;
        }else{
            *stream <<"file head"
                    << "\n\t version:" << ((version&0xFF000000)>>24) << "."<< ((version&0x00FF0000)>>16) << "."<< ((version&0x0000FF00)>>8) << "."<< (version&0x000000FF)
                    <<"\n\t ct:"<< QDateTime::fromMSecsSinceEpoch( ct ).toString()
                    <<"\n\t filenum:"<< fileNum
                    <<"\n\t reservedNum:"<< reservedNum
                    <<"\n\t protocolnum:"<< protocolNum
                    <<"\n\t protocols:"<< protocols.toHex()
                    <<"\n\t reservedByte:"<< reservedByte;
        }
    };
};

//帧2进制保存格式-数据头
struct FrameBinarySaveFormat_Head
{
    quint8 type; //数据类型 1：雷达数据 2：真值数据 3：视频数据
    quint8  devId; //设备ID
    quint64 ct; //创建时间
    quint32 cnt1;   //多文件计数
    quint32 cnt2;   //本文件计数
    quint32 cnt3;   //数据块计数
    quint16 bodyLen;   //数据主体长度

    bool    isRadar()
    {
        return type == 1;
    };

    void    saveToStream( QDataStream& stream ){
        stream.writeRawData( (const char*)&type, sizeof(type) );
        stream.writeRawData( (const char*)&devId, sizeof(devId) );
        quint64 ct_tmp = qToBigEndian( ct );
        stream.writeRawData( (const char*)&ct_tmp, sizeof(ct_tmp) );
        quint32 cnt1_tmp = qToBigEndian( cnt1 );
        stream.writeRawData( (const char*)&cnt1_tmp, sizeof(cnt1) );
        quint32 cnt2_tmp = qToBigEndian( cnt2 );
        stream.writeRawData( (const char*)&cnt2_tmp, sizeof(cnt2_tmp) );
        quint32 cnt3_tmp = qToBigEndian( cnt3 );
        stream.writeRawData( (const char*)&cnt3_tmp, sizeof(cnt3_tmp) );
        quint16 bodyLen_tmp = qToBigEndian( bodyLen );
        stream.writeRawData( (const char*)&bodyLen_tmp, sizeof(bodyLen_tmp) );
    };

    void    loadFromStream( QDataStream& stream )
    {
        stream.readRawData( (char*)&type, sizeof( type ) );
        stream.readRawData( (char*)&devId, sizeof( devId ) );
        stream.readRawData( (char*)&ct, sizeof( ct ) );
        stream.readRawData( (char*)&cnt1, sizeof( cnt1 ) );
        stream.readRawData( (char*)&cnt2, sizeof( cnt2 ) );
        stream.readRawData( (char*)&cnt3, sizeof( cnt3 ) );
        stream.readRawData( (char*)&bodyLen, sizeof( bodyLen ) );
        if( isLittleEndian() ){
            ct = qFromBigEndian( ct );
            cnt1 = qFromBigEndian( cnt1 );
            cnt2 = qFromBigEndian( cnt2 );
            cnt3 = qFromBigEndian( cnt3 );
            bodyLen = qFromBigEndian( bodyLen );
        }
    };

    void    printf( QTextStream* stream = NULL )
    {
        if( !stream ){
            qDebug() <<"data head"
                    << "\n\t type"<<type
                    <<"\n\t devid"<< devId
                    <<"\n\t ct"<< QDateTime::fromMSecsSinceEpoch( ct ).toString()
                    <<"\n\t cnt1"<< cnt1
                    <<"\n\t cnt2"<< cnt2
                    <<"\n\t cnt3"<< cnt3
                    <<"\n\t bodyLen"<< bodyLen;
        }else{
            *stream <<"\ndata head"
                    << "\n\t type:"<<type
                    <<"\n\t devid:"<< devId
                    <<"\n\t ct:"<< QDateTime::fromMSecsSinceEpoch( ct ).toString()
                    <<"\n\t cnt1:"<< cnt1
                    <<"\n\t cnt2:"<< cnt2
                    <<"\n\t cnt3:"<< cnt3
                    <<"\n\t bodyLen:"<< bodyLen;
        }
    };
};

//帧进制保存格式-数据体
struct FrameBinarySaveFormat_Body
{
    quint8 type; //帧类型 0:can 1:canfd
    quint8 expand; //是否位拓展帧 0:标准帧 1:拓展帧
    quint64 timestemp;  // 时间戳
    quint8 len; //数据长度
    quint32 id; //帧id
    QByteArray data;

    Devices::Can::CanFrame    toCanFrame()
    {
        return Devices::Can::CanFrame( 0, 0, Devices::Can::CanFrame::RecieveOrTransmit::RX,
                                  id, data, timestemp,
                                  (bool)expand );
    };

    quint16 bodyLen()
    {
        return sizeof( type ) + sizeof( expand ) + sizeof (timestemp) + sizeof( len ) + sizeof( id ) + data.size();
    };

    void    saveToStream( QDataStream& stream ){
        stream.writeRawData( (const char*)&type, sizeof(type) );
        stream.writeRawData( (const char*)&expand, sizeof(expand) );
        quint64 timestemp_tmp = qToBigEndian( timestemp );
        stream.writeRawData( (const char*)&timestemp_tmp, sizeof(timestemp) );
        stream.writeRawData( (const char*)&len, sizeof(len) );
        quint32 id_tmp = qToBigEndian( id );
        stream.writeRawData( (const char*)&id_tmp, sizeof(id) );
        stream.writeRawData( data.data(), data.size() );
    };

    void    loadFromStream( QDataStream& stream )
    {
        stream.readRawData( (char*)&type, sizeof( type ) );
        stream.readRawData( (char*)&expand, sizeof( expand ) );
        stream.readRawData( (char*)&timestemp, sizeof( timestemp ) );
        stream.readRawData( (char*)&len, sizeof( len ) );
        stream.readRawData( (char*)&id, sizeof( id ) );
        if( isLittleEndian() ){
            id = qFromBigEndian( id );
            timestemp = qFromBigEndian( timestemp );
        }
        data.resize( len );
        stream.readRawData( data.data(), len );
    };

    void    printf( QTextStream* stream = NULL )
    {
        if( !stream ){
            qDebug() <<"body"
                    << "\n\t type"<<type
                    <<"\n\t expand"<< expand
                    <<"\n\t len"<< len
                   <<"\n\t timestemp"<< QDateTime::fromMSecsSinceEpoch(timestemp).toString("yyyy-MM-dd hh:mm:ss.zzz")
                    <<"\n\t id"<< QString::number( id, 16)// QString("%1").arg(id).toHex
                    <<"\n\t data"<< data.toHex();
        }else{
            *stream <<"\nbody"
                    << "\n\t type:"<<QString::number(type)
                    <<"\n\t expand:"<< QString::number(expand)
                   <<"\n\t timestemp"<< QDateTime::fromMSecsSinceEpoch(timestemp).toString("yyyy-MM-dd hh:mm:ss.zzz")
                    <<"\n\t len:"<< QString::number(len)
                    <<"\n\t id:"<< QString::number( id, 16)// QString("%1").arg(id).toHex
                    <<"\n\t data:"<< QString( data.toHex() );
        }
    }
};

class FrameBinarySaveWorker : public QObject
{
    Q_OBJECT
    enum HeadDataType{
        RadarData = 1,      //雷达数据
        TrueData,           //真值数据
        VideoData,          //视频数据
        TargetPoint16Data,  //16个目标点数据
        LanePointData       //本车道/邻车道点
    };

public:
    explicit FrameBinarySaveWorker(QObject *parent = nullptr);
    ~FrameBinarySaveWorker();

public:
    bool startSave(const QString &savePath, const QDateTime &beginTime );
    bool stopSave();

private:
    void    clear();

signals:

public slots:
    void    addRadarBody( quint8 radarID, const Devices::Can::CanFrame &frame, bool bEndFrame );
    void    addTargetPoint16Body( quint8 radarID, const Devices::Can::CanFrame &frame, bool bEndFrame );
    void    addLanePointBody( quint8 radarID, const Devices::Can::CanFrame &frame, bool bEndFrame );

private:
    void    addBody( quint8 radarID, HeadDataType dataType, const Devices::Can::CanFrame &frame, bool bEndFrame );

private:
    void    writeFileHead();
    void    writeHeadAndBody( quint8 radarID, HeadDataType dataType );

    void    writeCanHead( quint8 radarID );
    void    writeCanBody( quint8 radarID );


private:
    bool    mSave;
    QString mSavePath;

    quint32 mCurrentCnt; //当前数据块计数
    QFile mFile;
    QDataStream mStream;

    QByteArray mCanData[MAX_RADAR_COUNT]; //雷达帧数据
    QByteArray mTargetPoint16Data[MAX_RADAR_COUNT];//16个目标点数据
    QByteArray mLanePointData[MAX_RADAR_COUNT];   //本车道/邻车道点数据
};

};

#endif // FRAMEBINARYSAVEWORKER_H
