<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DAQCalterahCtrl</class>
 <widget class="QWidget" name="DAQCalterahCtrl">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>668</width>
    <height>338</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="leftMargin">
    <number>3</number>
   </property>
   <property name="topMargin">
    <number>3</number>
   </property>
   <property name="rightMargin">
    <number>3</number>
   </property>
   <property name="bottomMargin">
    <number>3</number>
   </property>
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>671</width>
        <height>296</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>3</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <widget class="QCheckBox" name="checkBoxAnlyIP">
           <property name="text">
            <string>Anly IP：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="listenLineEdit">
           <property name="minimumSize">
            <size>
             <width>150</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>150</width>
             <height>16777215</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_8">
           <property name="text">
            <string>端口：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditPort">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>16777215</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxRadarType"/>
         </item>
         <item>
          <widget class="QComboBox" name="protocolComboBox"/>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxDataType"/>
         </item>
         <item>
          <widget class="QComboBox" name="channelComboBox"/>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>358</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <widget class="QPushButton" name="serverPushButton">
           <property name="text">
            <string>启动服务</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="collectPushButton">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>300</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>开始采集</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="HILPushButton">
           <property name="maximumSize">
            <size>
             <width>300</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>回灌</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="singleFrameCheckBox">
           <property name="text">
            <string>单帧回灌</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="nextPushButton">
           <property name="text">
            <string>下一帧</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="sizeLabel">
           <property name="text">
            <string>帧大小(MB):   </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="sizeLineEdit">
           <property name="maximumSize">
            <size>
             <width>300</width>
             <height>16777215</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxByte">
           <property name="text">
            <string>Byte</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="cntLabel">
           <property name="text">
            <string>帧数量：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="cntLineEdit">
           <property name="maximumSize">
            <size>
             <width>300</width>
             <height>16777215</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="progressLabel">
           <property name="text">
            <string>0/0</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_4">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>回灌车身文件：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="carFileLineEdit"/>
         </item>
         <item>
          <widget class="QPushButton" name="selCarFilePushButton">
           <property name="text">
            <string>...</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>回灌ADC文件： </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="ADCFileLineEdit"/>
         </item>
         <item>
          <widget class="QCheckBox" name="ZeerFormatCheckBox">
           <property name="text">
            <string>泽尔格式</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="selADCFilePushButton">
           <property name="text">
            <string>...</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <item>
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>配置文件：    </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="cfgFileLineEdit"/>
         </item>
         <item>
          <widget class="QPushButton" name="selCfgFilepushButton">
           <property name="text">
            <string>...</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QTextEdit" name="msgTextEdit"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="labelClient">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
