﻿#ifndef IDEVICEFILE_H
#define IDEVICEFILE_H

#include <string>
#include <vector>
#include <chrono>

#include "devices/canframe.h"

namespace Devices {
    namespace Can {

    typedef bool(*ValidID)(int id);

		class CANDeviceFile;

		class IDeviceFile
		{
		public:
			enum FileType
			{
				ASC,    //vector输出的CAN保存格式
				BLF,    //vector输出的二进制文件
				UNKNOWN
			};

			struct FileTypeInfo
			{
				FileType  type;
				std::string fileSuffix;
			};

			static IDeviceFile::FileType fileType(std::string &name);

			explicit IDeviceFile(CANDeviceFile *device = 0);

			bool isOpened() const { return mOpened; }

			bool open(const std::string  &name, bool inMode = true);
			bool close();
			virtual bool readData() = 0;
            virtual bool writeData(const CanFrame::Ptr pFrame) = 0;

            void setValidID(ValidID validID) { mfValidID = validID; }
			const std::string &filename() const { return mFilename; }
			inline const std::string errorString() const { return mErrorString; }

		protected:
			virtual bool openFile() = 0;
			virtual bool closeFile() = 0;

            bool validID(int id);
            void callback(const CanFrame::Ptr pFrame);
            void dely(uint64_t time_diff, uint64_t id = 0);

                        CANDeviceFile *mDeviceFile{ 0 };
			static std::vector<IDeviceFile::FileTypeInfo>  mFileTypeList;

            ValidID mfValidID{ 0 };

			std::string mFilename;
			std::string mErrorString;

			bool mOpenInMode{ false };
			bool mOpened{ false };
			bool mFirst{ false };
			uint64_t mSaveTimestamp;			///< 文件保存时间戳（微秒级）
			uint64_t mTimeDifferential{ 0 };	///< 时间差速(us)

			std::chrono::steady_clock::time_point m_time_point;
		};
	}
}

#endif // IDEVICEFILE_H
