﻿#ifndef RADARCONFIG_H
#define RADARCONFIG_H

#include "functions_global.h"

#include <QDialog>
#include <QByteArray>
#include <QDebug>
#include <QMutex>
#include <QMutexLocker>

namespace Ui {
class RadarConfig;
}

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Functions {

class FUNCTIONS_EXPORT RadarConfig : public QDialog
{
    Q_OBJECT

public:
    explicit RadarConfig(QWidget *parent = 0);
    explicit RadarConfig(Devices::Can::DeviceManager *deviceManager, QWidget *parent = 0);
    ~RadarConfig();

    int protocol_type;
    int radar_addr;
    int spd_model;

signals:
    void sigCmdSendData(int id , QByteArray datAry);
    void sigUpdateRoad(float radius, float yaw);

public slots:
    void slot_Receive_CanData(int CanCH, int id , QByteArray datAry );
    void sendData(int channelIndex, int id, QByteArray data);

private slots:
    void on_buttonBox_accepted();

    /** @brief 配置使能 */
    void on_pushButton_clicked();

    void on_pushButton_2_clicked();

    void on_pushButton_3_clicked();

    void on_pushButton_4_clicked();

    void on_pushButton_5_clicked();

    void on_pushButton_save_clicked();

    /** @brief 获取版本 */
    void on_pushButton_getVersion_clicked();

    void on_lineEdit_rec_id_textChanged(const QString &arg1);

    void on_pushButton_data_mode_clicked();

    void on_pushButton_6_clicked();

private:
    void setCheckID(int id);
    //进入调试模式
    void CAN_CMD_DebugModeSW(uint8_t sw);

    Ui::RadarConfig *ui;

    Devices::Can::DeviceManager *mDeviceManager{0};
    int checkCanId ;
    QMutex mSendDataMutex; //广成盒子貌似不能多线程同时发送数据，同时发送会导致后面的线程一直运行，无法退出
};

} // namespace Functions

#endif // RADARCONFIG_H
