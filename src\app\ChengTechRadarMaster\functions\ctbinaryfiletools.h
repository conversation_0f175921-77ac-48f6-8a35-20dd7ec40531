﻿#ifndef CTBINARYFILETOOLS_H
#define CTBINARYFILETOOLS_H

#include <QWidget>

#include <QtEndian>
#include <QFile>
#include <QTextStream>
#include <QDebug>

#include <devices/canframe.h>

class CTBinaryFile;

namespace Ui {
class CTBinaryFileTools;
}

class CTBinaryFileTools : public QWidget
{
    Q_OBJECT

public:
    explicit CTBinaryFileTools(QWidget *parent = nullptr);
    ~CTBinaryFileTools();

private slots:
    void closed();
    void readEnd();
    void writeIndex(int index);

    void frameRecieved(const Devices::Can::CanFrame &frame);

    void on_pushButtonBinaryFile_clicked();

    void on_pushButtonOpenBinaryFile_clicked();

    void on_pushButtonExport_clicked();

private:
    Ui::CTBinaryFileTools *ui;

    CTBinaryFile *mCTBinaryFile{0};
};

class CTBinaryFile : public QObject
{
    Q_OBJECT
public:
#pragma pack(1)
    typedef struct CTBinaryFileHeaderBase
    {
        unsigned int       mVersion;            // 上位机版本
        unsigned long long mCreateTime;         // 创建时间
        unsigned char      mFileIndex;          // 文件计数
        unsigned char      mReservedDataCount;  // 预留字节数量
        unsigned char      mProtocolCount;      // 协议数量
    }CTBinaryFileHeaderBase;

    typedef struct CTBinaryFileHeader
    {
        CTBinaryFileHeaderBase mHeaderBase;
        unsigned char mProtocols[0xFF];          // 协议
        unsigned char mReservedData[0xFF];       // 预留字节
    }CTBinaryFileHeader;

    //帧2进制保存格式-数据头
    typedef struct CTBinaryFileBlockHeader
    {
        unsigned char  mType;                   // 数据类型 1：雷达数据 2：真值数据 3：视频数据
        unsigned char  mDeviceID;               // 设备ID
        unsigned long long mCreateTime;         // 创建时间(ms时间戳)
        unsigned int mMutilFileIndex;           // 多文件计数
        unsigned int mCurrentFileIndex;         // 本文件计数
        unsigned int mBlockIndex;               // 数据块计数
        unsigned short mBodyLength;             // 数据主体长度
    }CTBinaryFileBlockHeader;

    //帧进制保存格式-数据体
    typedef struct CTBinaryFileData
    {
        unsigned char  mType;                   // 帧类型 0:can 1:canfd
        unsigned char  mExpand;                 // 是否位拓展帧 0:标准帧 1:拓展帧
        unsigned long long mTimestemp;          // 时间戳
        unsigned char  mLength;                 // 数据长度
        unsigned int mID;                       // 帧id
//        unsigned char *mData;                   // 数据
    }CTBinaryFileData;

#pragma pack()

    typedef struct RadarDataLocation
    {
        unsigned char  mType;                   // 数据类型 1：雷达数据 2：真值数据 3：视频数据
        unsigned char  mDeviceID;               // 设备ID
        unsigned int   mLocation;
    }RadarDataLocation;

    explicit CTBinaryFile();
    ~CTBinaryFile();

    bool open(const QString &filename);
    bool close();
    bool exportFile(int skipFrame, int saveTotal, const QString &filename);
    bool stopExport();
    bool startRead(int skipFrame);
    bool stopRead();

    bool isOpened() const { return mOpened; }
    bool isExporting() const { return mExporting; }
    const QString &errorString() const { return mErrorString; }

    const CTBinaryFileHeader &fileHeader() const { return mCTBinaryFileHeader; }
    unsigned int radarFrameCount() const { return mRadarDataLocations.size(); }
    unsigned int radarFrameCount(unsigned char id) const { return mRadarFrameCount[id]; }
    unsigned int radarFirstFrameIndex(unsigned char id) const { return mRadarFirstFrameIndex[id]; }

signals:
    void opened();
    void closed();
    void readBegin();
    void readEnd();
    void writeIndex(int index);
    void firstFrame(int radarID, unsigned int rawIndex, unsigned int trakIndex);

public slots:
    void run();

private:
    bool radeHeader();
    bool wirteHeader();
    bool skip(int skipFrame);
    bool readBlockHeaders();
    bool readBlockHeader(CTBinaryFileBlockHeader &blockHeader);
    bool readBlockData(const CTBinaryFileBlockHeader &blockHeader);
    bool writeBlock(CTBinaryFileBlockHeader blockHeader);
    bool readNextBlock();

    CTBinaryFileHeader mCTBinaryFileHeader;

    QFile       mFile;
    QFile mFileExport;
    int mHeaderDataLength{0};
    unsigned int mResivedSize{0};
    bool mOpened{false};
    bool mReading{false};
    bool mExporting{false};
    int mSaveFrameTotal{-1};
    int mSaveFrameCount{0};
    QString mErrorString;

    unsigned char mRadarCount{ 0 };
    unsigned int  mRadarFrameCount[0xF];
    unsigned int  mRadarFirstFrameIndex[0xF];
    QVector<RadarDataLocation> mRadarDataLocations;
};

#endif // CTBINARYFILETOOLS_H
