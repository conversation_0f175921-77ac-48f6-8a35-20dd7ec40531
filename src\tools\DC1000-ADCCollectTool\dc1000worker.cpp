﻿#include "dc1000worker.h"

#include "networkudpserver.h"
#include "CANDeviceZLG.h"
#include "cansaveworker.h"
#include "cameraworker.h"
#include "devicefileasc.h"

#include <iostream>
#include <math.h>
#include <QDir>

#include <QDebug>

#define MB 1048576

#define BIG_LITTLE_SWAP16(x)        ( (((*(short int *)&x) & 0xff00) >> 8) | \
                                      (((*(short int *)&x) & 0x00ff) << 8) )

#define BIG_LITTLE_SWAP32(x)        ( (((*(long int *)&x) & 0xff000000) >> 24) | \
                                      (((*(long int *)&x) & 0x00ff0000) >> 8) | \
                                      (((*(long int *)&x) & 0x0000ff00) << 8) | \
                                      (((*(long int *)&x) & 0x000000ff) << 24) )


typedef struct
{
#if IS_BIG_ENDIAN==1
    uint64_t outputMode               : 2;
    uint64_t carVelMode               : 2;
    uint64_t workMode                 : 4;
    uint64_t sendExtInfo              : 2;
    uint64_t estimatedSpeedCfgExpand  : 2;
    uint64_t angleMapperMode                    : 2;
    uint64_t sendYawRateMode                  : 2;
    uint64_t canBaudrateMode                  : 2;
    uint64_t sendExtInfo2                           : 2;
    uint64_t dbgMode                  : 2;
    uint64_t cohesionMode             : 2;
    uint64_t canMode                  : 2;
    uint64_t protVer                  : 4;
    uint64_t resv                                  : 34;
#else
    uint64_t resv                     : 34;
    uint64_t protVer                  : 4;
    uint64_t canMode                  : 2;
    uint64_t cohesionMode             : 2;
    uint64_t dbgMode                  : 2;
    uint64_t sendExtInfo2                           : 2;
    uint64_t canBaudrateMode                  : 2;
    uint64_t sendYawRateMode                  : 2;
    uint64_t angleMapperMode                    : 2;
    uint64_t estimatedSpeedCfgExpand  : 2;
    uint64_t sendExtInfo              : 2;
    uint64_t workMode                 : 4;
    uint64_t carVelMode               : 2;
    uint64_t outputMode               : 2;
#endif
}stWorkModeMsg;

enum DC1000Commands {
    RESET_FPGA_CMD_CODE              = 0x01,
    RESET_AR_DEV_CMD_CODE            = 0x02,
    CONFIG_FPGA_GEN_CMD_CODE         = 0x03,
    CONFIG_EEPROM_CMD_CODE           = 0x04,
    RECORD_START_CMD_CODE            = 0x05,
    RECORD_STOP_CMD_CODE             = 0x06,
    PLAYBACK_START_CMD_CODE          = 0x07,
    PLAYBACK_STOP_CMD_CODE           = 0x08,
    SYSTEM_CONNECT_CMD_CODE          = 0x09,
    SYSTEM_ERROR_CMD_CODE            = 0x0A,
    CONFIG_PACKET_DATA_CMD_CODE      = 0x0B,
    CONFIG_DATA_MODE_AR_DEV_CMD_CODE = 0x0C,
    INIT_FPGA_PLAYBACK_CMD_CODE      = 0x0D,
    READ_FPGA_VERSION_CMD_CODE       = 0x0E,
};

static char DATA[1456];

void netReceived(unsigned short port, int len, unsigned char *data, void *obj)
{
    DC1000Worker *pWorker = (DC1000Worker*)obj;
    pWorker->pushFrame(port, len, data);
}

DC1000Worker::DC1000Worker(CANDeviceZLG *canDevice, CANSaveWorker *canSaveWorker, CameraWorker *cameraWorker, QObject *parent)
    : QObject(parent), mCANDeviceZLG(canDevice), mCANSaveWorker(canSaveWorker), mCameraWorker(cameraWorker)
{
    mNetworkUDPServer = new NetworkUDPServer(netReceived, this, "192.168.33.30", 4098, "192.168.33.180", 4098, true);
    mNetworkUDPServerConfig = new NetworkUDPServer(netReceived, this, "192.168.33.30", 4096, "192.168.33.180", 4096, true);
    mDeviceFileASC = new DeviceFileASC;

    connect(this, &DC1000Worker::opened, this, &DC1000Worker::config);
    connect(this, &DC1000Worker::frameReceived, this, &DC1000Worker::parse);

    memset(DATA, 0, sizeof (DATA));
}

bool DC1000Worker::open(Type openType)
{
    mType = openType;
    mConnected = false;
    mSafeQueue.clear();

    if (!mNetworkUDPServer->open()) {
        mErrorString = mNetworkUDPServer->errorstring();
        qDebug() << __FUNCTION__ << __LINE__ << mNetworkUDPServer->errorstring().c_str();
        return false;
    }
    if (!mNetworkUDPServerConfig->open()) {
        mErrorString = mNetworkUDPServerConfig->errorstring();
        qDebug() << __FUNCTION__ << __LINE__ << mNetworkUDPServerConfig->errorstring().c_str();
        mNetworkUDPServer->close();
        return false;
    }

    mOpened = true;

    emit opened();

    return true;
}

void DC1000Worker::close()
{
    mOpened = false;
    mConnected = false;

    mNetworkUDPServer->close();
    mNetworkUDPServerConfig->close();

    emit clientConnected(0, mConnected);
}

void DC1000Worker::pushFrame(unsigned short port, int len, unsigned char *data)
{
    mSafeQueue.push(NETFrame(port, len, data));
    emit frameReceived();
}

void DC1000Worker::radarReceived(int radarID)
{
//    qDebug() << __FUNCTION__ << __LINE__ << radarID;
    if (mRadarAlready) {
        emit showLog(QString::fromLocal8Bit("收到雷达【%1】结束帧 - %2").arg(radarID).arg(++mRadarFrameCount));
    }
    if (radarID == mRadarID) {
       mRadarReceived = true;
    }
}

void DC1000Worker::checkRadarReceived()
{
    if (mRadarReceivedCheckCount++ >= 1000) {
        emit showLog(QString::fromLocal8Bit("未收到雷达【%1】结束帧").arg(mRadarID));
        return;
    }
    if (!mRadarReceived) {
        QTimer::singleShot(10, this, &DC1000Worker::checkRadarReceived);
        return;
    }
    playbackStart();
}

void DC1000Worker::stop()
{
    switch (mType) {
    case Record:
        stopRadar();
        mTimerStop.singleShot(5000, this, &DC1000Worker::stopCollect);
        break;
    case Playback:
        mTimerStop.singleShot(500, this, &DC1000Worker::stopPlayback);
        break;
    }
}

void DC1000Worker::start(int frameSize, unsigned long long size, int canChannel, int chirpCount, int radarID, int vechileJump,
                         const QString &ADCFilename, const QString &CANFilename)
{
    mFrameSize = frameSize;
    mNeedSize = size;
    mCanChannel = canChannel;
    mChirpSize = frameSize / chirpCount;
    mChirpCount = chirpCount;
    mRadarID = radarID;
    mVechileJump = vechileJump;
    mADCFilename = ADCFilename;
    mCANFilename = CANFilename;

    qDebug() << __FUNCTION__ << __LINE__ << frameSize << mChirpCount;

    mCollecting = false;
    mSequenceNumber = 0;
    mCollectSize = 0;
    mSavedSize = 0;
    mColletCout = 0;
    mCurrntChirp = 0;

    mStarted = true;

    switch (mType) {
    case Record:
        prepareRecord();
        break;
    case Playback:
        preparePlayback();
        break;
    }
}

void DC1000Worker::startCollect()
{
    mSequenceNumber = 0;
    mCollectSize = 0;
    mSavedSize = 0;
    mColletCout = 0;
    mRadarCollectCount = 0;

    // 雷达开始
    quint32 frameCnt = mNeedSize / mFrameSize;
    frameCnt = BIG_LITTLE_SWAP32(frameCnt);
    QByteArray data = QByteArray::fromHex("43 54 43 54 00 00 00 00");
    data[4] = ( (char*)&frameCnt )[0];
    data[5] = ( (char*)&frameCnt )[1];
    data[6] = ( (char*)&frameCnt )[2];
    data[7] = ( (char*)&frameCnt )[3];
    CANFrame frame(mCanChannel, 0x3F9, (unsigned char*)data.data(), data.length(), true, false);
    mCollecting = mCANDeviceZLG->sendFrame(frame);
    if (!mCollecting) {
        emit showLog(QString::fromLocal8Bit("向雷达下发采集指令失败! %1").arg(data.toHex(' ').data()));
        return;
    }
    mRadarFrameCount = 0;
    emit showLog(QString::fromLocal8Bit("向雷达下发采集指令成功. %1").arg(data.toHex(' ').data()));
    emit collectStatusChanged();
}

void DC1000Worker::stopCollect()
{
    mTimerStop.stop();
    mCollecting = false;
    mStarted = false;
    stopSave();

    if (mType == Record) {
        qDebug() << __FUNCTION__ << __LINE__ << mFrameSize << mRadarCollectCount;
        emit showLog(QString::fromLocal8Bit("雷达采集数据大小      ：%1(%2)\n"
                                            "采集板采集数据大小    ：%3/%4(%5)\n"
                                            "保存ADC文件大小      ：%6(%7)\n"
                                            "ADC文件大小差值      ：%8\n")
                     .arg(mRadarCollectCount * mFrameSize).arg(mRadarCollectCount)
                     .arg(mSequenceNumber).arg(mCollectSize).arg(mCollectSize / mFrameSize)
                     .arg(mSavedSize).arg(mSavedSize / mFrameSize)
                     .arg(mCollectSize - mSavedSize));
    }
    emit collectStatusChanged();
}

void DC1000Worker::radarAlready(unsigned int status)
{
    qDebug() << __FUNCTION__ << __LINE__ << mCollecting << mStarted << status;
    if (mCollecting || !mStarted || status != 1) {
        return;
    }
    emit showLog(QString::fromLocal8Bit("雷达准备就绪."));
    mRadarAlready = true;

    QByteArray data = getDC1000Data(RECORD_STOP_CMD_CODE);
    emit showLog(QString::fromLocal8Bit("【RECORD_STOP_CMD_CODE】%1").arg(data.toHex(' ').data()));
    if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
        emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
        return;
    }
    data = getDC1000Data(RECORD_START_CMD_CODE);
    emit showLog(QString::fromLocal8Bit("【RECORD_START_CMD_CODE】%1").arg(data.toHex(' ').data()));
    if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
        emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
    }
}

void DC1000Worker::radarCollectStoped(unsigned int count)
{
    mRadarCollectCount = count;
    emit showLog(QString::fromLocal8Bit("雷达结束采集. 采集帧数：%1").arg(mRadarCollectCount));
    mRadarAlready = false;
}

void DC1000Worker::startPlayback()
{
    emit showLog(QString::fromLocal8Bit("采集板回灌准备就绪."));
    mCollecting = true;
    emit collectStatusChanged();

    // 发送雷达DEBUG模式
    sendRadarMode();

    playbackStart();
}

void DC1000Worker::stopPlayback()
{
    mTimerStop.stop();
    mCollecting = false;
    mStarted = false;
    stopSave();
    emit showLog(QString::fromLocal8Bit("采集板回灌结束."));
    emit collectStatusChanged();
}

void DC1000Worker::playbackStart()
{
    if (mCurrntChirp > mChirpCount) {
        return;
    }
    mRadarReceived = false;
    mRadarReceivedCheckCount = 0;
    QByteArray data = getDC1000Data(PLAYBACK_START_CMD_CODE);
    emit showLog(QString::fromLocal8Bit("【PLAYBACK_START_CMD_CODE】%1").arg(data.toHex(' ').data()));
    if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
        emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
    }
}

void DC1000Worker::playback_cmd1_frameInterrupt()
{
    // -> 0x07 PLAYBACK_START_CMD_CODE
    // <- 0x07 PLAYBACK_START_CMD_CODE
    // -> 发送数据
    // -> 0x0d INIT_FPGA_PLAYBACK_CMD_CODE
    // <- 0x0d INIT_FPGA_PLAYBACK_CMD_CODE
    // -> 0x08 PLAYBACK_STOP_CMD_CODE
    // <- 0x08 PLAYBACK_STOP_CMD_CODE
    char buffer[1472];
    uint32_t *seq = (uint32_t*)buffer;
    (*seq) = 0;

    int length = cmd1_frameInterrupt.length();
    for (int len = 0; len < length;) {
        int size = ((length - len) >= 1468) ?  1468 : (length - len);
        memcpy(buffer + 4, cmd1_frameInterrupt.mid(len, size).data(), size);
        (*seq)++;
        len += size;
        qDebug() << __FUNCTION__ << __LINE__ << seq << len << size;
        QByteArray data = QByteArray::fromRawData(buffer, size + 4);
        if (mNetworkUDPServer->sendData( data.data(), data.size() ) < 0) {
            emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
        }
    }

    QByteArray data = getDC1000Data(INIT_FPGA_PLAYBACK_CMD_CODE);
    emit showLog(QString::fromLocal8Bit("【INIT_FPGA_PLAYBACK_CMD_CODE】%1").arg(data.toHex(' ').data()));
    if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
        emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
    }
}

static void adcDecode(short* inputVec, short channelNum, short sampleNumPerChirp, short laneNum, short* outputVec) {
    //要求：len(intputVec)==channelNum*sampleNumPerChirp*2
    //默认情况下，channelNum = 4;sampleNumPerChirp=256,laneNum = 2;
    unsigned int laneIdx,IQIdx,sampleIdx,channelIdx;

    for(unsigned int i = 0; i < channelNum * sampleNumPerChirp * 2U; i++){
        laneIdx = i % laneNum;
        IQIdx = ((i % (laneNum * 2U)) / 2);
        sampleIdx = (i / laneNum / 2U) % (sampleNumPerChirp / laneNum);
        channelIdx = (i / 2 / sampleNumPerChirp);

        outputVec[IQIdx+(sampleIdx * laneNum + laneIdx) * 2 + channelIdx * sampleNumPerChirp * 2U] = inputVec[i];
    }
}

static unsigned char swapBitsInByte(unsigned char byte) {
    unsigned char result = 0;
    for (int i = 0; i < 4; i++) {
        // 通过位运算取出指定位置的位
        unsigned char leftBit = (byte >> i) & 0x01;
        unsigned char rightBit = (byte >> (7 - i)) & 0x01;

        // 将交换后的位设置到新字节的对应位置
        if (leftBit) {
            result |= (1 << (7 - i));
        }
        if (rightBit) {
            result |= (1 << i);
        }
    }
    return result;
}

void DC1000Worker::playbackData()
{
    if (mCollectSize >= mFileLength) {
        emit showLog(QString::fromLocal8Bit("数据文件发送结束! %1-%2").arg(mFileLength).arg(mCollectSize));
        return;
    }
    if (mCurrntChirp == 0) {
        if (!sendVehicle()) {
            emit showLog(QString("CAN send vechicle error!"));
            return;
        }
        playback_cmd1_frameInterrupt();
        return;
    }

    // -> 0x07 PLAYBACK_START_CMD_CODE
    // <- 0x07 PLAYBACK_START_CMD_CODE
    // -> 发送数据
    // -> 0x0d INIT_FPGA_PLAYBACK_CMD_CODE
    // <- 0x0d INIT_FPGA_PLAYBACK_CMD_CODE
    // -> 0x08 PLAYBACK_STOP_CMD_CODE
    // <- 0x08 PLAYBACK_STOP_CMD_CODE

    char buffer[1472];
    uint32_t *seq = (uint32_t*)buffer;
    (*seq) = 0;

    QByteArray sendData;
    QByteArray chirpData = mFile.read(mChirpSize);
    int chirpLen = chirpData.size();
    QByteArray decodeData(chirpLen, 0);
    mCollectSize += chirpLen;

    short *outData = (short *)decodeData.data();
    adcDecode((short *)chirpData.data(), 4, chirpLen / 2 / 4 / 2, 2, outData);

    uint8_t *pData = (uint8_t *)outData;
    for (int i = 0; i <= (chirpLen - 4); i += 4) {
        uint32_t *data = (uint32_t *)(pData + i);
        *data = BIG_LITTLE_SWAP32(*data);
    }

    for (int i = 0; i < chirpLen; ++i) {
        uint8_t data = swapBitsInByte(pData[i]);
        switch (i % 4) {
        case 0:
        {
            uint32_t data1 = (((uint32_t)data) << 1) | 0x00060001;
            data1 = BIG_LITTLE_SWAP32(data1);
            sendData.append((char *)&data1, sizeof (data1));
            uint32_t data2 = (((uint32_t)data) << 1) | 0x00020001;
            data2 = BIG_LITTLE_SWAP32(data2);
            sendData.append((char *)&data2, sizeof (data2));
            break;
        }
        case 1:
        case 2:
        case 3:
        {
            uint32_t data1 = (((uint32_t)data) << 1) | 0x00040001;
            data1 = BIG_LITTLE_SWAP32(data1);
            sendData.append((char *)&data1, sizeof (data1));
            uint32_t data2 = (((uint32_t)data) << 1) | 0x00000001;
            data2 = BIG_LITTLE_SWAP32(data2);
            sendData.append((char *)&data2, sizeof (data2));
            break;
        }
        }
    }

    sendData.append(cmd2_chirpInterrupt);
    int length = sendData.length();
    for (int len = 0; len < length;) {
        int size = ((length - len) >= 1468) ?  1468 : (length - len);
        memcpy(buffer + 4, sendData.mid(len, size).data(), size);
        (*seq)++;
        len += size;
//        qDebug() << __FUNCTION__ << __LINE__ << seq << len << size;
        QByteArray data = QByteArray::fromRawData(buffer, size + 4);
        if (mNetworkUDPServer->sendData( data.data(), data.size() ) < 0) {
            emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
        }
    }

    QByteArray data = getDC1000Data(INIT_FPGA_PLAYBACK_CMD_CODE);
    emit showLog(QString::fromLocal8Bit("【INIT_FPGA_PLAYBACK_CMD_CODE】%1").arg(data.toHex(' ').data()));
    if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
        emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
    }
}

void DC1000Worker::playbackStop()
{
    QByteArray data = getDC1000Data(PLAYBACK_STOP_CMD_CODE);
    emit showLog(QString::fromLocal8Bit("【PLAYBACK_STOP_CMD_CODE】%1").arg(data.toHex(' ').data()));
    if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
        emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
    }
}

void DC1000Worker::config()
{
    QByteArray data = getDC1000Data(SYSTEM_CONNECT_CMD_CODE);
    emit showLog(QString::fromLocal8Bit("【SYSTEM_CONNECT_CMD_CODE】%1").arg(data.toHex(' ').data()));
    if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
        emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
    }
}

void DC1000Worker::parse()
{
    std::shared_ptr<NETFrame> pFrame = mSafeQueue.pop();
    if (pFrame) {
        parseFrame(*pFrame.get());
    }
}

bool DC1000Worker::sendRadarMode()
{
    int ret = false;
    QByteArray _0x300Data = QByteArray::fromHex("31 58 AF 80 00 00 00 00");
    CANFrame _0x300Frame(mCanChannel, 0x300 | (mRadarID & 0xF), (unsigned char *)_0x300Data.data(), _0x300Data.length(), true, false);
    ret = mCANDeviceZLG->sendFrame(_0x300Frame);

    QByteArray data(8, 0);
    stWorkModeMsg *p = (stWorkModeMsg*)data.data();
    p->resv = 0x0;
    p->protVer = 0x0;
    p->canMode = 0x0;
    p->cohesionMode = 0x0;
    p->dbgMode = 0x2;
    p->sendExtInfo2 = 0x0;
    p->canBaudrateMode = 0x0;
    p->sendYawRateMode = 0x0;
    p->angleMapperMode = 0x0;
    p->estimatedSpeedCfgExpand = 0x0;
    p->sendExtInfo = 0x0;
    p->workMode = 0x0;
    p->carVelMode = 0x0;
    p->outputMode = 0x3;
    std::reverse(data.begin(), data.end());
    CANFrame _0x200Frame(mCanChannel, 0x200 | (mRadarID & 0xF), (unsigned char *)data.data(), data.length(), true, false);
    ret = ret && mCANDeviceZLG->sendFrame(_0x200Frame);
    qDebug() << __FUNCTION__ << __LINE__ << QString::number(_0x300Frame.id, 16) << QByteArray::fromRawData((char *)_0x300Frame.data, _0x300Frame.len).toHex(' ');
    qDebug() << __FUNCTION__ << __LINE__ << QString::number(_0x200Frame.id, 16) << QByteArray::fromRawData((char *)_0x200Frame.data, _0x200Frame.len).toHex(' ');


    return ret;
}

bool DC1000Worker::sendVehicle()
{
    int ret = false;
    CANFrame frame;
    unsigned int id = (0x3F0 | mRadarID);
    while (true || mVechileJump > 0){
        if (!mDeviceFileASC->readData(frame)) {
            emit showLog(QString("no vechicle frame!"));
            return false;
        }

        if (frame.id == (0x600 | mRadarID))
        {
            qDebug() << __FUNCTION__ << __LINE__ << QString::number(frame.id, 16) << QByteArray::fromRawData((char *)frame.data, frame.len).toHex(' ');
            if (!(ret = mCANDeviceZLG->sendFrame(frame))) {
                emit showLog(QString("CAN send header error!"));
            }
        } else if (frame.id == (0x3F0 | mRadarID))
        {
            mVechileJump--;
            qDebug() << __FUNCTION__ << __LINE__ << QString::number(frame.id, 16) << QByteArray::fromRawData((char *)frame.data, frame.len).toHex(' ');
            if (!(ret = ret && mCANDeviceZLG->sendFrame(frame))) {
                emit showLog(QString("CAN send vechicle error!"));
            }
            break;
        }
    }

    return ret;
}

void DC1000Worker::prepareRecord()
{
    // 保存启动
    emit showLog(QString::fromLocal8Bit("正在启动保存."));
    if (!startSave()) {
        emit showLog(QString::fromLocal8Bit("启动保存失败! %1").arg(mProjectSavePath));
        return;
    }
    emit showLog(QString::fromLocal8Bit("启动保存成功."));

    // 雷达准备
    stopRadar();
}

void DC1000Worker::preparePlayback()
{
    mDeviceFileASC->closeFile();
    if (!mDeviceFileASC->openFile(mCANFilename.toLocal8Bit().data(), true)) {
        emit showLog(QString::fromLocal8Bit("打开CAN文件失败! %1").arg(mCANFilename));
        return;
    }
    emit showLog(QString::fromLocal8Bit("打开CAN文件成功。 %1").arg(mCANFilename));

    if (mFile.isOpen()) {
        mFile.close();
    }

    mFile.setFileName(mADCFilename);
    if (!mFile.open(QIODevice::ReadOnly | QIODevice::Truncate)) {
        emit showLog(QString::fromLocal8Bit("打开ADC文件失败! %1").arg(mADCFilename));
        return;
    }
    mFileLength = mFile.size();
    emit showLog(QString::fromLocal8Bit("打开ADC文件成功。 %1").arg(mADCFilename));

    cmd1_frameInterrupt.clear();
    QFile file1(":/files/cmd1_frameInterrupt.bin");
    if (!file1.open(QIODevice::ReadOnly | QIODevice::Truncate)) {
        emit showLog(QString::fromLocal8Bit("打开cmd1_frameInterrupt文件失败! %1").arg(file1.fileName()));
        return;
    }
    cmd1_frameInterrupt = file1.readAll();
    file1.close();

    cmd2_chirpInterrupt.clear();
    QFile file2(":/files/cmd2_chirpInterrupt.bin");
    if (!file2.open(QIODevice::ReadOnly | QIODevice::Truncate)) {
        emit showLog(QString::fromLocal8Bit("打开cmd2_chirpInterrupt文件失败! %1").arg(file2.fileName()));
        return;
    }
    cmd2_chirpInterrupt = file2.readAll();
    file2.close();

    // 保存启动
    emit showLog(QString::fromLocal8Bit("正在启动保存."));
    if (!startSave()) {
        emit showLog(QString::fromLocal8Bit("启动保存失败! %1").arg(mProjectSavePath));
        return;
    }
    emit showLog(QString::fromLocal8Bit("启动保存成功."));

    QTimer::singleShot(100, this, &DC1000Worker::startPlayback);
}

bool DC1000Worker::startSave()
{
    if (!mkSavePath()) {
        return false;
    }

    emit saveFilename(mProjectSavePath);

    QString saveTime = mSaveTime.toString("yyyy-MM-dd hh-mm-ss-zzz");

    if (mType == Record) {
        QString adcFilename = QString("%1/%2.dat").arg(mProjectSavePath).arg(saveTime);
        QString errorFilename = QString("%1/%2_error.txt").arg(mProjectSavePath).arg(saveTime);
        if (!startSaveADC(adcFilename, errorFilename)) {
            return false;
        }
        if (mCameraWorker->isOpened()) {
            QString cameraFilename = QString("%1/%2.mp4").arg(mVideoSavePath).arg(saveTime);
            if (!mCameraWorker->startSave(cameraFilename.toStdString())) {
                stopSaveADC();
                return false;
            }
        }
    }

    QString canFilename = QString("%1/%2.asc").arg(mCANSavePath).arg(saveTime);
    QString objFilename = QString("%1/%2.csv").arg(mProjectSavePath).arg(saveTime);
    if (!mCANSaveWorker->startSave(canFilename, objFilename)) {
        if (mType == Record) {
            stopSaveADC();
            mCameraWorker->stopSave();
        }
    }

    return true;
}

void DC1000Worker::stopSave()
{
    if (mType == Record) {
        qDebug() << __FUNCTION__ << __LINE__ << "message";
        stopSaveADC();

        qDebug() << __FUNCTION__ << __LINE__ << "message";
        if (mCameraWorker->isSaving()) {
            mCameraWorker->stopSave();
        }
    }
    qDebug() << __FUNCTION__ << __LINE__ << "message";

    if (mCANSaveWorker->isSaving()) {
        mCANSaveWorker->stopSave();
    }
    qDebug() << __FUNCTION__ << __LINE__ << "message";
}

bool DC1000Worker::startSaveADC(QString filename, QString errorFilename)
{
    if (mpFileError) {
        fclose(mpFileError);
    }
    if (mpFile) {
        fclose(mpFile);
    }
    mpFileError = fopen(errorFilename.toLocal8Bit(), "w");
    if (!mpFileError) {
        return false;
    }
    mpFile = fopen(filename.toLocal8Bit(), "wb");
    if (!mpFile) {
        fclose(mpFileError);
        return false;
    }

    return true;
}

void DC1000Worker::stopSaveADC()
{
    if (mpFileError) {
        fclose(mpFileError);
        mpFileError = 0;
    }
    if (mpFile) {
        fclose(mpFile);
        mpFile = 0;
    }
}

void DC1000Worker::stopRadar()
{
    QByteArray data = QByteArray::fromHex("43 54 1A 2B 00 00 00 00");
    CANFrame frame(mCanChannel, 0x3F9, (unsigned char*)data.data(), data.length(), true, false);
    if (mCANDeviceZLG->sendFrame(frame)) {
        emit showLog(QString::fromLocal8Bit("向雷达下发准备/停止指令成功. %1").arg(data.toHex(' ').data()));
    } else {
        emit showLog(QString::fromLocal8Bit("向雷达下发准备/停止指令失败! %1").arg(data.toHex(' ').data()));
    }
}

void DC1000Worker::parseFrame(const NETFrame &frame)
{
    switch (frame.port) {
    case 4096:
        parseDC1000_4096(frame);
        break;
    case 4098:
        parseDC1000_4098(frame);
        break;
    }
}

void DC1000Worker::parseDC1000_4096(const NETFrame &frame)
{
    quint16 command = 0, status = 0;
    QByteArray _data;
    if (frame.len >= 8)
    {
        memcpy(&command, frame.data + 2, 2);
        memcpy(&status, frame.data + 4, 2);
//        qDebug() << __FUNCTION__ << __LINE__ << QString::number(command, 16) << status << _data.toHex(' ');
        switch (command) {
        case RESET_FPGA_CMD_CODE             :  // 0x01
            break;
        case RESET_AR_DEV_CMD_CODE           :  // 0x02
            break;
        case CONFIG_FPGA_GEN_CMD_CODE        :  // 0x03
        {
            switch (mType) {
            case Record:
            {
                QByteArray data = getDC1000Data(CONFIG_PACKET_DATA_CMD_CODE);
                emit showLog(QString::fromLocal8Bit("【CONFIG_PACKET_DATA_CMD_CODE】%1").arg(data.toHex(' ').data()));
                if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
                    emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
                }
            }
                break;
            case Playback:
                mConnected = true;
                emit clientConnected(frame.port, mConnected);
                break;
            default:
                break;
            }
        }
            break;
        case CONFIG_EEPROM_CMD_CODE          :  // 0x04
            break;
        case RECORD_START_CMD_CODE           :  // 0x05
            emit showLog(QString::fromLocal8Bit("采集板采集准备就绪."));
            startCollect();
            break;
        case RECORD_STOP_CMD_CODE            :  // 0x06
            break;
        case PLAYBACK_START_CMD_CODE         :  // 0x07
        {
           emit showLog(QString::fromLocal8Bit("回灌数据【开始】： %1 - %2").arg(mColletCout).arg(mCurrntChirp));
           playbackData();
        }
            break;
        case PLAYBACK_STOP_CMD_CODE          :  // 0x08
        {
            emit showLog(QString::fromLocal8Bit("回灌数据【结束】： %1 - %2").arg(mColletCout).arg(mCurrntChirp));
            mCurrntChirp++;
            if (mCurrntChirp <= mChirpCount) {
                QTimer::singleShot(30, this, &DC1000Worker::playbackStart);
//                playbackStart();
            } else if (mCollecting) {
                if (mCollectSize < mNeedSize) {
                    mColletCout++;
                    mCurrntChirp = 0;
                    checkRadarReceived();
                } else {
                    stopPlayback();
                }
            }
        }
            break;
        case SYSTEM_CONNECT_CMD_CODE         :  // 0x09
        {
            QByteArray data = getDC1000Data(READ_FPGA_VERSION_CMD_CODE);
            emit showLog(QString::fromLocal8Bit("【READ_FPGA_VERSION_CMD_CODE】%1").arg(data.toHex(' ').data()));
            if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
                emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
            }
        }
            break;
        case SYSTEM_ERROR_CMD_CODE           :  // 0x0A
            switch (status) {
            case 0x0100: // 雷达停止传输数据
            {
                emit showLog(QString::fromLocal8Bit("采集板停止采集. %1/%2/%3").arg(mColletCout).arg(mSequenceNumber).arg(mCollectSize));
                stopCollect();
            }
                break;
            case 0x01:
            case 0x02:
            case 0x03:
            case 0x08:
            default:
                emit showLog(QString("system error! %1").arg(status, 4, 16, QLatin1Char('0')));
                break;
            }
            break;
        case CONFIG_PACKET_DATA_CMD_CODE     :  // 0x0B
            mConnected = true;
            emit clientConnected(frame.port, mConnected);
            break;
        case CONFIG_DATA_MODE_AR_DEV_CMD_CODE:  // 0x0C
            break;
        case INIT_FPGA_PLAYBACK_CMD_CODE     :  // 0x0D
        {
            playbackStop();
        }
            break;
        case READ_FPGA_VERSION_CMD_CODE      :  // 0x0E
        {
            emit showLog(QString("FPGA_VERSION: %1").arg(_data.toHex().data()));
            // 下发配置
            QByteArray data = getDC1000Data(CONFIG_FPGA_GEN_CMD_CODE);
            emit showLog(QString::fromLocal8Bit("【CONFIG_FPGA_GEN_CMD_CODE】%1").arg(data.toHex(' ').data()));
            qDebug() << __FUNCTION__ << __LINE__ << data.toHex( ' ' );
            if (mNetworkUDPServerConfig->sendData( data.data(), data.size() ) < 0) {
                emit showLog(QString("network send error! %1").arg(data.toHex(' ').data()));
            }
        }
            break;
        default:
            break;
        }
    }
}

void DC1000Worker::parseDC1000_4098(const NETFrame &frame)
{
    if (!mCollecting) {
        return;
    }
    if (frame.len != 1466)
        qDebug() << __FUNCTION__ << __LINE__ << frame.len;
    int dataLength = frame.len - 10;
    quint32 sequenceNumber = *((quint32*)(frame.data));
    quint64 byteCount = *((quint64*)(frame.data + 4));
    byteCount &= 0x0000FFFFFFFFFFFF;

    if ((sequenceNumber - mSequenceNumber) != 1) {
        emit showLog( QString( "ERROR...   SN: %1, SN(need):%2 FrameNo:%3" )
                      .arg(sequenceNumber)
                      .arg(mSequenceNumber + 1)
                      .arg(mCollectSize / mFrameSize));
        QString error = QString("%1, %2, %3, %4, %5\n")
                .arg(mCollectSize / mFrameSize)
                .arg(mSequenceNumber)
                .arg(sequenceNumber)
                .arg(sequenceNumber - mSequenceNumber - 1)
                .arg((sequenceNumber - mSequenceNumber - 1) * 1456);
        qDebug() << __FUNCTION__ << __LINE__ << sequenceNumber << byteCount << mCollectSize << error;
        fwrite(error.toLocal8Bit(), sizeof (unsigned char), error.length(), mpFileError);
        quint64 size = byteCount - mCollectSize;
        qDebug() << __FUNCTION__ << __LINE__ << size;
        if (mpFile) {
            for (int i = mSequenceNumber + 1; i < sequenceNumber; ++i) {
                if (fwrite(DATA, sizeof (unsigned char), sizeof (DATA), mpFile) != sizeof (DATA)) {
                    emit showLog(QString::fromLocal8Bit("补【0】数据写入文件失败!"));
                    stop();
                }
                mSavedSize += sizeof (DATA);
            }
        }
    }
    mSequenceNumber = sequenceNumber;
//    qDebug() << __FUNCTION__ << __LINE__ << mSequenceNumber;

    mCollectSize = byteCount + dataLength;
    unsigned int frameNo = mCollectSize / mFrameSize;
    if (frameNo > mColletCout) {
        mColletCout = frameNo;
        emit showLog( QString( "Saving...   %1: %2 MB(%3 B)" )
                      .arg(frameNo, 4, 10).arg((double)mCollectSize / MB, 10, 'f').arg( mCollectSize ));
    }

    if (mpFile) {
        if (fwrite(frame.data + 10, sizeof (unsigned char), dataLength, mpFile) != dataLength) {
            emit showLog(QString::fromLocal8Bit("数据写入文件失败! %1").arg(mSequenceNumber));
            stop();
        }
        mSavedSize += dataLength;
    }
}

QByteArray DC1000Worker::getDC1000Data(quint16 command)
{
    QByteArray data = QByteArray::fromHex("5A A5");
    data.append((const char*)&command, 2);
    QByteArray body;
    switch (command) {
    case RESET_FPGA_CMD_CODE             :  // 0x01
        break;
    case RESET_AR_DEV_CMD_CODE           :  // 0x02
        break;
    case CONFIG_FPGA_GEN_CMD_CODE        :  // 0x03
        switch (mType) {
        case Record:
            body = QByteArray::fromHex("01 02 01 02 03 1e");
            break;
        case Playback:
            body = QByteArray::fromHex("01 02 02 02 03 1e");
            break;
        }
        break;
    case CONFIG_EEPROM_CMD_CODE          :  // 0x04
        break;
    case RECORD_START_CMD_CODE           :  // 0x05
        break;
    case RECORD_STOP_CMD_CODE            :  // 0x06
        break;
    case PLAYBACK_START_CMD_CODE         :  // 0x07
        break;
    case PLAYBACK_STOP_CMD_CODE          :  // 0x08
        break;
    case SYSTEM_CONNECT_CMD_CODE         :  // 0x09
        break;
    case SYSTEM_ERROR_CMD_CODE           :  // 0x0A
        break;
    case CONFIG_PACKET_DATA_CMD_CODE     :  // 0x0B
        body = QByteArray::fromHex("c0 05 6a 18 e8 03");
        break;
    case CONFIG_DATA_MODE_AR_DEV_CMD_CODE:  // 0x0C
        break;
    case INIT_FPGA_PLAYBACK_CMD_CODE     :  // 0x0D
        break;
    case READ_FPGA_VERSION_CMD_CODE      :  // 0x0E
        break;
    default:
        break;
    }
    quint16 size = body.size();
    data.append((const char *)(&size), 2);
    data.append(body);
    data.append(QByteArray::fromHex("AA EE"));


//    qDebug() << __FUNCTION__ << __LINE__ << data.toHex( ' ' );

    return data;
}

bool DC1000Worker::mkSavePath()
{
    if (mSavePath.isEmpty()) {
        mSavePath = "./data";
    }
    mSaveTime = QDateTime::currentDateTime();
    mProjectSavePath = QString("%1/%2").arg(mSavePath).arg(mSaveTime.toString("yyyy-MM-dd hh-mm-ss-zzz"));
    mCANSavePath = QString("%1/%2").arg(mProjectSavePath).arg("CAN");
    mVideoSavePath = QString("%1/%2").arg(mProjectSavePath).arg("Video");

    QDir logDir(mSavePath);
    if (!logDir.exists()) {
        logDir.setPath("");
        if (!logDir.mkpath(mSavePath)) {
                return false;
            }
    }

    logDir.setPath("");
    if (!logDir.mkpath(mProjectSavePath)) {
        return false;
    }

    logDir.setPath("");
    if (!logDir.mkpath(mCANSavePath)) {
        return false;
    }

    logDir.setPath("");
    if (!logDir.mkpath(mVideoSavePath)) {
        return false;
    }

    return true;
}
