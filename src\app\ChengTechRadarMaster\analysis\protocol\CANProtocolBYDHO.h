﻿#pragma once

#include "ianalysisprotocol.h"

#include <vector>

namespace Analysis {

namespace Protocol {
        class CANProtocolBYDHO :
            public IAnalysisProtocol
                {
                    Q_OBJECT
                public:
                    explicit CANProtocolBYDHO(AnalysisWorker *analysisWorker, QObject *parent = nullptr);

                    void setChannelRadarID(int *channelRadarID, int size);

                    bool analysisFrame(const Devices::Can::CanFrame &frame) override;
                    void paserType(bool _0x659) { mParse0x659 = _0x659; }

                signals:

                private:
                    bool parse_0x61A(const Devices::Can::CanFrame &frame);
                    bool parse_0x659(const Devices::Can::CanFrame &frame);
                    bool parse_0x61A_0x659(const Devices::Can::CanFrame &frame);
                    bool parse_0x62E_0x66D(const Devices::Can::CanFrame &frame);
                    bool parse_0x630_0x670(const Devices::Can::CanFrame &frame);
                    bool parse_0x64E_0x66F(const Devices::Can::CanFrame &frame);

                    bool viewObjValid{false};

                    bool mParse0x659{true};

                    int mBYDHDChannelRadarID[8]{4, 5, 6, 7, 0, 0, 0, 0};                ///< BYD高阶通道-雷达ID设置
                };
}
}

