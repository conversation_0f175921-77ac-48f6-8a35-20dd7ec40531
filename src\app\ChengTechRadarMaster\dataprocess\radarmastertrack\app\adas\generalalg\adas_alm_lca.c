﻿/**
 * @file adas_alm_lca.c
 * @brief 
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#include <stdbool.h>
#include <math.h>
#include <string.h>

#ifdef ALPSPRO_ADAS
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#elif defined (PC_DBG_FW)
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/system_mgr/typedefs.h"
#include "alg/track/rdp_clth_radar_lib.h"
#else
#include "app/rdp/rdp_clth_radar_lib.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "common/include/vehicle_cfg.h"
#endif

typedef struct _ADAS_LCA_Param{
    float ttc_buf;              // TTC缓冲buf
    float spd_buf;              // 速度缓冲
    float xspd_buf;             // 横向速度缓冲 横向速度超过一定值时抑制报警. 对已报警目标增加缓冲.
    float range_buf;            // 距离缓冲
    float range_buf_near;       // 后车靠近自车时距离buffer
    float x_dyn_buf;
    float roadline_buf0;        // 本车边线buffer
    float roadline_buf1;        // 目标边线buffer
    float radius_threshold;     // 转弯半径阈值
    float radiusAbs;            // 转弯半径
    bool  alarmFlag;            // 报警标志
    bool  stopAlarm;            // 报警抑制
    bool isTargetInAlmZone;     // 是否处于LCA报警区域
	bool ispreTTCFit;           // TTC预满足
    bool isTTCFit;              // TTC是否满足
    float radiusfardif_threshold;
    float radiusneardif_threshold;
    float xfar_threshold;
    float xnear_threshold;
}ADAS_LCA_Param;



/**
 * @brief LCA 参数初始化
 * 
 * @param lcaparam LCA报警相关参数
 * @param pfreezedVehDyncData 车辆动态数据 表征冻结的车辆动态数据，为数据处理和报警逻辑提供数据源
 */
static void ADAS_LCA_initParams(ADAS_LCA_Param *lcaparam, const VDY_DynamicEstimate_t *pfreezedVehDyncData)
{
    lcaparam->ttc_buf = 0.0f;
    lcaparam->spd_buf = 0.0f;
    lcaparam->xspd_buf = 0.0f;
    lcaparam->range_buf = 0.0f;
    lcaparam->range_buf_near = 0.0f;
    lcaparam->x_dyn_buf = 0.0f;
    lcaparam->roadline_buf0 = 0.0f;
    lcaparam->roadline_buf1 = 0.0f;
    lcaparam->radiusAbs = fabsf(pfreezedVehDyncData->vdyCurveRadius);
    lcaparam->alarmFlag = 0;
    lcaparam->stopAlarm = false;
    lcaparam->isTargetInAlmZone = false;
	lcaparam->ispreTTCFit = false;
    lcaparam->isTTCFit = false;    
}

/**
 * @brief 边线判断条件 本车边线buffer，速度越快本车离边线越远
 * @param lcaparam
 * @param BSDVelSpeedVal
 */
static void ADAS_LCA_cacBuffer(ADAS_LCA_Param *lcaparam, const OBJ_NODE_STRUCT *pobjPath, uint8_t i, float BSDVelSpeedVal)
{
    if (fabsf(BSDVelSpeedVal) >= 35)    //本车边线buffer
    {
        lcaparam->roadline_buf0 = LCA_CARSPEED_ROADLINEBUF_HIGH;
    }
    else if (fabsf(BSDVelSpeedVal) >= 20)
    {
        lcaparam->roadline_buf0 = LCA_CARSPEED_ROADLINEBUF_MID;
    }
    else
    {
        lcaparam->roadline_buf0 = LCA_CARSPEED_ROADLINEBUF_LOW;
    }

    if (fabsf(pobjPath[i].vy * 3.6f) >= 30) //目标边线buffer
    {
        lcaparam->roadline_buf1 = LCA_OBJSPEED_ROADLINEBUF_HIGH;
    }
    if (fabsf(pobjPath[i].vy * 3.6f) >= 15)
    {
        lcaparam->roadline_buf1 = LCA_OBJSPEED_ROADLINEBUF_MID;
    }
    else
    {
        lcaparam->roadline_buf1 = LCA_OBJSPEED_ROADLINEBUF_LOW;
    }

    if((pobjPath[i].vy) < 3)    //当前测试表现lca经常出现目标车比较远的横向偏差，速度大x方向漂移大，所以调整为速度变化的处理，放大报警区域
    {
        lcaparam->x_dyn_buf = 0;
    }
    else if((pobjPath[i].vy) > 10) //10+
    {
        lcaparam->x_dyn_buf = LCA_VYSPEED_HIGH_X_DYN_BUF;
    }
    else
    {
        lcaparam->x_dyn_buf = LCA_VYSPEED_RATE_X_DYN_BUF * (pobjPath[i].vy);
    }

    lcaparam->radiusfardif_threshold = LCA_FAR_ALARM_WIDTH_SIZE;
    lcaparam->radiusneardif_threshold = LCA_WARNING_WIDTH_MIN + LCA_CURVE_COMPENSATE_X;
    lcaparam->xfar_threshold = LCA_FAR_ALARM_WIDTH_SIZE + lcaparam->x_dyn_buf;
    lcaparam->xnear_threshold = LCA_WARNING_WIDTH_MIN;
    lcaparam->radius_threshold = LCA_TURNING_RADIUS_MIN;

    if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA))  //上次达到报警条件，之后都扩大报警区域
    {
        lcaparam->spd_buf = LCA_ACTIVE_MIN_SPEED_BUF;
        lcaparam->xspd_buf = LCA_ABNORMAL_VX_THRESHOLD_BUF;
        lcaparam->ttc_buf = LCA_TTC_BUF;           //增加buf用于防止闪烁
        lcaparam->range_buf = LCA_LENGTH_BUF;
        lcaparam->radiusfardif_threshold += LCA_FAR_ALARM_WIDTH_SIZE_BUF;
        lcaparam->radiusneardif_threshold += LCA_NEAR_ALARM_WIDTH_SIZE_BUF;
        lcaparam->xfar_threshold += LCA_FAR_ALARM_WIDTH_SIZE_BUF;
        lcaparam->xnear_threshold += LCA_NEAR_ALARM_WIDTH_SIZE_BUF;
        lcaparam->radius_threshold += LCA_TURNING_RADIUS_MIN_BUF;

        if (fabs(pobjPath[i].vyByCarSpeed) < 2.0f)
        {
            lcaparam->range_buf_near = 0.5f;    //不满足BSD则加buffer
        }
    }
}

/**
 * @brief TTC是否满足.
 * 
 * @param lcaparam LCA报警相关参数
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 */
static void ADAS_LCA_TTCFit(ADAS_LCA_Param *lcaparam, const OBJ_NODE_STRUCT *pobjPath, uint8_t i)
{
    /*策略五：过滤异常跟踪点*/
    /* VX阈值过滤异常点易导致隔车道切入目标抑制报警, 导致报警偏晚. 以及大车分裂时横向测速不准导致中断. */
    if(fabsf(pobjPath[i].vx) > (LCA_ABNORMAL_VX_THRESHOLD + lcaparam->xspd_buf))
    {
        lcaparam->stopAlarm = true;
    }
	/* 预触发时间满足 */
    if (pobjPath[i].y <= (pobjPath[i].vy * (LCA_TTC_PRE_TIME)))
	{
		lcaparam->ispreTTCFit = true;
	}
    if((pobjPath[i].y <= (pobjPath[i].vy * (LCA_TTC + lcaparam->ttc_buf))))
    {
        lcaparam->isTTCFit = true;
    }
}

/**
 * @brief 此跟踪点的可信度判断
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return flag
 */
uint8_t ADAS_LCA_checkTrkObjReliability(const OBJ_NODE_STRUCT *pobjPath, uint8_t i)
{
    uint8_t flag = 0;
    float reliability_max = 0.0f;

    reliability_max = LCA_RELIABILITY_MIN;

    if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA))
    {
        reliability_max += LCA_RELIABILITY_BUF;
    }

    if(pobjPath[i].TrkObjReliability >= reliability_max)
    {
        flag = 1;
    }

    return flag;
}

/**
 * @brief 目标是否满足LCA报警条件
 * 
 * @param lcaparam LCA报警相关参数
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param BSDVelSpeedVal 自车速度
 * @param RoadLine 边线
 * @param centerx 自车转弯半径
 */
static void ADAS_LCA_checkTargetInZone( ADAS_LCA_Param *lcaparam, const OBJ_NODE_STRUCT *pobjPath, uint8_t i, float BSDVelSpeedVal, float RoadLine, float centerx, ALARM_OBJECT_T *pobjAlm)
{
    uint8_t posflag = 0;        // 位置是否在LCA区域. 0:否 1:是
    uint8_t speedflag = 0;      // 速度是否符合. 0:否 1:是
    uint8_t roadlineflag = 0;   // 边线符合 0:否 1:是
    uint8_t centerxflag = 0;    // 转弯半径是否符合 0:否 1:是
    uint8_t reliability = 0;

    reliability = ADAS_LCA_checkTrkObjReliability(pobjPath, i);

    // 防止大转弯时候出现在边上的目标触发的报警
    if ((pobjPath[i].y <= (LCA_LENGTH + lcaparam->range_buf)) && (pobjPath[i].y >= (BSD_LENGTH - lcaparam->range_buf_near)) && (pobjPath[i].x <= (70 / 2)))
    {
        posflag = 1;
    }

    // 目标速度大于2.0m/s
    if ((pobjPath[i].vyByCarSpeed > (LCA_TARGET_SPEED_MIN - lcaparam->spd_buf)) && ((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U))
    {
        speedflag = 1;
    }

    // 当转弯半径偏小时, 目标Y轴又相距较远, Y对转弯半径的影响会增大. 导致自车向相反方向打方向盘时, 转弯半径的值远大于X的值.

    // 急转弯不做判断. 根据转弯半径确认报警区域.
    if ((fabsf(centerx) > lcaparam->radius_threshold) && 
		(((centerx < 0) && ((pobjPath[i].rcDis - lcaparam->radiusAbs) <= lcaparam->radiusfardif_threshold) && ((pobjPath[i].rcDis - lcaparam->radiusAbs) > lcaparam->radiusneardif_threshold )) ||
        ((centerx > 0) && ((lcaparam->radiusAbs - pobjPath[i].rcDis) <= lcaparam->radiusfardif_threshold) && ((lcaparam->radiusAbs - pobjPath[i].rcDis) > lcaparam->radiusneardif_threshold )) ||
		((fabsf(centerx) > 300) && (pobjPath[i].x <= lcaparam->xfar_threshold) && (pobjPath[i].x > lcaparam->xnear_threshold))))
    {
        centerxflag = 1;
    }

    // 非转弯场景二次判断 
    // if ((false == ADAS_getCenterx()->turnflag) && ((fabsf(centerx) > lcaparam->radius_threshold)) && (pobjPath[i].x <= lcaparam->xfar_threshold) && (pobjPath[i].x > lcaparam->xnear_threshold))
    // {
    //     centerxflag = 1;
    // }

    // 无边线或目标点处于边线内
    if ((RoadLine < MIN_SIDE_VALUE) || ((RoadLine > (MIN_ROAD_SIDE_DISTANCE + lcaparam->roadline_buf0)) && (pobjPath[i].x < (RoadLine - lcaparam->roadline_buf1))))
    {
        roadlineflag = 1;
    }

    if((lcaparam->stopAlarm == false) && (speedflag == 1U) && (posflag == 1U) && (roadlineflag == 1U) && (centerxflag == 1U) && (reliability == 1U))
    {
        lcaparam->isTargetInAlmZone = true;
    }
}

/**
 * @brief 报警进入以及退出策略
 * 
 * @param lcaparam LCA报警相关参数
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 */
static void ADAS_LCA_setAlarmManager(ADAS_LCA_Param *lcaparam, OBJ_NODE_STRUCT *pobjPath, uint8_t i, float centerx)
{
    uint8_t centerxflag = 0;
    uint8_t limitx = 0;
    // 对于LCA明显不在区域偶发误报仿真无法复现的的问题, 目前未发现根本原因, 尝试doublecheck报警区域,持续测试是否ok.
    if ((fabsf(centerx) > lcaparam->radius_threshold) && 
		(((centerx < 0) && ((pobjPath[i].rcDis - lcaparam->radiusAbs) <= lcaparam->radiusfardif_threshold) && ((pobjPath[i].rcDis - lcaparam->radiusAbs) > lcaparam->radiusneardif_threshold )) ||
        ((centerx > 0) && ((lcaparam->radiusAbs - pobjPath[i].rcDis) <= lcaparam->radiusfardif_threshold) && ((lcaparam->radiusAbs - pobjPath[i].rcDis) > lcaparam->radiusneardif_threshold )) ||
		((fabsf(centerx) > 300) && (pobjPath[i].x <= lcaparam->xfar_threshold) && (pobjPath[i].x > lcaparam->xnear_threshold))))
    {
        centerxflag = 1;
    }

    // // 非转弯场景二次判断 
    // if ((false == ADAS_getCenterx()->turnflag) && ((fabsf(centerx) > lcaparam->radius_threshold)) && (pobjPath[i].x <= lcaparam->xfar_threshold) && (pobjPath[i].x > lcaparam->xnear_threshold))
    // {
    //     centerxflag = 1;
    // }

    // double check 无效, 依然出现了右侧目标误报左侧LCA. 对目标横向坐标实行绝对限制
    if ((fabsf(centerx) > lcaparam->radius_threshold) && (pobjPath[i].x <= lcaparam->xfar_threshold + LCA_AREA_X_LIMIT_BUF) && (pobjPath[i].x > lcaparam->xnear_threshold - LCA_AREA_X_LIMIT_BUF))
    {
        limitx = 1;
    }

    if((lcaparam->isTargetInAlmZone == true) && (lcaparam->ispreTTCFit == true  && lcaparam->isTTCFit == false && pobjPath[i].vy > LCA_RELATIVE_SPEED_MAX) && (1 == centerxflag) && (1 == limitx)) // 先累加start计数
    {
        if (ALARM_ACTIVE_LCA != pobjPath[i].preAlarmType){
            pobjPath[i].startLcaAlarmDly = 0;
            pobjPath[i].overLcaAlarmDly  = 0U;
            pobjPath[i].preAlarmType = (uint32_t)ALARM_ACTIVE_LCA;
        }
        else{
            if (pobjPath[i].startLcaAlarmDly < LCA_START_ALARM_DELAY)
            {
                pobjPath[i].startLcaAlarmDly++;
            }  
        }
    }

    if((lcaparam->isTargetInAlmZone == true) && (lcaparam->isTTCFit == true && pobjPath[i].vy > LCA_RELATIVE_SPEED_MAX) && (1 == centerxflag) && (1 == limitx))
    {
        if(!ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA)) //状态初始化
        {
            //pobjPath[i].startLcaAlarmDly = 0;    //第一次报警
            pobjPath[i].overLcaAlarmDly  = 0U;
            ADAS_setBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA);
            ADAS_setBit(&pobjPath[i].preAlarmType, ALARM_ACTIVE_LCA);
        }
        
        if (pobjPath[i].startLcaAlarmDly >= LCA_START_ALARM_DELAY) //报警延迟
        {
            lcaparam->alarmFlag = true; //连续多帧出现报警
            pobjPath[i].overLcaAlarmDly |= LCA_OVER_ALARM_DELAY;
            ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_LCA);
        }
        else
        {
            pobjPath[i].startLcaAlarmDly++;
        }
    }
    else
    {
        if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA))
        {
            if (pobjPath[i].startLcaAlarmDly >= LCA_START_ALARM_DELAY)
            {
                if (pobjPath[i].overLcaAlarmDly == 0)  //已经产生了报警，则执行报警退出延迟
                {
                    ADAS_setBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA);
                }
                else
                {
                    if (1 != limitx)        // X设置为强制检测项
                    {
                        ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA);
                        return ;
                    }

                    lcaparam->alarmFlag = true;

                    ADAS_setBit(&pobjPath[i].alarmType, ALARM_ACTIVE_LCA);

                    if(lcaparam->isTargetInAlmZone == false)
                    {
                        if(pobjPath[i].overLcaAlarmDly > LCA_OVER_ALARM_DELAY) //超出区域或者其他原因推出报警，退出ttc保护
                        {
                            pobjPath[i].overLcaAlarmDly = LCA_OVER_ALARM_DELAY;
                        }
                    }
                    else
                    {
                        if(lcaparam->isTTCFit == false) //ttc不满足退出报警
                        {
                            if(pobjPath[i].vy < LCA_LOW_VY_ALM_QUIT_PROTECTION_VY && pobjPath[i].vy > 0)    //相对速度较小，进入ttc保护
                            {
                                if(pobjPath[i].overLcaAlarmDly <= LCA_OVER_ALARM_DELAY)    //初始化ttc保护计数
                                {
                                    pobjPath[i].overLcaAlarmDly = LCA_LOW_VY_ALM_QUIT_PROTECTION_FRAME_NUM_BASE + LCA_LOW_VY_ALM_QUIT_PROTECTION_FRAME_NUM;
                                }
                                
                                if(pobjPath[i].overLcaAlarmDly == LCA_LOW_VY_ALM_QUIT_PROTECTION_FRAME_NUM_BASE)   //ttc保护计数已过
                                {
                                    pobjPath[i].overLcaAlarmDly = 1;
                                }
                            }
                            else
                            {
                                if(pobjPath[i].overLcaAlarmDly > LCA_OVER_ALARM_DELAY)
                                {
                                    pobjPath[i].overLcaAlarmDly = LCA_OVER_ALARM_DELAY;
                                }
                            }
                        }
                    }

                    pobjPath[i].overLcaAlarmDly--;

                    gadasFunctionState.adasLCAWarning = (uint8_t)ADAS_WARNING_LEVEL1;
                }
            }
            else
            {
                ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_LCA);
            }
        }
    }
}


/** 
 * @brief LCA报警策略主函数
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param pVDY VDY所有信息 
 * @return alarmFlag
 */
bool ADAS_LCA_runMain(ALARM_OBJECT_T *pobjAlm
                             , OBJ_NODE_STRUCT *pobjPath
                             , uint8_t i
                             , const VDY_Info_t *pVDY)
{
    //uint8_t l_r = pobjAlm->l_r;
    float centerx = pobjAlm->centerx;
    float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
    float RoadLine = pobjAlm->RoadLine;
    ADAS_LCA_Param lcaparam;

    ADAS_LCA_initParams(&lcaparam, pVDY->pVDY_DynamicInfo);  
    ADAS_LCA_cacBuffer(&lcaparam, pobjPath, i, BSDVelSpeedVal);
    ADAS_LCA_TTCFit(&lcaparam, pobjPath, i);
    ADAS_LCA_checkTargetInZone(&lcaparam, pobjPath, i, BSDVelSpeedVal, RoadLine, centerx, pobjAlm);
    ADAS_LCA_setAlarmManager(&lcaparam, pobjPath, i, centerx);

    return lcaparam.alarmFlag;
}

/**
 * @brief ELKA报警策略主函数
 * 
 * @param pobjAlm 报警结构体地址 
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @param pVDY VDY所有信息 
 * @return alarmFlag
 */
bool ADAS_ELKA_runMain(ALARM_OBJECT_T *pobjAlm
                             , OBJ_NODE_STRUCT *pobjPath
                             , uint8_t i
                             , const VDY_Info_t *pVDY)
{
	bool alarmFlag = false;
    static int8_t adasOverDly = 0;

	if (((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U) && /*运动的目标*/
		(pobjPath[i].y <= (LCA_LENGTH)) &&
		(pobjPath[i].y >= (-2)) &&			   /* y轴区域限制*/
		(pobjPath[i].x <= LCA_FAR_ALARM_WIDTH_SIZE) && /* x轴区域限制*/
		(pobjPath[i].x >= LCA_NEAR_ALARM_WIDTH_SIZE) &&
		(pobjPath[i].vy >= 0.1) &&	 /* 相对速度为正 */
		(pobjPath[i].lifeCycle >= 2U) && /* 目标确认 */
        (pobjPath[i].maxY >= 3) /* 确保是后方来车,避免超车误触发 */
#if defined(VEHICLE_TYPE_BYD_SONG_SF_5R5V)
		&& ((pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 1) && (pVDY->pVDY_VehicleFuncSwtInfo->vdyTabStatus != 2)) /* 极致转向关闭 */
#endif
	)
	{
		const ADAS_FunctionState_t *adasData = ADAS_getAdasDataPointer();
        // 对于已经报警的目标, 扩大buffer
        if (1 == adasData->adasELKAOTWarning){
            if ((pobjPath[i].y <= (pobjPath[i].vy * (ELKA_OT_TTC + ELKA_OT_TTC_BUF))) && (pobjPath[i].x <= (ELKA_OT_X_DISTANCE + ELKA_OT_X_DISTANCE_BUF))){
                adasOverDly = ELKA_OT_OVERDLY;
                gadasFunctionState.adasELKAOTWarning = 0x1;
                alarmFlag = true;
            }
        }else{
            if ((pobjPath[i].y <= (pobjPath[i].vy * (ELKA_OT_TTC))) && (pobjPath[i].x <= ELKA_OT_X_DISTANCE)){
                adasOverDly = ELKA_OT_OVERDLY;
                gadasFunctionState.adasELKAOTWarning = 0x1;
                alarmFlag = true;
            }
        }
    }
    if ((0 != adasOverDly) && (--adasOverDly >= 0))
    {
        gadasFunctionState.adasELKAOTWarning = 0x1;
    }

    return alarmFlag;
}
