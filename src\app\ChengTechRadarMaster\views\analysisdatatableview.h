﻿#ifndef ANALYSISDATATABLEVIEW_H
#define ANALYSISDATATABLEVIEW_H

#include <QWidget>
#include "analysis/analysisdata_global.h"

namespace Ui {
class AnalysisDataTableView;
}

class QAbstractTableModel;

namespace Views {
namespace AnalysisView {

class AnalysisDataTableView : public QWidget
{
    Q_OBJECT

public:
    explicit AnalysisDataTableView(quint8 radarID, int vType, QWidget *parent = nullptr);
    ~AnalysisDataTableView();
    void changeTableView();

    int viewType();
    QAbstractTableModel *model() const { return mModel; }

signals:
    void sigDelete();
    void sigViewTypeChanged();

private slots:
    void on_pushButtonDelete_clicked();

    void on_comboBoxDataType_currentIndexChanged(int index);

private:
    void modelForwardRadar(int frameType);
    void modelAngularRadar(int frameType);

    Ui::AnalysisDataTableView *ui;

    quint8 mRadarID{0};
    QAbstractTableModel *mModel{0};
};

}
}

#endif // ANALYSISDATATABLEVIEW_H
