#ifndef SENDUDSFORM_H
#define SENDUDSFORM_H

#include "devices/canframe.h"

#include <QWidget>
#include <QAbstractItemModel>


namespace Ui {
class SendUDSForm;
}

namespace Functions{
class UDS;
};

namespace Devices {
namespace Can {
class DeviceManager;
}
}

class SendUDSModel : public QAbstractTableModel
{
    Q_OBJECT

    struct SendUDSModelItem{
        bool checked;
        quint32 requestID;
        quint32 responseID;
        QByteArray requestData;
        QByteArray responseData;
        bool responseError;
    };

public:
    SendUDSModel(/* Devices::Can::DeviceManager* deviceManager, */QObject *parent = nullptr );

public:
    void addData( quint32 requestID, const QByteArray& requestData, quint32 responseID, const QByteArray& responseData );
    void clearData();
    void clearResponseData();
    void deleteData( quint32 row );
    void swapData( quint32 row1, quint32 row2 );

    //获取勾选的数据
    //返回值为行号，若返回值为-1则表示无匹配数据
    //参数row代表从第几行开始匹配
    int getCheckedData( int row, quint32& requestID, QByteArray& requestData, quint32& responseID );
    void setResponseData( int row, bool error, const QByteArray& responseData );

public:
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    Qt::ItemFlags flags(const QModelIndex &index) const override;

signals:
//    void startOrStop( bool bStart );
//    void sendComverResult();

//private slots:
//    void update();


private:
    QStringList mHeader;
    QList<SendUDSModelItem> mData;
};

class SendUDSForm : public QWidget
{
    Q_OBJECT

public:
    explicit SendUDSForm(Devices::Can::DeviceManager *deviceManager,QWidget *parent = nullptr);
    ~SendUDSForm();

signals:
    void requestIDOrResponseIDChanged( quint32 requestID, quint32 responseID );
    void msg( const QString& msg, bool bError );

public slots:
    void canFrame(const Devices::Can::CanFrame &frame);

    void deviceOpened();
    void deviceClosed();

private slots:
    void on_pushButtonSend_clicked();

    void on_pushButtonAddToTable_clicked();

    void on_pushButtonClear_clicked();

    void on_pushButtonDel_clicked();

    void on_pushButtonUp_clicked();

    void on_pushButtonDown_clicked();

    void on_pushButtonClearResponseData_clicked();

    void on_pushButtonSendTable_clicked();

    void on_pushButtonDelResponseData_clicked();

private:
    bool checkDataAndID();
    bool sendUdsRequest( quint32 requestID, const QByteArray& requestData, quint32 responseID, QByteArray& responseData );

private:
    Ui::SendUDSForm *ui;
    Functions::UDS *mUDS{NULL};
    SendUDSModel* mModel{NULL};
    Devices::Can::DeviceManager *mDeviceMgr{NULL};
};

#endif // SENDUDSFORM_H
