﻿#include "truetargetsmonitor.h"
#include "ui_truetargetmonitor.h"
#include "ui_truetargetsmonitor.h"
#include "../analysis/analysismanager.h"
#include "../analysis/analysisworker.h"

#include <QDebug>
#include <QTableWidget>
#include <QFileDialog>

trueTargetMonitor::trueTargetMonitor(trueTargetMonitor_CB* cb, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::trueTargetMonitor)
{
    mCallBack = cb;
    ui->setupUi(this);

    mGraphTarget = ui->customPlot->addGraph();
    mGraphTarget->setPen(QPen(Qt::blue));
    mGraphTrue = ui->customPlot->addGraph();
    mGraphTrue->setPen(QPen(Qt::black));

    ui->customPlot->setInteractions(QCP::iRangeDrag|QCP::iRangeZoom| QCP::iSelectAxes |
                                      QCP::iSelectLegend | QCP::iSelectPlottables);

//    QFont font;
//    font.setPixelSize(5);
//    ui->customPlot->xAxis->setLabelFont(font);
//    ui->customPlot->yAxis->setLabelFont(font);

    //游标
    mTracer = new QCPItemTracer(ui->customPlot); //生成游标
    mTracer->setPen(QPen(Qt::red));//圆圈轮廓颜色
    mTracer->setBrush(QBrush(Qt::red));//圆圈圈内颜色
    mTracer->setStyle(QCPItemTracer::tsCircle);//圆圈
    mTracer->setSize(5);//设置大小
    //游标文本
    mTracerLabel = new QCPItemText(ui->customPlot); //生成游标说明
    mTracerLabel->setLayer("overlay");//设置图层为overlay，因为需要频繁刷新
    mTracerLabel->setPen(QPen(Qt::black));//设置游标说明颜色
    mTracerLabel->setPositionAlignment(Qt::AlignLeft | Qt::AlignTop);//左上
    mTracerLabel->position->setParentAnchor(mTracer->position);//将游标说明锚固在tracer位置处，实现自动跟随

    connect(ui->customPlot, SIGNAL(mouseMove(QMouseEvent*)), this, SLOT(showInfo(QMouseEvent*)));

    updateAxis();
}

trueTargetMonitor::~trueTargetMonitor()
{
    delete ui;
}

void trueTargetMonitor::showInfo(QMouseEvent *e)
{
    //获得鼠标位置处对应的横坐标数据x
    double x = ui->customPlot->xAxis->pixelToCoord(e->pos().x());
    double y = ui->customPlot->yAxis->pixelToCoord(e->pos().y());


//    xValue = x;//xValue就是游标的横坐标
//    yValue = x*x;//yValue就是游标的纵坐标，这里直接根据产生数据的函数获得

    mTracer->position->setCoords(x, y);//设置游标位置
    QString text = mCallBack->getMsg( x );
    mTracerLabel->setText(QString("x=%1,y=%2\n%3").arg(x).arg(y).arg(text));//设置游标说明内容
    ui->customPlot->replot();//绘制器一定要重绘，否则看不到游标位置更新情况
}

void trueTargetMonitor::setTitle(const QString &title)
{
    mTitle = title;
    showTitle();
}

void trueTargetMonitor::setUnit(const QString &unit)
{
    mUnit = unit;
    showTitle();
}

void trueTargetMonitor::addData(double targetValue, double searchValue, double trueValue, bool bSearchErr)
{
    updateGraph( targetValue, searchValue, trueValue, bSearchErr );
    updateDiffValue( targetValue, searchValue, trueValue, bSearchErr );
}

void trueTargetMonitor::updateDiffValue(double targetValue, double searchValue, double trueValue, bool bSearchErr)
{
    //更新指定跟踪点差值
    double targetDiff = qAbs( trueValue - targetValue );
    if( targetDiff > mTargetMaxDiff ){
        mTargetMaxDiff = targetDiff; //指定跟踪点  最大差值
    }
    if( mDataCount == 0 ){
        mTargetAveDiff = targetDiff; //指定跟踪点  平均差值
    }else{
        mTargetAveDiff = (mTargetAveDiff * (mDataCount - 1) + targetDiff ) / mDataCount; //指定跟踪点  平均差值
    }

    //更新搜寻差值
    if( !bSearchErr ){
        double searchDiff = qAbs( trueValue - searchValue );
        if( searchDiff > mSearchMaxDiff ){
            mSearchMaxDiff = searchDiff; //搜寻跟踪点  最大差值
        }
        if( mDataCount == 0 ){
            mSearchAveDiff = searchDiff; //搜寻跟踪点  平均差值
        }else{
            mSearchAveDiff = ( mSearchAveDiff * (mDataCount - 1) + searchDiff ) / mDataCount; //搜寻跟踪点  平均差值
        }
    }


    ui->lineEditAveDiff->setText( QString("T:%1 ST:%2").arg(mTargetAveDiff).arg(mSearchAveDiff) );
    ui->lineEditMaxDiff->setText( QString("T:%1 ST:%2").arg(mTargetMaxDiff).arg(mSearchMaxDiff) );
}

void trueTargetMonitor::updateGraph(double targetValue, double searchValue, double trueValue, bool bSearchErr)
{
    //自动搜索的折线
    QCPGraph *preGraph, *curGraph;
    getSearchGraph( bSearchErr, &preGraph, &curGraph);
//    qDebug() << __FUNCTION__ << __LINE__ << bSearchErr << preGraph << curGraph;
    if( curGraph ){
        if( bSearchErr ){
            curGraph->addData(mDataCount -1, mPreSearchValue);
        }
        curGraph->addData(mDataCount, searchValue);
    }
    if( preGraph && mPreSearchValueErr ){
        preGraph->addData(mDataCount, searchValue);
    }

    //指定跟踪点ID的折线
    mGraphTarget->addData( mDataCount, targetValue );

    //真值折线
    mGraphTrue->addData(mDataCount, trueValue);

    mDataCount++;
    mPreSearchValue = searchValue;
    mPreSearchValueErr = bSearchErr;

    //更新坐标系
    double min = targetValue < trueValue ? targetValue : trueValue;
    min = searchValue < min ? searchValue : min;
    double max = targetValue > trueValue ? targetValue : trueValue;
    max = searchValue > max ? searchValue : max;
    if( updateXAxisRang( mDataCount ) || updateYAxisRang( min, max ) ){ //判断是否需要更新X轴和Y轴
        updateAxis();
    }
    ui->customPlot->replot( QCustomPlot::rpQueuedReplot );
}


void trueTargetMonitor::clear()
{
    mDataCount = 0;
    mMaxX = 100;
    mMinY = 0;
    mMaxY = 1;

    mTargetMaxDiff = 0; //指定跟踪点  最大差值
    mTargetAveDiff = 0; //指定跟踪点  平均差值
    mSearchMaxDiff = 0; //搜寻跟踪点  最大差值
    mSearchAveDiff = 0; //搜寻跟踪点  平均差值


    mGraphTarget->data().data()->clear();
    for( int i=0; i<mGraphSearch.size(); i++ ){
        ui->customPlot->removeGraph( mGraphSearch[i] );
        //delete mGraphTarget[i];
    }
    mGraphSearch.clear();

    mGraphTrue->data().data()->clear();
    mGraphTarget->data().data()->clear();

    updateAxis();
    ui->customPlot->replot();
}

void trueTargetMonitor::exportPdf(const QString &path)
{
    QString fileName = path + QString("/%1.pdf").arg( mTitle );
    ui->customPlot->savePdf( fileName/*, 0, 0, QCP::epNoCosmetic*/ );

//    QPixmap pixmap = ui->customPlot->toPixmap();

//    QString fileName = path + QString("/%1.png").arg( mTitle );
//    ui->customPlot->savePng( fileName );
}

void trueTargetMonitor::showGraph(bool bTarget, bool bSearch, bool bTrue)
{
    mShowSearchGraph = bSearch;
    mGraphTarget->setVisible(bTarget);
    mGraphTrue->setVisible(bTrue);
    for( int i=0; i<mGraphSearch.size(); i++ ){
        mGraphSearch[i]->setVisible(bSearch);
    }
    ui->customPlot->replot();
}

void trueTargetMonitor::showTitle()
{
    //ui->labelTitle->setText( mTitle + " [" + mUnit + "]");
    ui->customPlot->yAxis->setLabel( mTitle + " [" + mUnit + "]" );
}

bool trueTargetMonitor::updateXAxisRang(double max)
{
    if( max > mMaxX ){
        mMaxX = max / 3 * 4; //增加3分之1
        return true;
    }
    return false;
}

bool trueTargetMonitor::updateYAxisRang(double min, double max)
{
    bool bRet = false;
    if( min < mMinY ){
        mMinY = min / 3 * 4;
        bRet = true;
    }
    if( max > mMaxY ){
        mMaxY = max / 3 * 4;
        bRet = true;
    }
    return bRet;
}

void trueTargetMonitor::updateAxis()
{
    ui->customPlot->xAxis->setRange(0, mMaxX);
    ui->customPlot->yAxis->setRange(mMinY, mMaxY);
}

bool trueTargetMonitor::getSearchGraph(bool bErrValue, QCPGraph** preGraph, QCPGraph** curGraph )
{
    *preGraph = NULL;
    if( bErrValue != mPreSearchValueErr || mGraphSearch.size() == 0 ){
        mGraphSearch.push_back( ui->customPlot->addGraph() );
        mGraphSearch[mGraphSearch.size()-1]->setVisible(mShowSearchGraph);
        if( bErrValue ){
            mGraphSearch[mGraphSearch.size()-1]->setPen(QPen(Qt::red));
        }else{
            mGraphSearch[mGraphSearch.size()-1]->setPen(QPen(Qt::green));
        }
        if( mGraphSearch.size() > 1 ){
            *preGraph = mGraphSearch[mGraphSearch.size()-2];
        }
    }
    *curGraph = mGraphSearch[mGraphSearch.size()-1];
    return true;
}


////////////////////////////////////////////////////////////////////////////

trueTargetsMonitor::trueTargetsMonitor(Analysis::AnalysisManager* analysisMgr,QWidget *parent) :
    QWidget(parent),
    ui(new Ui::trueTargetsMonitor)
{
    ui->setupUi(this);
    this->setWindowTitle( "True Value Compar" );
    mAnalysisMgr = analysisMgr;

    connect( ui->checkBoxShowTarget, &QCheckBox::stateChanged, this, &trueTargetsMonitor::showGraph );
    connect( ui->checkBoxShowTrue, &QCheckBox::stateChanged, this, &trueTargetsMonitor::showGraph );
    connect( ui->checkBoxShowSearch, &QCheckBox::stateChanged, this, &trueTargetsMonitor::showGraph );

    init();
    createGraph();

//    ui->pushButtonExportPDF->setVisible( false );
}

trueTargetsMonitor::~trueTargetsMonitor()
{
    delete ui;
}

QString trueTargetsMonitor::getMsg(quint64 index)
{
    QString msg = "";
    if( index < mNodes.size() ){
        msg = QString("T-NO:%1,T-ID:%2,ST-ID:%3")
                .arg( mNodes[index].targetNo )
                .arg(mNodes[index].targetID)
                .arg( mNodes[index].searchTargetID );
    }
    return msg;
}

void trueTargetsMonitor::analysisFinished(quint8 radarID, const AnalysisData &analysisData)
{
    if( radarID != mRadarID ){
        return;
    }

    AnalysisData data = analysisData;
    data.calculate();

//    if( !analysisData.mValid ){
//        return;
//    }

    switch (analysisData.mRadarID) {
    case 4:
        qDebug() << __FUNCTION__ << __LINE__ << analysisData.mRadarID
                 << data.mTrueTarget[mTrueTargetIndex].mX
                 << data.mTrueTarget[mTrueTargetIndex].mY
                 << data.mTrueTarget[mTrueTargetIndex].mAx
                 << data.mTrueTarget[mTrueTargetIndex].mAy;
        data.mTrueTarget[mTrueTargetIndex].mY = -(data.mTrueTarget[mTrueTargetIndex].mY);
        data.mTrueTarget[mTrueTargetIndex].mVy = -data.mTrueTarget[mTrueTargetIndex].mVy;
        data.mTrueTarget[mTrueTargetIndex].mAy = -data.mTrueTarget[mTrueTargetIndex].mAy;
        break;
    case 5:
        data.mTrueTarget[mTrueTargetIndex].mX = -data.mTrueTarget[mTrueTargetIndex].mX;
        data.mTrueTarget[mTrueTargetIndex].mVx = -data.mTrueTarget[mTrueTargetIndex].mVx;
        data.mTrueTarget[mTrueTargetIndex].mAx = -data.mTrueTarget[mTrueTargetIndex].mAx;

        data.mTrueTarget[mTrueTargetIndex].mY = -(data.mTrueTarget[mTrueTargetIndex].mY);
        data.mTrueTarget[mTrueTargetIndex].mVy = -data.mTrueTarget[mTrueTargetIndex].mVy;
        data.mTrueTarget[mTrueTargetIndex].mAy = -data.mTrueTarget[mTrueTargetIndex].mAy;
        break;
    case 6:
//        data.mTrueTarget[mTrueTargetIndex].mY = -data.mTrueTarget[mTrueTargetIndex].mY;
//        data.mTrueTarget[mTrueTargetIndex].mVy = -data.mTrueTarget[mTrueTargetIndex].mVy;
//        data.mTrueTarget[mTrueTargetIndex].mAy = -data.mTrueTarget[mTrueTargetIndex].mAy;
        break;
    case 7:
        data.mTrueTarget[mTrueTargetIndex].mX = -data.mTrueTarget[mTrueTargetIndex].mX;
        data.mTrueTarget[mTrueTargetIndex].mVx = -data.mTrueTarget[mTrueTargetIndex].mVx;
        data.mTrueTarget[mTrueTargetIndex].mAx = -data.mTrueTarget[mTrueTargetIndex].mAx;
//        data.mTrueTarget[mTrueTargetIndex].mY = -data.mTrueTarget[mTrueTargetIndex].mY;
//        data.mTrueTarget[mTrueTargetIndex].mVy = -data.mTrueTarget[mTrueTargetIndex].mVy;
//        data.mTrueTarget[mTrueTargetIndex].mAy = -data.mTrueTarget[mTrueTargetIndex].mAy;
        break;
    default:
        break;
    }

    nodeItem item;
    const Target *searchTarget = calcTraget( data);
    item.targetNo = data.mTargets[FrameTrackTarget].mTargetHeader.mMeasurementCount;
    item.targetID = mTargetID;

    const Target *target = 0;
    for (int i = 0; i < data.mTargets[FrameTrackTarget].mTargetCount; ++i) {
        if (data.mTargets[FrameTrackTarget].mTargets[i].mID == mTargetID)  {
            target = data.mTargets[FrameTrackTarget].mTargets + i;
            break;
        }
    }

//    if (target) {
//        qDebug() << __FUNCTION__ << __LINE__ << target->mID << data.mTargets[FrameTrackTarget].mTargetCount;
//    }
//    if (searchTarget) {
//        qDebug() << __FUNCTION__ << __LINE__ << searchTarget->mID << data.mTargets[FrameTrackTarget].mTargetCount;
//    }

    for( int i=0; i<mMonitorList.size(); i++ ){
        monitorItem& item = mMonitorList[i];
        item.monitor->addData( !target ? 0 : target->value(item.monitorType),
                               !searchTarget ? 0 : searchTarget->value(item.monitorType),
                               data.mTrueTarget[mTrueTargetIndex].value(item.monitorType),
                               searchTarget);
    }


    mNodes.push_back(item);
}

void trueTargetsMonitor::clear()
{
    mNodes.clear();
}

void trueTargetsMonitor::init()
{
    ui->checkBoxShowTrue->setStyleSheet("color:black;");
    ui->checkBoxShowTarget->setStyleSheet("color:blue;");
    ui->checkBoxShowSearch->setStyleSheet("color:green;");

    ui->lineEditSearchRang->setText("5");
    ui->lineEditTargetID->setText("0");
    ui->lineEditSearchResult->setText("-1");
    ui->lineEditTrueTargetIndex->setText("0");

    ui->lineEditSearchResult->setEnabled( false );
    ui->pushButtonStop->setEnabled( false );
}

void trueTargetsMonitor::createGraph()
{
    if( !mTabWidget ){
        mTabWidget = new QTabWidget( this );
        ui->gridLayout->addWidget(mTabWidget, 0, 0, 1, 1);
    }

    createMonitor(AnalysisType::X);
    createMonitor(AnalysisType::Y);
    createMonitor(AnalysisType::Vx);
    createMonitor(AnalysisType::Vy);
    createMonitor(AnalysisType::Ax);
    createMonitor(AnalysisType::Ay);
    createMonitor(AnalysisType::Angle);
    createMonitor(AnalysisType::TrackFrameAngle);
}

void trueTargetsMonitor::createMonitor(AnalysisType type)
{
    monitorItem item;
    item.monitor = new trueTargetMonitor(this,this);
    item.monitorType = type;
    //ui->gridLayout->addWidget(item.monitor, mMonitorList.size(), 0, 1, 1);
    item.monitor->setTitle( AnalysisTypeNames[type].mName );
    item.monitor->setUnit( AnalysisTypeNames[type].mUnit );

    mTabWidget->addTab( item.monitor, AnalysisTypeNames[type].mName );
    mMonitorList.push_back( item );
}

const Target *trueTargetsMonitor::calcTraget(const AnalysisData &analysisData)
{
//    if( !mAutoTargetID ){
//        return mSearchTargetID;
//    }

    double true_X = analysisData.mTrueTarget[mTrueTargetIndex].value(X);
    double true_Y = analysisData.mTrueTarget[mTrueTargetIndex].value(Y);

    qint32 index = -1;

    double minDiffDist = 0.0;
    const Targets& targets = analysisData.mTargets[FrameTrackTarget];
    for( int i=0; i<targets.mTargetCount; i++ ){
        const Target& target = targets.mTargets[i];
        if( !target.mValid || target.mDynamicProperty != 0x1 /* 不为动态目标 */ ){
            continue;
        }
        //距离最小的动态跟踪点
        double curDiffDist = qSqrt( qPow( true_X - target.value(X), 2 ) + qPow( true_Y - target.value(Y), 2 ) );
        //qDebug() << __FUNCTION__ << __LINE__ << curDiffDist;
        if( curDiffDist > mSearchRang ){
            continue;
        }

        if( -1 == index || curDiffDist < minDiffDist ){
            minDiffDist = curDiffDist;
            index = i;
        }
    }
    if (index == -1) {
        ui->lineEditSearchResult->setText( "-1" );
        return 0;
    }
    ui->lineEditSearchResult->setText( QString::number(analysisData.mTargets->mTargets[index].mID) );
    return  analysisData.mTargets->mTargets + index;
}

void trueTargetsMonitor::startOrStop(bool bStart)
{
    if( bStart ){
        mSearchRang = ui->lineEditSearchRang->text().toUInt();
        mRadarID = ui->comboBoxRadarID->currentText().toUInt();
        mTargetID = ui->lineEditTargetID->text().toUInt();
        mTrueTargetIndex = ui->lineEditTrueTargetIndex->text().toUInt();
        if( mTargetID > MAX_TARGET_COUNT ){
            mTargetID = 0;
        }

        ui->checkBoxShowTrue->setChecked( true );
        ui->checkBoxShowTarget->setChecked( true );
        ui->checkBoxShowSearch->setChecked( true );

        connect( mAnalysisMgr->analysisWorker(), &Analysis::AnalysisWorker::analysisFinished,
                 this, &trueTargetsMonitor::analysisFinished );
    }else{
        disconnect( mAnalysisMgr->analysisWorker(), &Analysis::AnalysisWorker::analysisFinished,
                 this, &trueTargetsMonitor::analysisFinished );
    }

    ui->lineEditTargetID->setEnabled( !bStart );
    ui->lineEditSearchRang->setEnabled( !bStart );
    ui->lineEditTrueTargetIndex->setEnabled( !bStart );
    ui->comboBoxRadarID->setEnabled( !bStart );
    ui->pushButtonClear->setEnabled( !bStart );
    ui->pushButtonStart->setEnabled( !bStart );
    ui->pushButtonStop->setEnabled( bStart );
    ui->pushButtonExportPDF->setEnabled( !bStart );
}

void trueTargetsMonitor::on_pushButtonStart_clicked()
{
    startOrStop( true );
}

void trueTargetsMonitor::on_pushButtonStop_clicked()
{
    startOrStop( false );
}

void trueTargetsMonitor::on_pushButtonClear_clicked()
{
//    mMonitorX->clear();
//    mMonitorY->clear();
//    mMonitorVX->clear();
//    mMonitorVY->clear();
    for( int i=0; i<mMonitorList.size(); i++ ){
        monitorItem& item = mMonitorList[i];
        item.monitor->clear();
    }
    mNodes.clear();
}

void trueTargetsMonitor::showGraph()
{
    bool bTarget = ui->checkBoxShowTarget->isChecked();
    bool bSearch = ui->checkBoxShowSearch->isChecked();
    bool bTrue = ui->checkBoxShowTrue->isChecked();

//    mMonitorX->showGraph( bTarget, bSearch, bTrue );
//    mMonitorY->showGraph( bTarget, bSearch, bTrue );
//    mMonitorVX->showGraph( bTarget, bSearch, bTrue );
//    mMonitorVY->showGraph( bTarget, bSearch, bTrue );
    for( int i=0; i<mMonitorList.size(); i++ ){
        monitorItem& item = mMonitorList[i];
        item.monitor->showGraph( bTarget, bSearch, bTrue );
    }
}

void trueTargetsMonitor::on_pushButtonExportPDF_clicked()
{
    QString path = QFileDialog::getExistingDirectory();
    for( int i=0; i<mMonitorList.size(); i++ ){

        mTabWidget->setCurrentIndex( i ); //切换至对应的页面，使得该折线图显示，否则导出的DPF有问题

        monitorItem& item = mMonitorList[i];
        item.monitor->exportPdf( path );
    }
    QMessageBox::information( this, "Infomation", "export success" );
}
