﻿#ifndef FFMPEGCAMERA_H
#define FFMPEGCAMERA_Hsd

#include <QObject>
#include <QCamera>

#include <string>
#include <mutex>

#ifdef __cplusplus
extern "C" {
#endif
#include <libavdevice/avdevice.h>
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavfilter/buffersink.h>
#include <libavfilter/buffersrc.h>
#include "libavfilter/avfilter.h"
#include "libavutil/avutil.h"
#include "libavutil/pixfmt.h"
#include "libavutil/error.h"
#include "libavutil/imgutils.h"
#include "libswscale/swscale.h"
#ifdef __cplusplus
}
#endif

class FFmpegVideoWidget;
class GLYuvWidget;
class FFmpegReader;
class FFmpegWirter;

class FFmpegCamera : public QObject
{
    Q_OBJECT
public:
    static std::list<std::string> available_cameras();

    explicit FFmpegCamera(const std::string &url, int index, QObject *parent = nullptr);

    bool open();
    bool close();
    bool startSave(const char *filename);
    void startSaveVideo(const QString &savePath, quint64 saveTime);
    bool stopSave();

    const std::string &url() const { return mURL; }
    const std::string &errorString() const { return mErrorString; }
    bool isOpened() const { return mOpened; }
    bool isSaveing() const;

    QCamera::State state() const { return mState; }

signals:
    void stateChanged(QCamera::State state);
    void showImage(QImage image);
    void cameraSaveIndex(int cIndex, long long sIndex);

private:
    void closeCamera();
    bool initFillter();
    bool freeFillter();
    void readFrame();
//    int waterMark(AVFrame *frame_in,AVFrame *frame_out);
    void sendFrame(AVFrame *frame);
    bool toRGB32(AVFrame *frame);

    friend void videoRead(FFmpegCamera *pVideoDecoder);

    QCamera::State mState{QCamera::UnloadedState};
    std::string mURL;
    std::string mErrorString;
    int mCameraIndex{-1};
    bool mOpened{false};

    FFmpegReader *mFFmpegReader;
    FFmpegWirter *mFFmpegWirter;

    QImage *mImage{0};

    SwsContext* mpSwsContextRGB32{0};

    AVFilterContext *buffersink_ctx = NULL;
    AVFilterContext *buffersrc_ctx = NULL;
    AVFilterContext *filter_ctx;//上下文
    AVFilterGraph *filter_graph = NULL; //最关键的过滤器结构体
    AVFilterInOut *outputs ;
    AVFilterInOut *inputs;

    int mSaveIndex = 0;
};

#endif // FFMPEGCAMERA_H
