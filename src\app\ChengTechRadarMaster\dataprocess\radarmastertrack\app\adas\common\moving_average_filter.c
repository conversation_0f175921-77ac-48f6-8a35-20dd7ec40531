/**
*********************************************************************************************************
*               Copyright (C) SHL. All rights reserved.
**********************************************************************************************************
* 文件名file:moving_average_filter.c
* 简述brief:
* 作者author:
* 日期date:
* 版本version:
*********************************************************************************************************
*/

/***********************
 * * 本地头文件
 ***********************/
#include "moving_average_filter.h"
#include "stdint.h"

static data_record_item_t movingHistory[MOVING_MEAN_WIN_SIZE];       // 历史值，其中history[filter_WIN_SIZE-1]为最近的记录
static data_record_item_t weightedHistory[WEIGHTED_MOVING_WIN_SIZE]; // 历史值，其中history[filter_WIN_SIZE-1]为最近的记录

static int movingBuff_init = 0;   // 前filter_WIN_SIZE-1次填充后才能开始输出
static int movingIndex = 0;       // 环形数组可放数据的位置
static int weightedBuff_init = 0; // 前filter_WIN_SIZE-1次填充后才能开始输出
static int weightedIndex = 0;     // 环形数组可放数据的位置

static int factor[WEIGHTED_MOVING_WIN_SIZE] = {1, 2, 3, 4, 5, 6}; // 加权系数
static int K = 21;                                                // 1+2+3+4+5+6

/**
 * @brief 滑动平均滤波
 *
 * @param current
 * @return data_record_t
 */
data_record_t moving_mean_filter(data_record_item_t *current)
{
    int i, j;
    struct __filter_t filter = {.filter_x = -1, .filter_y = -1, .filter_velSpeedVal = -1};
    data_record_t record;
    record.currentAlarm = current->alarmType;
    record.lastAlarm = current->lastAlarmType;
    record.id = current->id; 
    record.extendedAlarmTime = 0;
    record.delayCnt = 0;
    record.adasBSDWarning = current->adasBSDWarning;

    if (movingBuff_init == 0)
    {
        movingHistory[movingIndex] = *current;
        movingIndex++;
        if (movingIndex >= (MOVING_MEAN_WIN_SIZE - 1))
        {
            movingBuff_init = 1; // movingIndex有效范围是0-5，前面放到5，下一个就可以输出
        }
        record.filter.filter_x = current->x;
        record.filter.filter_y = current->y;
        record.filter.filter_velSpeedVal = current->velSpeedVal;
        return record; // 当前无法输出，做个特殊标记区分
    }
    else
    {
        movingHistory[movingIndex] = *current;
        movingIndex++;
        if (movingIndex >= MOVING_MEAN_WIN_SIZE)
        {
            movingIndex = 0; // movingIndex有效最大5,下次再从0开始循环覆盖
        }

        // 最新的数据是weightedHistory[j]，最早的数据是((j + (WEIGHTED_MOVING_WIN_SIZE - 1)) % WEIGHTED_MOVING_WIN_SIZE)
        j = movingIndex;
        for (i = 0; i < MOVING_MEAN_WIN_SIZE; i++)
        {
            ////注意i=0的值并不是最早的值
            // filter.filter_x += history[j].x * factor[i];//注意防止数据溢出
            // filter.filter_y += history[j].y * factor[i];//注意防止数据溢出
            // 注意i=0的值并不是最早的值
            filter.filter_x += movingHistory[j].x;                     // 注意防止数据溢出
            filter.filter_y += movingHistory[j].y;                     // 注意防止数据溢出
            filter.filter_velSpeedVal += movingHistory[j].velSpeedVal; // 注意防止数据溢出
            j++;
            if (j == MOVING_MEAN_WIN_SIZE)
            {
                j = 0;
            }
        }

        // filter.filter_x = filter.filter_x / K;//注意防止数据溢出
        // filter.filter_y = filter.filter_y / K;//注意防止数据溢出
        filter.filter_x = filter.filter_x / MOVING_MEAN_WIN_SIZE;                     // 注意防止数据溢出
        filter.filter_y = filter.filter_y / MOVING_MEAN_WIN_SIZE;                     // 注意防止数据溢出
        filter.filter_velSpeedVal = filter.filter_velSpeedVal / MOVING_MEAN_WIN_SIZE; // 注意防止数据溢出

        record.filter = filter;

        return record;
    }
}

/**
 * @brief 加权滑动滤波
 *
 * @param current
 * @return data_record_t
 */
data_record_t weighted_moving_filter(data_record_item_t *current)
{
    int i, j;
    struct __filter_t filter = {.filter_x = -1, .filter_y = -1};
    data_record_t record;
    record.currentAlarm = current->alarmType;
    record.lastAlarm = current->lastAlarmType;

    if (weightedBuff_init == 0)
    {
        weightedHistory[weightedIndex] = *current;
        weightedIndex++;
        if (weightedIndex >= (WEIGHTED_MOVING_WIN_SIZE - 1))
        {
            weightedBuff_init = 1; // weightedIndex有效范围是0-5，前面放到5，下一个就可以输出
        }
        record.filter.filter_x = current->x;
        record.filter.filter_y = current->y;
        record.filter.filter_velSpeedVal = current->velSpeedVal;
        return record; // 当前无法输出，做个特殊标记区分
    }
    else
    {
        weightedHistory[weightedIndex] = *current;
        weightedIndex++;
        if (weightedIndex >= WEIGHTED_MOVING_WIN_SIZE)
        {
            weightedIndex = 0; // weightedIndex有效最大5,下次再从0开始循环覆盖
        }

        // 最新的数据是weightedHistory[j]，最早的数据是 ((j + (WEIGHTED_MOVING_WIN_SIZE - 1)) % WEIGHTED_MOVING_WIN_SIZE)
        j = weightedIndex;
        for (i = 0; i < WEIGHTED_MOVING_WIN_SIZE; i++)
        {
            // 注意i=0的值并不是最早的值
            filter.filter_x += weightedHistory[j].x * factor[i];                     // 注意防止数据溢出
            filter.filter_y += weightedHistory[j].y * factor[i];                     // 注意防止数据溢出
            filter.filter_velSpeedVal += weightedHistory[j].velSpeedVal * factor[i]; // 注意防止数据溢出
            j++;
            if (j == WEIGHTED_MOVING_WIN_SIZE)
            {
                j = 0;
            }
        }

        filter.filter_x = filter.filter_x / K;                     // 注意防止数据溢出
        filter.filter_y = filter.filter_y / K;                     // 注意防止数据溢出
        filter.filter_velSpeedVal = filter.filter_velSpeedVal / K; // 注意防止数据溢出

        record.filter = filter;

        return record;
    }
}
