<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ChengTechFrameCheck</class>
 <widget class="QMainWindow" name="ChengTechFrameCheck">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ChengTechFrameCheck</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QPlainTextEdit" name="plainTextEditLog"/>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuDevice">
    <property name="title">
     <string>设备</string>
    </property>
    <addaction name="actionOpenDevice"/>
    <addaction name="actionSelectDevice"/>
   </widget>
   <widget class="QMenu" name="menuRun">
    <property name="title">
     <string>运行</string>
    </property>
    <addaction name="actionSetOutTime"/>
    <addaction name="actionReset"/>
   </widget>
   <addaction name="menuRun"/>
   <addaction name="menuDevice"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionOpenDevice"/>
   <addaction name="actionSelectDevice"/>
   <addaction name="actionReset"/>
  </widget>
  <action name="actionOpenDevice">
   <property name="text">
    <string>打开设备</string>
   </property>
  </action>
  <action name="actionSelectDevice">
   <property name="text">
    <string>选择设备</string>
   </property>
  </action>
  <action name="actionSetOutTime">
   <property name="text">
    <string>设置超时时间</string>
   </property>
  </action>
  <action name="actionReset">
   <property name="text">
    <string>重置</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
