﻿#include "analysisdataviewf.h"
#include "ui_analysisdataviewf.h"

#include "analysisdatatableview.h"
#include "analysismodel.h"

namespace Views {
namespace AnalysisView {

AnalysisDataViewF::AnalysisDataViewF(quint8 radarID, Parser::ParsedDataTypedef::ParsedData *parsedData, QWidget *parent) :
    AnalysisDataViewI(radarID, parent),
    ui(new Ui::AnalysisDataViewF),
    mParsedData(parsedData)
{
    mRadarType = ForwardRadar;

    ui->setupUi(this);
}

AnalysisDataViewF::~AnalysisDataViewF()
{
    delete ui;
}

void AnalysisDataViewF::parsedData(const Parser::ParsedDataTypedef::ParsedData &parsedData)
{
    foreach (AnalysisDataTableView *view, mAnalysisDataTableViews) {
        QAbstractTableModel *model = view->model();
        if (!model) {
            continue;
        }
        int viewType = view->viewType();
        switch(viewType) {
        case Parser::ParsedDataTypedef::TargetRaw:
        case Parser::ParsedDataTypedef::TargetTrack:
        case Parser::ParsedDataTypedef::HeSaiLider:
            ((AnalysisModelF*)model)->targets(0, viewType, parsedData.mTargets[viewType]);
            break;
        default:
            break;
        }
    }
}

void AnalysisDataViewF::setViewAnalysisTypes(int fType, bool moving, bool continuous, const ViewTypes &types)
{
    switch(fType) {
    case Parser::ParsedDataTypedef::TargetRaw:
        mMovingOnlyRaw = moving;
        mContinuousDisplayRaw = continuous;
        mAnalysisTypesRaw = types;
        break;
    case Parser::ParsedDataTypedef::TargetTrack:
        mMovingOnlyRaw = moving;
        mContinuousDisplayRaw = continuous;
        mAnalysisTypesTrack = types;
        break;
    case Parser::ParsedDataTypedef::HeSaiLider:
        break;
    default:
        break;
    }
    foreach (AnalysisDataTableView *view, mAnalysisDataTableViews) {
        QAbstractTableModel *model = view->model();
        if (!model) {
            continue;
        }
        int viewType = view->viewType();
        if (viewType != fType) {
            continue;
        }
        switch(viewType) {
        case Parser::ParsedDataTypedef::TargetRaw:
        case Parser::ParsedDataTypedef::TargetTrack:
        case Parser::ParsedDataTypedef::HeSaiLider:
            ((AnalysisModelF*)model)->setViewAnalysisTypes(moving, continuous, types);
        default:
            return;
        }
        view->changeTableView();
    }
}

void AnalysisDataViewF::viewTypeChanged()
{
    AnalysisDataTableView *tableView = qobject_cast<AnalysisDataTableView *>(sender());
    if (!tableView) {
        return;
    }
    QAbstractTableModel *model = tableView->model();
    if (!model) {
        return;
    }

    int viewType = tableView->viewType();
    switch(viewType) {
    case Parser::ParsedDataTypedef::TargetRaw:
        ((AnalysisModelF*)model)->setViewAnalysisTypes(mMovingOnlyRaw, mContinuousDisplayRaw, mAnalysisTypesRaw);
        ((AnalysisModelF*)model)->targets(mRadarID, viewType, mParsedData->mTargets[viewType]);
        break;
    case Parser::ParsedDataTypedef::TargetTrack:
        ((AnalysisModelF*)model)->setViewAnalysisTypes(mMovingOnlyTrack, mContinuousDisplayTrack, mAnalysisTypesTrack);
        ((AnalysisModelF*)model)->targets(mRadarID, viewType, mParsedData->mTargets[viewType]);
        break;
    case Parser::ParsedDataTypedef::HeSaiLider:
        break;
    default:
        break;
    }

    tableView->changeTableView();
}

}
}
