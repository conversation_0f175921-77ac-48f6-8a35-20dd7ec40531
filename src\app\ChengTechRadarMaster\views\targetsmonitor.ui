<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TargetsMonitor</class>
 <widget class="QWidget" name="TargetsMonitor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>586</width>
    <height>417</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QComboBox" name="comboBoxFrameType">
       <item>
        <property name="text">
         <string>原始点</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>跟踪点</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>跟踪点(16)</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>监视ID:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="spinBoxMonitorTargetID">
       <property name="maximum">
        <number>244</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>显示数量:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditViewTargetCount">
       <property name="text">
        <string>500</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonClear">
       <property name="text">
        <string>清除</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonAddMonitor">
       <property name="text">
        <string>添加</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QGridLayout" name="gridLayout"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
