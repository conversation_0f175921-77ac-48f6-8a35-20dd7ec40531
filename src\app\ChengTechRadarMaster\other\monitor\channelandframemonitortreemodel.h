#ifndef CHANNELANDFRAMEMONITORTREEMODEL_H
#define CHANNELANDFRAMEMONITORTREEMODEL_H

namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
};

#include <QObject>
#include <QAbstractItemModel>

class ChannelAndFrameMonitorTreeItem : public QObject
{
    Q_OBJECT

    struct monitorFrameInfo{
        quint32 frameID;        //帧ID
        quint32 monitorCycle;   //监视周期 单位ms
        quint32 recvCycle;      //接收周期 单位ms
        bool bContinue;         //是否继续后续流程
        quint64 recvCount;      //接收数量
        quint64 errorCount;     //异常数量
        quint64 lastTime;       //上一次接收的时间
        QByteArray lastData;    //上一次的帧数据
        quint64 lastCheckTime;  //上一次校验时间
    };

    struct monitorChannelInfo{
        quint8 channelIndex; //通道ID
        bool bContinue; //是否后续流程
    };

public:
    enum TYPE{
        TREEITEMTYPE_ERROR = 0,
        TREEITEMTYPE_CHANNEL,
        TREEITEMTYPE_FRAME
    };

public:
    ChannelAndFrameMonitorTreeItem( ChannelAndFrameMonitorTreeItem* parent, TYPE type );
    ~ChannelAndFrameMonitorTreeItem();

signals:
    void frameCheckAbnormal( quint8 channelIdx, quint64 frameID, quint64 lastTime,
                  quint64 currTime, quint64 diffTime, quint64 checkCycle, const QByteArray& );

public:
//    void setPtr(void* p) { mPtr = p; }
//    void* ptr() const { return mPtr; }

    TYPE type() const { return mType; };
    quint32 row() const { return mRow; };
    ChannelAndFrameMonitorTreeItem* parent(){ return mParent; };
    ChannelAndFrameMonitorTreeItem* child( int row );
    void deleteChild( int row );
    int childCount(){ return mChilds.size(); };

    QVariant data( int column );
    void setChannelData( quint8 channelIndex, bool bContinue );
    void setFrameData( quint32 frameID, quint32 monitorCycle, bool bContinue, quint64 recvCount = 0, quint64 errorCount = 0 );

    void initFrame();
//    void recvFrame( quint64 currentTime, const QByteArray& data );//接收到帧，更新计数
    void recvFrame( const Devices::Can::CanFrame& frame );
//    bool checkFrame( quint64 currentTime ); //根据周期时间进行校验

protected:
    void addChild( ChannelAndFrameMonitorTreeItem* child );

protected:
    ChannelAndFrameMonitorTreeItem* mParent{NULL};
    QList< ChannelAndFrameMonitorTreeItem* > mChilds;
    TYPE mType{TREEITEMTYPE_ERROR};
    void* mPtr{NULL};
    quint32 mRow{0};
};

class ChannelAndFrameMonitorTreeModel : public QAbstractItemModel
{
    Q_OBJECT
public:
    ChannelAndFrameMonitorTreeModel( QObject *parent = nullptr );
    ~ChannelAndFrameMonitorTreeModel();

signals:
    void frameCheckAbnormal( quint8 channelIdx, quint64 frameID, quint64 lastTime,
                  quint64 currTime, quint64 diffTime, quint64 checkCycle, const QByteArray& );

public:
    void addChannelMonitor(); //添加通道监视
    void addFrameMonitor( const QModelIndex& index ); //添加帧监视
    void deleteMonitor( const QModelIndex& index ); //删除监视
    bool isChannelIndex( const QModelIndex& index ); //判断index是否为通道节点
    bool isRun(){ return mRun; };

    void getChannelFilter( QList<quint8>& channelIdxList );
    void getFrameFilter( QMap< quint8, QList<quint64> > & frameMap );

public slots:
    void startOrStop();
    void recvFrame( const Devices::Can::CanFrame& frame );
    void saveToXml();

protected: //父类虚函数
    QVariant headerData(int section, Qt::Orientation orientation, int role) const;
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    Qt::ItemFlags flags(const QModelIndex &index) const override;

    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    virtual QModelIndex parent(const QModelIndex &child) const;
    //virtual QModelIndex sibling(int row, int column, const QModelIndex &idx) const;
    //virtual bool hasChildren(const QModelIndex &parent = QModelIndex()) const;

protected:
    //根据index获取树节点
    ChannelAndFrameMonitorTreeItem* getItemFromIndex( const QModelIndex &index ) const;

    //初始化
    void initChannelMap();
//    void initTimerMap();
//    void closeTimer();
//    void timerEvent(QTimerEvent *event); //定时器  用于检查是否丢帧

    void dataChange( ChannelAndFrameMonitorTreeItem* pItem ); //更新界面显示

//    void timerOut( quint32 timerID );

//private:
    void clear();
    void loadFromXml();


private:
        QStringList mHeader;
        ChannelAndFrameMonitorTreeItem* mTreeRoot{NULL};
        QMap< quint8, QList<ChannelAndFrameMonitorTreeItem*> > mChannelMap; //通道下有哪些帧
//        QMap< int, QList<ChannelAndFrameMonitorTreeItem*> > mTimerMap; //定时器 <timerID,frameItem>
//        QMap< quint64, int > mTimerMap2; //定时器信息<MS,timerID>
        bool mRun{false};
};

#endif // CHANNELANDFRAMEMONITORTREEMODEL_H
