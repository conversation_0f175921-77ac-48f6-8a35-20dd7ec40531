﻿#include "mulitcamera.h"
#include "ui_mulitcamera.h"

//#include "ffmpeg/ffmpegcamera.h"
#include "opencv/opencvcamera.h"

#include <QCameraViewfinder>
#include <QCameraInfo>
#include <QInputDialog>
#include <QThread>

namespace Camera {

QMap<QString /*description + deviecname*/, Camera::MulitCamera*> MulitCamera::mListCameraInfo;     ///< 摄像头信息
int MulitCamera::mCameraOpenCount{0};

bool MulitCamera::isOpened(const QCameraInfo &cameraInfo)
{
    return mListCameraInfo.contains(cameraInfo.description() + cameraInfo.deviceName());
}

MulitCamera::MulitCamera(const QCameraInfo &cameraInfo, const QString alais, QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::MulitCamera),
    mCameraInfo(cameraInfo),
    m<PERSON><PERSON>s(alais)
{
    ui->setupUi(this);

    QActionGroup *videoDevicesGroup = new QActionGroup(this);
    videoDevicesGroup->setExclusive(true);

//    QCamera *camera = new QCamera(cameraInfo);
    QString deviceName = cameraInfo.deviceName();
    deviceName.replace("@device:pnp:", "@device_pnp_");
    std::string url = QString("video=%1").arg(deviceName).toLocal8Bit().data();
//    mCamera = new FFmpegCamera(url, ++mCameraOpenCount);
//    connect(mCamera, &FFmpegCamera::showImage, ui->videoWidget, &CameraImage::showImage, Qt::BlockingQueuedConnection);
//    connect(ui->actionStart, &QAction::triggered, mCamera, &FFmpegCamera::open);
//    connect(ui->actionStop, &QAction::triggered, mCamera, &FFmpegCamera::close);
//    connect(mCamera, &FFmpegCamera::cameraSaveIndex, this, &MulitCamera::cameraSaveIndex);
//    connect(mCamera, &FFmpegCamera::stateChanged, this, &MulitCamera::updateCameraState);
//    connect(this, &MulitCamera::open, mCamera, &FFmpegCamera::open);
//    connect(this, &MulitCamera::close, mCamera, &FFmpegCamera::close);
//    connect(this, &MulitCamera::cameraQuit, mCamera, &FFmpegCamera::close);
//    connect(this, &MulitCamera::startSaveVideo, mCamera, &FFmpegCamera::startSaveVideo);
//    connect(this, &MulitCamera::stopSave, mCamera, &FFmpegCamera::stopSave);


    const QList<QCameraInfo> availableCameras = QCameraInfo::availableCameras();
    int index = availableCameras.indexOf(cameraInfo);
    qDebug() << __FUNCTION__ << __LINE__ << index;
    mCamera = new OpenCVCamera(index);
    QThread *cameraThread = new QThread;
    mCamera->moveToThread(cameraThread);

//    mCamera->namedWindowToWidget(QString("OpenCVCamera_%1").arg(index).toStdString(), ui->videoWidget);
    connect(cameraThread , &QThread::started, mCamera , &OpenCVCamera::run, Qt::QueuedConnection);//CAn线程启动，单独跑，不影响主进程
    connect(mCamera, &OpenCVCamera::opened, this, [=]() { cameraThread->start(); });
    connect(mCamera, &OpenCVCamera::stoped, cameraThread , &QThread::quit);

    connect(mCamera, &OpenCVCamera::showImage, ui->videoWidget, &CameraImage::showImage, Qt::BlockingQueuedConnection);
    connect(ui->actionStart, &QAction::triggered, mCamera, &OpenCVCamera::open);
    connect(ui->actionStop, &QAction::triggered, mCamera, &OpenCVCamera::close);
    connect(mCamera, &OpenCVCamera::cameraSaveIndex, this, &MulitCamera::cameraSaveIndex);
//    connect(mCamera, &OpenCVCamera::stateChanged, this, &MulitCamera::updateCameraState);
    connect(this, &MulitCamera::open, mCamera, &OpenCVCamera::open, Qt::DirectConnection);
    connect(this, &MulitCamera::close, mCamera, &OpenCVCamera::close, Qt::DirectConnection);
    connect(this, &MulitCamera::cameraQuit, mCamera, &OpenCVCamera::close, Qt::DirectConnection);
    connect(this, &MulitCamera::startSaveVideo, mCamera, &OpenCVCamera::startSaveVideo, Qt::DirectConnection);
    connect(this, &MulitCamera::stopSave, mCamera, &OpenCVCamera::stopSave, Qt::DirectConnection);


    connect(ui->actionQuit, &QAction::triggered, this, &MulitCamera::quit);
    connect(ui->actionSettings, &QAction::triggered, this, &MulitCamera::settings);
    connect(ui->actionAlias, &QAction::triggered, this, &MulitCamera::setAlias);

//    connect(this, &MulitCamera::cameraQuit, camera, &QThread::deleteLater);
//    connect(camera, &QCamera::destroyed, cameraThread, &QThread::quit);
//    connect(cameraThread, &QThread::finished, cameraThread, &QThread::deleteLater);
    mCamera->open();
    updateCameraState(mCamera->state());

    mListCameraInfo[mCameraInfo.description() + mCameraInfo.deviceName()] = this;
}

MulitCamera::~MulitCamera()
{
    if (mCamera->isOpened()) {
        mCamera->close();
    }
    mListCameraInfo.remove(mCameraInfo.description() + mCameraInfo.deviceName());
    delete ui;
}

bool MulitCamera::isOpened()
{
    return mCamera->isOpened();
}

bool MulitCamera::isSaveing()
{
    return mCamera->isSaveing();
}

/**
* <AUTHOR>
* @date 2022-05-22
* @param
* @return void
* @note
* 关闭摄像头
* @remarks
*/
void MulitCamera::quit()
{
    emit cameraQuit(mCameraInfo.description() + mCameraInfo.deviceName());
}

/**
* <AUTHOR>
* @date 2022-05-22
* @param
* @return void
* @note
* 设置摄像头
* @remarks
*/
void MulitCamera::settings()
{

}

/**
* <AUTHOR>
* @date 2022-05-23
* @param
* @return void
* @note
* 设置别名
* @remarks
*/
void MulitCamera::setAlias()
{
    bool ok;
    QString alias = QInputDialog::getText(this, tr("Set Camera Alais"),
                                         tr("Camera Alais:"), QLineEdit::Normal,
                                         mAlias, &ok);
    if (ok && !alias.isEmpty() && mAlias != alias)
    {
        mAlias = alias;
        emit aliasChenged(alias);
    }
}

/**
* <AUTHOR>
* @date 2022-05-22
* @param
* @return void
* @note
* 摄像头状态改变
* @remarks
*/
void MulitCamera::updateCameraState(QCamera::State state)
{
    ui->actionStart->setEnabled(state != QCamera::ActiveState);
    ui->actionStop->setEnabled(state == QCamera::ActiveState);
    ui->actionSettings->setEnabled(state == QCamera::ActiveState);
}

} // namespace Camera
