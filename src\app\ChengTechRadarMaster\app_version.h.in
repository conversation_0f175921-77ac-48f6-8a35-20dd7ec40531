﻿#pragma once

namespace Core {
namespace Constants {

#define STRINGIFY_INTERNAL(x) #x
#define STRINGIFY(x) STRINGIFY_INTERNAL(x)

const char MASTER_DISPLAY_NAME[] = \"$${MASTER_DISPLAY_NAME}\";
const char MASTER_ID[] = \"$${MASTER_ID}\";
const char MASTER_CASED_ID[] = \"$${MASTER_CASED_ID}\";

#define MASTER_VERSION $${MASTER_VERSION}
#define MASTER_VERSION_STR STRINGIFY(MASTER_VERSION)
#define MASTER_VERSION_DISPLAY_DEF $${MASTER_DISPLAY_VERSION}
#define MASTER_VERSION_COMPAT_DEF $${MASTER_COMPAT_VERSION}

#define MASTER_VERSION_MAJOR $$replace(MASTER_VERSION, "^(\\d+)\\.\\d+\\.\\d+(-.*)?$", \\1)
#define MASTER_VERSION_MINOR $$replace(MASTER_VERSION, "^\\d+\\.(\\d+)\\.\\d+(-.*)?$", \\1)
#define MASTER_VERSION_RELEASE $$replace(MASTER_VERSION, "^\\d+\\.\\d+\\.(\\d+)(-.*)?$", \\1)

const char MASTER_VERSION_LONG[]      = MASTER_VERSION_STR;
const char MASTER_VERSION_DISPLAY[]   = STRINGIFY(MASTER_VERSION_DISPLAY_DEF);
const char MASTER_VERSION_COMPAT[]    = STRINGIFY(MASTER_VERSION_COMPAT_DEF);
const char MASTER_AUTHOR[]            = \"The ChengTech Ltd\";
const char MASTER_YEAR[]              = \"$${MASTER_COPYRIGHT_YEAR}\";

#ifdef MASTER_REVISION
const char MASTER_REVISION_STR[]      = STRINGIFY(MASTER_REVISION);
#else
const char MASTER_REVISION_STR[]      = \"\";
#endif

const char MASTER_REVISION_URL[]  = \"$${MASTER_REVISION_URL}\";

// changes the path where the settings are saved to
#ifdef MASTER_SETTINGSVARIANT
const char MASTER_SETTINGSVARIANT_STR[]      = STRINGIFY(MASTER_SETTINGSVARIANT);
#else
const char MASTER_SETTINGSVARIANT_STR[]      = \"ChengTechRadarMaster\";
#endif

#ifdef MASTER_COPY_SETTINGS_FROM_VARIANT
const char MASTER_COPY_SETTINGS_FROM_VARIANT_STR[] = STRINGIFY(MASTER_COPY_SETTINGS_FROM_VARIANT);
#else
const char MASTER_COPY_SETTINGS_FROM_VARIANT_STR[] = \"\";
#endif

#undef MASTER_VERSION_COMPAT_DEF
#undef MASTER_VERSION_DISPLAY_DEF
#undef MASTER_VERSION
#undef MASTER_VERSION_STR
#undef STRINGIFY
#undef STRINGIFY_INTERNAL

} // Constants
} // Core
