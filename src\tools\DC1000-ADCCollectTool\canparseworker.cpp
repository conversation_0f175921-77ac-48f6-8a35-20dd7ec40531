﻿#include "canparseworker.h"

#include <iostream>
#include <QDebug>

#define BIG_LITTLE_SWAP16(x)        ( (((*(short int *)&x) & 0xff00) >> 8) | \
                                      (((*(short int *)&x) & 0x00ff) << 8) )

#define BIG_LITTLE_SWAP32(x)        ( (((*(long int *)&x) & 0xff000000) >> 24) | \
                                      (((*(long int *)&x) & 0x00ff0000) >> 8) | \
                                      (((*(long int *)&x) & 0x0000ff00) << 8) | \
                                      (((*(long int *)&x) & 0x000000ff) << 24) )

void frameParse(CANParseWorker *pParse) {
    std::cout << "线程结束，开始解析CAN数据." << std::endl;
    while (pParse->isParsing())
    {
        pParse->parse();
    }
    std::cout << "线程结束，停止解析CAN数据." << std::endl;
}

CANParseWorker::CANParseWorker(CANSaveWorker *canSaveWorker, QObject *parent) : QObject(parent), mCANSaveWorker(canSaveWorker)
{
    mAnalysisProtocolCT410 = new AnalysisProtocolCT410(this, this);
}

bool CANParseWorker::start()
{
    mAnalysisDatas.mCameraSaveIndex = 0;
    mSafeQueue.clear();
    mParsing = true;

    std::thread(frameParse, this).detach();

    return mParsing;
}

bool CANParseWorker::stop()
{
    mParsing = false;

    return !mParsing;
}

void CANParseWorker::parse()
{
    std::shared_ptr<CANFrame> pFrame = mSafeQueue.pop();
    if (pFrame) {
        parse(*pFrame.get());
    }
}

void CANParseWorker::analysisEnd(quint8 radarID, bool assigned)
{
    emit targets(mAnalysisDatas);
    if (mCANSaveWorker->isSaving()) {
        mCANSaveWorker->save(mAnalysisDatas);
    }
}

void CANParseWorker::cameraReady(unsigned long long saveIndex)
{
    mAnalysisDatas.mCameraSaveIndex = saveIndex;
    QByteArray data = QByteArray::fromHex(QString("43 54 00 01 %1 %2")
                                          .arg(0, 8, 16, QLatin1Char('0'))
                                          .arg(saveIndex, 16, 16, QLatin1Char('0'))
                                          .toLocal8Bit()
                                          );
    mSafeQueue.push(CANFrame(0,
                             0,
                             (unsigned char *)data.data(),
                             data.length(),
                             true,
                             true));
}

void CANParseWorker::parse(CANFrame &frame)
{
    if (mCANSaveWorker->isSaving()) {
        mCANSaveWorker->pushFrame(frame);
    }

    if (!frame.rx) {
        return;
    }

    switch (frame.id) {
    case 0x3F9:
        parse0x3F9(frame);
        break;
    case 0x4F4:
    case 0x4F5:
    case 0x4F6:
    case 0x4F7:
//        qDebug() << __FUNCTION__ << __LINE__ << QString::number(frame.id, 16) << frame.rx;
        emit radarReceived(frame.id & 0xF);
    default:
        mAnalysisProtocolCT410->analysisFrame(frame);
        break;
    }
}

void CANParseWorker::parse0x3F9(CANFrame &frame)
{
    qDebug() << __FUNCTION__ << __LINE__ << QString::number(frame.id, 16) << frame.rx << QByteArray::fromRawData((char *)frame.data, 8).toHex(' ');
    quint8 *data = frame.data;
    unsigned short head = BIG_LITTLE_SWAP16(*((unsigned short*)(data)));
    if (head == 0x4354 ) {

        unsigned short cmd = BIG_LITTLE_SWAP16(*((unsigned short*)(data + 2)));
        unsigned int status = BIG_LITTLE_SWAP32(*((unsigned int*)(data + 4)));
        qDebug() << __FUNCTION__ << __LINE__ << head << cmd << status;
        switch (cmd) {
        case 0x1A2B:
            emit radarAlready(status);
            break;
        case 0x4354:
            emit radarCollectStoped(status);
            break;
        }
    }
}
