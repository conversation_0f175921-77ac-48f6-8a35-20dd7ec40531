﻿#include "ffmpegwirter.h"

#include "ffmpeginitializer.h"

#include <iostream>
#include <QDateTime>

FFmpegWirter::FFmpegWirter()
{
    FFmpegInitializer::initFFmpeg();
}

FFmpegWirter::~FFmpegWirter()
{
    FFmpegInitializer::releaseFFmpeg();
}

bool FFmpegWirter::save(const char *filename, AVStream *streamIn)
{
    if (!streamIn) {
        return false;
    }
    // FILE* fp = nullptr;
    // fopen_s(&fp, "1.h264", "wb");
    // 改变为保存mp4方式
    if (!mpFormatContextOutput) {
        avformat_alloc_output_context2(&mpFormatContextOutput, NULL, NULL, filename);
    }
    if (!mpFormatContextOutput)
    {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "Could not create output context" << std::endl;
        return false;
    }

    if (0 > avio_open(&mpFormatContextOutput->pb, filename, AVIO_FLAG_WRITE)) {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "Could not avio_open output context" << std::endl;
        return false;
    }

    // 8. 根据编码器ID，获取解码器, 不需要释放，解码器上下文释放时，会一起释放;
    const AVCodec* encodec = avcodec_find_encoder(mpFormatContextOutput->oformat->video_codec);
    //avcodec_find_encoder_by_name();
    if (!encodec)
    {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "encodec == NULL" << std::endl;
    }
    // 释放：avcodec_free_context()
    if (!mpCodecContextOutput) {
        mpCodecContextOutput = avcodec_alloc_context3(encodec);
    }
    if (!mpCodecContextOutput)
    {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "enc == NULL" << std::endl;
        return false;
    }

    mpCodecContextOutput->width = streamIn->codecpar->width;
    mpCodecContextOutput->height = streamIn->codecpar->height;
    mpCodecContextOutput->pix_fmt = AV_PIX_FMT_YUV420P/**/;
    mpCodecContextOutput->time_base = {1, 30};
    mpCodecContextOutput->framerate = {30, 1};
    mpCodecContextOutput->bit_rate = 4000000;
    mpCodecContextOutput->gop_size = 10;
    mpCodecContextOutput->max_b_frames = 1;
    mpCodecContextOutput->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;
//    mpCodecContextOutput->max_b_frames = 1;                      // 非B帧之间的最大B帧数(有些格式不支持)
    //加载预设
    av_opt_set(mpCodecContextOutput->priv_data, "preset", "slow", 0);
    av_opt_set(mpCodecContextOutput->priv_data, "tune", "zerolatency", 0);

    int ret = avcodec_open2(mpCodecContextOutput, encodec, NULL);
    if (ret < 0)
    {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "encodec open error" << std::endl;
        return false;
    }

    // 用户需要调用avformat_free_context()来清理avformat_new_stream()分配的内存。
    if (!mpStreamsOutput) {
        mpStreamsOutput = avformat_new_stream(mpFormatContextOutput, NULL);
    }
    if (!mpStreamsOutput)
    {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "Failed allocating output stream" << std::endl;
        return false;
    }
    //Copy the settings of AVCodecContext
    if (avcodec_parameters_copy(mpStreamsOutput->codecpar, streamIn->codecpar) < 0)
    {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "Failed to copy context from input to output stream codec context" << std::endl;
        return false;
    }
//    mpStreamsOutput->codecpar->codec_tag = 0;
//    if (mpFormatContextOutput->oformat->flags & AVFMT_GLOBALHEADER)
//    {
//        encodeCtx->flags |= CODEC_FLAG_GLOBAL_HEADER;
//    }

    avcodec_parameters_from_context(mpStreamsOutput->codecpar, mpCodecContextOutput); //必须


    av_dump_format(mpFormatContextOutput, 0, filename, 1);

    if (!(mpFormatContextOutput->oformat->flags & AVFMT_NOFILE))
    {
        ret = avio_open(&mpFormatContextOutput->pb, filename, AVIO_FLAG_WRITE);
        if (ret < 0)
        {
            std::cout << __FUNCTION__ << " " << __LINE__ << " " << "Could not open output file " << filename << std::endl;
            return false;
        }
    }


    if (avformat_write_header(mpFormatContextOutput, NULL) < 0)
    {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "Error occurred when opening output file" << std::endl;
        return false;
    }

    // 12. 数据帧，释放av_frame_free();
    if (!mpFrameYUV420P) {
        mpFrameYUV420P = av_frame_alloc();
    }

    if (!mpPacketOutput) {
        mpPacketOutput = av_packet_alloc();
    }
    if (!mpPacketOutput) {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "Error av_packet_alloc" << std::endl;
        return false;
    }

    mSaveIndex = 0;
    mSaveing = true;
    std::cout << __FUNCTION__ << " " << __LINE__ << " " << "Save begin ..." << std::endl;

    return true;
}

bool FFmpegWirter::stop()
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutexSave);

    mSaveing = false;

    if (0 > av_write_trailer(mpFormatContextOutput)) {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "av_write_trailer error!" << std::endl;
    }

    // 释放frame、packet，地址会被置为0

    av_frame_free(&mpFrameYUV420P);
    av_packet_free(&mpPacketOutput);

    sws_freeContext(mpSwsContextYUV420P);
    mpSwsContextYUV420P = NULL;

    // 关闭解码器上下文，mpCodecContext需要手动释放
    avcodec_close(mpCodecContextOutput);
    // 释放解码器上下文, mpCodecContext会被置为0
    avcodec_free_context(&mpCodecContextOutput);

    // 关闭输入，释放封装上下文，mpFormatContext会被置0，不需要手动释放
    //（1）调用AVInputFormat的read_close()方法关闭输入流
    //（2）调用avformat_free_context()释放AVFormatContext
    //（3）调用avio_close()关闭并且释放AVIOContext
    avformat_close_input(&mpFormatContextOutput);
    // 内存被avformat_close_input过程释放，但是地址需要置空
    mpStreamsOutput = 0;

    return true;
}

qreal rationalToDouble(AVRational* rational)
{
    qreal frameRate = (rational->den == 0) ? 0 : (qreal(rational->num) / rational->den);
    return frameRate;
}

long long FFmpegWirter::write(AVFrame *frame)
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutexSave);
    if (!mSaveing)
    {
        return -1;
    }
    if (!toYUV420P(frame)) {
        return -1;
    }


    if (mpFrameYUV420P)
    {
        mpFrameYUV420P->pts = mSaveIndex;
    }

    // 将图像传入编码器
    int ret = avcodec_send_frame(mpCodecContextOutput, mpFrameYUV420P);
    if (ret < 0)
    {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avcodec_send_frame error!" << std::endl;
        return -1;
    }

    // 循环读取所有编码完的帧

    // 从编码器中读取图像帧
    if (0 <= avcodec_receive_packet(mpCodecContextOutput, mpPacketOutput)) {

        // 将数据包中的有效时间字段（时间戳/持续时间）从一个时基转换为 输出流的时间
        av_packet_rescale_ts(mpPacketOutput, mpCodecContextOutput->time_base, mpStreamsOutput->time_base);


        qint64 pts = qRound64(mpPacketOutput->pts * (1000 * rationalToDouble(&(mpStreamsOutput->time_base))));
        qint64 dts = qRound64(mpPacketOutput->dts * (1000 * rationalToDouble(&(mpStreamsOutput->time_base))));
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << mSaveIndex << " " << mpFrameYUV420P->pts << " " << mpPacketOutput->pts << pts << dts << std::endl;
//        av_write_frame(mpFormatContextOutput, mpPacketOutput);   // 将数据包写入输出媒体文件
        av_interleaved_write_frame(mpFormatContextOutput, mpPacketOutput);
        av_packet_unref(mpPacketOutput);
    }

    mSaveIndex++;

    return mSaveIndex - 1;
}

bool FFmpegWirter::toYUV420P(AVFrame *mpFrame)
{
    // 图像转换上下文
    if (!mpSwsContextYUV420P)
    {
        // 获取缓存的图像转换上下文。首先校验参数是否一致，如果校验不通过就释放资源；然后判断上下文是否存在，如果存在直接复用，如不存在进行分配、初始化操作
        mpSwsContextYUV420P = sws_getCachedContext(mpSwsContextYUV420P,
                                            mpFrame->width,                    // 输入图像的宽度
                                            mpFrame->height,                   // 输入图像的高度
                                            (AVPixelFormat) mpFrame->format,   // 输入图像的像素格式
                                            mpFrame->width,                    // 输出图像的宽度
                                            mpFrame->height,                   // 输出图像的高度
                                            AV_PIX_FMT_YUV420P,                // 输出图像的像素格式
                                            SWS_FAST_BILINEAR,                 // 选择缩放算法(只有当输入输出图像大小不同时有效),一般选择SWS_FAST_BILINEAR
                                            nullptr,                           // 输入图像的滤波器信息, 若不需要传NULL
                                            nullptr,                           // 输出图像的滤波器信息, 若不需要传NULL
                                            nullptr);                          // 特定缩放算法需要的参数(?)，默认为NULL
        if (!mpSwsContextYUV420P)
        {
            return false;
        }
    }

    mpFrameYUV420P->width = mpFrame->width;
    mpFrameYUV420P->height = mpFrame->height;
    mpFrameYUV420P->height = mpFrame->height;
    mpFrameYUV420P->pts = mpFrame->pts;
    mpFrameYUV420P->pkt_dts = mpFrame->pkt_dts;
    mpFrameYUV420P->time_base = mpFrame->time_base;
    mpFrameYUV420P->pkt_size = mpFrame->pkt_size;
    mpFrameYUV420P->pkt_dts = mpFrame->pkt_dts;
    mpFrameYUV420P->quality = mpFrame->quality;
    mpFrameYUV420P->quality = mpFrame->quality;
    mpFrameYUV420P->format = AV_PIX_FMT_YUV420P;

    if (!mpFrameYUV420P->data[0])
    {
        av_frame_get_buffer(mpFrameYUV420P, 1);
        //        av_image_alloc(m_frame1->data, m_frame1->linesize, m_frame1->width, m_frame1->height, AV_PIX_FMT_YUV420P, 1);
    }

    int ret = sws_scale(mpSwsContextYUV420P,          // 缩放上下文
                        mpFrame->data,         // 原图像数组
                        mpFrame->linesize,     // 包含源图像每个平面步幅的数组
                        0,                     // 开始位置
                        mpFrameYUV420P->height,      // 行数
                        mpFrameYUV420P->data,        // 目标图像数组
                        mpFrameYUV420P->linesize);   // 包含目标图像每个平面的步幅的数组
    if (ret < 0)
    {
        return false;
    }
    return true;
}
