cmake 3.29.0
emscripten.patch 416ffaeb778323dd210a34b93d02efc2820b794c98c28f0ac5f5d82799cb50fd
features core
fix-arm-uwp.patch c9038ac61297ac59aa34bf0c93e3f2cea9d5f053e65ce5e4ad0207db47594720
install.patch 2945ebec5e350e275e1c0076a2b5bd4bd7bae4a9c6f1cef2a19988b739f4b75e
pkgconfig.patch a678ab71351fbf8ed2b80ca3c6934a332f74af9c6c0ee6d7ea1b0a598b8c8d08
portfile.cmake f95cbfbd877b11da6e6dd4411c7d9744a8c7ea99cc77e9c967c4d9418a265310
ports.cmake 0500e9e2422fe0084c99bdd0c9de4c7069b76da14c8b58228a7e95ebac43058a
post_build_checks 2
powershell 7.2.16
triplet x86-windows
triplet_abi 3e71dd1d4afa622894ae367adbbb1ecbd42c57c51428a86b675fa1c8cad3a581-e36df1c7f50ab25f9c182fa927d06c19ae082e0d599f132b3f655784b49e4b33-249e2da0fced5777e3f907c3dc10380ddd2c9c98
usage 2fc37651df1d64d9134a1aa6597de5f927efe1b5138a243bb87ba746aca04416
vcpkg-cmake a0a36e21d32b4206f4f67926d6d4f3a1085958580627e52ffa8bff1f0c62cae2
vcpkg-cmake-config aa32252af4aed54be2549eafd2e11448b181de98e4368128e2f3828b13482864
vcpkg.json 98b60e394a4b54e250ddf315d319ff17bae4057c7ab7bf14790e57e437731417
vcpkg_copy_pdbs d57e4f196c82dc562a9968c6155073094513c31e2de475694143d3aa47954b1c
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_fixup_pkgconfig 904e67c46ecbb67379911bc1d7222855c0cbfcf1129bf47783858bcf0cc44970
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
