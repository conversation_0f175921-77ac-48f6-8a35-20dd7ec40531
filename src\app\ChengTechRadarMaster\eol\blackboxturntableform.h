#ifndef BLACKBOXTURNTABLEFORM_H
#define BLACKBOXTURNTABLEFORM_H

#include "eoldefine.h"

#include <QWidget>
#include <QModbusTcpClient>

namespace Ui {
class BlackBoxTurnTableForm;
}

class BlackBoxTurnTableForm : public QWidget
{
    Q_OBJECT

public:
    explicit BlackBoxTurnTableForm(QWidget *parent = nullptr);
    ~BlackBoxTurnTableForm();

private slots:
    void on_rotateOpenPushButton_clicked();

    void on_connectPushButton_clicked();

    void modBusTcpStateChanged( QModbusDevice::State state );
    void modBusReplyFinished();


    void on_rotateClosePushButton_clicked();

signals:
    void showMsg( const QString& msg, EOL_MSG_TYPE type );

private:

private:
    Ui::BlackBoxTurnTableForm *ui;
    QModbusTcpClient mModBusClient;
    bool mConnect{false};
};

#endif // BLACKBOXTURNTABLEFORM_H
