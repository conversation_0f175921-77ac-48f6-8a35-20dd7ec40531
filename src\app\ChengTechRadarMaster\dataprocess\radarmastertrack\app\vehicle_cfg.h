#ifndef __VEHICLE_CFG__
#define __VEHICLE_CFG__

#include "other/temp.h"

//--------------------------------------------------------
// 车型定义
//--------------------------------------------------------
#define VEHICLE_TYPE_BYD_HAN_2022  //车型

//--------------------------------------------------------
// 平台定义
//--------------------------------------------------------
#define BYD_J5E_PLATFORM	1		// J5E平台，默认关闭
#if (BYD_J5E_PLATFORM == 1)
	#define BYD_ADVANCED_PLATFORM
#endif

//--------------------------------------------------------
// 承泰内部软件版本号
//--------------------------------------------------------
#define SW_VERSION { 5, 0, 0, 7 }  //软件版本

//--------------------------------------------------------
// 内部版本号任意值大于15后,宏要为 1
//--------------------------------------------------------
// #define INTERNAL_VERSION_PROTOCOL		0

// ADAS功能开关
#define ALARM_TYPE_DOW_EN 		1
#define ALARM_TYPE_LCA_EN 		1
#define ALARM_TYPE_BSD_EN 		1
#define ALARM_TYPE_FCTA_EN 		1
#define ALARM_TYPE_FCTB_EN 		1
#define ALARM_TYPE_RCTA_EN 		1
#define ALARM_TYPE_RCTB_EN 		1
#define ALARM_TYPE_RCW_EN 		1
#define ALARM_TYPE_EDR_EN 		1
#define ALARM_TYPE_FDOW_EN 		0
#define ALARM_TYPE_ELKA_OT_EN 	1
#define PARAM_DATAMODE_EN 		0 	//默认发送原始点开关

// 制动减速度
#define RCTB_START_DEC_VAL  	(-3.0f)		// RCTB触发制动时的减速度
#define RCTB_CONTINUE_DEC_VAL  	(-2.0f)     // RCTB减速完成后维持的减速度
#define FCTB_START_DEC_VAL  	(-4.0f)     // FCTB触发制动时的减速度
#define FCTB_CONTINUE_DEC_VAL  	(-2.0f)     // FCTB减速完成后维持的减速度

// hanev转向比是查表形式.  均值14
#define DEFAULT_STEER_RATIO   	(14.49f)  // 默认的转向比  即方向盘转动多少度  车轮转动一度.


#define DOW_WARNING_WIDTH_MIN   (-0.25f)

//#############################################################
// 雷达安装设置
//#############################################################

//--------------------------------------------------------
// 雷达接插件方向，接插件朝外乘以-1，接插件朝里乘以1
//--------------------------------------------------------
#define CONNECTOR_OUTWARD		-1
#define CONNECTOR_INWARD		1

#define CONNECTOR_DIR_FRONT_LEFT		CONNECTOR_INWARD	// 左前角雷达接插件朝向
#define CONNECTOR_DIR_FRONT_RIGHT		CONNECTOR_INWARD	// 右前角雷达接插件朝向
#define CONNECTOR_DIR_REAR_LEFT			CONNECTOR_OUTWARD	// 左后雷达接插件朝向
#define CONNECTOR_DIR_REAR_RIGHT		CONNECTOR_OUTWARD	// 右后雷达接插件朝向


//--------------------------------------------------------
// 雷达安装角度[单位：deg]
//--------------------------------------------------------
#define ALN_OBJ_HORIZONTAL_ANGLE_FCR		-45.0f  //前角雷达标定目标的位置，需要根据现场环境设定
#define ALN_OBJ_HORIZONTAL_ANGLE_RCR		-45.0f  //后角雷达标定目标的位置，需要根据现场环境设定

#define ALN_EXPECT_FIX_ANGLE_FCR			53.0f   //希望安装角度,即默认安装角度
#define ALN_EXPECT_FIX_ANGLE_RCR			45.0f   //希望安装角度,即默认安装角度

//--------------------------------------------------------
// 雷达安装位置[单位：m]，用于雷达目标的坐标系转换为后轴中心,X轴正方向为车辆纵向超车头，Y轴正方向为车辆横向超左，原点为后轴中心
//--------------------------------------------------------
#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL 3.50845f										//左前角雷达与后轴中心在X轴方向的距离,D1
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL 0.86158f										//左前角雷达与后轴中心在Y轴方向的距离,D3

#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL -0.91138f										//左后角雷达与后轴中心在X轴方向的距离,D2
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL 0.68230f										//左后角雷达与后轴中心在Y轴方向的距离,D4

#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_FR MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL				//右前角雷达与后轴中心在X轴方向的距离
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FR MOUNTPOS_TO_REAR_AXLE_CENTER_Y_FL * (-1)		//右前角雷达与后轴中心在Y轴方向的距离

#define MOUNTPOS_TO_REAR_AXLE_CENTER_X_RR MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL				//右后角雷达与后轴中心在X轴方向的距离
#define MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RR MOUNTPOS_TO_REAR_AXLE_CENTER_Y_RL * (-1)		//右后角雷达与后轴中心在Y轴方向的距离

#define MOUNT6POS_TO_OUTER_EDGE_X_FL 0.073f												//左前角雷达车身外边缘在X轴方向的距离,D5
#define MOUNT6POS_TO_OUTER_EDGE_Y_FL 0.44255f											//左前角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感

#define MOUNT4POS_TO_OUTER_EDGE_X_FL 0.264f												//左后角雷达与后轴中心在X轴方向的距离,D6
#define MOUNT4POS_TO_OUTER_EDGE_Y_FL 0.1932f											//左后角雷达与后轴中心在Y轴方向的距离,暂时缺失，Y轴方向距离暂不敏感

#define MOUNT7POS_TO_OUTER_EDGE_X_FL MOUNT6POS_TO_OUTER_EDGE_X_FL						//右前角雷达车身外边缘在X轴方向的距离,与左前角对称
#define MOUNT7POS_TO_OUTER_EDGE_Y_FL MOUNT6POS_TO_OUTER_EDGE_Y_FL						//右前角雷达与后轴中心在Y轴方向的距离,与左前角对称

#define MOUNT5POS_TO_OUTER_EDGE_X_FL MOUNT4POS_TO_OUTER_EDGE_X_FL						//右后角雷达车身外边缘在X轴方向的距离,与左后角对称
#define MOUNT5POS_TO_OUTER_EDGE_Y_FL MOUNT4POS_TO_OUTER_EDGE_Y_FL						//右后角雷达与后轴中心在Y轴方向的距离,与左后角对称


//#define RCTA_DDCI_MIN                       (-6.0f - MOUNTPOS_TO_REAR_AXLE_CENTER_X_RL)     //触发 RCTA 的最小后纵向距离（从后桥测量）RCTADistanceMin

// 功能规范差异项
//RCTAB
#define RCTA_DDCI_MIN                       (-5.0f)                                      //触发 RCTA 的最小后纵向距离（从后桥测量）RCTADistanceMin
#define RCTB_DDCI_MIN                       (-4.0f)                                      //触发RCTB的最小后纵向距离（从后桥测量）
#define RCTA_ACTIVE_MIN_IA                  (40.0f)                                      //目标和目标车辆的轨迹之间的相交角
#define RCTA_ACTIVE_MAX_IA                  (140.0f)                                     //目标和目标车辆的轨迹之间的相交角
#define RCTB_BRAKEINTERVALTIME              (10)                                         //二次刹车间隔时间

//RCW
#define RCW_MIN_SPD_LOWER                   (0.0f)
//FCTAB
#define FCTB_ACTIVE_MIN_SPEED               (4.0f)                                       //FCTB报警支持的最小速度,km/h 状态机使用
#define FCTB_ACTIVE_MAX_SPEED       		(20.0f)                                      //FCTB报警支持的最大速度,km/h 状态机使用（自车）
#define FCTA_ACTIVE_MAX_SPEED       		(20.0f)										 //FCTA最大启动速度
//DOW
#define ADAS_FUNCTION_A3                                                                  //启用此宏定义表示使用A3规范
#define DOW_ACTIVE_MIN_SPEED                (-0.1)
#define DOW_ACTIVE_MAX_SPEED                (2.0)


//#############################################################
// CAN相关配置
//#############################################################

// VCAN
#define BOOT_RADAR_USE_CAN0         0        // 0:CANFD     1:CAN
#define APP_RADAR_USE_CAN0          0        // 0:CANFD     1:CAN

// PCAN
#define APP_RADAR_USE_CAN1			1		 // 0:CANFD_5M	1:CANFD_2M

#define CAN_ID_FILTER_ELEMENT_NUM          19           //实际使用大小+1
#define CAN_ID_FILTER_ELEMENT_NUM1         5

#if DV_TEST_MODE_ENABLE

#define CANBUS_PROTOCOL_VER_USING CANBUS_PROTOCOL_VER_Project_410_QZ      //CAN协议接口
#define DBF_READ_FROM_FLASH	      1                                       //0：采用曲线拟合方式   1：采用从一度一校准方式
#define RADAR_USE_CAN0_FD         0                                       //0:CAN     1:CANFD
#define RADAR_USE_CAN1_FD         0                                       //0:CAN     1:CANFD
#define RADAR_CAN0_NOMI_BAND      CAN_BAUDRATE_500KBPS
#define RADAR_CAN0_DATA_BAND      0                                       //默认0
#define RADAR_CAN1_NOMI_BAND      CAN_BAUDRATE_500KBPS
#define RADAR_CAN1_DATA_BAND      0                                       //默认0

#else

#define CANBUS_PROTOCOL_VER_USING CANBUS_PROTOCOL_VER_CONTI410            //CAN协议接口
// #define DBF_READ_FROM_FLASH	      1                                       //0：采用曲线拟合方式   1：采用从一度一校准方式

#define RADAR_USE_CAN0_FD         1                                       //0:CAN     1:CANFD
#define RADAR_USE_CAN1_FD         1                                       //0:CAN     1:CANFD

#if RADAR_USE_CAN0_FD
#define RADAR_CAN0_NOMI_BAND      CAN_BAUDRATE_500KBPS
#define RADAR_CAN0_DATA_BAND      CAN_BAUDRATE_2MBPS
#else
#define RADAR_CAN0_NOMI_BAND      CAN_BAUDRATE_500KBPS
#define RADAR_CAN0_DATA_BAND      0                                       //默认0
#endif

#if RADAR_USE_CAN1_FD
#define RADAR_CAN1_NOMI_BAND      CAN_BAUDRATE_500KBPS
#define RADAR_CAN1_DATA_BAND      CAN_BAUDRATE_2MBPS
#else
#define RADAR_CAN1_NOMI_BAND      CAN_BAUDRATE_1MBPS
#define RADAR_CAN1_DATA_BAND      0                                       //默认0
#endif

#endif

//#############################################################
// UDS相关配置
//#############################################################

#define DIAGNOSTIC_VARIANT_COMPARE_EN		1			//0:写配置字不跟gVariantCoding数据比较     1(需要将gVariantCoding改成对应车型的配置字):写配置字跟gVariantCoding数据比较

//--------------------------------------------------------
// 车型配置字
//--------------------------------------------------------

// TODO:车型配置字在变量gVariantCoding中增加

//--------------------------------------------------------
// 软件编码
//--------------------------------------------------------
#define UDS_ECU_SW_CODE			        	{0x4A,0x55,0x00,0x11,0x83,0x81,0x00,0x00,0x00}		//4A5500118381000000 

#define UDS_DATASET_SW_NUM					{0x32,0x33,0x00,0x11,0x83,0x70,0x00,0x03,0xFF}
#define UDS_DATASET_SW_VER_NUM				{0x4E,0x21,0x15,0x08,0x0E,0x01}

//--------------------------------------------------------
// App诊断软件版本号和硬件版本号
//--------------------------------------------------------
// #ifdef OS_FREERTOS 
//硬件版本号
//此值表示硬件版本号,范围:0.0.1~2.5.5, 版本每次增量为0.0.1.如果当前版本为: 1.4.5; 则次处的值为145转换车16进制: 0x91
#define DID_HW_VERSION_H        0
#define DID_HW_VERSION_M        1
#define DID_HW_VERSION_L        2
#define DID_HW_VERSION_YEAR     2022    //发布此次硬件版本的年份,比如2019年此处的值是:0x13 (0x13转换成10进制=19，19+2000=2019)
#define DID_HW_VERSION_MONTH    11      //发布此次硬件版本的月份,比如12月此处的值是:0x0C (0x0C转换成10进制=12)
#define DID_HW_VERSION_DATE     26      //发布此次硬件版本的日期,比如28日此处的值是:0x1C (0x1C转换成10进制=28)
#define DID_HW_VERSION_EXT      0       //扩展信息,无扩展信息此值为:0x00

//软件版本号
// 软件版本号，当前第一个版本按照0.0.0
//  -供S1阶段使用:0.00.01~0.99.99，版本每次增量为0.00.01（举例:当前版本为S1阶段的第356次发版本则为0.03.56转换成16进制:0x0164）
//  -供S2阶段使用:1.00.00~1.99.99，
//  -供S3阶段使用:2.00.00~2.99.99，
//  -供P阶使用:3.00.00~3.99.99，
//  -供上市阶段使用:4.00.00~4.99.99
#define DID_SW_VERSION_H        0
#define DID_SW_VERSION_M        0
#define DID_SW_VERSION_L        5		//0.00.01    4.00.00
#define DID_SW_VERSION_YEAR     2024    //发布此次软件版本的年份,比如2019年此处的值是:0x13 (0x13转换成10进制=19，19+2000=2019)
#define DID_SW_VERSION_MONTH    3       //发布此次软件版本的月份,比如12月此处的值是:0x0C (0x0C转换成10进制=12)
#define DID_SW_VERSION_DATE     21      //发布此次软件版本的日期,比如28日此处的值是:0x1C (0x1C转换成10进制=28)
#define DID_SW_VERSION_EXT      0       //本次受控版本与上次受控版本之间调试程序的次数 -此值表示此次软件版本与上次发布版本的调试程序的次数,比如调试了100次，此处的值是:0x64

#define UDS_ECU_HW_VER_NUM			{(DID_HW_VERSION_H*100+DID_HW_VERSION_M*10+DID_HW_VERSION_L),(DID_HW_VERSION_YEAR-2000),DID_HW_VERSION_MONTH,DID_HW_VERSION_DATE,DID_HW_VERSION_EXT}
#define UDS_APP_SW_VER_NUM			{(((DID_SW_VERSION_H*10000+DID_SW_VERSION_M*100+DID_SW_VERSION_L)>>8)&0xFF),((DID_SW_VERSION_H*10000+DID_SW_VERSION_M*100+DID_SW_VERSION_L)&0xFF),(DID_SW_VERSION_YEAR-2000),DID_SW_VERSION_MONTH,DID_SW_VERSION_DATE,DID_SW_VERSION_EXT}

// #else
//--------------------------------------------------------
// BootLoader诊断Boot版本号
//--------------------------------------------------------

//BYD底层软件Bootload版本号
//字节1~3年周次是引导程序开发人员完成此版引导程序时间标记。字节7-9年月日是产品工程师完成特定MCU移植时间标记。
//即年周次是对应引导程序开发完成并且定版发布的时间，也就是它对应的版本信息，年月日是引导程序结合产品应用程序及其功能修改后生成的时间。
#define DID_Boot_VERSION_YEAR_1     		2023    //发布此次硬件版本的年份,比如2019年此处的值是:0x13 (0x13转换成10进制=19，19+2000=2019)
#define DID_Boot_VERSION_WEEK     			41      //发布此次Bootload版本的周数,比如0x30表示48周
#define DID_Boot_VERSION_WEEK_CHANGE_NUM    0       //表示Bootload本周变更次数,比如0x7F表示变更127次
//表示Bootload版本号,范围为:1.0~25.5, 增量为0.1,比如0x0B表示版本为:1.1; 0x79表示版本为:12.1
#define DID_Boot_VERSION_H        			1
#define DID_Boot_VERSION_L        			1
#define DID_Boot_VERSION_EXT      			0       //表示次要信息,次值含义由ZF自定义。如无次要信息次值为:0x00
#define DID_Boot_MCU      					0       //表示MCU型号,次值含义由ZF自定义。(0x00:Infineon Aurix  ...)
#define DID_Boot_VERSION_YEAR     			2023    //发布此次硬件版本的年份,比如2019年此处的值是:0x13 (0x13转换成10进制=19，19+2000=2019)
#define DID_Boot_VERSION_MONTH    			10      //发布此次硬件版本的月份,比如12月此处的值是:0x0C (0x0C转换成10进制=12)
#define DID_Boot_VERSION_DATE     			11      //发布此次硬件版本的日期,比如28日此处的值是:0x1C (0x1C转换成10进制=28)

#define UDS_BOOT_SW_VER_NUM					{(DID_Boot_VERSION_YEAR_1-2000),DID_Boot_VERSION_WEEK,DID_Boot_VERSION_WEEK_CHANGE_NUM,(DID_Boot_VERSION_H*10+DID_Boot_VERSION_L), \
											 DID_Boot_VERSION_EXT,DID_Boot_MCU,(DID_Boot_VERSION_YEAR-2000),DID_Boot_VERSION_MONTH,DID_Boot_VERSION_DATE}

//--------------------------------------------------------
// 承泰内部Boot版本号
//--------------------------------------------------------
// P0：大版本号，按照奇数增加；
// P1：车型版本号，每增加1个车型+1；
// P2，P3：小版本号
// #if (defined __BOOT2__) //暂时默认打开使用，直接赋值给cfg.c作为内部版本号 TODO
//承泰版本号
#define BOOT_VAR_P0	5
#define BOOT_VAR_P1	0
#define BOOT_VAR_P2	0
#define BOOT_VAR_P3	2
// #endif //(defined __BOOT2__)

// #endif // define OS_FREERTOS


#endif
