﻿/**
 * @file adas_alm_rcw.c
 * @brief 
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#include <stdbool.h>
#include <math.h>
#include <string.h>

#ifdef ALPSPRO_ADAS
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "adas/common/linear_regression.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#elif defined (PC_DBG_FW)
#include "app/system_mgr/typedefs.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "other/temp.h"
#else
#include "app/rdp/rdp_clth_radar_lib.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "common/include/vehicle_cfg.h"
#endif

// /* 角度转弧度DEG2RAD = pi/180 */
// #define DEG2RAD 0.017453292519943295f

// /* 弧度转角度：RAD2DEG = 180/pi */
// #define RAD2DEG 57.295779513082323f

/**
 * @brief RCW转弯时的策略，计算RCW纵向长度对应的弧度，
 * 		这里纵向长度为40m，转弯时40m为弧长，
 *		θ（弧度）= s弧长 / r半径
 *		40m固定弧长不变，弧度不变，是个定值
 *      A角度=θ（弧度）*180/π
 */
#define GET_RCW_TURN_RADIAN(turnRadius, almState) ((RCW_LENGTH + almState) / turnRadius)														   // 获得转弯弧度, almState这里两用，0表示第一次报警没有滞回buf，非0表示继续报警和滞回buf
#define GetRcwTurnRadius_Angle(turnRadian) (turnRadian * RAD2DEG)																				   // 获得转弯角度 暂时用不到
#define GetRcwTurnRadius_YMax(turnRadius, almState) (turnRadius * sinf(GET_RCW_TURN_RADIAN(turnRadius, almState)))								   // 获得RCW纵向弧长距离为40m时对应的y值
#define __GET_RCW_TURN_X_MAX(turnRadius, almState) (turnRadius - (turnRadius * cosf(GET_RCW_TURN_RADIAN(turnRadius, almState))))				   // 获得RCW纵向弧长距离为40m时对应的x值
#define GetRcwTurnRadius_Inside_XMax(turnRadius, vehicleWidth, almState) __GET_RCW_TURN_X_MAX((turnRadius - (vehicleWidth / 2)), (almState >> 1))  // 获得RCW纵向弧长距离为40m时对应的x值，内侧
#define GetRcwTurnRadius_Outside_XMax(turnRadius, vehicleWidth, almState) __GET_RCW_TURN_X_MAX((turnRadius + (vehicleWidth / 2)), (almState >> 1)) // 获得RCW纵向弧长距离为40m时对应的x值，外侧

// static inline float absf(float a)
// {
// 	return (a >= 0 ? a : (0 - a));
// }


/**
 * @brief Get the Rcw Turn Radian by turn radius
 *
 * @param turnRadius
 * @return float
 */
// static inline float getRcwTurnRadian(const float turnRadius)
// {
// 	return (RCW_LENGTH / turnRadius);
// }
/**
 * @brief Get the Rcw Turn Angle by turn radian
 *
 * @param turnRadian
 * @return float
 */
// static inline float getRcwTurnAngle(const float turnRadian)
// {
// 	return (turnRadian * RAD2DEG);
// }
/**
 * @brief Get the Rcw Turn Y Max by radius
 *
 * @param turnRadius
 * @return float
 */
// static inline float getRcwTurnYMax(const float turnRadius)
// {
// 	return (turnRadius * sinf(getRcwTurnRadian(turnRadius)));
// }
/**
 * @brief Get the Rcw Turn X Max by radius
 *
 * @param turnRadius
 * @return float
 */
// static inline float getRcwTurnXMax(const float turnRadius)
// {
// 	return (turnRadius - turnRadius * cosf(getRcwTurnRadian(turnRadius)));
// }
/**
 * @brief Get the Rcw Inside Turn X Max object
 *
 * @param turnRadius
 * @param vehicleWidth
 * @return float
 */
// static inline float getRcwInsideTurnXMax(const float turnRadius, const float vehicleWidth)
// {
// 	float insideTurnRadius = turnRadius - vehicleWidth / 2;
// 	float retXMax = getRcwTurnXMax(insideTurnRadius);

// 	return retXMax;
// }
/**
 * @brief Get the Rcw Outside Turn X Max object
 *
 * @param turnRadius
 * @param vehicleWidth
 * @return float
 */
// static inline float getRcwOutsideTurnXMax(const float turnRadius, const float vehicleWidth)
// {
// 	float outsideTurnRadius = turnRadius + vehicleWidth / 2;
// 	float retXMax = getRcwTurnXMax(outsideTurnRadius);

// 	return retXMax;
// }
/**
 * @brief 获得目标车的坐标与原点的距离 Get the Rcw Turn Target Range object
 *
 * @param x
 * @param y
 * @param turnRadius
 * @return float
 */
// static float getRcwTurnTargetRange(const float x, const float y, const float turnRadius)
// {
// 	float tmpX = x;
// 	float tmpY = y;
// 	float tmpTurnRadius = turnRadius;
// 	float range = 0.0;

// 	range = tmpY * tmpY + (tmpTurnRadius - tmpX) * (tmpTurnRadius - tmpX);
// 	range = sqrt(range);

// 	return range;
// }

/**
 * @brief 此跟踪点的可信度判断
 *
 * @param i 跟踪ID
 * @param pobjPath 目标相关结构体地址
 * @return bool 1：可信，0：不可信
 */
static bool ADAS_RCW_checkTrkObjReliability(uint8_t i, const OBJ_NODE_STRUCT *pobjPath)
{
    bool flag = false;
    uint8_t reliability_max = 0U;

	reliability_max = RCW_RELIABILITYMININRADARFOV;

    if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW))
    {
        reliability_max += RCW_RELIABILITYBUF;
    }

    if(pobjPath[i].TrkObjReliability >= reliability_max)
    {
        flag = true;
    }

    return flag;
}

/**
 * @brief 置信度策略
 * 		检查目标的质量，主要是过滤假点及静态点，与功能规范无关
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return true 有效
 * @return false 无效
 */
static bool ADAS_RCW_checkTargetQuality(ALARM_OBJECT_T *pobjAlm, OBJ_NODE_STRUCT *pobjPath, const uint8_t i)
{
	bool flag = true; // 默认目标有效

	if(pobjPath[i].objFakeDOt == true)
	{
		return false;   //目标无效
	}

    if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW) == false)   //对未产生报警的目标严格限制
    {
        //对非动态点或前方来的目标过滤
        if (((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) == 0U) || (pobjPath[i].maxY <1.0f) == 1U)
        {
            return false;   //目标无效
        }

        //如果是丢失3帧的目标，则抑制报警,目的是防止正后方来车时误报和DOW斜停误报
        if((pobjPath[i].TrkObjMissCnt > 2U) != 0U)
        {
            return false;   //目标无效
        }

        // 生命周期小于5帧的目标或者生命周期在5帧以上，10帧以内的置信度小于70的目标，抑制报警,目的是抑制斜停误报
        if(pobjPath[i].lifeCycle < 20U)
        {
            return false;   //目标无效
        }
    }

    if(ADAS_RCW_checkTrkObjReliability(i, pobjPath) == false)   //目标置信度低
    {
        if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW) == false) //上次不在报警
        {

        }
        else
        {
            ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW);
        }

        return false;
    }

	// 一定时间内不重复报警
	if (pobjAlm->rcwntervaltime < RCW_WARNING_INTERVAL)
	{
		return false;
	}

	return flag;
}

/**
 * @brief 重叠率判断
 * 		确认目标的重叠率符合要求，只有在稳定跟踪范围内的点才能通过重叠率，近处的跟踪点会往本车外侧窜
 *
 * @param pobjPath 目标相关结构体地址
 * @param i 跟踪ID
 * @return true 满足
 * @return false 不满足
 */
static bool ADAS_RCW_checkOverlap(const OBJ_NODE_STRUCT *pobjPath, const uint8_t i, float BSDVelSpeedVal, uint8_t movedobjnum )
{
    int k = 0;
    int iCnt = 0;
    int ipassiveCnt = 0;
    bool flag = false; // 目标默认不满足
	float sum = 0.0f;

    // 在车后方的目标，1m以上报警时，如果简单的覆盖率大于最大重叠率，则抑制报警,目的是防止正后方来车时误报 
    if (((pobjPath[i].coverRate >= (RCW_OBJ_OVERLAP_RANGE_RATE_HIGH_THRESHOLD + RCW_OBJ_OVERLAP_RANGE_RATE_HIGH_THRESHOLD_BUFF)) || //
            (pobjPath[i].coverRate <= (RCW_OBJ_OVERLAP_RANGE_RATE_LOW_THRESHOLD + RCW_OBJ_OVERLAP_RANGE_RATE_LOW_THRESHOLD_BUFF))) //
            /* || (pobjPath[i].x < (-(VEHICLE_WIDTH_INFO + RCW_OBJ_X_NEGATIVE_DIRT_MIN_BUFF))) */
    )
    {
        //flag = false;
    }

    // 覆盖率在某个范围  过去一秒内的又由满足某些位置关系 则认为重叠率满足, 主要是首先满足 100%重叠率
    if ((pobjPath[i].y < RCW_BEHIND_ACTIVE_JUDGE_Y_MAX) && (pobjPath[i].y > RCW_BEHIND_ACTIVE_JUDGE_Y_MIN))
    {
        for (k = 0; k < ADAS_HISTORY_NUM; k++)
        {
            if ((pobjPath[i].stored_last_x[k] < (RCW_BEHIND_ACTIVE_JUDGE_X_LEFT)) && (pobjPath[i].stored_last_x[k] > (RCW_BEHIND_ACTIVE_JUDGE_Y_RIGHT)))
            {
                iCnt++;
            }
        }
        if (iCnt >= (ADAS_HISTORY_NUM - 5))
        {
            if ((pobjPath[i].x <= (-0.4)) && (pobjPath[i].x >= (-1.2)) && (fabsf(pobjPath[i].vx) < RCW_BEHIND_ACTIVE_VX_LIMIT))
            {
                flag = true;
            }
        }
    }
	// 高重叠率的触发场景
    if ((pobjPath[i].y < RCW_BEHIND_ACTIVE_JUDGE_Y_MAX) && (pobjPath[i].y > RCW_BEHIND_ACTIVE_JUDGE_Y_MIN))
    {
		iCnt = 0;
        for (k = 0; k < ADAS_HISTORY_NUM; k++)
        {
            if ((pobjPath[i].stored_last_x[k] < (RCW_OVRELAP_M_ACTIVE_JUDGE_X_LEFT)) && (pobjPath[i].stored_last_x[k] > (RCW_OVRELAP_H_ACTIVE_JUDGE_Y_RIGHT)))
            {
                iCnt++;
            }
        }
        if (iCnt >= (ADAS_HISTORY_NUM - 5))
        {
			if ((pobjPath[i].x <= (0.1f)) && ((pobjPath[i].boxDot1X <= (0.0f)) && (pobjPath[i].boxDot2X >= (0.0f))) && 
				((fabsf(pobjPath[i].boxDot2X) <= (fabsf(pobjPath[i].boxDot1X)))))
            {
                flag = true;
            }
        }
    }	

    // 从侧面穿过的目标不触发. 限制小重叠率及侧面报警
    if ((pobjPath[i].y < RCW_BEHIND_PASSIVE_JUDGE_Y_MAX) && (pobjPath[i].y > RCW_BEHIND_PASSIVE_JUDGE_Y_MIN))
    {
        for (k = 0; k < ADAS_HISTORY_NUM; k++)
        {
            if ((pobjPath[i].stored_last_x[k] > 0.1) || (pobjPath[i].stored_last_x[k] < (-(VEHICLE_WIDTH_INFO + 0.1))))
            {
                ipassiveCnt++;
            }
        }
        if (ipassiveCnt >= (ADAS_HISTORY_NUM - 5))
        {
            if ((pobjPath[i].x > 0.15) || (pobjPath[i].x < (-(VEHICLE_WIDTH_INFO + 0.15))))
            {
                flag = false;
            }
        }
    }
	// 航迹框限制RCW误报, 在一定范围内抑制之后, 近处直接抑制 确保较高重叠率的报警
    if ((pobjPath[i].y < RCW_BEHIND_PASSIVE_JUDGE_Y_MAX) && (pobjPath[i].y > RCW_BEHIND_PASSIVE_JUDGE_Y_MIN) && (pobjPath[i].vy >= (RCW_START_SPEED / 3.6f)))
    {
        if ((pobjPath[i].x >= (-0.1)) && ((pobjPath[i].boxDot1X <= (0.0f)) && (pobjPath[i].boxDot2X >= (0.0f))) && 
            ((fabsf(pobjPath[i].boxDot2X) >= (fabsf(pobjPath[i].boxDot1X)))))
        {
            flag = false;
        }
        if ((pobjPath[i].x >= (-0.2)) && ((pobjPath[i].boxDot1X >= (0.0f)) && (pobjPath[i].boxDot2X >= (0.0f))))
        {
            flag = false;
        }
    }
    // 低速场景, 纵向距离1米时还未报警, 抑制其报警.
	if ((BSDVelSpeedVal < 1.0f) && (!ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW)) && (pobjPath[i].y < RCW_BEHIND_PASSIVE_JUDGE_Y_MIN))
    {
        flag = false;
    }

	// 横向远距离误报, 针对直行场景强制限制X距离
	if (((pobjPath[i].x > (0.5f)) || (pobjPath[i].x < (-VEHICLE_WIDTH_INFO))) && (!ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW))){
		flag = false;
	}

    // 低重叠率目标不触发
    if ((pobjPath[i].vy > (RCW_START_SPEED / 3.6f)) && (pobjPath[i].x < 1.0f) && (!ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW)))
    {
        iCnt = 0;
        for (k = 0; k < ADAS_HISTORY_NUM; k++)
        {
            if (pobjPath[i].stored_last_x[k] > 0)
            {
                iCnt++;
            }
			sum += pobjPath[i].stored_last_x[k];
        }
		// 跟踪点都在车外,且最近20帧总和达到一定值, 认定为低重叠率。
        if ((iCnt >= (ADAS_HISTORY_NUM - 1)) && (sum >= 2.0f))
        {
            flag = false;
        }
    }

	// 较远距离时， 还没开始计算重叠率, 此时X大于0就认为不满足 尽可能稍微严格一些
	if ((pobjPath[i].y > RCW_BEHIND_PASSIVE_JUDGE_Y_MAX) && (pobjPath[i].x > 0))
	{
		flag = false;
	}


	// 高重叠率的触发场景
	// 使用原始点点云辅助判断, 尽量确保高重叠率场景能够正常报警 主要处理 75%以上的场景正常触发
    if ((pobjPath[i].y < RCW_BEHIND_ACTIVE_JUDGE_Y_MAX) && (pobjPath[i].y > RCW_BEHIND_ACTIVE_JUDGE_Y_MIN))
    {
		iCnt = 0;
        for (k = 0; k < ADAS_HISTORY_NUM; k++)
        {
			// 横向位置放宽一些, 之后通过原始点云分布较为严格的判断
            if ((pobjPath[i].stored_last_x[k] < (RCW_OVRELAP_H_ACTIVE_JUDGE_X_LEFT)) && (pobjPath[i].stored_last_x[k] > (RCW_OVRELAP_H_ACTIVE_JUDGE_Y_RIGHT)))
            {
                iCnt++;
            }
        }
        if (iCnt >= (ADAS_HISTORY_NUM - 5))
        {
			// 航迹框可能不准确, 使用足够数量的点云分布统计
			// 点云分布靠近内侧, 而且累计够一定计算帧数. 累计一定帧数能一定程度上避免斜穿误报 先暂定15帧.
			// 这里要特别关注是否会导致稍微斜停的后方目标误报
			if ((pobjPath[i].x <= (RCW_OVRELAP_H_ACTIVE_JUDGE_X_LEFT)) && ((pobjPath[i].boxDot1X <= (0.0f)) && (pobjPath[i].boxDot2X >= (0.0f))) && 
				(pobjPath[i].TrkObjRcwOverLapx < (-0.15f)) && (pobjPath[i].TrkObjOverLapxCnt >= 10))
            {
                flag = true;
            }
        }
    }

	// 对于重叠率场景, 使用点云分布 
	// 一般高重叠率都是正常报警,  低重叠率可能误报, 使用点云分布 是否考虑异侧场景?
	// 理论情况下  50%重叠率时  TrkObjRcwOverLapx 值为0 向内为负  向外为正
	// TrkObjRcwOverLapx的默认赋值相当于雷达到车身边缘的offset 此值为负  所以这种赋值方式的话 TrkObjRcwOverLapx只能用于已报警目标的抑制报警
	// 不能用于判断雷达雷达需要报警的场景(如需判断则要更改 TrkObjRcwOverLapx 的默认赋值方式) 
	if ((true == flag) && ((pobjPath[i].TrkObjRcwOverLapx > 0.01f) /* || (pobjPath[i].TrkObjRcwOverLapx < -0.8f))*/ ))
	{
		flag = false;
	}

	// 自车运动变道类型的场景报警, 设定一个小区间, 在这个区间内确认是正后方    就满足重叠率
	if ((BSDVelSpeedVal > 5.0f) && ((pobjPath[i].y < RCW_BEHIND_ACTIVE_JUDGE_MOVE_Y_MAX) && (pobjPath[i].y > RCW_BEHIND_ACTIVE_JUDGE_Y_MIN)))
	{
		iCnt = 0;
        for (k = 0; k < (ADAS_HISTORY_NUM / 2); k++)
        {
            if ((pobjPath[i].stored_last_x[k] < (RCW_OVRELAP_M_ACTIVE_JUDGE_X_LEFT)) && (pobjPath[i].stored_last_x[k] > (RCW_OVRELAP_H_ACTIVE_JUDGE_Y_RIGHT)))
            {
                iCnt++;
            }
        }
        if (iCnt >= ((ADAS_HISTORY_NUM / 2) - 5))		// 有一满足在后方
        {
			if ((pobjPath[i].x <= (RCW_OVRELAP_M_ACTIVE_JUDGE_X_LEFT)) && ((pobjPath[i].boxDot1X <= (0.0f)) && (pobjPath[i].boxDot2X >= (0.0f))) && 
				((fabsf(pobjPath[i].boxDot2X) <= (fabsf(pobjPath[i].boxDot1X)))))
            {
                flag = true;
            }
			// 切入后高重叠率
			if ((pobjPath[i].x <= (-0.4)) && (pobjPath[i].x >= (-1.2)) && (fabsf(pobjPath[i].vx) < RCW_BEHIND_ACTIVE_VX_LIMIT))
			{
				flag = true;
			}
        }		
	}

	// 自车小角度斜停,  目标斜穿测试DOW时, 可能会误触发RCW 特征是到正后方时, 由于航迹框移动策略, 可能导致在正后方停留时间过长
	// 比如5号雷达  目标在远处时在自车右侧 到近处时到自车后方. 由于正后方的航迹框移动策略, 导致在正后方停留时间过长, 满足重叠率 触发RCW
	// 如何识别斜穿是抑制的重点, 斜穿目标到正后方时不改变斜穿趋势
	// 自车静止时,多帧高横向速度目标, 不满足重叠率
	// 此策略放在较为靠后位置,  用于前面误报了报警的目标, 在此处解除报警.
	if ((BSDVelSpeedVal < FLOAT_EPS) && (fabsf(pobjPath[i].maxX) > 3.0f))
	{
		iCnt = 0;
        for (k = 0; k < ADAS_HISTORY_NUM; k++)
        {
			if ((pobjPath[i].stored_last_vx[k] < (-1.0f)) || (pobjPath[i].stored_last_vx[k] > 1.0f))
			{
				iCnt++;
			}
        }
		// 目标多帧横向速度都比较高  认为是不符合RCW触发的目标, 需考虑目标切入类场景的特征是否会被抑制掉
        if (iCnt >= (ADAS_HISTORY_NUM - 3))
        {
            flag = false;
        }		
	}

	// 繁华路段场景更加严格的限制 防止误报 限制静止场景
	if ((movedobjnum >= 5)){
		if ((BSDVelSpeedVal < 1.0f) && (pobjPath[i].x > (-0.2f) || (fabsf(pobjPath[i].vx) > (0.8f))) && (!ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW))){
			flag = false;
		}
	}

	return flag;
}


/**
 * @brief 策略 暂时没用
 * 		目标车是否在弧长组成的扇形区域内标志 Get the Rcw Turn Target Range Flag object
 *
 * @param turnRadius 转弯半径
 * @param pobjPath 目标相关结构体地址
 * @param i
 * @image  D1:Dot1,距离原点（雷达）最近的点，这个在4个雷达中都是如此

		   D3  D4  |   |   D4  D3
		   +---+   |   |   +---+
		   |obj|   |   |   |obj|
		   +---+   |   |   +---+
		   D2  D1  |   |   D1  D2
		   --6-----+---+-----7---
				   | V |
		   --4-----+---+-----5---
		   D2  D1  |   |   D1  D2
		   +---+   |   |   +---+
		   |obj|   |   |   |obj|
		   +---+   |   |   +---+
		   D3  D4          D4  D3

 * @return uint8_t
 */
// static bool ADAS_RCW_isTargetWithinTurningRCWAlarmZone(const float turnRadius, const OBJ_NODE_STRUCT *pobjPath, const uint8_t i)
// {
// 	bool rangeFlag = false;

// 	float tmpTurnRadius = turnRadius; // 本车转弯半径

// 	if ((pobjPath[i].boxWidth > 0.5) && (pobjPath[i].rcDis <= 1300)) // 航迹框大于0.5使用此策略，0.4为默认值
// 	{
// 		float targetY = pobjPath[i].y;
// 		// 用目标框的内侧点（转弯方向为内侧，不分雷达ID）Dot2的X，聚类的点的Y
// 		float targetX = pobjPath[i].boxDot2X;
// 		// 获得目标车内侧距离转弯半径原点的距离，
// 		float rangeInside = getRcwTurnTargetRange(targetX, targetY, GetRcwTurnRadius_Inside_XMax(turnRadius, VEHICLE_WIDTH_INFO, (uint8_t)(pobjPath[i].lastAlarmType == ALARM_ACTIVE_RCW)));

// 		// 用目标框的外侧点（转弯方向为内侧，不分雷达ID）Dot1的X，聚类的点的Y
// 		targetX = pobjPath[i].boxDot1X;
// 		// 获得目标车外侧距离转弯半径原点的距离
// 		float rangeOutside = getRcwTurnTargetRange(targetX, targetY, GetRcwTurnRadius_Outside_XMax(turnRadius, VEHICLE_WIDTH_INFO, (uint8_t)(pobjPath[i].lastAlarmType == ALARM_ACTIVE_RCW)));

// 		/**
// 		 * @brief 目标车的框的D1和D2点的坐标，到本车转弯半径的原点(0, 0)的距离为TV_R1和TV_R2，本车转
// 		 * 	弯半径为SV_R，如果本车和目标车在一个车道内，那SV_R应该是在TV_R1和TV_R2之间，
// 		 * 	暂时没有考虑重合率 ，重合率可以用tmpTurnRadius在[rangeInside, rangeOutside]之间的占比
// 		 */
// 		if ((tmpTurnRadius > rangeInside) && (tmpTurnRadius < rangeOutside))
// 		{
// 			rangeFlag = true;
// 		}
// 	}
// 	else
// 	{
// 		float targetInTurnRadius = fabsf(pobjPath[i].rcDis - (VEHICLE_WIDTH_INFO / 2));
// 		float targetOutTurnRadius = fabsf(pobjPath[i].rcDis + (VEHICLE_WIDTH_INFO / 2));
// 		float inBuff = -0.4;

// 		/**
// 		 * @brief 目标车的框的D1和D2点的坐标，到本车转弯半径的原点(0, 0)的距离为TV_R1和TV_R2，本车转
// 		 * 	弯半径为SV_R，如果本车和目标车在一个车道内，那SV_R应该是在TV_R1和TV_R2之间，
// 		 * 	暂时没有考虑重合率 ，重合率可以用tmpTurnRadius在[rangeInside, rangeOutside]之间的占比
// 		 */
// 		if ((tmpTurnRadius >= targetInTurnRadius + inBuff) && (tmpTurnRadius <= targetOutTurnRadius))
// 		{
// 			rangeFlag = true;
// 		}
// 	}

// 	return rangeFlag;
// }

/**
 * @brief 判断目标是否在RCW报警纵向区域内
 *
 * @param turnRadius
 * @param pobjPath
 * @return true
 * @return false
 */
static bool ADAS_RCW_isLongitudinalDistanceInRCWAlarmZone(const float turnRadius, const OBJ_NODE_STRUCT *pobjPath, const uint8_t i, const float range_buf)
{
	uint8_t longitudinalFlag = false; // 纵向距离标志
	float targetY = pobjPath[i].y;
	float maxLongitudinalY = GetRcwTurnRadius_YMax(turnRadius, (uint8_t)(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW)));
	maxLongitudinalY = maxLongitudinalY + range_buf; // 增加一个buffer

	/**
	 * @brief 目标车的聚类的点的y值理论上应该在D1和D2点之间，所以只需要判断y是否在[RCW_RANGE_MIN, 40]，或者转弯时[RCW_RANGE_MIN, maxY]
	 * 	还没有考虑重合率
	 */
	if (((targetY > RCW_RANGE_MIN) && (targetY < maxLongitudinalY)) && // 目标y的距离是否在报警区域内
		(pobjPath[i].maxY > 1.0)									   // 目标生命周期内的最远y轴距离大于1.0m
	)
	{
		longitudinalFlag = true;
	}

	return longitudinalFlag;
}

/**
 * @brief
 * @param pobjAlm 报警结构体地址
 * @param pobjPath 道路结构体地址
 * @param i
 * @return true
 * @return false
 */ 
bool ADAS_RCW_runMain(ALARM_OBJECT_T *pobjAlm,
					  OBJ_NODE_STRUCT *pobjPath,
					  uint8_t i,
					  const VDY_Info_t *pVDY)
{
	float centerx = pobjAlm->centerx;
	float BSDVelSpeedVal = pobjAlm->BSDVelSpeedVal;
	uint8_t movedobjnum =  pobjAlm->movedobjnum;

	bool alarmFlag = false;

	if (!ADAS_RCW_checkTargetQuality(pobjAlm, pobjPath, i ))
	{
		return false;
	}

	if ((pobjPath[i].vy * 3.6) > RCW_START_SPEED)
	{
		float ttc_buf = 0.0;
		//float spd_buf = 0.0;
		float range_buf = 0.0;
		//float width_buf = 0.0;
		int pathCollision = 0;

		if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW))
		{
			if ((BSDVelSpeedVal >= RCW_MIN_SPD_LOWER) && (BSDVelSpeedVal <= RCW_MAX_SPD_UPPER))
			{
				//spd_buf = RCW_OWN_HOLDUP_SPEED;
				range_buf = 3.0;
			}

			if ((pobjPath[i].vy * 3.6) <= 10.0) // 根据车速增加相应的buff
			{
				ttc_buf = 0.3;
				//width_buf = 0.4;
			}
			else if ((pobjPath[i].vy * 3.6) <= 20.0)
			{
				ttc_buf = 0.3;
				//width_buf = 0.5;
			}
			else if ((pobjPath[i].vy * 3.6) <= 30.0)
			{
				ttc_buf = 0.3;
				//width_buf = 0.5;
			}
			else // pobjPath[i].vy * 3.6 > 30km/h
			{
				ttc_buf = 0.3;
				//width_buf = 0.6;
			}
		}

		float rcw_ttc_max = ((pobjPath[i].vy * 3.6) < 30.0) ? (RCW_TTC_MAX * pobjPath[i].vy * 3.6 / 30.0) : RCW_TTC_MAX;

		// 重叠率场景下  可能会导致误报或漏报, 越靠近时, 重叠率相对越稳定
		// 抑制低重叠率误报, 60%以下重叠时,压TTC 避免一开始重叠较高,之后重叠率低时,整体重叠率还没下来导致误报
		if ((pobjPath[i].TrkObjRcwOverLapx > (-0.15f)) && (pobjPath[i].TrkObjOverLapxCnt >= 10))
		{
			if ((pobjPath[i].vy * 3.6) <= 10.0)
			{
				rcw_ttc_max = rcw_ttc_max + RCW_TTC_DELTA_10;
			}
			else if ((pobjPath[i].vy * 3.6) <= 20.0)
			{
				rcw_ttc_max = rcw_ttc_max - RCW_TTC_DELTA_20;
			}
			else if ((pobjPath[i].vy * 3.6) <= 30.0)
			{
				rcw_ttc_max = rcw_ttc_max - RCW_TTC_DELTA_30;
			}
			else // pobjPath[i].vy * 3.6 > 30km/h
			{
				rcw_ttc_max = rcw_ttc_max - RCW_TTC_DELTA_40;
			}
		}

		/* 目标车是否在纵向范围内（弧长组成的扇形区域内）标志 */
		bool overlapFlag = false; // ture表示在范围内
		overlapFlag = ADAS_RCW_checkOverlap(pobjPath, i, BSDVelSpeedVal, movedobjnum);

		/* 目标车是否在纵向范围内（弧长组成的扇形区域内）标志 */
		bool longitudinaFlag = false; // ture表示在范围内
		longitudinaFlag = ADAS_RCW_isLongitudinalDistanceInRCWAlarmZone(centerx, pobjPath, i, range_buf);

        float rcw_ia_threshold;

        if (BSDVelSpeedVal < (0.1f)){
            rcw_ia_threshold = RCW_ACTIVE_MAX_IA;
        }else{
            rcw_ia_threshold = RCW_ACTIVE_MAX_IA + 5;
        }

        if(ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW))   //上一帧在报警，则对区域和夹角增加缓冲
        {
            rcw_ia_threshold += RCW_ACTIVE_MAX_IA_BUF;
        }

        if(
        //( pobjPath[i].lockx >= RCW_LOCK_X_MAX ) && //锁定位置在本雷达的主要探测区
        ((fabsf(pobjPath[i].headingAnglerealRCWDOW) <= rcw_ia_threshold))&&		// 自车静止时, 判断航向角
		(pobjPath[i].targetclass >= RCW_OBJ_MIN_SIZE) && // 最小目标尺寸
        (pobjPath[i].vy > 0.1) &&								   // 至少是来向目标，来向为正
        (overlapFlag != false) &&	  // 判断目标是否在扇形区域报警范围内
        (longitudinaFlag != false) && // 判断目标是否在纵向报警范围内
        (pobjPath[i].x <= (10)) && // 防止大转弯时候出现在边上的目标触发的报警
        (fabs(pVDY->pVDY_DynamicInfo->vdySteeringAngle) < RCW_MAX_STEERINGANGLE) //
		)
		{
			/**
			 * @brief  碰撞预测
			 *
			 */
			if (fabsf(pobjPath[i].vx) > 0.8) // 横向速度0.3不是很准确
			{
				// 有横向速度，看横向逃离碰撞区前，y轴是否已经碰上
				float tx = 0.0; // 横向逃逸碰撞区所需时间,,横向逃逸碰撞区所需时间,,横向逃逸碰撞区所需时间
				float ty = 0.0;
				pobjPath[i].startrcwAlarmDly = 0;
				pobjPath[i].overAlarmDly  = 0U; 
				ADAS_setBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW);
				pobjPath[i].alarmDlyThr   = RCW_START_ALARM_DELAY;

				ty = (pobjPath[i].y - MOUNT4POS_TO_OUTER_EDGE_Y_FL) / pobjPath[i].vy; // 纵向到达碰撞区所需时间, MOUNT4POS_TO_OUTER_EDGE_Y_FL是左后角雷达到车身外边缘在Y轴方向的距离,D10

				if (pobjPath[i].vx > 0.0) // 表示x来向
				{
					tx = (pobjPath[i].x + (VEHICLE_WIDTH_INFO + 0.5)) / pobjPath[i].vx; // VEHICLE_WIDTH_INFO,自车宽度 + 冗余
				}
				else
				{
					tx = (pobjPath[i].x - 0.5) / pobjPath[i].vx; // pobjPath[i].x已经包含车宽，0.5为冗余
				}

				if (ty <= (tx + (ttc_buf / 2.0))) // tx大于ty，表示横向逃逸时间大于纵向碰撞时间，逃不掉
				{
					if (ty <= (rcw_ttc_max + ttc_buf))
					{
						pathCollision = 1; //
					}
				}
			}
			else // 无横向速度，计算y轴是否碰撞即可
			{
				float ty = 0.0;
				ty = (pobjPath[i].y - 0.2) / pobjPath[i].vy; // 纵向到达碰撞区所需时间

				if (ty <= (rcw_ttc_max + ttc_buf))
				{
					pathCollision = 1;
				}
			}
		}

		/**
		 * @brief 为1表示有碰撞风险
		 *
		 */
		if (pathCollision == 1)
		{
			if (!ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW)) // 状态初始化
			{
				pobjPath[i].startrcwAlarmDly = 0;
				pobjPath[i].overAlarmDly = 0;
				ADAS_setBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW);
				pobjPath[i].alarmDlyThr = RCW_START_ALARM_DELAY;

				// 相对速度<20km/h的且在纵向距离5m内目标根据lifecycle进行判断，减小误报
				if ((pobjPath[i].y < 5) && ((pobjPath[i].vy * 3.6) <= 20.0))
				{
					if (pobjPath[i].lifeCycle >= 40)
					{
						pobjPath[i].alarmDlyThr += 0;
					}
					else if (pobjPath[i].lifeCycle >= 20)
					{
						pobjPath[i].alarmDlyThr += 1;
					}
					else
					{
						pobjPath[i].alarmDlyThr += 2;
					}
				}

				// 滤除目标异常点
				float rcw_ttc_cal = 0;								  // ttc 计算值
				rcw_ttc_cal = (pobjPath[i].y - 0.2) / pobjPath[i].vy; // 纵向到达碰撞区所需时间
				if (((rcw_ttc_max - rcw_ttc_cal) > 0.2) && (pobjPath[i].lifeCycle <= 50) && (pobjPath[i].y <= 5))
				{
					pobjPath[i].alarmDlyThr += 40;
				}

				if ((pobjPath[i].vy * 3.6) <= 10.0) // 根据相对速度，确定延迟次数，减少漏报的情况
				{
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 20) ? 5 : 0;
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 10) ? 6 : 0;
				}
				else if ((pobjPath[i].vy * 3.6) <= 20.0)
				{
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 20) ? 4 : 0;
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 10) ? 5 : 0;
				}
				else if ((pobjPath[i].vy * 3.6) <= 30.0)
				{
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 20) ? 3 : 0;
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 10) ? 4 : 0;
				}
				else if ((pobjPath[i].vy * 3.6) <= 40.0)
				{
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 20) ? 2 : 0;
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 10) ? 3 : 0;
				}
				else // 相对速度>40km/h
				{
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 20) ? 2 : 0;
					pobjPath[i].alarmDlyThr += (pobjPath[i].lifeCycle < 10) ? 2 : 0;
				}

				// pobjPath[i].alarmDlyThr  += (pobjPath[i].lifeCycle < 20) ? 5 : 0;
				// pobjPath[i].alarmDlyThr  += (pobjPath[i].lifeCycle < 10) ? 8 : 0;

				if (pobjPath[i].x > 0) // 边界模糊区域多次判断
				{
					pobjPath[i].alarmDlyThr += 2;
				}
			}

				alarmFlag = true;

				pobjPath[i].overAlarmDly = RCW_OVER_ALARM_DELAY;
				ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_RCW);
			// if (pobjPath[i].startrcwAlarmDly >= pobjPath[i].alarmDlyThr) // 报警延迟
			// {
			// 	alarmFlag = true;

			// 	pobjPath[i].overAlarmDly = RCW_OVER_ALARM_DELAY;
			// 	ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_RCW);
			// }
			// else
			// {
			// 	pobjPath[i].startrcwAlarmDly++;
			// }
		}
		else
		{
			if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW))
			{
				if (pobjPath[i].startrcwAlarmDly >= pobjPath[i].alarmDlyThr)
				{
                    if (pobjPath[i].overAlarmDly == 0)  //已经产生了报警，则执行报警退出延迟
					{ 
						ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW);
					}
					else
					{
						alarmFlag = true;

						pobjPath[i].overAlarmDly--;

						ADAS_doWarning(i, pobjPath, ALARM_ACTIVE_RCW);
					}
				}
				else
				{ 
					ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW);
				}
			}
		}
	}
	else
	{ 
		if (ADAS_checkBit(pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW))
		{ 
			ADAS_clearBit(&pobjPath[i].lastAlarmType, ALARM_ACTIVE_RCW);
		}
	}

	return alarmFlag;
}
