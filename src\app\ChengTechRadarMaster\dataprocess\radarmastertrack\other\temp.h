﻿#ifndef TEMP_H
#define TEMP_H

#include "hal/rsp/rsp_types.h"
#include "app/vehicle/vdy/vdy_types.h"
// #include "app/hmi_types.h"
#include "alg/track/rdp_types.h"
#include "app/adas/customizedrequirements/adas.h"
// #include "other/temp.h"
#include "app/include/sharedDef.h"

#define DATA_HIL_DET_TRK_NONE   0x0

#define RADAR_ID_REAR_LEFT      4
#define RADAR_ID_REAR_RIGHT     5
#define RADAR_ID_FRONT_LEFT     6
#define RADAR_ID_FRONT_RIGHT    7

#define MRR_RADAR               1
#define RADAR_TYPE              MRR_RADAR //Boot中区分前、角雷达

#define CANBUS_PROTOCOL_VER_DEFAULT        0
#define CANBUS_PROTOCOL_VER_Project_410_QZ 1 //清智LRR 410项目协议版本 //为升科协议
#define CANBUS_PROTOCOL_VER_HW_MDC         2 //华为MDC
#define CANBUS_PROTOCOL_VER_CONTI          3 //大陆协议
#define CANBUS_PROTOCOL_VER_CONTI410       4 //大陆410协议
#define CANBUS_PROTOCOL_VER_BoschMrr       5 //博世协议
#define CANBUS_PROTOCOL_VER_BYDGAOJIEMrr   6 //比亚迪高阶协议
//#define CANBUS_PROTOCOL_VER_USING CANBUS_PROTOCOL_VER_CONTI410  //CAN协议接口

#define BYD_ORIN_OTGVEL_PLATFORM        

#define MAX_TRK_NUM             128				//最大跟踪点个数
#define MAX_CDI_NUM             256				//最大参与跟踪点个数
#define MAX_GROUP_NUM           MAX_CDI_NUM        //dbscan最大组数

extern uint32_t gFrameNb;
extern uint32_t gFrameTrackNb;
extern uint8_t gIsTurning;

// extern   float gxoffset, gyoffset, groadlineoffset;
extern float gxoffset;
extern float gyoffset;
extern float groadlineoffset;
RSP_DetObjectList_t *updateDetObjListData(float globalNoise);

const RSP_DetObjectList_t *RSP_getDetObjListPointer();

VDY_StaticState_t *VDY_getVehStaticDataPointer();

const VDY_ChassisInfo_t* VDY_getChassisData();

void Radar_initCfg(void);
uint8_t APAR_getAparRadarId();
uint8_t APAR_setAparRadarId(uint8_t ID);
uint8_t CFG_getRadarId(void);
float CFG_getRadarInstallAngle(void);

RDP_TrkObjectList_t *RDP_getTrkObjectListSimulatorPointer();
RDP_TrkObjectList_t *updateTrackObjListData();

/**
 * @brief 获取水平安装角度
 * @return float 返回的水平安装角度，单位为deg
 *
 */
float APAR_getAparInstallAzimuthAngle();

const RDP_TrkObjectList_t *getSimulator();
int32_t initVDYinfo(void);

void Simulator(float times);
RDP_TrkObjectList_t* RDP_getCustomizeObjListData(float times);
uint8_t DBG_getHILDataDbgMode(void);

/**
 * @brief VDY模块向外部模块提供的获取车辆动态数据冻结后的接口。
 *
 * 其他模块调用此函数后，会获取到车辆动态数据冻结后的参数，包括 @c VDY_DynamicEstimate_t 结构体中的参数。
 *
 * @param [in] None
 * @param [out] None
 * @return VDY_DynamicEstimate_t* 车身数据冻结后的数据存储地址
*/
VDY_DynamicEstimate_t *VDY_getFreezedVehDyncDataPointer();

VDY_vehicleFuncSwt_t *VDY_getVehFuncSwtPointer();

/**
 * @brief 获取VDY所有模块信息接口。
 *
 *
 * @param [in] None
 * @param [out] None
 * @return VDY_Info_t* 车身数据冻结后的数据存储地址
 */
VDY_Info_t *VDY_getVDY_Info_Pointer(void);


extern Target_Thres_t  gTargets[2][2][MAX_NB_OF_TARGETS];
extern objectInfo_t  gOutTargets[MAX_NB_OF_TARGETS * 2];
extern radarDspParam_t gDspParam;
extern float aparInstallAzimuthAngle;
extern VDY_DynamicEstimate_t freezedVehDyncData;
extern RDP_TrkObjectList_t gRDP_TrkObjectListSimulator;			//跟踪目标列表，只包含Track类型的目标
extern uint8_t radarID;

#define PARAM_DATAMODE_RAW_TRACK 	0x2
extern const VDY_vehicleFuncSwt_t* VDY_getVehiFuncSwtPointer(void);
extern void VDY_setCloseDOWOfIGPDStatu(uint8_t dowSta);

#if defined(_MSC_VER)
// 定义调试打印函数，支持普通字符串
// void debug_printf(const char* format, ...);
// #define printf debug_printf
void openDebugConsole();
#endif

#endif // TEMP_H



