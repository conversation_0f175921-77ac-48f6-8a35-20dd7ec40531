#include "frameconvertform.h"
//#include "ui_frameconvertform.h"
#include "../devices/canframe.h"
#include "../devices/devicemanager.h"
//#include "frameconver_haval2geely.h"
#include "frameconvertmodel.h"


#include <QThread>

FrameConvertForm::FrameConvertForm(Devices::Can::DeviceManager *deviceManager, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::FrameConvertForm)
{
    ui->setupUi(this);

    this->setWindowTitle( "Frame Convert Set" );
    Qt::WindowFlags flags = Qt::Dialog;
    flags |= Qt::WindowCloseButtonHint /*| Qt::WindowMaximizeButtonHint | Qt::WindowMinimizeButtonHint*/;
    this->setWindowFlags(flags);
    //mFrameConvertForm->setAttribute(Qt::WA_DeleteOnClose);

    mFrameConvertModel = new FrameConvertModel( deviceManager, this );
    ui->tableView->setModel( mFrameConvertModel );

//    mDeviceManager = deviceManager;
//    mH2G = new FrameConver_Haval2Geely( mDeviceManager );
//    QThread* thread = new QThread( this );
//    mH2G->moveToThread( thread );
//    thread->start();

//    connect(mH2G, SIGNAL(destroyed()), thread, SLOT(quit()));
//    connect(thread, SIGNAL(finished()), thread, SLOT(deleteLater()) );
//    connect(this, SLOT(started()), mH2G, SIGNAL(start()));
//    connect(this, SLOT(stoped()), mH2G, SIGNAL(stop()));
}

FrameConvertForm::~FrameConvertForm()
{
    delete ui;
}

//void FrameConvertForm::on_startPushButton_clicked()
//{
//    emit started();
//}

//void FrameConvertForm::on_stopPushButton_clicked()
//{
//    emit stoped();
//}
