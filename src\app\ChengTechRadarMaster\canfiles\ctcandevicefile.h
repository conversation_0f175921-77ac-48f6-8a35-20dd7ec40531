﻿#ifndef CTCANDEVICEFILE_H
#define CTCANDEVICEFILE_H

#include <QObject>
#include "CANDeviceFile.h"
#include "analysis/analysisdata.h"

namespace Analysis {
class AnalysisWorker;
class CalculationWorker;
}
class DataProcessWorker;

class CTCANDeviceFile : public QObject, public Devices::Can::CANDeviceFile
{
    Q_OBJECT
public:
    explicit CTCANDeviceFile(QObject *parent = nullptr);

    bool open(const QStringList &files, int goToFrame, bool goTo);
    bool close();
    bool isOpened() const { return mOpened; }
    bool readNextFrame();

    void callback(const Devices::Can::CanFrame::Ptr pFrame) override;

    void setRadarID(unsigned int id) { mRadarID = id; }
    static unsigned int radarID() { return mRadarID; }

signals:
    void opened();
    void closed();
    void canFrame(const Devices::Can::CanFrame &frame);
    void calculateFinished(quint8 radarID, const AnalysisData &analysisData);
    void currentFrame(int frameNo, int firstFrame, int frame);

public slots:
    void pause(bool paused) { mPaused = paused; }

private slots:
    void calculate(quint8 radarID, const AnalysisData &analysisData);

private:

private:
    Analysis::AnalysisWorker *mAnalysisWorker{0};
    Analysis::CalculationWorker *mCalculationWorker{0};
    DataProcessWorker *mDataProcessWorker{0};

    bool mOpened{ false };
    bool mPaused{ false };
    bool mReadOK{ false };
    bool mGoTo{ false };
    int mGoToFrame{ -1 };
    int mFrameIndex{ -1 };
    int mFirstFrame{-1};
    quint64 mPreviousFrameDateTime{0};


    static unsigned int mRadarID;
};

#endif // CTCANDEVICEFILE_H
