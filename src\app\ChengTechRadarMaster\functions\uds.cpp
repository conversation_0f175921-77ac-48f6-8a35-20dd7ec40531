﻿#include "uds.h"

#include "devices/devicemanager.h"

#include <QThread>
#include <QDebug>

#define BIG_LITTLE_SWAP32(x)        ( (((*(long int *)&x) & 0xff000000) >> 24) | \
                                      (((*(long int *)&x) & 0x00ff0000) >> 8) | \
                                      (((*(long int *)&x) & 0x0000ff00) << 8) | \
                                      (((*(long int *)&x) & 0x000000ff) << 24) )

uint32_t SeedToKey (uint32_t seed, uint32_t MASK)
{
    uint32_t KeyResult=0,STccKey=0;
    KeyResult = ((((seed >> 1) ^ seed) << 3) ^ (seed >> 2));
    STccKey = KeyResult ^ MASK;
    return STccKey;
}

/**
 * @Description: 航盛安全算法，level1 只在APP中使用
 * @Version: 1.0
 * @Autor: mo yican
 * @Date: 2023-11-02 16:26:00
 * @LastEditors: mo yican
 * @LastEditTime: Do not edit
 * @param {uint32_t} SEED
 * @param {uint32_t} MASK
 */
uint32_t SeedToKeyHASE (uint32_t SEED, uint32_t MASK)
{
        #if 0 //航盛项目 不再使用 2024-01-19
        uint32_t KeyResult=0,STccKey=0;
        KeyResult = (seed + 0x68431630) ^ (seed +  0x50918980);
        EMBARC_PRINTF("%s(%d) KeyResult = 0x%X\r\n",__func__,__LINE__,KeyResult);
        return KeyResult;
        #endif

    uint32_t KeyResult = 0;
        uint8_t mask[4];
        uint8_t seed[4];
        uint8_t seed1[4];
        uint8_t key[4];
        uint8_t key1[4];
    uint8_t key2[4];

        seed[3] = (uint8_t)(SEED >> 24) & 0xFF; // 取高8位
    seed[2] = (uint8_t)(SEED >> 16) & 0xFF; // 取次高8位
    seed[1] = (uint8_t)(SEED >> 8) & 0xFF;  // 取次低8位
    seed[0] = (uint8_t)SEED & 0xFF;         // 取低8位

        mask[3] = (uint8_t)(MASK >> 24) & 0xFF; // 取高8位
    mask[2] = (uint8_t)(MASK >> 16) & 0xFF; // 取次高8位
    mask[1] = (uint8_t)(MASK >> 8) & 0xFF;  // 取次低8位
    mask[0] = (uint8_t)MASK & 0xFF;         // 取低8位
        // EMBARC_PRINTF("%s(%d) seed[0]= 0x%X, seed[1]= 0x%X, seed[2]= 0x%X, seed[3]= 0x%X\r\n",__func__,__LINE__,seed[0],seed[1],seed[2],seed[3]);
        // EMBARC_PRINTF("%s(%d) mask[0]= 0x%X, mask[1]= 0x%X, mask[2]= 0x%X, mask[3]= 0x%X\r\n",__func__,__LINE__,mask[0],mask[1],mask[2],mask[3]);

        // 移位运算
    for (int i = 0; i < 4; i++) {
        seed1[i] = seed[i] << 1;
    }

        // 异或操作
    for (int i = 0; i < 4; i++) {
        key1[i] = seed[i] ^ mask[i];
        key2[i] = seed1[i] ^ mask[i];
    }

        // 相加得到最终的Key
    for (int i = 0; i < 4; i++) {
        key[i] = key1[i] + key2[i];
    }
        // EMBARC_PRINTF("%s(%d) key[0]= 0x%X, key[1]= 0x%X, key[2]= 0x%X, key[3]= 0x%X\r\n",__func__,__LINE__,key[0],key[1],key[2],key[3]);

        //注意系统的内存布局和字节顺序
        // KeyResult = *(uint32_t *)key;
        KeyResult = ((uint32_t)key[0] << 24) | ((uint32_t)key[1] << 16) | ((uint32_t)key[2] << 8) | (uint32_t)key[3];
//    EMBARC_PRINTF("%s(%d) KeyResult = 0x%X\r\n",__func__,__LINE__,KeyResult);

        return BIG_LITTLE_SWAP32(KeyResult);
}

quint8 GeelySeedToKey(quint8 seed[3u], /*quint8 data[5u],*/ quint8 *key)
{
    quint8 data[5u] = {0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu};
    quint32 position_a = 0xC541A9u;
    quint32 position_a1 = position_a & 0x01U;
    quint32 position_b24 = 0;
    quint8 tmp[8u] = {0u};
    quint8 i = 0u;
    quint8 key_storage[3u] = {0u};
    for(i = 0u; i < 5u; i++) {
      tmp[i + 3u] = data[i];
    }
    for(i = 0u; i < 3u; i++) {
      tmp[i] = seed[i];
      // xprintf("i is %d , seed is %d \r\n", i, seed[i]);
    }
    quint64 fixed_bytes = 0u;
    fixed_bytes = ((quint64)tmp[7u] << 56u) + ((quint64)tmp[6u] << 48u) + ((quint64)tmp[5u] << 40u)
                    + ((quint64)tmp[4u] << 32u) + ((quint64)tmp[3u] << 24u) + ((quint64)tmp[2u] << 16u)
                    + ((quint64)tmp[1u] << 8u) + ((quint64)tmp[0u]);
    quint64* challenge_bits_ptr = &fixed_bytes;
    quint64 challenge_bits = *challenge_bits_ptr;
    quint32 position_c = 0;

    // EMBARC_PRINTF("%x \n",challenge_bits>>32);
    // EMBARC_PRINTF("%x \n",challenge_bits);
    for(quint8 i =0u; i< 64U; i++) {
        position_b24 = position_a1^(quint32)(challenge_bits&0x01U);
        position_c = ((position_a >>1) | (position_b24<<23))^(position_b24 <<3)^(position_b24 <<5)^(position_b24 <<12)^(position_b24 <<15)^(position_b24 <<20);
        position_a = position_c;
        position_a1 = position_a &0x01U;
        challenge_bits=challenge_bits >>1;
    }

    quint8 key_r1 = (quint8)(position_c >> 4) & 0xffU;
    quint8 key_r2 = ((quint8)(position_c >> 20) & 0x0fU) | (((quint8)(position_c >> 12) & 0x0fU) << 4);
    quint8 key_r3 = ((quint8)(position_c >> 16) & 0x0fU) | ((quint8)(position_c  & 0x0fU) << 4);
    // EMBARC_PRINTF("key is %x %x %x\r\n",key_r1, key_r2, key_r3);
    key_storage[0u] = key_r1;
    key_storage[1u] = key_r2;
    key_storage[2u] = key_r3;

    (void)(memcpy(key,key_storage,3));

    return 0;
}

void GwmSeedToKey(quint32 seed ,const quint8 *key)
{
    uint32_t maskData = 0x4342433Du;
    uint8_t result = 0u;
    uint32_t stccKey = 0u;
    uint8_t i = 0u;
    quint8 seedToKey[4] = {0u};
    if(seed != 0u) {
            for(i = 0u; i < 35u; i++) {
                    if(seed & 0x80000000u) {
                            seed = seed << 1u;
                            seed = seed ^ maskData;
                    } else {
                            seed = seed << 1u;
                    }
            }
            stccKey = seed;
    }
    seedToKey[3] = stccKey;
    seedToKey[2] = (stccKey >> 8);
    seedToKey[1] = (stccKey >> 16);
    seedToKey[0] = (stccKey >> 24);

    result = memcmp(key, seedToKey, 4);
}

namespace Functions {

UDS::UDS(Devices::Can::DeviceManager *deviceManager, QObject *parent)
    : QObject(parent), mDeviceManager(deviceManager)
{

}

void UDS::appendCanFrame(const Devices::Can::CanFrame &frame)
{
    QMutexLocker locker(&mMutex);
    mReceiveCanFrames.enqueue(frame);
    if (mReceiveCanFrames.size() > mMaxKeepNumber)
    {
        mReceiveCanFrames.dequeue();
    }
}

Devices::Can::CanFrame UDS::takeFirstFrame(bool &ok)
{
    QMutexLocker locker(&mMutex);
    if (mReceiveCanFrames.size())
    {
        ok = true;
        return mReceiveCanFrames.dequeue();
    }
    else
    {
        ok = false;
        return Devices::Can::CanFrame();
    }
}

void UDS::clearReceive()
{
    mReceiveCanFrames.clear();
}

bool UDS::sendFrame(quint16 frameID, const QByteArray &data)
{
    if (!mDeviceManager->sendFrame(mChannelIndex, frameID, data, false, mCanUpdate))
    {
        mErrorString = QString("[CAN] Send Frame Error! Device(%1:%2-%3)")
                .arg(/*deviceName()*/"Device")
                .arg(mDeviceManager->deviceIndex())
                .arg(mChannelIndex);
        return false;
    }

    return true;
}

bool UDS::sendData(quint16 frameID, const QString &dataHex)
{
    QString data = dataHex;

    return sendData(frameID, QByteArray::fromHex(data.remove(QRegExp("\\s")).toLocal8Bit()));
}

bool UDS::sendData(quint16 frameID, const QByteArray &data)
{
    quint8 serviceID = 0;
    if (data.length() > ((m8BitUpdate || mCanUpdate) ? 7 : 62))
    {
        UDSFrame responseFrame;
        serviceID = sendDataMultiFrame(frameID, data, &responseFrame);
    }
    else
    {
        serviceID = sendDataSingleFrame(frameID, data);
    }

    return serviceID;
}

bool UDS::sendData(quint16 frameID, quint8 serviceID, const QByteArray &subID, const QByteArray &data)
{
    QByteArray dat(1, serviceID);
    dat.append(subID);
    dat.append(data);

    qDebug() << __FUNCTION__ << __LINE__ << "               ==> " << dat.mid(0, 8).toHex(' ') << dat.size();

    return sendData(frameID, dat);
}

bool UDS::sendData(quint16 frameID, const QString &dataHex, UDSFrame *responseFrame, qint64 timeOut)
{
    QString data = dataHex;
//    qDebug() << __FUNCTION__ << __LINE__ << dataHex << QByteArray::fromHex(data.remove(QRegExp("\\s")).toLocal8Bit()).toHex(' ');
    return sendData(frameID, QByteArray::fromHex(data.remove(QRegExp("\\s")).toLocal8Bit()), responseFrame, timeOut);
}

bool UDS::sendData(quint16 frameID, quint8 serviceID, const QByteArray &subID, const QByteArray &data, UDSFrame *responseFrame, qint64 timeOut)
{
    QByteArray dat(1, serviceID);
    dat.append(subID);
    dat.append(data);

    return sendData(frameID, dat, responseFrame, timeOut);
}

bool UDS::sendData(quint16 frameID, const QByteArray &data, UDSFrame *responseFrame, qint64 timeOut)
{
    clearReceive();
    quint8 serviceID = 0;
    if (data.length() > ((m8BitUpdate || mCanUpdate) ? 7 : 62))
    {
        serviceID = sendDataMultiFrame(frameID, data, responseFrame, timeOut);
    }
    else
    {
        serviceID = sendDataSingleFrame(frameID, data);
    }

    if (!serviceID)
    {
        qDebug() << __FUNCTION__ << __LINE__ << serviceID;
        return false;
    }

    qint64 currentTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    while((QDateTime::currentDateTime().toMSecsSinceEpoch () - currentTime) < timeOut)
    {
        bool ok{false};
        Devices::Can::CanFrame frame = takeFirstFrame(ok);
//        qDebug() << __FUNCTION__ << __LINE__ << QDateTime::currentDateTime()<< ok;
        if (ok && frame.id() == mResponseID)
        {
            UDSFrameType frameType = parseFrame(frame, responseFrame);
//            qDebug() << __FUNCTION__ << __LINE__ << frameType;
            if (frameType == UnknowFrame)
            {
                mErrorString = "[UDS] Parse Frame Error! " + frame.dataHex();
                return false;
            }
            else if (frameType == FlowControlFrame) {
                qDebug() << __FUNCTION__ << __LINE__ << "error: Received Flow Control Frame!";
                continue;
            }
            else if (frameType == FirstFrame)
            {
                if (!sendFlowControlFrame(frameID))
                {
                    mErrorString = "[UDS] Send Flow Control Frame Error!";
                    return false;
                }
            }
            if ((quint32)responseFrame->mData.length() == responseFrame->mIntactDataLength)
            {
                if (responseFrame->mServiceID == 0x7f)
                {
                    if ((quint8)responseFrame->mData[1] == serviceID && (quint8)responseFrame->mData[2] == 0x78)
                    {
                        responseFrame->clear();
                        currentTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
                        timeOut = 5000;
                        continue;
                    }
                    else
                    {
                        mErrorString = QString("[UDS] NAK! Service ID (0x%1), Error Code (0x%2).")
                                .arg((quint8)responseFrame->mData[1], 2, 16, QLatin1Char('0'))
                                .arg((quint8)responseFrame->mData[2], 2, 16, QLatin1Char('0'));
                        return false;
                    }
                }

                if (responseFrame->mServiceID == (serviceID + 0x40))
                {
                    return true;
                }
            }
        }
        QThread::msleep(1);
    }

    mErrorString = QString("[UDS] RTO Response TimeOut(%1ms).").arg(timeOut);
    return false;
}

quint8 UDS::sendDataSingleFrame(quint16 frameID, const QByteArray &data)
{
    int DL = data.size();
    if (!DL) {
        mErrorString = QString("[UDS] Data Length Error! Length = %1.").arg(DL);
        return false;
    }
    int DLC = 8;
    // 单帧发送
    if (DL <= 7) {
        DLC = 8;
    }
    else if (DL <= 10) {
        DLC = 12;   // 9
    }
    else if (DL <= 14) {
        DLC = 16;   // 10
    }
    else if (DL <= 18) {
        DLC = 20;   // 11
    }
    else if (DL <= 22) {
        DLC = 24;   // 12
    }
    else if (DL <= 30) {
        DLC = 32;   // 13
    }
    else if (DL <= 46) {
        DLC = 48;   // 14
    }
    else if (DL <= 62) {
        DLC = 64;   // 15
    }

    quint8 serviceID = 0x00;
    QByteArray canData(DLC, 0x55);
    if (DLC <= 8)
    {
        canData[0] = DL;
        canData.replace(1, DL, data);
        serviceID = canData[1];
    }
    else// if (DLC > 8)
    {
        canData[0] = 0x0;
        canData[1] = DL;
        canData.replace(2, DL, data);
        serviceID = canData[2];
    }

//    qDebug() << __FUNCTION__ << __LINE__ << "   ==> " << QString::number(frameID, 16) << canData.toHex(' ');

    if (!mDeviceManager->sendFrame(mChannelIndex, frameID, canData, false, mCanUpdate))
    {
//        qDebug() << __FUNCTION__ << __LINE__ << deviceName();
        mErrorString = QString("[UDS] Send Frame Error! Device(%1:%2-%3)")
                .arg(/*deviceName()*/"Device")
                .arg(mDeviceManager->deviceIndex())
                .arg(mChannelIndex);
        return 0x0;
    }

    return serviceID;
}

quint8 UDS::sendDataMultiFrame(quint16 frameID, const QByteArray &data, UDSFrame *responseFrame, qint64 timeOut)
{
    quint8 serviceID = data[0];
    quint64 FF_DL = data.size();
    QByteArray multiData((m8BitUpdate || mCanUpdate) ? 8 : 64, 0x55);
    int DL = (m8BitUpdate || mCanUpdate) ? 6 : 62;
    if (FF_DL <= 4095 || (m8BitUpdate || mCanUpdate)) {
        multiData[0] = 0x10 | ((FF_DL & 0xF00) >> 8);
        multiData[1] = (FF_DL & 0x0FF);
    } else {
        multiData[0] = 0x10;
        multiData[1] = 0x00;
        multiData[2] = (FF_DL & 0xFF000000) >> 24;
        multiData[3] = (FF_DL & 0x00FF0000) >> 16;
        multiData[4] = (FF_DL & 0x0000FF00) >> 8;
        multiData[5] = (FF_DL & 0x000000FF);
        DL = 58;
    }

    // 发送首帧数据
    multiData.replace(2, DL, data.data(), (m8BitUpdate || mCanUpdate) ? 6 : 62);
//    qDebug() << __FUNCTION__ << __LINE__ << "    ==> " << QString::number(frameID, 16) << multiData.toHex(' ');
    if (!mDeviceManager->sendFrame(mChannelIndex, frameID, multiData, false, mCanUpdate))
    {
        return 0x0;
    }

    // 等待接收流控帧
    bool flowControl = false;
    qint64 currentTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    while((QDateTime::currentDateTime().toMSecsSinceEpoch () - currentTime) < timeOut)
    {
        bool ok;
        Devices::Can::CanFrame frame = takeFirstFrame(ok);
        if (ok && frame.id() == mResponseID)
        {
//            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            UDSFrameType frameType = parseFrame(frame, responseFrame);
            if (frameType == UnknowFrame)
            {
                return false;
            }
            if (frameType == FlowControlFrame)
            {
                flowControl = true;
                break;
            }
        }
    }

    // 未接收到流控帧
    if (!flowControl)
    {
        return 0x0;
    }

    // 发送连续帧
    if (!sendSuccessiveFrame(frameID, data, DL))
    {
        return 0x0;
    }

    return serviceID;
}

bool UDS::sendSuccessiveFrame(quint16 frameID, const QByteArray &data, const qint8 beginIndex)
{
    quint32 dataSize = data.size() - beginIndex;
    int blockCount = dataSize / ((m8BitUpdate || mCanUpdate) ? 7 : 63);
//    qDebug() << __FUNCTION__ << __LINE__ << "   --> " << data.size() << beginIndex << dataSize << blockCount;
    for (int i = 0; i < blockCount; ++i)
    {
        QByteArray dat(1, 0x55);
        dat[0] = 0x20 | ((quint8)((i + 1) % 0x10));
        dat.append(data.mid(beginIndex + i * ((m8BitUpdate || mCanUpdate) ? 7 : 63), ((m8BitUpdate || mCanUpdate) ? 7 : 63)));
//        qDebug() << __FUNCTION__ << __LINE__ << "   --> " << QString::number(frameID, 16) << dat.toHex(' ') << dat.size() << i + 1 << beginIndex + i * 63;
        if (!mDeviceManager->sendFrame(mChannelIndex, frameID, dat, false, mCanUpdate))
        {
            return false;
        }
        QThread::msleep(1);
    }

//    qDebug() << __FUNCTION__ << __LINE__ << "   --> " << dataSize % 63;
    if (dataSize % ((m8BitUpdate || mCanUpdate) ? 7 : 63))
    {
        QByteArray dat(((m8BitUpdate || mCanUpdate) ? 8 : 64), 0x55);
        dat[0] = 0x20 | ((quint8)((blockCount + 1) % 0x10));
        dat.replace(1, dataSize % ((m8BitUpdate || mCanUpdate) ? 7 : 63), data.mid(beginIndex + blockCount * ((m8BitUpdate || mCanUpdate) ? 7 : 63)));
//        qDebug() << __FUNCTION__ << __LINE__ << "   --> " << QString::number(frameID, 16) << dat.toHex(' ') << dat.size() << blockCount + 1 <<  beginIndex + blockCount * 63;
        if (!mDeviceManager->sendFrame(mChannelIndex, frameID, dat, false, mCanUpdate))
        {
            return false;
        }
    }

    return true;
}

bool UDS::sendFlowControlFrame(quint16 frameID, quint8 fs, quint8 bs, quint8 sTmin)
{
    QByteArray data(8, 0x55);
    data[0] = 0x30 | (fs & 0x0F);
    data[1] = bs;
    data[2] = sTmin;

    return mDeviceManager->sendFrame(mChannelIndex, frameID, data, false, mCanUpdate);
}

UDSFrameType UDS::parseFrame(const Devices::Can::CanFrame &canframe, UDSFrame *udcFrame)
{
    //const QByteArray &data = canframe.data();
    quint8 *data = (quint8 *)canframe.data().data();
    UDSFrameType frameType = (UDSFrameType)((data[0] & 0xF0) >> 4);
//    qDebug() << __FUNCTION__ << __LINE__ << frameType << canframe.dataHex();
    switch (frameType)
    {
    case SingleFrame: // 单帧
    {
        int SF_DL = 0;
        if (canframe.length() <= 8) {
            SF_DL = data[0] & 0x0F;
            udcFrame->mIntactDataLength = SF_DL;
            udcFrame->mData.append(canframe.data().mid(1, SF_DL));
        } else {
            SF_DL = data[1];
            udcFrame->mIntactDataLength = SF_DL;
            udcFrame->mData.append(canframe.data().mid(2, SF_DL));
        }
        udcFrame->mServiceID = udcFrame->mData[0];
    }
        break;
    case FirstFrame: // 首帧
    {
        int FF_DL = (((quint16)(data[0] & 0x0F)) << 8) | ((quint16)(data[1]));
        if (FF_DL) { // <= 4095
            udcFrame->mIntactDataLength = FF_DL;
            udcFrame->mData.append(canframe.data().mid(2));
        } else { // > 4095
            FF_DL = (((quint32)data[2]) << 24) | (((quint32)data[3]) << 16) | (((quint32)data[4]) << 8) | (quint32)data[5];
            udcFrame->mIntactDataLength = FF_DL;
            udcFrame->mData.append(canframe.data().mid(6));
        }
        udcFrame->mServiceID = udcFrame->mData[0];
    }
        break;
    case SuccessiveFrame:
    {
        quint8 SN = data[0] & 0xF;
        if (SN != ((udcFrame->mIndex + 1) & 0xF))
        {
            qDebug() << __FUNCTION__ << __LINE__ << SN << udcFrame->mIndex << canframe.dataHex();
            return UnknowFrame;
        }
        udcFrame->mData.append(canframe.data().mid(1, udcFrame->mIntactDataLength - udcFrame->mData.length()));
        udcFrame->mIndex++;
    }
        break;
    case FlowControlFrame: // 流控帧
//        qDebug() << __FUNCTION__ << __LINE__ << "Flow Control Frame";
        udcFrame->mFS = (UDSFSType)(data[0] & 0xF);
        udcFrame->mBS = data[1];
        udcFrame->mSTmin = data[2];
        break;
    default:
        break;
    }
//    qDebug() << __FUNCTION__ << __LINE__ << frameType;
    return frameType;
}

} // namespace Functions

/************************************************************************/
typedef struct {
    uint32_t eK[44], dK[44];    // encKey, decKey
    int Nr; // 10 rounds
}AesKey;

/* for 128-bit blocks, Rijndael never uses more than 10 rcon values */
// AES-128轮常量
static const uint32_t rcon[10] = {
        0x01000000UL, 0x02000000UL, 0x04000000UL, 0x08000000UL, 0x10000000UL,
        0x20000000UL, 0x40000000UL, 0x80000000UL, 0x1B000000UL, 0x36000000UL
};

// S盒
unsigned char S[256] = {
        0x63, 0x7C, 0x77, 0x7B, 0xF2, 0x6B, 0x6F, 0xC5, 0x30, 0x01, 0x67, 0x2B, 0xFE, 0xD7, 0xAB, 0x76,
        0xCA, 0x82, 0xC9, 0x7D, 0xFA, 0x59, 0x47, 0xF0, 0xAD, 0xD4, 0xA2, 0xAF, 0x9C, 0xA4, 0x72, 0xC0,
        0xB7, 0xFD, 0x93, 0x26, 0x36, 0x3F, 0xF7, 0xCC, 0x34, 0xA5, 0xE5, 0xF1, 0x71, 0xD8, 0x31, 0x15,
        0x04, 0xC7, 0x23, 0xC3, 0x18, 0x96, 0x05, 0x9A, 0x07, 0x12, 0x80, 0xE2, 0xEB, 0x27, 0xB2, 0x75,
        0x09, 0x83, 0x2C, 0x1A, 0x1B, 0x6E, 0x5A, 0xA0, 0x52, 0x3B, 0xD6, 0xB3, 0x29, 0xE3, 0x2F, 0x84,
        0x53, 0xD1, 0x00, 0xED, 0x20, 0xFC, 0xB1, 0x5B, 0x6A, 0xCB, 0xBE, 0x39, 0x4A, 0x4C, 0x58, 0xCF,
        0xD0, 0xEF, 0xAA, 0xFB, 0x43, 0x4D, 0x33, 0x85, 0x45, 0xF9, 0x02, 0x7F, 0x50, 0x3C, 0x9F, 0xA8,
        0x51, 0xA3, 0x40, 0x8F, 0x92, 0x9D, 0x38, 0xF5, 0xBC, 0xB6, 0xDA, 0x21, 0x10, 0xFF, 0xF3, 0xD2,
        0xCD, 0x0C, 0x13, 0xEC, 0x5F, 0x97, 0x44, 0x17, 0xC4, 0xA7, 0x7E, 0x3D, 0x64, 0x5D, 0x19, 0x73,
        0x60, 0x81, 0x4F, 0xDC, 0x22, 0x2A, 0x90, 0x88, 0x46, 0xEE, 0xB8, 0x14, 0xDE, 0x5E, 0x0B, 0xDB,
        0xE0, 0x32, 0x3A, 0x0A, 0x49, 0x06, 0x24, 0x5C, 0xC2, 0xD3, 0xAC, 0x62, 0x91, 0x95, 0xE4, 0x79,
        0xE7, 0xC8, 0x37, 0x6D, 0x8D, 0xD5, 0x4E, 0xA9, 0x6C, 0x56, 0xF4, 0xEA, 0x65, 0x7A, 0xAE, 0x08,
        0xBA, 0x78, 0x25, 0x2E, 0x1C, 0xA6, 0xB4, 0xC6, 0xE8, 0xDD, 0x74, 0x1F, 0x4B, 0xBD, 0x8B, 0x8A,
        0x70, 0x3E, 0xB5, 0x66, 0x48, 0x03, 0xF6, 0x0E, 0x61, 0x35, 0x57, 0xB9, 0x86, 0xC1, 0x1D, 0x9E,
        0xE1, 0xF8, 0x98, 0x11, 0x69, 0xD9, 0x8E, 0x94, 0x9B, 0x1E, 0x87, 0xE9, 0xCE, 0x55, 0x28, 0xDF,
        0x8C, 0xA1, 0x89, 0x0D, 0xBF, 0xE6, 0x42, 0x68, 0x41, 0x99, 0x2D, 0x0F, 0xB0, 0x54, 0xBB, 0x16
};

/* For CMAC Calculation */
unsigned char const_Rb[16] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x87
};
unsigned char const_Zero[16] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

// uint8_t y[4] -> uint32_t x
#define LOAD32H(x, y) \
  do { (x) = ((uint32_t)((y)[0] & 0xff)<<24) | ((uint32_t)((y)[1] & 0xff)<<16) | \
             ((uint32_t)((y)[2] & 0xff)<<8)  | ((uint32_t)((y)[3] & 0xff));} while(0)

// uint32_t x -> uint8_t y[4]
#define STORE32H(x, y) \
  do { (y)[0] = (uint8_t)(((x)>>24) & 0xff); (y)[1] = (uint8_t)(((x)>>16) & 0xff);   \
       (y)[2] = (uint8_t)(((x)>>8) & 0xff); (y)[3] = (uint8_t)((x) & 0xff); } while(0)

// 从uint32_t x中提取从低位开始的第n个字节
#define BYTE(x, n) (((x) >> (8 * (n))) & 0xff)

/* used for keyExpansion */
// 字节替换然后循环左移1位
#define MIX(x) (((S[BYTE(x, 2)] << 24) & 0xff000000) ^ ((S[BYTE(x, 1)] << 16) & 0xff0000) ^ \
                ((S[BYTE(x, 0)] << 8) & 0xff00) ^ (S[BYTE(x, 3)] & 0xff))

// uint32_t x循环左移n位
#define ROF32(x, n)  (((x) << (n)) | ((x) >> (32-(n))))

#define BLOCKSIZE 16  //AES-128分组长度为16字节

/* copy in[16] to state[4][4] */
int loadStateArray(uint8_t(*state)[4], const uint8_t *in) {
    int i, j;
    for (i = 0; i < 4; ++i) {
        for (j = 0; j < 4; ++j) {
            state[j][i] = *in++;
        }
    }
    return 0;
}

/* copy state[4][4] to out[16] */
int storeStateArray(uint8_t(*state)[4], uint8_t *out) {
    int i, j;
    for (i = 0; i < 4; ++i) {
        for (j = 0; j < 4; ++j) {
            *out++ = state[j][i];
        }
    }
    return 0;
}

//秘钥扩展
int keyExpansion(const uint8_t *key, uint32_t keyLen, AesKey *aesKey) {
    uint32_t *w = aesKey->eK;  //加密秘钥
    uint32_t *v = aesKey->dK;  //解密秘钥

    /* keyLen is 16 Bytes, generate uint32_t W[44]. */

    /* W[0-3] */
    int i;
    for (i = 0; i < 4; ++i) {
        LOAD32H(w[i], key + 4 * i);
    }
    /* W[4-43] */

    for (i = 0; i < 10; ++i) {
        w[4] = w[0] ^ MIX(w[3]) ^ rcon[i];
        w[5] = w[1] ^ w[4];
        w[6] = w[2] ^ w[5];
        w[7] = w[3] ^ w[6];
        w += 4;
    }

    w = aesKey->eK + 44 - 4;
    //解密秘钥矩阵为加密秘钥矩阵的倒序，方便使用，把ek的11个矩阵倒序排列分配给dk作为解密秘钥
    //即dk[0-3]=ek[41-44], dk[4-7]=ek[37-40]... dk[41-44]=ek[0-3]
    int j;
    for (j = 0; j < 11; ++j) {

        for (i = 0; i < 4; ++i) {
            v[i] = w[i];
        }
        w -= 4;
        v += 4;
    }

    return 0;
}

// 轮秘钥加
int addRoundKey(uint8_t(*state)[4], const uint32_t *key) {
    uint8_t k[4][4];
    int i;
    int j;
    /* i: row, j: col */
    for (i = 0; i < 4; ++i) {
        for (j = 0; j < 4; ++j) {
            k[i][j] = (uint8_t)BYTE(key[j], 3 - i);  /* 把 uint32 key[4] 先转换为矩阵 uint8 k[4][4] */
            state[i][j] ^= k[i][j];
        }
    }

    return 0;
}

//字节替换
int subBytes(uint8_t(*state)[4]) {
    /* i: row, j: col */
    int i, j;
    for (i = 0; i < 4; ++i) {
        for (j = 0; j < 4; ++j) {
            state[i][j] = S[state[i][j]]; //直接使用原始字节作为S盒数据下标
        }
    }

    return 0;
}

//行移位
int shiftRows(uint8_t(*state)[4]) {
    uint32_t block[4] = { 0 };

    /* i: row */
    int i;
    for (i = 0; i < 4; ++i) {
        //便于行循环移位，先把一行4字节拼成uint_32结构，移位后再转成独立的4个字节uint8_t
        LOAD32H(block[i], state[i]);
        block[i] = ROF32(block[i], 8 * i);
        STORE32H(block[i], state[i]);
    }
    return 0;
}

/* Galois Field (256) Multiplication of two Bytes */
// 两字节的伽罗华域乘法运算
uint8_t GMul(uint8_t u, uint8_t v) {
    uint8_t p = 0;
    int i;
    for (i = 0; i < 8; ++i) {
        if (u & 0x01) {    //
            p ^= v;
        }

        int flag = (v & 0x80);
        v <<= 1;
        if (flag) {
            v ^= 0x1B; /* x^8 + x^4 + x^3 + x + 1 */
        }
        u >>= 1;
    }
    return p;
}

// 列混合
int mixColumns(uint8_t(*state)[4]) {
    uint8_t tmp[4][4];
    uint8_t M[4][4] = { {0x02, 0x03, 0x01, 0x01},
                       {0x01, 0x02, 0x03, 0x01},
                       {0x01, 0x01, 0x02, 0x03},
                       {0x03, 0x01, 0x01, 0x02} };
    /* copy state[4][4] to tmp[4][4] */
    int i, j;
    for (i = 0; i < 4; ++i) {
        for (j = 0; j < 4; ++j) {
            tmp[i][j] = state[i][j];
        }
    }

    for (i = 0; i < 4; ++i) {
        for (j = 0; j < 4; ++j) {  //伽罗华域加法和乘法
            state[i][j] = GMul(M[i][0], tmp[0][j]) ^ GMul(M[i][1], tmp[1][j])
                ^ GMul(M[i][2], tmp[2][j]) ^ GMul(M[i][3], tmp[3][j]);
        }
    }
    return 0;
}

// AES-128加密接口，输入key应为16字节长度，输入长度应该是16字节整倍数，
// 这样输出长度与输入长度相同，函数调用外部为输出数据分配内存
int aesEncrypt(const uint8_t *key, uint32_t keyLen, const uint8_t *pt, uint8_t *ct, uint32_t len) {

    AesKey aesKey;
    uint8_t *pos = ct;
    const uint32_t *rk = aesKey.eK;  //解密秘钥指针
    uint8_t out[BLOCKSIZE] = { 0 };
    uint8_t actualKey[16] = { 0 };
    uint8_t state[4][4] = { 0 };

    memcpy(actualKey, key, keyLen);
    keyExpansion(actualKey, 16, &aesKey);  // 秘钥扩展
    uint32_t i;
    // 使用ECB模式循环加密多个分组长度的数据
    for (i = 0; i < len; i= (i + BLOCKSIZE)) {
        // 把16字节的明文转换为4x4状态矩阵来进行处理
        loadStateArray(state, pt);
        // 轮秘钥加
        addRoundKey(state, rk);
        int j;
        for (j = 1; j < 10; ++j) {
            rk += 4;
            subBytes(state);   // 字节替换
            shiftRows(state);  // 行移位
            mixColumns(state); // 列混合
            addRoundKey(state, rk); // 轮秘钥加
        }

        subBytes(state);    // 字节替换
        shiftRows(state);  // 行移位
        // 此处不进行列混合
        addRoundKey(state, rk + 4); // 轮秘钥加

        // 把4x4状态矩阵转换为uint8_t一维数组输出保存
        storeStateArray(state, pos);

        pos += BLOCKSIZE;  // 加密数据内存指针移动到下一个分组
        pt += BLOCKSIZE;   // 明文数据指针移动到下一个分组
        rk = aesKey.eK;    // 恢复rk指针到秘钥初始位置
    }
    return 0;
}

/* Basic Functions */
void xor_128(unsigned char *a, unsigned char *b, unsigned char *out)
{
    int i;
    for (i = 0;i < 16; i++)
    {
        out[i] = a[i] ^ b[i];
    }
}

/* AES-CMAC Generation Function */
void leftshift_onebit(unsigned char *input, unsigned char *output)
{
    int         i;
    unsigned char overflow = 0;

    for (i = 15; i >= 0; i--) {
        output[i] = input[i] << 1;
        output[i] |= overflow;
        overflow = (input[i] & 0x80) ? 1 : 0;
    }
    return;
}

void generate_subkey(unsigned char *key, unsigned char *K1, unsigned char *K2)
{
    unsigned char L[16];
    unsigned char Z[16];
    unsigned char tmp[16];
    int i;

    for (i = 0; i < 16; i++) Z[i] = 0;

    aesEncrypt(key, 16, Z, L, 16);

    if ((L[0] & 0x80) == 0) { /* If MSB(L) = 0, then K1 = L << 1 */
        leftshift_onebit(L, K1);
    }
    else {    /* Else K1 = ( L << 1 ) (+) Rb */
        leftshift_onebit(L, tmp);
        xor_128(tmp, const_Rb, K1);
    }

    if ((K1[0] & 0x80) == 0) {
        leftshift_onebit(K1, K2);
    }
    else {
        leftshift_onebit(K1, tmp);
        xor_128(tmp, const_Rb, K2);
    }
    return;
}

void padding(unsigned char *lastb, unsigned char *pad, int length)
{
    int         j;

    /* original last block */
    for (j = 0; j < 16; j++) {
        if (j < length) {
            pad[j] = lastb[j];
        }
        else if (j == length) {
            pad[j] = 0x80;
        }
        else {
            pad[j] = 0x00;
        }
    }
}

static void AES_CMAC(unsigned char *key, unsigned char *input, int length, unsigned char *mac)
{
    unsigned char       X[16], Y[16], M_last[16], padded[16];
    unsigned char       K1[16], K2[16];
    int         n, i, flag;
    generate_subkey(key, K1, K2);

    n = (length + 15) / 16;       /* n is number of rounds */

    if (n == 0) {
        n = 1;
        flag = 0;
    }
    else {
        if ((length % 16) == 0) { /* last block is a complete block */
            flag = 1;
        }
        else { /* last block is not complete block */
            flag = 0;
        }
    }

    if (flag) { /* last block is complete block */
        xor_128(&input[16 * (n - 1)], K1, M_last);
    }
    else {
        padding(&input[16 * (n - 1)], padded, length % 16);
        xor_128(padded, K2, M_last);
    }
    for (i = 0; i < 16; i++) X[i] = 0;
    for (i = 0; i < n - 1; i++) {
        xor_128(X, &input[16 * i], Y); /* Y := Mi (+) X  */
        aesEncrypt(key, 16, Y, X, 16);      /* X := AES-128(KEY, Y); */
    }

    xor_128(X, M_last, Y);
    aesEncrypt(key, 16, Y, X, 16);

    for (i = 0; i < 16; i++) {
        mac[i] = X[i];
    }
}

int GenerateKeyEx_BEIQI_BE12_PROJECT(const unsigned char *iSeedArray, unsigned short iSeedArraySize, const unsigned int iSecurityLevel, const char *iVariant, unsigned char *ioKeyArray, unsigned int iKeyArraySize, unsigned int *oSize)
{
#define SECURITY_LEVEL1		0x01
#define SECURITY_LEVEL2		0x11
    static uint8_t app_key_factor[16] = { 0x82, 0x42, 0x35, 0x59, 0x43, 0x25, 0xE2, 0x3A,
                                          0x1F, 0x2E, 0xB5, 0x30, 0x49, 0xAA, 0x5D, 0x66 };		//客户主密钥key
    static uint8_t app_key_DF[16] = { 0x1F, 0x9D, 0x6D, 0xC2, 0xCC, 0xF3, 0xE5, 0x9C, 0x16, 0x00, 0x81, 0xE6, 0x4E, 0x2D, 0x88, 0x99 };
    static uint8_t boot_key_factor[16] = { 0x65, 0xB7, 0xEA, 0xB1, 0xB3, 0x5C, 0x32, 0x44,
                                           0xE9, 0xC0, 0x5C, 0x02, 0x62, 0xE7, 0x3C, 0x6B };
    static uint8_t boot_key_DF[16] = { 0x1F, 0x9D, 0x6D, 0xC2, 0xCC, 0xF3, 0xE5, 0x9C, 0x16, 0x00, 0x81, 0xE6, 0x4E, 0x2D, 0x88, 0x99 };

    if (0 == iSeedArray || 0 == ioKeyArray) return -1;

        unsigned char bSeed[16] = { 0 };
        uint8_t key_factor[16] = { 0 };
        uint8_t key_df[16] = { 0 };
        uint8_t key_sk[16] = { 0u };
        uint8_t sk_tmp[16] = { 0u };
        uint8_t sk_token[16] = { 0u };
        uint8_t index = 0;

        if (SECURITY_LEVEL1 == iSecurityLevel)
        {
            memcpy(key_factor, app_key_factor, sizeof(key_factor));
            memcpy(key_df, app_key_DF, sizeof(app_key_DF));
        }
        else if (SECURITY_LEVEL2 == iSecurityLevel)
        {
            memcpy(key_factor, boot_key_factor, sizeof(key_factor));
            memcpy(key_df, boot_key_DF, sizeof(boot_key_DF));
        }
        else
        {
            return -1;
        }

        for (index = 0; index < 16; index++) {
            bSeed[index] = (unsigned char)iSeedArray[index]; /* LSB */
        }

        //第一次加密,使用AES-CMAC算法和派生因子DF对安全访问主密钥Key进行派生,得到16字节会话密钥SK
        AES_CMAC(key_factor, key_df, 16, key_sk);
        memcpy(sk_tmp, key_sk, 16);
        //第二次加密,使用会话密钥SK对Seed值进行AES-CMAC计算,得到16字节Token值
        AES_CMAC(sk_tmp, bSeed, 16, sk_token);

        memcpy(&ioKeyArray[0], key_df, 16);
        memcpy(&ioKeyArray[16], sk_token, 16);
        *oSize = 32;

        return 0;
}

quint8 GeelySeedToKey180Pro(quint8 seed[3u], /*quint8 data[5u],*/ quint8 *key)
{
    static uint8_t debug_mask[5u] = { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF };/*DEFAULT*/
    uint32_t position_a = 0xC541A9;
    uint8_t position_a1 = position_a & 0x01;
    uint8_t position_b24 = 0;
    uint8_t tmp[8u] = { 0u };
    uint8_t i = 0u;
    uint8_t key_storage[3u] = { 0u };
    uint8_t bSeed[3] = { 0 };

    bSeed[2] = (unsigned char)seed[2];
    bSeed[1] = (unsigned char)seed[1];
    bSeed[0] = (unsigned char)seed[0];


    for (i = 0u; i < 5u; i++) {
        tmp[i + 3u] = debug_mask[i];
    }
    for (i = 0u; i < 3u; i++) {
        tmp[i] = bSeed[i];
    }

    uint64_t fixed_bytes = 0u;
    fixed_bytes = ((uint64_t)tmp[7u] << 56u) + ((uint64_t)tmp[6u] << 48u) + ((uint64_t)tmp[5u] << 40u)
            + ((uint64_t)tmp[4u] << 32u) + ((uint64_t)tmp[3u] << 24u) + ((uint64_t)tmp[2u] << 16u)
            + ((uint64_t)tmp[1u] << 8u) + ((uint64_t)tmp[0u]);
    uint64_t* challenge_bits_ptr = &fixed_bytes;
    uint64_t challenge_bits = *challenge_bits_ptr;
    uint32_t position_c = 0;

    for (uint8_t i = 0; i<64; i++) {
        position_b24 = position_a1 ^ (challenge_bits & 0x01);
        position_c = ((position_a >> 1) | (position_b24 << 23)) ^ (position_b24 << 3) ^ (position_b24 << 5) ^ (position_b24 << 12) ^ (position_b24 << 15) ^ (position_b24 << 20);
        position_a = position_c;
        position_a1 = position_a & 0x01;
        challenge_bits = challenge_bits >> 1;
    }

    uint8_t key_r1 = (position_c >> 4) & 0xff;
    uint8_t key_r2 = ((position_c >> 20) & 0x0f) | (((position_c >> 12) & 0x0f) << 4);
    uint8_t key_r3 = ((position_c >> 16) & 0x0f) | ((position_c & 0x0f) << 4);

    key_storage[0u] = key_r1;
    key_storage[1u] = key_r2;
    key_storage[2u] = key_r3;


    (void)(memcpy(key,key_storage,3));

    return 0;
}

quint8 GeelySeedToKey180ProNew(quint8 seed[], quint8 *key, int index)
{
    static uint8_t debug_mask_1[][5u] = {
//        { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF },
//        { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF },
//        { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF },
//        { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF },
        {0x2C, 0x9F, 0xE8, 0xD1, 0x81},
        {0x4D, 0xB6, 0x2E, 0x83, 0x52},
        {0x33, 0x9E, 0x11, 0x7B, 0x14},
        {0x8D, 0x91, 0xB4, 0x82, 0x40},};/*DEFAULT*/
    uint32_t position_a = 0xC541A9;
    uint8_t position_a1 = position_a & 0x01;
    uint8_t position_b24 = 0;
    uint8_t tmp[8u] = { 0u };
    uint8_t i = 0u;
    uint8_t key_storage[3u] = { 0u };
    uint8_t bSeed[3] = { 0 };

    bSeed[2] = (unsigned char)seed[2];
    bSeed[1] = (unsigned char)seed[1];
    bSeed[0] = (unsigned char)seed[0];


    for (i = 0u; i < 5u; i++) {
        tmp[i + 3u] = debug_mask_1[index][i];
    }
    for (i = 0u; i < 3u; i++) {
        tmp[i] = bSeed[i];
    }

    uint64_t fixed_bytes = 0u;
    fixed_bytes = ((uint64_t)tmp[7u] << 56u) + ((uint64_t)tmp[6u] << 48u) + ((uint64_t)tmp[5u] << 40u)
            + ((uint64_t)tmp[4u] << 32u) + ((uint64_t)tmp[3u] << 24u) + ((uint64_t)tmp[2u] << 16u)
            + ((uint64_t)tmp[1u] << 8u) + ((uint64_t)tmp[0u]);
    uint64_t* challenge_bits_ptr = &fixed_bytes;
    uint64_t challenge_bits = *challenge_bits_ptr;
    uint32_t position_c = 0;

    for (uint8_t i = 0; i<64; i++) {
        position_b24 = position_a1 ^ (challenge_bits & 0x01);
        position_c = ((position_a >> 1) | (position_b24 << 23)) ^ (position_b24 << 3) ^ (position_b24 << 5) ^ (position_b24 << 12) ^ (position_b24 << 15) ^ (position_b24 << 20);
        position_a = position_c;
        position_a1 = position_a & 0x01;
        challenge_bits = challenge_bits >> 1;
    }

    uint8_t key_r1 = (position_c >> 4) & 0xff;
    uint8_t key_r2 = ((position_c >> 20) & 0x0f) | (((position_c >> 12) & 0x0f) << 4);
    uint8_t key_r3 = ((position_c >> 16) & 0x0f) | ((position_c & 0x0f) << 4);

    key_storage[0u] = key_r1;
    key_storage[1u] = key_r2;
    key_storage[2u] = key_r3;

    memcpy(key, key_storage, 3);

    return 0;
}

int GenerateKeyEx_GEELY_E245_PROJECT(int type, const unsigned char *iSeedArray, unsigned short iSeedArraySize, const unsigned int iSecurityLevel, const char *iVariant, unsigned char *ioKeyArray, unsigned int iKeyArraySize, unsigned int *oSize)
{
    static uint8_t key_constant[][16] = { // l,r-27 05,boot-27 01
                                          { 0xAC, 0xD2, 0x0E, 0x9D, 0x4C, 0x54, 0xF5, 0xDA, 0x80, 0x7F, 0xD5, 0xDE, 0x88, 0xCB, 0x96, 0x63 }, //客户安全常数
                                          { 0xA3, 0xCE, 0x53, 0x60, 0xAA, 0x8A, 0x82, 0xCD, 0x29, 0x50, 0x7D, 0x82, 0x53, 0xF2, 0x82, 0xA1 },
                                          { 0xAC, 0xD2, 0x0E, 0x9D, 0x4C, 0x54, 0xF5, 0xDA, 0x80, 0x7F, 0xD5, 0xDE, 0x88, 0xCB, 0x96, 0x63 }, //客户安全常数
                                          { 0xA3, 0xCE, 0x53, 0x60, 0xAA, 0x8A, 0x82, 0xCD, 0x29, 0x50, 0x7D, 0x82, 0x53, 0xF2, 0x82, 0xA1 },
                                          { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF }};

    if (0 == iSeedArray || 0 == ioKeyArray) return -1;

    unsigned char bSeed[16] = { 0 };
    uint8_t key_sk[16] = { 0u };
    uint8_t index = 0;

    for (index = 0; index < 16; index++) {
        bSeed[index] = (unsigned char)iSeedArray[index]; /* LSB */
    }

    AES_CMAC(key_constant[type], bSeed, 16, key_sk);

    memcpy(&ioKeyArray[0], key_sk, 16);
    *oSize = 16;

    return 0;
}
