#include "ecuupdatemodel.h"
#include "ecuupdateitem.h"

#include <QDebug>
#include <QColor>

ECUUpdateModel::ECUUpdateModel( QObject *parent )
 : QAbstractTableModel( parent )
{
    mHeader << "描述" << "数据" << "异常信息";
}

ECUUpdateModel::~ECUUpdateModel()
{
    clear();
}

void ECUUpdateModel::addData(ECUUpdateItem *item)
{
    beginResetModel();
    mData.append( item );
    endResetModel();
    //qDebug() << __FUNCTION__ << __LINE__;
}

void ECUUpdateModel::insertData(ECUUpdateItem *item, const QByteArray &udsCmd, bool before)
{
    beginResetModel();
    //mData.append( item );
    bool bDeal = false;
    if( udsCmd.isEmpty() ){  //放到最前或最后
        if( before ){
            mData.insert( mData.begin(), item );
            bDeal = true;
        }else{
            mData.insert( mData.end(), item );
            bDeal = true;
        }
    }else{  //放到某条uds指令的前面或后面
        for( int i=0; i<mData.size(); i++ ){
            if( ECUUpdateItem::TYPE::General == mData[i]->type() ){
                //QByteArray data = QByteArray::fromHex( mData[i]->dataStr().remove(QRegExp("\\s")).toLocal8Bit() );
                ECUUpdateItem_General* item = (ECUUpdateItem_General*)mData[i];
                QByteArray data = item->cmd();
                if( data == udsCmd ){
                    if( before ){
                        mData.insert( i, item );
                        bDeal = true;
                    }else{
                        mData.insert( i +1, item );
                        bDeal = true;
                    }
                    bDeal = true;
                    break;
                }
            }
        }
    }
    endResetModel();
}

void ECUUpdateModel::updateMatchData(const QByteArray &udsCmd, const QString &desc, const QByteArrayList &words, bool bWholeWordMatch, bool bEqual)
{
    for( int i=0; i<mData.size(); i++ ){
        if( ECUUpdateItem::TYPE::General == mData[i]->type() ){
            ECUUpdateItem_General* item = (ECUUpdateItem_General*)mData[i];
            QByteArray data = item->cmd();
            if( data == udsCmd ){
                item->updateMatchData( desc, words, bWholeWordMatch, bEqual );
                break;
            }
        }
    }
}

QVariant ECUUpdateModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal){
        switch (role){
        case Qt::DisplayRole:
            return mHeader[section];
        case Qt::TextAlignmentRole:
            return  Qt::AlignLeft;
        case Qt::SizeHintRole:
            break;
        }
    }
    else{
        switch (role){
        case Qt::DisplayRole:
            return section;
        case Qt::SizeHintRole:
            break;
        }
    }

    return QAbstractTableModel::headerData(section, orientation, role);
}

int ECUUpdateModel::rowCount(const QModelIndex &parent) const
{
    return mData.size();
}

int ECUUpdateModel::columnCount(const QModelIndex &parent) const
{
    return mHeader.size();
}

QVariant ECUUpdateModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid())
            return QVariant();

    int nRow = index.row();
    int nColumn = index.column();

    switch (role){
    case Qt::DisplayRole:{
            switch( nColumn ){
            case 0:
                return mData[nRow]->describeStr();
            case 1:
                return mData[nRow]->dataStr();
            case 2:
                return mData[nRow]->abnormalStr();
            }
        }
        break;
    case Qt::TextColorRole:{
            if( mData[nRow]->isAbnormal() ){
                return QColor( Qt::red );
            }
        }
        break;
    case Qt::BackgroundColorRole:{
            if( mData[nRow]->isRuned() ){
                if( !mData[nRow]->runResult() ){
                    return QColor( Qt::red );
                }else{
                    return QColor( Qt::green );
                }
            }
        }
        break;
    default:
        return QVariant();
    }
    return QVariant();
}

bool ECUUpdateModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    return true;
}

Qt::ItemFlags ECUUpdateModel::flags(const QModelIndex &index) const
{
    return QAbstractTableModel::flags(index);
}

void ECUUpdateModel::clear()
{
    beginResetModel();
    for( int i=0; i<mData.size(); i++ ){
        delete mData[i];
    }
    mData.clear();
    endResetModel();
}

void ECUUpdateModel::updateAddrs(quint32 phyAddr, quint32 funAddr, quint32 dstAddr)
{
    for( int i=0; i<mData.size(); i++ ){
        mData[i]->setDstAddr( dstAddr );
        mData[i]->setPhyAddr( phyAddr );
        mData[i]->setFunAddr( funAddr );
    }
}

bool ECUUpdateModel::run(quint8 channelIndex)
{
    bool bRet = true;
    for( int i=0; i<mData.size(); i++ ){
        beginResetModel();
        emit running( i, channelIndex );
        bRet = mData[i]->run( channelIndex );
        endResetModel();
        if( !bRet ){
            break;
        }
    }

    //刷新显示界面
    beginResetModel();
    endResetModel();

    return bRet;
}

bool ECUUpdateModel::isAbnormal()
{
    for( int i=0; i<mData.size(); i++ ){
        if( mData[i]->isAbnormal() ){
            return true;
        }
    }
    return false;
}

void ECUUpdateModel::initRunResult()
{
    beginResetModel();

    for( int i=0; i<mData.size(); i++ ){
        mData[i]->initRunResult();
    }

    endResetModel();
}
