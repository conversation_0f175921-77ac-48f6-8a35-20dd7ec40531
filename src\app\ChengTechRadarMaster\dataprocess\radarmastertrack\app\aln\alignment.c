/*
 * @Author: fan<PERSON> <PERSON><PERSON><PERSON>
 * @Date: 2022-04-15 14:47:51
 * @LastEditTime: 2025-03-21 17:59:34
 * @LastEditors: <PERSON>can
 * @Description: 雷达标定校准
 * @FilePath: alignment.c
 * Copyright (C) 2021 Chengtech Ltd.
 */
#ifndef PC_DBG_FW
#include "alignment.h"
#include "sharedVar.h"
#include "cfg.h"
#include "radardsp.h"
#include "app_can_msg.h"
#include "diagnostic.h"
#include "app_vehicle.h"
#include "flash_mmap.h"
#include "crclib.h"
#include "flash_if.h"
#include "app_diag.h"
#include "target_proc.h"
#include "sys_status.h"
#include "sortlib.h"
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "pow_fast.h"
#include "cfg.h"
#include "aln_install_cfg.h"
#include "aln_autoEolFun.h"
#else
#include "app/system_mgr/typedefs.h"
#include "app/include/sharedVar.h"
#include "app/protocol/prot_pcan_app.h"
#include "app/aln/alignment.h"
#include "app/aln/aln_staticEolFun.h"
#include "app/aln/aln_dynamicEolFun.h"
#include "app/aln/aln_autoEolFun.h"
#include "app/aln/aln_type.h" 
#endif 

#define ALIGN_DATA_FLAG             0x43414C49U
#define DATA_CALIB_ALIGNMENT_FLAG   0xAA55U  //旧的安装标定完成标志

#define AUTO_CALIB_NULL_STATUS_FLASG    0x00U     //认为是第一次上电，不产生DTC
#define AUTO_CALIB_SUCCESS_STATUS_FLASG 0x5A4BU   //当前自标定成功，不产生DTC
#define AUTO_CALIB_FAIL_STATUS_FLASG    0x4BA5U   //当前自标定失败，产生DTC

/* Alignment info */
#ifndef TI_ADAS
#define FLASH_INSTALL_CALIB_BASE_ADDR       (0x202000)//(0x203000)
#endif
#define FLASH_INSTALL_CALIB_SIZE            (4 << 10)   // 4K
/* Alignment info backup */
#ifndef TI_ADAS
#define FLASH_INSTALL_CALIB_BACKUP_BASE_ADDR (0x203000)//(0x204000)
#define FLASH_INSTALL_CALIB_BACKUP_SIZE     (4 << 11)   // 1K//(4 << 10)   // 4K
#endif
/* Auto Calib info */
#define FLASH_AUTO_CALIB_BASE_ADDR       (0x1E7000)
#define FLASH_AUTO_CALIB_SIZE            (4 << 10)   // 4K

#define SERVICE_CALI_VALUE_FLAG     (uint32_t)(0x5A5A5A5A)

// ----------------MRR-----------------

static stAlignInfo          gAlignData;
static stAutoAlignInfo      gAutoAlignData;         //自标定信息
static stAutoAlignCntCurrent gAutoAlignCnt = {0,0}; //当前每次小自标定次数
static radarInstallInfo     gRadarInstallInfo;      //下线信息
static radarCaliInfo        gRadarCaliInfo;         //服务标定信息
static radarInstallCalcInfo gRadarInstallCalcInfo;  //下线标定信息
static radarInstallCalcCfg  gRadarInstallCalcCfg;   //下线标定配置
static radarServiceCaliInfo gServiceCaliInfo;       //服务标定信息

// float getTotalAngleOffset(void)
// {
//     return (radar_config_using->installHorizontalAngleOffset /* + radar_config_using->angleOffset*/);
// }
void ALN_syncAlign2ALNServiceInfo(CT_EOL_DCR_t *ptrALNServiceInfo)
{
    ptrALNServiceInfo->service_count = gAlignData.installAlignCnt;
}

void ALN_syncStaticALNService2AlignInfo(const CT_EOL_DCR_t *ptrALNServiceInfo)
{
    gAlignData.installAlignCnt = ptrALNServiceInfo->service_count;
    gAlignData.installHrzAngle = ptrALNServiceInfo->new_fix_angle;

    if (ptrALNServiceInfo->run_status == CALC_SUCCESS_END)
    {
        gAlignData.installAlignFlag = DATA_CALIB_ALIGNMENT_FLAG;
        gAlignData.flag = ALIGN_DATA_FLAG;
    }
    else
    {
        gAlignData.installAlignFlag = 0u;
    }
}

void ALN_syncDynamicALNService2AlignInfo(const CT_DYNAMIC_EOL_INFO_t *ptrALNServiceInfo)
{
    gAlignData.serviceAlignCnt = ptrALNServiceInfo->service_count;
    gAlignData.serviceHrzAngle = ptrALNServiceInfo->new_fix_angle;

    if (ptrALNServiceInfo->run_status == CALC_SUCCESS_END)
    {
        gAlignData.serviceAlignFlag = DATA_CALIB_ALIGNMENT_FLAG;
        gAlignData.flag = ALIGN_DATA_FLAG;
    }
    else
    {
        gAlignData.serviceAlignFlag = 0u;
    }
}

void ALN_syncClearAlignInfo(void)
{
    ALN_clearAlignFlag();
}

bool ALN_isInstallAlignOK(void)
{
    return (gAlignData.installAlignFlag == DATA_CALIB_ALIGNMENT_FLAG) ? true : false;
}

bool ALN_isServiceAlignOK(void)
{
    return (gAlignData.serviceAlignFlag == DATA_CALIB_ALIGNMENT_FLAG) ? true : false;
}

bool ALN_isAlignDone(void)
{
    return (gAlignData.installAlignFlag == DATA_CALIB_ALIGNMENT_FLAG || gAlignData.serviceAlignFlag == DATA_CALIB_ALIGNMENT_FLAG) ? true : false;
}

bool ALN_isEverAlignment(void)
{
    return (gAlignData.installAlignCnt || gAlignData.serviceAlignCnt) ? true : false;
}

/**
 * @brief 主分区数据备份
 *
 * @param pBuf 数据
 * @param len 数据长度
 */
static void ALN_saveCfgToBackupArea(const uint8_t *pBuf, uint32_t len)
{
#ifndef PC_DBG_FW
    Flash_eraseByte(FLASH_INSTALL_CALIB_BACKUP_BASE_ADDR, FLASH_INSTALL_CALIB_BACKUP_SIZE);
    int ret = Flash_writeByte(FLASH_INSTALL_CALIB_BACKUP_BASE_ADDR, (uint8_t *)pBuf, len);
    if (ret != 0)
    {
        // EMBARC_PRINTF("write alignment backup err\r\n");
    }
#endif // !PC_DBG_FW 
}

/**
 * @brief 数据保存到主分区
 *
 * @param pBuf 数据
 * @param len 数据长度
 */
static void ALN_saveCfgToMainArea(const uint8_t *pBuf, uint32_t len)
{ 
#ifndef PC_DBG_FW
    Flash_eraseByte(FLASH_INSTALL_CALIB_BASE_ADDR, FLASH_INSTALL_CALIB_SIZE);
    int ret = Flash_writeByte(FLASH_INSTALL_CALIB_BASE_ADDR, (uint8_t *)pBuf, len);
    if (ret != 0)
    {
        // EMBARC_PRINTF("write alignment main err\r\n");
    }
#endif // !PC_DBG_FW
}

/**
 * @brief 自标定数据保存到主分区（无备份区）
 *
 * @param pBuf 数据
 * @param len 数据长度
 */
static void ALN_saveAutoInfoToMainArea(const uint8_t *pBuf, uint32_t len)
{ 
#ifndef PC_DBG_FW
    Flash_eraseByte(FLASH_AUTO_CALIB_BASE_ADDR, FLASH_AUTO_CALIB_SIZE);
    int ret = Flash_writeByte(FLASH_AUTO_CALIB_BASE_ADDR, (uint8_t *)pBuf, len);
    if (ret != 0)
    {
        EMBARC_PRINTF("write auto alignment main err!!!\r\n");
    }
#endif // !PC_DBG_FW
}

/**
 * @brief 自标定 配置有效性检测和是否恢复
 *
 * @param Cfg_Is_Ok 主配置有效，返回1
 * @param Restore_En true 恢复启用使能
 * @return true 恢复成功
 * @return false 恢复失败，无有效参数
 */
static bool ALN_autoCfgCheckAndRestore(uint8_t *Cfg_Is_Ok,  bool Restore_En)
{
     *Cfg_Is_Ok = 1;

    uint32_t crc0 = 0;
    int ret = 0;

    /* 检测是否使能恢复 */
    if (false == Restore_En)
    {
        return true;
    }

     /* 检查分区 */
    gAutoAlignData.crc = 0xFFFFFFFF;
#ifndef PC_DBG_FW
    ret = Flash_readByte(FLASH_AUTO_CALIB_BASE_ADDR, (uint8_t *)&gAutoAlignData, sizeof(gAutoAlignData));
#endif // !PC_DBG_FW
    if (ret != 0)
    {
        *Cfg_Is_Ok = 0;
        goto _Check_AUTOCFG_Restore;
    }

#ifndef PC_DBG_FW
    crc0 = CRC_updateCrc32(0, (char *)&gAutoAlignData, sizeof(gAutoAlignData) - 4);
#endif // !PC_DBG_FW 
    if (crc0 != gAutoAlignData.crc)
    {
        *Cfg_Is_Ok = 0;
    }
    //EMBARC_PRINTF("*Cfg_Is_Ok %d, crc = 0x%X, gAutoAlignData.crc = 0x%X \r\n",*Cfg_Is_Ok, crc0, gAutoAlignData.crc);

_Check_AUTOCFG_Restore:
    /* 参数无效使用默认配置 */
    if (0 == (*Cfg_Is_Ok))
    {
#ifdef FUNC_SAFETY
        // TODO: 走到这儿说明 自标定配置校验失败了 2025-2-12
        // setFlashCrcError(FlashInstallCALIError);
#endif
#ifndef PC_DBG_FW
        // EMBARC_PRINTF("check auto alignment cfg useless, gAutoAlignData.crc = 0x%X, autoAlignCnt:0x%X 0x%X 0x%X\n", 
        //     gAutoAlignData.crc, gAutoAlignData.autoAlignCntSucc, gAutoAlignData.autoAlignCntFail, gAutoAlignData.autoAlignCntFlash);
        if (gAutoAlignData.crc == 0xFFFFFFFF)
        {
            gAutoAlignData.autoHrzAngle = CFG_getRadarInstallAngle();
            EMBARC_PRINTF("CFG_getRadarInstallAngle() = %.2f\r\n", CFG_getRadarInstallAngle());
            gAutoAlignData.autoElvAngle = 0.0f; // TODO pRadarCfg->installPitchAngleOffset 2025-2-12

            //认为是第一次上电，并且从没有自标定的结果产生，所以不需要生产DTC。
            gAutoAlignData.autoAlignFlag = AUTO_CALIB_NULL_STATUS_FLASG;
            gAutoAlignData.autoAlignCntSucc  = 0U;
            gAutoAlignData.autoAlignCntFail  = 0U;
            gAutoAlignData.autoAlignCntFlash = 0U;
        }
        else
        {
            memset(&gAutoAlignData, 0, sizeof(gAutoAlignData));
        }
#endif
        return false;
    }

    return true;
}

/**
 * @brief 配置有效性检测和是否恢复
 *
 * @param Cfg_Is_Ok 主配置有效，返回1
 * @param Backup_Cfg_Is_Ok 备份配置有效，返回1
 * @param Restore_En true 恢复启用使能
 * @return true 恢复成功
 * @return false 恢复失败，无有效参数
 */
static bool ALN_cfgCheckAndRestore(uint8_t *Cfg_Is_Ok, uint8_t *Backup_Cfg_Is_Ok, bool Restore_En)
{
#ifndef PC_DBG_FW
    *Cfg_Is_Ok = 1;
    *Backup_Cfg_Is_Ok = 1;

    uint32_t crc0 = 0;
    uint32_t crc1 = 0;
    int ret = 0;

    /* 检查主分区 */
    gAlignData.crc = 0xFFFFFFFF; 
    ret = Flash_readByte(FLASH_INSTALL_CALIB_BASE_ADDR, (uint8_t *)&gAlignData, sizeof(gAlignData)); 
    if (ret != 0)
    {
        *Cfg_Is_Ok = 0;
        goto _Read_CFG_Backup;
    }

    crc0 = CRC_updateCrc32(0, (char *)&gAlignData, sizeof(gAlignData) - 4);
    if (crc0 != gAlignData.crc)
    {
        *Cfg_Is_Ok = 0;
        goto _Read_CFG_Backup;
    }

_Read_CFG_Backup:

    /* 检查备份分区 */
    gAlignData.crc = 0xFFFFFFFF; 
    ret = Flash_readByte(FLASH_INSTALL_CALIB_BACKUP_BASE_ADDR, (uint8_t *)&gAlignData, sizeof(gAlignData)); 
    if (ret != 0)
    {
        *Backup_Cfg_Is_Ok = 0;
        goto _Check_CFG_Restore;
    }
 
    crc1 = CRC_updateCrc32(0, (char *)&gAlignData, sizeof(gAlignData) - 4); 
    if (crc1 != gAlignData.crc)
    {
        *Backup_Cfg_Is_Ok = 0;
    }

_Check_CFG_Restore:
    /* 参数无效使用默认配置 */
    if (0 == (*Cfg_Is_Ok) && 0 == (*Backup_Cfg_Is_Ok))
    {
#ifdef FUNC_SAFETY
        // 走到这儿说明 配置校验失败了
        setFlashCrcError(FlashInstallCALIError);
#endif
        // EMBARC_PRINTF("check alignment cfg err\n");
        if (gAlignData.crc == 0xFFFFFFFF)
        {
            const radar_config_t *pRadarCfg = CFG_getRadarCfg();
            if (gAlignData.installAlignFlag == DATA_CALIB_ALIGNMENT_FLAG)
            {
                gAlignData.installHrzAngle = pRadarCfg->installHorizontalAngleOffset;
                gAlignData.installElvAngle = pRadarCfg->installPitchAngleOffset;
            }
            else if (gAlignData.serviceAlignFlag == DATA_CALIB_ALIGNMENT_FLAG)
            {
                gAlignData.serviceHrzAngle = pRadarCfg->installHorizontalAngleOffset;
                gAlignData.serviceElvAngle = pRadarCfg->installPitchAngleOffset;
            }

            if (gAlignData.installAlignCnt == 0xFFFF)
            {
                gAlignData.installAlignCnt = 0;
            }

            if (gAlignData.serviceAlignCnt == 0xFFFF)
            {
                gAlignData.serviceAlignCnt = 0;
            }
        }
        else
        {
            memset(&gAlignData, 0, sizeof(gAlignData));
        }

        ALN_syncAlign2ALNServiceInfo(ALN_getStaticEolAlnInfo());

        return false;
    }

    /* 检测是否使能恢复 */
    if (false == Restore_En)
    {
        return true;
    }

    /* 检测主分区与备份是否有效，相互恢复 */
    if (1 == (*Cfg_Is_Ok) && 1 == (*Backup_Cfg_Is_Ok))
    {
      /* 检测主分区与备份分区是否一致，否则优先使用主分区并触发备份 */
      if (crc0 != crc1)
      {
          Flash_readByte(FLASH_INSTALL_CALIB_BASE_ADDR, (uint8_t *)&gAlignData, sizeof(gAlignData));
          ALN_saveCfgToBackupArea((const uint8_t *)&gAlignData, sizeof(gAlignData));
          // EMBARC_PRINTF("replace backup alignment cfg\n");
      }
      else
      {
            EMBARC_PRINTF("Alignment CFG Check Ok\r\n");
      }
    }
    else
    {
        /* 主分区有效，备份分区无效 */
        if (1 == (*Cfg_Is_Ok) && 0 == (*Backup_Cfg_Is_Ok))
        { 
            Flash_readByte(FLASH_INSTALL_CALIB_BASE_ADDR, (uint8_t *)&gAlignData, sizeof(gAlignData));
            ALN_saveCfgToBackupArea((const uint8_t *)&gAlignData, sizeof(gAlignData));
            // EMBARC_PRINTF("restore backup alignment cfg\n"); 
        }
        /* 主分区无效，备份分区有效 */
        if (0 == (*Cfg_Is_Ok) && 1 == (*Backup_Cfg_Is_Ok))
        {
            Flash_readByte(FLASH_INSTALL_CALIB_BACKUP_BASE_ADDR, (uint8_t *)&gAlignData, sizeof(gAlignData));
            ALN_saveCfgToMainArea((const uint8_t *)&gAlignData, sizeof(gAlignData));
        }
    }

    /* 恢复 */
    if (gAlignData.installAlignFlag == DATA_CALIB_ALIGNMENT_FLAG)
    { 
        CFG_setRadarInstallAngle(gAlignData.installHrzAngle);
        CFG_setRadarElevatedOffset(gAlignData.installElvAngle); 
        
    }

    else if (gAlignData.serviceAlignFlag == DATA_CALIB_ALIGNMENT_FLAG)
    { 
        CFG_setRadarInstallAngle(gAlignData.serviceHrzAngle);
        CFG_setRadarElevatedOffset(gAlignData.serviceElvAngle); 
    }
    ALN_syncAlign2ALNServiceInfo(ALN_getStaticEolAlnInfo());
#endif

    return true;
}

/**
 * @brief 下线标定数据有效性检测
 *
 * @param Cfg_Is_Ok 主配置有效，返回1
 * @param Backup_Cfg_Is_Ok 备份配置有效，返回1
 */
void ALN_dataCheck(uint8_t *Cfg_Is_Ok, uint8_t *Backup_Cfg_Is_Ok)
{
    ALN_cfgCheckAndRestore(Cfg_Is_Ok, Backup_Cfg_Is_Ok, false);
}

int32_t ALN_clearAlignFlag(void)
{
    int32_t retVal = 0;

    gAlignData.installAlignFlag = 0;
    gAlignData.serviceAlignFlag = 0;
#ifndef PC_DBG_FW
    gAlignData.crc = CRC_updateCrc32(0, (char *)&gAlignData, sizeof(gAlignData) - 4);
#endif // !PC_DBG_FW

    /* 保存到主分区 */
    ALN_saveCfgToMainArea((const uint8_t *)&gAlignData, sizeof(gAlignData));

    //同时也清楚自标定的缓存和flash的相关数据 by 大山 2025-3-18
    ALN_clearAutoAlignmentData();

    return retVal;
}

void ALN_initSelfCaliInfo(void)
{
    //自校准默认配置
    /*
    gRadarCaliInfo.cfg.leastSquareThr = 91;     // 0.93 最小二乘法相关性
    gRadarCaliInfo.cfg.cycleThr       = 10;     //一个校准周期需要稳定多少帧，默认10
    gRadarCaliInfo.cfg.caliThr        = 4;      //总共校准到多少个周期
    gRadarCaliInfo.cfg.FrameAngleThr  = 5;      //相邻帧角度跳动范围 0.3
    gRadarCaliInfo.cfg.cycleAngleThr  = 20;     //校准周期间的角度跳动范围 2度
    gRadarCaliInfo.cfg.sendFlag.word  = 0x1;    //默认不上报较准结果
    */

    gRadarCaliInfo.cfg.stdThr             = 100;    //均方差门限
    gRadarCaliInfo.cfg.cycleWaitThr       = 10;     //计算等待门限 10 一个计算周期间隔帧
    gRadarCaliInfo.cfg.caliCntThr         = 15;     //几个计算周期算一次校准 //默认 10次
    gRadarCaliInfo.cfg.caliObjNumberThr   = 15;     //参与计算的目标数量门限
    gRadarCaliInfo.cfg.caliAngleBeatThr   = 6;      //角度跳动门限
    gRadarCaliInfo.cfg.canCaliMinAngleThr = 2;      //允许标定的最小角度门限
    gRadarCaliInfo.cfg.sendFlag.word      = 0x1;
#ifndef PC_DBG_FW
    gRadarCaliInfo.calcHorizontalAngle    = CFG_getRadarAngleOffset();
#else
    gRadarCaliInfo.calcHorizontalAngle    = radar_config_using->installHorizontalAngleOffset;
#endif
}

void ALN_initInstallInfo(void)
{
#ifndef PC_DBG_FW
    const radar_config_t *pRadarCfg = CFG_getRadarCfg();

    //安装信息
    gRadarInstallInfo.height           = pRadarCfg->installHeight;
    gRadarInstallInfo.horizontalAngle  = pRadarCfg->installHorizontalAngleOffset;
    gRadarInstallInfo.horizontalOffset = 0; // pRadarCfg->horizontalOffset;
    gRadarInstallInfo.verticalAngle_y  = 0;
    gRadarInstallInfo.verticalAngle_x  = 0;
    #endif
}

void ALN_initInstallCali(void)
{
#ifndef PC_DBG_FW
    const radar_config_t *pRadarCfg = CFG_getRadarCfg();

    // 安装标定
    /* Intall calibration information */
    gRadarInstallCalcInfo.calcInstallAngle = pRadarCfg->installHorizontalAngleOffset;
    gRadarInstallCalcInfo.calcState        = pRadarCfg->installCalcState;
    gRadarInstallCalcInfo.calcCycleCnt   = 0;
    gRadarInstallCalcInfo.debugFlag.word = 0;
    memset(&(gRadarInstallCalcInfo.obj), 0, sizeof(gRadarInstallCalcInfo.obj));
    gRadarInstallCalcInfo.obj.idx        = -1; //无效
    gRadarInstallCalcInfo.traClearCnt    = 0;	//跟踪清除统计

    /* Intall calibration config */
    gRadarInstallCalcCfg.calcObjAngle    = 0;
    gRadarInstallCalcCfg.objAngleSearchThreshold = 10; //角度搜索范围 ±10度
    gRadarInstallCalcCfg.calcObjRange    = 1.22;
    gRadarInstallCalcCfg.objRangSearchThreshold  = 0.5;
//    gRadarInstallCalcCfg.histAngleThreshold = 0.2;
    gRadarInstallCalcCfg.calcMaxAngle    = 3; //±3度
    gRadarInstallCalcCfg.calcEn          = 0;
//    gRadarInstallCalcCfg.cohesionEn = 1;//默认启用聚类
    gRadarInstallCalcCfg.sendFlag.word   = 0;//默认不上报0x42n
    gRadarInstallCalcCfg.waitFrame       = 3;
//    gRadarInstallCalcCfg.timeOutFrame = 100;
//    gRadarInstallCalcCfg.invalidObjMax = 10;
//    gRadarInstallCalcCfg.histMaxNumber = 7;
//    gRadarInstallCalcCfg.TxSel = 1; //默认只使用天线1的数据校准
//    gRadarInstallCalcCfg.UsingTxSel = gRadarInstallCalcCfg.TxSel; //正在使用的Tx
    gRadarInstallCalcCfg.timeOutThr      = 1000; //1s
#endif
}

void ALN_initSrvCaliInfo(void)
{
    //服务校准相关初始化--从Flash中读取
    //readServiceCaliInfoFromFlash(&gServiceCaliInfo);

    gServiceCaliInfo.magicFlag           = SERVICE_CALI_VALUE_FLAG;
    gServiceCaliInfo.calcHorizontalAngle = CFG_getRadarInstallAngle();
    gServiceCaliInfo.calcVerticalAngle   = 0;
    gServiceCaliInfo.cfg.timeOutThr      = 1000 * 60 * 15; //15分钟
    gServiceCaliInfo.warningInfo.word    = 0;
    gServiceCaliInfo.calcState           = SERVICE_CALC_INIT;
    gServiceCaliInfo.enable              = 0;
    gServiceCaliInfo.crc                 = 0;
}

const radarCaliInfo *ALN_getSelfCaliInfo(void)
{
    return &gRadarCaliInfo;
}

const radarInstallInfo *ALN_getInstallInfo(void)
{
    return &gRadarInstallInfo;
}

const radarInstallCalcInfo *ALN_getInstallCaliInfo(void)
{
    return &gRadarInstallCalcInfo;
}

const radarInstallCalcCfg *ALN_getInstallCaliCfg(void)
{
    return &gRadarInstallCalcCfg;
}

float ALN_getAutoHrzAngle(void)
{
#if (1U == AUTO_ALIGNMENT_EN)
    return gAutoAlignData.autoHrzAngle;
#else
    return 0.0f;
#endif
}

void ALN_setAutoHrzAngle(const float autoHrzAngle)
{
    gAutoAlignData.autoHrzAngle = autoHrzAngle;
}

bool ALN_getAutoAlignFlag(void)
{
    bool retVal = true;
    if (ADAS_getAutoEolCtrl_IsErr() != -1)
    {
        //完成 CALIB_RUN_MAX 次自标定后得到结果才更新 autoAlignFlag
        switch (ADAS_getAutoEolCtrl_IsErr())
        {
            case 0x00:
            case 0x01:
                gAutoAlignData.autoAlignFlag = AUTO_CALIB_SUCCESS_STATUS_FLASG;
                break;
            case 0x02:
                gAutoAlignData.autoAlignFlag = AUTO_CALIB_FAIL_STATUS_FLASG;
                break;
            default:
                break;
        }
    }

#if (1U == AUTO_ALIGNMENT_EN)
    //实时 DTC
    switch (gAutoAlignData.autoAlignFlag)
    {
        case AUTO_CALIB_FAIL_STATUS_FLASG:
            retVal =  false;
            break;
        default:
            retVal =  true;
            break;
    } 
#else
    
#endif
    return retVal;
}

uint16_t ALN_getAutoAlignCntOfFlash(uint8_t whichCnt)
{
    uint16_t tempCnt = 0U;
    switch (whichCnt)
    {
    case 0:
        tempCnt = gAutoAlignData.autoAlignCntSucc;
        break;
    case 1:
        tempCnt = gAutoAlignData.autoAlignCntFail;
        break;
    case 2:
        tempCnt = gAutoAlignData.autoAlignCntFlash;
        break;
    default:
        break;
    }

    return tempCnt;
}

uint8_t ALN_getAutoAlignCntOfReadTime(uint8_t whichCnt)
{
    uint8_t tempCnt = 0U;
    switch (whichCnt)
    {
    case 0:
        tempCnt = gAutoAlignCnt.autoAlignCntSucc;
        break;
    case 1:
        tempCnt = gAutoAlignCnt.autoAlignCntFail;
        break;
    default:
        break;
    }

    return tempCnt;
}

/**
 * @Description: 单次自标定成功或失败计数(实时计数)
 * @Version: 1.0
 * @Autor: mo yican
 * @Date: 2025-02-14 14:27:29
 * @LastEditors: mo yican
 * @LastEditTime: Do not edit
 * @param {uint8_t} autoAlignFlag 0:失败计数一次 1:成功计数一次 other:不计
 */
void ALN_updateAutoAlignCnt(uint8_t autoAlignFlag)
{
    switch (autoAlignFlag)
    {
        case 0x0:
            if (gAutoAlignCnt.autoAlignCntFail < 0xE)
            {
                gAutoAlignCnt.autoAlignCntFail++;
            }
            else
            {
                gAutoAlignCnt.autoAlignCntFail = 1U;
            }
            break;
        case 0x1:
            if (gAutoAlignCnt.autoAlignCntSucc < 0xE)
            {
                gAutoAlignCnt.autoAlignCntSucc++;
            }
            else
            {
                gAutoAlignCnt.autoAlignCntSucc = 1U;
            }
            break;
        default:
            break;
    }
}

void ALN_resetInstallCaliStatus(void)
{
    gRadarInstallCalcInfo.calcInstallAngle = 0;
    gRadarInstallCalcInfo.calcState = 0;

#ifndef PC_DBG_FW
    CFG_setRadarInstallCaliStatus(0);
#endif // !PC_DBG_FW
}

const radarServiceCaliInfo *ALN_getSrvCaliInfo(void)
{
    return &gServiceCaliInfo;
}

void ALN_resetSrvCaliStatus(void)
{
    gServiceCaliInfo.calcState           = 0;
    gServiceCaliInfo.calcHorizontalAngle = 0;
    gServiceCaliInfo.calcVerticalAngle   = 0;
}

/**
 *  @b Description
 *  @n
 *      The function is used to load the calibration data from flash.
 *
 *  @retval
 *      Success -   0
 *  @retval
 *      Error   -   <0
 */
int32_t ALN_loadAlignmentData(void)
{
    uint8_t Cfg_Is_Ok, Backup_Cfg_Is_Ok;

    ALN_cfgCheckAndRestore(&Cfg_Is_Ok, &Backup_Cfg_Is_Ok, true);

#if (1U == AUTO_ALIGNMENT_EN)
    uint8_t AutoCfg_Is_Ok;
    ALN_autoCfgCheckAndRestore(&AutoCfg_Is_Ok, true);
#endif

    if (1U == Cfg_Is_Ok || 1U == Backup_Cfg_Is_Ok)
    {
        return 0;
     }

    return -1;
}

void ALN_clearAutoAlignmentData(void)
{
    memset(&gAutoAlignData, 0, sizeof(gAutoAlignData));
    gAutoAlignData.crc = 0xFFFFFFFF;

    memset(&gAutoAlignCnt, 0, sizeof(gAutoAlignCnt));

    // 清除当前每次小自标定的计数数据
    ALN_clearAutoAlignCnt();

    // 初始化ADAS的自标定数据
    ADAS_resetAutoEolCtrlInitStatus();
    ADAS_initAutoEolCtrlStatus();

    // 存储进flash
    ALN_saveAutoInfoToMainArea((const uint8_t *)&gAutoAlignData, sizeof(gAutoAlignData));

    EMBARC_PRINTF("Clear and Save AutoAlignment data!!!\r\n");
}

void ALN_clearAutoAlignCnt(void)
{
    gAutoAlignCnt.autoAlignCntFail = 0;
    gAutoAlignCnt.autoAlignCntSucc = 0;
}

/**
 *  @b Description
 *  @n *      The function is used to save the auto calibration data to flash.
 *            2025-3-19：
 *            1、自标定角度0~5°：无DTC（可存储进flash），角度需要存储进flash。
 *            2、自标定角度>5°，生成DTC，DTC需要存储，角度（上一次的角度）需存储进flash。
 *  @retval
 *      Success -   0
 *  @retval
 *      Error   -   <0
 */
int32_t ALN_saveAutoAlignmentData(void)
{
    int32_t retVal = 0;
#ifndef PC_DBG_FW
    // 判断数据是否有效，有效才能存入flash
    if (ADAS_getAutoEolCtrlStatus() != -1)
    {
        //存储前也更新一次 autoAlignFlag：用于判断是否有DTC
        switch (ADAS_getAutoEolCtrl_IsErr())
        {
            case 0x00:
            case 0x01:
                // 无DTC，更新角度存储进flash
                gAutoAlignData.autoAlignFlag = AUTO_CALIB_SUCCESS_STATUS_FLASG;
                gAutoAlignData.autoHrzAngle = ALN_getAutoHrzAngle();
                break;
            case 0x02:
                // 生成DTC，不更新角度，存储进flash
                gAutoAlignData.autoAlignFlag = AUTO_CALIB_FAIL_STATUS_FLASG;
                break;
            default:
                break;
        }

        if (gAutoAlignData.autoAlignCntFlash < 0xFFFE)
        {
            gAutoAlignData.autoAlignCntFlash++;
        }
        else
        {
            gAutoAlignData.autoAlignCntFlash = 1;
        }

        gAutoAlignData.autoAlignCntFail += ALN_getAutoAlignCntOfReadTime(1);
        gAutoAlignData.autoAlignCntSucc += ALN_getAutoAlignCntOfReadTime(0);

        ADAS_clearAutoEolCtrlStatus_PowerOnOff();
        ALN_clearAutoAlignCnt();

        gAutoAlignData.flag = ALIGN_DATA_FLAG;
        gAutoAlignData.crc  = CRC_updateCrc32(0, (char *)&gAutoAlignData, sizeof(gAutoAlignData) - 4);
        // EMBARC_PRINTF("ALN_saveAutoAlignmentData: gAutoAlignData.crc = 0x%X\r\n", gAutoAlignData.crc);
        // EMBARC_PRINTF("CNT 0x%X, 0x%X 0x%X\n",gAutoAlignData.autoAlignCntSucc, gAutoAlignData.autoAlignCntFail, gAutoAlignData.autoAlignCntFlash);

        ALN_saveAutoInfoToMainArea((const uint8_t *)&gAutoAlignData, sizeof(gAutoAlignData));

    }
    else
    {
        ADAS_clearAutoEolCtrlStatus_PowerOnOff();
        ALN_clearAutoAlignCnt();
        retVal = -1;
    }
#endif
    return retVal;
}

/**
 *  @b Description
 *  @n *      The function is used to save the intall calibration data to flash.
 *
 *  @param[in]  type
 *      The type of calibration.
 *
 *  @retval
 *      Success -   0
 *  @retval
 *      Error   -   <0
 */
int32_t ALN_saveAlignmentData(E_CALI_TYPE type)
{
    int32_t retVal = 0;
#ifndef PC_DBG_FW
    const radar_config_t *pRadarCfg = CFG_getRadarCfg();

    if (type == INSTALL_CALI)
    {
        gAlignData.installHrzAngle  = pRadarCfg->installHorizontalAngleOffset;
        gAlignData.installElvAngle  = pRadarCfg->installPitchAngleOffset;
        gAlignData.installAlignFlag = DATA_CALIB_ALIGNMENT_FLAG;
        if (gAlignData.installAlignCnt < 65534)
        {
            gAlignData.installAlignCnt++;
        }
    }
    else if (type == SERVICE_CALI)
    {
        gAlignData.serviceHrzAngle  = pRadarCfg->installHorizontalAngleOffset;
        gAlignData.serviceElvAngle  = pRadarCfg->installPitchAngleOffset; //暂时不将俯仰标定结果写入flash
        gAlignData.serviceAlignFlag = DATA_CALIB_ALIGNMENT_FLAG;
        if (gAlignData.serviceAlignCnt < 65534)
        {
            gAlignData.serviceAlignCnt++;
        }
    }

    gAlignData.flag = ALIGN_DATA_FLAG;
    gAlignData.crc  = CRC_updateCrc32(0, (char *)&gAlignData, sizeof(gAlignData) - 4);

    /* 保存到主分区 */
    ALN_saveCfgToMainArea((const uint8_t *)&gAlignData, sizeof(gAlignData));

    /* 保存到备份分区 */
    ALN_saveCfgToBackupArea((const uint8_t *)&gAlignData, sizeof(gAlignData));
#endif // !PC_DBG_FW

    return retVal;
}

/**
 *  @b Description
 *  @n
 *      Initialize the alignment module.
 *
 *  @retval
 *      Success -   0
 *  @retval
 *      Error   -   <0
 */
int32_t ALN_initAlignment(void)
{
    int32_t retVal = 0;
    uint8_t eolFlag = 0;

    eolFlag = (gAlignData.installAlignFlag == DATA_CALIB_ALIGNMENT_FLAG) ? 1 : ((gAlignData.serviceAlignFlag == DATA_CALIB_ALIGNMENT_FLAG) ? 2 : 0); 

    retVal = ALN_loadAlignmentData();

    ALN_initSelfCaliInfo();

    ALN_initInstallInfo();

    ALN_initInstallCali();

    ALN_initSrvCaliInfo();

    setUnAlignInstallAngle();

#ifndef PC_DBG_FW
    setDetermine2useFlag(eolFlag);
#endif

    return retVal;
}

// LRR 适配
const radarInstallCalcCfg *getInstallCaliCfg(void)
{
    return &gRadarInstallCalcCfg;
}

void setInstallCalcAngle(enum INSTALL_CFG type, float angle)
{
	(void)type;
	(void)angle;
}

void setInstallInfo(enum INSTALL_CFG_E type, float value)
{
	(void)type;
	(void)value;
}

/**
 *  @b Description
 *  @n
 *      if not align set vechcile installHorizontalAngleOffset
 *
 *  @retval
 *      Success -   0
 *  @retval
 *      Error   -   <0
 */

int32_t setUnAlignInstallAngle(void)
{
#ifndef PC_DBG_FW
    // 如果没有校准过，使用默认角度操作
    if (ALN_isAlignDone() == 0)
    {
        switch (CFG_getRadarId())
        {
#if ( defined(VEHICLE_TYPE_BYD_HA5) || defined(VEHICLE_TYPE_BYD_EM2) || defined(VEHICLE_TYPE_BYD_UR) )
            case RADAR_ID_REAR_LEFT:    // 4
            {
                CFG_setRadarInstallAngle(get_install_message()->aln_execpt_angle_rcr);
                break;
            }
            case RADAR_ID_REAR_RIGHT:    // 5
            {
                CFG_setRadarInstallAngle(get_install_message()->aln_execpt_angle_rcr);
                break;
            }
            case RADAR_ID_FRONT_LEFT:    // 6
            {
                CFG_setRadarInstallAngle(get_install_message()->aln_execpt_angle_fcr);
                break;
            }
            case RADAR_ID_FRONT_RIGHT:    // 7
            {
                CFG_setRadarInstallAngle(get_install_message()->aln_execpt_angle_fcr);
                break;
            }
            default:
            {
                CFG_setRadarInstallAngle(get_install_message()->aln_execpt_angle_fcr);
                break;
            }
#else            
            case RADAR_ID_REAR_LEFT:    // 4
            {
                CFG_setRadarInstallAngle(ALN_EXPECT_FIX_ANGLE_RCR);
                break;
            }
            case RADAR_ID_REAR_RIGHT:   // 5
            {
                CFG_setRadarInstallAngle(ALN_EXPECT_FIX_ANGLE_RCR);
                break;
            }
            case RADAR_ID_FRONT_LEFT:   // 6
            {
                CFG_setRadarInstallAngle(ALN_EXPECT_FIX_ANGLE_FCR);
                break;
            }
            case RADAR_ID_FRONT_RIGHT:  // 7
            {
                CFG_setRadarInstallAngle(ALN_EXPECT_FIX_ANGLE_FCR);
                break;
            }
            default:
            {
                CFG_setRadarInstallAngle(ALN_EXPECT_FIX_ANGLE_FCR);
                break;
            }
#endif
        }
    }
#endif // !PC_DBG_FW
    return 0;
}
#ifndef RADAR_AUTOSAR
#else
//autosar todo
void setServiceCaliEnabled(uint8_t enable)
{
    if(enable == 0)
    {
        Service31Handle_DynamicEol(02);
    }
    else
    {
        Service31Handle_DynamicEol(01);
    }
    
    gServiceCaliInfo.enable = enable;
}

uint8_t getServiceCaliEnabled(void)
{
    return gServiceCaliInfo.enable;
}

const radarInstallCalcInfo *getInstallCaliInfo(void)
{
    return &gRadarInstallCalcInfo;
}

const serviceCaliInfo *getSrvCaliInfo(void)
{
    return NULL;
}

uint8_t getSrvCaliDepthStatus(void)
{
    return 0;
}

void setInstallCaliEnable(uint8_t en)
{
    gRadarInstallCalcCfg.calcEn = en;
}

uint8_t getInstallCaliEnable(void)
{
    return gRadarInstallCalcCfg.calcEn;
}

void setInstallCaliStatus(uint8_t status)
{
    gRadarInstallCalcInfo.calcState = status;
}

void setSrvCaliStatus(uint8_t status)
{
    gServiceCaliInfo.calcState = status;
}

bool isAlignDone(void)
{
	return gAlignData.installAlignFlag == DATA_CALIB_ALIGNMENT_FLAG 
	       || gAlignData.serviceAlignFlag == DATA_CALIB_ALIGNMENT_FLAG;
}

bool isEverAlignment(void)
{
	return (gAlignData.installAlignCnt != 0U) || (gAlignData.serviceAlignCnt != 0U);
}
#endif

