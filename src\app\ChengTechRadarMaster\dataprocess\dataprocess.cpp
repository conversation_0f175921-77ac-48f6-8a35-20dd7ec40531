﻿#include "dataprocess.h"

#include "iinterpolation.h"
#include "utils/utils.h"

extern "C"{
#include "alg/track/rdp_clth_radar_lib.h"
#include "alg/track/rdp_interface.h"
#include "alg/track/rdp_kf_track.h"
#include "app/apar/apar_types.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/customizedrequirements/adas_signal_integration.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas_vehicle_ctrls.h"
#include "app/aln/ALN_DynamicEolFun.h"
#include "app/vehicle/vdy/vdy_interface.h"
#include "app/vehicle/vdy/vdy_types.h"
#include "other/pc_vr_mcu.h"
#include "other/temp.h"
}

#include <QApplication>
#include <QtGlobal>
#include <QThread>
#include <QTimer>
#include <QDateTime>
#include <qDebug>
#include <QtMath>
#include <QFile>
#include <QFileInfo>
#include <QDir>

static DataProcess *gDataProcessInstance = nullptr;

float gxoffset = 0.0;
float gyoffset = 0.0;
float groadlineoffset = 0.0;
uint32_t gFrameNb;
uint32_t gFrameTrackNb;
extern uint8_t gIsTurning;

extern const SlaveRadarWarningsStatus* get_LeftRadarWarnInfo(void); 

DataProcess *DataProcess::instance()
{
    if (!gDataProcessInstance)
    {
        gDataProcessInstance = new DataProcess;
    }

    return gDataProcessInstance;
}

DataProcess::DataProcess(QObject *parent)
    : QObject(parent),
      mSharedMemorySlaveRadar("ChengTechRadarMasterDataProcessSlaveRadar", this),
      mSharedMemoryInteractive("ChengTechRadarMasterDataProcessInteractive", this)
{

}

DataProcess::~DataProcess()
{

}

bool DataProcess::init(bool radarFusion, quint8 masterRadarID, quint8 sloveRadarID, quint8 Isorgpoint, quint8 IsShowCandiObj, quint8 eolmode)
{
    mMasterRadar = radarFusion;
    mMasterRadarID = masterRadarID;
    mSlaveRadarID = sloveRadarID;
    mIsorgpoint = Isorgpoint;
    mEolMode = eolmode;
    mIsShowCandiObj = IsShowCandiObj;
//    qDebug() << __FUNCTION__ << __LINE__ << radarFusion;
    if (radarFusion) {
        if (!mNetworkTcpServer) {
            mNetworkTcpServer = new NetworkTcpServer;
            QThread *thread = new QThread;
            connect(this, &DataProcess::openTCPServer, mNetworkTcpServer, &NetworkTcpServer::openTCPServer);
            connect(this, &DataProcess::write, mNetworkTcpServer, &NetworkTcpServer::write);
            connect(mNetworkTcpServer, &NetworkTcpServer::read, this, &DataProcess::read);

            mNetworkTcpServer->moveToThread(thread);

            thread->start();
        }
        QTimer::singleShot(100, this, [=](){ emit openTCPServer("127.0.0.1", 60001); });

    } else {
        if (!mNetworkTcpClient) {
            mNetworkTcpClient = new NetworkTcpClient;
            QThread *thread = new QThread;
            connect(this, &DataProcess::connectTCPServer, mNetworkTcpClient, &NetworkTcpClient::connectTCPServer);
            connect(this, &DataProcess::write, mNetworkTcpClient, &NetworkTcpClient::write);
            connect(mNetworkTcpClient, &NetworkTcpClient::read, this, &DataProcess::read);
            connect(mNetworkTcpClient, &NetworkTcpClient::tcpServerConnected,
                    this, [=](const QString IP, quint16 port){ emit message(QString("Connected TCP Server: %1:%2").arg(IP).arg(port)); });
            connect(mNetworkTcpClient, &NetworkTcpClient::tcpServerDisonnected, this, [=](){ emit message("Disconnected"); });

            mNetworkTcpClient->moveToThread(thread);

            thread->start();
        }
        QTimer::singleShot(100, this, [=](){ emit connectTCPServer("127.0.0.1", 60001); });
    }
    float rangeRateScope[4] = {39.9772f,34.98f};
    uint8_t subFrameNum = 2;

    Radar_initCfg();
    RDP_initTrack(subFrameNum, rangeRateScope);    //跟踪初始化
    ADAS_initParams();
    initVDYinfo();
    // ALN_initEOLMode();

    return true;
}

void DataProcess::initEolType(quint8 eolType)
{
    ALN_DynamicEOL_setEolType(eolType);
}
void DataProcess::setEOLRunState(void)
{
    ALN_DynamicEOL_setRun(1);
}

void DataProcess::connectMasterRadar()
{
//    qDebug() << __FUNCTION__ << __LINE__ << "message";
    QTimer::singleShot(100, this, [=](){ emit connectTCPServer("127.0.0.1", 60001); });
}

bool DataProcess::process(AnalysisData &analysisData, bool slave)
{
//     qDebug() << __FUNCTION__ << __LINE__ << analysisData.mRadarID;

    // 雷达融合，从雷达
    if (mMasterRadar && mSlaveRadarID == analysisData.mRadarID) {
        if (!setSlaveRadarData(analysisData)) {
            qDebug() << __FUNCTION__ << __LINE__ << "slove radar data process error!";
            return false;
        }
        return true;
    }

    if ((!slave && analysisData.mRadarID != mMasterRadarID) || (slave && analysisData.mRadarID != mSlaveRadarID)) {
        return true;
    }
    analysisData.mFrameNb = analysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount;
    analysisData.mFrameTrackNb = analysisData.mTargets[FrameTrackTarget].mTargetHeader.mMeasurementCount;
    APAR_setAparRadarId(analysisData.mRadarID);
    gFrameNb = analysisData.mFrameNb;
    gFrameTrackNb = analysisData.mFrameTrackNb;

	// 获取航迹数据
    Targets &trackTargets = analysisData.mTargets[FrameTrackTarget];
/*
    for (int i = 0; i < trackTargets.mTargetCount; ++i) {
        qDebug() << __FUNCTION__ << __LINE__ << trackTargets.mTargets[i].mID;
    }
    return true;
    // 清除航迹数据
    trackTargets.clear();
*/

    // 原始点仿真
    if (mIsorgpoint)
    {
        // 获取航迹数据
        Targets &trackTargets = analysisData.mTargets[FrameTrackTarget];
        // 清除航迹数据
        trackTargets.clear();

        //无效数据--往往是跳帧后的第一帧，只有之前的车速，但是没有目标信息，会导致跑数据处理车速有问题
        if (analysisData.mTargets[FrameRawTarget].mTargetHeader.mResponseTaskCycleTime < 0.1f)
        {
            analysisData.mVehicleData.clear();
            return true;
        }

        setData(analysisData);

		updateDetObjListData((float)(analysisData.mTargets[FrameRawTarget].mTargetHeader.mNoiseGlobal));

        static int timestemp = 0;
        static int time = 50;
        if (timestemp) {
            time = (analysisData.mEndFrameData.mFrameTime - timestemp) / 1000;
        }
    //    qDebug() << __FUNCTION__ << __LINE__ << time << QDateTime::fromMSecsSinceEpoch(analysisData.mEndFrameData.mFrameTime);
        timestemp = analysisData.mEndFrameData.mFrameTime;

        RDP_runMainFunction(analysisData.mRadarID,
                            RSP_getDetObjListPointer(),
                            VDY_getFreezedVehDyncDataPointer(),
                            APAR_getAparInstallAzimuthAngle(),
                            ((double)(analysisData.mTargets[FrameRawTarget].mTargetHeader.mTargetIntervalTime) / 1000.f));
        groadlineoffset = gxoffset; // 原始点直接补偿安装偏移

        ADAS_runMainFunction(RDP_getTrkObjectListPointer(),
                             VDY_getVDY_Info_Pointer(),
                             ADAS_getRadarCongiguration(),
                             get_LeftRadarWarnInfo(),
                             0,
                            ((double)(analysisData.mTargets[FrameRawTarget].mTargetHeader.mTargetIntervalTime) / 1000.f));

        getData(analysisData);
    }
    else // 跟踪点仿真
    {
        // 获取航迹数据
        Targets &trackTargets = analysisData.mTargets[FrameTrackTarget];
        Targets &rawTargets = analysisData.mTargets[FrameRawTarget];

        static int timestemp = 0;
        static int time = 50;
        if (timestemp)
        {
            time = analysisData.mEndFrameData.mFrameTime - timestemp;
        }
        timestemp = analysisData.mEndFrameData.mFrameTime;
        // analysisData.mFrameNb = analysisData.mTargets[FrameTrackTarget].mTargetHeader.mMeasurementCount;

        setData(analysisData);                                           // 设置车身相关数据.
        gettrackData(analysisData);                                      // 跟踪数据同步到RDP_getTrkObjectListPointer
        groadlineoffset = -analysisData.mEndFrameData.mRoadSideDistance; // 跟踪点直接拿保存的边线数据

        ADAS_runMainFunction(RDP_getTrkObjectListSimulatorPointer(), // 在gettrackData被赋值 RDP_getCustomizeObjListData(((double)(analysisData.mTargets[FrameTrackTarget].mTargetHeader.mResponseTaskCycleTime) / 1000.f)), //
                             VDY_getVDY_Info_Pointer(),
                             ADAS_getRadarCongiguration(),
                             get_LeftRadarWarnInfo(),
                             0,
                             ((double)(analysisData.mTargets[FrameTrackTarget].mTargetHeader.mResponseTaskCycleTime) / 1000.f));

        getalarmData(analysisData);
    }

    return true;
}

#define INTPERPOLATION_DELTA_L      (50 - 20)
#define INTPERPOLATION_DELTA_H      (50 + 20)
#define INTPERPOLATION_MAX_DELTA    150
int DataProcess::injection(int radarID, bool ahead,
                           quint64 rawFrameTimeStampGlobal,
                           Devices::Can::FrameTimestamps *timestamps,
                           Devices::Can::stCanTxMsg *frameArray,
                           Analysis::IInterpolation *_interpolation)
{
    int frameCount = 0;
    uint64_t deltaT = 0, lastTimestamps = 0;
    int16_t trkValidNum = 0;
#ifdef ALGORITHM_GEELY
	RDP_TrkFrameInfoGeely2_0* outputObjList = RSP_getGeelyObjectBufferPointer(&trkValidNum);
#else
    RDP_TrkObjectInfo_t* outputObjList = RDP_getBYDOutputObjList(&trkValidNum);
#endif
    for (int i = 0; i < timestamps->mCountInterpolation; ++i) {
        //trkValidNum = 0;
        Devices::Can::FrameTimestamp* timestampCurrent = timestamps->mTimestamps + i;
        // 进行插值处理
        if (i == 0)
        {
			if (ahead) 
			{
				deltaT = rawFrameTimeStampGlobal - timestampCurrent->OD_TimeStampMs;
			} 
			else 
			{
				deltaT = timestampCurrent->OD_TimeStampMs - rawFrameTimeStampGlobal;
			}
            qDebug() << __FUNCTION__ << __LINE__ << deltaT << timestampCurrent->OD_TimeStampMs << rawFrameTimeStampGlobal;
            //if (deltaT > INTPERPOLATION_MAX_DELTA)
            //{
            //    qDebug() << __FUNCTION__ << __LINE__ << "message 1" << deltaT;
            //    //break;// 异常告警
            //}
            //else
			if(deltaT > 5.f && deltaT < 200.f)
            {
                RDP_trkInterpolation(((float)deltaT / 1000));
            }
        }
        else
        {
            deltaT = timestampCurrent->OD_TimeStampMs - lastTimestamps;
            qDebug() << __FUNCTION__ << __LINE__ << deltaT << timestampCurrent->OD_TimeStampMs << lastTimestamps;
            //if (deltaT < INTPERPOLATION_DELTA_L || deltaT > INTPERPOLATION_DELTA_H)
            //{
            //    qDebug() << __FUNCTION__ << __LINE__ << "message 2" << deltaT;
            //    //break;// 异常告警
            //}
            //else
			if (deltaT > 5.f && deltaT < 200.f)
            {
                RDP_trkInterpolation(((float)deltaT / 1000));
            }
        }
        lastTimestamps = timestampCurrent->OD_TimeStampMs;

        frameCount += _interpolation->encodeFrame(radarID, timestampCurrent, outputObjList, trkValidNum, timestampCurrent->msgCounter, frameArray + frameCount);
        qDebug() << __FUNCTION__ << __LINE__ << trkValidNum << frameCount;

        Analysis::AnalysisWorker *analysisWorker = _interpolation->analysisWorker();
        analysisWorker->analysisEnd(radarID, false, true);

       Utils::dely(50);

    }

    return frameCount;
}

/**
* <AUTHOR>
* @date 2023-05-25
* @param
* @return bool
* @note
* 子进程设置从雷达数据到主进程（主雷达）
* @remarks
*/
bool DataProcess::setInteractiveData(AnalysisData &analysisData)
{
    qDebug() << __FUNCTION__ << __LINE__ << analysisData.mRadarID;

    if (mSharedMemoryInteractive.isAttached()) {
        if (!mSharedMemoryInteractive.detach()) {
            qDebug() << __FUNCTION__ << __LINE__ << "Unable to detach from interactive shared memory.";
        }
    }

    if (!mSharedMemoryInteractive.create(sizeof (AnalysisData))) {
        qDebug() << __FUNCTION__ << __LINE__ << "create interactive shared memory error!";
        return false;
    }

    if (!mSharedMemoryInteractive.lock()) {
        qDebug() << __FUNCTION__ << __LINE__ << "lock error";
        return false;
    }
    char *to = (char*)mSharedMemoryInteractive.data();
    int size = sizeof (AnalysisData);
    memcpy(to, &analysisData, qMin(mSharedMemoryInteractive.size(), size));
    mSharedMemoryInteractive.unlock();

    // 发送消息到主进程
    QString msg = QString("[SLOVE] %1(%2)").arg(analysisData.mRadarID).arg(analysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount);
    emit message(msg);
    emit write(msg.toUtf8());

    return true;
}

/**
* <AUTHOR>
* @date 2023-05-25
* @param
* @return bool
* @note
* 主进程（主雷达）获取子进程从雷达的数据
* @remarks
*/
bool DataProcess::getInteractiveData()
{
    if (!mSharedMemoryInteractive.attach()) {
        qDebug() << __FUNCTION__ << __LINE__ << "Unable to attach to shared memory segment.\n" \
                                                "Load an image first.";
        return false;
    }

    if (mSharedMemoryInteractive.size() < sizeof (AnalysisData)) {
        qDebug() << __FUNCTION__ << __LINE__ << mSharedMemoryInteractive.size() << sizeof (AnalysisData);
        return false;
    }

    if (!mSharedMemoryInteractive.lock()) {
        qDebug() << __FUNCTION__ << __LINE__ << "lock error";
        return false;
    }
    char *from = (char*)mSharedMemoryInteractive.data();
    int size = sizeof (AnalysisData);
    memcpy(&mSlaveRadarAnalysisData, from, qMin(mSharedMemorySlaveRadar.size(), size));
    mSharedMemoryInteractive.unlock();

    mSharedMemoryInteractive.detach();

    qDebug() << __FUNCTION__ << __LINE__ << mSlaveRadarAnalysisData.mRadarID << mSlaveRadarAnalysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount;

    return true;
}

/**
* <AUTHOR>
* @date 2023-05-25
* @param
* @return bool
* @note
* 主进程写入从雷达数据
* @remarks
*/
bool DataProcess::setSlaveRadarData(AnalysisData &analysisData)
{
//    qDebug() << __FUNCTION__ << __LINE__ << analysisData.mRadarID;

#if 1
    if (mSharedMemorySlaveRadar.isAttached()) {
        if (!mSharedMemorySlaveRadar.detach()) {
            qDebug() << __FUNCTION__ << __LINE__ << "Unable to detach from slove radar shared memory.";
        }
    }

    if (!mSharedMemorySlaveRadar.create(sizeof (AnalysisData))) {
        qDebug() << __FUNCTION__ << __LINE__ << "create slove shared memory error!";
        return false;
    }

    if (!mSharedMemorySlaveRadar.lock()) {
        qDebug() << __FUNCTION__ << __LINE__ << "lock error";
        return false;
    }
    char *to = (char*)mSharedMemorySlaveRadar.data();
    int size = sizeof (AnalysisData);
    memcpy(to, &analysisData, qMin(mSharedMemorySlaveRadar.size(), size));
    mSharedMemorySlaveRadar.unlock();
#endif

    // 发送信息到子进程
    emit write(QString("[MASTER] %1(%2)").arg(analysisData.mRadarID).arg(analysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount).toUtf8());

    return true;
}

/**
* <AUTHOR>
* @date 2023-05-25
* @param
* @return bool
* @note
* 子进程获取从雷达数据
* @remarks
*/
bool DataProcess::getSlaveRadarData()
{
    if (!mSharedMemorySlaveRadar.attach()) {
        qDebug() << __FUNCTION__ << __LINE__ << "Unable to attach to shared memory segment.\n" \
                                                "Load an image first.";
        return false;
    }

    if (mSharedMemorySlaveRadar.size() < sizeof (AnalysisData)) {
        qDebug() << __FUNCTION__ << __LINE__ << mSharedMemorySlaveRadar.size() << sizeof (AnalysisData);
        return false;
    }
    if (!mSharedMemorySlaveRadar.lock()) {
        qDebug() << __FUNCTION__ << __LINE__ << "lock error";
        return false;
    }
    char *from = (char*)mSharedMemorySlaveRadar.data();
    int size = sizeof (AnalysisData);
    memcpy(&mSlaveRadarAnalysisData, from, qMin(mSharedMemorySlaveRadar.size(), size));
    mSharedMemorySlaveRadar.unlock();

    mSharedMemorySlaveRadar.detach();

    qDebug() << __FUNCTION__ << __LINE__ << mSlaveRadarAnalysisData.mRadarID << mSlaveRadarAnalysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount;

    return true;
}

void DataProcess::read(const QByteArray &data)
{
    qDebug() << __FUNCTION__ << __LINE__ << data;
    emit message(data);
    if (data.startsWith("[MASTER]")) {
#if 1
        // 获取从雷达数据
        if (!getSlaveRadarData()) {
            qDebug() << __FUNCTION__ << __LINE__ << "get slove radar data error!";
            return;
        }

        // 进行数据处理
        process(mSlaveRadarAnalysisData, true);

        // 设置从雷达数据
        if (!setInteractiveData(mSlaveRadarAnalysisData)) {
            qDebug() << __FUNCTION__ << __LINE__ << "set interactive data error!";
            return;
        }
#endif

    } else if (data.startsWith("[SLOVE]")) {
        // 获取从雷达数据
        if (!getInteractiveData()) {
            qDebug() << __FUNCTION__ << __LINE__ << "get interactive data error!";
            return;
        }
        // 设置从雷达数据
        setSlaveData(mSlaveRadarAnalysisData);

        emit sloveRadarFinished(mSlaveRadarAnalysisData.mRadarID);
    }
}

void DataProcess::setIsShowCandiObj(bool isShow)
{
    mIsShowCandiObj = isShow;
}

bool DataProcess::setSlaveData(AnalysisData &analysisData)
{
    return true;
}

bool DataProcess::setData(AnalysisData &analysisData)
{
    Targets &rawTargets = analysisData.mTargets[FrameRawTarget];

    VehicleData &vehicleData = analysisData.mVehicleData;

    for(unsigned int i = 0 ; i <(MAX_NB_OF_TARGETS * 2) ;i++)
    {
        gOutTargets[i].isUsing = 0;
    }

    for (unsigned int i = 0; i < rawTargets.mTargetCount; i++)
    {
        if (!rawTargets.mTargets[i].mValid)
        {
            continue;
        }

        gOutTargets[rawTargets.mTargets[i].mID].angle       = rawTargets.mTargets[i].mAngle;
        gOutTargets[rawTargets.mTargets[i].mID].isUsing     = 1;
        gOutTargets[rawTargets.mTargets[i].mID].range       = rawTargets.mTargets[i].mRange;
        gOutTargets[rawTargets.mTargets[i].mID].snr         = rawTargets.mTargets[i].mSNR;
		gOutTargets[rawTargets.mTargets[i].mID].rcs         = rawTargets.mTargets[i].mRCS;
        gOutTargets[rawTargets.mTargets[i].mID].velocity    = rawTargets.mTargets[i].mV;
        gOutTargets[rawTargets.mTargets[i].mID].heighAngle  = rawTargets.mTargets[i].mPitchAngle;
        gOutTargets[rawTargets.mTargets[i].mID].DetStatus   = rawTargets.mTargets[i].mStatus;
        gOutTargets[rawTargets.mTargets[i].mID].MF = rawTargets.mTargets[i].mMatchFlag;
        gOutTargets[rawTargets.mTargets[i].mID].fftMag      = rawTargets.mTargets[i].mMAG;
    }

    gDspParam.outAllTargets = rawTargets.mTargetCount;
    float trkDbgFrameTime = 0.05;
    trkDbgFrameTime = rawTargets.mTargetHeader.mResponseTaskCycleTime/1000.0;
    mcu_time += rawTargets.mTargetHeader.mResponseTaskCycleTime;

    VDY_DynamicEstimate_t *freezedVehDyncDataAddr = VDY_getFreezedVehDyncDataPointer();
    if(vehicleData.mGear != 2) // 非倒挡
        freezedVehDyncDataAddr->vdySpeedInmps = vehicleData.mVehicleSpeed / 3.6;  //上位机保存的速度单位是km/h，且为标量，故在此赋予速度方向，并转换为m/s
    else
        freezedVehDyncDataAddr->vdySpeedInmps = -vehicleData.mVehicleSpeed / 3.6;

    if (vehicleData.mGear == 2) // GEAR_R
		freezedVehDyncDataAddr->vdyDriveDirection = 1;
    else
		freezedVehDyncDataAddr->vdyDriveDirection = 0;

    freezedVehDyncDataAddr->vdyYawRate = vehicleData.mYawRate;
    freezedVehDyncDataAddr->vdyCurveRadius = vehicleData.mRadius;
    freezedVehDyncDataAddr->vdySteeringAngle = vehicleData.mSteeringWheelAngle;
    //freezedVehDyncDataAddr->vdywheelangle = (vehicleData.mSteeringWheelAngle / DEFAULT_STEER_RATIO);
    freezedVehDyncDataAddr->vdyAccelLong = vehicleData.mLongitudinalAcceleration;
    freezedVehDyncDataAddr->vdyAccelLat = vehicleData.mLateralAcceleration;
    freezedVehDyncDataAddr->vdyGearState = vehicleData.mGear;

//        radar_config_using->compli_para.radarId = 5;
    aparInstallAzimuthAngle = analysisData.mEndFrameData.mEndOfLineEstablishedAngle;

//    gDspParam.dataMode = PARAM_DATAMODE_RAW_TRACK;

    // 设置状态机开关
    gadasFunctionState.adasLCAFuncState = analysisData.mAlarmData.mAlarmLCAState;
    gadasFunctionState.adasBSDFuncState = analysisData.mAlarmData.mAlarmBSDState;
    gadasFunctionState.adasRCTAFuncState = analysisData.mAlarmData.mAlarmRCTAState;
    gadasFunctionState.adasRCTBFuncState = analysisData.mAlarmData.mAlarmRCTBState;
    gadasFunctionState.adasDOWFuncState = analysisData.mAlarmData.mAlarmDOWState;
    gadasFunctionState.adasRCWFuncState = analysisData.mAlarmData.mAlarmRCWState;
    gadasFunctionState.adasFCTAFuncState = analysisData.mAlarmData.mAlarmFCTAState;
    gadasFunctionState.adasFCTBFuncState = analysisData.mAlarmData.mAlarmFCTBState;
//    gadasFunctionState.adasFCTAPlanBFuncState = gadasFunctionState.adasFCTAFuncState;
//    gadasFunctionState.adasFCTBPlanBFuncState = gadasFunctionState.adasFCTBFuncState;

    #if 0 //临时状态机
    if(fabs(vehicleData.mVehicleSpeed) > 12.0f)
    {
        gadasFunctionState.adasLCAFuncState = 2;
        gadasFunctionState.adasBSDFuncState = 2;
        gadasFunctionState.adasFCTAFuncState = 2;
    }
    else
    {
        gadasFunctionState.adasRCTAFuncState = 2;
        gadasFunctionState.adasDOWFuncState = 2;
        gadasFunctionState.adasRCWFuncState = 2;
    }

    #endif

    gxoffset = analysisData.mTargets[FrameTrackTarget].mTargetHeader.mOffsetToSideX;
    gyoffset = analysisData.mTargets[FrameTrackTarget].mTargetHeader.mOffsetToSideY;

    return true;
}

bool DataProcess::getData(AnalysisData &analysisData)
{
    AlarmData &alarmData = analysisData.mAlarmData;
    Targets &rawTargets = analysisData.mTargets[FrameRawTarget];
    Targets &trackTargets = analysisData.mTargets[FrameTrackTarget];
    Targets& track16Targets = analysisData.m16Targets;
    trackTargets.clear();
    track16Targets.clear();

    trackTargets.mTargetHeader.mMeasurementCount = rawTargets.mTargetHeader.mMeasurementCount;
    trackTargets.mTargetHeader.mTargetIntervalTime = rawTargets.mTargetHeader.mTargetIntervalTime;

    const RDP_TrkObjectList_t *RDP_TrkObjectListAddr = RDP_getTrkObjectListPointer();
    const trk_pkg_t *RDP_TrackTargetsAddr = RDP_getTrackTargetsPointer();
    const cdi_pkg_t* RDP_Cdi = RDP_getBKTargetsListPointer();
/*
    QString name = QString("./gStepCost111111.csv");
    QFile UpgradeLog(name);
    UpgradeLog.open(QIODevice::WriteOnly | QIODevice::Append);

    QString strlog = "";
*/
//    qDebug() << __FUNCTION__ << __LINE__ << RDP_TrkObjectListAddr->rdpDetObjectNum;
    for (int j = 0, cnt = 0; (cnt < RDP_TrkObjectListAddr->rdpDetObjectNum) && (j < MAX_NUM_OF_TRACKS); j++)
    {
        if(RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjRange <= 0.1
                || (RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrackType == CANDI && (mIsShowCandiObj == false)))
        {
//            qDebug() << __FUNCTION__ << __LINE__ << RDP_TrkObjectListAddr->rdpTrkObject[j].id << mIsShowCandiObj << RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjRange <<RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrackType;
            continue;
        }
        cnt++;
        int i = trackTargets.mTargetCount;
        trackTargets.mTargets[i].mID = RDP_TrkObjectListAddr->rdpTrkObject[j].id;
//        qDebug() << __FUNCTION__ << __LINE__ << trackTargets.mTargets[i].mID;
        trackTargets.mTargets[i].mRange = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjRange;
        trackTargets.mTargets[i].mAngle = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjAzimuthAngle;

        trackTargets.mTargets[i].mX = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjDistX;
        trackTargets.mTargets[i].mY = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjDistY;

        trackTargets.mTargets[i].mV = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjVelocity;
        trackTargets.mTargets[i].mSNR = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjsnr;
        trackTargets.mTargets[i].mVx = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjVrelX;
        trackTargets.mTargets[i].mVy = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjVrelY;
        trackTargets.mTargets[i].mAy = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjArelY;
        trackTargets.mTargets[i].mAx = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjArelX;

        //计算 boxCenterX、boxCenterY
        trackTargets.mTargets[i].mTrackFrameX = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjBoxCenterX;
        trackTargets.mTargets[i].mTrackFrameY = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjBoxCenterY;
        trackTargets.mTargets[i].mTrackFrameLength = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjBoxLength;
        trackTargets.mTargets[i].mTrackFrameWidth = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjBoxWidth;
        trackTargets.mTargets[i].mTrackFrameAngle = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjHeadingAngle;

        trackTargets.mTargets[i].mDynamicProperty = RDP_TrkObjectListAddr->rdpTrkObject[j].rdpTrkObjStatus & 0x01;

        trackTargets.mTargets[i].mValid = true;
        trackTargets.mTargets[i].mGroupID = RDP_TrackTargetsAddr->trk[j].idx_1;
        trackTargets.mTargets[i].mStatus = RDP_TrackTargetsAddr->trk[j].status;
        trackTargets.mTargetCount++;
		trackTargets.mTargets[i].mObjectType = RDP_TrackTargetsAddr->trk[j].objType;
        trackTargets.mTargets[i].mTrackType = RDP_TrackTargetsAddr->trk[j].type;
        trackTargets.mTargets[i].mRCS = RDP_TrackTargetsAddr->trk[j].rcs / 10.f;

        if(RDP_TrackTargetsAddr->trk[j].idx_1 >= 0)
        {
            trackTargets.mTargets[i].mAssocRawIdx = RDP_Cdi->cdi[RDP_TrackTargetsAddr->trk[j].idx_1].DetIdx;
        }
        else
        {
            trackTargets.mTargets[i].mAssocRawIdx = -1;
        }
    }

    
    //UpgradeLog.close();

    //重置原始点的某些信息
    for (uint32_t j = 0; j < MAX_TARGET_COUNT; j++)
    {
        rawTargets.mTargets[j].mGroupID = -1;
    }

    //更新原始点的某些信息
    for (uint32_t j = 0; j < RDP_Cdi->number; j++)
    {
        rawTargets.mTargets[RDP_Cdi->cdi[j].DetIdx].mGroupID = RDP_Cdi->cdi[j].groupId;
        rawTargets.mTargets[RDP_Cdi->cdi[j].DetIdx].mStatus  = (uint16_t)RDP_Cdi->cdi[j].status;
    }

    //护栏边界
    sideLine_pkr_t sideLines = RDP_TrackTargetsAddr->sideLines;     //护栏信息
    analysisData.mEndFrameData.mGuardrail01_c0          = sideLines.strongParam[0];
    analysisData.mEndFrameData.mGuardrail01_c1          = sideLines.strongParam[1];
    analysisData.mEndFrameData.mGuardrail01_c2          = sideLines.strongParam[2];
    analysisData.mEndFrameData.mGuardrail01_c3          = sideLines.strongParam[3];
    analysisData.mEndFrameData.mGuardrail01_vaild       = sideLines.strongSildLineValid;
    analysisData.mEndFrameData.mGuardrail01_IngStart    = sideLines.strongFenceRange[0];
    analysisData.mEndFrameData.mGuardrail01_IngEnd      = sideLines.strongFenceRange[1];
    analysisData.mEndFrameData.mGuardrail02_c0          = -fabsf(sideLines.weekParam[0]);
    analysisData.mEndFrameData.mGuardrail02_c1          = sideLines.weekParam[1];
    analysisData.mEndFrameData.mGuardrail02_c2          = sideLines.weekParam[2];
    analysisData.mEndFrameData.mGuardrail02_c3          = sideLines.weekParam[3];
    analysisData.mEndFrameData.mGuardrail02_vaild       = sideLines.weekSildLineValid;
    analysisData.mEndFrameData.mGuardrail02_IngStart    = sideLines.weekFenceRange[0];
    analysisData.mEndFrameData.mGuardrail02_IngEnd      = sideLines.weekFenceRange[1];

    trackTargets.mValid = RDP_TrkObjectListAddr->rdpDetObjectNum;

    const ADAS_FunctionState_t *almflag = ADAS_getAdasDataPointer();

    alarmData.mDrivingFunctionAlarmModule = almflag->adasAlmFlag;
    alarmData.mAlarmBSDObjectID = almflag->adasBSDAlarmObjID;
    alarmData.mAlarmLCAObjectID = almflag->adasLCAAlarmObjID;
    alarmData.mAlarmLCAObjectTTC = almflag->adasLCATtc;
    alarmData.mAlarmDOWObjectID = almflag->adasDOWAlarmObjID;
    alarmData.mAlarmDOWObjectTTC = almflag->adasDOWTtc;
    alarmData.mAlarmRCWObjectID = almflag->adasRCWAlarmObjID;
    alarmData.mAlarmRCWObjectTTC = almflag->adasRCWTtc;
    alarmData.mAlarmRCTAObjectID = almflag->adasRCTAAlarmObjID;
    alarmData.mAlarmRCTAObjectTTC = almflag->adasRCTATtc;
    alarmData.mAlarmRCTBObjectID = almflag->adasRCTAAlarmObjID;
    alarmData.mAlarmRCTBObjectTTC = almflag->adasRCTATtc;
    alarmData.mAlarmFCTAObjectID = almflag->adasFCTAAlarmObjID;
    alarmData.mAlarmFCTAObjectTTC = almflag->adasFCTATtc;
    alarmData.mAlarmFCTBObjectID = almflag->adasFCTAAlarmObjID;
    alarmData.mAlarmFCTBObjectTTC = almflag->adasFCTATtc;
    alarmData.mAlarmBSDLevel = almflag->adasBSDWarning;
    alarmData.mAlarmLCALevel = almflag->adasLCAWarning;
    alarmData.mAlarmDOWFLevel = almflag->adasDOWWarning;
    alarmData.mAlarmDOWRLevel = almflag->adasDOWWarning;
    alarmData.mAlarmRCWLevel = almflag->adasRCWWarning;
    alarmData.mAlarmRCTALevel = almflag->adasRCTAWarning;
    alarmData.mAlarmFCTALevel = almflag->adasFCTAWarning;
    alarmData.mAlarmBSDState = almflag->adasBSDFuncState;
    alarmData.mAlarmLCAState = almflag->adasLCAFuncState;
    alarmData.mAlarmDOWState = almflag->adasDOWFuncState;
    alarmData.mAlarmRCTAState = almflag->adasRCTAFuncState;
    alarmData.mAlarmRCTBState = almflag->adasRCTBFuncState;
    alarmData.mAlarmFCTAState = almflag->adasFCTAFuncState;
    alarmData.mAlarmFCTBState = almflag->adasFCTBFuncState;
    alarmData.mAlarmRCWState = almflag->adasRCWFuncState;

    if ((4 == analysisData.mRadarID) || (5 == analysisData.mRadarID))
    {
        alarmData.mAlarmBSDLevel = almflag->adasBSDWarning;
        alarmData.mAlarmLCALevel = almflag->adasLCAWarning;
        alarmData.mAlarmDOWLevelFront = almflag->adasDOWWarning;
        alarmData.mAlarmDOWLevelRear = almflag->adasDOWWarning;
        alarmData.mAlarmRCWLevel = almflag->adasRCWWarning;
        alarmData.mAlarmRCTALevel = almflag->adasRCTAWarning;
        alarmData.mAlarmRCTBLevel = almflag->adasRCTBWarning;
    }
    else
    {
        alarmData.mAlarmDOWLevelFront = almflag->adasDOWWarning;
        alarmData.mAlarmDOWLevelRear = almflag->adasDOWWarning;
        alarmData.mAlarmFCTALevel = almflag->adasFCTAWarning;
        alarmData.mAlarmFCTBLevel = almflag->adasFCTBWarning;
    }

    // 设置功能状态机
    alarmData.mAlarmRCTBState = analysisData.mAlarmData.mAlarmRCTBState;
    alarmData.mAlarmBSDState = analysisData.mAlarmData.mAlarmBSDState;
    alarmData.mAlarmLCAState = analysisData.mAlarmData.mAlarmLCAState;
    alarmData.mAlarmDOWState = analysisData.mAlarmData.mAlarmDOWState;
    alarmData.mAlarmRCTAState = analysisData.mAlarmData.mAlarmRCTAState;
    alarmData.mAlarmFCTAState = analysisData.mAlarmData.mAlarmFCTAState;
    alarmData.mAlarmFCTBState = analysisData.mAlarmData.mAlarmFCTBState;
    alarmData.mAlarmRCWState = analysisData.mAlarmData.mAlarmRCWState;

#if 0

    alarmData.mAlarmRCTBState = almflag->adasRCTBFuncState;
    alarmData.mAlarmBSDState = almflag->adasBSDFuncState;
    alarmData.mAlarmLCAState = almflag->adasLCAFuncState;
    alarmData.mAlarmDOWState = almflag->adasDOWFuncState;
    alarmData.mAlarmRCTAState = almflag->adasRCTAFuncState;
    alarmData.mAlarmFCTAState = almflag->adasFCTAFuncState;
    alarmData.mAlarmFCTBState = almflag->adasFCTBFuncState;
    alarmData.mAlarmRCWState = almflag->adasRCWFuncState;
#endif

    return true;
}

bool DataProcess::gettrackData(AnalysisData &analysisData)
{
    AlarmData &alarmData = analysisData.mAlarmData;
    Targets &rawTargets = analysisData.mTargets[FrameRawTarget];
    Targets &trackTargets = analysisData.mTargets[FrameTrackTarget];

    trackTargets.mTargetHeader.mMeasurementCount = trackTargets.mTargetHeader.mMeasurementCount;
    trackTargets.mTargetHeader.mTargetIntervalTime = trackTargets.mTargetHeader.mTargetIntervalTime;

    const RDP_TrkObjectList_t *RDP_TrkObjectListAddr = RDP_getTrkObjectListPointer();
    const trk_pkg_t *RDP_TrackTargetsAddr = RDP_getTrackTargetsPointer();

    //qDebug() << __FUNCTION__ << __LINE__ << "目标数量" << trackTargets.mTargetCount;
    memset (gRDP_TrkObjectListSimulator.rdpTrkObject, 0x00, sizeof (RDP_TrkObjectInfo_t) * MAX_NUM_OF_TRACKS);
    // 目标数据给到ADAS 模块 后续有哪个数据不正确, 优先从这里分析取值是否正确.
    // for (int i = 0, cnt = 0; (cnt < trackTargets.mTargetCount) && (i < MAX_TARGET_COUNT); i++){
    for (int i = 0, cnt = 0; (i < MAX_TRACKTARGET_COUNT); i++)
    {
        int id = trackTargets.mTargets[i].mID;
        cnt++;
        if ((0 == trackTargets.mTargets[i].mID) && (0 == trackTargets.mTargets[i].mTrackLifeCycleCnt) && (0 == trackTargets.mTargets[i].mTrackType))
        {
            continue;
        }
        trackTargets.mTargets[i].mRange = qSqrt(qPow(trackTargets.mTargets[i].mX, 2) + qPow(trackTargets.mTargets[i].mY, 2));
        trackTargets.mTargets[i].mV = -qSqrt(qPow(trackTargets.mTargets[i].mVx, 2) + qPow(trackTargets.mTargets[i].mVy, 2));
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].id = trackTargets.mTargets[i].mID;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjDistX = trackTargets.mTargets[i].mX;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjDistY = trackTargets.mTargets[i].mY;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjVrelX = trackTargets.mTargets[i].mVx;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjVrelY = trackTargets.mTargets[i].mVy;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjReliability = trackTargets.mTargets[i].mExistProbability;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjRange = trackTargets.mTargets[i].mRange;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjStatus = trackTargets.mTargets[i].mStatus + (trackTargets.mTargets[i].mStatus02 * (2 ^ 10));
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjAzimuthAngle = atan2f(trackTargets.mTargets[i].mX, trackTargets.mTargets[i].mY) * RAD2DEG;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjVelocity = trackTargets.mTargets[i].mV;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjsnr = trackTargets.mTargets[i].mSNR;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjBoxCenterX = trackTargets.mTargets[i].mTrackFrameX;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjBoxCenterY = trackTargets.mTargets[i].mTrackFrameY;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjBoxLength = trackTargets.mTargets[i].mTrackFrameLength;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjBoxWidth = trackTargets.mTargets[i].mTrackFrameWidth;
        // gRDP_TrkObjectListSimulator.trk[i].idx_1 = trackTargets.mTargets[i].mGroupID;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjHitCnt = trackTargets.mTargets[i].mHitCnt;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpTrkObjMissCnt = trackTargets.mTargets[i].mMissCnt;
        gRDP_TrkObjectListSimulator.rdpTrkObject[id].rdpLifeCycleCnt = trackTargets.mTargets[i].mTrackLifeCycleCnt;
        // gRDP_TrkObjectListSimulator.rdpTrkObject[i].rdpTrkObjStatus |= (trackTargets.mTargets[i].mStatus02 * (2^10));

        // gRDP_TrkObjectListSimulator.rdpTrkObject[i].rdpTrkObjStatus= 1;
        gRDP_TrkObjectListSimulator.rdpDetObjectNum = i;

        ////qDebug() << "track" << i << trackTargets.mTargets[i].mStatus;
    }

    return true;
}

bool DataProcess::saveAlarm(AnalysisData &analysisData)
{
    //RadarID,Alarm No.,Alarm ID No., Raw No.,Track No.,"
    QString headerList = "RadarID,Alarm No.,Alarm ID No., Raw No.,Track No.,"
                         "BSD Level,LCA Level,DOW Level,RCW Level,RCTA Level,RCTB Level,FCTA Level,FCTB Level,"
                         "Save Time, PATH\r\n";

    QDateTime date = QDateTime::currentDateTime();

    const ADAS_FunctionState_t *almflag = ADAS_getAdasDataPointer();
    bool alarm = false;
    qint32 alarmID = -1;

    if (almflag->adasBSDWarning) {
        alarm  = true;
        alarmID = almflag->adasBSDAlarmObjID;
    }
    if (almflag->adasLCAWarning) {
        alarm  = true;
        alarmID = almflag->adasLCAAlarmObjID;
    }
    if (almflag->adasDOWWarning) {
        alarm  = true;
        alarmID = almflag->adasDOWAlarmObjID;
    }
    if (almflag->adasRCWWarning) {
        alarm  = true;
        alarmID = almflag->adasRCWAlarmObjID;
    }
    if (almflag->adasRCTAWarning) {
        alarm  = true;
        alarmID = almflag->adasRCTAAlarmObjID;
    }
    if (almflag->adasRCTBWarning) {
        alarm  = true;
        //alarmID = almflag->adasRCTBAlarmObjID;
    }
    if (almflag->adasFCTAWarning) {
        alarm  = true;
        alarmID = almflag->adasFCTAAlarmObjID;
    }
    if (almflag->adasFCTBWarning) {
        alarm  = true;
        //alarmID = almflag->adasFCTBAlarmObjID;
    }

    if (alarm) {
        QString text = QString("%1,%2,%3,%4,%5,%6,%7,%8,%9,%10,%11,%12,%13\r\n")
                .arg(analysisData.mRadarID)
                .arg(analysisData.mRadarAlarmCount[analysisData.mRadarID])
                .arg(analysisData.mRadarAlarmIDCount[analysisData.mRadarID])
                .arg(analysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount)
                .arg(analysisData.mTargets[FrameTrackTarget].mTargetHeader.mMeasurementCount)

                .arg(almflag->adasBSDWarning)
                .arg(almflag->adasLCAWarning)
                .arg(almflag->adasDOWWarning)
                .arg(almflag->adasRCWWarning)
                .arg(almflag->adasRCTAWarning)
                .arg(almflag->adasRCTBWarning)
                .arg(almflag->adasFCTAWarning)
                .arg(almflag->adasFCTBWarning)
                .arg(date.toString("yyyy-MM-dd_hh:mm:ss.zzz"))
                .arg(mfilesinglename);

        if (mFilebackAlarm.isOpen()) {
            mFilebackAlarm.close();
        }
        if (mFilebackAlarm.open(QIODevice::Append | QIODevice::WriteOnly)) {
            mFilebackAlarm.write(text.toLocal8Bit());

            mFilebackAlarm.flush();
            qDebug() << "___" << text.toLocal8Bit() << endl;
        }
        if (mFilebackAlarm.isOpen()) {
            mFilebackAlarm.close();
        }
        analysisData.mRadarAlarmCount[analysisData.mRadarID]++;
        analysisData.mRadarAlarmIDCount[analysisData.mRadarID]++;
    }else{
        analysisData.mRadarAlarmCount[analysisData.mRadarID] = 0;
        analysisData.mRadarAlarmIDCount[analysisData.mRadarID] = 0;
    }
    return true;
}


bool DataProcess::getalarmData(AnalysisData &analysisData)
{
    AlarmData &alarmData = analysisData.mAlarmData;
    static int iCnt = 0;

    const ADAS_FunctionState_t *almflag = ADAS_getAdasDataPointer();

    alarmData.mDrivingFunctionAlarmModule = almflag->adasAlmFlag;
    alarmData.mAlarmBSDObjectID = almflag->adasBSDAlarmObjID;
    alarmData.mAlarmLCAObjectID = almflag->adasLCAAlarmObjID;
    alarmData.mAlarmLCAObjectTTC = almflag->adasLCATtc;
    alarmData.mAlarmDOWObjectID = almflag->adasDOWAlarmObjID;
    alarmData.mAlarmDOWObjectTTC = almflag->adasDOWTtc;
    alarmData.mAlarmRCWObjectID = almflag->adasRCWAlarmObjID;
    alarmData.mAlarmRCWObjectTTC = almflag->adasRCWTtc;
    alarmData.mAlarmRCTAObjectID = almflag->adasRCTAAlarmObjID;
    alarmData.mAlarmRCTAObjectTTC = almflag->adasRCTATtc;
    alarmData.mAlarmRCTBObjectID = almflag->adasRCTAAlarmObjID;
    alarmData.mAlarmRCTBObjectTTC = almflag->adasRCTATtc;
    alarmData.mAlarmFCTAObjectID = almflag->adasFCTAAlarmObjID;
    alarmData.mAlarmFCTAObjectTTC = almflag->adasFCTATtc;
    alarmData.mAlarmFCTBObjectID = almflag->adasFCTAAlarmObjID;
    alarmData.mAlarmFCTBObjectTTC = almflag->adasFCTATtc;

    if ((4 == analysisData.mRadarID) || (5 == analysisData.mRadarID)){
        alarmData.mAlarmBSDLevel = almflag->adasBSDWarning;
        alarmData.mAlarmLCALevel = almflag->adasLCAWarning;
        alarmData.mAlarmDOWLevelFront = almflag->adasDOWWarning;
        alarmData.mAlarmDOWLevelRear = almflag->adasDOWWarning;
        alarmData.mAlarmRCWLevel = almflag->adasRCWWarning;
        alarmData.mAlarmRCTALevel = almflag->adasRCTAWarning;
        alarmData.mAlarmRCTBLevel = almflag->adasRCTBWarning;
    }else{
        alarmData.mAlarmDOWLevelFront = almflag->adasDOWWarning;
        alarmData.mAlarmDOWLevelRear = almflag->adasDOWWarning;
        alarmData.mAlarmFCTALevel = almflag->adasFCTAWarning;
        alarmData.mAlarmFCTBLevel = almflag->adasFCTBWarning;
    }
    if (almflag->adasBSDWarning || almflag->adasLCAWarning || almflag->adasDOWWarning || almflag->adasRCWWarning || almflag->adasRCTAWarning || 
        almflag->adasRCTBWarning || almflag->adasFCTAWarning || almflag->adasFCTBWarning){
            saveAlarm(analysisData);
    }
    // 设置功能状态机
    alarmData.mAlarmRCTBState = analysisData.mAlarmData.mAlarmRCTBState;
    alarmData.mAlarmBSDState = analysisData.mAlarmData.mAlarmBSDState;
    alarmData.mAlarmLCAState = analysisData.mAlarmData.mAlarmLCAState;
    alarmData.mAlarmDOWState = analysisData.mAlarmData.mAlarmDOWState;
    alarmData.mAlarmRCTAState = analysisData.mAlarmData.mAlarmRCTAState;
    alarmData.mAlarmFCTAState = analysisData.mAlarmData.mAlarmFCTAState;
    alarmData.mAlarmFCTBState = analysisData.mAlarmData.mAlarmFCTBState;
    alarmData.mAlarmRCWState = analysisData.mAlarmData.mAlarmRCWState;
#if 1
    alarmData.mAlarmRCTBState = almflag->adasRCTBFuncState;
    alarmData.mAlarmBSDState = almflag->adasBSDFuncState;
    alarmData.mAlarmLCAState = almflag->adasLCAFuncState;
    alarmData.mAlarmDOWState = almflag->adasDOWFuncState;
    alarmData.mAlarmRCTAState = almflag->adasRCTAFuncState;
    alarmData.mAlarmFCTAState = almflag->adasFCTAFuncState;
    alarmData.mAlarmFCTBState = almflag->adasFCTBFuncState;
    alarmData.mAlarmRCWState = almflag->adasRCWFuncState;
#endif
    return true;
}

void DataProcess::clrradardata(void)
{
    memset(gRDP_TrkObjectListSimulator.rdpTrkObject, 0x00, sizeof(gRDP_TrkObjectListSimulator.rdpTrkObject));
}

void DataProcess::fileChanged(const QString &filename)
{
    static int iFirst = 0;
    mfilename = filename;
    //qDebug() << "filepath________" << mfilename;

    const ADAS_FunctionState_t *almflag = ADAS_getAdasDataPointer();
    QDateTime beginTime = QDateTime::currentDateTime();
    // 指定路径
    QString filePath = mfilename;
    // 创建QFileInfo对象
    QFileInfo fileInfo(filePath);

    mfilesinglename = fileInfo.baseName();      // blf文件名

    // 获取文件所在的目录路径
    QString directoryPath = fileInfo.dir().path();      // 路径名
    //qDebug() << "directoryPath________" << directoryPath;    

    QString alarmfilename = QString("%1_%2.csv").arg("Alarm").arg(beginTime.toString("yyyy-MM-dd_hh-mm-ss-zzz"));   // alm文件命名
    //QString alarmfilename = QString("%1_%2.csv").arg("Alarm").arg(mfilesinglename);

    //qDebug() << "alarmfilename________" << alarmfilename;
    QDir dir(directoryPath);
    QString combinedPath = dir.filePath(alarmfilename); // 将newpath和filename结合，得到新的路径
    //qDebug() << "combinedPath________" << combinedPath;
    if (mFilebackAlarm.isOpen()) {
        mFilebackAlarm.close();
    }
    if (0 == iFirst){
        mFilebackAlarm.setFileName(combinedPath);
        //qDebug() << "iFirst_______" << iFirst;    
    
        if (mFilebackAlarm.open(QIODevice::WriteOnly)) {
            QString headerList = "RadarID, Raw No., Track No.,"
                                "BSD Level, LCA Level, DOW Level, RCW Level, RCTA Level, RCTB Level, FCTA Level, FCTB Level, Save Time, PATH\r\n";
            mFilebackAlarm.write(headerList.toLocal8Bit());
            mFilebackAlarm.flush();
        }
        iFirst = 1;
    }
}
