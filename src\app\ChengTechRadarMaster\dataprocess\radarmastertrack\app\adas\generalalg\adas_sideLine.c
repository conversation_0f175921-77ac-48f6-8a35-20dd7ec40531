﻿/**
 * @file adas_sideLine.c
 * <AUTHOR> name (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2022-10-17
 * 
 * @copyright Copyright (c) 2022
 * 
 */

#ifdef ALPSPRO_ADAS
#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "time.h"
#include "rsp/rsp_types.h"
#include "rdp/track/data_process/rdp_interface.h"
#include "vdy/vdy_types.h"
#include "math.h"
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#elif defined (PC_DBG_FW)
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "math.h"
#include "app/vehicle/vdy/vdy_types.h"
#include "app/apar/apar_types.h"
#include "alg/track/rdp_interface.h"
#include "hal/rsp/rsp_types.h"
#include "other/temp.h"
#include <stdbool.h>
#else
#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "time.h"
#include "app/rsp/rsp_types.h"
#include "app/rdp/rdp_interface.h"
#include "app/vdy/vdy_types.h"
#include "app/rdp/rdp_clth_radar_lib.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/adas/customizedrequirements/adas_standard_params.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/cfg/cfg.h"
#include "common/include/vehicle_cfg.h"
#endif 

#ifndef M_PI
#define M_PI (3.1415926f)
#endif

//边线缓存长度
#define ROAD_SIDE_BUF_SIZE 15
#define ROAD_SIDE_MIN_CNT  7     // 原来是5
#define ROAD_SIDE_MIN_Y_CNT 5U   // 原来是4

#define ROAD_SIDE_MIN_DIF_Y 6   // 边线纵向距离差原来是4

#define MAX_OBJ_POINT_SIZE 10
#define ROAD_SIDE_LIFE_CNT_MIN 10U

static float aryRoadSideLine[ROAD_SIDE_BUF_SIZE];

#define ROAD_SIDE_DIS_BIN_CNT 14 // 每一个bin是0.5m ,这里是7m距离   ,之前版本是 12
#define OS_MODE 0

#define ROADSIDE_TIMER_CNT 40 //边线缓存

#if OS_MODE
#else
static int pCount[ROAD_SIDE_DIS_BIN_CNT];
static float sumLine[ROAD_SIDE_DIS_BIN_CNT];
static float py_max[ROAD_SIDE_DIS_BIN_CNT];
static float py_min[ROAD_SIDE_DIS_BIN_CNT];
static uint8_t minYCnt[ROAD_SIDE_DIS_BIN_CNT];
static float diffYDis; // 最大距离差
#endif


static RDP_roadDescription_t gRoadLine;    //道路描述

/**
 * @brief 道路描述结果，当前主要是输出边线位置
 * 
 * @return const RDP_roadDescription_t* 
 */
const RDP_roadDescription_t* RDP_getRoadDescriptionPointer(void)
{
    return &gRoadLine;
}



uint32_t roadLineLifeCnt = 0;       //边线的生命周期


typedef struct
{
    float x;
    float y;
    float x_speed;
    float y_speed;
    float angle;
    float org_spd;
    float obj_org_spd_x;
    float distance;
    float snr;
    float rc_dis; //到转向圆心的距离
    bool valid_flag;
    uint16_t rspDetStatus;  // 这个状态的 0x1 动态标志位是恒为 0 的.这个获取的是rsp的属性.
} OBJ_POINT_STRUCT;

static OBJ_POINT_STRUCT gTargetsPointList[ADAS_DET_POINTS];


#define MAX_ITERATIONS 50      // 
#define THRESHOLD 0.35           // 距离阈值，决定点是否是内点  暂定 30cm
#define MAX_LINEAR_CNT  50

// 定义点结构
typedef struct {
    float x;
    float y;
} Point;

Point points[MAX_LINEAR_CNT] = {0};

// 计算点到直线的垂直距离
float point_to_line_distance(Point p, float m, float b) {
    return fabs(p.y - (m * p.x + b)) / sqrt(m * m + 1);
}

// 用两个点拟合直线 y = mx + b
void fit_line(Point p1, Point p2, float *m, float *b) {
    *m = (p2.y - p1.y) / (p2.x - p1.x);  // 斜率
    *b = p1.y - (*m) * p1.x;             // 截距
}

// RANSAC算法拟合直线
bool ransac(Point *points, int n, float *m_best, float *b_best, int *inliers_best) {
    srand(time(NULL));
    int best_inliers_count = 0;
    float inliers_ratio = 0.0f;

    // 最大迭代次数
    for (int i = 0; i < MAX_ITERATIONS; i++) {
        // 随机选择两个点
        int idx1 = rand() % n;
        int idx2 = rand() % n;

        // 确保两个点不同
        while (idx2 == idx1) {
            idx2 = rand() % n;
        }

        Point p1 = points[idx1];
        Point p2 = points[idx2];

        // 拟合直线
        float m, b;
        fit_line(p1, p2, &m, &b);

        // 计算内点数量
        int inliers_count = 0;
        for (int j = 0; j < n; j++) {
            if (point_to_line_distance(points[j], m, b) < THRESHOLD) {
                inliers_count++;
            }
        }

        // 更新最佳模型
        if (inliers_count > best_inliers_count) {
            best_inliers_count = inliers_count;
            *m_best = m;
            *b_best = b;
            *inliers_best = inliers_count;
        }
    }
    inliers_ratio =  (float)best_inliers_count / n;
    //printf("Debug console inliers_ratio. %f\n", inliers_ratio);
    if (inliers_ratio < 0.45) {
        return false;
    }
    return true;
}

/**
 * @brief 将RDP内部维护的检测点列表进行极坐标变换
 * 
 * @param pRDP_DetObjectList  RDP内部维护的检测点列表
 * @param pfreezedVehDyncData VDY车身动态数据地址 
 */
static void ADAS_covSystemCoordinate(const RSP_DetObjectList_t *pRDP_DetObjectList, const VDY_DynamicEstimate_t *pfreezedVehDyncData)
{
    uint8_t l_r = (((gADASRadarId % 2) == 0) ? BSD_RADAR_LEFT : BSD_RADAR_RIGHT);
    float centerx;
    uint16_t i = 0U, k = 0U;

    //初始化缓存
    for (i = 0U; i < ADAS_DET_POINTS; i++)
    {
        gTargetsPointList[i].valid_flag = FALSE;
    }

    //进行坐标变换
    for (k = 0U, i = 0U; k < ADAS_DET_POINTS; k++)
    {
        //不处理小于0.3米和大于110米的目标
        if ((pRDP_DetObjectList->rspDetObject[k].rspDetRange <= 0.3) || (pRDP_DetObjectList->rspDetObject[k].rspDetRange > 110.0))
        {
            continue;
        }

        //去掉相对速度过大的目标，根据距离的不同，处理不同速度
        if (pRDP_DetObjectList->rspDetObject[k].rspDetRange < 15)
        {
            if (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity < (-25.0f))
            {
                continue;
            }
        }
        else if (pRDP_DetObjectList->rspDetObject[k].rspDetRange < 20)
        {
            if (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity < (-30.0f))
            {
                continue;
            }
        }
        else if (pRDP_DetObjectList->rspDetObject[k].rspDetRange < 25)
        {
            if (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity < (-35.0f))
            {
                continue;
            }
        }
        else if (pRDP_DetObjectList->rspDetObject[k].rspDetRange < 30)
        {
            if (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity < (-40.0f))
            {
                continue;
            }
        }
        else if (pRDP_DetObjectList->rspDetObject[k].rspDetRange < 35)
        {
            if (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity < (-45.0f))
            {
                continue;
            }
        }
        else if (pRDP_DetObjectList->rspDetObject[k].rspDetRange < 40)
        {
            if (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity < (-50.0f))
            {
                continue;
            }
        }
        else
        {
            if (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity < (-55.0f))
            {
                continue;
            }
        }

        gTargetsPointList[i].valid_flag = TRUE;
        gTargetsPointList[i].snr = pRDP_DetObjectList->rspDetObject[k].rspDetSNR;
        gTargetsPointList[i].angle = pRDP_DetObjectList->rspDetObject[k].rspDetAzimuthAngle + gadasInstallAzimuthAngle;

        if (l_r == BSD_RADAR_LEFT)
        {
            centerx = 0 - pfreezedVehDyncData->vdyCurveRadius; //右边雷达的半径对称处理
        }
        else
        {
             centerx = pfreezedVehDyncData->vdyCurveRadius; //右边雷达的半径对称处理
        }

        gTargetsPointList[i].org_spd = pRDP_DetObjectList->rspDetObject[k].rspDetVelocity;
        gTargetsPointList[i].distance = pRDP_DetObjectList->rspDetObject[k].rspDetRange;
        gTargetsPointList[i].x = sinf(gTargetsPointList[i].angle * (M_PI / 180.0)) * pRDP_DetObjectList->rspDetObject[k].rspDetRange;
        gTargetsPointList[i].y = cosf(gTargetsPointList[i].angle * (M_PI / 180.0)) * pRDP_DetObjectList->rspDetObject[k].rspDetRange;
        gTargetsPointList[i].rc_dis = sqrtf(((centerx - gTargetsPointList[i].x) * (centerx - gTargetsPointList[i].x)) + (gTargetsPointList[i].y * gTargetsPointList[i].y));
        gTargetsPointList[i].rspDetStatus = pRDP_DetObjectList->rspDetObject[k].rspDetStatus;

        if (pfreezedVehDyncData->vdyDriveDirection == 0) //本车非倒车时，P、N、D档位
        {
            gTargetsPointList[i].x_speed = 0;

            if ((gTargetsPointList[i].angle > -35) && (gTargetsPointList[i].angle < 125))
            {
                if ((REAR_LEFT_RADAR_ID == gADASRadarId) || (REAR_RIGHT_RADAR_ID == gADASRadarId)){
                    gTargetsPointList[i].y_speed = (gAdasSpeedInkmph * cosf(gTargetsPointList[i].angle * (M_PI / 180.0))) - (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity * 3.6);
                }else{
                    gTargetsPointList[i].y_speed = (gAdasSpeedInkmph * cosf(gTargetsPointList[i].angle * (M_PI / 180.0))) + (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity * 3.6);
                }            
            }
            else
            {
                gTargetsPointList[i].y_speed = 0;
            }

            //去掉异常的数据速度  暂时定义180kph不正常
            if (gTargetsPointList[i].y_speed > 180)
            {
                gTargetsPointList[i].valid_flag = FALSE;
            }
        }
        else
        {
            float d_spd;
            //y速度用于计算边线
            if ((gTargetsPointList[i].angle > -35.f) && (gTargetsPointList[i].angle < 125.f))
            {
                gTargetsPointList[i].y_speed = (gAdasSpeedInkmph * cosf(gTargetsPointList[i].angle * (M_PI / 180.0f))) - (pRDP_DetObjectList->rspDetObject[k].rspDetVelocity * 3.6f);
            }
            else
            {
                gTargetsPointList[i].y_speed = 0.0f;
            }

            d_spd = (gTargetsPointList[i].org_spd * 3.6f) - (fabsf(gAdasSpeedInkmph) * cosf(gTargetsPointList[i].angle * (M_PI / 180.0f)));
            if ((gTargetsPointList[i].angle > -35.0f) && (gTargetsPointList[i].angle < 125.0f))
            {
                //这里用cosf可能不对，x_speed目前没用到，先不管它
                gTargetsPointList[i].x_speed = d_spd / cosf(gTargetsPointList[i].angle * M_PI / 180.0f);
            }
            else
            {
                gTargetsPointList[i].x_speed = 0.0f;
            }
        }

        i++;

        if (i >= ADAS_DET_POINTS)
        {
            break;
        }
    }
}

/**
 * @brief 边线计算处理
 *  
 * @param pfreezedVehDyncData VDY车身动态数据地址 
 */
void ADAS_calSideLine(const VDY_DynamicEstimate_t *pfreezedVehDyncData)
{
    int i = 0;
    float tmpLine = 0.0f;
    int maxP = 0;
    int totalCount = 0;
    int maxCountIdx = 0;
    int maxlinenearcnt = 0;     // 线性拟合最大选点数量
    float max_y = 25;
    uint8_t covercnt = 0;    // 右侧雷达可能存在遮挡的计数值
    uint8_t movecnt = 0;     // 动态点数量
    static float sumguessheading = 0.0f; // , guessheading = 0.0f;
    static float guesscnt = 0;

#if OS_MODE
    int *pCount = (int *)pvPortMalloc(ROAD_SIDE_DIS_BIN_CNT * 4);
    float *sumLine = (float *)pvPortMalloc(ROAD_SIDE_DIS_BIN_CNT * 4);
    float *py_max = (float *)pvPortMalloc(ROAD_SIDE_DIS_BIN_CNT * 4);
    float *py_min = (float *)pvPortMalloc(ROAD_SIDE_DIS_BIN_CNT * 4);
    u8 *minYCnt = (u8 *)pvPortMalloc(ROAD_SIDE_DIS_BIN_CNT);
#else
    //定义为全局变量
#endif

    for (i = 0; i < ROAD_SIDE_DIS_BIN_CNT; i++)
    {
        pCount[i]  =  0;
        sumLine[i] =  0;
        py_max[i]  = -1000.0f;
        py_min[i]  =  1000.0f;
        minYCnt[i] =  0U;
        diffYDis = 0U;
    }

    //如果转弯半径小于 20 m，只计算y轴近距离目标
    if (fabsf(pfreezedVehDyncData->vdyCurveRadius) <= 20)
    {
        max_y = 10;
    }

    //边线处理
    for (i = 0; i < ADAS_DET_POINTS; i++)
    {
        if (
#if 0
            ((pfreezedVehDyncData->vdyDriveDirection == 0) && (fabsf(gTargetsPointList[i].y_speed)<6) ) &&
            (gAdasSpeedInkmph > 0) &&
#else
            ((fabsf(gTargetsPointList[i].y_speed) < 1.8) ||
            ((fabsf(gAdasSpeedInkmph) > 3.6) &&
             (fabsf(gTargetsPointList[i].y_speed) < 3.6))) &&
#endif
            (gTargetsPointList[i].valid_flag == TRUE) &&
            (gTargetsPointList[i].y < max_y) &&
            (gTargetsPointList[i].y > 1) &&
            (gTargetsPointList[i].x > MIN_SIDE_VALUE) &&
            (gTargetsPointList[i].x < (ROAD_SIDE_DIS_BIN_CNT * 0.5f)) &&
            (gTargetsPointList[i].angle > -78) &&
            (gTargetsPointList[i].angle < 78) &&
            (0 == (gTargetsPointList[i].rspDetStatus & POINT_STATUS_DYNAMIC_BMP)))
        {
            //倒车情况下，判断距离不能太远，否则可能不对
            if ((pfreezedVehDyncData->vdyDriveDirection == 1) && (gTargetsPointList[i].y > 5))
            {
                continue;
            }

            totalCount++;
            int n = 0;
            for (n = (ROAD_SIDE_DIS_BIN_CNT - 1); n >= 0; n--)
            {
                float side = 0.5f * n;
                if (gTargetsPointList[i].x > side)
                {
                    pCount[n]++;
                    sumLine[n] += gTargetsPointList[i].x;
                    if (py_max[n] < gTargetsPointList[i].y)
                    {
                        py_max[n] = gTargetsPointList[i].y; //y最大值
                    }
                    if (py_min[n] > gTargetsPointList[i].y)
                    {
                        py_min[n] = gTargetsPointList[i].y; //y最小值
                    }
                    //要求指定局域内的近距离y不能少点，暂定8米距离
                    if ((gTargetsPointList[i].y > 1) && (gTargetsPointList[i].y < 15))
                    {
                        minYCnt[n]++;
                    }
                    break;
                }
            }

        }
    }

    // 拟合护栏
    if (fabsf(gAdasSpeedInkmph) < 0.1f)
    {
        for (i = 0; i < ADAS_DET_POINTS; i++)
        {
            if (
                (gTargetsPointList[i].valid_flag == TRUE) &&
                (fabsf(gTargetsPointList[i].y_speed) < 3.6) &&
                (gTargetsPointList[i].y < 70.0f) &&
                (gTargetsPointList[i].y > 0.0f) &&
                (gTargetsPointList[i].distance > 5.0f)   &&        // 假定护栏距离自车不会太近. 贴近护栏侧不考虑
                (gTargetsPointList[i].angle > 0.0f) &&             // 只考虑本车侧的目标
                (gTargetsPointList[i].snr > 25.0f))
                {
                    points[maxlinenearcnt].x = gTargetsPointList[i].x;
                    points[maxlinenearcnt].y = gTargetsPointList[i].y;
                    if (maxlinenearcnt < (MAX_LINEAR_CNT-1))
                    {
                        maxlinenearcnt++;
                    }
                    else
                    {
                        break;
                    }
                }
            if (fabsf(gTargetsPointList[i].y_speed) > 3.6f)
            {
                movecnt++;
            }
        }
        // 自车刚静止的一小段时间计算一下.
        if ((maxlinenearcnt >= (MAX_LINEAR_CNT-1)) && (movecnt < 15) && (guesscnt < 100))
        {
            float m_best, b_best, theta = 0.0f, temp_heading = 0.0f;
            int inliers_best;
    
            // 调用RANSAC算法拟合直线
            if (ransac(points, MAX_LINEAR_CNT, &m_best, &b_best, &inliers_best))
            {
                theta = atanf(m_best);
                if (theta < FLOAT_EPS)
                {
                    temp_heading = -(90 + theta * (180.0 / M_PI));
                }
                else
                {
                    temp_heading = (90 - theta * (180.0 / M_PI));
                }
                // 拟合出来的结果用于DOW计算斜停角度
                sumguessheading += temp_heading;
                guesscnt++;
    
                gRoadLine.coeff[1] = (sumguessheading / guesscnt);
            }

            //printf("Debug console opened. %f\n", gRoadLine.coeff[1]);
        }
    }
    else
    {
        sumguessheading = 0.0f;
        // guessheading = 0.0f;
        guesscnt = 0;
    }

    // 判断右侧雷达是否可穿越.  道路测试时, 后方一般测4号雷达  前方一般测7号雷达. 不能用左右区分
    // 暂时先5号雷达判断遮挡.  压制TTC. 后期若有前向DOW 可能需要针对6号雷达做类似操作.
    if (((REAR_RIGHT_RADAR_ID == gADASRadarId) || (FRONT_LEFT_RADAR_ID == gADASRadarId)))
    {
        // 右侧一定范围内, 存在SNR大于一定值的静止目标.  判断多帧会更加稳定一些.
        // 需要考虑到是否会存在误判的情况.
        if (fabsf(gAdasSpeedInkmph) < 0.1f)
        {
            for (i = 0; i < ADAS_DET_POINTS; i++)
            {
                if ((gTargetsPointList[i].valid_flag == TRUE) &&
                    (gTargetsPointList[i].distance > 1.0f) &&
                    (gTargetsPointList[i].distance < 9.0f) &&
                    (gTargetsPointList[i].y < 8.0f) &&
                    (gTargetsPointList[i].y > -2.0f) &&
                    (gTargetsPointList[i].x > -1.0f) &&
                    (gTargetsPointList[i].x < 6.0f) &&
                    (gTargetsPointList[i].angle > -78.0f) &&
                    (gTargetsPointList[i].angle < 78.0f) &&
                    (gTargetsPointList[i].snr > 40))
                    {
                        if (fabsf(gTargetsPointList[i].y_speed) < 3.6f)
                        {
                            covercnt++;
                        }
                        else if (fabsf(gTargetsPointList[i].y_speed) > 3.6f)
                        {
                            if (covercnt > 0)
                            {
                                covercnt--;
                            }
                        }
                    }
            }
            // 可能存在遮挡.  累加场景计数.
            if (covercnt >= 6)
            {
                gRoadLine.covercnt++;
            }
            else if (covercnt < 3)      // 避免误检测,  要有递减机制
            {
                if (gRoadLine.covercnt > 0)
                {
                    gRoadLine.covercnt--;
                }
            }
        }
        else
        {
            gRoadLine.covercnt = 0;
        }
    }

    pCount[0] = 0;
    minYCnt[0] = 0U;
    for (i = 1; i < (ROAD_SIDE_DIS_BIN_CNT - 2); i++)
    {
        pCount[i] = pCount[i] + pCount[i + 1] + pCount[i + 2];
        minYCnt[i] = minYCnt[i] + minYCnt[i + 1] + minYCnt[i + 2];

        if (gRoadLine.coeff[0] <= MIN_SIDE_VALUE)
        {
            if (pCount[i] < ROAD_SIDE_MIN_CNT) //该区域点过少
            {
                pCount[i] = 0;
            }
        }
        else
        {
            if (pCount[i] < 2)
            {
                pCount[i] = 0;
            }
        }
    }

    pCount[ROAD_SIDE_DIS_BIN_CNT - 2] = 0;
    pCount[ROAD_SIDE_DIS_BIN_CNT - 1] = 0;
    minYCnt[ROAD_SIDE_DIS_BIN_CNT - 2] = 0U;
    minYCnt[ROAD_SIDE_DIS_BIN_CNT - 1] = 0U;

    maxP = pCount[0];
    for (i = 0; i < (ROAD_SIDE_DIS_BIN_CNT - 2); i++)
    {
        float dif_y = 0;
        if (maxP < pCount[i])
        {
            maxP = pCount[i]; //最大计数值
            maxCountIdx = i;  //最大值出现的位置
        }

        float max = (py_max[maxCountIdx] > py_max[maxCountIdx + 1]) ? py_max[maxCountIdx] : py_max[maxCountIdx + 1];
        max = (max > py_max[maxCountIdx + 2]) ? max : py_max[maxCountIdx + 2];

        float min = (py_min[maxCountIdx] < py_min[maxCountIdx + 1]) ? py_min[maxCountIdx] : py_min[maxCountIdx + 1];
        min = (min < py_min[maxCountIdx + 2]) ? min : py_min[maxCountIdx + 2];

        dif_y = fabsf(max - min);
        diffYDis = dif_y;

        //近距离已经有多个点，直接使用，但是要保证y的差值大于ROAD_SIDE_MIN_DIF_Y米
        if (/*(maxCountIdx <= 6) &&*/ (maxP >= ROAD_SIDE_MIN_CNT) && (minYCnt[maxCountIdx] >= ROAD_SIDE_MIN_Y_CNT) && (dif_y > ROAD_SIDE_MIN_DIF_Y))
        {
            break;
        }
    }

    if (maxP < 2)
    {
        tmpLine = 0;
    }
    else if ((minYCnt[maxCountIdx] < ROAD_SIDE_MIN_Y_CNT) && (roadLineLifeCnt < ROAD_SIDE_LIFE_CNT_MIN))
    {
        tmpLine = 0;
    }
    else
    {
        // 单位bin内最大索引  最大索引至少应该大于6 ? 
        if ((maxCountIdx > 0) && (maxCountIdx < (ROAD_SIDE_DIS_BIN_CNT - 2)) && (pCount[maxCountIdx]>6))
        {
            tmpLine = (sumLine[maxCountIdx] + sumLine[maxCountIdx + 1] + sumLine[maxCountIdx + 2]) / (pCount[maxCountIdx]);
        }
        else
        {
            tmpLine = 0;
        }
    }

    {
        float sum_line = 0.0f;
        int sum_count = 0;
        for (i = 0; i < (ROAD_SIDE_BUF_SIZE - 1); i++)
        {
            aryRoadSideLine[i] = aryRoadSideLine[i + 1];
        }
        aryRoadSideLine[i] = tmpLine;

        //快速移动边线
        if ((maxP >= 5) && (minYCnt[maxCountIdx] >= ROAD_SIDE_MIN_Y_CNT) && (diffYDis > ROAD_SIDE_MIN_DIF_Y))
        {
            for (i = (ROAD_SIDE_BUF_SIZE / 5); i < (ROAD_SIDE_BUF_SIZE); i++) //(ROAD_SIDE_BUF_SIZE / 2)
            {
                aryRoadSideLine[i] = tmpLine;
            }
        }

        for (i = 0; i < ROAD_SIDE_BUF_SIZE; i++)
        {
            if (aryRoadSideLine[i] > MIN_SIDE_VALUE)
            {
                sum_line += aryRoadSideLine[i];
                sum_count++;
            }
        }

        if (gRoadLine.coeff[0] <= MIN_SIDE_VALUE)
        {
            if (sum_count >= 5)
            {
                gRoadLine.coeff[0] = sum_line / sum_count;
            }
        }
        else
        {
            if (sum_count >= 5)
            {
                gRoadLine.coeff[0] = sum_line / sum_count;
            }
            else
            {
                gRoadLine.coeff[0] = 0.0f;
            }
        }

        //计算边线的时候计算了1.5m的宽度，这里补偿一点值
        if (fabsf(pfreezedVehDyncData->vdyCurveRadius) <= 150)
        {
            gRoadLine.coeff[0] -= 0.65;
        }
        else
        {
            gRoadLine.coeff[0] -= 0.35;
        }

        if (gRoadLine.coeff[0] <= MIN_SIDE_VALUE)
        {
            gRoadLine.coeff[0] = 0.0f;
        }
    }

#if OS_MODE
    vPortFree(pCount);
    vPortFree(sumLine);
    vPortFree(py_max);
    vPortFree(py_min);
    vPortFree(minYCnt);
#endif

    //如果速度为0 不处理边线
    //if (fabsf(gAdasSpeedInkmph) < 0.1)
    // {
    //     gRoadLine.coeff[0] = 0.0;
    // }
    //如果边线有效，则边线生命线++，否则归零
    if(gRoadLine.coeff[0] > MIN_SIDE_VALUE)
    {
        roadLineLifeCnt++;
    }
    else
    {
        roadLineLifeCnt = 0U;
    }
}

/**
 * @brief 边线模块主函数
 * 
 * @param pfreezedVehDyncData VDY车身动态数据地址 
 */
void ADAS_runRoadLine(const VDY_DynamicEstimate_t *pfreezedVehDyncData)
{
    const RSP_DetObjectList_t *RDP_DetObjectListAddr = RDP_getDetObjectListPointer();
    
    ADAS_covSystemCoordinate(RDP_DetObjectListAddr, pfreezedVehDyncData);

    ADAS_calSideLine(pfreezedVehDyncData);
}
