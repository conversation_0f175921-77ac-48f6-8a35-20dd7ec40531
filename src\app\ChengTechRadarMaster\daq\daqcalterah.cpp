﻿#include "daqcalterah.h"

#include "daqsaveworker.h"

#include "devices/ideviceworker.h"
#include "network/networktcpserver.h"
#include "network/networkudpserver.h"

#include "master/savemanager.h"
#include "analysis/framebinarysaveworker.h"
#include "analysis/protocol/analysisprotocolct410.h"

#include <iostream>
#include <QtEndian>
#include <QThread>
#include <QEventLoop>
#include <QTimer>

//QString DAQ::DAQCalterah::mIP = QString( "*************" );
//quint32 DAQ::DAQCalterah::mPort = 5001;

#define END_FRAME_ID 0x4F0
#define MB (1024 * 1024)
#define GET_VALUE_SPECIFIED_BYTE(value, offset) ((value >> (8 * offset)) & 0xFF)



namespace DAQ{

void networkCallback(void *pObj, unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp)
{
    DAQCalterah* pManager = static_cast<DAQCalterah*>(pObj);
    if (pManager) {
        pManager->recvData(remoteAddr, remotePort, localAddr, localPort, len, data, tcp);
    } else {
        return;
    }
}

void networkConnectCallback(void *pObj, unsigned long addr, unsigned short port, bool connected)
{
    DAQCalterah* pManager = static_cast<DAQCalterah*>(pObj);
    if (pManager) {
        pManager->clientConnected(addr, port, connected);
    } else {
        return;
    }
}

static uint32_t getNumberOfTcChannel(RadarCfgParams* pSensorCfg) {
    uint32_t nValueTmpTxGroup = 0, antiVelAmbEn = 0, numberOfTx = 0;
    if (NULL == pSensorCfg)
        return 0;
    antiVelAmbEn = pSensorCfg->antiVelambEn;
    for (int i = 0; i < pSensorCfg->txGroups.size(); i++) {
        nValueTmpTxGroup = nValueTmpTxGroup | pSensorCfg->txGroups[i];
    }
    if ((nValueTmpTxGroup & 0xF000) > 0) {
        numberOfTx = 4;
    }
    else if ((nValueTmpTxGroup & 0xF00) > 0) {
        numberOfTx = 3;
    }
    else if ((nValueTmpTxGroup & 0xF0) > 0) {
        numberOfTx = 2;
    }
    else if ((nValueTmpTxGroup & 0xF) > 0) {
        numberOfTx = 1;
    }
    else {
        return 0;
    }
    if (antiVelAmbEn == 1) {
        numberOfTx++;
    }
    return numberOfTx;
}

DAQCalterah::DAQCalterah( Core::SaveManager* saveMgr, Devices::Can::IDeviceWorker* device, QObject *parent )
    : QObject( parent )
{ 
    qRegisterMetaType<Network::NetworkData::Ptr>("Network::NetworkData::Ptr");

    mFrameSize = 0;
    mFrameCnt = 0;
    mDevice = device;
    mSaveMgr = saveMgr;
    mZeerFormat = false;
    mBlfFormat = true;
    mHBLF = INVALID_HANDLE_VALUE;

    mTcpSendLog.setFileName("./tcpSend_newVer.log");
    mTcpSendLog.open( QIODevice::WriteOnly );

    mDAQSaveWorker = new DAQSaveWorker;
}


DAQCalterah::~DAQCalterah()
{
    if( mTcpSendLog.isOpen() ){
        mTcpSendLog.close();
    }
    if( mDAQSaveWorker->isOpen() ){
        mDAQSaveWorker->close();
    }
    if( mHILCarFile.isOpen() ){
        mHILCarFile.close();
    }
    if( mHILAdcFile.isOpen() ){
        mHILAdcFile.close();
    }
}

void DAQCalterah::changeProtocol(int protocl)
{
    qDebug() << __FUNCTION__ << __LINE__ << protocl << mProtocol;
    if (mNetworkDevice && protocl != mProtocol) {
        if (mNetworkDevice->isOpened()) {
            mNetworkDevice->close();
        }
        delete mNetworkDevice;
        mNetworkDevice = 0;
    }
    mProtocol = (DAQProtocol)protocl;
    if (!mNetworkDevice) {
        Network::NetworkDeviecSetting setting;
        switch (mProtocol) {
        case Alps:
        case AlpsPro:
            qDebug() << __FUNCTION__ << __LINE__ << "UDP SERVER";
            mNetworkDevice = new Network::NetworkTCPServer();
            setting.mNetworkType = Network::NetworkDeviecSetting::TCPServer;
            break;
        case DC1000:
            qDebug() << __FUNCTION__ << __LINE__ << "UDP SERVER";
            mNetworkDevice = new Network::NetworkUDPServer();
            setting.mNetworkType = Network::NetworkDeviecSetting::UDPServer;
            setting.mRemoteIP = "**************";

            if (!mNetworkDeviceConfig) {
                setting.mRemotePort = 4096;
                mNetworkDeviceConfig = new Network::NetworkUDPServer();
                mNetworkDeviceConfig->setCallback(this, networkCallback, networkConnectCallback);
            }
            setting.mRemotePort = 4098;
            break;
        default:
            break;
        }
        mNetworkDevice->setNetworkDeviceSetting(setting);
        mNetworkDevice->setCallback(this, networkCallback, networkConnectCallback);
    }
}

void DAQCalterah::openOrCloseServer(const QString &ip, quint32 port, bool anlyIP)
{
    if (!mNetworkDevice) {
        emit showMsg("network device error!");
        return;
    }
    if (!mNetworkDevice || !mNetworkDevice->isOpened()) {
        Network::NetworkDeviecSetting setting = mNetworkDevice->getNetworkDeviceSetting();
        setting.mLocalIP = ip.toStdString();
        setting.mLocalPort = port;
        setting.mAnyIP = anlyIP;

        qDebug() << __FUNCTION__ << __LINE__ << setting.mNetworkType;
        mNetworkDevice->setNetworkDeviceSetting(setting);

        if (mNetworkDevice->open()) {
            if (mProtocol == DC1000 && mNetworkDeviceConfig) {
                setting.mRemotePort = 4096;
                setting.mLocalPort = 4096;
                mNetworkDeviceConfig->setNetworkDeviceSetting(setting);
                if (mNetworkDeviceConfig->open()) {
                    QByteArray data = getDC1000Data(SYSTEM_CONNECT_CMD_CODE);
                    if (mNetworkDeviceConfig->sendData( data.data(), data.size() ) > 0) {
                        emit serverStateChanged(true);
                    } else {
                        emit showMsg(QString("network open error! %1").arg(mNetworkDeviceConfig->errorString().c_str()));
                    }
                }
            } else {
                emit serverStateChanged(true);
            }
        } else {
            emit showMsg(QString("network open error! %1").arg(mNetworkDevice->errorString().c_str()));
        }
    } else {
        if (mNetworkDeviceConfig && mNetworkDeviceConfig->isOpened()) {
            mNetworkDeviceConfig->close();
        }
        if (mNetworkDevice && mNetworkDevice->close()) {
            emit serverStateChanged(false);
        }
    }
}

void DAQCalterah::startCollect(quint8 channel, quint8 collectType, quint64 frameByte, quint32 frameCnt , const QStringList &cfgFiles)
{
    mCollectType = (DAQCollectType)collectType;
    mChannel = channel;
    mFrameByte = frameByte;
    mFrameSize = frameByte / MB;
    mFrameCnt = frameCnt;
    mTotalByte = mFrameByte * mFrameCnt;

    emit showMsg(QString("CAN %1; Collect Type %2; %3 MB(%4 B) * %5 = %6 MB(%7 B)")
                 .arg(mChannel).arg(mCollectType)
                 .arg(mFrameSize).arg(mFrameByte)
                 .arg(mFrameCnt)
                 .arg(mFrameSize * mFrameCnt).arg(mTotalByte));

    setCfgFiles( cfgFiles );

    //adc文件
    if( !openSaveFile() ) {
        emit collectStateChanged( Failed );
        emit showMsg( QString( "open save file error!" ) );
        return;
    }

    //发送采集请求
    mCollectState = Running;
    if( !sendCollectRequest( frameCnt ) ) {
        closeSaveFile();
        emit collectStateChanged( Failed );
        emit showMsg( QString( "send collect request error!" ) );
    }
    emit collectStateChanged( Running );

}

void DAQCalterah::stopCollect()
{
    mCollectState = Stoped;
    closeSaveFile();
    sendStopCollectRequest();
    emit collectStateChanged(Stoped);
    emit showMsg( QString( "Stoped       %1: %2 MB(%3 B)" )
                  .arg(mCurrentByte / mFrameByte, 4, 10).arg((double)mCurrentByte / MB, 10, 'f').arg( mCurrentByte ));
}

void DAQCalterah::startHIL( quint8 calterahProtocol, quint8 channel, quint8 collectType,
                            quint64 frameByte, quint32 frameCnt,
                            const QStringList &cfgFiles, const QString &carFile,
                            bool bBlfFormat, const QString &adcFile, bool bZeerFormat)
{
    if( !mNetworkDevice || !mNetworkDevice->isOpened() ){
        finishHIL( false, "network client error" );
        return;
    }

    mProtocol = (DAQProtocol)calterahProtocol;
    mCollectType = (DAQCollectType)collectType;

    mBlfFormat = bBlfFormat;
    mZeerFormat = bZeerFormat;

    mChannel = channel;
    mFrameByte = frameByte;
    mFrameSize = frameByte / MB;
    mFrameCnt = frameCnt;
    mTotalByte = mFrameByte * mFrameCnt;
    setCfgFiles( cfgFiles );
    mCurrentByte = 0;
    mAdcIndex = 0;
    qDebug() << __FUNCTION__ << __LINE__ << mZeerFormat << mFrameCnt;

    emit showMsg(QString("CAN %1; HIL Type %2; %3 MB(%4 B) * %5 = %6 MB(%7 B) Zeer: %8")
                 .arg(mChannel).arg(mCollectType)
                 .arg(mFrameSize).arg(mFrameByte)
                 .arg(mFrameCnt)
                 .arg(mFrameSize * mFrameCnt).arg(mTotalByte).arg(mZeerFormat ? "true" : "false"));

    if( mHILCarFile.isOpen() )
        mHILCarFile.close();
    if( mHILAdcFile.isOpen() )
        mHILAdcFile.close();
    if( mHBLF != INVALID_HANDLE_VALUE ){
        BLCloseHandle( mHBLF );
        mHBLF = INVALID_HANDLE_VALUE;
    }

    if( !mBlfFormat ){ //binary格式
        mHILCarFile.setFileName( carFile );
        if( !mHILCarFile.open( QIODevice::ReadOnly ) ){
            finishHIL( false, QString( "open file error(%1)").arg(carFile) );
            return;
        }
        mHILCarStream.setDevice( &mHILCarFile );
        //读取文件头
        Analysis::FrameBinarySaveFormat_FHead fhead;
        fhead.loadFromStream( mHILCarStream );
    }else{ //blf格式
        //mHBLF = BLCreateFile( carFile.toStdString().c_str(), GENERIC_READ);
        mHBLF = BLCreateFile( carFile.toLocal8Bit().data(), GENERIC_READ);
        if ( INVALID_HANDLE_VALUE == mHBLF){
            finishHIL( false, QString( "open file error(%1)").arg(carFile) );
            return;
        }

        VBLFileStatisticsEx statistics = { sizeof( statistics)};
        BLGetFileStatisticsEx( mHBLF, &statistics); //这一行是不是可以不要
    }

    mHILAdcFile.setFileName( adcFile );
    if( !mHILAdcFile.open( QIODevice::ReadOnly ) ){
        finishHIL( false, QString( "open file error(%1)").arg(adcFile) );
        return;
    }
    mHILAdcStream.setDevice( &mHILAdcFile );

    //给雷达和dck板子发送回灌指令
    if( !sendHILRequest() ){
        finishHIL( false, QString( "send HIL Request error") );
        return;
    }

    mCollectState = Running;

    //回灌数据
    if( mHilPauseFrameNum < 0 ){
        HILData();
    }
}

void DAQCalterah::setHilPauseFramNum(int frameNum)
{
    mHilPauseFrameNum = frameNum;
}

QByteArray DAQCalterah::getTcpConfig(bool bHIL/*, DAQCalterahType type, QStringList cfgFiles*/)
{
    QByteArray data;

//    data = QByteArray::fromHex("00 de ad cc 03 08 00 0a 00 00 00 01 40 06 32 00 00 04 00 00 03 08 00 0a 00 00 00 01 40 06 32 00 00 04 00 00 03 00 00 0a 00 00  00 00 00 00 00 00 00 00 00 00 03 00 00 0a 00 00 00 00 00 00 00 00 00 00 00 00 02 00 04 ");
//    return data;

    if( !bHIL ){ //采集
        data.resize( 71 );
        data.fill( 0 );
        data[0] = 0x03;
        data[1] = 0xde;
        data[2] = 0xad;
        data[3] = 0xcc;
        return data;
    }

    if( Alps == mProtocol ){ // Alps
        data.resize( 71 );
        data.fill( 0 );
        if( bHIL ){//回灌
            if( !loadRadarCfgParam_Alps( /*cfgFiles*/ ) || !loadHILConfigParam() ){
                return QByteArray();
            }
//            emit showMsg( QString::fromLocal8Bit( "set dck param") );
            //QByteArray data = QByteArray(71, 0x00);
            data[0] = 0x00;
            data[1] = 0xde;
            data[2] = 0xad;
            data[3] = 0xcc;

            data[70] = 0x04;	//default param
            uint32_t offset = 0;
            for (int i = 0; i < mHilCfgParams.size(); i++) {
                offset = 16 * i;
                data[4 + offset] = 0x03;//0x03
                data[5 + offset] = 0x00;//src
                data[6 + offset] = 0x00;//type
                data[7 + offset] = GET_VALUE_SPECIFIED_BYTE(mFrameCnt, 0);//frame_count
                data[8 + offset] = GET_VALUE_SPECIFIED_BYTE(mFrameCnt, 1);//frame_count
                data[9 + offset] = GET_VALUE_SPECIFIED_BYTE(mFrameCnt, 2);//frame_count
                data[10 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpNmb, 0);//chirp_num
                data[11 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpNmb, 1);//chirp_num
                data[12 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpLen, 0);//chirp_len
                data[13 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpLen, 1);//chirp_len
                data[14 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].skipNmb, 0);//skip_nmb
                data[15 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].skipNmb, 1);//skip_nmb
                data[16 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].sampleNmb, 0);//sample_nmb
                data[17 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].sampleNmb, 1);//sample_nmb
//                qDebug("mHilCfgParams[%d] chirpNmb[%d] chirpLen[%d] skipNmb[%d] sampleNmb[%d]\r\n",
//                    i, mHilCfgParams[i].chirpNmb, mHilCfgParams[i].chirpLen, mHilCfgParams[i].skipNmb, mHilCfgParams[i].sampleNmb);
                //qDebug() <<__FUNCTION__<<__LINE__<< offset << 17 + offset;
//                qDebug() << __FUNCTION__ << __LINE__ << mFrameCnt;
            }
            data[68] = mProfileConfigParams.size();	//must be right
            data[69] = mProfileConfigParams.size(); //must be right
        }
    }else{ //Alps Pro
        if( !loadRadarCfgParam_AlpsPro( /*cfgFiles*/ ) || !loadHILConfigParam() ){
            return QByteArray();
        }
#if 0
        data = QByteArray::fromHex("00 de ad cc 03 10 00 01 00 00"
                                              "00 08 40 06 32 00 00 04 00 00 19 01 98 04 00 02"
                                              "00 01 80 00 00 00 00 00 00 00 00 00 00 00 00 00"
                                              "00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"
                                              "00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"
                                              "00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"
                                              "00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"
                                              "00 00 00 00 00 00 00 00 00 00 00 00 00 00 01 00"
                                              "04 04");
#elif 1
        data.resize( 124 );
        data.fill( 0 );
        quint8 *pData = (quint8 *)data.data();
        pData[0] = 0x00;
        pData[1] = 0xde;
        pData[2] = 0xad;
        pData[3] = 0xcc;

        for (int i = 0; i < mHilCfgParams.size(); i++) {
            pData += 29 * i;
            pData[4] = bHIL ? 0x03 : 0x00; // 0-ADC; 3-HIL
            pData[5] = GET_VALUE_SPECIFIED_BYTE(mFrameByte / 1024, 1);
            pData[6] = GET_VALUE_SPECIFIED_BYTE(mFrameByte / 1024, 0);
            pData[7] = GET_VALUE_SPECIFIED_BYTE(mFrameCnt, 0);//frame_count
            pData[8] = GET_VALUE_SPECIFIED_BYTE(mFrameCnt, 1);//frame_count
            pData[9] = GET_VALUE_SPECIFIED_BYTE(mFrameCnt, 2);//frame_count
            pData[10] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpNmb, 0);//chirp_num
            pData[11] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpNmb, 1);//chirp_num
            pData[12] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpLen, 0);//chirp_len
            pData[13] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpLen, 1);//chirp_len
            pData[14] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].skipNmb, 0);//skip_nmb
            pData[15] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].skipNmb, 1);//skip_nmb
            pData[16] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].sampleNmb, 0);//sample_nmb
            pData[17] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].sampleNmb, 1);//sample_nmb
            pData[18] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].ena, 0);
            pData[19] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].enb, 0);
            pData[20] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].sam_freq, 0);
            pData[21] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].rx_time * 10, 0);
            pData[22] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].rx_time * 10, 1);
            pData[23] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].rng_fft, 0);
            pData[24] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].rng_fft, 1);
            pData[25] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].vel_fft, 0);
            pData[26] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].vel_fft, 1);
            pData[27] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].ddm, 0);
            pData[28] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirp_delay, 0);
        }
        pData[120] = mProfileConfigParams.size();	// CfgCount
        pData[121] = mCollectType;   // hil Type 0-null; 1-fft1d; 2-fft2d
        pData[122] = 0x04;   //
        pData[123] = 0x04;   //
#else
        data.resize( 71 );
        data.fill( 0 );
        data[0] = 0x00;
        data[1] = 0xde;
        data[2] = 0xad;
        data[3] = 0xcc;

        data[70] = 0x04;	//default param
        uint32_t offset = 0;
        for (int i = 0; i < mHilCfgParams.size(); i++) {
            offset = 16 * i;
            //data[4 + offset] = 0x03;//0x03
            if( bHIL ){//回灌
                data[4 + offset] = 0x03;
            }else{//采集
                data[4 + offset] = 0x00;
            }
            //data[5 + offset] = 0x00;//src
            data[5 + offset] = 0x08;//src
            data[6 + offset] = 0x00;//type
            data[7 + offset] = GET_VALUE_SPECIFIED_BYTE(mFrameCnt, 0);//frame_count
            data[8 + offset] = GET_VALUE_SPECIFIED_BYTE(mFrameCnt, 1);//frame_count
            data[9 + offset] = GET_VALUE_SPECIFIED_BYTE(mFrameCnt, 2);//frame_count
            data[10 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpNmb, 0);//chirp_num
            data[11 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpNmb, 1);//chirp_num
            data[12 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpLen, 0);//chirp_len
            data[13 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].chirpLen, 1);//chirp_len
            data[14 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].skipNmb, 0);//skip_nmb
            data[15 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].skipNmb, 1);//skip_nmb
            data[16 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].sampleNmb, 0);//sample_nmb
            data[17 + offset] = GET_VALUE_SPECIFIED_BYTE(mHilCfgParams[i].sampleNmb, 1);//sample_nmb
        }
        data[68] = mProfileConfigParams.size();	//must be right
        data[69] = 0x00;
#endif
    }
    return data;
}

QByteArray DAQCalterah::getDC1000Data(quint16 command)
{
    QByteArray data = QByteArray::fromHex("5A A5");
    data.append((const char*)&command, 2);
    QByteArray body;
    switch (command) {
    case RESET_FPGA_CMD_CODE             :  // 0x01
        break;
    case RESET_AR_DEV_CMD_CODE           :  // 0x02
        break;
    case CONFIG_FPGA_GEN_CMD_CODE        :  // 0x03
        body = QByteArray::fromHex("01 02 01 02 03 1e");
        break;
    case CONFIG_EEPROM_CMD_CODE          :  // 0x04
        break;
    case RECORD_START_CMD_CODE           :  // 0x05
        break;
    case RECORD_STOP_CMD_CODE            :  // 0x06
        break;
    case PLAYBACK_START_CMD_CODE         :  // 0x07
        break;
    case PLAYBACK_STOP_CMD_CODE          :  // 0x08
        break;
    case SYSTEM_CONNECT_CMD_CODE         :  // 0x09
        break;
    case SYSTEM_ERROR_CMD_CODE           :  // 0x0A
        break;
    case CONFIG_PACKET_DATA_CMD_CODE     :  // 0x0B
        body = QByteArray::fromHex("c0 05 6a 18 e8 03");
        break;
    case CONFIG_DATA_MODE_AR_DEV_CMD_CODE:  // 0x0C
        break;
    case INIT_FPGA_PLAYBACK_CMD_CODE     :  // 0x0D
        break;
    case READ_FPGA_VERSION_CMD_CODE      :  // 0x0E
        break;
    default:
        break;
    }
    quint16 size = body.size();
    data.append((const char *)(&size), 2);
    data.append(body);
    data.append(QByteArray::fromHex("AA EE"));


    qDebug() << __FUNCTION__ << __LINE__ << data.toHex( ' ' );

    return data;
}

bool DAQCalterah::sendCollectRequest( quint32 frameCnt )
{
    if( !mNetworkDevice || !mNetworkDevice->isOpened() ){
        emit showMsg( QString( "network connect error!" ) );
        return false;
    }

    switch (mProtocol) {
    case Alps:
    case AlpsPro:
    {
        QByteArray data = getTcpConfig( false );
        if( data.length() == 0 ){
            emit showMsg( QString( "get cfg param error" ) );
            return false;
        }
        qDebug() << __FUNCTION__ << __LINE__ << data.toHex( ' ' );
        if (mNetworkDevice->sendData( data.data(), data.size() ) <= 0) {
            emit showMsg( QString( "send cfg param error" ) );
            return false;
        }
    }
        break;
    case DC1000:
    {
        QByteArray data = getDC1000Data(RECORD_START_CMD_CODE);
        if (mNetworkDeviceConfig->sendData( data.data(), data.size() ) < 0) {
            emit showMsg(QString("network send error! %1").arg(mNetworkDeviceConfig->errorString().c_str()));
            return false;
        }
    }
        break;
    default:
        break;
    }

    QByteArray data( 8, (char)0 );
    data[0] = 0x43;
    data[1] = 0x54;
    data[2] = 0x43;
    data[3] = 0x54;
    frameCnt = qToBigEndian( frameCnt );
    data[4] = ( (char*)&frameCnt )[0];
    data[5] = ( (char*)&frameCnt )[1];
    data[6] = ( (char*)&frameCnt )[2];
    data[7] = ( (char*)&frameCnt )[3];
    quint64 id = 0x3f9;
    switch (mCollectType) {
    case ADC:
        id = 0x3f9;
        break;
    case _1DFFT:
        id = 0x3fA;
        break;
    case _2DFFT:
        id = 0x3fB;
        break;
    default:
        break;
    }
    if( !mDevice->sendFrame( mChannel, id, data ) ){
        emit showMsg( QString( "send can error!" ) );
        return false;
    } else {
        emit showMsg(QString( "send can: %1" ).arg(data.toHex(' ').data()));
    }


    mRecvBuf.clear();
    //emit showMsg( QString( data.toHex() ) );
    return true;
}

bool DAQCalterah::sendStopCollectRequest()
{
    //发送结束采集指令
    //return mDevice->sendFrame(mChannel, 0x304, QByteArray::fromHex("4354435400000001")/*, true*/);
    //return mDevice->sendFrame(mChannel, 0x3FB, QByteArray::fromHex("4354435400000000")/*, true*/);
    switch (mProtocol) {
    case Alps:
    case AlpsPro:
        break;
    case DC1000:
    {
        QByteArray data = getDC1000Data(RECORD_STOP_CMD_CODE);
        if (mNetworkDeviceConfig->sendData( data.data(), data.size() ) < 0) {
            emit showMsg(QString("network config send error! %1").arg(mNetworkDeviceConfig->errorString().c_str()));
        }
    }
        break;
    }
    return true;
}

bool DAQCalterah::openSaveFile()
{
    closeSaveFile();
    QString fileName = QString("%1/ADC_%2.dat").arg(mSaveMgr->projectSavePath())
                       .arg(QDateTime::currentDateTime().toLocalTime().toString("yyyyMMdd-hhmmsszzz"));
    if( !mDAQSaveWorker->open( fileName ) ){
        emit showMsg( QString( "open file error!" ) );
        emit collectStateChanged( Failed );
        return false;
    }
    mCurrentByte = 0;
    mCurrentFrame = 0;
    mSequenceNumber = 0;
    mPackageCount = 0;
    mByteCount = 0;
    return true;
}

bool DAQCalterah::closeSaveFile()
{
    if( mDAQSaveWorker->isOpen() )
        mDAQSaveWorker->close();
    return true;
}

bool DAQCalterah::sendHILRequest()
{
    //雷达重启
    QByteArray da = QByteArray::fromHex("4354435400000000");
    if( !mDevice->sendFrame( mChannel, 0x3FB, da ) ){
        //qDebug() << __FUNCTION__ << __LINE__ << "send "
        return false;
    }
    emit showMsg( QString::fromLocal8Bit( "wait radar reboot") );
    QThread::msleep( 3000 );//等待雷达重启
    emit showMsg( QString::fromLocal8Bit( "radar enter hil mode") );


    //进入hil模式
    da = QByteArray::fromHex("43544354");
    quint32 frameCnt = qToBigEndian( mFrameCnt );
    da.append( ( (char*)&frameCnt )[0] );
    da.append( ( (char*)&frameCnt )[1] );
    da.append( ( (char*)&frameCnt )[2] );
    da.append( ( (char*)&frameCnt )[3] );
    qDebug() << __FUNCTION__ << __LINE__ << mFrameCnt << frameCnt << da.toHex(' ');
    //da = QByteArray::fromHex("43544354000000C8");


    QByteArray data = getTcpConfig( true );
    if( data.length() == 0 ){
        emit showMsg( QString( "get cfg param error" ) );
        return false;
    }
    if (!mNetworkDevice || mNetworkDevice->sendData( data.data(), data.size() ) <= 0) {
        emit showMsg(QString("network send error! %1").arg(mNetworkDevice->errorString().c_str()));
        return false;
    }

    qDebug() << __FUNCTION__ << __LINE__ << data.toHex(' ');

    mTcpSendLog.write( data.toHex(' ') );
    char c = '\n';
    mTcpSendLog.write( &c, 1 );
    if( !mDevice->sendFrame( mChannel, 0x3FA, da ) /*HIL*/){
        return false;
    }
    return true;
}

void DAQCalterah::HILData()
{
    if( !mHILAdcFile.isOpen() || ( !mHILCarFile.isOpen() && mHBLF == INVALID_HANDLE_VALUE ) ) {
        emit showMsg("File not open ...");
        return;
    }

    emit showMsg("HIL DATA ...");

    while ( mCurrentByte < mTotalByte ) {
        if( mHILAdcStream.atEnd() ){
            finishHIL( false, QString( "ADC FILE frame cnt error[mCurrentByte=%1,mTotalByte=%2]" )
                         .arg(mCurrentByte).arg(mTotalByte) );
            return;
        }
        if( !mBlfFormat ){
            if( mHILCarStream.atEnd() ){
                finishHIL( false, QString( "CAR FILE frame cnt error" ) );
                return;
            }
        }

        //回灌1帧
        if( !HILData_ADC() /*|| !HILData_Car()//先不下发车身数据*/ ) {
            finishHIL( false, QString( "ADC send error!" ) );
            return;
        }

        if( mCurrentByte % mFrameByte  == 0 ){
            emit showMsg( QString( "HIL...   %1: %2 MB(%3 B)" )
                          .arg(mCurrentByte / mFrameByte, 4, 10).arg((double)mCurrentByte / MB, 10, 'f').arg( mCurrentByte ));
        }

        //等待50ms  再发送下一帧
        if( mCurrentByte % ( (qint32)mFrameSize * MB )== 0 ){
            //emit showMsg( QString( "wait 60ms, currentByte=%1").arg(mCurrentByte) );
            QEventLoop loop;
            QTimer timer;
            timer.setSingleShot( true );
            connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
            //timer.start( 60 );
            timer.start( 100 );
            loop.exec();
            timer.stop();
        }

    }

    if( mCurrentByte >= mTotalByte ){
        finishHIL( true, QString( "HIL success [totalByte=%1,sendByte=%2]" ).arg(mTotalByte).arg(mCurrentByte) );
    }
}

bool DAQCalterah::HILData_ADC()
{
    if( !mNetworkDevice || !mNetworkDevice->isOpened() ){
        finishHIL( false, " network client error " );
        return false;
    }

    quint64 biyteSend = 0;
    QByteArray data( 1028, (char)0 );
    int i = 0;
    while( !mHILAdcStream.atEnd() && biyteSend < mFrameByte ){

        int size = 0;
        data[0] = 6;//head;
        data[1] = ( mAdcIndex & 0xFF0000 ) >> 16;
        data[2] = ( mAdcIndex & 0x00FF00 ) >> 8;
        data[3] = ( mAdcIndex & 0x0000FF );
        if( !mZeerFormat ){
            size = mHILAdcStream.readRawData( data.data() + 4, 1024 );
        }else{
            size = mHILAdcStream.readRawData( data.data(), 1028 );
        }

        if (mNetworkDevice->sendData( data.data(), data.size() ) <= 0) {
            qDebug() << __FUNCTION__ << __LINE__ << "message";
            return false;
        }

        mTcpSendLog.write( data.toHex(' ') + '\n' );

        mCurrentByte += 1024;
        biyteSend += 1024;
        mAdcIndex++;
        qDebug()  << __FUNCTION__ << __LINE__ << ++i << mAdcIndex << biyteSend << mFrameByte << mCurrentByte << mZeerFormat << size;
    }

    if( mZeerFormat ){//泽尔格式跳过2字节的校验数据
        mHILAdcStream.skipRawData( 1 );
    }

    return true;
}

bool DAQCalterah::HILData_Car()
{
    if( mBlfFormat ){
        return HILData_Car_Blf();
    }else{
        return HILData_Car_Binary();
    }
}

bool DAQCalterah::HILData_Car_Binary()
{
    bool bRet = true;
    Analysis::FrameBinarySaveFormat_Head head;
    head.loadFromStream( mHILCarStream );

    QByteArray ba;
    ba.resize( head.bodyLen );
    mHILCarStream.readRawData( ba.data(), head.bodyLen );

    Analysis::Protocol::AnalysisProtocolCT410 protocol( NULL, NULL );
    QDataStream s( &ba, QIODevice::ReadOnly );
    while( !s.atEnd() ){
        Analysis::FrameBinarySaveFormat_Body body;
        body.loadFromStream( s );

        Devices::Can::CanFrame frame = body.toCanFrame();
        bRet = bRet & mDevice->sendFrame( mChannel, frame.id(), frame.data() );
    }
    if( !bRet )
        finishHIL( false, " send Frame error " );
    return bRet;
}

bool DAQCalterah::HILData_Car_Blf()
{
//    qDebug() << __FUNCTION__ << __FILE__ << "lxw debug";
    bool bRun = true;

    while( bRun ){
        bool bSuccess = false;
        bool bSend = true;
        VBLObjectHeaderBase base;
        VBLCANMessage can_msg;
        VBLCANFDMessage canfd_msg;
        VBLCANFDMessage64 canfd_msg_64;
        BLPeekObject( mHBLF, &base);

        switch ( base.mObjectType)
        {
        case BL_OBJ_TYPE_CAN_MESSAGE:
            /* read CAN message */
            can_msg.mHeader.mBase = base;
            bSuccess = BLReadObjectSecure( mHBLF, &can_msg.mHeader.mBase, sizeof(can_msg));
            /* free memory for the CAN fd message */
            if( bSuccess) {
                QByteArray arr /*= QByteArray::fromRawData*/( (char*)can_msg.mData, 8 );
                Devices::Can::CanFrame frame( 0, (quint16)can_msg.mChannel,
                                              Devices::Can::CanFrame::RX,
                                              can_msg.mID,
                                              arr, can_msg.mHeader.mObjectTimeStamp  );
                if( frame.idN() != 0x3F0 ){
                    //continue;
                }else{
                    bRun = false;
                    bSend = bSend & mDevice->sendFrame( mChannel, frame.id(), frame.data() );
                }

//                if( frame.idN() == END_FRAME_ID ){
//                    bRun = false;
//                }
                BLFreeObject( mHBLF, &can_msg.mHeader.mBase);
            }
            break;
        case BL_OBJ_TYPE_CAN_FD_MESSAGE_64:
            /* read CAN fd message */
            canfd_msg_64.mHeader.mBase = base;
            bSuccess = BLReadObjectSecure( mHBLF, &canfd_msg_64.mHeader.mBase, sizeof(canfd_msg_64));
            /* free memory for the CAN fd message */
            if( bSuccess) {
                QByteArray arr /*= QByteArray::fromRawData*/( (char*)canfd_msg_64.mData, (quint8)canfd_msg_64.mValidDataBytes );
                if( canfd_msg_64.mValidDataBytes == 0 ){
                    arr = QByteArray( (char*)canfd_msg_64.mData,  Devices::Can::CanFrame::DLCToLength( (quint8)canfd_msg_64.mDLC ) );
                }
                Devices::Can::CanFrame frame( 0, /*(quint16)canfd_msg_64.mChannel*/ 0,
                                              Devices::Can::CanFrame::RX,
                                              canfd_msg_64.mID,
                                              arr, canfd_msg_64.mHeader.mObjectTimeStamp );
                if( frame.idN() != 0x3F0 ){
                    //continue;
                }else{
                    bRun = false;
                    bSend = bSend & mDevice->sendFrame( mChannel, frame.id(), frame.data() );
                }

//                if( frame.idN() == END_FRAME_ID ){
//                    bRun = false;
//                }
                BLFreeObject( mHBLF, &canfd_msg_64.mHeader.mBase);
            }
            break;
        case BL_OBJ_TYPE_CAN_FD_MESSAGE:
            /* read CAN fd message */
            canfd_msg.mHeader.mBase = base;
            bSuccess = BLReadObjectSecure( mHBLF, &canfd_msg.mHeader.mBase, sizeof(canfd_msg));
            /* free memory for the CAN fd message */
            if( bSuccess) {
                QByteArray arr /*= QByteArray::fromRawData*/( (char*)canfd_msg.mData, (quint8)canfd_msg.mValidDataBytes );
                if( canfd_msg.mValidDataBytes == 0 ){
                    arr = QByteArray( (char*)canfd_msg.mData, Devices::Can::CanFrame::DLCToLength( (quint8)canfd_msg.mDLC ) );
                }
                Devices::Can::CanFrame frame( 0, (quint16)canfd_msg.mChannel,
                                              Devices::Can::CanFrame::RX,
                                              canfd_msg.mID,
                                              arr, canfd_msg.mHeader.mObjectTimeStamp  );
                if( frame.idN() != 0x3F0 ){
                    //continue;
                }else{
                    bRun = false;
                    bSend = bSend & mDevice->sendFrame( mChannel, frame.id(), frame.data() );
                }

//                if( frame.idN() == END_FRAME_ID ){
//                    bRun = false;
//                }
                BLFreeObject( mHBLF, &canfd_msg.mHeader.mBase);
            }
            break;
        default:
            /* skip all other objects */
            bSuccess = BLSkipObject( mHBLF, &base);
            break;
        }

        if( !bSuccess ){
            finishHIL( false, " file format error " );
            return false;
        }
        if( !bSend ){
            finishHIL( false, " send Frame error " );
            return false;
        }
    }


    return true;
}

void DAQCalterah::finishHIL( bool bsuccess, const QString& msg )
{
    mCollectState = bsuccess ? Success : Failed;
    emit showMsg( msg );
    emit HILFinished( bsuccess );
    if( mHILAdcFile.isOpen() )
        mHILAdcFile.close();
    if( mHILCarFile.isOpen() )
        mHILCarFile.close();
    if( mHBLF != INVALID_HANDLE_VALUE ){
        BLCloseHandle( mHBLF );
        mHBLF = INVALID_HANDLE_VALUE;
    }
}

void DAQCalterah::setCfgFiles(const QStringList &files)
{
    mCfgFiles = files;
    mCfgFiles.sort();
    qDebug() << __FUNCTION__ << __LINE__ << mCfgFiles;
}

bool DAQCalterah::loadRadarCfgParam_Alps(/*const QStringList &files*/)
{
    mProfileConfigParams.clear();
    mHilCfgParams.clear();
    foreach (const QString &filename, mCfgFiles){
        QFile file(filename);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)){
            return false;
        }

        QString text = file.readAll();
        file.close();

        RadarCfgParams radarCfgParams;

        QRegExp rx("\\s*\\.(\\w+)\\s*=\\s*((\\{.*\\})|(\\w+)),");
        rx.setMinimal(true);
        QMap<QString, QString> params;
        int pos = 0;
        while ((pos = rx.indexIn(text, pos)) != -1) {
            params[rx.cap(1)] = rx.cap(2);
            pos += rx.matchedLength();
        }

        radarCfgParams.velNfft = params["vel_nfft"].toUInt();
        radarCfgParams.adcSampleStart = params["adc_sample_start"].toUInt();
        radarCfgParams.adcFreq = params["adc_freq"].toUInt();//
        radarCfgParams.decFactor = params["dec_factor"].toUInt();
        radarCfgParams.rngNfft = params["rng_nfft"].toUInt();
        radarCfgParams.antiVelambEn = params["de_vel_amb"].toLower() == "true" ? true : false;
        QString tx_groups = params["tx_groups"];//
        tx_groups.replace(QRegExp("\\{|\\}"), "");
        tx_groups.remove(QRegExp("\\s"));
        QStringList groups = tx_groups.split(",");
        foreach (const QString &group, groups)
        {
            radarCfgParams.txGroups << group.toUInt(0, 16);
        }
        radarCfgParams.numOfTcChannel = getNumberOfTcChannel(&radarCfgParams);
        mProfileConfigParams << radarCfgParams;
    }

    return true;
}

bool DAQCalterah::loadRadarCfgParam_AlpsPro(/*const QStringList &files*/)
{
    if( mCfgFiles.size() % 2 != 0 ){
        emit showMsg( QString( "cfg file num error") );
        return false;
    }

    QStringList bb_cfgs;
    QStringList radio_cfgs;

    bb_cfgs = mCfgFiles.mid( 0, mCfgFiles.size() / 2 ); //取前一半的文件名
    radio_cfgs = mCfgFiles.mid( mCfgFiles.size() / 2 ); //取后一半的文件名

    if( bb_cfgs.size() != radio_cfgs.size() || bb_cfgs.size() > 4 ){
        emit showMsg( QString( "cfg file num error") );
        return false;
    }

    mProfileConfigParams.clear();

    for( int i=0; i<bb_cfgs.size(); i++ ){
        QFile bb_file( bb_cfgs[i] );
        QFile radio_file( radio_cfgs[i] );
        if( !bb_file.open(QIODevice::ReadOnly | QIODevice::Text)
            || !radio_file.open(QIODevice::ReadOnly | QIODevice::Text)  ){
            return false;
        }

        QString bb_text = bb_file.readAll();
        QString radio_text = radio_file.readAll();
        bb_file.close();
        radio_file.close();

        QRegExp rx("\\s*\\.(\\w+)\\s*=\\s*((\\{.*\\})|(\\w+)|(-?\\d+\\.\\d+)),");
        rx.setMinimal(true);
        QMap<QString, QString> params;
        int pos = 0;
        while ((pos = rx.indexIn(bb_text, pos)) != -1) {
            params[rx.cap(1)] = rx.cap(2);
            pos += rx.matchedLength();
        }
        pos = 0;
        while ((pos = rx.indexIn(radio_text, pos)) != -1) {
            params[rx.cap(1)] = rx.cap(2);
            pos += rx.matchedLength();
        }

        RadarCfgParams radarCfgParams;

        radarCfgParams.velNfft = params["vel_nfft"].toUInt();
        radarCfgParams.adcSampleStart = params["adc_sample_start"].toUInt();
        radarCfgParams.adcFreq = params["adc_freq"].toUInt();
        radarCfgParams.decFactor = params["dec_factor"].toUInt();
        radarCfgParams.rngNfft = params["rng_nfft"].toUInt();
        radarCfgParams.antiVelambEn = params["de_vel_amb_en"].toLower() == "true" ? true : false;

        radarCfgParams.ena         = params[""].toUInt();
        radarCfgParams.enb         = params[""].toUInt();
//        radarCfgParams.rx_time     = params[""].toUInt();
        radarCfgParams.ddm         = params[""].toUInt();
        radarCfgParams.chirp_delay = params[""].toUInt();

        radarCfgParams.adc_sample_start = params["adc_sample_start"].toUInt();
        radarCfgParams.adc_sample_end = params["adc_sample_end"].toUInt();

        radarCfgParams.rx_time = radarCfgParams.adc_sample_end - radarCfgParams.adc_sample_start;

        QString tx_groups = params["tx_groups"];
        tx_groups.replace(QRegExp("\\{|\\}"), "");
        tx_groups.remove(QRegExp("\\s"));
        QStringList groups = tx_groups.split(",");
        foreach (const QString &group, groups)
        {
            radarCfgParams.txGroups << group.toUInt(0, 16);
        }
        radarCfgParams.numOfTcChannel = getNumberOfTcChannel(&radarCfgParams);

        mProfileConfigParams << radarCfgParams;
    }

    return true;
}

bool DAQCalterah::loadHILConfigParam()
{
    mHilCfgParams.clear();
    for( int i=0; i<mProfileConfigParams.size(); i++ ){
        RadarCfgParams& radarCfgParams = mProfileConfigParams[i];

        HilCfgParams hilCfgParams;
        //velNfft * numOfTcChannel
        hilCfgParams.chirpNmb = radarCfgParams.numOfTcChannel * radarCfgParams.velNfft;
        hilCfgParams.chirpLen = 1600;//1600; //TBD
        hilCfgParams.sampleNmb = radarCfgParams.rngNfft;
        //adcSampleStart * adc_freq * dec_factor
        hilCfgParams.skipNmb = radarCfgParams.adcSampleStart * radarCfgParams.adcFreq * radarCfgParams.decFactor;
        //计算package的size
        hilCfgParams.frameSize = hilCfgParams.chirpNmb * hilCfgParams.sampleNmb * 4 * 2;
        hilCfgParams.packageNum = hilCfgParams.frameSize / 1024/*DCK_BOARD_PACKAGE_DATA_SIZE*/;
        if (hilCfgParams.skipNmb % 2)
            hilCfgParams.skipNmb -= 1;

        hilCfgParams.sam_freq = radarCfgParams.adcFreq;
        hilCfgParams.rng_fft = radarCfgParams.rngNfft;
        hilCfgParams.vel_fft = radarCfgParams.velNfft;

        hilCfgParams.adc_sample_start = radarCfgParams.adc_sample_start;
        hilCfgParams.adc_sample_end = radarCfgParams.adc_sample_end;
        hilCfgParams.data_len = hilCfgParams.sam_freq * (hilCfgParams.adc_sample_end - hilCfgParams.adc_sample_start);

        hilCfgParams.ena =         radarCfgParams.ena;
        hilCfgParams.enb =         radarCfgParams.enb;
        hilCfgParams.rx_time =     radarCfgParams.rx_time;
        hilCfgParams.ddm =         radarCfgParams.ddm;
        hilCfgParams.chirp_delay = radarCfgParams.chirp_delay;

        hilCfgParams.isValid = 1;
        mHilCfgParams << hilCfgParams;
    }
    return true;
}
void DAQCalterah::recvData(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp)
{
    switch (mProtocol) {
    case Alps:
    case AlpsPro:
        parseAlpsData(remoteAddr, remotePort, localAddr, localPort, len, data, tcp);
        break;
    case DC1000:
        parseDC1000Data(remoteAddr, remotePort, localAddr, localPort, len, data, tcp);
        break;
    }
}

void DAQCalterah::parseAlpsData(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp)
{
    QMutexLocker locker(&mMutex);
    if( !mDAQSaveWorker->isOpen() || mCollectState == Stoped){
//        qDebug() << __FUNCTION__ << __LINE__ << len;
        mRecvBuf.clear();
        return;
    }

    qDebug() << __FUNCTION__ << __LINE__ << len;
    mRecvBuf.append( data, len );

    while( mRecvBuf.size() >= 8 ){
        switch( mRecvBuf[0] ){
        case 8:
            mBeginSave = true;
            mRecvBuf.remove( 0, 8 );
            qDebug()<<"8 remove";
            break;
        case 4:
        {
            if( mRecvBuf.size() < 1028 )
                return;
            quint8 head = mRecvBuf[0];

            quint8 *data = (quint8 *)mRecvBuf.data();
            quint32 index = ( (quint32)data[3] << 16) + ( (quint32)data[2] << 8) + data[1];
            //QString msg = QString( "head=%1,index=%2   %3" ).arg( head).arg( index ).arg( QString( mRecvBuf.mid( 0, 10 ).toHex( ' ' ) ) );
            //QString msg = QString( "save index=%1" ).arg( index );

            if (!mDAQSaveWorker->isOpen() || !mBeginSave || (mCollectState == Stoped)) {
                mRecvBuf.remove( 0, 1028 );
                break;
            }
            if( mCurrentByte == 0 ){
                QString msg = QString( "head=%1,index=%2   %3" ).arg( head).arg( index ).arg( QString( mRecvBuf.mid( 0, 10 ).toHex( ' ' ) ) );
            }

            int len = mDAQSaveWorker->writeRawData( mRecvBuf.mid( 4, 1024).data(), 1024 );
            if (len != -1) {
                mCurrentByte +=  len;
            }
            mRecvBuf.remove( 0, 1028 );
            if( mCurrentByte % mFrameByte  == 0 ){
                emit showMsg( QString( "Saving...   %1: %2 MB(%3 B)" )
                              .arg(mCurrentByte / mFrameByte, 4, 10).arg((double)mCurrentByte / MB, 10, 'f').arg( mCurrentByte ));
            }
            if( mCurrentByte >= mTotalByte ){
                mCollectState = Stoped;
                qDebug() << __FUNCTION__ << __LINE__ << "message" << mCurrentByte << mTotalByte;
                closeSaveFile();
                sendStopCollectRequest();
                emit showMsg( QString( "Success      %1: %2 MB(%3 B)" )
                              .arg(mCurrentByte / mFrameByte, 4, 10).arg((double)mCurrentByte / MB, 10, 'f').arg( mCurrentByte ));

                emit collectStateChanged( Success );
                qDebug() << __FUNCTION__ << __LINE__ << "message";
                //break;
            }
        }
            break;
        case 9://dck version
            mRecvBuf.remove( 0, 8 );
            qDebug()<<"9 remove";
            break;
        default:
            emit showMsg( "analysis protocol fail!!!!");
            qDebug()<<"clear remove";
            mRecvBuf.clear();
            break;
        }
    }
}



void DAQCalterah::parseDC1000Data(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp)
{
/**********************************************
 * 交互流程
 * 1. 连接测试
 **********************************************/
    switch (localPort) {
    case 4096:
        parseDC1000_4096(remoteAddr, remotePort, localAddr, localPort, len, data, tcp);
        break;
    case 4098:
        parseDC1000_4098(remoteAddr, remotePort, localAddr, localPort, len, data, tcp);
        break;
    }
}
void DAQCalterah::parseDC1000_4096(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp)
{
    mDC1000Data_4096.append( data, len );
    while (mDC1000Data_4096.size() && !mDC1000Data_4096.startsWith(QByteArray::fromHex("5A A5"))) {
        mDC1000Data_4096.remove(0, 1);
    }
    qDebug() << __FUNCTION__ << __LINE__ << mDC1000Data_4096.size() << mDC1000Data_4096.toHex(' ');
    mBeginSave = false;
    quint16 command = 0, status = 0;
    QByteArray _data;
    while (mDC1000Data_4096.size() >= 8)
    {
        memcpy(&command, mDC1000Data_4096.data() + 2, 2);
        memcpy(&status, mDC1000Data_4096.data() + 4, 2);
        qDebug() << __FUNCTION__ << __LINE__ << QString::number(command, 16) << status << _data.toHex(' ');
        switch (command) {
        case RESET_FPGA_CMD_CODE             :  // 0x01
            break;
        case RESET_AR_DEV_CMD_CODE           :  // 0x02
            break;
        case CONFIG_FPGA_GEN_CMD_CODE        :  // 0x03
        {
            QByteArray data = getDC1000Data(CONFIG_PACKET_DATA_CMD_CODE);
            if (mNetworkDeviceConfig->sendData( data.data(), data.size() ) < 0) {
                emit showMsg(QString("network send error! %1").arg(mNetworkDeviceConfig->errorString().c_str()));
            }
        }
            break;
        case CONFIG_EEPROM_CMD_CODE          :  // 0x04
            break;
        case RECORD_START_CMD_CODE           :  // 0x05
            mBeginSave = true;
            break;
        case RECORD_STOP_CMD_CODE            :  // 0x06
            mBeginSave = false;
            break;
        case PLAYBACK_START_CMD_CODE         :  // 0x07
            break;
        case PLAYBACK_STOP_CMD_CODE          :  // 0x08
            break;
        case SYSTEM_CONNECT_CMD_CODE         :  // 0x09
        {
            QByteArray data = getDC1000Data(READ_FPGA_VERSION_CMD_CODE);
            if (mNetworkDeviceConfig->sendData( data.data(), data.size() ) < 0) {
                emit showMsg(QString("network send error! %1").arg(mNetworkDeviceConfig->errorString().c_str()));
            }
        }
            break;
        case SYSTEM_ERROR_CMD_CODE           :  // 0x0A
            emit showMsg(QString("system error! %1").arg(status, 4, 16, QLatin1Char('0')));
            switch (status) {
            case 0x01:
                break;
            case 0x02:
                break;
            case 0x03:
                break;
            case 0x0100: // 雷达停止传输数据
            {
                mCollectState = Stoped;
                closeSaveFile();
                sendStopCollectRequest();
                emit showMsg( QString( "SequenceNumber: %1; PackageCount: %2; CurrentByte: %3B; SaveByteCount:%4B)" )
                              .arg(mSequenceNumber).arg(mPackageCount).arg(mCurrentByte).arg(mByteCount + mDataLength));
                emit showMsg( QString( "Success      %1: %2 MB(%3 B)" )
                              .arg(mCurrentByte / mFrameByte, 4, 10).arg((double)mCurrentByte / MB, 10, 'f').arg( mCurrentByte ));
                emit collectStateChanged( Success );
            }
                break;
            default:
                break;
            }
            break;
        case CONFIG_PACKET_DATA_CMD_CODE     :  // 0x0B
            emit clientConnected( remoteAddr, remotePort, true);
            break;
        case CONFIG_DATA_MODE_AR_DEV_CMD_CODE:  // 0x0C
            break;
        case INIT_FPGA_PLAYBACK_CMD_CODE     :  // 0x0D
            break;
        case READ_FPGA_VERSION_CMD_CODE      :  // 0x0E
        {
            emit showMsg(QString("FPGA_VERSION: %1").arg(_data.toHex().data()));
            // 下发配置
            QByteArray data = getDC1000Data(CONFIG_FPGA_GEN_CMD_CODE);
            qDebug() << __FUNCTION__ << __LINE__ << data.toHex( ' ' );
            if (mNetworkDeviceConfig->sendData( data.data(), data.size() ) < 0) {
                emit showMsg(QString("network send error! %1").arg(mNetworkDeviceConfig->errorString().c_str()));
            }
        }
            break;
        default:
            break;
        }

        mDC1000Data_4096.remove(0, 8);

        while (mDC1000Data_4096.size() && !mDC1000Data_4096.startsWith(QByteArray::fromHex("5A A5"))) {
            mDC1000Data_4096.remove(0, 1);
        }
    }
}

void DAQCalterah::parseDC1000_4098(unsigned long remoteAddr, unsigned short remotePort, unsigned long localAddr, unsigned short localPort, int len, const char *data, bool tcp)
{
    if( !mDAQSaveWorker->isOpen() || !mBeginSave ) {
        return;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << *((quint32*)(pData->mData)) << ++mPackageCount << pData->mLength;
    if (len >= 10)
    {
        quint32 sequenceNumber = *((quint32*)(data));
        quint64 byteCount = *((quint64*)(data + 4));
        byteCount &= 0x0000FFFFFFFFFFFF;

        if ((sequenceNumber - mSequenceNumber) != 1) {
            emit showMsg( QString( "ERROR...   SN: %1, SN(need):%2" ).arg(sequenceNumber).arg(mSequenceNumber + 1));
            closeSaveFile();
            sendStopCollectRequest();
            emit showMsg( QString( "SequenceNumber: %1; PackageCount: %2; CurrentByte: %3B; SaveByteCount:%4B)" )
                          .arg(mSequenceNumber).arg(mPackageCount).arg(mCurrentByte).arg(mByteCount + mDataLength));
            emit showMsg( QString( "Failed      %1: %2 MB(%3 B)" )
                          .arg(mCurrentByte / mFrameByte, 4, 10).arg((double)mCurrentByte / MB, 10, 'f').arg( mCurrentByte ));

            emit collectStateChanged( Failed );
            return;
        }
        mSequenceNumber = sequenceNumber;
        mByteCount = byteCount;

        mDataLength = len - 10;
        mPackageCount++;
//        std::cout << __FUNCTION__ << " " << __LINE__ << " " << len << " " << sequenceNumber << " " << mPackageCount << " " << byteCount << " " << dataLength << std::endl;

        if (mDataLength)
        {
            int length = mDAQSaveWorker->writeRawData(data + 10, mDataLength);;
            if (length > 0) {
                mCurrentByte +=  length;
            } else {
                qDebug() << __FUNCTION__ << __LINE__ << "write data error!";
                emit showMsg( QString( "write data error!"));
                closeSaveFile();
                sendStopCollectRequest();
                emit showMsg( QString( "SequenceNumber: %1; PackageCount: %2; CurrentByte: %3B; SaveByteCount:%4B)" )
                              .arg(mSequenceNumber).arg(mPackageCount).arg(mCurrentByte).arg(mByteCount + mDataLength));
                emit showMsg( QString( "Failed      %1: %2 MB(%3 B)" )
                              .arg(mCurrentByte / mFrameByte, 4, 10).arg((double)mCurrentByte / MB, 10, 'f').arg( mCurrentByte ));

                emit collectStateChanged( Failed );
            }

            quint32 frameNo = mCurrentByte / mFrameByte;
            if( frameNo  > mCurrentFrame ){
                mCurrentFrame = frameNo;
                emit showMsg( QString( "Saveing...   %1: %2 MB(%3 B)" )
                              .arg(mCurrentByte / mFrameByte, 4, 10).arg((double)mCurrentByte / MB, 10, 'f').arg( mCurrentByte ));
            }
            if( mCurrentByte >= mTotalByte ){
                mCollectState = Stoped;
                closeSaveFile();
                sendStopCollectRequest();
                emit showMsg( QString( "SequenceNumber: %1; PackageCount: %2; CurrentByte: %3B; SaveByteCount:%4B)" )
                              .arg(mSequenceNumber).arg(mPackageCount).arg(mCurrentByte).arg(mByteCount + mDataLength));
                emit showMsg( QString( "Success      %1: %2 MB(%3 B)" )
                              .arg(mCurrentByte / mFrameByte, 4, 10).arg((double)mCurrentByte / MB, 10, 'f').arg( mCurrentByte ));

                emit collectStateChanged( Success );
                //break;
            }
        }
    }
}

};
