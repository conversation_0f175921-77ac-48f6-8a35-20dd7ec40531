﻿#include "calibrationform.h"
#include "ui_calibrationform.h"

#include "uds.h"
#include "devices/devicemanager.h"

#include <QThread>

CalibrationForm::CalibrationForm(Devices::Can::DeviceManager *deviceManager, CalibrationForm::ShowMode m, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CalibrationForm),
    mAfterSaleCalibrationWorker(new CalibrationWorker(deviceManager)),
    mShowMode(m)
{
    ui->setupUi(this);

    if (mShowMode == ExitFactoryMode) {
        this->setWindowTitle(QString::fromLocal8Bit("退出工厂模式"));
        ui->comboBoxCalibrationType->setCurrentIndex(2);
        ui->comboBoxCalibrationType->hide();
        ui->pushButtonStop->hide();
        ui->pushButtonStart->setText(QString::fromLocal8Bit("退出工厂模式"));
    }

    on_comboBoxCalibrationType_currentIndexChanged(0);

    QThread *thread = new QThread;
    mAfterSaleCalibrationWorker->moveToThread(thread);

    connect(deviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameRecieved , mAfterSaleCalibrationWorker, &CalibrationWorker::canFrame, Qt::DirectConnection);
//    connect(deviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameTransmited , mAfterSaleCalibrationWorker, &CalibrationWorker::frameTransmited);

    connect(this, &CalibrationForm::start, mAfterSaleCalibrationWorker, &CalibrationWorker::start);
    connect(this, &CalibrationForm::stop, mAfterSaleCalibrationWorker, &CalibrationWorker::stop);
//    connect(this, &AfterSaleCalibrationForm::read, mAfterSaleCalibrationWorker, &AfterSaleCalibrationWorker::read);
    connect(mAfterSaleCalibrationWorker, &CalibrationWorker::message, this, &CalibrationForm::message);
    connect(mAfterSaleCalibrationWorker, &CalibrationWorker::calibrationStarted, this, &CalibrationForm::calibrationStarted);
    connect(mAfterSaleCalibrationWorker, &CalibrationWorker::calibrationFinished, this, &CalibrationForm::calibrationFinished);

    thread->start();

    connect(&mTimerRunning, &QTimer::timeout, mAfterSaleCalibrationWorker, &CalibrationWorker::read);
}

CalibrationForm::~CalibrationForm()
{
    delete ui;
}

void CalibrationForm::message(const QString message)
{
    ui->plainTextEdit->appendPlainText(message);
}

void CalibrationForm::calibrationStarted()
{
    mTimerRunning.start(ui->comboBoxCalibrationType->currentIndex() == 1 ? 3000 : 1500);
}

void CalibrationForm::calibrationFinished(bool ok)
{
    mTimerRunning.stop();
    message(QString::fromLocal8Bit(ok ? "校准成功！" : "校准失败"));
}

void CalibrationForm::on_pushButtonStart_clicked()
{
    ui->plainTextEdit->clear();
    mAfterSaleCalibrationWorker->setProtocolType((CalibrationWorker::ProtocolType)ui->comboBoxProtocolType->currentIndex(), 0);
    mAfterSaleCalibrationWorker->setCalibrationType((CalibrationWorker::CalibrationType)ui->comboBoxCalibrationType->currentIndex());
    emit start();
}

void CalibrationForm::on_pushButtonStop_clicked()
{
    mTimerRunning.stop();
    mAfterSaleCalibrationWorker->setCalibrationType((CalibrationWorker::CalibrationType)ui->comboBoxCalibrationType->currentIndex());
    emit stop();
}

void CalibrationForm::on_comboBoxChannelIndex_currentIndexChanged(int index)
{
    mAfterSaleCalibrationWorker->uds()->setChannelIndex(index);
    mAfterSaleCalibrationWorker->setProtocolType(CalibrationWorker::ProtocolBYD220, 0);
}

void CalibrationForm::on_checkBoxCAN_stateChanged(int arg1)
{
    mAfterSaleCalibrationWorker->uds()->canUpdate(arg1 != Qt::Unchecked);
}

void CalibrationForm::on_comboBoxCalibrationType_currentIndexChanged(int index)
{
    mAfterSaleCalibrationWorker->setCalibrationType((CalibrationWorker::CalibrationType)ui->comboBoxCalibrationType->currentIndex());
}
