﻿/*
 * @Author: your name
 * @Date: 2020-05-30 10:35:42
 * @LastEditTime: 2021-06-04 18:52:44
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \radar-sensor-firmware\calterah\common\user\cfg.h
 */

#ifndef MODULE_CFG_CFG_H_
#define MODULE_CFG_CFG_H_
#ifndef PC_DBG_FW
#include "sharedVar.h"
#include "sensor_config.h"
#else
#include "app/include/sharedVar.h"
#endif
#define TRX_SYNC_INTERRUPT 0

#if SAMPLE_CNT == 1024
#define T_CHIRP_SCALE ((float)11 / 10) //解速度模糊chirp时长比例
#else
#define T_CHIRP_SCALE ((float)8 / 7) //解速度模糊chirp时长比例
#endif

#define CONFIG_MAGIC_NUM 0xCACACACA

#define ANGLE_FILTER 0
#define RANGE_FILTER 1
#define MAX_ANGLE_VAL 45
//#define FILTER_BOUNDARY_RNG     2
#define MAX_RANGE_VAL 200

#define CALTERAH_ANGLE_CAL 1

#define NO_FILTER_FENCE 0	//不过滤
#define FILTER_ALL_FENCE 1	//过滤护栏
#define FILTER_FIX_FENCE 2	//修正护栏
#define FILTER_STATIC_OBJ 4 //过滤静止目标
#define FILTER_3_LANE 8		//过滤三车道外目标

#define CONFIG_INIT_TYPE_ALL		1
#define CONFIG_INIT_TYPE_LOAD_FLASH	2


#if RADAR_TYPE == MRR_RADAR
#define PARAM_DATAMODE_NONE         0x0
#define PARAM_DATAMODE_TRACK        0x1
#define PARAM_DATAMODE_RAW          0x2
#define PARAM_DATAMODE_RAW_TRACK    0x3
#else
#define PARAM_DATAMODE_RAW 			0x0u
#define PARAM_DATAMODE_TRACK 		0x1u
#define PARAM_DATAMODE_RAW_TRACK	0x2u
#define PARAM_DATAMODE_NONE 		0x3u
#endif

typedef struct
{
	char type[16];
	char pcbaBBSN[24];
	char imuName[24];
	char radarSN[24];
	unsigned char macaddr[6];
	unsigned char ipaddr[4];
	char HwVer[4];
	char SwVer[4];
	char bootSwVer[4];
	char CalVer[2];
#ifndef PC_DBG_FW
} __attribute__((packed)) radar_info_t;
#else
} radar_info_t;
#endif

typedef struct
{
	uint32_t phyReqId;
	uint32_t funReqId;
	uint32_t phyRspId;
	uint32_t accessKey;
} stUdsParam;

typedef struct
{
	unsigned int radarId;
	int iVersion;
	uint8_t sendCarVel;	 // 是否发送速度
	uint8_t sendYawrate; // 是否发送曲率
	uint8_t speedSource;
	uint8_t installCalcState;

	stUdsParam udsParam;

	float anten_distance[4][16];
	float anten_distance_y[4][16];
	float anten_phase[4][16];
	int8_t rcsOffset[4];
	int8_t azimuthDecayTx[4][181]; // 方向图
	int8_t antCaliMode[4];		   // 使能1度1校准还是拟合的方式, 0 表示使用1度1校准的方式，1表示使用拟合的方式
#if 1							   //(PHASE_SHIFT_CALIB == 1)
	uint8_t enableTxPhaseShiftCalData;
#endif

	float fmcw_startfreq;
	unsigned int tx_pattern; // 0-normal,1-switch between tx1 and tx2, 2-switch bandwidth, 3-切换带宽和天线，other-normal
	unsigned int tx_sel;	 // used when tx_pattern = 0;  1:tx1, 2:tx2, 3:tx1&tx2
	unsigned int tx_cw;		 // cw mode, 0: normal, 1 : cw

	uint8_t dataMode; // 数据发送模式

	/* System control parameters */
	uint8_t angleReverse;		// 角度反转
	uint8_t agingTest;			// 老化信息发送开关
	uint8_t resolutionTestMode; // 雷达测试模式     //0-正常模式  1-分辨率测试模式
	uint32_t randomId;

	// float installAngle;			 // 安装水平角
	// float angleOffset;			 // 角度偏移
	// float installElevatedOffset; // 安装俯仰角补偿
	float installHeight;    				//雷达-安装高度偏移-距离地面高度
	float horizontalOffset; 				//雷达-安装水平偏移-距离车中心轴距离
	float installHorizontalAngleOffset;     //雷达-下线标定-水平角
	float installPitchAngleOffset; 			//雷达-下线标定-俯仰角
	float serviceHorizontalAngleOffset;		//雷达-服务标定-水平角
	float servicePitchAngleOffset;			//雷达-服务标定-俯仰角
	float selfCaliHorizontalAngleOffset;    //雷达-自标定-水平角
	float selfCaliPitchAngleOffset;			//雷达-自标定-俯仰角
	float determineHrzCalibrationValue2use; //选择下线/服务水平标定值使用的值
	float determinePitCalibrationValue2use; //选择下线/服务标定俯仰值使用的值
	uint8_t determine2useFlag;				//0-没有标定值被使用；1-下线标定；2-服务标定
	uint8_t calcErrorFlag;					//前雷达：0-自标定结果正确；1-自标定结果错误；2-水平结果错误；3-俯仰结果错误

	uint8_t debugModeSW;	  // 调试模式开关，0不开，1开启
	uint8_t objExtendInfoSW;  // 雷达扩展信息是能开关，0不发送，1发送
	uint8_t objExtendInfoSW2; // 雷达扩展信息2是能开关，0不发送，1发送
	uint8_t protocolVersion;  // 雷达协议版本

	radar_info_t radar_info;
#ifndef PC_DBG_FW
} __attribute__((packed)) radar_config_t;
#else
} radar_config_t;
#endif 


// extern radar_config_t *radar_config_using;
extern const radar_config_t *const radar_config_using;
#ifdef PC_DBG_FW
    extern const radar_config_t radar_config_default;
#endif
typedef struct
{
	float latRange;			//横向距离分界
	uint8_t nfRangeThrd[2]; //远近距离分界点
	uint8_t nearRange;		//近距离分界
	uint8_t txCW;			//0 -- normal, 1 -- CW	0xff -- close all
	uint8_t speedType;		//启动解速度模糊
	uint8_t isRecFromCWMode; //表示是否从CW模式中恢复，0-表示false,1-表示true
} radarDspCfg_t;

typedef struct
{
	uint16_t txch;
	uint16_t nfft_range;
	uint16_t nfft_vel;
	uint16_t nchirp;
	double tup;
	double tdown;
	double tidle;
	double bandWidth;
	double startFreq;
	uint8_t	adcFreq;
	uint8_t decFactor;
} fmcwParam_t;

extern radarDspCfg_t radarDspCfg;
extern fmcwParam_t gFmcwParam[2][2];
// extern stLdGuardCfg gLdGuardCfg;
void rebootFlagInfoSave(void);
void rebootFlagInfoRead(void);
int eraseRebootFlagInfo(void);
int system_param_init_from_flash(void);

void config_init(void);
int configWrite(radar_config_t *cfg);
void eraseConfig(uint32_t addr , uint32_t len);

void setFmcwParam(void);

#ifndef PC_DBG_FW
void initFmcwParam(sensor_config_t *bb, int i);
#endif

void DSPConfigure(fmcwParam_t *param);
void rebootFlagInfoSave(void);
void rebootFlagInfoRead(void);
int eraseRebootFlagInfo(void);

void updateFmcwParam(float bandWidth , float centerFreq);

#endif
