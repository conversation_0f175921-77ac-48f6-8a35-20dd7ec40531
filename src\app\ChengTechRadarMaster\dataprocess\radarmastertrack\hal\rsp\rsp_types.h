﻿/**
 * @file     rsp_types.h
 * @brief    The structure and variables of RSP module are defined in this header file.
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2022-12-26
 *
 *
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2022-12-26  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _RSP_TYPES_H_
#define _RSP_TYPES_H_


/*****************************************************************************
  INCLUDES
 *****************************************************************************/
#ifndef PC_DBG_FW
#include "app_common.h"
#include "sharedVar.h"
#include "radardsp.h"
#else
#include "app/vehicle/app_common.h"
#endif

// #define MAX_NB_OF_POINTS        256
#define MAX_NB_OF_TRACKS        128
#define MAX_NB_OF_130PRO_POINTS 32
#ifndef PC_DBG_FW
#else
#define MAX_NB_OF_POINTS        256
#define MAX_NB_OF_TARGETS       MAX_NB_OF_TRACKS
#endif

#ifndef MAX_NUM_OF_POINTS
#define MAX_NUM_OF_POINTS 256   //允许最大检测点数
#endif
#define MAX_NUM_OF_TRACKS 64  //128    //允许最大航迹数

/*****************************************************************************
  DEFINE
 *****************************************************************************/


/*****************************************************************************
  ENUM
 *****************************************************************************/
// 雷达工作模式：0：正常波形，1：跳频波形；2：高温降chirp波形
typedef enum
{
   NORMAL_WAVE_TYPE                 = 0,    //正常工作波形
   FREQHOP_WAVE_TYPE                = 1,    //跳频波形
   TEMPER_REDUCE_CHIRP_WAVE_TYPE    = 2,    //高温降chirp波形
   TEMPER_HOP_WAVE_TYPE             = 3,    //高温降chirp同时跳频波形
   INCREASE_CFAR_WAVE_TYPE          = 4,    //满点增加cfar检测
} RADAR_WORK_MODE_TYPE;


// FMCW参数状态
//typedef enum NEW_PARAM_TYPE_e
//{
//    NEW_PARAM_NONE     = 0,    //只发送不做信号处理
//    NEW_PARAM_SET      = 1,    //配置新的发送参数
//    NEW_PARAM_CW       = 2,    //配置CW模式
//    NEW_PARAM_CLOSE_TX = 3,    //关闭TX
//    NEW_PARAM_ADC      = 4,    //采集ADC
//    NEW_PARAM_SW_CALI  = 5,    //标定模式切换
//    NEW_TO_IDLE_MODE   = 6,    //等待空闲模式
//    NEW_IDLE_MODE      = 7,    //空闲模式
//    NEW_PARAM_NORMAL   = 0xFF, //正常处理
//} NEW_PARAM_TYPE;


/*****************************************************************************
  STRUCTURE
*****************************************************************************/
/*!
 * @brief
 *  Detection object information.
 */
typedef struct RSP_detObjectInfo
{
    /*! @brief RSP模块解算的目标距离，单位是m */
    float rspDetRange;

    /*! @brief RSP模块解算的目标速度（解模糊后），单位是m/s */
    float rspDetVelocity;

    /*! @brief RSP模块解算的水平角，单位为deg */
    float rspDetAzimuthAngle;

    /*! @brief RSP模块解算的俯仰角
    /// @details 单位为deg */
    float rspDetElevationAngle;

    /*! @brief RSP模块解算的检测目标SNR值，单位为dB */
    float rspDetSNR;

    /*! @brief RSP模块解算的检测目标FFTMAG，单位为NA */
    float rspDetFFTMAG;

    /*! @brief RSP模块解算的检测目标DOAMAG，单位为NA */
    float rspDetDBFMAG;

    /*! @brief RSP模块解算的检测目标RCS，单位为dBsm */
    float rspDetRCS;

    /*! @brief 当前目标的距离单元 */
    uint16_t rspDetRangeBin;

    /*! @brief 当前目标的速度单元 */
    uint16_t rspDetDopplerBin;

    /*! @brief RSP模块检测目标的一级解模糊后的速度
    /// @details 单位为m/s ,可选项*/
    float rspDetSolveVelAmbiguity1st;

    /*! @brief RSP模块检测目标的二级解模糊后的速度，单位为m/s ,可选项*/
    float rspDetSolveVelAmbiguity2st;

    /*! @brief 当前目标的CFAR特征 ,可选项*/
    float  rspDetCFARFeature;

    /*! @brief 当前目标的DOA特征 ,可选项*/
    float  rspDetDOAFeature[3];

    /*! @brief 当前目标的DOA特征 ,可选项*/
    float  P0Px;

    /*! @brief 当前目标的质量 ,可选项*/
    float rspDetMeasQuality;

    /*! @brief 当前目标的质量 ,可选项*/
    uint8_t rspDetMatchFlag;

    /*! @brief 检测目标的状态 */
    uint16_t rspDetStatus;

    /*! @brief 检测目标的有效 */
    uint8_t  rspDetValid;

    /*! @brief 当前目标所属的Profile序号 */
    uint8_t  rspProfileIdx;

    /*! @brief 当前目标原始的索引 */
    uint8_t rspRawIdx;
} RSP_DetObjectInfo_t;

/*!
 * @brief
 *  Detection object list.
 */
typedef struct RSP_detObjectList
{
    /*! @brief 接口版本号 */
    uint16_t versionNumber;

    /*! @brief 当前雷达的测试模式 */
    uint8_t  radarResolutionTestMode;

    /*! @brief 信号header */
    APP_signalHeader_t signalHeader;

    /*! @brief 检测目标的数量 */
    uint16_t rspDetObjectNum;

    /*! @brief 当前的底噪,dB */
    float rspDetNoiseCurrent;

    /*! @brief 全局底噪,dB */
    float rspDetNoiseGlobal;

    /*! @brief cfar后的目标数 */
    uint16_t rspDetCfarObjectNum;

    /// @brief 干扰标志
    uint8_t interferedFlag;

    /// @brief 被干扰的程度
    uint8_t intPercent;

    /// @brief 遮挡标志
    uint8_t blockFlag;

    /// @brief 被遮挡的程度
    uint8_t blockPercent;

    /// @brief 遮挡故障上报标志
    uint8_t blockErrorFlag;

    /*! @brief 目标所属的当前模式 */
    uint8_t rspCurFrameMode;

    /// @brief 是否跳频的工作状态标记位
    uint8_t FreqHopWorkState;

    /// @brief 雷达id
    uint8_t radarId;

    /// @brief 水平安装角度
    float installAziAngle;

    /*! @brief 检测目标list */
    RSP_DetObjectInfo_t rspDetObject[MAX_NUM_OF_POINTS];

    /*! @brief RSP回波的时间戳 */
    uint64_t rspTimestamp;
} RSP_DetObjectList_t;

/*!
 * @brief
 *  Raw detection target info.
 */
typedef struct
{
    int8_t valid;     //是否有效
    int8_t cIdx;        //关联到的跟踪点，radartarget调试用
    int8_t cdiIdx;      //对应的cdi idx
    int8_t isUsing;     //是否使用
    int8_t F;         //DDMIMO中间参数
    int8_t ambIdx;    //解速度模糊的模糊数

    int8_t angleNum;  //有几个角度，超分辨时可能有2个
    float range;      //距离
    float velocity;   //速度
    float realVel;    //解速度模糊后的结果
    float angle;      //水平角度
    float heighAngle; //高度角
    float sig_elv;
    uint16_t rangeBin;    //距离bin
    int16_t velocityBin; //速度bin
    float fftMag;       //fft mag值
    float doaMag;    //doa mag值
    float mag1;        //当前fftMag与前和后一个距离bin的fftMag值平方和开根号
    float threshold; //底噪mag值
    float phase[4]; //相位值
    float firstvelocity; 

    float snr;
    float x;
    float y;
    float rcs; //DDM track_read_hv cali
    uint8_t realProfileIdx; //实际radar内部的frameType
    // #if DDM_RAW_DATA_EXT_INFO
    uint16_t  threshold3T; //3T阈值调试用
    int8_t  deltaRange;  //距离
    int8_t  deltaVel;    //速度
    // #endif
    int8_t staticFlag;

    uint8_t peakNum[2];
    float sideLobe[3];
} Target_Thres_t;
/**
 * @brief 信号处理工作参数
 */
typedef struct
{
    uint32_t measCnt;           /**< 累计帧数 */
    uint16_t targetNum[4][2];   /**< 未解模糊前点数量 [frame][chirp] */
    uint16_t outTargets[4];     /**< 模糊后点数量 [frame] */
    uint16_t outAllTargets;     /**< 每帧最终输出目标点数 */
    uint8_t frameType;          /**< 当前帧类型 */
    uint8_t profile;            /**< 当前所使用的Profile */
    uint8_t chirpType;          /**< 当前chirp类型 CHIRP_TYPE_E */
    uint8_t workMode;           /**< 工作模式 WORK_MODE_E */
    uint8_t fixFrameMode;       /**< 是否为固定帧工作模式，如标定模式下指定帧发送 */
    uint8_t resv[1];
    uint32_t txPattern;         /**< tx切换类型 */
} radarDspParam_t;

typedef struct
{
    uint8_t  isUsing; //是否使用
    uint8_t  objTxCh; //区分远近天线的目标  //或者哪个Profile目标--再output 1时
    uint16_t fftMag;  //fft Mag值
    uint16_t doaMag;  //doa mag值
    int8_t   cIdx;    //关联到的跟踪点，radartarget调试用
    int8_t   cdiIdx;  //对应的cdi idx
    float range;      //距离
    float velocity;   //速度
    float angle;      //水平角度
    float heighAngle; //高度角
    float snr;        //SNR
    float rcs;
    float phase[4]; //相位值
    float sideRatio;
    float firstvelocity;
    uint8_t  MF;
    uint8_t  velBin;   //原始点多普勒索引
    float txOrderConf1;
    float leakRatio;    // 泄露
    uint8_t  DetStatus;   //原始点状态信息，仿真在用
} objectInfo_t;

typedef struct
{
    uint8_t     dataMode;
    uint8_t     speedVagueCheck;
    float       minimumNoise[4];    //记录最小底噪值
    float       noiseCurrent;     // 当前的底噪
    float       noiseGlobal;      // 全局底噪
    uint16_t    cfarObjectNum; // cfar后的目标数

    uint8_t interferedFlag;     // 干扰标志
    uint8_t intPercent;         // 被干扰的程度
} dspProcessParam_t;

// debug info
typedef struct
{
    uint16_t dbf_obj_num;           // dbf 目标点数
    float dbf_range_res;            // dbf 距离分辨率 应该是0.25
    float dbf_Range_target0;        // dbf 后第一个目标的距离
    float dbf_RI_target0;           // dbf后第一个目标的距离bin
    uint16_t dml_obj_num;           // dml 目标点数
    float dml_Range_target0;        // dml 后第一个目标的距离
    float dml_RI_target0;           // dml后第一个目标的距离bin
    uint16_t trackread_obj_num;     // trackread后目标点数
    float trackread_Range_target0;  // trackread后第一个目标的距离
    float trackread_RI_target0;     // trackread后第一个目标的距离bin
    uint16_t trackread_dbf_obj_num;     // trackread后目标点数
    float trackread_dbf_Range_target0;  // trackread后第一个目标的距离
    float trackread_dbf_RI_target0;     // trackread后第一个目标的距离bin
    uint16_t abmatch_obj_num;       // abmatch 目标点数
    float abmatch_Range_target0;    // abmatch 后第一个目标的距离
    float abmatch_RI_target0;       // abmatch 后第一个目标的距离bin
} Debug_RSP_out_data;

#endif
