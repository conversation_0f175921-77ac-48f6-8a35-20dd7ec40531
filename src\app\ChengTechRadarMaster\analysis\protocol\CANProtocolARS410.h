﻿#pragma once

#include "ianalysisprotocol.h"

#include <vector>

namespace Analysis {

namespace Protocol {
		class CANProtocolARS410 :
            public IAnalysisProtocol
		{
		public:
            CANProtocolARS410(AnalysisWorker *analysisWorker, QObject *parent = nullptr);
			~CANProtocolARS410();

            bool analysisFrame(const Devices::Can::CanFrame &frame) override;

		private:
            bool parseStatus(const Devices::Can::CanFrame &frame);

            std::vector<Devices::Can::CanFrame> mCANFrames;
		};
	}
}

