-- 4.0.4

AlarmType = {
    Alarm_None = 0x00,
    Alarm_BSD = 0x01,
    Alarm_LCA = 0x02,
    Alarm_RCW = 0x04,
    Alarm_DOW_F = 0x08,
    Alarm_DOW_R = 0x10,
    Alarm_RCTA = 0x20,
    Alarm_RCTB = 0x40,
    Alarm_FCTA = 0x80,
    Alarm_FCTB = 0x0100,
    Alarm_JA = 0x0200,
}

-- target:  {r, x, y, z, vx, vy, vz, v}
-- vehicle: {
--           v, 
--           gear, acc_pedal, 
--           trailer, 0x0: not trailered; 0x1: trailered
--           esp, 
--           door_f_l, 
--           door_f_r, 
--           door_r_l, 
--           door_r_r
--          }
-- switch： {BSD, DOW, FCTA, FCTB, LCA, RCTA, RCTB, RCW, JA}
-- return: {{type, level, ttc}, {}}

-- return {type, level, ttc}
function warning_bsd(target, vehicle)
    -- print("BSD")
    print(vehicle.v, vehicle.trailer, vehicle.esp, target.v, target.x, target.y)
    if (vehicle.v > 12 and not(vehicle.trailer) and not(vehicle.esp) and (target.v > 7.2)) then
        return {AlarmType.Alarm_BSD, 0, 0.0}
    end
    
    return {AlarmType.Alarm_None, 0, 0.0}
end


function warning_dow(target, vehicle)
    -- print("DOW")
    
    return {AlarmType.Alarm_None, 0, 0.0}
end

function warning_fcta(target, vehicle)
    -- print("FCTA")
    
    return {AlarmType.Alarm_None, 0, 0.0}
end

function warning_fctb(target, vehicle)
    -- print("FCTB")
    
    return {AlarmType.Alarm_None, 0, 0.0}
end

function warning_lca(target, vehicle)
    -- print("LCA")
    
    return {AlarmType.Alarm_None, 0, 0.0}
end

function warning_rcta(target, vehicle)
    -- print("RCTA")
    
    return {AlarmType.Alarm_None, 0, 0.0}
end

function warning_rctb(target, vehicle)
    -- print("RCTB")
    
    return {AlarmType.Alarm_None, 0, 0.0}
end

function warning_rcw(target, vehicle)
    -- print("RCW")
    
    return {AlarmType.Alarm_None, 0, 0.0}
end

function warning_ja(target, vehicle)
    -- print("JA")
    
    return {AlarmType.Alarm_None, 0, 0.0}
end

function warning_front(index, target, vehicle, switch)
    if (switch.FCTA) then
        t = warning_fcta(target, vehicle)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (switch.FCTB) then
        t = warning_fctb(target, vehicle)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end    
    
    if (switch.JA) then
        t = warning_ja(target, vehicle)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
end

function warning_rear(index, target, vehicle, switch)
    if (switch.BSD) then
        t = warning_bsd(target, vehicle)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (switch.DOW) then
        t = warning_dow(target, vehicle)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (switch.LCA) then
        t = warning_lca(target, vehicle)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (switch.RCTA) then
        t = warning_rcta(target, vehicle)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (switch.RCTB) then
        t = warning_rctb(target, vehicle)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (switch.RCW) then
        t = warning_rcw(target, vehicle)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
end

function early_warning(radarID, index, target, vehicle, switch)
    warning = {}
    if (radarID == 4) or (radarID == 5) then 
        warning_rear(index, target, vehicle, switch);
    elseif (radarID == 6) or (radarID == 7) then
        warning_front(index, target, vehicle, switch);
    end
    
    return warning
end