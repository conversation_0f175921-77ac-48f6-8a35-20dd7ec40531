﻿#ifndef MASTERICONS_H
#define MASTERICONS_H

#include "master_global.h"

#include <utils/icon.h>

namespace Core {
namespace Icons {

MASTER_EXPORT extern const Utils::Icon CHENGTECTRADARMASTER_LOGO;
MASTER_EXPORT extern const Utils::Icon CHENGTECTRADARMASTER_SAVE_CSV;
MASTER_EXPORT extern const Utils::Icon CHENGTECTRADARMASTER_UNSAVE_CSV;
MASTER_EXPORT extern const Utils::Icon CHENGTECTRADARMASTER_SAVE_PATH;
MASTER_EXPORT extern const Utils::Icon CHENGTECTRADARMASTER_START;
MASTER_EXPORT extern const Utils::Icon CHENGTECTRADARMASTER_STOP;

} // namespace Icons
} // namespace Core
#endif // MASTERICONS_H
