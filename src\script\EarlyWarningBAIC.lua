-- 4.0.4

bsd_condition = {
    gear = 2, -- 非: 1:P; 2:R; 3:N; 4:D
}

AlarmType = {
    Alarm_None = 0x00,
    Alarm_BSD = 0x01,
    Alarm_LCA = 0x02,
    Alarm_RCW = 0x04,
    Alarm_DOWF = 0x08,
    Alarm_DOWR = 0x10,
    Alarm_RCTA = 0x20,
    Alarm_RCTB = 0x40,
    Alarm_FCTA = 0x80,
    Alarm_FCTB = 0x0100,
    Alarm_JA = 0x0200
}

Gear = {
    P = 1,
    R = 2,
    N = 3,
    D = 4
}

function isAlarmType(alarmTypes, alarmeType)
    if ((alarmTypes & alarmeType) == alarmeType) then
        return true
    else
        return false
    end
end

-- target:  {x, y, z, vx, vy, vz, vsog, courseRngle}
-- vehicle: {
--           v, 
--           gear, 1:P; 2:R; 3:N; 4:D
--           radius,
--           acc_pedal, %
--           trailer, 0x0: not trailered; 0x1: trailered
--           esp, 
--           light_f_l, 
--           light_f_r, 
--           light_r_l, 
--           light_r_r
--          }
-- switch： {BSD, DOW, FCTA, FCTB, LCA, RCTA, RCTB, RCW, JA}
-- return: {{type, level, ttc}, {}}

-- return {type, level, ttc}
function warning_bsd(oldAlarmed, target, light)
    -- print("BSD")
    alarmed = {AlarmType.Alarm_None, 0, 0.0}
    
    if ((target.vsog < 7.2) or (target.vy > 15) or (target.vx > 15)) then
        return alarmed
    end

    if (oldAlarmed) then
        if ((target.x < 0.3) or (target.x > 4.25) or (target.y < -2.5) or (target.y > 5.5)) then
            return alarmed
        end
    elseif ((target.x < 0.5) or (target.x > 3.75) or (target.y < -2.5) or (target.y > 5)) then
        return alarmed
    end
    
    level = 1;
    if (light) then
        level = 2;
    end

    return {AlarmType.Alarm_BSD, level, 0.0};
end

function warning_lca(oldAlarmed, target, light)
    -- print("LCA")
    alarmed = {AlarmType.Alarm_None, 0, 0.0}
    
    if ((target.vsog <= 7.2) or (target.vy >= 0)) then
        return alarmed
    end

    ttc = (target.y / (-target.vy))

    if (oldAlarmed) then
        if ((target.x < 0.3) or (target.x > 4.25) or (target.y < 5) or (target.y > 70) or (ttc >= 4.5)) then
            return alarmed
        end
    elseif ((target.x < 0.5) or (target.x > 3.75) or (target.y < 5) or (target.y > 70) or (ttc >= 3.5)) then
        return alarmed
    end
    
    level = 1;
    if (light) then
        level = 2;
    end
     
    return {AlarmType.Alarm_LCA, level, ttc}
end

function warning_rcw(oldAlarmed, target, light)
    -- print("RCW")
    alarmed = {AlarmType.Alarm_None, 0, 0.0}

    if (target.vsog < 7.2) then
        return alarmed
    end
    
    return {AlarmType.Alarm_RCW, 0, 0.0}
end

function warning_dow(oldAlarmed, target, light)
    -- print("DOW")
    alarmed = {AlarmType.Alarm_None, 0.0}

    if ((target.vsog < 7.2) or (target.vy >= 0) or ((target.courseRngle > -170) and (target.courseRngle < 170))) then
        return alarmed
    end

    if (oldAlarmed) then
        if ((target.x < 0.3) or (target.x > 3)) then
            return alarmed
        end
    else
        if ((target.x < 0.3) or (target.x > 2)) then
            return alarmed
        end
    end
    
    ttc = (target.y / (-target.vy))
    if (ttc > 2.5) then
        return alarmed
    end
    
    return {AlarmType.Alarm_DOWF | AlarmType.Alarm_DOWR, ttc}
end

function warning_rcta(oldAlarmed, target, light)
    -- print("RCTA")
    
    alarmed = {AlarmType.Alarm_None, 0, 0.0}

    if ((target.vsog < 5.4) or (target.y < -1.5) or (target.courseRngle > -45) or (target.courseRngle < -135)) then
        return alarmed
    end

    if ((oldAlarmed) and (target.y > 6.5)) then
        return alarmed
    end
    if (not(oldAlarmed) and (target.y > 5.5)) then
        return alarmed
    end

    ttcX = 2.5
    ttcY = 2.5
    if (target.vy < 0) then
        ttcY = (target.y / (-target.vy))
    end
    if (target.vx < 0) then
        ttcX = (target.x / (-target.vx))
    end
    if ((ttcX > 2.5) and (ttcY > 2.5)) then
        return alarmed
    end
    
    return {AlarmType.Alarm_RCTA, 2, math.min(ttcX, ttcY)}
end

function warning_rctb(oldAlarmed, target, light)
    -- print("RCTB")
    
    alarmed = {AlarmType.Alarm_None, 0, 0.0}

    if ((target.vsog < 5.4) or (target.y < 1.5) or (target.y > 5.5) or (target.courseRngle > -45) or (target.courseRngle < -135)) then
        return alarmed
    end

    ttcX = 1.6
    ttcY = 1.6
    if (target.vy < 0) then
        ttcY = (target.y / (-target.vy))
    end
    if (target.vx < 0) then
        ttcX = (target.x / (-target.vx))
    end
    if ((ttcX > 1.6) and (ttcY > 1.6)) then
        return alarmed
    end
    
    return {AlarmType.Alarm_RCTB, 2, math.min(ttcX, ttcY)}
end

function warning_fcta(oldAlarmed, target, light)
    -- print("FCTA")
    alarmed = {AlarmType.Alarm_None, 0, 0.0}

    if ((target.vsog < 5) or (target.vsog > 60) or (target.y < -1.5) or (target.y > 3.5) or (target.courseRngle > -45) or (target.courseRngle < -135)) then
        return alarmed
    end

    ttcX = 3
    ttcY = 3
    if (target.vy < 0) then
        ttcY = (target.y / (-target.vy))
    end
    if (target.vx < 0) then
        ttcX = (target.x / (-target.vx))
    end
    if ((ttcX > 3) and (ttcY > 3)) then
        return alarmed
    end
    print(ttcX, ttcY, "FCTA")
    
    return {AlarmType.Alarm_FCTA, 2, math.min(ttcX, ttcY)}
end

function warning_fctb(oldAlarmed, target, light)
    -- print("FCTB")
    
    alarmed = {AlarmType.Alarm_None, 0, 0.0}

    if ((target.vsog < 5) or (target.vsog > 60) or (target.y < -1.5) or (target.y > 3.5) or (target.courseRngle > -45) or (target.courseRngle < -135)) then
        print(target.vsog, target.y, target.courseRngle)
        return alarmed
    end

    ttcX = 1
    ttcY = 1
    if (target.vy < 0) then
        ttcY = (target.y / (-target.vy))
    end
    if (target.vx < 0) then
        ttcX = (target.x / (-target.vx))
    end
    if ((ttcX > 1) and (ttcY > 1)) then
        return alarmed
    end
    print(ttcX, ttcY)
    
    return {AlarmType.Alarm_FCTB, 2, math.min(ttcX, ttcY)}
end

function warning_ja(oldAlarmed, target, light)
    -- print("JA")
    
    alarmed = {AlarmType.Alarm_None, 0, 0.0}

    if ((target.v < 5) or (target.v > 60) or (target.y < 1.5) or (target.y > 5)) then
        return alarmed
    end

    if (target.vy ~= 0) then
        ttcY = (target.y / (-target.vy))
    end
    if (target.vx ~= 0) then
        ttcX = (target.x / (-target.vx))
    end
    if ((ttcX > 3) and (ttcY > 3)) then
        return alarmed
    end
    
    return {AlarmType.Alarm_JA, 0, 0.0}
end

function warning_front(alarmTypes, vehicleAlarmTypes, target, vehicle, light)
    if (isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_FCTA)) then
        t = warning_fcta(isAlarmType(alarmTypes, AlarmType.Alarm_FCTA), target, light)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_FCTB)) then
        t = warning_fctb(isAlarmType(alarmTypes, AlarmType.Alarm_FCTB), target, light)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end    
    
    if (isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_JA)) then
        t = warning_ja(isAlarmType(alarmTypes, AlarmType.Alarm_JA), target, light)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
end

function warning_rear(alarmTypes, vehicleAlarmTypes, target, vehicle, light)
    if (isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_BSD)) then
        t = warning_bsd(isAlarmType(alarmTypes, AlarmType.Alarm_BSD), target, light)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_LCA)) then
        t = warning_lca(isAlarmType(alarmTypes, AlarmType.Alarm_LCA), target, light)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_RCW)) then
        t = warning_rcw(isAlarmType(alarmTypes, AlarmType.Alarm_RCW), target, light)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_DOWF) or isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_DOWF)) then
        t = warning_dow(false, target, light)
        if (t[1] ~= AlarmType.Alarm_None) then
            if (vehicle.door_f_l or vehicle.door_f_r) then
                table.insert(warning, {AlarmType.Alarm_DOWF, 2, t[2]})
            else
                table.insert(warning, {AlarmType.Alarm_DOWF, 1, t[2]})
            end
            if (vehicle.door_r_l or vehicle.door_r_r) then
                table.insert(warning, {AlarmType.Alarm_DOWR, 2, t[2]})
            else
                table.insert(warning, {AlarmType.Alarm_DOWR, 1, t[2]})
            end
        end
    end
    if (isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_RCTA)) then
        t = warning_rcta(isAlarmType(alarmTypes, AlarmType.Alarm_RCTA), target, light)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
    if (isAlarmType(vehicleAlarmTypes, AlarmType.Alarm_RCTB)) then
        t = warning_rctb(isAlarmType(alarmTypes, AlarmType.Alarm_RCTB), target, light)
        if (t[1] ~= AlarmType.Alarm_None) then
            table.insert(warning, t)
        end
    end
end

-- 目标是否满足报警条件
function early_warning(radarID, alarmTypes, vehicleAlarmTypes, target, vehicle, light)
    warning = {}
    if (radarID == 4) or (radarID == 5) then 
        warning_rear(alarmTypes, vehicleAlarmTypes, target, vehicle, light);
    elseif (radarID == 6) or (radarID == 7) then
        warning_front(alarmTypes, vehicleAlarmTypes, target, light);
    end
    
    return warning
end

-- 车身是否满足告警条件
-- radarID:     雷达ID
-- alarmTypes:  历史告警类型
-- switch:      告警开关
-- vehicle:     车身信息
function vehicle_warning(radarID, alarmTypes, switch, vehicle)
    warningTypes = AlarmType.Alarm_None
    if (radarID == 4) or (radarID == 5) then    -- 后角
        -- Alarm_BSD = 0x01,
        if ((switch.BSD) and (vehicle.gear ~= Gear.R) and not(vehicle.trailer) and not(vehicle.esp)) then
            if isAlarmType(alarmTypes, AlarmType.Alarm_BSD) then
                if (vehicle.v > 10) then
                    warningTypes = warningTypes | AlarmType.Alarm_BSD
                end
            elseif (vehicle.v > 12) then
                warningTypes = warningTypes | AlarmType.Alarm_BSD
            end
        end
        -- Alarm_LCA = 0x02,
        if ((switch.LCA) and (vehicle.gear ~= Gear.R) and not(vehicle.trailer) and not(vehicle.esp)) then
            if isAlarmType(alarmTypes, AlarmType.Alarm_LCA) then
                if (vehicle.v > 10) then
                    warningTypes = warningTypes | AlarmType.Alarm_LCA
                end
            elseif (vehicle.v > 12) then
                warningTypes = warningTypes | AlarmType.Alarm_LCA
            end
        end
        -- Alarm_RCW = 0x04,
        if ((switch.RCW) and (vehicle.gear ~= Gear.R) and not(vehicle.trailer) and not(vehicle.esp) and (vehicle.v >= 0) and (vehicle.v <= 130)) then
            warningTypes = warningTypes | AlarmType.Alarm_RCW
        end
        -- Alarm_DOWF = 0x08,
        -- Alarm_DOWR = 0x10,
        if ((switch.DOW) and not(vehicle.trailer) and not(vehicle.esp) and (vehicle.v < 2)) then
            warningTypes = warningTypes | AlarmType.Alarm_DOWF | AlarmType.Alarm_DOWR
        end
        -- Alarm_RCTA = 0x20,
        if ((switch.RCTA) and (vehicle.gear == Gear.R) and not(vehicle.trailer) and not(vehicle.esp)) then
            if isAlarmType(alarmTypes, AlarmType.Alarm_RCTA) then
                if ((vehicle.v >= 0) and (vehicle.v <= 10)) then
                    warningTypes = warningTypes | AlarmType.Alarm_RCTA
                end
            elseif ((vehicle.v >= 0) and (vehicle.v <= 12)) then
                warningTypes = warningTypes | AlarmType.Alarm_RCTA
            end
        end
        -- Alarm_RCTB = 0x40,
        if ((switch.RCTB) and (vehicle.gear == Gear.R) and (vehicle.acc_pedal < 80) and not(vehicle.trailer) and not(vehicle.esp)) then
            if isAlarmType(alarmTypes, AlarmType.Alarm_RCTB) then
                if ((vehicle.v >= 0) and (vehicle.v <= 10)) then
                    warningTypes = warningTypes | AlarmType.Alarm_RCTB
                end
            elseif ((vehicle.v >= 0) and (vehicle.v <= 12)) then
                warningTypes = warningTypes | AlarmType.Alarm_RCTB
            end
        end
    elseif (radarID == 6) or (radarID == 7) then    -- 前角
        -- Alarm_FCTA = 0x80,
        if ((switch.FCTA) and (vehicle.gear == Gear.D) and (vehicle.v >= 0) and (vehicle.v <= 12)) then
            warningTypes = warningTypes | AlarmType.Alarm_FCTA
        end
        -- Alarm_FCTB = 0x0100,
        if ((switch.FCTB) and (vehicle.gear == Gear.D) and (vehicle.v >= 0) and (vehicle.v <= 13)) then
            warningTypes = warningTypes | AlarmType.Alarm_FCTB
        end
        -- Alarm_JA = 0x0200
        if ((switch.JA) and (vehicle.gear == Gear.D) and (vehicle.v >= 8) and (vehicle.v <= 32)) then
            warningTypes = warningTypes | AlarmType.Alarm_JA
        end
    end

    return warningTypes
end
