﻿#ifndef OBJECTCORRDINATESYSTEMCONFIGDIALOG_H
#define OBJECTCORRDINATESYSTEMCONFIGDIALOG_H

#include <QDialog>
#include <QGroupBox>

#include "objectcoordinatesystem.h"
#include "analysis/analysisdata.h"

class QCheckBox;
class QComboBox;
class QSpinBox;
class QLineEdit;
class QVBoxLayout;

namespace Ui {
class ObjectCorrdinateSystemConfigDialog;
}

namespace Views {
namespace ObjectView {

class ObjectCoordinateSystem;

class ObjectConfig : public QWidget
{
    Q_OBJECT
public:
    ObjectConfig(MoveStatus moveStatus, const DrawConfig &drawConfig, QWidget *parent = nullptr);

    void setConfig(const DrawConfig &drawConfig);

    DrawConfig config();

    MoveStatus moveStatus() const { return mMoveStatus; }

private:
    QCheckBox *mCheckBoxShow{0};
    QComboBox *mComboBoxShape{0};
    QPushButton *mPushButtonColor{0};
    QColor mColor;
    QSpinBox *mSpinBoxSize{0};

    MoveStatus mMoveStatus;
};

class ObjectsConfig : public QGroupBox
{
    Q_OBJECT
public:
    ObjectsConfig(AnalysisFrameType frameType, const ObjectsDrawConfig &drawConfig, QWidget *parent = nullptr);

    void setConfig(const ObjectsDrawConfig &drawConfig);

    void config(ObjectsDrawConfig &drawConfig);

    AnalysisFrameType frameType() const { return mFrameType; }

signals:
    void objectApplyConfig();

private:
    QCheckBox *mCheckBoxNegateX{0};
    QCheckBox *mCheckBoxNegateY{0};
    QLineEdit *mLineEditOffset{0};
    QVBoxLayout *mLayoutObjects{0};

    AnalysisFrameType mFrameType;
};

class ObjectCorrdinateSystemConfigDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ObjectCorrdinateSystemConfigDialog(quint8 currentRadarID, ObjectCoordinateSystem *system, QWidget *parent = nullptr);
    ~ObjectCorrdinateSystemConfigDialog();

private slots:
    void on_pushButtonApply_clicked();

    void on_pushButtonOK_clicked();

    void on_comboBoxRadarID_currentIndexChanged(int index);

    void on_pushButtonSetAxis_clicked();

private:
    Ui::ObjectCorrdinateSystemConfigDialog *ui;

    ObjectCoordinateSystem *mObjectCoordinateSystem{0};
};

} // namespace ObjectView
} // namespace Views

#endif // OBJECTCORRDINATESYSTEMCONFIGDIALOG_H
