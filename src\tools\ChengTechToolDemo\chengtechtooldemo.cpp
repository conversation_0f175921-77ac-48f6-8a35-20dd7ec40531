﻿#include "chengtechtooldemo.h"
#include "ui_chengtechtooldemo.h"

#include <devices/devicemanager.h>
#include <devices/deviceconfigdialog.h>
#include <utils/settingshandler.h>
#include <utils/utils.h>
#include "version.h"
#include "ctanalysisworker.h"

#include <QThread>
#include <QMessageBox>

static const char deviceSettingsKey[] = "Devices/DeviceSettings";

ChengTechToolDemo::ChengTechToolDemo(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::ChengTechToolDemo),
    mDeviceManager(new Devices::Can::DeviceManager(this))
{
    qRegisterMetaType<Devices::Can::CanFrame>("CanFrame");

    ui->setupUi(this);
    this->setWindowTitle( QString("%1 %2")
                    .arg(CHENGTECH_TOOL_DEMO_NAME)
                    .arg(CHENGTECH_TOOL_DEMO_VERSION));

    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceOpened, this, [=](){
        ui->actionOpenDevice->setEnabled(true);
        ui->actionOpenDevice->setText(QString::fromLocal8Bit("关闭设备"));
        ui->actionSelectDevice->setEnabled(false);
    });
    connect(mDeviceManager, &Devices::Can::DeviceManager::deviceClosed, this, [=](){
        ui->actionOpenDevice->setEnabled(true);
        ui->actionOpenDevice->setText(QString::fromLocal8Bit("打开设备"));
        ui->actionSelectDevice->setEnabled(true);
    });

    QThread *analysisThread = new QThread;
    mCTAnalysisWorker = new CTAnalysisWorker;
    mCTAnalysisWorker->moveToThread(analysisThread);

    analysisThread->start();

    loadSettings();
}

ChengTechToolDemo::~ChengTechToolDemo()
{
    delete ui;
}

void ChengTechToolDemo::closeEvent(QCloseEvent *event)
{
    if (mDeviceManager->isOpened()) {
        mDeviceManager->closeDevice();
    }

    savesettings();
}


void ChengTechToolDemo::on_actionOpenDevice_triggered()
{
    ui->actionOpenDevice->setEnabled(false);
    if (!mDeviceManager->isOpened()) {
        if (!mDeviceManager->openDevice()) {
            ui->actionOpenDevice->setEnabled(true);
            QMessageBox::warning(this, QString::fromLocal8Bit("运行"), QString::fromLocal8Bit("运行失败!\n%1").arg(mDeviceManager->errorString()));
            return;
        }
    } else {

        if (!mDeviceManager->closeDevice()) {
            ui->actionOpenDevice->setEnabled(true);
            return;
        }
    }
}

void ChengTechToolDemo::on_actionSelectDevice_triggered()
{
    Devices::Can::DeviceConfigDialog *dialog = mDeviceManager ?
                new Devices::Can::DeviceConfigDialog(mDeviceManager->deviceSettings(), this) :
                new Devices::Can::DeviceConfigDialog(this);
    connect(dialog, &Devices::Can::DeviceConfigDialog::applied, this, [=](){
        Devices::Can::DeviceSettings deviceSetting = dialog->settings();
        deviceChanged(deviceSetting);
    });
    dialog->exec();
}

void ChengTechToolDemo::deviceChanged(Devices::Can::DeviceSettings deviceSettings)
{
    bool newDeviceWorker = false;
    mDeviceManager->setDeviceSettings(deviceSettings, newDeviceWorker);
    if (newDeviceWorker)
    {
        Devices::Can::IDeviceWorker *deviceWorker = mDeviceManager->deviceWorker();
        connect(deviceWorker, &Devices::Can::IDeviceWorker::frameRecieved, mCTAnalysisWorker, &CTAnalysisWorker::canFrame);
    }
}

void ChengTechToolDemo::loadSettings()
{
    Devices::Can::DeviceSettings deviceSettings = Devices::Can::DeviceSettings{Devices::Can::DeviceSettings::ZLG, 41, 0};
    deviceSettings.mDeviceChannelSettings << Devices::Can::DeviceChannelSettings{6, 0, 0, true, 500000, 2000000} << Devices::Can::DeviceChannelSettings{6, 0, 1, true, 500000, 2000000};
    QVariant settings = SETTINGS_GET_VALUE(QLatin1String(deviceSettingsKey));
    if (settings.isValid())
    {
        deviceSettings.setSettings(settings);
    }

    deviceChanged(deviceSettings);
}

void ChengTechToolDemo::savesettings()
{
    SETTINGS_SET_VALUE(QLatin1String(deviceSettingsKey), mDeviceManager->deviceSettings().getSettings());
}
