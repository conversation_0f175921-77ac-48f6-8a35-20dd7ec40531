﻿#include "ffmpegcamera.h"

#include "ffmpeginitializer.h"
#include "ffmpegreader.h"
#include "ffmpegwirter.h"

#include <QDateTime>

#include <iostream>
#include <thread>

void videoRead(FFmpegCamera *pVideoDecoder) {
    std::cout << __FUNCTION__ << " " << __LINE__ << " " << "线程开始，开始读取视频数据 ..." << std::endl;
    while (pVideoDecoder->isOpened())
    {
        pVideoDecoder->readFrame();
    }
    pVideoDecoder->closeCamera();
    std::cout << __FUNCTION__ << " " << __LINE__ << " " << "线程结束，停止读取视频数据." << std::endl;
}

std::list<std::string> FFmpegCamera::available_cameras()
{
    FFmpegInitializer::initFFmpeg();

    std::list<std::string> cameras;

    const AVInputFormat *inputFormat = av_find_input_format("dshow"); // dshow方式打开
    if (!inputFormat) {
        return cameras;
    }

    AVDeviceInfoList *device_list;
    avdevice_list_input_sources(inputFormat,nullptr,nullptr,&device_list);
//    avdevice_list_devices(s, &device_list);
    std::cout << __FUNCTION__ << " " << __LINE__ << " " << "nb_devices            : " << device_list->nb_devices     << std::endl;
    std::cout << __FUNCTION__ << " " << __LINE__ << " " << "default_device        : " << device_list->default_device << std::endl;
    for (int i = 0; i < device_list->nb_devices; ++i) {
        AVDeviceInfo *device = device_list->devices[i];
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "device_name           : " << device->device_name        << std::endl;
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "device_description    : " << device->device_description << std::endl;
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "nb_media_types        : " << device->nb_media_types     << std::endl;
        for (int j = 0; j < device->nb_media_types; ++j) {
            std::cout << __FUNCTION__ << " " << __LINE__ << " " << device->media_types[j];
            if (device->media_types[j] == AVMEDIA_TYPE_VIDEO) {
                cameras.push_back(device->device_name);
            }
        }
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << std::endl;
    }

    avdevice_free_list_devices(&device_list);

    return cameras;
}

FFmpegCamera::FFmpegCamera(const std::string &url, int index, QObject *parent)
    : QObject(parent), mURL(url), mCameraIndex(index)
{
    mFFmpegReader = new FFmpegReader(url, false);
    mFFmpegWirter = new FFmpegWirter();
}

bool FFmpegCamera::open()
{
    if (mOpened) {
        return true;
    }

    if (mURL.empty()) {
        return false;
    }

    std::cout << __FUNCTION__ << " " << __LINE__ << " " << mURL << std::endl;

    if (!mFFmpegReader->open()) {
        return false;
    }

    mOpened = true;
    std::thread(videoRead, this).detach();

    mState = QCamera::ActiveState;
    emit stateChanged(mState);

    return true;
}

bool FFmpegCamera::close()
{
    mOpened = false;
    if (mFFmpegWirter->isSaveing()) {
        stopSave();
    }

    return true;
}

bool FFmpegCamera::startSave(const char *filename)
{
    if (!mFFmpegWirter->save(filename, mFFmpegReader->streamIn())) {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "save error!" << std::endl;
        return false;
    }
    std::cout << __FUNCTION__ << " " << __LINE__ << " " << "Save begin ..." << std::endl;

    return true;
}

void FFmpegCamera::startSaveVideo(const QString &savePath, quint64 saveTime)
{
    startSave(QString("%1/%2_%3.mp4")
              .arg(savePath)
              .arg(QDateTime::fromMSecsSinceEpoch(saveTime).toString("yyyy-MM-dd hh-mm-ss-zzz"))
              .arg(mCameraIndex).toLocal8Bit());
}

bool FFmpegCamera::stopSave()
{
    mFFmpegWirter->stop();
    return true;
}

bool FFmpegCamera::isSaveing() const
{
    return mFFmpegWirter->isSaveing();
}

void FFmpegCamera::closeCamera()
{
    mFFmpegReader->close();

    sws_freeContext(mpSwsContextRGB32);
    mpSwsContextRGB32 = NULL;

    if (mImage) {
        delete mImage;
        mImage = 0;
    }

    mState = QCamera::UnloadedState;
    emit stateChanged(mState);
}

//bool FFmpegCamera::initFillter()
//{
//    /*********************************************初始化结构体****************************************/
//    int ret;
//    const AVFilter *buffersrc = avfilter_get_by_name("buffer");                 //输入，原始数据输入处
//    const AVFilter *buffersink = avfilter_get_by_name("buffersink");        //输出，处理后的数据输出
//    outputs = avfilter_inout_alloc();
//    inputs = avfilter_inout_alloc();
//    filter_graph = avfilter_graph_alloc();
//    enum AVPixelFormat pix_fmts[] = { mpCodecContextInput->pix_fmt, AV_PIX_FMT_NONE };  //摄像头的像素格式

//    char args[256];
//    snprintf(args, sizeof(args),"video_size=%dx%d:pix_fmt=%d:time_base=%d/%d:pixel_aspect=%d/%d",
//             mpCodecContextInput->width, mpCodecContextInput->height, mpCodecContextInput->pix_fmt,1, 30, 1, 1);   //输入过滤器参数配置：1280*720 j420p 帧率30
//    ret = avfilter_graph_create_filter(&buffersrc_ctx, buffersrc, "in", args, NULL, filter_graph);  //初始化并创建输入过滤器参数，通过参数指向具体的过滤器
//    if (ret < 0)
//    {
//        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avfilter_graph_create_filter" << std::endl;
//        return false;
//    }

//    ret = avfilter_graph_create_filter(&buffersink_ctx, buffersink, "out",NULL, NULL, filter_graph); //创建输出过滤器
//    if (ret < 0)
//    {
//        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avfilter_graph_create_filter" << std::endl;
//        return false;
//    }

//    //参考和总结其他人的参数配置，实现水印格式添加
//    outputs->name = av_strdup("in");
//    outputs->filter_ctx = buffersrc_ctx;
//    outputs->pad_idx = 0;
//    outputs->next = NULL;
//    inputs->name = av_strdup("out");
//    inputs->filter_ctx = buffersink_ctx;
//    inputs->pad_idx = 0;
//    inputs->next = NULL;

//    //将一串通过字符串描述的Graph添加到FilterGraph中
//    char filter_descr[128];
//    memset(filter_descr,0,sizeof(filter_descr));
//    sprintf(filter_descr,"drawtext=fontfile =verdanaz.ttf:x =w-tw:fontcolor=white:fontsize=20:text="":x=5:y=5");
//    if ((ret = avfilter_graph_parse_ptr(filter_graph, filter_descr,&inputs, &outputs, NULL)) < 0) {
//        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avfilter_graph_parse_ptr" << std::endl;
//        return false;
//    }
//    if ((ret = avfilter_graph_config(filter_graph, NULL)) < 0) {
//        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avfilter_graph_parse_ptr" << std::endl;
//        return false;
//    }

//    int parsed_drawtext_0_index = -1;
//    for (int i = 0; i < filter_graph->nb_filters; i++)
//    {
//        AVFilterContext* filter_ctxn = filter_graph->filters[i];
//        fprintf(stdout, "[%s](%d) filter_ctxn->name=%s\n",__func__,__LINE__,filter_ctxn->name);
//        if(!strcmp(filter_ctxn->name,"Parsed_drawtext_0"))
//            parsed_drawtext_0_index = i;
//    }
//    if(parsed_drawtext_0_index == -1) {
//        fprintf(stderr, "[%s](%d) no Parsed_drawtext_0\n",__func__,__LINE__);
//        std::cout << __FUNCTION__ << " " << __LINE__ << " " << "avfilter_graph_parse_ptr" << std::endl;
//        return false;
//    }
//    filter_ctx = filter_graph->filters[parsed_drawtext_0_index];
//    /*****************************************************初始化完成*********************************************/

//    return true;
//}

//bool FFmpegCamera::freeFillter()
//{
//    avfilter_inout_free(&outputs);
//    avfilter_inout_free(&inputs);
//    avfilter_graph_free(&filter_graph);

//    return true;
//}

void FFmpegCamera::readFrame()
{
//    waterMark(mpFrame, mpFrame);
    AVFrame *frame = mFFmpegReader->read();
    if (frame) {
        emit cameraSaveIndex(mCameraIndex, mFFmpegWirter->write(frame));
        sendFrame(frame);
    }
}

//frame_in:需要加水印的输入帧
//frame_out:完成加水印后取到的帧，执行完函数并完成相关逻辑后需要av_frame_unref(frame_out);
//int FFmpegCamera::waterMark(AVFrame *frame_in,AVFrame *frame_out)
//{

//    if (!initFillter()) {
//        closeReader();
//        return false;
//    }

//     /**************************************************给图像添加水印*********************************************/
//    char sys_time[64];
//    time_t ltime;
//    time(&ltime);
//    struct tm* today = localtime(&ltime);
//    strftime(sys_time, sizeof(sys_time), "%Y-%m-%d %a %H:%M:%S", today);       //24小时制
//    av_opt_set(filter_ctx->priv, "text", sys_time, 0 );  //设置text到过滤器

//    // 往源滤波器buffer中输入待处理的数据
//    if (av_buffersrc_add_frame(buffersrc_ctx, frame_in) < 0)
//    {
//        return  -3;
//    }
//    // 从目的滤波器buffersink中输出处理完的数据
//    int ret = av_buffersink_get_frame(buffersink_ctx, frame_out);
//    if (ret < 0)
//        return  ret;

//    freeFillter();

//    return 0;
//}

void FFmpegCamera::sendFrame(AVFrame *frame)
{
    if (!toRGB32(frame)) {
        return;
    }

    if (mImage) {
        emit showImage(mImage->copy());
    }
}

bool FFmpegCamera::toRGB32(AVFrame *frame)
{
    // 图像转换上下文
    if (!mpSwsContextRGB32)
    {
        // 获取缓存的图像转换上下文。首先校验参数是否一致，如果校验不通过就释放资源；然后判断上下文是否存在，如果存在直接复用，如不存在进行分配、初始化操作
        mpSwsContextRGB32 = sws_getCachedContext(mpSwsContextRGB32,
                                            frame->width,                    // 输入图像的宽度
                                            frame->height,                   // 输入图像的高度
                                            (AVPixelFormat) frame->format,   // 输入图像的像素格式
                                            frame->width,                    // 输出图像的宽度
                                            frame->height,                   // 输出图像的高度
                                            AV_PIX_FMT_RGB32,                // 输出图像的像素格式
                                            SWS_BICUBIC,                    // 选择缩放算法(只有当输入输出图像大小不同时有效),一般选择SWS_FAST_BILINEAR
                                            nullptr,                           // 输入图像的滤波器信息, 若不需要传NULL
                                            nullptr,                           // 输出图像的滤波器信息, 若不需要传NULL
                                            nullptr);                          // 特定缩放算法需要的参数(?)，默认为NULL
        if (!mpSwsContextRGB32)
        {
            return false;
        }
    }

    if (!mImage) {
        mImage = new QImage(frame->width, frame->height, QImage::Format_RGB32);
    }
    uint8_t* data[1] = { reinterpret_cast<uint8_t*>(mImage->bits()) };
    int linesize[1] = { static_cast<int>(mImage->bytesPerLine()) };
    int ret = sws_scale(mpSwsContextRGB32,          // 缩放上下文
                        frame->data,         // 原图像数组
                        frame->linesize,     // 包含源图像每个平面步幅的数组
                        0,                     // 开始位置
                        frame->height,      // 行数
                        data,        // 目标图像数组
                        linesize);   // 包含目标图像每个平面的步幅的数组
    if (ret < 0)
    {
        return false;
    }
    return true;
}
