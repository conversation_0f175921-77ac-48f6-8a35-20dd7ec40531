﻿#ifndef GWMTARGETPROTOCOL_H
#define GWMTARGETPROTOCOL_H

#include "ianalysisprotocol.h"

namespace Analysis {
namespace Protocol {

class GWMTargetProtocol : public IAnalysisProtocol
{
    Q_OBJECT
public:
    GWMTargetProtocol( AnalysisWorker *analysisWorker, QObject *parent = nullptr );

    bool analysisFrame(const Devices::Can::CanFrame &frame) override;

private:
    bool GWM16TargetClear(quint8 radarID);
    bool GWM16TargetHeaderParse(quint8 radarID, const Devices::Can::CanFrame &frame );
    bool GWM16TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame );
    bool GWMElkTargetParse( const Devices::Can::CanFrame &frame );
    bool GWMLaneTargetParse( const Devices::Can::CanFrame &frame );

    int mGWM16TargetCount[MAX_RADAR_COUNT];
    int mGWM16TargetCurrentIndex[MAX_RADAR_COUNT];
};

}
}

#endif // GWMTARGETPROTOCOL_H
