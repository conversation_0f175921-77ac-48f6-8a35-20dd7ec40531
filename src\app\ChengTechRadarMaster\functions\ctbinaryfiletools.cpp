﻿#include "ctbinaryfiletools.h"
#include "ui_ctbinaryfiletools.h"

#include "utils/settingshandler.h"

#include <QThread>
#include <QFileDialog>
#include <QMessageBox>

#define BIG_LITTLE_SWAP16(x)        ( (((*(short int *)&x) & 0xff00) >> 8) | \
                                      (((*(short int *)&x) & 0x00ff) << 8) )

#define BIG_LITTLE_SWAP32(x)        ( (((*(long int *)&x) & 0xff000000) >> 24) | \
                                      (((*(long int *)&x) & 0x00ff0000) >> 8) | \
                                      (((*(long int *)&x) & 0x0000ff00) << 8) | \
                                      (((*(long int *)&x) & 0x000000ff) << 24) )

#define BIG_LITTLE_SWAP64(x)        ( (((*(long long int *)&x) & 0xff00000000000000) >> 56) | \
                                      (((*(long long int *)&x) & 0x00ff000000000000) >> 40) | \
                                      (((*(long long int *)&x) & 0x0000ff0000000000) >> 24) | \
                                      (((*(long long int *)&x) & 0x000000ff00000000) >> 8) | \
                                      (((*(long long int *)&x) & 0x00000000ff000000) << 8) | \
                                      (((*(long long int *)&x) & 0x0000000000ff0000) << 24) | \
                                      (((*(long long int *)&x) & 0x000000000000ff00) << 40) | \
                                      (((*(long long int *)&x) & 0x00000000000000ff) << 56) )

CTBinaryFileTools::CTBinaryFileTools(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CTBinaryFileTools)
{
    ui->setupUi(this);

    ui->lineEditBinaryFile->setText(SETTINGS_GET_VALUE("CTBinaryFileTools/BinaryFile").toString());

    QThread *thread = new QThread;
    mCTBinaryFile = new CTBinaryFile;
    mCTBinaryFile->moveToThread(thread);

    connect(thread, &QThread::started, mCTBinaryFile, &CTBinaryFile::run);
    connect(mCTBinaryFile, &CTBinaryFile::readBegin, thread, [=]() { thread->start(); });
    connect(mCTBinaryFile, &CTBinaryFile::readEnd, thread, &QThread::quit);
    connect(mCTBinaryFile, &CTBinaryFile::destroyed, thread, &QThread::deleteLater);

    connect(mCTBinaryFile, &CTBinaryFile::writeIndex, this, &CTBinaryFileTools::writeIndex);
    connect(mCTBinaryFile, &CTBinaryFile::closed, this, &CTBinaryFileTools::closed);
    connect(mCTBinaryFile, &CTBinaryFile::readEnd, this, &CTBinaryFileTools::readEnd);

//    connect(mCTBinaryFile, &CTBinaryFile::frameRecieved, this, &CTBinaryFileTools::frameRecieved);
//    connect( mDeviceWorkerBinFile, &Devices::Can::DeviceWorkerBinFile::currentFrameTimestemp, this, &CTBinaryFileTools::currentFrameTimeChanged );
//    connect( mDeviceWorkerBinFile, &Devices::Can::DeviceWorkerBinFile::fileBeginEndTimestemp, this, &CTBinaryFileTools::fileBeginEndTimestemp );
}

CTBinaryFileTools::~CTBinaryFileTools()
{
    delete ui;
}

void CTBinaryFileTools::closed()
{
    ui->pushButtonOpenBinaryFile->setText(QString::fromLocal8Bit(mCTBinaryFile->isOpened() ? "关闭" : "打开"));
    ui->pushButtonExport->setEnabled(mCTBinaryFile->isOpened());
}

void CTBinaryFileTools::readEnd()
{
    ui->pushButtonExport->setText(QString::fromLocal8Bit(mCTBinaryFile->isExporting() ? "停止" : "导出"));
}

void CTBinaryFileTools::writeIndex(int index)
{
    ui->labelWriteIndex->setText(QString::number(index));
}

void CTBinaryFileTools::frameRecieved(const Devices::Can::CanFrame &frame)
{
    QString text = QString("0x%1 %2").arg(frame.idHex()).arg(frame.dataHex());

    ui->textEdit->append(text);
}

void CTBinaryFileTools::on_pushButtonBinaryFile_clicked()
{
    QString fileName = QFileDialog::getOpenFileName(this, tr("Open CT Binary File"),
                                                      ui->lineEditBinaryFile->text(),
                                                      tr("CT Binary (*.binary)"));

    if (fileName.isEmpty()) {
        return;
    }

    SETTINGS_SET_VALUE("CTBinaryFileTools/BinaryFile", fileName);
    ui->lineEditBinaryFile->setText(fileName);
}

void CTBinaryFileTools::on_pushButtonOpenBinaryFile_clicked()
{
    if (!mCTBinaryFile->isOpened()) {
        ui->textEdit->clear();
        if (!mCTBinaryFile->open(ui->lineEditBinaryFile->text())) {
            QMessageBox::warning(this, QString::fromLocal8Bit("打开文件"), QString::fromLocal8Bit("打开文件失败!\n%1\n%2")
                                 .arg(mCTBinaryFile->errorString())
                                 .arg(ui->lineEditBinaryFile->text()));
        } else {
            const CTBinaryFile::CTBinaryFileHeader &header = mCTBinaryFile->fileHeader();
            QString text = QString("Version:           %1\n"
                                   "CreateTime:        %2\n"
                                   "FileIndex:         %3\n"
                                   "ReservedDataCount: %4\n"
                                   "ProtocolCount:     %5\n"
                                   "Protocols:         %6\n"
                                   "ReservedData:      %7\n")
                    .arg(header.mHeaderBase.mVersion, 0, 16)             // 上位机版本
                    .arg(QDateTime::fromMSecsSinceEpoch(header.mHeaderBase.mCreateTime).toString("yyyy-MM-dd hh:mm:ss.zzz"))          // 创建时间
                    .arg(header.mHeaderBase.mFileIndex)           // 文件计数
                    .arg(header.mHeaderBase.mReservedDataCount)   // 预留字节数量
                    .arg(header.mHeaderBase.mProtocolCount)       // 协议数量
                    .arg(QByteArray::fromRawData((char *)header.mProtocols, header.mHeaderBase.mProtocolCount).toHex(' ').data())          // 协议
                    .arg(QByteArray::fromRawData((char *)header.mReservedData, header.mHeaderBase.mReservedDataCount).toHex(' ').data());       // 预留字节

            ui->textEdit->append(text);
            text = QString("\n4: %1; 5: %2; 6: %3; 7: %4\n")
                    .arg(mCTBinaryFile->radarFrameCount(4))
                    .arg(mCTBinaryFile->radarFrameCount(5))
                    .arg(mCTBinaryFile->radarFrameCount(6))
                    .arg(mCTBinaryFile->radarFrameCount(7));

            ui->textEdit->append(text);

            ui->checkBoxRadar_4->setChecked(mCTBinaryFile->radarFrameCount(4));
            ui->checkBoxRadar_5->setChecked(mCTBinaryFile->radarFrameCount(5));
            ui->checkBoxRadar_6->setChecked(mCTBinaryFile->radarFrameCount(6));
            ui->checkBoxRadar_7->setChecked(mCTBinaryFile->radarFrameCount(7));
            ui->labelRadarFrameCount->setText(QString::number(mCTBinaryFile->radarFrameCount()));
        }
    } else {
        mCTBinaryFile->close();
    }

    ui->pushButtonOpenBinaryFile->setText(QString::fromLocal8Bit(mCTBinaryFile->isOpened() ? "关闭" : "打开"));
    ui->pushButtonExport->setEnabled(mCTBinaryFile->isOpened());
}

void CTBinaryFileTools::on_pushButtonExport_clicked()
{
    QString fileName = QFileDialog::getSaveFileName(this, tr("Save CT Binary File"),
                                                      ui->lineEditBinaryFile->text(),
                                                      tr("CT Binary (*.binary)"));

    if (fileName.isEmpty()) {
        return;
    }

    ui->lineEditExportFile->setText(fileName);

    if (!mCTBinaryFile->isExporting()) {
        writeIndex(0);
        if (!mCTBinaryFile->exportFile(ui->lineEditSkipCount->text().toInt(), ui->lineEditSaveCount->text().toInt(), fileName)) {
            QMessageBox::warning(this, QString::fromLocal8Bit("导出文件"), QString::fromLocal8Bit("导出文件失败!\n%1\n%2")
                                 .arg(mCTBinaryFile->errorString())
                                 .arg(ui->lineEditExportFile->text()));
        }
    } else {
        mCTBinaryFile->stopExport();
    }

    ui->pushButtonExport->setText(QString::fromLocal8Bit(mCTBinaryFile->isExporting() ? "停止" : "导出"));
}

CTBinaryFile::CTBinaryFile()
{

}

CTBinaryFile::~CTBinaryFile()
{

}

bool CTBinaryFile::open(const QString &filename)
{
    if (mOpened) {
        mErrorString = QString::fromLocal8Bit("其它已打开文件未关闭!");
        return false;
    }

    if (filename.isEmpty()) {
        mErrorString = QString::fromLocal8Bit("文件名为空!");
        return mOpened;
    }

    mFile.setFileName( filename );
    if( !mFile.open( QFile::ReadOnly ) ){
        mErrorString = QString::fromLocal8Bit("打开文件失败!\r\n%1").arg(filename);
        return mOpened;
    }

    mOpened = radeHeader();

    if (mOpened) {
        emit opened();
    }

    return mOpened;
}

bool CTBinaryFile::close()
{
    if (!mOpened) {
        return !mOpened;
    }

    if (!mExporting && !mReading) {
        mFile.close();
        emit closed();
    }

    mOpened = false;
    mReading = false;
    mExporting = false;

    return !mOpened;
}

bool CTBinaryFile::exportFile(int skipFrame, int saveTotal, const QString &filename)
{
    if (!mOpened) {
        mErrorString = QString::fromLocal8Bit("文件未打开!");
        return false;
    }

    mFile.seek(mHeaderDataLength);

    if (!skip(skipFrame)) {
        return false;
    }
    mReading = false;
    mExporting = false;
    mSaveFrameTotal = mRadarCount * saveTotal;
    mSaveFrameCount = 0;
    mFileExport.setFileName(filename);
    if( !mFileExport.open( QFile::WriteOnly ) ){
        mErrorString = QString::fromLocal8Bit("打开保存文件失败!\r\n%1").arg(filename);
        return mExporting;
    }

    if (!wirteHeader()) {
        mErrorString = QString::fromLocal8Bit("文件头写入失败!1");
        return mExporting;
    }

    mReading = true;
    mExporting = true;

    emit readBegin();

    return mExporting;
}

bool CTBinaryFile::stopExport()
{
    if (!mOpened) {
        return !mOpened;
    }

    mReading = false;

    return true;
}

bool CTBinaryFile::startRead(int skipFrame)
{
    if (!mOpened) {
        mErrorString = QString::fromLocal8Bit("文件未打开!");
        return false;
    }

    mFile.seek(mHeaderDataLength);

    if (!skip(skipFrame)) {
        return false;
    }

    mSaveFrameTotal = -1;
    mReading = true;
    mExporting = false;

    emit readBegin();

    return true;
}

bool CTBinaryFile::stopRead()
{
    mReading = false;

    return true;
}

void CTBinaryFile::run()
{
    while (mOpened && mReading && readNextBlock()) {
    }

    if (mExporting) {
        mFileExport.close();
        mExporting = false;
    } else {
        if (!mOpened) {
            mFile.close();
            emit closed();
        }
    }

    qDebug() << __FUNCTION__ << __LINE__ << mOpened << mReading << mExporting;

    emit readEnd();
}

bool CTBinaryFile::radeHeader()
{
    mResivedSize = mFile.size();
    if (mResivedSize < sizeof (CTBinaryFileHeaderBase)) {
        mErrorString = QString::fromLocal8Bit("数据长度异常【文件头】!");
        return false;
    }


    if (sizeof (mCTBinaryFileHeader.mHeaderBase) != mFile.read((char *)&(mCTBinaryFileHeader.mHeaderBase), sizeof (mCTBinaryFileHeader.mHeaderBase))) {
        mErrorString = QString::fromLocal8Bit("数据读取异常【文件头】!");
        return false;
    }
    mResivedSize -= sizeof (mCTBinaryFileHeader.mHeaderBase);

    mCTBinaryFileHeader.mHeaderBase.mCreateTime = BIG_LITTLE_SWAP64(mCTBinaryFileHeader.mHeaderBase.mCreateTime);

    if (mCTBinaryFileHeader.mHeaderBase.mProtocolCount && (
            (mResivedSize < mCTBinaryFileHeader.mHeaderBase.mProtocolCount) ||
            (mCTBinaryFileHeader.mHeaderBase.mProtocolCount !=
             mFile.read((char *)mCTBinaryFileHeader.mProtocols, mCTBinaryFileHeader.mHeaderBase.mProtocolCount)))) {
        mErrorString = QString::fromLocal8Bit("数据读取异常【文件头-协议】!");
        return false;
    }
    mResivedSize -= mCTBinaryFileHeader.mHeaderBase.mProtocolCount;

    if (mCTBinaryFileHeader.mHeaderBase.mReservedDataCount && (
                (mResivedSize < mCTBinaryFileHeader.mHeaderBase.mReservedDataCount) ||
            (mCTBinaryFileHeader.mHeaderBase.mReservedDataCount !=
             mFile.read((char *)mCTBinaryFileHeader.mReservedData, mCTBinaryFileHeader.mHeaderBase.mReservedDataCount)))) {
        mErrorString = QString::fromLocal8Bit("数据读取异常【文件头-预留】!");
        return false;
    }
    mResivedSize -= mCTBinaryFileHeader.mHeaderBase.mReservedDataCount;

    mHeaderDataLength = mFile.size() - mResivedSize;

//    qDebug() << __FUNCTION__ << __LINE__ << endl
//             << QString::number(mCTBinaryFileHeader.mHeaderBase.mVersion, 16) << endl
//             << mCTBinaryFileHeader.mHeaderBase.mCreateTime << endl
//             << mCTBinaryFileHeader.mHeaderBase.mFileIndex << endl
//             << mCTBinaryFileHeader.mHeaderBase.mReservedDataCount << endl
//             << mCTBinaryFileHeader.mHeaderBase.mProtocolCount << endl
//             << QByteArray::fromRawData((const char *)mCTBinaryFileHeader.mProtocols, mCTBinaryFileHeader.mHeaderBase.mProtocolCount).toHex(' ') << endl
//             << QByteArray::fromRawData((const char *)mCTBinaryFileHeader.mReservedData, mCTBinaryFileHeader.mHeaderBase.mReservedDataCount).toHex(' ') << endl;

    return readBlockHeaders();
}

bool CTBinaryFile::wirteHeader()
{
    CTBinaryFileHeader header = mCTBinaryFileHeader;
    header.mHeaderBase.mCreateTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    mCTBinaryFileHeader.mHeaderBase.mCreateTime = BIG_LITTLE_SWAP64(mCTBinaryFileHeader.mHeaderBase.mCreateTime);

    mFileExport.write((char *)&(header.mHeaderBase), sizeof (header.mHeaderBase));
    mFileExport.write((char *)header.mProtocols, header.mHeaderBase.mProtocolCount);
    mFileExport.write((char *)header.mReservedData, header.mHeaderBase.mReservedDataCount);

    mSaveFrameCount = 0;

    return true;
}

bool CTBinaryFile::skip(int skipFrame)
{
    int skipCount = mRadarCount * skipFrame;
    if (skipCount <= 0) {
        return true;
    }

    if (skipCount >= mRadarDataLocations.size()) {
        mErrorString = QString::fromLocal8Bit("跳转帧数超过数据大小!");
        return false;
    }

    const RadarDataLocation &loction = mRadarDataLocations[skipCount];
    mResivedSize = mFile.size() - loction.mLocation;
    mFile.seek(loction.mLocation);

    return true;
}

bool CTBinaryFile::readBlockHeaders()
{
    mRadarDataLocations.clear();
    mRadarCount = 0;
    memset(mRadarFrameCount, 0, sizeof (mRadarFrameCount));
    memset(mRadarFirstFrameIndex, 0, sizeof (mRadarFirstFrameIndex));
    unsigned int size = mFile.size();
    unsigned int loction = size - mResivedSize;
    unsigned int currentLoction = size - mResivedSize;
    CTBinaryFileBlockHeader blockHeader;
    while (!mFile.atEnd()) {
        currentLoction = size - mResivedSize;
        if (!readBlockHeader(blockHeader)) {
            mResivedSize = size - loction;
            mFile.seek(loction);
            return false;
        }

        if (blockHeader.mType == 1) {
            mRadarFrameCount[blockHeader.mDeviceID]++;
            mRadarDataLocations << RadarDataLocation{blockHeader.mType, blockHeader.mDeviceID, currentLoction};
            if (mRadarFrameCount[blockHeader.mDeviceID] == 1) { // 首帧
                mRadarFirstFrameIndex[blockHeader.mDeviceID] = 0;
            }
        }

        if (blockHeader.mBodyLength) {
            mFile.seek(size - mResivedSize + blockHeader.mBodyLength);
            mResivedSize -= blockHeader.mBodyLength;
        }
    }

    qDebug() << __FUNCTION__ << __LINE__ << mRadarDataLocations.size() << mRadarFrameCount[4] << mRadarFrameCount[5] << mRadarFrameCount[6] << mRadarFrameCount[7];


    for (int i = 4; i < 8; ++i) {
        if (mRadarFrameCount[i]) {
            mRadarCount++;
        }
    }

    mResivedSize = size - loction;
    mFile.seek(loction);

    return true;
}

bool CTBinaryFile::readBlockHeader(CTBinaryFileBlockHeader &blockHeader)
{
    if (mResivedSize < sizeof (blockHeader) ||
            (sizeof (blockHeader) != mFile.read((char *)(&blockHeader), sizeof (blockHeader)))) {
        mErrorString = QString::fromLocal8Bit("数据长度异常【数据块头】!");
        return false;
    }

    blockHeader.mCreateTime = BIG_LITTLE_SWAP64(blockHeader.mCreateTime);
    blockHeader.mMutilFileIndex = BIG_LITTLE_SWAP32(blockHeader.mMutilFileIndex);
    blockHeader.mCurrentFileIndex = BIG_LITTLE_SWAP32(blockHeader.mCurrentFileIndex);
    blockHeader.mBlockIndex = BIG_LITTLE_SWAP32(blockHeader.mBlockIndex);
    blockHeader.mBodyLength = BIG_LITTLE_SWAP16(blockHeader.mBodyLength);

//    qDebug() << __FUNCTION__ << __LINE__ << endl
//             << blockHeader.mType << endl
//             << blockHeader.mDeviceID << endl
//             << blockHeader.mCreateTime << endl
//             << blockHeader.mMutilFileIndex << endl
//             << blockHeader.mCurrentFileIndex << endl
//             << blockHeader.mBlockIndex << endl
//             << blockHeader.mBodyLength << endl;

    mResivedSize -= sizeof (blockHeader);

    return true;
}

bool CTBinaryFile::readBlockData(const CTBinaryFile::CTBinaryFileBlockHeader &blockHeader)
{
    CTBinaryFileData data;
    unsigned int bodyLength = blockHeader.mBodyLength;
    while( bodyLength > 0 ){
        if (sizeof (data) != mFile.read((char *)(&data), sizeof (data))) {
            mErrorString = QString::fromLocal8Bit("数据读取异常【数据】!");
            return false;
        }

        mResivedSize -= sizeof (data);
        bodyLength -= sizeof (data);

        if (mResivedSize < data.mLength) {
            mErrorString = QString::fromLocal8Bit("数据读取异常【数据长度】!");
            return false;
        }

        QByteArray bdata = mFile.read(data.mLength);

        mResivedSize -= bdata.size();
        bodyLength -= bdata.size();

        data.mTimestemp = BIG_LITTLE_SWAP64(data.mTimestemp);
        data.mID = BIG_LITTLE_SWAP32(data.mID);

//        qDebug() << __FUNCTION__ << __LINE__
//                 << data.mType
//                 << data.mExpand
//                 << data.mTimestemp
//                 << data.mLength
//                 << data.mID;


        //body.printf();
        Devices::Can::CanFrame frame = Devices::Can::CanFrame( 0, 0, Devices::Can::CanFrame::RecieveOrTransmit::RX,
                                                               data.mID, bdata, data.mTimestemp,
                                                               (bool)data.mExpand);
        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    }

    return true;
}

bool CTBinaryFile::writeBlock(CTBinaryFileBlockHeader blockHeader)
{
    unsigned short bodyLength = blockHeader.mBodyLength;

    blockHeader.mMutilFileIndex = mSaveFrameCount;
    blockHeader.mBlockIndex = mSaveFrameCount;

    blockHeader.mCreateTime = BIG_LITTLE_SWAP64(blockHeader.mCreateTime);
    blockHeader.mMutilFileIndex = BIG_LITTLE_SWAP32(blockHeader.mMutilFileIndex);
    blockHeader.mCurrentFileIndex = BIG_LITTLE_SWAP32(blockHeader.mCurrentFileIndex);
    blockHeader.mBlockIndex = BIG_LITTLE_SWAP32(blockHeader.mBlockIndex);
    blockHeader.mBodyLength = BIG_LITTLE_SWAP16(blockHeader.mBodyLength);

    mResivedSize -= bodyLength;
    mFileExport.write((char *)(&blockHeader), sizeof (blockHeader));

    if (bodyLength != mFileExport.write(mFile.read(bodyLength))) {
        qDebug() << __FUNCTION__ << __LINE__ << bodyLength;
        mReading = false;
        return false;
    }

    if (blockHeader.mType == 1) {
        mSaveFrameCount++;
//        qDebug() << __FUNCTION__ << __LINE__ << mSaveFrameCount;
        emit writeIndex(mSaveFrameCount);
    }

    if (mSaveFrameTotal != -1 && mSaveFrameCount == mSaveFrameTotal) {
        mReading = false;
    }

    return true;
}

bool CTBinaryFile::readNextBlock()
{
    CTBinaryFileBlockHeader blockHeader;
    if (!readBlockHeader(blockHeader)) {
        return false;
    }

    if (mExporting) {
        return writeBlock(blockHeader);
    }

    if (blockHeader.mBodyLength <= 0) {
        return true;
    }

    return readBlockData(blockHeader);
}
