﻿#include "truesystemview.h"
#include "ui_truesystemview.h"
#include "truetargetsmonitor.h"

#include <QDebug>

namespace Views {
namespace TrueSystemView {

TrueSystemView::TrueSystemView(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::TrueSystemView)
{
    ui->setupUi(this);
}

TrueSystemView::~TrueSystemView()
{
    delete ui;
}

void TrueSystemView::setAnalysisManager(Analysis::AnalysisManager *analysisMgr)
{
    mAnalysisManager = analysisMgr;
}

void TrueSystemView::slotTrueObjInfo(stTrueObjInfo *trueObjInfo)
{
    // 0x600 自车时间戳
    QString timeStr = "";
    timeStr.append(QString("20%1-%2-%3 %4:%5:%6:%7")
                   .arg(trueObjInfo->mInsYear, 2, 10, QLatin1Char('0'))
                   .arg(trueObjInfo->mInsMonth, 2, 10, QLatin1Char('0'))
                   .arg(trueObjInfo->mInsDay, 2, 10, QLatin1Char('0'))
                   .arg(trueObjInfo->mInsHour, 2, 10, QLatin1Char('0'))
                   .arg(trueObjInfo->mInsMinute, 2, 10, QLatin1Char('0'))
                   .arg(trueObjInfo->mInsSecond, 2, 10, QLatin1Char('0'))
                   .arg(trueObjInfo->mInsMilliSecond, 3, 10, QLatin1Char('0'))
                   );
    ui->lineEdit_INS_TimeStamp->setText(timeStr);

    // 0x602 定位高度和状态
    ui->lineEditInsPosAlt->setText(QString::number(trueObjInfo->mInsPosAlt, 'f', 3));
    ui->lineEditInsInsPosEllipsoid->setText(QString::number(trueObjInfo->mInsPosEllipsoid, 'f', 3));
    ui->lineEditInsPosMode->setText(QString::number(trueObjInfo->mInsPosMode));
    ui->lineEditInsSolState->setText(QString::number(trueObjInfo->mInsSolState));
    ui->lineEditInsStasNum->setText(QString::number(trueObjInfo->mInsStasNum));

    // 0x606 自车姿态角
    ui->lineEditInsAngleHeading->setText(QString::number(trueObjInfo->mInsAngleHeading, 'f', 2));
    ui->lineEditInsAnglePitch->setText(QString::number(trueObjInfo->mInsAnglePitch, 'f', 2));
    ui->lineEditInsAngleRoll->setText(QString::number(trueObjInfo->mInsAngleRoll, 'f', 2));

    // 0x60c 自车速度速度（车辆坐标系下）

    // 0x6d1 学习与监控状态
    ui->lineEditDualAntOfsLrn->setText(QString::number(trueObjInfo->mDualAntOfsLrn, 'f', 1));
    ui->lineEditDualAntOfsLrnProgress->setText(QString::number(trueObjInfo->mDualAntOfsLrnProgress));
    ui->lineEditWsErr->setText(QString::number(trueObjInfo->mWsErr, 'f', 4));
    ui->lineEditWsErrCalcProgress->setText(QString::number(trueObjInfo->mWsErrCalcProgress));
    ui->lineEditImuExtrinPitchAng->setText(QString::number(trueObjInfo->mImuExtrinPitchAng, 'f', 1));
    ui->lineEditImuExtrinPitchProgress->setText(QString::number(trueObjInfo->mImuExtrinPitchProgress));
    ui->lineEditImuExtrinYawAng->setText(QString::number(trueObjInfo->mImuExtrinYawAng, 'f', 1));
    ui->lineEditImuExtrinYawProgress->setText(QString::number(trueObjInfo->mImuExtrinYawProgress));


    // 0x4A0 从车状态（主车坐标系下）
    ui->lineEditRDMStatus->setText(QString::number(trueObjInfo->mRDMStatus));
    ui->lineEditRDMStatusAzumith->setText(QString::number(trueObjInfo->mRDMAzumith , 'f', 2));
    ui->lineEditRDMStatusDistance->setText(QString::number(trueObjInfo->mRDMDistance , 'f', 2));
    ui->lineEditRDMStatusDelay->setText(QString::number(trueObjInfo->mRDMDelayMilliSecond));

    // 0x4A1 从车位置（主车坐标系下）
    ui->lineEditRDMPosX->setText(QString::number(trueObjInfo->mRDMPos[stTrueObjInfo::Origin].mRDMPosX, 'f',6));
    ui->lineEditRDMPosY->setText(QString::number(trueObjInfo->mRDMPos[stTrueObjInfo::Origin].mRDMPosY, 'f',6));
    ui->lineEditRDMPosZ->setText(QString::number(trueObjInfo->mRDMPos[stTrueObjInfo::Origin].mRDMPosZ, 'f', 6));

    ui->lineEditRDMPosX_R->setText(QString::number(trueObjInfo->mRDMPos[stTrueObjInfo::Origin].mRDMPosX, 'f',6));
    ui->lineEditRDMPosY_R->setText(QString::number(-trueObjInfo->mRDMPos[stTrueObjInfo::Origin].mRDMPosY, 'f',6));
    ui->lineEditRDMPosX_LF->setText(QString::number(trueObjInfo->mRDMPos[stTrueObjInfo::LeftFront].mRDMPosX, 'f',6));
    ui->lineEditRDMPosY_LF->setText(QString::number(-trueObjInfo->mRDMPos[stTrueObjInfo::LeftFront].mRDMPosY, 'f',6));
    ui->lineEditRDMPosX_RF->setText(QString::number(trueObjInfo->mRDMPos[stTrueObjInfo::RightFront].mRDMPosX, 'f',6));
    ui->lineEditRDMPosY_RF->setText(QString::number(-trueObjInfo->mRDMPos[stTrueObjInfo::RightFront].mRDMPosY, 'f',6));
    ui->lineEditRDMPosX_LR->setText(QString::number(trueObjInfo->mRDMPos[stTrueObjInfo::LeftRear].mRDMPosX, 'f',6));
    ui->lineEditRDMPosY_LR->setText(QString::number(-trueObjInfo->mRDMPos[stTrueObjInfo::LeftRear].mRDMPosY, 'f',6));
    ui->lineEditRDMPosX_RR->setText(QString::number(trueObjInfo->mRDMPos[stTrueObjInfo::RightRear].mRDMPosX, 'f',6));
    ui->lineEditRDMPosY_RR->setText(QString::number(-trueObjInfo->mRDMPos[stTrueObjInfo::RightRear].mRDMPosY, 'f',6));

    // 0x4A2 从车速度（主车坐标系下）
    ui->lineEditRDMVelX->setText(QString::number(trueObjInfo->mRDMVelX, 'f', 2));
    ui->lineEditRDMVelY->setText(QString::number(trueObjInfo->mRDMVelY, 'f', 2));
    ui->lineEditRDMVelZ->setText(QString::number(trueObjInfo->mRDMVelZ, 'f', 2));

    // 0x4A3 从车加速度（主车坐标系下）
    ui->lineEditRDMAccX->setText(QString::number(trueObjInfo->mRDMAccX, 'f', 3));
    ui->lineEditRDMAccY->setText(QString::number(trueObjInfo->mRDMAccY, 'f', 3));
    ui->lineEditRDMAccZ->setText(QString::number(trueObjInfo->mRDMAccZ, 'f', 3));

    // 0x7a5 目标角速度
    ui->lineEditRDMAngleRateVceX->setText(QString::number(trueObjInfo->mTgtAngRateVceX, 'f', 3));
    ui->lineEditRDMAngleRateVceY->setText(QString::number(trueObjInfo->mTgtAngRateVceY, 'f', 3));
    ui->lineEditRDMAngleRateVceZ->setText(QString::number(trueObjInfo->mTgtAngRateVceZ, 'f', 3));

    // 0x7a6 从车姿态角
    ui->lineEditTgtAngleHeading->setText(QString::number(trueObjInfo->mTgtAngleHeading, 'f', 2));
    ui->lineEditTgtAnglePitch->setText(QString::number(trueObjInfo->mTgtAnglePitch, 'f', 2));
    ui->lineEditTgtAngleRoll->setText(QString::number(trueObjInfo->mTgtAngleRoll, 'f', 2));

    // 0x7ac 从车速度
    ui->lineEditTgtVelX->setText(QString::number(trueObjInfo->mTgtVelX, 'f', 2));
    ui->lineEditTgtVelY->setText(QString::number(trueObjInfo->mTgtVelY, 'f', 2));
    ui->lineEditTgtVelZ->setText(QString::number(trueObjInfo->mTgtVelZ, 'f', 2));

    // 0x606 0x706
    ui->lineEditHeading->setText(QString::number(trueObjInfo->mHeading, 'f', 2));
}

} // namespace TrueSystemView
} // namespace Views

void Views::TrueSystemView::TrueSystemView::on_pushButtonCompare_clicked()
{
    if( !mMonitor && mAnalysisManager){
        mMonitor = new trueTargetsMonitor( mAnalysisManager,this );
        mMonitor->setWindowFlag( Qt::Dialog );
        //mMonitor->setAttribute(Qt::WA_DeleteOnClose);
    }

    if( mMonitor ){
        mMonitor->show();
    }
}

void Views::TrueSystemView::TrueSystemView::on_pushButtonSet_clicked()
{

}
