!isEmpty(CHENG<PERSON>CH_PRI_INCLUDED):error("chengtech.pri already included")
CHENGTECH_PRI_INCLUDED = 1

MASTER_COPYRIGHT_YEAR = 2024
MASTER_VERSION = 4.8.18
MASTER_COMPAT_VERSION = 4.8.18
#MASTER_VERSION = 5.0.0
#MASTER_COMPAT_VERSION = 5.0.0
MASTER_DISPLAY_VERSION = "$$MASTER_VERSION"
#MASTER_DISPLAY_NAME = ChengTechRadarMaster
MASTER_DISPLAY_NAME = Radar Master
MASTER_ID = chengtechradarmasterr
MASTER_CASED_ID = ChengTechRadarMaster
MASTER_APP_TARGET = "$$MASTER_DISPLAY_NAME $$MASTER_VERSION"
#VERSIONS = $$split(MASTER_VERSION, .)
MASTER_APP_TARGET_PATH = "$$MASTER_DISPLAY_NAME"
# $$replace(MASTER_VERSION, "^(\\d+)\\.\\d+\\.\\d+(-.*)?$", \\1).$$replace(MASTER_VERSION, "^\\d+\\.(\\d+)\\.\\d+(-.*)?$", \\1).0"

MASTER_SOURCE_TREE = $$PWD
isEmpty(MASTER_BUILD_TREE) {
    sub_dir = $$_PRO_FILE_PWD_
    sub_dir ~= s,^$$re_escape($$PWD),,
    MASTER_BUILD_TREE = $$clean_path($$OUT_PWD)
    MASTER_BUILD_TREE ~= s,$$re_escape($$sub_dir)$,,
}

#message($$MASTER_BUILD_TREE)

VERSION = $$MASTER_VERSION

# 使用静态库，修改此处后需要重新构建项目
DEFINES += MASTER_USE_STATIC_LIBARY

MASTER_LIBRARY_PATH = $$MASTER_BUILD_TREE/libs
MASTER_LIBS = $$MASTER_SOURCE_TREE/src/libs
MASTER_3RDPARTY_LIBRARY_PATH = $$MASTER_LIBS/3rdparty
CONFIG(release, debug|release): MASTER_APP_PATH = $$MASTER_SOURCE_TREE/bin/$${MASTER_APP_TARGET_PATH}_Release
else:CONFIG(debug, debug|release): MASTER_APP_PATH = $$MASTER_SOURCE_TREE/bin/$${MASTER_APP_TARGET_PATH}_Debug


INCLUDEPATH += \
    $$MASTER_BUILD_TREE/src \ # for <app/app_version.h> in case of actual build directory
    $$MASTER_SOURCE_TREE/src \ # for <app/app_version.h> in case of binary package with dev package
    $$MASTER_SOURCE_TREE/src/libs \
    $$MASTER_SOURCE_TREE/tools \
    $$MASTER_3RDPARTY_LIBRARY_PATH

LIBS += -L$$MASTER_LIBRARY_PATH

for(depend, MASTER_LIB_DEPENDS) {
    LIBS += -l$$depend
    contains(DEFINES, MASTER_USE_STATIC_LIBARY) {
        DEFINES += $$upper($$depend)_STATIC_LIBRARY
    }
}
