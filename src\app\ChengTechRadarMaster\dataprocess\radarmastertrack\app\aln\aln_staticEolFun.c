/**
 * @file     aln_staticEolFun.c
 * @brief    This file defines the functions for static EOL alignment.
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2023-03-28
 * 
 * 
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2023-03-28  <td>1.0      <td>Wison      <td>Porting from ctmrr120 radar
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

/****************************************************************************
  INCLUDE
 ****************************************************************************/
#ifndef PC_DBG_FW
#include <math.h>
#include <stdbool.h>
#include "cfg.h"
#include "rdp/track/alignment/aln_type.h"
#include "rdp/track/alignment/aln_staticEolFun.h"
#include "rdp/track/alignment/aln_install_cfg.h"
#include "vehicle_cfg.h"
#else
#include <math.h>
#include <stdbool.h>
#include <stdio.h>
#include "app/aln/aln_type.h"
#include "app/aln/aln_staticEolFun.h"
#include "app/aln/aln_install_cfg.h"
#include "app/vehicle/vdy/vdy_types.h"
#include "hal/rsp/rsp_types.h"
#include "other/temp.h"
#endif


/****************************************************************************
  DEFINE
 ****************************************************************************/
#define PRRINT_ALN_DBG_OUTPUT   0

#ifndef NULL
#define NULL    0
#endif

#define EN_CHECK_MULT_OBJ               0       //是否判断一个弧度上的目标个数，这个可能有问题，需要要求对应的距离差值小的情况才有效
#define MAX_SET_OFFSET_ANGLE            3.0f    //最大允许的角度偏差
#define MAX_SEARCH_OFFSET_ANGLE         10.0f   //最大搜索最大角度
#define MAX_STATIC_ALN_TIMER_CNT        100     //最大静态标定的实际
#define MAX_ALN_RANGE_FILTER            5.0
#define MIN_ALN_MAG_FILTER              20      //暂定，还没有使用


static uint8_t ALN_routineSt = ROUTINE_ST_STOP;
static int gInstallAngleCalcState = 0;
static float gCalcInstallAngle = 0;
static float horizontalDeviationAngle = 0.0f;     // 水平安装角度偏差

/****************************************************************************
  GLOBAL VARIABLES
 ****************************************************************************/
CT_EOL_DCR_t dc_result = {0};

CT_EOL_OPT_t CT_EOL_Param=
{
    .r_obj = 1.75,                              //当前默认的雷达到角反的纵向距离， 推荐值1.4-1.8 实际值 1.0-2.5
    .r_dif = 0.75,                              //r_obj 的距离差 ±
    // .r_obj = 1.5,                               //当前默认的雷达到角反的纵向距离， 推荐值1.4-1.8
    // .r_dif = 0.5,                               //r_obj 的距离差 ±
    // .r_obj = 1.65,                              //当前默认的雷达到角反的纵向距离， 推荐值1.4-1.8 实际值 1.0-2.3
    // .r_dif = 0.65,                              //r_obj 的距离差 ±
    .spd_obj = 0,                               //未使用
    .v_dif = 0.1,                               //未使用
    .a_obj = ALN_OBJ_HORIZONTAL_ANGLE_RCR,      //靶标即角反的相对雷达的角度
    .a_dif = MAX_SEARCH_OFFSET_ANGLE,           //在 a_obj 的基础上，最大搜索角度范围 ±
    .fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR, //会根据雷达id改值
};
CT_ALN_EOL_t gTxObj[2];
uint8_t CT_ALN_Timecnt = 0;

void ALN_StaticEOL_toStop(void)
{
    ALN_routineSt = ROUTINE_ST_STOP;
}

/*
 * 统计分析对应目标的角度分布，使用概率分布统计计数出对应的目标，回调返回满足分别点的总和与个数
 */
void ALN_EolAnalyze(CT_ALN_EOL_t *pObj, float *sum, int *objCnt, int txIdx)
{
    int   i = 0;
    float angleSum = 0;
    float angleAvg = 0;
    float stdDif = 0;   //标准差
    float stdSum = 0;
    float tmpDif = 0;
    int   cnt = 0;
    
    if (pObj->objCnt == 0)
    {
        *sum = 0;
        *objCnt = 0;
        return;
    }
    for (i = 0 ; i < pObj->objCnt; i++)
    {
        angleSum += pObj->obj[i].a;
    }

    angleAvg = angleSum / pObj->objCnt;

    for (i = 0 ; i < pObj->objCnt; i++)
    {
        tmpDif = pObj->obj[i].a - angleAvg;
        stdSum += (tmpDif*tmpDif);
    }
    stdDif = stdSum / pObj->objCnt;
    stdDif = sqrtf(stdDif); //计数出方差

#if PRRINT_ALN_DBG_OUTPUT
    EMBARC_PRINTF("pObj->objCnt:%d, angleSum:%0.2f, angleAvg:%0.2f, tmpDif:%0.2f, stdSum:%0.2f, stdDif:%0.2f!\r\n", 
        pObj->objCnt, angleSum, angleAvg, tmpDif, stdSum, stdDif);
#endif

    //统计满足2倍方差的点的个数与和
    angleSum = 0;
    for (i = 0; i < pObj->objCnt; i++)
    {
        tmpDif = fabsf(pObj->obj[i].a - angleAvg);
        //如果方差很小，所有的直接使用,要求角度误差1
        if (stdDif < 0.5)
        {
            cnt++;
            angleSum += pObj->obj[i].a;
            continue;
        }
        //如果方差很大，也直接不用
        if (stdDif > 1.3)
        {
            cnt = 0;
            angleSum = 0;
            break;
        }
        //如果方差比较大，需要处理
        if (tmpDif <= (stdDif*2))
        {
            cnt++;
            angleSum += pObj->obj[i].a;
        }
    }
    //判断分布比例是否满足 2 倍方差比例
    tmpDif = ((float)(cnt)) / pObj->objCnt;

#if PRRINT_ALN_DBG_OUTPUT
    EMBARC_PRINTF("tmpDif=%0.2f, cnt=%d, stdDif=%0.2f\r\n" , tmpDif , cnt, stdDif);
#endif

    // （一般情况   1倍 68% 2倍95% 3倍99%  ，这里取85%）
    if (txIdx == 0)
    {
        CT_EOL_Param.tx1_cnt = cnt;
        CT_EOL_Param.tx1per = tmpDif*100;
        CT_EOL_Param.tx1sqrt = stdDif;
    }
    else
    {
        CT_EOL_Param.tx2_cnt = cnt;
        CT_EOL_Param.tx2per = tmpDif*100;
        CT_EOL_Param.tx2sqrt = stdDif;
    }
    if (tmpDif < 0.85)  
    {
        //分配
        cnt = 0;    
    }
    *sum = angleSum;
    *objCnt = cnt;
}

/**
 * @brief 进行下线eol的操作，根据指定的目标搜索对应的参数后校准安装角度，更新算法为单独对单个天线进行计算，取角度差距最小的天线为对应值计算补偿值
 * 
 * @param ptrRawList 原始目标列表
 * @param rawNum 原始目标列表中的目标个数
 * @param ptrVdyInfo 本车数据
 * @param angleCalc 从外面传入的数据地址，用于返回校准后的角度
 * @return int 返回状态，参考
 */
int ALN_StaticEolAngleCalcMainFun(const RSP_DetObjectList_t *ptrDetObjectList)
{
    int tx = 0;
    const RSP_DetObjectInfo_t *ptrRawList = NULL;

    if (ALN_routineSt == ROUTINE_ST_STOP)
    {
        return CALC_UNCAL_END;
    }
    ALN_routineSt = ROUTINE_ST_RUN;

    float r_dif_buf = CT_EOL_Param.r_dif;//0.5; 
    float r_obj     = CT_EOL_Param.r_obj;//1.5;
//    float v_dif_buf = CT_EOL_Param.v_dif;//0.1;  //直接要求等于0
    float a_obj     = CT_EOL_Param.a_obj;//0;    //校准对应的角度
    float a_dif_buf = CT_EOL_Param.a_dif;//10.0;
    float min_cst = 10*10;//3*3+10*10;    //距离1，角度3
    float min_tx2_cst = 10*10;//3*3+10*10;    //距离1，角度3
    int   min_idx = -1;
    int   min_tx2_idx = -1;
    int   max_collect_cnt = EOL_COLLECT_CNT_MAX;
    float tx0_min_a = 1000;
    float tx0_max_a = -1000;
    float tx1_min_a = 1000;
    float tx1_max_a = -1000;
#if EN_CHECK_MULT_OBJ
    float maxAangle = -1000, minAngle = 1000;
#endif
    int   i = 0;
    int   ret = CT_EOL_Param.eol_run_ok;

    ptrRawList = ptrDetObjectList->rspDetObject;
    if (ptrRawList == NULL)
    {
        CT_EOL_Param.en_eol = 0;
        ret = CT_EOL_Param.eol_run_ok;
        return ret;
    }

#if PRRINT_ALN_DBG_OUTPUT
    EMBARC_PRINTF("r_dif_buf:%0.2f, r_obj:%0.2f, a_obj:%0.2f, a_dif_buf:%0.2f,\r\n", 
        r_dif_buf, r_obj, /*v_dif_buf,*/ a_obj, a_dif_buf);
    EMBARC_PRINTF("run staticEol...CT_ALN_Timecnt=%d, ALN_routineSt=%d\r\n", CT_ALN_Timecnt, ALN_routineSt);
#endif

    //开始执行的时候将计数清空
    if (CT_ALN_Timecnt == 0)
    {
        gTxObj[0].objCnt = 0;
        gTxObj[1].objCnt = 0;
        CT_EOL_Param.eol_run_ok = CALC_UNCAL_END;
        gInstallAngleCalcState = CALC_UNCAL_END;
    }

    //校准超时
    if (CT_ALN_Timecnt > MAX_STATIC_ALN_TIMER_CNT)
    {
        CT_EOL_Param.eol_run_ok = CALC_FAILED_TIMEOUT;
        gInstallAngleCalcState = CALC_FAILED_TIMEOUT;
        CT_EOL_Param.en_eol = 0;
        CT_ALN_Timecnt = 0;
        ret = CT_EOL_Param.eol_run_ok;
        ALN_routineSt = ROUTINE_ST_STOP;

        return ret;
    }

    //条件判断
    for (i = 0; i < ptrDetObjectList->rspDetObjectNum; i++)
    {
        if (ptrRawList[i].rspDetRange > 1023)
        {
            tx = 1;
            continue;
        }
        //距离限制
        if (ptrRawList[i].rspDetRange > MAX_ALN_RANGE_FILTER)
        {
            continue;
        }
        //mag限制 （还没有定量值是多少）
#if 0
        if(ptrRawList[i].rspDetFFTMAG < MIN_ALN_MAG_FILTER)
        {
            continue;
        }
#endif
        float r_dif = fabsf(ptrRawList[i].rspDetRange - r_obj);
        float a_dif = fabsf(ptrRawList[i].rspDetAzimuthAngle - a_obj);
        float tmp_cst = 0;
        if ((r_dif < r_dif_buf) && (fabsf(ptrRawList[i].rspDetVelocity) < 0.1)) //速度为0
        {
            //在角度搜索范围内
            if (a_dif < a_dif_buf) // a_dif_buf
            {
                tmp_cst = a_dif*a_dif;//r_dif*r_dif + a_dif*a_dif;//不使用距离进行计算，容易出现选点错误
                if (tx == 0)    //天线判断
                {   
                    if (min_cst > tmp_cst)
                    {
                        min_cst = tmp_cst;
                        min_idx = i;
                    }
                }
                else
                {
                    if (min_tx2_cst > tmp_cst)
                    {
                        min_tx2_cst = tmp_cst;
                        min_tx2_idx = i;
                    }
                }
            }
#if EN_CHECK_MULT_OBJ
            //不在角度范围内，确认整体的角度偏差
            //在一个距离范围内确实是多个目标没，如果是有多个目标，并且差值大，认为失败，这个阶段不判断天线
            if (minAngle > cdi_pkg->cdi[i].mea_z[4])
            {
                minAngle = cdi_pkg->cdi[i].mea_z[4];
            }
            if (maxAangle < cdi_pkg->cdi[i].mea_z[4])
            {
                maxAangle = cdi_pkg->cdi[i].mea_z[4];
            }
#endif
        }
    }

#if PRRINT_ALN_DBG_OUTPUT
    EMBARC_PRINTF("min_idx=%d, min_tx2_idx=%d, obj0_Cnt:%d, obj1_Cnt:%d\r\n", min_idx, min_tx2_idx, gTxObj[0].objCnt, gTxObj[1].objCnt);
#endif

    if (min_idx >= 0)
    {
        gTxObj[0].obj[gTxObj[0].objCnt].r = ptrRawList[min_idx].rspDetRange;
        gTxObj[0].obj[gTxObj[0].objCnt].v = ptrRawList[min_idx].rspDetVelocity;
        gTxObj[0].obj[gTxObj[0].objCnt].a = ptrRawList[min_idx].rspDetAzimuthAngle;
        gTxObj[0].obj[gTxObj[0].objCnt].mag = ptrRawList[min_idx].rspDetFFTMAG;
#if PRRINT_ALN_DBG_OUTPUT
        EMBARC_PRINTF("rawlist, r=%0.2f, v=%0.2f, a=%0.2f, mag=%0.2f!\r\n",
            ptrRawList[min_idx].rspDetRange, ptrRawList[min_idx].rspDetVelocity, ptrRawList[min_idx].rspDetAzimuthAngle, ptrRawList[min_idx].rspDetFFTMAG);
        EMBARC_PRINTF("min_idx entry! r=%0.2f, v=%0.2f, a=%0.2f,\r\n",
            gTxObj[0].obj[gTxObj[0].objCnt].r, gTxObj[0].obj[gTxObj[0].objCnt].v, gTxObj[0].obj[gTxObj[0].objCnt].a);
#endif
        gTxObj[0].objCnt++;
    }

    if (min_tx2_idx >= 0)
    {
        gTxObj[1].obj[gTxObj[1].objCnt].r = ptrRawList[min_tx2_idx].rspDetRange;
        gTxObj[1].obj[gTxObj[1].objCnt].v = ptrRawList[min_tx2_idx].rspDetVelocity;
        gTxObj[1].obj[gTxObj[1].objCnt].a = ptrRawList[min_tx2_idx].rspDetAzimuthAngle;
        gTxObj[1].obj[gTxObj[1].objCnt].mag = ptrRawList[min_tx2_idx].rspDetFFTMAG;
#if PRRINT_ALN_DBG_OUTPUT
        EMBARC_PRINTF("rawlist, r=%0.2f, v=%0.2f, a=%0.2f, mag=%0.2f!\r\n",
            ptrRawList[min_tx2_idx].rspDetRange, ptrRawList[min_tx2_idx].rspDetVelocity, ptrRawList[min_tx2_idx].rspDetAzimuthAngle, ptrRawList[min_tx2_idx].rspDetFFTMAG);
        EMBARC_PRINTF("min_tx2_idx entry. r=%0.2f, v=%0.2f, a=%0.2f,\r\n",
            gTxObj[1].obj[gTxObj[1].objCnt].r, gTxObj[1].obj[gTxObj[1].objCnt].v, gTxObj[1].obj[gTxObj[1].objCnt].a);
#endif
        gTxObj[1].objCnt++;
    }

    //判断极值，用于控制次数，如果极值差距大于1.5度，需要的采集数据会大些，否则30帧数据处理
    //如果数量大于1/2的情况下
    if ( (gTxObj[0].objCnt >= (EOL_COLLECT_CNT_MAX/2)) && (gTxObj[1].objCnt >= (EOL_COLLECT_CNT_MAX/2)) )
    {
        for (i = 0; i < gTxObj[0].objCnt; i++)
        {
            if (tx0_min_a > gTxObj[0].obj[i].a)
            {
                tx0_min_a = gTxObj[0].obj[i].a;
            }
            if (tx0_max_a < gTxObj[0].obj[i].a)
            {
                tx0_max_a = gTxObj[0].obj[i].a;
            }
        }
        for (i = 0; i < gTxObj[1].objCnt; i++)
        {
            if (tx1_min_a > gTxObj[1].obj[i].a)
            {
                tx1_min_a = gTxObj[1].obj[i].a;
            }
            if (tx1_max_a < gTxObj[1].obj[i].a)
            {
                tx1_max_a = gTxObj[1].obj[i].a;
            }
        }
    }
    dc_result.chanle_0_obj_cnt = gTxObj[0].objCnt;
    dc_result.chanle_1_obj_cnt = gTxObj[1].objCnt;

#if 0
    if(
        ((tx0_max_a - tx0_min_a) < 1.5) &&
        ((tx1_max_a - tx1_min_a) < 1.5) &&
        (gTxObj[0].objCnt > (EOL_COLLECT_CNT_MAX/2)) &&
        (gTxObj[1].objCnt > (EOL_COLLECT_CNT_MAX/2))
    )
    {
        max_collect_cnt = EOL_COLLECT_CNT_MAX/2;
    }
#endif

    //完成采集数据后进行数据统计
    CT_ALN_Timecnt++;
    dc_result.run_counter = CT_ALN_Timecnt;
    CT_EOL_Param.eol_time = CT_ALN_Timecnt;

    do {
        if(CT_ALN_Timecnt >= max_collect_cnt)
        {
            ALN_routineSt = ROUTINE_ST_STOP;
            CT_ALN_Timecnt = 0;
            CT_EOL_Param.en_eol = 0;

            float sumAngle1 = 0, sumAngle2 = 0;
            int   objCnt[2] = {0, 0};
            float eolAngleTxAvg1 = 0.0f, eolAngleTxAvg2=0.0f;
            float eolAngleAvg = 0;

            //如果存在一个弧度内多个角度目标，并且角度大，本次失败
#if EN_CHECK_MULT_OBJ
            if (fabsf(maxAangle - minAngle) > 3)
            {
                CT_ALN_Timecnt = 0;
                CT_EOL_Param.eol_run_ok = CALC_FAILED_2Tx;
                ALN_routineSt = ROUTINE_ST_STOP;
                ret = CT_EOL_Param.eol_run_ok;

                return ret;
            }
#endif

            CT_EOL_Param.tx1_s_cnt = gTxObj[0].objCnt;
            CT_EOL_Param.tx2_s_cnt = gTxObj[1].objCnt;
#if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("gTxObj[0].objCnt=%d , gTxObj[1].objCnt=%d \r\n",gTxObj[0].objCnt,gTxObj[1].objCnt);
            EMBARC_PRINTF("gTxObj[0].r=%0.2f, v=%0.2f, a=%0.2f, gTxObj[1].r=%0.2f, v=%0.2f, a=%0.2f\r\n",
                gTxObj[0].obj[gTxObj[0].objCnt].r, gTxObj[0].obj[gTxObj[0].objCnt].v, gTxObj[0].obj[gTxObj[0].objCnt].a,
                gTxObj[1].obj[gTxObj[1].objCnt].r, gTxObj[1].obj[gTxObj[1].objCnt].v, gTxObj[1].obj[gTxObj[1].objCnt].a);
#endif

            ALN_EolAnalyze(&(gTxObj[0]), &sumAngle1, &(objCnt[0]), 0);
            ALN_EolAnalyze(&(gTxObj[1]), &sumAngle2, &(objCnt[1]), 1);

            if ((objCnt[0] == 0) && (objCnt[1] == 0))
            {
                CT_EOL_Param.eol_run_ok = CALC_FAILED_MUL_ERR;
                gInstallAngleCalcState = CALC_FAILED_MUL_ERR;

#if PRRINT_ALN_DBG_OUTPUT
                EMBARC_PRINTF("no obj for eol analyze error\r\n");
#endif

                break;
            }  
            if (objCnt[0] > 0)
            {
                eolAngleTxAvg1 = sumAngle1 / objCnt[0];
            }
            if (objCnt[1] > 0)
            {
                eolAngleTxAvg2 = sumAngle2 / objCnt[1];
            }
            
            CT_EOL_Param.tx1avg = eolAngleTxAvg1;
            CT_EOL_Param.tx2avg = eolAngleTxAvg2;

#if 0   //不对两个角度的进行对比
            //判断是否正确，加入两个目标的角度差
            if(fabsf(eolAngleTxAvg1 - eolAngleTxAvg2) > 3)
            {
                CT_EOL_Param.eol_run_ok = CALC_FAILED_2Tx_MEAN;
                EMBARC_PRINTF("two tx angle diff error\n");
                break;
            }
#else
            //对两边天线直接取与标定目标角度差最小的点
            float tx1DifA = fabsf(a_obj - CT_EOL_Param.tx1avg);
            float tx2DifA = fabsf(a_obj - CT_EOL_Param.tx2avg);
            if ((objCnt[0] < 5) && (objCnt[1] > 5))
            {
                eolAngleAvg = CT_EOL_Param.tx2avg;
            }
            else if ((objCnt[0] > EOL_COLLECT_CNT_MIN) && (objCnt[1] < EOL_COLLECT_CNT_MIN))
            {
                eolAngleAvg = CT_EOL_Param.tx1avg;
            }
            else if ((objCnt[0] > EOL_COLLECT_CNT_MIN) && (objCnt[1] > EOL_COLLECT_CNT_MIN))
            {
                if (tx1DifA < tx2DifA)
                {
                    eolAngleAvg = CT_EOL_Param.tx1avg;
                }
                else
                {
                    eolAngleAvg = CT_EOL_Param.tx2avg;
                }
            }
            else
            {
                CT_EOL_Param.eol_run_ok = CALC_FAILED_MUL_ERR;
                gInstallAngleCalcState = CALC_FAILED_MUL_ERR;
#if PRRINT_ALN_DBG_OUTPUT
                EMBARC_PRINTF("no obj collect error\r\n");
#endif

                break;
            }
            CT_EOL_Param.eol_angle = eolAngleAvg;
#endif

#if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("sumAngle1=%0.2f,objCnt1=%d,eolAngleTxAvg1=%0.2f,sumAngle2=%0.2f,objCnt2=%d,eolAngleTxAvg2=%0.2f,eolAngleAvg=%0.2f,max_collect_cnt=%d\r\n",
                            sumAngle1,objCnt[0],eolAngleTxAvg1,sumAngle2,objCnt[1],eolAngleTxAvg2,eolAngleAvg,max_collect_cnt);
#endif

            CT_EOL_Param.eol_finish_cnt++;
            CT_EOL_Param.eol_run_ok = CALC_PRO_END;
            gInstallAngleCalcState = CALC_PRO_END;
            //完成计算后上报数据
        }
    }while(0);

    if (gTxObj[0].objCnt >= max_collect_cnt)
    {
        gTxObj[0].objCnt = 0;
    }    
    if (gTxObj[1].objCnt >= max_collect_cnt)
    {
        gTxObj[1].objCnt = 0;
    }
    //标定完成，并且正确
    if (CT_EOL_Param.eol_run_ok == CALC_PRO_END)
    {
        CT_ALN_Timecnt = 0;
        
        //超时之后停止继续
        gInstallAngleCalcState = CALC_FAILED_TIMEOUT;
        // 偏差 = 认为标定目标物角度 - 实测角度
        horizontalDeviationAngle = CT_EOL_Param.a_obj - CT_EOL_Param.eol_angle;
        //  最终安装角度 = 希望安装角度 + （认为标定目标物角度 - 实测角度）
        gCalcInstallAngle = CT_EOL_Param.fixAngleOffset + horizontalDeviationAngle;
        
        //判断范围
        if ((gCalcInstallAngle) < (CT_EOL_Param.fixAngleOffset - MAX_SET_OFFSET_ANGLE))
        {
            CT_EOL_Param.eol_run_ok = CALC_FAILED_ANGLE_MIN_OUT;
            gInstallAngleCalcState = CALC_FAILED_ANGLE_MIN_OUT;
        }
        else if ((gCalcInstallAngle) > (CT_EOL_Param.fixAngleOffset + MAX_SET_OFFSET_ANGLE))
        {
            CT_EOL_Param.eol_run_ok = CALC_FAILED_ANGLE_MAX_OUT;
            gInstallAngleCalcState = CALC_FAILED_ANGLE_MAX_OUT;
        }
        else
        {
            CT_EOL_Param.eol_run_ok = CALC_SUCCESS_END;
            gInstallAngleCalcState = CALC_SUCCESS_END;
        }

        ALN_routineSt = ROUTINE_ST_STOP;
    }

    ret = CT_EOL_Param.eol_run_ok;
    return ret;
}

void ALN_initParaForStart(void)
{
#ifndef PC_DBG_FW
    switch (CFG_getRadarId())
    {
#if ( defined(VEHICLE_TYPE_BYD_HA5) || defined(VEHICLE_TYPE_BYD_EM2) || defined(VEHICLE_TYPE_BYD_UR) )
    case 4:
    {
        CT_EOL_Param.a_obj = get_install_message()->aln_obj_angle_rcr;  //ALN_OBJ_HORIZONTAL_ANGLE_RCR;
        CT_EOL_Param.fixAngleOffset = get_install_message()->aln_execpt_angle_rcr;  //ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 5:
    {
        CT_EOL_Param.a_obj = get_install_message()->aln_obj_angle_rcr; //ALN_OBJ_HORIZONTAL_ANGLE_RCR;
        CT_EOL_Param.fixAngleOffset = get_install_message()->aln_execpt_angle_rcr; //ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 6:
    {
        CT_EOL_Param.a_obj = get_install_message()->aln_obj_angle_fcr; //ALN_OBJ_HORIZONTAL_ANGLE_FCR;
        CT_EOL_Param.fixAngleOffset = get_install_message()->aln_execpt_angle_fcr; //ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    case 7:
    {
        CT_EOL_Param.a_obj = get_install_message()->aln_obj_angle_fcr; //ALN_OBJ_HORIZONTAL_ANGLE_FCR;
        CT_EOL_Param.fixAngleOffset = get_install_message()->aln_execpt_angle_fcr; //ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    default:
    {
        CT_EOL_Param.a_obj = get_install_message()->aln_obj_angle_rcr;  //ALN_OBJ_HORIZONTAL_ANGLE_RCR;
        CT_EOL_Param.fixAngleOffset = get_install_message()->aln_execpt_angle_rcr; //ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
#else
    case 4:
    {
        CT_EOL_Param.a_obj = ALN_OBJ_HORIZONTAL_ANGLE_RCR;
        CT_EOL_Param.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 5:
    {
        CT_EOL_Param.a_obj = ALN_OBJ_HORIZONTAL_ANGLE_RCR;
        CT_EOL_Param.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
    case 6:
    {
        CT_EOL_Param.a_obj = ALN_OBJ_HORIZONTAL_ANGLE_FCR;
        CT_EOL_Param.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    case 7:
    {
        CT_EOL_Param.a_obj = ALN_OBJ_HORIZONTAL_ANGLE_FCR;
        CT_EOL_Param.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_FCR;
        break;
    }
    default:
    {
        CT_EOL_Param.a_obj = ALN_OBJ_HORIZONTAL_ANGLE_RCR;
        CT_EOL_Param.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
        break;
    }
#endif
    }
#endif

        // 仿真时设定雷达ID
    #ifdef PC_DBG_FW   
            CT_EOL_Param.a_obj = ALN_OBJ_HORIZONTAL_ANGLE_RCR;
            CT_EOL_Param.fixAngleOffset = ALN_EXPECT_FIX_ANGLE_RCR;
    #endif

    gInstallAngleCalcState = CALC_START;
    dc_result.routine_status = ROUTINE_STATUS_ACTIVE;
    dc_result.hdev_angle = 0;
    dc_result.routine_result = ROUTINE_RESULT_NO_RESULT;
    dc_result.run_status = CALC_UNCAL_END;
    dc_result.run_counter = 0;
    dc_result.chanle_0_obj_cnt = 0;
    dc_result.chanle_1_obj_cnt = 0;
    dc_result.is_save_to_nvm = false;
//    // 经与莫工沟通，删除此部分逻辑 20221126
//    if (dc_result.run_counter < 63334)
//    {
//        dc_result.run_counter++;
//    }

    ALN_routineSt = ROUTINE_ST_START;

}



CT_EOL_DCR_t *Service31Handle_StaticALNAngleCalc(int subServerNumber)
{
    switch (subServerNumber)
    {
        case 01:
        {
            ALN_initParaForStart();
            #if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("[uds 31 01] gInstallAngleCalcState=%d\r\n",gInstallAngleCalcState);
            #endif
            break;
        }
        case 02:
        {
            if(ALN_routineSt == ROUTINE_ST_START)
            {
                //如果还在运行，用户主动退出，增加记录
                //add here
            }
            gInstallAngleCalcState = CALC_STOP;
            dc_result.routine_status = ROUTINE_STATUS_INACTIVE;
            ALN_routineSt = ROUTINE_ST_STOP;
            #if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("[uds 31 02] gInstallAngleCalcState=%d\r\n",gInstallAngleCalcState);
            #endif
            break;
        }
        case 03:
        {
            switch (gInstallAngleCalcState)
            {
                case CALC_STOP:
                {
                    dc_result.routine_status = ROUTINE_STATUS_INACTIVE;
                    break;
                }
                case CALC_START:
                case CALC_UNCAL_END:
                {
                    dc_result.routine_status = ROUTINE_STATUS_ACTIVE;
                    break;
                }
                // 0x02存FLASH失败，暂时没有存储功能
                // case CALC_STOP:
                //     service31RespX1Low = 0x02;
                //     break;
                case CALC_FAILED_TIMEOUT:
                {
                    dc_result.routine_status = ROUTINE_STATUS_TIMEOUT;
                    dc_result.routine_result = ROUTINE_STATUS_INACTIVE;
                    break;
                }
                case CALC_SUCCESS_END:
                {
                    dc_result.routine_status = ROUTINE_STATUS_FINCORRECTLY;
                    //存储flash，需要考虑上报78操作，不确定是否支持78上报
                    dc_result.is_save_to_nvm = true;
                    dc_result.routine_result = ROUTINE_RESULT_CORRECT;
                    dc_result.new_fix_angle = gCalcInstallAngle;
                    dc_result.hdev_angle = horizontalDeviationAngle;        //偏差值
                    break;
                }
                case CALC_FAILED_SPEED_OUT:
                {
                    dc_result.routine_status = ROUTINE_STATUS_ABORTED;
                    dc_result.routine_result = ROUTINE_STATUS_INACTIVE;
                    break;
                }
                case CALC_FAILED_ANGLE_MAX_OUT:
                {
                    dc_result.routine_status = ROUTINE_STATUS_ABORTED;
                    dc_result.routine_result = ROUTINE_STATUS_ACTIVE;
                    dc_result.hdev_angle = horizontalDeviationAngle;        //偏差值
                    break;
                }
                case CALC_FAILED_ANGLE_MIN_OUT:
                {
                    dc_result.routine_status = ROUTINE_STATUS_ABORTED;
                    dc_result.routine_result = ROUTINE_STATUS_ACTIVE;
                    dc_result.hdev_angle = horizontalDeviationAngle;        //偏差值
                    break;
                }
                case CALC_FAILED_MUL_ERR:
                {
                    dc_result.routine_status = ROUTINE_STATUS_ABORTED;
                    dc_result.routine_result = ROUTINE_STATUS_INACTIVE;
                    break;
                }
                default:
                {
                    dc_result.routine_status = ROUTINE_STATUS_INACTIVE;
                    break;
                }
            }
            dc_result.run_status = gInstallAngleCalcState;
            #if PRRINT_ALN_DBG_OUTPUT
            EMBARC_PRINTF("[uds 31 03] gInstallAngleCalcState=%d , gCalcInstallAngle=%f\r\n",gInstallAngleCalcState,gCalcInstallAngle);
            #endif
            break;
        }
        default:
        {
            break;
        }
    }

    return (&dc_result);
}

void ALN_staticClearSaveFlag(void)
{
    dc_result.is_save_to_nvm = false;
}
CT_EOL_DCR_t *ALN_getStaticEolAlnInfo(void)
{
    return &dc_result;
}

// 获取静态标定状态
uint8_t ALN_getStaticState()
{
    uint8_t gAlgOnStaticAlnMode = 0U;        // 静态标定状态,0:非静态标定,1:进入静态标定

    if(ALN_routineSt == ROUTINE_ST_STOP)
    {
        gAlgOnStaticAlnMode = 0;
    }
    else
    {
        gAlgOnStaticAlnMode = 1;
    }

    return gAlgOnStaticAlnMode;
}
