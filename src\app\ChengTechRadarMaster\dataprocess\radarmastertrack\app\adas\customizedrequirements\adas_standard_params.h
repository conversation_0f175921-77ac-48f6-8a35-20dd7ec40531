﻿#ifndef _ADAS_STANDARD_PARAMS_H_
#define _ADAS_STANDARD_PARAMS_H_

#ifdef ALPSPRO_ADAS
#include "vehicle_cfg.h"
#elif defined(PC_DBG_FW)

#else
#include "app/common/app_common.h"
#include "common/include/vehicle_cfg.h"
#endif


#define ALARM_ROAD_WIDTH                    (3.5f)     //LCA,BSD报警区域道路宽度,文档中的G线和L线

//比亚迪RCTA功能规范参数        
#define RCTA_LENGTH                         (50.0f)     //RCTA区域宽度
#define RCTA_LENGTH_BUF                     (1.0f)      //RCTA区域宽度缓冲区
#define RCTA_X_MIN                          (-VEHICLE_WIDTH_INFO)      //RCTA最大X坐标,byd要求目标完全离开车辆后方再退出
#define RCTA_TTC_MAX_TIME                   (3.0f)      //RCTA功能最大报警时间
#define RCTA_TTC_MAX_TIME_BUF               (1.0f)      //RCTA功能最大报警时间缓冲
#define RCTA_TTC_MIN_TIME                   (0.1f)      //RCTA功能最小报警时间
#define RCTA_TTC_MIN_TIME_BUF               (-0.1f)     //RCTA功能最小报警时间缓冲
#define RCTA_ALM_ROAD_WIDTH_SIZE            (5.0f)      //RCTA区域长度
#define RCTA_ALM_ROAD_WIDTH_SIZE_BUF        (0.0f)      //RCTA区域长度缓冲
#define RCTA_ACTIVE_MIN_SPEED               (0.0f)      //RCTA报警支持的最小速度,km/h 车厂速度只有绝对值，状态机使用
#define RCTA_ACTIVE_MIN_SPEED_BUF           (0.0f)      //RCTA报警支持的最小速度,km/h 车厂速度只有绝对值，状态机使用缓冲
#define RCTA_ACTIVE_MAX_SPEED               (15.0f)     //RCTA报警支持的最大速度,km/h 状态机使用
#define RCTA_ACTIVE_MAX_SPEED_BUF           (0.0f)      //RCTA报警支持的最大速度,km/h 状态机使用缓冲
#define RCTA_OBJ_MAX_SPEED                  (60.0f)     //目标车最大车速 60kph  预留缓冲
#define RCTA_OBJ_MAX_SPEED_BUF              (5.0f)      //目标车最大车速缓冲
#define RCTA_OBJ_MIN_SPEED                  (3.6f)      //目标车最小车速 3.6kph
#define RCTA_OBJ_MIN_SPEED_BUF              (0.0f)      //目标车最小车速 3.6kph缓冲
#define RCTA_DDCI_MIN_BUF                   (2.0f)      //触发 RCTA 的最小后纵向距离（从后桥测量）缓冲
#define RCTA_DDCI_MAX                       (1.5f)      //触发 RCTA 的最大后纵向距离（从后桥测量）RCTADistanceMax
#define RCTA_DDCI_MAX_BUF                   (2.0f)      //触发 RCTA 的最大后纵向距离（从后桥测量）缓冲
#ifndef RCTA_ACTIVE_MIN_IA
#define RCTA_ACTIVE_MIN_IA                  (30.0f)     //目标和目标车辆的轨迹之间的相交角30度<IA<150度
#endif
#define RCTA_ACTIVE_MIN_IA_BUF              (-35.0f)     //目标和目标车辆的轨迹之间的相交角30度<IA<150度缓冲
#ifndef RCTA_ACTIVE_MAX_IA
#define RCTA_ACTIVE_MAX_IA                  (150.0f)    //目标和目标车辆的轨迹之间的相交角30度<IA<150度
#endif
#define RCTA_ACTIVE_MAX_IA_BUF              (35.0f)      //目标和目标车辆的轨迹之间的相交角30度<IA<150度缓冲
#define RCTA_COLLISIO_NHAZARD_ZONE_Y        (1.5f)      //RCTA必报预警区域Y轴距离
#define RCTA_COLLISIO_NHAZARD_ZONE_Y_BUF    (0.0f)      //RCTA必报预警区域Y轴距离缓冲
#define RCTA_COLLISIO_NHAZARD_ZONE_MAX_X    (0.0f)      //RCTA必报预警区域X轴最大值
#define RCTA_COLLISIO_NHAZARD_ZONE_MIN_X    (-(VEHICLE_WIDTH_INFO / 2))      //RCTA必报预警区域X轴最小值，表示车辆中轴线
#define RCTA_COLLISIO_NHAZARD_ZONE_X_BUF    (0.0f)      //RCTA必报预警区域X轴距离缓冲
#define RCTA_LENGTH_Y_BUF                   (2.0f)      //动态Y范围缓冲区
#define RCTA_SENSITIVEAREAS_MIN_START_X     (0.5f)      //必报区确保目标startX在从外侧切入.避免横穿目标左右侧都报警.
#define RCTA_NCALC_TTM_ZONE_Y               (4.0f)      //RCTA已报警目标不计算TTM的Y轴区域.


//比亚迪RCTB功能规范参数
#define RCTB_ACTIVE_MAX_SPEED                   (10.0f)     //RCTB报警支持的最大速度,km/h 状态机使用（自车）
#define RCTB_ACTIVE_MAX_SPEED_BUF               (0.0f)      //RCTB报警支持的最大速度,km/h 状态机使用（自车）缓冲
#define RCTB_ACTIVE_MIN_SPEED                   (0.0f)      //RCTB报警支持的最小速度,km/h 状态机使用
#define RCTB_ACTIVE_MIN_SPEED_BUF               (0.0f)      //RCTB报警支持的最小速度,km/h 状态机使用
#define RCTB_TARGVEHSPEDMAX                     (60.0f)     //目标车最大车速（实际车速）
#define RCTB_TARGVEHSPEDMAX_BUF                 (0.0f)      //目标车最大车速（实际车速）
#define RCTB_TARGVEHSPEDMIN                     (3.6f)      //目标车最小车速（实际车速）
#define RCTB_TARGVEHSPEDMIN_BUF                 (0.0f)      //目标车最小车速（实际车速）
#define RCTB_TTC_MAX_TIME                       (1.6f)      //RCTB功能最大报警时间
#define RCTB_TTC_MAX_TIME_BUF                   (0.0f)      //RCTB功能最大报警时间缓冲
#define RCTB_TTC_MIN_TIME                       (0.0f)      //RCTB功能最小报警时间
#define RCTB_TTC_MIN_TIME_BUF                   (0.0f)      //RCTB功能最小报警时间缓冲
#define RCTB_RELEVANTOBJTTM                     (3.0f)      //要警告的目标车辆的TTM上限
#define RCTB_RELEVANTOBJTTM_BUF                 (0.0f)      //要警告的目标车辆的TTM上限
#ifndef RCTB_DDCI_MIN
#define RCTB_DDCI_MIN                           (-6.5f)     //触发RCTB的最小后纵向距离（从后桥测量）
#endif
#define RCTB_DDCI_MIN_BUF                       (0.0f)      //触发RCTB的最小后纵向距离（从后桥测量）
#define RCTB_DDCI_MAX                           (1.5f)      //触发RCTB的最大后纵向距离（从后桥测量）注意此处车前方为正，后方为负与雷达坐标系定义相反
#define RCTB_DDCI_MAX_BUF                       (0.0f)      //触发RCTB的最大后纵向距离（从后桥测量）注意此处车前方为正，后方为负与雷达坐标系定义相反
#define RCTB_COLLISIO_NHAZARD_ZONE_X            (0.3f)      //RCTB必刹区域的x轴长度
#define RCTB_COLLISIO_NHAZARD_ZONE_X_BUF        (0.0f)      //RCTB必刹区域的x轴长度
#define RCTB_COLLISIO_NHAZARD_ZONE_Y            (1.3f)      //RCTB必刹区域的y轴长度
#define RCTB_COLLISIO_NHAZARD_ZONE_Y_BUF        (0.0f)      //RCTB必刹区域的y轴长度
#define RCTB_MAX_SPD_LOWER                      (9.0f)      //最大允许车速的下限值（实际车速）
#define RCTB_MAX_SPD_LOWER_BUF                  (0.0f)      //最大允许车速的下限值（实际车速）
#define RCTB_MAX_SPD_UPPER                      (10.0f)     //最大允许车速的上限值（实际车速）
#define RCTB_MAX_SPD_UPPER_BUF                  (0.0f)      //最大允许车速的上限值（实际车速）
#define RCTB_MIN_SPD_LOWER                      (0.0f)      //最小允许车速的下限值（实际车速）
#define RCTB_MIN_SPD_LOWER_BUF                  (0.0f)      //最小允许车速的下限值（实际车速）
#define RCTB_MIN_SPD_UPPER                      (0.72f)     //最小允许车速的上限值（实际车速）
#define RCTB_MIN_SPD_UPPER_BUF                  (0.0f)      //最小允许车速的上限值（实际车速）
#define RCTB_WARNING_WIDTH_BUF                  (0.5f)
#define RCTB_BREAK_AREA_Y_MIN                   (-3.0f)     //(x,y)->(0,-0.3)
#define RCTB_BREAK_AREA_Y_MAX                   (1.8f)      // Y适当给的大一点
#define RCTB_BREAK_AREA_X_MIN                   (-2.0f)     //(x,y)->(-2,0.5) 碰撞区
#define RCTB_BREAK_AREA_X_MAX                   (0.0f)
#define RCTB_ACTIVE_MIN_IA_BUF                  (-10.0f)     //目标和目标车辆的轨迹之间的相交角缓冲 RCTB的缓冲稍微小一些. 
#define RCTB_ACTIVE_MAX_IA_BUF                  (10.0f)      //目标和目标车辆的轨迹之间的相交角缓冲
#ifndef RCTB_BRAKEINTERVALTIME
#define RCTB_BRAKEINTERVALTIME 			        (19)        //二次刹车间隔时间
#endif
//#define RCTB_HOLD_TIME                          (2000)      //RCTB保压时间
#define RCTB_HOLD_TIME_S                        (2.0f)      //RCTB保压时间 S为单位
#define SUB_RCTB_ACCEL_DEPTH_HOLD_EXIT_TIME     (300U)
//#define RCTB_ACCEL_DEPTH_HOLD_EXIT_TIME         (300 / 50)   // 在保压阶段，油门深度大于20%，持续300ms以上，退出保压 FCTB_ACCELEROGRAPH_DEPTH_HOLD_EXIT_TIME
#define RCTB_ACCEL_DEPTH_HOLD_EXIT_TIME_S       (0.3f)      // 在保压阶段，油门深度大于20%，持续300ms以上，退出保压 FCTB_ACCELEROGRAPH_DEPTH_HOLD_EXIT_TIME


//比亚迪FCTA功能规范参数
#ifndef FCTA_ACTIVE_MAX_SPEED
#define FCTA_ACTIVE_MAX_SPEED       (25.0f)
#endif
#define FCTA_ACTIVE_MIN_SPEED       (10.0f)
#define FCTA_Distance_A              (-(MOUNTPOS_TO_REAR_AXLE_CENTER_X_FL + MOUNT6POS_TO_OUTER_EDGE_Y_FL))  //触发FCTA的最小后纵向距离:D1+D9
#define FCTA_DISTANCEMIN             0                              // 触发FCTA的最小后纵向距离（从后桥测量） // 缩小区域到车头. FCTADistanceMin
#define FCTA_DISTANCEMIN_BUF         0                              // 触发FCTA的最小后纵向距离（从后桥测量） // 缩小区域到车头. FCTADistanceMin
#define FCTA_DISTANCEMAX             (4.0f) //(7.75f + FCTA_Distance_A)      // 触发FCTA的最大后纵向距离（从后桥测量）注意此处车前方为正 功能规范期望的是前保0-7.75米. 这里把纵向距离缩短,相当于收紧了功能规范的策略. 减少误刹. FCTADistanceMax
#define FCTA_DISTANCEMAX_BUF         (0.0f)                         // 触发FCTA的最大后纵向距离（从后桥测量）注意此处车前方为正 功能规范期望的是前保0-7.75米. 这里把纵向距离缩短,相当于收紧了功能规范的策略. 减少误刹.
#define FCTA_ACTIVE_MAX_IA          (125.0f)                        // 功能规范到135 起步时刻通过buffer补偿
#define FCTA_ACTIVE_MAX_IA_BUF      (10.0f)
#define FCTA_ACTIVE_MIN_IA          (55.0f)                         //(50.0f) // IA角度提前预留5度的buffer空间.
#define FCTA_ACTIVE_MIN_IA_BUF      (-10.0f)                         //(50.0f) // IA角度提前预留5度的buffer空间.
#define FCTA_ACTIVE_STAND_IA        (90.0f)                         // 标准IA角   90度
#define FCTA_OBJ_MIN_SPEED          (3.6f)                          //报警目标的最小速度阈值,比亚迪客户要求是从0开始，为了抑制误刹，从3.6开始
#define FCTA_OBJ_MIN_SPEED_BUF      (-3.6f)                          //报警目标的最小速度阈值,比亚迪客户要求是从0开始，为了抑制误刹，从3.6开始
#define FCTA_OBJ_MAX_SPEED          (70.0f)                         //报警目标的最大速度阈值
#define FCTA_OBJ_MAX_SPEED_BUF      (5.0f)                          //报警目标的最大速度阈值
#define FCTA_TTC_MAX_TIME           (2.5f)
#define FCTA_TTC_MAX_TIME_BUF       (0.5f)
#define FCTA_TTC_MIN_TIME           (0.001f)
#define FCTA_TTC_MIN_TIME_BUF       (-0.0005f)
#define FCTA_LENGTH                 (25.0f)                         //FCTA区域宽度
#define FCTA_LENGTH_BUF             (1.0f)                          //FCTA区域宽度
#define FCTA_CRISISLEVEL            (0.5f)                          //危机程度阈值
#define FCTA_AREA_Y_MIN             (-3.0f)                         //(x,y)->(0,-0.3)
#define FCTA_AREA_Y_MIN_BUF         (0.0f)                         //(x,y)->(0,-0.3)
#define FCTA_AREA_Y_MAX             (1.0f)                          // 
#define FCTA_AREA_Y_MAX_BUF         (1.5f)                          // 
#define FCTA_AREA_X_MIN             (-2.0f)                         //
#define FCTA_AREA_X_MIN_BUF         (0.0f)                         //
#define FCTA_AREA_X_MAX             (0.0f)
#define FCTA_AREA_X_MAX_BUF         (0.0f)

//比亚迪FCTB功能规范参数
#ifndef FCTB_ACTIVE_MAX_SPEED
#define FCTB_ACTIVE_MAX_SPEED       (25.0f)                             //FCTB报警支持的最大速度,km/h 状态机使用（自车）
#endif
#ifndef FCTB_ACTIVE_MIN_SPEED
#define FCTB_ACTIVE_MIN_SPEED       (0.0f)                              //FCTB报警支持的最小速度,km/h 状态机使用
#endif
#define FCTB_TARGVEHSPEDMAX         (70.0f)                             //目标车最大车速（实际车速）  FCTB_TargVehSpedMax
#define FCTB_TARGVEHSPEDMIN         (0.1f)                              //目标车最小车速（实际车速）
#define FCTB_WARNINGTTC             (1.5f)                              //本车和目标对象之间触发紧急制动的TTC   FCTBWarningTtc
#define FCTB_WARNINGTTC_BUF         (0.3f) //针对车对车场景额外补偿TTC 
#define FCTB_DISTANCEMIN            FCTA_DISTANCEMIN                     //触发FCTB的最小后纵向距离（从后桥测量）
#define FCTB_DISTANCEMAX            (7.75f + FCTA_Distance_A)          //触发FCTB的最大后纵向距离（从后桥测量）注意此处车前方为正 
#define FCTB_DISTANCEBRK            (1.5f)                             //刹停后与目标的安全距离  FCTBDistanceBrk
#define FCTB_BRAKEINTERVALTIME 		(20.0f)                             //二次刹车间隔时间
#define FCTB_CRISISLEVEL            (0.9f)                              //危机程度阈值 
#define FCTB_ACTIVE_MAX_IA          (125.0f)                            // 略放宽FCTB航向角
#define FCTB_ACTIVE_MAX_IA_BUF      (5.0f)                             // 
#define FCTB_ACTIVE_MIN_IA          (55.0f)                             //(55.0f)     //预留一定的buffer空间  实际应该时60度.
#define FCTB_ACTIVE_MIN_IA_BUF      (-5.0f)                             //(55.0f)     //
#define FCTB_HOLD_TIME              (2000)                              //MS#define FCTB_Warning_Width_buf      (0.5f)
#define FCTB_HOLD_TIME_S            (2.0f)                              // S为单位的hold time
#define FCTB_WARNING_WIDTH_BUF      (1.0f)                              // FCTB_Warning_Width_buf
#define FCTB_BREAK_AREA_Y_MIN       (-3.0f)                             //(x,y)->(0,-0.3)
#define FCTB_BREAK_AREA_Y_MAX       (1.0f)                              // FCTB_Break_Area_Y_Max
#define FCTB_BREAK_AREA_X_MIN       (-2.0f)                             //(x,y)->(-2,0.5) 碰撞区
#define FCTB_BREAK_AREA_X_MAX       (0.0f)
#define FCTB_BREAK_AREA_Y_MAX_BUFFER_L   (0.5f)                           // 针对车对车场景额外补偿  根据车速动态补偿 车速小于10 补偿0.5 
#define FCTB_BREAK_AREA_Y_MAX_BUFFER_M   (1.0f)                           // 针对车对车场景额外补偿 车速大于10 补偿1


//比亚迪LCA功能规范参数
#define LCA_TTC                         (3.5f)                      //最小进入时间，s
#define LCA_TTC_BUF                     (1.0f)                      //最小进入时间，s
#define LCA_NEAR_ALARM_WIDTH_SIZE       (0.5f)                      //LCA目标靠近车身一侧的忽略区域
#define LCA_NEAR_ALARM_WIDTH_SIZE_BUF   (-0.3f)                      //LCA目标靠近车身一侧的忽略区域
#define LCA_FAR_ALARM_WIDTH_SIZE        ALARM_ROAD_WIDTH            //LCA目标远离车身一侧
#define LCA_FAR_ALARM_WIDTH_SIZE_BUF    (0.5f)                      //LCA目标远离车身一侧
#define LCA_TTC_PRE_TIME                (LCA_TTC + 1.0f)            //LCA预触发ttc时间.
#define LCA_ACTIVE_MIN_SPEED            (13.6f)                     //LCA功能启动速度，km/h
#define LCA_ACTIVE_MIN_SPEED_BUF        (0.5f)                      //LCA功能启动速度，km/h
#define LCA_LENGTH                      (70.0f)                     //LCA报警区域纵向长度
#define LCA_LENGTH_BUF                  (2.0f)                      //LCA报警区域纵向长度
#define LCA_WARNING_WIDTH_MIN           (0.5f)                      //LCA报警区域最小横向距离,文档中的F线和K线
#define LCA_WARNING_WIDTH_MIN_BUF       (0.0f)                      //LCA报警区域最小横向距离,文档中的F线和K线
#define LCA_RELATIVE_SPEED_MAX          (-20.0f)                    //目标车相对主车的速度，m/s，"-"号表示目标车在远离主车，绝对值越大，越不危险。
#define LCA_RELATIVE_SPEED_MAX_BUF      (0.0f)                      //目标车相对主车的速度，m/s，"-"号表示目标车在远离主车，绝对值越大，越不危险。
#define LCA_MAX_BSD_SPD_LOWER           (146.0f)                    //LCA功能最大允许车速的下限值，km/h
#define LCA_MAX_BSD_SPD_UPPER           (151.0f)                    //LCA功能最大允许车速的上限值，km/h
#define LCA_MIN_BSD_SPD_LOWER           (11.8f)                     //LCA功能最小允许车速的下限值，km/h
#define LCA_MIN_BSD_SPD_UPPER           (13.6f)                     //LCA功能最小允许车速的上限值，km/h
#define LCA_TARGET_SPEED_MIN            (0.0f)                      //目标车速度，m/s 
#define LCA_TARGET_SPEED_MIN_BUF        (0.0f)                      //目标车速度，m/s，buffer

//比亚迪BSD功能规范参数
#define BSD_LENGTH                      (5.0f)                          //BSD报警区域纵向长度，实际有1.5米偏差
#define BSD_LENGTH_BUF                  (1.0f)                          //BSD报警区域纵向长度，实际有1.5米偏差
#define BSD_LENGTH_FRONT                (-2.0f)                         //BSD报警区域纵向向前长度，到本车后视镜位置
#define BSD_LENGTH_FRONT_BUF            (-0.5f)                         //BSD报警区域纵向向前缓冲
#define BSD_NEAR_ALARM_WIDTH_SIZE       LCA_NEAR_ALARM_WIDTH_SIZE       //BSD目标靠近车身一侧的忽略区域
#define BSD_NEAR_ALARM_WIDTH_SIZE_BUF   LCA_NEAR_ALARM_WIDTH_SIZE_BUF   //BSD目标靠近车身一侧的忽略区域
#define BSD_FAR_ALARM_WIDTH_SIZE        LCA_FAR_ALARM_WIDTH_SIZE        //BSD目标远离车身一侧
#define BSD_FAR_ALARM_WIDTH_SIZE_BUF    LCA_FAR_ALARM_WIDTH_SIZE_BUF    //BSD目标远离车身一侧
#define BSD_ACTIVE_MIN_SPEED            (13.6f)                         //BSD功能启动速度，km/h
#define BSD_ACTIVE_MIN_SPEED_BUF        (0.5f)                          //BSD功能启动速度，km/h
#define BSD_RELATIVE_SPEED_MIN          (-4.0f)                         //车厂要求的不报警的最小相对速度 ，m/s  客户觉得超车时报警晚，临时加大相对速度阈值
#define BSD_RELATIVE_SPEED_MIN_BUF      (72.0f/(-3.6f))                  //已经报警的目标，不对相对速度进行限制，所以给一个大缓冲
#define BSD_RELATIVE_SPEED_MID          (-4.0f)                         //中型及以上车型的相对速度，m/s  暂时未使用
#define BSD_RELATIVE_SPEED_MID_BUF      (0.0f)                          //中型及以上车型的相对速度，m/s  暂时未使用
#define BSD_MAX_BSD_SPD_LOWER           LCA_MAX_BSD_SPD_LOWER           //BSD功能最大允许车速的下限值，km/h
#define BSD_MAX_BSD_SPD_UPPER           LCA_MAX_BSD_SPD_UPPER           //BSD功能最大允许车速的上限值，km/h
#define BSD_MIN_BSD_SPD_LOWER           LCA_MIN_BSD_SPD_LOWER           //BSD功能最小允许车速的下限值，km/h
#define BSD_MIN_BSD_SPD_UPPER           LCA_MIN_BSD_SPD_UPPER           //BSD功能最小允许车速的上限值，km/h
#define BSD_TARGET_SPEED_MIN            LCA_TARGET_SPEED_MIN            //目标车速度，m/s
#define BSD_TARGET_SPEED_MIN_BUF        LCA_TARGET_SPEED_MIN_BUF        //目标车速度，m/s，buffer
#define BSD_TARGET_ABS_SPEED_MIN        (7.2f)                          // 目标车绝对速度 kph

//比亚迪RCW功能规范参数
#define RCW_LENGTH                  (40.0f)
#define RCW_LENGTH_BUF              (3.0f)
#define RCW_RANGE_MIN               (0.5f)
#define RCW_RANGE_MIN_BUF           (0.0f)
#define RCW_TTC_MAX                 (1.4f)      //唐和其他车型要求不一样，唐要求1.0，其他为1.4，暂时都用1.4
#define RCW_START_SPEED             (8.0f)      //启动速度(km/h),根据最近距离0.5m和TTC得到 功能规范是3m/s 之前是6km/h  考虑测试case改为8km/h
#define RCW_START_SPEED_BUF         (0.0f)      //启动速度(km/h),根据最近距离0.5m和TTC得到 功能规范是3m/s 之前是6km/h  考虑测试case改为8km/h

#ifndef RCW_MIN_SPD_LOWER
#define RCW_MIN_SPD_LOWER           (4.5f)      //自车最小速度下限，km/h
#endif
#define RCW_MIN_SPD_UPPER           (0.0f)      //自车最小速度上限，km/h
#define RCW_MAX_SPD_LOWER           (120.0f)    //自车最大速度下限，km/h
#define RCW_MAX_SPD_UPPER           (146.0f)    //自车最大速度上限，km/h

//比亚迪DOW功能规范参数
#define DOW_TARGET_SPEED_MIN                        (2.0f)      //报警启动最小速度，单位m/s  dow_Speed_Min
#define DOW_TARGET_SPEED_MIN_BUF                    (1.0f)      //报警启动最小速度，单位m/s  dow_Speed_Min
#define DOW_TARGET_SPEED_MAX                        (22.2f)     //报警启动最大速度，单位m/s  dow_Speed_Max 最新规范80kph
#define DOW_TARGET_SPEED_MAX_BUF                    DOW_TARGET_SPEED_MIN_BUF   //报警启动最大速度，单位m/s  dow_Speed_Max
#define DOW_TTC_MAX                                 (2.65f)      //DOW最大TTC
#define DOW_TTC_MIN                                 (0.0f)      //DOW最小TTC
#define DOW_TTC_BUF                                 (1.0f)      //TTC缓冲  大车低速近处速度容易降低. 减少早退
#define DOW_WARNING_WIDTH_MAX                       (2.25f)     //BYD协议-0.25~2.0  dow_Warning_Width_Max      //多写15cm  预留一定的buffer.
#define DOW_WARNING_WIDTH_MAX_BUF                   (0.5f)      //BYD协议-0.25~2.0  dow_Warning_Width_Max
#define DOW_WARNING_WIDTH_MIN_BUF                   (-0.3f)     //向内侧buffer
#define DOW_WARNING_LENGTH_MAX                      (55.0f)     // dow_Warning_Length_Max
#define DOW_WARNING_LENGTH_MAX_BUF                  (0.5f)      // dow_Warning_Length_Max
#define DOW_WARNING_LENGTH_MIN                      (-2.0f)     // dow_Warning_Length_Min
#define DOW_WARNING_LENGTH_MIN_BUF                  (-0.5f)
#ifndef DOW_ACTIVE_MIN_SPEED
#define DOW_ACTIVE_MIN_SPEED                        (-0.1)
#endif
#ifndef DOW_ACTIVE_MAX_SPEED
#define DOW_ACTIVE_MAX_SPEED                        (0.1)
#endif
#define DOW_ACTIVE_MIN_IA                           (0.0f) 
#define DOW_ACTIVE_MIN_IA_BUF                       (10.0f) 
#define DOW_ACTIVE_MAX_IA                           (33.0f)     //功能规范 30  多写一定的buffer
#define DOW_ACTIVE_MAX_IA_BUF                       (10.0f) 
#define DOW_CLOSING_CAR_ACTIVE_MAX_IA_BUF           (60.0f)     //当目标距离本车后方较近时，夹角的缓冲给一个较大值

// ELKA参数
#define ELKA_OT_TTC                         (3.0f) // ELKA_OT功能规范的TTC时间.
#define ELKA_OT_TTC_BUF                     (0.5f)
#define ELKA_OT_X_DISTANCE                  (1.2f)
#define ELKA_OT_X_DISTANCE_BUF              (0.3f)
#define ELKA_OT_OVERDLY                     (3U)      //  elka延迟退出帧数 防止中断


#endif