﻿#ifndef ANALYSISDATA_H
#define ANALYSISDATA_H

#include "analysis_global.h"
#include "analysisdata_global.h"

#include <QObject>
#include <QDebug>

#define MAX_TARGET_COUNT 		256
#define MAX_TRACKTARGET_COUNT   128
#define MAX_RADAR_COUNT 		16
#define MAX_CAMERA_COUNT 		10


#define TRUE_VALUE_RADAR_ID_IFS300 4
#define TRUE_VALUE_RADAR_ID_Pandar64 5

enum AnalysisType
{
    /*=================目标信息=================*/
    TARGET_TYPE_BEGIN,
    ID = TARGET_TYPE_BEGIN,           ///< 目标ID
    X,                                ///< 横坐标位置
    Y,                                ///< 纵坐标位置
    Z,                                ///< 垂直坐标位置
    V,                                ///< 速度（相对）
    Vx,                               ///< 横向速度（相对）
    Vy,                               ///< 纵向速度（相对）
    Vz,                               ///< 垂直速度（相对）
    Vsog,                             ///< 速度（对地）
    Vxsog,                            ///< 横向速度（对地）
    Vysog,                            ///< 纵向速度（对地）
    Vzsog,                            ///< 垂直速度（对地）
    Ax,                               ///< 横向加速度
    Ay,                               ///< 纵向加速度
    Az,                               ///< 垂直加速度
    XRms,                             ///< 横向(均方根)距离relative velocity
    YRms,                             ///< 纵向(均方根)距离
    ZRms,                             ///< 垂直(均方根)距离
    VRms,                             ///< (均方根)速度
    VxRms,                            ///< 横向(均方根)速度
    VyRms,                            ///< 纵向(均方根)速度
    VzRms,                            ///< 垂直(均方根)速度
    AxRms,                            ///< 横向(均方根)加速度
    AyRms,                            ///< 纵向(均方根)加速度
    AzRms,                            ///< 垂直(均方根)加速度
    XStd,                             ///< 横向位置标准差
    YStd,                             ///< 纵向位置标准差
    ZStd,                             ///< 垂直位置标准差
    VxStd,                            ///< 横向速度标准差
    VyStd,                            ///< 纵向速度标准差
    VzStd,                            ///< 垂直速度标准差
    AxStd,                            ///< 横向加速度标准差
    AyStd,                            ///< 纵向加速度标准差
    AzStd,                            ///< 垂直加速度标准差
    TrackFrameX,                    ///< 航迹框（相对）横向距离
    TrackFrameY,                    ///< 航迹框（相对）纵向距离
    TrackFrameZ,                    ///< 航迹框（相对）垂直距离
    TrackFrameWidth,                ///< 航迹框宽度
    TrackFrameLength,               ///< 航迹框长度
    TrackFrameHeight,               ///< 航迹框高度
    TrackFrameAngle,                ///< 航向角
    Range,                          ///< 距离
    Angle,                          ///< 角度
    PitchAngle,                     ///< 俯仰角
    SNR,                            ///< 信噪比
    MAG,                            ///< 信号幅度magnitude
    RCS,                            ///< 雷达截面积radar cross section
    Status,                         ///< 状态
    MeasureState,                   ///< 测量状态
    DynamicProperty,                ///< 动态特性 0x0:moving; 0x1:statinary; 0x2:oncoming 0x3:stationary candidate 0x4:unkown 0x5:crossing stationary 0x6:crossing moving 0x7:stopped
    SubframeID,                     ///< 子帧ID
    AssociatedTrackID,              ///< 关联跟踪点ID
    GroupID,                        ///< 组ID
    AssocRawIdx,                    // 关联的rawIdx
    Class,                          // 分类
    ExistProbability,               ///< 存在概率
    ObstacleProbability,            ///< 障碍概率
    RollingCount,                   ///< 计数器
    Checksum,                       ///< 校验和
    ObjectType,                     ///< 目标类型
    TrackType,                      ///< 目标类型 0=none 1=待起批目标 2=稳定跟踪目标
    TrackLifeCycleCnt,              ///< 跟踪次数
    HitCnt,                         ///< 连续命中次数
    MissCnt,                        ///< 连续丢失次数
    AssociatedNearestID,            ///< 最近关联点
    AssociatedStrongestID,          ///< 最强关联点
    FusionID,                       ///< 融合目标ID
    RefencePointPostion,            ///< 参考点信息
    MotionState,                    ///< 运动状态
    MotionDirection,                ///< 运动方向
    DetectionStatus,                ///< 检查状态
    MeasQuality,                    ///< 可表示置信度、权重等
    MatchFlag,                      ///< AB波匹配结果
    Reserve,                        ///< 预留数据
    ObservationHist,               ///< 目标关联的结果
    Chks,                          ///< CRC
    Cntr,                          ///< 计数器 Counter
    Conf,                          ///< 置信度 Confidence
    MtnPat,                        ///< 运动模式
    ElevnSts,                      ///< 仰角状态 Elevation Status
    TypConfBike,                   ///< 自信车跟踪目标类型分类的置信度
    TypConfPed,                    ///< 行人跟踪目标类型分类的置信度
    TrackSts,                      ///< 跟踪状态
    IsInFreeSpace,                 ///< 指示对象是否在自由空间内
    ElevnConf,                     ///< 俯仰置信度
    MirrProblty,                   ///< 镜像对象概率
    NotRealProblty,                ///< 假目标概率
    TypConfVeh,                    ///< 卡车跟踪目标类型分类的置信度
    StatyCnt,                      ///< 平稳次数
    TiAlv,                         ///< 生命周期
    CoastCnt,                      ///< 对象不与任何东西关联的周期数
    VelocityBin,                   ///< 多普勒索引(速度单元)
    TARGET_TYPE_END,                    ///< 目标类型分割
    /*=================目标头信息=================*/
    TARGET_HEAD_TYPE_BEGIN,                    ///< 目标头分割
    TargetIntervalTime = TARGET_HEAD_TYPE_BEGIN,             ///< 周期间隔计时
    TargetRollingCount,             ///< 循环计数器
    TargetBlockCounter,             ///< 块计数器
    TargetChecksum,                 ///< 校验和
    MeasurementCount,               ///< 测量计数器
    FunctionCalculationTime,        ///< 功能计算时间
    ProfileCount,                   ///< Profile数量
    ResponseTaskCycleTime,          ///< 响应任务周期时间
    ProtocolVersion,                 ///< 协议版本
    NoiseCurrent,                    ///< 噪声电流
    NoiseGlobal,                     ///< 全局噪声
    DataSource,                      ///< 数据源
    NumberAfterCFAR,                 ///< CFAR编号
    NumberAfterFilter,              ///< 过滤编号
    FrameModeNumber,                ///< 帧类型模式编号
    FrequencyHoppingState,          ///< 调频状态标记位
    TunnelsceneState,               ///< 隧道场景标记位
    CurrentFrameModeIndex,          ///< 当前帧模式
    BlockageFlag,                   ///< 是否遮挡
    BlockagePercent,                ///< 遮挡程度
    InterferenceFlag,               ///< 是否干扰
    InterferencePercent,            ///< 干扰程度
    OffsetToSideX,                  ///< x偏移量
    OffsetToSideY,                  ///< y偏移量
    OffsetToSideZ,                  ///< z偏移量
    WaveType,                       ///< AB类型
    HeaderReserve,                  ///< 预留数据
    HeaderSenceFlag,                ///< 当前场景标识
    HeaderMagOffset,                ///< 下位机计算的Mag/Noise与输出值之间的偏移关系
    HeaderCurFrameMode,             ///< 当前工作波形
    HeaderNumAfterCFAR,             ///< CFAR输出目标点数
    TARGET_HEAD_TYPE_END,                    ///< 目标头分割
    /*=================雷达信息=================*/
    END_FRAME_TYPE_BEGIN,
    EndFrameChecksum = END_FRAME_TYPE_BEGIN,              ///< 校验和
    EndFrameIntervalTime,                   ///< 周期间隔计时
    RadarRollingCount,              ///< 循环计数器
    EndOfLineEstablishedAngle,               ///< 产线安装角
    SelfCalibrationEstablishedAngle,               ///< 自标定安装角
    SpeedMode,                          ///< 速度模式
    RoadSideDistance,                   ///< 车道线距离
    TimeTick,                        ///< 时间节拍
    SendTotalCANFrmCnt,             ///< 发送报文总数
    Recv410FrameCount,              ///< 收到的410协议报文总计数
    AutoALNSts,                     ///< 自标定运行状态
    Temperature,                    ///< 雷达温度
    END_FRAME_TYPE_END,                     ///< 雷达类型分割
    /*=================车身信息=================*/
    VEHICLE_TYPE_BEGIN,
    VehicleChecksum = VEHICLE_TYPE_BEGIN,                ///< 车身信息校验和
    VehicleSpeed,                   ///< 汽车速度
    VehicleSpeedInMPS,              ///< 车身微处理器速度
    Radius,                         ///< 转弯半径
    YawRate,                        ///< 横摆角速度
    Gear,                           ///< 档位
    SteeringWheelAngle,             ///< 方向盘角度
    KeyStatus,                      ///< 钥匙状态
    SecurityLock,                   ///< 安全锁
    TrailerStatus,                  ///< 拖车状态
    ESPFailStatus,                  ///< ESP失败状态,汽车esp是车身稳定系统
    AcceleratorPedalActiveLevel,    ///< 加速踏板激活水平
    BrakePedalActiveLevel,          ///< 刹车踏板激活水平
    BrakePedalStatus,               ///< 刹车踏板状态
    LateralAcceleration,            ///< 横向加速度
    LongitudinalAcceleration,       ///< 纵向加速度
    TurnLightLeft,                  ///< 左转向灯
    TurnLightRight,                 ///< 右转向灯
    WheelSpeedFontLeft,             ///< 前左车轮速度
    WheelSpeedFontRight,            ///< 前右车轮速度
    WheelSpeedRearLeft,             ///< 后左车轮速度
    WheelSpeedRearRight,            ///< 后右车轮速度
    WheelSpeedDirectionFontLeft,    ///< 前左车轮速度方向
    WheelSpeedDirectionFontRight,   ///< 前右车轮速度方向
    WheelSpeedDirectionRearLeft,    ///< 后左车轮速度方向
    WheelSpeedDirectionRearRight,   ///< 后右车轮速度方向
    DoorFrontLeft,                  ///< 前左车门
    DoorFrontRight,                 ///< 前右车门
    DoorRearLeft,                   ///< 后左车门
    DoorRearRight,                  ///< 后右车门
    SwitchBSDFunction,              ///< BSD功能开关
    SwitchDOWFunction,              ///< DOW功能开关
    SwitchFCTAFunction,             ///< FCTA功能开关
    SwitchFCTBFunction,             ///< FCTB功能开关
    SwitchRCTAFunction,             ///< RCTA功能开关
    SwitchRCTBFunction,             ///< RCTB功能开关
    SwitchRCWFunction,              ///< RCWF功能开关
    SwitchJAFunction,               ///< JAF功能开关
    SwitchMainFunction,             ///< 主功能开关
    VehicleRollingCount,            ///< 循环计数器
    VehicleYear,                    ///< 车身（年）
    VehicleMonth,                    ///< 车身（月）
    VehicleDay,                      ///< 车身（日）
    VehicleHour,                     ///< 车身（时）
    VehicleMinute,                   ///< 车身（分）
    VehicleSecond,                   ///< 车身（秒）
    VehicleVersion,                  ///< 车身（版本）
    UseVehicleSpeed,                 ///< 是否使用车身速度
    UseYawRate,                     ///< 是否使用角速度
    DoorLockFrontLeft,              ///< 左前车门上锁开关
    DoorLockFrontRight,             ///< 右前车门上锁开关
    DoorLockRearLeft,               ///< 左后车门上锁开关
    DoorLockRearRight,              ///< 右后车门上锁开关
    SteerWheelRotationSpdAK,        ///< 方向盘转角速度
    RadarInstallAngleH,
    RadarInstallAngleP,
    RadarSelfCaliAngleH,
    RadarSelfCaliAngleP,
    RadarWhlBaseY,
    RadarWhlBaseX1,
    RadarWhlBaseX2,
    RadarOffsetHrz,
    RadarOffsetVrt,
    RadarSelfCaliBufCnt,
    VEHICLE_TYPE_END,   ///< 车身信息分割
    /*=========================================*/
    ALARM_TYPE_BEGIN,
    DrivingFunctionAlarmModule = ALARM_TYPE_BEGIN,
    AlarmBSDLevel,                  ///< BSD报警级别
    AlarmBSDObjectID,               ///< BSD报警目标ID
    AlarmBSDState,                  ///< BSD报警状态
    AlarmDOWRLevel,                 ///< DOWR报警级别
    AlarmDOWFLevel,                 ///< DOWR报警级别
    AlarmDOWObjectID,               ///< DOW报警目标ID
    AlarmDOWObjectTTC,              ///< DOW报警TTC
    AlarmDOWState,                  ///< DOW报警状态
    AlarmFCTALevel,                 ///< FCTA报警级别
    AlarmFCTAObjectID,              ///< FCTA报警目标ID
    AlarmFCTAObjectTTC,             ///< FCTA报警TTC
    AlarmFCTAState,                 ///< FCTA报警状态
    AlarmFCTBObjectID,              ///< FCTB报警目标ID
    AlarmFCTBObjectTTC,             ///< FCTB报警TTC
    AlarmFCTBLevel,                 ///< FCTB报警级别
    AlarmFCTBState,                 ///< FCTB报警状态
    AlarmLCALevel,                  ///< LCA报警级别
    AlarmLCAObjectID,               ///< LCA报警目标ID
    AlarmLCAObjectTTC,              ///< LCA报警TTC
    AlarmLCAState,                  ///< LCA报警状态
    AlarmRCTALevel,                 ///< RCTA报警级别
    AlarmRCTAObjectID,              ///< RCTA报警目标ID
    AlarmRCTAObjectTTC,             ///< RCTA报警TTC
    AlarmRCTAState,                 ///< RCTA报警状态
    AlarmRCTBObjectID,              ///< RCTB报警目标ID
    AlarmRCTBObjectTTC,             ///< RCTB报警TTC
    AlarmRCTBLevel,                 ///< RCTB报警级别
    AlarmRCTBState,                 ///< RCTB报警状态
    AlarmRCWLevel,                  ///< RCW报警级别
    AlarmRCWObjectID,               ///< RCW报警目标ID
    AlarmRCWObjectTTC,              ///< RCW报警TTC
    AlarmRCWState,                  ///< RCW报警状态
    AlarmJALevel,                   ///< JA报警级别
    AlarmJAObjectID,                ///< JA报警目标ID
    AlarmJAObjectTTC,               ///< JA报警TTC
    AlarmJAState,                   ///< JA报警状态
    AlarmELKLevel,                  ///< ELK报警级别
    AlarmELKObjectID,               ///< ELK报警目标ID
    ALARM_TYPE_END,   ///< 告警信息分割
    /*=========================================*/
    UnknowAnalysisType,
    /*=================用户信息=================*/
    UserAnalysisType = 20000
};

typedef struct ANALYSIS_EXPORT AnalysisTypeName
{
    QString mName;
    QString mUnit;
}AnalysisTypeName;

static const QList<AnalysisTypeName> AnalysisTypeNames{
    /*=================目标信息=================*/
    {"ID", ""},
    {"X", "m"},
    {"Y", "m"},
    {"Z", "m"},
    {"V", "m/s"},
    {"Vx", "m/s"},
    {"Vy", "m/s"},
    {"Vz", "m/s"},
    {"Vsog", "m/s"},
    {"Vxsog", "m/s"},
    {"Vysog", "m/s"},
    {"Vzsog", "m/s"},
    {"Ax", "m/ss"},
    {"Ay", "m/ss"},
    {"Az", "m/ss"},
    {"XRms", ""},                             ///< 横向(均方根)距离relative velocity
    {"YRms", ""},                             ///< 纵向(均方根)距离
    {"ZRms", ""},                             ///< 垂直(均方根)距离
    {"VRms", ""},                             ///< (均方根)速度
    {"VxRms", ""},                            ///< 横向(均方根)速度
    {"VyRms", ""},                            ///< 纵向(均方根)速度
    {"VzRms", ""},                            ///< 垂直(均方根)速度
    {"AxRms", ""},                            ///< 横向(均方根)加速度
    {"AyRms", ""},                            ///< 纵向(均方根)加速度
    {"AzRms", ""},                            ///< 垂直(均方根)加速度
    {"XStd", ""},                             ///< 横向位置标准差
    {"YStd", ""},                             ///< 纵向位置标准差
    {"ZStd", ""},                             ///< 垂直位置标准差
    {"VxStd", ""},                            ///< 横向速度标准差
    {"VyStd", ""},                            ///< 纵向速度标准差
    {"VzStd", ""},                            ///< 垂直速度标准差
    {"AxStd", ""},                            ///< 横向加速度标准差
    {"AyStd", ""},                            ///< 纵向加速度标准差
    {"AzStd", ""},                            ///< 垂直加速度标准差
    {"Track Frame X", ""},                   ///< 航迹框（相对）横向距离
    {"Track Frame Y", ""},                   ///< 航迹框（相对）纵向距离
    {"Track Frame Z", ""},                   ///< 航迹框（相对）垂直距离
    {"Track Frame Width", ""},               ///< 航迹框宽度
    {"Track Frame Length", ""},              ///< 航迹框长度
    {"Track Frame Height", ""},              ///< 航迹框高度
    {"Track Frame Angle", ""},                ///< 航向角
    {"Range", "m"},                                ///< 距离
    {"Angle", "deg"},                                ///< 角度
    {"P Angle", "deg"},                          ///< 俯仰角
    {"SNR", "dB"},                                  ///< 信噪比
    {"MAG", "dB"},                            ///< 信号幅度magnitude
    {"RCS", "mm"},                            ///< 雷达截面积radar cross section
    {"Status", ""},                        ///< 状态
    {"Measure State", ""},                   ///< 测量状态
    {"Dynamic Property", ""},                ///< 动态特性 0x0:moving; 0x1:statinary; 0x2:oncoming 0x3:stationary candidate 0x4:unkown 0x5:crossing stationary 0x6:crossing moving 0x7:stopped
    {"Subframe ID", ""},                    ///< 子帧ID
    {"Associated Track ID", ""},              ///< 关联跟踪点ID
    {"Group ID", ""},                       ///< 组ID
    {"AssocRawIdx ID", ""},                       // 组ID
    {"Class", ""},                          // 分类
    {"Exist Probability", ""},               ///< 存在概率
    {"Obstacle Probability", ""},            ///< 障碍概率
    {"RollingCount", ""},                  ///< 计数器
    {"Checksum", ""},                 ///< 校验和
    {"ObjectType", ""},                     ///< 目标类型
    {"TrackType", ""},                 ///< 目标类型 0=none 1=待起批目标 2=稳定跟踪目标
    {"TrackLifeCycleCnt", ""},              ///< 跟踪次数
    {"HitCnt", ""},                         ///< 连续命中次数
    {"MissCnt", ""},                       ///< 连续丢失次数
    {"AssociatedNearestID", ""},            ///< 最近关联点
    {"AssociatedStrongestID", ""},         ///< 最强关联点
    {"FusionID", "" },                      ///< 融合目标ID
    {"RefencePointPostion", ""},            //参考点信息
    {"MotionState", ""},                    ///< 运动状态
    {"MotionDirection", ""},                ///< 运动方向
    {"DetectionStatus", ""},                ///< 检查状态
    {"MeasQuality", ""},                    ///< 可表示置信度、权重等
    {"MatchFlag", ""},                      ///< AB波匹配结果
    {"Reserve", ""},                        ///< 预留数据
    {"ObservationHist", ""},               ///< 目标关联的结果
    {"Chks", ""},                          ///< CRC
    {"Cntr", ""},                          ///< 计数器 Counter
    {"Conf", ""},                          ///< 置信度 Confidence
    {"MtnPat", ""},                        ///< 运动模式
    {"ElevnSts", ""},                      ///< 仰角状态 Elevation
    {"TypConfBike", ""},                   ///< 自信车跟踪目标类型分类的置信
    {"TypConfPed", ""},                    ///< 行人跟踪目标类型分类的置信度
    {"TrackSts", ""},                      ///< 跟踪状态
    {"IsInFreeSpace", ""},                 ///< 指示对象是否在自由空间内
    {"ElevnConf", ""},                     ///< 俯仰置信度
    {"MirrProblty", ""},                   ///< 镜像对象概率
    {"NotRealProblty", ""},                ///< 假目标概率
    {"TypConfVeh", ""},                    ///< 卡车跟踪目标类型分类的置信度
    {"StatyCnt", ""},                      ///< 平稳次数
    {"TiAlv", ""},                         ///< 生命周期
    {"CoastCnt", ""},                      ///< 对象不与任何东西关联的周期数
    {"VelocityBin", ""},                   ///< 多普勒索引(速度单元)
    {"TARGET_TYPE_END", ""},                          ///< 目标类型分割
    /*=================目标头信息=================*/
    {"Target Interval Time", "ms"},                 ///< 周期间隔计时
    {"Target Rolling Count", ""},                 ///< 循环计数器
    {"Target Block Counter", ""},             ///< 块计数器
    {"Target Checksum", ""},                 ///< 校验和
    {"Measurement Count", ""},              ///< 测量计数器
    {"Function Calculation Time", ""},        ///< 功能计算时间
    {"Profile Count", ""},                   ///< Profile数量
    {"Response Task Cycle Time", ""},          ///< 响应任务周期时间
    {"ProtocolVersion", ""},                 ///< 协议版本
    {"NoiseCurrent", ""},                    ///< 噪声电流
    {"NoiseGlobal", ""},
    {"DataSource", ""},                      ///< 数据源
    {"NumberAfterCFAR", ""},
    {"NumberAfterFilter", ""},
    {"FrameModeNumber", ""},
    {"FrequencyHoppingState", ""},
    {"TunnelsceneState", ""},               ///< 隧道场景标记位
    {"CurrentFrameModeIndex", ""},
    {"BlockageFlag", ""},                   ///< 是否遮挡
    {"BlockagePercent", "%"},              ///< 遮挡程度
    {"InterferenceFlag", ""},               ///< 是否干扰
    {"InterferencePercent", "%"},            ///< 干扰程度
    {"OffsetToSideX", "m" },          ///< x偏移量
    {"OffsetToSideY", "m" },          ///< y偏移量
    {"OffsetToSideZ", "m" },          ///< z偏移量
    {"WaveType", ""},                       ///< AB类型
    {"HeaderReserve", ""},                  ///< 预留数据
    {"HeaderSenceFlag", ""},                ///< 当前场景标识
    {"HeaderMagOffset", ""},                ///< 下位机计算的Mag/Noise与输出值之间的偏移关系
    {"HeaderCurFrameMode", ""},             ///< 当前工作波形
    {"HeaderNumAfterCFAR", ""},             ///< CFAR输出目标点数
    {"TARGET_HEAD_TYPE_END", ""},                          ///< 目标头类型分割
    /*=================雷达信息=================*/
    {"End Frame Checksum", ""},              ///< 校验和
    {"Interval Time", ""},                        ///< 周期间隔计时
    {"Radar Rolling Count", ""},                  ///< 循环计数器
    {"End Of Line Established Angle", ""},            ///< 产线安装角
    {"Self Calibration Established Angle", ""},               ///< 自标定安装角
    {"SpeedMode", ""},                          ///< 速度模式
    {"Road Side Distance", ""},                   ///< 车道线距离
    {"Time Tick", ""},                        ///< 时间节拍
    {"SendTotalCANFrmCnt", ""},                        ///< 时间节拍
    {"Recv410FrameCount", ""},             ///< 收到的410协议报文总计数
    {"AutoALNSts", ""},             ///< 自标定运行状态
    {"Temperature", ""},                   ///< 雷达温度
    {"RADAR_TYPE_END", ""},                           ///< 雷达类型分割
    /*=================车身信息=================*/
    {"Vehicle Checksum", ""},                      ///< 车身信息校验和
    {"Vehicle Speed", ""},                        ///< 汽车速度
    {"Vehicle Speed InMPS", ""},                    ///< 车身微处理器速度
    {"Radius", ""},                               ///< 转弯半径
    {"Yaw Rate", ""},                             ///< 横摆角速度
    {"Gear", ""},                                 ///< 档位
    {"Steering Wheel Angle", ""},                 ///< 方向盘角度
    {"Key Status", ""},                           ///< 钥匙状态
    {"Security Lock", ""},                        ///< 安全锁
    {"Trailer Status", ""},                       ///< 拖车状态
    {"ESPFail Status", ""},                       ///< ESP失败状态,汽车esp是车身稳定系统
    {"Accelerator Pedal Active Level", ""},       ///< 加速踏板激活水平
    {"Brake Pedal Active Level", ""},             ///< 刹车踏板激活水平
    {"Brake Pedal Status", ""},                   ///< 刹车踏板状态
    {"Lateral Acceleration", ""},                 ///< 横向加速度
    {"Longitudinal Acceleration", ""},            ///< 纵向加速度
    {"Turn Light Left", ""},                      ///< 左转向灯
    {"Turn Light Right", ""},
    {"Wheel Speed Font Left", ""},                ///< 前左车轮速度
    {"Wheel Speed Font Right", ""},
    {"Wheel Speed Rear Left", ""},
    {"Wheel Speed Rear Right", ""},
    {"Wheel Speed Direction Font Left", ""},      ///< 前左车轮速度方向
    {"Wheel Speed Direction Font Right", ""},
    {"Wheel Speed Direction Rear Left", ""},
    {"Wheel Speed Direction Rear Right", ""},
    {"Door Front Left", ""},                      ///< 前左车门
    {"Door Front Right", ""},
    {"Door Rear Left", ""},
    {"Door Rear Right", ""},
    {"Switch BSD Function", ""},                  ///< BSD功能开关
    {"Switch DOW Function", ""},
    {"Switch FCTA Function", ""},
    {"Switch FCTB Function", ""},
    {"Switch RCTA Function", ""},
    {"Switch RCTB Function", ""},
    {"Switch RCW Function", ""},
    {"Switch JA Function", ""},
    {"Switch Main Function", ""},
    {"Vehicle Rolling Count", ""},                ///< 循环计数器
    {"Vehicle Year", ""},
    {"Vehicle Month", ""},
    {"Vehicle Day", ""},
    {"Vehicle Hour", ""},
    {"Vehicle Minute", ""},
    {"Vehicle Secon", ""},
    {"VehicleVersion", ""},
    {"UseVehicleSpeed", ""},
    {"UseYawRate", ""},
    {"DoorLockFrontLeft", ""},
    {"DoorLockFrontRight", ""},
    {"DoorLockRearLeft", ""},
    {"DoorLockRearRight", ""},
    {"SteerWheelRotationSpdAK", ""},
    {"RadarInstallAngleH", ""},
    {"RadarInstallAngleP", ""},
    {"RadarSelfCaliAngleH", ""},
    {"RadarSelfCaliAngleP", ""},
    {"RadarWhlBaseY", ""},
    {"RadarWhlBaseX1", ""},
    {"RadarWhlBaseX2", ""},
    {"RadarOffsetHrz", ""},
    {"RadarOffsetVrt", ""},
    {"RadarSelfCaliBufCnt", ""},
    {"VEHICLE_TYPE_END", ""},   ///< 车身信息分割
    /*=========================================*/
    {"Driving Function Alarm Module", ""},
    {"Alarm BSD Level", ""},
    {"Alarm BSD Object ID", ""},
    {"Alarm BSD State", ""},
    {"Alarm DOWR Level", ""},
    {"Alarm DOWF Level", ""},
    {"Alarm DOW Object ID", ""},
    {"Alarm DOW Object TTC", "ms"},
    {"Alarm DOW State", ""},
    {"Alarm FCTA Level", ""},
    {"Alarm FCTA Object ID", ""},
    {"Alarm FCTA Object TTC", "ms"},
    {"Alarm FCTA State", ""},
    {"Alarm FCTB Object ID", ""},
    {"Alarm FCTB Object TTC", "ms"},
    {"Alarm FCTB Level", ""},
    {"Alarm FCTB State", ""},
    {"Alarm LCA Level", ""},
    {"Alarm LCA Object ID", ""},
    {"Alarm LCA Object TTC", "ms"},
    {"Alarm LCA State", ""},
    {"Alarm RCTA Level", ""},
    {"Alarm RCTA Object ID", ""},
    {"Alarm RCTA Object TTC", "ms"},
    {"Alarm RCTA State", ""},
    {"Alarm RCTB ObjectI D", ""},
    {"Alarm RCTB Object TTC", "ms"},
    {"Alarm RCTB Level", ""},
    {"Alarm RCTB State", ""},
    {"Alarm RCW Level", ""},
    {"Alarm RCW Object ID", ""},
    {"Alarm RCW Object TTC", "ms"},
    {"Alarm RCW State", ""},
    {"Alarm JA Level", ""},
    {"Alarm JA Object ID", ""},
    {"Alarm JA Object TTC", "ms"},
    {"Alarm JA State", ""},
    {"ALARM_TYPE_END", ""},   ///< 告警信息分割
    /*=========================================*/
    {"UnknowAnalysisType", ""},
    /*=================用户信息=================*/
    {"User Analysis Type", ""}
};

ANALYSIS_EXPORT AnalysisType gAnalysisType(const QString &name);
ANALYSIS_EXPORT QString gAnalysisTypeName(AnalysisType type);
ANALYSIS_EXPORT QString gAnalysisTypeUnit(AnalysisType type);
ANALYSIS_EXPORT QStringList gAnalysisTypeNames(int pos, int length = -1);

///< 不可更改顺序
enum AnalysisFrameType
{
    FrameRawTarget,
    FrameTrackTarget,
    FrameTargetCount,
    FrameAlarm = FrameTargetCount,
    FrameEndFrame,
    FrameVehicle,
    Frame200Raw,
    Frame16Track,
    Frame3Track,
    FrameELKTrack,
    FrameTrueIFS300,        ///< 戴世真值
    FrameOthers
};

static const QStringList AnalysisFrameTypeNames{
    "Raw",
    "Track",
    "Alarm",
    "End Frame",
    "Vehicle",
    "16 Track",
    "3 Track",
    "ELK Track",
    "Others"
};

ANALYSIS_EXPORT AnalysisFrameType gAnalysisFrameType(const QString &name);
ANALYSIS_EXPORT QString gAnalysisFrameTypeNames(AnalysisFrameType type);

enum TargetModel
{
    RealTime,
    Playback
};

enum MoveStatus
{
    Motionless,
    InwardMovement,
    OutwardMovement,
    UnknowMoveStatus
};

enum ObjectClass {
    ObjectUnknown,              ///< 未知
    ObjectCar,                  ///< 轿车
    ObjectTruck,                ///< 卡车
    ObjectPedestran,            ///< 行人
    ObjectMotorcycle,           ///< 两轮车
    ObjectBicycle,              ///< 自行车
    ObjectWide,                 ///< 广泛的
    OjbectObstdVert_1,
    OjbectObstdVert_2,
    ObjectAnimal,               ///< 动物
    ObjectObjGen,
    ObjectVehofUnknown,
    ObjectReserved
};

typedef struct ANALYSIS_EXPORT CalculatorConfig
{
    bool mNegateX{false};       ///< 反转X轴
    bool mNegateY{false};       ///< 反转Y轴
    double mOffsetAngle{0.0};   ///< 补偿角，逆时针为正，角度制
}CalculatorConfig;

typedef struct ANALYSIS_EXPORT AnalysisFrameSignal
{
    quint64 mFrameID{0x0};
    QString mSignalName;
}AnalysisFrameSignal;

Q_DECLARE_METATYPE(AnalysisFrameSignal);

#define ANALYSIS_VALUE(analysisType) \
    case analysisType: \
        return m##analysisType;

#define SET_ANALYSIS_VALUE(analysisType, v) \
    case analysisType: \
        m##analysisType = v; \
        break;

enum ProtocolType {
    ProtocolChengTech,
    ProtocolChengTech410,
    ProtocolBYD120,
    ProtocolBAIC,
    ProtocolGWM,
    ProtocolGEELY,
    ProtocolHonzonADF,
    ProtocolBYDHO,          ///< BYD高阶
    ProtocolBYDHO_F,        ///< BYD高阶（前雷达）
    ProtocolUnknown,
};

typedef struct ANALYSIS_EXPORT Target
{
    double value(AnalysisType analysisType) const
    {
        switch (analysisType)
        {
        ANALYSIS_VALUE(ID)
        ANALYSIS_VALUE(X)
        ANALYSIS_VALUE(Y)
        ANALYSIS_VALUE(Z)
        ANALYSIS_VALUE(V)
        ANALYSIS_VALUE(Vx)
        ANALYSIS_VALUE(Vy)
        ANALYSIS_VALUE(Vz)
        ANALYSIS_VALUE(Vsog)
        ANALYSIS_VALUE(Vxsog)
        ANALYSIS_VALUE(Vysog)
        ANALYSIS_VALUE(Vzsog)
        ANALYSIS_VALUE(Ax)
        ANALYSIS_VALUE(Ay)
        ANALYSIS_VALUE(Az)
        ANALYSIS_VALUE(XRms)                             ///< 横向(均方根)距离relative velocity
        ANALYSIS_VALUE(YRms)                             ///< 纵向(均方根)距离
        ANALYSIS_VALUE(ZRms)                             ///< 垂直(均方根)距离
        ANALYSIS_VALUE(VRms)                             ///< (均方根)速度
        ANALYSIS_VALUE(VxRms)                            ///< 横向(均方根)速度
        ANALYSIS_VALUE(VyRms)                            ///< 纵向(均方根)速度
        ANALYSIS_VALUE(VzRms)                            ///< 垂直(均方根)速度
        ANALYSIS_VALUE(AxRms)                            ///< 横向(均方根)加速度
        ANALYSIS_VALUE(AyRms)                            ///< 纵向(均方根)加速度
        ANALYSIS_VALUE(AzRms)                            ///< 垂直(均方根)加速度
        ANALYSIS_VALUE(XStd)                             ///< 横向位置标准差
        ANALYSIS_VALUE(YStd)                             ///< 纵向位置标准差
        ANALYSIS_VALUE(ZStd)                             ///< 垂直位置标准差
        ANALYSIS_VALUE(VxStd)                            ///< 横向速度标准差
        ANALYSIS_VALUE(VyStd)                            ///< 纵向速度标准差
        ANALYSIS_VALUE(VzStd)                            ///< 垂直速度标准差
                ANALYSIS_VALUE(AxStd)                            ///< 横向速度标准差
                ANALYSIS_VALUE(AyStd)                            ///< 纵向速度标准差
                ANALYSIS_VALUE(AzStd)                            ///< 垂直速度标准差
        ANALYSIS_VALUE(TrackFrameX)                   ///< 航迹框（相对）横向距离
        ANALYSIS_VALUE(TrackFrameY)                   ///< 航迹框（相对）纵向距离
        ANALYSIS_VALUE(TrackFrameZ)                   ///< 航迹框（相对）垂直距离
        ANALYSIS_VALUE(TrackFrameWidth)               ///< 航迹框（相对）宽度
        ANALYSIS_VALUE(TrackFrameLength)                ///< 航迹框（相对）长度
        ANALYSIS_VALUE(TrackFrameHeight)                ///< 航迹框（相对）高度
        ANALYSIS_VALUE(TrackFrameAngle)                ///< 航向角
        ANALYSIS_VALUE(Range)                           ///< 距离
        ANALYSIS_VALUE(Angle)                           ///< 角度
        ANALYSIS_VALUE(PitchAngle)                      ///< 俯仰角
        ANALYSIS_VALUE(SNR)                             ///< 信噪比
        ANALYSIS_VALUE(MAG)                             ///< 信号幅度magnitude
        ANALYSIS_VALUE(RCS)                             ///< 雷达截面积radar cross section
        ANALYSIS_VALUE(Status)                        ///< 状态
        ANALYSIS_VALUE(MeasureState)                   ///< 测量状态
        ANALYSIS_VALUE(DynamicProperty)                 ///< 动态特性
        ANALYSIS_VALUE(SubframeID)                    ///< 子帧ID
        ANALYSIS_VALUE(AssociatedTrackID)              ///< 关联跟踪点ID
        ANALYSIS_VALUE(GroupID)                       ///< 组ID
        ANALYSIS_VALUE(AssocRawIdx)                       // 关联的rawIdx
        ANALYSIS_VALUE(Class)                          // 分类
        ANALYSIS_VALUE(ExistProbability)                 ///< 存在概率
        ANALYSIS_VALUE(ObstacleProbability)             ///< 障碍概率
        ANALYSIS_VALUE(RollingCount)                  ///< 计数器
        ANALYSIS_VALUE(Checksum)                 ///< 校验和
        ANALYSIS_VALUE(ObjectType)                     ///< 目标类型
        ANALYSIS_VALUE(TrackType)                   ///< 目标类型
        ANALYSIS_VALUE(TrackLifeCycleCnt)           ///< 跟踪次数
        ANALYSIS_VALUE(HitCnt)                      ///< 连续命中次数
        ANALYSIS_VALUE(MissCnt)                     ///< 连续丢失次数
        ANALYSIS_VALUE(AssociatedNearestID)         ///< 最近关联点
        ANALYSIS_VALUE(AssociatedStrongestID)       ///< 最强关联点
        ANALYSIS_VALUE(FusionID)                    ///< 融合目标ID
        ANALYSIS_VALUE(RefencePointPostion)         ///< 参考点信息
                ANALYSIS_VALUE(MotionState)                    ///< 运动状态
                ANALYSIS_VALUE(MotionDirection)                ///< 运动方向
                ANALYSIS_VALUE(DetectionStatus)                ///< 检查状态
                ANALYSIS_VALUE(MeasQuality)                    ///< 可表示置信度、权重等
                ANALYSIS_VALUE(MatchFlag)                      ///< AB波匹配结果
                ANALYSIS_VALUE(Reserve)                        ///< 预留数据
                ANALYSIS_VALUE(ObservationHist)               ///< 目标关联的结果
                ANALYSIS_VALUE(Chks)                          ///< CRC
                ANALYSIS_VALUE(Cntr)                          ///< 计数器 Counter
                ANALYSIS_VALUE(Conf)                          ///< 置信度 Confidence
                ANALYSIS_VALUE(MtnPat)                        ///< 运动模式
                ANALYSIS_VALUE(ElevnSts)                      ///< 仰角状态 Elevation Status
                ANALYSIS_VALUE(TypConfBike)                   ///< 自信车跟踪目标类型分类的置信度
                ANALYSIS_VALUE(TypConfPed)                    ///< 行人跟踪目标类型分类的置信度
                ANALYSIS_VALUE(TrackSts)                      ///< 跟踪状态
                ANALYSIS_VALUE(IsInFreeSpace)                 ///< 指示对象是否在自由空间内
                ANALYSIS_VALUE(ElevnConf)                     ///< 俯仰置信度
                ANALYSIS_VALUE(MirrProblty)                   ///< 镜像对象概率
                ANALYSIS_VALUE(NotRealProblty)                ///< 假目标概率
                ANALYSIS_VALUE(TypConfVeh)                    ///< 卡车跟踪目标类型分类的置信度
                ANALYSIS_VALUE(StatyCnt)                      ///< 平稳次数
                ANALYSIS_VALUE(TiAlv)                         ///< 生命周期
                ANALYSIS_VALUE(CoastCnt)                      ///< 对象不与任何东西关联的周期数
                ANALYSIS_VALUE(VelocityBin)                   ///< 多普勒索引(速度单元)
        default:
            break;
        }

        return 0.0;
    }

    void setValue(AnalysisType analysisType, double v)
    {
        switch (analysisType)
        {
        SET_ANALYSIS_VALUE(ID, v)
        SET_ANALYSIS_VALUE(X, v)
        SET_ANALYSIS_VALUE(Y, v)
        SET_ANALYSIS_VALUE(Z, v)
        SET_ANALYSIS_VALUE(V, v)
        SET_ANALYSIS_VALUE(Vx, v)
        SET_ANALYSIS_VALUE(Vy, v)
        SET_ANALYSIS_VALUE(Vz, v)
        SET_ANALYSIS_VALUE(Vsog, v)
        SET_ANALYSIS_VALUE(Vxsog, v)
        SET_ANALYSIS_VALUE(Vysog, v)
        SET_ANALYSIS_VALUE(Vzsog, v)
        SET_ANALYSIS_VALUE(Ax, v)
        SET_ANALYSIS_VALUE(Ay, v)
        SET_ANALYSIS_VALUE(Az, v)
        SET_ANALYSIS_VALUE(XRms, v)                             ///< 横向(均方根)距离relative velocity
        SET_ANALYSIS_VALUE(YRms, v)                             ///< 纵向(均方根)距离
        SET_ANALYSIS_VALUE(ZRms, v)                             ///< 垂直(均方根)距离
        SET_ANALYSIS_VALUE(VRms, v)                             ///< (均方根)速度
        SET_ANALYSIS_VALUE(VxRms, v)                            ///< 横向(均方根)速度
        SET_ANALYSIS_VALUE(VyRms, v)                            ///< 纵向(均方根)速度
        SET_ANALYSIS_VALUE(VzRms, v)                            ///< 垂直(均方根)速度
        SET_ANALYSIS_VALUE(AxRms, v)                            ///< 横向(均方根)加速度
        SET_ANALYSIS_VALUE(AyRms, v)                            ///< 纵向(均方根)加速度
        SET_ANALYSIS_VALUE(AzRms, v)                            ///< 垂直(均方根)加速度
        SET_ANALYSIS_VALUE(XStd, v)                             ///< 横向位置标准差
        SET_ANALYSIS_VALUE(YStd, v)                             ///< 纵向位置标准差
        SET_ANALYSIS_VALUE(ZStd, v)                             ///< 垂直位置标准差
        SET_ANALYSIS_VALUE(VxStd, v)                            ///< 横向速度标准差
        SET_ANALYSIS_VALUE(VyStd, v)                            ///< 纵向速度标准差
        SET_ANALYSIS_VALUE(VzStd, v)                            ///< 垂直速度标准差
                SET_ANALYSIS_VALUE(AxStd, v)                            ///< 横向速度标准差
                SET_ANALYSIS_VALUE(AyStd, v)                            ///< 纵向速度标准差
                SET_ANALYSIS_VALUE(AzStd, v)                            ///< 垂直速度标准差
        SET_ANALYSIS_VALUE(TrackFrameX, v)                      ///< 航迹框（相对）横向距离
        SET_ANALYSIS_VALUE(TrackFrameY, v)                      ///< 航迹框（相对）纵向距离
        SET_ANALYSIS_VALUE(TrackFrameZ, v)                      ///< 航迹框（相对）垂直距离
        SET_ANALYSIS_VALUE(TrackFrameWidth, v)                  ///< 航迹框（相对）宽度
        SET_ANALYSIS_VALUE(TrackFrameLength, v)                   ///< 航迹框（相对）长度
        SET_ANALYSIS_VALUE(TrackFrameHeight, v)                   ///< 航迹框（相对）高度
        SET_ANALYSIS_VALUE(TrackFrameAngle, v)                ///< 航向角
        SET_ANALYSIS_VALUE(Range, v)                            ///< 距离
        SET_ANALYSIS_VALUE(Angle, v)                            ///< 角度
        SET_ANALYSIS_VALUE(PitchAngle, v)                       ///< 俯仰角
        SET_ANALYSIS_VALUE(SNR, v)                              ///< 信噪比
        SET_ANALYSIS_VALUE(MAG, v)                              ///< 信号幅度magnitude
        SET_ANALYSIS_VALUE(RCS, v)                              ///< 雷达截面积radar cross section
        SET_ANALYSIS_VALUE(Status, v)                           ///< 状态
        SET_ANALYSIS_VALUE(MeasureState, v)                   ///< 测量状态
        SET_ANALYSIS_VALUE(DynamicProperty, v)                  ///< 动态特性
        SET_ANALYSIS_VALUE(SubframeID, v)                       ///< 子帧ID
        SET_ANALYSIS_VALUE(AssociatedTrackID, v)                ///< 关联跟踪点ID
        SET_ANALYSIS_VALUE(GroupID, v)                          ///< 组ID
        SET_ANALYSIS_VALUE(AssocRawIdx, v)                          // 关联的rawIdx
        SET_ANALYSIS_VALUE(Class, v)                          // 分类
        SET_ANALYSIS_VALUE(ExistProbability, v)                 ///< 存在概率
        SET_ANALYSIS_VALUE(ObstacleProbability, v)              ///< 障碍概率
        SET_ANALYSIS_VALUE(RollingCount, v)                  ///< 计数器
        SET_ANALYSIS_VALUE(Checksum, v)                   ///< 校验和
        SET_ANALYSIS_VALUE(ObjectType, v)                     ///< 目标类型
        SET_ANALYSIS_VALUE(TrackType, v)                 ///< 目标类型
        SET_ANALYSIS_VALUE(TrackLifeCycleCnt, v)        ///< 跟踪次数
        SET_ANALYSIS_VALUE(HitCnt, v)                   ///< 连续命中次数
        SET_ANALYSIS_VALUE(MissCnt, v)                  ///< 连续丢失次数
        SET_ANALYSIS_VALUE(AssociatedNearestID, v)      ///< 最近关联点
        SET_ANALYSIS_VALUE(AssociatedStrongestID, v)    ///< 最强关联点
        SET_ANALYSIS_VALUE(FusionID, v)                 ///< 融合目标ID
        SET_ANALYSIS_VALUE(RefencePointPostion, v)      ///< 参考点信息
                SET_ANALYSIS_VALUE(MotionState, v)                    ///< 运动状态
                SET_ANALYSIS_VALUE(MotionDirection, v)                ///< 运动方向
                SET_ANALYSIS_VALUE(DetectionStatus, v)                ///< 检查状态
                SET_ANALYSIS_VALUE(MeasQuality, v)                    ///< 可表示置信度、权重等
                SET_ANALYSIS_VALUE(MatchFlag, v)                      ///< AB波匹配结果
                SET_ANALYSIS_VALUE(Reserve, v)                        ///< 预留数据
                SET_ANALYSIS_VALUE(ObservationHist, v)               ///< 目标关联的结果
                SET_ANALYSIS_VALUE(Chks, v)                          ///< CRC
                SET_ANALYSIS_VALUE(Cntr, v)                          ///< 计数器 Counter
                SET_ANALYSIS_VALUE(Conf, v)                          ///< 置信度 Confidence
                SET_ANALYSIS_VALUE(MtnPat, v)                        ///< 运动模式
                SET_ANALYSIS_VALUE(ElevnSts, v)                      ///< 仰角状态 Elevation Status
                SET_ANALYSIS_VALUE(TypConfBike, v)                   ///< 自信车跟踪目标类型分类的置信度
                SET_ANALYSIS_VALUE(TypConfPed, v)                    ///< 行人跟踪目标类型分类的置信度
                SET_ANALYSIS_VALUE(TrackSts, v)                      ///< 跟踪状态
                SET_ANALYSIS_VALUE(IsInFreeSpace, v)                 ///< 指示对象是否在自由空间内
                SET_ANALYSIS_VALUE(ElevnConf, v)                     ///< 俯仰置信度
                SET_ANALYSIS_VALUE(MirrProblty, v)                   ///< 镜像对象概率
                SET_ANALYSIS_VALUE(NotRealProblty, v)                ///< 假目标概率
                SET_ANALYSIS_VALUE(TypConfVeh, v)                    ///< 卡车跟踪目标类型分类的置信度
                SET_ANALYSIS_VALUE(StatyCnt, v)                      ///< 平稳次数
                SET_ANALYSIS_VALUE(TiAlv, v)                         ///< 生命周期
                SET_ANALYSIS_VALUE(CoastCnt, v)                      ///< 对象不与任何东西关联的周期数
                SET_ANALYSIS_VALUE(VelocityBin, v)                   ///< 多普勒索引(速度单元)
        default:
            break;
        }
    }

    void print() const;

    quint16 mID{0};                             ///< 目标ID
    double mRange{0.0};
    double mAngle{0.0};
    double mPitchAngle{0.0};                    ///< 俯仰角
    double mX{0.0};                             ///< 横向(相对)距离relative velocity
    double mY{0.0};                             ///< 纵向(相对)距离
    double mZ{0.0};                             ///< 垂直(相对)距离
    double mV{0.0};                             ///< (相对)速度
    double mVx{0.0};                            ///< 横向(相对)速度 // 靠近为负
    double mVy{0.0};                            ///< 纵向(相对)速度
    double mVz{0.0};                            ///< 垂直(相对)速度
    double mVsog{0.0};                          ///< 横向(对地)速度
    double mVxsog{0.0};                         ///< 横向(对地)速度speed over ground
    double mVysog{0.0};                         ///< 纵向(对地)速度
    double mVzsog{0.0};                         ///< 垂直(对地)速度
    double mAx{0.0};                            ///< 横向(相对)加速度
    double mAy{0.0};                            ///< 纵向(相对)加速度
    double mAz{0.0};                            ///< 垂直(相对)加速度
    double mXRms{0.0};                             ///< 横向(均方根)距离relative velocity
    double mYRms{0.0};                             ///< 纵向(均方根)距离
    double mZRms{0.0};                             ///< 垂直(均方根)距离
    double mVRms{0.0};                             ///< (均方根)速度
    double mVxRms{0.0};                            ///< 横向(均方根)速度
    double mVyRms{0.0};                            ///< 纵向(均方根)速度
    double mVzRms{0.0};                            ///< 垂直(均方根)速度
    double mAxRms{0.0};                            ///< 横向(均方根)加速度
    double mAyRms{0.0};                            ///< 纵向(均方根)加速度
    double mAzRms{0.0};                            ///< 垂直(均方根)加速度
    double mXStd{0.0};                             ///< 横向位置标准差
    double mYStd{0.0};                             ///< 纵向位置标准差
    double mZStd{0.0};                             ///< 垂直位置标准差
    double mVxStd{0.0};                            ///< 横向速度标准差
    double mVyStd{0.0};                            ///< 纵向速度标准差
    double mVzStd{0.0};                            ///< 垂直速度标准差
    double mAxStd{0.0};                            ///< 横向加速度标准差
    double mAyStd{0.0};                            ///< 纵向加速度标准差
    double mAzStd{0.0};                            ///< 垂直加速度标准差
    double mTrackFrameX{0.0};                   ///< 航迹框（相对）横向距离
    double mTrackFrameY{0.0};                   ///< 航迹框（相对）纵向距离
    double mTrackFrameZ{0.0};                   ///< 航迹框（相对）纵向距离
    double mTrackFrameWidth{0.0};               ///< 航迹框宽度
    double mTrackFrameLength{0.0};              ///< 航迹框长度
    double mTrackFrameHeight{0.0};              ///< 航迹框高度
    double mTrackFrameAngle{0.0};               ///< 航向角

    double mSNR{0.0};                           ///< 信噪比signal-noise ratio
    double mMAG{0.0};                           ///< 信号幅度magnitude
    double mRCS{0.0};                           ///< 雷达截面积radar cross section
    double mStatus{0.0};                        ///< 状态
    double mMeasureState{0.0};                  ///< 测量状态
    double mDynamicProperty{0x1};               ///< 动态特性 0x0:moving; 0x1:statinary; 0x2:oncoming 0x3:stationary candidate 0x4:unkown 0x5:crossing stationary 0x6:crossing moving 0x7:stopped
    double mSubframeID{0.0};                    ///< 子帧ID
    double mDopplerVelocity{0.0};                    ///< 子帧ID
    double mAssociatedTrackID{0.0};             ///< 关联跟踪点ID
    double mGroupID{0.0};                       // 组ID
    double mAssocRawIdx{0.0};                        // 关联的rawIdx
    double mClass{0.0};                         ///< 分类
    double mExistProbability{0.0};              ///< 存在概率
    double mObstacleProbability{0.0};           ///< 障碍概率
    double mRollingCount{0.0};                  ///< 计数器
    double mChecksum{0.0};                      ///< 校验和
    double mObjectType{0.0};                    ///< 目标类型

    //协议版本1中新增的跟踪点属性
    double mTrackType{0.0};                     ///< 目标类型 0=none 1=待起批目标 2=稳定跟踪目标
    double mTrackLifeCycleCnt{0.0};             ///< 跟踪次数
    double mHitCnt{0.0};                        ///< 连续命中次数
    double mMissCnt{0.0};                       ///< 连续丢失次数
    double mAssociatedNearestID{0.0};           ///< 最近关联点
    double mAssociatedStrongestID{0.0};         ///< 最强关联点
    double mFusionID{0.0};                      ///< 融合目标ID
    double mStatus02{0.0};

    double mRefencePointPostion{0.0};           ///< 目标参考点信息
    double mMotionState{0.0};                   ///< 运动状态
    double mMotionDirection{0.0};               ///< 运动方向
    double mDetectionStatus{0.0};               ///< 检查状态
    double mMeasQuality{0.0};                   ///< 可表示置信度、权重等
    double mMatchFlag{0.0};                     ///< AB波匹配结果
    double mReserve{0.0};                       ///< 预留数据

    double mObservationHist{0.0};               ///< 目标关联的结果
    double mChks{0.0};                          ///< CRC
    double mCntr{0.0};                          ///< 计数器 Counter
    double mConf{0.0};                          ///< 置信度 Confidence
    double mMtnPat{0.0};                        ///< 运动模式
    double mElevnSts{0.0};                      ///< 仰角状态 Elevation Status
    double mTypConfBike{0.0};                   ///< 自信车跟踪目标类型分类的置信度
    double mTypConfPed{0.0};                    ///< 行人跟踪目标类型分类的置信度
    double mTrackSts{0.0};                      ///< 跟踪状态
    double mIsInFreeSpace{0.0};                 ///< 指示对象是否在自由空间内
    double mElevnConf{0.0};                     ///< 俯仰置信度
    double mMirrProblty{0.0};                   ///< 镜像对象概率
    double mNotRealProblty{0.0};                ///< 假目标概率
    double mTypConfVeh{0.0};                    ///< 卡车跟踪目标类型分类的置信度
    double mStatyCnt{0.0};                      ///< 平稳次数
    double mTiAlv{0.0};                         ///< 生命周期
    double mCoastCnt{0.0};                      ///< 对象不与任何东西关联的周期数
    double mVelocityBin{0.0};                   ///< 多普勒索引(速度单元）
//    double mUsedTracker{0.0};                   ///<
//    double mQualityBits{0.0};                   ///<
//    double mUB{0.0};                            ///<

    MoveStatus mStateOfMotion{UnknowMoveStatus};
    bool mAlarmed{false};
    bool mEarlyWarning{false};
    bool mValid{false};

    ProtocolType mProtocolType{ProtocolChengTech};
}Target;

Q_DECLARE_METATYPE(Target);

typedef struct  ANALYSIS_EXPORT TargetHeader
{
    double value(AnalysisType analysisType) const
    {
        switch (analysisType)
        {
        ANALYSIS_VALUE(TargetIntervalTime)
                ANALYSIS_VALUE(TargetRollingCount)
                ANALYSIS_VALUE(TargetBlockCounter)
                ANALYSIS_VALUE(TargetChecksum)                 ///< 校验和
                ANALYSIS_VALUE(MeasurementCount)
                ANALYSIS_VALUE(FunctionCalculationTime)
                ANALYSIS_VALUE(ProfileCount)
                ANALYSIS_VALUE(ResponseTaskCycleTime)
                ANALYSIS_VALUE(ProtocolVersion)                 ///< 协议版本
                ANALYSIS_VALUE(NoiseCurrent)                    ///< 噪声电流
                ANALYSIS_VALUE(NoiseGlobal)
                ANALYSIS_VALUE(DataSource)                      ///< 数据源
                ANALYSIS_VALUE(NumberAfterCFAR)
                ANALYSIS_VALUE(NumberAfterFilter)
                ANALYSIS_VALUE(FrameModeNumber)
                ANALYSIS_VALUE(FrequencyHoppingState)
                ANALYSIS_VALUE(TunnelsceneState)               ///< 隧道场景标记位
                ANALYSIS_VALUE(CurrentFrameModeIndex)
                ANALYSIS_VALUE(BlockageFlag)
                ANALYSIS_VALUE(BlockagePercent)
                ANALYSIS_VALUE(InterferenceFlag)
                ANALYSIS_VALUE(InterferencePercent)
                ANALYSIS_VALUE(OffsetToSideX)
                ANALYSIS_VALUE(OffsetToSideY)
                ANALYSIS_VALUE(OffsetToSideZ)
                ANALYSIS_VALUE(WaveType)                       ///< AB类型
                ANALYSIS_VALUE(HeaderReserve)                  ///< 预留数据
                ANALYSIS_VALUE(HeaderSenceFlag)                ///< 当前场景标识
                ANALYSIS_VALUE(HeaderMagOffset)                ///< 下位机计算的Mag/Noise与输出值之间的偏移关系
                ANALYSIS_VALUE(HeaderCurFrameMode)             ///< 当前工作波形
                ANALYSIS_VALUE(HeaderNumAfterCFAR)             ///< CFAR输出目标点数
                default:
            break;
        }

        return 0.0;
    }

    void setValue(AnalysisType analysisType, double v)
    {
        switch (analysisType)
        {
        SET_ANALYSIS_VALUE(TargetIntervalTime, v)
                SET_ANALYSIS_VALUE(TargetRollingCount, v)
                SET_ANALYSIS_VALUE(TargetBlockCounter, v)
                SET_ANALYSIS_VALUE(TargetChecksum, v)                ///< 校验和
                SET_ANALYSIS_VALUE(MeasurementCount, v)
                SET_ANALYSIS_VALUE(FunctionCalculationTime, v)
                SET_ANALYSIS_VALUE(ProfileCount, v)
                SET_ANALYSIS_VALUE(ResponseTaskCycleTime, v)
                SET_ANALYSIS_VALUE(ProtocolVersion, v)                 ///< 协议版本
                SET_ANALYSIS_VALUE(NoiseCurrent, v)                    ///< 噪声电流
                SET_ANALYSIS_VALUE(NoiseGlobal, v)
                SET_ANALYSIS_VALUE(DataSource, v)                      ///< 数据源
                SET_ANALYSIS_VALUE(NumberAfterCFAR, v)
                SET_ANALYSIS_VALUE(NumberAfterFilter, v)
                SET_ANALYSIS_VALUE(FrameModeNumber, v)
                SET_ANALYSIS_VALUE(FrequencyHoppingState, v)
                SET_ANALYSIS_VALUE(TunnelsceneState, v)               ///< 隧道场景标记位
                SET_ANALYSIS_VALUE(CurrentFrameModeIndex, v)
                SET_ANALYSIS_VALUE(BlockageFlag, v)
                SET_ANALYSIS_VALUE(BlockagePercent, v)
                SET_ANALYSIS_VALUE(InterferenceFlag, v)
                SET_ANALYSIS_VALUE(InterferencePercent, v)
                SET_ANALYSIS_VALUE(OffsetToSideX, v)
                SET_ANALYSIS_VALUE(OffsetToSideY, v)
                SET_ANALYSIS_VALUE(OffsetToSideZ, v)
                SET_ANALYSIS_VALUE(WaveType, v)                       ///< AB类型
                SET_ANALYSIS_VALUE(HeaderReserve, v)                  ///< 预留数据
                SET_ANALYSIS_VALUE(HeaderSenceFlag, v)                ///< 当前场景标识
                SET_ANALYSIS_VALUE(HeaderMagOffset, v)                ///< 下位机计算的Mag/Noise与输出值之间的偏移关系
                SET_ANALYSIS_VALUE(HeaderCurFrameMode, v)             ///< 当前工作波形
                SET_ANALYSIS_VALUE(HeaderNumAfterCFAR, v)             ///< CFAR输出目标点数
                default:
            break;
        }
    }

    quint64 mTargetCount{0};                    ///< 目标数量
    quint64 mTargetIntervalTime{0};             ///< 周期间隔计时
    quint64 mTargetRollingCount{0};             ///< 循环计数器
    quint64 mTargetBlockCounter{0};             ///< 块计数器
    quint64 mTargetChecksum{0};                 ///< 校验和
    quint64 mMeasurementCount{0};               ///< 测量计数器
    quint64 mFunctionCalculationTime{0};        ///< 功能计算时间
    quint64 mProfileCount{0};                   ///< Profile数量
    quint64 mBlockageFlag{0};                   ///< 是否遮挡
    double mBlockagePercent{0.0};               ///< 遮挡程度
    quint64 mInterferenceFlag{0};               ///< 是否干扰
    double mInterferencePercent{0.0};           ///< 干扰程度
    double mOffsetToSideX{0.0};                 ///< x偏移量
    double mOffsetToSideY{0.0};                 ///< y偏移量
    double mOffsetToSideZ{0.0};                 ///< z偏移量
    double mResponseTaskCycleTime{0};           ///< 响应任务周期时间
    double mProtocolVersion{0};                 ///< 协议版本
    double mNoiseCurrent{0};                    ///< 电流噪声
    double mNoiseGlobal{0};                     ///< 全局噪声
    double mDataSource{0};                      ///< 数据源
    double mNumberAfterCFAR{0};
    double mNumberAfterFilter{0};
    double mFrameModeNumber{0};
    double mFrequencyHoppingState{0};          ///< 调频状态
    double mTunnelsceneState{0};               ///< 隧道场景标记位
    double mCurrentFrameModeIndex{0};
    double mWaveType{0};                       ///< AB类型
    double mHeaderReserve{0};                  ///< 预留数据
    double mHeaderSenceFlag{0};                ///< 当前场景标识
    double mHeaderMagOffset{0};                ///< 下位机计算的Mag/Noise与输出值之间的偏移关系
    double mHeaderCurFrameMode{0};             ///< 当前工作波形
    double mHeaderNumAfterCFAR{0};             ///< CFAR输出目标点数

}TargetHeader;

Q_DECLARE_METATYPE(TargetHeader);

typedef struct ANALYSIS_EXPORT Targets
{
    void clear();

    void print() const;

    Target mTargets[MAX_TARGET_COUNT];
    TargetHeader mTargetHeader;
    int mTargetCount{0};                        ///< 目标数量
    bool mValid{false};
}Targets;

Q_DECLARE_METATYPE(Targets);

enum AlarmType {
    Alarm_None = 0x00,
    Alarm_BSD = 0x01,
    Alarm_LCA = 0x02,
    Alarm_RCW = 0x04,
    Alarm_DOWF = 0x08,
    Alarm_DOWR = 0x10,
    Alarm_RCTA = 0x20,
    Alarm_RCTB = 0x40,
    Alarm_FCTA = 0x80,
    Alarm_FCTB = 0x0100,
    Alarm_JA = 0x0200,
};

typedef struct ANALYSIS_EXPORT AlarmData
{
    double value(AnalysisType analysisType) const
    {
        switch (analysisType)
        {
        ANALYSIS_VALUE(DrivingFunctionAlarmModule)
        ANALYSIS_VALUE(AlarmBSDLevel)
        ANALYSIS_VALUE(AlarmBSDObjectID)
                ANALYSIS_VALUE(AlarmBSDState)
        ANALYSIS_VALUE(AlarmDOWRLevel)
        ANALYSIS_VALUE(AlarmDOWFLevel)
        ANALYSIS_VALUE(AlarmDOWObjectID)
        ANALYSIS_VALUE(AlarmDOWObjectTTC)
                ANALYSIS_VALUE(AlarmDOWState)
        ANALYSIS_VALUE(AlarmFCTALevel)
        ANALYSIS_VALUE(AlarmFCTAObjectID)
        ANALYSIS_VALUE(AlarmFCTAObjectTTC)
                ANALYSIS_VALUE(AlarmFCTAState)
        ANALYSIS_VALUE(AlarmFCTBObjectID)
        ANALYSIS_VALUE(AlarmFCTBObjectTTC)
                ANALYSIS_VALUE(AlarmFCTBLevel)
                ANALYSIS_VALUE(AlarmFCTBState)
        ANALYSIS_VALUE(AlarmLCALevel)
        ANALYSIS_VALUE(AlarmLCAObjectID)
        ANALYSIS_VALUE(AlarmLCAObjectTTC)
                ANALYSIS_VALUE(AlarmLCAState)
        ANALYSIS_VALUE(AlarmRCTALevel)
        ANALYSIS_VALUE(AlarmRCTAObjectID)
        ANALYSIS_VALUE(AlarmRCTAObjectTTC)
                ANALYSIS_VALUE(AlarmRCTAState)
        ANALYSIS_VALUE(AlarmRCTBObjectID)
        ANALYSIS_VALUE(AlarmRCTBObjectTTC)
                ANALYSIS_VALUE(AlarmRCTBLevel)
                ANALYSIS_VALUE(AlarmRCTBState)
        ANALYSIS_VALUE(AlarmRCWLevel)
        ANALYSIS_VALUE(AlarmRCWObjectID)
        ANALYSIS_VALUE(AlarmRCWObjectTTC)
                ANALYSIS_VALUE(AlarmRCWState)
                ANALYSIS_VALUE(AlarmJALevel)
                ANALYSIS_VALUE(AlarmJAObjectID)
                ANALYSIS_VALUE(AlarmJAObjectTTC)
                ANALYSIS_VALUE(AlarmJAState)
                ANALYSIS_VALUE(AlarmELKLevel)
                ANALYSIS_VALUE(AlarmELKObjectID)
        default:
            break;
        }
        return 0.0;
    }

    void setValue(AnalysisType analysisType, double v)
    {
        switch (analysisType)
        {
        SET_ANALYSIS_VALUE(DrivingFunctionAlarmModule, v)
        SET_ANALYSIS_VALUE(AlarmBSDLevel, v)
        SET_ANALYSIS_VALUE(AlarmBSDObjectID, v)
                SET_ANALYSIS_VALUE(AlarmBSDState, v)
        SET_ANALYSIS_VALUE(AlarmDOWRLevel, v)
        SET_ANALYSIS_VALUE(AlarmDOWFLevel, v)
        SET_ANALYSIS_VALUE(AlarmDOWObjectID, v)
        SET_ANALYSIS_VALUE(AlarmDOWObjectTTC, v)
                SET_ANALYSIS_VALUE(AlarmDOWState, v)
        SET_ANALYSIS_VALUE(AlarmFCTALevel, v)
        SET_ANALYSIS_VALUE(AlarmFCTAObjectID, v)
        SET_ANALYSIS_VALUE(AlarmFCTAObjectTTC, v)
                SET_ANALYSIS_VALUE(AlarmFCTAState, v)
        SET_ANALYSIS_VALUE(AlarmFCTBObjectID, v)
        SET_ANALYSIS_VALUE(AlarmFCTBObjectTTC, v)
                SET_ANALYSIS_VALUE(AlarmFCTBLevel, v)
                SET_ANALYSIS_VALUE(AlarmFCTBState, v)
        SET_ANALYSIS_VALUE(AlarmLCALevel, v)
        SET_ANALYSIS_VALUE(AlarmLCAObjectID, v)
        SET_ANALYSIS_VALUE(AlarmLCAObjectTTC, v)
                SET_ANALYSIS_VALUE(AlarmLCAState, v)
        SET_ANALYSIS_VALUE(AlarmRCTALevel, v)
        SET_ANALYSIS_VALUE(AlarmRCTAObjectID, v)
        SET_ANALYSIS_VALUE(AlarmRCTAObjectTTC, v)
                SET_ANALYSIS_VALUE(AlarmRCTAState, v)
        SET_ANALYSIS_VALUE(AlarmRCTBObjectID, v)
        SET_ANALYSIS_VALUE(AlarmRCTBObjectTTC, v)
                SET_ANALYSIS_VALUE(AlarmRCTBLevel, v)
                SET_ANALYSIS_VALUE(AlarmRCTBState, v)
        SET_ANALYSIS_VALUE(AlarmRCWLevel, v)
        SET_ANALYSIS_VALUE(AlarmRCWObjectID, v)
        SET_ANALYSIS_VALUE(AlarmRCWObjectTTC, v)
                SET_ANALYSIS_VALUE(AlarmRCWState, v)
                SET_ANALYSIS_VALUE(AlarmJALevel, v)
                SET_ANALYSIS_VALUE(AlarmJAObjectID, v)
                SET_ANALYSIS_VALUE(AlarmJAObjectTTC, v)
                SET_ANALYSIS_VALUE(AlarmJAState, v)
                SET_ANALYSIS_VALUE(AlarmELKLevel, v)
                SET_ANALYSIS_VALUE(AlarmELKObjectID, v)
        default:
            break;
        }
    }

    quint16 alarmTypes();
    void clear();
    void print() const;

    double mDrivingFunctionAlarmModule{0.0};
    double mAlarmBSDLevel{0.0};
    double mAlarmBSDObjectID{0.0};
    double mAlarmBSDState{0.0};
    double mAlarmDOWLevelRear{0.0};
    double mAlarmDOWLevelFront{0.0};
    double mAlarmDOWRLevel{0.0};
    double mAlarmDOWFLevel{0.0};
    double mAlarmDOWObjectID{0.0};
    double mAlarmDOWObjectTTC{0.0};
    double mAlarmDOWState{0.0};
    double mAlarmFCTALevel{0.0};
    double mAlarmFCTAObjectID{0.0};
    double mAlarmFCTAObjectTTC{0.0};
    double mAlarmFCTAState{0.0};
    double mAlarmFCTBObjectID{0.0};
    double mAlarmFCTBObjectTTC{0.0};
    double mAlarmFCTBLevel{0.0};
    double mAlarmFCTBState{0.0};
    double mAlarmLCALevel{0.0};
    double mAlarmLCAObjectID{0.0};
    double mAlarmLCAObjectTTC{0.0};
    double mAlarmLCAState{0.0};
    double mAlarmRCTALevel{0.0};
    double mAlarmRCTAObjectID{0.0};
    double mAlarmRCTAObjectTTC{0.0};
    double mAlarmRCTAState{0.0};
    double mAlarmRCTBObjectID{0.0};
    double mAlarmRCTBObjectTTC{0.0};
    double mAlarmRCTBState{0.0};
    double mAlarmRCTBLevel{0.0};
    double mAlarmRCWLevel{0.0};
    double mAlarmRCWObjectID{0.0};
    double mAlarmRCWObjectTTC{0.0};
    double mAlarmRCWState{0.0};

    double mAlarmJALevel{0.0};
    double mAlarmJAObjectID{0.0};
    double mAlarmJAObjectTTC{0.0};
    double mAlarmJAState{0.0};
    double mAlarmELKLevel{0.0};
    double mAlarmELKObjectID{0.0};
    quint8 mErrorInformation0x4DN[256]{0};             ///< 故障信息
    quint8 mErrorInformation0x4EN[256]{0};             ///< 故障信息
}AlarmData;

Q_DECLARE_METATYPE(AlarmData);

typedef struct ANALYSIS_EXPORT VehicleData
{
    double value(AnalysisType analysisType) const
    {
        switch (analysisType)
        {
        ANALYSIS_VALUE(VehicleChecksum)                ///< 车身信息校验和
        ANALYSIS_VALUE(VehicleSpeed)                   ///< 汽车速度
        ANALYSIS_VALUE(VehicleSpeedInMPS)              ///< 车身微处理器速度
        ANALYSIS_VALUE(Radius)                         ///< 转弯半径
        ANALYSIS_VALUE(YawRate)                        ///< 横摆角速度
        ANALYSIS_VALUE(Gear)                           ///< 档位
        ANALYSIS_VALUE(SteeringWheelAngle)             ///< 方向盘角度
        ANALYSIS_VALUE(KeyStatus)                      ///< 钥匙状态
        ANALYSIS_VALUE(SecurityLock)                   ///< 安全锁
        ANALYSIS_VALUE(TrailerStatus)                  ///< 拖车状态
        ANALYSIS_VALUE(ESPFailStatus)                  ///< ESP失败状态,汽车esp是车身稳定系统
        ANALYSIS_VALUE(AcceleratorPedalActiveLevel)    ///< 加速踏板激活水平
        ANALYSIS_VALUE(BrakePedalActiveLevel)          ///< 刹车踏板激活水平
        ANALYSIS_VALUE(BrakePedalStatus)               ///< 刹车踏板状态
        ANALYSIS_VALUE(LateralAcceleration)            ///< 横向加速度
        ANALYSIS_VALUE(LongitudinalAcceleration)       ///< 纵向加速度
        ANALYSIS_VALUE(TurnLightLeft)                  ///< 左转向灯
        ANALYSIS_VALUE(TurnLightRight)
        ANALYSIS_VALUE(WheelSpeedFontLeft)             ///< 前左车轮速度
        ANALYSIS_VALUE(WheelSpeedFontRight)
        ANALYSIS_VALUE(WheelSpeedRearLeft)
        ANALYSIS_VALUE(WheelSpeedRearRight)
        ANALYSIS_VALUE(WheelSpeedDirectionFontLeft)    ///< 前左车轮速度方向
        ANALYSIS_VALUE(WheelSpeedDirectionFontRight)
        ANALYSIS_VALUE(WheelSpeedDirectionRearLeft)
        ANALYSIS_VALUE(WheelSpeedDirectionRearRight)
        ANALYSIS_VALUE(DoorFrontLeft)                  ///< 前左车门
        ANALYSIS_VALUE(DoorFrontRight)
        ANALYSIS_VALUE(DoorRearLeft)
        ANALYSIS_VALUE(DoorRearRight)
        ANALYSIS_VALUE(SwitchBSDFunction)              ///< BSD功能开关
        ANALYSIS_VALUE(SwitchDOWFunction)
        ANALYSIS_VALUE(SwitchFCTAFunction)
        ANALYSIS_VALUE(SwitchFCTBFunction)
        ANALYSIS_VALUE(SwitchRCTAFunction)
        ANALYSIS_VALUE(SwitchRCTBFunction)
        ANALYSIS_VALUE(SwitchRCWFunction)
        ANALYSIS_VALUE(SwitchJAFunction)
        ANALYSIS_VALUE(SwitchMainFunction)
        ANALYSIS_VALUE(VehicleRollingCount)            ///< 循环计数器
        ANALYSIS_VALUE(VehicleYear)
        ANALYSIS_VALUE(VehicleMonth)
        ANALYSIS_VALUE(VehicleDay)
        ANALYSIS_VALUE(VehicleHour)
        ANALYSIS_VALUE(VehicleMinute)
        ANALYSIS_VALUE(VehicleSecond)
                ANALYSIS_VALUE(VehicleVersion)
                ANALYSIS_VALUE(UseVehicleSpeed)                   ///< 汽车速度
                ANALYSIS_VALUE(UseYawRate)                        ///< 横摆角速度
                ANALYSIS_VALUE(DoorLockFrontLeft)                  ///< 前左车门
                ANALYSIS_VALUE(DoorLockFrontRight)
                ANALYSIS_VALUE(DoorLockRearLeft)
                ANALYSIS_VALUE(DoorLockRearRight)
                ANALYSIS_VALUE(SteerWheelRotationSpdAK)
                ANALYSIS_VALUE(RadarInstallAngleH)
                ANALYSIS_VALUE(RadarInstallAngleP)
                ANALYSIS_VALUE(RadarSelfCaliAngleH)
                ANALYSIS_VALUE(RadarSelfCaliAngleP)
                ANALYSIS_VALUE(RadarWhlBaseY)
                ANALYSIS_VALUE(RadarWhlBaseX1)
                ANALYSIS_VALUE(RadarWhlBaseX2)
                ANALYSIS_VALUE(RadarOffsetHrz)
                ANALYSIS_VALUE(RadarOffsetVrt)
                ANALYSIS_VALUE(RadarSelfCaliBufCnt)
        default:
            break;
        }
        return 0.0;
    }

    void setValue(AnalysisType analysisType, double v)
    {
        switch (analysisType)
        {
        SET_ANALYSIS_VALUE(VehicleChecksum, v)                ///< 车身信息校验和
        SET_ANALYSIS_VALUE(VehicleSpeed, v)                   ///< 汽车速度
        SET_ANALYSIS_VALUE(VehicleSpeedInMPS, v)              ///< 车身微处理器速度
        SET_ANALYSIS_VALUE(Radius, v)                         ///< 转弯半径
        SET_ANALYSIS_VALUE(YawRate, v)                        ///< 横摆角速度
        SET_ANALYSIS_VALUE(Gear, v)                           ///< 档位
        SET_ANALYSIS_VALUE(SteeringWheelAngle, v)             ///< 方向盘角度
        SET_ANALYSIS_VALUE(KeyStatus, v)                      ///< 钥匙状态
        SET_ANALYSIS_VALUE(SecurityLock, v)                   ///< 安全锁
        SET_ANALYSIS_VALUE(TrailerStatus, v)                  ///< 拖车状态
        SET_ANALYSIS_VALUE(ESPFailStatus, v)                  ///< ESP失败状态,汽车esp是车身稳定系统
        SET_ANALYSIS_VALUE(AcceleratorPedalActiveLevel, v)    ///< 加速踏板激活水平
        SET_ANALYSIS_VALUE(BrakePedalActiveLevel, v)          ///< 刹车踏板激活水平
        SET_ANALYSIS_VALUE(BrakePedalStatus, v)               ///< 刹车踏板状态
        SET_ANALYSIS_VALUE(LateralAcceleration, v)            ///< 横向加速度
        SET_ANALYSIS_VALUE(LongitudinalAcceleration, v)       ///< 纵向加速度
        SET_ANALYSIS_VALUE(TurnLightLeft, v)                  ///< 左转向灯
        SET_ANALYSIS_VALUE(TurnLightRight, v)
        SET_ANALYSIS_VALUE(WheelSpeedFontLeft, v)             ///< 前左车轮速度
        SET_ANALYSIS_VALUE(WheelSpeedFontRight, v)
        SET_ANALYSIS_VALUE(WheelSpeedRearLeft, v)
        SET_ANALYSIS_VALUE(WheelSpeedRearRight, v)
        SET_ANALYSIS_VALUE(WheelSpeedDirectionFontLeft, v)    ///< 前左车轮速度方向
        SET_ANALYSIS_VALUE(WheelSpeedDirectionFontRight, v)
        SET_ANALYSIS_VALUE(WheelSpeedDirectionRearLeft, v)
        SET_ANALYSIS_VALUE(WheelSpeedDirectionRearRight, v)
        SET_ANALYSIS_VALUE(DoorFrontLeft, v)                  ///< 前左车门
        SET_ANALYSIS_VALUE(DoorFrontRight, v)
        SET_ANALYSIS_VALUE(DoorRearLeft, v)
        SET_ANALYSIS_VALUE(DoorRearRight, v)
        SET_ANALYSIS_VALUE(SwitchBSDFunction, v)              ///< BSD功能开关
        SET_ANALYSIS_VALUE(SwitchDOWFunction, v)
        SET_ANALYSIS_VALUE(SwitchFCTAFunction, v)
        SET_ANALYSIS_VALUE(SwitchFCTBFunction, v)
        SET_ANALYSIS_VALUE(SwitchRCTAFunction, v)
        SET_ANALYSIS_VALUE(SwitchRCTBFunction, v)
        SET_ANALYSIS_VALUE(SwitchRCWFunction, v)
        SET_ANALYSIS_VALUE(SwitchJAFunction, v)
        SET_ANALYSIS_VALUE(SwitchMainFunction, v)
        SET_ANALYSIS_VALUE(VehicleRollingCount, v)            ///< 循环计数器
        SET_ANALYSIS_VALUE(VehicleYear, v)
        SET_ANALYSIS_VALUE(VehicleMonth, v)
        SET_ANALYSIS_VALUE(VehicleDay, v)
        SET_ANALYSIS_VALUE(VehicleHour, v)
        SET_ANALYSIS_VALUE(VehicleMinute, v)
        SET_ANALYSIS_VALUE(VehicleSecond, v)
                SET_ANALYSIS_VALUE(VehicleVersion, v)
                SET_ANALYSIS_VALUE(UseVehicleSpeed, v)                   ///< 汽车速度
                SET_ANALYSIS_VALUE(UseYawRate, v)                        ///< 横摆角速度
                SET_ANALYSIS_VALUE(DoorLockFrontLeft, v)                  ///< 前左车门
                SET_ANALYSIS_VALUE(DoorLockFrontRight, v)
                SET_ANALYSIS_VALUE(DoorLockRearLeft, v)
                SET_ANALYSIS_VALUE(DoorLockRearRight, v)
                SET_ANALYSIS_VALUE(SteerWheelRotationSpdAK, v)
                SET_ANALYSIS_VALUE(RadarInstallAngleH, v)
                SET_ANALYSIS_VALUE(RadarInstallAngleP, v)
                SET_ANALYSIS_VALUE(RadarSelfCaliAngleH, v)
                SET_ANALYSIS_VALUE(RadarSelfCaliAngleP, v)
                SET_ANALYSIS_VALUE(RadarWhlBaseY, v)
                SET_ANALYSIS_VALUE(RadarWhlBaseX1, v)
                SET_ANALYSIS_VALUE(RadarWhlBaseX2, v)
                SET_ANALYSIS_VALUE(RadarOffsetHrz, v)
                SET_ANALYSIS_VALUE(RadarOffsetVrt, v)
                SET_ANALYSIS_VALUE(RadarSelfCaliBufCnt, v)
        default:
            break;
        }
    }

    void clear();
    void print() const;

    double mVehicleChecksum{0.0};                ///< 汽车信息校验和
    double mVehicleSpeed{0.0};                   ///< 汽车速度
    double mVehicleSpeedInMPS{0.0};              ///< 车身微处理器速度
    double mRadius{0.0};                         ///< 转弯半径
    double mYawRate{0.0};                        ///< 横摆角速度
    double mGear{0.0};                           ///< 档位; 1:P; 2:R; 3:N; 4:D
    double mSteeringWheelAngle{0.0};             ///< 方向盘角度
    double mKeyStatus{0.0};                      ///< 钥匙状态
    double mSecurityLock{0.0};                   ///< 安全锁
    double mTrailerStatus{0.0};                  ///< 拖车状态
    double mESPFailStatus{0.0};                  ///< ESP失败状态,汽车esp是车身稳定系统
    double mAcceleratorPedalActiveLevel{0.0};    ///< 加速踏板激活水平
    double mBrakePedalActiveLevel{0.0};          ///< 刹车踏板激活水平
    double mBrakePedalStatus{0.0};               ///< 刹车踏板状态
    double mLateralAcceleration{0.0};            ///< 横向加速度
    double mLongitudinalAcceleration{0.0};       ///< 纵向加速度
    double mTurnLightLeft{0.0};                  ///< 左转向灯
    double mTurnLightRight{0.0};
    double mWheelSpeedFontLeft{0.0};             ///< 前左车轮速度
    double mWheelSpeedFontRight{0.0};
    double mWheelSpeedRearLeft{0.0};
    double mWheelSpeedRearRight{0.0};
    double mWheelSpeedDirectionFontLeft{0.0};    ///< 前左车轮速度方向
    double mWheelSpeedDirectionFontRight{0.0};
    double mWheelSpeedDirectionRearLeft{0.0};
    double mWheelSpeedDirectionRearRight{0.0};
    double mDoorFrontLeft{0.0};                  ///< 前左车门
    double mDoorFrontRight{0.0};
    double mDoorRearLeft{0.0};
    double mDoorRearRight{0.0};
    double mSwitchBSDFunction{0.0};              ///< BSD功能开关
    double mSwitchDOWFunction{0.0};
    double mSwitchFCTAFunction{0.0};
    double mSwitchFCTBFunction{0.0};
    double mSwitchRCTAFunction{0.0};
    double mSwitchRCTBFunction{0.0};
    double mSwitchRCWFunction{0.0};
    double mSwitchJAFunction{0.0};
    double mSwitchMainFunction{0.0};
    double mVehicleRollingCount{0.0};            ///< 循环计数器

    double mVehicleYear{0.0};
    double mVehicleMonth{0.0};
    double mVehicleDay{0.0};
    double mVehicleHour{0.0};
    double mVehicleMinute{0.0};
    double mVehicleSecond{0.0};

    double mVehicleVersion{0.0};
    double mUseVehicleSpeed{0.0};                   ///< 汽车速度
    double mUseYawRate{0.0};                        ///< 横摆角速度
    double mDoorLockFrontLeft{0.0};                  ///< 前左车门
    double mDoorLockFrontRight{0.0};
    double mDoorLockRearLeft{0.0};
    double mDoorLockRearRight{0.0};
    double mSteerWheelRotationSpdAK{0.0};
    double mRadarInstallAngleH{0.0};
    double mRadarInstallAngleP{0.0};
    double mRadarSelfCaliAngleH{0.0};
    double mRadarSelfCaliAngleP{0.0};
    double mRadarWhlBaseY{0.0};
    double mRadarWhlBaseX1{0.0};
    double mRadarWhlBaseX2{0.0};
    double mRadarOffsetHrz{0.0};
    double mRadarOffsetVrt{0.0};
    double mRadarSelfCaliBufCnt{0.0};

    double mCurvature{0.0};                     ///< 曲率滤波
}VehicleData;

Q_DECLARE_METATYPE(VehicleData);

typedef struct ANALYSIS_EXPORT EndFrameData
{
    double value(AnalysisType analysisType) const
    {
        switch (analysisType)
        {
        ANALYSIS_VALUE(EndFrameChecksum)              ///< 校验和
        ANALYSIS_VALUE(EndFrameIntervalTime)                ///< 周期间隔计时
        ANALYSIS_VALUE(RadarRollingCount)           ///< 循环计数器
        ANALYSIS_VALUE(EndOfLineEstablishedAngle)            ///< 产线安装角
        ANALYSIS_VALUE(SelfCalibrationEstablishedAngle)               ///< 自标定安装角
        ANALYSIS_VALUE(SpeedMode)                          ///< 速度模式
        ANALYSIS_VALUE(RoadSideDistance)                   ///< 车道线距离
        ANALYSIS_VALUE(TimeTick)                        ///< 时间节拍
        ANALYSIS_VALUE(SendTotalCANFrmCnt)             ///< 发送报文总数
        ANALYSIS_VALUE(Recv410FrameCount)              ///< 收到的410协议报文总计数
        ANALYSIS_VALUE(AutoALNSts)              ///< 自标定运行状态
        ANALYSIS_VALUE(Temperature)                   ///< 雷达温度
        default:
            break;
        }
        return 0.0;
    }

    void setValue(AnalysisType analysisType, double v)
    {
        switch (analysisType)
        {
        SET_ANALYSIS_VALUE(EndFrameChecksum, v)              ///< 校验和
        SET_ANALYSIS_VALUE(EndFrameIntervalTime, v)                 ///< 周期间隔计时
        SET_ANALYSIS_VALUE(RadarRollingCount, v)            ///< 循环计数器
        SET_ANALYSIS_VALUE(EndOfLineEstablishedAngle, v)             ///< 产线安装角
        SET_ANALYSIS_VALUE(SelfCalibrationEstablishedAngle, v)               ///< 自标定安装角
        SET_ANALYSIS_VALUE(SpeedMode, v)                          ///< 速度模式
        SET_ANALYSIS_VALUE(RoadSideDistance, v)                   ///< 车道线距离
        SET_ANALYSIS_VALUE(TimeTick, v)                           ///< 时间节拍
        SET_ANALYSIS_VALUE(SendTotalCANFrmCnt, v)                 ///< 时间节拍
        SET_ANALYSIS_VALUE(Recv410FrameCount, v)                  ///< 收到的410协议报文总计数
                SET_ANALYSIS_VALUE(AutoALNSts, v)                         ///< 自标定运行状态
                SET_ANALYSIS_VALUE(Temperature, v)                         ///< 自标定运行状态
        default:
            break;
        }
    }

    double mEndFrameChecksum{0.0};              ///< 校验和
    double mEndFrameIntervalTime{0};                    ///< 周期间隔计时
    double mRadarRollingCount{0};                        ///< 循环计数器
    double mEndOfLineEstablishedAngle{0};                ///< 产线安装角
    double mSelfCalibrationEstablishedAngle{0};              ///< 自标定安装角
    double mSpeedMode{0};                          ///< 速度模式
    double mRoadSideDistance{0};                   ///< 车道线距离
    double mTimeTick{0};                        ///< 时间节拍
    quint64 mSendTotalCANFrmCnt;                ///< 发送报文总数
    quint64 mRecv410FrameCount{0}; ///< 收到的410协议报文总计数
    double mAutoALNSts{0}; ///< 自标定运行状态
    double mTemperature{0.0};                   ///< 雷达温度

    double mGuardrail01_c0{0.0};
    double mGuardrail01_c1{0.0};
    double mGuardrail01_c2{0.0};
    double mGuardrail01_c3{0.0};
    double mGuardrail01_IngStart{0.0};
    double mGuardrail01_IngEnd{0.0};
    double mGuardrail01_vaild{0.0};
    double mGuardrail02_c0{0.0};
    double mGuardrail02_c1{0.0};
    double mGuardrail02_c2{0.0};
    double mGuardrail02_c3{0.0};
    double mGuardrail02_IngStart{0.0};
    double mGuardrail02_IngEnd{0.0};
    double mGuardrail02_vaild{0.0};


    bool mInit410FrameCountFlag{true}; ///< 标识mRecv410FrameCount是否对齐mRadar410FrameCount
    //雷达发送结束帧报文中的410协议报文总计数
    quint64 mInitRadar410FrameCount{0};  ///< 初始值
    quint64 mCurrentRadar410FrameCount{0}; ///< 当前值

    quint64 mDiff410FrameCount{0}; ///< 丢帧数
    double mDiff410FrameCountRate{0.0};///< 丢帧百分比
    //bool mLostFrameFlag{false}; ///< 标识是否丢帧
    bool mPreLostFrameFlag{false}; ///< 标识本结束帧和上一个结束帧之间是否丢帧


    quint64 mFrameTime;         // 时间戳(微秒级时间戳）
    quint64 mSystemFrameTime;   // 系统时间戳(微秒级时间戳）
    quint64 mFrameIndex{0};

    bool mValid{false};
    quint8 mRadarVersion[5][8];   ///< 软件版本 硬件版本 boot版本 校准版本 发行版本

    quint64 mFrameTimeStampLocal{0};
    quint64 mFrameTimeStampGlobal{0};
    quint8 mFrameInterTime{0};
    quint64 mVehicleInfoTimeOffset{0};
    quint64 mFirstTrackFrameNo{0};
    quint8 mPointListIsAhead{0};
}EndFrameData;

Q_DECLARE_METATYPE(EndFrameData);

//检测点范围信息
typedef struct ANALYSIS_EXPORT DeteStatisInfo
{
    double mMaxVel{0.0};
    double mMinVel{0.0};
    double mMaxLat{0.0};
    double mMinLat{0.0};
    double mMaxLng{0.0};
    double mMinLng{0.0};
    quint16 mDeteWinSize{10};
    quint16 mMatchMaxCost{0};

    //统计信息
    double  mDeteProb{0.0};
    quint64 mDeteCnt{0};
    quint64 mDeteAllCnt{0};
    quint64 mDeteTime{0};

}DeteStatisData;
Q_DECLARE_METATYPE(DeteStatisData);

/** @brief 角度补偿 */
typedef struct AngleCompensation {
    double mAngleCompensationRaw{0.0};
    double mAngleCompensationTrack{0.0};
    bool mUseRadarOffsetToSide{true};           ///< 使用雷达边距补偿
}AngleCompensation;

class ANALYSIS_EXPORT AnalysisData
{
public:
    AnalysisData();

    void calculateTarget(AnalysisFrameType frameType);
    void calculate(bool assigned = false);
    void clearTarget(AnalysisFrameType frameType);
    void clear();
    void clearRecv410FrameCount();

    void print() const;

    quint64 mHeSaiLiderSaveIndex{0};
    Target mTrueTarget[5];                              ///< 真值目标点
    Targets mTargets[FrameTargetCount];
    Targets m200Targets;
    Targets m16Targets;
    Targets mLockTargets;                               ///< 临车道、本车道的点
    Targets mELKTargets;                                ///< ELK的点
    AlarmData mAlarmData;                               ///< 告警信息
    AlarmData mEarlyAlarmData;                          ///< 预警信息(由上位机计算)
    VehicleData mVehicleData;                           ///< 汽车数据
    EndFrameData mEndFrameData;
    DeteStatisData mDeteStatisData;                     ///< 检测点统计范围
    long long mCameraSaveIndex[MAX_CAMERA_COUNT];       ///< 雷达保存帧号

    AngleCompensation mAngleCompensation;       ///< 角度补偿
    quint64 mRadarAlarmCount[MAX_RADAR_COUNT];
    quint64 mRadarAlarmIDCount[MAX_RADAR_COUNT];

    QByteArray mADC_1DFFT_2DFFT_DATA;

///<    bool mInit410FrameCountFlag; //标识mRecv410FrameCount是否对齐mRadar410FrameCount
///<    quint64 mRecv410FrameCount{0}; //收到的410协议报文总计数
///<    //雷达发送结束帧报文中的410协议报文总计数
///<    quint64 mInitRadar410FrameCount{0};  //初始值
///<    quint64 mCurrentRadar410FrameCount{0}; //当前值

    quint8 mRadarID;
    quint64 mFrameNb;
    quint64 mFrameTrackNb;

    bool mValid{false};
};

Q_DECLARE_METATYPE(AnalysisData);

/**
* @class EarlyWarningSettings
* @brief 预警设置
* <AUTHOR>
* @date 2022-12-20
* @note
* 预警设置
*/
typedef struct EarlyWarningSettings
{
    bool mDOWDisplay{true};             ///< 显示DOW区域
    double mDOWDistanceOfHeadway{4.96};    ///< DOW车头距
    double mDOWDistanceOfBody{-0.25};     ///< DOW车身距
    double mDOWWidth{2.25};             ///< DOW宽度
    double mDOWLength{45};               ///< DOW长度
    double mDOWTTC{2.5};                 ///< DOWTTC
    double mDOWVehicleSpeed{0};        ///< DOW车速

    bool mBSDDisplay{true};             ///< 显示BSD区域
    double mBSDDistanceOfHeadway{2};    ///< BSD车头距
    double mBSDDistanceOfBody{0.0};     ///< BSD车身距
    double mBSDWidth{3.75};             ///< BSD宽度
    double mBSDLength{7};               ///< BSD长度
    double mBSDTTC{3.5};                 ///< BSDTTC
    double mBSDVehicleSpeed{13.6};        ///< DOW车速

    bool mLCADisplay{true};             ///< 显示LCA区域
    double mLCADistanceOfHeadway{4.96}; ///< LCA车头距
    double mLCADistanceOfBody{0.0};     ///< LCA车身距
    double mLCAWidth{3.75};             ///< LCA宽度
    double mLCALength{70};              ///< LCA长度
    double mLCATTC{3.5};                 ///< LCATTC
    double mLCAVehicleSpeed{13.6};        ///< LCA车速

    bool mScriptFileCalculateEarlyWarning{false};       ///< 使用脚本文件计算告警
    QString mCalculateEarlyWarningScriptFile;          ///< 告警计算脚本
}EarlyWarningSettings;

Q_DECLARE_METATYPE(EarlyWarningSettings);

#endif // ANALYSISDATA_H
