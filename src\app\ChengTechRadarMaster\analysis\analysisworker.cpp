﻿#include "analysisworker.h"

#include "dataprocess/interpolationgeely.h"
#include "dataprocess/interpolationbydho.h"

#include "protocol/ianalysisprotocol.h"
#include "protocol/analysisprotocolct410.h"
#include "protocol/byd120targetprotocol.h"
#include "protocol/baictargetprotocol.h"
#include "protocol/truesystemprotocol.h"
#include "protocol/gwmtargetprotocol.h"
#include "protocol/geelytargetprotocol.h"
#include "protocol/bydhigherorderprotocol.h"

#include "protocol/CANProtocolChengTech710.h"
#include "protocol/CANProtocolBOSCH670.h"
#include "protocol/CANProtocolHozonADF.h"
#include "protocol/CANProtocolBYDHO.h"

#include <QThread>
#include <QDateTime>
#include <QDebug>

#define BIG_LITTLE_SWAP32(x)        ( (((*(long int *)&x) & 0xff000000) >> 24) | \
                                      (((*(long int *)&x) & 0x00ff0000) >> 8) | \
                                      (((*(long int *)&x) & 0x0000ff00) << 8) | \
                                      (((*(long int *)&x) & 0x000000ff) << 24) )

#define BIG_LITTLE_SWAP64(x)        ( (((*(long long int *)&x) & 0xff00000000000000) >> 56) | \
                                      (((*(long long int *)&x) & 0x00ff000000000000) >> 40) | \
                                      (((*(long long int *)&x) & 0x0000ff0000000000) >> 24) | \
                                      (((*(long long int *)&x) & 0x000000ff00000000) >> 8) | \
                                      (((*(long long int *)&x) & 0x00000000ff000000) << 8) | \
                                      (((*(long long int *)&x) & 0x0000000000ff0000) << 24) | \
                                      (((*(long long int *)&x) & 0x000000000000ff00) << 40) | \
                                      (((*(long long int *)&x) & 0x00000000000000ff) << 56) )

namespace Analysis {

AnalysisWorker::AnalysisWorker(bool dataProcess, QObject *parent)
    : QObject(parent),
      mAlarmCalculate(new AlarmCalculate(this)),
      mInterpolation(new InterpolationBYDHO(this))
//      mInterpolation(new InterpolationGEELY(this))
{
    for (int i = 0; i < sizeof (mAnalysisDatas) / sizeof (mAnalysisDatas[0]); ++i)
    {
        mAnalysisDatas[i].mRadarID = i;
    }


    mProtocols << Protocol::IAnalysisProtocol::newAnalysisProtocol(ProtocolChengTech410, this);
    if (!dataProcess) {
//    mProtocols << new Protocol::BYD120TargetProtocol(this, this);
//    mProtocols << new Protocol::BAICTargetProtocol(this, this); // 0x30N 与长城冲突
//    mProtocols << new Protocol::GWMTargetProtocol(this, this);  // 0x30A 与北汽冲突

    mProtocols << new Protocol::CANProtocolChengTech710(this, this);
//    mProtocols << new Protocol::CANProtocolBOSCH670(this, this);
//    mProtocols << new Protocol::CANProtocolHozonADF(this, this);
    mProtocols << new Protocol::GEELYTargetProtocol(this, this);
    mProtocols << new Protocol::BYDHOProtocol(this, this);
//    mProtocols << new Protocol::CANProtocolBYDHO(this, this);

    }
    mTrueSystemProtocol = new Protocol::TrueSystemProtocol(this, this);
}

void AnalysisWorker::analysisEnd(quint8 radarID, bool assigned, bool interpolation)
{
    if (mUseInterpolation && !interpolation) {
        return;
    }
    mAlarmCalculate->earlyWarning(&mAnalysisDatas[radarID]);

    memcpy(&mAnalysisDatas[radarID].mCameraSaveIndex, &mCameraSaveIndex, sizeof (mCameraSaveIndex));
//    qDebug() << __FUNCTION__ << __LINE__ << mAnalysisDatas[radarID].mCameraSaveIndex[1] << mCameraSaveIndex[1];
//    qDebug() << __FUNCTION__ << __LINE__ << mAnalysisDatas[radarID].mADC_1DFFT_2DFFT_DATA.length();
    emit analysisFinished(radarID, mAnalysisDatas[radarID]);
}

void AnalysisWorker::analysisEnd(quint8 radarID, AnalysisFrameType analysisType)
{
//    qDebug() << __FUNCTION__ << __LINE__ << radarID;
    emit analysisTargetFinished(radarID, analysisType);
}

void AnalysisWorker::analysisEnd(Parser::ParsedDataTypedef::ParsedDataType fType)
{
    mParsedData.mTargets[fType].mValid = true;
    switch (fType) {
    case Parser::ParsedDataTypedef::TargetRaw:
        break;
    case Parser::ParsedDataTypedef::TargetTrack:
    {
        uint64_t timestamp = QDateTime::currentMSecsSinceEpoch();
        mParsedData.mTargets[Parser::ParsedDataTypedef::TargetRaw].mRadarInfomation.mSystemTimstamp = timestamp;
        if (mParsedData.mTargets[Parser::ParsedDataTypedef::TargetRaw].mRadarInfomation.mRadarTimestamp == 0) {
            mParsedData.mTargets[Parser::ParsedDataTypedef::TargetRaw].mRadarInfomation.mRadarTimestamp = timestamp;
        }
        mParsedData.mTargets[fType].mRadarInfomation.mSystemTimstamp = timestamp;
        if (mParsedData.mTargets[fType].mRadarInfomation.mRadarTimestamp == 0) {
            mParsedData.mTargets[fType].mRadarInfomation.mRadarTimestamp = timestamp;
        }

        for (int i = 0; i <= Parser::ParsedDataTypedef::TargetTrack; ++i) {
            if (mParsedData.mTargets[i].mValid) {
                emit analysisTargetFinishedF(mParsedData.mTargets[i]);
                mParsedData.mTargets[i].mValid = false;
            }
        }
    }
    default:
        break;
    }

}

void AnalysisWorker::analysisTrueSystemEnd()
{
    emit analysisTrueSystemFinished();
}

void AnalysisWorker::setHozonBreakShort(bool breakShort)
{
    foreach (Protocol::IAnalysisProtocol *protocl, mProtocols) {
        if (protocl->protocolType() == ProtocolHonzonADF) {
            ((Protocol::CANProtocolHozonADF*)protocl)->setHozonBreakShort(breakShort);
        }
    }
}

void AnalysisWorker::setBYDHDChannelRadarID(bool raw600ByChannel, int channelRadarID[], int size)
{
    mInterpolation->setChannelRadarIDBYDHO(channelRadarID, size);
    mInterpolation->setRaw600ByChannel(raw600ByChannel);
    foreach (Protocol::IAnalysisProtocol *protocl, mProtocols) {
        if (protocl->protocolType() == ProtocolChengTech410) {
            ((Protocol::AnalysisProtocolCT410*)protocl)->setChannelRadarID(raw600ByChannel, channelRadarID, size);
        }
        if (protocl->protocolType() == ProtocolBYDHO) {
            ((Protocol::BYDHOProtocol*)protocl)->setChannelRadarID(channelRadarID, size);
        }
        if (protocl->protocolType() == ProtocolBYDHO_F) {
            ((Protocol::CANProtocolBYDHO*)protocl)->setChannelRadarID(channelRadarID, size);
        }
    }
}

void AnalysisWorker::setGEELYChannelRadarID(int *channelRadarID, int size)
{
    mInterpolation->setChannelRadarIDGEELY(channelRadarID, size);
    foreach (Protocol::IAnalysisProtocol *protocl, mProtocols) {
        if (protocl->protocolType() == ProtocolGEELY) {
            ((Protocol::GEELYTargetProtocol*)protocl)->setChannelRadarID(channelRadarID, size);
        }
    }
}

void AnalysisWorker::setSettings(const QJsonObject &settings)
{

}

void AnalysisWorker::analysisRadarFrameEnd(quint8 radarID, const Devices::Can::CanFrame &frame, bool bEndFrame)
{
    emit analysisRadarFrameFinished( radarID, frame, bEndFrame );
}

void AnalysisWorker::analysisTargetPoint16FrameEnd(quint8 radarID, const Devices::Can::CanFrame &frame, bool bEndFrame)
{
    emit analysisTargetPoint16FrameFinished( radarID, frame, bEndFrame );
}

void AnalysisWorker::analysisLanePointFrameEnd(quint8 radarID, const Devices::Can::CanFrame &frame, bool bEndFrame)
{
    emit analysisLanePointFrameFinished( radarID, frame, bEndFrame );
}

bool AnalysisWorker::isFilterFrame(quint8 channelIdx, quint64 frameID)
{
    if( -1 != mChannelFilter.indexOf( channelIdx ) ){
        return true;
    }

    QMap< quint8, QList<quint64> >::iterator it = mFrameFilter.find( channelIdx );
    if( it != mFrameFilter.end() ){
        QList<quint64>& frameIDList = it.value();
        if( -1 != frameIDList.indexOf( frameID ) ){
            return true;
        }
    }

    return false;
}

void AnalysisWorker::radarReset( quint8 channel )
{
    mRadarResetCount ++;
    mRadarResetLastChannel = channel;
    emit radarResetCountChanged( mRadarResetCount, mRadarResetLastChannel );
}

Protocol::IAnalysisProtocol *AnalysisWorker::analysisProtocol(ProtocolType protocolType)
{
    foreach (Protocol::IAnalysisProtocol *protocl, mProtocols) {
        if (protocl->protocolType() == protocolType) {
            return protocl;
        }
    }
    return 0;
}

bool AnalysisWorker::interpolationBegin(bool injection, int radarID)
{
    qDebug() << __FUNCTION__ << __LINE__ << injection << radarID;
    mUseInterpolation = injection;
    mInterpolationRadarID = radarID;
    if (mUseInterpolation) {
        return mInterpolation->start();
    }

    return true;
}

void AnalysisWorker::interpolationEnd()
{
    mInterpolation->stop();
}

void AnalysisWorker::canFrame(const Devices::Can::CanFrame &frame)
{
    if (!frame.isRx()) {
        return;
    }
//    qDebug() << __FUNCTION__ << __LINE__ << frame.channelIndex() << frame.idHex() << frame.dataHex();
#if 1
    if (frame.id() == 0) { // 用户数据 43 54 00 01 xx xx xx
        const quint8 *data = (const quint8*)(frame.data().data());
        switch (data[3]) {
        case 0x01: // Camera save index
        {
            int cameraIndex = BIG_LITTLE_SWAP32(*(int *)(data + 4));
            long long saveIndex = BIG_LITTLE_SWAP64(*(long long *)(data + 8));
//            qDebug() << __FUNCTION__ << __LINE__ << cameraIndex << saveIndex << frame.dataHex();
            if (cameraIndex < 0 || cameraIndex >= 10) {
                return;
            }
            mCameraSaveIndex[cameraIndex] = saveIndex;
        }
            break;
        default:
            break;
        }
        return;
    } else if (frame.id() == 0x3F9) {
        qDebug() << __FUNCTION__ << __LINE__ << "message";
        const quint8 *data = (const quint8*)(frame.data().data());
        if (data[7] == 0x54) {
            emit saveResponsed(true);
        } else if (data[7] == 0x44) {
            emit saveResponsed(true);
        }
    }

    if( isFilterFrame( frame.channelIndex(), frame.id() ) ){ //判断是否过滤
        return;
    }

    if (mTrueSystemOpened && mTrueSystemProtocol->analysisFrame(frame)){
        return;
    }

    if (mUseInterpolation) {
        mInterpolation->canFrame(mInterpolationRadarID, frame);
    }

    foreach (Protocol::IAnalysisProtocol *protocl, mProtocols) {
        if (protocl->analysisFrame(frame)) {
            break;
        }
    }
#else
    for (int i = 4; i < 8; ++i)
    {
        Devices::Can::CanFrame f4 = frame;
        f4.setIDN(i);
        if (!mIAnalysisProtocol->analysisFrame(f4))
        {

        }
    }
#endif
}

void AnalysisWorker::clearRecv410FrameCount()
{
    for( int i=0; i<MAX_RADAR_COUNT; i++ ){
         mAnalysisDatas[i].clearRecv410FrameCount();
    }
}

void AnalysisWorker::clearRadarResetCount()
{
    mRadarResetCount = 0;
    mRadarResetLastChannel = 0;
    emit radarResetCountChanged( mRadarResetCount, mRadarResetLastChannel );
}

void AnalysisWorker::clearFilter()
{
    mChannelFilter.clear();
    mFrameFilter.clear();
}

void AnalysisWorker::addChannelFilter(quint8 channelIdx)
{
    mChannelFilter.push_back( channelIdx );
}

void AnalysisWorker::addFrameFilter(quint8 channelIdx, quint64 frameID)
{
    mFrameFilter[channelIdx].push_back( frameID );
}

void AnalysisWorker::heSaiFrameNumber(int number)
{
    for (int i = 4; i < 8; ++i) {
        mAnalysisDatas[i].mHeSaiLiderSaveIndex = number;
    }
}

void AnalysisWorker::clearAnalysisData()
{
    for( int i=0; i<MAX_RADAR_COUNT; i++ ){
         mAnalysisDatas[i].clear();         
    }
}

} // namespace Analysis
