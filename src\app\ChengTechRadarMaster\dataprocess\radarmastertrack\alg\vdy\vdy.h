#ifndef VDY_H
#define VDY_H
#ifndef PC_DBG_FW
#include <stdbool.h>
#include <stdint.h>
#include "rsp_types.h"
#include "vdy_types.h"
#include "rdp_types.h"
#else
#include <stdbool.h>
#include <stdint.h>
#include "hal/rsp/rsp_types.h"
#include "app/vehicle/vdy/vdy_types.h"
#include "../track/rdp_types.h"
#endif
typedef struct
{
	uint8_t actualGear;
	float time;
	float speed;
	float wheelSpeed[4];	//mps 右前轮速  左前轮速  右后轮速  左后轮速 
	float yawrate;
	float lngAcceleration;
	float latAcceleration;

	float yaw;
	float sinYaw;
	float cosYaw;

	float compSpeed;
	float curvature;
	float rearAxleSideSlip;
	float radius;

	float vcsLngVel;
	float vcsLatVel;
	float vcsSlideSlip;

	float minSpeed;
	float fastDisCons;
	float slowDisCons;
	float minKalDist;
	float rearCorneringCompliance;
	float rearDisFront;
	float rearDisRear;
	float LatDis;

	float K[2];
	float kStates[2];
	float yawEst;
	float disEst;
	bool kalInit;
}stVehicleStatus;

const stVehicleStatus *getVdyStatus(void);

void initVdyStatus(void);
void vdyProcess(const RSP_DetObjectList_t* pDetects, VDY_DynamicEstimate_t* pVehInfo, float time, rdp_config_t* config);

#endif
