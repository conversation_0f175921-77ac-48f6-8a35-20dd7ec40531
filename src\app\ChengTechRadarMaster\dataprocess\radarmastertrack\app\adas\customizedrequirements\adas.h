﻿/**
 * @file adas.h
 * @brief adas模块对外的头文件
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2022-09-27
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-09-27 <td>1.0     <td>wangjuhua     <td>初版发布
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _ADAS_TYPES_H_
#define _ADAS_TYPES_H_

#ifdef ALPSPRO_ADAS
#include "stdbool.h"
#include "vdy/vdy_types.h"
#include "rdp/track/data_process/rdp_types.h"
//#include "app_process/app_common_types.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#elif defined(PC_DBG_FW)
#include "stdbool.h"
#include "stdio.h"
#include "app/vehicle/vdy/vdy_types.h"
#include "alg/track/rdp_types.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#else
#include <stdbool.h>
#include "app/vdy/vdy_types.h"
#include "app/rdp/rdp_types.h"
#include "app/vehicle/app_common.h"
// #include "app/common/app_common.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#endif

#define REAR_LEFT_RADAR_ID      4U
#define REAR_RIGHT_RADAR_ID     5U
#define FRONT_LEFT_RADAR_ID     6U
#define FRONT_RIGHT_RADAR_ID    7U

typedef enum
{
    BSD_FUNC_STATE_OFF = 0U,
    BSD_FUNC_STATE_PASSIVE = 1U,
    BSD_FUNC_STATE_ACTIVE = 2U,
    BSD_FUNC_STATE_ERROR = 3U
} BSD_FUNC_STATE_T;

typedef enum
{
    LCA_FUNC_STATE_OFF = 0U,
    LCA_FUNC_STATE_PASSIVE = 1U,
    LCA_FUNC_STATE_ACTIVE = 2U,
    LCA_FUNC_STATE_ERROR = 3U
} LCA_FUNC_STATE_T;

typedef enum
{
    FCTA_FUNC_STATE_OFF = 0U,
    FCTA_FUNC_STATE_PASSIVE = 1U,
    FCTA_FUNC_STATE_ACTIVE = 2U,
    FCTA_FUNC_STATE_ERROR = 3U
} FCTA_FUNC_STATE_T;

typedef enum
{
    FCTB_FUNC_STATE_OFF = 0U,
    FCTB_FUNC_STATE_PASSIVE = 1U,
    FCTB_FUNC_STATE_ACTIVE = 2U,
    FCTB_FUNC_STATE_ERROR = 3U
} FCTB_FUNC_STATE_T;

typedef enum
{
    RCTA_FUNC_STATE_OFF = 0U,
    RCTA_FUNC_STATE_PASSIVE = 1U,
    RCTA_FUNC_STATE_ACTIVE = 2U,
    RCTA_FUNC_STATE_ERROR = 3U
} RCTA_FUNC_STATE_T;

typedef enum
{
    RCTB_FUNC_STATE_OFF = 0U,
    RCTB_FUNC_STATE_PASSIVE = 1U,
    RCTB_FUNC_STATE_ACTIVE = 2U,
    RCTB_FUNC_STATE_ERROR = 3U
} RCTB_FUNC_STATE_T;

typedef enum
{
    DOW_FUNC_STATE_OFF = 0U,
    DOW_FUNC_STATE_PASSIVE = 1U,
    DOW_FUNC_STATE_ACTIVE = 2U,
    DOW_FUNC_STATE_ERROR = 3U
} DOW_FUNC_STATE_T;

typedef enum
{
    FDOW_FUNC_STATE_INVAILD = 0,
    FDOW_FUNC_STATE_OFF = 1,
    FDOW_FUNC_STATE_PASSIVE = 2,
    FDOW_FUNC_STATE_ACTIVE = 3,
    FDOW_FUNC_STATE_ERROR = 4
} FDOW_FUNC_STATE_T;

typedef enum
{
    RCW_FUNC_STATE_OFF = 0U,
    RCW_FUNC_STATE_PASSIVE = 1U,
    RCW_FUNC_STATE_ACTIVE = 2U,
    RCW_FUNC_STATE_ERROR = 3U
} RCW_FUNC_STATE_T;

typedef enum
{
    RCTB_WARNING_OFF = 0U,
    RCTB_WARNING_LEFT = 1U,
    RCTB_WARNING_RIGHT = 2U,
    RCTB_WARNING_LEFT_RIGHT = 3U
} RCTB_WARNING_LR_T;

typedef enum
{
    FUNC_STATE_OFF = 0,
    FUNC_STATE_PASSIVE = 1,
    FUNC_STATE_ACTIVE = 2,
    FUNC_STATE_ERROR = 3
} ADAS_FUNC_STATE_T;

typedef enum
{
    ADAS_NO_WARNING = 0U,
    ADAS_WARNING_LEVEL1 = 1U,
    ADAS_WARNING_LEVEL2 = 2U
} ADAS_FUNC_WARNING_T;

typedef enum
{
    DTC_SYSERROR_NULL = 0U,   // 无故障
    DTC_SYSERROR_SYSTEM = 1U, // 系统故障
    DTC_SYSERROR_SDA = 2U     // 未标定故障
} DTC_SYS_STATUS;

typedef enum
{
    NO_BREAK = 0U,   // 无制动
    FULL_BREAK = 1U, // 全力减速
    HOLD_BREAK = 2U  // 保压
} ADAS_BREAK_STATE_T;

typedef struct
{
    uint32_t adasTimeCnt; // 2^32*50/1000/60/60/24/365=6.8 year
    float trackTime;
} ADAS_TimeClase_t;

typedef struct
{
    uint8_t radarId;
    float installAzimuthAngle;
    uint8_t hilMode;
} ADAS_RadarConfiguration_t;

typedef struct
{
    /// @brief 接口版本号
    uint16_t versionNumber;

    /// @brief 信号header
    SignalHeader_t signalHeader;

    /**
     * @brief BSD报警状态
     *
     * @details -0x0=Warning off
     *          -0x1=Warning Normal,
     *          -0x2=Warning Blinking
     *          -0x3~0xFF=Reserved
     */
    uint8_t adasBSDWarning;

    /**
     * @brief BSD功能状态机
     *
     * @details new 0x0：OFF
     *               0x1：Passive
     *               0x2：Active
     *               0x3：fault
     *      old，not used{-0x0=OFF，
     *          -0x1=ON,
     *          -0x2=Active，
     *          -0x3=Passive，
     *          -0x4=Disabled，
     *          -0x5=Error，
     *          -0x6~0xFF=Reserved}
     */
    uint8_t adasBSDFuncState;

    /**
     * @brief BSD模块报警的目标
     * @details 如果存在多个目标，选择距离最近的目标上报
     */
    uint8_t adasBSDAlarmObjID;

    /**
     * @brief LCA报警状态
     *
     * @details -0x0=Warning off
     *          -0x1=Warning Normal,
     *          -0x2=Warning Blinking
     *          -0x3~0xFF=Reserved
     */
    uint8_t adasLCAWarning;

    uint8_t adasLCAFuncState;

    /**
     * @brief LCA模块报警的目标
     * @details 如果存在多个目标，选择TTC最小的目标上报
     */
    uint8_t adasLCAAlarmObjID;

    /**
     * @brief DOW报警状态
     *
     * @details -0x0=Warning off
     *          -0x1=Warning Normal,
     *          -0x2=Warning Blinking
     *          -0x3~0xFF=Reserved
     */
    uint8_t adasDOWWarning;

    uint8_t adasDOWFuncState;

    /**
     * @brief DOW模块报警的目标
     * @details 如果存在多个目标，选择TTC最小的目标上报
     */
    uint8_t adasDOWAlarmObjID;

    /**
     * @brief RCW报警状态
     *
     * @details -0x0=Warning off
     *          -0x1=Warning Normal,
     *          -0x2=Warning Blinking
     *          -0x3~0xFF=Reserved
     */
    uint8_t adasRCWWarning;

    uint8_t adasRCWFuncState;

    /**
     * @brief RCW模块报警的目标
     * @details 如果存在多个目标，选择TTC最小的目标上报
     */
    uint8_t adasRCWAlarmObjID;

    /**
     * @brief RCTA报警状态
     *
     * @details -0x0=Warning off
     *          -0x1=Warning Normal,
     *          -0x2=Warning Blinking
     *          -0x3~0xFF=Reserved
     */
    uint8_t adasRCTAWarning;

    uint8_t adasRCTAFuncState;

    /**
     * @brief RCTA模块报警的目标
     * @details 如果存在多个目标，选择TTC最小的目标上报
     */
    uint8_t adasRCTAAlarmObjID;

    /**
     * @brief RCTB报警状态
     *
     * @details -0x0=No Demand
     *          -0x1=Demand,
     *          -0x2~0xFF=Reserved
     */
    uint8_t adasRCTBWarning;

    /**
     * @brief RCTB功能状态机
     * @details new 0x0：OFF
     *               0x1：Passive
     *               0x2：Active
     *               0x3：fault
     *      old，not used{-0x0=OFF
     *          -0x1=ON,
     *          -0x2=Active，
     *          -0x3=Passive，
     *          -0x4=Disabled，
     *          -0x5=Error，
     *          -0x6=Brake Hold，
     *          -0x7~0xFF=Reserved}
     */
    uint8_t adasRCTBFuncState;

    /**
     * @brief RCTB模块报警的目标
     * @details 如果存在多个目标，选择TTC最小目标上报
     */
    uint8_t adasRCTBAlarmObjID;

    /**
     * @brief FCTA报警状态
     *
     * @details -0x0=Warning off
     *          -0x1=Warning Normal,
     *          -0x2=Warning Blinking
     *          -0x3~0xFF=Reserved
     */
    uint8_t adasFCTAWarning;

    uint8_t adasFCTAFuncState;


    /**
     * @brief FCTA模块报警的目标
     * @details 如果存在多个目标，选择TTC最小目标上报
     */
    uint8_t adasFCTAAlarmObjID;

    /**
     * @brief FCTB报警状态
     *
     * @details -0x0=No Demand
     *          -0x1=Demand,
     *          -0x2~0xFF=Reserved
     */
    uint8_t adasFCTBWarning;

    uint8_t adasFCTBFuncState;

    /**
     * @brief FCTB模块报警的目标
     * @details 如果存在多个目标，选择TTC最小目标上报
     */
    uint8_t adasFCTBAlarmObjID;

    /**
     * @brief ELKA_OT报警状态
     *
     * @details -0x0=Warning off
     *          -0x1=Warning Normal,
     */
    uint8_t adasELKAOTWarning;

    float adasLCATtc;

    float adasDOWTtc;

    float adasRCWTtc;

    float adasRCTATtc;

    float adasFCTATtc;

    float adasRCTBTtc;

    float adasFCTBTtc;

    float adasELTAOTTtc;

    uint8_t adasAlmFlag;

    uint8_t adasRCTBState;  //0:无制动请求；1:减速；2、保压；

    uint8_t adasFCTBState;  //0:无制动请求；1:减速；2、保压；

    uint8_t adasRCTBRequest;  //0:没有请求；1:左雷达发送请求; 2:右雷达发送请求；3:左右同时发送请求

    uint8_t adasFCTBRequest;  //0:没有请求；1:左雷达发送请求; 2:右雷达发送请求；3:左右同时发送请求

    uint8_t adasRTCBStatehold;  // 触发RTCB刹停后, 状态机passive 需保压两秒

    uint8_t adasFTCBStatehold;  // 触发FTCB刹停后, 状态机passive 需保压两秒
} ADAS_FunctionState_t;

typedef struct
{
    float M_DDCI;
    float M_IntersectionAngle;
    float M_TTM;
    float ObjDx;
    float ObjDy;
    float ObjVx;
    float ObjVy;
    float ObjAx;
    float ObjAy;
    uint8_t ObjCntAlive;    //目标出现时长
    uint8_t ObjMotionPattern;   //目标运动类型
    uint8_t ObjExist;   //目标存在置信度，0~100, unit: percentage
    uint8_t ObjObstcl;  //障碍物可能性，未实现，默认值为0
    uint8_t FCTBObjClassification;
} ADAS_EDRData_t;    //EDR中需要存储的数据结构体,参照BYD行泊车一体项目

/**
 * @brief 左雷达报警状态；左雷达发给右雷达，然后发给车端的和结构体CMRR_RL_0x66E一样的信息
 */
typedef struct
{
    uint64_t radarSystemState   : 2;
    uint64_t ELKA_WarningLeft   : 1;
    uint64_t resv0              : 5;
    uint64_t radarBSDWarningL   : 2;
    uint64_t resv1              : 2;
    uint64_t radarRCTAWarningL  : 1;
    uint64_t resv2              : 3;
    uint64_t radarBSDStatusL    : 2;
    uint64_t radarRCTAStatusL   : 2;
    uint64_t radarRcwStatusL    : 2;
    uint64_t radarDowStatusL    : 2;
    uint64_t radarDOWWarningL   : 2;
    uint64_t resv3              : 2;
    uint64_t radarRCWWarningL   : 2;
    uint64_t radarRCTBBrakingL  : 1; ///<0x0：Waringoff, 0x1：Waringon
    uint64_t resv4              : 1;
    uint64_t radarRCTBStatusL   : 2;
    uint64_t radarRCTBBrakingReq: 2;
    uint64_t resv5              : 4;
    uint64_t rctbTrgtDclL       : 8;
    uint64_t resv6              : 4;
    uint64_t msgCounter         : 4;
    // uint64_t checkSum           : 8;
    uint64_t radarFCTBBrakingL  : 2;
    uint64_t radarFCTBStatusL   : 2;
    uint64_t radarFCTAWarningL  : 2;
    uint64_t radarFCTAStatusL   : 2;
    uint64_t FLRadarDowSysSts   : 2;    /* 0x0:Off, 0x1:Passive, 0x2:Active, 0x3:Fault */
    uint64_t FLRadarDowWarning  : 2;    /* 0x0:NO Warning, 0x1:Warning Level1, 0x2:Warning Level2, 0x3:Reserved */
    uint64_t resv7              : 4;
} SlaveRadarWarningsStatus;

typedef struct{
    float acData[CENTERX_BUFFER_NUM];
    uint8_t positiveNum;        // 转弯半径正值计数
    uint8_t negativeNum;        // 转弯半径负值计数
    uint8_t nearpositiveNum;    // 最近几帧正值计数
    uint8_t nearnegativeNum;    // 最近几帧负值计数
    uint8_t posnegsymbol;       // 正负符号 0负 1正 标识转弯半径的左右
    uint32_t turnovercnt;        // 弯道计数
    uint32_t turncnt;           // 弯道计数
    bool turnflag;              // 是否正在转弯  0 否 1 是
}Centerx_t;
typedef struct
{
    uint32_t FCTA : 1; //0：No fault，1：fault 
    uint32_t FCTB : 1;
    uint32_t RCTA : 1;
    uint32_t RCTB : 1;
    uint32_t LCAandBSD : 1;
    uint32_t DOW : 1;
    uint32_t RCW : 1;
    uint32_t ELK : 1;

    uint32_t reserve : 24;
} ADAS_FaultList_t; //ADAS故障清单
typedef enum
{
    NO_FAULT   = 0U,   //无故障
    FAULT   = 1U,   //故障
} ADAS_FAULT_STATE_T;

extern const SlaveRadarWarningsStatus* get_LeftRadarWarnInfo(void);

/**
 * @brief 清除相关报警信息
 *
 */
void ADAS_clearMainFunction(void);
void ADAS_setclrFunctionState(const ADAS_TimeClase_t timeClass);

/**
 * @brief 初始化adas参数
 * 
 */
void ADAS_initParams(void);

/**
 * @brief adas主函数，通过输入各类信息来得出报警结果
 * 
 * @param RDP_TrkObjectListAddr 跟踪点列表 
 * @param pVDY 车身信息
 * @param pCFG 配置信息
 * @param pSlaveRadar 从雷达信息
 * @param SystemState 系统状态
 * @param trackTime 帧间隔（单位：s）
 */
void ADAS_runMainFunction(const RDP_TrkObjectList_t *RDP_TrkObjectListAddr,
                          const VDY_Info_t *pVDY,
                          const ADAS_RadarConfiguration_t *pCFG,
                          const SlaveRadarWarningsStatus *pSlaveRadar,
                          const uint8_t SystemState,
                          float trackTime);
/**
 * @brief 为确保ADAS内部的一致性,在ADAS外部冻结数据
 * @param pVDY 
 */
void ADAS_freezingParam(VDY_Info_t *pVDY, float trackTime);

/**
 * @brief 冻结数据的释放
 * @param pVDY 
 */
void ADAS_releaseFreezingParam(VDY_Info_t *pVDY);

/**
 * @brief adas运行结果，包含功能状态，报警等级，目标id及ttc等
 * 
 * @return const ADAS_FunctionState_t* 
 */
const ADAS_FunctionState_t *ADAS_getAdasDataPointer();

/**
 * @brief 获取弯道检测相关
 * @return const Centerx_t* 
 */
const Centerx_t *ADAS_getCenterx(void);

/**
 * @brief 道路描述结果，当前主要是输出边线位置
 * 
 * @return const RDP_roadDescription_t* 
 */
const RDP_roadDescription_t* RDP_getRoadDescriptionPointer();

/**
 * @brief EDR所需数据结果
 * 
 * @return const ADAS_EDRData_t* 
 */
const ADAS_EDRData_t *ADAS_getEDRData();

const ADAS_FaultList_t *getDiagFaultInfoPtr(void);

ADAS_RadarConfiguration_t *ADAS_getRadarCongiguration(void);

#endif
