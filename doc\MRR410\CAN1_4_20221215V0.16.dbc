VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: ConfigPC SRR_RR SRR_RL SRR


BO_ ********** VECTOR__INDEPENDENT_SIG_MSG: 0 Vector__XXX
 SG_ RL_RL_Interference_ERR : 0|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ RL_AlarmBsdObjTtc : 0|8@0+ (0.02,0) [0|5.1] "" Vector__XXX
 SG_ RL_RawObjAlt : 0|9@0+ (0.05,-12.8) [-12.8|12.75] "m" Vector__XXX
 SG_ RL_SensorMisAlnErr : 0|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ RL_PCANTxFailToRestartErr : 0|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ RL_VCANTxFailToRestartErr : 0|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ RL_RFModuleAbnormalErr : 0|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ RL_RawObjectRollingCnt04 : 0|3@0+ (1,0) [0|7] "" Vector__XXX
 SG_ RL_RawObjectRollingCnt03 : 0|3@0+ (1,0) [0|7] "" Vector__XXX
 SG_ RL_RawObjectRollingCnt02 : 0|3@0+ (1,0) [0|7] "" Vector__XXX
 SG_ RL_EndFrameSpeedMode : 0|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_RawHeaderChecksum : 0|8@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_EndFrameObjNumAfterCFAR : 0|8@0+ (1,0) [0|255] "" Vector__XXX
 SG_ RL_EndFrameObjNumAfterFilter : 0|8@0+ (1,0) [0|255] "" Vector__XXX

BO_ 1012 SRR_RL_VehicleInfo01: 32 Vector__XXX
 SG_ Veh_Radius : 199|18@0+ (0.25,-32767) [-32767|32768.75] "m" Vector__XXX
 SG_ Veh_SteerWheelAngle : 160|13@0+ (0.2,-738) [-738|900.2] "deg" Vector__XXX
 SG_ Veh_WhlSpdDirRearRi : 56|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_WhlSpdDirRearLe : 57|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_WhlSpdDirFrontRi : 58|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_WhlSpdDirFrontLe : 59|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_WhlSpdRearRi : 157|13@0+ (0.05625,0) [0|460.74375] "km/h" Vector__XXX
 SG_ Veh_WhlSpdRearLe : 138|13@0+ (0.05625,0) [0|460.74375] "km/h" Vector__XXX
 SG_ Veh_WhlSpdFrontRi : 135|13@0+ (0.05625,0) [0|460.74375] "km/h" Vector__XXX
 SG_ Veh_WhlSpdFrontLe : 119|13@0+ (0.05625,0) [0|460.74375] "km/h" Vector__XXX
 SG_ Veh_TrailerSts : 73|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_LgtAccel : 87|16@0+ (0.00098,-21.592) [-21.592|42.6323] "m/s^2" Vector__XXX
 SG_ Veh_LatAccel : 103|16@0+ (0.00098,-21.592) [-21.592|42.6323] "m/s^2" Vector__XXX
 SG_ Veh_ESPFailSts : 72|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_BrkPedalSts : 61|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Veh_BrkPedalActLevel : 64|7@0+ (1,0) [0|100] "" Vector__XXX
 SG_ Veh_AccPedalActLevel : 71|7@0+ (1,0) [0|100] "%" Vector__XXX
 SG_ RL_Veh_SwFctbFunc : 16|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SwFctaFunc : 17|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SecurityLock : 51|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ Veh_KeyState : 55|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RollingCntMsgVehInfo01 : 179|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ ChecksumMsgVehInfo01 : 255|8@0+ (1,0) [0|255] "/" Vector__XXX
 SG_ RL_Veh_YawRate : 7|16@0+ (0.00024,-2.093) [-2.093|2.093] "rad/s" Vector__XXX
 SG_ Veh_TurnLightRi : 62|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_TurnLightLe : 63|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SwRcwFunc : 21|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SwRctbFunc : 22|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SwRctaFunc : 23|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SwMainFunc : 18|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SwDowFunc : 19|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SwBSDFunc : 20|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_Speed : 31|13@0+ (0.05625,-230.4) [-230.4|230.34375] "km/h" Vector__XXX
 SG_ RL_Veh_Gear : 42|3@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_Veh_DoorRearRi : 44|2@0+ (1,0) [0|1] "" Vector__XXX
 SG_ RL_Veh_DoorRearLe : 46|2@0+ (1,0) [0|1] "" Vector__XXX
 SG_ RL_Veh_DoorFrontRi : 32|2@0+ (1,0) [0|1] "" Vector__XXX
 SG_ RL_Veh_DoorFrontLe : 34|2@0+ (1,0) [0|1] "" Vector__XXX

BO_ 1264 SRR_RL_EndFrame: 24 Vector__XXX
 SG_ RL_EndFrameMeasCnt : 103|32@0+ (1,0) [0|65535] "" Vector__XXX
 SG_ RL_EndFrameFuncCalcTime : 47|9@0+ (1,0) [0|511] "" Vector__XXX
 SG_ RL_EndFrameTimeTick : 71|32@0+ (1,0) [0|4294967295] "ms" Vector__XXX
 SG_ RL_EndFrameRoadSideDist : 53|10@0+ (0.05,-25) [-25|26.15] "m" Vector__XXX
 SG_ RL_EndFrameAutoCalAngleOffset : 20|11@0+ (0.1,-102.4) [-102.4|102.3] "" Vector__XXX
 SG_ RL_EndFrameRollingCnt : 59|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_EndFrameInterTime : 39|8@0+ (1,0) [0|255] "ms" Vector__XXX
 SG_ RL_EndFrameCheckSum : 7|8@0+ (1,0) [0|255] "" Vector__XXX
 SG_ RL_EndFrameEOLInstallAngle : 15|11@0+ (0.1,-102.4) [-102.4|102.3] "" Vector__XXX

BO_ 1024 SRR_RL_RawHeader: 16 Vector__XXX
 SG_ RL_RawHeaderFrameModeNum : 75|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_RawHeaderCurrentFrameModeIdx : 79|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_RawHeaderDataSource : 50|3@0+ (1,0) [0|7] "" Vector__XXX
 SG_ RL_RawHeaderObjNumAfterFilter : 71|8@0+ (1,0) [0|255] "" Vector__XXX
 SG_ RL_RawHeaderObjNumAfterCFAR : 63|8@0+ (1,0) [0|255] "" Vector__XXX
 SG_ RL_RawHeaderNoiseGlobal : 23|8@0+ (0.5,-100) [-100|27.5] "db" Vector__XXX
 SG_ RL_RawHeaderNoiseCurrent : 15|8@0+ (0.5,-100) [-100|27.5] "db" Vector__XXX
 SG_ RL_RawHeaderFuncCalcTime : 37|9@0+ (1,0) [0|511] "ms" Vector__XXX
 SG_ RL_RawHeaderRspTaskCycleTime : 44|9@0+ (1,0) [0|511] "ms" Vector__XXX
 SG_ RL_RawHeaderRollingCnt : 123|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_RawHeaderProtVer : 7|8@0+ (1,0) [0|255] "" Vector__XXX
 SG_ RL_RawHeaderObjNum : 31|10@0+ (1,0) [0|255] "" Vector__XXX

BO_ 1040 SRR_RL_RawDetections: 64 Vector__XXX
 SG_ RL_RawObjectGroupID04 : 489|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectGroupID03 : 361|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectGroupID02 : 233|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectGroupID01 : 105|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectAssociatedTrkId04 : 480|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectAssociatedTrkId03 : 352|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectAssociatedTrkId02 : 224|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectAssociatedTrkId01 : 96|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectSubFrameID04 : 484|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_RawObjectSubFrameID03 : 356|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_RawObjectSubFrameID02 : 228|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_RawObjectDopplerVelocity04 : 477|9@0+ (1,0) [0|511] "m/s" Vector__XXX
 SG_ RL_RawObjectDopplerVelocity03 : 349|9@0+ (1,0) [0|511] "m/s" Vector__XXX
 SG_ RL_RawObjectDopplerVelocity02 : 221|9@0+ (1,0) [0|511] "m/s" Vector__XXX
 SG_ RL_RawObjectStatus04 : 471|10@0+ (1,0) [0|1023] "" Vector__XXX
 SG_ RL_RawObjectStatus03 : 343|10@0+ (1,0) [0|1023] "" Vector__XXX
 SG_ RL_RawObjectStatus02 : 215|10@0+ (1,0) [0|1023] "" Vector__XXX
 SG_ RL_RawObjectSNR04 : 463|8@0+ (0.5,0) [0|127.5] "dB" Vector__XXX
 SG_ RL_RawObjectSNR03 : 335|8@0+ (0.5,0) [0|127.5] "dB" Vector__XXX
 SG_ RL_RawObjectSNR02 : 207|8@0+ (0.5,0) [0|127.5] "dB" Vector__XXX
 SG_ RL_RawObjectRCS04 : 455|8@0+ (0.5,0) [0|127.5] "dbm^2" Vector__XXX
 SG_ RL_RawObjectRCS03 : 327|8@0+ (0.5,0) [0|127.5] "dbm^2" Vector__XXX
 SG_ RL_RawObjectRCS02 : 199|8@0+ (0.5,0) [0|127.5] "dbm^2" Vector__XXX
 SG_ RL_RawObjectProbOfExist04 : 446|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectProbOfExist03 : 318|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectProbOfExist02 : 190|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectElevationAngle04 : 427|13@0+ (0.02,-81.92) [-81.92|81.9] "deg" Vector__XXX
 SG_ RL_RawObjectElevationAngle03 : 299|13@0+ (0.02,-81.92) [-81.92|81.9] "deg" Vector__XXX
 SG_ RL_RawObjectElevationAngle02 : 171|13@0+ (0.02,-81.92) [-81.92|81.9] "deg" Vector__XXX
 SG_ RL_RawObjectVelocity04 : 409|14@0+ (0.01,-81.92) [-81.92|81.91] "m/s" Vector__XXX
 SG_ RL_RawObjectVelocity03 : 281|14@0+ (0.01,-81.92) [-81.92|81.91] "m/s" Vector__XXX
 SG_ RL_RawObjectVelocity02 : 153|14@0+ (0.01,-81.92) [-81.92|81.91] "m/s" Vector__XXX
 SG_ RL_RawObjectAzimuth04 : 392|15@0+ (0.01,-163.84) [-163.84|163.83] "deg" Vector__XXX
 SG_ RL_RawObjectAzimuth03 : 264|15@0+ (0.01,-163.84) [-163.84|163.83] "deg" Vector__XXX
 SG_ RL_RawObjectAzimuth02 : 136|15@0+ (0.01,-163.84) [-163.84|163.83] "deg" Vector__XXX
 SG_ RL_RawObjectRange04 : 391|15@0+ (0.01,0) [0|327.67] "m" Vector__XXX
 SG_ RL_RawObjectRange03 : 263|15@0+ (0.01,0) [0|327.67] "m" Vector__XXX
 SG_ RL_RawObjectRange02 : 135|15@0+ (0.01,0) [0|327.67] "m" Vector__XXX
 SG_ RL_RawObjectDopplerVelocity01 : 93|9@0+ (1,0) [0|511] "m/s" Vector__XXX
 SG_ RL_RawObjectSubFrameID01 : 100|4@0+ (1,0) [0|7] "" Vector__XXX
 SG_ RL_RawObjectStatus01 : 87|10@0+ (1,0) [0|1023] "" Vector__XXX
 SG_ RL_RawObjectProbOfExist01 : 62|7@0+ (1,0) [0|127] "" Vector__XXX
 SG_ RL_RawObjectElevationAngle01 : 43|13@0+ (0.02,-81.92) [-81.92|81.9] "deg" Vector__XXX
 SG_ RL_RawObjectRollingCnt01 : 506|3@0+ (1,0) [0|15] "" Vector__XXX
 SG_ RL_RawObjectSNR01 : 79|8@0+ (0.5,0) [0|127.5] "dB" Vector__XXX
 SG_ RL_RawObjectRCS01 : 71|8@0+ (0.5,-30) [-30|97.5] "dbm^2" Vector__XXX
 SG_ RL_RawObjectAzimuth01 : 8|15@0+ (0.01,-163.84) [-163.84|163.83] "deg" Vector__XXX
 SG_ RL_RawObjectVelocity01 : 25|14@0+ (0.01,-81.92) [-81.92|81.91] "m/s" Vector__XXX
 SG_ RL_RawObjectRange01 : 7|15@0+ (0.01,0) [0|327.67] "m" Vector__XXX

BO_ 516 PC_RL_Dbg_Command: 8 Vector__XXX
 SG_ Cmd_workMode : 3|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ Cmd_vehicleInfoSel : 31|4@0+ (1,0) [0|15] "" Vector__XXX
 SG_ Cmd_sendYawRateMode : 9|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Cmd_sendExtInfo2 : 21|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Cmd_sendExtInfo : 15|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Cmd_outputMode : 7|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Cmd_estimatedSpeedCfgExpand : 13|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Cmd_dbgMode : 19|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Cmd_cohesionMode : 17|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Cmd_carVelMode : 5|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Cmd_canBaudrateMode : 23|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Cmd_angleMapperMode : 11|2@0+ (1,0) [0|0] "" Vector__XXX

BO_ 1232 SRR_RL_ErrorTestCurrent: 16 SRR_RL
 SG_ ERR_PMICReset : 39|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_Interference : 37|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_IMUCommFlt : 38|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PPARFlt : 35|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_AddrLineUnstab : 34|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_Blockage : 36|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_InputVoltNotCalib : 40|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_I2CComm : 33|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED2ShortToGND : 30|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED2OpenCircurt : 31|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED2NotCalib : 29|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM911ChirpMonitChk : 101|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM910OverTemp : 100|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM908IRLpInput : 99|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM907IFLpInput : 98|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM906IFLpSat : 97|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM204SwCrcForI2C : 90|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM905RFPower : 96|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM904BandgapVoltMonit : 95|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM901LdoTest : 94|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM805CfgRegRead : 93|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ RollingCntMsg0x494 : 123|4@0+ (1,0) [0|15] "/"  ConfigPC
 SG_ ChecksumMsg0x494 : 7|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ ERR_SM207ExtrFlashCrc : 92|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM107CpuRomEcc : 83|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM108OtpEcc : 84|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM109SRamEcc : 85|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM129CfgRegProt : 86|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM130FmcwChirp : 87|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM133XIPSRamEcc : 88|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM201VGAGainCheck : 89|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM206ExternalWd : 91|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM13Chirp : 75|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM14FmcwOverTemp : 76|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM101BasebandLbist : 77|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM102CpuTcmEcc : 78|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM103CpuCacheEcc : 79|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM104BBSRamEcc : 80|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM105BBRomEcc : 81|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM106CpuRamEcc : 82|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM4BandGapVoltage : 67|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM5CPUClockLock : 68|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM6RFPower : 69|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM8TiaOverSaturation : 70|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM9Vga1OverSaturation : 71|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM10Vga2OverSaturation : 72|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM11IfException : 73|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM12RfException : 74|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_TimeSync : 41|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SWHWCompatCheck : 42|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SensorPosDect : 44|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SensorBlind : 43|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM1LDO : 64|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM2AVDD33Power : 65|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM3DVDD11Power : 66|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_TimeGapOfDataProc : 22|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_TimeGapOfSignalProc : 23|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED1ShortToGND : 27|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED1OpenCircurt : 28|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED1NotCalib : 26|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_ChipSafetyFail : 32|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_AbnormalInterrupt : 21|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_WDogReset : 20|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_VcanSendTimeout : 24|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PcanSendTimeout : 25|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_MmicInitProc : 18|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PMICVoltFlt : 19|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PCANLostCom : 15|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_IntMemoryFlt : 16|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_MmicInitParamSet : 17|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_NotALN : 14|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_UnderTemp : 13|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_OverTemp : 12|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_VBATLow : 11|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_VBATHigh : 10|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PCANBusoff : 9|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_VCANBusoff : 8|1@0+ (1,0) [0|1] ""  ConfigPC

BO_ 2020 PC_RL_CfgReq: 8 ConfigPC
 SG_ RL_CfgReq_0x7E4 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  SRR_RL

BO_ 308 PC_RL_RadarCfg06: 8 ConfigPC
 SG_ Sig_RadarInfo : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  SRR_RL

BO_ 2036 PC_RL_CfgResp: 8 SRR_RL
 SG_ RL_CfgResp_0x7F4 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  SRR_RL

BO_ 1216 SRR_RL_AlarmObjInfo: 24 SRR_RL
 SG_ RL_AlarmFCTBState : 165|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ RL_AlarmFCTAState : 167|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ RL_AlarmRCWState : 163|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ RL_AlarmRCTBState : 153|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ RL_AlarmRCTAState : 155|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ RL_AlarmDOWState : 157|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ RL_AlarmLCAState : 159|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ RL_AlarmBsdState : 145|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ RL_AlarmFctaLevel : 147|2@0+ (1,0) [0|3] ""  ConfigPC
 SG_ RL_AlarmRctaLevel : 149|2@0+ (1,0) [0|3] ""  ConfigPC
 SG_ RL_AlarmRcwLevel : 151|2@0+ (1,0) [0|3] ""  ConfigPC
 SG_ RL_AlarmDowLevelRear : 137|2@0+ (1,0) [0|3] ""  ConfigPC
 SG_ RL_AlarmDowLevelFront : 139|2@0+ (1,0) [0|3] ""  ConfigPC
 SG_ RL_AlarmLcaLevel : 141|2@0+ (1,0) [0|3] ""  ConfigPC
 SG_ RL_AlarmBsdLevel : 143|2@0+ (1,0) [0|3] ""  ConfigPC
 SG_ RL_AlarmFctbObjTtc : 135|8@0+ (0.02,0) [0|5.1] ""  ConfigPC
 SG_ RL_AlarmFctaObjTtc : 119|8@0+ (0.02,0) [0|5.1] ""  ConfigPC
 SG_ RL_AlarmRctbObjTtc : 103|8@0+ (0.02,0) [0|5.1] ""  ConfigPC
 SG_ RL_AlarmRctaObjTtc : 87|8@0+ (0.02,0) [0|5.1] ""  ConfigPC
 SG_ RL_AlarmRcwObjTtc : 71|8@0+ (0.02,0) [0|5.1] ""  ConfigPC
 SG_ RL_AlarmDowObjTtc : 55|8@0+ (0.02,0) [0|5.1] ""  ConfigPC
 SG_ RL_AlarmLcaObjTtc : 39|8@0+ (0.02,0) [0|5.1] ""  ConfigPC
 SG_ RL_DrvFunc_AlarmModule : 15|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_AlarmFctbObjID : 127|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_AlarmFctaObjID : 111|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_AlarmRctbObjID : 95|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_AlarmRctaObjID : 79|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_AlarmRcwObjID : 63|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_AlarmDowObjID : 47|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_AlarmLcaObjID : 31|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_AlarmBsdObjID : 23|8@0+ (1,0) [0|255] ""  ConfigPC

BO_ 772 PC_RL_DBG_SetRadarMode: 8 ConfigPC
 SG_ DBG_SetModeSystemMode : 31|2@0+ (1,0) [0|16777215] "" Vector__XXX
 SG_ DBG_SetModePassword : 7|24@0+ (1,0) [0|16777215] ""  SRR_RL

BO_ 1168 SRR_RL_RadarInfo5: 16 SRR_RL
 SG_ RR_VerSwByte3 : 95|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerSwByte2 : 87|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerSwByte1 : 79|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerSwByte0 : 71|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerHwByte3 : 63|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerHwByte2 : 55|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerHwByte1 : 47|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerHwByte0 : 39|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerBootByte3 : 31|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerBootByte2 : 23|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerBootByte1 : 15|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RR_VerBootByte0 : 7|8@0+ (1,0) [0|255] "/"  ConfigPC

BO_ 1152 SRR_RL_RadarInfo4: 16 SRR_RL
 SG_ RR_DrvFunc_BeepState : 9|2@0+ (1,0) [0|1] ""  ConfigPC
 SG_ RR_DrvFunc_AlarmModule : 23|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RR_Sys_VcanConnected : 124|1@0+ (1,0) [0|1] "kph"  ConfigPC
 SG_ RR_Sys_SystemState : 13|4@0+ (1,0) [0|15] "kph"  ConfigPC
 SG_ RR_DrvFunc_LedState : 15|2@0+ (1,0) [0|1] ""  ConfigPC

BO_ 1008 SRR_VehicleInfo01: 32 SRR
 SG_ Veh_Radius : 199|18@0+ (0.25,-32767) [-32767|32768.75] "m" Vector__XXX
 SG_ Veh_SteerWheelAngle : 160|13@0+ (0.2,-738) [-738|900.2] "deg" Vector__XXX
 SG_ Veh_WhlSpdDirRearRi : 56|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_WhlSpdDirRearLe : 57|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_WhlSpdDirFrontRi : 58|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_WhlSpdDirFrontLe : 59|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_WhlSpdRearRi : 157|13@0+ (0.05625,0) [0|460.74375] "km/h" Vector__XXX
 SG_ Veh_WhlSpdRearLe : 138|13@0+ (0.05625,0) [0|460.74375] "km/h" Vector__XXX
 SG_ Veh_WhlSpdFrontRi : 135|13@0+ (0.05625,0) [0|460.74375] "km/h" Vector__XXX
 SG_ Veh_WhlSpdFrontLe : 119|13@0+ (0.05625,0) [0|460.74375] "km/h" Vector__XXX
 SG_ Veh_TrailerSts : 73|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_LgtAccel : 87|16@0+ (0.00098,-21.592) [-21.592|42.6323] "m/s^2" Vector__XXX
 SG_ Veh_LatAccel : 103|16@0+ (0.00098,-21.592) [-21.592|42.6323] "m/s^2" Vector__XXX
 SG_ Veh_ESPFailSts : 72|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_BrkPedalSts : 61|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ Veh_BrkPedalActLevel : 64|7@0+ (1,0) [0|100] "" Vector__XXX
 SG_ Veh_AccPedalActLevel : 71|7@0+ (1,0) [0|100] "%" Vector__XXX
 SG_ Veh_SwFctbFunc : 16|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SwFctaFunc : 17|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ Veh_SecurityLock : 51|4@0+ (1,0) [0|15] ""  SRR_RR
 SG_ Veh_KeyState : 55|4@0+ (1,0) [0|15] ""  SRR_RR
 SG_ RollingCntMsgVehInfo01 : 179|4@0+ (1,0) [0|15] ""  SRR_RR
 SG_ ChecksumMsgVehInfo01 : 255|8@0+ (1,0) [0|255] "/"  SRR_RR
 SG_ Veh_YawRate : 7|16@0+ (0.00024,-2.093) [-2.093|2.093] "rad/s"  SRR_RR
 SG_ Veh_TurnLightRi : 62|1@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_TurnLightLe : 63|1@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_SwRcwFunc : 21|1@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_SwRctbFunc : 22|1@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_SwRctaFunc : 23|1@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_SwMainFunc : 18|1@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_SwDowFunc : 19|1@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_SwBSDFunc : 20|1@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_Speed : 31|13@0+ (0.05625,-230.4) [-230.4|230.34375] "km/h"  SRR_RR
 SG_ Veh_Gear : 42|3@0+ (1,0) [0|15] ""  SRR_RR
 SG_ Veh_DoorRearRi : 44|2@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_DoorRearLe : 46|2@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_DoorFrontRi : 32|2@0+ (1,0) [0|1] ""  SRR_RR
 SG_ Veh_DoorFrontLe : 34|2@0+ (1,0) [0|1] ""  SRR_RR

BO_ 1248 SRR_RL_ErrorTestConfirm: 16 SRR_RL
 SG_ ERR_PMICReset : 39|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_Interference : 37|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_IMUCommFlt : 38|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PPARFlt : 35|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_AddrLineUnstab : 34|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_Blockage : 36|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_InputVoltNotCalib : 40|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_I2CComm : 33|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED2ShortToGND : 30|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED2OpenCircurt : 31|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED2NotCalib : 29|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM911ChirpMonitChk : 101|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM910OverTemp : 100|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM908IRLpInput : 99|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM907IFLpInput : 98|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM906IFLpSat : 97|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM204SwCrcForI2C : 90|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM905RFPower : 96|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM904BandgapVoltMonit : 95|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM901LdoTest : 94|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM805CfgRegRead : 93|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ RollingCntMsg0x494 : 123|4@0+ (1,0) [0|15] "/"  ConfigPC
 SG_ ChecksumMsg0x494 : 7|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ ERR_SM207ExtrFlashCrc : 92|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM107CpuRomEcc : 83|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM108OtpEcc : 84|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM109SRamEcc : 85|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM129CfgRegProt : 86|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM130FmcwChirp : 87|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM133XIPSRamEcc : 88|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM201VGAGainCheck : 89|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM206ExternalWd : 91|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM13Chirp : 75|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM14FmcwOverTemp : 76|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM101BasebandLbist : 77|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM102CpuTcmEcc : 78|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM103CpuCacheEcc : 79|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM104BBSRamEcc : 80|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM105BBRomEcc : 81|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM106CpuRamEcc : 82|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM4BandGapVoltage : 67|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM5CPUClockLock : 68|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM6RFPower : 69|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM8TiaOverSaturation : 70|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM9Vga1OverSaturation : 71|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM10Vga2OverSaturation : 72|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM11IfException : 73|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM12RfException : 74|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_TimeSync : 41|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SWHWCompatCheck : 42|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SensorPosDect : 44|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SensorBlind : 43|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM1LDO : 64|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM2AVDD33Power : 65|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_SM3DVDD11Power : 66|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_TimeGapOfDataProc : 22|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_TimeGapOfSignalProc : 23|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED1ShortToGND : 27|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED1OpenCircurt : 28|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_LED1NotCalib : 26|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_ChipSafetyFail : 32|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_AbnormalInterrupt : 21|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_WDogReset : 20|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_VcanSendTimeout : 24|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PcanSendTimeout : 25|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_MmicInitProc : 18|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PMICVoltFlt : 19|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PCANLostCom : 15|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_IntMemoryFlt : 16|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_MmicInitParamSet : 17|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_NotALN : 14|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_UnderTemp : 13|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_OverTemp : 12|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_VBATLow : 11|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_VBATHigh : 10|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_PCANBusoff : 9|1@0+ (1,0) [0|1] ""  ConfigPC
 SG_ ERR_VCANBusoff : 8|1@0+ (1,0) [0|1] ""  ConfigPC

BO_ 1136 SRR_RL_RadarInfo3: 24 SRR_RL
 SG_ RollingCntMsg0x474 : 187|4@0+ (1,0) [0|15] "/"  ConfigPC
 SG_ CAN1SendErrCnt : 181|5@0+ (1,0) [0|31] ""  ConfigPC
 SG_ CAN1ReinitCnt : 168|3@0+ (1,0) [0|7] ""  ConfigPC
 SG_ CAN1BusoffCnt : 171|3@0+ (1,0) [0|7] ""  ConfigPC
 SG_ CAN0SendErrCnt : 161|6@0+ (1,0) [0|63] ""  ConfigPC
 SG_ CAN0ReinitCnt : 164|3@0+ (1,0) [0|7] ""  ConfigPC
 SG_ CAN0BusoffCnt : 167|3@0+ (1,0) [0|7] ""  ConfigPC
 SG_ ChipSafetyRegDigitalSts : 135|32@0+ (1,0) [0|4294967295] "/"  ConfigPC
 SG_ ChipSafetyRegRfSts : 103|32@0+ (1,0) [0|4294967295] "/"  ConfigPC
 SG_ LedVoltageAdcBase : 87|16@0+ (0.0001,-3.2768) [-3.2768|3.2767] "v"  ConfigPC
 SG_ LedVoltageAdcValue : 71|16@0+ (0.0001,-3.2768) [-3.2768|3.2767] "v"  ConfigPC
 SG_ I2CErrStep : 43|4@0+ (1,0) [0|15] ""  ConfigPC
 SG_ I2CReadReturnVlaue : 55|16@0- (1,0) [-32768|32767] ""  ConfigPC
 SG_ FlashErrStep : 47|4@0+ (1,0) [0|15] ""  ConfigPC
 SG_ FlashWriteErrCode : 31|16@0- (1,0) [-32768|32767] ""  ConfigPC
 SG_ FlashReadErrCode : 15|16@0- (1,0) [-32768|32767] ""  ConfigPC
 SG_ ChecksumMsg0x474 : 7|8@0+ (1,0) [0|255] "/"  ConfigPC

BO_ 1120 SRR_RL_RadarInfo2: 24 SRR_RL
 SG_ RL_TemperatureRaw : 143|32@0+ (1,0) [0|4294967295] "/"  ConfigPC
 SG_ PMICWdgSts : 135|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ RollingCntMsg0x464 : 187|4@0+ (1,0) [0|15] "/"  ConfigPC
 SG_ PMICUV : 127|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ PMICOV : 119|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ ReferencePLLSts : 188|1@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ FmcwPLLStx : 189|1@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ ChipSocCupLoad : 63|8@0+ (1,0) [0|100] "/"  ConfigPC
 SG_ ChipSerielNumber : 71|32@0+ (1,0) [0|4294967295] "/"  ConfigPC
 SG_ TimeGapPcanSend : 111|8@0+ (1,0) [0|255] "ms"  ConfigPC
 SG_ TimeGapVcanSend : 103|8@0+ (1,0) [0|255] "ms"  ConfigPC
 SG_ TimeGapDataProcess : 55|8@0+ (1,0) [0|255] "ms"  ConfigPC
 SG_ TimeGapSignalProcess : 47|8@0+ (1,0) [0|255] "ms"  ConfigPC
 SG_ BatteryVoltage : 183|10@0+ (0.05,0) [0|51.15] "v"  ConfigPC
 SG_ BatteryVoltageRaw : 15|32@0+ (1,0) [0|4294967295] "/"  ConfigPC
 SG_ ChecksumMsg0x464 : 7|8@0+ (1,0) [0|255] "/"  ConfigPC

BO_ 1104 SRR_RL_RadarInfo1: 24 SRR_RL
 SG_ RollingCntMsg0x454 : 187|4@0+ (1,0) [0|15] "/"  ConfigPC
 SG_ NoofRawTarget : 103|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ Tx2Power : 95|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ Tx1Power : 87|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ MountID1Sts : 189|2@0+ (1,0) [0|3] "/"  ConfigPC
 SG_ MountID0Sts : 57|2@0+ (1,0) [0|3] ""  ConfigPC
 SG_ Tx2ObjectNum : 79|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ Tx1ObjectNum : 71|8@0+ (1,0) [0|255] "/"  ConfigPC
 SG_ NtcTemperature : 52|11@0+ (0.1,-50) [-50|154.7] ""  ConfigPC
 SG_ ChipTemperature : 47|11@0+ (0.1,-50) [-50|154.7] "℃"  ConfigPC
 SG_ LocalSystemTime : 15|32@0+ (1,0) [0|4294967295] "ms"  ConfigPC
 SG_ ChecksumMsg0x454 : 7|8@0+ (1,0) [0|255] "/"  ConfigPC

BO_ 1088 SRR_RL_ObjectList: 24 SRR_RL
 SG_ RL_ObjectDistAltitude : 178|8@0+ (0.05,-1.75) [-1.75|11] "m"  ConfigPC
 SG_ RL_ObjectHeight : 131|8@0+ (0.05,0) [0|12.75] "m"  ConfigPC
 SG_ RL_ObjectArelLatRms : 167|5@0+ (0.375,0) [0|11.625] "m/s^2"  ConfigPC
 SG_ RL_ObjectArelLongRms : 156|5@0+ (0.375,0) [0|11.625] "m/s^2"  ConfigPC
 SG_ RL_ObjectVrelLatRms : 145|5@0+ (0.375,0) [0|11.625] "m/s"  ConfigPC
 SG_ RL_ObjectVrelLongRms : 150|5@0+ (0.375,0) [0|11.625] "m/s"  ConfigPC
 SG_ RL_ObjectDistLatRms : 139|5@0+ (0.375,0) [0|11.625] "m"  ConfigPC
 SG_ RL_ObjectDistLongRms : 60|5@0+ (0.375,0) [0|11.625] "m"  ConfigPC
 SG_ RL_ObjectProbOfExist : 183|5@0+ (3.2258,0) [0|99.9998] ""  ConfigPC
 SG_ RL_ObjectMeasState : 115|3@0+ (1,0) [0|7] ""  ConfigPC
 SG_ RL_ObjectRollingCnt : 186|3@0+ (1,0) [0|7] ""  ConfigPC
 SG_ RL_ObjectOrientationAngle : 162|10@0+ (0.4,-180) [-180|180] "deg"  ConfigPC
 SG_ RL_ObjectDynProp : 135|4@0+ (1,0) [0|15] "dbm^2"  ConfigPC
 SG_ RL_ObjectClass : 48|4@0+ (1,0) [0|15] ""  ConfigPC
 SG_ RL_ObjectRCS : 112|9@0+ (0.5,-128) [-128|127.5] "dbm^2"  ConfigPC
 SG_ RL_ObjectWidth : 108|8@0+ (0.2,0) [0|51] "m"  ConfigPC
 SG_ RL_ObjectLength : 100|8@0+ (0.2,0) [0|51] "m"  ConfigPC
 SG_ RL_ObjectArelLat : 95|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ConfigPC
 SG_ RL_ObjectArelLong : 74|11@0+ (0.01,-10.24) [-10.24|10.23] "m/s^2"  ConfigPC
 SG_ RL_ObjectVrelLat : 71|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ConfigPC
 SG_ RL_ObjectVrelLong : 45|13@0+ (0.04,-163.84) [-163.84|163.8] "m/s"  ConfigPC
 SG_ RL_ObjectDistLat : 25|12@0+ (0.05,-102.4) [-102.4|102.35] "m"  ConfigPC
 SG_ RL_ObjectDistLong : 23|14@0+ (0.05,-409.6) [-409.6|409.55] "m"  ConfigPC
 SG_ RL_ObjectID : 15|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_ObjectChecksum : 7|8@0+ (1,0) [0|15] ""  ConfigPC

BO_ 1072 SRR_RL_ObjectHeader: 8 SRR_RL
 SG_ RL_TrkHeaderFuncCalcTime : 33|9@0+ (1,0) [0|511] "" Vector__XXX
 SG_ RL_TrkHeaderTaskCycleTime : 40|9@0+ (1,0) [0|511] "" Vector__XXX
 SG_ RL_ObjHeaderRollingCnt : 59|4@0+ (1,0) [0|15] ""  ConfigPC
 SG_ RL_ObjHeaderProtVer : 63|4@0+ (1,0) [0|15] ""  ConfigPC
 SG_ RL_ObjHeaderMeasCnt : 23|16@0+ (1,0) [0|65535] ""  ConfigPC
 SG_ RL_ObjHeaderObjNum : 15|8@0+ (1,0) [0|255] ""  ConfigPC
 SG_ RL_ObjHeaderChecksum : 7|8@0+ (1,0) [0|15] ""  ConfigPC



CM_ BO_ ********** "This is a message for not used signals, created by Vector CANdb++ DBC OLE DB Provider.";
CM_ SG_ ********** RL_EndFrameSpeedMode "1-OBD
2-自测速";
CM_ SG_ ********** RL_RawHeaderChecksum "message checksum";
CM_ SG_ 1024 RL_RawHeaderFrameModeNum "the total number of Frame Mode(such as Profile in calthrah alps chip)";
CM_ SG_ 1024 RL_RawHeaderCurrentFrameModeIdx "the current index of Frame Mode(such as Profile in calthrah alps chip)";
CM_ SG_ 1024 RL_RawHeaderDataSource "the data source of raw detection, rsp module, or data process mudule";
CM_ SG_ 1024 RL_RawHeaderNoiseGlobal "global noise in RSP module";
CM_ SG_ 1024 RL_RawHeaderNoiseCurrent "current noise of RSP module";
CM_ SG_ 1024 RL_RawHeaderFuncCalcTime "the wasted time in RSP module";
CM_ SG_ 1024 RL_RawHeaderObjNum "number of detection object";
CM_ BO_ 516 "上位机发给雷达的调试命令";
CM_ SG_ 516 Cmd_dbgMode "dbgMode:0-reserved,1-正常模式,2-检测目标回灌调试模式,3-跟踪目标回灌调试模式";
CM_ BO_ 308 "用于上位机查询指令";
CM_ SG_ 1216 RL_AlarmFCTBState "FUNC_STATE_OFF     = 0,
FUNC_STATE_PASSIVE = 1,
FUNC_STATE_ACTIVE  = 2,
FUNC_STATE_ERROR   = 3";
CM_ SG_ 1216 RL_AlarmFCTAState "FUNC_STATE_OFF     = 0,
FUNC_STATE_PASSIVE = 1,
FUNC_STATE_ACTIVE  = 2,
FUNC_STATE_ERROR   = 3";
CM_ SG_ 1216 RL_AlarmRCWState "FUNC_STATE_OFF     = 0,
FUNC_STATE_PASSIVE = 1,
FUNC_STATE_ACTIVE  = 2,
FUNC_STATE_ERROR   = 3";
CM_ SG_ 1216 RL_AlarmRCTBState "FUNC_STATE_OFF     = 0,
FUNC_STATE_PASSIVE = 1,
FUNC_STATE_ACTIVE  = 2,
FUNC_STATE_ERROR   = 3";
CM_ SG_ 1216 RL_AlarmRCTAState "FUNC_STATE_OFF     = 0,
FUNC_STATE_PASSIVE = 1,
FUNC_STATE_ACTIVE  = 2,
FUNC_STATE_ERROR   = 3";
CM_ SG_ 1216 RL_AlarmDOWState "FUNC_STATE_OFF     = 0,
FUNC_STATE_PASSIVE = 1,
FUNC_STATE_ACTIVE  = 2,
FUNC_STATE_ERROR   = 3";
CM_ SG_ 1216 RL_AlarmLCAState "FUNC_STATE_OFF     = 0,
FUNC_STATE_PASSIVE = 1,
FUNC_STATE_ACTIVE  = 2,
FUNC_STATE_ERROR   = 3";
CM_ SG_ 1216 RL_AlarmBsdState "FUNC_STATE_OFF     = 0,
FUNC_STATE_PASSIVE = 1,
FUNC_STATE_ACTIVE  = 2,
FUNC_STATE_ERROR   = 3";
CM_ SG_ 1152 RR_DrvFunc_BeepState "报警蜂鸣器状态";
CM_ SG_ 1152 RR_DrvFunc_LedState "报警LED状态";
CM_ SG_ 1088 RL_ObjectDynProp "0x0:moving
0x1:statinary
0x2:oncoming
0x3:stationary candidate
0x4:unkown
0x5:crossing stationary
0x6:crossing moving
0x7:stopped";
CM_ SG_ 1088 RL_ObjectClass "0x0:point
0x1:car
0x2:truck
0x3:pedestran
0x4:motorcycle
0x5:bicycle
0x6:wide
0x7:reserved";
BA_DEF_ BO_  "GenericFrameRequirementNb" STRING ;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","OnEvent","Cyclic_And_OnEvent";
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 65535;
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_  "DBName" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_DEF_  "GenericFrameRequirementNb" "";
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenMsgCycleTime" 50;
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_ "BusType" "CAN FD";
BA_ "DBName" "CTMRR410_PCANFD";
BA_ "VFrameFormat" BO_ 1012 14;
BA_ "VFrameFormat" BO_ 1264 14;
BA_ "VFrameFormat" BO_ 1024 14;
BA_ "VFrameFormat" BO_ 1040 14;
BA_ "VFrameFormat" BO_ 1232 14;
BA_ "GenMsgCycleTime" BO_ 2020 0;
BA_ "GenMsgSendType" BO_ 2020 1;
BA_ "VFrameFormat" BO_ 2020 14;
BA_ "GenMsgSendType" BO_ 308 1;
BA_ "VFrameFormat" BO_ 308 14;
BA_ "GenMsgCycleTime" BO_ 2036 0;
BA_ "GenMsgSendType" BO_ 2036 1;
BA_ "VFrameFormat" BO_ 2036 14;
BA_ "VFrameFormat" BO_ 1216 14;
BA_ "GenMsgCycleTime" BO_ 772 0;
BA_ "GenMsgSendType" BO_ 772 1;
BA_ "VFrameFormat" BO_ 772 14;
BA_ "GenMsgCycleTime" BO_ 1168 1000;
BA_ "VFrameFormat" BO_ 1168 14;
BA_ "VFrameFormat" BO_ 1152 14;
BA_ "VFrameFormat" BO_ 1008 14;
BA_ "VFrameFormat" BO_ 1248 14;
BA_ "VFrameFormat" BO_ 1136 14;
BA_ "VFrameFormat" BO_ 1120 14;
BA_ "VFrameFormat" BO_ 1104 14;
BA_ "VFrameFormat" BO_ 1088 14;
BA_ "VFrameFormat" BO_ 1072 14;

