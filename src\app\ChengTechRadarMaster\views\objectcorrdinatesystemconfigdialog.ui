<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ObjectCorrdinateSystemConfigDialog</class>
 <widget class="QDialog" name="ObjectCorrdinateSystemConfigDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>869</width>
    <height>423</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QGridLayout" name="gridLayout">
     <item row="0" column="2">
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item row="0" column="3">
      <widget class="QLineEdit" name="lineEditHMaxRange">
       <property name="text">
        <string>30</string>
       </property>
      </widget>
     </item>
     <item row="0" column="5">
      <widget class="QLineEdit" name="lineEditHInterval">
       <property name="text">
        <string>3</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>纵轴范围(m):</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QLineEdit" name="lineEditLaneWidth">
       <property name="text">
        <string>2.5</string>
       </property>
      </widget>
     </item>
     <item row="1" column="7">
      <widget class="QCheckBox" name="checkBoxDisplayVelocityFeversal">
       <property name="text">
        <string>显示速度方向</string>
       </property>
      </widget>
     </item>
     <item row="7" column="0" colspan="2">
      <widget class="QLabel" name="label_22">
       <property name="text">
        <string>后角雷达到后轮中心纵向距离：</string>
       </property>
      </widget>
     </item>
     <item row="1" column="3">
      <widget class="QLineEdit" name="lineEditVMaxRange">
       <property name="text">
        <string>50</string>
       </property>
      </widget>
     </item>
     <item row="1" column="4">
      <widget class="QLabel" name="label_6">
       <property name="text">
        <string>/</string>
       </property>
      </widget>
     </item>
     <item row="0" column="7">
      <widget class="QCheckBox" name="checkBoxDisplayTargetID">
       <property name="text">
        <string>显示ID</string>
       </property>
      </widget>
     </item>
     <item row="3" column="6">
      <widget class="QCheckBox" name="checkBoxDisplay16TrackTarget">
       <property name="text">
        <string>显示16个目标</string>
       </property>
      </widget>
     </item>
     <item row="2" column="7">
      <widget class="QCheckBox" name="checkBoxDisplayTrackFrame">
       <property name="text">
        <string>显示航迹框</string>
       </property>
      </widget>
     </item>
     <item row="2" column="8">
      <widget class="QCheckBox" name="checkBoxHighlightedVelocityAmbiguity">
       <property name="text">
        <string>凸显速度解模糊</string>
       </property>
      </widget>
     </item>
     <item row="0" column="4">
      <widget class="QLabel" name="label_5">
       <property name="text">
        <string>/</string>
       </property>
      </widget>
     </item>
     <item row="0" column="8">
      <widget class="QCheckBox" name="checkBoxDisplayHeSaiLider">
       <property name="text">
        <string>显示禾赛激光</string>
       </property>
      </widget>
     </item>
     <item row="3" column="3">
      <widget class="QLineEdit" name="lineEditLocalVehicleLongth">
       <property name="text">
        <string>5.0</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLineEdit" name="lineEditVMinRange">
       <property name="text">
        <string>-50</string>
       </property>
      </widget>
     </item>
     <item row="1" column="6">
      <widget class="QCheckBox" name="checkBoxDisplaygridLine">
       <property name="text">
        <string>显示网格线</string>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QLabel" name="label">
       <property name="text">
        <string>横轴范围(m):</string>
       </property>
      </widget>
     </item>
     <item row="6" column="0" colspan="2">
      <widget class="QLabel" name="label_23">
       <property name="text">
        <string>后角雷达到后轮中心横向距离：</string>
       </property>
      </widget>
     </item>
     <item row="2" column="6">
      <widget class="QCheckBox" name="checkBoxDisplayLane">
       <property name="text">
        <string>显示车道线</string>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="label_8">
       <property name="text">
        <string>自车宽长(m):</string>
       </property>
      </widget>
     </item>
     <item row="0" column="6">
      <widget class="QCheckBox" name="checkBoxDisplayZeroLine">
       <property name="text">
        <string>显示零线</string>
       </property>
      </widget>
     </item>
     <item row="1" column="5">
      <widget class="QLineEdit" name="lineEditVInterval">
       <property name="text">
        <string>5</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEditHMinRange">
       <property name="text">
        <string>-30</string>
       </property>
      </widget>
     </item>
     <item row="1" column="2">
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>-</string>
       </property>
      </widget>
     </item>
     <item row="1" column="8">
      <widget class="QCheckBox" name="checkBoxDisplayVelocityAmbiguity">
       <property name="text">
        <string>显示速度解模糊</string>
       </property>
      </widget>
     </item>
     <item row="7" column="3">
      <widget class="QLineEdit" name="lineEditRearWheelCenterRearY"/>
     </item>
     <item row="0" column="9" rowspan="8">
      <widget class="QPushButton" name="pushButtonSetAxis">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>设置</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QLineEdit" name="lineEditLocalVehicleWidth">
       <property name="text">
        <string>1.8</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_9">
       <property name="text">
        <string>车道宽度(m):</string>
       </property>
      </widget>
     </item>
     <item row="4" column="0" colspan="2">
      <widget class="QLabel" name="label_24">
       <property name="text">
        <string>前角雷达到后轮中心横向距离：</string>
       </property>
      </widget>
     </item>
     <item row="5" column="0" colspan="2">
      <widget class="QLabel" name="label_25">
       <property name="text">
        <string>前角雷达到后轮中心纵向距离：</string>
       </property>
      </widget>
     </item>
     <item row="4" column="3">
      <widget class="QLineEdit" name="lineEditRearWheelCenterFrontX"/>
     </item>
     <item row="5" column="3">
      <widget class="QLineEdit" name="lineEditRearWheelCenterFrontY"/>
     </item>
     <item row="6" column="3">
      <widget class="QLineEdit" name="lineEditRearWheelCenterRearX"/>
     </item>
     <item row="4" column="5" colspan="4">
      <widget class="QLabel" name="label_26">
       <property name="text">
        <string>航迹框:轿车-黄色；卡车-红色;行人-绿色;两轮车-洋红;自行车-深洋红;其它-白色</string>
       </property>
      </widget>
     </item>
     <item row="3" column="5">
      <widget class="QCheckBox" name="checkBoxDisplayELKOnly">
       <property name="text">
        <string>只显示ELK</string>
       </property>
      </widget>
     </item>
     <item row="3" column="7">
      <widget class="QCheckBox" name="checkBoxDisplay16TrackFrame">
       <property name="text">
        <string>显示16个目标航迹框</string>
       </property>
      </widget>
     </item>
     <item row="3" column="8">
      <widget class="QCheckBox" name="checkBoxDisplay16TrackShowV">
       <property name="text">
        <string>显示16个目标速度方向</string>
       </property>
      </widget>
     </item>
     <item row="5" column="7">
      <widget class="QCheckBox" name="checkBoxStatusFiltrateRaw">
       <property name="text">
        <string>原始点状态筛选 &lt;=</string>
       </property>
      </widget>
     </item>
     <item row="5" column="8">
      <widget class="QSpinBox" name="spinBoxStatusFiltrateRaw">
       <property name="value">
        <number>3</number>
       </property>
      </widget>
     </item>
     <item row="5" column="5">
      <widget class="QCheckBox" name="checkBoxDisplayAcceleration">
       <property name="text">
        <string>显示加速度</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>报警区域</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="1" column="6">
         <widget class="QLineEdit" name="lineEditBSDWidth">
          <property name="text">
           <string>3.75</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QCheckBox" name="checkBoxLCADisplay">
          <property name="text">
           <string>LCA区域</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLabel" name="label_14">
          <property name="text">
           <string>车身距(m):</string>
          </property>
         </widget>
        </item>
        <item row="2" column="8">
         <widget class="QLineEdit" name="lineEditLCALength">
          <property name="text">
           <string>75.0</string>
          </property>
         </widget>
        </item>
        <item row="2" column="5">
         <widget class="QLabel" name="label_17">
          <property name="text">
           <string>宽度(m):</string>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QLineEdit" name="lineEditLCADistanceOfBody">
          <property name="text">
           <string>0.0</string>
          </property>
         </widget>
        </item>
        <item row="1" column="8">
         <widget class="QLineEdit" name="lineEditBSDLength">
          <property name="text">
           <string>5.0</string>
          </property>
         </widget>
        </item>
        <item row="2" column="3">
         <widget class="QLabel" name="label_15">
          <property name="text">
           <string>车头距(m):</string>
          </property>
         </widget>
        </item>
        <item row="2" column="6">
         <widget class="QLineEdit" name="lineEditLCAWidth">
          <property name="text">
           <string>3.75</string>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QLineEdit" name="lineEditBSDDistanceOfBody">
          <property name="text">
           <string>0.0</string>
          </property>
         </widget>
        </item>
        <item row="1" column="4">
         <widget class="QLineEdit" name="lineEditBSDDistanceOfHeadway">
          <property name="text">
           <string>2.5</string>
          </property>
         </widget>
        </item>
        <item row="2" column="7">
         <widget class="QLabel" name="label_16">
          <property name="text">
           <string>长度(m):</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLabel" name="label_10">
          <property name="text">
           <string>车身距(m):</string>
          </property>
         </widget>
        </item>
        <item row="2" column="4">
         <widget class="QLineEdit" name="lineEditLCADistanceOfHeadway">
          <property name="text">
           <string>2.5</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QCheckBox" name="checkBoxBSDDisplay">
          <property name="text">
           <string>BSD区域</string>
          </property>
         </widget>
        </item>
        <item row="1" column="3">
         <widget class="QLabel" name="label_11">
          <property name="text">
           <string>车头距(m):</string>
          </property>
         </widget>
        </item>
        <item row="1" column="7">
         <widget class="QLabel" name="label_13">
          <property name="text">
           <string>长度(m):</string>
          </property>
         </widget>
        </item>
        <item row="1" column="5">
         <widget class="QLabel" name="label_12">
          <property name="text">
           <string>宽度(m):</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QCheckBox" name="checkBoxDOWDisplay">
          <property name="text">
           <string>DOW区域</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="label_18">
          <property name="text">
           <string>车身距(m):</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QLabel" name="label_19">
          <property name="text">
           <string>车头距(m):</string>
          </property>
         </widget>
        </item>
        <item row="0" column="5">
         <widget class="QLabel" name="label_20">
          <property name="text">
           <string>宽度(m):</string>
          </property>
         </widget>
        </item>
        <item row="0" column="7">
         <widget class="QLabel" name="label_21">
          <property name="text">
           <string>长度(m):</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QLineEdit" name="lineEditDOWDistanceOfBody">
          <property name="text">
           <string>-0.25</string>
          </property>
         </widget>
        </item>
        <item row="0" column="4">
         <widget class="QLineEdit" name="lineEditDOWDistanceOfHeadway">
          <property name="text">
           <string>5.0</string>
          </property>
         </widget>
        </item>
        <item row="0" column="8">
         <widget class="QLineEdit" name="lineEditDOWWidth">
          <property name="text">
           <string>45.0</string>
          </property>
         </widget>
        </item>
        <item row="0" column="6">
         <widget class="QLineEdit" name="lineEditDOWLength">
          <property name="text">
           <string>2.25</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label_7">
       <property name="text">
        <string>雷达;</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxRadarID"/>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayoutObjectConfg"/>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>88</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="text">
        <string>Cancel</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonApply">
       <property name="text">
        <string>Apply</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOK">
       <property name="text">
        <string>OK</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>pushButtonCancel</sender>
   <signal>clicked()</signal>
   <receiver>ObjectCorrdinateSystemConfigDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>454</x>
     <y>415</y>
    </hint>
    <hint type="destinationlabel">
     <x>236</x>
     <y>406</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
