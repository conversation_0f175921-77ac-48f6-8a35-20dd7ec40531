/**
 * @file rdp_matrixMath.c
 * @brief 
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */
#include <stdint.h>
/**
*  @b Description
*  @n
*       This function is used to add two matrices.
*       Matrices are all real, single precision floating point.
*       Matrices are in row-major order
*
*  @param[in]  rows
*       Number of rows
*  @param[in]  cols
*       Number of cols
*  @param[in]  A
*       Matrix A
*  @param[in]  B
*       Matrix B
*  @param[out]  C
*       Matrix C(rows,cols) = A(rows,cols) + B(rows,cols)
*
*  \ingroup GTRACK_ALG_MATH_FUNCTION
*
*  @retval
*      None
*/
void matrixAdd(uint16_t rows, uint16_t cols, float *A, float *B, float *C)
{
    /* C(rows*cols) = A(rows*cols) + B(rows*cols) */
    uint16_t i;
    for (i = 0U; i < rows*cols; i++)
    {
        C[i] = A[i] + B[i];
    }
}

/**
*  @b Description
*  @n
*       This function is used to subtract two matrices.
*       Matrices are all real, single precision floating point.
*       Matrices are in row-major order
*
*  @param[in]  rows
*       Number of rows
*  @param[in]  cols
*       Number of cols
*  @param[in]  A
*       Matrix A
*  @param[in]  B
*       Matrix B
*  @param[out]  C
*       Matrix C(rows,cols) = A(rows,cols) - B(rows,cols)
*
*  \ingroup GTRACK_ALG_MATH_FUNCTION
*
*  @retval
*      None
*/
void matrixSub(uint16_t rows, uint16_t cols, float *A, float *B, float *C)
{
    /* C(rows*cols) = A(rows*cols) - B(rows*cols) */
    uint16_t i;
    for (i = 0U; i < rows*cols; i++)
    {
        C[i] = A[i] - B[i];
    }
}
/**
*  @b Description
*  @n
*       This function is used to multiply two matrices. 
*       Matrices are all real, single precision floating point.
*       Matrices are in row-major order
*
*  @param[in]  rows
*       Outer dimension, number of rows
*  @param[in]  m
*       Inner dimension
*  @param[in]  cols
*       Outer dimension, number of cols
*  @param[in]  A
*       Matrix A
*  @param[in]  B
*       Matrix B
*  @param[out]  C
*       Matrix C(rows,cols) = A(rows,m) X B(m,cols)
*
*  \ingroup GTRACK_ALG_MATH_FUNCTION
*
*  @retval
*      None
*/

void matrixMultiply(uint16_t rows, uint16_t m, uint16_t cols, float *A, float *B, float *C)
{
    /* C(rows*cols) = A(rows*m)*B(m*cols) */
    uint16_t i,j, k;
    for (i = 0; i < rows; i++)
    {
        for (j = 0; j < cols; j++)
        {            
            C[i*cols + j] = 0;
            for (k = 0; k < m; k++)
                C[i*cols+j] += A[i*m+k] * B[k*cols + j];
        }
    }
}

/**
*  @b Description
*  @n
*       This function is used to multiply two matrices. Second Matrix is getting transposed first
*       Matrices are all real, single precision floating point.
*       Matrices are in row-major order
*
*  @param[in]  rows
*       Outer dimension, number of rows
*  @param[in]  m
*       Inner dimension
*  @param[in]  cols
*       Outer dimension, number of cols
*  @param[in]  A
*       Matrix A
*  @param[in]  B
*       Matrix B
*  @param[out]  C
*       Matrix C(rows,cols) = A(rows,m) X B(cols,m)T
*
*  \ingroup GTRACK_ALG_MATH_FUNCTION
*
*  @retval
*      None
*/
void matrixTransposeMultiply(uint16_t rows, uint16_t m, uint16_t cols, float *A, float *B, float *C)
{
    /* C(rows*cols) = A(rows*m)*B(cols*m)T */
    uint16_t i,j, k;
    for (i = 0; i < rows; i++)
    {
        for (j = 0; j < cols; j++)
        {            
            C[i*cols + j] = 0;
            for (k = 0; k < m; k++)
                C[i*cols+j] += A[i*m+k] * B[k + j*m];
        }
    }
}

/**
*  @b Description
*  @n
*       This function computes the inverse of 2x2 matrix.
*       Matrix is real, single precision floating point.
*       Matrix is in row-major order
*
*  @param[in]  A
*       Matrix A
*  @param[out]  inv
*       inv = inverse(A);
*
*  \ingroup GTRACK_ALG_MATH_FUNCTION
*
*  @retval
*      None
*/

void matrixInv2(float *A, float *inv)
{
    float det;
    float invdet;

    det = A[0] * A[3] - A[1] * (A[2]);

    invdet = 1 / det;

    inv[0] =  A[3] * invdet;
    inv[1] = -A[1] * invdet;
    inv[2] = -A[2] * invdet;
    inv[3] =  A[0] * invdet;
}

