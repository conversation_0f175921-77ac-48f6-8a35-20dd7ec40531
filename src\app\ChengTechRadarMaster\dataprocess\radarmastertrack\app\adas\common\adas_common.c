/**
 * @file adas_common.c
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2025.07.08
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2025-07-08 <td>1.0     <td><PERSON>     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2025  Shenzhen Cheng-Tech Co.,Ltd.
 */

/***********************
 * * 本地头文件
 ***********************/
#include "adas_common.h"

#if !defined(__GNUC__)
/**
 * @brief 求开方的倒数
 * 	根据雷神之锤3游戏里面的magic数0x5f375a86 更改过来的
 *
 * @param x
 * @remarks: 4开方为2,4开方的倒数为1/2=0.5，即传入4，返回0.5，
 * @return float
 */
float MagicInverseSqrt(float x)
{
    if (x < 0 && fabs(x) > FLOAT_EPS)
    {
        return NAN; // 如果输入小于零且绝对值大于阈值，则返回NAN
    }
    float xhalf = 0.5f * x;
    int i = *(int *)&x;             // get bits for floating VALUE
    i = 0x5f375a86 - (i >> 1);      // gives initial guess y0
    x = *(float *)&i;               // convert bits BACK to float
    x = x * (1.5f - xhalf * x * x); // Newton step, repeating increases accuracy
    // 增加Newton-Raphson迭代次数，精度更准
    x = x * (1.5f - xhalf * x * x);
    return x;
}

/**
 * @brief 开方
 *  根据雷神之锤3游戏里面的magic数0x5f375a86 更改过来的
 *
 *  测试563473654.46345 执行时间相差29.5倍
 *  MagicSqrt: sqrtNum=23751.57226562
 *  Execution time: 0.00020000 milliseconds
 *  math sqrt: sqrtNum=23737.59960938
 *  Execution time: 0.00590000 milliseconds
 *
 * @param x
 * @remarks 4开方为2
 * @return float
 */
float MagicSqrt(float x)
{
    if (x < 0 && fabs(x) > FLOAT_EPS)
    {
        return NAN; // 如果输入小于零且绝对值大于阈值，则返回NAN
    }
    float xhalf = 0.5f * x;
    int i = *(int *)&x;             // get bits for floating VALUE
    i = 0x5f375a86 - (i >> 1);      // gives initial guess y0
    x = *(float *)&i;               // convert bits BACK to float
    x = x * (1.5f - xhalf * x * x); // Newton step, repeating increases accuracy
    // 增加Newton-Raphson迭代次数，精度更准
    x = x * (1.5f - xhalf * x * x);
    x = (float)(1.0 / x);
    return x;
}
#else
typedef union __CommType_t
{
    int i32_Par;
    unsigned int ui32_Par;
    float f32_Par;
} CommType_t;
/**
 * @brief 求开方的倒数
 * 	根据雷神之锤3游戏里面的magic数0x5f375a86 更改过来的
 *
 *  测试563473654.46345 执行时间相差29.5倍
 *  MagicSqrt: sqrtNum=23751.57226562
 *  Execution time: 0.00020000 milliseconds
 *  math sqrt: sqrtNum=23737.59960938
 *  Execution time: 0.00590000 milliseconds
 *
 * @param x
 * @remarks: 4开方为2,4开方的倒数为1/2=0.5，即传入4，返回0.5，
 * @return float
 */
float MagicInverseSqrt(float x)
{
    if (x < 0 && fabs(x) > FLOAT_EPS)
    {
        return NAN; // 如果输入小于零且绝对值大于阈值，则返回NAN
    }
    CommType_t commType;
    commType.f32_Par = x;
    float xhalf = 0.5f * x;
    int i = commType.i32_Par;  // get bits for floating VALUE // int i = *(int*)&x;
    i = 0x5f375a86 - (i >> 1); // gives initial guess y0
    commType.i32_Par = i;
    x = commType.f32_Par;           // convert bits BACK to float // x = *(float*)&i;
    x = x * (1.5f - xhalf * x * x); // Newton step, repeating increases accuracy
    // 增加Newton-Raphson迭代次数，精度更准
    x = x * (1.5f - xhalf * x * x); 
    return x;
}

/**
 * @brief 开方
 * 	根据雷神之锤3游戏里面的magic数0x5f375a86 更改过来的, 相比sqrt() 函数，这套算法要快将近4倍
 *
 * @param x
 * @remarks 4开方为2
 * @return float
 */
float MagicSqrt(float x)
{
    if (x < 0 && fabs(x) > FLOAT_EPS)
    {
        return NAN; // 如果输入小于零且绝对值大于阈值，则返回NAN
    }
    CommType_t commType;
    commType.f32_Par = x;
    float xhalf = 0.5f * x;
    int i = commType.i32_Par;  // get bits for floating VALUE // int i = *(int*)&x;
    i = 0x5f375a86 - (i >> 1); // gives initial guess y0
    commType.i32_Par = i;
    x = commType.f32_Par;           // convert bits BACK to float // x = *(float*)&i;
    x = x * (1.5f - xhalf * x * x); // Newton step, repeating increases accuracy
    // 增加Newton-Raphson迭代次数，精度更准
    x = x * (1.5f - xhalf * x * x);
    x = 1.0 / x;
    return x;
}
#endif

#ifdef PC_DBG_FW
/**
 * @brief 替换atan2f函数，提供数值稳定的四象限反正切计算
 * @param y 纵坐标分量
 * @param x 横坐标分量
 * @return float 角度值（弧度），范围 (-π, π]
 */
float atan2_replacement(float y, float x)
{ 
    float angle; 

    // 处理数值稳定性：当x接近0时
    if (fabsf(x) < FLOAT_EPS)
    {
        if (y > FLOAT_EPS)
        {
            angle = M_PI / 2.0f;
        }
        else if (y < -FLOAT_EPS)
        {
            angle = -M_PI / 2.0f;
        }
        else
        {
            angle = 0.0f;  // 原点情况
        }
    }
    else if (x > 0.0f)
    {
        angle = atanf(y / x);
    }
    else  // x < 0
    {
        if (y >= 0.0f)
        {
            angle = atanf(y / x) + M_PI;
        }
        else
        {
            angle = atanf(y / x) - M_PI;
        }
    }

    return angle;
}
#else
/**
 * @brief 快速atan2近似算法（适用于实时ADAS系统）
 *      float atan2_fast_approx(float y, float x)
 * @param y 纵坐标分量
 * @param x 横坐标分量
 * @return float 角度值（弧度），最大误差约0.005弧度
 */
float atan2_replacement(float y, float x)
{
    const float abs_y = fabsf(y) + 1e-10f;  // 防止除零

    if (x >= 0.0f)
    {
        const float r = (x - abs_y) / (x + abs_y);
        const float angle = 0.1963f * r * r * r - 0.9817f * r + M_PI / 4.0f;
        return y < 0.0f ? -angle : angle;
    }
    else
    {
        const float r = (x + abs_y) / (abs_y - x);
        const float angle = 0.1963f * r * r * r - 0.9817f * r + 3.0f * M_PI / 4.0f;
        return y < 0.0f ? -angle : angle;
    }
}

#endif

