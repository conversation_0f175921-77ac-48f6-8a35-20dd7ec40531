﻿#ifndef TARGETVIEWCONFIGDIALOG_H
#define TARGETVIEWCONFIGDIALOG_H

#include <QDialog>

#include "targetsview.h"

class FlowLayout;
class QPushButton;
class QCheckBox;

class DisplayWidget : public QWidget
{
    Q_OBJECT
public:
    DisplayWidget(const TargetsView::PointDisplay &pointDisplay, QWidget *parent = nullptr);

    const TargetsView::PointDisplay &getPointDisplay() const { return mPointDisplay; }

private:
    QPushButton *mPushButtonColor{0};
    QCheckBox *mCheckBoxDisplay{0};

    TargetsView::PointDisplay mPointDisplay;
};

namespace Ui {
class TargetsViewConfigDialog;
}

class TargetsViewConfigDialog : public QDialog
{
    Q_OBJECT

public:
    explicit TargetsViewConfigDialog(const TargetsView::ViewSettings &settings, QWidget *parent = nullptr);
    ~TargetsViewConfigDialog();

signals:
    void changeSettings(const TargetsView::ViewSettings &settings);

private slots:
    void on_pushButtonOK_clicked();

    void on_pushButtonApply_clicked();

private:
    Ui::TargetsViewConfigDialog *ui;
    FlowLayout *mFlowLayout{0};
};

#endif // TARGETVIEWCONFIGDIALOG_H
