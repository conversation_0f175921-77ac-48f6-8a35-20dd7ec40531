#ifndef BJEV_B41V(EP)_ABUS_CMRR_FL_CANFD_V1_H
#define BJEV_B41V(EP)_ABUS_CMRR_FL_CANFD_V1_H

#include <stdint.h>

#define MSG_ID180_SAS_1 0x180
#define MSG_ID242_WCBS_ESP_13 0x242
#define MSG_ID280_EMS_5 0x280
#define MSG_ID311_WCBS_ESP_5 0x311
#define MSG_ID3e0_TCM_2 0x3e0
#define MSG_ID3f0_WCBS_ESP_6 0x3f0
#define MSG_ID4a0_BDC_BCM_1 0x4a0
#define MSG_ID4b3_BDC_BCM_2 0x4b3
#define MSG_ID523_CDC_EHC_3 0x523
#define MSG_ID527_CDC_EHC_6 0x527
#define MSG_ID570_BDC_CAPE_1 0x570
#define MSG_ID620_CDC_IPC1 0x620
#define MSG_ID640_WCBS_ESP_3 0x640
#define MSG_ID650_WCBS_ESP_4 0x650
#define MSG_ID6e7_CDC_EHC_12 0x6e7

// 0x180 length = 8
typedef struct SAS_1 {
	uint64_t : 24;
	// 65535 "Invalid"
	uint64_t SAS1_N_SteeringAngle : 16;
	uint64_t : 8;
	// 1 "Sensor information valid" 0 "Sensor information invalid,an internal sensor fault occurred"
	uint64_t SAS1_St_SASSensorInformation : 1;
	uint64_t : 15;
}SAS_1_t;

// 0x180 length = 8
typedef struct CT_SAS_1 {
	uint8_t SAS1_St_SASSensorInformation{0};
	float SAS1_N_SteeringAngle{0.0};
}CT_SAS_1_t;

// 0x242 length = 8
typedef struct WCBS_ESP_13 {
	uint64_t : 34;
	// 7 "Reserved" 6 "Reserved" 5 "Reserved" 4 "Invalid" 3 "Stop" 2 "Backward" 1 "Forward" 0 "Init"
	uint64_t WCBS_ESP13_St_RHRWDD : 3;
	// 7 "Reserved" 6 "Reserved" 5 "Reserved" 4 "Invalid" 3 "Stop" 2 "Backward" 1 "Forward" 0 "Init"
	uint64_t WCBS_ESP13_St_LHRWDD : 3;
	uint64_t : 2;
	// 7 "Reserved" 6 "Reserved" 5 "Reserved" 4 "Invalid" 3 "Stop" 2 "Backward" 1 "Forward" 0 "Init"
	uint64_t WCBS_ESP13_St_RHFWDD : 3;
	// 7 "Reserved" 6 "Reserved" 5 "Reserved" 4 "Invalid" 3 "Stop" 2 "Backward" 1 "Forward" 0 "Init"
	uint64_t WCBS_ESP13_St_LHFWDD : 3;
	uint64_t : 16;
}WCBS_ESP_13_t;

// 0x242 length = 8
typedef struct CT_WCBS_ESP_13 {
	uint8_t WCBS_ESP13_St_LHFWDD{0};
	uint8_t WCBS_ESP13_St_RHFWDD{0};
	uint8_t WCBS_ESP13_St_LHRWDD{0};
	uint8_t WCBS_ESP13_St_RHRWDD{0};
}CT_WCBS_ESP_13_t;

// 0x280 length = 8
typedef struct EMS_5 {
	uint64_t : 23;
	// 1 "Engine running" 0 "Engine NOT running, 200 ms after last revolution pulse."
	uint64_t EMS5_St_EngineRunning : 1;
	uint64_t : 40;
}EMS_5_t;

// 0x280 length = 8
typedef struct CT_EMS_5 {
	uint8_t EMS5_St_EngineRunning{0};
}CT_EMS_5_t;

// 0x311 length = 8
typedef struct WCBS_ESP_5 {
	uint64_t : 19;
	uint64_t WCBS_ESP5_N_VehicleSpeed : 13;
	uint64_t : 16;
	// 1 "Valid" 0 "Not valid??"
	uint64_t WCBS_ESP5_F_VehicleSpeed : 1;
	uint64_t : 15;
}WCBS_ESP_5_t;

// 0x311 length = 8
typedef struct CT_WCBS_ESP_5 {
	uint8_t WCBS_ESP5_F_VehicleSpeed{0};
	float WCBS_ESP5_N_VehicleSpeed{0.0};
}CT_WCBS_ESP_5_t;

// 0x3e0 length = 8
typedef struct TCM_2 {
	uint64_t : 40;
	// 15 "Undefined" 12 "S" 11 "M" 4 "D" 3 "N" 2 "R" 1 "P"
	uint64_t TCM2_N_ShiftLeverPos : 8;
	uint64_t : 10;
	// 3 "Value unreliable" 2 "Value above upper limit(Not used)" 1 "Value below lower limit(Not used)" 0 "Value OK"
	uint64_t TCM2_F_ShiftLeverPos : 2;
	uint64_t : 4;
}TCM_2_t;

// 0x3e0 length = 8
typedef struct CT_TCM_2 {
	uint8_t TCM2_F_ShiftLeverPos{0};
	uint8_t TCM2_N_ShiftLeverPos{0};
}CT_TCM_2_t;

// 0x3f0 length = 8
typedef struct WCBS_ESP_6 {
	uint64_t : 19;
	uint64_t WCBS_ESP6_N_YawVelocity : 13;
	uint64_t WCBS_ESP6_N_LateralAcc : 8;
	uint64_t : 4;
	uint64_t WCBS_ESP6_N_LongitudeAcc : 12;
	uint64_t : 5;
	// 1 "Value unreliable" 0 "Value OK"
	uint64_t WCBS_ESP6_F_YawVelocity : 1;
	// 1 "Value unreliable" 0 "Value OK"
	uint64_t WCBS_ESP6_F_LateralAcc : 1;
	// 1 "Value unreliable" 0 "Value OK"
	uint64_t WCBS_ESP6_F_LongitudeAcc : 1;
}WCBS_ESP_6_t;

// 0x3f0 length = 8
typedef struct CT_WCBS_ESP_6 {
	uint8_t WCBS_ESP6_F_LongitudeAcc{0};
	uint8_t WCBS_ESP6_F_LateralAcc{0};
	uint8_t WCBS_ESP6_F_YawVelocity{0};
	float WCBS_ESP6_N_LongitudeAcc{0.0};
	float WCBS_ESP6_N_LateralAcc{0.0};
	float WCBS_ESP6_N_YawVelocity{0.0};
}CT_WCBS_ESP_6_t;

// 0x4a0 length = 8
typedef struct BDC_BCM_1 {
	uint64_t : 34;
	// 1 "Door open" 0 "Door closed"
	uint64_t BCM1_St_RRDoor : 1;
	// 1 "Door open" 0 "Door closed"
	uint64_t BCM1_St_LRDoor : 1;
	// 1 "Door open" 0 "Door closed"
	uint64_t BCM1_St_PassengerDoor : 1;
	// 1 "Door open" 0 "Door closed"
	uint64_t BCM1_St_DriverDoor : 1;
	// 1 "Door unlocked" 0 "Door locked"
	uint64_t BCM1_St_PasDoorLock : 1;
	// 1 "Door unlocked" 0 "Door locked"
	uint64_t BCM1_St_LFDoorLock : 1;
	uint64_t : 5;
	// 1 "ON" 0 "OFF"
	uint64_t BCM1_Cmd_RightTurnLamp : 1;
	uint64_t : 1;
	// 1 "ON" 0 "OFF"
	uint64_t BCM1_Cmd_LeftTurnLamp : 1;
	uint64_t : 16;
}BDC_BCM_1_t;

// 0x4a0 length = 8
typedef struct CT_BDC_BCM_1 {
	uint8_t BCM1_Cmd_LeftTurnLamp{0};
	uint8_t BCM1_Cmd_RightTurnLamp{0};
	uint8_t BCM1_St_LFDoorLock{0};
	uint8_t BCM1_St_PasDoorLock{0};
	uint8_t BCM1_St_DriverDoor{0};
	uint8_t BCM1_St_PassengerDoor{0};
	uint8_t BCM1_St_LRDoor{0};
	uint8_t BCM1_St_RRDoor{0};
}CT_BDC_BCM_1_t;

// 0x4b3 length = 8
typedef struct BDC_BCM_2 {
	uint64_t : 1;
	// 1 "Door unlock" 0 "Door locked"
	uint64_t BCM2_St_RRDoorLock : 1;
	// 1 "Door unlock" 0 "Door locked"
	uint64_t BCM2_St_LRDoorLock : 1;
	uint64_t : 61;
}BDC_BCM_2_t;

// 0x4b3 length = 8
typedef struct CT_BDC_BCM_2 {
	uint8_t BCM2_St_LRDoorLock{0};
	uint8_t BCM2_St_RRDoorLock{0};
}CT_BDC_BCM_2_t;

// 0x523 length = 8
typedef struct CDC_EHC_3 {
	// 1 "off" 0 "on"
	uint64_t EHC3_Set_RCTA : 1;
	// 1 "off" 0 "on"
	uint64_t EHC3_Set_DOW : 1;
	// 1 "off" 0 "on"
	uint64_t EHC3_Set_LCA : 1;
	// 1 "off" 0 "on"
	uint64_t EHC3_Set_BSD : 1;
	uint64_t : 60;
}CDC_EHC_3_t;

// 0x523 length = 8
typedef struct CT_CDC_EHC_3 {
	uint8_t EHC3_Set_BSD{0};
	uint8_t EHC3_Set_LCA{0};
	uint8_t EHC3_Set_DOW{0};
	uint8_t EHC3_Set_RCTA{0};
}CT_CDC_EHC_3_t;

// 0x527 length = 8
typedef struct CDC_EHC_6 {
	uint64_t : 28;
	uint64_t EHC6_N_Second : 6;
	uint64_t EHC6_N_Minute : 6;
	uint64_t : 3;
	uint64_t EHC6_N_Hour : 5;
	uint64_t EHC6_N_Day : 5;
	uint64_t EHC6_N_Month : 4;
	uint64_t EHC6_N_Year : 7;
}CDC_EHC_6_t;

// 0x527 length = 8
typedef struct CT_CDC_EHC_6 {
	uint8_t EHC6_N_Year{0};
	uint8_t EHC6_N_Month{0};
	uint8_t EHC6_N_Day{0};
	uint8_t EHC6_N_Hour{0};
	uint8_t EHC6_N_Minute{0};
	uint8_t EHC6_N_Second{0};
}CT_CDC_EHC_6_t;

// 0x570 length = 8
typedef struct BDC_CAPE_1 {
	uint64_t : 59;
	// 5 "Crank Off(Reserve)" 4 "Crank" 3 "Crank Pending(Reserve)" 2 "Run" 1 "Acc" 0 "Off"
	uint64_t CAPE1_St_PowerMode : 3;
	// 2 "Valid" 1 "Invalid" 0 "Not available"
	uint64_t CAPE1_F_PowerMode : 2;
}BDC_CAPE_1_t;

// 0x570 length = 8
typedef struct CT_BDC_CAPE_1 {
	uint8_t CAPE1_F_PowerMode{0};
	uint8_t CAPE1_St_PowerMode{0};
}CT_BDC_CAPE_1_t;

// 0x620 length = 8
typedef struct CDC_IPC1 {
	uint64_t IPC1_N_OdoMeterOffset : 8;
	uint64_t : 8;
	uint64_t IPC1_N_OdoMeter : 32;
	uint64_t : 16;
}CDC_IPC1_t;

// 0x620 length = 8
typedef struct CT_CDC_IPC1 {
	float IPC1_N_OdoMeter{0.0};
	uint8_t IPC1_N_OdoMeterOffset{0};
}CT_CDC_IPC1_t;

// 0x640 length = 8
typedef struct WCBS_ESP_3 {
	uint64_t : 16;
	uint64_t WCBS_ESP3_Checksum : 8;
	uint64_t WCBS_ESP3_RollingCounter : 8;
	uint64_t WCBS_ESP3_N_FRWheelSpeed : 14;
	// 3 "Not used" 2 "Not used" 1 "Value unreliable" 0 "Value OK"
	uint64_t WCBS_ESP3_F_FRWheelSpeed : 2;
	uint64_t WCBS_ESP3_N_FLWheelSpeed : 14;
	// 3 "Not used" 2 "Not used" 1 "Value unreliable" 0 "Value OK"
	uint64_t WCBS_ESP3_F_FLWheelSpeed : 2;
}WCBS_ESP_3_t;

// 0x640 length = 8
typedef struct CT_WCBS_ESP_3 {
	uint8_t WCBS_ESP3_F_FLWheelSpeed{0};
	float WCBS_ESP3_N_FLWheelSpeed{0.0};
	uint8_t WCBS_ESP3_F_FRWheelSpeed{0};
	float WCBS_ESP3_N_FRWheelSpeed{0.0};
	uint8_t WCBS_ESP3_RollingCounter{0};
	uint8_t WCBS_ESP3_Checksum{0};
}CT_WCBS_ESP_3_t;

// 0x650 length = 8
typedef struct WCBS_ESP_4 {
	uint64_t WCBS_ESP4_N_RRWheelSpeed : 14;
	// 3 "Not used" 2 "Not used" 1 "Value unreliable" 0 "Value OK"
	uint64_t WCBS_ESP4_F_RRWheelSpeed : 2;
	uint64_t WCBS_ESP4_N_RLWheelSpeed : 14;
	// 3 "Not used" 2 "Not used" 1 "Value unreliable" 0 "Value OK"
	uint64_t WCBS_ESP4_F_RLWheelSpeed : 2;
	uint64_t WCBS_ESP4_RollingCounter : 8;
	uint64_t WCBS_ESP4_Checksum : 8;
	uint64_t : 16;
}WCBS_ESP_4_t;

// 0x650 length = 8
typedef struct CT_WCBS_ESP_4 {
	uint8_t WCBS_ESP4_Checksum{0};
	uint8_t WCBS_ESP4_RollingCounter{0};
	uint8_t WCBS_ESP4_F_RLWheelSpeed{0};
	float WCBS_ESP4_N_RLWheelSpeed{0.0};
	uint8_t WCBS_ESP4_F_RRWheelSpeed{0};
	float WCBS_ESP4_N_RRWheelSpeed{0.0};
}CT_WCBS_ESP_4_t;

// 0x6e7 length = 8
typedef struct CDC_EHC_12 {
	// 1 "off" 0 "on"
	uint64_t EHC12_Set_RCW : 1;
	// 1 "off" 0 "on"
	uint64_t EHC12_Set_FCTB : 1;
	uint64_t : 10;
	// 1 "off" 0 "on"
	uint64_t EHC12_Set_FCTA : 1;
	uint64_t : 4;
	// 1 "off" 0 "on"
	uint64_t EHC12_Set_RCTB : 1;
	uint64_t : 46;
}CDC_EHC_12_t;

// 0x6e7 length = 8
typedef struct CT_CDC_EHC_12 {
	uint8_t EHC12_Set_RCTB{0};
	uint8_t EHC12_Set_FCTA{0};
	uint8_t EHC12_Set_FCTB{0};
	uint8_t EHC12_Set_RCW{0};
}CT_CDC_EHC_12_t;


static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
	uint32_t const m = 0x1u << (bits - 1);

	return (data & m) ? -(data ^ m) : data;
}

// 0x180 length = 8
static bool decode_SAS_1(CT_SAS_1 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	SAS_1 *p = (SAS_1*)data;

	userData->SAS1_St_SASSensorInformation = p->SAS1_St_SASSensorInformation;
	userData->SAS1_N_SteeringAngle = p->SAS1_N_SteeringAngle * 0.1 - 780;

	return true;
}

// 0x180 length = 8
static bool encode_SAS_1(CT_SAS_1 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	SAS_1 *p = (SAS_1*)data;

	p->SAS1_St_SASSensorInformation = userData->SAS1_St_SASSensorInformation;
	p->SAS1_N_SteeringAngle = (userData->SAS1_N_SteeringAngle + 780) / 0.1;

	return true;
}
// 0x242 length = 8
static bool decode_WCBS_ESP_13(CT_WCBS_ESP_13 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_13 *p = (WCBS_ESP_13*)data;

	userData->WCBS_ESP13_St_LHFWDD = p->WCBS_ESP13_St_LHFWDD;
	userData->WCBS_ESP13_St_RHFWDD = p->WCBS_ESP13_St_RHFWDD;
	userData->WCBS_ESP13_St_LHRWDD = p->WCBS_ESP13_St_LHRWDD;
	userData->WCBS_ESP13_St_RHRWDD = p->WCBS_ESP13_St_RHRWDD;

	return true;
}

// 0x242 length = 8
static bool encode_WCBS_ESP_13(CT_WCBS_ESP_13 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_13 *p = (WCBS_ESP_13*)data;

	p->WCBS_ESP13_St_LHFWDD = userData->WCBS_ESP13_St_LHFWDD;
	p->WCBS_ESP13_St_RHFWDD = userData->WCBS_ESP13_St_RHFWDD;
	p->WCBS_ESP13_St_LHRWDD = userData->WCBS_ESP13_St_LHRWDD;
	p->WCBS_ESP13_St_RHRWDD = userData->WCBS_ESP13_St_RHRWDD;

	return true;
}
// 0x280 length = 8
static bool decode_EMS_5(CT_EMS_5 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	EMS_5 *p = (EMS_5*)data;

	userData->EMS5_St_EngineRunning = p->EMS5_St_EngineRunning;

	return true;
}

// 0x280 length = 8
static bool encode_EMS_5(CT_EMS_5 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	EMS_5 *p = (EMS_5*)data;

	p->EMS5_St_EngineRunning = userData->EMS5_St_EngineRunning;

	return true;
}
// 0x311 length = 8
static bool decode_WCBS_ESP_5(CT_WCBS_ESP_5 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_5 *p = (WCBS_ESP_5*)data;

	userData->WCBS_ESP5_F_VehicleSpeed = p->WCBS_ESP5_F_VehicleSpeed;
	userData->WCBS_ESP5_N_VehicleSpeed = p->WCBS_ESP5_N_VehicleSpeed * 0.05625;

	return true;
}

// 0x311 length = 8
static bool encode_WCBS_ESP_5(CT_WCBS_ESP_5 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_5 *p = (WCBS_ESP_5*)data;

	p->WCBS_ESP5_F_VehicleSpeed = userData->WCBS_ESP5_F_VehicleSpeed;
	p->WCBS_ESP5_N_VehicleSpeed = userData->WCBS_ESP5_N_VehicleSpeed / 0.05625;

	return true;
}
// 0x3e0 length = 8
static bool decode_TCM_2(CT_TCM_2 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	TCM_2 *p = (TCM_2*)data;

	userData->TCM2_F_ShiftLeverPos = p->TCM2_F_ShiftLeverPos;
	userData->TCM2_N_ShiftLeverPos = p->TCM2_N_ShiftLeverPos;

	return true;
}

// 0x3e0 length = 8
static bool encode_TCM_2(CT_TCM_2 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	TCM_2 *p = (TCM_2*)data;

	p->TCM2_F_ShiftLeverPos = userData->TCM2_F_ShiftLeverPos;
	p->TCM2_N_ShiftLeverPos = userData->TCM2_N_ShiftLeverPos;

	return true;
}
// 0x3f0 length = 8
static bool decode_WCBS_ESP_6(CT_WCBS_ESP_6 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_6 *p = (WCBS_ESP_6*)data;

	userData->WCBS_ESP6_F_LongitudeAcc = p->WCBS_ESP6_F_LongitudeAcc;
	userData->WCBS_ESP6_F_LateralAcc = p->WCBS_ESP6_F_LateralAcc;
	userData->WCBS_ESP6_F_YawVelocity = p->WCBS_ESP6_F_YawVelocity;
	userData->WCBS_ESP6_N_LongitudeAcc = p->WCBS_ESP6_N_LongitudeAcc * 0.02 - 25;
	userData->WCBS_ESP6_N_LateralAcc = p->WCBS_ESP6_N_LateralAcc * 0.2 - 25;
	userData->WCBS_ESP6_N_YawVelocity = p->WCBS_ESP6_N_YawVelocity * 0.001 - 2.5;

	return true;
}

// 0x3f0 length = 8
static bool encode_WCBS_ESP_6(CT_WCBS_ESP_6 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_6 *p = (WCBS_ESP_6*)data;

	p->WCBS_ESP6_F_LongitudeAcc = userData->WCBS_ESP6_F_LongitudeAcc;
	p->WCBS_ESP6_F_LateralAcc = userData->WCBS_ESP6_F_LateralAcc;
	p->WCBS_ESP6_F_YawVelocity = userData->WCBS_ESP6_F_YawVelocity;
	p->WCBS_ESP6_N_LongitudeAcc = (userData->WCBS_ESP6_N_LongitudeAcc + 25) / 0.02;
	p->WCBS_ESP6_N_LateralAcc = (userData->WCBS_ESP6_N_LateralAcc + 25) / 0.2;
	p->WCBS_ESP6_N_YawVelocity = (userData->WCBS_ESP6_N_YawVelocity + 2.5) / 0.001;

	return true;
}
// 0x4a0 length = 8
static bool decode_BDC_BCM_1(CT_BDC_BCM_1 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	BDC_BCM_1 *p = (BDC_BCM_1*)data;

	userData->BCM1_Cmd_LeftTurnLamp = p->BCM1_Cmd_LeftTurnLamp;
	userData->BCM1_Cmd_RightTurnLamp = p->BCM1_Cmd_RightTurnLamp;
	userData->BCM1_St_LFDoorLock = p->BCM1_St_LFDoorLock;
	userData->BCM1_St_PasDoorLock = p->BCM1_St_PasDoorLock;
	userData->BCM1_St_DriverDoor = p->BCM1_St_DriverDoor;
	userData->BCM1_St_PassengerDoor = p->BCM1_St_PassengerDoor;
	userData->BCM1_St_LRDoor = p->BCM1_St_LRDoor;
	userData->BCM1_St_RRDoor = p->BCM1_St_RRDoor;

	return true;
}

// 0x4a0 length = 8
static bool encode_BDC_BCM_1(CT_BDC_BCM_1 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	BDC_BCM_1 *p = (BDC_BCM_1*)data;

	p->BCM1_Cmd_LeftTurnLamp = userData->BCM1_Cmd_LeftTurnLamp;
	p->BCM1_Cmd_RightTurnLamp = userData->BCM1_Cmd_RightTurnLamp;
	p->BCM1_St_LFDoorLock = userData->BCM1_St_LFDoorLock;
	p->BCM1_St_PasDoorLock = userData->BCM1_St_PasDoorLock;
	p->BCM1_St_DriverDoor = userData->BCM1_St_DriverDoor;
	p->BCM1_St_PassengerDoor = userData->BCM1_St_PassengerDoor;
	p->BCM1_St_LRDoor = userData->BCM1_St_LRDoor;
	p->BCM1_St_RRDoor = userData->BCM1_St_RRDoor;

	return true;
}
// 0x4b3 length = 8
static bool decode_BDC_BCM_2(CT_BDC_BCM_2 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	BDC_BCM_2 *p = (BDC_BCM_2*)data;

	userData->BCM2_St_LRDoorLock = p->BCM2_St_LRDoorLock;
	userData->BCM2_St_RRDoorLock = p->BCM2_St_RRDoorLock;

	return true;
}

// 0x4b3 length = 8
static bool encode_BDC_BCM_2(CT_BDC_BCM_2 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	BDC_BCM_2 *p = (BDC_BCM_2*)data;

	p->BCM2_St_LRDoorLock = userData->BCM2_St_LRDoorLock;
	p->BCM2_St_RRDoorLock = userData->BCM2_St_RRDoorLock;

	return true;
}
// 0x523 length = 8
static bool decode_CDC_EHC_3(CT_CDC_EHC_3 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	CDC_EHC_3 *p = (CDC_EHC_3*)data;

	userData->EHC3_Set_BSD = p->EHC3_Set_BSD;
	userData->EHC3_Set_LCA = p->EHC3_Set_LCA;
	userData->EHC3_Set_DOW = p->EHC3_Set_DOW;
	userData->EHC3_Set_RCTA = p->EHC3_Set_RCTA;

	return true;
}

// 0x523 length = 8
static bool encode_CDC_EHC_3(CT_CDC_EHC_3 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	CDC_EHC_3 *p = (CDC_EHC_3*)data;

	p->EHC3_Set_BSD = userData->EHC3_Set_BSD;
	p->EHC3_Set_LCA = userData->EHC3_Set_LCA;
	p->EHC3_Set_DOW = userData->EHC3_Set_DOW;
	p->EHC3_Set_RCTA = userData->EHC3_Set_RCTA;

	return true;
}
// 0x527 length = 8
static bool decode_CDC_EHC_6(CT_CDC_EHC_6 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	CDC_EHC_6 *p = (CDC_EHC_6*)data;

	userData->EHC6_N_Year = p->EHC6_N_Year + 2000;
	userData->EHC6_N_Month = p->EHC6_N_Month;
	userData->EHC6_N_Day = p->EHC6_N_Day;
	userData->EHC6_N_Hour = p->EHC6_N_Hour;
	userData->EHC6_N_Minute = p->EHC6_N_Minute;
	userData->EHC6_N_Second = p->EHC6_N_Second;

	return true;
}

// 0x527 length = 8
static bool encode_CDC_EHC_6(CT_CDC_EHC_6 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	CDC_EHC_6 *p = (CDC_EHC_6*)data;

	p->EHC6_N_Year = (userData->EHC6_N_Year - 2000);
	p->EHC6_N_Month = userData->EHC6_N_Month;
	p->EHC6_N_Day = userData->EHC6_N_Day;
	p->EHC6_N_Hour = userData->EHC6_N_Hour;
	p->EHC6_N_Minute = userData->EHC6_N_Minute;
	p->EHC6_N_Second = userData->EHC6_N_Second;

	return true;
}
// 0x570 length = 8
static bool decode_BDC_CAPE_1(CT_BDC_CAPE_1 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	BDC_CAPE_1 *p = (BDC_CAPE_1*)data;

	userData->CAPE1_F_PowerMode = p->CAPE1_F_PowerMode;
	userData->CAPE1_St_PowerMode = p->CAPE1_St_PowerMode;

	return true;
}

// 0x570 length = 8
static bool encode_BDC_CAPE_1(CT_BDC_CAPE_1 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	BDC_CAPE_1 *p = (BDC_CAPE_1*)data;

	p->CAPE1_F_PowerMode = userData->CAPE1_F_PowerMode;
	p->CAPE1_St_PowerMode = userData->CAPE1_St_PowerMode;

	return true;
}
// 0x620 length = 8
static bool decode_CDC_IPC1(CT_CDC_IPC1 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	CDC_IPC1 *p = (CDC_IPC1*)data;

	userData->IPC1_N_OdoMeter = p->IPC1_N_OdoMeter * 0.01;
	userData->IPC1_N_OdoMeterOffset = p->IPC1_N_OdoMeterOffset;

	return true;
}

// 0x620 length = 8
static bool encode_CDC_IPC1(CT_CDC_IPC1 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	CDC_IPC1 *p = (CDC_IPC1*)data;

	p->IPC1_N_OdoMeter = userData->IPC1_N_OdoMeter / 0.01;
	p->IPC1_N_OdoMeterOffset = userData->IPC1_N_OdoMeterOffset;

	return true;
}
// 0x640 length = 8
static bool decode_WCBS_ESP_3(CT_WCBS_ESP_3 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_3 *p = (WCBS_ESP_3*)data;

	userData->WCBS_ESP3_F_FLWheelSpeed = p->WCBS_ESP3_F_FLWheelSpeed;
	userData->WCBS_ESP3_N_FLWheelSpeed = p->WCBS_ESP3_N_FLWheelSpeed * 0.0625;
	userData->WCBS_ESP3_F_FRWheelSpeed = p->WCBS_ESP3_F_FRWheelSpeed;
	userData->WCBS_ESP3_N_FRWheelSpeed = p->WCBS_ESP3_N_FRWheelSpeed * 0.0625;
	userData->WCBS_ESP3_RollingCounter = p->WCBS_ESP3_RollingCounter;
	userData->WCBS_ESP3_Checksum = p->WCBS_ESP3_Checksum;

	return true;
}

// 0x640 length = 8
static bool encode_WCBS_ESP_3(CT_WCBS_ESP_3 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_3 *p = (WCBS_ESP_3*)data;

	p->WCBS_ESP3_F_FLWheelSpeed = userData->WCBS_ESP3_F_FLWheelSpeed;
	p->WCBS_ESP3_N_FLWheelSpeed = userData->WCBS_ESP3_N_FLWheelSpeed / 0.0625;
	p->WCBS_ESP3_F_FRWheelSpeed = userData->WCBS_ESP3_F_FRWheelSpeed;
	p->WCBS_ESP3_N_FRWheelSpeed = userData->WCBS_ESP3_N_FRWheelSpeed / 0.0625;
	p->WCBS_ESP3_RollingCounter = userData->WCBS_ESP3_RollingCounter;
	p->WCBS_ESP3_Checksum = userData->WCBS_ESP3_Checksum;

	return true;
}
// 0x650 length = 8
static bool decode_WCBS_ESP_4(CT_WCBS_ESP_4 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_4 *p = (WCBS_ESP_4*)data;

	userData->WCBS_ESP4_Checksum = p->WCBS_ESP4_Checksum;
	userData->WCBS_ESP4_RollingCounter = p->WCBS_ESP4_RollingCounter;
	userData->WCBS_ESP4_F_RLWheelSpeed = p->WCBS_ESP4_F_RLWheelSpeed;
	userData->WCBS_ESP4_N_RLWheelSpeed = p->WCBS_ESP4_N_RLWheelSpeed * 0.0625;
	userData->WCBS_ESP4_F_RRWheelSpeed = p->WCBS_ESP4_F_RRWheelSpeed;
	userData->WCBS_ESP4_N_RRWheelSpeed = p->WCBS_ESP4_N_RRWheelSpeed * 0.0625;

	return true;
}

// 0x650 length = 8
static bool encode_WCBS_ESP_4(CT_WCBS_ESP_4 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	WCBS_ESP_4 *p = (WCBS_ESP_4*)data;

	p->WCBS_ESP4_Checksum = userData->WCBS_ESP4_Checksum;
	p->WCBS_ESP4_RollingCounter = userData->WCBS_ESP4_RollingCounter;
	p->WCBS_ESP4_F_RLWheelSpeed = userData->WCBS_ESP4_F_RLWheelSpeed;
	p->WCBS_ESP4_N_RLWheelSpeed = userData->WCBS_ESP4_N_RLWheelSpeed / 0.0625;
	p->WCBS_ESP4_F_RRWheelSpeed = userData->WCBS_ESP4_F_RRWheelSpeed;
	p->WCBS_ESP4_N_RRWheelSpeed = userData->WCBS_ESP4_N_RRWheelSpeed / 0.0625;

	return true;
}
// 0x6e7 length = 8
static bool decode_CDC_EHC_12(CT_CDC_EHC_12 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	CDC_EHC_12 *p = (CDC_EHC_12*)data;

	userData->EHC12_Set_RCTB = p->EHC12_Set_RCTB;
	userData->EHC12_Set_FCTA = p->EHC12_Set_FCTA;
	userData->EHC12_Set_FCTB = p->EHC12_Set_FCTB;
	userData->EHC12_Set_RCW = p->EHC12_Set_RCW;

	return true;
}

// 0x6e7 length = 8
static bool encode_CDC_EHC_12(CT_CDC_EHC_12 *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	CDC_EHC_12 *p = (CDC_EHC_12*)data;

	p->EHC12_Set_RCTB = userData->EHC12_Set_RCTB;
	p->EHC12_Set_FCTA = userData->EHC12_Set_FCTA;
	p->EHC12_Set_FCTB = userData->EHC12_Set_FCTB;
	p->EHC12_Set_RCW = userData->EHC12_Set_RCW;

	return true;
}

typedef struct CANFrame {
	uint32_t id;
	uint8_t dlc;
	unsigned char *data{0};
}CANFrame;

#define Frame CANFrame

static bool BJEV_B41V(EP)_ABUS_CMRR_FL_CANFD_V1(Frame *p) {
	switch (p->id) {
	case MSG_ID180_SAS_1:
		decode_SAS_1(0, p->data, p->dlc);
		break;
	case MSG_ID242_WCBS_ESP_13:
		decode_WCBS_ESP_13(0, p->data, p->dlc);
		break;
	case MSG_ID280_EMS_5:
		decode_EMS_5(0, p->data, p->dlc);
		break;
	case MSG_ID311_WCBS_ESP_5:
		decode_WCBS_ESP_5(0, p->data, p->dlc);
		break;
	case MSG_ID3e0_TCM_2:
		decode_TCM_2(0, p->data, p->dlc);
		break;
	case MSG_ID3f0_WCBS_ESP_6:
		decode_WCBS_ESP_6(0, p->data, p->dlc);
		break;
	case MSG_ID4a0_BDC_BCM_1:
		decode_BDC_BCM_1(0, p->data, p->dlc);
		break;
	case MSG_ID4b3_BDC_BCM_2:
		decode_BDC_BCM_2(0, p->data, p->dlc);
		break;
	case MSG_ID523_CDC_EHC_3:
		decode_CDC_EHC_3(0, p->data, p->dlc);
		break;
	case MSG_ID527_CDC_EHC_6:
		decode_CDC_EHC_6(0, p->data, p->dlc);
		break;
	case MSG_ID570_BDC_CAPE_1:
		decode_BDC_CAPE_1(0, p->data, p->dlc);
		break;
	case MSG_ID620_CDC_IPC1:
		decode_CDC_IPC1(0, p->data, p->dlc);
		break;
	case MSG_ID640_WCBS_ESP_3:
		decode_WCBS_ESP_3(0, p->data, p->dlc);
		break;
	case MSG_ID650_WCBS_ESP_4:
		decode_WCBS_ESP_4(0, p->data, p->dlc);
		break;
	case MSG_ID6e7_CDC_EHC_12:
		decode_CDC_EHC_12(0, p->data, p->dlc);
		break;
	default:
		return false;
		break;
	}
	return true;
}

#endif // BJEV_B41V(EP)_ABUS_CMRR_FL_CANFD_V1_H