﻿#ifndef FILEBATCHPARSINGWORKER_H
#define FILEBATCHPARSINGWORKER_H

#include <QObject>

namespace Devices {
    namespace Can {
        class IDeviceWorker;
    }
}

namespace Analysis {
class AnalysisWorker;
class AnalysisSaveWorker;
}

class FileBatchParsingWorker : public QObject
{
    Q_OBJECT
public:
    explicit FileBatchParsingWorker(QObject *parent = nullptr);

    bool start(const QString &dataPath, const QString &savePath, int fileType, bool deleteOld);
    bool stop();

    bool isGenerating() const { return mGenerating; }
    const QString &filesText() const { return mFilesText; }
    const QString &errorString() const { return mErrorString; }

signals:
    void message(const QString &msg);
    void started();
    void stoped();

public slots:
    void generate();


private:
    Devices::Can::IDeviceWorker *mDeviceWorker{0};
    Analysis::AnalysisWorker *mAnalysisWorker{0};
    Analysis::AnalysisSaveWorker *mAnalysisSaveWorker{0};

    bool mGenerating{false};
    bool mDeleteOld{false};

    quint32 mGeneratedCount{0};
    QString mDataPath;
    QString mSavePath;
    QStringList mFiles;
    QString mFilesText;
    QString mErrorString;
};

#endif // FILEBATCHPARSINGWORKER_H
