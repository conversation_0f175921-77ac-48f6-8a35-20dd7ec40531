<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DataProcessForm</class>
 <widget class="QWidget" name="DataProcessForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>767</width>
    <height>60</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>文件:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditFiles">
       <property name="text">
        <string>E:/Data/CAN/dataprocess/CanData 2023-11-30 11-31-39-819.blf</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonFiles">
       <property name="text">
        <string>...</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>雷达：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxRadarID">
       <item>
        <property name="text">
         <string>4</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>5</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>6</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>7</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>跳转</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxGoTo">
       <property name="text">
        <string>至</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditGoTo">
       <property name="maximumSize">
        <size>
         <width>50</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>-1</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonStartAndStop">
       <property name="text">
        <string>开始</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonPauseAndContionue">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="text">
        <string>暂停</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonNextFrame">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="text">
        <string>下一帧</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_5">
       <property name="text">
        <string>帧号:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelFrameNumber">
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>0</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>首帧帧号:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelFirstFrame">
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>0</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_6">
       <property name="text">
        <string>当前帧号：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelCurrentFrame">
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>0</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
