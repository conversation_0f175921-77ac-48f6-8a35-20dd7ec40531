﻿#include "CANProtocolBOSCH670.h"

#define _USE_MATH_DEFINES //需要放在math前,之后才可以使用M_PI等match定义参数
#include <math.h>
#include <iostream>

namespace Analysis {

namespace Protocol {
        CANProtocolBOSCH670::CANProtocolBOSCH670(AnalysisWorker *analysisWorker, QObject *parent)
            : IAnalysisProtocol(analysisWorker, parent)
        {
		}
        CANProtocolBOSCH670::~CANProtocolBOSCH670()
		{
		}
        bool CANProtocolBOSCH670::analysisFrame(const Devices::Can::CanFrame &frame)
        {
            bool ret = false;
            switch (frame.id())
            {
            case 0x64E:
                ret = parse0x64E(frame);
                break;
            case 0x66F:
                ret = parse0x66F(frame);
                break;
            case 0x670:
            case 0x671:
            case 0x672:
            case 0x673:
            case 0x674:
            case 0x675:
            case 0x676:
            case 0x677:
            case 0x678:
            case 0x679:
            case 0x67A:
            case 0x67B:
            case 0x67C:
            case 0x67D:
            case 0x67E:
            case 0x67F:
                ret = parse0x67N(frame);
                break;
            default:
                return false;
            }

//            frame.print(__FUNCTION__, __LINE__);

            return ret;
        }

        bool CANProtocolBOSCH670::parse0x64E(const Devices::Can::CanFrame &frame)
        {
            if (frame.length() != 8) {
                return false;
            }

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            const uint8_t *data = (const uint8_t *)frame.data().data();
            Parser::ParsedDataTypedef::RadarInfomation &radarInfo = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mRadarInfomation;

//            radarInfo.MRR_Checksum_0x64E = (data[0]);
//            radarInfo.MRR_MsgCounter_0x64E = ((data[1] & 0xFFU) + (((uint16_t)data[2] & 0xFFU) << 8));
//            radarInfo.MRR_SensorDirty = (data[4] & 0x1U);
//            radarInfo.MRR_SGUFail = ((data[4] & 0xEU) >> 1);
//            radarInfo.MRR_Modulation_Status = ((data[4] & 0x30U) >> 4);
//            radarInfo.MRR_Failure = ((data[4] & 0xC0U) >> 6);

            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mProtocolType = Parser::ParsedDataTypedef::BOSCH670;
            analysisEnd(Parser::ParsedDataTypedef::TargetTrack);

            return true;
        }

        bool CANProtocolBOSCH670::parse0x66F(const Devices::Can::CanFrame &frame)
		{
            if (frame.length() != 16) {
				return false;
            }

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].resetTargets();

            const uint8_t *data = (const uint8_t *)frame.data().data();
            Parser::ParsedDataTypedef::RadarInfomation &radarInfo = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mRadarInfomation;

//            radarInfo.MRR_Checksum = (data[0]);
//            radarInfo.MRR_MsgCounter = ((data[1] & 0xFFU) + (((uint16_t)data[2] & 0xFFU) << 8));
            radarInfo.mBlockCounter = (data[3] & 0xFU);
            radarInfo.mTimestampStaus = (data[4]);
            radarInfo.mRadarTimestamp =
                    ((data[5] & 0xFFU) +
                    (((uint64_t)data[6]) << 8) +
                    (((uint64_t)data[7]) << 16) +
                    (((uint64_t)data[8]) << 24) +
                    (((uint64_t)data[9]) << 32) +
                    (((uint64_t)data[10] & 0x3U) << 40));
            radarInfo.mTimeLatency = ((data[11] & 0xFFU) + (((uint16_t)data[12] & 0xFFU) << 8));
            radarInfo.mUploadObjectNumber = (data[13] & 0x3FU);

			return true;
        }

        bool CANProtocolBOSCH670::parse0x67N(const Devices::Can::CanFrame &frame)
		{
//            frame.print();
            if (frame.length() != 64) {
                return false;
            }

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);

            const uint8_t *data = (const uint8_t *)frame.data().data();

//            target.ObjID_Checksum_670 = (data[0]);
//            target.ObjID_MsgCounter_670 = ((data[1] & 0xFFU) + (((uint16_t)data[2] & 0xFFU) << 8));
//            target.ObjID_1_BlockCounter_670 = (data[3] & 0xFU);
            //目标1
            if (data[4] != 0xFF) {
                if (data[4] >= TARGET_MAX_COUNT) {
                    return false;
                }

                Parser::ParsedDataTypedef::TargetF &target = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber];
                mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber++;

                target.mValid = true;
                target.mID = (data[4]);
                target.mExistenceProbability = ((data[5] & 0x1FU) * 0.03125);
                target.mObstacleProbability  = (((data[5] & 0xE0U) + (((uint16_t)data[6] & 0x3U) << 3)) * 0.03125);
                target.mY                    = (((data[6] & 0xFCU) + (((uint16_t)data[7] & 0x3FU) << 6)) * 0.0625);
                target.mX                    = ((((data[7] & 0xC0U) + (((uint32_t)data[8]) << 2) + (((uint32_t)data[9] & 0xFU) << 10)) * 0.0078125) - 64);
                target.mAy                   = ((((data[9] & 0xF0U) + (((uint16_t)data[10] & 0x3FU) << 4)) * 0.03125) - 16);
                target.mAx                   = ((((data[10] & 0xC0U) + (((uint16_t)data[11] & 0xFFU) << 2)) * 0.03125) - 16);
                target.mVy                   = ((((data[12] & 0xFFU) + (((uint16_t)data[13] & 0x1FU) << 8)) * 0.03125) - 128);
                target.mVx                   = ((((data[13] & 0xE0U) + (((uint32_t)data[14]) << 3) + (((uint32_t)data[15] & 0x3U) << 11)) * 0.03125) - 128);
                target.mStDevY               = (((data[15] & 0xFCU) >> 2) * 0.0625);
                target.mStDevX               = ((data[16] & 0x3FU) * 0.0625);
                target.mStDevVy              = (((data[16] & 0xC0U) + (((uint16_t)data[17] & 0xFU) << 2)) * 0.0625);
                target.mStDevVx              = (((data[17] & 0xF0U) + (((uint16_t)data[18] & 0x3U) << 4)) * 0.0625);
                target.mStDevAy              = (((data[18] & 0xFCU) >> 2) * 0.0625);
                target.mStDevAx              = ((data[19] & 0x3FU) * 0.0625);
                target.mFusionSignal         = ((data[19] & 0x40U) >> 6);
                target.mFusionCameraID       = (data[20]);

                target.mClassification       = (data[38] & 0x7U);
                target.mDynamicProperty      = ((data[38] & 0x38U) >> 3);
                target.mLength               = ((data[39] & 0x7FU) * 0.125);
                target.mWidth                = ((data[40] & 0x7FU) * 0.125);
                target.mMaintenaceState      = (data[41] & 0x3U);
                target.mLifeCycle            = ((data[41] & 0xFCU) + (((uint16_t)data[42] & 0x3U) << 6));
                target.mRCS                  = ((((data[42] & 0xFCU) + (((uint32_t)data[43]) << 6) + (((uint32_t)data[44] & 0x3U) << 14)) * 0.00390625) - 128);

                target.mX = -target.mX;
                target.mRange = sqrt(target.mY * target.mY + target.mX * target.mX);
                target.mAngle = asinf(target.mX / target.mRange) / M_PI * 180;
                target.mV = target.mVy * cosf(target.mAngle / 180 * M_PI);
            }

            //目标2
            if (data[21] != 0xFF) {
                if (data[21] >= TARGET_MAX_COUNT) {
                    return false;
                }

                Parser::ParsedDataTypedef::TargetF &target = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber];
                mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber++;

                target.mValid = true;
                target.mID = (data[21]);
                target.mExistenceProbability = ((data[22] & 0x1FU) * 0.03125);
                target.mObstacleProbability  = (((data[22] & 0xE0U) + (((uint16_t)data[23] & 0x3U) << 3)) * 0.03125);
                target.mY                    = (((data[23] & 0xFCU) + (((uint16_t)data[24] & 0x3FU) << 6)) * 0.0625);
                target.mX                    = ((((data[24] & 0xC0U) + (((uint32_t)data[25]) << 2) + (((uint32_t)data[26] & 0xFU) << 10)) * 0.0078125) - 64);
                target.mAy                   = ((((data[26] & 0xF0U) + (((uint16_t)data[27] & 0x3FU) << 4)) * 0.03125) - 16);
                target.mAx                   = ((((data[27] & 0xC0U) + (((uint16_t)data[28] & 0xFFU) << 2)) * 0.03125) - 16);
                target.mVy                   = ((((data[29] & 0xFFU) + (((uint16_t)data[30] & 0x1FU) << 8)) * 0.03125) - 128);
                target.mVx                   = ((((data[30] & 0xE0U) + (((uint32_t)data[31]) << 3) + (((uint32_t)data[32] & 0x3U) << 11)) * 0.03125) - 128);
                target.mStDevY               = (((data[32] & 0xFCU) >> 2) * 0.0625);
                target.mStDevX               = ((data[33] & 0x3FU) * 0.0625);
                target.mStDevVy              = (((data[33] & 0xC0U) + (((uint16_t)data[34] & 0xFU) << 2)) * 0.0625);
                target.mStDevVx              = (((data[34] & 0xF0U) + (((uint16_t)data[35] & 0x3U) << 4)) * 0.0625);
                target.mStDevAy              = (((data[35] & 0xFCU) >> 2) * 0.0625);
                target.mStDevAx              = ((data[36] & 0x3FU) * 0.0625);
                target.mFusionSignal         = ((data[36] & 0x40U) >> 6);
                target.mFusionCameraID       = (data[37]);

                target.mClassification       = ((data[44] & 0x38U) >> 3);
                target.mDynamicProperty      = ((data[44] & 0xC0U) + (((uint16_t)data[45] & 0x1U) << 2));
                target.mLength               = (((data[45] & 0xFEU) >> 1) * 0.125);
                target.mWidth                = ((data[46] & 0x7FU) * 0.125);
                target.mMaintenaceState      = (data[47] & 0x3U);
                target.mLifeCycle            = ((data[47] & 0xFCU) + (((uint16_t)data[48] & 0x3U) << 6));
                target.mRCS                  = ((((data[48] & 0xFCU) + (((uint32_t)data[49]) << 6) + (((uint32_t)data[50] & 0x3U) << 14)) * 0.00390625) - 128);

                target.mX = -target.mX;
                target.mRange = sqrt(target.mY * target.mY + target.mX * target.mX);
                target.mAngle = asinf(target.mX / target.mRange) / M_PI * 180;
                target.mV = target.mVy * cosf(target.mAngle / 180 * M_PI);
            }
            return true;
		}
	}
}
