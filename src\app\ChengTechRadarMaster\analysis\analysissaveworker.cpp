﻿#include "analysissaveworker.h"

#include "analysisworker.h"

#include <QDebug>
#include <QThread>

#define BIG_LITTLE_SWAP16(x)        ( (((*(short int *)&x) & 0xff00) >> 8) | \
                                      (((*(short int *)&x) & 0x00ff) << 8) )

namespace Analysis {

AnalysisSaveWorker::AnalysisSaveWorker(AnalysisWorker *analysisWorker, QObject *parent)
    : QObject(parent),
      mAnalysisWorker(analysisWorker)
{

}

bool AnalysisSaveWorker::startSave(const QString &savePath, const QDateTime &beginTime, bool oldStyle, bool first, bool needResponse)
{
    qDebug() << __FUNCTION__ << __LINE__ << first << needResponse;
    mResponsed = !needResponse;
    mSaving = true;
    mSavePath = savePath;
    mOldStyle = oldStyle;

    if( mFileTrackInfo.isOpen() ){
        mFileTrackInfo.close();
    }
    mFileTrackInfo.setFileName(QString("%1/%2 %3.csv").arg(mSavePath).arg("TrackInfo").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
    if (mFileTrackInfo.open(QIODevice::WriteOnly)) {
        QString headerList = "RadarID,TrackID,16TrackNum,MovementTrackNum\n";
        mFileTrackInfo.write(headerList.toLocal8Bit());
        mFileTrackInfo.flush();
    }


    if( mFileLostFrame.isOpen() ){
        mFileLostFrame.close();
    }
    mFileLostFrame.setFileName(QString("%1/%2 %3.csv").arg(mSavePath).arg("LostFrameInfomation").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
    if (mFileLostFrame.open(QIODevice::WriteOnly)) {
        QString headerList = "Time,LostNum,RawMeasurementCount,TrackMeasurementCount\n";
        mFileLostFrame.write(headerList.toLocal8Bit());
        mFileLostFrame.flush();
    }

    if (mFileEarlyWarning.isOpen()) {
        mFileEarlyWarning.close();
    }
    mFileEarlyWarning.setFileName(QString("%1/%2 %3.csv").arg(mSavePath).arg("EarlyWarning").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
    if (mFileEarlyWarning.open(QIODevice::WriteOnly)) {
        QString headerList = "RadarID,Alarm No.,Alarm ID No., Raw No.,Track No.,"
                             "BSD Level,LCA Level,Front DOW Level,Rear DOW Level,RCW Level,RCTA Level,RCTB Level,FCTA Level,FCTB Level,JA Level,"
                             "BSD ID,LCA ID,Front DOW ID,Rear DOW ID,RCW ID,RCTA ID,RCTB ID,FCTA ID,FCTB ID,JA ID,"
                             "LCA TTC,Front DOW TTC,Rear DOW TTC,RCW TTC,RCTA TTC,RCTB TTC,FCTA TTC,FCTB TTC,JA TTC,Save Time\r\n";
        mFileEarlyWarning.write(headerList.toLocal8Bit());

        mFileEarlyWarning.flush();
        memset(mRadarEarlyWarningCount, 0, sizeof (mRadarEarlyWarningCount));
        memset(mRadarEarlyWarningIDCount, 0, sizeof (mRadarEarlyWarningIDCount));
        memset(mRadarEarlyWarningID, -1, sizeof (mRadarEarlyWarningID));
    }

    if (mFileAlarm.isOpen()) {
        mFileAlarm.close();
    }
    mFileAlarm.setFileName(QString("%1/%2 %3.csv").arg(mSavePath).arg("Alarm").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
    if (mFileAlarm.open(QIODevice::WriteOnly)) {
        QString headerList = "RadarID,Alarm No.,Alarm ID No., Raw No.,Track No.,"
                             "BSD Level,LCA Level,Front DOW Level,Rear DOW Level,RCW Level,RCTA Level,RCTB Level,FCTA Level,FCTB Level,JA Level,"
                             "BSD ID,LCA ID,Front DOW ID,Rear DOW ID,RCW ID,RCTA ID,RCTB ID,FCTA ID,FCTB ID,JA ID,"
                             "LCA TTC,Front DOW TTC,Rear DOW TTC,RCW TTC,RCTA TTC,RCTB TTC,FCTA TTC,FCTB TTC,JA TTC,Save Time\r\n";
        mFileAlarm.write(headerList.toLocal8Bit());

        mFileAlarm.flush();
        memset(mRadarAlarmCount, 0, sizeof (mRadarAlarmCount));
        memset(mRadarAlarmIDCount, 0, sizeof (mRadarAlarmIDCount));
        memset(mRadarAlarmID, -1, sizeof (mRadarAlarmID));
    }

    if (mFileAlarm2.isOpen()) {
        mFileAlarm2.close();
    }
    mFileAlarm2.setFileName(QString("%1/%2 %3.csv").arg(mSavePath).arg("Alarm2").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
    if (mFileAlarm2.open(QIODevice::WriteOnly)) {
        QString headerList = "RadarID,Track No,Type,Level,TTC,DDCI,Track ID,Save Time,";
        mFileAlarm2.write(headerList.toLocal8Bit());
        mFileAlarm2.write(gAnalysisTypeNames(TARGET_TYPE_BEGIN, TARGET_TYPE_END - TARGET_TYPE_BEGIN).join(",").toLocal8Bit());
        mFileAlarm2.write("\r\n");
        mFileAlarm2.flush();
    }

    if (mFile200Targets.isOpen()) {
        mFile200Targets.close();
    }
    mFile200Targets.setFileName(QString("%1/%2 %3.csv").arg(mSavePath).arg("200Targets").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
    if (mFile200Targets.open(QIODevice::WriteOnly)) {
        mFile200Targets.write(gAnalysisTypeNames(TARGET_TYPE_BEGIN, TARGET_TYPE_END - TARGET_TYPE_BEGIN).join(",").toLocal8Bit());
        mFile200Targets.write("\r\n");
        mFile200Targets.flush();
    }

    if (mFile16Targets.isOpen()) {
        mFile16Targets.close();
    }
    mFile16Targets.setFileName(QString("%1/%2 %3.csv").arg(mSavePath).arg("16Targets").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
    if (mFile16Targets.open(QIODevice::WriteOnly)) {
        mFile16Targets.write(gAnalysisTypeNames(TARGET_TYPE_BEGIN, TARGET_TYPE_END - TARGET_TYPE_BEGIN).join(",").toLocal8Bit());
        mFile16Targets.write("\r\n");
        mFile16Targets.flush();
    }


    QString saveFile;
    QString saveFileOld;
    for (int i = 0; i < FrameTargetCount; ++i) {
        if (mFile[i].isOpen()) {
            mFile[i].close();
        }
        if (mFileOld[i].isOpen()) {
            mFileOld[i].close();
        }

        switch (i) {
        case FrameRawTarget:
            mFile[i].setFileName(QString("%1/%2 %3.csv").arg(mSavePath).arg("Raw").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
            mFileOld[i].setFileName(QString("%1/%2 %3.old.csv").arg(mSavePath).arg("Raw").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
            break;
        case FrameTrackTarget:
            mFile[i].setFileName(QString("%1/%2 %3.csv").arg(mSavePath).arg("Track").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
            mFileOld[i].setFileName(QString("%1/%2 %3.old.csv").arg(mSavePath).arg("Track").arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
            break;
        default:
            break;
        }
    }

    for (int i = 0; i < FrameTargetCount; ++i) {
        if (!mFile[i].open(QIODevice::WriteOnly)) {
            for (int j = 0; j < i; ++j) {
                mFile[j].close();
                if (mOldStyle) {
                    mFileOld[j].close();
                }
            }

            return false;
        }
        if (mOldStyle && !mFileOld[i].open(QIODevice::WriteOnly)) {
            for (int j = 0; j < i; ++j) {
                mFile[j].close();
                mFileOld[j].close();
            }

            return false;
        }

        mFile[i].write("VehicleHeader,");
        mFile[i].write(gAnalysisTypeNames(VEHICLE_TYPE_BEGIN, VEHICLE_TYPE_END - VEHICLE_TYPE_BEGIN).join(",").toLocal8Bit());
        mFile[i].write("\r\n");
        mFile[i].write("AlarmHeader,");
        mFile[i].write(gAnalysisTypeNames(ALARM_TYPE_BEGIN, ALARM_TYPE_END - ALARM_TYPE_BEGIN).join(",").toLocal8Bit());
        mFile[i].write("\r\n");
        mFile[i].write("EndFrameHeader,");
        mFile[i].write(gAnalysisTypeNames(END_FRAME_TYPE_BEGIN, END_FRAME_TYPE_END - END_FRAME_TYPE_BEGIN).join(",").toLocal8Bit());
        mFile[i].write("\r\n");
        mFile[i].write("TargetHeadHeader,");
        mFile[i].write(gAnalysisTypeNames(TARGET_HEAD_TYPE_BEGIN, TARGET_HEAD_TYPE_END - TARGET_HEAD_TYPE_BEGIN).join(",").toLocal8Bit());
        mFile[i].write("\r\n");
        mFile[i].write(gAnalysisTypeNames(TARGET_TYPE_BEGIN, TARGET_TYPE_END - TARGET_TYPE_BEGIN).join(",").toLocal8Bit());
        mFile[i].write("\r\n");

        mFile[i].flush();

        if (mOldStyle) {
            QString Fileitem = "ID,Range,Speed,Angle,SNR,State,vehicleSpeed,X,Y,X_speed,Y_speed,"
                               "radius,ResponseTaskCycleTime,isUse,cntCandiTrk,noisePtr,yaw_rate,mPitchAngle,"
                               "resv1,resv2,Status,LateralAcceleration,LongitudinalAcceleration,SteeringWheelAngle,ExistProbability,"
                               "BlockageFlag,BlockagePercent,InterferenceFlag,InterferencePercent,"
                               "ErrorInformation0x4DN,ErrorInformation0x4EN\r\n";
            mFileOld[i].write(Fileitem.toLocal8Bit());

            mFileOld[i].flush();
        }
    }

    mSaveCountAll = 0;
    emit saveStarted(mFile[0].fileName());

    if (first)
    {
        qDebug() << __FUNCTION__ << __LINE__ << first;
        memset(mSaveCounts, 0, sizeof (mSaveCounts));
        memset(mSave16TargetsCounts, 0, sizeof (mSave16TargetsCounts));
        connect(mAnalysisWorker, &AnalysisWorker::analysisFinished, this, &AnalysisSaveWorker::analysisRadarData, Qt::DirectConnection);
        connect(mAnalysisWorker, &AnalysisWorker::analysisTargetFinished, this, &AnalysisSaveWorker::analysisTargets, Qt::DirectConnection);
        connect(mAnalysisWorker, &AnalysisWorker::analysisTargetFinishedF, this, &AnalysisSaveWorker::analysisTargetsF, Qt::DirectConnection);
        connect(mAnalysisWorker, &AnalysisWorker::saveResponsed, this, &AnalysisSaveWorker::saveResponsed, Qt::DirectConnection);
    }

    return true;
}

bool AnalysisSaveWorker::stopSave()
{
    mSaving = false;
    disconnect(mAnalysisWorker, &AnalysisWorker::analysisFinished, this, &AnalysisSaveWorker::analysisRadarData);
    disconnect(mAnalysisWorker, &AnalysisWorker::analysisTargetFinished, this, &AnalysisSaveWorker::analysisTargets);
    disconnect(mAnalysisWorker, &AnalysisWorker::analysisTargetFinishedF, this, &AnalysisSaveWorker::analysisTargetsF);
    disconnect(mAnalysisWorker, &AnalysisWorker::saveResponsed, this, &AnalysisSaveWorker::saveResponsed);
    QThread::msleep( 100 ); //睡眠100毫秒，等待消息队列中的数据保存至csv文件，再关闭文件
    for (int i = 0; i < FrameTargetCount; ++i) {
        if (mFile[i].isOpen()) {
            mFile[i].close();
        }
        if (mFileOld[i].isOpen()) {
            mFileOld[i].close();
        }
    }

    if( mFileLostFrame.isOpen() ){
        mFileLostFrame.close();
    }

    if (mFileEarlyWarning.isOpen()) {
        mFileEarlyWarning.close();
    }

    if (mFileAlarm.isOpen()) {
        mFileAlarm.close();
    }

    if (mFileAlarm2.isOpen()) {
        mFileAlarm2.close();
    }

    if (mFile200Targets.isOpen()) {
        mFile200Targets.close();
    }

    if (mFile16Targets.isOpen()) {
        mFile16Targets.close();
    }

    if( mFileTrackInfo.isOpen() ){
        mFileTrackInfo.close();
    }

    emit saveFinished();

    return true;
}

void AnalysisSaveWorker::analysisRadarData(quint8 radarID, const AnalysisData &analysisData)
{
    mAnalysisDatas[radarID] = analysisData;
//    qDebug() << __FUNCTION__ << __LINE__ << radarID
//             << analysisData.mADC_1DFFT_2DFFT_DATA.length()
//             << mAnalysisDatas[radarID].mADC_1DFFT_2DFFT_DATA.length();
    save(radarID);
}

void AnalysisSaveWorker::analysisTargets(quint8 radarID, int frameType)
{
    switch (frameType) {
    case Frame200Raw:
    {
        QDateTime saveTime = QDateTime::currentDateTime();
        mAnalysisDatas[radarID].m200Targets = mAnalysisWorker->mAnalysisDatas[radarID].m200Targets;
        memcpy(mAnalysisDatas[radarID].mTrueTarget, mAnalysisWorker->mAnalysisDatas[radarID].mTrueTarget, sizeof (mAnalysisDatas[radarID].mTrueTarget));
        save200Targets(radarID, saveTime);
    }
        break;
    case Frame16Track:
    {
        QDateTime saveTime = QDateTime::currentDateTime();
        mAnalysisDatas[radarID].m16Targets = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets;
        memcpy(mAnalysisDatas[radarID].mTrueTarget, mAnalysisWorker->mAnalysisDatas[radarID].mTrueTarget, sizeof (mAnalysisDatas[radarID].mTrueTarget));
        save16Targets(radarID, saveTime);
    }
        break;
    default:
        break;
    }
}

void AnalysisSaveWorker::analysisTargetsF(Parser::ParsedDataTypedef::TargetsF targets)
{
    qDebug() << __FUNCTION__ << __LINE__ << "message";
    Parser::ParsedDataTypedef::ParsedDataType dataType = targets.mParsedDataType;
    mParsedData.mTargets[dataType] = targets;
    if (mParsedData.mTargets[dataType].mValid) {
        qDebug() << __FUNCTION__ << __LINE__ << mParsedData.mTargets[dataType].mEffectiveNumber;
        mParsedData.mTargets[dataType].mValid = false;
    }
}

void AnalysisSaveWorker::analysisEarlyAlarmData(quint8 radarID, const AlarmData &alarmData)
{
    mEarlyAlarmData[radarID] = alarmData;
}

bool AnalysisSaveWorker::save(quint8 radarID)
{
//    qDebug() << __FUNCTION__ << __LINE__ << radarID;
    if( !mSaving || !mResponsed){
        return false;
    }
    saveData(radarID);
    mSaveCountAll++;
    if (mSaveCountAll >= mSaveCountMax)
    {
        startSave(mSavePath, QDateTime::currentDateTime(), mOldStyle, false, !mResponsed);
    }

    return true;
}

bool AnalysisSaveWorker::saveData(quint8 radarID)
{
    QDateTime saveTime = QDateTime::currentDateTime();
    bool ret = true;
    saveAlarm(radarID, saveTime);
    saveAlarm2( radarID, saveTime );
    saveLostFrameInfomation( radarID );
    saveTrackInfo( radarID );
    for (int i = 0; i < FrameTargetCount; ++i)
    {
        ret = saveTargets(radarID, (AnalysisFrameType)i, saveTime) && ret;

        if (mOldStyle)
        {
            saveOldTargets(radarID, (AnalysisFrameType)i, saveTime) && ret;
        }
    }

    return  ret;
}

bool AnalysisSaveWorker::saveAlarm(quint8 radarID, QDateTime &saveTime)
{
    QString headerList = "RadarID,Alarm No.,Alarm ID No., Raw No.,Track No.,"
                         "BSD Level,LCA Level,Front DOW Level,Rear DOW Level,RCW Level,RCTA Level,RCTB Level,FCTA Level,FCTB Level,JA Level,"
                         "BSD ID,LCA ID,Front DOW ID,Rear DOW ID,RCW ID,RCTA ID,RCTB ID,FCTA ID,FCTB ID,JA ID,"
                         "LCA TTC,Front DOW TTC,Rear DOW TTC,RCW TTC,RCTA TTC,RCTB TTC,FCTA TTC,FCTB TTC,JA TTC,Save Time\r\n";
    AlarmData &alarmData = mAnalysisDatas[radarID].mAlarmData;
    bool alarm = false;
    qint32 alarmID = -1;
    if (alarmData.mAlarmBSDLevel) {
        alarm  = true;
        alarmID = alarmData.mAlarmBSDObjectID;
    }
    if (alarmData.mAlarmLCALevel) {
        alarm  = true;
        alarmID = alarmData.mAlarmLCAObjectID;
    }
    if (alarmData.mAlarmDOWLevelFront || alarmData.mAlarmDOWLevelRear) {
        alarm  = true;
        alarmID = alarmData.mAlarmDOWObjectID;
    }

    if (alarmData.mAlarmRCWLevel) {
        alarm  = true;
        alarmID = alarmData.mAlarmRCWObjectID;
    }
    if (alarmData.mAlarmRCTALevel) {
        alarm  = true;
        alarmID = alarmData.mAlarmRCTAObjectID;
    }
    if (alarmData.mAlarmRCTBLevel) {
        alarm  = true;
        alarmID = alarmData.mAlarmRCTBObjectID;
    }
    if (alarmData.mAlarmFCTALevel) {
        alarm  = true;
        alarmID = alarmData.mAlarmFCTAObjectID;
    }
    if (alarmData.mAlarmFCTBLevel) {
        alarm  = true;
        alarmID = alarmData.mAlarmFCTBObjectID;
    }
    if (alarmData.mAlarmJALevel) {
        alarm  = true;
        alarmID = alarmData.mAlarmJAObjectID;
    }
    if (alarm) {
        if (mRadarAlarmID[radarID] != alarmID) {
            mRadarAlarmIDCount[radarID] = 0;
        }
        QString text = QString("%1,%2,%3,%4,%5,"
                               "%6,%7,%8,%9,%10,%11,%12,%13,%14,%15,"
                               "%16,%17,%18,%19,%20,%21,%22,%23,%24,%25,"
                               "%26,%27,%28,%29,%30,%31,%32,%33,%34,%35\r\n")
                .arg(radarID)
                .arg(mRadarAlarmCount[radarID])
                .arg(mRadarAlarmIDCount[radarID])
                .arg(mAnalysisDatas[radarID].mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount)
                .arg(mAnalysisDatas[radarID].mTargets[FrameTrackTarget].mTargetHeader.mMeasurementCount)
                .arg(alarmData.mAlarmBSDLevel)
                .arg(alarmData.mAlarmLCALevel)
                .arg(alarmData.mAlarmDOWLevelFront)
                .arg(alarmData.mAlarmDOWLevelRear)
                .arg(alarmData.mAlarmRCWLevel)
                .arg(alarmData.mAlarmRCTALevel)
                .arg(alarmData.mAlarmRCTBLevel)
                .arg(alarmData.mAlarmFCTALevel)
                .arg(alarmData.mAlarmFCTBLevel)
                .arg(alarmData.mAlarmJALevel)
                .arg(alarmData.mAlarmBSDObjectID)
                .arg(alarmData.mAlarmLCAObjectID)
                .arg(alarmData.mAlarmDOWObjectID)
                .arg(alarmData.mAlarmDOWObjectID)
                .arg(alarmData.mAlarmRCWObjectID)
                .arg(alarmData.mAlarmRCTAObjectID)
                .arg(alarmData.mAlarmRCTBObjectID)
                .arg(alarmData.mAlarmFCTAObjectID)
                .arg(alarmData.mAlarmFCTBObjectID)
                .arg(alarmData.mAlarmJAObjectID)
                .arg(alarmData.mAlarmLCAObjectTTC)
                .arg(alarmData.mAlarmDOWObjectTTC)
                .arg(alarmData.mAlarmDOWObjectTTC)
                .arg(alarmData.mAlarmRCWObjectTTC)
                .arg(alarmData.mAlarmRCTAObjectTTC)
                .arg(alarmData.mAlarmRCTBObjectTTC)
                .arg(alarmData.mAlarmFCTAObjectTTC)
                .arg(alarmData.mAlarmFCTBObjectTTC)
                .arg(alarmData.mAlarmJAObjectTTC)
                .arg(saveTime.toString("yyyy-MM-dd hh:mm:ss.zzz"));
        mFileAlarm.write(text.toLocal8Bit());

        mFileAlarm.flush();
        mRadarAlarmCount[radarID]++;
        mRadarAlarmIDCount[radarID]++;
    } else {
        mRadarAlarmCount[radarID] = 0;
        mRadarAlarmIDCount[radarID] = 0;
    }

    mRadarAlarmID[radarID] = alarmID;

    return true;
}

bool AnalysisSaveWorker::saveAlarm2(quint8 radarID, QDateTime &saveTime)
{
    struct alarmItem{
        QString alarmType;
        double alarmLevel;
        double alarmTTC;
        quint32 alarmID;
    };

    AlarmData &alarmData = mAnalysisDatas[radarID].mAlarmData;
    Targets& trackTargets = mAnalysisDatas[radarID].mTargets[FrameTrackTarget];

    QList< alarmItem > alarmList;
    if (alarmData.mAlarmBSDLevel) {
        alarmItem item;
        item.alarmType = "BSD";
        item.alarmLevel = alarmData.mAlarmBSDLevel;
        item.alarmTTC = 0;
        item.alarmID = alarmData.mAlarmBSDObjectID;
        alarmList.push_back( item );
    }
    if (alarmData.mAlarmLCALevel) {
        alarmItem item;
        item.alarmType = "LCA";
        item.alarmLevel = alarmData.mAlarmLCALevel;
        item.alarmTTC = alarmData.mAlarmLCAObjectTTC;
        item.alarmID = alarmData.mAlarmLCAObjectID;
        alarmList.push_back( item );
    }
    if (alarmData.mAlarmDOWFLevel ) {
        alarmItem item;
        item.alarmType = "DOW Front";
        item.alarmLevel = alarmData.mAlarmDOWFLevel;
        item.alarmTTC = alarmData.mAlarmDOWObjectTTC;
        item.alarmID = alarmData.mAlarmDOWObjectID;
        alarmList.push_back( item );
    }
    if ( alarmData.mAlarmDOWRLevel) {
        alarmItem item;
        item.alarmType = "DOW Rear";
        item.alarmLevel = alarmData.mAlarmDOWRLevel;
        item.alarmTTC = alarmData.mAlarmDOWObjectTTC;
        item.alarmID = alarmData.mAlarmDOWObjectID;
        alarmList.push_back( item );
    }

    if (alarmData.mAlarmRCWLevel) {
        alarmItem item;
        item.alarmType = "RCW";
        item.alarmLevel = alarmData.mAlarmRCWLevel;
        item.alarmTTC = alarmData.mAlarmRCWObjectTTC;
        item.alarmID = alarmData.mAlarmRCWObjectID;
        alarmList.push_back( item );
    }
    if (alarmData.mAlarmRCTALevel) {
        alarmItem item;
        item.alarmType = "RCTA";
        item.alarmLevel = alarmData.mAlarmRCTALevel;
        item.alarmTTC = alarmData.mAlarmRCTAObjectTTC;
        item.alarmID = alarmData.mAlarmRCTAObjectID;
        alarmList.push_back( item );
    }
    if (alarmData.mAlarmRCTBLevel) {
        alarmItem item;
        item.alarmType = "RCTB";
        item.alarmLevel = alarmData.mAlarmRCTBLevel;
        item.alarmTTC = alarmData.mAlarmRCTBObjectTTC;
        item.alarmID = alarmData.mAlarmRCTBObjectID;
        alarmList.push_back( item );
    }
    if (alarmData.mAlarmFCTALevel) {
        alarmItem item;
        item.alarmType = "FCTA";
        item.alarmLevel = alarmData.mAlarmFCTALevel;
        item.alarmTTC = alarmData.mAlarmFCTAObjectTTC;
        item.alarmID = alarmData.mAlarmFCTAObjectID;
        alarmList.push_back( item );
    }
    if (alarmData.mAlarmFCTBLevel) {
        alarmItem item;
        item.alarmType = "FCTB";
        item.alarmLevel = alarmData.mAlarmFCTBLevel;
        item.alarmTTC = alarmData.mAlarmFCTBObjectTTC;
        item.alarmID = alarmData.mAlarmFCTBObjectID;
        alarmList.push_back( item );
    }
    if (alarmData.mAlarmJALevel) {
        alarmItem item;
        item.alarmType = "JA";
        item.alarmLevel = alarmData.mAlarmJALevel;
        item.alarmTTC = alarmData.mAlarmJAObjectTTC;
        item.alarmID = alarmData.mAlarmJAObjectID;
        alarmList.push_back( item );
    }


    for( int i=0; i<alarmList.size(); i++ ){
        QString alarmText = QString( "%1,%2,%3,%4,%5,%6,%7,%8," )
                .arg( radarID )
                .arg(mAnalysisDatas[radarID].mTargets[FrameTrackTarget].mTargetHeader.mMeasurementCount)
                .arg( alarmList[i].alarmType )
                .arg( alarmList[i].alarmLevel )
                .arg( alarmList[i].alarmTTC )
                .arg(alarmData.mAlarmRCTBObjectTTC)
                .arg( alarmList[i].alarmID )
                .arg( saveTime.toString("yyyy-MM-dd hh:mm:ss.zzz") );
        mFileAlarm2.write( alarmText.toLocal8Bit() );
        mFileAlarm2.flush();
        if( alarmList[i].alarmID < 0 || alarmList[i].alarmID >=MAX_TARGET_COUNT ){
            continue;
        }

        QString targetText ;
        for (int k = 0; k < trackTargets.mTargetCount; ++k) {
            if (trackTargets.mTargets[k].mID == alarmList[i].alarmID) {
                const Target &trueTarget = trackTargets.mTargets[k];
                for (int j = TARGET_TYPE_BEGIN; j < TARGET_TYPE_END; ++j){
                    targetText.append(QString("%1,").arg(trueTarget.value((AnalysisType)j)));
                }
                break;
            }
        }
        targetText.append("\r\n");
        mFileAlarm2.write( targetText.toLocal8Bit() );
        mFileAlarm2.flush();
    }
    return true;
}

bool AnalysisSaveWorker::save200Targets(quint8 radarID, QDateTime &saveTime)
{
    AnalysisData &analysisData = mAnalysisDatas[radarID];
    Targets &targets = analysisData.mTargets[FrameRawTarget];
    Targets &_200Targets = mAnalysisDatas[radarID].m200Targets;
    qDebug() << __FUNCTION__ << __LINE__ << _200Targets.mTargetCount;

    QString header = "Begin\r\n";
    header.append(QString("RadarID,%1\r\n").arg(radarID));
    header.append(QString("RadarFrameNumber,%1\r\n").arg(targets.mTargetHeader.mMeasurementCount));
    header.append(QString("SaveFrameNumber,%1\r\n").arg(mSave16TargetsCounts[radarID]++));
    header.append(QString("SaveTime,%5\r\n").arg(saveTime.toString("yyyy-MM-dd hh:mm:ss.zzz")));
    mFile200Targets.write(header.toLocal8Bit());

    QString trueText = "TrueBegin\r\n";
    quint64 total = 0;
    const Target *trueTarget = analysisData.mTrueTarget;
    for (int i = 0; i < 5; ++i, ++trueTarget)
    {
        if (!trueTarget->mValid)
        {
//            continue;
        }

        for (int j = TARGET_TYPE_BEGIN; j < TARGET_TYPE_END; ++j)
        {
            trueText.append(QString("%1,").arg(trueTarget->value((AnalysisType)j)));
        }
        trueText.append("\r\n");
        total++;
    }

    trueText.append("TrueEnd\r\n");
    mFile200Targets.write(trueText.toLocal8Bit());

    QString targetsText = "TargetBegin\r\n";
    total = 0;
    Target *target = _200Targets.mTargets;
    for (int i = 0; i < _200Targets.mTargetCount; ++i, ++target)
    {
        if (!target->mValid)
        {
            continue;
        }

//        target->mY = -target->mY - 1.52;
        for (int j = TARGET_TYPE_BEGIN; j < TARGET_TYPE_END; ++j)
        {
            targetsText.append(QString("%1,").arg(target->value((AnalysisType)j)));
        }
        targetsText.append("\r\n");
        total++;
    }
    mFile200Targets.write(targetsText.toLocal8Bit());

    QString tail = QString("TargetEnd,Total,%1\r\nEnd\r\n").arg(total);
    mFile200Targets.write(tail.toLocal8Bit());
    mFile200Targets.flush();
    return true;
}

bool AnalysisSaveWorker::save16Targets(quint8 radarID, QDateTime &saveTime)
{
    AnalysisData &analysisData = mAnalysisDatas[radarID];
    Targets &targets = analysisData.mTargets[FrameTrackTarget];
    Targets &_16Targets = mAnalysisDatas[radarID].m16Targets;

    QString header = "Begin\r\n";
    header.append(QString("RadarID,%1\r\n").arg(radarID));
    header.append(QString("RadarFrameNumber,%1\r\n").arg(targets.mTargetHeader.mMeasurementCount));
    header.append(QString("SaveFrameNumber,%1\r\n").arg(mSave16TargetsCounts[radarID]++));
    header.append(QString("SaveTime,%5\r\n").arg(saveTime.toString("yyyy-MM-dd hh:mm:ss.zzz")));
    mFile16Targets.write(header.toLocal8Bit());

    QString trueText = "TrueBegin\r\n";
    quint64 total = 0;
    const Target *trueTarget = analysisData.mTrueTarget;
    for (int i = 0; i < 5; ++i, ++trueTarget)
    {
        if (!trueTarget->mValid)
        {
//            continue;
        }

        for (int j = TARGET_TYPE_BEGIN; j < TARGET_TYPE_END; ++j)
        {
            trueText.append(QString("%1,").arg(trueTarget->value((AnalysisType)j)));
        }
        trueText.append("\r\n");
        total++;
    }

    trueText.append("TrueEnd\r\n");
    mFile16Targets.write(trueText.toLocal8Bit());

    QString targetsText = "TargetBegin\r\n";
    total = 0;
    Target *target = _16Targets.mTargets;
    for (int i = 0; i < _16Targets.mTargetCount; ++i, ++target)
    {
        if (!target->mValid)
        {
            continue;
        }

//        target->mY = -target->mY - 1.52;
        for (int j = TARGET_TYPE_BEGIN; j < TARGET_TYPE_END; ++j)
        {
            targetsText.append(QString("%1,").arg(target->value((AnalysisType)j)));
        }
        targetsText.append("\r\n");
        total++;
    }
    mFile16Targets.write(targetsText.toLocal8Bit());

    QString tail = QString("TargetEnd,Total,%1\r\nEnd\r\n").arg(total);
    mFile16Targets.write(tail.toLocal8Bit());
    mFile16Targets.flush();
    return true;
}

bool AnalysisSaveWorker::saveTargets(quint8 radarID, AnalysisFrameType frameType, QDateTime &saveTime)
{
    QFile &file = mFile[frameType];
    if (!file.isOpen()) {
        return false;
    }
    AnalysisData &analysisData = mAnalysisDatas[radarID];
    Targets &targets = analysisData.mTargets[frameType];
    VehicleData &vehicleData = analysisData.mVehicleData;
    AlarmData &alarmData = analysisData.mAlarmData;
    EndFrameData &endFrameData = analysisData.mEndFrameData;
    QString header = "Begin\r\n";
    header.append(QString("RadarID,%1,CameraIndex").arg(radarID));
    for (int i = 0; i < sizeof (analysisData.mCameraSaveIndex) / sizeof (analysisData.mCameraSaveIndex[0]); ++i) {
        header.append(QString(",%1").arg(analysisData.mCameraSaveIndex[i]));
    }
    header.append("\r\n");
    header.append(QString("HeSaiLiderSaveIndex,%1\r\n").arg(analysisData.mHeSaiLiderSaveIndex));
    header.append(QString("RadarFrameNumber,%1\r\n").arg(targets.mTargetHeader.mMeasurementCount));
    header.append(QString("SaveFrameNumber,%1\r\n").arg(mSaveCounts[radarID][frameType]));
    header.append(QString("FrameTime,%1\r\n").arg(QDateTime::fromMSecsSinceEpoch(endFrameData.mFrameTime / 1000)
                                                  .toString("yyyy-MM-dd hh:mm:ss.zzz")));
    header.append(QString("SystemFrameTime,%1\r\n").arg(QDateTime::fromMSecsSinceEpoch(endFrameData.mSystemFrameTime / 1000)
                                                        .toString("yyyy-MM-dd hh:mm:ss.zzz")));
    header.append(QString("SaveTime,%5\r\n").arg(saveTime.toString("yyyy-MM-dd hh:mm:ss.zzz")));

    file.write(header.toLocal8Bit());

    QString vehicleText = "VehicleData,";
    for (int i = VEHICLE_TYPE_BEGIN; i < VEHICLE_TYPE_END; ++i)
    {
        vehicleText.append(QString("%1,").arg(vehicleData.value((AnalysisType)i)));
    }
    vehicleText.append("\r\n");
    file.write(vehicleText.toLocal8Bit());

    QString alarmText = "AlarmData,";
    for (int i = ALARM_TYPE_BEGIN; i < ALARM_TYPE_END; ++i)
    {
        alarmText.append(QString("%1,").arg(alarmData.value((AnalysisType)i)));
    }
    alarmText.append(QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4DN, 16).toHex(' ') + ",");
    alarmText.append(QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4EN, 16).toHex(' ') + ",");
    alarmText.append("\r\n");
    file.write(alarmText.toLocal8Bit());

    QString trueText = "TrueBegin\r\n";
    quint64 total = 0;
    const Target *trueTarget = analysisData.mTrueTarget;
    for (int i = 0; i < 5; ++i, ++trueTarget)
    {
        if (!trueTarget->mValid)
        {
//            continue;
        }

        for (int j = TARGET_TYPE_BEGIN; j < TARGET_TYPE_END; ++j)
        {
            trueText.append(QString("%1,").arg(trueTarget->value((AnalysisType)j)));
        }
        trueText.append("\r\n");
        total++;
    }

    trueText.append("TrueEnd\r\n");
    file.write(trueText.toLocal8Bit());

    QString endFrameText = "EndFrameData,";
    for (int i = END_FRAME_TYPE_BEGIN; i < END_FRAME_TYPE_END; ++i)
    {
        endFrameText.append(QString("%1,").arg(endFrameData.value((AnalysisType)i)));
    }
    endFrameText.append("\r\n");
    file.write(endFrameText.toLocal8Bit());

    QString targetHeadText = "TargetHeadData,";
    for (int i = TARGET_HEAD_TYPE_BEGIN; i < TARGET_HEAD_TYPE_END; ++i)
    {
        targetHeadText.append(QString("%1,").arg(targets.mTargetHeader.value((AnalysisType)i)));
    }
    targetHeadText.append("\r\n");
    file.write(targetHeadText.toLocal8Bit());

    QString targetsText = "TargetBegin\r\n";
    total = 0;
//    if (targets.mValid)
    {
        const Target *target = targets.mTargets;
        for (int i = 0; i < targets.mTargetCount; ++i, ++target)
        {
            if (!target->mValid)
            {
                continue;
            }

            for (int j = TARGET_TYPE_BEGIN; j < TARGET_TYPE_END; ++j)
            {
                if (j == Reserve) {
                    targetsText.append(QString("%1,").arg(target->value((AnalysisType)j), 0, 'f'));
                } else {
                    targetsText.append(QString("%1,").arg(target->value((AnalysisType)j)));
                }
            }
            targetsText.append("\r\n");
            total++;
        }
    }
    targetsText.append(QString("TargetEnd,Total,%1\r\n").arg(total));
    file.write(targetsText.toLocal8Bit());

    if (frameType == FrameRawTarget) {
        QString adcText = "append info start\r\n";

//        adcText.append(analysisData.mADC_1DFFT_2DFFT_DATA.toHex(' '));
//        adcText.append("\r\n");

//        qDebug() << __FUNCTION__ << __LINE__ << analysisData.mADC_1DFFT_2DFFT_DATA.length();
        int size = analysisData.mADC_1DFFT_2DFFT_DATA.size();
        short *pData = (short *)analysisData.mADC_1DFFT_2DFFT_DATA.data();
        for (int i = 0; i < size; i += 64) {
            adcText.append(QString::number(i / 64) + ",");
            for (int j = 0; j < 32; pData++, j++) {
                short d = /*BIG_LITTLE_SWAP16*/(*pData);
                adcText.append(QString::number(d) + ",");
            }
            adcText.append("\r\n");
        }

        adcText.append("append info end\r\n");
        file.write(adcText.toLocal8Bit());
    }

    QString tail = QString("End\r\n");
    file.write(tail.toLocal8Bit());


    mSaveCounts[radarID][frameType]++;

    return true;
}

bool AnalysisSaveWorker::saveOldTargets(quint8 radarID, AnalysisFrameType frameType, QDateTime &saveTime)
{
    QFile &file = mFileOld[frameType];
    AnalysisData &analysisData = mAnalysisDatas[radarID];
    Targets &targets = analysisData.mTargets[frameType];
    VehicleData &vehicleData = analysisData.mVehicleData;
    EndFrameData &endFrameData = analysisData.mEndFrameData;
    AlarmData &alarmData = analysisData.mAlarmData;

    QString OneFrame ;
    OneFrame.append(QString("START,FrameNb:%1,")
                    .arg(targets.mTargetHeader.mMeasurementCount));
    QString radarType = "l_bsd";
    switch (radarID) {
    case 4:
        radarType = "l_bsd_r";
        break;
    case 5:
        radarType = "r_bsd_r";
        break;
    case 6:
        radarType = "l_bsd_f";
        break;
    case 7:
        radarType = "r_bsd_f";
        break;
    }
    OneFrame.append(QString("RadarType:").append(radarType + "\n"));

    OneFrame.append(QDateTime::fromMSecsSinceEpoch(endFrameData.mFrameTime).toString("yyyy/MM/dd hh:mm:ss.zzz") + "\n")
            .append(QString::number(saveTime.toMSecsSinceEpoch()));

    int FrameTargetCount = 0;
    for(int i = 0 ;i < targets.mTargetCount; i++)
    {
#if 0
        ifr(targets.mTargets[i].speed > 40
                || targets.mTargets[i].speed < -25
                || targets.mTargets[i].distance <= 0
                )
#endif
            if( targets.mTargets[i].mValid == false )
            {
                continue;//不保存
            }
            else
            {   //有效目标数据，
                //添加Info MoFilelog.append(QString("%1\r\n").arg(MoFilelogNumber));
//                QString Fileitem = "ID,Range,Speed,Angle,SNR,State,vehicleSpeed,X,Y,X_speed,Y_speed,"
//                                   "radius,ResponseTaskCycleTime,isUse,cntCandiTrk,noisePtr,yaw_rate,mPitchAngle,"
//                                   "resv1,resv2,Status,LateralAcceleration,LongitudinalAcceleration,SteeringWheelAngle,ExistProbability\r\n";
//                qDebug() << __FUNCTION__ << __LINE__ << targets.mTargets[i].mTargetIntervalTime;
                OneFrame.append(QString("\n%1,%2,%3,%4,%5,%6,%7,%8,%9,%10,%11,%12,%13,%14,%15,%16,%17,%18,%19,%20,"\
                                        "%21,%22,%23,%24,%25,%26,%27,%28,%29,%30,%31")
                                .arg(targets.mTargets[i].mID)
                                .arg(targets.mTargets[i].mRange)
                                .arg(targets.mTargets[i].mV)
                                .arg(targets.mTargets[i].mAngle)
                                .arg(targets.mTargets[i].mSNR)
                                .arg(/*targets.mTargets[i].KState*/1)
                                .arg(vehicleData.mVehicleSpeed)
                                .arg(targets.mTargets[i].mX)
                                .arg(targets.mTargets[i].mY)
                                .arg(targets.mTargets[i].mVx)
                                .arg(targets.mTargets[i].mVy)
                                .arg(vehicleData.mRadius)
                                .arg(targets.mTargetHeader.mResponseTaskCycleTime)
                                .arg(/*targets.mTargets[i].isUse*/true)
                                .arg(/*DisTTargetInfoFile.cntCandiTrk*/0)
                                .arg(/*noisePtr[0]*/0)
                                .arg(vehicleData.mYawRate)
                                .arg(targets.mTargets[i].mPitchAngle)
                                .arg(/*targets.mTargets[i].resv[0]*/0)
                                .arg(/*targets.mTargets[i].resv[1]*/0)
                                .arg(targets.mTargets[i].mStatus)
                                .arg(vehicleData.mLateralAcceleration)
                                .arg(vehicleData.mLongitudinalAcceleration)
                                .arg(vehicleData.mSteeringWheelAngle)
                                .arg(targets.mTargets[i].mExistProbability)
                                .arg( targets.mTargetHeader.mBlockageFlag)
                                .arg( targets.mTargetHeader.mBlockagePercent)
                                .arg( targets.mTargetHeader.mInterferenceFlag)
                                .arg( targets.mTargetHeader.mInterferencePercent)
                                .arg( QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4DN, 16).toHex(' ').data() )
                                .arg( QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4EN, 16).toHex(' ').data() )

                        );
                FrameTargetCount++;
            }

    }

    //File END
    OneFrame.append(QString("\nEND,Total:%1,Count: %2\n")
                    .arg(FrameTargetCount).arg(targets.mTargetCount));

    file.write(OneFrame.toLatin1());

    mSaveCountsOld[radarID][frameType]++;

    return true;
}

bool AnalysisSaveWorker::saveLostFrameInfomation(quint8 radarID)
{
    AnalysisData &analysisData = mAnalysisDatas[radarID];
    EndFrameData& endFrameData = analysisData.mEndFrameData;
    if( endFrameData.mPreLostFrameFlag ){
        QString line = QString("%1,%2,%3,%4\n")
                .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss:zzz"))
                .arg( endFrameData.mDiff410FrameCount)
                .arg( analysisData.mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount )
                .arg( analysisData.mTargets[FrameTrackTarget].mTargetHeader.mMeasurementCount );
        mFileLostFrame.write( line.toLocal8Bit() );
        mFileLostFrame.flush();
    }
    return true;
}

bool AnalysisSaveWorker::saveTrackInfo(quint8 radarID)
{
    AnalysisData &analysisData = mAnalysisDatas[radarID];
    Targets& targets = analysisData.mTargets[FrameTrackTarget];
    Targets& targets16 = analysisData.m16Targets;

    quint32 targetsValidNum = 0, targets16ValidNum = 0;
    for( int i=0; i<targets.mTargetCount; i++ ){
        if( targets.mTargets[i].mValid && targets.mTargets[i].mDynamicProperty == 0x01 ){
            targetsValidNum++;
        }
    }
    for( int i=0; i<targets.mTargetCount; i++ ){
        if( targets16.mTargets[i].mValid ){
            targets16ValidNum++;
        }
    }

    QString line = QString("%1,%2,%3,%4\n")
                        .arg( radarID )
                        .arg( targets.mTargetHeader.mMeasurementCount)
                        .arg( targets16ValidNum )
                        .arg( targetsValidNum );
    mFileTrackInfo.write( line.toLocal8Bit() );
    mFileTrackInfo.flush();
    return true;
}

} // namespace Analysis
