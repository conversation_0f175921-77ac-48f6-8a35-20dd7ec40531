#ifndef PC_DBG_FW
#include "vdy.h"
#include <math.h>
#include <float.h>
#include <string.h>
#include "rdp_kf_track.h"
#include "rdp_types.h"
#else
#include "vdy.h"
#include <math.h>
#include <float.h>
#include <string.h>
#include "../track/rdp_kf_track.h"
#include "../track/rdp_types.h"
#endif

static const stVehicleStatus gVdyDefault = 
{
	.minSpeed = 0.1f,
	.fastDisCons = 0.5f,
	.slowDisCons = 3.0f,
	.minKalDist = 0.25f,
	.rearCorneringCompliance = 0.0053f,
	.rearDisFront = 3.6f,
	.rearDisRear = 0.75f,
	.LatDis = 0.8f,

	.K = {0.245100392384294f, 0.137377182204297f},
	.kalInit = false,
	.yawEst = 0,
	.disEst = 0
};

static stVehicleStatus gVdy;

void speedProcess(stVehicleStatus *pVdy, const RSP_DetObjectList_t* pDetects, rdp_config_t * config)
{
	#if ((CUSTOMER_PROTOCOL == CUSTOMER_PROTOCOL_GEELY20_15) || (CUSTOMER_PROTOCOL == CUSTOMER_PROTOCOL_GEELY10_4))
	float speedBasedWheel = 0, speedBasedStaticObj = 0;
	float detectVy;
	int staticObjsCnt = 0;
	int i, j, idx, minIdx = 199, maxIdx = 0, sedmaxIdx = 0;
	uint8_t gVelHist[200] = { 0 };

	for (i = 0; i < pDetects->rspDetObjectNum; i++)
	{
		float angle = pDetects->rspDetObject[i].rspDetAzimuthAngle + config->installAngle;

		if (fabsf(angle) > 85 && fabsf(angle) < 95)
		{
			continue;
		}
		if (pDetects->rspDetObject[i].rspDetRange > MAX_RANGE || pDetects->rspDetObject[i].rspDetVelocity > 90)
			continue;

		detectVy = pDetects->rspDetObject[i].rspDetVelocity / cosf(angle / 180.0f * M_PI);
		//后角雷达调整速度，speedBasedStaticObj速度统一为自车前进为正值，自车后退为负值
		detectVy = config->isFront ? detectVy : -detectVy;
		if (fabsf(detectVy + pVdy->speed) < 1.5f * 3.0f)
		{
			if (detectVy <= -50.0f || detectVy > 0.0f)
			{
				continue;
			}

			idx = (int)(detectVy / 0.25f + 199.0f + 0.5f);
			if (idx >= 200 || idx < 0)
			{
				continue;
			}

			gVelHist[idx]++;
			if (idx > maxIdx)
			{
				maxIdx = idx;
			}
			if (idx <= minIdx)
			{
				minIdx = idx;
			}

			staticObjsCnt++;
		}
	}

	if (staticObjsCnt > 0)	// 静止点数量门限：AEB测试等空旷场地或缓行路段静止点可能较少
	{
		for (i = j = minIdx; i <= maxIdx; i++)
		{
			if (gVelHist[i] > gVelHist[j])
			{
				j = i;
			}
		}
		speedBasedStaticObj = -(j - 199) * 0.25f;
	}

	//吉利修改项：自车启动会有车速延迟；
	if (fabsf(pVdy->speed) < 0.1f && pVdy->lngAcceleration > 0.5f
		&& fabsf(speedBasedStaticObj) > 0.1f && fabsf(speedBasedStaticObj) < 0.6f)
	{
		pVdy->compSpeed = config->isFront ? -speedBasedStaticObj : speedBasedStaticObj;
	}
	else
	{
		pVdy->compSpeed = pVdy->speed;
	}
	#else
	pVdy->compSpeed = pVdy->speed;
	#endif
}

void curvatureFilter(stVehicleStatus *pVdy)
{
	if (fabsf(pVdy->compSpeed) < pVdy->minSpeed)
	{
		pVdy->yaw = 0.f;
		pVdy->sinYaw = 0.f;
		pVdy->cosYaw = 1.f;
		pVdy->curvature = 0.f;
		pVdy->radius = FLT_MAX;
		return;
	}

	float curvature = pVdy->yawrate / fabsf(pVdy->compSpeed);
	float alpha = expf(-pVdy->time * fabsf(pVdy->compSpeed) / pVdy->fastDisCons);

	pVdy->curvature += (1.0f - alpha) * (curvature - pVdy->curvature);
	pVdy->yaw = pVdy->curvature * fabsf(pVdy->compSpeed) * pVdy->time;
	pVdy->sinYaw = sinf(pVdy->yaw);
	pVdy->cosYaw = cosf(pVdy->yaw);

	//转弯半径
	if (fabsf(pVdy->compSpeed) > pVdy->minSpeed)
	{
		pVdy->radius = fabsf(pVdy->compSpeed) / pVdy->yawrate;
	}
	else
	{
		pVdy->radius = 0xFFFF;
	}
}

void curvatureKalman(stVehicleStatus *pVdy)
{
	if (pVdy->compSpeed > pVdy->minSpeed)
	{
		pVdy->yawEst += pVdy->yawrate * pVdy->time;
		pVdy->disEst += pVdy->compSpeed * pVdy->time;
	}

	if (pVdy->disEst > pVdy->minKalDist)
	{
		float meas = pVdy->yawEst / pVdy->disEst;

		if (!pVdy->kalInit)
		{
			pVdy->kStates[0] = meas;
			pVdy->kStates[1] = 0;
			pVdy->kalInit = true;
		}
		else
		{
			float states[2];

			states[0] = pVdy->kStates[0] + pVdy->disEst * pVdy->kStates[1];
			states[1] = pVdy->kStates[1];

			float residual = meas - states[0];
			pVdy->kStates[0] = states[0] + pVdy->K[0] * residual;
			pVdy->kStates[1] = states[1] + pVdy->K[1] * residual;
		}

		pVdy->yawEst = 0;
		pVdy->disEst = 0;
	}
}

void calcCurvature(stVehicleStatus *pVdy)
{
	curvatureFilter(pVdy);

	//curvatureKalman(pVdy);
}

void calcSlideSlip(stVehicleStatus *pVdy, rdp_config_t* config)
{
	if (fabsf(pVdy->compSpeed) > 0.1f || pVdy->actualGear == 2)
	{
		pVdy->rearAxleSideSlip = pVdy->rearCorneringCompliance * pVdy->curvature * pVdy->compSpeed * pVdy->compSpeed;

		float yawrate = pVdy->curvature * fabsf(pVdy->compSpeed);

		pVdy->vcsLngVel = pVdy->compSpeed * cosf(pVdy->rearAxleSideSlip) - yawrate * pVdy->LatDis;
		if (config->isFront)
		{
			pVdy->vcsLatVel = pVdy->compSpeed * sinf(pVdy->rearAxleSideSlip) + yawrate * pVdy->rearDisFront;
		}
		else
		{
			pVdy->vcsLatVel = pVdy->compSpeed * sinf(pVdy->rearAxleSideSlip) + yawrate * pVdy->rearDisRear;
		}
		
		pVdy->vcsSlideSlip = atan2f(pVdy->vcsLatVel, pVdy->vcsLngVel);
	}
	else
	{
		pVdy->rearAxleSideSlip = 0.f;
		pVdy->vcsLngVel = 0.f;
		pVdy->vcsLatVel = 0.f;
		pVdy->vcsSlideSlip = 0.f;
	}
}

void vdyProcess(const RSP_DetObjectList_t* pDetects, VDY_DynamicEstimate_t* pVehInfo, float time, rdp_config_t* config)
{
	gVdy.time = time;
	gVdy.speed = config->isFront == 1 ? -pVehInfo->vdySpeedInmps : pVehInfo->vdySpeedInmps;
	switch(config->installPosition)
	{
	case 4:
	case 7:
		gVdy.yawrate = pVehInfo->vdyYawRate; /*/ 180.0f * M_PI;*/
		break;
	case 5:
	case 6:
		gVdy.yawrate = -pVehInfo->vdyYawRate; /*/ 180.0f * M_PI;*/
		break;
	default:
		gVdy.yawrate = pVehInfo->vdyYawRate; /*/ 180.0f * M_PI;*/
		break;
	}
	
	gVdy.latAcceleration = pVehInfo->vdyAccelLat;
	gVdy.lngAcceleration = pVehInfo->vdyAccelLong;
	gVdy.actualGear = pVehInfo->vdyGearState;

	gVdy.wheelSpeed[0] = pVehInfo->vdyWhlSpdFrontRi;
	gVdy.wheelSpeed[1] = pVehInfo->vdyWhlSpdFrontLe;
	gVdy.wheelSpeed[2] = pVehInfo->vdyWhlSpdRearRi;
	gVdy.wheelSpeed[3] = pVehInfo->vdyWhlSpdRearLe;

	//横纵轴距补偿
	if (config->isFront == 0)
	{
		gVdy.rearDisRear = config->installOffsetX;
	}
	else
	{
		gVdy.rearDisFront = config->installOffsetX;
	}
	gVdy.LatDis = config->installOffsetY;

	speedProcess(&gVdy, pDetects, config);

	calcCurvature(&gVdy);

	calcSlideSlip(&gVdy, config);

	pVehInfo->vdySpeedInmps = config->isFront == 1 ? -gVdy.compSpeed : pVehInfo->vdySpeedInmps;
}

const stVehicleStatus *getVdyStatus(void)
{
	return &gVdy;
}

void initVdyStatus(void)
{
	memcpy(&gVdy, &gVdyDefault, sizeof(gVdy));
}
