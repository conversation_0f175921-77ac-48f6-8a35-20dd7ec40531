﻿#include "cameraimage.h"

#include <QPainter>
#include <QDebug>

CameraImage::CameraImage(QWidget *parent) : QWidget(parent)
{

}

void CameraImage::showImage(QImage image, int idx)
{
//    qDebug() << __FUNCTION__ << __LINE__ << image.height() << image.width();
    mImage = image;
    update();
}

void CameraImage::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    QRect Temp(0,0,this->width(),this->height());
    painter.drawImage(Temp,mImage);
}
