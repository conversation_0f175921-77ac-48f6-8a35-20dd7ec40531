/**
 * @file     rdp_types.h
 * @brief    The structure and variables of RDP module are defined in this header file.
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2022-12-26
 *
 *
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2022-12-26  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _RDP_TYPES_H_
#define _RDP_TYPES_H_

#ifndef PC_DBG_FW
#include "app_common.h"
#include "rsp_types.h"
#else
#include "app/vehicle/app_common.h"
#include "hal/rsp/rsp_types.h"
#endif
/*****************************************************************************
  DEFINE
 *****************************************************************************/
#ifndef MAX_RANGE
#define MAX_RANGE       203
#endif

#define RCS_SCALE 10.f
#define RCS_SCALE_FOR_130PRO 256.0f

//客户协议
#define CUSTOMER_PROTOCOL_BYD_16        0
#define CUSTOMER_PROTOCOL_GEELY20_15    1
#define CUSTOMER_PROTOCOL_GEELY10_4     2
#define CUSTOMER_PROTOCOL   CUSTOMER_PROTOCOL_BYD_16

#define DEGREE		2  //护栏阶数
// 护栏定义
#define LEFT_FENCE  1
#define RIGHT_FENCE 2 

#define IS_OUTSIDE_WEEKFENCE_THIRD(lat, lng, param1, param2, param3,param4) (lat < (param1 * POWF_I(lng, 3) + param2 * POWF_I(lng, 2) + param3 * lng + param4 - 0.3))
#define IS_OUTSIDE_STRONGFENCE_THIRD(lat, lng, param1, param2, param3, param4) (lat > (param1 * POWF_I(lng, 3) + param2 * POWF_I(lng, 2) + param3 * lng + param4 + 0.3))

#define IS_OUTSIDE_WEEKFENCE_SECOND(lat, lng, param1, param2, param3) (lat < (param1 * POWF_I(lng, 2) + param2 * lng + param3 - 0.3))
#define IS_OUTSIDE_STRONGFENCE_SECOND(lat, lng, param1, param2, param3) (lat > (param1 * POWF_I(lng, 2) + param2 * lng + param3 + 0.3))
/*****************************************************************************
  ENUM
 *****************************************************************************/
typedef enum
{
    SENSOR_POSITION_REAR_LEFT   = 4,
    SENSOR_POSITION_REAR_RIGHT  = 5,
    SENSOR_POSITION_FRONT_LEFT  = 6,
    SENSOR_POSITION_FRONT_RIGHT = 7,
} rdp_sensorPosition_e;

/*
护栏结构体
*/
typedef struct {
	uint8_t		strongSildLineValid;	//强侧护栏有效位  0-无效， 1-有效
	uint8_t		weekSildLineValid;		//弱侧护栏有效位  0-无效， 1-有效
	int			weekFenceCnt;			//弱侧护栏出现帧数			
	int			weekFenceMiss;			//弱侧护栏消失帧数
	int			strongFenceCnt;			//强侧护栏出现次数
	int			strongFenceMiss;		//强侧护栏消失次数
	float		weekParam[DEGREE + 1];			//弱侧护栏系数
	float		strongParam[DEGREE + 1];			//强侧护栏系数
	float		weekFenceLat;			//弱侧护栏横向距离
	float		strongFenceLat;			//强侧护栏的横向距离
	float		weekFenceRange[2];		//弱侧护栏的起始和结束位置参数
	float		strongFenceRange[2];	//强侧护栏的起始和结束位置参数
}sideLine_pkr_t;

/*****************************************************************************
  STRUCT
 *****************************************************************************/
/*!
 * @brief
 *  Track object information.
 */
typedef struct RDP_trkObjectInfo
{
    float rdpTrkObjStartX;
    float rdpTrkObjStartY;
    float rdpTrkObjDistX;
    float rdpTrkObjDistY;
    float rdpTrkObjDistZ;
    float rdpTrkObjVrelX;
    float rdpTrkObjVrelY;
    float rdpTrkObjVrelZ;
    float rdpTrkObjRelVx;   // 相对速度
    float rdpTrkObjRelVy;
    float rdpTrkObjRelVz;
    float rdpTrkObjArelX;
    float rdpTrkObjArelY;
    float rdpTrkObjArelZ;
    float objDistXStd;
    float objDistYStd;
    float objRelVelXStd;
    float objRelVelYStd;
    float objAccelXStd;
    float objAccelYStd;
    float rdpTrkObjDistStd[9];
    float rdpTrkObjRange;
    float rdpTrkObjVelocity;
    float rdpTrkObjAzimuthAngle;
    float rdpTrkObjElevetionAngle;
    float rdpTrkObjHeadingAngle;
    float rdpTrkObjsnr;
    float rdpTrkObjcenterOffset;
    float rdpTrkObjLength;
    float rdpTrkObjWidth;
    float rdpTrkObjBoxCenterX;
    float rdpTrkObjBoxCenterY;
    float rdpTrkObjBoxLength;
    float rdpTrkObjBoxWidth;
    float nearestTargetX;           // 横向最近目标原始点值（相对于自车），x和y对应的不一定是同一个原始点
    float nearestTargetY;           // 纵向最近目标原始点值（相对于自车）
    float rdpTrkObjMirrorProb;      // J6M新增协议，目标镜像概率，目前默认输出0
    float rdpTrkObjHeadingAngleStd; // J6M新增协议，航向角标准差
    float rdpTrkObjRcwOverLapx;     // RCW在正后方的重叠率
    float rdpTrkObjDowOverLapx;     // DOW在正后方的重叠率
    uint16_t rdpTrkObjVr0_hit;
    uint16_t trkUpdataFlag;
    uint16_t rdpTrkObjOverLapxCnt;     // 重叠率累计计数值
    uint16_t rdpTrkObjStatus;       // @brief 跟踪目标的属性 bit0-动静属性，0-静态，1=动态
	uint16_t rdpLifeCycleCnt;        // @brief 跟踪目标的生命周期计数
    int16_t rdpTrkObjRcs;
    uint16_t rdpTrkObjRCSHighLevel; // 130pro新增协议。真值 = rdpTrkObjRCSHighLevel * 0.00390625 - 128
    int16_t rdpTrkObjAssoDetIdx;    // 关联点原始ID
    uint8_t rdpTrkObjHitCnt;
    uint8_t rdpTrkObjMissCnt;
    uint8_t rdpTrkObjReliability;       // 目标存在置信度，0~100, unit: percentage
    uint8_t rdpTrkObjType;              // 目标类型，0 = Unknown; 1 = pedestran; 2 = motorcycle; 3 = car; 4 = truck
    uint8_t rdpTrkObjReferPosi;         // 目标参考点位置，0=左前，1=右前，2=左后，3=右后
    uint8_t id;
    uint8_t rdpTrackType;           // @brief跟踪目标的类型, 0-None,1-CANDI,2-Track
    uint8_t rdpTrkObjMotionStat;    // 130pro新增协议。0未知，1运动，2静止，3运动停止
    uint8_t rdpTrkObjMotionDir;     // 130pro新增协议。0未知，1同向，2对向，3横穿。默认输出为0
    uint8_t rdpTrkObjDetectionStatus;   // 130pro新增协议。0默认值，1新目标，2正常跟踪目标，3预测目标
    uint8_t rdpTrkObjStableFlag;    //J6M新增协议，目标稳定跟踪标记位；0-false,1-true；目前默认输出true
    uint8_t rdpTrkObjClassification;    //J6M新增协议，目标分类；0-Point 1-Motorcycle 2-Car 3-Truck 4-Prdestrian 5-Bicycle 6-Wide 7-Unclassified
    uint8_t rdpTrkObjDynProp;       //J6M新增协议，目标动态属性；0-Moving 1-Stationary 2-Oncoming 3-Crossing_Left2Right 4-Crossing_Right2Left 5-Unknow 6-Stopped 7-Reserved
    uint8_t rdpTrkObjMaintenanceState;  //J6M新增协议；目标跟踪状态；0-Invalid 1-New 2-Measure 3- Predicted
    uint8_t rdpTrkObjObstacleProb;    //J6M新增协议，目标障碍物概率
    /**
     * @brief 连续被径向速度为零点关联计数。动态目标数值越大，越可能是假点
     * 
     */
	uint8_t rdpTrkObjDowShelterScene;		// 法规DOW遮挡场景标识
    uint8_t rdpTrkObjdowguardCrossCnt;      // 产品护栏DOW路段走查  此类目标报警更加宽松
    uint8_t rdpTrkObjdowguardblocksence;      // 产品走查后方2米遮挡车 护栏路段
} RDP_TrkObjectInfo_t;

typedef struct RDP_trkObjInfo_Geely2_0
{
    //基本指标
    float Dy;               //横向位置-最近点
    float Dx;               //纵向位置-最近点
    float Vy;               //横向速度-对地
    float Vx;               //纵向速度-对地
    float RelVy;            //横向速度-相对
    float RelVx;            //纵向速度-相对
    float Ay;               //横向加速度
    float Ax;               //纵向加速度
    float DyStdDe;          //横向位置的标准差
    float DxStdDe;          //纵向位置的标准差
    float VyStdDe;          //横向速度标准差
    float VxStdDe;          //纵向速度标准差
    float Heading;          //航向角-对地
    //概率
    float NotRealProblty;   //假目标跟踪的概率
    float Conf;             //置信度
    float TypConfVeh;       //分类为车的置信度
    float TypConfBike;      //分类自行车的置信度    
    float TypConfPed;       //分类行人的置信度 
    float ElevnConf;        //俯仰可信度-是否可跨越置信度
    //航迹框
    float BoxWidth;         //box的宽度
    float BoxLength;        //box的长度
    float ObjBoxCenterLgt;  //box中心相对于最近点的纵向位置
    float ObjBoxCenterLat;  //box中心相对于最近点的横向位置
    //跟踪状态
    uint32_t ObservationHist;  //每个周期中目标关联的结果,最近32帧
    uint8_t TrackSts;         //跟踪状态  0-未知  1-updata    2-newTrack  3-预测
    uint8_t TiAlv;            //生命周期 从0到127,到达127后保持
    uint8_t CoastCnt;         //未关联的周期数    最大循环数应该是6.
    uint8_t Typ;              //目标分类  0-未知，4-行人，9-两轮车，10-四轮车
    uint8_t ID;               //航迹ID
    
    //增加
    uint8_t ElevnSts;         //是否可跨越 0-未知  1-可跨越  2-同一个水平面上  3-可从下方开过
    uint8_t IsInFreeSpace;    //是否自由空间内的目标 0-NO 1-Yes
    uint8_t MtnPat;           //目标运动模式 0-未知 1-静态 2-运动到静止 3-运动中 4-远离 5-靠近 6-左到右横穿  7-右到左横穿【客户要求只做0和3】

    //预留项
    // float QualityBits;      //预留
    // float RdrObjUsedTracker;//预留
    // float MirrProblty;      //预留  镜像目标概率
    // float StatyCnt;         //预留  静止目标计数
}ObjInfoGeely2_0;


#define GEELY2_0_SEND_OBJECT_NUM    15
typedef struct RDP_TrkFrameInfo_Geely2_0
{
    uint8_t ReSideRdrLeStsRdrStsBlkdConf;   //雷达遮挡置信度，
    uint8_t ReSideRdrLeStsRdrStsDstbc;      //雷达干扰指示，0表示无干扰，100表示雷达完全饱和
    uint8_t ReSideRdrLeStsRdrStsBlkd0bin;   //雷达是否被遮挡，0-表示雷达没有被遮挡，1-表示雷达被完全挡住。
    uint8_t ReSideRdrLeStsRdrNrObj;         //跟踪目标数量
    uint8_t ReSideRdrLeStsRdrNrDetn;        //点云目标数量
    uint8_t ReSideRdrLeStsRdrStsLatency;    //最后一次测量结束到CAN输出的时间间隔，探测输出最大延迟为80ms
    ObjInfoGeely2_0 obj[GEELY2_0_SEND_OBJECT_NUM];
    uint8_t trkUpdataFlag;                  //跟踪点更新标志，在发送1次后清零，跟踪填充后置1
}RDP_TrkFrameInfoGeely2_0;


typedef struct RDP_TrkObjectList
{
    /// @brief 接口版本号
    uint16_t versionNumber;

    /*! @brief 信号header */
    APP_signalHeader_t signalHeader;

    /*! @brief 跟踪目标的数量 */
    uint16_t rdpDetObjectNum;

    /// @brief 干扰标志
    uint8_t interferedFlag;  

    /// @brief 被干扰的程度
    uint8_t intPercent;
    
    /// @brief 遮挡标志
    uint8_t blockFlag;

    /// @brief 被遮挡的程度
    uint8_t blockPercent;

    /*! @brief 跟踪目标列表，只包含Track类型的目标 */
    RDP_TrkObjectInfo_t rdpTrkObject[MAX_NUM_OF_TRACKS];
} RDP_TrkObjectList_t;

typedef struct
{
    /// @brief 接口版本号
    uint16_t versionNumber;

    /// @brief 信号header
    APP_signalHeader_t signalHeader;

    // 道路模型，f=c[3]*x^3 + c[2]*x^2 + c[1]*x + c[0]
    float coeff[4];

    uint32_t covercnt;  // 右侧雷达可能存在无法被DOW目标穿越的计数值
} RDP_roadDescription_t;

#define NUM_SUBFRAME_MAX 4

typedef struct
{
    rdp_sensorPosition_e installPosition;
    float   installAngle;   // unit: degree
    uint8_t isFront;        // 0: rear, 1:front
    uint8_t upsideDown;     // 0:normal, 1:upsidedown
	uint8_t numSubFrame;
    uint8_t  radarResolutionTestMode;   //当前雷达的测试模式 0-正常路试模式，1-距离分辨率模式(容易分裂)
	float dopplerVelocityScope[NUM_SUBFRAME_MAX];
    uint8_t reserved[3];    //
    uint8_t vdyGearState;        ///<档位信号，单位NA，0x0=invalid,0x1=Gear P,0x2=Gear R,0x3=Gear N,0x4=Gear D,0x5~0xFF=Reserved
    float installOffsetX;   // unit: m
    float installOffsetY;   // unit: m
    float speed;            // 车速,  unit: m/s
    float yawRate;          // 偏航率, unit: rad/s
    float speedDiff;        // 车速变化, unit: m/s
    float yawRateDiff;      // 偏航率变化, unit: rad/s
} rdp_config_t;

//目标需要输出的内容/flag：静止/运动，左护栏/正常，右护栏/正常，井盖/正常，天桥/正常，正前方车道静止目标/正常，，，自信度
//typedef union tagSortFlag
//{
//    uint16_t word;
//    struct
//    {
//        uint16_t res : 3;            //保留 -- 注意CAN协议只能传14位
//        uint16_t backObj : 1;        //后方来的目标 ,侧后方来车假目标
//        uint16_t moveObjectFlag : 1; //移动物体，或是有移动过的物体 标记
//        uint16_t moveObjectType : 3; //0-默认；1-近距离未与原始值匹配但经过预测的点
//        uint16_t moveAttributes : 2; //运动属性 0-不详 ，1来向 ，2去向 ，3静止
//        uint16_t midStatic : 1;      //中间测到绝对静止目标标记
//        uint16_t bridght : 1;        //跳桥/路牌标记
//        uint16_t cover : 1;          //井盖标记
//        uint16_t rightFrence : 1;    //右护栏标记
//        uint16_t leftFence : 1;      //左护栏标记
//        uint16_t unMoveFlag : 1;     //绝对静止标记
//    } bit_big;
//    struct
//    {
//        uint16_t unMoveFlag : 1;     //绝对静止标记
//        uint16_t leftFence : 1;      //左护栏标记
//        uint16_t rightFrence : 1;    //右护栏标记
//        uint16_t cover : 1;          //井盖标记
//        uint16_t bridght : 1;        //跳桥/路牌标记
//        uint16_t midStatic : 1;      //中间测到绝对静止目标标记
//        uint16_t moveAttributes : 2; //运动属性 0-不详 ，1来向 ，2去向 ，3静止 // BSD模式下  0 未标识 1 同向 2 反向
//        uint16_t moveObjectType : 3; //0-默认；1-近距离未与原始值匹配但经过预测的点
//        uint16_t moveObjectFlag : 1; //移动物体，或是有移动过的物体 标记
//        uint16_t backObj : 1;        //后方来的目标 ,侧后方来车假目标，针对BSD功能表示前面来的的反向目标，计算车后面1m后的状态
//        uint16_t matchLevel : 2;     //与跟踪带你的匹配等级 0 是默认不区分 后面等级自定义
//        uint16_t rctaDeg : 1;        //用于rcta的时候判断是否满足角度要求,0表示不满足要求，1表示满足要求，默认不满足，需要5帧计算
//    } bit;
//} TSortFlag;

typedef struct
{
  uint8_t valid; //CDI_STATUS
  uint8_t property;
  int16_t rcs;        // RCS of the detection, unit: 0.1dBsm
  short index;
  short index2;
  short index3;
  short DetIdx;
  float x;
  float y;
  float heighAngle;
  float otgVel;
  float mea_z[4];               // [0]:snr,[1]:range,[2]:velocity,[3]:angle
  float shelterAreaAngle[2];    // 遮挡点角度
  float latLim[2];              // lateral，存储原始点对应的最小和最大横向位置（左右方向）
  float lngLim[2];              // longitudinal, 存储原始点对应的最小和最大纵向位置（前后方向）
  float lngVel;
  float latVel;
  float cosv;
  float sinv;
  uint16_t status;
  uint16_t groupId;
  uint16_t groupId1;
  uint16_t groupId2;
  uint8_t shelterFlag;          // 遮挡点标记
  int8_t fence;				//原始点的护栏标记
  uint8_t weekStrongFence;		//该原始点隶属于强侧护栏还是弱侧护栏
  int8_t aebsidecararea;        // 点迹是否位于AEB遮挡鬼探头区域.
} cdi_t;

typedef struct
{
  uint32_t number;
  uint32_t raw_number;
  cdi_t cdi[MAX_NUM_OF_POINTS];
} cdi_pkg_t;

typedef struct
{
    float rdpTaskTimeCycle;     /* s */
    uint16_t rdpTaskTimeDiff;
    float rspTaskProcessTime;   /* 信号处理耗时统计: s */
} RDP_DebugInfo_t;


#endif
