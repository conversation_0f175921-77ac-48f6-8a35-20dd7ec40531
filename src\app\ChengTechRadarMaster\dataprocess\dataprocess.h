﻿#ifndef DATAPROCESS_H
#define DATAPROCESS_H

#include "dataprocess_global.h"

#include <QWidget>
#include <QObject>
#include <QSharedMemory>
#include <QFile>

#include "devices/canframe.h"
#include "analysis/analysisdata.h"

#include "networktcpclient.h"
#include "networktcpserver.h"

class NetworkTcpServer;
class NetworkTcpClient;
namespace Analysis {
class IInterpolation;
}

class DATAPROCESS_EXPORT DataProcess : public QObject
{
    Q_OBJECT
public:
    static DataProcess *instance();

    ~DataProcess();

    bool init(bool radarFusion = false, quint8 masterRadarID = 4, quint8 sloveRadarID = 5, quint8 Isorgpoint = false, quint8 IsShowCandiObj = false, quint8 eolmode = 0);

    void connectMasterRadar();
    void initEolType(quint8 eolType);
    void setEOLRunState();

    void fileChanged(const QString &filename);
    /** @brief 数据处理 */
    bool process(AnalysisData &analysisData, bool slave = false);
    /** @brief 插值处理 */
    int injection(int radarID, bool ahead, quint64 rawFrameTimeStampGlobal,
                  Devices::Can::FrameTimestamps *timestamps,
                  Devices::Can::stCanTxMsg * frameArray,
                  Analysis::IInterpolation *_interpolation);

    bool setInteractiveData(AnalysisData &analysisData);
    bool getInteractiveData();

    bool setSlaveRadarData(AnalysisData &analysisData);
    bool getSlaveRadarData();

    const AnalysisData &sloveRadarAnalysisData() const { return mSlaveRadarAnalysisData; }

    quint8 masterRadarID() const { return mMasterRadarID; }
    quint8 sloveRadarID() const { return mSlaveRadarID; }

    void clrradardata();

signals:
    void sloveRadarFinished(quint8 radarID);
    void message(const QString &str);

    void openTCPServer(const QString &IP, quint16 port, bool analyIP = false);
    void connectTCPServer(const QString IP, quint16 port);

    void write(const QByteArray &data);

public slots:
    void read(const QByteArray &data);
    void setIsShowCandiObj(bool isShow);

private:
    DataProcess(QObject *parent = nullptr);

    /** @brief 设置算法模块从雷达数据 */
    bool setSlaveData(AnalysisData &analysisData);

    /** @brief 设置算法模块数据 */
    bool setData(AnalysisData &analysisData);

    /** @brief 读取算法模块数据 */
    bool getData(AnalysisData &analysisData);

    /** @brief 读取算法模块数据 */
    bool gettrackData(AnalysisData &analysisData);

    bool warning_info(AnalysisData &analysisData);
    bool warning_write(AnalysisData &analysisData);

    /** @brief 获取报警数据 */
    bool getalarmData(AnalysisData &analysisData);
    bool DataProcess::saveAlarm(AnalysisData &analysisData);


    bool mMasterRadar{true};
    quint8 mMasterRadarID{4};
    quint8 mSlaveRadarID{5};
    quint8 mIsorgpoint{false};
    quint8 mIsShowCandiObj{false};
    quint8 mEolMode{0};
    QString mfilename;
    QString mfilesinglename;
    QFile mFilebackAlarm;   // 告警事件日志
    quint64 mFrameNb;

    QSharedMemory mSharedMemorySlaveRadar;
    QSharedMemory mSharedMemoryInteractive;

    AnalysisData mSlaveRadarAnalysisData;

    NetworkTcpServer *mNetworkTcpServer{0};
    NetworkTcpClient *mNetworkTcpClient{0};
};

#endif // DATAPROCESS_H
