/**
 * @file     aln_dynamicEolFun.h
 * @brief    The header file of aln_dynamicEolFun.c
 * <AUTHOR> (linkang<PERSON>@chengtech.com)
 * @version  1.0
 * @date     2023-02-15
 * 
 * 
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2023-02-15  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _ALN_DYNAMICEOLFUN_H_
#define _ALN_DYNAMICEOLFUN_H_




/*****************************************************************************
  INCLUDES
 *****************************************************************************/
#ifndef PC_DBG_FW
#include "rdp_types.h"
#include "vdy_types.h"
#else
#include "alg/track/rdp_types.h"
#include "app/vehicle/vdy/vdy_types.h"
#include "app/adas/customizedrequirements/adas.h"
#include "other/temp.h"
#endif

#ifdef PC_DBG_FW
typedef enum
{
    ALN_DYNAMIC_EOL_START = 0U,
    ALN_DYNAMIC_EOL_RUNNING = 1,
    ALN_DYNAMIC_EOL_ABORTING = 2,
    ALN_DYNAMIC_EOL_END = 3
} ALN_DYNAMIC_EOL_STATE_T;

typedef enum
{
    ALN_DYNAMIC_INACTIVE = 0U,
    ALN_DYNAMIC_RUNNING = 1,
    ALN_DYNAMIC_ABORTING = 2,
    ALN_DYNAMIC_END = 3,
    ALN_DYNAMIC_MAX_MIN = 4,
    ALN_DYNAMIC_TIMEOUT = 5
} ALN_DYNAMIC_RESULT_T;

typedef enum
{
    ALN_DYNAMIC_EOL_OK = 0U,
    ALN_DYNAMIC_EOL_ERR = 1U,
    ALN_DYNAMIC_EOL_TOSTART = 2U,
} ALN_DYNAMIC_STATUS_T;

typedef struct
{
    ALN_DYNAMIC_EOL_STATE_T state;
    uint32_t last_aln_tick;
    ALN_DYNAMIC_STATUS_T last_aln_status;
    ALN_DYNAMIC_RESULT_T last_aln_result;
    uint32_t err_cnt;

} ALN_DYNAMIC_EOL_T;

void ALN_initEOLMode(void);
void ALN_DynamicEOL_setRun(uint8_t mode);
void ALN_DynamicEOL_setEolType(uint8_t type);
void ALN_DynamicEolAngleCalc_WorkflowProcess(const ADAS_TimeClase_t *pTimeClass);
#endif

typedef struct{
    float fixAngleOffset;   //传入安装角度，希望的安装角度是多少
}CT_DYNAMIC_PARA_t;

typedef struct
{
    float previous_fix_angle; //进入标定前的安装角度，即当前正在使用的安装角度
    float new_fix_angle;      //服务标定成功后计算出的安装角度
    uint8_t run_status;       //标定的过程状态
    uint16_t obj_cnt;         //目标总数
    uint16_t run_step;         //运行过的位置
    uint8_t run_per;          //运行百分比
    uint8_t run_counter;      //运行计数,周期数量
    uint8_t routine_status;   //例程状态
    uint8_t routine_result;   //例程结果
    uint8_t is_save_to_nvm;   //是否已经保存到nvm，需要存入flash的前提是标定成功
    uint32_t service_count;   //进行了服务多少次
    uint32_t start_time;      //每次服务启动的时间
    float hdev_angle;         //水平角度偏差角
} CT_DYNAMIC_EOL_INFO_t;

void ALN_DynamicEOL_toStop(void);
int ALN_DynamicEolAngleCalcMainFun(const cdi_pkg_t *ptrCdiList , const VDY_DynamicEstimate_t *freezedVehDyncDataAddr, float time);
CT_DYNAMIC_EOL_INFO_t *ALN_getDynamicEolInfo();
void Service31Handle_DynamicEol(int subServerNumber);

void ALN_clearDynamicEolSaveFlag();

void StartALN_DynamicEolFun(void *param, void *pParam);

extern uint8_t ALN_DynamicEolFun_Flag;
uint8_t getALN_DynamicEolFun_Flag();
uint8_t getBYD_DynamicRunStatus(void);

#endif

