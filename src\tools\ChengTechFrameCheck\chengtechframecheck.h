﻿#ifndef CHENGTECHFRAMECHECK_H
#define CHENGTECHFRAMECHECK_H

#include <QMainWindow>

#include <devices/ideviceworker.h>

namespace Devices {
namespace Can {
class DeviceManager;
}
}

class CTAnalysisWorker;

QT_BEGIN_NAMESPACE
namespace Ui { class ChengTechFrameCheck; }
QT_END_NAMESPACE

class ChengTechFrameCheck : public QMainWindow
{
    Q_OBJECT

public:
    ChengTechFrameCheck(QWidget *parent = nullptr);
    ~ChengTechFrameCheck();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void radarConnected(int c, int r);
    void frameError(int c, int r);

    void on_actionOpenDevice_triggered();

    void on_actionSelectDevice_triggered();

    void on_actionSetOutTime_triggered();

    void on_actionReset_triggered();

private:
    void workupRadar();
    void log(const QString &text);

    /** @brief 设备发生改变 */
    void deviceChanged(Devices::Can::DeviceSettings deviceSettings);

    void loadSettings();
    void savesettings();

private:
    Ui::ChengTechFrameCheck *ui;

    Devices::Can::DeviceManager *mDeviceManager{0};
    CTAnalysisWorker *mCTAnalysisWorker{0};
};
#endif // CHENGTECHFRAMECHECK_H
