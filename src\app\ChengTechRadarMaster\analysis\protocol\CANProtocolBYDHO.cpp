﻿#include "CANProtocolBYDHO.h"

#define _USE_MATH_DEFINES //需要放在math前,之后才可以使用M_PI等match定义参数
#include <math.h>
#include <iostream>

namespace Analysis {

namespace Protocol {
static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
    uint32_t mask = ((1 << bits) - 1);
    uint32_t extracted = data & mask;
    int32_t sign_extended = (extracted & (1 << (bits - 1))) ? (int)(extracted | (~mask)) : (int)extracted;
    return sign_extended;
}

static uint32_t encode_sign_bit(int32_t data, uint8_t bits) {
    uint32_t const m = 0x1u << (bits - 1);

    return (data & m) ? (data | m) : data;
}

CANProtocolBYDHO::CANProtocolBYDHO(AnalysisWorker *analysisWorker, QObject *parent) : IAnalysisProtocol(analysisWorker, parent)
{
    mProtocolType = ProtocolBYDHO_F;
}

void CANProtocolBYDHO::setChannelRadarID(int *channelRadarID, int size)
{
    for (int i = 0; i < size && i < (sizeof (mBYDHDChannelRadarID) / sizeof (mBYDHDChannelRadarID[0])); ++i) {
        mBYDHDChannelRadarID[i] = channelRadarID[i];
        qDebug() << __FUNCTION__ << __LINE__ << channelRadarID[i] << mBYDHDChannelRadarID[i];
    }
}

bool CANProtocolBYDHO::analysisFrame(const Devices::Can::CanFrame &frame)
{
    int radarID = 0;

    if (frame.channelIndex() >= 5) {
        return false;
    }
    radarID = mBYDHDChannelRadarID[frame.channelIndex()];
    if (radarID != 0) {
        return false;
    }

    if (mParse0x659) {
        return parse_0x659(frame);
    } else {
        return parse_0x61A(frame);
    }

    return false;
}

bool CANProtocolBYDHO::parse_0x61A(const Devices::Can::CanFrame &frame)
{
    bool ret = false;

    switch (frame.id()) {
    case 0x61A: // 0x659
    case 0x61B:
    case 0x61C:
    case 0x61D:
    case 0x61E:
    case 0x61F:
    case 0x620:
    case 0x621:
    case 0x622:
    case 0x623:
    case 0x624:
    case 0x625:
    case 0x626:
    case 0x627:
    case 0x628:
    case 0x629:
    case 0x62A:
    case 0x62B:
    case 0x62C:
    case 0x62D: // 0x66c
        ret = parse_0x61A_0x659(frame);
        break;
    case 0x62E:
        ret = parse_0x62E_0x66D(frame);
        break;
    case 0x630:
        ret = parse_0x630_0x670(frame);
        break;
    case 0x64E: // 0x66F
        ret = parse_0x64E_0x66F(frame);
//        qDebug() << __FUNCTION__ << __LINE__ << QString::number(id, 16) << dataArray.toHex(' ');
        break;
    default:
        return false;
    }

    return ret;
}

bool CANProtocolBYDHO::parse_0x659(const Devices::Can::CanFrame &frame)
{
    bool ret = false;

    switch (frame.id()) {
    case 0x659:
    case 0x65A:
    case 0x65B:
    case 0x65C:
    case 0x65D:
    case 0x65E:
    case 0x65F:
    case 0x660:
    case 0x661:
    case 0x662:
    case 0x663:
    case 0x664:
    case 0x665:
    case 0x666:
    case 0x667:
    case 0x668:
    case 0x669:
    case 0x66A:
    case 0x66B:
    case 0x66C:
        ret = parse_0x61A_0x659(frame);
        break;
    case 0x66D:
        ret = parse_0x62E_0x66D(frame);
        break;
    case 0x670:
        ret = parse_0x630_0x670(frame);
        break;
    case 0x66F:
        ret = parse_0x64E_0x66F(frame);
        break;
    default:
        return false;
    }

    return ret;
}

bool CANProtocolBYDHO::parse_0x61A_0x659(const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![64]!" << frame.idHex() << frame.dataHex();
        return false;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << QString::number(id, 16) << dataArray.toHex(' ');
    Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
    const uint8_t *data = (const uint8_t *)frame.data().data();

//    target->Checksum_61A_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    target->Counter_61A_S = (data[2]);

    if (data[52] != 0xFF) {
        Parser::ParsedDataTypedef::TargetF *target = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mTargets + mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber;
        target->mValid = true;

        target->mID = (data[52]);

        float x  = ((((data[3] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.0875) - 100);
        float y  = (((((data[4] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0xFFU) << 4)) * 0.0875) - 179.113);
        float vx = ((((data[6] & 0xFFU) + (((uint16_t)data[7] & 0x7U) << 8)) * 0.125) - 128);
        float vy = (((((data[7] & 0xF8U) >> 3) + (((uint16_t)data[8] & 0x3FU) << 5)) * 0.125) - 128);
        float ax = (((((data[8] & 0xC0U) >> 6) + (((uint16_t)data[9] & 0x7FU) << 2)) * 0.125) - 31.875);
        float ay = (((((data[9] & 0x80U) >> 7) + (((uint16_t)data[10] & 0xFFU) << 1)) * 0.125) - 31.875);
        target->mStDevY = ((data[11]) * 0.05);
        target->mStDevX = - ((data[12]) * 0.1);
        target->mStDevVy = ((data[13] & 0x7FU) * 0.1);
        target->mStDevVx = - ((((data[13] & 0x80U) >> 7) + (((uint16_t)data[14] & 0x3FU) << 1)) * 0.2);
        target->mStDevAy = ((((data[14] & 0xC0U) >> 6) + (((uint16_t)data[15] & 0x1FU) << 2)) * 0.25);
        target->mStDevAx = -((((data[15] & 0xE0U) >> 5) + (((uint16_t)data[16] & 0x1FU) << 3)) * 0.25);
        target->mRCS = (((((data[16] & 0xE0U) >> 5) + (((uint16_t)data[17] & 0x3FU) << 3)) * 0.2) - 51.1);
        target->mLength = ((((data[17] & 0xC0U) >> 6) + (((uint16_t)data[18] & 0x1FU) << 2)) * 0.2);
        target->mWidth = ((((data[18] & 0xE0U) >> 5) + (((uint16_t)data[19] & 0xFU) << 3)) * 0.2);
        target->mTrackAngle = (((((data[19] & 0xF0U) >> 4) + (((uint16_t)data[20] & 0x3FU) << 4)) * 0.01) - 5.11);
        target->mTrackAngleStd = ((((data[20] & 0xC0U) >> 6) + (((uint16_t)data[21] & 0x3U) << 2)) * 0.002);
        target->mTrackReferencePoint = ((data[21] & 0xCU) >> 2);
        target->mClassificationAdvanced = ((data[21] & 0x70U) >> 4);
        target->mMotionState = (((data[21] & 0x80U) >> 7) + (((uint16_t)data[22] & 0x3U) << 1));
        target->mExistProbability = (((data[22] & 0xFCU) >> 2) + (((uint16_t)data[23] & 0x1U) << 6));
        target->mMirrorProbability = ((data[23] & 0xFEU) >> 1);
        target->mObstacleProbability = (data[24] & 0x7FU);
        target->mMaintenanceState = (((data[24] & 0x80U) >> 7) + (((uint16_t)data[25] & 0x1U) << 1));
        target->mLifeCycle = (((data[25] & 0xFEU) >> 1) + (((uint32_t)data[26]) << 7) + (((uint32_t)data[27] & 0x1U) << 15));

//        if(target->mID == 41)
//        {
//            qDebug() << __FUNCTION__ << __LINE__ << target->mID << y << x << vy << vx << ay << ax;
//        }

        target->mVx = vy;
        target->mVy = vx;
        target->mRange = QString::number(sqrt(x * x + y * y), 'f',2).toFloat();
        target->mAngle = QString::number(asinf(y / target->mRange) / M_PI * 180,'f',2).toFloat();
        target->mV = QString::number(vx * cosf(target->mAngle / 180 * M_PI), 'f',2).toFloat();
        target->mX = y;
        target->mY = x;
        target->mAx = ay;
        target->mAy = ax;

//        if (target->ID == 28)
//            qDebug() << __FUNCTION__ << __LINE__ << target->ID << target->X_Lateral << target->Y_Longitudinal
//                     << ARS_OD_Classification_Obj_00 << ARS_OD_DynProp_Obj_00;

        mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber++;
    }


    if (data[53] != 0xFF) {
        Parser::ParsedDataTypedef::TargetF *target = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mTargets + mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber;
        target->mValid = true;

        target->mID = (data[53]);

        float x  = (((((data[27] & 0xFEU) >> 1) + (((uint16_t)data[28] & 0x1FU) << 7)) * 0.0875) - 100);
        float y  = (((((data[28] & 0xE0U) >> 5) + (((uint32_t)data[29]) << 3) + (((uint32_t)data[30] & 0x1U) << 11)) * 0.0875) - 179.113);
        float vx = (((((data[30] & 0xFEU) >> 1) + (((uint16_t)data[31] & 0xFU) << 7)) * 0.125) - 128);
        float vy = (((((data[31] & 0xF0U) >> 4) + (((uint16_t)data[32] & 0x7FU) << 4)) * 0.125) - 128);
        float ax = (((((data[32] & 0x80U) >> 7) + (((uint16_t)data[33] & 0xFFU) << 1)) * 0.125) - 31.875);
        float ay = ((((data[34] & 0xFFU) + (((uint16_t)data[35] & 0x1U) << 8)) * 0.125) - 31.875);
        target->mStDevY = ((((data[35] & 0xFEU) >> 1) + (((uint16_t)data[36] & 0x1U) << 7)) * 0.05);
        target->mStDevX = - ((((data[36] & 0xFEU) >> 1) + (((uint16_t)data[37] & 0x1U) << 7)) * 0.1);
        target->mStDevVy = (((data[37] & 0xFEU) >> 1) * 0.1);
        target->mStDevVx = - ((data[38] & 0x7FU) * 0.2);
        target->mStDevAy = ((((data[38] & 0x80U) >> 7) + (((uint16_t)data[39] & 0x3FU) << 1)) * 0.25);
        target->mStDevAx = - ((((data[39] & 0xC0U) >> 6) + (((uint16_t)data[40] & 0x3FU) << 2)) * 0.25);
        target->mRCS = (((((data[40] & 0xC0U) >> 6) + (((uint16_t)data[41] & 0x7FU) << 2)) * 0.2) - 51.1);
        target->mLength = ((((data[41] & 0x80U) >> 7) + (((uint16_t)data[42] & 0x3FU) << 1)) * 0.2);
        target->mWidth = ((((data[42] & 0xC0U) >> 6) + (((uint16_t)data[43] & 0x1FU) << 2)) * 0.2);
        target->mTrackAngle = (((((data[43] & 0xE0U) >> 5) + (((uint16_t)data[44] & 0x7FU) << 3)) * 0.01) - 5.11);
        target->mTrackAngleStd = ((((data[44] & 0x80U) >> 7) + (((uint16_t)data[45] & 0x7U) << 1)) * 0.002);
        target->mTrackReferencePoint = ((data[45] & 0x18U) >> 3);
        target->mClassificationAdvanced = ((data[45] & 0xE0U) >> 5);
        target->mMotionState = (data[46] & 0x7U);
        target->mExistProbability = (((data[46] & 0xF8U) > 3) + (((uint16_t)data[47] & 0x3U) << 5));
        target->mMirrorProbability = (((data[47] & 0xFCU) >> 2) + (((uint16_t)data[48] & 0x1U) << 6));
        target->mObstacleProbability = ((data[48] & 0xFEU) >> 1);
        target->mMaintenanceState = (data[49] & 0x3U);
        target->mLifeCycle = (((data[49] & 0xFCU) >> 2) + (((uint32_t)data[50]) << 6) + (((uint32_t)data[51] & 0x3U) << 14));

//        if(target->mID == 41)
//        {
//            qDebug() << __FUNCTION__ << __LINE__ << target->mID << y << x << vy << vx << ay << ax;
//        }

        target->mVx = vy;
        target->mVy = vx;
        target->mRange = QString::number(sqrt(x * x + y * y), 'f',2).toFloat();
        target->mAngle = QString::number(asinf(y / target->mRange) / M_PI * 180,'f',2).toFloat();
        target->mV = QString::number(vx * cosf(target->mAngle / 180 * M_PI), 'f',2).toFloat();
        target->mX = y;
        target->mY = x;
        target->mAx = ay;
        target->mAy = ax;

//        if (target->ID == 28)
//            qDebug() << __FUNCTION__ << __LINE__ << target->ID << target->X_Lateral << target->Y_Longitudinal
//                     << ARS_OD_Classification_Obj_01 << ARS_OD_DynProp_Obj_01;

        mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mEffectiveNumber++;
    }
    return true;
}

bool CANProtocolBYDHO::parse_0x62E_0x66D(const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![64]!" << frame.idHex() << frame.dataHex();
        return false;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << QString::number(id, 16) << dataArray.toHex(' ');

    Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
    const uint8_t *data = (const uint8_t *)frame.data().data();

//    target->Checksum_62E_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    target->Counter_62E_S = (data[2]);
    quint64 mesTimestamp = ((data[3] & 0xFFU) + (((uint32_t)data[4]) << 8) + (((uint32_t)data[5]) << 16) + (((uint32_t)data[6] & 0xFFU) << 24));
//    qDebug() << __FUNCTION__ << __LINE__ << mesTimestamp;
    quint64 timestamp = ((data[7] & 0xFFU) + (((uint32_t)data[8]) << 8) + (((uint32_t)data[9]) << 16) + (((uint32_t)data[10] & 0xFFU) << 24));
//    qDebug() << __FUNCTION__ << __LINE__ << mDataAnalysis->DataAnalysisTTargetInfoFrame->timestamp;
    timestamp = mesTimestamp * 1000 + timestamp / 1000000;

    mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mRadarInfomation.mRadarTimestamp = timestamp;
    qDebug() << __FUNCTION__ << __LINE__ << mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mRadarInfomation.mRadarTimestamp;
//    qDebug() << __FUNCTION__ << __LINE__ << mDataAnalysis->DataAnalysisTTargetInfoFrame->timestamp;
//    target->ARS_RDI_TimeStampLocal = ((data[11] & 0xFFU) + (((uint32_t)data[12]) << 8) + (((uint32_t)data[13]) << 16) + (((uint32_t)data[14] & 0xFFU) << 24));
//    target->ARS_RDI_Latency = ((((data[15] & 0xFFU) + (((uint16_t)data[16] & 0x7U) << 8)) * 0.1) + 30);
//    target->ARS_RDI_MeasCounter = ((data[16] & 0xF8U) + (((uint32_t)data[17]) << 5) + (((uint32_t)data[18] & 0x7U) << 13));
//    target->ARS_RDI_CycleCounter = ((data[18] & 0xF8U) + (((uint32_t)data[19]) << 5) + (((uint32_t)data[20] & 0x7U) << 13));
//    target->ARS_RDI_NumOfClusters = ((data[20] & 0xF8U) + (((uint16_t)data[21] & 0xFU) << 5));
//    target->ARS_RDI_AmbigFreeDopplerRange = (((data[21] & 0xF0U) + (((uint16_t)data[22] & 0xFFU) << 4)) * 0.01);
//    target->ARS_RDI_MaxDetectionRange = (data[23]);
//    target->ARS_RDI_RangeResolution = ((data[24] & 0x7FU) * 0.01);
//    target->ARS_RDI_TaskValidFlag = ((data[24] & 0x80U) >> 7);
//    target->ARS_RDI_ExtendedCycleFlag = (data[25] & 0x1U);
    mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mVehicleInfomation.mV = (((((data[25] & 0xFEU) >> 1) + (((uint16_t)data[26] & 0xFU) << 7)) * 0.125) - 128);
//    target->ARS_RDI_EgoVelocityStd = (((data[26] & 0xF0U) + (((uint16_t)data[27] & 0x7U) << 4)) * 0.1);
//    target->ARS_RDI_EgoAcceleration = ((((data[27] & 0xF8U) + (((uint16_t)data[28] & 0xFU) << 5)) * 0.125) - 31.875);
    mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mVehicleInfomation.mYawRate = -(((((data[28] & 0xF0U) >> 4) + (((uint16_t)data[29] & 0x3FU) << 4)) * 0.001) - 0.511) * 180.0f / M_PI;
//    target->ARS_RDI_EgoCurvature = (((data[29] & 0xC0U) + (((uint16_t)data[30] & 0x3FU) << 2)) * 0.001);
//    target->ARS_RDI_TimesynSts = (data[31]);

    return true;
}

bool CANProtocolBYDHO::parse_0x630_0x670(const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 32) {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![32]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
    Parser::ParsedDataTypedef::RadarInfomation &radarInfo = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mRadarInfomation;
    const uint8_t *data = (const uint8_t *)frame.data().data();

//    userData->Checksum_630_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    userData-> = ((data[0]) * 0);
//    userData-> = ((data[0]) * 0);
//    userData->Counter_630_S = (data[2]);
    radarInfo.mPolynomialL.c     = ((((data[3] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.001) - 2.047);
    radarInfo.mPolynomialL.b     = (((((data[4] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0x3FU) << 4)) * 0.0001) - 0.0511);
    radarInfo.mPolynomialL.a     = (((((data[5] & 0xC0U) >> 6) + (((uint16_t)data[6] & 0xFFU) << 2) + (((uint16_t)data[7] & 0x01U) << 10)) * 0.000001) -0.001023);
    radarInfo.mPolynomialL.d     = ((((data[7] & 0xFEU) >> 1) + (((uint16_t)data[8] & 0x1U) << 7)) * 0.1);
    radarInfo.mPolynomialR.d     = (((((data[8] & 0xFEU) >> 1) + (((uint16_t)data[9] & 0x1U) << 7)) * 0.1) - 25.5);
    radarInfo.mPolynomialL.begin = ((((data[9] & 0xFEU) >> 1) + (((uint16_t)data[10] & 0x3U) << 7)) * 0.5);
    radarInfo.mPolynomialR.begin = ((((data[10] & 0xFCU) >> 2) + (((uint16_t)data[11] & 0x7U) << 6)) * 0.5);
    radarInfo.mPolynomialL.end   = ((((data[11] & 0xF8U) >> 3) + (((uint16_t)data[12] & 0xFU) << 5)) * 0.5);
    radarInfo.mPolynomialR.end   = ((((data[12] & 0xF0U) >> 4) + (((uint16_t)data[13] & 0x1FU) << 4)) * 0.5);
//    userData->ARS_RD_Confidence_Left = (((data[13] & 0xE0U) >> 5) + (((uint16_t)data[14] & 0xFU) << 3));
//    userData->ARS_RD_Confidence_Right = (((data[14] & 0xF0U) >> 4) + (((uint16_t)data[15] & 0x7U) << 4));
//    userData->ARS_RD_NofLane_Left = ((data[15] & 0x18U) >> 3);
//    userData->ARS_RD_NofLane_Right = ((data[15] & 0x60U) >> 5);
//    userData->ARS_RD_NofLaneProb_Left = (((data[15] & 0x80U) >> 7) + (((uint16_t)data[16] & 0x3FU) << 1));
//    userData->ARS_RD_NofLaneProb_Right = (((data[16] & 0xC0U) >> 6) + (((uint16_t)data[17] & 0x1FU) << 2));
//    userData->ARS_RD_RoadType = ((data[17] & 0x60U) >> 5);
//    userData->ARS_RD_RoadTypeConf = (((data[17] & 0x80U) >> 7) + (((uint16_t)data[18] & 0x3FU) << 1));
     radarInfo.mPolynomialR.c = ((((data[19] & 0xFFU) + (((uint16_t)data[20] & 0xFU) << 8)) * 0.001) - 2.047);
     radarInfo.mPolynomialR.b = (((((data[20] & 0xF0U) >> 4) + (((uint16_t)data[21] & 0x3FU) << 4)) * 0.0001) - 0.0511);
     radarInfo.mPolynomialR.a = (((((data[21] & 0xC0U) >> 6) + (((uint16_t)data[22] & 0xFFU) << 2) + (((uint16_t)data[23] & 0x01U) << 10)) * 0.000001) -0.001023);

//     qDebug() << __FUNCTION__ << __LINE__ << dataArray.toHex(' ');
//     qDebug() << __FUNCTION__ << __LINE__
//              << DataAnalysisTTargetInfoFrame->mPolynomialL.a
//              << DataAnalysisTTargetInfoFrame->mPolynomialL.b
//              << DataAnalysisTTargetInfoFrame->mPolynomialL.c
//              << DataAnalysisTTargetInfoFrame->mPolynomialL.d
//              << DataAnalysisTTargetInfoFrame->mPolynomialL.begin
//              << DataAnalysisTTargetInfoFrame->mPolynomialL.end
//              << DataAnalysisTTargetInfoFrame->mPolynomialR.a
//              << DataAnalysisTTargetInfoFrame->mPolynomialR.b
//              << DataAnalysisTTargetInfoFrame->mPolynomialR.c
//              << DataAnalysisTTargetInfoFrame->mPolynomialR.d
//              << DataAnalysisTTargetInfoFrame->mPolynomialR.begin
//              << DataAnalysisTTargetInfoFrame->mPolynomialR.end;

    return true;
}

bool CANProtocolBYDHO::parse_0x64E_0x66F(const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8) {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
    Parser::ParsedDataTypedef::RadarInfomation &radarInfo = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mRadarInfomation;
    const uint8_t *data = (const uint8_t *)frame.data().data();

    radarInfo.mInterpolation = data[3] & 0x01;
    radarInfo.mMRR_Modulation_Status = (data[3] & 0x30) >> 4;

//    target->Checksum_64E_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    target->Counter_64E_S = (data[2]);
    radarInfo.mMRR_SensorDirty = (data[3] & 0x1U);
    radarInfo.mMRR_SGUFail = ((data[3] & 0xEU) >> 1);
    radarInfo.mMRR_Modulation_Status = (data[3] & 0x30) >> 4;
    radarInfo.mMRR_Failure = ((data[3] & 0xC0U) >> 6);



    mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mProtocolType = Parser::ParsedDataTypedef::BYDHO;
    mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mValid = true;

    analysisEnd(Parser::ParsedDataTypedef::TargetTrack);

    return true;
}
    }
}
