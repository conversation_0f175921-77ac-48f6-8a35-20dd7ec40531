﻿#include "daqsaveworker.h"

#include <iostream>
#include <thread>

namespace DAQ{

bool CircularQueue::enqueue(const char *data, int len)
{
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    if (!mEmpty && (mFront == mTail)) {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << mFront << " " << mTail << " " << len << std::endl;
        return false;
    }
    memcpy(mData[mTail].mData, data, len);
    mData[mTail].mDataLength = len;
    mTail = (mTail + 1) % QUEUE_SIZE;
    mEmpty = false;

//    if (mTail == 0)
//    std::cout << __FUNCTION__ << " " << __LINE__ << " " << mFront << " " << mTail << std::endl;

    return true;
}

DAQData *CircularQueue::dequeue()
{
    if (mEmpty) {
        return 0;
    }
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex);
    DAQData *data = mData + mFront;
    mFront = (mFront + 1) % QUEUE_SIZE;
    mEmpty = (mFront == mTail);

//    if (mTail == 0)
//    std::cout << __FUNCTION__ << " " << __LINE__ << " " << mFront << " " << mTail << std::endl;

    return data;
}

void deviceReceive(DAQSaveWorker *pDAQSaveWorker) {
    std::cout << "线程开始，UDP保存数据 ..." << std::endl;
    while (pDAQSaveWorker->canSave())
    {
        pDAQSaveWorker->writeRawData();
    }

    while (pDAQSaveWorker->writeRawData()) {

    }

    pDAQSaveWorker->closeFile();
    std::cout << "线程结束，停止UDP保存." << std::endl;
}

DAQSaveWorker::DAQSaveWorker(QObject *parent)
{
}

bool DAQSaveWorker::open(const QString &filename, bool newThread)
{
    if (mpFile) {
        fclose(mpFile);
    }

    mpFile = fopen(filename.toLocal8Bit(), "wb");
    if (!mpFile) {
        return false;
    }

    mPackageCount = 0;
    mOpened = true;
    mCanSave = true;

    mNewThread = newThread;
    if (mNewThread) {
        if (!mCircularQueue) {
            mCircularQueue = new CircularQueue;
        }
        std::thread(deviceReceive, this).detach();
    }

    return true;
}

void DAQSaveWorker::closeFile()
{
    mOpened = false;

    if (mpFile) {
        fclose(mpFile);
        mpFile = 0;
    }
    if (mNewThread) {
        std::cout << __FUNCTION__ << " " << __LINE__ << " " << mCircularQueue->mFront << " " << mCircularQueue->mTail << std::endl;
    }
}

void DAQSaveWorker::close()
{
    mCanSave = false;
    if (!mNewThread) {
        closeFile();
    }
}

quint64 DAQSaveWorker::writeRawData()
{
    int len = 0;
    DAQData *data = mCircularQueue->dequeue();
    while (data ) {
        len += fwrite(data->mData, sizeof (char), data->mDataLength, mpFile);
        data = mCircularQueue->dequeue();
    }

    return len;
}

qint64 DAQSaveWorker::writeRawData(const char *data, qint64 len)
{
    if (mNewThread) {
        if (!mCircularQueue->enqueue(data, len)) {
            return -1;
        }
        return len;
    } else {
        return fwrite(data, sizeof (char *), len, mpFile);
    }
}

}
