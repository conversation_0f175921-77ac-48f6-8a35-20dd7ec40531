﻿/**
 * @file     vdy_types.h
 * @brief    The structure and variables of VDY module are defined in this header file.
 * <AUTHOR> (<EMAIL>)
 * @version  1.0
 * @date     2023-02-15
 * 
 * 
 * @par Verion logs:
 * <table>
 * <tr>  <th>Date        <th>Version  <th>Author     <th>Description
 * <tr>  <td>2023-02-15  <td>1.0      <td>Wison      <td>First Version
 * </table>
 * @copyright Copyright (c) 2023  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _VDY_TYPES_H_
#define _VDY_TYPES_H_

/****************************************************************************
  INCLUDE
 ****************************************************************************/

#ifndef PC_DBG_FW
#include "app_common.h"
#include "../vehicle/com_types.h"
#else
#include "app/protocol/com_types.h"
#endif

/****************************************************************************
  DEFINE
 ****************************************************************************/
#define vdyKeyStateOn   4


/**
 * @brief 车辆动态数据 \n
 * 表征冻结的车辆动态数据，为数据处理和报警逻辑提供数据源
 */
typedef struct vehicleDynamicData
{
    uint16_t versionNumber;   ///< 接口版本号
    SignalHeader_t signalHeader; ///< 信号header
    float vdySpeedInmps;         ///<车速，单位m/s，前进为正后退为负
    float vdyYawRate;            ///<横摆角速度，单位rad/s
    float vdyYawRateOffset;      ///<横摆角速度偏移，单位rad/s
    float vdyedrYawRate;         ///<edr横摆角速度
    float vdyedrYawRateOffset;   ///<edr横摆角速度偏移
    float vdyAccelLong;          ///<纵向加速度，单位m/s^2
    float vdyAccelLat;           ///<横向加速度，单位m/s^2
    float vdyAccelLongOffset;    ///<纵向加速度偏移量m/s^2
    float vdyAccelLatOffset;     ///<横向加速度偏移量m/s^2
    float vdyedrAccelLong;          ///<纵向加速度，单位m/s^2
    float vdyedrAccelLat;           ///<横向加速度，单位m/s^2
    float vdyedrAccelLongOffset;    ///<纵向加速度偏移量m/s^2
    float vdyedrAccelLatOffset;     ///<横向加速度偏移量m/s^2
    float vdyCurveRadius;        ///<横向加速度，单位m，方向左负右正
    float vdySteeringAngle;      ///<方向盘转角，单位为deg，左正右负
    float vdySteeringAngleSpeed;  //方向盘转速
    float vdyWhlSpdRearLe;       ///<左后轮轮速，单位m/s，前进为正后退为负
    float vdyWhlSpdRearRi;       ///<右后轮轮速，单位m/s，前进为正后退为负
    float vdyWhlSpdFrontLe;      ///<左前轮轮速，单位m/s，前进为正后退为负
    float vdyWhlSpdFrontRi;      ///<右前轮轮速，单位m/s，前进为正后退为负
    float vdywheelangle;         ///< 根据方向盘转角估算的车轮转角, 小齿轮转角/12，左正右负
    float vdyComSensorRot;      ///<域控下发的安装角度>
    uint16_t vdyLatitdDecimalPart;  ///<纬度信息小数部分
    uint8_t vdyLatitudeDirection;  ///<纬度方向
    uint16_t vdyLatitdIntegPart;   ///<纬度信息整数部分
    uint16_t vdyLongitdDecimlPart; ///<经度信息小数部分
    uint8_t vdyLongitdDirection;   ///<经度方向
    uint16_t vdyLongitdIntegrPart; ///<经度信息整数部分
    uint16_t vdyAngleReqValue;     ///<角度请求值.
    uint8_t vdyGearState;        ///<档位信号，单位NA，0x0=invalid,0x1=Gear P,0x2=Gear R,0x3=Gear N,0x4=Gear D,0x5~0xFF=Reserved
    uint8_t vdyDriveDirection;   ///<行驶方向，单位NA，0x0=Forward,0x1=backward,0x2=invalid
    uint8_t year;   ///<域控发的时间戳
	uint8_t month;
	uint8_t day;
	uint8_t hour;
	uint8_t minute;
	uint8_t second;	
} VDY_DynamicEstimate_t;

/**
 * @brief 一些位
 */
typedef struct vehicleCommonBit
{
    uint16_t igPowerDownTimeOfDOW :  1; // 1正常poweron状态，0时间到关闭dow； IG下电时间，在3分钟到时才会关闭DOW
    uint64_t resv0                : 63;
} VDY_CommonBit_t;



/**
 * @brief 用户操作的开关信号 \n
 * 
 * @details 用于保存用户的操作信息，包括功能开关、转向灯开关、车门开关等
 */
typedef struct vehicleFunctionSwitch
{
    /// 接口版本号
    uint16_t versionNumber;

    /// 信号header
    APP_signalHeader_t signalHeader;

    /// 功能开关
    vehicleSwitch_t vehFuncSwitchReq;

    /// 整车CAN传输的开关门信息
    vehicleDoorStatus_t vdyDoorStatus;

    /// 转向灯信号
    vehicleTurnLight_t vdyTurnSignal;

    /**
     * @brief 后保信号灯工作工况
     *
     *  0:invalid, 无效
     *  1:NotWorking, 不工作
     *  2: LeftTurnSignalLightNormalFlashing, 左转向信号灯（正常闪烁）
     *  3: LeftTurnSignalLightFlashingQuickly, 左转向信号灯（故障快闪）
     *  4: LeftTurnSignalLightFlashingNormaly, 右转向信号灯（正常闪烁）
     *  5: RightTurnSignalLightFaultyFlashingFastWarningSignal, 右转向信号灯（故障快闪）
     *  6: DangerWarningSicnal, 危险警告信号
     *  7: EmergencyBrakingSignal, 紧急制动信号
     *  8: RearEndCollisionWarningSignal, 追尾警告信号
     *  9: NormalFlashing, 普通快闪
     *  0xA-0xF：预留
     */
    uint8_t turnSignalWorkCon;
    
    // 车辆门锁信号
    vehicleDoorLockStatus_t vdyLockStatus;

    /**
     * @brief 车身状态（车身防盗）
     *
     * @details 信号值对应为：0x0:正常状态, 0x1:防盗设定状态, 0x2:防盗状态, 0x3-0x7:预留
     */
    uint8_t vdySecurtiyStatus;

    /**
     * @brief 极致转向状态
     *
     * @details
     * 0x0：关闭
     * 0x1：待命
     * 0x2：激活
     * 0x3：预留
     * 默认值：0x0
     */
    uint8_t vdyTabStatus;

    /**
     * @brief 前角Dow状态
     * 0x0：invalid, 0x1：Off, 0x2：Passive, 0x3：Active, 0x4：Fault, 0x5-0x7：Reserved
     */
    uint8_t vdyFcrDowStatus;

    /**
     * @brief 后角Dow状态
     * 0x0: 0FF, 0x1: Passive, 0x2: Active, 0x3: Fault
     */
    uint8_t vdyRcrDowStatus;
    
    /**
     * @brief 公共位 
     *
     * @details igPowerDownTimeOfDOW
     */
    VDY_CommonBit_t commonBit;
} VDY_vehicleFuncSwt_t;

/**
 * @brief 车辆非动态数据结构体
 */
typedef struct vehicleState
{
    /// @brief 接口版本号
    uint16_t versionNumber;

    /// @brief 信号header
    APP_signalHeader_t signalHeader;

    /// @brief 整车CAN传输的点火状态信号
    /// @details 信号的详细解析为：0x0:invalid,0x1:OFF,0x2:acc,0x3:crank,0x4:on
    uint8_t vdyKeyState;
    
    /// @brief 整车CAN传输的电压
    /// @details 范围为0-32,单位v
    float vdyVehicleVoltage;

    /// @brief 里程读取 Km 精度1km/h
    uint32_t vdyTotalOdometer;

    /// @brief 日期-年
    uint8_t vdyDateYear;

    /// @brief 日期-月
    uint8_t vdyDateMonth;

    /// @brief 日期-日
    uint8_t vdyDateDay;

    /// @brief 时间-时
    uint8_t vdyTimeHour;

    /// @brief 时间-分
    uint8_t vdyTimeMinute;

    /// @brief 时间-秒
    uint8_t vdyTimeSecond;

      /**
     * @brief APA_自动泊车状态
     * 
     * @details 
     * 0x0：Passive待机
     * 0x1：寻车位中
     * 0x2：泊车中
     * 0x3：泊车暂停
     * 0x4：泊车中(终)止（异常退出）
     * 0x5：泊车完成
     * 0x6：自动泊车不可用
     * 0x7：准备就绪状态
     */
    uint8_t comApaAutoParkingState;
} VDY_StaticState_t;

typedef struct
{
    uint16_t resv0             :  8;
    uint16_t HDCCtrl           :  2; //0x0:OFF,0x1:On active braking,0x2:On not active braking,0x3:Reserved
    uint16_t ESPDiagActv       :  1; //ESP 诊断结果 0x0:ESP diagnosis is inactive,No Failure,	0x1:ESP diagnosis is active,Failure
    uint16_t CTABrkAvailable   :  1; //执行器状态，0x0:Not Available，0x1:Available
    uint16_t AEBIBActv         :  1;
    uint16_t AEBBAActv         :  1;
    uint16_t standStill        :  2; //0：非静止，1：静止
    uint16_t tcsFaultStatus    :  1; // 0x0：No Failure 0x1：Failure ; Initial/Default：0x1
    uint16_t vdcFaultStatus    :  1; //  0x0：No Failure 0x1：Failure ; Initial/Default：0x1
    uint8_t VDCActiveSts    :  1; // 0x0:No Active, 0x1:Active
    uint8_t TCSActiveSts    :  1;   // 0x0:No Active, 0x1:Active
    uint8_t AEBNotAvailable :  1; //0x0:Not Available, 0x1:Available
    uint8_t AEBDecActive : 1;   //0x0:Not Available, 0x1:Available
    uint8_t rctbActive         :  1;    // 0 not  1 on 
    uint8_t rctbAvailable      :  1;
    uint8_t  espOffStatus      :  1; //开关状态  0: 0N 1:OFF

} espStatus_t; // ESP状态

typedef struct
{
    espStatus_t vdyEspStatus; // ESP状态
    uint8_t comDragModeConfig;
    uint8_t vdyTrailerSts;    //拖车模式，0-拖车模式关闭；1-拖车模式打开
    uint8_t vdyBrakeActLevel; //制动踏板开度，0-100%
    uint8_t vdyImputRodStrokeState; // 制动踏板行程状态 0 : 有效  1 : 无效
    uint8_t vdyAccPedelPosn;  //加速踏板开度，0-100%
    uint8_t vdyAccelerograph_Depth; //油门深度, 0-100% 0x342;
    uint8_t vdyBrkPedalSts;   //刹车踏板状态，0x0:Not pressed，0x1:Pressed
    float vdyStrngWhlTorq;       //EPS扭矩. PH∈[-12.50，12.50]Nm  0x318
    float vdyIPBSimulatorPressure;  // IPB压力. [-5，301.9]bar
    float vdyIPBPlungerPressure;    // IPB压力 [-5，301.9]bar
    uint16_t vdyEdrBrakeActLevel;      // EDR存储的制动踏板实际值
    uint8_t AEB_Not_Available;    // 前穿行制动状态 0：notavailable  1:available
    uint8_t AEB_BRK_TQ;           // 制动是否可用 包含FCTB RCTB
    uint8_t vdySuspensionHeight; //空气悬架调节高度 0x0：无效，0x1：车身正在上升，0x2：车身正在下降，0x3：调节完成，0x4: 调节中断，0x5-0x7：预留
    uint8_t raceModeStatus;     // 赛道模式状态 0：关闭， 1：开启
    uint8_t drivingMode;       //0x0:无效 0x1:舒适 0x2:经济 0x3:运动 0x4:雪地/冰面/草地/碎石 0x5:沙地 0x6:泥地/车辙 0x7:山地 0x8:岩石 0x9:涉水 0xA:浮水 0xB:蠕行模式 0xC:原地掉头 0xD:拖挂模式 0xE:长续航模式 0xF:预留 0x10:防滑链模式 0x11:达喀尔模式（拉力模式） 0x12: 跛行模式 0x13: 自定义模式 0x14:超频（狂暴）模式 0x15:自动模式 0x16-0xFF：预留
} VDY_ChassisInfo_t;

/**
 * @brief 包含VDY所有信息结构体
 *
 * @details
 */
typedef struct
{
    const VDY_DynamicEstimate_t* pVDY_DynamicInfo;
    const VDY_vehicleFuncSwt_t*  pVDY_VehicleFuncSwtInfo;
    const VDY_StaticState_t*     pVDY_StaticInfo;
    const VDY_ChassisInfo_t*     pVDY_ChassisInfo;
}VDY_Info_t;

int32_t VDY_setFuncSwitch(uint8_t switchType, uint8_t switchState);
int32_t VDY_getFuncSwitch(uint8_t switchType, uint8_t *switchState);
void VDY_setCloseDOWOfIGPDStatu(uint8_t dowSta);
uint8_t VDY_getCloseDOWOfIGPDStatu(void);

#endif

