﻿#include "udstoolboxform.h"
#include "ui_udstoolboxform.h"
#include "uds.h"
#include "../utils/utils.h"
#include "devices/devicemanager.h"
#include "sendudsform.h"


#include <QtConcurrent>
#include <QDebug>
#include <QMessageBox>

UDSToolBoxForm::UDSToolBoxForm(Devices::Can::DeviceManager *deviceManager,QWidget *parent) :
    QWidget(parent),
    ui(new Ui::UDSToolBoxForm),
    mDeviceManager( deviceManager )
{
    ui->setupUi(this);
    setWindowTitle( QString::fromLocal8Bit( "UDS工具箱" ) );

    if( deviceManager->deviceWorker() ){
        connect(deviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameRecieved , this, &UDSToolBoxForm::canFrame);
        connect(deviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameTransmited , this, &UDSToolBoxForm::frameTransmited);
    }

    connect( deviceManager, &Devices::Can::DeviceManager::deviceOpened, this, &UDSToolBoxForm::deviceOpened );
    connect( deviceManager, &Devices::Can::DeviceManager::deviceClosed, this, &UDSToolBoxForm::deviceClosed );



    connect( this, &UDSToolBoxForm::msg, this, &UDSToolBoxForm::showMsg );
//    connect( this, &UDSToolBoxForm::wakeUpFail, this, &UDSToolBoxForm::on_pushButtonWakeUpStop_clicked );

    initUDS();
    initSendUDS();
    initDTCText();

    //隐藏前雷达角度，协议不一样
//    ui->labelAngle_F->setVisible( false );
//    ui->lineEditAngle_F->setVisible( false );

    ui->tabWidget->setCurrentIndex( 0 );
}

UDSToolBoxForm::~UDSToolBoxForm()
{
    delete ui;
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        delete mUDS[i];
    }

}

void UDSToolBoxForm::initUDS()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        mUDS[i] = new Functions::UDS( mDeviceManager );
        mUDS[i]->setResponseID( mResponseAddress[mRadarProjectType][i] );
        //mUDS[i]->canUpdate( true );
    }
}

void UDSToolBoxForm::initSendUDS()
{
    SendUDSForm* sendUDS = new SendUDSForm( mDeviceManager, this );
    ui->tabWidget->insertTab( 2, sendUDS,  QString::fromLocal8Bit("发送指令" ) );
    ui->tabWidget->setCurrentIndex( 2 );
    connect( sendUDS, &SendUDSForm::requestIDOrResponseIDChanged, this, &UDSToolBoxForm::setOtherPhysicalAndResponseAddress, Qt::DirectConnection );
    connect( sendUDS, &SendUDSForm::msg, this, &UDSToolBoxForm::showMsg );
}

void UDSToolBoxForm::initDTCText()
{
    mDTCText.clear();
    //mDTCText
    mDTCText[0xD10017] = QString::fromLocal8Bit("电压过高");
    mDTCText[0xD10116] = QString::fromLocal8Bit("电压过低");
    mDTCText[0xC07388] = QString::fromLocal8Bit("VCAN bus off");
    mDTCText[0xC1D788] = QString::fromLocal8Bit("Pcan bus off");
    mDTCText[0xACD000] = QString::fromLocal8Bit("工厂模式未关闭");
    mDTCText[0xACD178] = QString::fromLocal8Bit("从来未校准");
    mDTCText[0xACD278] = QString::fromLocal8Bit("校准未完成");
    mDTCText[0xACE655] = QString::fromLocal8Bit("下线NVM无配置信息");
    mDTCText[0xACE876] = QString::fromLocal8Bit("系统致盲");
    mDTCText[0xACEB94] = QString::fromLocal8Bit("水平校准不通过");

    mDTCText[0xACD488] = QString::fromLocal8Bit("工厂数据错误");
    mDTCText[0xACD640] = QString::fromLocal8Bit("内部电源管理系统故障");
    mDTCText[0xACD804] = QString::fromLocal8Bit("内部硬件故障");
    mDTCText[0xACD917] = QString::fromLocal8Bit("电源管理芯片过压故障");
    mDTCText[0xACDB16] = QString::fromLocal8Bit("电源管理芯片欠压故障");
    mDTCText[0xACDD00] = QString::fromLocal8Bit("雷达调制配置故障");
    mDTCText[0xACDF52] = QString::fromLocal8Bit("软件故障");
    mDTCText[0xACE000] = QString::fromLocal8Bit("雷达前端数据处理故障");
    mDTCText[0xACE152] = QString::fromLocal8Bit("软件临时故障");
    mDTCText[0xACE24B] = QString::fromLocal8Bit("微处理器温度超出范围故障");


    mDTCText[0xD01786] = QString::fromLocal8Bit("EPS信号无效（EPS信号值无效故障）");
    mDTCText[0xC13187] = QString::fromLocal8Bit("与 EPS（电动助力转向）模块失去通讯");

    mDTCText[0xC42082] = QString::fromLocal8Bit("EPS计数器错误");

    mDTCText[0xC42083] = QString::fromLocal8Bit("EPS校验错误");
    mDTCText[0xC19780] = QString::fromLocal8Bit("与IPB失去通讯");
    mDTCText[0x5C6286] = QString::fromLocal8Bit("IPB信号无效");
    mDTCText[0x5C6302] = QString::fromLocal8Bit("IPB计数器错误");
    mDTCText[0x5C6462] = QString::fromLocal8Bit("IPB校验错误");
    mDTCText[0xC1A887] = QString::fromLocal8Bit("与左车身域控制器失去通讯");
    mDTCText[0x5C6762] = QString::fromLocal8Bit("左车身域控制器校验错误");

    mDTCText[0x5C6602] = QString::fromLocal8Bit("左车身域控制器计数器错误");
    mDTCText[0x5C6586] = QString::fromLocal8Bit("左车身域控制器信号无效");
    mDTCText[0xACED00] = QString::fromLocal8Bit("天线图错误");
    mDTCText[0xC1EA87] = QString::fromLocal8Bit("私有通信失去通信");
    mDTCText[0xC1EA83] = QString::fromLocal8Bit("私有通信校验错误");
    mDTCText[0xC1EA82] = QString::fromLocal8Bit("私有通信计数器错误");

    mDTCText[0xACDE00] = QString::fromLocal8Bit("雷达调制故障");

    mDTCText[0xD0C286] = QString::fromLocal8Bit("与VCU失去通信");
    mDTCText[0xD27182] = QString::fromLocal8Bit("VCU计数器错误");
    mDTCText[0xD27183] = QString::fromLocal8Bit("VCU校验错误");
    mDTCText[0xC1D529] = QString::fromLocal8Bit("VCU信号失效");
    mDTCText[0xC2F186] = QString::fromLocal8Bit("与ADS失去通讯");
    mDTCText[0xD09282] = QString::fromLocal8Bit("ADS计数器错误");
    mDTCText[0xD09183] = QString::fromLocal8Bit("ADS校验错误");

    mDTCText[0xD12E87] = QString::fromLocal8Bit("与DiSus失去通讯");
    mDTCText[0xD0F582] = QString::fromLocal8Bit("DiSus计数器错误");
    mDTCText[0xD13F83] = QString::fromLocal8Bit("DiSus校验错误");
    mDTCText[0xD13F81] = QString::fromLocal8Bit("DiSus信号无效");
    mDTCText[0xD09687] = QString::fromLocal8Bit("与CCU失去通讯");
    mDTCText[0xD09882] = QString::fromLocal8Bit("CCU计数器错误");
    mDTCText[0xD09783] = QString::fromLocal8Bit("CCU校验错误");
    mDTCText[0xD09886] = QString::fromLocal8Bit("CCU信号无效");

    if( mRadarProjectType == RADAR_PROJECT_GWM_140PLUS ){
        mDTCText[0x534A78] = QString::fromLocal8Bit("下线标定从未进行");
        mDTCText[0x530478] = QString::fromLocal8Bit("雷达安装角度水平方向相差过大");
        mDTCText[0x534878] = QString::fromLocal8Bit("标定失败");
    }
}

void UDSToolBoxForm::readSoftVersion(quint32 udsIndex)
{
    UDSFrame responseFrame;
    responseFrame.clear();
    quint32 ID = mPhysicalAddress[mRadarProjectType][udsIndex];
    QString version;
    if (!mUDS[udsIndex]->sendData(ID, QString("22 F1 95"), &responseFrame)) {
        version = "ERROR";
    }else{
        if( getDID( responseFrame.mData ) == 0xF195 ){
            quint8* pData = (quint8*)responseFrame.mData.data();
            quint32 verInformation = (quint32)pData[3] * 0x100 + pData[4];
            quint32 year = 2000 + pData[5];
            quint32 month = pData[6];
            quint32 day = pData[7];
            version = QString("%1-%2%3%4").arg( verInformation ).arg(year).arg(month).arg(day);
        }
    }
    showSoftVersion( udsIndex, version );
}

void UDSToolBoxForm::readSoftCode(quint32 udsIndex)
{
    UDSFrame responseFrame;
    responseFrame.clear();
    quint32 ID = mPhysicalAddress[mRadarProjectType][udsIndex];
    QString code;
    if (!mUDS[udsIndex]->sendData(ID, QString("22 F1 94"), &responseFrame)) {
        code = "ERROR";
    }else{
        if( getDID( responseFrame.mData ) == 0xF194 ){
            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
            //code = responseFrame.mData.mid( 3, 9 ).toHex();
            code = responseFrame.mData.mid( 3 ).toHex();
            code = code.toUpper();
        }
    }
    showSoftCode( udsIndex, code );
}

void UDSToolBoxForm::readAngle(quint32 udsIndex)
{
    UDSFrame responseFrame;
    responseFrame.clear();
    quint32 ID = mPhysicalAddress[mRadarProjectType][udsIndex];
    QString angle;
    if (!mUDS[udsIndex]->sendData(ID, QString("22 34 04"), &responseFrame)) {
        angle = "ERROR";
    }else{
        if( getDID( responseFrame.mData ) == 0x3404 ){
            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
            //code = responseFrame.mData.mid( 3, 9 ).toHex();
            angle = responseFrame.mData.mid( 3 ).toHex();
            angle = QString("%1").arg( (double)angle.toUInt( NULL, 16 ) / 16384 );
        }
    }

    if (udsIndex == RADAR_UDS_INDEX_F) {
        QString angleV;
        responseFrame.clear();
        if (!mUDS[udsIndex]->sendData(ID, QString("22 34 41"), &responseFrame)) {
            angleV = "ERROR";
        }else{
            if( getDID( responseFrame.mData ) == 0x3441 ){
                qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
                //code = responseFrame.mData.mid( 3, 9 ).toHex();
                angleV = responseFrame.mData.mid( 3 ).toHex();
                angleV = QString("%1").arg( (double)angle.toUInt( NULL, 16 ) / 16384 );
            }
        }

        angle = angle + " / " + angleV;
    }
    showAngle( udsIndex, angle);
}

void UDSToolBoxForm::clearDTC(quint32 udsIndex)
{
    UDSFrame responseFrame;
    responseFrame.clear();
    quint32 ID = mPhysicalAddress[mRadarProjectType][udsIndex];
    QString DTCMsg = "";
    bool bError = true;

    if (!mUDS[udsIndex]->sendData(ID, QString("14 FF FF FF"), &responseFrame)) {
        DTCMsg = QString::fromLocal8Bit("清除DTC错误");
        bError = true;
    }else{
        DTCMsg = QString::fromLocal8Bit("清除DTC成功");
        bError = false;
    }

    showRadarMsg( udsIndex, DTCMsg, bError );
}

void UDSToolBoxForm::readDTC(quint32 udsIndex)
{
    UDSFrame responseFrame;
    responseFrame.clear();
    quint32 ID = mPhysicalAddress[mRadarProjectType][udsIndex];
    QString DTCMsg = "";
    bool bError = true;

    if (!mUDS[udsIndex]->sendData(ID, QString("19 02 09"), &responseFrame)) {
        DTCMsg = QString::fromLocal8Bit("DTC读取错误");
        bError = true;
    }else{
        if( getDID( responseFrame.mData ) == 0x0209 ){
            QByteArray dtcBuffer = responseFrame.mData.mid( 3 );
            for( int i=0; i<dtcBuffer.size(); i+=4 ){
                QByteArray tmp = dtcBuffer.mid( i, 4 );
                quint8* pData = (quint8*)tmp.data();
                quint32 dtc = ( pData[0] << 16 ) + ( pData[1] << 8 ) + ( pData[2] );
                DTCMsg += "\n\t0x" + QString::number( dtc, 16 ).toUpper();
                DTCMsg += "--" + getDTCText( dtc ) + "--";
                if( pData[3] == 0x09 ){
                    DTCMsg += QString::fromLocal8Bit("实时码");
                }else{
                    DTCMsg += QString::fromLocal8Bit("历史码");
                }
            }
            bError = false;
        }
    }

    showRadarMsg( udsIndex, DTCMsg, bError );
}

void UDSToolBoxForm::readCfgCode(quint32 udsIndex)
{
    UDSFrame responseFrame;
    responseFrame.clear();
    quint32 ID = mPhysicalAddress[mRadarProjectType][udsIndex];

    QString cfgCode = "ERROR";
    if (!mUDS[udsIndex]->sendData(ID, QString("22 21 00"), &responseFrame)) {
        //DTCMsg = QString::fromLocal8Bit("DTC读取错误");
    }else{
        cfgCode = responseFrame.mData.mid( 3 ).toHex();
        cfgCode = cfgCode.toUpper();
    }
    showCfgCode( udsIndex, cfgCode );
}

void UDSToolBoxForm::factoryMode(quint32 udsIndex, bool bEnter)
{
    UDSFrame responseFrame;

    quint32 ID = mPhysicalAddress[mRadarProjectType][udsIndex];

    if( !securityVerification( udsIndex ) ){
        return;
    }

    QString enterStr = QString("2E F1 C2 01");
    QString exitStr = QString("2E F1 C2 02");
    if( udsIndex == RADAR_UDS_INDEX_F ){
        enterStr = QString( "2E 34 01 01" );
        exitStr = QString( "2E 34 01 00" );
    }

    if( bEnter ){
        //进入工厂模式
        responseFrame.clear();
        if (!mUDS[udsIndex]->sendData(ID, enterStr, &responseFrame)) {
            showRadarMsg( udsIndex, QString::fromLocal8Bit("进入工厂模式失败"), true );
            return;
        }
        showRadarMsg( udsIndex, QString::fromLocal8Bit("进入工厂模式成功"), false );
    }else{
        //退出工厂模式
        responseFrame.clear();
        if (!mUDS[udsIndex]->sendData(ID, exitStr, &responseFrame)) {
            showRadarMsg( udsIndex, QString::fromLocal8Bit("退出工厂模式失败"), true );
            return;
        }
        showRadarMsg( udsIndex, QString::fromLocal8Bit("退出工厂模式成功"), false );
    }
}

bool UDSToolBoxForm::securityVerification(quint32 udsIndex)
{
    UDSFrame responseFrame;

    quint32 ID = mPhysicalAddress[mRadarProjectType][udsIndex];
    //进入拓展模式
    responseFrame.clear();
    if (!mUDS[udsIndex]->sendData(ID, QString("10 03"), &responseFrame)) {
        showRadarMsg( udsIndex, QString::fromLocal8Bit("进入扩展模式失败"), true );
        return false;
    }

    //请求种子
    responseFrame.clear();
    if (!mUDS[udsIndex]->sendData(ID, QString("27 01"), &responseFrame)) {
        showRadarMsg( udsIndex, QString::fromLocal8Bit("请求种子失败"), true );
        return false;
    }
    uint32_t seed{0};
    memcpy(&seed, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);

    //验证秘钥
    uint32_t key = SeedToKey(seed, mMASK[mRadarProjectType][udsIndex]);
    responseFrame.clear();
    if (!mUDS[udsIndex]->sendData(ID, QString("27 02 %1").arg(key, 8, 16, QLatin1Char('0')), &responseFrame)) {
        showRadarMsg( udsIndex, QString::fromLocal8Bit("验证秘钥失败"), true );
        return false;
    }

    return true;
}

void UDSToolBoxForm::writeCfgCode(quint32 udsIndex, const QString &code)
{
    UDSFrame responseFrame;

    quint32 ID = mPhysicalAddress[mRadarProjectType][udsIndex];

    if( !securityVerification( udsIndex ) ){
        return;
    }

    //擦除配置字
    responseFrame.clear();
    if( !mUDS[udsIndex]->sendData(ID, QString("2E f1 a1 ff ff ff ff ff ff ff ff") , &responseFrame ) ) {
        showRadarMsg( udsIndex, QString::fromLocal8Bit("擦除配置字失败"), true );
        return;
    }

    //写入配置字
    responseFrame.clear();
    //if( !mUDS[udsIndex]->sendData(ID, QString("2E 21 00 11 83 81 01 03 00 00 4c") , &responseFrame) ) {
    if( !mUDS[udsIndex]->sendData(ID, QString("2E 21 00 %1").arg(code) , &responseFrame) ) {
        showRadarMsg( udsIndex, QString::fromLocal8Bit("写入配置字失败"), true );
        return;
    }

    //重启ECU
    responseFrame.clear();
    if( !mUDS[udsIndex]->sendData(ID, QString("11 01") ) ) {
        showRadarMsg( udsIndex, QString::fromLocal8Bit("重启ECU失败"), true );
        return;
    }

    showRadarMsg( udsIndex, QString::fromLocal8Bit("写入配置字完成"), false );
    showRadarMsg( udsIndex, QString::fromLocal8Bit("正在重启ECU，请等待..."), false );
    QThread::sleep( 3 );
    showRadarMsg( udsIndex, QString::fromLocal8Bit("ECU重启完成"), false );
}

quint32 UDSToolBoxForm::getDID(const QByteArray &data)
{
    quint8* pData = (quint8*)data.data();
    quint32 ret = pData[1] ;
    ret *= 0x100;
    ret += pData[2];
    return ret;
}

void UDSToolBoxForm::showFrame(const Devices::Can::CanFrame &frame, bool bSend)
{
    QTextCursor cursor = ui->textBrowserFrame->textCursor();
    cursor.movePosition( QTextCursor::End );
    ui->textBrowserFrame->setTextCursor( cursor );


    if( bSend ){
        ui->textBrowserFrame->setTextColor( Qt::blue );
    }else{
        ui->textBrowserFrame->setTextColor( Qt::green );
    }

    QString head = QString( "%1==>").arg( QDateTime::currentDateTime().toLocalTime().toString( "hh:mm:ss:zzz" ) );
    QString msg = QString( "0x%1\t%2").arg(frame.idHex()).arg(frame.dataHex());
    ui->textBrowserFrame->insertPlainText( head + msg + "\n" );

    if( mFrameLog.isOpen() ){
        mFrameLog.write( QString( head + msg + "\n" ).toLocal8Bit() );
        mFrameLog.flush();
    }


    //    cursor = ui->msgTextBrowser->textCursor();
    //    cursor.movePosition( QTextCursor::End );
    //    ui->msgTextBrowser->setTextCursor( cursor );
}

void UDSToolBoxForm::showMsg(const QString &msg, bool bError)
{
    QTextCursor cursor = ui->textBrowserMsg->textCursor();
    cursor.movePosition( QTextCursor::End );
    ui->textBrowserMsg->setTextCursor( cursor );

    if( bError ){
        ui->textBrowserMsg->setTextColor( Qt::red );
    }else{
        ui->textBrowserMsg->setTextColor( Qt::black );
    }

    QString head = QString( "%1==>").arg( QDateTime::currentDateTime().toLocalTime().toString( "hh:mm:ss:zzz" ) );
    //QString msg = QString( "0x%1\t%2").arg(frame.idHex()).arg(frame.dataHex());
    ui->textBrowserMsg->insertPlainText( head + msg + "\n" );

    if( mMsgLog.isOpen() ){
        mMsgLog.write( QString( head + msg + "\n" ).toLocal8Bit() );
        mMsgLog.flush();
    }
}

void UDSToolBoxForm::setOtherPhysicalAndResponseAddress(quint32 physical, quint32 response)
{
    mOtherPhysicalAddress = physical;
    mOtherResponseAddress = response;
}

void UDSToolBoxForm::showSoftVersion(quint32 udsIndex, const QString& version)
{
    switch( udsIndex ){
    case RADAR_UDS_INDEX_LF : //左前雷達
        ui->lineEditSoftVersion_LF->setText( version );
        break;
    case RADAR_UDS_INDEX_RF: //右前雷達
        ui->lineEditSoftVersion_RF->setText( version );
        break;
    case RADAR_UDS_INDEX_LR: //左后雷達
        ui->lineEditSoftVersion_LR->setText( version );
        break;
    case RADAR_UDS_INDEX_RR: //右后雷達
        ui->lineEditSoftVersion_RR->setText( version );
        break;
    case RADAR_UDS_INDEX_F: //前雷達
        ui->lineEditSoftVersion_F->setText( version );
        break;
    default:
        break;
    }
}

void UDSToolBoxForm::showSoftCode(quint32 udsIndex, const QString &code)
{
    //qDebug()<<__FUNCTION__ <<__LINE__ << udsIndex << code;
    switch( udsIndex ){
    case RADAR_UDS_INDEX_LF : //左前雷達
        ui->lineEditSoftCode_LF->setText( code );
        break;
    case RADAR_UDS_INDEX_RF: //右前雷達
        ui->lineEditSoftCode_RF->setText( code );
        break;
    case RADAR_UDS_INDEX_LR: //左后雷達
        ui->lineEditSoftCode_LR->setText( code );
        break;
    case RADAR_UDS_INDEX_RR: //右后雷達
        ui->lineEditSoftCode_RR->setText( code );
        break;
    case RADAR_UDS_INDEX_F: //前雷達
        ui->lineEditSoftCode_F->setText( code );
        break;
    default:
        break;
    }
}

void UDSToolBoxForm::showRadarMsg(quint32 udsIndex, const QString &dtcMsg, bool bError)
{
    QString head;
    switch( udsIndex ){
    case RADAR_UDS_INDEX_LF : //左前雷達
        head = QString::fromLocal8Bit("左前雷达");
        break;
    case RADAR_UDS_INDEX_RF: //右前雷達
        head = QString::fromLocal8Bit("右前雷达");
        break;
    case RADAR_UDS_INDEX_LR: //左后雷達
        head = QString::fromLocal8Bit("左后雷达");
        break;
    case RADAR_UDS_INDEX_RR: //右后雷達
        head = QString::fromLocal8Bit("右后雷达");
        break;
    case RADAR_UDS_INDEX_F: //前雷達
        head = QString::fromLocal8Bit("前雷达");
        break;
    default:
        head = QString::fromLocal8Bit("未知雷达");
        break;
    }
    emit msg( "[" + head + "]\t" + dtcMsg,  bError );
}

void UDSToolBoxForm::showCfgCode(quint32 udsIndex, const QString &cfgCode)
{
    switch( udsIndex ){
    case RADAR_UDS_INDEX_LF : //左前雷達
        ui->lineEditReadCfgCode_LF->setText( cfgCode );
        break;
    case RADAR_UDS_INDEX_RF: //右前雷達
        ui->lineEditReadCfgCode_RF->setText( cfgCode );
        break;
    case RADAR_UDS_INDEX_LR: //左后雷達
        ui->lineEditReadCfgCode_LR->setText( cfgCode );
        break;
    case RADAR_UDS_INDEX_RR: //右后雷達
        ui->lineEditReadCfgCode_RR->setText( cfgCode );
        break;
    case RADAR_UDS_INDEX_F: //前雷達
        ui->lineEditReadCfgCode_F->setText( cfgCode );
        break;
    default:
        break;
    }
}

void UDSToolBoxForm::showAngle(quint32 udsIndex, const QString &angle)
{
    switch( udsIndex ){
    case RADAR_UDS_INDEX_LF : //左前雷達
        ui->lineEditAngle_LF->setText( angle );
        break;
    case RADAR_UDS_INDEX_RF: //右前雷達
        ui->lineEditAngle_RF->setText( angle );
        break;
    case RADAR_UDS_INDEX_LR: //左后雷達
        ui->lineEditAngle_LR->setText( angle );
        break;
    case RADAR_UDS_INDEX_RR: //右后雷達
        ui->lineEditAngle_RR->setText( angle );
        break;
    case RADAR_UDS_INDEX_F: //前雷達
        ui->lineEditAngle_F->setText( angle );
        break;
    default:
        break;
    }
}

QString UDSToolBoxForm::getDTCText(quint32 dtc)
{
    QMap< quint32, QString >::iterator it = mDTCText.find( dtc );
    if( it == mDTCText.end() ) {
            return QString::fromLocal8Bit( "未知DTC" );
    }else{
        return it.value();
    }
}

void UDSToolBoxForm::on_pushButtonReadSoftVersion_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        QtConcurrent::run(this, &UDSToolBoxForm::readSoftVersion, i );
    }
}

void UDSToolBoxForm::canFrame(const Devices::Can::CanFrame &frame)
{
    for (int i = 0; i < RADAR_UDS_INDEX_COUNT; ++i){
        if (frame.id() == mUDS[i]->responseID()){
            mUDS[i]->appendCanFrame(frame);
            showFrame( frame, false );
            //break;
            return;
        }
    }

    if( mOtherResponseAddress == frame.id() ){
        showFrame( frame, false );
        return;
    }
}

void UDSToolBoxForm::frameTransmited(const Devices::Can::CanFrame &frame, bool success)
{
    if( !success ){
        return;
    }
    for (int i = 0; i < RADAR_UDS_INDEX_COUNT; ++i){
        if (frame.id() == mPhysicalAddress[mRadarProjectType][i] ){
            //mUDS[i]->appendCanFrame(frame);
            showFrame( frame, true );
            return;
            //break;
        }
    }

    if( mOtherPhysicalAddress == frame.id() ){
        showFrame( frame, true );
        return;
    }
}


void UDSToolBoxForm::on_pushButtonClearSoftVersion_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        showSoftVersion( i, "" );
    }
}

void UDSToolBoxForm::on_pushButtonReadSoftCode_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        QtConcurrent::run(this, &UDSToolBoxForm::readSoftCode, i );
    }
}

void UDSToolBoxForm::on_pushButtonClearSoftCode_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        showSoftCode( i, "" );
    }
}

void UDSToolBoxForm::on_pushButtonClearMsg_clicked()
{
    ui->textBrowserMsg->clear();
    ui->textBrowserFrame->clear();
}

void UDSToolBoxForm::on_pushButtonReadDTC_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        QtConcurrent::run(this, &UDSToolBoxForm::readDTC, i );
    }
}

void UDSToolBoxForm::on_pushButtonClearDTC_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        QtConcurrent::run(this, &UDSToolBoxForm::clearDTC, i );
    }
}

void UDSToolBoxForm::on_pushButtonReadCfgCode_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        QtConcurrent::run(this, &UDSToolBoxForm::readCfgCode, i );
    }
}

void UDSToolBoxForm::on_pushButtonWriteCfgCode_LF_clicked()
{
    QString code = ui->lineEditWriteCfgCode_LF->text();
    QtConcurrent::run(this, &UDSToolBoxForm::writeCfgCode, (quint32)RADAR_UDS_INDEX_LF, code );
}

void UDSToolBoxForm::on_pushButtonWriteCfgCode_RR_clicked()
{
    QString code = ui->lineEditWriteCfgCode_RR->text();
    QtConcurrent::run(this, &UDSToolBoxForm::writeCfgCode, (quint32)RADAR_UDS_INDEX_RR, code );
}

void UDSToolBoxForm::on_pushButtonWriteCfgCode_RF_clicked()
{
    QString code = ui->lineEditWriteCfgCode_RF->text();
    QtConcurrent::run(this, &UDSToolBoxForm::writeCfgCode, (quint32)RADAR_UDS_INDEX_RF, code );

}

void UDSToolBoxForm::on_pushButtonWriteCfgCode_LR_clicked()
{
    QString code = ui->lineEditWriteCfgCode_LR->text();
    QtConcurrent::run(this, &UDSToolBoxForm::writeCfgCode, (quint32)RADAR_UDS_INDEX_LR, code );
}

void UDSToolBoxForm::on_pushButtonWriteCfgCode_F_clicked()
{
//    QMessageBox::warning( this, QString::fromLocal8Bit("警告"), QString::fromLocal8Bit("暂时不支持前雷达的配置字写入") );
//    return; //前雷达的秘钥验证会失败，因为MASK还不知，故先屏蔽

    QString code = ui->lineEditWriteCfgCode_F->text();
    QtConcurrent::run(this, &UDSToolBoxForm::writeCfgCode, (quint32)RADAR_UDS_INDEX_F, code );
}

void UDSToolBoxForm::on_pushButtonClearCfgCode_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        showCfgCode( i, "" );
    }
}

void UDSToolBoxForm::on_pushButtonExitFactoryMode_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        QtConcurrent::run(this, &UDSToolBoxForm::factoryMode, i, false );
    }
}

void UDSToolBoxForm::on_comboBoxChannel_currentIndexChanged(int index)
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        mUDS[i]->setChannelIndex( index );
    }
}

void UDSToolBoxForm::on_pushButtonClearAngle_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        showAngle( i, "" );
    }
}

void UDSToolBoxForm::on_pushButtonAngleCode_clicked()
{
    for( int i=0; i<RADAR_UDS_INDEX_COUNT; i++ ){
        QtConcurrent::run(this, &UDSToolBoxForm::readAngle, i );
    }
}

void UDSToolBoxForm::on_checkBoxCycleReadDTC_stateChanged(int arg1)
{
    mMsgLog.close();
    mFrameLog.close();

    if( arg1 ){
        QString curTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");
        QString fileName1 = QString::fromLocal8Bit( "定时读取DTC(Frame)-%1.txt" )
                .arg( curTime );
        QString fileName2 = QString::fromLocal8Bit( "定时读取DTC(Msg)-%1.txt" )
                .arg( curTime );
        mFrameLog.setFileName( fileName1 );
        mMsgLog.setFileName( fileName2 );
        mFrameLog.open( QIODevice::WriteOnly );
        mMsgLog.open( QIODevice::WriteOnly );
        connect( &mTimer, &QTimer::timeout, this, &UDSToolBoxForm::on_pushButtonReadDTC_clicked );
        mTimer.start( 1000 );
    }else{
        mTimer.stop();
    }
}

//void UDSToolBoxForm::on_pushButtonWakeUpStart_clicked()
//{
//    if( !ui->checkBoxWakeUpRadar4->checkState() &&
//            !ui->checkBoxWakeUpRadar5->checkState() &&
//            !ui->checkBoxWakeUpRadar6->checkState() &&
//            !ui->checkBoxWakeUpRadar7->checkState() ){
//        QMessageBox::warning( this, QString::fromLocal8Bit("提示"), QString::fromLocal8Bit("请勾选需要测试的雷达!") );
//        return;
//    }

//    mMsgLog.close();
//    mFrameLog.close();

//    QString curTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");
//    QString fileName1 = QString::fromLocal8Bit( "唤醒测试(Frame)-%1.txt" )
//            .arg( curTime );
//    QString fileName2 = QString::fromLocal8Bit( "唤醒测试(Msg)-%1.txt" )
//            .arg( curTime );
//    mFrameLog.setFileName( fileName1 );
//    mMsgLog.setFileName( fileName2 );
//    mFrameLog.open( QIODevice::WriteOnly );
//    mMsgLog.open( QIODevice::WriteOnly );


//    mWarkUpTesting = true;
//    mWarkUpTestResult = true;
//    ui->labelWakeUpResult->setStyleSheet( "background-color: rgb(255, 255, 0);" );
//    ui->labelWakeUpResult->setText( "RUNNING" );

//#define WARKUP_TEST_TIME_INTERVAL (20)

//    if( ui->checkBoxWakeUpRadar4->checkState() ){
//        QtConcurrent::run(this, &UDSToolBoxForm::warkUpTest, (quint8)RADAR_UDS_INDEX_LR, 3, WARKUP_TEST_TIME_INTERVAL );
//    }
//    if( ui->checkBoxWakeUpRadar5->checkState() ){
//        QtConcurrent::run(this, &UDSToolBoxForm::warkUpTest, (quint8)RADAR_UDS_INDEX_RR, 3, WARKUP_TEST_TIME_INTERVAL );
//    }
//    if( ui->checkBoxWakeUpRadar6->checkState() ){
//        QtConcurrent::run(this, &UDSToolBoxForm::warkUpTest, (quint8)RADAR_UDS_INDEX_LF, 3, WARKUP_TEST_TIME_INTERVAL );
//    }
//    if( ui->checkBoxWakeUpRadar7->checkState() ){
//        QtConcurrent::run(this, &UDSToolBoxForm::warkUpTest, (quint8)RADAR_UDS_INDEX_RF, 3, WARKUP_TEST_TIME_INTERVAL );
//    }
//}

//bool UDSToolBoxForm::warkUpTest( quint8 udsIndex, quint64 timeOut )
//{
//    qint64 beginMS = QDateTime::currentMSecsSinceEpoch();
//    qDebug() << __FUNCTION__ << __LINE__ << QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss:zzz");
//    while( true  ){
//        quint64 curMS = QDateTime::currentMSecsSinceEpoch();
//        if( curMS - beginMS > timeOut * 1000 ){
//            qDebug() << __FUNCTION__ << __LINE__ << QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss:zzz");
//            return false;
//        }

//        UDSFrame responseFrame;
//        responseFrame.clear();
//        quint32 ID = mPhysicalAddress[udsIndex];
//        if( mUDS[udsIndex]->sendData(ID, QString("19 02 09"), &responseFrame)) {
//            qDebug() << __FUNCTION__ << __LINE__ << QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss:zzz");
//            return true;
//        }else{
//            QEventLoop loop;
//            QTimer timer;
//            timer.setSingleShot( true );
//            connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
//            timer.start( 20 ); // 等待
//            loop.exec();
//            timer.stop();
//        }

//    }
//    return false;
//}

//void UDSToolBoxForm::warkUpTest(quint8 udsIndex, quint64 timeOut, quint64 timeInterval)
//{
//    quint64 loopCnt = 0;
//    while( mWarkUpTesting ){
//        loopCnt++;
//        if ( !warkUpTest( udsIndex, timeOut ) ) {
//            mWarkUpTesting = false;
//            mWarkUpTestResult = false;
//            showRadarMsg( udsIndex, QString::fromLocal8Bit("唤醒雷达测试 响应失败 %1").arg(loopCnt), true );
//            emit wakeUpFail();
//            break;
//        }else{
//            showRadarMsg( udsIndex, QString::fromLocal8Bit("唤醒雷达测试 响应成功 %1").arg(loopCnt), false );
//            QEventLoop loop;
//            QTimer timer;
//            timer.setSingleShot( true );
//            connect( &timer, SIGNAL( timeout() ), &loop, SLOT( quit() ) );
//            timer.start( timeInterval * 1000 ); // 等待雷达休眠
//            loop.exec();
//            timer.stop();
//        }
//    }
//}

//void UDSToolBoxForm::on_pushButtonWakeUpStop_clicked()
//{
//    mWarkUpTesting = false;
//    if( mWarkUpTestResult ){
//        ui->labelWakeUpResult->setStyleSheet( "background-color: rgb(0, 255, 0);" );
//        ui->labelWakeUpResult->setText( "SUCCESS" );
//    }else{
//        ui->labelWakeUpResult->setStyleSheet( "background-color: rgb(255, 0, 0);" );
//        ui->labelWakeUpResult->setText( "FAIL" );
//    }
//    mMsgLog.close();
//    mFrameLog.close();
//}

void UDSToolBoxForm::deviceOpened()
{
    connect(mDeviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameRecieved , this, &UDSToolBoxForm::canFrame, Qt::UniqueConnection );
    connect(mDeviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameTransmited , this, &UDSToolBoxForm::frameTransmited, Qt::UniqueConnection );
}

void UDSToolBoxForm::deviceClosed()
{
    disconnect(mDeviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameRecieved , this, &UDSToolBoxForm::canFrame);
    disconnect(mDeviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameTransmited , this, &UDSToolBoxForm::frameTransmited);
}

void UDSToolBoxForm::on_comboBoxProject_currentIndexChanged(int index)
{
    mRadarProjectType = (RADAR_PROJECT_TYPE)index;
    initUDS();
    initDTCText();
}
