﻿#include <stdarg.h>
#include <stdio.h>
#include <windows.h>
#include "hal/rsp/rsp_types.h"
#include "other/temp.h"
#include "app/vehicle/vdy/vdy_types.h"
#include "alg/track/rdp_types.h"
#include "math.h"
//#include "app/hmi_types.h"
#include "app/adas/customizedrequirements/adas.h"
#include "app/system_mgr/cfg.h"
#include "app/system_mgr/radarCfg.h"
#include "common/common_eol.h"
#include "app/include/sharedDef.h"

//float gxoffset = 0.f, gyoffset = 0.f, groadlineoffset = 0.f;

static VDY_Info_t  vdyInfo;
static COM_vehicleSignals_t vehicleSignals;

static RSP_DetObjectList_t rspDetObjectList;

static VDY_vehicleFuncSwt_t functionSwitch;

static VDY_StaticState_t freezedVehStaticData;

static VDY_ChassisInfo_t chassisInfo;

RDP_TrkObjectList_t gRDP_TrkObjectListSimulator;			//跟踪目标列表，只包含Track类型的目标

#ifdef PC_DBG_FW
const radar_config_t radar_config_default =
#else
static const radar_config_t radar_config_default =
#endif
{
    .protocolVersion = (uint8_t)PROTOCOL_VER_USING,

    .radarId = 0u,
    .iVersion = 1u,
    .sendCarVel = 1u,      // Whether send estV
    .speedSource = 1u,     // Which speed to use? //0-est 1-OBD
    .debugModeSW = 0u,     // debug 开关，默认不打开
    .objExtendInfoSW = 1u, // 默认
    .objExtendInfoSW2 = 1u,
#if (1 == DEFAULT_SEND_RAW_Data)
    .dataMode = PARAM_DATAMODE_RAW_TRACK,
#else
#if (1U == DV_TEST_MODE_ENABLE)
    .dataMode = PARAM_DATAMODE_RAW_TRACK,
#else
    .dataMode = PARAM_DATAMODE_TRACK,
#endif
#endif

    .angleReverse = 1u,
    .agingTest = 0u,
    .resolutionTestMode = 0u, // 默认正常模式

    .anten_distance = ANT_DISTANCE_CAL,
    .anten_distance_y = ANT_DISTANCE_CAL_Y,
    .anten_phase = ANT_PHASE_CAL,

    .tx_pattern = (unsigned int)TX_SW_MODE,
    .tx_sel = 0u,
    .tx_cw = 0u,

    // .angleOffset = 0.0f,
    .installHorizontalAngleOffset = 0.0f,
    .installPitchAngleOffset = 0.0f,
    // .horizontalOffset = 0.0f,
    .installHeight = 0.0f,

    .radar_info = {
        .macaddr = {0x00, 0x04, 0x9F, 0x11, 0x22, 0x33},
        .ipaddr = {192U, 168U, 1U, 60U},
        /* LDRA_EXCLUDE_START 433 S*/
        .HwVer = HW_VERSION,
        .SwVer = SW_VERSION,
        .pcbaBBSN = {"001010063C1002070000x"},
        .radarSN = {"001010063C100234860199"},
        .imuName = {"LSM_6DS33_NULL"},
        .CalVer = {0, 0},
        /* LDRA_EXCLUDE_START 433 S*/
    },

    // LDRA软件错报了2个问题，Signed/unsigned conversion without cast. : (unsigned long and signed char):
    //  和 Value outside range of underlying type. : -8 exceeds unsigned long
    // 所以这一句不在分析。
    /*LDRA_NOANALYSIS */
    .rcsOffset = {-8, -7, -9, 0},
    /*LDRA_ANALYSIS */
    .antCaliMode = {EOL_CALIB_DATA_ONE_ANGLE_ONE_CHECK, EOL_CALIB_DATA_ONE_ANGLE_ONE_CHECK, EOL_CALIB_DATA_POS_AND_COPS, 0},
    //        .azimuthDecayTx = {
    //// LDRA_INSPECTED 243 S  Included file not protected with #define.   case is ok, minzubing 2023.03.13
    // #include "antPattern.hxx"
    //         },
    .enableTxPhaseShiftCalData = 0u,
};
static radar_config_t radar_config_user;
const radar_config_t *const radar_config_using = &radar_config_user;

// 如果在 Visual Studio 中编译，将 printf 重定向为 debug_printf
#if defined(_MSC_VER)
// 定义调试打印函数，支持普通字符串
// void debug_printf(const char* format, ...)
//{
//    char buffer[1024];  // 临时缓冲区，用于存储格式化后的字符串
//    va_list args;
//
//    // 获取变参列表并进行格式化
//    va_start(args, format);
//    vsnprintf(buffer, sizeof(buffer), format, args);
//    va_end(args);
//
//    // 使用 OutputDebugStringA 打印普通字符串
//    OutputDebugStringA(buffer);
//}

void openDebugConsole()
{
    AllocConsole();
    FILE *console;
    freopen_s(&console, "CONOUT$", "w", stdout);
    // printf("Debug console opened.\n");
}
// 使用宏替换 printf
// #define printf debug_printf
#endif

void Radar_initCfg(void)
{
    memcpy(&radar_config_user, &radar_config_default, sizeof(radar_config_user));
} // BASE_CFG

ADAS_RadarConfiguration_t RadarConfiguration;
/*
ADAS_RadarConfiguration_t *ADAS_getRadarCongiguration(void)
{
    RadarConfiguration.installAzimuthAngle = APAR_getAparInstallAzimuthAngle(); //获取雷达安装角
    RadarConfiguration.radarId = APAR_getAparRadarId(); //获取雷达Id

    return &RadarConfiguration;
}
*/
uint8_t CFG_getRadarId(void)
{
    return RadarConfiguration.radarId;
}

float CFG_getRadarInstallAngle(void)
{
    return RadarConfiguration.installAzimuthAngle;
}

//外部模块获取gRDP_TrkObjectList地址
RDP_TrkObjectList_t *RDP_getTrkObjectListSimulatorPointer()
{
    return &gRDP_TrkObjectListSimulator;
}

const RSP_DetObjectList_t *RSP_getDetObjListPointer()
{
    return &rspDetObjectList;
}

uint8_t radarID = 4;

VDY_vehicleFuncSwt_t *VDY_getVehFuncSwtPointer()
{
    functionSwitch.vehFuncSwitchReq.bit.BSDSwtReq = 1;
    functionSwitch.vehFuncSwitchReq.bit.DOWSwtReq = 1;
    functionSwitch.vehFuncSwitchReq.bit.LCASwtReq = 1;
    functionSwitch.vehFuncSwitchReq.bit.MainSwitch = 1;
    functionSwitch.vehFuncSwitchReq.bit.RCTABrkSwtReq = 1;
    functionSwitch.vehFuncSwitchReq.bit.RCTASwtReq = 1;
    functionSwitch.vehFuncSwitchReq.bit.RCWSwtReq = 1;
    functionSwitch.commonBit.igPowerDownTimeOfDOW = 1;

    functionSwitch.vdyLockStatus.bit.doorLockFrontRi = 2;
    functionSwitch.vdyLockStatus.bit.doorLockFrontLe = 2;
    functionSwitch.vdyLockStatus.bit.doorLockRearRi = 2;
    functionSwitch.vdyLockStatus.bit.doorLockRearLe = 2;


    return &functionSwitch;
}

VDY_StaticState_t *VDY_getVehStaticDataPointer()
{
    return &freezedVehStaticData;
}

const VDY_ChassisInfo_t* VDY_getChassisData()
{

    chassisInfo.vdyEspStatus.ESPDiagActv = 0;
    chassisInfo.vdyTrailerSts = 0;
    chassisInfo.vdyEspStatus.standStill = 1; //自车静止
    return &chassisInfo;
}

uint8_t APAR_getAparRadarId()
{
    radarID = RadarConfiguration.radarId;
    return radarID;
}

uint8_t APAR_setAparRadarId(uint8_t ID)
{
    RadarConfiguration.radarId = ID;
    radarID = ID;
}

float aparInstallAzimuthAngle = 46.9;   //安装角，根据实际雷达进行设置

float APAR_getAparInstallAzimuthAngle()
{
    return aparInstallAzimuthAngle;
}


SlaveRadarWarningsStatus gLeftRaWarnInfo;

const SlaveRadarWarningsStatus *get_LeftRadarWarnInfo(void)
{
    return (&gLeftRaWarnInfo);
}

Target_Thres_t  gTargets[2][2][MAX_NB_OF_TARGETS];
objectInfo_t  gOutTargets[MAX_NB_OF_TARGETS * 2];
radarDspParam_t gDspParam;

/**
 * @brief 获取VDY所有模块信息接口。
 *
 *
 * @param [in] None
 * @param [out] None
 * @return VDY_Info_t* 车身数据冻结后的数据存储地址
 */
VDY_Info_t *VDY_getVDY_Info_Pointer(void)
{
    return &vdyInfo;
}



RSP_DetObjectList_t *updateDetObjListData(float globalNoise)
{
    RSP_DetObjectList_t *RSP_DetObjectListAddr = &rspDetObjectList;			//RSP检测列表地址

//    RSP_DetObjectListAddr = RSP_getDetObjListPointer();

    unsigned short i = 0;
    int  retVal = 0;
    unsigned short allNumObject = 0;

    allNumObject = gDspParam.outAllTargets;
    if(allNumObject > MAX_NB_OF_TARGETS)
    {
        allNumObject = MAX_NB_OF_TARGETS;
        retVal = -1;
    }
    /* Update other information */
    RSP_DetObjectListAddr->rspDetObjectNum = allNumObject;
    RSP_DetObjectListAddr->rspCurFrameMode = 0;      // TODO, set it to 0 temporarily
    RSP_DetObjectListAddr->radarResolutionTestMode = 0x0;   //默认为0正常测试
    RSP_DetObjectListAddr->rspDetNoiseGlobal = globalNoise;

    /* Update detion list data */
    for(i = 0; i < RSP_DetObjectListAddr->rspDetObjectNum; i++)
    {
        RSP_DetObjectListAddr->rspDetObject[i].rspDetValid                = 1;    // TODO, set it to 1 temporarily
        RSP_DetObjectListAddr->rspDetObject[i].rspDetRange                = gOutTargets[i].range; // m;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetVelocity             = gOutTargets[i].velocity; // m;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetAzimuthAngle         = gOutTargets[i].angle;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetElevationAngle       = gOutTargets[i].heighAngle;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetSNR                  = gOutTargets[i].snr;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetFFTMAG               = gOutTargets[i].fftMag;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetDBFMAG               = gOutTargets[i].doaMag;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetRCS                  = gOutTargets[i].rcs;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetSolveVelAmbiguity1st = gOutTargets[i].firstvelocity;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetSolveVelAmbiguity2st = 0;    // TODO, set it to 0 temporarily
        RSP_DetObjectListAddr->rspDetObject[i].rspDetStatus = gOutTargets[i].DetStatus;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetMatchFlag              = gOutTargets[i].MF;
    }

    return RSP_DetObjectListAddr;
}

RDP_TrkObjectList_t *updateTrackObjListData()
{
    RSP_DetObjectList_t *RSP_DetObjectListAddr = &rspDetObjectList;			//RSP检测列表地址

//    RSP_DetObjectListAddr = RSP_getDetObjListPointer();

    unsigned short i = 0;
    int  retVal = 0;
    unsigned short allNumObject = 0;

    allNumObject = gDspParam.outAllTargets;
    if(allNumObject > MAX_NB_OF_TARGETS)
    {
        allNumObject = MAX_NB_OF_TARGETS;
        retVal = -1;
    }
    /* Update other information */
    RSP_DetObjectListAddr->rspDetObjectNum = allNumObject;
    RSP_DetObjectListAddr->rspCurFrameMode = 0;      // TODO, set it to 0 temporarily

    /* Update detion list data */
    for(i = 0; i < RSP_DetObjectListAddr->rspDetObjectNum; i++)
    {
        RSP_DetObjectListAddr->rspDetObject[i].rspDetValid                = 1;    // TODO, set it to 1 temporarily
        RSP_DetObjectListAddr->rspDetObject[i].rspDetRange                = gOutTargets[i].range; // m;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetVelocity             = gOutTargets[i].velocity; // m;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetAzimuthAngle         = gOutTargets[i].angle;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetElevationAngle       = gOutTargets[i].heighAngle;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetSNR                  = gOutTargets[i].snr;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetFFTMAG               = gOutTargets[i].fftMag;
        //RSP_DetObjectListAddr->rspDetObject[i].rspDetDBFMAG               = gOutTargets[i].DbfMag;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetRCS                  = gOutTargets[i].rcs;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetSolveVelAmbiguity1st = gOutTargets[i].firstvelocity;
        RSP_DetObjectListAddr->rspDetObject[i].rspDetSolveVelAmbiguity2st = 0;    // TODO, set it to 0 temporarily
        RSP_DetObjectListAddr->rspDetObject[i].rspDetStatus = gOutTargets[i].DetStatus;
//        RSP_DetObjectListAddr->rspDetObject[i].rspDetStatus = 0;
    }

    return RSP_DetObjectListAddr;
}

RDP_TrkObjectList_t simulatortargets = { .rdpTrkObject[0].rdpTrkObjDistX = 30 };
// RDP_TrkObjectList_t gRDP_TrkObjectListSimulator;			//跟踪目标列表，只包含Track类型的目标

VDY_DynamicEstimate_t freezedVehDyncData;

VDY_DynamicEstimate_t *VDY_getFreezedVehDyncDataPointer()
{
    return &freezedVehDyncData;
}

const RDP_TrkObjectList_t *getSimulatorgetSimulator()
{
    simulatortargets.rdpDetObjectNum = 1;

    for (int i = 0; i < 1; i++)
    {

//            simulatortargets.rdpTrkObject[i].rdpTrkObjVelocity = 0;
//            simulatortargets.rdpTrkObject[i].rdpTrkObjsnr = 60;
//            simulatortargets.rdpTrkObject[i].rdpTrkObjElevetionAngle = 15;
//            simulatortargets.rdpTrkObject[i].rdpTrkObjVrelX= -0.8;
//            simulatortargets.rdpTrkObject[i].rdpTrkObjVrelY= -5.96;
//            simulatortargets.rdpTrkObject[i].rdpTrkObjDistX = 2.25;
//            simulatortargets.rdpTrkObject[i].rdpTrkObjDistY= 3.75;
//            simulatortargets.rdpTrkObject[i].rdpTrkObjRange = 4.12;
//            simulatortargets.rdpTrkObject[i].rdpTrkObjAzimuthAngle = 66.03;
//            simulatortargets.rdpTrkObject[i].rdpTrkObjStatus = 1;

            freezedVehDyncData.vdySpeedInmps = 3.188;
            freezedVehDyncData.vdywheelangle = 0.2 / 12;

            simulatortargets.rdpTrkObject[i].rdpTrkObjVelocity = 0;
            simulatortargets.rdpTrkObject[i].rdpTrkObjsnr = 60;
            simulatortargets.rdpTrkObject[i].rdpTrkObjElevetionAngle = 15;
            simulatortargets.rdpTrkObject[i].rdpTrkObjVrelX= -1.16;
            simulatortargets.rdpTrkObject[i].rdpTrkObjVrelY= -4.24;
            simulatortargets.rdpTrkObject[i].rdpTrkObjDistX = 1.05;
            simulatortargets.rdpTrkObject[i].rdpTrkObjDistY= 1.8;
            simulatortargets.rdpTrkObject[i].rdpTrkObjRange = 4.12;
            simulatortargets.rdpTrkObject[i].rdpTrkObjAzimuthAngle = 59.74;
            simulatortargets.rdpTrkObject[i].rdpTrkObjStatus = 1;

    }

    return &simulatortargets;
}

int32_t initVDYinfo(void)
{
    vdyInfo.pVDY_ChassisInfo        = VDY_getChassisData();
    vdyInfo.pVDY_DynamicInfo        = VDY_getFreezedVehDyncDataPointer();
    vdyInfo.pVDY_StaticInfo         = VDY_getVehStaticDataPointer();
    vdyInfo.pVDY_VehicleFuncSwtInfo = VDY_getVehFuncSwtPointer();

    return 0;
}

uint8_t DBG_getHILDataDbgMode(void)
{
    return 0;
}

/**
 * @brief 获取VDY所有模块信息接口。
 *
 *
 * @param [in] None
 * @param [out] None
 * @return VDY_Info_t* 车身数据冻结后的数据存储地址
 */
VDY_Info_t *VDY_getVDY_Info_Pointer(void);

/**
 *  @b Description
 *  @n
 *      The function is used to get the vehicle signal data pointer.
 *
 *  @param[in]  trailStatus
 *      The trailer status, 0:Exit, 1:Enter
 *  @param[out]  VCAN_vehicleSignals*
 *      The pointer of vehicle signals
 */
const COM_vehicleSignals_t* VCAN_getVehicleSignal(void)
{
    return (const COM_vehicleSignals_t *)&vehicleSignals;
}

/**
 * @brief IG下电(IGPD:IGPowerDown)
3分钟才被允许关闭DOW，这里是时间到了，设置DOW状态，但是不能直接操
 *  作functionSwitch.vehFuncSwitchReq.bit.DOWSwtReq，所以更改为functionSwitch.commonBit.igPowerDownTimeOfDOW
 *  初始化时为1
 */
void VDY_setCloseDOWOfIGPDStatu(uint8_t dowSta)
{
    if (1 == dowSta)
    {
        functionSwitch.commonBit.igPowerDownTimeOfDOW = 1;
    }
    else if (0 == dowSta)
    {
        functionSwitch.commonBit.igPowerDownTimeOfDOW = 0;
    }
}
uint8_t VDY_getCloseDOWOfIGPDStatu(void)
{
    return functionSwitch.commonBit.igPowerDownTimeOfDOW; 
}

const VDY_vehicleFuncSwt_t* VDY_getVehiFuncSwtPointer(void)
{
    // 从通讯模块中获取开关状态
    const COM_vehicleSignals_t *vehiclePtr = VCAN_getVehicleSignal();

    functionSwitch.vdyDoorStatus = vehiclePtr->comDoorStatus;
    functionSwitch.vdyTurnSignal = vehiclePtr->comTurnLight;
    functionSwitch.turnSignalWorkCon = vehiclePtr->comTurnSignalWorkCon;
    functionSwitch.vdyLockStatus = vehiclePtr->comDoorLockStatus;
    functionSwitch.vdyTabStatus = vehiclePtr->comTabStatus;
    functionSwitch.vdyFcrDowStatus = vehiclePtr->comFcrDOWStatus;
    functionSwitch.vdyRcrDowStatus = vehiclePtr->comRcrDOWStatus;
    functionSwitch.vdySecurtiyStatus = vehiclePtr->comVehicleStatusSecurtiy; 
    functionSwitch.commonBit.igPowerDownTimeOfDOW = VDY_getCloseDOWOfIGPDStatu();

    // TODO:前角关闭报警
    uint8_t Radarid = CFG_getRadarId();
    if (Radarid == 6 || Radarid == 7)
    {
        functionSwitch.vehFuncSwitchReq.bit.BSDSwtReq = 0;
#if  (1 != ALARM_TYPE_FDOW_EN)
        functionSwitch.vehFuncSwitchReq.bit.DOWSwtReq = 0;
#endif
        functionSwitch.vehFuncSwitchReq.bit.LCASwtReq = 0;
        functionSwitch.vehFuncSwitchReq.bit.MainSwitch = 0;
        functionSwitch.vehFuncSwitchReq.bit.RCTABrkSwtReq = 0;
        functionSwitch.vehFuncSwitchReq.bit.RCTASwtReq = 0;
        functionSwitch.vehFuncSwitchReq.bit.RCWSwtReq = 0;
#ifdef VEHICLE_TYPE_BYD_YUAN_PIUS
        functionSwitch.vehFuncSwitchReq.bit.MainSwitch = 1;
        functionSwitch.vehFuncSwitchReq.bit.FCTASwtReq = 1;
        functionSwitch.vehFuncSwitchReq.bit.FCTABrkSwtReq = 1;
#endif
    }
    if (Radarid == 4 || Radarid == 5)
    {
        functionSwitch.vehFuncSwitchReq.bit.FCTASwtReq = 0;
        functionSwitch.vehFuncSwitchReq.bit.FCTABrkSwtReq = 0;
#ifdef VEHICLE_TYPE_BYD_YUAN_PIUS
        functionSwitch.vehFuncSwitchReq.bit.BSDSwtReq = 1;
        functionSwitch.vehFuncSwitchReq.bit.DOWSwtReq = 1;
        functionSwitch.vehFuncSwitchReq.bit.LCASwtReq = 1;
        functionSwitch.vehFuncSwitchReq.bit.MainSwitch = 1;
        functionSwitch.vehFuncSwitchReq.bit.RCTASwtReq = 1;
        functionSwitch.vehFuncSwitchReq.bit.RCWSwtReq = 1;
#endif 
    }

    return &functionSwitch;
}

void Simulator(float times)
{
    int usedCnt = 0;
    float move;

    simulatortargets.rdpDetObjectNum = 1;

    for (int i = 0; i < 1; i++)
    {

        simulatortargets.rdpTrkObject[i].rdpTrkObjVelocity = 10;

        simulatortargets.rdpTrkObject[i].rdpTrkObjsnr = 60;
        simulatortargets.rdpTrkObject[i].rdpTrkObjElevetionAngle = 15;
        simulatortargets.rdpTrkObject[i].rdpTrkObjHeadingAngle = 90;
        simulatortargets.rdpTrkObject[i].rdpTrkObjVrelX = -2;
        simulatortargets.rdpTrkObject[i].rdpTrkObjVrelY = 0;
        simulatortargets.rdpTrkObject[i].rdpTrkObjStatus = 255;
        simulatortargets.rdpTrkObject[i].rdpTrkObjDistX = 30 + 0.05 * times * simulatortargets.rdpTrkObject[i].rdpTrkObjVrelX;
        simulatortargets.rdpTrkObject[i].rdpTrkObjDistY = 1;
        move = 1 + simulatortargets.rdpTrkObject[i].rdpTrkObjDistX * simulatortargets.rdpTrkObject[i].rdpTrkObjDistX;
        simulatortargets.rdpTrkObject[i].rdpTrkObjRange = sqrtf(move);
        simulatortargets.rdpTrkObject[i].rdpTrkObjRange = simulatortargets.rdpTrkObject[i].rdpTrkObjDistX;
        simulatortargets.rdpTrkObject[i].rdpTrkObjAzimuthAngle = coshf(simulatortargets.rdpTrkObject[i].rdpTrkObjDistX / move) * 57.3;
        simulatortargets.rdpTrkObject[i].rdpTrkObjAzimuthAngle = 90;
        simulatortargets.rdpTrkObject[i].rdpTrkObjBoxLength = 6;
        simulatortargets.rdpTrkObject[i].rdpTrkObjBoxWidth = 6;
    }
}

RDP_TrkObjectList_t* RDP_getCustomizeObjListData(float times)
{
    int usedCnt = 0;
    uint8_t i = 0; 
    float move;

    simulatortargets.rdpDetObjectNum = 1;

    for (i = 0; i < 1; i++)
    {

        simulatortargets.rdpTrkObject[i].rdpTrkObjReliability = 99;
        simulatortargets.rdpTrkObject[i].rdpTrkObjVelocity = 10;

        simulatortargets.rdpTrkObject[i].rdpTrkObjsnr = 60;
        simulatortargets.rdpTrkObject[i].rdpTrkObjElevetionAngle = 15;
        simulatortargets.rdpTrkObject[i].rdpTrkObjHeadingAngle = 90;
        simulatortargets.rdpTrkObject[i].rdpTrkObjVrelX = -7;
        simulatortargets.rdpTrkObject[i].rdpTrkObjVrelY = -0.5f;
        simulatortargets.rdpTrkObject[i].rdpTrkObjStatus = 255;
        if (simulatortargets.rdpTrkObject[i].rdpTrkObjDistX > 4.0f)
        {
            simulatortargets.rdpTrkObject[i].rdpTrkObjDistX = simulatortargets.rdpTrkObject[i].rdpTrkObjDistX +
                                                              times * simulatortargets.rdpTrkObject[i].rdpTrkObjVrelX;
            simulatortargets.rdpTrkObject[i].rdpTrkObjDistY = 1.5f;
        }
        else
        {
            simulatortargets.rdpTrkObject[i].rdpTrkObjVrelX = -1.9; 
            simulatortargets.rdpTrkObject[i].rdpTrkObjDistX = simulatortargets.rdpTrkObject[i].rdpTrkObjDistX + times * simulatortargets.rdpTrkObject[i].rdpTrkObjVrelX;
            // simulatortargets.rdpTrkObject[i].rdpTrkObjDistY = simulatortargets.rdpTrkObject[i].rdpTrkObjDistY + times * simulatortargets.rdpTrkObject[i].rdpTrkObjVrelY;
            // simulatortargets.rdpTrkObject[i].rdpTrkObjDistY = 2.0f + 3 * (4 - simulatortargets.rdpTrkObject[i].rdpTrkObjDistX) / 4;
            simulatortargets.rdpTrkObject[i].rdpTrkObjDistY = 1.5f;
        }
        
        move = 1 + simulatortargets.rdpTrkObject[i].rdpTrkObjDistX * simulatortargets.rdpTrkObject[i].rdpTrkObjDistX;
        simulatortargets.rdpTrkObject[i].rdpTrkObjRange = sqrtf(move);
        simulatortargets.rdpTrkObject[i].rdpTrkObjRange = simulatortargets.rdpTrkObject[i].rdpTrkObjDistX;
        simulatortargets.rdpTrkObject[i].rdpTrkObjAzimuthAngle = coshf(simulatortargets.rdpTrkObject[i].rdpTrkObjDistX / move) * 57.3;
        simulatortargets.rdpTrkObject[i].rdpTrkObjAzimuthAngle = 90;
        simulatortargets.rdpTrkObject[i].rdpTrkObjBoxLength = 6;
        simulatortargets.rdpTrkObject[i].rdpTrkObjBoxWidth = 6; 
        simulatortargets.rdpTrkObject[i].rdpTrkObjMissCnt = 0; 
        if (simulatortargets.rdpTrkObject[i].rdpTrkObjHitCnt < 255)
        {
            simulatortargets.rdpTrkObject[i].rdpTrkObjHitCnt++;
            simulatortargets.rdpTrkObject[i].rdpLifeCycleCnt++;
        }
    }

    return &simulatortargets; 
}


