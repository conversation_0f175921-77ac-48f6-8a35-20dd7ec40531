﻿#ifndef VIEWSMANAGER_H
#define VIEWSMANAGER_H

#include "views_global.h"

#include <QObject>
#include <QMap>
#include <QTimer>
#include <QMutex>
#include <QElapsedTimer>

#include "analysis/analysisdataf.h"
#include "analysis/analysisdata.h"
#include "truevalue/hesailider.h"

class QComboBox;

namespace Analysis {
class CalculationWorker;
}

namespace Views {
namespace ObjectView {
class ObjectCoordinateSystem;
class ObjectView;
}
namespace AnalysisView {
class AnalysisDataViewI;
}

namespace TrueSystemView {
class TrueSystemView;
}

class VIEWS_EXPORT ViewsManager : public QObject
{
    Q_OBJECT
public:
    explicit ViewsManager(Analysis::CalculationWorker *calculationWorker, QObject *parent = nullptr);
    ~ViewsManager();

    QComboBox *comboBoxRadarType() const { return mComboBoxRadarType; }

    bool startView();
    bool stopView();

    AnalysisView::AnalysisDataViewI* analysisDataView(quint8 radarID, QWidget *parent = nullptr);
    bool analysisDataViewExisted(quint8 radarID) const { return mAnalysisDataViews[radarID]; }

    ObjectView::ObjectView* objectView(const QVariant &settings, QWidget *parent = nullptr);

    TrueSystemView::TrueSystemView* trueSystemView(QWidget *parent = nullptr);
    bool trueSystemViewExisted() const { return mTrueSystemView; }

    QVariant getAnalysisDataViewsSettings() const;
    void setAnalysisDataViewsSettings(const QVariant& settings);

    QVariant getObjectViewsSettings() const;

    void showFaultInfo( bool bShow );
    void showVersionInfo( bool bShow );
    void showAngleInfo( bool bShow );

signals:
    void trueSystemViewChanged(bool openned);

public slots:
    void viewData();
    void dataProcessRawViewData(int radarID);
    void deviceOpened();
    void deviceClosed();
    void updateRadarResetCount( quint64 count, quint8 channel );

//private slots:
    /** @brief 雷达类型改变 */
    void radarTypeChanged(int index);
    void calculateFinished(quint8 radarID, const AnalysisData &analysisData);
    void calculateFinishedF(const Parser::ParsedDataTypedef::TargetsF &targets);
    void calculateTargetFinished(quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &target);

    /** @brief 禾赛真值 */
    void showHeSaiTarget(int index, int count, const QString &targetString);

private:
    Analysis::CalculationWorker *mCalculationWorker{0};

    AnalysisView::AnalysisDataViewI* mAnalysisDataViews[MAX_RADAR_COUNT];
    QList<ObjectView::ObjectCoordinateSystem*> mObjectCoordinateSystems;
    TrueSystemView::TrueSystemView *mTrueSystemView{0};

    Parser::ParsedDataTypedef::ParsedData mParsedData;  // 前雷达
    AnalysisData mAnalysisDatas[MAX_RADAR_COUNT];       // 角雷达
    Pandar64_Targets *mPandar64_Targets = new Pandar64_Targets;
    bool mDeviceOpened{false};

    QMutex mMutex;
    QTimer mViewTimer;
    QElapsedTimer mElapsedTimer;

    QComboBox *mComboBoxRadarType{0};
};

} // namespace Views

#endif // VIEWSMANAGER_H
