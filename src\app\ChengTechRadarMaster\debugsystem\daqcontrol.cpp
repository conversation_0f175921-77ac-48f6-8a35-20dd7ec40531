﻿#include "daqcontrol.h"
#include "ui_daqcontrol.h"

#include "playbacknetworkworker.h"
#include "playbackcanwoker.h"

#include "devices/devicemanager.h"
#include "devices/ideviceworker.h"

#include "analysis/analysismanager.h"
#include "analysis/analysisworker.h"
#include "analysis/calculationworker.h"

#include "utils/settingshandler.h"

#include <QTimer>
#include <QEventLoop>
#include <QFileInfo>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>

DAQControl::DAQControl(Devices::Can::DeviceManager *deviceManager, Analysis::AnalysisManager *analysisManager, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::DAQControl),
    mDeviceManager(deviceManager),
    mAnalysisManager(analysisManager)
{
    ui->setupUi(this);

    mPlaybackCanWoker = new PlaybackCanWoker(mDeviceManager);
    mPlaybackCanWorkerThread = new QThread;
    mPlaybackCanWoker->moveToThread(mPlaybackCanWorkerThread);

    mPlaybackNetworkWorker = new PlaybackNetworkWorker;
    mPlaybackNetworkWorkerThread = new QThread;
    mPlaybackNetworkWorker->moveToThread(mPlaybackNetworkWorkerThread);

    connect(mPlaybackCanWorkerThread, &QThread::started, this, &DAQControl::DAQControl::playbackThreadStarted);

    connect(mPlaybackCanWoker, &PlaybackCanWoker::analysisRadarData,
            mAnalysisManager->calculationWorker(), &Analysis::CalculationWorker::calculatePlayback);

    connect(this, &DAQControl::startPlayback, mPlaybackCanWoker, &PlaybackCanWoker::start);
    connect(this, &DAQControl::stopPlayback, mPlaybackCanWoker, &PlaybackCanWoker::stop);
    connect(this, &DAQControl::pausePlayback, mPlaybackCanWoker, &PlaybackCanWoker::pause);
    connect(this, &DAQControl::playNextPlayback, mPlaybackCanWoker, &PlaybackCanWoker::playNext);
    connect(this, &DAQControl::playPrevPlayback, mPlaybackCanWoker, &PlaybackCanWoker::playPrev);
    connect(this, &DAQControl::playPlayback, mPlaybackCanWoker, &PlaybackCanWoker::play);

//    connect(mAnalysisManager, &AnalysisManager::analysisWorkerNew, this, [=](/*AnalysisDataView*/void *analysisDataView){
//         AnalysisWorker *analysisWorker = ((AnalysisDataView*)analysisDataView)->analysisWorker();
//         connect(mPlaybackWoker, &PlaybackWoker::analysisTargets, analysisWorker, &AnalysisWorker::playbackAnalysisTargets);
//         connect(mPlaybackWoker, &PlaybackWoker::analysisRadarData, analysisWorker, &AnalysisWorker::playbackAnalysisRadarData);
//    });

    connect(mPlaybackCanWoker, &PlaybackCanWoker::playFrameTotal, this, [=](int total) {
        qDebug() << __FUNCTION__ << __LINE__ << total;
        ui->labelFrame->setText(QString("00:00:00.000 0/%1").arg(total));
        if (!total)
        {
            ui->pushButtonStartAndStop->setEnabled(false);
            ui->pushButtonStartAndStop->setChecked(false);
            QMessageBox::warning(this, QString::fromLocal8Bit("初始化"), QString::fromLocal8Bit("无数据!"));
        }
    });
    connect(mPlaybackCanWoker, &PlaybackCanWoker::playIndex, this, [=](int index, int total, const QDateTime &time) {
        ui->labelFrame->setText(QString("%1 %2/%3").arg(time.toString("hh:mm:ss.zzz")).arg(index + 1).arg(total));
    });
    connect(mPlaybackCanWoker, &PlaybackCanWoker::playStarted, this, [=](){
        ui->pushButtonNextFrame->setEnabled(true);
        ui->pushButtonPrevFrame->setEnabled(true);
    });
    connect(mPlaybackCanWoker, &PlaybackCanWoker::playFinished, this, [=]() {
        ui->pushButtonStartAndStop->setChecked(false);
        ui->pushButtonNextFrame->setEnabled(false);
        ui->pushButtonPrevFrame->setEnabled(false);
        if (mWorkMode == PlaybackNetworkWorker::RechargeMode) {
            exitDebugMode();
        }
    });

    connect(this, &DAQControl::openTCPServer, mPlaybackNetworkWorker, &PlaybackNetworkWorker::openTCPServer);
    connect(this, &DAQControl::closeTCPServer, mPlaybackNetworkWorker, &PlaybackNetworkWorker::closeTCPServer);
    connect(mPlaybackNetworkWorker, &PlaybackNetworkWorker::tcpServerOpened, this, &DAQControl::tcpServerOpened);
    connect(mPlaybackNetworkWorker, &PlaybackNetworkWorker::tcpServerClosed, this, &DAQControl::tcpServerClosed);
    connect(mPlaybackNetworkWorker, &PlaybackNetworkWorker::tcpClientConnected, this, &DAQControl::tcpClientConnected);
    connect(mPlaybackNetworkWorker, &PlaybackNetworkWorker::tcpClientDisonnected, this, &DAQControl::tcpClientDisonnected);
    connect(mPlaybackNetworkWorker, &PlaybackNetworkWorker::dckVersion, this, &DAQControl::dckVersion);

    connect(this, &DAQControl::startAlgorithmPlay, mPlaybackNetworkWorker, &PlaybackNetworkWorker::start);
    connect(this, &DAQControl::stopAlgorithmPlay, mPlaybackNetworkWorker, &PlaybackNetworkWorker::stop);
    connect(this, &DAQControl::nextAlgorithmPlayFrame, mPlaybackNetworkWorker, &PlaybackNetworkWorker::nextFrame);
    connect(this, &DAQControl::pauseAlgorithmPlay, mPlaybackNetworkWorker, &PlaybackNetworkWorker::pause);
    connect(mPlaybackNetworkWorker, &PlaybackNetworkWorker::initResult, this, &DAQControl::initAlgorithmPlayResult);
    connect(mPlaybackNetworkWorker, &PlaybackNetworkWorker::playState, this, &DAQControl::playAlgorithmPlayState);
    connect(mPlaybackNetworkWorker, &PlaybackNetworkWorker::playFrameInfo, this, &DAQControl::playAlgorithmPlayFrameInfo);

    loadSettings();
}

DAQControl::~DAQControl()
{
    saveSettings();
    delete ui;
}

void DAQControl::loadSettings()
{
    mWorkMode = (PlaybackNetworkWorker::WorkMode)SETTINGS_GET_VALUE("DAQControl/WorkMode", 0).toInt();
    ui->comboBoxModel->setCurrentIndex(mWorkMode);
    mPlayDataType = (PlayDataType)SETTINGS_GET_VALUE("DAQControl/CollectMode", 0).toInt();
    ui->comboBoxCollectMode->setCurrentIndex(mPlayDataType);
    ui->checkBoxSingleStep->setChecked(SETTINGS_GET_VALUE("DAQControl/SingleStep", false).toBool());
    ui->spinBoxStartFrameIndex->setValue(SETTINGS_GET_VALUE("DAQControl/StartFrmaeIndex", 1).toInt());
    ui->spinBoxOneFrameProfileCount->setValue(SETTINGS_GET_VALUE("DAQControl/OneFrameProfileCount", 2).toInt());
    mCanChannelIndex = SETTINGS_GET_VALUE("DAQControl/CANIndex", 0).toInt();
    mPlaybackCanWoker->setChannelIndex(mCanChannelIndex);
    ui->comboBoxCanIndex->setCurrentIndex(mCanChannelIndex);
    ui->lineEditCollectFrameCount->setText(SETTINGS_GET_VALUE("DAQControl/CollectFrameCount", "-1").toString());
    ui->comboBoxTransportProtocol->setCurrentIndex(SETTINGS_GET_VALUE("DAQControl/Network/TransportProtocol", 0).toInt());
    ui->checkBoxAnalyIP->setChecked(SETTINGS_GET_VALUE("DAQControl/Network/AnalyIP", false).toBool());
    ui->lineEditIP->setText(SETTINGS_GET_VALUE("DAQControl/Network/IP", "*************").toString());
    ui->lineEditPort->setText(SETTINGS_GET_VALUE("DAQControl/Network/Port", "5001").toString());
    ui->lineEditProjectFile->setText(SETTINGS_GET_VALUE("DAQControl/Project/ProjectFile").toString());
    ui->lineEditProfilesConfig->setText(SETTINGS_GET_VALUE("DAQControl/Project/ProfileConfigFiles").toString());

    ui->checkBoxAlgorithm->setChecked(SETTINGS_GET_VALUE("DAQControl/Algorithm/Algorithm", false).toBool());
    ui->comboBoxAlgorithmRadarID->setCurrentText(SETTINGS_GET_VALUE("DAQControl/Algorithm/AlgorithmRadarID", "4").toString());

    ui->pushButtonInit->setEnabled(mWorkMode == PlaybackNetworkWorker::PlaybackMode || mPlayDataType == PlayRawTarget || mPlayDataType == PlayTrackTarget);
}

void DAQControl::saveSettings()
{
    SETTINGS_SET_VALUE("DAQControl/WorkMode", ui->comboBoxModel->currentIndex());
    SETTINGS_SET_VALUE("DAQControl/CollectMode", ui->comboBoxCollectMode->currentIndex());
    SETTINGS_SET_VALUE("DAQControl/SingleStep", ui->checkBoxSingleStep->isChecked());
    SETTINGS_SET_VALUE("DAQControl/StartFrmaeIndex", ui->spinBoxStartFrameIndex->value());
    SETTINGS_SET_VALUE("DAQControl/OneFrameProfileCount", ui->spinBoxOneFrameProfileCount->value());
    SETTINGS_SET_VALUE("DAQControl/CANIndex", ui->comboBoxCanIndex->currentIndex());
    SETTINGS_SET_VALUE("DAQControl/CollectFrameCount", ui->lineEditCollectFrameCount->text().toInt());
    SETTINGS_SET_VALUE("DAQControl/Network/TransportProtocol", ui->comboBoxTransportProtocol->currentIndex());
    SETTINGS_SET_VALUE("DAQControl/Network/AnalyIP", ui->checkBoxAnalyIP->isChecked());
    SETTINGS_SET_VALUE("DAQControl/Network/IP", ui->lineEditIP->text());
    SETTINGS_SET_VALUE("DAQControl/Network/Port", ui->lineEditPort->text());
    SETTINGS_SET_VALUE("DAQControl/Project/ProjectFile", ui->lineEditProjectFile->text());
    SETTINGS_SET_VALUE("DAQControl/Project/ProfileConfigFiles", ui->lineEditProfilesConfig->text());

    SETTINGS_SET_VALUE("DAQControl/Algorithm/Algorithm", ui->checkBoxAlgorithm->isChecked());
    SETTINGS_SET_VALUE("DAQControl/Algorithm/AlgorithmRadarID", ui->comboBoxAlgorithmRadarID->currentText());
}

void DAQControl::saveStarted(const QString &saveFile)
{
    int collectFrameCount = ui->lineEditCollectFrameCount->text().toInt();
    if (collectFrameCount <= 0)
    {
        ui->lineEditCollectFrameCount->setText("-1");
        collectFrameCount = -1;
    }

    mPlaybackNetworkWorker->setDAQFile(saveFile);
    mPlaybackNetworkWorker->setCollectFrameCount(collectFrameCount);

    emit startAlgorithmPlay(ui->spinBoxStartFrameIndex->value() - 1,
               50,
               ui->checkBoxSingleStep->isChecked());

    collectFrameCount *= ui->spinBoxOneFrameProfileCount->value();
    QTimer::singleShot(2000, this, [=](){
        if (!mPlaybackNetworkWorker->isPlaying())
        {
            return;
        }
        QByteArray data = QByteArray::fromHex("4354435400000000");
        data[4] = ui->comboBoxCollectMode->currentIndex() & 0xFF;
        data[5] = (collectFrameCount >> 16) & 0xFF;
        data[6] = (collectFrameCount >> 8) & 0xFF;
        data[7] = collectFrameCount & 0xFF;

        if (!mDeviceManager->sendFrame(mCanChannelIndex, 0x3F9, data, true))
        {
            playAlgorithmPlayState(PlaybackNetworkWorker::PlayError, QString::fromLocal8Bit("CAN启动采数失败!"));
            emit stopSave();
        }
    });
}

void DAQControl::playbackThreadStarted()
{
    ui->pushButtonStartAndStop->setEnabled(true);
}

void DAQControl::closeEvent(QCloseEvent *event)
{
    qDebug() << __FUNCTION__ << __LINE__ << event;
}

void DAQControl::tcpServerOpened(bool opened, const QString &message)
{
    if (opened)
    {
        ui->pushButtonStartStopServer->setText(QString::fromLocal8Bit("关闭服务"));
    }
    else
    {
        QMessageBox::warning(this, QString::fromLocal8Bit("启动服务"), QString::fromLocal8Bit("启动服务失败!\n%1").arg(message));
    }
}

void DAQControl::tcpServerClosed()
{
    ui->pushButtonStartStopServer->setText(QString::fromLocal8Bit("启动服务"));
    tcpClientDisonnected();
    mPlaybackNetworkWorkerThread->quit();
}

void DAQControl::tcpClientConnected(const QString &IP, quint16 port)
{
    ui->labelIP->setText(IP);
    ui->labelPort->setText(QString::number(port));
    ui->pushButtonInit->setEnabled(true);
    ui->pushButtonStartAndStop->setEnabled(true);
}

void DAQControl::tcpClientDisonnected()
{
    ui->labelIP->setText("0.0.0.0");
    ui->labelPort->setText("0000");
    ui->labelDCKVersion->setText("0.0.0");
    ui->pushButtonInit->setEnabled(false);
    ui->pushButtonStartAndStop->setEnabled(false);
}

void DAQControl::dckVersion(const QString &version)
{
    ui->labelDCKVersion->setText(version);
}

void DAQControl::initAlgorithmPlayResult(bool ok)
{
    if (ok && (mWorkMode == PlaybackNetworkWorker::PlaybackMode || mPlaybackNetworkWorker->clientConnected()))
    {
        ui->spinBoxOneFrameProfileCount->setRange(1, mPlaybackNetworkWorker->profileSize());
        QTimer::singleShot(2000, this, [=](){
            ui->pushButtonStartAndStop->setEnabled(true);
        });
    }
}

void DAQControl::playAlgorithmPlayState(int state, const QString &message)
{
    qDebug() << __FUNCTION__ << __LINE__ << state << message;
    switch (state) {
    case PlaybackNetworkWorker::Playing:
        ui->pushButtonStartAndStop->setChecked(true);
        ui->spinBoxStartFrameIndex->setRange(1, mPlaybackNetworkWorker->frameCount());
        ui->pushButtonInit->setEnabled(false);
        ui->pushButtonStartAndStop->setEnabled(true);
        break;
    case PlaybackNetworkWorker::PlayError:
        QMessageBox::warning(this, "DAQ", message);
    case PlaybackNetworkWorker::PlayStoped:
        ui->pushButtonStartAndStop->setChecked(false);
        ui->pushButtonInit->setEnabled(true);
        ui->pushButtonStartAndStop->setEnabled(false);
        if (mWorkMode == PlaybackNetworkWorker::CollectMode)
        {
            emit stopSave();
        }
        break;
    }
}

void DAQControl::playAlgorithmPlayFrameInfo(int index, int total, const QString &message)
{
//    qDebug() << __FUNCTION__ << __LINE__ << index << total;
    ui->labelCurrentFrame->setText(QString("%1/%2").arg(index).arg(total));
}

void DAQControl::on_pushButtonStartStopServer_clicked()
{
    if (QString::fromLocal8Bit("启动服务") == ui->pushButtonStartStopServer->text())
    {
        QString ip  = ui->lineEditIP->text();
        quint16 port = ui->lineEditPort->text().toULongLong();
        if (ip.isEmpty() || !port)
        {
            QMessageBox::warning(this, QString::fromLocal8Bit("启动服务"), QString::fromLocal8Bit("IP或端口设置有误"));
            return;
        }

        mPlaybackNetworkWorkerThread->start();
        emit openTCPServer(ip, port, ui->checkBoxAnalyIP->isChecked());
    }
    else
    {
        emit closeTCPServer();
    }
}

void DAQControl::on_pushButtonProjectFile_clicked()
{
    QString filter = tr("All(*.csv *.dat *.prj);; Target (*.csv);; ADC (*.dat);; Project files (*.prj)");
    switch (mPlayDataType)
    {
    case PlayRawTarget:
    case PlayTrackTarget:
        filter = tr("Target (*.csv);; Project files (*.prj)");
        break;
    case PlayADC:
    case Play1DFFT:
    case Play2DFFT:
        filter = tr("ADC (*.dat);; Project files (*.prj)");
        break;
    }

    QString filename = QFileDialog::getOpenFileName(this,
                                                    QString::fromLocal8Bit("打开回放文件"),
                                                    ui->lineEditProjectFile->text(),
                                                    filter);
    if (filename.isEmpty())
    {
//        QMessageBox::warning(this, QString::fromLocal8Bit("选择回放文件"), QString::fromLocal8Bit("未正确选择回放文件"));
        return;
    }
    ui->lineEditProjectFile->setText(filename);
}

void DAQControl::on_pushButtonProfilesConfig_clicked()
{
    ui->pushButtonStartAndStop->setEnabled(false);
    QStringList filenames = QFileDialog::getOpenFileNames(this, "Open Profile Config File", ui->lineEditProjectFile->text(), tr("Text (*.hxx)"));
    if (!filenames.size())
    {
        return;
    }
    ui->lineEditProfilesConfig->setText(filenames.join(";"));
}

void DAQControl::on_pushButtonStartAndStop_clicked(bool checked)
{
    if (checked) {
        qDebug() << __FUNCTION__ << __LINE__ << mPlayDataType;
        if (mPlayDataType == PlayRawTarget || mPlayDataType == PlayTrackTarget) {
            if (mWorkMode == PlaybackNetworkWorker::RechargeMode) {
                intoDebugMode();
            }
            else if (mPlayDataType == PlayTrackTarget && ui->checkBoxAlgorithm->isChecked()) {
#ifdef ALGORITHM_DEBUG
                qDebug() << __FUNCTION__ << __LINE__ << "message";
                mPlaybackCanWoker->initDataProcess();
#endif
            }
            emit startPlayback(ui->spinBoxStartFrameIndex->value(),
                               mWorkMode == PlaybackNetworkWorker::RechargeMode,
                               ui->checkBoxSingleStep->isChecked(),
                               ui->checkBoxAlgorithm->isChecked(),
                               ui->comboBoxAlgorithmRadarID->currentText().toUInt());
        } else {
            if (mWorkMode == PlaybackNetworkWorker::CollectMode) // 采集
            {
                QString adcType = ui->comboBoxCollectMode->currentText();
                emit startSave(adcType);
            }
            else if (mWorkMode == PlaybackNetworkWorker::RechargeMode) // 回灌
            {
                QByteArray data = QByteArray::fromHex("4354435400000000");
                quint32 profileFrameTotal = mPlaybackNetworkWorker->profileFrameCount();
                data[4] = (profileFrameTotal >> 24) & 0xFF;
                data[5] = (profileFrameTotal >> 16) & 0xFF;
                data[6] = (profileFrameTotal >> 8) & 0xFF;
                data[7] = profileFrameTotal & 0xFF;

                mDeviceManager->sendFrame(mCanChannelIndex, 0x3FA, data, true);

                QTimer::singleShot(2000, this, [=](){
                    emit startAlgorithmPlay(ui->spinBoxStartFrameIndex->value() - 1,
                                            50,
                                            ui->checkBoxSingleStep->isChecked());
                });
            }
            else if (mWorkMode == PlaybackNetworkWorker::PlaybackMode) // 回放
            {

                    emit startAlgorithmPlay(ui->spinBoxStartFrameIndex->value() - 1,
                                            50,
                                            ui->checkBoxSingleStep->isChecked());
            }
            else
            {
                return;
            }
        }
    }
    else
    {
        if (mPlayDataType == PlayRawTarget || mPlayDataType == PlayTrackTarget)
        {
            emit stopPlayback();
        }
        else
        {
            if (mWorkMode == PlaybackNetworkWorker::CollectMode) // 采集
            {
                emit stopSave();
            }

            emit stopAlgorithmPlay();
        }
    }
}

void DAQControl::on_pushButtonInit_clicked()
{
    QString filename = ui->lineEditProjectFile->text();
    if (mWorkMode != PlaybackNetworkWorker::CollectMode && (filename.isEmpty() || !QFileInfo(filename).exists())) {
        QMessageBox::warning(this, QString::fromLocal8Bit("初始化回放文件"), QString::fromLocal8Bit("未正确选择回放文件(*.csv *.dat *.prj)"));
        return;
    }

    switch (mPlayDataType) {
    case PlayRawTarget:
    case PlayTrackTarget:
    {
        mPlaybackCanWorkerThread->quit();
        if (!mPlaybackCanWoker->parseFile(mPlayDataType == PlayRawTarget ? FrameRawTarget : FrameTrackTarget,
                                          filename))
        {
//            QMessageBox::warning(this, QString::fromLocal8Bit("初始化"), QString::fromLocal8Bit("解析回放文件失败!"));
            return;
        }

        // 回灌或采集（不是回放），且设备没有打开
        if (mWorkMode != PlaybackNetworkWorker::PlaybackMode && !mDeviceManager->isOpened())
        {
            emit run();
            if (!mDeviceManager->isOpened())
            {
                QMessageBox::warning(this, QString::fromLocal8Bit("回放"), QString::fromLocal8Bit("设备未成功打开"));
                return;
            }
        }

        // 回灌目标数据，需要雷达进入调式模式
        if (mWorkMode == PlaybackNetworkWorker::RechargeMode && !intoDebugMode()) {
            QMessageBox::warning(this, QString::fromLocal8Bit("初始化"), QString::fromLocal8Bit("雷达进入调试模式失败!"));
            return;
        }

        ui->pushButtonStartAndStop->setEnabled(false);
        mPlaybackCanWorkerThread->start();
    }
        break;
    case PlayADC:
    case Play1DFFT:
    case Play2DFFT:
    default:
    {
        QString profilesName = ui->lineEditProfilesConfig->text();
        if (profilesName.isEmpty())
        {
            QMessageBox::warning(this, QString::fromLocal8Bit("初始化"), QString::fromLocal8Bit("未正确选择Profiles(*.hxx)!"));
            return;
        }
        ui->pushButtonStartAndStop->setEnabled(false);
        // 回灌或采集（不是回放），且设备没有打开
        if (mWorkMode != PlaybackNetworkWorker::PlaybackMode && !mDeviceManager->isOpened())
        {
            emit run();
            if (!mDeviceManager->isOpened()) {
                QMessageBox::warning(this, QString::fromLocal8Bit("回放"), QString::fromLocal8Bit("设备未成功打开"));
                return;
            }
        }
        // 雷达Reboot
        mDeviceManager->sendFrame(mCanChannelIndex, 0x3FB, QByteArray::fromHex("4354435400000000"), true);

        mPlaybackNetworkWorker->parseProfileConfigs(profilesName.split(";"));
        mPlaybackNetworkWorker->init(mWorkMode,
                                  filename,
                                  ui->spinBoxOneFrameProfileCount->value());
    }
        break;
    }
}

void DAQControl::on_checkBoxSingleStep_clicked(bool checked)
{
    qDebug() << __FUNCTION__ << __LINE__ << checked;
    if (mPlaybackNetworkWorker->isPlaying())
    {

    }
    mPlaybackNetworkWorker->setSingleStep(checked);
    mPlaybackCanWoker->setSingleStep(checked);
}

void DAQControl::on_comboBoxModel_currentIndexChanged(int index)
{
    mWorkMode = (PlaybackNetworkWorker::WorkMode)index;
    ui->pushButtonStartStopServer->setEnabled(mWorkMode != PlaybackNetworkWorker::PlaybackMode);
    ui->pushButtonInit->setEnabled(mWorkMode == PlaybackNetworkWorker::PlaybackMode || mPlayDataType == PlayRawTarget || mPlayDataType == PlayTrackTarget);
}

void DAQControl::on_comboBoxCollectMode_currentIndexChanged(int index)
{
    mPlayDataType = (PlayDataType)index;

    ui->pushButtonInit->setEnabled(mWorkMode == PlaybackNetworkWorker::PlaybackMode || mPlayDataType == PlayRawTarget || mPlayDataType == PlayTrackTarget);
}

void DAQControl::on_pushButtonPrevFrame_clicked()
{
    if (mPlayDataType == PlayRawTarget || mPlayDataType == PlayTrackTarget)
    {
        emit playPrevPlayback();
    }
    else
    {

    }
}

void DAQControl::on_pushButtonNextFrame_clicked()
{
    if (mPlayDataType == PlayRawTarget || mPlayDataType == PlayTrackTarget)
    {
        emit playNextPlayback();
    }
    else
    {
        emit nextAlgorithmPlayFrame();
    }
}

bool DAQControl::intoDebugMode()
{
    bool ret = true;
    QString mode = "C000080000000000";
    if (mPlayDataType == PlayTrackTarget)
    {
        mode = "40000C0000000000";
    }
    for (quint8 i = 0x04; i < 8; ++i) {
        ret = ret && mDeviceManager->sendFrame(mCanChannelIndex, 0x300 | i, QByteArray::fromHex("3158AF8000010000"), true);
        ret = ret && mDeviceManager->sendFrame(mCanChannelIndex, 0x200 | i, QByteArray::fromHex(mode.toLocal8Bit()), true);
    }
    return ret;
}

bool DAQControl::exitDebugMode()
{
    bool ret = true;
    for (quint8 i = 0x04; i < 8; ++i) {
        ret = ret && mDeviceManager->sendFrame(mCanChannelIndex, 0x300 | i, QByteArray::fromHex("3158AF8000000000"), true);
        ret = ret && mDeviceManager->sendFrame(mCanChannelIndex, 0x200 | i, QByteArray::fromHex("0000040000000000"), true);
    }
    return ret;
}

void DAQControl::on_comboBoxCanIndex_currentIndexChanged(int index)
{
    mCanChannelIndex = index;
    mPlaybackCanWoker->setChannelIndex(mCanChannelIndex);
}

void DAQControl::on_pushButtonIntoDebugMode_clicked()
{
    intoDebugMode();
}

void DAQControl::on_pushButtonExitDebugMode_clicked()
{
    exitDebugMode();
}

void DAQControl::on_pushButtonCollectStart_clicked()
{
    QString com = "43544354FFFFFFFF";
    if (ui->lineEditCollectFrameCount->text().toUInt() > 0)
    {
        com = QString("43544354%1").arg(ui->lineEditCollectFrameCount->text().toUInt() * ui->spinBoxOneFrameProfileCount->value(), 8, 16, QLatin1Char('0'));
    }
    mDeviceManager->sendFrame(mCanChannelIndex, 0x3f9, QByteArray::fromHex(com.toLocal8Bit()), true);
}

void DAQControl::on_pushButtonCollectStop_clicked()
{
    mDeviceManager->sendFrame(mCanChannelIndex, 0x304, QByteArray::fromHex("4354435400000001"), true);
}

void DAQControl::on_pushButtonReboot_clicked()
{
    mDeviceManager->sendFrame(mCanChannelIndex, 0x3FB, QByteArray::fromHex("4354435400000000"), true);
}

void DAQControl::on_comboBoxAlgorithmRadarID_currentIndexChanged(const QString &arg1)
{
    mPlaybackCanWoker->setAlgorithmRadarID(arg1.toUInt());
}

void DAQControl::on_checkBoxAlgorithm_stateChanged(int arg1)
{
    mPlaybackCanWoker->setAlgorithm(arg1 != Qt::Unchecked);
}
