﻿#include "objectview.h"
#include "ui_objectview.h"


#include "analysis/analysisworker.h"
#include "utils/multiselectcombobox.h"

#include <QToolBar>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QComboBox>
#include <QStandardItemModel>
#include <QDebug>

namespace Views {
namespace ObjectView {

ObjectView::ObjectView(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::ObjectView)
{
    ui->setupUi(this);

    QHBoxLayout *layout = new QHBoxLayout(ui->toolWidget);
    layout->setContentsMargins(0, 0, 0, 0);
    QToolBar *toolBar = new QToolBar(this);

    toolBar->addAction(ui->actionShowConfig);
    toolBar->addAction(ui->actionClearObject);
    toolBar->addAction(ui->actionPause);
    toolBar->addAction(ui->actionClose);

    MultiSelectComboBox *comboBoxAnalysisTypes = new MultiSelectComboBox(this);
    comboBoxAnalysisTypes->setSizeAdjustPolicy(QComboBox::AdjustToMinimumContentsLength);
    comboBoxAnalysisTypes->setMinimumContentsLength(50);
    comboBoxAnalysisTypes->setMinimumWidth(50);
    for (int i = ID + 1; i < TARGET_TYPE_END; ++i) {
        comboBoxAnalysisTypes->addItem(gAnalysisTypeName((AnalysisType)i), i);
    }
    connect(comboBoxAnalysisTypes, &QComboBox::editTextChanged, this, [=](const QString &text){
        QStringList types = text.split(";", Qt::SkipEmptyParts);
        QList<AnalysisType> targetVeiwAnalysisTypes;
        foreach (const QString &t, types) {
            targetVeiwAnalysisTypes << gAnalysisType(t);
        }
        ui->coordinateSystem->setTargetVeiwAnalysisTypes(targetVeiwAnalysisTypes);
    });
    toolBar->addWidget(comboBoxAnalysisTypes);

    layout->addWidget(toolBar);

    connect(ui->actionClose, &QAction::triggered, this, [=](){
        if (QMessageBox::Yes == QMessageBox::question(this, QString::fromLocal8Bit("关闭目标视图"), QString::fromLocal8Bit("确定删除目标视图？"), QMessageBox::Yes | QMessageBox::No))
        {
            emit closeObjectView();
        }
    });
    connect(this, &ObjectView::draw, ui->coordinateSystem, &ObjectCoordinateSystem::draw);
}

ObjectView::~ObjectView()
{
    delete ui;
}

ObjectCoordinateSystem *ObjectView::coordinateSystem()
{
    return ui->coordinateSystem;
}

void ObjectView::on_actionShowConfig_triggered()
{
    ui->coordinateSystem->showConfig(0);
}

void ObjectView::on_actionClearObject_triggered()
{
    ui->coordinateSystem->clear();
}

void ObjectView::on_actionPause_triggered(bool checked)
{
    if (checked)
    {
        ui->actionPause->setText("Play");
    }
    else
    {
        ui->actionPause->setText("Pause");
    }
}

} // namespace ObjectView
} // namespace Views
