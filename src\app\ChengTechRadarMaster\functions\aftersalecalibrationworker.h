﻿#ifndef AFTERSALECALIBRATIONWORKER_H
#define AFTERSALECALIBRATIONWORKER_H

#include <QObject>
#include <QTimer>
//#include <QMutex>
#include <mutex>

#include "devices/canframe.h"

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Functions {

class UDS;

class AfterSaleCalibrationWorker : public QObject
{
    Q_OBJECT
public:
    enum ProtocolType {
        ProtocolBYD120,
        ProtocolMRR410,
        ProtocolBAIC,
        ProtocolGEELY,
        ProtocolGEELY180Pro,
        ProtocolGEELY180ProKX11,
        ProtocolGEELY180ProE245,
        ProtocolGWM,
        ProtocolHozon,
        ProtocolBAIC_BE12,
        ProtocolCount
    };

    explicit AfterSaleCalibrationWorker(Devices::Can::DeviceManager *deviceManager, QObject *parent = nullptr);

    void protocolChanged(int index);

    void stop(bool sda[]);
    void start(int channelIndex[], bool sda[], bool can);
    void readResult();
    void readResult(int index);

    void calibration(int index, bool stop = false);
    void result(int index);

public slots:
    void canFrame(const Devices::Can::CanFrame &frame);
    void frameTransmited(const Devices::Can::CanFrame &frame, bool success);

signals:
    void message(int index, const QString &msg);
    void calibrationStarted(int index);
    void calibrationFinished(int index);
    void sendOrRecvCanFrame( int index, bool bSend, quint64 id, const QString& data );

private:
    void result_GWM( int index );
    void result_Hozon( int index );
    void result_GELLY_180Pro(int index);
    void result_GELLY_LNKY(int index);

    bool seedToKeyByDLL( quint8 index, quint8 level, const QByteArray& seed, QByteArray& key );

//    typedef int  (*DLL_Seed2Key)(	const unsigned char*	iSeedArray,			/* Array for the seed [in] */
//                        const unsigned short	iSeedArraySize,		/* Length of the array for the seed [in] */
//                        const unsigned int		iSecurityLevel,		/* Security level [in] */
//                        const char*				iVariant, 			/* Name of the active variant [in] */
//                        unsigned char*			ioKeyArray, 		/* Array for the key [in, out] */
//                        unsigned int			iKeyArraySize,		/* Maximum length of the array for the key [in] */
//                        unsigned int*			oSize);

//private:
//    QMutex mDLLMutex;


private:
    UDS *mUDS[4];
    quint32 mResponseAddress[ProtocolCount][4]{{0x7DC, 0x7CA, 0x768, 0x769},
                                               {0x7AE, 0x7AF, 0x768, 0x769},
                                               {0x7C9, 0x7CC, 0x7C8, 0x7CA},
                                               {0x640, 0x641, 0xFFF, 0xFFF},
                                               {0x650, 0x651, 0xFFF, 0xFFF},
                                               {0x650, 0x651, 0x631, 0x630},
                                               {0x650, 0x651, 0x631, 0x630},
                                               {0x7AE, 0x7AF, 0x64E, 0x64F},
                                               {0x7D8, 0x7D9, 0x7D4, 0x7D7},
                                               {0x7C9, 0x7CC, 0x7C8, 0x7CA}}; // 4,5,6,7
    quint32 mFunctionAddress[ProtocolCount][4]{{0x7D4, 0x7C2, 0x760, 0x761},
                                               {0x7DF, 0x7DF, 0x760, 0x761},
                                               {0x749, 0x74C, 0x748, 0x74A},
                                               {0x740, 0x741, 0xFFF, 0xFFF},
                                               {0x750, 0x751, 0xFFF, 0xFFF},
                                               {0x750, 0x751, 0x731, 0x730},
                                               {0x750, 0x751, 0x731, 0x730},
                                               {0x760, 0x760, 0x760, 0x760},
                                               {0x7C8, 0x7C9, 0x7C4, 0x7C7},
                                               {0x749, 0x74C, 0x748, 0x74A}};
    quint32 mPhysicalAddress[ProtocolCount][4]{{0x7D4, 0x7C2, 0x760, 0x761},
                                               {0x76E, 0x76F, 0x760, 0x761},
                                               {0x749, 0x74C, 0x748, 0x74A},
                                               {0x740, 0x741, 0xFFF, 0xFFF},
                                               {0x750, 0x751, 0xFFF, 0xFFF},
                                               {0x750, 0x751, 0x731, 0x730},
                                               {0x750, 0x751, 0x731, 0x730},
                                               {0x76E, 0x76F, 0x60E, 0x60F},
                                               {0x7C8, 0x7C9, 0x7C4, 0x7C7},
                                               {0x749, 0x74C, 0x748, 0x74A}};
    uint32_t mMASK[ProtocolCount][4]{{0x156, 0x156, 0x1A1, 0x1A2},
                                     {0x156, 0x156, 0x1A1, 0x1A2},
                                     {0x156, 0x156, 0x1A1, 0x1A2},
                                     {0x156, 0x156, 0x1A1, 0x1A2},
                                     {0x156, 0x156, 0x1A1, 0x1A2},
                                     {0x156, 0x156, 0x1A1, 0x1A2},
                                     {0x156, 0x156, 0x1A1, 0x1A2},
                                     {0x156, 0x156, 0x1A1, 0x1A2},
                                     {0x156, 0x156, 0x1A1, 0x1A2},
                                     {0x156, 0x156, 0x1A1, 0x1A2}};
    ProtocolType mProtocolIndex{ProtocolBYD120};

//    QMutex mMutex[4];
    std::mutex mMutex[4];
};

} // namespace Functions

#endif // AFTERSALECALIBRATIONWORKER_H
