﻿#ifndef STATICCALIBRATIONWORKER_H
#define STATICCALIBRATIONWORKER_H

#include <QObject>
#include <QTimer>
#include <QMutex>

#include "devices/canframe.h"

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Functions {

class UDS;

class StaticCalibrationWorker : public QObject
{
    Q_OBJECT
public:
    enum ProtocolType {
        ProtocolBYD120,
        ProtocolMRR410,
        ProtocolBAIC,
        ProtocolGWM,
        ProtocolHozon,
        ProtocolBAIC_BE12,
        ProtocolGEELY_E245_J6M,
        ProtocolCount
    };
    explicit StaticCalibrationWorker(Devices::Can::DeviceManager *deviceManager, QObject *parent = nullptr);

    void protocolChanged(int index);
    void setBAICTargetPosition(quint16 position[4][2]);

    void stop();
    void start(int channelIndex[], bool sda[]);
    void readResult();
    void readResult(int index);

    void calibration(int index, bool stop = false);
    void result(int index);

public slots:
    void canFrame(const Devices::Can::CanFrame &frame);
    void frameTransmited(const Devices::Can::CanFrame &frame, bool success);


signals:
    void message(int index, const QString &msg);
    void calibrationStarted(int index);
    void calibrationFinished(int index, quint16 ok = false);
    void sendOrRecvCanFrame( int index, bool bSend, quint64 id, const QString& data );

private:
    void result_GWM( int index );
    void result_Hozon( int index );
    void result_GEELY_E245_J6M( int index );
    void initFirstReset();

    bool seedToKeyByDLL( quint8 index, quint8 level, const QByteArray& seed, QByteArray& key );

private:
    UDS *mUDS[4];
    quint32 mResponseAddress[ProtocolCount][4]{
        // 4,5,6,7
        {0x7DC, 0x7CA, 0x768, 0x769},
        {0x7AE, 0x7AF, 0x768, 0x769},
        {0x7C9, 0x7CC, 0x7C8, 0x7CA},
        {0x7AE, 0x7AF, 0x64E, 0x64F},
        {0x7D8, 0x7D9, 0x7D4, 0x7D7},
        {0x7C9, 0x7CC, 0x7C8, 0x7CA},
        {0x650, 0x651, 0xFFF, 0xFFF},
    };
    quint32 mFunctionAddress[ProtocolCount][4]{
        {0x7D4, 0x7C2, 0x760, 0x761},
        {0x7DF, 0x7DF, 0x760, 0x761},
        {0x749, 0x74C, 0x748, 0x74A},
        {0x760, 0x760, 0x760, 0x760},
        {0x7C8, 0x7C9, 0x7C4, 0x7C7},
        {0x749, 0x74C, 0x748, 0x74A},
        {0x750, 0x751, 0xFFF, 0xFFF},
    };
    quint32 mPhysicalAddress[ProtocolCount][4]{
        {0x7D4, 0x7C2, 0x760, 0x761},
        {0x76E, 0x76F, 0x760, 0x761},
        {0x749, 0x74C, 0x748, 0x74A},
        {0x76E, 0x76F, 0x60E, 0x60F},
        {0x7C8, 0x7C9, 0x7C4, 0x7C7},
        {0x749, 0x74C, 0x748, 0x74A},
        {0x750, 0x751, 0xFFF, 0xFFF},
    };
    uint32_t mMASK[ProtocolCount][4]{
        {0x156, 0x156, 0x1A1, 0x1A2},
        {0x156, 0x156, 0x1A1, 0x1A2},
        {0x156, 0x156, 0x1A1, 0x1A2},
        {0x156, 0x156, 0x1A1, 0x1A2},
        {0x156, 0x156, 0x1A1, 0x1A2},
        {0x156, 0x156, 0x1A1, 0x1A2},
        {0x156, 0x156, 0x1A1, 0x1A2},
    };
    ProtocolType mProtocolIndex{ProtocolBYD120};
    quint16 mBAICTargetPosition[4][2];
    bool mFirstReset[4];

    QMutex mMutex[4];
};

} // namespace Functions

#endif // STATICCALIBRATIONWORKER_H
