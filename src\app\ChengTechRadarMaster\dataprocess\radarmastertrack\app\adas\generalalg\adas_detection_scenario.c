﻿/**
 * @file adas_sideLine.c
 * <AUTHOR> name (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2022-10-17
 * 
 * @copyright Copyright (c) 2022
 * 
 */

#ifdef ALPSPRO_ADAS
#include <string.h>
#include <math.h>
#include "cfg.h"
#include "rdp/track/data_process/rdp_interface.h"
#include "vdy/vdy_interface.h"
#include "adas/common/linear_regression.h"
#include "adas/customizedrequirements/adas_signal_integration.h"
#include "adas/customizedrequirements/adas_vehicle_ctrls.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#include "embARC_debug.h"
#include "rdp/track/alignment/aln_install_cfg.h"
#include "dbg/app_dbg.h"
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "rdp/track/data_process/rdp_matrixMath.h"
#elif defined(PC_DBG_FW)
#include <windows.h>
#include <string.h>
#include <math.h>
#include "other/temp.h"
#include "alg/track/rdp_interface.h"
#include "alg/track/rdp_clth_radar_lib.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/customizedrequirements/adas_vehicle_ctrls.h"
#include "app/adas/customizedrequirements/adas_signal_integration.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/vehicle/vdy/vdy_interface.h"
#include "app/vehicle/vdy/vdy_types.h"
#else
#include <string.h>
#include <math.h>
#include "app/cfg/cfg.h"
#include "app/adas/common/linear_regression.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas_vehicle_ctrls.h"
#include "app/rdp/rdp_interface.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/vdy/vdy_interface.h"
#include "common/include/vehicle_cfg.h"
#include "app/rdp/rdp_clth_radar_lib.h"
#endif

static uint8_t gCPTARF_Scene = 0;    // 本地的全局变量，为了给外部调用返回结果
//static uint8_t gCCCscp8_9_Scene = 0; // 本地的全局变量，为了给外部调用返回结果

/**
 * @brief 检测快速方向盘转动
 * @param pRDP_TrkObjectList RDP跟踪列表地址
 * @param pfreezedVehDyncData VDY车身动态数据地址
 * @param pobjPath 道路结构体地址
 */
void ADAS_detectTurnRadiusRapidChange(ALARM_OBJECT_T *pobjAlm,
                                      OBJ_NODE_STRUCT *pobjPath,
                                      const VDY_Info_t *pVDY,
                                      const ADAS_TimeClase_t timeClase_t)
{
    uint8_t i = 0 /*, j = 0 */;
    uint8_t turningRadiusTrend = 0; // 0，没有行车转弯趋势（相对），1，正在转弯，2，正在回正；
    uint8_t turningRadiusIncreasing = 0, turningRadiusDecreasing = 0;
    /*!> 时钟部分 */
    uint32_t nowTick = 0;
    static uint32_t sysTick_1 = 0, sysTick_2 = 0; // 类似于时钟节拍的东西
    nowTick = timeClase_t.adasTimeCnt;              // +1=50ms

    if ((pobjAlm->l_r == BSD_RADAR_RIGHT) && (pVDY->pVDY_DynamicInfo->vdyCurveRadius > 1000.0f))
    {
        sysTick_1 = nowTick;

        // 如果转弯半径有突变过，且时间大于6s,清除标志
        if ((pobjAlm->adasRegulationScene.turnRadiusRapidChange_Scene == 1) && (nowTick > (sysTick_2 + 120U))) // 50*120=6000ms
        {
            sysTick_2 = nowTick;
            pobjAlm->adasRegulationScene.turnRadiusRapidChange_Scene = 0;
        }
    }
    else if ((pobjAlm->l_r == BSD_RADAR_RIGHT) && (pVDY->pVDY_DynamicInfo->vdyCurveRadius >= 0.1f))
    {
        for (i = 0; i < (ADAS_HISTORY_NUM - 1); i++)
        {
            // 表示正在回正
            if (pobjAlm->adasRegulationScene.stored_last_TurnRadiusRight[i] > (pobjAlm->adasRegulationScene.stored_last_TurnRadiusRight[i + 1] + 0.5f))
            {
                turningRadiusIncreasing++;
            }
            // 表示转弯半径越来越小
            else if ((pobjAlm->adasRegulationScene.stored_last_TurnRadiusRight[i] + 0.5f) < pobjAlm->adasRegulationScene.stored_last_TurnRadiusRight[i + 1])
            {
                turningRadiusDecreasing++;
            }
            else
            {
            }
        }
        if ((turningRadiusIncreasing + 3) < turningRadiusDecreasing)
        {
            turningRadiusTrend = 1;
        }
        else if (turningRadiusIncreasing > (turningRadiusDecreasing + 3))
        {
            turningRadiusTrend = 2;
        }
        else
        {
            turningRadiusTrend = 0;
        }

        // 转弯半径在3s内从1000降到100，认为在
        if ((nowTick < (sysTick_1 + 60U)) && (nowTick > (sysTick_1 + 5U)) && // 0.25s~3s 50*60=3000ms
            (pVDY->pVDY_DynamicInfo->vdyCurveRadius < 100.0f) &&                //
            (pobjAlm->adasRegulationScene.avgTurnRadiusRight < ((1000 - 100) / 2)) &&            // 小于2分之一，上一帧即之前的均值
            (turningRadiusTrend == 1))
        {
            sysTick_1 = nowTick;
            sysTick_2 = sysTick_1;
            pobjAlm->adasRegulationScene.turnRadiusRapidChange_Scene = 1;
        }

        // 如果转弯半径有突变过，且时间大于8s,清除标志
        if ((pobjAlm->adasRegulationScene.turnRadiusRapidChange_Scene == 1) && (nowTick > (sysTick_2 + 160U))) // 50*160=8000ms
        {
            sysTick_2 = nowTick;
            pobjAlm->adasRegulationScene.turnRadiusRapidChange_Scene = 0;
        }
    }

    (void)memmove(&pobjAlm->adasRegulationScene.stored_last_TurnRadiusRight[1], &pobjAlm->adasRegulationScene.stored_last_TurnRadiusRight[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
    if (pVDY->pVDY_DynamicInfo->vdyCurveRadius >= FLOAT_EPS)
    {
        pobjAlm->adasRegulationScene.stored_last_TurnRadiusRight[0] = pVDY->pVDY_DynamicInfo->vdyCurveRadius;
    }
    else
    {
        pobjAlm->adasRegulationScene.stored_last_TurnRadiusRight[0] = 32767.0f;
    }
    pobjAlm->adasRegulationScene.avgTurnRadiusRight = ADAS_cacAvg(pobjAlm->adasRegulationScene.stored_last_TurnRadiusRight, ADAS_HISTORY_HEADINGANGLE_NUM);

    (void)memmove(&pobjAlm->adasRegulationScene.stored_last_TurnRadiusLeft[1], &pobjAlm->adasRegulationScene.stored_last_TurnRadiusLeft[0], sizeof(float) * (ADAS_HISTORY_NUM - 1U));
    if (pVDY->pVDY_DynamicInfo->vdyCurveRadius <= FLOAT_EPS)
    {
        pobjAlm->adasRegulationScene.stored_last_TurnRadiusLeft[0] = pVDY->pVDY_DynamicInfo->vdyCurveRadius;
    }
    else
    {
        pobjAlm->adasRegulationScene.stored_last_TurnRadiusLeft[0] = -32767.0f;
    }
    pobjAlm->adasRegulationScene.avgTurnRadiusLeft = ADAS_cacAvg(pobjAlm->adasRegulationScene.stored_last_TurnRadiusLeft, ADAS_HISTORY_HEADINGANGLE_NUM);
}

/**
 * @brief ENCAP 2023 CPTA RF 场景识别
 * @param pobjAlm 报警结构体地址 报警结构体地址
 * @param pobjPath 道路结构体地址
 * @return uint8_t
 */
static void ADAS_FCTAB_detectCPTARF_RightTurnWithPedestrian(ALARM_OBJECT_T *pobjAlm,
                                                            OBJ_NODE_STRUCT *pobjPath,
                                                            const VDY_Info_t *pVDY,
                                                            float threshold,
                                                            const ADAS_TimeClase_t timeClass)
{
    if (pobjAlm == NULL || pobjPath == NULL || pVDY == NULL)
    {
        // 处理错误情况，例如返回 false 或打印错误信息
        return;
    }

    uint8_t i = 0 /*, j = 0*/;
    uint16_t closeRangeMovedObjNum = 0;
    //float maxAbsolutevabs = 0.0f;    // 目标的对地速度
    //float maxObjVxByCarSpeed = 0.0f; // 目标的对地速度
    //float maxObjVyByCarSpeed = 0.0f; //
    /*!> 时钟部分 */
    uint32_t nowTick = 0;
    static uint32_t sysTick_1 = 0 /*, sysTick_2 = 0*/; // 类似于时钟节拍的东西
    uint32_t nowTickSpeed = 0;
    static uint32_t sysTickSpeed_1 = 0 /*, sysTickSpeed_2 = 0*/; // 类似于时钟节拍的东西
    nowTick = nowTickSpeed = timeClass.adasTimeCnt;         // +1=50ms

    // FCTB 在激活且起步阶段
    if ((pobjAlm->l_r == BSD_RADAR_RIGHT) && (pVDY->pVDY_DynamicInfo->vdyCurveRadius > 1000.0f) &&
        (pVDY->pVDY_DynamicInfo->vdySpeedInmps < 0.1f))
    {
        sysTickSpeed_1 = nowTickSpeed;
    }
    else if ((pobjAlm->l_r == BSD_RADAR_RIGHT) && (pVDY->pVDY_DynamicInfo->vdyCurveRadius > 1000.0f) &&
             ((pVDY->pVDY_DynamicInfo->vdySpeedInmps >= 0.1f) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps < 1.5f)) &&
             (nowTickSpeed < (sysTickSpeed_1 + 30U))) // 50*30=1500ms
    {
        // nothing
    }
    else if ((pobjAlm->l_r == BSD_RADAR_RIGHT) && (pVDY->pVDY_DynamicInfo->vdyCurveRadius > 1000.0f) &&
             ((pVDY->pVDY_DynamicInfo->vdySpeedInmps > 1.5f) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps < 3.5f)) &&
             ((nowTickSpeed >= (sysTickSpeed_1 + 30U)) && (nowTickSpeed < (sysTickSpeed_1 + 90U)))) // 1.5~4.5s 50*90=4500ms
    {
        // 1.5~4.5s内的速度均值
        pobjAlm->avgSpeedVal_FCTB_Xs = ADAS_cacAvg(pobjAlm->stored_last_SpeedVal, ADAS_HISTORY_HEADINGANGLE_NUM);
    }
    else if ((nowTickSpeed > (sysTickSpeed_1 + 90U))) // 4.5s 50*90=4500ms
    {
        sysTickSpeed_1 = nowTickSpeed;
    }
    else
    {
    }

    for (i = 0; i < ADAS_TRK_OBJNUM; i++)
    {
        if (((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U) &&
            ((pobjPath[i].x < 30.0f) && (pobjPath[i].x > -5.0f)) &&
            ((pobjPath[i].y < 30.0f) && (pobjPath[i].y > -5.0f)))
        {
            closeRangeMovedObjNum++;
        }

        // 找到动态目标中的最大速度
        if (((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U) &&
            ((pobjPath[i].x < 15.0f) && (pobjPath[i].x > -3.0f)) &&
            ((pobjPath[i].y < 15.0f) && (pobjPath[i].y > -3.0f)))
        {
            // if (pobjPath[i].absolutevabs > maxAbsolutevabs)
            // {
            //     maxAbsolutevabs = pobjPath[i].absolutevabs;
            // }
        }
    }

#if 0
    closeRangeMovedObjNum = 1;
    maxAbsolutevabs = 1.0f;
#endif

    if ((pobjAlm->l_r == BSD_RADAR_RIGHT) &&
        (pVDY->pVDY_DynamicInfo->vdyGearState == GEAR_SIG_D) &&
        ((pVDY->pVDY_DynamicInfo->vdySpeedInmps >= 1.8f) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps <= 3.5f)) &&
        ((pobjAlm->movedobjnum <= 3) || (closeRangeMovedObjNum <= 2)) &&
        (pobjAlm->adasRegulationScene.turnRadiusRapidChange_Scene == 1) &&
        ((pobjAlm->centerx < 300.0f) && (pobjAlm->centerx >= 0.1f)) &&
        ((pobjAlm->avgSpeedVal_FCTB_Xs >= 2.2f) && (pobjAlm->avgSpeedVal_FCTB_Xs <= 2.8f)))
    {
        sysTick_1 = nowTick;
        pobjAlm->adasRegulationScene.CPTARF_Scene = 1; // 检测为CPTA场景
        if (pobjAlm->adasRegulationScene.CPTARF_ScenarioCnt < 10)
        {
            pobjAlm->adasRegulationScene.CPTARF_ScenarioCnt++;
        }
    }
    // 50*240=12000ms=12s
    else if ((nowTick < (sysTick_1 + 240U)) && (pobjAlm->adasRegulationScene.CPTARF_Scene == 1) && (pobjAlm->adasRegulationScene.CPTARF_ScenarioCnt >= 1))
    {
        // sysTick_1 = nowTick;
        pobjAlm->adasRegulationScene.CPTARF_ScenarioCnt--;
    }
    else
    {
        pobjAlm->adasRegulationScene.CPTARF_Scene = 0; // 检测为CPTA场景
        pobjAlm->adasRegulationScene.CPTARF_ScenarioCnt = 0;
    }
    gCPTARF_Scene = pobjAlm->adasRegulationScene.CPTARF_Scene;
}

/**
 * @brief ENCAP 2023 CCCscp 场景识别
 * @param pobjAlm 报警结构体地址 报警结构体地址
 * @param pobjPath 道路结构体地址
 * @param GVT 的全称是 Global Vehicle Target，也可以理解为“全球车辆目标”或“通用车辆目标”
 * @return uint8_t
 */
static void ADAS_FCTAB_detectCCCscp_LeftWithGVT(ALARM_OBJECT_T *pobjAlm,
                                                OBJ_NODE_STRUCT *pobjPath,
                                                const VDY_Info_t *pVDY,
                                                float threshold,
                                                const ADAS_TimeClase_t timeClass)
{
    if (pobjAlm == NULL || pobjPath == NULL || pVDY == NULL)
    {
        // 处理错误情况，例如返回 false 或打印错误信息
        return;
    }

    uint8_t i = 0 /*, j = 0*/;
    uint16_t closeRangeMovedObjNum = 0;
    float maxAbsolutevabs = 0.0f;    // 目标的对地速度
    //float maxObjVxByCarSpeed = 0.0f; // 目标的对地速度
    //float maxObjVyByCarSpeed = 0.0f; //
    /*!> 时钟部分 */
    uint32_t nowTick = 0;
    static uint32_t sysTick_1 = 0 /*, sysTick_2 = 0*/; // 类似于时钟节拍的东西
    nowTick = timeClass.adasTimeCnt;              // +1=50ms

    for (i = 0; i < ADAS_TRK_OBJNUM; i++)
    {
        if (((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U) &&
            ((pobjPath[i].x < 30.0f) && (pobjPath[i].x > -5.0f)) &&
            ((pobjPath[i].y < 30.0f) && (pobjPath[i].y > -5.0f)))
        {
            closeRangeMovedObjNum++;
        }

        // 找到动态目标中的最大速度
        if (((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U) &&
            ((pobjPath[i].x < 25.0f) && (pobjPath[i].x > -3.0f)) &&
            ((pobjPath[i].y < 15.0f) && (pobjPath[i].y > -3.0f)))
        {
            if (pobjPath[i].absolutevabs > maxAbsolutevabs)
            {
                maxAbsolutevabs = pobjPath[i].absolutevabs;
            }
        }
    }

#if 0
    closeRangeMovedObjNum = 1;
    maxAbsolutevabs = 1.0f;
#endif

    if ((pobjAlm->l_r == BSD_RADAR_LEFT) &&
        (pVDY->pVDY_DynamicInfo->vdyGearState == GEAR_SIG_D) &&
        ((pVDY->pVDY_DynamicInfo->vdySpeedInmps >= 0.1f) && (pVDY->pVDY_DynamicInfo->vdySpeedInmps <= 3.5f)) &&
        ((pobjAlm->movedobjnum <= 3) || (closeRangeMovedObjNum <= 2)) &&
        ((pobjAlm->centerx >= 300.0f) || (pobjAlm->centerx <= -300.0f)) &&
        (maxAbsolutevabs > 4.5f))
    {
        sysTick_1 = nowTick;
        pobjAlm->adasRegulationScene.CCCscp8_9_Scene = 1; // 检测为CCCscp 8-9场景
        if (pobjAlm->adasRegulationScene.CCCscp8_9_ScenarioCnt < 10)
        {
            pobjAlm->adasRegulationScene.CCCscp8_9_ScenarioCnt++;
        }
    }
    // 50*240=12000ms=12s
    else if ((nowTick < (sysTick_1 + 240U)) && (pobjAlm->adasRegulationScene.CCCscp8_9_Scene == 1) && (pobjAlm->adasRegulationScene.CCCscp8_9_ScenarioCnt >= 1))
    {
        // sysTick_1 = nowTick;
        pobjAlm->adasRegulationScene.CCCscp8_9_ScenarioCnt--;
    }
    else
    {
        pobjAlm->adasRegulationScene.CCCscp8_9_Scene = 0; // 检测为CCCscp 8-9场景
        pobjAlm->adasRegulationScene.CCCscp8_9_ScenarioCnt = 0;
    }
    //gCCCscp8_9_Scene = pobjAlm->adasRegulationScene.CCCscp8_9_Scene;
}

/**
 * @brief 场景识别结果
 * @param pobjAlm 报警结构体地址 报警结构体地址
 * @param pobjPath 道路结构体地址
 * @return uint8_t ALN_getAutoEolRunResult
 */
uint8_t ADAS_FCTAB_getDetectCPTARF_RightTurnWithPedestrian(void)
{
    return gCPTARF_Scene;
}

/**
 * @brief FCTAB场景检测模块主函数
 *
 * @param pfreezedVehDyncData VDY车身动态数据地址
 */
void ADAS_FCTAB_runDetectionScene(ALARM_OBJECT_T *pobjAlm,
                                  OBJ_NODE_STRUCT *pobjPath,
                                  const VDY_Info_t *pVDY,
                                  const ADAS_TimeClase_t timeClase_t)
{
    // 不能放在for循环里面，且需要让CPTAScenarioCnt能被执行到
    ADAS_FCTAB_detectCPTARF_RightTurnWithPedestrian(pobjAlm, pobjPath, pVDY, 100, timeClase_t);
    ADAS_FCTAB_detectCCCscp_LeftWithGVT(pobjAlm, pobjPath, pVDY, 100, timeClase_t);
}

/**
 * @brief FCTAB场景时间检测，一定时间内条件放宽，之后把条件收紧
 *
 * @param pfreezedVehDyncData VDY车身动态数据地址
 */
void ADAS_FCTAB_StrictDetectXs(ALARM_OBJECT_T *pobjAlm,
                               OBJ_NODE_STRUCT *pobjPath,
                               const VDY_Info_t *pVDY,
                               const ADAS_FunctionState_t adasFuncState,
                               const ADAS_TimeClase_t timeClase_t)
{
    /*!> 时钟部分 */ 
    static uint32_t sysTick_1 = 0U; //
    uint16_t closeRangeMovedObjNum = 0; 
    uint8_t i = 0;

    for (i = 0; i < ADAS_TRK_OBJNUM; i++)
    {
        if (((pobjPath[i].attrFlag & (uint16_t)TRACK_STATUS_MOVING_BMP) != 0U) &&
            ((pobjPath[i].x < 20.0f) && (pobjPath[i].x > -2.0f)) &&
            ((pobjPath[i].y < 10.0f) && (pobjPath[i].y > -3.0f)))
        {
            closeRangeMovedObjNum++;
        }
    }
    // 动态目标、20m内的动态目标、时间限制
    if ((pobjAlm->runingtime <= FCTB_CHECK_L_STRICT_RUNNING_TIME) &&
        ((pobjAlm->movedobjnum <= FCTB_CHECK_MOVED_NUM) ||
         (closeRangeMovedObjNum <= 3U))) 
    {
        sysTick_1++;
        pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene = 1U; // 时间到之后收紧参数标志pobjAlm->runingtime
    }
    else
    {
        sysTick_1 = 0U;
        pobjAlm->adasRegulationScene.fctab_LooseDetectXs_Scene = 0U;
    }
}
