﻿#include "truesystemprotocol.h"

#include "utils/utils.h"
#include "analysis/truesystemdata.h"

#include <QtMath>

namespace Analysis {
namespace Protocol {

TrueSystemProtocol::TrueSystemProtocol(AnalysisWorker *analysisWorker, QObject *parent)
    : IAnalysisProtocol(analysisWorker, parent)
{
    mOriginSettingsINS = OriginSettings{3.472542f, 3.472542f, 0.896058f, 0.896058f, 0.85927f, 0.85927f, 0.67762f, 0.67762f};
//    mOriginSettingsRDM = OriginSettings{4.85f, 4.85f, 0.0f, 0.0f, 0.975f, 0.975f, 0.975f, 0.975f};
    mOriginSettingsRDM = OriginSettings{4.455f, 4.455f, 0.0f, 0.0f, 0.9375f, 0.9375f, 0.9375f, 0.9375f};
}

bool TrueSystemProtocol::analysisFrame(const Devices::Can::CanFrame &frame)
{
    return parseFrame_IFS300_1_4_1(frame);
}

bool TrueSystemProtocol::parseFrame_IFS300_1_4_1_INS_0x60N(const Devices::Can::CanFrame &frame)
{
    stTrueObjInfo &trueObjectInfo = mAnalysisWorker->mTrueObjectInfo;
    const uint8_t *data = (const uint8_t *)frame.data().data();
    switch (frame.id()) {
    case 0x600: // INS时间戳
    {
        if (frame.length() != 6) {
//            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }

        INS_TimeStamp_t *p = (INS_TimeStamp_t*)data;
        trueObjectInfo.mInsYear = p->InsYear;
        trueObjectInfo.mInsMonth = p->InsMonth;
        trueObjectInfo.mInsDay = p->InsDay;
        trueObjectInfo.mInsHour = p->InsHour;
        trueObjectInfo.mInsMinute = p->InsMinute;
        trueObjectInfo.mInsSecond = p->InsSecond;
        trueObjectInfo.mInsMilliSecond = p->InsMilliSecond;
    }
        break;
    case 0x601: // INS定位经纬度
        if (frame.length() != 8) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        break;
    case 0x602:
    {
        if (frame.length() != 8) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        INS_AltStatus_t *p = (INS_AltStatus_t *)data;
        trueObjectInfo.mInsPosAlt = (p->PosAlt > 0x7FFFFF ? (int32_t)(p->PosAlt | 0xFF000000) : (int32_t)p->PosAlt) * 0.001f;
        trueObjectInfo.mInsPosEllipsoid = (p->PosEllipsoid > 0x7FFFFF ? (int32_t)(p->PosEllipsoid | 0xFF000000) : (int32_t)p->PosEllipsoid) * 0.001f;
        trueObjectInfo.mInsPosMode = p->PosMode;
        trueObjectInfo.mInsSolState = p->InsSolState;
        trueObjectInfo.mInsStasNum = p->InsStasNum;
    }
        break;
    case 0x603: // 地球坐标系(北东地)速度
    case 0x604: // 车辆坐标系加速度
    case 0x605: // 车辆坐标系角速度
        if (frame.length() != 8) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        break;
    case 0x606: // 姿态角
    {
        if (frame.length() != 6) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        INS_Attitude_t *p = (INS_Attitude_t *)data;
        trueObjectInfo.mInsAngleHeading = p->AngleHeading * 0.01f;
        trueObjectInfo.mInsAnglePitch = ((int16_t)p->AnglePitch) * 0.01f;
        trueObjectInfo.mInsAngleRoll = ((int16_t)p->AngleRoll) * 0.01f;
    }
        break;
    case 0x607: // 位置标准差
    case 0x608: // 速度标准差
        if (frame.length() != 8) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        break;
    case 0x609: // 标准差(姿态角)
        if (frame.length() != 6) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        break;
    case 0x60A: // 无定义
    case 0x60B: // INS航向航迹
        if (frame.length() != 8) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        break;
    case 0x60C: // 车辆坐标系下速度
    {
        if (frame.length() != 6) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }

        INS_VelocityVce_t *p = (INS_VelocityVce_t*)data;
        trueObjectInfo.mVelocityVceX = ((int16_t)p->VelocityVceX) * 0.01f;
        trueObjectInfo.mVelocityVceY = ((int16_t)p->VelocityVceY) * 0.01f;
        trueObjectInfo.mVelocityVceZ = ((int16_t)p->VelocityVceZ) * 0.01f;
        break;
    }
    case 0x60D: // 大地坐标系角加速度
    case 0x60E: // 车辆坐标系角加速度
    case 0x60F: // INS位置纬度
    case 0x610: // INS位置经度
    case 0x611: // 侧偏角
        if (frame.length() != 8) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        break;
    case 0x6D0: // 诊断报文
    {
        if (frame.length() != 8) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        calculatingTheBody();
        mAnalysisWorker->analysisTrueSystemEnd();
        break;
    }
        break;
    case 0x6D1: // 学习与监控状态
    {
        if (frame.length() != 8) {
            return false;
        }

        LrnMon_t *p = (LrnMon_t*)data;

        trueObjectInfo.mDualAntOfsLrn = ((int8_t)p->DualAntOfsLrn) * 0.1f;
        trueObjectInfo.mDualAntOfsLrnProgress = p->DualAntOfsLrnProgress;
        trueObjectInfo.mWsErr = ((int8_t)p->WsErr) * 0.0001f;
        trueObjectInfo.mWsErrCalcProgress = p->WsErrCalcProgress;
        trueObjectInfo.mImuExtrinPitchAng = ((int8_t)p->ImuExtrinPitchAng) * 0.1f;
        trueObjectInfo.mImuExtrinPitchProgress = p->ImuExtrinPitchProgress;
        trueObjectInfo.mImuExtrinYawAng = ((int8_t)p->ImuExtrinYawAng) * 0.1f;
        trueObjectInfo.mImuExtrinYawProgress = p->ImuExtrinYawProgress;
    }
        break;
    case 0x6D2:
        break;
    case 0x6D3: // DAISCH算法
        break;
    case 0x6D4: // DAISCH算法
        break;
    case 0x6D5: // DAISCH算法
        break;
    case 0x6D6: // IMU原始角速度静止自学习零偏值
        break;
    case 0x6D7: // DAISCH算法
        break;
    case 0x6D8: // DAISCH算法
        break;
    case 0x6DA: // 刹车检测
        break;
    default:
        return false;
    }

    return true;
}

bool TrueSystemProtocol::parseFrame_IFS300_1_4_1_RDM_0x4AN(const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8) {
//        qDebug() << __FUNCTION__ << __LINE__ << frame.channelIndex() << frame.idHex() << frame.dataHex();
        return false;
    }
    stTrueObjInfo &trueObjectInfo = mAnalysisWorker->mTrueObjectInfo;
    const uint8_t *data = (const uint8_t *)frame.data().data();
    switch (frame.id()) {
    case 0x4A0: // 从车状态
    {
        RDM_DistStatus_t *p = (RDM_DistStatus_t *)data;
        trueObjectInfo.mRDMStatus = p->RDMStatus;
        trueObjectInfo.mRDMAzumith = p->RDMAzumith * 0.01f;
        trueObjectInfo.mRDMDistance = p->RDMDistance * 0.01f;
        trueObjectInfo.mRDMDelayMilliSecond = p->RDMDelayMilliSecond;
    }
        break;
    case 0x4A1: // 从车位置（主车坐标系下）
    {
        RDM_Pos_t *p = (RDM_Pos_t *)data;

        trueObjectInfo.mRDMPos[stTrueObjInfo::Origin].mRDMPosX = (p->RDMPosX > 0x7FFFFF ? (int32_t)(p->RDMPosX | 0xFF000000) : (int32_t)p->RDMPosX) * 0.01f;
        trueObjectInfo.mRDMPos[stTrueObjInfo::Origin].mRDMPosY = (p->RDMPosY > 0x7FFFFF ? (int32_t)(p->RDMPosY | 0xFF000000) : (int32_t)p->RDMPosY) * 0.01f;
        trueObjectInfo.mRDMPos[stTrueObjInfo::Origin].mRDMPosZ = ((int16_t)p->RDMPosZ) * 0.01f;

        //发送位置显示
        trueObjectInfo.validFlag = true;
//        qDebug() << __FUNCTION__ << __LINE__ << trueObjectInfo.validFlag;

        QDateTime time = QDateTime::currentDateTime();
        qint64 time0 = time.toMSecsSinceEpoch();
        trueObjectInfo.TimeUnix = time0;
        trueObjectInfo.TimeStamp = time0;
    }
        break;
    case 0x4A2: // 从车速度（主车坐标系下）
    {
        RDM_Vel_t *p = (RDM_Vel_t *)data;
        trueObjectInfo.mRDMVelX = ((int16_t)p->RDMVelX) * 0.01f;
        trueObjectInfo.mRDMVelY = ((int16_t)p->RDMVelY) * 0.01f;
        trueObjectInfo.mRDMVelZ = ((int16_t)p->RDMVelZ) * 0.01f;
    }
        break;
    case 0x4A3: // 从车纵向加速度（主车坐标系下）：m/s/s
    {
        RDM_Acc_t *p = (RDM_Acc_t *)data;
        trueObjectInfo.mRDMAccX = ((int16_t)p->RDMAccX) * 0.001f;
        trueObjectInfo.mRDMAccY = ((int16_t)p->RDMAccY) * 0.001f;
        trueObjectInfo.mRDMAccZ = ((int16_t)p->RDMAccZ) * 0.001f;
    }
        break;
    case 0x4A4: // AEB检测
    {
        RDM1_AEB_t *p = (RDM1_AEB_t*)data;

        trueObjectInfo.mRDM_THW = p->RDM_THW * 0.01f;
        trueObjectInfo.mRDM_TTC = p->RDM_TTC * 0.01f;
        trueObjectInfo.mRDM_ETTC = p->RDM_ETTC * 0.01f;
    }
        break;
    default:
        return false;
    }

    return true;
}

bool TrueSystemProtocol::parseFrame_IFS300_1_4_1_Target_0x7AN(const Devices::Can::CanFrame &frame)
{
    stTrueObjInfo &trueObjectInfo = mAnalysisWorker->mTrueObjectInfo;
    const uint8_t *ptr = (const uint8_t *)frame.data().data();
    switch (frame.id()) {
    case 0x7A1: // 从车经纬度
    case 0x7A4: // 从车加速度
        if (frame.length() != 8) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        break;
    case 0x7A5: // 从车角速度
    {
        if (frame.length() != 8) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        Target_AngRateVce_t *p = (Target_AngRateVce_t *)ptr;
        trueObjectInfo.mTgtAngRateVceX = (p->AngRateVceX > 0x7FFFF ? (int32_t)(p->AngRateVceX | 0xFFF00000) : (int32_t)p->AngRateVceX) * 0.001f;
        trueObjectInfo.mTgtAngRateVceY = (p->AngRateVceY > 0x7FFFF ? (int32_t)(p->AngRateVceY | 0xFFF00000) : (int32_t)p->AngRateVceY) * 0.001f;
        trueObjectInfo.mTgtAngRateVceZ = (p->AngRateVceZ > 0x7FFFF ? (int32_t)(p->AngRateVceZ | 0xFFF00000) : (int32_t)p->AngRateVceZ) * 0.001f;
    }
        break;
    case 0x7A6: // 从车姿态角
    {
        if (frame.length() != 6) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        Target_Attitude_t *p = (Target_Attitude_t *)ptr;

        trueObjectInfo.mTgtAngleHeading = p->Heading * 0.01f;
        trueObjectInfo.mTgtAnglePitch = ((int16_t)p->Pitch) * 0.01f;
        trueObjectInfo.mTgtAngleRoll  = ((int16_t)p->Roll) * 0.01f;
    }
        break;
    case 0x7AC: // 从车速度
    {
        if (frame.length() != 6) {
            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
            return false;
        }
        Target_VelocityVce1 *p = (Target_VelocityVce1 *)ptr;

        trueObjectInfo.mTgtVelX = ((int16_t)p->Tgt_VelX) * 0.01f;
        trueObjectInfo.mTgtVelY = ((int16_t)p->Tgt_VelY) * 0.01f;
        trueObjectInfo.mTgtVelZ = ((int16_t)p->Tgt_VelZ) * 0.01f;

    }
        break;
    default:
        return false;
    }

    return true;
}

bool TrueSystemProtocol::parseFrame_IFS300_1_4_1(const Devices::Can::CanFrame &frame)
{
    if (frame.length() > 8) {
        return false;
    }

    bool ret = false;
    // 0x60N -> 0x4AN -> 0x7AN -> 0x6DN
    switch (frame.id() & 0xF00) {
    // INS_ 主车数据
    case 0x600:
        ret = parseFrame_IFS300_1_4_1_INS_0x60N(frame);
        break;
    case 0x400:
        ret = parseFrame_IFS300_1_4_1_RDM_0x4AN(frame);
        break;
    // TargetN_ 从车数据
    case 0x700:
        ret = parseFrame_IFS300_1_4_1_Target_0x7AN(frame);
        break;
    default:
        return false;
    }
    return ret;
}

bool TrueSystemProtocol::calculatingTheBody()
{
    /**************************************************************************
     * 北东地坐标系       十六个目标坐标系
     * x ^                       ^ x
     *   |                       |
     *   |                       |
     * --|-------->     <--------|--
     *   |        y     y        |
     **************************************************************************/

    stTrueObjInfo &trueObjectInfo = mAnalysisWorker->mTrueObjectInfo;
    trueObjectInfo.validFlag = true;

    double x = trueObjectInfo.mRDMPos[stTrueObjInfo::Origin].mRDMPosX;
    double y = trueObjectInfo.mRDMPos[stTrueObjInfo::Origin].mRDMPosY;

    trueObjectInfo.mRDMPos[stTrueObjInfo::LeftFront].mRDMPosX = x + mOriginSettingsRDM.mXLF;
    trueObjectInfo.mRDMPos[stTrueObjInfo::LeftFront].mRDMPosY = y - mOriginSettingsRDM.mYLF;
    trueObjectInfo.mRDMPos[stTrueObjInfo::RightFront].mRDMPosX = x + mOriginSettingsRDM.mXRF;
    trueObjectInfo.mRDMPos[stTrueObjInfo::RightFront].mRDMPosY = y + mOriginSettingsRDM.mYRF;
    trueObjectInfo.mRDMPos[stTrueObjInfo::LeftRear].mRDMPosX = x - mOriginSettingsRDM.mXLR;
    trueObjectInfo.mRDMPos[stTrueObjInfo::LeftRear].mRDMPosY = y - mOriginSettingsRDM.mYLR;
    trueObjectInfo.mRDMPos[stTrueObjInfo::RightRear].mRDMPosX = x  - mOriginSettingsRDM.mXRR;
    trueObjectInfo.mRDMPos[stTrueObjInfo::RightRear].mRDMPosY = y + mOriginSettingsRDM.mYRR;

    trueObjectInfo.mHeading = trueObjectInfo.mInsAngleHeading - trueObjectInfo.mTgtAngleHeading;
    float heading = qDegreesToRadians(trueObjectInfo.mHeading);
    qreal s = qSin(heading);
    qreal c = qCos(heading);
    for (int i = stTrueObjInfo::Origin + 1; i < stTrueObjInfo::TruePointCount; ++i)
    {
        double yTemp = (trueObjectInfo.mRDMPos[i].mRDMPosY - y) * c -
                                             (trueObjectInfo.mRDMPos[i].mRDMPosX - x) * s + y;

        trueObjectInfo.mRDMPos[i].mRDMPosX = (trueObjectInfo.mRDMPos[i].mRDMPosY - y) * s +
                                             (trueObjectInfo.mRDMPos[i].mRDMPosX - x) * c + x;
        trueObjectInfo.mRDMPos[i].mRDMPosY = yTemp;
    }

    for (int j = 4; j <= 7; ++j) {
        AnalysisData &mAnalysisData = mAnalysisWorker->mAnalysisDatas[j/*TRUE_VALUE_RADAR_ID_IFS300*/];
        Target *targets = mAnalysisData.mTrueTarget;

        for (int i = 0; i < stTrueObjInfo::TruePointCount; ++i)
        {
            targets[i].mX = -trueObjectInfo.mRDMPos[i].mRDMPosY;
            targets[i].mY = trueObjectInfo.mRDMPos[i].mRDMPosX;
            targets[i].mVx = -trueObjectInfo.mRDMVelY;  // 横向相对速度
            targets[i].mVy = trueObjectInfo.mRDMVelX;  // 纵向相对速度
            targets[i].mVxsog = -trueObjectInfo.mTgtVelY;  // 横向绝对速度
            targets[i].mVysog = trueObjectInfo.mTgtVelX;  // 纵向绝对速度
            targets[i].mAx = -trueObjectInfo.mRDMAccY;     // 横向相对加速度
            targets[i].mAy = trueObjectInfo.mRDMAccX;     // 横向相对加速度
            targets[i].mAngle = qAtan2(targets[i].mX, targets[i].mY) * 180 / M_PI;
            targets[i].mTrackFrameAngle = trueObjectInfo.mHeading; // 航向角
            targets[i].mValid = true;
        }
    }

    return true;
}

} // namespace Protocol
} // namespace Analysis
