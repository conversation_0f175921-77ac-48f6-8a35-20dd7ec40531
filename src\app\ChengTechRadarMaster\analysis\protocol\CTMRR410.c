﻿/**
* Copyright (c) 2016-2022 ChengTech All rights reserved.
* @file CTMRR410.c
* @brief brief
* <AUTHOR>
* @date 2022-12-22
* @version 1.0.0
* @note
* description
*/

#include "CTMRR410.h"

static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
    uint32_t mask = ((1 << bits) - 1);
    uint32_t extracted = data & mask;
    int32_t sign_extended = (extracted & (1 << (bits - 1))) ? (int)(extracted | (~mask)) : (int)extracted;
    return sign_extended;
}

static uint32_t encode_sign_bit(int32_t data, uint8_t bits) {
	uint32_t const m = 0x1u << (bits - 1);

	return (data & m) ? (data | m) : data;
}

// 0x3f0 length = 32
bool decode_SRR_VehicleInfo(CT_SRR_VehicleInfo_t *userData, uint8_t *data, int length) {
	if (length != 32) {
		return false;
	}

	userData->Veh_YawRate = ((((data[1] & 0xFFU) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.00024) - 2.093);
	userData->Veh_SwRctaFunc = ((data[2] & 0x80U) >> 7);
	userData->Veh_SwRctbFunc = ((data[2] & 0x40U) >> 6);
	userData->Veh_SwRcwFunc = ((data[2] & 0x20U) >> 5);
	userData->Veh_SwBSDFunc = ((data[2] & 0x10U) >> 4);
	userData->Veh_SwDowFunc = ((data[2] & 0x8U) >> 3);
	userData->Veh_SwMainFunc = ((data[2] & 0x4U) >> 2);
	userData->Veh_SwFctaFunc = ((data[2] & 0x2U) >> 1);
	userData->Veh_SwFctbFunc = (data[2] & 0x1U);
	userData->Veh_Speed = ((((data[4] & 0xF8U) >> 3) + (((uint16_t)data[3] & 0xFFU) << 5)) * 0.05625);
	userData->Veh_DoorFrontLe = ((data[4] & 0x6U) >> 1);
	userData->Veh_DoorFrontRi = (((data[5] & 0x80U) >> 7) + (((uint16_t)data[4] & 0x1U) << 1));
	userData->Veh_DoorRearLe = ((data[5] & 0x60U) >> 5);
	userData->Veh_DoorRearRi = ((data[5] & 0x18U) >> 3);
	userData->Veh_Gear = (data[5] & 0x7U);
	userData->Veh_KeyState = ((data[6] & 0xF0U) >> 4);
	userData->Veh_SecurityLock = (data[6] & 0xFU);
	userData->Veh_TurnLightLe = ((data[7] & 0x80U) >> 7);
	userData->Veh_TurnLightRi = ((data[7] & 0x40U) >> 6);
	userData->Veh_BrkPedalSts = ((data[7] & 0x30U) >> 4);
	userData->Veh_WhlSpdDirFrontLe = ((data[7] & 0x8U) >> 3);
	userData->Veh_WhlSpdDirFrontRi = ((data[7] & 0x4U) >> 2);
	userData->Veh_WhlSpdDirRearLe = ((data[7] & 0x2U) >> 1);
	userData->Veh_WhlSpdDirRearRi = (data[7] & 0x1U);
	userData->Veh_AccPedalActLevel = ((data[8] & 0xFEU) >> 1);
	userData->Veh_BrkPedalActLevel = (((data[9] & 0xFCU) >> 2) + (((uint16_t)data[8] & 0x1U) << 6));
	userData->Veh_TrailerSts = ((data[9] & 0x2U) >> 1);
	userData->Veh_ESPFailSts = (data[9] & 0x1U);
	userData->Veh_LgtAccel = ((((data[11] & 0xFFU) + (((uint16_t)data[10] & 0xFFU) << 8)) * 0.00098) - 21.592);
	userData->Veh_LatAccel = ((((data[13] & 0xFFU) + (((uint16_t)data[12] & 0xFFU) << 8)) * 0.00098) - 21.592);
	userData->Veh_WhlSpdFrontLe = ((((data[15] & 0xF8U) >> 3) + (((uint16_t)data[14] & 0xFFU) << 5)) * 0.05625);
	userData->Veh_WhlSpdFrontRi = ((((data[17] & 0xF8U) >> 3) + (((uint16_t)data[16] & 0xFFU) << 5)) * 0.05625);
	userData->Veh_WhlSpdRearLe = ((((data[19] & 0xC0U) >> 6) + (((uint32_t)data[18]) << 2) + (((uint32_t)data[17] & 0x7U) << 10)) * 0.05625);
	userData->Veh_WhlSpdRearRi = ((((data[20] & 0xFEU) >> 1) + (((uint16_t)data[19] & 0x3FU) << 7)) * 0.05625);
	userData->Veh_SteerWheelAngle = (((((data[22] & 0xF0U) >> 4) + (((uint32_t)data[21]) << 4) + (((uint32_t)data[20] & 0x1U) << 12)) * 0.2) - 738);
	userData->RollingCntMsgVehInfo01 = (data[22] & 0xFU);
	userData->Veh_Radius = (((((data[26] & 0xC0U) >> 6) + (((uint32_t)data[25]) << 2) + (((uint32_t)data[24] & 0xFFU) << 10)) * 0.25) - 32767);
	userData->ChecksumMsgVehInfo01 = (data[31]);

	return true;
}

// 0x3f0 length = 32
bool encode_SRR_VehicleInfo(CT_SRR_VehicleInfo_t *userData, uint8_t *data, int length) {
	if (length != 32) {
		return false;
	}

	uint16_t Veh_YawRate = (((userData->Veh_YawRate) + 2.093) / 0.00024);
	uint8_t Veh_SwRctaFunc = (userData->Veh_SwRctaFunc);
	uint8_t Veh_SwRctbFunc = (userData->Veh_SwRctbFunc);
	uint8_t Veh_SwRcwFunc = (userData->Veh_SwRcwFunc);
	uint8_t Veh_SwBSDFunc = (userData->Veh_SwBSDFunc);
	uint8_t Veh_SwDowFunc = (userData->Veh_SwDowFunc);
	uint8_t Veh_SwMainFunc = (userData->Veh_SwMainFunc);
	uint8_t Veh_SwFctaFunc = (userData->Veh_SwFctaFunc);
	uint8_t Veh_SwFctbFunc = (userData->Veh_SwFctbFunc);
	uint16_t Veh_Speed = ((userData->Veh_Speed) / 0.05625);
	uint8_t Veh_DoorFrontLe = (userData->Veh_DoorFrontLe);
	uint8_t Veh_DoorFrontRi = (userData->Veh_DoorFrontRi);
	uint8_t Veh_DoorRearLe = (userData->Veh_DoorRearLe);
	uint8_t Veh_DoorRearRi = (userData->Veh_DoorRearRi);
	uint8_t Veh_Gear = (userData->Veh_Gear);
	uint8_t Veh_KeyState = (userData->Veh_KeyState);
	uint8_t Veh_SecurityLock = (userData->Veh_SecurityLock);
	uint8_t Veh_TurnLightLe = (userData->Veh_TurnLightLe);
	uint8_t Veh_TurnLightRi = (userData->Veh_TurnLightRi);
	uint8_t Veh_BrkPedalSts = (userData->Veh_BrkPedalSts);
	uint8_t Veh_WhlSpdDirFrontLe = (userData->Veh_WhlSpdDirFrontLe);
	uint8_t Veh_WhlSpdDirFrontRi = (userData->Veh_WhlSpdDirFrontRi);
	uint8_t Veh_WhlSpdDirRearLe = (userData->Veh_WhlSpdDirRearLe);
	uint8_t Veh_WhlSpdDirRearRi = (userData->Veh_WhlSpdDirRearRi);
	uint8_t Veh_AccPedalActLevel = (userData->Veh_AccPedalActLevel);
	uint8_t Veh_BrkPedalActLevel = (userData->Veh_BrkPedalActLevel);
	uint8_t Veh_TrailerSts = (userData->Veh_TrailerSts);
	uint8_t Veh_ESPFailSts = (userData->Veh_ESPFailSts);
	uint16_t Veh_LgtAccel = (((userData->Veh_LgtAccel) + 21.592) / 0.00098);
	uint16_t Veh_LatAccel = (((userData->Veh_LatAccel) + 21.592) / 0.00098);
	uint16_t Veh_WhlSpdFrontLe = ((userData->Veh_WhlSpdFrontLe) / 0.05625);
	uint16_t Veh_WhlSpdFrontRi = ((userData->Veh_WhlSpdFrontRi) / 0.05625);
	uint16_t Veh_WhlSpdRearLe = ((userData->Veh_WhlSpdRearLe) / 0.05625);
	uint16_t Veh_WhlSpdRearRi = ((userData->Veh_WhlSpdRearRi) / 0.05625);
	uint16_t Veh_SteerWheelAngle = (((userData->Veh_SteerWheelAngle) + 738) / 0.2);
	uint8_t RollingCntMsgVehInfo01 = (userData->RollingCntMsgVehInfo01);
	uint32_t Veh_Radius = (((userData->Veh_Radius) + 32767) / 0.25);
	uint8_t ChecksumMsgVehInfo01 = (userData->ChecksumMsgVehInfo01);

	data[0] = ((Veh_YawRate & 0xFF00U) >> 8);
	data[1] = ((Veh_YawRate & 0xFFU));
	data[2] = (((userData->Veh_SwRctaFunc & 0x1U) << 7) | ((userData->Veh_SwRctbFunc & 0x1U) << 6) | ((userData->Veh_SwRcwFunc & 0x1U) << 5) | ((userData->Veh_SwBSDFunc & 0x1U) << 4) | ((userData->Veh_SwDowFunc & 0x1U) << 3) | ((userData->Veh_SwMainFunc & 0x1U) << 2) | ((userData->Veh_SwFctaFunc & 0x1U) << 1) | (userData->Veh_SwFctbFunc & 0x1U));
	data[3] = ((Veh_Speed & 0x1FE0U) >> 5);
	data[4] = (((Veh_Speed & 0x1FU) << 3) | ((userData->Veh_DoorFrontLe & 0x3U) << 1) | ((userData->Veh_DoorFrontRi & 0x2U) >> 1));
	data[5] = (((userData->Veh_DoorFrontRi & 0x1U) << 7) | ((userData->Veh_DoorRearLe & 0x3U) << 5) | ((userData->Veh_DoorRearRi & 0x3U) << 3) | (userData->Veh_Gear & 0x7U));
	data[6] = (((userData->Veh_KeyState & 0xFU) << 4) | (userData->Veh_SecurityLock & 0xFU));
	data[7] = (((userData->Veh_TurnLightLe & 0x1U) << 7) | ((userData->Veh_TurnLightRi & 0x1U) << 6) | ((userData->Veh_BrkPedalSts & 0x3U) << 4) | ((userData->Veh_WhlSpdDirFrontLe & 0x1U) << 3) | ((userData->Veh_WhlSpdDirFrontRi & 0x1U) << 2) | ((userData->Veh_WhlSpdDirRearLe & 0x1U) << 1) | (userData->Veh_WhlSpdDirRearRi & 0x1U));
	data[8] = (((userData->Veh_AccPedalActLevel & 0x7FU) << 1) | ((userData->Veh_BrkPedalActLevel & 0x40U) >> 6));
	data[9] = (((userData->Veh_BrkPedalActLevel & 0x3FU) << 2) | ((userData->Veh_TrailerSts & 0x1U) << 1) | (userData->Veh_ESPFailSts & 0x1U));
	data[10] = ((Veh_LgtAccel & 0xFF00U) >> 8);
	data[11] = ((Veh_LgtAccel & 0xFFU));
	data[12] = ((Veh_LatAccel & 0xFF00U) >> 8);
	data[13] = ((Veh_LatAccel & 0xFFU));
	data[14] = ((Veh_WhlSpdFrontLe & 0x1FE0U) >> 5);
	data[15] = ((Veh_WhlSpdFrontLe & 0x1FU) << 3);
	data[16] = ((Veh_WhlSpdFrontRi & 0x1FE0U) >> 5);
	data[17] = (((Veh_WhlSpdFrontRi & 0x1FU) << 3) | ((Veh_WhlSpdRearLe & 0x1C00U) >> 10));
	data[18] = ((Veh_WhlSpdRearLe & 0x3FCU) >> 2);
	data[19] = (((Veh_WhlSpdRearLe & 0x3U) << 6) | ((Veh_WhlSpdRearRi & 0x1F80U) >> 7));
	data[20] = (((Veh_WhlSpdRearRi & 0x7FU) << 1) | ((Veh_SteerWheelAngle & 0x1000U) >> 12));
	data[21] = ((Veh_SteerWheelAngle & 0xFF0U) >> 4);
	data[22] = (((Veh_SteerWheelAngle & 0xFU) << 4) | (userData->RollingCntMsgVehInfo01 & 0xFU));
	data[23] = 0;
	data[24] = ((Veh_Radius & 0x3FC00U) >> 10);
	data[25] = ((Veh_Radius & 0x3FCU) >> 2);
	data[26] = ((Veh_Radius & 0x3U) << 6);
	data[27] = 0;
	data[28] = 0;
	data[29] = 0;
	data[30] = 0;
	data[31] = (userData->ChecksumMsgVehInfo01 & 0xFFU);

	return true;
}
// 0x400 length = 16
bool decode_SRR_RawHeader(CT_SRR_RawHeader_t *userData, uint8_t *data, int length) {
	if (length != 16) {
		return false;
	}

	userData->RawHeaderProtVer = (data[0]);
	userData->RawHeaderNoiseCurrent = (((data[1]) * 0.5) - 100);
	userData->RawHeaderNoiseGlobal = (((data[2]) * 0.5) - 100);
	userData->RawHeaderObjNum = (((data[4] & 0xC0U) >> 6) + (((uint16_t)data[3] & 0xFFU) << 2));
	userData->RawHeaderFuncCalcTime = (((data[5] & 0xE0U) >> 5) + (((uint16_t)data[4] & 0x3FU) << 3));
	userData->RawHeaderRspTaskCycleTime = (((data[6] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0x1FU) << 4));
	userData->RawHeaderDataSource = (data[6] & 0x7U);
	userData->RawHeaderObjNumAfterCFAR = (data[7]);
	userData->RawHeaderObjNumAfterFilter = (data[8]);
	userData->RawHeaderCurrentFrameModeIdx = ((data[9] & 0xF0U) >> 4);
	userData->RawHeaderFrameModeNum = (data[9] & 0xFU);
	userData->RawHeaderRollingCnt = (data[15] & 0xFU);

	return true;
}

// 0x400 length = 16
bool encode_SRR_RawHeader(CT_SRR_RawHeader_t *userData, uint8_t *data, int length) {
	if (length != 16) {
		return false;
	}

	uint8_t RawHeaderProtVer = (userData->RawHeaderProtVer);
	uint8_t RawHeaderNoiseCurrent = (((userData->RawHeaderNoiseCurrent) + 100) / 0.5);
	uint8_t RawHeaderNoiseGlobal = (((userData->RawHeaderNoiseGlobal) + 100) / 0.5);
	uint16_t RawHeaderObjNum = (userData->RawHeaderObjNum);
	uint16_t RawHeaderFuncCalcTime = (userData->RawHeaderFuncCalcTime);
	uint16_t RawHeaderRspTaskCycleTime = (userData->RawHeaderRspTaskCycleTime);
	uint8_t RawHeaderDataSource = (userData->RawHeaderDataSource);
	uint8_t RawHeaderObjNumAfterCFAR = (userData->RawHeaderObjNumAfterCFAR);
	uint8_t RawHeaderObjNumAfterFilter = (userData->RawHeaderObjNumAfterFilter);
	uint8_t RawHeaderCurrentFrameModeIdx = (userData->RawHeaderCurrentFrameModeIdx);
	uint8_t RawHeaderFrameModeNum = (userData->RawHeaderFrameModeNum);
	uint8_t RawHeaderRollingCnt = (userData->RawHeaderRollingCnt);

	data[0] = (userData->RawHeaderProtVer & 0xFFU);
	data[1] = (RawHeaderNoiseCurrent & 0xFFU);
	data[2] = (RawHeaderNoiseGlobal & 0xFFU);
	data[3] = ((userData->RawHeaderObjNum & 0x3FCU) >> 2);
	data[4] = (((userData->RawHeaderObjNum & 0x3U) << 6) | ((userData->RawHeaderFuncCalcTime & 0x1F8U) >> 3));
	data[5] = (((userData->RawHeaderFuncCalcTime & 0x7U) << 5) | ((userData->RawHeaderRspTaskCycleTime & 0x1F0U) >> 4));
	data[6] = (((userData->RawHeaderRspTaskCycleTime & 0xFU) << 4) | (userData->RawHeaderDataSource & 0x7U));
	data[7] = (userData->RawHeaderObjNumAfterCFAR & 0xFFU);
	data[8] = (userData->RawHeaderObjNumAfterFilter & 0xFFU);
	data[9] = (((userData->RawHeaderCurrentFrameModeIdx & 0xFU) << 4) | (userData->RawHeaderFrameModeNum & 0xFU));
	data[10] = 0;
	data[11] = 0;
	data[12] = 0;
	data[13] = 0;
	data[14] = 0;
	data[15] = (userData->RawHeaderRollingCnt & 0xFU);

	return true;
}
// 0x410 length = 64
bool decode_SRR_RawDetections(CT_SRR_RawDetections_t *userData, uint8_t *data, int length) {
	if (length != 64) {
		return false;
	}

	userData->RawObjectRange01 = ((((data[1] & 0xFEU) >> 1) + (((uint16_t)data[0] & 0xFFU) << 7)) * 0.01);
	userData->RawObjectAzimuth01 = (((((data[3] & 0xFCU) >> 2) + (((uint32_t)data[2]) << 6) + (((uint32_t)data[1] & 0x1U) << 14)) * 0.01) - 163.84);
	userData->RawObjectVelocity01 = (((((data[5] & 0xF0U) >> 4) + (((uint32_t)data[4]) << 4) + (((uint32_t)data[3] & 0x3U) << 12)) * 0.01) - 81.92);
	userData->RawObjectElevationAngle01 = (((((data[7] & 0x80U) >> 7) + (((uint32_t)data[6]) << 1) + (((uint32_t)data[5] & 0xFU) << 9)) * 0.02) - 81.92);
	userData->RawObjectProbOfExist01 = (data[7] & 0x7FU);
	userData->RawObjectRCS01 = (((data[8]) * 0.5) - 30);
	userData->RawObjectSNR01 = ((data[9]) * 0.5);
	userData->RawObjectStatus01 = (((data[11] & 0xC0U) >> 6) + (((uint16_t)data[10] & 0xFFU) << 2));
	userData->RawObjectDopplerVelocity01 = (((data[12] & 0xE0U) >> 5) + (((uint16_t)data[11] & 0x3FU) << 3));
	userData->RawObjectSubFrameID01 = ((data[12] & 0x1EU) >> 1);
	userData->RawObjectAssociatedTrkId01 = (((data[13] & 0xFCU) >> 2) + (((uint16_t)data[12] & 0x1U) << 6));
	userData->RawObjectGroupID01 = (((data[14] & 0xF8U) >> 3) + (((uint16_t)data[13] & 0x3U) << 5));
	userData->RawObjectRange02 = ((((data[17] & 0xFEU) >> 1) + (((uint16_t)data[16] & 0xFFU) << 7)) * 0.01);
	userData->RawObjectAzimuth02 = (((((data[19] & 0xFCU) >> 2) + (((uint32_t)data[18]) << 6) + (((uint32_t)data[17] & 0x1U) << 14)) * 0.01) - 163.84);
	userData->RawObjectVelocity02 = (((((data[21] & 0xF0U) >> 4) + (((uint32_t)data[20]) << 4) + (((uint32_t)data[19] & 0x3U) << 12)) * 0.01) - 81.92);
	userData->RawObjectElevationAngle02 = (((((data[23] & 0x80U) >> 7) + (((uint32_t)data[22]) << 1) + (((uint32_t)data[21] & 0xFU) << 9)) * 0.02) - 81.92);
	userData->RawObjectProbOfExist02 = (data[23] & 0x7FU);
	userData->RawObjectRCS02 = ((data[24]) * 0.5);
	userData->RawObjectSNR02 = ((data[25]) * 0.5);
	userData->RawObjectStatus02 = (((data[27] & 0xC0U) >> 6) + (((uint16_t)data[26] & 0xFFU) << 2));
	userData->RawObjectDopplerVelocity02 = (((data[28] & 0xE0U) >> 5) + (((uint16_t)data[27] & 0x3FU) << 3));
	userData->RawObjectSubFrameID02 = ((data[28] & 0x1EU) >> 1);
	userData->RawObjectAssociatedTrkId02 = (((data[29] & 0xFCU) >> 2) + (((uint16_t)data[28] & 0x1U) << 6));
	userData->RawObjectGroupID02 = (((data[30] & 0xF8U) >> 3) + (((uint16_t)data[29] & 0x3U) << 5));
	userData->RawObjectRange03 = ((((data[33] & 0xFEU) >> 1) + (((uint16_t)data[32] & 0xFFU) << 7)) * 0.01);
	userData->RawObjectAzimuth03 = (((((data[35] & 0xFCU) >> 2) + (((uint32_t)data[34]) << 6) + (((uint32_t)data[33] & 0x1U) << 14)) * 0.01) - 163.84);
	userData->RawObjectVelocity03 = (((((data[37] & 0xF0U) >> 4) + (((uint32_t)data[36]) << 4) + (((uint32_t)data[35] & 0x3U) << 12)) * 0.01) - 81.92);
	userData->RawObjectElevationAngle03 = (((((data[39] & 0x80U) >> 7) + (((uint32_t)data[38]) << 1) + (((uint32_t)data[37] & 0xFU) << 9)) * 0.02) - 81.92);
	userData->RawObjectProbOfExist03 = (data[39] & 0x7FU);
	userData->RawObjectRCS03 = ((data[40]) * 0.5);
	userData->RawObjectSNR03 = ((data[41]) * 0.5);
	userData->RawObjectStatus03 = (((data[43] & 0xC0U) >> 6) + (((uint16_t)data[42] & 0xFFU) << 2));
	userData->RawObjectDopplerVelocity03 = (((data[44] & 0xE0U) >> 5) + (((uint16_t)data[43] & 0x3FU) << 3));
	userData->RawObjectSubFrameID03 = ((data[44] & 0x1EU) >> 1);
	userData->RawObjectAssociatedTrkId03 = (((data[45] & 0xFCU) >> 2) + (((uint16_t)data[44] & 0x1U) << 6));
	userData->RawObjectGroupID03 = (((data[46] & 0xF8U) >> 3) + (((uint16_t)data[45] & 0x3U) << 5));
	userData->RawObjectRange04 = ((((data[49] & 0xFEU) >> 1) + (((uint16_t)data[48] & 0xFFU) << 7)) * 0.01);
	userData->RawObjectAzimuth04 = (((((data[51] & 0xFCU) >> 2) + (((uint32_t)data[50]) << 6) + (((uint32_t)data[49] & 0x1U) << 14)) * 0.01) - 163.84);
	userData->RawObjectVelocity04 = (((((data[53] & 0xF0U) >> 4) + (((uint32_t)data[52]) << 4) + (((uint32_t)data[51] & 0x3U) << 12)) * 0.01) - 81.92);
	userData->RawObjectElevationAngle04 = (((((data[55] & 0x80U) >> 7) + (((uint32_t)data[54]) << 1) + (((uint32_t)data[53] & 0xFU) << 9)) * 0.02) - 81.92);
	userData->RawObjectProbOfExist04 = (data[55] & 0x7FU);
	userData->RawObjectRCS04 = ((data[56]) * 0.5);
	userData->RawObjectSNR04 = ((data[57]) * 0.5);
	userData->RawObjectStatus04 = (((data[59] & 0xC0U) >> 6) + (((uint16_t)data[58] & 0xFFU) << 2));
	userData->RawObjectDopplerVelocity04 = (((data[60] & 0xE0U) >> 5) + (((uint16_t)data[59] & 0x3FU) << 3));
	userData->RawObjectSubFrameID04 = ((data[60] & 0x1EU) >> 1);
	userData->RawObjectAssociatedTrkId04 = (((data[61] & 0xFCU) >> 2) + (((uint16_t)data[60] & 0x1U) << 6));
	userData->RawObjectGroupID04 = (((data[62] & 0xF8U) >> 3) + (((uint16_t)data[61] & 0x3U) << 5));
	userData->RawObjectRollingCnt01 = (data[63] & 0x7U);

	return true;
}

// 0x410 length = 64
bool encode_SRR_RawDetections(CT_SRR_RawDetections_t *userData, uint8_t *data, int length) {
	if (length != 64) {
		return false;
	}

	uint16_t RawObjectRange01 = ((userData->RawObjectRange01) / 0.01);
	uint16_t RawObjectAzimuth01 = (((userData->RawObjectAzimuth01) + 163.84) / 0.01);
	uint16_t RawObjectVelocity01 = (((userData->RawObjectVelocity01) + 81.92) / 0.01);
	uint16_t RawObjectElevationAngle01 = (((userData->RawObjectElevationAngle01) + 81.92) / 0.02);
	uint8_t RawObjectProbOfExist01 = (userData->RawObjectProbOfExist01);
	uint8_t RawObjectRCS01 = (((userData->RawObjectRCS01) + 30) / 0.5);
	uint8_t RawObjectSNR01 = ((userData->RawObjectSNR01) / 0.5);
	uint16_t RawObjectStatus01 = (userData->RawObjectStatus01);
	uint16_t RawObjectDopplerVelocity01 = (userData->RawObjectDopplerVelocity01);
	uint8_t RawObjectSubFrameID01 = (userData->RawObjectSubFrameID01);
	uint8_t RawObjectAssociatedTrkId01 = (userData->RawObjectAssociatedTrkId01);
	uint8_t RawObjectGroupID01 = (userData->RawObjectGroupID01);
	uint16_t RawObjectRange02 = ((userData->RawObjectRange02) / 0.01);
	uint16_t RawObjectAzimuth02 = (((userData->RawObjectAzimuth02) + 163.84) / 0.01);
	uint16_t RawObjectVelocity02 = (((userData->RawObjectVelocity02) + 81.92) / 0.01);
	uint16_t RawObjectElevationAngle02 = (((userData->RawObjectElevationAngle02) + 81.92) / 0.02);
	uint8_t RawObjectProbOfExist02 = (userData->RawObjectProbOfExist02);
	uint8_t RawObjectRCS02 = ((userData->RawObjectRCS02) / 0.5);
	uint8_t RawObjectSNR02 = ((userData->RawObjectSNR02) / 0.5);
	uint16_t RawObjectStatus02 = (userData->RawObjectStatus02);
	uint16_t RawObjectDopplerVelocity02 = (userData->RawObjectDopplerVelocity02);
	uint8_t RawObjectSubFrameID02 = (userData->RawObjectSubFrameID02);
	uint8_t RawObjectAssociatedTrkId02 = (userData->RawObjectAssociatedTrkId02);
	uint8_t RawObjectGroupID02 = (userData->RawObjectGroupID02);
	uint16_t RawObjectRange03 = ((userData->RawObjectRange03) / 0.01);
	uint16_t RawObjectAzimuth03 = (((userData->RawObjectAzimuth03) + 163.84) / 0.01);
	uint16_t RawObjectVelocity03 = (((userData->RawObjectVelocity03) + 81.92) / 0.01);
	uint16_t RawObjectElevationAngle03 = (((userData->RawObjectElevationAngle03) + 81.92) / 0.02);
	uint8_t RawObjectProbOfExist03 = (userData->RawObjectProbOfExist03);
	uint8_t RawObjectRCS03 = ((userData->RawObjectRCS03) / 0.5);
	uint8_t RawObjectSNR03 = ((userData->RawObjectSNR03) / 0.5);
	uint16_t RawObjectStatus03 = (userData->RawObjectStatus03);
	uint16_t RawObjectDopplerVelocity03 = (userData->RawObjectDopplerVelocity03);
	uint8_t RawObjectSubFrameID03 = (userData->RawObjectSubFrameID03);
	uint8_t RawObjectAssociatedTrkId03 = (userData->RawObjectAssociatedTrkId03);
	uint8_t RawObjectGroupID03 = (userData->RawObjectGroupID03);
	uint16_t RawObjectRange04 = ((userData->RawObjectRange04) / 0.01);
	uint16_t RawObjectAzimuth04 = (((userData->RawObjectAzimuth04) + 163.84) / 0.01);
	uint16_t RawObjectVelocity04 = (((userData->RawObjectVelocity04) + 81.92) / 0.01);
	uint16_t RawObjectElevationAngle04 = (((userData->RawObjectElevationAngle04) + 81.92) / 0.02);
	uint8_t RawObjectProbOfExist04 = (userData->RawObjectProbOfExist04);
	uint8_t RawObjectRCS04 = ((userData->RawObjectRCS04) / 0.5);
	uint8_t RawObjectSNR04 = ((userData->RawObjectSNR04) / 0.5);
	uint16_t RawObjectStatus04 = (userData->RawObjectStatus04);
	uint16_t RawObjectDopplerVelocity04 = (userData->RawObjectDopplerVelocity04);
	uint8_t RawObjectSubFrameID04 = (userData->RawObjectSubFrameID04);
	uint8_t RawObjectAssociatedTrkId04 = (userData->RawObjectAssociatedTrkId04);
	uint8_t RawObjectGroupID04 = (userData->RawObjectGroupID04);
	uint8_t RawObjectRollingCnt01 = (userData->RawObjectRollingCnt01);

	data[0] = ((RawObjectRange01 & 0x7F80U) >> 7);
	data[1] = (((RawObjectRange01 & 0x7FU) << 1) | ((RawObjectAzimuth01 & 0x4000U) >> 14));
	data[2] = ((RawObjectAzimuth01 & 0x3FC0U) >> 6);
	data[3] = (((RawObjectAzimuth01 & 0x3FU) << 2) | ((RawObjectVelocity01 & 0x3000U) >> 12));
	data[4] = ((RawObjectVelocity01 & 0xFF0U) >> 4);
	data[5] = (((RawObjectVelocity01 & 0xFU) << 4) | ((RawObjectElevationAngle01 & 0x1E00U) >> 9));
	data[6] = ((RawObjectElevationAngle01 & 0x1FEU) >> 1);
	data[7] = (((RawObjectElevationAngle01 & 0x1U) << 7) | (userData->RawObjectProbOfExist01 & 0x7FU));
	data[8] = (RawObjectRCS01 & 0xFFU);
	data[9] = (RawObjectSNR01 & 0xFFU);
	data[10] = ((userData->RawObjectStatus01 & 0x3FCU) >> 2);
	data[11] = (((userData->RawObjectStatus01 & 0x3U) << 6) | ((userData->RawObjectDopplerVelocity01 & 0x1F8U) >> 3));
	data[12] = (((userData->RawObjectDopplerVelocity01 & 0x7U) << 5) | ((userData->RawObjectSubFrameID01 & 0xFU) << 1) | ((userData->RawObjectAssociatedTrkId01 & 0x40U) >> 6));
	data[13] = (((userData->RawObjectAssociatedTrkId01 & 0x3FU) << 2) | ((userData->RawObjectGroupID01 & 0x60U) >> 5));
	data[14] = ((userData->RawObjectGroupID01 & 0x1FU) << 3);
	data[15] = 0;
	data[16] = ((RawObjectRange02 & 0x7F80U) >> 7);
	data[17] = (((RawObjectRange02 & 0x7FU) << 1) | ((RawObjectAzimuth02 & 0x4000U) >> 14));
	data[18] = ((RawObjectAzimuth02 & 0x3FC0U) >> 6);
	data[19] = (((RawObjectAzimuth02 & 0x3FU) << 2) | ((RawObjectVelocity02 & 0x3000U) >> 12));
	data[20] = ((RawObjectVelocity02 & 0xFF0U) >> 4);
	data[21] = (((RawObjectVelocity02 & 0xFU) << 4) | ((RawObjectElevationAngle02 & 0x1E00U) >> 9));
	data[22] = ((RawObjectElevationAngle02 & 0x1FEU) >> 1);
	data[23] = (((RawObjectElevationAngle02 & 0x1U) << 7) | (userData->RawObjectProbOfExist02 & 0x7FU));
	data[24] = (RawObjectRCS02 & 0xFFU);
	data[25] = (RawObjectSNR02 & 0xFFU);
	data[26] = ((userData->RawObjectStatus02 & 0x3FCU) >> 2);
	data[27] = (((userData->RawObjectStatus02 & 0x3U) << 6) | ((userData->RawObjectDopplerVelocity02 & 0x1F8U) >> 3));
	data[28] = (((userData->RawObjectDopplerVelocity02 & 0x7U) << 5) | ((userData->RawObjectSubFrameID02 & 0xFU) << 1) | ((userData->RawObjectAssociatedTrkId02 & 0x40U) >> 6));
	data[29] = (((userData->RawObjectAssociatedTrkId02 & 0x3FU) << 2) | ((userData->RawObjectGroupID02 & 0x60U) >> 5));
	data[30] = ((userData->RawObjectGroupID02 & 0x1FU) << 3);
	data[31] = 0;
	data[32] = ((RawObjectRange03 & 0x7F80U) >> 7);
	data[33] = (((RawObjectRange03 & 0x7FU) << 1) | ((RawObjectAzimuth03 & 0x4000U) >> 14));
	data[34] = ((RawObjectAzimuth03 & 0x3FC0U) >> 6);
	data[35] = (((RawObjectAzimuth03 & 0x3FU) << 2) | ((RawObjectVelocity03 & 0x3000U) >> 12));
	data[36] = ((RawObjectVelocity03 & 0xFF0U) >> 4);
	data[37] = (((RawObjectVelocity03 & 0xFU) << 4) | ((RawObjectElevationAngle03 & 0x1E00U) >> 9));
	data[38] = ((RawObjectElevationAngle03 & 0x1FEU) >> 1);
	data[39] = (((RawObjectElevationAngle03 & 0x1U) << 7) | (userData->RawObjectProbOfExist03 & 0x7FU));
	data[40] = (RawObjectRCS03 & 0xFFU);
	data[41] = (RawObjectSNR03 & 0xFFU);
	data[42] = ((userData->RawObjectStatus03 & 0x3FCU) >> 2);
	data[43] = (((userData->RawObjectStatus03 & 0x3U) << 6) | ((userData->RawObjectDopplerVelocity03 & 0x1F8U) >> 3));
	data[44] = (((userData->RawObjectDopplerVelocity03 & 0x7U) << 5) | ((userData->RawObjectSubFrameID03 & 0xFU) << 1) | ((userData->RawObjectAssociatedTrkId03 & 0x40U) >> 6));
	data[45] = (((userData->RawObjectAssociatedTrkId03 & 0x3FU) << 2) | ((userData->RawObjectGroupID03 & 0x60U) >> 5));
	data[46] = ((userData->RawObjectGroupID03 & 0x1FU) << 3);
	data[47] = 0;
	data[48] = ((RawObjectRange04 & 0x7F80U) >> 7);
	data[49] = (((RawObjectRange04 & 0x7FU) << 1) | ((RawObjectAzimuth04 & 0x4000U) >> 14));
	data[50] = ((RawObjectAzimuth04 & 0x3FC0U) >> 6);
	data[51] = (((RawObjectAzimuth04 & 0x3FU) << 2) | ((RawObjectVelocity04 & 0x3000U) >> 12));
	data[52] = ((RawObjectVelocity04 & 0xFF0U) >> 4);
	data[53] = (((RawObjectVelocity04 & 0xFU) << 4) | ((RawObjectElevationAngle04 & 0x1E00U) >> 9));
	data[54] = ((RawObjectElevationAngle04 & 0x1FEU) >> 1);
	data[55] = (((RawObjectElevationAngle04 & 0x1U) << 7) | (userData->RawObjectProbOfExist04 & 0x7FU));
	data[56] = (RawObjectRCS04 & 0xFFU);
	data[57] = (RawObjectSNR04 & 0xFFU);
	data[58] = ((userData->RawObjectStatus04 & 0x3FCU) >> 2);
	data[59] = (((userData->RawObjectStatus04 & 0x3U) << 6) | ((userData->RawObjectDopplerVelocity04 & 0x1F8U) >> 3));
	data[60] = (((userData->RawObjectDopplerVelocity04 & 0x7U) << 5) | ((userData->RawObjectSubFrameID04 & 0xFU) << 1) | ((userData->RawObjectAssociatedTrkId04 & 0x40U) >> 6));
	data[61] = (((userData->RawObjectAssociatedTrkId04 & 0x3FU) << 2) | ((userData->RawObjectGroupID04 & 0x60U) >> 5));
	data[62] = ((userData->RawObjectGroupID04 & 0x1FU) << 3);
	data[63] = (userData->RawObjectRollingCnt01 & 0x7U);

	return true;
}
// 0x430 length = 8
bool decode_SRR_ObjectHeader(CT_SRR_ObjectHeader_t *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	userData->ObjHeaderChecksum = (data[0]);
	userData->ObjHeaderObjNum = (data[1]);
	userData->ObjHeaderMeasCnt = ((data[3] & 0xFFU) + (((uint16_t)data[2] & 0xFFU) << 8));
	userData->TrkHeaderFuncCalcTime = (((data[5] & 0xFEU) >> 1) + (((uint16_t)data[4] & 0x3U) << 7));
	userData->TrkHeaderTaskCycleTime = ((data[6] & 0xFFU) + (((uint16_t)data[5] & 0x1U) << 8));
	userData->ObjHeaderProtVer = ((data[7] & 0xF0U) >> 4);
	userData->ObjHeaderRollingCnt = (data[7] & 0xFU);

	return true;
}

// 0x430 length = 8
bool encode_SRR_ObjectHeader(CT_SRR_ObjectHeader_t *userData, uint8_t *data, int length) {
	if (length != 8) {
		return false;
	}

	uint8_t ObjHeaderChecksum = (userData->ObjHeaderChecksum);
	uint8_t ObjHeaderObjNum = (userData->ObjHeaderObjNum);
	uint16_t ObjHeaderMeasCnt = (userData->ObjHeaderMeasCnt);
	uint16_t TrkHeaderFuncCalcTime = (userData->TrkHeaderFuncCalcTime);
	uint16_t TrkHeaderTaskCycleTime = (userData->TrkHeaderTaskCycleTime);
	uint8_t ObjHeaderProtVer = (userData->ObjHeaderProtVer);
	uint8_t ObjHeaderRollingCnt = (userData->ObjHeaderRollingCnt);

	data[0] = (userData->ObjHeaderChecksum & 0xFFU);
	data[1] = (userData->ObjHeaderObjNum & 0xFFU);
	data[2] = ((userData->ObjHeaderMeasCnt & 0xFF00U) >> 8);
	data[3] = ((userData->ObjHeaderMeasCnt & 0xFFU));
	data[4] = ((userData->TrkHeaderFuncCalcTime & 0x180U) >> 7);
	data[5] = (((userData->TrkHeaderFuncCalcTime & 0x7FU) << 1) | ((userData->TrkHeaderTaskCycleTime & 0x100U) >> 8));
	data[6] = ((userData->TrkHeaderTaskCycleTime & 0xFFU));
	data[7] = (((userData->ObjHeaderProtVer & 0xFU) << 4) | (userData->ObjHeaderRollingCnt & 0xFU));

	return true;
}
// 0x440 length = 24
bool decode_SRR_ObjectList(CT_SRR_ObjectList_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	userData->ObjectChecksum = (data[0]);
	userData->ObjectID = (data[1]);
	userData->ObjectDistLong = (((((data[3] & 0xFCU) >> 2) + (((uint16_t)data[2] & 0xFFU) << 6)) * 0.05) - 409.6);
	userData->ObjectDistLat = (((((data[5] & 0xC0U) >> 6) + (((uint32_t)data[4]) << 2) + (((uint32_t)data[3] & 0x3U) << 10)) * 0.05) - 102.4);
	userData->ObjectVrelLong = (((((data[6] & 0xFEU) >> 1) + (((uint16_t)data[5] & 0x3FU) << 7)) * 0.04) - 163.84);
	userData->ObjectClass = (((data[7] & 0xE0U) >> 5) + (((uint16_t)data[6] & 0x1U) << 3));
	userData->ObjectDistLongRms = ((data[7] & 0x1FU) * 0.375);
	userData->ObjectVrelLat = (((((data[9] & 0xF8U) >> 3) + (((uint16_t)data[8] & 0xFFU) << 5)) * 0.04) - 163.84);
	userData->ObjectArelLong = ((((data[10] & 0xFFU) + (((uint16_t)data[9] & 0x7U) << 8)) * 0.01) - 10.24);
	userData->ObjectArelLat = (((((data[12] & 0xE0U) >> 5) + (((uint16_t)data[11] & 0xFFU) << 3)) * 0.01) - 10.24);
	userData->ObjectLength = ((((data[13] & 0xE0U) >> 5) + (((uint16_t)data[12] & 0x1FU) << 3)) * 0.2);
	userData->ObjectWidth = ((((data[14] & 0xE0U) >> 5) + (((uint16_t)data[13] & 0x1FU) << 3)) * 0.2);
	userData->ObjectMeasState = ((data[14] & 0xEU) >> 1);
	userData->ObjectRCS = ((((data[15] & 0xFFU) + (((uint16_t)data[14] & 0x1U) << 8)) * 0.5) - 128);
	userData->ObjectDynProp = ((data[16] & 0xF0U) >> 4);
	userData->ObjectHeight = ((((data[17] & 0xF0U) >> 4) + (((uint16_t)data[16] & 0xFU) << 4)) * 0.05);
	userData->ObjectDistLatRms = ((((data[18] & 0x80U) >> 7) + (((uint16_t)data[17] & 0xFU) << 1)) * 0.375);
	userData->ObjectVrelLongRms = (((data[18] & 0x7CU) >> 2) * 0.375);
	userData->ObjectVrelLatRms = ((((data[19] & 0xE0U) >> 5) + (((uint16_t)data[18] & 0x3U) << 3)) * 0.375);
	userData->ObjectArelLongRms = ((data[19] & 0x1FU) * 0.375);
	userData->ObjectArelLatRms = (((data[20] & 0xF8U) >> 3) * 0.375);
	userData->ObjectOrientationAngle = (((((data[21] & 0xFEU) >> 1) + (((uint16_t)data[20] & 0x7U) << 7)) * 0.4) - 180);
	userData->ObjectProbOfExist = (((data[22] & 0xF8U) >> 3) * 3.2258);
	userData->ObjectDistAltitude = (((((data[23] & 0xF8U) >> 3) + (((uint16_t)data[22] & 0x7U) << 5)) * 0.05) - 1.75);
	userData->ObjectRollingCnt = (data[23] & 0x7U);

	return true;
}

// 0x440 length = 24
bool encode_SRR_ObjectList(CT_SRR_ObjectList_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	uint8_t ObjectChecksum = (userData->ObjectChecksum);
	uint8_t ObjectID = (userData->ObjectID);
	uint16_t ObjectDistLong = (((userData->ObjectDistLong) + 409.6) / 0.05);
	uint16_t ObjectDistLat = (((userData->ObjectDistLat) + 102.4) / 0.05);
	uint16_t ObjectVrelLong = (((userData->ObjectVrelLong) + 163.84) / 0.04);
	uint8_t ObjectClass = (userData->ObjectClass);
	uint8_t ObjectDistLongRms = ((userData->ObjectDistLongRms) / 0.375);
	uint16_t ObjectVrelLat = (((userData->ObjectVrelLat) + 163.84) / 0.04);
	uint16_t ObjectArelLong = (((userData->ObjectArelLong) + 10.24) / 0.01);
	uint16_t ObjectArelLat = (((userData->ObjectArelLat) + 10.24) / 0.01);
	uint8_t ObjectLength = ((userData->ObjectLength) / 0.2);
	uint8_t ObjectWidth = ((userData->ObjectWidth) / 0.2);
	uint8_t ObjectMeasState = (userData->ObjectMeasState);
	uint16_t ObjectRCS = (((userData->ObjectRCS) + 128) / 0.5);
	uint8_t ObjectDynProp = (userData->ObjectDynProp);
	uint8_t ObjectHeight = ((userData->ObjectHeight) / 0.05);
	uint8_t ObjectDistLatRms = ((userData->ObjectDistLatRms) / 0.375);
	uint8_t ObjectVrelLongRms = ((userData->ObjectVrelLongRms) / 0.375);
	uint8_t ObjectVrelLatRms = ((userData->ObjectVrelLatRms) / 0.375);
	uint8_t ObjectArelLongRms = ((userData->ObjectArelLongRms) / 0.375);
	uint8_t ObjectArelLatRms = ((userData->ObjectArelLatRms) / 0.375);
	uint16_t ObjectOrientationAngle = (((userData->ObjectOrientationAngle) + 180) / 0.4);
	uint8_t ObjectProbOfExist = ((userData->ObjectProbOfExist) / 3.2258);
	uint8_t ObjectDistAltitude = (((userData->ObjectDistAltitude) + 1.75) / 0.05);
	uint8_t ObjectRollingCnt = (userData->ObjectRollingCnt);

	data[0] = (userData->ObjectChecksum & 0xFFU);
	data[1] = (userData->ObjectID & 0xFFU);
	data[2] = ((ObjectDistLong & 0x3FC0U) >> 6);
	data[3] = (((ObjectDistLong & 0x3FU) << 2) | ((ObjectDistLat & 0xC00U) >> 10));
	data[4] = ((ObjectDistLat & 0x3FCU) >> 2);
	data[5] = (((ObjectDistLat & 0x3U) << 6) | ((ObjectVrelLong & 0x1F80U) >> 7));
	data[6] = (((ObjectVrelLong & 0x7FU) << 1) | ((userData->ObjectClass & 0x8U) >> 3));
	data[7] = (((userData->ObjectClass & 0x7U) << 5) | (ObjectDistLongRms & 0x1FU));
	data[8] = ((ObjectVrelLat & 0x1FE0U) >> 5);
	data[9] = (((ObjectVrelLat & 0x1FU) << 3) | ((ObjectArelLong & 0x700U) >> 8));
	data[10] = ((ObjectArelLong & 0xFFU));
	data[11] = ((ObjectArelLat & 0x7F8U) >> 3);
	data[12] = (((ObjectArelLat & 0x7U) << 5) | ((ObjectLength & 0xF8U) >> 3));
	data[13] = (((ObjectLength & 0x7U) << 5) | ((ObjectWidth & 0xF8U) >> 3));
	data[14] = (((ObjectWidth & 0x7U) << 5) | ((userData->ObjectMeasState & 0x7U) << 1) | ((ObjectRCS & 0x100U) >> 8));
	data[15] = ((ObjectRCS & 0xFFU));
	data[16] = (((userData->ObjectDynProp & 0xFU) << 4) | ((ObjectHeight & 0xF0U) >> 4));
	data[17] = (((ObjectHeight & 0xFU) << 4) | ((ObjectDistLatRms & 0x1EU) >> 1));
	data[18] = (((ObjectDistLatRms & 0x1U) << 7) | ((ObjectVrelLongRms & 0x1FU) << 2) | ((ObjectVrelLatRms & 0x18U) >> 3));
	data[19] = (((ObjectVrelLatRms & 0x7U) << 5) | (ObjectArelLongRms & 0x1FU));
	data[20] = (((ObjectArelLatRms & 0x1FU) << 3) | ((ObjectOrientationAngle & 0x380U) >> 7));
	data[21] = ((ObjectOrientationAngle & 0x7FU) << 1);
	data[22] = (((ObjectProbOfExist & 0x1FU) << 3) | ((ObjectDistAltitude & 0xE0U) >> 5));
	data[23] = (((ObjectDistAltitude & 0x1FU) << 3) | (userData->ObjectRollingCnt & 0x7U));

	return true;
}
// 0x4c0 length = 24
bool decode_SRR_AlarmObjInfo(CT_SRR_AlarmObjInfo_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	userData->DrvFunc_AlarmModule = (data[1]);
	userData->AlarmBsdObjID = (data[2]);
	userData->AlarmLcaObjID = (data[3]);
	userData->AlarmLcaObjTtc = ((data[4]) * 0.02);
	userData->AlarmDowObjID = (data[5]);
	userData->AlarmDowObjTtc = ((data[6]) * 0.02);
	userData->AlarmRcwObjID = (data[7]);
	userData->AlarmRcwObjTtc = ((data[8]) * 0.02);
	userData->AlarmRctaObjID = (data[9]);
	userData->AlarmRctaObjTtc = ((data[10]) * 0.02);
	userData->AlarmRctbObjID = (data[11]);
	userData->AlarmRctbObjTtc = ((data[12]) * 0.02);
	userData->AlarmFctaObjID = (data[13]);
	userData->AlarmFctaObjTtc = ((data[14]) * 0.02);
	userData->AlarmFctbObjID = (data[15]);
	userData->AlarmFctbObjTtc = ((data[16]) * 0.02);
	userData->AlarmBsdLevel = ((data[17] & 0xC0U) >> 6);
	userData->AlarmLcaLevel = ((data[17] & 0x30U) >> 4);
	userData->AlarmDowLevelFront = ((data[17] & 0xCU) >> 2);
	userData->AlarmDowLevelRear = (data[17] & 0x3U);
	userData->AlarmRcwLevel = ((data[18] & 0xC0U) >> 6);
	userData->AlarmRctaLevel = ((data[18] & 0x30U) >> 4);
	userData->AlarmFctaLevel = ((data[18] & 0xCU) >> 2);
	userData->AlarmBsdState = (data[18] & 0x3U);
	userData->AlarmLCAState = ((data[19] & 0xC0U) >> 6);
	userData->AlarmDOWState = ((data[19] & 0x30U) >> 4);
	userData->AlarmRCTAState = ((data[19] & 0xCU) >> 2);
	userData->AlarmRCTBState = (data[19] & 0x3U);
	userData->AlarmFCTAState = ((data[20] & 0xC0U) >> 6);
	userData->AlarmFCTBState = ((data[20] & 0x30U) >> 4);
	userData->AlarmRCWState = ((data[20] & 0xCU) >> 2);

	return true;
}

// 0x4c0 length = 24
bool encode_SRR_AlarmObjInfo(CT_SRR_AlarmObjInfo_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	uint8_t DrvFunc_AlarmModule = (userData->DrvFunc_AlarmModule);
	uint8_t AlarmBsdObjID = (userData->AlarmBsdObjID);
	uint8_t AlarmLcaObjID = (userData->AlarmLcaObjID);
	uint8_t AlarmLcaObjTtc = ((userData->AlarmLcaObjTtc) / 0.02);
	uint8_t AlarmDowObjID = (userData->AlarmDowObjID);
	uint8_t AlarmDowObjTtc = ((userData->AlarmDowObjTtc) / 0.02);
	uint8_t AlarmRcwObjID = (userData->AlarmRcwObjID);
	uint8_t AlarmRcwObjTtc = ((userData->AlarmRcwObjTtc) / 0.02);
	uint8_t AlarmRctaObjID = (userData->AlarmRctaObjID);
	uint8_t AlarmRctaObjTtc = ((userData->AlarmRctaObjTtc) / 0.02);
	uint8_t AlarmRctbObjID = (userData->AlarmRctbObjID);
	uint8_t AlarmRctbObjTtc = ((userData->AlarmRctbObjTtc) / 0.02);
	uint8_t AlarmFctaObjID = (userData->AlarmFctaObjID);
	uint8_t AlarmFctaObjTtc = ((userData->AlarmFctaObjTtc) / 0.02);
	uint8_t AlarmFctbObjID = (userData->AlarmFctbObjID);
	uint8_t AlarmFctbObjTtc = ((userData->AlarmFctbObjTtc) / 0.02);
	uint8_t AlarmBsdLevel = (userData->AlarmBsdLevel);
	uint8_t AlarmLcaLevel = (userData->AlarmLcaLevel);
	uint8_t AlarmDowLevelFront = (userData->AlarmDowLevelFront);
	uint8_t AlarmDowLevelRear = (userData->AlarmDowLevelRear);
	uint8_t AlarmRcwLevel = (userData->AlarmRcwLevel);
	uint8_t AlarmRctaLevel = (userData->AlarmRctaLevel);
	uint8_t AlarmFctaLevel = (userData->AlarmFctaLevel);
	uint8_t AlarmBsdState = (userData->AlarmBsdState);
	uint8_t AlarmLCAState = (userData->AlarmLCAState);
	uint8_t AlarmDOWState = (userData->AlarmDOWState);
	uint8_t AlarmRCTAState = (userData->AlarmRCTAState);
	uint8_t AlarmRCTBState = (userData->AlarmRCTBState);
	uint8_t AlarmFCTAState = (userData->AlarmFCTAState);
	uint8_t AlarmFCTBState = (userData->AlarmFCTBState);
	uint8_t AlarmRCWState = (userData->AlarmRCWState);

	data[0] = 0;
	data[1] = (userData->DrvFunc_AlarmModule & 0xFFU);
	data[2] = (userData->AlarmBsdObjID & 0xFFU);
	data[3] = (userData->AlarmLcaObjID & 0xFFU);
	data[4] = (AlarmLcaObjTtc & 0xFFU);
	data[5] = (userData->AlarmDowObjID & 0xFFU);
	data[6] = (AlarmDowObjTtc & 0xFFU);
	data[7] = (userData->AlarmRcwObjID & 0xFFU);
	data[8] = (AlarmRcwObjTtc & 0xFFU);
	data[9] = (userData->AlarmRctaObjID & 0xFFU);
	data[10] = (AlarmRctaObjTtc & 0xFFU);
	data[11] = (userData->AlarmRctbObjID & 0xFFU);
	data[12] = (AlarmRctbObjTtc & 0xFFU);
	data[13] = (userData->AlarmFctaObjID & 0xFFU);
	data[14] = (AlarmFctaObjTtc & 0xFFU);
	data[15] = (userData->AlarmFctbObjID & 0xFFU);
	data[16] = (AlarmFctbObjTtc & 0xFFU);
	data[17] = (((userData->AlarmBsdLevel & 0x3U) << 6) | ((userData->AlarmLcaLevel & 0x3U) << 4) | ((userData->AlarmDowLevelFront & 0x3U) << 2) | (userData->AlarmDowLevelRear & 0x3U));
	data[18] = (((userData->AlarmRcwLevel & 0x3U) << 6) | ((userData->AlarmRctaLevel & 0x3U) << 4) | ((userData->AlarmFctaLevel & 0x3U) << 2) | (userData->AlarmBsdState & 0x3U));
	data[19] = (((userData->AlarmLCAState & 0x3U) << 6) | ((userData->AlarmDOWState & 0x3U) << 4) | ((userData->AlarmRCTAState & 0x3U) << 2) | (userData->AlarmRCTBState & 0x3U));
	data[20] = (((userData->AlarmFCTAState & 0x3U) << 6) | ((userData->AlarmFCTBState & 0x3U) << 4) | ((userData->AlarmRCWState & 0x3U) << 2));
	data[21] = 0;
	data[22] = 0;
	data[23] = 0;

	return true;
}
// 0x4f0 length = 24
bool decode_SRR_EndFrame(CT_SRR_EndFrame_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	userData->EndFrameCheckSum = (data[0]);
	userData->EndFrameEOLInstallAngle = (((((data[2] & 0xE0U) >> 5) + (((uint16_t)data[1] & 0xFFU) << 3)) * 0.1) - 102.4);
	userData->EndFrameAutoCalAngleOffset = (((((data[3] & 0xFCU) >> 2) + (((uint16_t)data[2] & 0x1FU) << 6)) * 0.1) - 102.4);
	userData->EndFrameInterTime = (data[4]);
	userData->EndFrameFuncCalcTime = (((data[6] & 0x80U) >> 7) + (((uint16_t)data[5] & 0xFFU) << 1));
	userData->EndFrameRoadSideDist = (((((data[7] & 0xF0U) >> 4) + (((uint16_t)data[6] & 0x3FU) << 4)) * 0.05) - 25);
	userData->EndFrameRollingCnt = (data[7] & 0xFU);
	userData->EndFrameTimeTick = ((data[11] & 0xFFU) + (((uint32_t)data[10]) << 8) + (((uint32_t)data[9]) << 16) + (((uint32_t)data[8] & 0xFFU) << 24));
	userData->EndFrameMeasCnt = ((data[15] & 0xFFU) + (((uint32_t)data[14]) << 8) + (((uint32_t)data[13]) << 16) + (((uint32_t)data[12] & 0xFFU) << 24));

	return true;
}

// 0x4f0 length = 24
bool encode_SRR_EndFrame(CT_SRR_EndFrame_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	uint8_t EndFrameCheckSum = (userData->EndFrameCheckSum);
	uint16_t EndFrameEOLInstallAngle = (((userData->EndFrameEOLInstallAngle) + 102.4) / 0.1);
	uint16_t EndFrameAutoCalAngleOffset = (((userData->EndFrameAutoCalAngleOffset) + 102.4) / 0.1);
	uint8_t EndFrameInterTime = (userData->EndFrameInterTime);
	uint16_t EndFrameFuncCalcTime = (userData->EndFrameFuncCalcTime);
	uint16_t EndFrameRoadSideDist = (((userData->EndFrameRoadSideDist) + 25) / 0.05);
	uint8_t EndFrameRollingCnt = (userData->EndFrameRollingCnt);
	uint32_t EndFrameTimeTick = (userData->EndFrameTimeTick);
	uint32_t EndFrameMeasCnt = (userData->EndFrameMeasCnt);

	data[0] = (userData->EndFrameCheckSum & 0xFFU);
	data[1] = ((EndFrameEOLInstallAngle & 0x7F8U) >> 3);
	data[2] = (((EndFrameEOLInstallAngle & 0x7U) << 5) | ((EndFrameAutoCalAngleOffset & 0x7C0U) >> 6));
	data[3] = ((EndFrameAutoCalAngleOffset & 0x3FU) << 2);
	data[4] = (userData->EndFrameInterTime & 0xFFU);
	data[5] = ((userData->EndFrameFuncCalcTime & 0x1FEU) >> 1);
	data[6] = (((userData->EndFrameFuncCalcTime & 0x1U) << 7) | ((EndFrameRoadSideDist & 0x3F0U) >> 4));
	data[7] = (((EndFrameRoadSideDist & 0xFU) << 4) | (userData->EndFrameRollingCnt & 0xFU));
	data[8] = ((userData->EndFrameTimeTick & 0xFF000000U) >> 24);
	data[9] = ((userData->EndFrameTimeTick & 0xFF0000U) >> 16);
	data[10] = ((userData->EndFrameTimeTick & 0xFF00U) >> 8);
	data[11] = ((userData->EndFrameTimeTick & 0xFFU));
	data[12] = ((userData->EndFrameMeasCnt & 0xFF000000U) >> 24);
	data[13] = ((userData->EndFrameMeasCnt & 0xFF0000U) >> 16);
	data[14] = ((userData->EndFrameMeasCnt & 0xFF00U) >> 8);
	data[15] = ((userData->EndFrameMeasCnt & 0xFFU));
	data[16] = 0;
	data[17] = 0;
	data[18] = 0;
	data[19] = 0;
	data[20] = 0;
	data[21] = 0;
	data[22] = 0;
	data[23] = 0;

	return true;
}

bool proc_CTMRR410(CTMRR410_t *userData, Frame *p) {
	bool ret = false;

	switch (p->id) {
	case MSG_ID3f0_SRR_VEHICLEINFO:
		ret = decode_SRR_VehicleInfo(&userData->_SRR_VehicleInfo, p->data, p->dlc);
		break;
	case MSG_ID400_SRR_RAWHEADER:
		ret = decode_SRR_RawHeader(&userData->_SRR_RawHeader, p->data, p->dlc);
		break;
	case MSG_ID410_SRR_RAWDETECTIONS:
		ret = decode_SRR_RawDetections(&userData->_SRR_RawDetections, p->data, p->dlc);
		break;
	case MSG_ID430_SRR_OBJECTHEADER:
		ret = decode_SRR_ObjectHeader(&userData->_SRR_ObjectHeader, p->data, p->dlc);
		break;
	case MSG_ID440_SRR_OBJECTLIST:
		ret = decode_SRR_ObjectList(&userData->_SRR_ObjectList, p->data, p->dlc);
		break;
	case MSG_ID4c0_SRR_ALARMOBJINFO:
		ret = decode_SRR_AlarmObjInfo(&userData->_SRR_AlarmObjInfo, p->data, p->dlc);
		break;
	case MSG_ID4f0_SRR_ENDFRAME:
		ret = decode_SRR_EndFrame(&userData->_SRR_EndFrame, p->data, p->dlc);
		break;
	default:
		return false;
		break;
	}

	return ret;
}


