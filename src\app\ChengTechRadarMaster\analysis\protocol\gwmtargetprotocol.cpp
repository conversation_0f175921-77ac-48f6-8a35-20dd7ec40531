﻿#include "gwmtargetprotocol.h"

namespace Analysis {
namespace Protocol {


GWMTargetProtocol::GWMTargetProtocol( AnalysisWorker *analysisWorker, QObject *parent )
    : IAnalysisProtocol( analysisWorker, parent )
{

}

bool GWMTargetProtocol::analysisFrame(const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    bool ret = false;
    switch (frame.id())
    {
    case 0x16F:
        ret = GWMElkTargetParse( frame );
        break;
    case 0x30A:
        ret = GWMLaneTargetParse( frame );
        break;
    case 0x5CE:
        ret = GWM16TargetHeaderParse(4, frame);
        break;
    case 0x580:
        ret = GWM16TargetClear(4);
    case 0x581:
    case 0x582:
    case 0x583:
    case 0x584:
    case 0x585:
    case 0x586:
    case 0x587:
        ret = GWM16TargetParse(4, frame);
        break;
    case 0x5CF:
        ret = GWM16TargetHeaderParse(5, frame);
        break;
    case 0x590:
        ret = GWM16TargetClear(5);
    case 0x591:
    case 0x592:
    case 0x593:
    case 0x594:
    case 0x595:
    case 0x596:
    case 0x597:
        ret = GWM16TargetParse(5, frame);
        break;
    default:
        return false;
    }
    return ret;
}

bool GWMTargetProtocol::GWM16TargetClear(quint8 radarID)
{
    mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.clear();

    return true;
}

bool GWMTargetProtocol::GWM16TargetHeaderParse(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 32) {
        return false;
    }

    const uint8_t *data = (const uint8_t *)frame.data().data();

//    userData->RSDS_L_SGU_HEAD_Checksum1 = (data[0]);
//    userData-> = ((data[0]) * 0);
//    userData-> = ((data[0]) * 0);
//    userData->RSDS_L_hdr_TimeStamp = (data[1]);
//    userData->RSDS_L_hdr_TimeStampStatus = (data[2]);
//    userData->RSDS_L_BLIND = ((data[3] & 0xC0U) >> 6);
    mGWM16TargetCount[radarID] = (data[3] & 0x3FU);
    mGWM16TargetCurrentIndex[radarID] = 0;
//    userData->RSDS_L_GuardrailConfidence = (((data[4] & 0xFEU) >> 1) * 0.01);
//    userData->RSDS_L_Guardrail_dxEnd = (((((data[6] & 0xFEU) >> 1) + (((uint16_t)data[5] & 0xFFU) << 7)) * 0.01) - 160);
//    userData->RSDS_L_hdr_ErrorStatus = ((data[7] & 0xF0U) >> 4);
//    userData->RSDS_L_SGU_HEAD_RollingCounter1 = (data[7] & 0xFU);
//    userData->RSDS_L_SGU_HEAD_Checksum2 = (data[8]);
//    userData->RSDS_L_hdr_AutosarTimeStamp = (((data[14] & 0xC0U) >> 6) + (((uint64_t)data[13]) << 2) + (((uint64_t)data[12]) << 10) + (((uint64_t)data[11]) << 18) + (((uint64_t)data[10]) << 26) + (((uint64_t)data[9] & 0xFFU) << 34));
//    userData->RSDS_L_SGU_HEAD_RollingCounter2 = (data[15] & 0xFU);
//    userData->RSDS_L_SGU_HEAD_Checksum3 = (data[16]);
//    userData->RSDS_L_GuardrailC0 = (((((data[18] & 0xF8U) >> 3) + (((uint16_t)data[17] & 0xFFU) << 5)) * 0.01) - 30);
//    userData->RSDS_L_GuardrailC1 = (((((data[20] & 0xF8U) >> 3) + (((uint16_t)data[19] & 0xFFU) << 5)) * 0.0009) - 2);
//    userData->RSDS_L_SGU_HEAD_RollingCounter3 = (data[23] & 0xFU);
//    userData->RSDS_L_SGU_HEAD_Checksum4 = (data[24]);
//    userData->RSDS_L_Guardrail_dxStart = (((((data[29] & 0xE0U) >> 5) + (((uint32_t)data[28]) << 3) + (((uint32_t)data[27] & 0xFU) << 11)) * 0.01) - 160);
//    userData->RSDS_L_ObjAziAngleCalib = ((((data[30] & 0xFFU) + (((uint16_t)data[29] & 0x1FU) << 8)) * 0.0009) - 2);
//    userData->RSDS_L_SGU_HEAD_BlockCounter = ((data[31] & 0xF0U) >> 4);
//    userData->RSDS_L_SGU_HEAD_RollingCounter4 = (data[31] & 0xFU);

//    qDebug() << __FUNCTION__ << __LINE__ << radarID << mGWM16TargetCount[radarID];

    return true;
}

bool GWMTargetProtocol::GWM16TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
            return false;
        }

    const uint8_t *data = (const uint8_t *)frame.data().data();
    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount; // 注意必须使用引用
    if (mGWM16TargetCurrentIndex[radarID]++ < mGWM16TargetCount[radarID]) {
    Target &target1 = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets[targetCount++];
        target1.mChecksum = (data[0]);
        target1.mID = (data[1]);
        target1.mExistProbability = (((data[2] & 0xF8U) >> 3) * 0.03326);
        target1.mObstacleProbability = ((((data[3] & 0xC0U) >> 6) + (((uint16_t)data[2] & 0x7U) << 2)) * 0.03326);
        target1.mY = (((((data[4] & 0xFCU) >> 2) + (((uint16_t)data[3] & 0x3FU) << 6)) * 0.0625) - 128);
        target1.mX = (((((data[6] & 0x80U) >> 7) + (((uint32_t)data[5]) << 1) + (((uint32_t)data[4] & 0x3U) << 9)) * 0.125) - 128);
        target1.mYStd = (((data[6] & 0x7EU) >> 1) * 0.1);
        target1.mObjectType = ((data[7] & 0xF0U) >> 4);
//        target1.RSDS_L_SGU1_RollingCounter1 = (data[7] & 0xFU);
//        target1.RSDS_L_SGU1_Checksum2 = (data[8]);
        target1.mVy = (((((data[10] & 0xF8U) >> 3) + (((uint16_t)data[9] & 0xFFU) << 5)) * 0.0312) - 128);
        target1.mVx = (((((data[12] & 0xC0U) >> 6) + (((uint32_t)data[11]) << 2) + (((uint32_t)data[10] & 0x7U) << 10)) * 0.0312) - 128);
        target1.mAy = (((((data[13] & 0xF0U) >> 4) + (((uint16_t)data[12] & 0x3FU) << 4)) * 0.03125) - 16);
        target1.mAx = (((((data[14] & 0xFCU) >> 2) + (((uint16_t)data[13] & 0xFU) << 6)) * 0.03125) - 16);
//        target1.RSDS_L_ObjMeasured_1 = (data[14] & 0x3U);
//        target1.RSDS_L_ObjMotionType_1 = ((data[15] & 0xE0U) >> 5);
//        target1.RSDS_L_SGU1_RollingCounter2 = (data[15] & 0xFU);
//        target1.RSDS_L_SGU1_Checksum3 = (data[16]);
        target1.mXStd = (((data[17] & 0xFCU) >> 2) * 0.1);
        target1.mVyStd = ((((data[18] & 0xF0U) >> 4) + (((uint16_t)data[17] & 0x3U) << 4)) * 0.0625);
        target1.mVxStd = ((((data[19] & 0xC0U) >> 6) + (((uint16_t)data[18] & 0xFU) << 2)) * 0.0625);
        target1.mAyStd = ((data[19] & 0x3FU) * 0.0625);
        target1.mAxStd = (((data[20] & 0xFCU) >> 2) * 0.0625);
        target1.mTrackFrameWidth = (((data[21] & 0xFFU) + (((uint16_t)data[20] & 0x3U) << 8)) * 0.01);
//        target1.RSDS_L_ObjAge_1 = (data[22]);
//        target1.RSDS_L_ObjMotionDirection_1 = ((data[23] & 0xE0U) >> 5);
//        target1.RSDS_L_SGU1_RollingCounter3 = (data[23] & 0xFU);
//        target1.RSDS_L_SGU1_Checksum4 = (data[24]);
        target1.mTrackFrameLength = ((((data[26] & 0xC0U) >> 6) + (((uint16_t)data[25] & 0xFFU) << 2)) * 0.05);
//        target1.RSDS_L_ObjHeading_1 = (((((data[27] & 0xFCU) >> 2) + (((uint16_t)data[26] & 0x3FU) << 6)) * 0.002) - 3.14);
//        target1.RSDS_L_ObjYawrate_1 = (((((data[29] & 0xC0U) >> 6) + (((uint32_t)data[28]) << 2) + (((uint32_t)data[27] & 0x3U) << 10)) * 0.002) - 3.14);
        target1.mRCS = (((((data[30] & 0xF0U) >> 4) + (((uint16_t)data[29] & 0x3FU) << 4)) * 0.125) - 50);
//        target1.RSDS_L_ObjRefPoint_1 = ((data[30] & 0xEU) >> 1);
//        target1.RSDS_L_HWATargetValidity_1 = (data[30] & 0x1U);
        target1.mRollingCount = (data[31] & 0xFU);

        target1.mValid = (target1.mX != 0 || target1.mY != 0);

//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
//        qDebug() << __FUNCTION__ << __LINE__ << radarID << target1.mID << target1.mX << target1.mY;
    }

    if (mGWM16TargetCurrentIndex[radarID]++ < mGWM16TargetCount[radarID]) {
        Target &target2 = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets[targetCount++];
        target2.mChecksum = (data[32]);
        target2.mID = (data[33]);
        target2.mExistProbability = (((data[34] & 0xF8U) >> 3) * 0.03326);
        target2.mObstacleProbability = ((((data[35] & 0xC0U) >> 6) + (((uint16_t)data[34] & 0x7U) << 2)) * 0.03326);
        target2.mY = (((((data[36] & 0xFCU) >> 2) + (((uint16_t)data[35] & 0x3FU) << 6)) * 0.0625) - 128);
        target2.mX = (((((data[38] & 0x80U) >> 7) + (((uint32_t)data[37]) << 1) + (((uint32_t)data[36] & 0x3U) << 9)) * 0.125) - 128);
        target2.mYStd = (((data[38] & 0x7EU) >> 1) * 0.1);
        target2.mObjectType = ((data[39] & 0xF0U) >> 4);
//        target2.RSDS_L_SGU1_RollingCounter5 = (data[39] & 0xFU);
//        target2.RSDS_L_SGU1_Checksum6 = (data[40]);
        target2.mVy = (((((data[42] & 0xF8U) >> 3) + (((uint16_t)data[41] & 0xFFU) << 5)) * 0.0312) - 128);
        target2.mVx = (((((data[44] & 0xC0U) >> 6) + (((uint32_t)data[43]) << 2) + (((uint32_t)data[42] & 0x7U) << 10)) * 0.0312) - 128);
        target2.mAy = (((((data[45] & 0xF0U) >> 4) + (((uint16_t)data[44] & 0x3FU) << 4)) * 0.03125) - 16);
        target2.mAx = (((((data[46] & 0xFCU) >> 2) + (((uint16_t)data[45] & 0xFU) << 6)) * 0.03125) - 16);
//        target2.RSDS_L_ObjMeasured_2 = (data[46] & 0x3U);
//        target2.RSDS_L_ObjMotionType_2 = ((data[47] & 0xE0U) >> 5);
//        target2.RSDS_L_SGU1_RollingCounter6 = (data[47] & 0xFU);
//        target2.RSDS_L_SGU1_Checksum7 = (data[48]);
        target2.mXStd = (((data[49] & 0xFCU) >> 2) * 0.1);
        target2.mVyStd = ((((data[50] & 0xF0U) >> 4) + (((uint16_t)data[49] & 0x3U) << 4)) * 0.0625);
        target2.mVxStd = ((((data[51] & 0xC0U) >> 6) + (((uint16_t)data[50] & 0xFU) << 2)) * 0.0625);
        target2.mAyStd = ((data[51] & 0x3FU) * 0.0625);
        target2.mAxStd = (((data[52] & 0xFCU) >> 2) * 0.0625);
        target2.mTrackFrameWidth = (((data[53] & 0xFFU) + (((uint16_t)data[52] & 0x3U) << 8)) * 0.01);
//        target2.RSDS_L_ObjAge_2 = (data[54]);
//        target2.RSDS_L_ObjMotionDirection_2 = ((data[55] & 0xE0U) >> 5);
//        target2.RSDS_L_SGU1_RollingCounter7 = (data[55] & 0xFU);
//        target2.RSDS_L_SGU1_Checksum8 = (data[56]);
        target2.mTrackFrameLength = ((((data[58] & 0xC0U) >> 6) + (((uint16_t)data[57] & 0xFFU) << 2)) * 0.05);
//        target2.RSDS_L_ObjHeading_2 = (((((data[59] & 0xFCU) >> 2) + (((uint16_t)data[58] & 0x3FU) << 6)) * 0.002) - 3.14);
//        target2.RSDS_L_ObjYawrate_2 = (((((data[61] & 0xC0U) >> 6) + (((uint32_t)data[60]) << 2) + (((uint32_t)data[59] & 0x3U) << 10)) * 0.002) - 3.14);
        target2.mRCS = (((((data[62] & 0xF0U) >> 4) + (((uint16_t)data[61] & 0x3FU) << 4)) * 0.125) - 50);
//        target2.RSDS_L_ObjRefPoint_2 = ((data[62] & 0xEU) >> 1);
//        target2.RSDS_L_HWATargetValidity_2 = (data[62] & 0x1U);
//        target2.RSDS_L_SGU1_BlockCounter = ((data[63] & 0xF0U) >> 4);
        target2.mRollingCount = (data[63] & 0xFU);

        target2.mValid = (target2.mX != 0 || target2.mY != 0);

//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
//        qDebug() << __FUNCTION__ << __LINE__ << radarID << target2.mID << target2.mX << target2.mY;
    }

    bool bEndFrame = false;
    if (frame.id() == 0x587 || frame.id() == 0x597) {
        mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mValid = true;
        mAnalysisWorker->analysisEnd(radarID, Frame16Track);
        bEndFrame = true;
    }

    mAnalysisWorker->analysisTargetPoint16FrameEnd( radarID, frame, bEndFrame );
    return true;
}

bool GWMTargetProtocol::GWMElkTargetParse(const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    quint8* data = (quint8*)frame.data().data();
    if (frame.data().length() != 64) {
        return false;
    }
    mAnalysisWorker->mAnalysisDatas[4].mELKTargets.clear();
    mAnalysisWorker->mAnalysisDatas[5].mELKTargets.clear();
    mAnalysisWorker->mAnalysisDatas[6].mELKTargets.clear();
    mAnalysisWorker->mAnalysisDatas[7].mELKTargets.clear();

    Target& targetLeft = mAnalysisWorker->mAnalysisDatas[5].mELKTargets.mTargets[0];
    Target& targetRight = mAnalysisWorker->mAnalysisDatas[5].mELKTargets.mTargets[1];

    targetLeft.mValid = ((data[17] & 0xC0U) >> 6) != 0x2;
    targetRight.mValid = ((data[33] & 0xC0U) >> 6) != 0x2;
    /*if( targetLeft.mValid )*/{
        targetLeft.mID = (data[17] & 0x3FU);
        targetLeft.mX = (((((data[20] & 0xC0U) >> 6) + (((uint16_t)data[19] & 0x3FU) << 2)) * 0.1) - 12.8);
        targetLeft.mY = (((((data[19] & 0xC0U) >> 6) + (((uint16_t)data[18] & 0xFFU) << 2)) * 0.1) - 92.3);
        targetLeft.mVx = (((((data[22] & 0xE0U) >> 5) + (((uint16_t)data[21] & 0xFU) << 3)) * 0.1) - 6.4);
        targetLeft.mVy = (((((data[21] & 0xF0U) >> 4) + (((uint16_t)data[20] & 0x3FU) << 4)) * 0.1) - 10);
        targetLeft.mAx = (((((data[52] & 0xC0U) >> 6) + (((uint16_t)data[51] & 0xFFU) << 2)) * 0.03125) - 16);
        targetLeft.mAy = (((((data[2] & 0xC0U) >> 6) + (((uint16_t)data[1] & 0xFFU) << 2)) * 0.03125) - 16);
        targetLeft.mTrackFrameLength = ((data[25] & 0xF0U) >> 4);
        targetLeft.mTrackFrameWidth = (((data[25] & 0xEU) >> 1) * 0.5);
        targetLeft.mDynamicProperty = (data[25] & 0x1U);
    }

    targetLeft.mValid = (targetLeft.mX != 0 && targetLeft.mY != 0);

    /*if( targetRight.mValid )*/{
        targetRight.mID = (data[33] & 0x3FU);
        targetRight.mX = (((((data[36] & 0xC0U) >> 6) + (((uint16_t)data[35] & 0x3FU) << 2)) * 0.1) - 12.8);
        targetRight.mY = (((((data[35] & 0xC0U) >> 6) + (((uint16_t)data[34] & 0xFFU) << 2)) * 0.1) - 92.3);
        targetRight.mVx = (((((data[38] & 0xE0U) >> 5) + (((uint16_t)data[37] & 0xFU) << 3)) * 0.1) - 6.4);
        targetRight.mVy = (((((data[37] & 0xF0U) >> 4) + (((uint16_t)data[36] & 0x3FU) << 4)) * 0.1) - 10);;
        targetRight.mAx = (((((data[53] & 0xF0U) >> 4) + (((uint16_t)data[52] & 0x3FU) << 4)) * 0.03125) - 16);
        targetRight.mAy = (((((data[54] & 0xFCU) >> 2) + (((uint16_t)data[53] & 0xFU) << 6)) * 0.03125) - 16);
        targetRight.mTrackFrameLength = (data[26] & 0xFU);
        targetRight.mTrackFrameWidth = (((data[27] & 0xE0U) >> 5) * 0.5);
        targetRight.mDynamicProperty = ((data[27] & 0x10U) >> 4);
    }

    targetRight.mValid = (targetRight.mX != 0 && targetRight.mY != 0);

    mAnalysisWorker->mAnalysisDatas[5].mELKTargets.mTargetCount = 2;

    mAnalysisWorker->mAnalysisDatas[5].mELKTargets.mTargetHeader.mMeasurementCount = 0;
    mAnalysisWorker->mAnalysisDatas[5].mELKTargets.mValid = true;
    mAnalysisWorker->analysisEnd(5, FrameELKTrack);

//    if (targetLeft.mValid || targetRight.mValid) {
//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
//        qDebug() << __FUNCTION__ << __LINE__ << targetLeft.mValid << targetLeft.mID << targetLeft.mX << targetLeft.mY;
//        qDebug() << __FUNCTION__ << __LINE__ << targetRight.mValid << targetRight.mID << targetRight.mX << targetRight.mY;
//    }

    return true;
}

bool GWMTargetProtocol::GWMLaneTargetParse(const Devices::Can::CanFrame &frame)
{
    quint8* data = (quint8*)frame.data().data();
    if (frame.data().length() != 64) {
        return false;
    }
    mAnalysisWorker->mAnalysisDatas[4].mLockTargets.clear();
    mAnalysisWorker->mAnalysisDatas[5].mLockTargets.clear();
    mAnalysisWorker->mAnalysisDatas[6].mLockTargets.clear();
    mAnalysisWorker->mAnalysisDatas[7].mLockTargets.clear();

    Target& targetLeft1 = mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[0];
    Target& targetLeft2 = mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[1];
    Target& targetMid1 = mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[2];
    Target& targetMid2 = mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[3];
    Target& targetRight1 = mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[4];
    Target& targetRight2 = mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[5];

    targetLeft1.mValid = ((data[9] & 0x80U) >> 7);
    if( targetLeft1.mValid ){
        targetLeft1.mID = 0;
        targetLeft1.mY = (((((data[11] & 0xC0U) >> 6) + (((uint16_t)data[10] & 0xFFU) << 2)) * 0.1) - 90);
        targetLeft1.mX = ((data[9] & 0x7FU) * 0.1);
    }
    targetLeft2.mValid = ((data[11] & 0x20U) >> 5);
    if( targetLeft2.mValid ){
        targetLeft2.mID = 1;
        targetLeft2.mY = (((((data[12] & 0xF8U) >> 3) + (((uint16_t)data[11] & 0x1FU) << 5)) * 0.1) - 90);
        targetLeft2.mX = ((((data[13] & 0xF0U) >> 4) + (((uint16_t)data[12] & 0x7U) << 4)) * 0.1);
    }
    targetMid1.mValid = ((data[20] & 0x40U) >> 6);
    if( targetMid1.mValid ){
        targetMid1.mID = 2;
        targetMid1.mY = (((((data[21] & 0xF0U) >> 4) + (((uint16_t)data[20] & 0x3FU) << 4)) * 0.1) - 90);
        targetMid1.mX = (((((data[22] & 0xE0U) >> 5) + (((uint16_t)data[21] & 0xFU) << 3)) * 0.1) - 4);
    }
    targetMid2.mValid = ((data[22] & 0x10U) >> 4);
    if( targetMid2.mValid ){
        targetMid2.mID = 3;
        targetMid2.mY = (((((data[26] & 0xC0U) >> 6) + (((uint16_t)data[25] & 0xFFU) << 2)) * 0.1) - 90);
        targetMid2.mX = (((((data[27] & 0x80U) >> 7) + (((uint16_t)data[26] & 0x3FU) << 1)) * 0.1) - 4);
    }
    targetRight1.mValid = ((data[13] & 0x8U) >> 3);
    if( targetRight1.mValid ){
        targetRight1.mID = 4;
        targetRight1.mY = (((((data[14] & 0xFEU) >> 1) + (((uint16_t)data[13] & 0x7U) << 7)) * 0.1) - 90);
        targetRight1.mX = ((((data[17] & 0xFEU) >> 1) * 0.1) - 8);
    }
    targetRight2.mValid = (data[17] & 0x1U);
    if( targetRight2.mValid ){
        targetRight2.mID = 5;
        targetRight2.mY = (((((data[19] & 0xC0U) >> 6) + (((uint16_t)data[18] & 0xFFU) << 2)) * 0.1) - 90);
        targetRight2.mX = (((((data[20] & 0x80U) >> 7) + (((uint16_t)data[19] & 0x3FU) << 1)) * 0.1) - 8);
    }

    mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargetCount = 6;

    mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargetHeader.mMeasurementCount = 0;
    mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mValid = true;
    mAnalysisWorker->analysisEnd(5, Frame3Track);
#if 0
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    bool ok = false;
    for (int i = 0; i < 6; ++i) {
        if (mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[i].mValid) {
            ok = true;
            qDebug() << __FUNCTION__ << __LINE__ << i
                     << mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[i].mID
                     << mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[i].mValid
                     << mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[i].mX
                     << mAnalysisWorker->mAnalysisDatas[5].mLockTargets.mTargets[i].mY;
        }
    }
    if (ok) {
        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    }
#endif

    return true;
}

}
}
