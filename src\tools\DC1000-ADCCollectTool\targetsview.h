﻿
#ifndef TARGETSVIEW_H
#define TARGETSVIEW_H

#include <QWidget>
#include <QPixmap>
#include <QMutex>

#include "analysisdata.h"

class TargetsView : public QWidget
{
    Q_OBJECT
public:
    typedef struct {
        bool mDisplay{true};
        uint32_t mColor{0xFFFFFFFF}; ///< RGBA
        const char *mName;
    }PointDisplay;
    typedef struct ViewSettings
    {
        enum DisplayType {
            DisplayTargetRaw                   ,   ///< 静止原始点
            DisplayTargetRawMoved              ,   ///< 运动原始点
            DisplayTargetTrack                 ,   ///< 静止跟踪点
            DisplayTargetTrackMoved            ,   ///< 运动跟踪点
            DisplayIDLabel                     ,   ///< ID标签
            DisplayHighlightedVelocityAmbiguity,   ///< 凸显解模糊
            DisplayCount
        };

        float mXmin{-20.0f};
        float mXmax{20.0f};
        float mXInterval{5.0f};         ///< 间隔(m)

        float mYmin{-10.0f};
        float mYmax{250.0f};
        float mYInterval{50.0f};         ///< 间隔(m)

        float mLocalVehicleLengthHalf{5.0f / 2};    ///< 自车长度(m)
        float mLocalVehicleWidthHalf{1.80f / 2};

        float mPointSizeRaw{3.0f};
        float mPointSizeRawHalf{1.5f};
        float mPointSizeTrack{15.0f};
        float mPointSizeTrackHalf{7.5f};

        bool mDisplayIDLabelRaw{false};
        bool mDisplayIDLabelTrack{true};

        PointDisplay mPointDisplay[DisplayCount]{
            {true, 0x808080B4, "静止原始点"},     ///< 静止原始点
            {true, 0x808080FF, "静止跟踪点"},     ///< 静止跟踪点
            {false, 0xFFFFFFFF, "ID标签"},     ///< ID标签
            {true,  0xFFFFFFFF, "凸显解模糊"},  ///< 凸显解模糊
        };

        QVariant getSettings() const;
        void setSettings(const QVariant &settings);
    }ViewSettings;

    explicit TargetsView(QWidget *parent = nullptr);

    void config();

    QVariant getSettings() const;
    void setSettings(const QVariant &settings);

signals:

public slots:
    void targets(AnalysisData data);

private slots:
    void changeSettings(const ViewSettings &settings);

protected:
    void drawBackground();
    void resizeEvent(QResizeEvent *event) override;
    void paintEvent(QPaintEvent *event) override;

    ViewSettings mViewSettings;
    QPointF mZeroPointF;
    float mXPixelPerMetre{1.0f};    ///< 像素比列(pix/m),一个米需要多少个像素点
    float mYPixelPerMetre{1.0f};    ///< 像素比列(pix/m),一个米需要多少个像素点

    QMutex mMutex;
    QPixmap mPixmapBackground;
    QPixmap mPixmapCar;

    AnalysisData mAnalysisData;

};

#endif // TARGETSVIEW_H
