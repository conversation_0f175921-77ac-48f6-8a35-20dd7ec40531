﻿#include "radarresetworkerform.h"
#include "ui_radarresetworkerform.h"

#include "radarresetworker.h"

#include "devices/devicemanager.h"

#include <QScrollBar>
#include <QThread>

namespace Functions {

RadarResetWorkerForm::RadarResetWorkerForm(Devices::Can::DeviceManager *deviceManager, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::RadarResetWorkerForm)
{
    ui->setupUi(this);

    ui->pushButtonStopCalibration->hide();
    ui->pushButtonCalibrationResult->hide();

    mRadarResetWorkerWorker = new RadarResetWorkerWorker(deviceManager);
    QThread *thread = new QThread;

    mRadarResetWorkerWorker->moveToThread(thread);

    connect(mRadarResetWorkerWorker, &RadarResetWorkerWorker::message, this, &RadarResetWorkerForm::message);
    connect(mRadarResetWorkerWorker, &RadarResetWorkerWorker::calibrationFinished, this, &RadarResetWorkerForm::calibrationFinished);
    connect(mRadarResetWorkerWorker, &RadarResetWorkerWorker::calibrationStarted, this, &RadarResetWorkerForm::calibrationStarted);
    connect(mRadarResetWorkerWorker, &RadarResetWorkerWorker::sendOrRecvCanFrame, this, &RadarResetWorkerForm::sendOrRecvCanFrame);

    connect(this, &RadarResetWorkerForm::destroyed, thread, &QThread::quit);
    connect(thread, &QThread::finished, &RadarResetWorkerWorker::deleteLater);
    connect(thread, &QThread::finished, thread, &QThread::deleteLater);


    connect(deviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameRecieved , mRadarResetWorkerWorker, &RadarResetWorkerWorker::canFrame/*, Qt::DirectConnection*/ );
    connect(deviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameTransmited , mRadarResetWorkerWorker, &RadarResetWorkerWorker::frameTransmited);


    for (int i = 0; i < 4; ++i)
    {
        connect(&mTimerResult[i], &QTimer::timeout, this, [=](){
            mRadarResetWorkerWorker->readResult(i);
        });
    }

    thread->start();
}

RadarResetWorkerForm::~RadarResetWorkerForm()
{
    delete ui;
}

void RadarResetWorkerForm::message(int index, const QString &msg)
{
    switch (index) {
    case 0:
        ui->plainTextEditRadar4->appendPlainText(msg);
        ui->plainTextEditRadar4->verticalScrollBar()->setValue(ui->plainTextEditRadar4->verticalScrollBar()->maximum());
        break;
    case 1:
        ui->plainTextEditRadar5->appendPlainText(msg);
        ui->plainTextEditRadar5->verticalScrollBar()->setValue(ui->plainTextEditRadar5->verticalScrollBar()->maximum());
        break;
    case 2:
        ui->plainTextEditRadar6->appendPlainText(msg);
        ui->plainTextEditRadar6->verticalScrollBar()->setValue(ui->plainTextEditRadar6->verticalScrollBar()->maximum());
        break;
    case 3:
        ui->plainTextEditRadar7->appendPlainText(msg);
        ui->plainTextEditRadar7->verticalScrollBar()->setValue(ui->plainTextEditRadar7->verticalScrollBar()->maximum());
        break;
    }
}

void RadarResetWorkerForm::calibrationFinished(int index)
{
    mTimerResult[index].stop();
}

void RadarResetWorkerForm::calibrationStarted(int index)
{
    mTimerResult[index].start( 1000 );
}

void RadarResetWorkerForm::sendOrRecvCanFrame(int index, bool bSend, quint64 id, const QString &data)
{
    QPlainTextEdit *pTextEdit = NULL;
    switch ( index ) {
    case 0:
        pTextEdit = ui->plainTextEditCanLog4;
        break;
    case 1:
        pTextEdit = ui->plainTextEditCanLog5;
        break;
    case 2:
        pTextEdit = ui->plainTextEditCanLog6;
        break;
    case 3:
        pTextEdit = ui->plainTextEditCanLog7;
        break;
    }

    if( !pTextEdit ){
        return;
    }

    QString text = QString::fromLocal8Bit("%1=>%2 %3 %4")
            .arg(QDateTime::currentDateTime().toLocalTime().toString("yyyy-MM-dd HH:mm:ss:zzz"))
            .arg( bSend ? "TX" : "RX" )
            .arg( QString::number(id, 16) )
            .arg( data );

    pTextEdit->appendPlainText( text );
    pTextEdit->moveCursor( QTextCursor::End );

    if( mCanLogFile[index].isOpen() ){
        mCanLogFile[index].write( text.toLocal8Bit() );
        mCanLogFile[index].write( "\n" );
        mCanLogFile[index].flush();
    }
}

void RadarResetWorkerForm::on_pushButtonStopCalibration_clicked()
{
    bool sda[4]{ui->checkBoxRadar4->checkState() == Qt::Checked,
                ui->checkBoxRadar5->checkState() == Qt::Checked,
                ui->checkBoxRadar6->checkState() == Qt::Checked,
                ui->checkBoxRadar7->checkState() == Qt::Checked,
               };
    mRadarResetWorkerWorker->stop(sda);
    for (int i = 0; i < 4; ++i)
    {
        mTimerResult[i].stop();
    }
//    closeCanLogFile();
}

void RadarResetWorkerForm::on_pushButtonStartCalibration_clicked()
{
    ui->plainTextEditRadar4->clear();
    ui->plainTextEditRadar5->clear();
    ui->plainTextEditRadar6->clear();
    ui->plainTextEditRadar7->clear();

    int channelIndex[4]{ ui->comboBoxRadar4->currentIndex(),
                ui->comboBoxRadar5->currentIndex(),
                ui->comboBoxRadar6->currentIndex(),
                ui->comboBoxRadar7->currentIndex()
                       };
    bool sda[4]{ui->checkBoxRadar4->checkState() == Qt::Checked,
                ui->checkBoxRadar5->checkState() == Qt::Checked,
                ui->checkBoxRadar6->checkState() == Qt::Checked,
                ui->checkBoxRadar7->checkState() == Qt::Checked,
               };

    openCanLogFile();

    mRadarResetWorkerWorker->start(channelIndex, sda);

//    for (int i = 0; i < 4; ++i)
//    {
//        if (sda[i])
//        {
//            mTimerResult[i].start(1000);
//        }
//    }
}

void RadarResetWorkerForm::on_pushButtonCalibrationResult_clicked()
{
    mRadarResetWorkerWorker->readResult();
}

void RadarResetWorkerForm::on_comboBoxProtocol_currentIndexChanged(int index)
{
    mRadarResetWorkerWorker->protocolChanged(index);
    if( index == RadarResetWorkerWorker::ProtocolGWM ){  //长城没有退出指令
        ui->pushButtonStopCalibration->setEnabled( false );
    }else{
        ui->pushButtonStopCalibration->setEnabled( true );
    }
}

void RadarResetWorkerForm::openCanLogFile()
{
    closeCanLogFile();

    if( !ui->checkBoxSaveCanLog->checkState() ){
        return;
    }
    QString curDate = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");
    for( int i=0; i<4; i++ ){
        QString fileName = QString::fromLocal8Bit( "./动态标定CanLog-%1-雷达%2.txt").arg( curDate ).arg( i+4 );
        mCanLogFile[i].setFileName( fileName );
        if( !mCanLogFile[i].open( QIODevice::WriteOnly ) ){
            //qDebug() << __FUNCTION__ << __LINE__ << "open fail!";
        }/*else{
            qDebug() << __FUNCTION__ << __LINE__ << "open success!";
        }*/
    }
}

void RadarResetWorkerForm::closeCanLogFile()
{
    for( int i=0; i<4; i++ ){
        if( mCanLogFile[i].isOpen() ){
            mCanLogFile[i].close();
        }
    }
}

} // namespace Functions


