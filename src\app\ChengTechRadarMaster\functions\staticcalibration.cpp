﻿#include "staticcalibration.h"
#include "ui_staticcalibration.h"

#include "staticcalibrationworker.h"

#include "devices/devicemanager.h"

#include <QScrollBar>
#include <QThread>
#include <QDebug>

namespace Functions {

StaticCalibration::StaticCalibration(Devices::Can::DeviceManager *deviceManager, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::StaticCalibration)
{
    ui->setupUi(this);

    mStaticCalibrationWorker = new StaticCalibrationWorker(deviceManager);
    QThread *thread = new QThread;

    mStaticCalibrationWorker->moveToThread(thread);

    connect(mStaticCalibrationWorker, &StaticCalibrationWorker::message, this, &StaticCalibration::message);
    connect(mStaticCalibrationWorker, &StaticCalibrationWorker::calibrationFinished, this, &StaticCalibration::calibrationFinished);
    connect(mStaticCalibrationWorker, &StaticCalibrationWorker::calibrationStarted, this, &StaticCalibration::calibrationStarted);
    connect(mStaticCalibrationWorker, &StaticCalibrationWorker::sendOrRecvCanFrame, this, &StaticCalibration::sendOrRecvCanFrame);

    connect(this, &StaticCalibration::destroyed, thread, &QThread::quit);
    connect(thread, &QThread::finished, &StaticCalibrationWorker::deleteLater);
    connect(thread, &QThread::finished, thread, &QThread::deleteLater);

    connect(deviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameRecieved , mStaticCalibrationWorker, &StaticCalibrationWorker::canFrame, Qt::DirectConnection);
    connect(deviceManager->deviceWorker() , &Devices::Can::IDeviceWorker::frameTransmited , mStaticCalibrationWorker, &StaticCalibrationWorker::frameTransmited);

    for (int i = 0; i < 4; ++i)
    {
        connect(&mTimerResult[i], &QTimer::timeout, this, [=](){
            mStaticCalibrationWorker->readResult(i);
        });
    }

    thread->start();

    on_comboBoxProtocolType_currentIndexChanged(0);
}

StaticCalibration::~StaticCalibration()
{
    delete ui;
}

void StaticCalibration::message(int index, const QString &msg)
{
    switch (index) {
    case 0:
        ui->plainTextEditRadar4->appendPlainText(msg);
        ui->plainTextEditRadar4->verticalScrollBar()->setValue(ui->plainTextEditRadar4->verticalScrollBar()->maximum());
        break;
    case 1:
        ui->plainTextEditRadar5->appendPlainText(msg);
        ui->plainTextEditRadar5->verticalScrollBar()->setValue(ui->plainTextEditRadar5->verticalScrollBar()->maximum());
        break;
    case 2:
        ui->plainTextEditRadar6->appendPlainText(msg);
        ui->plainTextEditRadar6->verticalScrollBar()->setValue(ui->plainTextEditRadar6->verticalScrollBar()->maximum());
        break;
    case 3:
        ui->plainTextEditRadar7->appendPlainText(msg);
        ui->plainTextEditRadar7->verticalScrollBar()->setValue(ui->plainTextEditRadar7->verticalScrollBar()->maximum());
        break;
    }
}

void StaticCalibration::calibrationFinished(int index, bool ok)
{
    mTimerResult[index].stop();
    mResult[index] = ok;
}

void StaticCalibration::calibrationStarted(int index)
{
    mTimerResult[index].start( 1000 );
}


void StaticCalibration::sendOrRecvCanFrame(int index, bool bSend, quint64 id, const QString &data)
{
    QPlainTextEdit *pTextEdit = NULL;
    switch ( index ) {
    case 0:
        pTextEdit = ui->plainTextEditCanLog4;
        break;
    case 1:
        pTextEdit = ui->plainTextEditCanLog5;
        break;
    case 2:
        pTextEdit = ui->plainTextEditCanLog6;
        break;
    case 3:
        pTextEdit = ui->plainTextEditCanLog7;
        break;
    }

    if( !pTextEdit ){
        return;
    }

    QString text = QString::fromLocal8Bit("%1=>%2 %3 %4")
            .arg(QDateTime::currentDateTime().toLocalTime().toString("yyyy-MM-dd HH:mm:ss:zzz"))
            .arg( bSend ? "TX" : "RX" )
            .arg( QString::number(id, 16) )
            .arg( data );

    pTextEdit->appendPlainText( text );
    pTextEdit->moveCursor( QTextCursor::End );

    if( mCanLogFile[index].isOpen() ){
        mCanLogFile[index].write( text.toLocal8Bit() );
        mCanLogFile[index].write( "\n" );
        mCanLogFile[index].flush();
    }
}

void StaticCalibration::on_pushButtonStopCalibration_clicked()
{
    mStaticCalibrationWorker->stop();
    for (int i = 0; i < 4; ++i)
    {
        mTimerResult[i].stop();
    }
//    closeCanLogFile();
}

void StaticCalibration::on_pushButtonStartCalibration_clicked()
{
    ui->plainTextEditRadar4->clear();
    ui->plainTextEditRadar5->clear();
    ui->plainTextEditRadar6->clear();
    ui->plainTextEditRadar7->clear();

    ui->plainTextEditCanLog4->clear();
    ui->plainTextEditCanLog5->clear();
    ui->plainTextEditCanLog6->clear();
    ui->plainTextEditCanLog7->clear();

    openCanLogFile();

    if (ui->comboBoxProtocolType->currentIndex() == StaticCalibrationWorker::ProtocolBAIC ||
            ui->comboBoxProtocolType->currentIndex() == StaticCalibrationWorker::ProtocolBAIC_BE12) {
        quint16 position[4][2];
        position[0][0] = (ui->doubleSpinBox_1->value() + 180) * 100;
        position[1][0] = (ui->doubleSpinBox_2->value() + 180) * 100;
        position[2][0] = (ui->doubleSpinBox_3->value() + 180) * 100;
        position[3][0] = (ui->doubleSpinBox_4->value() + 180) * 100;
        position[0][1] = ui->doubleSpinBox_5->value() * 100;
        position[1][1] = ui->doubleSpinBox_6->value() * 100;
        position[2][1] = ui->doubleSpinBox_7->value() * 100;
        position[3][1] = ui->doubleSpinBox_8->value() * 100;
        mStaticCalibrationWorker->setBAICTargetPosition(position);
    }

    memset(mResult, 0, sizeof (mResult));

    int channelIndex[4]{ ui->comboBoxRadar4->currentIndex(),
                ui->comboBoxRadar5->currentIndex(),
                ui->comboBoxRadar6->currentIndex(),
                ui->comboBoxRadar7->currentIndex()
                       };
    bool sda[4]{ui->checkBoxRadar4->checkState() == Qt::Checked,
                ui->checkBoxRadar5->checkState() == Qt::Checked,
                ui->checkBoxRadar6->checkState() == Qt::Checked,
                ui->checkBoxRadar7->checkState() == Qt::Checked,
               };

    mStaticCalibrationWorker->start(channelIndex, sda);

//    for (int i = 0; i < 4; ++i)
//    {
//        if (sda[i])
//        {
//            mTimerResult[i].start(1000);
//        }
//    }
}

void StaticCalibration::on_pushButtonCalibrationResult_clicked()
{
    mStaticCalibrationWorker->readResult();
}

void StaticCalibration::on_pushButtonFinishedCalibration_clicked()
{
    for (int index = 0; index < 4; ++index)
    {
        mTimerResult[index].stop();
        if (!mResult[index])
        {
            message(index, QString::fromLocal8Bit("校准未完成， 请重新校准"));
        }
    }
}

void StaticCalibration::on_pushButtonRepeatCalibration_clicked()
{
    bool sda[4]{ui->checkBoxRadar4->checkState() == Qt::Checked,
                ui->checkBoxRadar5->checkState() == Qt::Checked,
                ui->checkBoxRadar6->checkState() == Qt::Checked,
                ui->checkBoxRadar7->checkState() == Qt::Checked,
               };

    for (int i = 0; i < 4; ++i)
    {
        if (sda[i])
        {
            mTimerResult[i].start(1000);
        }
    }
}

} // namespace Functions

void Functions::StaticCalibration::on_comboBoxProtocolType_currentIndexChanged(int index)
{
    mStaticCalibrationWorker->protocolChanged(index);
    bool bHidden = false;
    if( index != StaticCalibrationWorker::ProtocolBAIC &&
            index != StaticCalibrationWorker::ProtocolGWM &&
            index != StaticCalibrationWorker::ProtocolBAIC_BE12){
        bHidden = true;
    }
    ui->doubleSpinBox_1->setHidden(bHidden);
    ui->doubleSpinBox_2->setHidden(bHidden);
    ui->doubleSpinBox_3->setHidden(bHidden);
    ui->doubleSpinBox_4->setHidden(bHidden);
    ui->doubleSpinBox_5->setHidden(bHidden);
    ui->doubleSpinBox_6->setHidden(bHidden);
    ui->doubleSpinBox_7->setHidden(bHidden);
    ui->doubleSpinBox_8->setHidden(bHidden);


//    ui->pushButtonStopCalibration->setEnabled( index != StaticCalibrationWorker::ProtocolGWM );


//    ui->doubleSpinBox_1->setHidden(index != StaticCalibrationWorker::ProtocolBAIC);
//    ui->doubleSpinBox_2->setHidden(index != StaticCalibrationWorker::ProtocolBAIC);
//    ui->doubleSpinBox_3->setHidden(index != StaticCalibrationWorker::ProtocolBAIC);
//    ui->doubleSpinBox_4->setHidden(index != StaticCalibrationWorker::ProtocolBAIC);
//    ui->doubleSpinBox_5->setHidden(index != StaticCalibrationWorker::ProtocolBAIC);
//    ui->doubleSpinBox_6->setHidden(index != StaticCalibrationWorker::ProtocolBAIC);
//    ui->doubleSpinBox_7->setHidden(index != StaticCalibrationWorker::ProtocolBAIC);
//    ui->doubleSpinBox_8->setHidden(index != StaticCalibrationWorker::ProtocolBAIC);
}

void Functions::StaticCalibration::openCanLogFile()
{
    closeCanLogFile();

    if( !ui->checkBoxSaveCanLog->checkState() ){
        return;
    }
    QString curDate = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");
    for( int i=0; i<4; i++ ){
        QString fileName = QString::fromLocal8Bit( "./静态标定CanLog-%1-雷达%2.txt").arg( curDate ).arg( i+4 );
        mCanLogFile[i].setFileName( fileName );
        if( !mCanLogFile[i].open( QIODevice::WriteOnly ) ){
            qDebug() << __FUNCTION__ << __LINE__ << "open fail!";
        }/*else{
            qDebug() << __FUNCTION__ << __LINE__ << "open success!";
        }*/
    }
}

void Functions::StaticCalibration::closeCanLogFile()
{
    for( int i=0; i<4; i++ ){
        if( mCanLogFile[i].isOpen() ){
            mCanLogFile[i].close();
        }
    }
}
