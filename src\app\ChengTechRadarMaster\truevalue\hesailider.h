﻿#pragma once

#define BIG_LITTLE_SWAP16(x)        ( (((*(short int *)&x) & 0xff00) >> 8) | \
                                      (((*(short int *)&x) & 0x00ff) << 8) )

#define BIG_LITTLE_SWAP32(x)        ( (((*(long int *)&x) & 0xff000000) >> 24) | \
                                      (((*(long int *)&x) & 0x00ff0000) >> 8) | \
                                      (((*(long int *)&x) & 0x0000ff00) << 8) | \
                                      (((*(long int *)&x) & 0x000000ff) << 24) )

#define BIG_LITTLE_SWAP64(x)        ( (((*(long long int *)&x) & 0xff00000000000000) >> 56) | \
                                      (((*(long long int *)&x) & 0x00ff000000000000) >> 40) | \
                                      (((*(long long int *)&x) & 0x0000ff0000000000) >> 24) | \
                                      (((*(long long int *)&x) & 0x000000ff00000000) >> 8) | \
                                      (((*(long long int *)&x) & 0x00000000ff000000) << 8) | \
                                      (((*(long long int *)&x) & 0x0000000000ff0000) << 24) | \
                                      (((*(long long int *)&x) & 0x000000000000ff00) << 40) | \
                                      (((*(long long int *)&x) & 0x00000000000000ff) << 56) )

#pragma pack(1)

typedef char sint8;
typedef unsigned char uint8;
typedef unsigned short uint16;
typedef unsigned int uint32;
typedef unsigned long long uint64;
typedef float float32;

typedef struct Pandar64_Header {
    uint16 EEFF;
    uint8 LaserN;
    uint8 BlockN;
    uint8 Reserved_0;
    uint8 DisUnit;
    uint8 Reserved_1;
    uint8 Reserved_2;
}Pandar64_Header;

typedef struct Pandar64_ChannelXX_Data {
    uint16 Distance;
    uint8 Reflectivity;
}Pandar64_ChannelXX_Data;

typedef struct Pandar64_Data {
    uint16 Azimuth;
    Pandar64_ChannelXX_Data ChannelXXData[64];
}Pandar64_Data;

typedef struct Pandar64_Tail {
    uint8 Reserved[5];
    uint8 HighTemperatureShutdownFlag;
    uint16 Reserved_1;
    uint16 MotorSpeed;
    uint32 Timestamp;
    uint8 ReturnMode;
    uint8 FactoryInformation;
    uint8 DateTime[6];
    uint32 UDPSequence;
}Pandar64_Tail;

#define PANDAR64_TARGET_COUNT (64)
#define PANDAR64_TARGET_MAX (PANDAR64_TARGET_COUNT * 6 * 1024)

typedef struct Pandar64_Target {
    float x;
    float y;
    float z;
    float angle;
    bool valid;
}Pandar64_Target;

typedef struct Pandar64_Targets {
    Pandar64_Target mTargets[PANDAR64_TARGET_MAX];
    uint32 mTargetsCount;
    bool mValid{false};
}Pandar64_Targets;

#pragma pack()

Pandar64_Header reverseHeaderData(Pandar64_Header *header);

Pandar64_Tail reverseTailData(Pandar64_Tail *tail);
