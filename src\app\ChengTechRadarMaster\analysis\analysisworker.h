﻿#ifndef ANALYSISWORKER_H
#define ANALYSISWORKER_H

#include "analysis_global.h"
#include "alarmcalculate.h"

#include <QObject>
#include <QJsonArray>
#include <QJsonObject>
#include <QMap>

#include "analysisdataf.h"
#include "analysisdata.h"
#include "truesystemdata.h"
#include "devices/canframe.h"

namespace Analysis {

class IInterpolation;

namespace Protocol {
class IAnalysisProtocol;
class CalculationWorker;
class TrueSystemProtocol;
}

class ANALYSIS_EXPORT AnalysisWorker : public QObject
{
    Q_OBJECT
public:
    explicit AnalysisWorker(bool dataProcess = false, QObject *parent = nullptr);

    void emitAnalysisResult(quint8 radarID);
    void analysisEnd(quint8 radarID, bool assigned, bool interpolation = false);
    void analysisEnd(quint8 radarID, AnalysisFrameType analysisType);
    void analysisEnd(Parser::ParsedDataTypedef::ParsedDataType fType);
    void analysisTrueSystemEnd();
    void setHozonBreakShort(bool breakShort);
    void setTrueSystemOpen(bool opened)  { mTrueSystemOpened = opened; }
    void setBYDHDChannelRadarID(bool raw600ByChannel, int *channelRadarID, int size);
    void setGEELYChannelRadarID(int *channelRadarID, int size);

    void setSettings(const QJsonObject &settings);

    void analysisRadarFrameEnd( quint8 radarID,  const Devices::Can::CanFrame &frame, bool bEndFrame );
    void analysisTargetPoint16FrameEnd( quint8 radarID,  const Devices::Can::CanFrame &frame, bool bEndFrame );
    void analysisLanePointFrameEnd( quint8 radarID,  const Devices::Can::CanFrame &frame, bool bEndFrame );

    //判断是否为过滤帧
    bool isFilterFrame( quint8 channelIdx, quint64 frameID );

    void radarReset( quint8 channel );

    Protocol::IAnalysisProtocol* analysisProtocol(ProtocolType protocolType);
    AlarmCalculate *alarmCalculate() const { return mAlarmCalculate; }

    IInterpolation *interpolation() { return mInterpolation; } // 插值
    bool interpolationBegin(bool injection, int radarID);
    void interpolationEnd();

signals:
    void analysisTargetFinished(quint8 radarID, /*AnalysisFrameType*/int frameType);
    void analysisTargetFinishedF(Parser::ParsedDataTypedef::TargetsF targets);
    void analysisFinished(quint8 radarID, const AnalysisData &analysisData);
    void analysisTrueSystemFinished();

    //解析雷达帧完成，bEndFrame标识是否为结束帧
    void analysisRadarFrameFinished( quint8 radarID,  const Devices::Can::CanFrame &frame, bool bEndFrame );
    void analysisTargetPoint16FrameFinished( quint8 radarID,  const Devices::Can::CanFrame &frame, bool bEndFrame );
    void analysisLanePointFrameFinished( quint8 radarID,  const Devices::Can::CanFrame &frame, bool bEndFrame );

    void radarResetCountChanged( quint64 count, quint8 lastChannel );
    void saveResponsed(bool yes);

public slots:
    void canFrame(const Devices::Can::CanFrame &frame);
    void clearRecv410FrameCount();
    void clearRadarResetCount();

    void clearFilter();//清空过滤信息
    void addChannelFilter( quint8 channelIdx );
    void addFrameFilter( quint8 channelIdx, quint64 frameID );

    void heSaiFrameNumber(int number);

    void clearAnalysisData();

public:
    long long mCameraSaveIndex[MAX_CAMERA_COUNT];
    Parser::ParsedDataTypedef::ParsedData mParsedData;  // 前雷达
    AnalysisData mAnalysisDatas[MAX_RADAR_COUNT];       // 角雷达
    stTrueObjInfo mTrueObjectInfo; ///< 真值目标信息

private:
    QList<quint8> mChannelFilter; //通道过滤
    QMap< quint8, QList<quint64> > mFrameFilter; //帧过滤 <通道ID,帧ID List>

private:
    QList<Protocol::IAnalysisProtocol*> mProtocols;
    Protocol::IAnalysisProtocol *mTrueSystemProtocol{0};

    bool mTrueSystemOpened{false};    ///< 接入真值系统，真值系统只能接到CAN0
    friend class Protocol::IAnalysisProtocol;

    qint64 mRadarResetCount{0}; //雷达复位次数
    quint8 mRadarResetLastChannel{0}; //雷达复位报文最后一次的次数

    AlarmCalculate *mAlarmCalculate;

    bool mUseInterpolation{false};
    int mInterpolationRadarID{4};
    IInterpolation *mInterpolation{0}; // 插值
};

} // namespace Analysis

#endif // ANALYSISWORKER_H
