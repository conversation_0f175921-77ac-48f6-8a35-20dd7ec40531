﻿#include "idevicefile.h"

#include <iostream>
#include <thread>

#include "../CANDeviceFile.h"

namespace Devices {
	namespace Can {

		std::vector<IDeviceFile::FileTypeInfo> IDeviceFile::mFileTypeList {
		{ IDeviceFile::ASC, ".asc" },
		{ IDeviceFile::BLF, ".blf" }
		};

		IDeviceFile::FileType IDeviceFile::fileType(std::string &name)
		{
			char drive[_MAX_DRIVE];
			char dir[_MAX_DIR];
			char fname[_MAX_FNAME];
			char ext[_MAX_EXT];

			_splitpath_s(name.c_str(), drive, dir, fname, ext);
			for (int i = 0; i < mFileTypeList.size(); ++i) {
				if (ext == mFileTypeList[i].fileSuffix) {
					return mFileTypeList[i].type;
				}
			}
			return IDeviceFile::UNKNOWN;
		}

        IDeviceFile::IDeviceFile(CANDeviceFile *device) : mDeviceFile(device)
		{

		}

		bool IDeviceFile::open(const std::string & name, bool inMode)
		{
			mOpenInMode = inMode;
			mFilename = name;
			mSaveTimestamp = 0;
			mTimeDifferential = 0;
			mOpened = openFile();
			return mOpened;
		}

		bool IDeviceFile::close()
		{
			if (mOpened) {
				mOpened = !closeFile();
			}
            return !mOpened;
        }

        bool IDeviceFile::validID(int id)
        {
            if (mfValidID) {
                return mfValidID(id);
            }

            return true;
        }

                void IDeviceFile::callback(const CanFrame::Ptr pFrame)
		{
            if (mDeviceFile) {
                mDeviceFile->callback(pFrame);
			}
		}

		/**
		* <AUTHOR>
		* @date @@Today
        * @param time_diff 与第一帧的时间差速(us)
		* @return void
		* @note
		* 函数描述
		* @remarks
        */
#include <Windows.h>
        void IDeviceFile::dely(uint64_t time_diff, uint64_t id)
        {
            return;
			std::chrono::steady_clock::time_point timeNow = std::chrono::steady_clock::now();
			if (mTimeDifferential > 0 && time_diff > mTimeDifferential) {
				uint64_t us = time_diff - mTimeDifferential;
				// 微秒
                long long duration_microsecond = std::chrono::duration<double, std::micro>(timeNow - m_time_point).count(); // 已运行的时间us

//                std::cout << std::hex << id << " " << std::dec << time_diff << " " << mTimeDifferential << " " << us << " " << duration_microsecond  << std::endl;
                if (us > duration_microsecond && us > 500) {
					std::this_thread::sleep_for(std::chrono::microseconds(us - duration_microsecond));
				}
			}
			m_time_point = timeNow;
			mTimeDifferential = time_diff;
		}

	}
}
