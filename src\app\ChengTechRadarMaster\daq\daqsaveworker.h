﻿#ifndef DAQSAVEWORKER_H
#define DAQSAVEWORKER_H

#include <QFile>

#include <mutex>
#include "utils/SafeQueue.h"
#include "network/networkdata.h"

namespace DAQ{

#define QUEUE_SIZE (1024*4)

typedef struct DAQData {
    char mData[1024 * 2];
    int  mDataLength{0};
}DAQData;

typedef struct CircularQueue {
    DAQData mData[QUEUE_SIZE];
    int mFront{0};
    int mTail{0};
    bool mEmpty{true};
    std::mutex mMutex;

    bool enqueue(const char *data, int len);
    DAQData *dequeue();
}CircularQueue;

class DAQSaveWorker
{
public:
    explicit DAQSaveWorker(QObject *parent = nullptr);

    bool open(const QString &filename, bool newThread = true);
    void closeFile();
    void close();

    bool isOpen() const { return mOpened; }
    bool canSave() const { return mCanSave; }
    bool saveCount() const { return mPackageCount; }

public:
    quint64 writeRawData();
    qint64 writeRawData(const char *data, qint64 len);

private:
    bool mNewThread{false};
    bool mOpened{false};
    bool mCanSave{false};
    FILE *mpFile{0};
    quint64 mPackageCount{0};

    CircularQueue *mCircularQueue{0};
};

}

#endif // DAQSAVEWORKER_H
