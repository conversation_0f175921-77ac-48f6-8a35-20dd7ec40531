﻿#include "analysisprotocolct.h"

#include <QDebug>

namespace Analysis {
namespace Protocol {

AnalysisProtocolCT::AnalysisProtocolCT(AnalysisWorker *analysisWorker, QObject *parent)
    : IAnalysisProtocol(analysisWorker, parent)
{
    mProtocolType = ProtocolChengTech;
}

bool AnalysisProtocolCT::analysisFrame(const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();

    return true;
}

} // namespace Protocol
} // namespace Analysis
