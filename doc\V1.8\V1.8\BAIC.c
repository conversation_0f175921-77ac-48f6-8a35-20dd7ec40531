/**
* Copyright (c) 2016-2022 ChengTech All rights reserved.
* @file BAIC.c
* @brief brief
* <AUTHOR>
* @date 2023-02-26
* @version 1.0.0
* @note
* description
*/

#include "BAIC.h"

static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
	uint32_t const m = 0x1u << (bits - 1);

	return (data & m) ? -((int32_t)(data ^ m)) : data;
}

static uint32_t encode_sign_bit(int32_t data, uint8_t bits) {
	uint32_t const m = 0x1u << (bits - 1);

	return (data & m) ? (data | m) : data;
}

// 0x280 length = 24
bool decode_SRR_RL_ObjectList01(CT_SRR_RL_ObjectList01_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	userData->RL_ObjectChecksum_01 = (data[0]);
	userData->RL_ObjectID_01 = (data[1]);
	userData->RL_ObjectDistLong_01 = (((((data[3] & 0xFCU) >> 2) + (((uint16_t)data[2] & 0xFFU) << 6)) * 0.05) - 409.6);
	userData->RL_ObjectDistLat_01 = (((((data[5] & 0xC0U) >> 6) + (((uint32_t)data[4]) << 2) + (((uint32_t)data[3] & 0x3U) << 10)) * 0.05) - 102.4);
	userData->RL_ObjectVrelLong_01 = (((((data[6] & 0xFEU) >> 1) + (((uint16_t)data[5] & 0x3FU) << 7)) * 0.04) - 163.84);
	userData->RL_ObjectCntr_01 = (data[7]);
	userData->RL_ObjectVrelLat_01 = (((((data[9] & 0xF8U) >> 3) + (((uint16_t)data[8] & 0xFFU) << 5)) * 0.04) - 163.84);
	userData->RL_ObjectArelLong_01 = ((((data[10] & 0xFFU) + (((uint16_t)data[9] & 0x7U) << 8)) * 0.01) - 10.24);
	userData->RL_ObjectArelLat_01 = (((((data[12] & 0xE0U) >> 5) + (((uint16_t)data[11] & 0xFFU) << 3)) * 0.01) - 10.24);
	userData->RL_ObjectLength_01 = ((((data[13] & 0xE0U) >> 5) + (((uint16_t)data[12] & 0x1FU) << 3)) * 0.2);
	userData->RL_ObjectWidth_01 = ((((data[14] & 0xE0U) >> 5) + (((uint16_t)data[13] & 0x1FU) << 3)) * 0.2);
	userData->RL_ObjectHeadYawAgl_01 = ((((data[15] & 0xFFU) + (((uint16_t)data[14] & 0xFU) << 8)) * 0.1) - 204.8);
	userData->RL_ObjectDynProp_01 = ((data[16] & 0xF0U) >> 4);
	userData->RL_ObjectExistnc_01 = (((data[17] & 0xE0U) >> 5) + (((uint16_t)data[16] & 0xFU) << 3));
	userData->RL_ObjectRCS_01 = (((((data[18] & 0xF0U) >> 4) + (((uint16_t)data[17] & 0x1FU) << 4)) * 0.5) - 128);
	userData->RL_ObjectClass_01 = (data[18] & 0xFU);
	userData->RL_ObjectDistAltitude_01 = (((data[19]) * 0.05) - 1.75);
	userData->RL_ObjectLink_01 = (data[20]);
	userData->RL_ObjectRefPointPos_01 = ((data[21] & 0xC0U) >> 6);
	userData->RL_ObjectRollingCnt_01 = (data[23] & 0xFU);

	return true;
}

// 0x280 length = 24
bool encode_SRR_RL_ObjectList01(CT_SRR_RL_ObjectList01_t *userData, uint8_t *data, int length) {
	if (length != 24) {
		return false;
	}

	uint8_t RL_ObjectChecksum_01 = (userData->RL_ObjectChecksum_01);
	uint8_t RL_ObjectID_01 = (userData->RL_ObjectID_01);
	uint16_t RL_ObjectDistLong_01 = (((userData->RL_ObjectDistLong_01) + 409.6) / 0.05);
	uint16_t RL_ObjectDistLat_01 = (((userData->RL_ObjectDistLat_01) + 102.4) / 0.05);
	uint16_t RL_ObjectVrelLong_01 = (((userData->RL_ObjectVrelLong_01) + 163.84) / 0.04);
	uint8_t RL_ObjectCntr_01 = (userData->RL_ObjectCntr_01);
	uint16_t RL_ObjectVrelLat_01 = (((userData->RL_ObjectVrelLat_01) + 163.84) / 0.04);
	uint16_t RL_ObjectArelLong_01 = (((userData->RL_ObjectArelLong_01) + 10.24) / 0.01);
	uint16_t RL_ObjectArelLat_01 = (((userData->RL_ObjectArelLat_01) + 10.24) / 0.01);
	uint8_t RL_ObjectLength_01 = ((userData->RL_ObjectLength_01) / 0.2);
	uint8_t RL_ObjectWidth_01 = ((userData->RL_ObjectWidth_01) / 0.2);
	uint16_t RL_ObjectHeadYawAgl_01 = (((userData->RL_ObjectHeadYawAgl_01) + 204.8) / 0.1);
	uint8_t RL_ObjectDynProp_01 = (userData->RL_ObjectDynProp_01);
	uint8_t RL_ObjectExistnc_01 = (userData->RL_ObjectExistnc_01);
	uint16_t RL_ObjectRCS_01 = (((userData->RL_ObjectRCS_01) + 128) / 0.5);
	uint8_t RL_ObjectClass_01 = (userData->RL_ObjectClass_01);
	uint8_t RL_ObjectDistAltitude_01 = (((userData->RL_ObjectDistAltitude_01) + 1.75) / 0.05);
	uint8_t RL_ObjectLink_01 = (userData->RL_ObjectLink_01);
	uint8_t RL_ObjectRefPointPos_01 = (userData->RL_ObjectRefPointPos_01);
	uint8_t RL_ObjectRollingCnt_01 = (userData->RL_ObjectRollingCnt_01);

	data[0] = (userData->RL_ObjectChecksum_01 & 0xFFU);
	data[1] = (userData->RL_ObjectID_01 & 0xFFU);
	data[2] = ((RL_ObjectDistLong_01 & 0x3FC0U) >> 6);
	data[3] = (((RL_ObjectDistLong_01 & 0x3FU) << 2) | ((RL_ObjectDistLat_01 & 0xC00U) >> 10));
	data[4] = ((RL_ObjectDistLat_01 & 0x3FCU) >> 2);
	data[5] = (((RL_ObjectDistLat_01 & 0x3U) << 6) | ((RL_ObjectVrelLong_01 & 0x1F80U) >> 7));
	data[6] = ((RL_ObjectVrelLong_01 & 0x7FU) << 1);
	data[7] = (userData->RL_ObjectCntr_01 & 0xFFU);
	data[8] = ((RL_ObjectVrelLat_01 & 0x1FE0U) >> 5);
	data[9] = (((RL_ObjectVrelLat_01 & 0x1FU) << 3) | ((RL_ObjectArelLong_01 & 0x700U) >> 8));
	data[10] = ((RL_ObjectArelLong_01 & 0xFFU));
	data[11] = ((RL_ObjectArelLat_01 & 0x7F8U) >> 3);
	data[12] = (((RL_ObjectArelLat_01 & 0x7U) << 5) | ((RL_ObjectLength_01 & 0xF8U) >> 3));
	data[13] = (((RL_ObjectLength_01 & 0x7U) << 5) | ((RL_ObjectWidth_01 & 0xF8U) >> 3));
	data[14] = (((RL_ObjectWidth_01 & 0x7U) << 5) | ((RL_ObjectHeadYawAgl_01 & 0xF00U) >> 8));
	data[15] = ((RL_ObjectHeadYawAgl_01 & 0xFFU));
	data[16] = (((userData->RL_ObjectDynProp_01 & 0xFU) << 4) | ((userData->RL_ObjectExistnc_01 & 0x78U) >> 3));
	data[17] = (((userData->RL_ObjectExistnc_01 & 0x7U) << 5) | ((RL_ObjectRCS_01 & 0x1F0U) >> 4));
	data[18] = (((RL_ObjectRCS_01 & 0xFU) << 4) | (userData->RL_ObjectClass_01 & 0xFU));
	data[19] = (RL_ObjectDistAltitude_01 & 0xFFU);
	data[20] = (userData->RL_ObjectLink_01 & 0xFFU);
	data[21] = ((userData->RL_ObjectRefPointPos_01 & 0x3U) << 6);
	data[22] = 0;
	data[23] = (userData->RL_ObjectRollingCnt_01 & 0xFU);

	return true;
}
// 0x300 length = 6
bool decode_SRR_Rear_LockTarget01(CT_SRR_Rear_LockTarget01_t *userData, uint8_t *data, int length) {
	if (length != 6) {
		return false;
	}

	userData->Lock_Target_Lane_ID_01 = (data[0]);
	userData->Lock_Target_Abscissa_01 = ((data[1]) - 100);
	userData->Lock_Target_Ordinate_01 = ((data[2]) - 100);
	userData->Lock_Target_LonRelative_Spd_01 = (data[3]);
	userData->Lock_Target_LatRelative_Spd_01 = (data[4]);
	userData->Lock_Target_MovingState_01 = ((data[5] & 0xC0U) >> 6);
	userData->Lock_Target_State_01 = ((data[5] & 0x20U) >> 5);
	userData->Lock_Target_Type_01 = ((data[5] & 0x1EU) >> 1);

	return true;
}

// 0x300 length = 6
bool encode_SRR_Rear_LockTarget01(CT_SRR_Rear_LockTarget01_t *userData, uint8_t *data, int length) {
	if (length != 6) {
		return false;
	}

	uint8_t Lock_Target_Lane_ID_01 = (userData->Lock_Target_Lane_ID_01);
	uint8_t Lock_Target_Abscissa_01 = ((userData->Lock_Target_Abscissa_01) + 100);
	uint8_t Lock_Target_Ordinate_01 = ((userData->Lock_Target_Ordinate_01) + 100);
	uint8_t Lock_Target_LonRelative_Spd_01 = (userData->Lock_Target_LonRelative_Spd_01);
	uint8_t Lock_Target_LatRelative_Spd_01 = (userData->Lock_Target_LatRelative_Spd_01);
	uint8_t Lock_Target_MovingState_01 = (userData->Lock_Target_MovingState_01);
	uint8_t Lock_Target_State_01 = (userData->Lock_Target_State_01);
	uint8_t Lock_Target_Type_01 = (userData->Lock_Target_Type_01);

	data[0] = (userData->Lock_Target_Lane_ID_01 & 0xFFU);
	data[1] = (Lock_Target_Abscissa_01 & 0xFFU);
	data[2] = (Lock_Target_Ordinate_01 & 0xFFU);
	data[3] = (userData->Lock_Target_LonRelative_Spd_01 & 0xFFU);
	data[4] = (userData->Lock_Target_LatRelative_Spd_01 & 0xFFU);
	data[5] = (((userData->Lock_Target_MovingState_01 & 0x3U) << 6) | ((userData->Lock_Target_State_01 & 0x1U) << 5) | ((userData->Lock_Target_Type_01 & 0xFU) << 1));

	return true;
}

bool proc_BAIC(BAIC_t *userData, Frame *p) {
	bool ret = false;

	switch (p->id) {
	case MSG_ID280_SRR_RL_OBJECTLIST01:
		ret = decode_SRR_RL_ObjectList01(&userData->_SRR_RL_ObjectList01, p->data, p->dlc);
		break;
	case MSG_ID300_SRR_REAR_LOCKTARGET01:
		ret = decode_SRR_Rear_LockTarget01(&userData->_SRR_Rear_LockTarget01, p->data, p->dlc);
		break;
	default:
		return false;
		break;
	}

	return ret;
}


