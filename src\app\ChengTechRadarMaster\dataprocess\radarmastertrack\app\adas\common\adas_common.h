/**
 * @file adas_common.h
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2025.07.08
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2025-07-08 <td>1.0     <td><PERSON>     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2025  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef __ADAS_COMMON_H__
#define __ADAS_COMMON_H__

#include <stdint.h>
#include <math.h>

#ifndef FLOAT_EPS
#define FLOAT_EPS           (1e-6f)
#endif
#ifndef FLOAT_NEG_EPS
#define FLOAT_NEG_EPS       (-1e-6f)
#endif

#ifndef M_PI
#define M_PI 3.1415926f
#endif

float MagicInverseSqrt(float x);
float MagicSqrt(float x);

float atan2_replacement(float y, float x);

#endif


