﻿#include "ianalysisprotocol.h"

#include "analysisprotocolct.h"
#include "analysisprotocolct410.h"

#include <QDebug>

namespace Analysis {
namespace Protocol {

IAnalysisProtocol *IAnalysisProtocol::newAnalysisProtocol(ProtocolType protocolType, AnalysisWorker *analysisWorker, QObject *parent)
{
    switch (protocolType)
    {
    case ProtocolChengTech:
        return new AnalysisProtocolCT(analysisWorker, parent);
        break;
    case ProtocolChengTech410:
        return new AnalysisProtocolCT410(analysisWorker, parent);
        break;
    default:
        break;
    }
    return 0;
}

IAnalysisProtocol::IAnalysisProtocol(AnalysisWorker *analysisWorker, QObject *parent)
    : QObject(parent),
      mAnalysisWorker(analysisWorker)
{
    memset(mNewBegin, 0, sizeof (mNewBegin));
}

void IAnalysisProtocol::setAnalysisConfig(const QJsonObject &config)
{

}

QJsonObject IAnalysisProtocol::analysisConfig() const
{
    return QJsonObject();
}

void IAnalysisProtocol::analysisEnd(int radarID, bool assigned)
{
    if (radarID < 4 || radarID > 7) {
        qDebug() << __FUNCTION__ << __LINE__ << "End frame error!" << radarID;
        return;
    }
    mAnalysisWorker->analysisEnd(radarID, assigned);

}

void IAnalysisProtocol::analysisEnd(Parser::ParsedDataTypedef::ParsedDataType fType)
{
    mAnalysisWorker->analysisEnd(fType);
}

} // namespace Protocol
} // namespace Analysis
