﻿#include "aftersalecalibrationworker.h"

#include "uds.h"
#include "utils/utils.h"
#include "utils/baicuds/crypto/baciseedtokey.h"
#include "utils/seedtokeybydll.h"
#include "utils/seedtokey.h"

#include <QtConcurrent>
#include <QDebug>


namespace Functions {

AfterSaleCalibrationWorker::AfterSaleCalibrationWorker(Devices::Can::DeviceManager *deviceManager, QObject *parent) : QObject(parent)
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        mUDS[i] = new UDS(deviceManager, this);
        mUDS[i]->setResponseID(mResponseAddress[mProtocolIndex][i]);
    }
}

void AfterSaleCalibrationWorker::protocolChanged(int index)
{
    mProtocolIndex = (ProtocolType)index;
    qDebug() << __FUNCTION__ << __LINE__ << mProtocolIndex;
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        mUDS[i]->setResponseID(mResponseAddress[mProtocolIndex][i]);
    }
}

void AfterSaleCalibrationWorker::stop(bool sda[])
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        if (sda[i])
        {
            QFuture<void> res = QtConcurrent::run(this, &AfterSaleCalibrationWorker::calibration, i, true);
        }
    }
}

void AfterSaleCalibrationWorker::start(int channelIndex[], bool sda[], bool can)
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        mUDS[i]->canUpdate(can);
        mUDS[i]->setChannelIndex(channelIndex[i]);
        if (sda[i])
        {
            QFuture<void> res = QtConcurrent::run(this, &AfterSaleCalibrationWorker::calibration, i, false);
        }
    }
}

void AfterSaleCalibrationWorker::readResult()
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        readResult(i);
    }
}

void AfterSaleCalibrationWorker::readResult(int index)
{
    QFuture<void> res = QtConcurrent::run(this, &AfterSaleCalibrationWorker::result, index);
}

bool AfterSaleCalibrationWorker::seedToKeyByDLL( quint8 index, quint8 level, const QByteArray &seed, QByteArray &key)
{
    QString path;
    switch ( mProtocolIndex ) {
    case ProtocolGWM:
        path = "./secureDLL/GWM_DLL.dll";
        break;
    default:
        break;
    }

    SeedToKeyByDLL dll;
    if( !dll.seedToKey( path, level, seed, key ) ){
        emit message(index, dll.errorMsg() );
    }
    return true;

    /*
    QMutexLocker locker( &mDLLMutex );
    QLibrary library;
    DLL_Seed2Key pDLLFunPtr = NULL;

    QString path;
    switch ( mProtocolIndex ) {
    case ProtocolGWM:
        path = "./secureDLL/GWM_DLL.dll";
        break;
    default:
        break;
    }

    library.setFileName( path );
    if( !library.load() ){
        emit message(index, QString::fromLocal8Bit("加载安全算法库失败！(%1)-(%2)").arg( path ).arg( library.errorString() ) );
        return false;
    }
    const char* funStr = "GenerateKeyEx";
    pDLLFunPtr = (DLL_Seed2Key)library.resolve( funStr );


    if( pDLLFunPtr ){
        unsigned char* iSeedArray = (unsigned char*)seed.data();
        unsigned int  iSize = seed.size();
        unsigned char ioKeyArray[256];
        unsigned int  oSize;
        //0x5754380A  in
        //0x36E91FD6 out
//        iSeedArray[0] = 0xA8;
//        iSeedArray[1] = 0xDA;
//        iSeedArray[2] = 0x5D;
//        iSeedArray[3] = 0x2F;

        pDLLFunPtr( iSeedArray, iSize, level, NULL, ioKeyArray, 256, &oSize );

        key.clear();
        for( int i=0; i<oSize; i++ ){
            key.append( ioKeyArray[i] );
        }

//        qDebug() << __FUNCTION__ << __LINE__
//                 << QByteArray( (const char *)ioKeyArray, sizeof (ioKeyArray)).mid(0,oSize).toHex(' ');
    }else{
        emit message(index, QString::fromLocal8Bit("加载安全算法库失败！(%1)-(%2)").arg( path ).arg( library.errorString() ) );
        return false;
    }

    library.unload();
    return true;*/
}


void AfterSaleCalibrationWorker::calibration(int index, bool stop)
{
    qDebug() << __FUNCTION__ << __LINE__ << QThread::currentThreadId() << index;
#define SEND_DATA_HEX_RESPONSE(ID, DATA, MESSAGE) \
    responseFrame.clear(); \
    if (!mUDS[index]->sendData(ID, QString(DATA), &responseFrame)) { \
        emit message(index, MESSAGE + mUDS[index]->errorString()); \
        emit calibrationFinished(index); \
        return; \
    }

    UDSFrame responseFrame;

    if (!stop)
    {
        if (mProtocolIndex == ProtocolBAIC || mProtocolIndex == ProtocolBAIC_BE12) {
        mUDS[index]->sendFrame(0x411, QByteArray::fromHex("00 40 00 00 00 00 00 00"));
        Utils::dely(1000);
        mUDS[index]->sendFrame(0x411, QByteArray::fromHex("00 40 00 00 00 00 00 00"));
        Utils::dely(1000);
        mUDS[index]->sendFrame(0x411, QByteArray::fromHex("00 40 00 00 00 00 00 00"));
        } else if (mProtocolIndex == ProtocolGEELY180ProKX11 || mProtocolIndex == ProtocolGEELY180ProE245) {
            mUDS[index]->sendFrame(0x501, QByteArray::fromHex("01 40 00 20 00 00 00 00"));
            Utils::dely(1000);
            mUDS[index]->sendFrame(0x501, QByteArray::fromHex("01 40 00 20 00 00 00 00"));
            Utils::dely(1000);
            mUDS[index]->sendFrame(0x501, QByteArray::fromHex("01 40 00 20 00 00 00 00"));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "10 01", QString::fromLocal8Bit("开启诊断仪失败"));
            emit message(index, QString::fromLocal8Bit("开启诊断仪成功"));
            Utils::dely(20);
        }

        SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "10 03", QString::fromLocal8Bit("进入扩展模式失败"));
        emit message(index, QString::fromLocal8Bit("进入扩展模式成功"));
        if (mProtocolIndex == ProtocolGEELY180ProKX11 || mProtocolIndex == ProtocolGEELY180ProE245) {
            Utils::dely(20);
        }

        emit message(index, QString::fromLocal8Bit("请求种子"));
        if (mProtocolIndex == ProtocolGEELY || mProtocolIndex == ProtocolGEELY180Pro ||
                mProtocolIndex == ProtocolGEELY180ProKX11 || mProtocolIndex == ProtocolGEELY180ProE245) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "27 05", QString::fromLocal8Bit("请求种子失败")); // 请求种子
            Utils::dely(20);
        }else if (mProtocolIndex == ProtocolHozon) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "27 03", QString::fromLocal8Bit("请求种子失败")); // 请求种子
        }else{
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "27 01", QString::fromLocal8Bit("请求种子失败")); // 请求种子
        }
        emit message(index, QString::fromLocal8Bit("请求种子成功"));
        uint32_t seed{0};
        memcpy(&seed, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);
    //    qDebug() << __FUNCTION__ << __LINE__ << mMASK << QString::number(mMASK);
        if (mProtocolIndex == ProtocolBAIC) {
            uint8_t key[16];
            BAICSendToKey(seed, UDSFactor, key);
            qDebug() << __FUNCTION__ << __LINE__ << QString::number(seed, 16);
            qDebug() << __FUNCTION__ << __LINE__ << QByteArray((const char *)key, sizeof (key)).toHex(' ');
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
        } else if (mProtocolIndex == ProtocolBAIC_BE12) {
            unsigned char iSeedArray[16];
            memcpy((void*)iSeedArray, responseFrame.mData.mid(2, 16).data(), 16);
            unsigned short iSeedArraySize = sizeof(iSeedArray);
            const unsigned int iSecurityLevel = 0x01;
            const char* iVariant = 0;
            unsigned char ioKeyArray[32]{ 0 };
            unsigned int iKeyArraySize = sizeof(ioKeyArray);
            unsigned int oSize = 0;
            if (GenerateKeyEx_BEIQI_BE12_PROJECT(iSeedArray,
                                                 iSeedArraySize,
                                                 iSecurityLevel,
                                                 iVariant,
                                                 ioKeyArray,
                                                 iKeyArraySize,
                                                 &oSize) != 0) {
                qDebug() << __FUNCTION__ << __LINE__ << "message";
            }
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(QByteArray((const char *)ioKeyArray, sizeof (ioKeyArray)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
        } else if (mProtocolIndex == ProtocolGEELY) {
            quint8 geely_seed[3];
            quint8 key[3];
            memcpy(geely_seed, (responseFrame.mData.mid(2, 3)).data(), 3);
            GeelySeedToKey( geely_seed, key );
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 06 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
        } else if (mProtocolIndex == ProtocolGEELY180Pro) {
            quint8 geely_seed[3];
            quint8 key[3];
            memcpy(geely_seed, (responseFrame.mData.mid(2, 3)).data(), 3);
            GeelySeedToKey180Pro( geely_seed, key );
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 06 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
        } else if (mProtocolIndex == ProtocolGEELY180ProKX11) {
            quint8 geely_seed[3]/*{0x18, 0x70, 0x0B}*/;
            quint8 key[3];
            memcpy(geely_seed, (responseFrame.mData.mid(2, 3)).data(), 3);
            GeelySeedToKey180ProNew( geely_seed, key, index );
//            qDebug() << __FUNCTION__ << __LINE__ << index
//                     << QByteArray::fromRawData((char *)geely_seed, 3).toHex(' ')
//                     << QByteArray::fromRawData((char *)key, 3).toHex(' ');
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 06 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥

            Utils::dely(20);
        } else if (mProtocolIndex == ProtocolGEELY180ProE245) {
            unsigned char iSeedArray[16];
            memcpy((void*)iSeedArray, responseFrame.mData.mid(2, 16).data(), 16);
            unsigned short iSeedArraySize = sizeof(iSeedArray);
            const unsigned int iSecurityLevel = 0x01;
            const char* iVariant = 0;
            unsigned char ioKeyArray[16]{ 0 };
            unsigned int iKeyArraySize = sizeof(ioKeyArray);
            unsigned int oSize = 0;
            if (GenerateKeyEx_GEELY_E245_PROJECT(index,
                                                 iSeedArray,
                                                 iSeedArraySize,
                                                 iSecurityLevel,
                                                 iVariant,
                                                 ioKeyArray,
                                                 iKeyArraySize,
                                                 &oSize) != 0) {
                qDebug() << __FUNCTION__ << __LINE__ << "message";
            }
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 06 %1").arg(QByteArray((const char *)ioKeyArray, sizeof (ioKeyArray)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
            Utils::dely(20);
        } else if (mProtocolIndex == ProtocolGWM) {
            QByteArray key;
            seedToKeyByDLL( index, 0x01, responseFrame.mData.mid(2, 4), key );

//            quint32 gwm_seed;
//            quint8 key[4];
//            memcpy(&gwm_seed, (responseFrame.mData.mid(2, 4)).data(), 4);
//            gwm_seed = 0xA8DA5D2F;
//            GwmSeedToKey( gwm_seed, key );

//            qDebug() << __FUNCTION__ << __LINE__ << QString::number(geely_seed[0], 16)<< QString::number(geely_seed[1], 16)<< QString::number(geely_seed[2], 16);
            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.mid(2, 4).toHex(' ')
                     << QByteArray((const char *)key, sizeof (key)).toHex(' ');
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(QByteArray((const char *)key, sizeof (key)).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
        }else  if (mProtocolIndex == ProtocolHozon) {
            unsigned char iSeedArray[4] = { 0x00 };
            unsigned int iSecurityLevel = 0x01;
            unsigned char ioKeyArray[16]{ 0 };
            memcpy((void*)iSeedArray, Utils::reverseArray(responseFrame.mData.mid(2, 4)).data(), 4);
            GenerateKeyEx_HOZON_PROJECT(iSecurityLevel, iSeedArray, ioKeyArray);
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 04 %1").arg(QByteArray((const char *)ioKeyArray, sizeof (ioKeyArray)).mid(0,4).toHex(' ').data()), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
        }else {
            uint32_t key = SeedToKey(seed, mMASK[mProtocolIndex][index]);
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], QString("27 02 %1").arg(key, 8, 16, QLatin1Char('0')), QString::fromLocal8Bit("密钥验证失败")); // 发送密钥
        }
        emit message(index, QString::fromLocal8Bit("密钥验证成功"));

        if (mProtocolIndex == ProtocolGEELY180ProKX11 || mProtocolIndex == ProtocolGEELY180ProE245) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 30 23 00", QString::fromLocal8Bit("执行Lock Control失败"));
            if (responseFrame.mData.data()[4] == 0x11) {
                emit message(index, QString::fromLocal8Bit("上锁失败"));
                emit calibrationFinished(index); \
                return;
            }
            emit message(index, QString::fromLocal8Bit("执行Lock Control成功"));

            Utils::dely(20);
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 01", QString::fromLocal8Bit("查询雷达故障码"));
            QByteArray data = responseFrame.mData
                    .replace(QByteArray::fromHex("af"), QByteArray())
                    .replace(QByteArray::fromHex("59 02"), QByteArray())
                    .replace(QByteArray::fromHex("5A 67 54"), QByteArray())
                    .replace(QByteArray::fromHex("5A 67 55"), QByteArray())
                    .replace(QByteArray::fromHex("d1 4b 51"), QByteArray())
                    .replace(QByteArray::fromHex("d1 4c 51"), QByteArray());
            if (data.size() > 3){
                emit message(index, QString::fromLocal8Bit("雷达存在故障，故障码是 %1, 请修复故障后再校准").arg(data.toHex(' ').data()));
                emit calibrationFinished(index);
                return;
            }
            emit message(index, QString::fromLocal8Bit("读取障记录成功"));
            Utils::dely(20);
        }
        if (mProtocolIndex == ProtocolGEELY || mProtocolIndex == ProtocolGEELY180Pro) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 DC 01", QString::fromLocal8Bit("开始校准失败"));
        }else if (mProtocolIndex == ProtocolGWM) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 23 00 01", QString::fromLocal8Bit("开始校准失败"));
        }else if (mProtocolIndex == ProtocolHozon) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "14 FF FF FF", QString::fromLocal8Bit("清故障记录失败"));
            emit message(index, QString::fromLocal8Bit("清故障记录成功"));
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 09", QString::fromLocal8Bit("读取障记录失败"));
            emit message(index, QString::fromLocal8Bit("读取障记录成功"));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 35 F4", QString::fromLocal8Bit("读取条件失败"));
            quint8 state = *( (quint8*)responseFrame.mData.mid( 4, 1 ).data() );
            if (state) {
                QString text;
                switch (state) {
                case 0x01:
                    text = QString::fromLocal8Bit("FL 车门未关闭");
                    break;
                case 0x02:
                    text = QString::fromLocal8Bit("FR 车门未关闭");
                    break;
                case 0x03:
                    text = QString::fromLocal8Bit("RL 车门未关闭");
                    break;
                case 0x04:
                    text = QString::fromLocal8Bit("RR 车门未关闭");
                    break;
                case 0x05:
                    text = QString::fromLocal8Bit("雷达未初始化");
                    break;
                case 0x06:
                    text = QString::fromLocal8Bit("雷达电压不满足，不在 9V-16V 范围内");
                    break;
                case 0x07:
                    text = QString::fromLocal8Bit("车辆行驶中，车速 > 0.3m/s，或者车速无效，或者有DTC 故障");
                    break;
                case 0x08:
                    text = QString::fromLocal8Bit("方向盘转角是否小于 10 度");
                    break;
                default:
                    break;
                }
                emit message(index, QString::fromLocal8Bit("条件不满足：%1") .arg(text));
                emit calibrationFinished(index);
                return;
            }
            emit message(index, QString::fromLocal8Bit("条件满足"));

            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 35 F2", QString::fromLocal8Bit("例程启动失败"));
            state = *( (quint8*)responseFrame.mData.mid( 4, 1 ).data() );
            if (state == 0x05) {
                emit message(index, QString::fromLocal8Bit("例程启动失败或没有运行"));
                emit calibrationFinished(index);
                return;
            }
            emit message(index, QString::fromLocal8Bit("例程启动成功"));
        } else if (mProtocolIndex == ProtocolGEELY180ProKX11 || mProtocolIndex == ProtocolGEELY180ProE245) {
            responseFrame.clear();
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 01 DC 01 5A 5A", QString::fromLocal8Bit("开始校准失败"));
            if (responseFrame.mData.data()[2] == 0x22) {
                emit message(index, QString::fromLocal8Bit("车速过大，请在小于20km/h 时开启校准") + mUDS[index]->errorString());
                emit calibrationFinished(index);
                return;
            }
            Utils::dely(20);
        } else {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], (mProtocolIndex == ProtocolBAIC || mProtocolIndex == ProtocolBAIC_BE12) ? "31 01 12 7B" : "31 01 03 02", QString::fromLocal8Bit("开始校准失败"));
        }
        emit message(index, QString::fromLocal8Bit("可启动车辆, 开始校准..."));
        emit calibrationStarted(index);
    }
    else
    {
        if (mProtocolIndex == ProtocolGEELY || mProtocolIndex == ProtocolGEELY180Pro || mProtocolIndex == ProtocolGEELY180ProKX11 || mProtocolIndex == ProtocolGEELY180ProE245) {
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 02 DC 01", QString::fromLocal8Bit("终止校准失败"));
            Utils::dely(20);
        }else{
            SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], (mProtocolIndex == ProtocolBAIC || mProtocolIndex == ProtocolBAIC_BE12) ? "31 02 12 7B" : "31 02 03 02", QString::fromLocal8Bit("终止校准失败"));
        }
        emit message(index, QString::fromLocal8Bit("终止校准成功, 校准结束"));

        emit calibrationFinished(index);
    }
}

void AfterSaleCalibrationWorker::result_GWM(int index)
{
    UDSFrame responseFrame;
    responseFrame.clear();
    QString data = "22 FC 11";
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取校准结果失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    bool bSuccess = false;
    bool bFail = false;

    emit message(index, QString::fromLocal8Bit("读取校准结果成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
    quint8 process = responseFrame.mData[4];
    switch( (responseFrame.mData[3] & 0xF) ){
    case 0x00:
        emit message(index, QString::fromLocal8Bit("校准未开始") );
        break;
    case 0x01:
        emit message(index, QString::fromLocal8Bit("校准正在执行 【%1%】").arg(process));
        break;
    case 0x02:
        emit message(index, QString::fromLocal8Bit("校准完成") );
        bSuccess = true;
        break;
    case 0x03:
        emit message(index, QString::fromLocal8Bit("校准失败") );
        bFail = true;
        break;
    default:
        emit message(index, QString::fromLocal8Bit("未知校准状态") );
        bFail = true;
        break;
    }

    if( bSuccess || bFail ){
        emit calibrationFinished(index);
    }

    //bSuccess = true;
    if( bSuccess ){
        //读取角度
        responseFrame.clear();
        data = "22 FC 01";
        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame)) {
            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
            emit message(index, QString::fromLocal8Bit("读取校准角度失败") + mUDS[index]->errorString());
            return;
        }
        quint8 stat = responseFrame.mData[3];
        quint16 _angle = (quint16)((((quint16)responseFrame.mData[4]) << 8) + (quint8)responseFrame.mData[5]);
        double angle = _angle * 0.01;
        QString msg = "";
        switch ( stat ) {
        case 0:
            msg = QString::fromLocal8Bit("雷达位置偏向不确定");
            break;
        case 1:
            msg = QString::fromLocal8Bit("左边雷达位置相对车辆推进线向左偏或右边雷达位置相对车辆推进线向右偏");
            break;
        case 2:
            msg = QString::fromLocal8Bit("右边雷达位置相对车辆推进线向左偏或左边雷达位置相对车辆推进线向右偏");
            break;
        default:
            msg = QString::fromLocal8Bit("未知角度状态");
            break;
        }
        emit message(index, QString::fromLocal8Bit("%1 [angle=%2]").arg(msg).arg(angle) );

        //清除故障码
//        responseFrame.clear();
//        data = "14 FF FF FF";
//        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame)) {
//            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
//            emit message(index, QString::fromLocal8Bit("清除故障码失败") + mUDS[index]->errorString());
//            return;
//        }

//        //读取故障码
//        responseFrame.clear();
//        data = "19 0A";
//        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame )) {
//            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
//            emit message(index, QString::fromLocal8Bit("读取故障码失败") + mUDS[index]->errorString());
//            return;
//        }

//        QByteArray dtcArray = responseFrame.mData.mid( 3 );
//        if( dtcArray.size() > 0 ){
//            emit message(index, QString::fromLocal8Bit("故障码读取结果:") );
//        }
//        for( int i=0; i<dtcArray.size(); i+=4 ){
//            QByteArray dtc = dtcArray.mid( i, 4 );
//            quint8* pData = (quint8*)dtc.data();
//            if( pData[3] != 0 ){
//                emit message(index, dtc.toHex(' ') );
//            }
//        }
    }
}

void AfterSaleCalibrationWorker::result_Hozon(int index)
{
    UDSFrame responseFrame;
    responseFrame.clear();

    bool bEnd = false;
    bool bNext = false;

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "31 03 35 F2", QString::fromLocal8Bit("读取例程状态失败"));
    emit message(index, QString::fromLocal8Bit("读取例程状态成功"));
    quint8 state = *( (quint8*)responseFrame.mData.mid( 4, 1 ).data() );
    quint8 process = *( (quint8*)responseFrame.mData.mid( 5, 1 ).data() );
    switch (state) {
    case 0x00:
        emit message(index, QString::fromLocal8Bit("例程初始化 【%1】").arg(process));
        break;
    case 0x01:
        emit message(index, QString::fromLocal8Bit("标定进行中 【%1】").arg(process));
        break;
    case 0x02:
        emit message(index, QString::fromLocal8Bit("标定成功 【%1】").arg(process));
        bNext = true;
        break;
    case 0x03:
        emit message(index, QString::fromLocal8Bit("标定不成功 【%1】").arg(process));
        bEnd = true;
    default:
        break;
    }

    if( bNext || bEnd){
        responseFrame.clear();
        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString( "22 F1 04" ), &responseFrame)) {
            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
            emit message(index, QString::fromLocal8Bit("读取校准状态失败") + mUDS[index]->errorString());
            emit calibrationFinished(index);
            return;
        }

        state = *( (quint8*)responseFrame.mData.mid( 4, 1 ).data() );
        //    stat = 3;
        switch ( state ) {
        case 0x0:  //下一步
            bNext = true;
            break;
        default:
        {
            bNext = false;
            QString text;
            if (state & 0x01) {
                text.append(QString::fromLocal8Bit("【标定超时】"));
            }
            if (state & 0x02)  {
                text.append(QString::fromLocal8Bit("【角度超范】"));
            }
            if (state & 0x04)  {
                text.append(QString::fromLocal8Bit("【目标不充分】"));
            }
            if (state & 0x08)  {
                text.append(QString::fromLocal8Bit("【电压超范围】"));
            }
            if (state & 0x10)  {
                text.append(QString::fromLocal8Bit("【未知异常】"));
            }
            emit message(index, QString::fromLocal8Bit("标定失败! %1").arg(text) );

            bEnd = true;
        }
            break;
        }
    }

    if( bNext ){
        responseFrame.clear();
        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString( "22 F1 03" ), &responseFrame)) {
            emit message(index, QString::fromLocal8Bit("读取校准角度失败") + mUDS[index]->errorString());
            emit calibrationFinished(index);
            return;
        }
        emit message(index, responseFrame.mData.toHex(' ') );
        quint8* data = (quint8*)responseFrame.mData.mid( 3, 4 ).data();
        double angle = ( ( ( (quint16)data[0] ) << 8 ) + data[1] ) * 0.01 - 180;
        double eangle = ( ( ( (quint16)data[2] ) << 8 ) + data[3] ) * 0.01 - 180;
        emit message(index, QString::fromLocal8Bit("水平角度=%1, 垂直角度=%2").arg(angle).arg(eangle) );
        bEnd = true;
    }

    if( !bEnd ){
        return;
    }

//    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "11 01", QString::fromLocal8Bit("重启ECU"));
//    emit message(index, QString::fromLocal8Bit("重启ECU成功"));

    emit message(index, QString::fromLocal8Bit("延时3s"));
    Utils::dely(3000);

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "14 FF FF FF", QString::fromLocal8Bit("清除DTC失败"));
    emit message(index, QString::fromLocal8Bit("清除DTC成功"));

    SEND_DATA_HEX_RESPONSE(mPhysicalAddress[mProtocolIndex][index], "19 02 09", QString::fromLocal8Bit("读取DTC失败"));
    emit message(index, QString::fromLocal8Bit("读取DTC成功"));


    emit calibrationFinished(index);
}

void AfterSaleCalibrationWorker::result_GELLY_180Pro(int index)
{
    UDSFrame responseFrame;
    responseFrame.clear();

    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("31 03 DC 01"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("请求标定运行结果失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("请求标定运行结果成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');

    quint16 process = (quint16)((((quint16)responseFrame.mData[4]) << 8) + (quint8)responseFrame.mData[5]);
    switch (process) {
    case 0x2204:
        emit message(index, QString::fromLocal8Bit("标定正在执行"));
        return;
    case 0x2000:
        emit message(index, QString::fromLocal8Bit("标定成功"));
        break;
    case 0x2001:
        emit message(index, QString::fromLocal8Bit("标定失败"));
        break;
    }

    responseFrame.clear();
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("22 D0 5B"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取校准状态失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("读取校准状态成功"));
    emit message(index, responseFrame.mData.toHex(' '));

//    emit message(index, responseFrame.mData.toHex(' '));
    QString text;
    switch (responseFrame.mData[3])
    {
    case 0x01:
        text.append(QString::fromLocal8Bit("【雷达已校准】"));
        break;
    case 0x04:
        text.append(QString::fromLocal8Bit("【雷达故障】"));
        break;
    case 0x0D:
        text.append(QString::fromLocal8Bit("【误差过大】"));
        break;
    case 0x15:
        text.append(QString::fromLocal8Bit("【雷达未校准】"));
        break;
    }
    emit message(index, text);

    responseFrame.clear();
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("22 D0 39"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取雷达水平角偏差失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("读取雷达水平角偏差成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    emit message(index, QString::fromLocal8Bit("【水平偏差角度 %1°】").arg(responseFrame.mData[3]));

    responseFrame.clear();
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("19 02 29"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取DTC失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }
    emit message(index, QString::fromLocal8Bit("读取DTC成功"));
    emit message(index, responseFrame.mData.toHex(' '));

    responseFrame.clear();
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("14 FF FF FF"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("清除DTC失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }
    emit message(index, QString::fromLocal8Bit("清除DTC成功"));

    emit calibrationFinished(index);
}

void AfterSaleCalibrationWorker::result_GELLY_LNKY(int index)
{

    UDSFrame responseFrame;
    responseFrame.clear();

    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("31 03 DC 01"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("请求标定运行结果失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("请求标定运行结果成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');

    quint16 process = (quint16)((((quint16)responseFrame.mData[4]) << 8) + (quint8)responseFrame.mData[5]);
    switch (process) {
    case 0x2204:
        emit message(index, QString::fromLocal8Bit("标定正在执行"));
        return;
    case 0x2000:
        emit message(index, QString::fromLocal8Bit("标定成功"));
        break;
    case 0x2001:
        emit message(index, QString::fromLocal8Bit("标定失败"));
        break;
    }

    responseFrame.clear();
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("22 D0 5B"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取校准状态失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("读取校准状态成功"));
    emit message(index, responseFrame.mData.toHex(' '));

//    emit message(index, responseFrame.mData.toHex(' '));
    QString text;
    switch (responseFrame.mData[3])
    {
    case 0x15:
        text.append(QString::fromLocal8Bit("【雷达已校准】"));
        break;
    case 0x0D:
        text.append(QString::fromLocal8Bit("【雷达故障】"));
        break;
    case 0x04:
        text.append(QString::fromLocal8Bit("【误差过大】"));
        break;
    case 0x01:
        text.append(QString::fromLocal8Bit("【雷达未校准】"));
        break;
    }
    emit message(index, text);

    responseFrame.clear();
    Utils::dely(20);
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("22 D0 39"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取雷达水平角偏差失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("读取雷达水平角偏差成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    emit message(index, QString::fromLocal8Bit("【水平偏差角度 %1°】").arg((int8_t)responseFrame.mData[3] * 0.1f));

    if (mProtocolIndex == ProtocolGEELY180ProKX11) {
        responseFrame.clear();
        if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("22 FD 03"), &responseFrame)) {
            qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
            emit message(index, QString::fromLocal8Bit("读取雷达安装角失败") + mUDS[index]->errorString());
            emit calibrationFinished(index);
            return;
        }

        emit message(index, QString::fromLocal8Bit("读取雷达安装角成功"));
        emit message(index, responseFrame.mData.toHex(' '));
        emit message(index, QString::fromLocal8Bit("【安装角 %1°】").arg((((int32_t)responseFrame.mData[3] << 8) + (int32_t)responseFrame.mData[4]) * 0.01f));
    }

    responseFrame.clear();
    Utils::dely(20);
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("19 02 29"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取DTC失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }
    emit message(index, QString::fromLocal8Bit("读取DTC成功"));
    emit message(index, responseFrame.mData.toHex(' '));

    responseFrame.clear();
    Utils::dely(20);
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString("14 FF FF FF"), &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("清除DTC失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }
    emit message(index, QString::fromLocal8Bit("清除DTC成功"));

    emit calibrationFinished(index);
}

void AfterSaleCalibrationWorker::result(int index)
{
//    QMutexLocker lock( &mMutex[index] ); //此函数不可重入
    // 加锁(对象释放时自动解锁)
    std::lock_guard<std::mutex> lg(mMutex[index]);

    switch ( mProtocolIndex ) {
    case ProtocolGWM:
        return result_GWM( index );
    case ProtocolHozon:
        return result_Hozon(index);
    case ProtocolGEELY180Pro:
        return result_GELLY_180Pro(index);
    case ProtocolGEELY180ProKX11:
    case ProtocolGEELY180ProE245:
        return result_GELLY_LNKY(index);
    default:
        break;
    }

    UDSFrame responseFrame;
    responseFrame.clear();

    QString data = "";
    switch ( mProtocolIndex ) {
    case ProtocolBAIC:
    case ProtocolBAIC_BE12:
        data = "31 03 12 7B";
        break;
    case ProtocolGEELY:
        data = "31 03 DC 01";
        break;
    default:
        data = "31 03 03 02";
        break;
    }

    //if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], QString((mProtocolIndex == ProtocolBAIC) ? "31 03 12 7B" : "31 03 03 02"), &responseFrame)) {
    if (!mUDS[index]->sendData(mPhysicalAddress[mProtocolIndex][index], data, &responseFrame)) {
        qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
        emit message(index, QString::fromLocal8Bit("读取校准结果失败") + mUDS[index]->errorString());
        emit calibrationFinished(index);
        return;
    }

    emit message(index, QString::fromLocal8Bit("读取校准结果成功"));
    emit message(index, responseFrame.mData.toHex(' '));
    qDebug() << __FUNCTION__ << __LINE__ << responseFrame.mData.toHex(' ');
    quint8 process = responseFrame.mData[6];
    if ((responseFrame.mData[4] & 0xF) == 0x01)
    {
        emit message(index, QString::fromLocal8Bit("校准正在执行 【%1%】").arg(process));
//        return;
    }

//    emit message(index, responseFrame.mData.toHex(' '));
    QString text;
    switch (responseFrame.mData[4] & 0xF)
    {
    case 0x0:
        text.append(QString::fromLocal8Bit("【校准不在执行】"));
        break;
    case 0x1:
        text.append(QString::fromLocal8Bit("【校准正在执行】"));
        break;
    case 0x2:
        text.append(QString::fromLocal8Bit("【校准写入NVM失败】"));
        break;
    case 0x3:
        text.append(QString::fromLocal8Bit("【校准执行超时】"));
        break;
    case 0x4:
        text.append(QString::fromLocal8Bit("【校准正确执行完毕】"));
        break;
    case 0x5:
        text.append(QString::fromLocal8Bit("【校准执行中止】"));
        break;
    }
    switch (responseFrame.mData[4] & 0xF0)
    {
    case 0x00:
        text.append(QString::fromLocal8Bit("【校准结果未出】"));
        break;
    case 0x10:
        text.append(QString::fromLocal8Bit("【校准结果不正确】"));
        break;
    case 0x20:
        text.append(QString::fromLocal8Bit("【校准结果正确】"));
        break;
    }

    text.append(QString::fromLocal8Bit("【"));
    if (responseFrame.mData[5] & 0x1)
    {
        text.append(QString::fromLocal8Bit("速度过慢"));
    }
    if (responseFrame.mData[5] & 0x2)
    {
        text.append(QString::fromLocal8Bit("速度过快"));
    }
    if (responseFrame.mData[5] & 0x4)
    {
        text.append(QString::fromLocal8Bit("横摆角过大"));
    }
    if (responseFrame.mData[5] & 0x8)
    {
        text.append(QString::fromLocal8Bit("加速过快"));
    }
    if (responseFrame.mData[5] & 0x10)
    {
        text.append(QString::fromLocal8Bit("目标不充分"));
    }
    if (responseFrame.mData[5] & 0x20)
    {
        text.append(QString::fromLocal8Bit("雷达失明"));
    }
    text.append(QString::fromLocal8Bit("】"));

    responseFrame.mData[6];

    double angle = 0.0;
    quint16 _angle = (quint16)((((quint16)responseFrame.mData[7]) << 8) + (quint8)responseFrame.mData[8]);
    if (_angle < 0x8000)
    {
        angle = _angle * 0.01;
    }
    else
    {
        angle = (_angle - 0xFFFF) * 0.01;
    }
    text.append(QString::fromLocal8Bit("【水平偏差角度 %1°】").arg(angle));
    angle = 0.0;
    _angle = (quint16)((((quint16)responseFrame.mData[11]) << 8) + (quint8)responseFrame.mData[12]);


    if (mProtocolIndex == ProtocolBAIC || mProtocolIndex == ProtocolBAIC_BE12) {
        angle = _angle * 0.01;
        text.append(QString::fromLocal8Bit("【安装角度 %1°】").arg(angle));
        _angle = (quint16)((((quint16)responseFrame.mData[13]) << 8) + (quint8)responseFrame.mData[14]);
        text.append(QString::fromLocal8Bit("采集的目标总数 %1°】").arg(angle));
        _angle = (quint16)((((quint16)responseFrame.mData[15]) << 8) + (quint8)responseFrame.mData[16]);
        text.append(QString::fromLocal8Bit("运行流程记录 %1°】").arg(angle));
    }else{
        angle = _angle * 0.1;
        text.append(QString::fromLocal8Bit("【安装角度 %1°】").arg(angle));
    }

    emit message(index, text);

    if (responseFrame.mData.data()[4] != 0x24)
    {
        return;
    }

    emit calibrationFinished(index);
}

void AfterSaleCalibrationWorker::canFrame(const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        if (frame.id() == mUDS[i]->responseID())
        {
            mUDS[i]->appendCanFrame(frame);
            emit sendOrRecvCanFrame( i, false, frame.id(), frame.dataHex() );
            break;
        }
    }
}

void AfterSaleCalibrationWorker::frameTransmited(const Devices::Can::CanFrame &frame, bool success)
{
    for (int i = 0; i < (sizeof (mUDS) / sizeof (mUDS[0])); ++i)
    {
        if ( frame.id() == mPhysicalAddress[mProtocolIndex][i] || frame.id() == mFunctionAddress[mProtocolIndex][i] ){
            emit sendOrRecvCanFrame( i, true, frame.id(), frame.dataHex() );
            break;
        }
    }
}



} // namespace Functions
