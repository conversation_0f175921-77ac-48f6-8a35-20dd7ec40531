﻿/**
 * @file apar_manager.h
 * @brief APAR模块接口声明
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2022-10-02
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-02 <td>1.0     <td>Wang Juhua     <td>初始版本
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _APAR_MANAGER_
#define _APAR_MANAGER_

#ifndef PC_DBG_FW
#include "apar_types.h"
#include "boot_app_defines.h"


/** @defgroup APAR Application Part Data
 * @{
 */



/**
 * @brief 获取APAR模块内部数据指针的接口
 * @return const APAR_CfgData_t* 返回的数据的指针
 */
const APAR_CfgData_t *APAR_getAparDataPtr();

/**
 * @brief 获取配置中的TX通道
 * @return uint8_t 
 */
uint8_t APAR_getAparTxSelectMode();

/**
 * @brief 获取水平安装角度
 * @return float 返回的水平安装角度，单位为deg
 * 
 */
float APAR_getAparInstallAzimuthAngle();

/**
 * @brief 获取雷达的ID
 * @return uint8_t 返回的雷达ID
 */
uint8_t APAR_getAparRadarId();

/**
 * @brief 设置雷达的ID
 * @param [in] radarId 输入参数，需要设置的雷达ID
 * @return int32_t 标识是否返回成功
 * @retval 0=返回成功
 * @retval 非0=返回失败
 */
int32_t APAR_setAparRadarId(uint8_t radarId);

// int32_t APAR_readAparDataFromFlash(void);

/**
 * @brief 将APAR所有数据存入Flash中
 * @return int32_t 标识是否返回成功
 * @retval 0=返回成功
 * @retval 非0=返回失败
 */
int32_t APAR_writeAparDataToFlash(void);

/**
 * @brief 初始化APAR数据
 * @details 如果Flash中存储的数据正确，则使用Flash中读取的数据；如果读取Flash过程异常或者Flash数据异常，使用默认值
 * @return int32_t 
 */
int32_t APAR_initAparData(void);

/**
 * @brief 获取SoC芯片序列号
 * @return uint32_t 返回的SoC序列号
 */
uint32_t APAR_getChipSerialNum();


/**
 * @}
 */
#else
#include <math.h>
#include "app/apar/apar_types.h"

/**
 * @brief 获取APAR模块内部数据指针的接口
 * @return const APAR_CfgData_t* 返回的数据的指针
 */
APAR_CfgData_t *APAR_getAparDataPtr();

// float APAR_getAparInstallAzimuthAngle();
#endif


#endif
