#ifndef FRAMECONVERTMODEL_H
#define FRAMECONVERTMODEL_H

#include <QObject>
#include <QAbstractTableModel>
#include <QTimer>

class IFrameConver;

namespace Devices {
namespace Can {
    class CanFrame;
    class DeviceManager;
}
}

class FrameConvertModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    FrameConvertModel( Devices::Can::DeviceManager* deviceManager, QObject *parent = nullptr );

    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    Qt::ItemFlags flags(const QModelIndex &index) const override;

signals:
    void startOrStop( bool bStart );
    void sendComverResult();

private slots:
    void update();


private:
    QStringList mHeader;
    QList<IFrameConver*> mFrameConverList;
    Devices::Can::DeviceManager* mDeviceManager;
    QTimer mSendTimer;
};

#endif // FRAMECONVERTMODEL_H
