﻿#ifndef IEARLYWARNINGLUA_H
#define IEARLYWARNINGLUA_H

#include "iearlywarningscript.h"

typedef struct lua_State lua_State;

class EarlyWarningLua : public IEarlyWarningScript
{
    Q_OBJECT
public:
    explicit EarlyWarningLua(QObject *parent = nullptr);

    /** @brief 加载脚本 */
    bool loadScript(const QString &filename) override;
    /** @brief 卸载脚本 */
    bool unloadScript() override;
    /** @brief 自车满足的报警类型 */
    quint16 vehicleEarlyWarning(int radarID, quint16 alarmTypes, VehicleData *vehicleData) override;
    /** @brief 计算告警 */
    bool earlyWaring(int radarID, Target *target, quint16 alarmTypes, quint16 vehicleAlarmTypes, VehicleData *vehicleData, QList<EarlyWarningData> &earlyWarningDatas) override;

signals:

private:

    lua_State *mLuaState;
};

#endif // IEARLYWARNINGLUA_H
