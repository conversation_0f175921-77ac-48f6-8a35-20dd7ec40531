﻿#ifndef CHENGTECHRADARSWITCH_H
#define CHENGTECHRADARSWITCH_H

#include <QMainWindow>

#include <devices/ideviceworker.h>

namespace Devices {
namespace Can {
class DeviceManager;
}
}

class CTAnalysisWorker;

QT_BEGIN_NAMESPACE
namespace Ui { class ChengTechRadarSwitch; }
QT_END_NAMESPACE

class ChengTechRadarSwitch : public QMainWindow
{
    Q_OBJECT

public:
    ChengTechRadarSwitch(QWidget *parent = nullptr);
    ~ChengTechRadarSwitch();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void stateChanged(bool opened);

    void log(const QString &text);

    void on_actionSelectDevice_triggered();

    void on_actionOpenDevice_triggered();

    void on_pushButtonRawOpen_clicked();

    void on_pushButtonRawClose_clicked();

private:
    /** @brief 设备发生改变 */
    void deviceChanged(Devices::Can::DeviceSettings deviceSettings);

    void loadSettings();
    void savesettings();

    Ui::ChengTechRadarSwitch *ui;

    Devices::Can::DeviceManager *mDeviceManager{0};
    CTAnalysisWorker *mCTAnalysisWorker{0};
};
#endif // CHENGTECHRADARSWITCH_H
