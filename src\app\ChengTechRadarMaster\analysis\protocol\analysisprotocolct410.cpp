﻿#include "analysisprotocolct410.h"

#include "CTMRR410.h"
#include <QDebug>
#include <QtMath>
#include <QThread>
#include <iostream>

#include "utils/utils.h"

namespace Analysis {
namespace Protocol {

bool AnalysisProtocolCT410::mRaw600ByChannel{false};
int AnalysisProtocolCT410::mBYDHDChannelRadarID[8]{4, 5, 6, 7, 0, 0, 0, 0};                ///< BYD高阶通道-雷达ID设置

qint8 AnalysisProtocolCT410::radarID(const Devices::Can::CanFrame &frame)
{
    if (mRaw600ByChannel) {
        if (frame.channelIndex() >= 8) {
            return -1;
        }
        return mBYDHDChannelRadarID[frame.channelIndex()];
    }

    quint8 radarID = frame.radarID();
    if( radarID < 4 || radarID > 7 )
        return -1;

    switch ( frame.idN() ) {
    case 0x3F0: // 车身信息
    case 0x400: // 原始点头
    case 0x410: // 原始点
    case 0x430: // 跟踪点头
    case 0x440: // 跟踪点
    case 0x4C0: // 报警信息
    case 0x4D0: // 故障信息
    case 0x4E0: // 故障信息
    case 0x4F0: // 结束帧
    case 0x600: // 原始点头
    case 0x710: // 原始点
    case 0x7A0: // ADC 1dfft 2dfft数据
        return radarID;
    default:
        break;
    }
    return -1;
}

AnalysisProtocolCT410::AnalysisProtocolCT410(AnalysisWorker *analysisWorker, QObject *parent)
    : AnalysisProtocolCT(analysisWorker, parent)
{
    memset(mRawID, 0, sizeof (mRawID));
    mProtocolType = ProtocolChengTech410;
}

bool AnalysisProtocolCT410::analysisFrame(const Devices::Can::CanFrame &frame)
{
    return parseFrame(frame);
}

void AnalysisProtocolCT410::setChannelRadarID(bool raw600ByChannel, int *channelRadarID, int size)
{
    mRaw600ByChannel = raw600ByChannel;
    for (int i = 0; i < size && i < (sizeof (mBYDHDChannelRadarID) / sizeof (mBYDHDChannelRadarID[0])); ++i) {
        mBYDHDChannelRadarID[i] = channelRadarID[i];
        qDebug() << __FUNCTION__ << __LINE__ << channelRadarID[i] << mBYDHDChannelRadarID[i];
    }
}

bool AnalysisProtocolCT410::parseFrame(const Devices::Can::CanFrame &frame)
{
    int _radarID = frame.radarID();
    if (mRaw600ByChannel) {
        if (frame.channelIndex() >= 8) {
            return false;
        }
        _radarID = mBYDHDChannelRadarID[frame.channelIndex()];
    }
    if (_radarID < 4 || _radarID > 7) {
        return false;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << _radarID << frame.idHex();

    bool bEndFrame = false;
    bool ret = false;
    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[_radarID];
    analysisData->mRadarID = _radarID;
    switch (frame.idN()) {
    case 0x3F0: // 车身信息
        ret = parseVehicleInfomation(frame, analysisData);
        break;
    case 0x400: // 原始点头
        ret = parseRawTargetHeader(frame, analysisData);
        if (ret) {
            if (mNewBegin[_radarID]) {
                qDebug() << __FUNCTION__ << __LINE__ << _radarID << "frame loss!!!";
            }
            mNewBegin[_radarID] = true;
        }
        break;
    case 0x410: // 原始点
        ret = parseRawTarget(frame, analysisData);
        break;
    case 0x430: // 跟踪点头
        ret = parseTrackTargetHeader(frame, analysisData);
        break;
    case 0x440: // 跟踪点
        ret = parseTrackTarget(frame, analysisData);
        break;
    case 0x450:
    case 0x460:
    case 0x470:
    case 0x480:
    case 0x490:
    case 0x4A0:
    case 0x4B0:
        break;
    case 0x4C0: // 报警信息
        ret = parseAlarmInfomation(frame, analysisData);
        break;
    case 0x4D0: // 故障信息
        ret = parse0x4DN(frame, analysisData);
        break;
    case 0x4E0: // 故障信息
        ret = parse0x4EN(frame, analysisData);
        break;
    case 0x4F0: // 结束帧
        analysisData->mEndFrameData.mRecv410FrameCount++;//结束帧的处理函数中会将解析结果发出出去，故需在解析前先计数
        ret = parseEndFrame(frame, analysisData);
        bEndFrame = true;
        mNewBegin[_radarID] = false;
        break;
    case 0x600:
    {
        if (mRaw600ByChannel) {
            switch (frame.id()) {
            case 0x600: // 原始点头
                ret = parse0x600_VERSION_7(frame, analysisData);
                break;
            case 0x601: // 原始点目标信息
                switch ((int)analysisData->mTargets[FrameRawTarget].mTargetHeader.mProtocolVersion) {
                case 7:
                    ret = parse0x710_64_V7(frame, analysisData);
                    break;
                case 8:
                    ret = parse0x710_64_V8(frame, analysisData);
                    break;
                }
                break;
            case 0x602: // 车身信息
                ret = parseVehicleInfomation(frame, analysisData);
                break;
            case 0x603: // 报警信息
                ret = parseAlarmInfomation(frame, analysisData);
                break;
            case 0x604: // 时间信息等
                ret = parse0x604(frame, analysisData);
                break;
            case 0x605: // 结束帧
                analysisData->mEndFrameData.mRecv410FrameCount++;//结束帧的处理函数中会将解析结果发出出去，故需在解析前先计数
                ret = parseEndFrame(frame, analysisData);
                bEndFrame = true;
                mNewBegin[_radarID] = false;
                break;
            case 0x606: //雷达复位
                radarReset( frame );
                ret = true;
                break;
            }
        } else {
            ret = parse0x600_VERSION_7(frame, analysisData);
        }
    }
        break;
    case 0x250:
    case 0x710:
        switch ((int)analysisData->mTargets[FrameRawTarget].mTargetHeader.mProtocolVersion) {
        case 7:
            ret = parse0x710_64_V7(frame, analysisData);
            break;
        case 8:
            ret = parse0x710_64_V8(frame, analysisData);
            break;
        }
        break;
    case 0x700://雷达版本
        ret = parseRadarVersion( frame, analysisData );
        break;
    case 0x7A0:
        ret = parseADC_1DFFT_2DFFT(frame, analysisData);
        break;
    case 0x5F0:  //雷达复位
        radarReset( frame );
        ret = true;
        break;
    default:
//        qDebug() << __FUNCTION__ << __LINE__ << frame.deviceIndex() << frame.channelIndex() << frame.idHex() << frame.dataHex();
        return false;
    }

    if( frame.idN() != 0x4F0 && frame.idN() != 0x700 ){
        analysisData->mEndFrameData.mRecv410FrameCount++;
    }

    //qDebug() << __FUNCTION__ << __LINE__ << ":" << frame.idHex() << analysisData->mEndFrameData.mRecv410FrameCount;
    mAnalysisWorker->analysisRadarFrameEnd( _radarID, frame, bEndFrame );
    return ret;
}

bool AnalysisProtocolCT410::parseVehicleInfomation(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    switch (frame.length()) {
    case 32:
        return parseVehicleInfomation_32(frame, analysisData);
    case 64:
        return parseVehicleInfomation_64(frame, analysisData);
    default:
        break;
    }

    return false;
}

bool AnalysisProtocolCT410::parseVehicleInfomation_32(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 32) {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![32]!" << frame.idHex() << frame.dataHex();
        return false;
    }
    const uint8_t *data = (const uint8_t *)frame.data().data();

    VehicleData &vehicleData = analysisData->mVehicleData;
    vehicleData.mYawRate = ((((data[1] & 0xFFU) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.00024) - 2.093) * 180.0f / M_PI;  //转换为deg/s
    vehicleData.mSwitchRCTAFunction = ((data[2] & 0x80U) >> 7);
    vehicleData.mSwitchRCTBFunction = ((data[2] & 0x40U) >> 6);
    vehicleData.mSwitchRCWFunction = ((data[2] & 0x20U) >> 5);
    vehicleData.mSwitchBSDFunction = ((data[2] & 0x10U) >> 4);
    vehicleData.mSwitchDOWFunction = ((data[2] & 0x8U) >> 3);
    vehicleData.mSwitchMainFunction = ((data[2] & 0x4U) >> 2);
    vehicleData.mSwitchFCTAFunction = ((data[2] & 0x2U) >> 1);
    vehicleData.mSwitchFCTBFunction = (data[2] & 0x1U);
    vehicleData.mVehicleSpeed = ((((data[4] & 0xF8U) >> 3) + (((uint16_t)data[3] & 0xFFU) << 5)) * 0.05625);
    vehicleData.mDoorFrontLeft = ((data[4] & 0x6U) >> 1);
    vehicleData.mDoorFrontRight = (((data[5] & 0x80U) >> 7) + (((uint16_t)data[4] & 0x1U) << 1));
    vehicleData.mDoorRearLeft = ((data[5] & 0x60U) >> 5);
    vehicleData.mDoorRearRight = ((data[5] & 0x18U) >> 3);
    vehicleData.mGear = (data[5] & 0x7U);
    vehicleData.mKeyStatus = ((data[6] & 0xF0U) >> 4);
    vehicleData.mSecurityLock = (data[6] & 0xFU);
    vehicleData.mTurnLightLeft = ((data[7] & 0x80U) >> 7);
    vehicleData.mTurnLightRight = ((data[7] & 0x40U) >> 6);
    vehicleData.mBrakePedalStatus = ((data[7] & 0x30U) >> 4);
    vehicleData.mWheelSpeedDirectionFontLeft = ((data[7] & 0x8U) >> 3);
    vehicleData.mWheelSpeedDirectionFontRight = ((data[7] & 0x4U) >> 2);
    vehicleData.mWheelSpeedDirectionRearLeft = ((data[7] & 0x2U) >> 1);
    vehicleData.mWheelSpeedDirectionRearRight = (data[7] & 0x1U);
    vehicleData.mAcceleratorPedalActiveLevel = ((data[8] & 0xFEU) >> 1);
    vehicleData.mBrakePedalActiveLevel = (((data[9] & 0xFCU) >> 2) + (((uint16_t)data[8] & 0x1U) << 6));
    vehicleData.mTrailerStatus = ((data[9] & 0x2U) >> 1);
    vehicleData.mESPFailStatus = (data[9] & 0x1U);
    vehicleData.mLongitudinalAcceleration = ((((data[11] & 0xFFU) + (((uint16_t)data[10] & 0xFFU) << 8)) * 0.00098) - 21.592);
    vehicleData.mLateralAcceleration = ((((data[13] & 0xFFU) + (((uint16_t)data[12] & 0xFFU) << 8)) * 0.00098) - 21.592);
    vehicleData.mWheelSpeedFontLeft = ((((data[15] & 0xF8U) >> 3) + (((uint16_t)data[14] & 0xFFU) << 5)) * 0.05625);
    vehicleData.mSwitchJAFunction = (data[15] & 0x1U);
    vehicleData.mWheelSpeedFontRight = ((((data[17] & 0xF8U) >> 3) + (((uint16_t)data[16] & 0xFFU) << 5)) * 0.05625);
    vehicleData.mWheelSpeedRearLeft = ((((data[19] & 0xC0U) >> 6) + (((uint32_t)data[18]) << 2) + (((uint32_t)data[17] & 0x7U) << 10)) * 0.05625);
    vehicleData.mWheelSpeedRearRight = ((((data[20] & 0xFEU) >> 1) + (((uint16_t)data[19] & 0x3FU) << 7)) * 0.05625);
    vehicleData.mSteeringWheelAngle = (((((data[22] & 0xF0U) >> 4) + (((uint32_t)data[21]) << 4) + (((uint32_t)data[20] & 0x1U) << 12)) * 0.2) - 738);
    vehicleData.mVehicleRollingCount = (data[22] & 0xFU);
    vehicleData.mRadius = (((((data[26] & 0xC0U) >> 6) + (((uint32_t)data[25]) << 2) + (((uint32_t)data[24] & 0xFFU) << 10)) * 0.25) - 32767);
    vehicleData.mVehicleChecksum = (data[31]);

//    qDebug() << __FUNCTION__ << __LINE__ << vehicleData.mGear << (data[5] & 0x7U);

    return true;
}

static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
    uint32_t mask = ((1 << bits) - 1);
    uint32_t extracted = data & mask;
    int32_t sign_extended = (extracted & (1 << (bits - 1))) ? (int)(extracted | (~mask)) : (int)extracted;
    return sign_extended;
}

bool AnalysisProtocolCT410::parseVehicleInfomation_64(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 64) {
        return false;
    }
    const uint8_t *data = (const uint8_t *)frame.data().data();

    VehicleData &vehicleData = analysisData->mVehicleData;
    vehicleData.mVehicleChecksum = (data[0]);
    vehicleData.mVehicleVersion = (data[1]);
    vehicleData.mYawRate = (((decode_sign_bit((data[3] & 0xFFU) + (((uint16_t)data[2] & 0xFFU) << 8), 16)) * 0.00024) - 2.093);
    vehicleData.mSwitchRCTAFunction = ((data[4] & 0x80U) >> 7);
    vehicleData.mSwitchRCTBFunction = ((data[4] & 0x40U) >> 6);
    vehicleData.mSwitchRCWFunction = ((data[4] & 0x20U) >> 5);
    vehicleData.mSwitchBSDFunction = ((data[4] & 0x10U) >> 4);
    vehicleData.mSwitchDOWFunction = ((data[4] & 0x8U) >> 3);
    vehicleData.mSwitchMainFunction = ((data[4] & 0x4U) >> 2);
    vehicleData.mSwitchFCTAFunction = ((data[4] & 0x2U) >> 1);
    vehicleData.mSwitchFCTBFunction = (data[4] & 0x1U);
    vehicleData.mVehicleSpeed = ((((data[6] & 0xF8U) >> 3) + (((uint16_t)data[5] & 0xFFU) << 5)) * 0.05625);
    vehicleData.mSwitchJAFunction = ((data[6] & 0x4U) >> 2);
    vehicleData.mDoorFrontLeft = ((data[7] & 0xC0U) >> 6);
    vehicleData.mDoorFrontRight = ((data[7] & 0x30U) >> 4);
    vehicleData.mDoorRearLeft = ((data[7] & 0xCU) >> 2);
    vehicleData.mDoorRearRight = (data[7] & 0x3U);
    vehicleData.mKeyStatus = ((data[8] & 0xF0U) >> 4);
    vehicleData.mSecurityLock = (data[8] & 0xFU);
    vehicleData.mTurnLightLeft = ((data[9] & 0x80U) >> 7);
    vehicleData.mTurnLightRight = ((data[9] & 0x40U) >> 6);
    vehicleData.mBrakePedalStatus = ((data[9] & 0x30U) >> 4);
    vehicleData.mDoorLockFrontLeft = ((data[9] & 0x8U) >> 3);
    vehicleData.mDoorLockFrontRight = ((data[9] & 0x4U) >> 2);
    vehicleData.mDoorLockRearLeft = ((data[9] & 0x2U) >> 1);
    vehicleData.mDoorLockRearRight = (data[9] & 0x1U);
    vehicleData.mWheelSpeedDirectionFontLeft = ((data[10] & 0xC0U) >> 6);
    vehicleData.mWheelSpeedDirectionFontRight = ((data[10] & 0x30U) >> 4);
    vehicleData.mWheelSpeedDirectionRearLeft = ((data[10] & 0xCU) >> 2);
    vehicleData.mWheelSpeedDirectionRearRight = (data[10] & 0x3U);
    vehicleData.mAcceleratorPedalActiveLevel = ((data[11] & 0xFEU) >> 1);
    vehicleData.mTrailerStatus = (data[11] & 0x1U);
    vehicleData.mBrakePedalActiveLevel = ((data[12] & 0xFEU) >> 1);
    vehicleData.mESPFailStatus = (data[12] & 0x1U);
    vehicleData.mLongitudinalAcceleration = (((decode_sign_bit((data[14] & 0xFFU) + (((uint16_t)data[13] & 0xFFU) << 8), 16)) * 0.00098) - 21.592);
    vehicleData.mLateralAcceleration = (((decode_sign_bit((data[16] & 0xFFU) + (((uint16_t)data[15] & 0xFFU) << 8), 16)) * 0.00098) - 21.592);
    vehicleData.mWheelSpeedRearLeft = ((((data[18] & 0xF8U) >> 3) + (((uint16_t)data[17] & 0xFFU) << 5)) * 0.05625);
    vehicleData.mWheelSpeedFontRight = ((((data[20] & 0xF8U) >> 3) + (((uint16_t)data[19] & 0xFFU) << 5)) * 0.05625);
    vehicleData.mWheelSpeedRearLeft = ((((data[22] & 0xF8U) >> 3) + (((uint16_t)data[21] & 0xFFU) << 5)) * 0.05625);
    vehicleData.mWheelSpeedRearRight = ((((data[24] & 0xF8U) >> 3) + (((uint16_t)data[23] & 0xFFU) << 5)) * 0.05625);
    vehicleData.mSteeringWheelAngle = (((decode_sign_bit(((data[26] & 0xF8U) >> 3) + (((uint16_t)data[25] & 0xFFU) << 5), 13)) * 0.2) - 738);
    vehicleData.mRadius = (((((data[29] & 0xC0U) >> 6) + (((uint32_t)data[28]) << 2) + (((uint32_t)data[27] & 0xFFU) << 10)) * 0.25) - 32767);
    vehicleData.mVehicleRollingCount = (data[29] & 0xFU);
    vehicleData.mRadarInstallAngleH = ((decode_sign_bit(data[30], 8)) * 0.1);
    vehicleData.mRadarInstallAngleP = ((decode_sign_bit(data[31], 8)) * 0.1);
    vehicleData.mRadarSelfCaliAngleH = ((decode_sign_bit(data[32], 8)) * 0.1);
    vehicleData.mRadarSelfCaliAngleP = ((decode_sign_bit(data[33], 8)) * 0.1);
    vehicleData.mUseVehicleSpeed = ((((data[35] & 0xF0U) >> 4) + (((uint16_t)data[34] & 0xFFU) << 4)) * 0.06875);
    vehicleData.mUseYawRate = (((decode_sign_bit((data[36] & 0xFFU) + (((uint16_t)data[35] & 0xFU) << 8), 12)) * 0.00213) - 2.0943);
    vehicleData.mSteerWheelRotationSpdAK = ((decode_sign_bit(((data[38] & 0xFEU) >> 1) + (((uint16_t)data[37] & 0xFFU) << 7), 15)) * 0.1);
    vehicleData.mRadarWhlBaseY = ((((data[40] & 0xF8U) >> 3) + (((uint16_t)data[39] & 0xFFU) << 5)) * 0.001);
    vehicleData.mRadarWhlBaseX1 = ((((data[42] & 0xF8U) >> 3) + (((uint16_t)data[41] & 0xFFU) << 5)) * 0.001);
    vehicleData.mGear = (data[42] & 0x7U);
    vehicleData.mRadarWhlBaseX2 = ((((data[44] & 0xF8U) >> 3) + (((uint16_t)data[43] & 0xFFU) << 5)) * 0.001);
    vehicleData.mRadarOffsetHrz = ((decode_sign_bit((data[45] & 0xFFU) + (((uint16_t)data[44] & 0x3U) << 8), 10)) * 0.01);
    vehicleData.mRadarOffsetVrt = ((data[46]) * 0.01);
    vehicleData.mRadarSelfCaliBufCnt = (data[47]);

    return true;
}

bool AnalysisProtocolCT410::parseRawTargetHeader(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 16)
    {
//        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![16]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    mRawID[analysisData->mRadarID] = 0;

    const uint8_t *data = (const uint8_t *)frame.data().data();

    analysisData->clearTarget(FrameRawTarget);

    Targets &targets = analysisData->mTargets[FrameRawTarget];
    targets.mTargetHeader.mProtocolVersion = (data[0]);
    targets.mTargetHeader.mNoiseCurrent = (((data[1]) * 0.5) - 100);
    targets.mTargetHeader.mNoiseGlobal = (((data[2]) * 0.5) - 100);
    targets.mTargetHeader.mTargetCount = (((data[4] & 0xC0U) >> 6) + (((uint16_t)data[3] & 0xFFU) << 2));
    targets.mTargetHeader.mFunctionCalculationTime = (((data[5] & 0xE0U) >> 5) + (((uint16_t)data[4] & 0x3FU) << 3));
    targets.mTargetHeader.mResponseTaskCycleTime = (((data[6] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0x1FU) << 4));
    targets.mTargetHeader.mDataSource = (data[6] & 0x7U);
    targets.mTargetHeader.mNumberAfterCFAR = (data[7]);
    targets.mTargetHeader.mNumberAfterFilter = (data[8]);
    targets.mTargetHeader.mCurrentFrameModeIndex = ((data[9] & 0xF0U) >> 4);
    targets.mTargetHeader.mFrameModeNumber = (data[9] & 0xFU);

    //if( 0 == targets.mTargetHeader.mProtocolVersion ){ //旧版协议
    if( 1 != targets.mTargetHeader.mProtocolVersion ){ //旧版协议
        targets.mTargetHeader.mTargetRollingCount = (data[15] & 0xFU);
    }else if( 1 == targets.mTargetHeader.mProtocolVersion ){ //新版协议
        targets.mTargetHeader.mBlockageFlag = ((data[10] & 0x80U) >> 7);
        targets.mTargetHeader.mBlockagePercent = (((data[10] & 0x78U) >> 3) * 6.67);
        targets.mTargetHeader.mInterferenceFlag = ((data[10] & 0x4U) >> 2);
        targets.mTargetHeader.mInterferencePercent = ((((data[11] & 0xC0U) >> 6) + (((uint16_t)data[10] & 0x3U) << 2)) * 6.67);
        targets.mTargetHeader.mFrequencyHoppingState = ((data[11] & 0x20U) >> 5);
        targets.mTargetHeader.mTunnelsceneState = ((data[11] & 0x10U) >> 4);
        targets.mTargetHeader.mTargetRollingCount = (data[15] & 0xFU);

//        qDebug() << __FUNCTION__ << __LINE__ << targets.mTargetHeader.mFrequencyHoppingState << frame.idHex() << frame.dataHex();

//        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
//        qDebug() << __FUNCTION__ << __LINE__
//                 << targets.mTargetHeader.mBlockageFlag
//                 << targets.mTargetHeader.mBlockagePercent
//                 << targets.mTargetHeader.mInterferenceFlag
//                 << targets.mTargetHeader.mInterferencePercent
//                 << targets.mTargetHeader.mTargetRollingCount;
    }else{
        qDebug() << __FUNCTION__ << __LINE__ << "version error"<< targets.mTargetHeader.mProtocolVersion;
        return false;
    }

    return true;
}

bool AnalysisProtocolCT410::parseRawTarget(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 64)
    {
//        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![64]!" << frame.idHex() << frame.dataHex();
        return false;
    }
    const uint8_t *data = (const uint8_t *)frame.data().data();

    int &targetCount = analysisData->mTargets[FrameRawTarget].mTargetCount; // 注意必须使用引用
    int id = mRawID[analysisData->mRadarID]++;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    double range = ((((data[1] & 0xFEU) >> 1) + (((uint16_t)data[0] & 0xFFU) << 7)) * 0.01);
    if (range <= 0.0) {
        return true;
    }
    Target &target_1 = analysisData->mTargets[FrameRawTarget].mTargets[targetCount++];
    target_1.mID = id;
    target_1.mRange = range;//((((data[1] & 0xFEU) >> 1) + (((uint16_t)data[0] & 0xFFU) << 7)) * 0.01);
    target_1.mAngle = (((((data[3] & 0xFCU) >> 2) + (((uint32_t)data[2]) << 6) + (((uint32_t)data[1] & 0x1U) << 14)) * 0.01) - 163.84);
    target_1.mV = (((((data[5] & 0xF0U) >> 4) + (((uint32_t)data[4]) << 4) + (((uint32_t)data[3] & 0x3U) << 12)) * 0.01) - 81.92);
    target_1.mPitchAngle = (((((data[7] & 0x80U) >> 7) + (((uint32_t)data[6]) << 1) + (((uint32_t)data[5] & 0xFU) << 9)) * 0.02) - 81.92);
    target_1.mExistProbability = (data[7] & 0x7FU);
    target_1.mRCS = (((data[8]) * 0.5) - 30);
    target_1.mSNR = ((data[9]) * 0.5);
    target_1.mStatus = (((data[11] & 0xC0U) >> 6) + (((uint16_t)data[10] & 0xFFU) << 2));
    target_1.mDynamicProperty = (((data[12] & 0xE0U) >> 5) + (((uint16_t)data[11] & 0x3FU) << 3));
    target_1.mSubframeID = ((data[12] & 0x1EU) >> 1);
    target_1.mAssociatedTrackID = (((data[13] & 0xFCU) >> 2) + (((uint16_t)data[12] & 0x1U) << 6));
    target_1.mGroupID = (((data[14] & 0xF8U) >> 3) + (((uint16_t)data[13] & 0x3U) << 5));
    target_1.mRollingCount = (data[63] & 0x7U);
    target_1.mValid = true;

#if 0
    qDebug() << __FUNCTION__ << __LINE__
             << target_1.mID
             << target_1.mX
             << target_1.mY
             << target_1.mRange
             << target_1.mAngle
             << target_1.mDynamicProperty
             << frame.dataHex();
#endif

    id = mRawID[analysisData->mRadarID]++;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    range = ((((data[17] & 0xFEU) >> 1) + (((uint16_t)data[16] & 0xFFU) << 7)) * 0.01);
    if (range <= 0.0) {
        return true;
    }
    Target &target_2 = analysisData->mTargets[FrameRawTarget].mTargets[targetCount++];
    target_2.mID = id;
    target_2.mRange = range;//((((data[17] & 0xFEU) >> 1) + (((uint16_t)data[16] & 0xFFU) << 7)) * 0.01);
    target_2.mAngle = (((((data[19] & 0xFCU) >> 2) + (((uint32_t)data[18]) << 6) + (((uint32_t)data[17] & 0x1U) << 14)) * 0.01) - 163.84);
    target_2.mV = (((((data[21] & 0xF0U) >> 4) + (((uint32_t)data[20]) << 4) + (((uint32_t)data[19] & 0x3U) << 12)) * 0.01) - 81.92);
    target_2.mPitchAngle = (((((data[23] & 0x80U) >> 7) + (((uint32_t)data[22]) << 1) + (((uint32_t)data[21] & 0xFU) << 9)) * 0.02) - 81.92);
    target_2.mExistProbability = (data[23] & 0x7FU);
    target_2.mRCS = (((data[24]) * 0.5) - 30);
    target_2.mSNR = ((data[25]) * 0.5);
    target_2.mStatus = (((data[27] & 0xC0U) >> 6) + (((uint16_t)data[26] & 0xFFU) << 2));
    target_2.mDynamicProperty = (((data[28] & 0xE0U) >> 5) + (((uint16_t)data[27] & 0x3FU) << 3));
    target_2.mSubframeID = ((data[28] & 0x1EU) >> 1);
    target_2.mAssociatedTrackID = (((data[29] & 0xFCU) >> 2) + (((uint16_t)data[28] & 0x1U) << 6));
    target_2.mGroupID = (((data[30] & 0xF8U) >> 3) + (((uint16_t)data[29] & 0x3U) << 5));
    target_2.mRollingCount = (data[63] & 0x7U);
    target_2.mValid = true;

#if 0
    qDebug() << __FUNCTION__ << __LINE__
             << target_2.mID
             << target_2.mX
             << target_2.mY
             << target_2.mRange
             << target_2.mAngle
             << target_2.mDynamicProperty
             << frame.dataHex();
#endif

    id = mRawID[analysisData->mRadarID]++;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    range = ((((data[33] & 0xFEU) >> 1) + (((uint16_t)data[32] & 0xFFU) << 7)) * 0.01);
    if (range <= 0.0) {
        return true;
    }
    Target &target_3 = analysisData->mTargets[FrameRawTarget].mTargets[targetCount++];
    target_3.mID = id;
    target_3.mRange = range;//((((data[33] & 0xFEU) >> 1) + (((uint16_t)data[32] & 0xFFU) << 7)) * 0.01);
    target_3.mAngle = (((((data[35] & 0xFCU) >> 2) + (((uint32_t)data[34]) << 6) + (((uint32_t)data[33] & 0x1U) << 14)) * 0.01) - 163.84);
    target_3.mV = (((((data[37] & 0xF0U) >> 4) + (((uint32_t)data[36]) << 4) + (((uint32_t)data[35] & 0x3U) << 12)) * 0.01) - 81.92);
    target_3.mPitchAngle = (((((data[39] & 0x80U) >> 7) + (((uint32_t)data[38]) << 1) + (((uint32_t)data[37] & 0xFU) << 9)) * 0.02) - 81.92);
    target_3.mExistProbability = (data[39] & 0x7FU);
    target_3.mRCS = (((data[40]) * 0.5) - 30);
    target_3.mSNR = ((data[41]) * 0.5);
    target_3.mStatus = (((data[43] & 0xC0U) >> 6) + (((uint16_t)data[42] & 0xFFU) << 2));
    target_3.mDynamicProperty = (((data[44] & 0xE0U) >> 5) + (((uint16_t)data[43] & 0x3FU) << 3));
    target_3.mSubframeID = ((data[44] & 0x1EU) >> 1);
    target_3.mAssociatedTrackID = (((data[45] & 0xFCU) >> 2) + (((uint16_t)data[44] & 0x1U) << 6));
    target_3.mGroupID = (((data[46] & 0xF8U) >> 3) + (((uint16_t)data[45] & 0x3U) << 5));
    target_3.mRollingCount = (data[63] & 0x7U);
    target_3.mValid = true;

#if 0
    qDebug() << __FUNCTION__ << __LINE__
             << target_3.mID
             << target_3.mX
             << target_3.mY
             << target_3.mRange
             << target_3.mAngle
             << target_3.mDynamicProperty
             << frame.dataHex();
#endif

    id = mRawID[analysisData->mRadarID]++;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    range = ((((data[49] & 0xFEU) >> 1) + (((uint16_t)data[48] & 0xFFU) << 7)) * 0.01);
    if (range <= 0.0) {
        return true;
    }
    Target &target_4 = analysisData->mTargets[FrameRawTarget].mTargets[targetCount++];
    target_4.mID = id;
    target_4.mRange = range;//((((data[49] & 0xFEU) >> 1) + (((uint16_t)data[48] & 0xFFU) << 7)) * 0.01);
    target_4.mAngle = (((((data[51] & 0xFCU) >> 2) + (((uint32_t)data[50]) << 6) + (((uint32_t)data[49] & 0x1U) << 14)) * 0.01) - 163.84);
    target_4.mV = (((((data[53] & 0xF0U) >> 4) + (((uint32_t)data[52]) << 4) + (((uint32_t)data[51] & 0x3U) << 12)) * 0.01) - 81.92);
    target_4.mPitchAngle = (((((data[55] & 0x80U) >> 7) + (((uint32_t)data[54]) << 1) + (((uint32_t)data[53] & 0xFU) << 9)) * 0.02) - 81.92);
    target_4.mExistProbability = (data[55] & 0x7FU);
    target_4.mRCS = (((data[56]) * 0.5) - 30);
    target_4.mSNR = ((data[57]) * 0.5);
    target_4.mStatus = (((data[59] & 0xC0U) >> 6) + (((uint16_t)data[58] & 0xFFU) << 2));
    target_4.mDynamicProperty = (((data[60] & 0xE0U) >> 5) + (((uint16_t)data[59] & 0x3FU) << 3));
    target_4.mSubframeID = ((data[60] & 0x1EU) >> 1);
    target_4.mAssociatedTrackID = (((data[61] & 0xFCU) >> 2) + (((uint16_t)data[60] & 0x1U) << 6));
    target_4.mGroupID = (((data[62] & 0xF8U) >> 3) + (((uint16_t)data[61] & 0x3U) << 5));
    target_4.mRollingCount = (data[63] & 0x7U);
    target_4.mValid = true;

#if 0
    qDebug() << __FUNCTION__ << __LINE__
             << target_4.mID
             << target_4.mX
             << target_4.mY
             << target_4.mRange
             << target_4.mAngle
             << target_4.mDynamicProperty
             << frame.dataHex();
#endif

    return true;
}

bool AnalysisProtocolCT410::parseTrackTargetHeader(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 8 && frame.length() != 16)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }
    const uint8_t *data = (const uint8_t *)frame.data().data();

    analysisData->clearTarget(FrameTrackTarget);

    Targets &targets = analysisData->mTargets[FrameTrackTarget];

    targets.mTargetHeader.mTargetChecksum = (data[0]);
    targets.mTargetHeader.mTargetCount = (data[1]);
    targets.mTargetHeader.mMeasurementCount = ((data[3] & 0xFFU) + (((uint16_t)data[2] & 0xFFU) << 8));
    targets.mTargetHeader.mFunctionCalculationTime = (((data[5] & 0xFEU) >> 1) + (((uint16_t)data[4] & 0x3U) << 7));
    targets.mTargetHeader.mResponseTaskCycleTime = ((data[6] & 0xFFU) + (((uint16_t)data[5] & 0x1U) << 8));
    targets.mTargetHeader.mProtocolVersion = ((data[7] & 0xF0U) >> 4);
    targets.mTargetHeader.mTargetRollingCount = (data[7] & 0xFU);

    if( frame.length() == 16 && targets.mTargetHeader.mProtocolVersion == 1 ){
        targets.mTargetHeader.mBlockagePercent = (((data[4] & 0xF0U) >> 4) * 6.67);
        targets.mTargetHeader.mBlockageFlag = ((data[4] & 0x8U) >> 3);
        targets.mTargetHeader.mInterferenceFlag = ((data[4] & 0x4U) >> 2);
        targets.mTargetHeader.mInterferencePercent = (((data[8] & 0xF0U) >> 4) * 6.67);
        targets.mTargetHeader.mOffsetToSideY = ((((data[9] & 0xE0U) >> 5) + (((uint16_t)data[8] & 0xFU) << 3)) * 0.01);
        targets.mTargetHeader.mOffsetToSideX = ((((data[10] & 0xC0U) >> 6) + (((uint16_t)data[9] & 0x1FU) << 2)) * 0.01);
        targets.mTargetHeader.mTunnelsceneState = ((data[10] & 0x20U) >> 5);

//        qDebug() << __FUNCTION__ << __LINE__ << analysisData->mRadarID << targets.mTargetHeader.mOffsetToSideX << targets.mTargetHeader.mOffsetToSideY;
//                 << targets.mTargetHeader.mMeasurementCount
//                 << targets.mTargetHeader.mBlockageFlag
//                 << targets.mTargetHeader.mBlockagePercent
//                 << targets.mTargetHeader.mInterferenceFlag
//                 << targets.mTargetHeader.mInterferencePercent
//                 << targets.mTargetHeader.mTargetRollingCount;
    }

    return true;
}

bool AnalysisProtocolCT410::parseTrackTarget(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 24 && frame.length() != 64 )
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![24]!" << frame.idHex() << frame.dataHex();
        return false;
    }


    if( frame.length() == 24 && analysisData->mTargets[FrameTrackTarget].mTargetHeader.mProtocolVersion == 0 ){
        return parseTrackTargetVersion0( frame, analysisData );
    }else{
        return parseTrackTargetVersion1( frame, analysisData );
    }

    return true;
}

bool AnalysisProtocolCT410::parseTrackTargetVersion0(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    const uint8_t *data = (const uint8_t *)frame.data().data();

    int &targetCount = analysisData->mTargets[FrameTrackTarget].mTargetCount; // 注意必须使用引用
    quint8 id = (data[1]);
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Track target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    Target &target = analysisData->mTargets[FrameTrackTarget].mTargets[targetCount++];
    target.mChecksum = (data[0]);
    target.mID = (data[1]);
    target.mY = (((((data[3] & 0xFCU) >> 2) + (((uint16_t)data[2] & 0xFFU) << 6)) * 0.05) - 409.6);
    target.mX = (((((data[5] & 0xC0U) >> 6) + (((uint32_t)data[4]) << 2) + (((uint32_t)data[3] & 0x3U) << 10)) * 0.05) - 102.4);
    target.mVy = (((((data[6] & 0xFEU) >> 1) + (((uint16_t)data[5] & 0x3FU) << 7)) * 0.04) - 163.84);
    target.mClass = (((data[7] & 0xE0U) >> 5) + (((uint16_t)data[6] & 0x1U) << 3));
    target.mYRms = ((data[7] & 0x1FU) * 0.375);
    target.mVx = (((((data[9] & 0xF8U) >> 3) + (((uint16_t)data[8] & 0xFFU) << 5)) * 0.04) - 163.84);
    target.mAy = ((((data[10] & 0xFFU) + (((uint16_t)data[9] & 0x7U) << 8)) * 0.01) - 10.24);
    target.mAx = (((((data[12] & 0xE0U) >> 5) + (((uint16_t)data[11] & 0xFFU) << 3)) * 0.01) - 10.24);
    target.mTrackFrameLength = ((((data[13] & 0xE0U) >> 5) + (((uint16_t)data[12] & 0x1FU) << 3)) * 0.2);
    target.mTrackFrameWidth = ((((data[14] & 0xE0U) >> 5) + (((uint16_t)data[13] & 0x1FU) << 3)) * 0.2);
    target.mMeasureState = ((data[14] & 0xEU) >> 1);
    target.mRCS = ((((data[15] & 0xFFU) + (((uint16_t)data[14] & 0x1U) << 8)) * 0.5) - 128);
    target.mDynamicProperty = ((data[16] & 0xF0U) >> 4);
    target.mStatus = target.mDynamicProperty;
    //target.mTrackFrameLength = ((((data[17] & 0xF0U) >> 4) + (((uint16_t)data[16] & 0xFU) << 4)) * 0.05);
    target.mTrackFrameHeight = ((((data[17] & 0xF0U) >> 4) + (((uint16_t)data[16] & 0xFU) << 4)) * 0.05);
    target.mXRms = ((((data[18] & 0x80U) >> 7) + (((uint16_t)data[17] & 0xFU) << 1)) * 0.375);
    target.mVyRms = (((data[18] & 0x7CU) >> 2) * 0.375);
    target.mVxRms = ((((data[19] & 0xE0U) >> 5) + (((uint16_t)data[18] & 0x3U) << 3)) * 0.375);
    target.mAyRms = ((data[19] & 0x1FU) * 0.375);
    target.mAxRms = (((data[20] & 0xF8U) >> 3) * 0.375);
    target.mAngle = (((((data[21] & 0xFEU) >> 1) + (((uint16_t)data[20] & 0x7U) << 7)) * 0.4) - 180);
    target.mExistProbability = (((data[22] & 0xF8U) >> 3) * 3.2258);
    target.mZ = (((((data[23] & 0xF8U) >> 3) + (((uint16_t)data[22] & 0x7U) << 5)) * 0.05) - 1.75);
    target.mRollingCount = (data[23] & 0x7U);
    target.mTrackFrameAngle = qAtan2( target.mVx, target.mVy ) * 180 / M_PI;
    target.mValid = true;

#if 0
    qDebug() << __FUNCTION__ << __LINE__
             << target.mID
             << target.mX
             << target.mY
             << target.mRange
             << target.mAngle << qAtan2(target.mX, target.mY) * 180 / M_PI
             << target.mDynamicProperty
             << frame.idHex() << frame.dataHex();
#endif

    return true;
}

bool AnalysisProtocolCT410::parseTrackTargetVersion1(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    const uint8_t *data = (const uint8_t *)frame.data().data();
    int &targetCount = analysisData->mTargets[FrameTrackTarget].mTargetCount; // 注意必须使用引用
    quint8 id1 = (data[0]);
    if( id1 == 0xFF ) {
        return true;
    }
    if (id1 >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Track target id error!" << id1 << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    Target &target1 = analysisData->mTargets[FrameTrackTarget].mTargets[targetCount++];

    target1.mID = (data[0]);
    target1.mY = (((((data[2] & 0xFCU) >> 2) + (((uint16_t)data[1] & 0xFFU) << 6)) * 0.025) - 204.8);
    target1.mX = (((((data[4] & 0xE0U) >> 5) + (((uint32_t)data[3]) << 3) + (((uint32_t)data[2] & 0x3U) << 11)) * 0.025) - 102.4);
    target1.mZ = (((((data[5] & 0xF8U) >> 3) + (((uint16_t)data[4] & 0x1FU) << 5)) * 0.025) - 12.8);
    target1.mVy = (((((data[7] & 0xC0U) >> 6) + (((uint32_t)data[6]) << 2) + (((uint32_t)data[5] & 0x7U) << 10)) * 0.04) - 163.84);
    target1.mTrackFrameHeight = ((data[7] & 0x3FU) * 0.2);
    target1.mVx = (((((data[9] & 0xF8U) >> 3) + (((uint16_t)data[8] & 0xFFU) << 5)) * 0.04) - 163.84);
    target1.mAy = (((((data[10] & 0xFCU) >> 2) + (((uint16_t)data[9] & 0x7U) << 6)) * 0.05) - 12.8);
    target1.mAx = (((((data[11] & 0xFEU) >> 1) + (((uint16_t)data[10] & 0x3U) << 7)) * 0.05) - 12.8);
    target1.mDynamicProperty = (((data[12] & 0xE0U) >> 5) + (((uint16_t)data[11] & 0x1U) << 3));
    target1.mTrackFrameY = (((((data[14] & 0x80U) >> 7) + (((uint32_t)data[13]) << 1) + (((uint32_t)data[12] & 0x1FU) << 9)) * 0.025) - 204.8);
    target1.mExistProbability = (((data[14] & 0x7CU) >> 2) * 3.2258);
    target1.mMeasureState = (data[14] & 0x3U);
    target1.mTrackFrameLength = ((data[15]) * 0.2);
    target1.mTrackFrameWidth = ((data[16]) * 0.2);
    target1.mTrackFrameX = (((((data[18] & 0xF8U) >> 3) + (((uint16_t)data[17] & 0xFFU) << 5)) * 0.025) - 102.4);
    target1.mTrackType = ((data[18] & 0x6U) >> 1);
    target1.mRCS = (((data[19]) * 0.5) - 30);
    target1.mTrackFrameZ = (((((data[21] & 0xC0U) >> 6) + (((uint16_t)data[20] & 0xFFU) << 2)) * 0.025) - 12.8);
    target1.mStatus = (((data[22] & 0xF0U) >> 4) + (((uint16_t)data[21] & 0x3FU) << 4));
    target1.mClass = (data[22] & 0xFU);
    target1.mTrackLifeCycleCnt = (data[23]);
    target1.mHitCnt = (data[24]);
    target1.mMissCnt = (data[25]);
    target1.mSNR = ((data[26]) * 0.5);
    target1.mAssociatedTrackID = (data[27]);
    target1.mAssociatedNearestID = (data[28]);
    target1.mAssociatedStrongestID = (data[29]);
    target1.mFusionID = (data[30]);
    //target1.mAngle = qAtan( target1.mVx/ target1.mVy ) * 180 / M_PI;
    target1.mAngle = qAtan2(target1.mX, target1.mY) * 180 / M_PI;
    target1.mTrackFrameAngle = qAtan2( target1.mVx, target1.mVy ) * 180 / M_PI;
    target1.mValid = true;


    quint8 id2 = (data[32]);
    if( id2 == 0xFF ) {
        return true;
    }
    if ( id2 >= MAX_TARGET_COUNT  || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Track target id error!" << id2 << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    Target &target2 = analysisData->mTargets[FrameTrackTarget].mTargets[targetCount++];
    target2.mID = (data[32]);
    target2.mY = (((((data[34] & 0xFCU) >> 2) + (((uint16_t)data[33] & 0xFFU) << 6)) * 0.025) - 204.8);
    target2.mX = (((((data[36] & 0xE0U) >> 5) + (((uint32_t)data[35]) << 3) + (((uint32_t)data[34] & 0x3U) << 11)) * 0.025) - 102.4);
    target2.mZ = (((((data[37] & 0xF8U) >> 3) + (((uint16_t)data[36] & 0x1FU) << 5)) * 0.025) - 12.8);
    target2.mVy = (((((data[39] & 0xC0U) >> 6) + (((uint32_t)data[38]) << 2) + (((uint32_t)data[37] & 0x7U) << 10)) * 0.04) - 163.84);
    target2.mTrackFrameHeight  = ((data[39] & 0x3FU) * 0.2);
    target2.mVx  = (((((data[41] & 0xF8U) >> 3) + (((uint16_t)data[40] & 0xFFU) << 5)) * 0.04) - 163.84);
    target2.mAy = (((((data[42] & 0xFCU) >> 2) + (((uint16_t)data[41] & 0x7U) << 6)) * 0.05) - 12.8);
    target2.mAx = (((((data[43] & 0xFEU) >> 1) + (((uint16_t)data[42] & 0x3U) << 7)) * 0.05) - 12.8);
    target2.mDynamicProperty  = (((data[44] & 0xE0U) >> 5) + (((uint16_t)data[43] & 0x1U) << 3));
    target2.mTrackFrameY = (((((data[46] & 0x80U) >> 7) + (((uint32_t)data[45]) << 1) + (((uint32_t)data[44] & 0x1FU) << 9)) * 0.025) - 204.8);
    target2.mExistProbability  = (((data[46] & 0x7CU) >> 2) * 3.2258);
    target2.mMeasureState = (data[46] & 0x3U);
    target2.mTrackFrameLength = ((data[47]) * 0.2);
    target2.mTrackFrameWidth = ((data[48]) * 0.2);
    target2.mTrackFrameX = (((((data[50] & 0xF8U) >> 3) + (((uint16_t)data[49] & 0xFFU) << 5)) * 0.025) - 102.4);
    target2.mTrackType = ((data[50] & 0x6U) >> 1);
    target2.mRCS = (((data[51]) * 0.5) - 30);
    target2.mTrackFrameZ = (((((data[53] & 0xC0U) >> 6) + (((uint16_t)data[52] & 0xFFU) << 2)) * 0.025) - 12.8);
    target2.mStatus = (((data[54] & 0xF0U) >> 4) + (((uint16_t)data[53] & 0x3FU) << 4));
    target2.mClass = (data[54] & 0xFU);
    target2.mTrackLifeCycleCnt = (data[55]);
    target2.mHitCnt = (data[56]);
    target2.mMissCnt = (data[57]);
    target2.mSNR = ((data[58]) * 0.5);
    target2.mAssociatedTrackID = (data[59]);
    target2.mAssociatedNearestID = (data[60]);
    target2.mAssociatedStrongestID = (data[61]);
    target2.mFusionID = (data[62]);
    //target2.mAngle = qAtan( target2.mVx/ target2.mVy ) * 180 / M_PI;
    target2.mAngle = qAtan2(target2.mX, target2.mY) * 180 / M_PI;
    target2.mTrackFrameAngle = qAtan2( target2.mVx, target2.mVy ) * 180 / M_PI;
    target2.mValid = true;

    return true;
}

bool AnalysisProtocolCT410::parse0x600_VERSION_7(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 16)
    {
//        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![16]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    mRawID[analysisData->mRadarID] = 0;

    const uint8_t *data = (const uint8_t *)frame.data().data();

    analysisData->clearTarget(FrameRawTarget);

    Targets &targets = analysisData->mTargets[FrameRawTarget];
    targets.mTargetHeader.mProtocolVersion = (data[0]);
    targets.mTargetHeader.mNoiseCurrent = ((data[1]) * 0.5);
    targets.mTargetHeader.mNoiseGlobal = ((data[2]) * 0.5);
    targets.mTargetHeader.mHeaderSenceFlag = (data[3]);
    targets.mTargetHeader.mHeaderMagOffset = ((((data[4] & 0xF0U) >> 4) * 32) - 256);
    targets.mTargetHeader.mTargetRollingCount = (data[4] & 0xFU);
    targets.mTargetHeader.mTargetCount = (((data[6] & 0xC0U) >> 6) + (((uint16_t)data[5] & 0xFFU) << 2));
    targets.mTargetHeader.mHeaderCurFrameMode = ((data[6] & 0x3CU) >> 2);
    targets.mTargetHeader.mWaveType = ((data[6] & 0x2U) >> 1);
    targets.mTargetHeader.mTargetIntervalTime = (((data[7] & 0xFFU) + (((uint16_t)data[6] & 0x1U) << 8)) * 0.5);
    targets.mTargetHeader.mMeasurementCount = ((data[9] & 0xFFU) + (((uint16_t)data[8] & 0xFFU) << 8));
    targets.mTargetHeader.mResponseTaskCycleTime = ((((data[11] & 0x80U) >> 7) + (((uint16_t)data[10] & 0xFFU) << 1)) * 0.5);
    targets.mTargetHeader.mHeaderNumAfterCFAR = (((data[12] & 0xE0U) >> 5) + (((uint16_t)data[11] & 0x7FU) << 3));
    targets.mTargetHeader.mBlockageFlag = ((data[12] & 0x10U) >> 4);
    targets.mTargetHeader.mBlockagePercent = ((data[12] & 0xFU) * 6.67);
    targets.mTargetHeader.mInterferenceFlag = ((data[13] & 0x80U) >> 7);
    targets.mTargetHeader.mInterferencePercent = (((data[13] & 0x78U) >> 3) * 6.67);
    targets.mTargetHeader.mHeaderReserve = (data[14] + ((uint16_t)(data[13] & 0x7U) << 8));
    targets.mTargetHeader.mTargetChecksum = (data[15]);

    return true;
}

bool AnalysisProtocolCT410::parse0x710_64_V7(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 64) {
        return false;
    }

    const uint8_t *data = (const uint8_t *)frame.data().data();
    for (int i = 0; i < 4; ++i, data += 16) {
        int &targetCount = analysisData->mTargets[FrameRawTarget].mTargetCount; // 注意必须使用引用
        int id = mRawID[analysisData->mRadarID]++;
        if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
            qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
            return true;
        }
        double range = ((((data[1] & 0xFEU) >> 1) + (((uint16_t)data[0] & 0xFFU) << 7)) * 0.01);
        if (range <= 0.0) {
            return true;
        }
        Target &target = analysisData->mTargets[FrameRawTarget].mTargets[targetCount++];
        target.mValid = true;
        target.mID = id;
#if 0
        target.mRange = ((((data[1] & 0xF8U) >> 3) + (((uint16_t)data[0] & 0xFFU) << 5)) * 0.05);
//        target.mTXChannel = ((data[1] & 0x6U) >> 1);
//        target.mUsing = (data[1] & 0x1U);
//                target.Obj1_Res_1 = (data[2]);
        target.mV = (((((data[4] & 0xF0U) >> 4) + (((uint16_t)data[3] & 0xFFU) << 4)) * 0.05) - 102.4);
        target.mVy = ((((data[5] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.05) - 102.4);
        target.mAngle = (((((data[7] & 0xF0U) >> 4) + (((uint16_t)data[6] & 0xFFU) << 4)) * 0.05) - 102.4);
        target.mPitchAngle = ((((data[8] & 0xFFU) + (((uint16_t)data[7] & 0xFU) << 8)) * 0.05) - 102.4);
        target.mRCS = (((((data[10] & 0xF0U) >> 4) + (((uint16_t)data[9] & 0xFFU) << 4)) * 0.05) - 102.4);
        target.mSNR = (((data[11] & 0xFFU) + (((uint16_t)data[10] & 0xFU) << 8)) * 0.05);
        target.mMAG = ((data[12] & 0xFEU) >> 1);
//                target.Obj1_Res_2 = (((data[13] & 0xF0U) >> 4) + (((uint16_t)data[12] & 0x1U) << 4));
//        target.mMatchFlag = ((data[13] & 0xCU) >> 2);
//                target.Obj1_Res_3 = ((data[14] & 0xFFU) + (((uint16_t)data[13] & 0x3U) << 8));
//                target.Obj1_Res_4 = (data[15]);
#else
        target.mRange = (((data[1] & 0xFFU) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.01);
        target.mV = (((((data[3] & 0xF0U) >> 4) + (((uint16_t)data[2] & 0xFFU) << 4)) * 0.05) - 112.4);
        target.mAngle = ((((data[4] & 0xFFU) + (((uint16_t)data[3] & 0xFU) << 8)) * 0.05) - 102.4);
        target.mPitchAngle = (((((data[6] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0xFFU) << 4)) * 0.05) - 102.4);
        target.mMatchFlag = ((data[6] & 0xCU) >> 2);
        target.mVelocityBin = ((data[7] & 0xFFU) + (((uint16_t)data[6] & 0x3U) << 8));
        target.mRCS = (((data[8]) * 0.5) - 50);
        target.mSNR = ((data[9]) * 0.5);
        target.mStatus = (((data[11] & 0xC0U) >> 6) + (((uint16_t)data[10] & 0xFFU) << 2));
        target.mMeasQuality = ((data[11] & 0x3FU) * 2);
        target.mMAG = ((data[12] & 0xFEU) >> 1);
#endif

        target.mX = target.mRange * std::sinf(target.mAngle * M_PI / 180);
        target.mZ = target.mRange * std::sinf(target.mPitchAngle * M_PI / 180);
        target.mY = target.mRange * std::cosf(target.mAngle * M_PI / 180);
//				target.mY = std::sqrt(target.mRange * target.mRange - target.mX * target.mX - target.mZ * target.mZ);//target.mRange * std::cosf(target.mAngle * M_PI / 180);

        //std::cout
        //	<< target.mID << " " << target.mRange << " " << target.mAngle << " " << target.mPitchAngle << " "
        //	<< target.mX << " " << target.mY << " " << target.mZ << " "
        //	<< (target.mRange * target.mRange - target.mX * target.mX - target.mZ * target.mZ) << std::endl;
    }

    return true;
}

bool AnalysisProtocolCT410::parse0x710_64_V8(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 64) {
        return false;
    }

    const uint8_t *data = (const uint8_t *)frame.data().data();

    int &targetCount = analysisData->mTargets[FrameRawTarget].mTargetCount; // 注意必须使用引用
    int id = mRawID[analysisData->mRadarID]++;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << analysisData->mRadarID << frame.channelIndex() << frame.idHex() << frame.dataHex();
        return true;
    }
    double range = (((data[1]) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.01);
    if (range <= 0.0) {
        return true;
    }
    Target &target_1 = analysisData->mTargets[FrameRawTarget].mTargets[targetCount++];
    target_1.mValid = true;
    target_1.mID = id;
    target_1.mRange = (((data[1]) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.01);
    target_1.mV = (((((data[3] & 0xF0U) >> 4) + (((uint16_t)data[2] & 0xFFU) << 4)) * 0.05) - 112.4);
    target_1.mAngle = ((((data[4]) + (((uint16_t)data[3] & 0xFU) << 8)) * 0.05) - 102.4);
    target_1.mVelocityBin = ((data[6] & 0xC0U) >> 6) + (((uint16_t)data[5] & 0xFFU) << 2);
    target_1.mRCS = (((((data[7] & 0xC0U) >> 6) + (((uint16_t)data[6] & 0x3FU) << 2)) * 0.5) - 50);
    target_1.mMeasQuality = (((data[7] & 0x3FU)) * 2);
    target_1.mPitchAngle = (((((data[9] & 0xF0U) >> 4) + (((uint16_t)data[8] & 0xFFU) << 4)) * 0.05) - 102.4);
    target_1.mSNR = ((((data[10] & 0xF0U) >> 4) + (((uint16_t)data[9] & 0xFU) << 4)) * 0.5);
    target_1.mStatus = ((data[11] & 0xFCU) >> 2) + (((uint16_t)data[10] & 0xFU) << 6);
    target_1.mMAG = ((data[12] & 0xF8U) >> 3) + (((uint16_t)data[11] & 0x3U) << 5);
    target_1.mReserve = ((data[15] & 0xC0U) >> 6) + (((uint32_t)data[14]) << 2) + (((uint32_t)data[13]) << 10) + (((uint32_t)data[12] & 0x7U) << 18);

    target_1.mMatchFlag = (data[23] & 0x3U);

    target_1.mX = target_1.mRange * std::sinf(target_1.mAngle * M_PI / 180);
    target_1.mZ = target_1.mRange * std::sinf(target_1.mPitchAngle * M_PI / 180);
    target_1.mY = target_1.mRange * std::cosf(target_1.mAngle * M_PI / 180);

//    qDebug() << __FUNCTION__ << __LINE__ << target_1.mID << target_1.mRange << target_1.mPitchAngle << target_1.mX << target_1.mY;

    id = mRawID[analysisData->mRadarID]++;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    range = (((data[17]) + (((uint16_t)data[16] & 0xFFU) << 8)) * 0.01);
    if (range <= 0.0) {
        return true;
    }
    Target &target_2 = analysisData->mTargets[FrameRawTarget].mTargets[targetCount++];
    target_2.mValid = true;
    target_2.mID = id;
    target_2.mMeasQuality = (((data[15] & 0x3FU)) * 2);
    target_2.mRange = (((data[17]) + (((uint16_t)data[16] & 0xFFU) << 8)) * 0.01);
    target_2.mV = (((((data[19] & 0xF0U) >> 4) + (((uint16_t)data[18] & 0xFFU) << 4)) * 0.05) - 112.4);
    target_2.mAngle = ((((data[20]) + (((uint16_t)data[19] & 0xFU) << 8)) * 0.05) - 102.4);
    target_2.mPitchAngle = (((((data[22] & 0xF0U) >> 4) + (((uint16_t)data[21] & 0xFFU) << 4)) * 0.05) - 102.4);
    target_2.mVelocityBin = ((data[23] & 0xFCU) >> 2) + (((uint16_t)data[22] & 0xFU) << 6);

    target_2.mRCS = ((((data[24])) * 0.5) - 50);
    target_2.mSNR = (((data[25])) * 0.5);
    target_2.mStatus = ((data[27] & 0xC0U) >> 6) + (((uint16_t)data[26] & 0xFFU) << 2);
    target_2.mMAG = ((data[28] & 0x80U) >> 7) + (((uint16_t)data[27] & 0x3FU) << 1);
    target_2.mReserve = ((data[30] & 0xFCU) >> 2) + (((uint32_t)data[29]) << 6) + (((uint32_t)data[28] & 0x7FU) << 14);
    target_2.mMatchFlag = (data[31] & 0x3U);

    target_2.mX = target_2.mRange * std::sinf(target_2.mAngle * M_PI / 180);
    target_2.mZ = target_2.mRange * std::sinf(target_2.mPitchAngle * M_PI / 180);
    target_2.mY = target_2.mRange * std::cosf(target_2.mAngle * M_PI / 180);

//    qDebug() << __FUNCTION__ << __LINE__ << target_2.mID << target_2.mRange << target_2.mPitchAngle << target_2.mX << target_2.mY;

    id = mRawID[analysisData->mRadarID]++;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    range = (((data[33]) + (((uint16_t)data[32] & 0xFFU) << 8)) * 0.01);
    if (range <= 0.0) {
        return true;
    }
    Target &target_3 = analysisData->mTargets[FrameRawTarget].mTargets[targetCount++];
    target_3.mValid = true;
    target_3.mID = id;
    target_3.mRCS = (((((data[31] & 0xFCU) >> 2) + (((uint16_t)data[30] & 0x3U) << 6)) * 0.5) - 50);

    target_3.mRange = (((data[33]) + (((uint16_t)data[32] & 0xFFU) << 8)) * 0.01);
    target_3.mV = (((((data[35] & 0xF0U) >> 4) + (((uint16_t)data[34] & 0xFFU) << 4)) * 0.05) - 112.4);
    target_3.mAngle = ((((data[36]) + (((uint16_t)data[35] & 0xFU) << 8)) * 0.05) - 102.4);
    target_3.mPitchAngle = (((((data[38] & 0xF0U) >> 4) + (((uint16_t)data[37] & 0xFFU) << 4)) * 0.05) - 102.4);
    target_3.mMatchFlag = ((data[38] & 0xCU) >> 2);
    target_3.mVelocityBin = (data[39]) + (((uint16_t)data[38] & 0x3U) << 8);
    target_3.mSNR = (((data[40])) * 0.5);
    target_3.mStatus = ((data[42] & 0xC0U) >> 6) + (((uint16_t)data[41] & 0xFFU) << 2);
    target_3.mMeasQuality = (((data[42] & 0x3FU)) * 2);
    target_3.mMAG = ((data[43] & 0xFEU) >> 1);
    target_3.mReserve = ((data[46] & 0xF0U) >> 4) + (((uint32_t)data[45]) << 4) + (((uint32_t)data[44]) << 12) + (((uint32_t)data[43] & 0x1U) << 20);

    target_3.mX = target_3.mRange * std::sinf(target_3.mAngle * M_PI / 180);
    target_3.mZ = target_3.mRange * std::sinf(target_3.mPitchAngle * M_PI / 180);
    target_3.mY = target_3.mRange * std::cosf(target_3.mAngle * M_PI / 180);

//    qDebug() << __FUNCTION__ << __LINE__ << target_3.mID << target_3.mRange << target_3.mPitchAngle << target_3.mX << target_3.mY;

    id = mRawID[analysisData->mRadarID]++;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
    range = (((data[49]) + (((uint16_t)data[48] & 0xFFU) << 8)) * 0.01);
    if (range <= 0.0) {
        return true;
    }
    Target &target_4 = analysisData->mTargets[FrameRawTarget].mTargets[targetCount++];
    target_4.mID = id;
    target_4.mValid = true;
    target_4.mV = ((((data[47]) + (((uint16_t)data[46] & 0xFU) << 8)) * 0.05) - 112.4);
    target_4.mRange = (((data[49]) + (((uint16_t)data[48] & 0xFFU) << 8)) * 0.01);
    target_4.mAngle = (((((data[51] & 0xF0U) >> 4) + (((uint16_t)data[50] & 0xFFU) << 4)) * 0.05) - 102.4);
    target_4.mPitchAngle = ((((data[52]) + (((uint16_t)data[51] & 0xFU) << 8)) * 0.05) - 102.4);
    target_4.mVelocityBin = ((data[54] & 0xC0U) >> 6) + (((uint16_t)data[53] & 0xFFU) << 2);
    target_4.mRCS = (((((data[55] & 0xC0U) >> 6) + (((uint16_t)data[54] & 0x3FU) << 2)) * 0.5) - 50);
    target_4.mMeasQuality = (((data[55] & 0x3FU)) * 2);
    target_4.mSNR = (((data[56])) * 0.5);
    target_4.mMatchFlag = ((data[57] & 0xC0U) >> 6);
    target_4.mStatus = ((data[58] & 0xF0U) >> 4) + (((uint16_t)data[57] & 0x3FU) << 4);
    target_4.mMAG = ((data[59] & 0xE0U) >> 5) + (((uint16_t)data[58] & 0xFU) << 3);
    target_4.mReserve = (data[61]) + (((uint32_t)data[60]) << 8) + (((uint32_t)data[59] & 0x1FU) << 16);
//    target_4.re00 = ((data[62] & 0xF0U) >> 4);
//    target_4.rawRollingCnt = (data[62] & 0xFU);
//    target_4.rawCheckSum = (data[63]);

    target_4.mX = target_4.mRange * std::sinf(target_4.mAngle * M_PI / 180);
    target_4.mZ = target_4.mRange * std::sinf(target_4.mPitchAngle * M_PI / 180);
    target_4.mY = target_4.mRange * std::cosf(target_4.mAngle * M_PI / 180);

//    qDebug() << __FUNCTION__ << __LINE__ << QString("%1").arg(target_4.mReserve, 0, 'f') << target_4.mID << target_4.mRange << target_4.mPitchAngle << target_4.mX << target_4.mY;

    return true;
}

bool AnalysisProtocolCT410::parseAlarmInfomation(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 24 && frame.length() != 64)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![24]!" << frame.idHex() << frame.dataHex();
        return false;
    }
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    const uint8_t *data = (const uint8_t *)frame.data().data();

    analysisData->mAlarmData.mDrivingFunctionAlarmModule = (data[1]);
    analysisData->mAlarmData.mAlarmBSDObjectID = (data[2]);
    analysisData->mAlarmData.mAlarmLCAObjectID = (data[3]);
    analysisData->mAlarmData.mAlarmLCAObjectTTC = ((data[4]) * 0.02);
    analysisData->mAlarmData.mAlarmDOWObjectID = (data[5]);
    analysisData->mAlarmData.mAlarmDOWObjectTTC = ((data[6]) * 0.02);
    analysisData->mAlarmData.mAlarmRCWObjectID = (data[7]);
    analysisData->mAlarmData.mAlarmRCWObjectTTC = ((data[8]) * 0.02);
    analysisData->mAlarmData.mAlarmRCTAObjectID = (data[9]);
    analysisData->mAlarmData.mAlarmRCTAObjectTTC = ((data[10]) * 0.02);
    analysisData->mAlarmData.mAlarmRCTBObjectID = (data[11]);
    analysisData->mAlarmData.mAlarmRCTBObjectTTC = ((data[12]) * 0.02);
    analysisData->mAlarmData.mAlarmFCTAObjectID = (data[13]);
    analysisData->mAlarmData.mAlarmFCTAObjectTTC = ((data[14]) * 0.02);
    analysisData->mAlarmData.mAlarmFCTBObjectID = (data[15]);
    analysisData->mAlarmData.mAlarmFCTBObjectTTC = ((data[16]) * 0.02);
    analysisData->mAlarmData.mAlarmBSDLevel = ((data[17] & 0xC0U) >> 6);
    analysisData->mAlarmData.mAlarmLCALevel = ((data[17] & 0x30U) >> 4);
    analysisData->mAlarmData.mAlarmDOWFLevel = ((data[17] & 0xCU) >> 2);
    analysisData->mAlarmData.mAlarmDOWRLevel = (data[17] & 0x3U);
    analysisData->mAlarmData.mAlarmRCWLevel = ((data[18] & 0xC0U) >> 6);
    analysisData->mAlarmData.mAlarmRCTALevel = ((data[18] & 0x30U) >> 4);
    analysisData->mAlarmData.mAlarmFCTALevel = ((data[18] & 0xCU) >> 2);
    analysisData->mAlarmData.mAlarmBSDState = (data[18] & 0x3U);
    analysisData->mAlarmData.mAlarmLCAState = ((data[19] & 0xC0U) >> 6);
    analysisData->mAlarmData.mAlarmDOWState = ((data[19] & 0x30U) >> 4);
    analysisData->mAlarmData.mAlarmRCTAState = ((data[19] & 0xCU) >> 2);
    analysisData->mAlarmData.mAlarmRCTBState = (data[19] & 0x3U);
    analysisData->mAlarmData.mAlarmFCTAState = ((data[20] & 0xC0U) >> 6);
    analysisData->mAlarmData.mAlarmFCTBState = ((data[20] & 0x30U) >> 4);
    analysisData->mAlarmData.mAlarmRCWState = ((data[20] & 0xCU) >> 2);

    analysisData->mAlarmData.mAlarmRCTBLevel = ((data[20] & 0x3U) >> 1);
    analysisData->mAlarmData.mAlarmFCTBLevel = ((data[20] & 0x1U));

    analysisData->mAlarmData.mAlarmJAObjectID = (data[21]);
    analysisData->mAlarmData.mAlarmJAObjectTTC = ((data[22]) * 0.02);
    analysisData->mAlarmData.mAlarmJALevel = ((data[23] & 0xC0U) >> 6);
    analysisData->mAlarmData.mAlarmJAState = ((data[23] & 0x30U) >> 4);

//    qDebug() << __FUNCTION__ << __LINE__
//             << analysisData->mAlarmData.mAlarmRCTBState
//             << analysisData->mAlarmData.mAlarmRCTBLevel
//             << analysisData->mAlarmData.mAlarmFCTBState
//             << analysisData->mAlarmData.mAlarmFCTBLevel
//             << frame.idHex() << frame.dataHex();

    return true;
}

bool AnalysisProtocolCT410::parse0x4DN(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 8 && frame.length() != 16 && frame.length() != 64)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }
    const uint8_t *data = (const uint8_t *)frame.data().data();
//    memcpy(analysisData->mAlarmData.mErrorInformation0x4DN, frame.data().data(), frame.length());
    EndFrameData &endFrameData = analysisData->mEndFrameData;
    memcpy(analysisData->mAlarmData.mErrorInformation0x4DN, data, 8);
    memcpy(analysisData->mAlarmData.mErrorInformation0x4EN, data + 8, 8);

//    userData-> = ((data[0]) * 0);
//    userData-> = ((data[0]) * 0);

    endFrameData.mGuardrail01_c0 = (decode_sign_bit(data[16], 8) * 0.1);
    endFrameData.mGuardrail01_c1 = ((decode_sign_bit(((data[18] & 0xF0U) >> 4) + (((uint16_t)data[17] & 0xFFU) << 4), 12)) * 0.001);
    endFrameData.mGuardrail01_c2 = ((decode_sign_bit(((data[19] & 0xFCU) >> 2) + (((uint16_t)data[18] & 0xFU) << 6), 10)) * 0.0001);
    endFrameData.mGuardrail01_c3 = ((decode_sign_bit(((data[21] & 0x80U) >> 7) + ((uint16_t)data[20] << 1) + (((uint16_t)data[19] & 0x03U) << 9), 11)) * 0.000001);
    endFrameData.mGuardrail01_IngStart = ((decode_sign_bit(((data[22] & 0xE0U) >> 5) + (((uint16_t)data[21] & 0x7FU) << 3), 10)) * 0.5);
    endFrameData.mGuardrail01_IngEnd = ((decode_sign_bit(((data[23] & 0xF8U) >> 3) + (((uint16_t)data[22] & 0x1FU) << 5), 10)) * 0.5);
    endFrameData.mGuardrail01_vaild = (data[23] & 0x1U);
    endFrameData.mGuardrail02_c0 = -(decode_sign_bit(data[24], 8) * 0.1);
    endFrameData.mGuardrail02_c1 = ((decode_sign_bit(((data[26] & 0xF0U) >> 4) + (((uint16_t)data[25] & 0xFFU) << 4), 12)) * 0.001);
    endFrameData.mGuardrail02_c2 = ((decode_sign_bit(((data[27] & 0xFCU) >> 2) + (((uint16_t)data[26] & 0xFU) << 6), 10)) * 0.0001);
    endFrameData.mGuardrail02_c3 = ((decode_sign_bit(((data[29] & 0x80U) >> 7) + ((uint16_t)data[28] << 1) + (((uint16_t)data[27] & 0x03U) << 9), 11)) * 0.000001);
    endFrameData.mGuardrail02_IngStart = ((decode_sign_bit(((data[30] & 0xE0U) >> 5) + (((uint16_t)data[29] & 0x7FU) << 3), 10)) * 0.5);
    endFrameData.mGuardrail02_IngEnd = ((decode_sign_bit(((data[31] & 0xF8U) >> 3) + (((uint16_t)data[30] & 0x1FU) << 5), 10)) * 0.5);
    endFrameData.mGuardrail02_vaild = (data[31] & 0x1U);

//    qDebug() << __FUNCTION__ << __LINE__ << endFrameData.mGuardrail01_c0 << endFrameData.mGuardrail01_c1 << endFrameData.mGuardrail01_c2 << endFrameData.mGuardrail01_c3
//             << frame.dataHex();

//    qDebug() << __FUNCTION__ << __LINE__ << endFrameData.mGuardrail01_c0 << endFrameData.mGuardrail01_c1 << endFrameData.mGuardrail01_c2 << endFrameData.mGuardrail01_c3<< frame.idHex() << frame.dataHex();

    return true;
}

bool AnalysisProtocolCT410::parse0x4EN(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 8 && frame.length() != 16)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }
    memcpy(analysisData->mAlarmData.mErrorInformation0x4EN, frame.data().data(), frame.length());

    return true;
}

bool AnalysisProtocolCT410::parse0x604(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 64)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }
    const uint8_t *data = (const uint8_t *)frame.data().data();

    EndFrameData &endFrameData = analysisData->mEndFrameData;
    memcpy(analysisData->mAlarmData.mErrorInformation0x4DN, data, 8);
    memcpy(analysisData->mAlarmData.mErrorInformation0x4EN, data + 8, 8);

//    userData-> = ((data[0]) * 0);
//    userData-> = ((data[0]) * 0);
    endFrameData.mGuardrail01_c0 = (decode_sign_bit(data[16], 8) * 0.1);
    endFrameData.mGuardrail01_c1 = ((decode_sign_bit(((data[18] & 0xF0U) >> 4) + (((uint16_t)data[17] & 0xFFU) << 4), 12)) * 0.001);
    endFrameData.mGuardrail01_c2 = ((decode_sign_bit(((data[19] & 0xFCU) >> 2) + (((uint16_t)data[18] & 0xFU) << 6), 10)) * 0.0001);
    endFrameData.mGuardrail01_c3 = ((decode_sign_bit(((data[21] & 0x80U) >> 7) + ((uint16_t)data[20] << 1) + (((uint16_t)data[19] & 0x03U) << 9), 11)) * 0.000001);
    endFrameData.mGuardrail01_IngStart = ((decode_sign_bit(((data[22] & 0xE0U) >> 5) + (((uint16_t)data[21] & 0x7FU) << 3), 10)) * 0.5);
    endFrameData.mGuardrail01_IngEnd = ((decode_sign_bit(((data[23] & 0xF8U) >> 3) + (((uint16_t)data[22] & 0x1FU) << 5), 10)) * 0.5);
    endFrameData.mGuardrail01_vaild = (data[23] & 0x1U);
    endFrameData.mGuardrail02_c0 = -(decode_sign_bit(data[24], 8) * 0.1);
    endFrameData.mGuardrail02_c1 = ((decode_sign_bit(((data[26] & 0xF0U) >> 4) + (((uint16_t)data[25] & 0xFFU) << 4), 12)) * 0.001);
    endFrameData.mGuardrail02_c2 = ((decode_sign_bit(((data[27] & 0xFCU) >> 2) + (((uint16_t)data[26] & 0xFU) << 6), 10)) * 0.0001);
    endFrameData.mGuardrail02_c3 = ((decode_sign_bit(((data[29] & 0x80U) >> 7) + ((uint16_t)data[28] << 1) + (((uint16_t)data[27] & 0x03U) << 9), 11)) * 0.000001);
    endFrameData.mGuardrail02_IngStart = ((decode_sign_bit(((data[30] & 0xE0U) >> 5) + (((uint16_t)data[29] & 0x7FU) << 3), 10)) * 0.5);
    endFrameData.mGuardrail02_IngEnd = ((decode_sign_bit(((data[31] & 0xF8U) >> 3) + (((uint16_t)data[30] & 0x1FU) << 5), 10)) * 0.5);
    endFrameData.mGuardrail02_vaild = (data[31] & 0x1U);

//    qDebug() << __FUNCTION__ << __LINE__ << endFrameData.mGuardrail01_c0 << endFrameData.mGuardrail01_c1 << endFrameData.mGuardrail01_c2 << endFrameData.mGuardrail01_c3
//             << frame.dataHex();

//    qDebug() << __FUNCTION__ << __LINE__ << endFrameData.mGuardrail01_c0 << endFrameData.mGuardrail01_c1<< frame.idHex() << frame.dataHex();

    return true;
}

bool AnalysisProtocolCT410::parseEndFrame(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    //static quint64 frameCount = 0;
    if (frame.length() != 24 && frame.length() != 32 && frame.length() != 64)
    {
//        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![24]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    const uint8_t *data = (const uint8_t *)frame.data().data();

    EndFrameData &endFrameData = analysisData->mEndFrameData;
    if (frame.length() == 64) {
        endFrameData.mEndFrameChecksum = (data[0]);
        endFrameData.mRadarRollingCount = ((data[1] & 0xF0U) >> 4);
        endFrameData.mPointListIsAhead = ((data[1] & 0x8U) >> 3);
//        endFrameData.mChipTemperature = ((decode_sign_bit((data[2] & 0xFFU) + (((uint16_t)data[1] & 0x7U) << 8), 11)) * 0.1);
        endFrameData.mVehicleInfoTimeOffset = (decode_sign_bit(data[3], 8));
        endFrameData.mFirstTrackFrameNo = ((data[7] & 0xFFU) + (((uint32_t)data[6]) << 8) + (((uint32_t)data[5]) << 16) + (((uint32_t)data[4] & 0xFFU) << 24));
        endFrameData.mFrameTimeStampLocal = ((data[11] & 0xFFU) + (((uint32_t)data[10]) << 8) + (((uint32_t)data[9]) << 16) + (((uint32_t)data[8] & 0xFFU) << 24));
        endFrameData.mFrameTimeStampGlobal = (((data[17] & 0xC0U) >> 6) + (((uint64_t)data[16]) << 2) + (((uint64_t)data[15]) << 10) + (((uint64_t)data[14]) << 18) + (((uint64_t)data[13]) << 26) + (((uint64_t)data[12] & 0xFFU) << 34));

        endFrameData.mSelfCalibrationEstablishedAngle = (((((data[18] & 0xF8U) >> 3) + (((uint16_t)data[17] & 0x3FU) << 5)) * 0.1) - 102.4);
        endFrameData.mEndOfLineEstablishedAngle = ((((data[19] & 0xFFU) + (((uint16_t)data[18] & 0x07U) << 8)) * 0.1) - 102.4);


//        qDebug() << __FUNCTION__ << __LINE__ << endFrameData.mFrameTimeStampGlobal << endFrameData.mFrameTimeStampLocal  << frame.dataHex();
    } else {

    endFrameData.mEndFrameChecksum = (data[0]);
//    endFrameData.mEndOfLineEstablishedAngle = (((((data[2] & 0xE0U) >> 5) + (((uint16_t)data[1] & 0xFFU) << 3)) * 0.1) - 102.4);
//    endFrameData.mSelfCalibrationEstablishedAngle = (((((data[3] & 0xFCU) >> 2) + (((uint16_t)data[2] & 0x1FU) << 6)) * 0.1) - 102.4);
    endFrameData.mSelfCalibrationEstablishedAngle = (((((data[2] & 0xE0U) >> 5) + (((uint16_t)data[1] & 0xFFU) << 3)) * 0.1) - 102.4);
    endFrameData.mEndOfLineEstablishedAngle = (((((data[3] & 0xFCU) >> 2) + (((uint16_t)data[2] & 0x1FU) << 6)) * 0.1) - 102.4);
    endFrameData.mEndFrameIntervalTime = (data[4]);
//    endFrameData.EndFrameFuncCalcTime = (((data[6] & 0x80U) >> 7) + (((uint16_t)data[5] & 0xFFU) << 1));
    endFrameData.mRoadSideDistance = (((((data[7] & 0xF0U) >> 4) + (((uint16_t)data[6] & 0x3FU) << 4)) * 0.05) - 25);
    endFrameData.mRadarRollingCount = (data[7] & 0xFU);
    endFrameData.mTimeTick = ((data[11] & 0xFFU) + (((uint32_t)data[10]) << 8) + (((uint32_t)data[9]) << 16) + (((uint32_t)data[8] & 0xFFU) << 24));
    //版本1的410协议的结束帧中新增雷达发送的总帧数
    if( analysisData->mTargets[FrameTrackTarget].mTargetHeader.mProtocolVersion == 1 ||
            analysisData->mTargets[FrameRawTarget].mTargetHeader.mProtocolVersion == 1 ){
        endFrameData.mSendTotalCANFrmCnt = ((data[18] & 0xFFU) + (((uint32_t)data[17]) << 8) + (((uint32_t)data[16] & 0xFFU) << 16));
        endFrameData.mCurrentRadar410FrameCount = endFrameData.mSendTotalCANFrmCnt;
        endFrameData.mAutoALNSts = ((data[19] & 0x1CU) >> 2);
        //qDebug() << __FUNCTION__ << __LINE__ << (int)(endFrameData.mAutoALNSts);
    }

    if (frame.length() >= 32) {
        endFrameData.mTemperature = (((((data[24] & 0xE0U) >> 5) + (((uint16_t)data[23] & 0xFFU) << 3)) * 0.1) - 50.0);
    }

    analysisData->mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount = ((data[15] & 0xFFU) + (((uint32_t)data[14]) << 8) + (((uint32_t)data[13]) << 16) + (((uint32_t)data[12] & 0xFFU) << 24));
    }

//    qDebug() << __FUNCTION__ << __LINE__  << frame.idHex() << frame.dataHex();
//    qDebug() << __FUNCTION__ << __LINE__
//             << analysisData->mTargets[FrameRawTarget].mTargetHeader.mMeasurementCount
//             << analysisData->mTargets[FrameRawTarget].mTargetHeader.mInterferencePercent
//             << endFrameData.mRadarRollingCount;


    endFrameData.mFrameTime = frame.timestemp();
    endFrameData.mSystemFrameTime = frame.systemTimeStemp();

//    frameCount += 20;
//    endFrameData.mCurrentRadar410FrameCount = frameCount;

    if( endFrameData.mInit410FrameCountFlag ){
        endFrameData.mInitRadar410FrameCount = endFrameData.mCurrentRadar410FrameCount;
        analysisData->clearRecv410FrameCount();
        endFrameData.mInit410FrameCountFlag = false;
    }

    if( endFrameData.mCurrentRadar410FrameCount != 0 ){
        if( endFrameData.mInitRadar410FrameCount > endFrameData.mCurrentRadar410FrameCount ){
            //防止雷达断电，重新启动后，重新对齐
            endFrameData.mInitRadar410FrameCount = endFrameData.mCurrentRadar410FrameCount;
        }
        quint64 sendTotal = endFrameData.mCurrentRadar410FrameCount - endFrameData.mInitRadar410FrameCount;
        quint64 preDiff = endFrameData.mDiff410FrameCount;
        if( endFrameData.mRecv410FrameCount > sendTotal ){
            endFrameData.mRecv410FrameCount = sendTotal;  //防止上位机计数 大于 下位机发送计数 导致出现负数
        }
        endFrameData.mDiff410FrameCount = sendTotal - endFrameData.mRecv410FrameCount;
        endFrameData.mDiff410FrameCountRate = (double)endFrameData.mDiff410FrameCount / sendTotal * 100;
        if( endFrameData.mDiff410FrameCount != 0 && preDiff != endFrameData.mDiff410FrameCount ){
            endFrameData.mPreLostFrameFlag = true;
        }else{
            endFrameData.mPreLostFrameFlag = false;
        }
//        qDebug() << __FUNCTION__ <<__LINE__ << ":" << QThread::currentThreadId()
//                 << sendTotal
//                 <<endFrameData.mCurrentRadar410FrameCount
//                <<endFrameData.mInitRadar410FrameCount
//               <<endFrameData.mRecv410FrameCount
//              << endFrameData.mDiff410FrameCount;

    }else{
        endFrameData.mDiff410FrameCount = 0;
        endFrameData.mDiff410FrameCountRate = 0.0;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << analysisData->mRadarID
//             << analysisData->mTargets[FrameRawTarget].mTargetCount
//             << analysisData->mTargets[FrameTrackTarget].mTargetCount;


//    qDebug() << __FUNCTION__ << __LINE__ << analysisData->mADC_1DFFT_2DFFT_DATA.length();
    analysisEnd(analysisData->mRadarID, false);

    return true;
}

bool AnalysisProtocolCT410::parseRadarVersion(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
    if (frame.length() != 8)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![24]!" << frame.idHex() << frame.dataHex();
        return false;
    }
    quint8 *p = (unsigned char *)(frame.data().data());

    if(p[0] == 0x00){
        return false;
    }

    if (analysisData->mRadarID != 0)
    {
        quint8 index = (p[0] & 0xF0) >> 4;
        if( index > 0 && index <= 5 ){
            memcpy( analysisData->mEndFrameData.mRadarVersion[index-1], p, 8 );
//            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
//            qDebug() << __FUNCTION__ << __LINE__ << analysisData->mEndFrameData.mRadarVersion[index-1][0] << p[0];
        }
        return true;
    }
    return false;
}

void AnalysisProtocolCT410::radarReset( const Devices::Can::CanFrame &frame )
{
    mAnalysisWorker->radarReset( frame.channelIndex() );
}

bool AnalysisProtocolCT410::parseADC_1DFFT_2DFFT(const Devices::Can::CanFrame &frame, AnalysisData *analysisData)
{
//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.length() << frame.dataHex();
    if (frame.length() != 64)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![64]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    QByteArray &data = analysisData->mADC_1DFFT_2DFFT_DATA;

    data.append(Utils::reverseArray(frame.data()));

    return true;
}

} // namespace Protocol
} // namespace Analysis
