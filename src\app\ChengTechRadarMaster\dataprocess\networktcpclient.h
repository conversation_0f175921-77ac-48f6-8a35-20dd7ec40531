﻿#ifndef NETWORKTCPCLIENT_H
#define NETWORKTCPCLIENT_H

#include <QObject>

#include <QTcpSocket>

class NetworkTcpClient : public QObject
{
    Q_OBJECT
public:
    explicit NetworkTcpClient(QObject *parent = nullptr);

    bool isConnected() const { return mConnected; }

public slots:
    void connectTCPServer(const QString IP, quint16 port);
    void disconnectTCPServer();

signals:
    void tcpServerConnected(const QString IP, quint16 port);
    void tcpServerDisonnected();

    void write(const QByteArray &data);
    void read(const QByteArray &data);

private slots:
    void readData();
    void writeData(const QByteArray &data);

private:
    QTcpSocket *mTcpClient{0};

    bool mConnected{false};
};

#endif // NETWORKTCPCLIENT_H
