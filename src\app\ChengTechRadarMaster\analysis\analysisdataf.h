﻿#pragma once

#include <stdint.h>
#include <vector>
#include <memory>

#define TARGET_MAX_COUNT 1024

namespace Parser {
namespace ParsedDataTypedef {

enum ParsedDataType
{
    TargetRaw,                       ///< 原始点目标
    TargetTrack,                     ///< 跟踪点目标
    HeSaiLider,                      ///< 禾赛激光雷达点
    ParsedDataNone,
//    CalterahADC,                     ///< 加特兰ADC数据
};

static const char * const sgParsedDataTypeNames[]{
    "原始点目标",
    "跟踪点目标",
    "禾赛激光雷达点"
};

enum TargetAttributeType {
    ID,                               ///< ID
    X,                                ///< 横向(相对)距离
    Y,                                ///< 纵向(相对)距离
    Z,                                ///< 垂直(相对)距离
    Vx,                               ///< 横向(相对)速度
    Vy,                               ///< 纵向(相对)速度
    Vz,                               ///< 垂直(相对)速度
    Vxsog,                            ///< 横向(对地绝对)速度
    Vysog,                            ///< 纵向(对地绝对)速度
    Vzsog,                            ///< 垂直(对地绝对)速度
    Ax,                               ///< 横向(相对)加速度
    Ay,                               ///< 纵向(相对)加速度
    Az,                               ///< 垂直(相对)加速度
    StDevX,                           ///< 横向位置标准差
    StDevY,                           ///< 纵向位置标准差
    StDevZ,                           ///< 垂直位置标准差
    StDevVx,                          ///< 横向(相对)速度标准差
    StDevVy,                          ///< 纵向(相对)速度标准差
    StDevVz,                          ///< 垂直(相对)速度标准差
    StDevAx,                          ///< 横向(相对)加速度标准差
    StDevAy,                          ///< 纵向(相对)加速度标准差
    StDevAz,                          ///< 垂直(相对)加速度标准差
    Length,                           ///< 长
    Width,                            ///< 宽
    Hight,                            ///< 高
    Range,                            ///< 距离矢量
    Angle,                            ///< 方位角
    PitchAngle,                       ///< 俯仰角
    V,                                ///< 速度
    SNR,                              ///< 信噪比
    RCS,                              ///< 雷达散射截面
    MAG,                              ///< (DBF)信号幅度(magnitude)
    Using,                            ///< 使用中
    ObjectType,                       ///< 目标类型
    MotionPattern,                    ///< 运动模式
    MoveAttributes,                   ///< 运动属性
    TXChannel,                        ///< TX通道
    MatchFlag,                        ///< AB波匹配的标志位，DDM_1T4T和3T的匹配标志位F
    Threshold3T,                      ///< 3T的阈值
    DeltaRange,                       ///< 1T4T的delta距离
    DeltaVelocity,                    ///< 1T4T的delta速度
    DMLMode,                          ///< DML模式
    TrackReliablility,                ///< 跟踪可靠性
    ExistenceProbability,             ///< 置信度
    ObstacleProbability,              ///< 为障碍物概率
    ClusterID,                        ///< 分组ID
    FusionSignal,                     ///< 融合信号
    FusionCameraID,                   ///< 融合摄像头ID
    Classification,                   ///< 分类
    DynamicProperty,                  ///< 运动属性
    MaintenaceState,                  ///< 维护状态
    LifeCycle,                        ///< 生命周期
    Checksum,                         ///< 校验码
    MeasurementState,                 ///< 测量状态、卡尔曼状态
    MeasurementCounter,               ///< 测量计数器
    BlockCounter,                     ///< 块链计数器
    AliveCounter,                     ///< 生命计数器
    MeasurementFlag,                  ///< 测量标识符
    ValidFlag,                        ///< 有效标识符
    UpdateFlag,                       ///< 更新标识符
    SortFlag,                         ///< 分类标识符
    TAG,                              ///< 标签
    MoveStatus,                       ///< 动静状态
    KalmanState,                      ///< 测量/卡尔曼状态
    TrackAngle,                       ///< 航向角
    TrackAngleStd,                    ///< 航向角(标准差)
    TrackReferencePoint,              ///< 航迹框参考点
    ClassificationAdvanced,           ///< Car; Truck; Peserved; Pedestrian; Unknown
    MotionState,                      ///< Unknown; Moving; Stop; Stationary
    ExistProbability,                 ///< 目标可靠性、存在概率
    MirrorProbability,                ///< 镜像概率
    MaintenanceState,                 ///< 跟着状态

    AttributeUnknow
};

typedef struct TargetAttribute {
    TargetAttributeType mTargetAttributeType;   ///< 属性类型
    const char *mTargetAttributeNameShort;      ///< 简称
    const char *mTargetAttributeNameEN;         ///< 英文名称
    const char *mTargetAttributeNameCN;         ///< 中文名称
    const char *mTargetAttributeUnit;           ///< 单位
}TargetAttribute;

static TargetAttribute sgTargetAttributes[]{
    {ID,                           "ID",                           "ID",                          "ID",                           ""},
    {X,                            "X",                            "X",                           "横向距离",                      "m"},
    {Y,                            "Y",                            "Y",                           "纵向距离",                      "m"},
    {Z,                            "Z",                            "Z",                           "垂直距离",                      "m"},
    {Vx,                           "Vx",                           "Vx",                          "横向(相对)速度"                 "m/s"},
    {Vy,                           "Vy",                           "Vy",                          "纵向(相对)速度",                "m/s"},
    {Vz,                           "Vz",                           "Vz",                          "垂直(相对)速度",                "m/s"},
    {Vxsog,                        "Vxsog",                        "Vxsog",                       "横向(对地绝对)速度",            "m/s"},
    {Vysog,                        "Vysog",                        "Vysog",                       "纵向(对地绝对)速度",            "m/s"},
    {Vzsog,                        "Vzsog",                        "Vzsog",                       "垂直(对地绝对)速度",            "m/s"},
    {Ax,                           "Ax",                           "Ax",                          "横向(相对)加速度",              "m/ss"},
    {Ay,                           "Ay",                           "Ay",                          "纵向(相对)加速度",              "m/ss"},
    {Az,                           "Az",                           "Az",                          "垂直(相对)加速度",              "m/ss"},
    {StDevX,                       "StDevX",                       "StDevX",                      "横向位置标准差",                ""},
    {StDevY,                       "StDevY",                       "StDevY",                      "纵向位置标准差",                ""},
    {StDevZ,                       "StDevZ",                       "StDevZ",                      "垂直位置标准差",                ""},
    {StDevVx,                      "StDevVx",                      "StDevVx",                     "横向(相对)速度标准差",          ""},
    {StDevVy,                      "StDevVy",                      "StDevVy",                     "纵向(相对)速度标准差",          ""},
    {StDevVz,                      "StDevVz",                      "StDevVz",                     "垂直(相对)速度标准差",          ""},
    {StDevAx,                      "StDevAx",                      "StDevAx",                     "横向(相对)加速度标准差",        ""},
    {StDevAy,                      "StDevAy",                      "StDevAy",                     "纵向(相对)加速度标准差",        ""},
    {StDevAz,                      "StDevAz",                      "StDevAz",                     "垂直(相对)加速度标准差",        ""},
    {Length,                       "Length",                       "Length",                      "长",                           "m"},
    {Width,                        "Width",                        "Width",                       "宽",                           "m"},
    {Hight,                        "Hight",                        "Hight",                       "高",                           "m"},
    {Range,                        "R",                            "Range",                       "距离矢量",                      "m"},
    {Angle,                        "A",                            "Angle",                       "方位角",                        "deg"},
    {PitchAngle,                   "PA",                           "PitchAngle",                  "俯仰角",                        "deg"},
    {V,                            "V",                            "V",                           "速度",                        "m/s"},
    {SNR,                          "SNR",                          "SNR",                         "信噪比",                        "dB"},
    {RCS,                          "RCS",                          "RCS",                         "雷达散射截面",                  "m^2"},
    {MAG,                          "MAG",                          "Magnitude",                   "(DBF)信号幅度",                  "m^2"},
    {Using,                        "Using",                        "Using",                       "使用中",                  ""},
    {ObjectType,                   "ObType",                       "ObjectType",                  "目标类型",                  ""},
    {MotionPattern,                "MotPat",                       "MotionPattern",               "运动模式",                  ""},
    {MoveAttributes,               "MoveAttr",                      "MoveAttributes",              "运动属性",                  ""},
    {TXChannel,                    "TXCH",                         "TXChannel",                   "TX通道",                  ""},
    {MatchFlag,                    "MFlag",                        "MatchFlag",                   "AB波匹配的标志位",               ""},
    {Threshold3T,                  "Thresh3T",                     "Threshold3T",                 "3T的阈值",                     ""},
    {DeltaRange,                   "DeltaR",                       "DeltaRange",                  "1T4T的delta距离",             "m"},
    {DeltaVelocity,                "DeltaV",                       "DeltaVelocity",               "1T4T的delta速度",           "m/s"},
    {DMLMode,                      "DML",                          "DMLMode",                     "DML模式",                   ""},
    {TrackReliablility,            "TrkReli",                      "TrackReliablility",           "跟踪可靠性",                   ""},
    {ExistenceProbability,         "ExitProb",                     "ExistenceProbability",        "目标置信度",                  ""},
    {ObstacleProbability,          "ObstaPro",                     "ObstacleProbability",         "为障碍物概率",                  ""},
    {ClusterID,                    "ClusterID",                    "ClusterID",                   "分组ID",                  ""},
    {FusionSignal,                 "FusionSig",                    "FusionSignal",                "融合信号",                  ""},
    {FusionCameraID,               "FusionCamID",                  "FusionCameraID",              "融合摄像头ID",                  ""},
    {Classification,               "Clsfy",                        "Classification",              "分类",                  ""},
    {DynamicProperty,              "DynPro",                       "DynamicProperty",             "运动属性",                  ""},
    {MaintenaceState,              "MaintSt",                      "MaintenaceState",             "维护状态",                  ""},
    {LifeCycle,                    "Life",                         "LifeCycle",                   "生命周期",                  ""},
    {Checksum,                     "Checksum",                     "Checksum",                    "校验码",                  ""},
    {MeasurementState,             "MeassSta",                     "MeasurementState",            "测量/卡尔曼状态",                  ""},
    {MeasurementCounter,           "MeasCnt",                      "MeasurementCounter",          "测量计数器",                  ""},
    {BlockCounter,                 "BlockCnt",                     "BlockCounter",                "块链计数器",                  ""},
    {AliveCounter,                 "AliveCnt",                     "AliveCounter",                "生命计数器",                  ""},
    {MeasurementFlag,              "MeasFlag",                     "MeasurementFlag",             "测量标识符",                  ""},
    {ValidFlag,                    "ValidFlag",                    "ValidFlag",                   "有效标识符",                  ""},
    {UpdateFlag,                   "UpdateFlag",                   "UpdateFlag",                  "更新标识符",                  ""},
    {SortFlag,                     "SortFlag",                     "SortFlag",                    "分类标识符",                  ""},
    {TAG,                          "TAG",                          "TAG",                         "标签",                  ""},
    {MoveStatus,                   "MoveSts",                      "MoveStatus",                  "动静状态",                  ""},
    {KalmanState,                  "KState",                       "KalmanState",                 "测量/卡尔曼状态",                  ""},
    {TrackAngle,                   "TrackAngle",                   "TrackAngle",                  "航向角",                  ""},
    {TrackAngleStd,                "TrackAngleStd",                "TrackAngleStd",               "航向角(标准差)",                  ""},
    {TrackReferencePoint,          "TrackReferencePoint",          "TrackReferencePoint",         "航迹框参考点",                  ""},
    {ClassificationAdvanced,       "ClassificationAdvanced",       "ClassificationAdvanced",      "Car; Truck; Peserved; Pedestrian; Unknown",                  ""},
    {MotionState,                  "MotionState",                  "MotionState",                 "Unknown; Moving; Stop; Stationary",                  ""},
    {ExistProbability,             "ExistProbability",             "ExistProbability",            "目标可靠性、存在概率",                  ""},
    {MirrorProbability,            "MirrorProbability",            "MirrorProbability",           "镜像概率",                  ""},
    {MaintenanceState,             "MaintenanceState",             "MaintenanceState",            "跟着状态",                  ""},
};

/** @brief 目标标签 */
enum TargetTag {
    TagUnknown,                                          ///< 未知
    TagVelocityDeblurringError,                          ///< 速度解模糊错误的点
    TagAbnormalAngleDataProcessing,                      ///< 角度异常的点(数据与处理策略)
    TagLargeCar,                                         ///< 大型车
    TagMultipath,                                        ///< 多径
    TagAbnormalAngleUniversal,                           ///< 角度异常的点(通用策略)
    TagCount,
};

static const char * const sgTargetTagNames[]{
    "未知",
    "速度解模糊错误的点",
    "角度异常的点(数据与处理策略)",
    "大型车",
    "多径",
    "角度异常的点(通用策略)",
};

/** @brief 目标运动状态 */
enum TargetMoveStatus
{
    MoveStatusUnknow,                                    ///< 未知
    MoveStatusInward,                                    ///< 运动(靠近)
    MoveStatusOutward,                                   ///< 运动(远离)
    MoveStatusMotorialSog,                               ///< 运动(相对静止)
    MoveStatusMotorial,                                  ///< 运动
    MoveStatusMotionlessSog,                             ///< 静止(对地绝对)
    MoveStatusCount
};

static const char * const sgTargetMoveStatusNames[]{
    "未知",
    "运动(靠近)",
    "运动(远离)",
    "运动(相对静止)",
    "运动",
    "静止(对地绝对)"
};

typedef struct TargetF {
    typedef std::shared_ptr<TargetF> Ptr;
    typedef std::shared_ptr<const TargetF> ConstPtr;

    uint16_t   mID{ 0 };                                 ///< ID
    float      mX{ 0.0 };                                ///< 横向(相对)距离
    float      mY{ 0.0 };                                ///< 纵向(相对)距离
    float      mZ{ 0.0 };                                ///< 垂直(相对)距离
    float      mVx{ 0.0 };                               ///< 横向(相对)速度
    float      mVy{ 0.0 };                               ///< 纵向(相对)速度
    float      mVz{ 0.0 };                               ///< 垂直(相对)速度
    float      mVxsog{ 0.0 };                            ///< 横向(对地绝对)速度
    float      mVysog{ 0.0 };                            ///< 纵向(对地绝对)速度
    float      mVzsog{ 0.0 };                            ///< 垂直(对地绝对)速度
    float      mAx{ 0.0 };                               ///< 横向(相对)加速度
    float      mAy{ 0.0 };                               ///< 纵向(相对)加速度
    float      mAz{ 0.0 };                               ///< 垂直(相对)加速度
    float      mStDevX{ 0.0 };                           ///< 横向位置标准差
    float      mStDevY{ 0.0 };                           ///< 纵向位置标准差
    float      mStDevZ{ 0.0 };                           ///< 垂直位置标准差
    float      mStDevVx{ 0.0 };                          ///< 横向(相对)速度标准差
    float      mStDevVy{ 0.0 };                          ///< 纵向(相对)速度标准差
    float      mStDevVz{ 0.0 };                          ///< 垂直(相对)速度标准差
    float      mStDevAx{ 0.0 };                          ///< 横向(相对)加速度标准差
    float      mStDevAy{ 0.0 };                          ///< 纵向(相对)加速度标准差
    float      mStDevAz{ 0.0 };                          ///< 垂直(相对)加速度标准差
    float      mLength{ 0.0 };                           ///< 长
    float      mWidth{ 0.0 };                            ///< 宽
    float      mHight{ 0.0 };                            ///< 高
    float      mRange{ 0.0 };                            ///< 距离矢量
    float      mAngle{ 0.0 };                            ///< 方位角
    float      mPitchAngle{ 0.0 };                       ///< 俯仰角
    float      mV{ 0.0 };                                ///< 速度
    float      mSNR{ 0.0 };                              ///< 信噪比
    float      mRCS{ 0.0 };                              ///< 雷达散射截面
    float      mMAG{ 0.0 };                              ///< (DBF)信号幅度(magnitude)
    float      mUsing{ 0.0 };                            ///< 使用中
    float      mObjectType{ 0.0 };                       ///< 目标类型
    float      mMotionPattern{ 0.0 };                    ///< 运动模式
    float      mMoveAttributes{ 0.0 };                   ///< 运动属性
    float      mTXChannel{ 0.0 };                        ///< TX通道
    float      mMatchFlag{ 0.0 };                        ///< AB波匹配的标志位，DDM_1T4T和3T的匹配标志位F
    float      mThreshold3T{ 0.0 };                      ///< 3T的阈值
    float      mDeltaRange{ 0.0 };                       ///< 1T4T的delta距离
    float      mDeltaVelocity{ 0.0 };                    ///< 1T4T的delta速度
    float      mDMLMode{ 0.0 };                          ///< DML模式
    float      mTrackReliablility{ 0.0 };                ///< 跟踪可靠性
    float      mExistenceProbability{ 0.0 };             ///< 目标置信度
    float      mObstacleProbability{ 0.0 };              ///< 为障碍物概率
    float      mClusterID{ 0.0 };                        ///< 分组ID
    float      mFusionSignal{ 0.0 };                     ///< 融合信号
    float      mFusionCameraID{ 0.0 };                   ///< 融合摄像头ID
    float      mClassification{ 0.0 };                   ///< 分类
    float      mDynamicProperty{ 0.0 };                  ///< 运动属性
    float      mMaintenaceState{ 0.0 };                  ///< 维护状态
    float      mLifeCycle{ 0.0 };                        ///< 生命周期
    float      mChecksum{ 0.0 };                         ///< 校验码
    float      mMeasurementState{ 0.0 };                 ///< 测量状态、卡尔曼状态
    float      mMeasurementCounter{ 0.0 };               ///< 测量计数器
    float      mBlockCounter{ 0.0 };                     ///< 块链计数器
    float      mAliveCounter{ 0.0 };                     ///< 生命计数器
    float      mMeasurementFlag{ 0.0 };                  ///< 测量标识符
    float      mValidFlag{ 0.0 };                        ///< 有效标识符
    float      mUpdateFlag{ 0.0 };                       ///< 更新标识符
    float      mSortFlag{ 0.0 };                         ///< 分类标识符
    float      mKalmanState{ 0.0 };                      ///< 分类标识符

    float      mTrackAngle{ 0.0 };                       ///< 航向角
    float      mTrackAngleStd{ 0.0 };                    ///< 航向角(标准差)
    float      mTrackReferencePoint{ 0.0 };              ///< 航迹框参考点
    float      mClassificationAdvanced{ 0.0 };           ///< Car; Truck; Peserved; Pedestrian; Unknown
    float      mMotionState{ 0.0 };                      ///< Unknown; Moving; Stop; Stationary
    float      mExistProbability{ 0.0 };                 ///< 目标可靠性、存在概率
    float      mMirrorProbability{ 0.0 };                ///< 镜像概率
    float      mMaintenanceState{ 0.0 };                 ///< 跟着状态

    TargetTag  mTAG{TagUnknown};                         ///< 标签
    TargetMoveStatus mMoveStatus{MoveStatusUnknow};      ///< 动静状态
    bool mValid{ false };

    float      sideSlobe0{ 0.0 };
    float      sideSlobe1{ 0.0 };
    float      sideSlobe2{ 0.0 };
    float      peakNum1{ 0.0 };
    float      peakNum2{ 0.0 };

    float value(TargetAttributeType attr) const {
        switch (attr)
        {
        case ID:
            return mID;
        case X:
            return mX;
        case Y:
            return mY;
        case Z:
            return mZ;
        case Vx:
            return mVx;
        case Vy:
            return mVy;
        case Vz:
            return mVz;
        case Vxsog:
            return mVxsog;
        case Vysog:
            return mVysog;
        case Vzsog:
            return mVzsog;
        case Ax:
            return mAx;
        case Ay:
            return mAy;
        case Az:
            return mAz;
        case StDevX:
            return mStDevX;
        case StDevY:
            return mStDevY;
        case StDevZ:
            return mStDevZ;
        case StDevVx:
            return mStDevVx;
        case StDevVy:
            return mStDevVy;
        case StDevVz:
            return mStDevVz;
        case StDevAx:
            return mStDevAx;
        case StDevAy:
            return mStDevAy;
        case StDevAz:
            return mStDevAz;
        case Length:
            return mLength;
        case Width:
            return mWidth;
        case Hight:
            return mHight;
        case Range:
            return mRange;
        case Angle:
            return mAngle;
        case PitchAngle:
            return mPitchAngle;
        case V:
            return mV;
        case SNR:
            return mSNR;
        case RCS:
            return mRCS;
        case MAG:
            return mMAG;
        case Using:
            return mUsing;
        case ObjectType:
            return mObjectType;
        case MotionPattern:
            return mMotionPattern;
        case MoveAttributes:
            return mMoveAttributes;
        case TXChannel:
            return mTXChannel;
        case MatchFlag:
            return mMatchFlag;
        case Threshold3T:
            return mThreshold3T;
        case DeltaRange:
            return mDeltaRange;
        case DeltaVelocity:
            return mDeltaVelocity;
        case DMLMode:
            return mDMLMode;
        case TrackReliablility:
            return mTrackReliablility;
        case ExistenceProbability:
            return mExistenceProbability;
        case ObstacleProbability:
            return mObstacleProbability;
        case ClusterID:
            return mClusterID;
        case FusionSignal:
            return FusionSignal;
        case FusionCameraID:
            return mFusionCameraID;
        case Classification:
            return mClassification;
        case DynamicProperty:
            return mDynamicProperty;
        case MaintenaceState:
            return mMaintenaceState;
        case LifeCycle:
            return mLifeCycle;
        case Checksum:
            return mChecksum;
        case MeasurementState:
            return mMeasurementState;
        case MeasurementCounter:
            return mMeasurementCounter;
        case BlockCounter:
            return mBlockCounter;
        case AliveCounter:
            return mAliveCounter;
        case MeasurementFlag:
            return mMeasurementFlag;
        case ValidFlag:
            return mValidFlag;
        case UpdateFlag:
            return mUpdateFlag;
        case SortFlag:
            return mSortFlag;
        case TAG:
            return mTAG;
        case MoveStatus:
            return mMoveStatus;
        case KalmanState:
            return mKalmanState;
        case TrackAngle:
            return mTrackAngle;
        case TrackAngleStd:
            return mTrackAngleStd;
        case TrackReferencePoint:
            return mTrackReferencePoint;
        case ClassificationAdvanced:
            return mClassificationAdvanced;
        case MotionState:
            return mMotionState;
        case ExistProbability:
            return mExistProbability;
        case MirrorProbability:
            return mMirrorProbability;
        case MaintenanceState:
            return mMaintenanceState;
        default:
            break;
        }
        return 0.0f;
    }
}TargetF;

enum ProtocolType {
    ARS410,                     ///< 大陆410
    ARS408,                     ///< 大陆480
    BOSCH670,                   ///< 博世670
    ChengTech710,               ///< 承泰710
    BYDHO,               ///< 承泰710
    RadarInteraction,           ///< 雷达交互协议
    UnknownProtocol,
};

static const char * const sgProtocolTypeNames[]{
    "大陆410",
    "大陆480",
    "博世670",
    "承泰710",
    "雷达交互协议"
};

typedef struct {
    float a;
    float b;
    float c;
    float d;
    float begin;
    float end;
}Polynomial;

typedef struct RadarInfomation {
    uint64_t    mSystemTimstamp{ 0 };                    ///< 系统时间戳

    uint16_t    mWaveType{ 0 };                          ///< AB波类型，
    uint16_t    mUploadObjectNumber{ 0 };                ///< 上传目标数量
    uint16_t    mTxChannel{ 0 };                         ///< 发送通道
    uint16_t    mProtocolVersion{ 0 };                   ///< CAN协议版本
    uint16_t    mSenceFlag{ 0 };                         ///< 场景标记
    uint16_t    mNoise{ 0 };                             ///< 底噪
    uint16_t    mMeasurementCounter{ 0 };                ///< 发送次数计数
    uint16_t    mProcessingTime{ 0 };                    ///< 算法处理时间(ms)
    uint16_t    mFrameIntervalTime { 0 };                ///< 帧间隔时间(ms)
    uint16_t    mSelfCalibrationBuffCount { 0 };         ///< 自标BUFFER次数
    uint64_t    mRadarTimestamp{ 0 };                    ///< 雷达内部时间戳
    uint64_t    mTimestampStaus{ 0 };                    ///< 雷达内部时间戳状态
    uint64_t    mTimeLatency { 0 };                      ///< 时间延迟
    uint64_t    mBlockCounter { 0 };                     ///< 块链计数器
    uint64_t    mErrorCode{ 0 };                         ///< 错误码

    uint8_t     mSpeedMode;                              ///< 速度模式 1-OBD 2-自测速
    uint8_t     mSelfCalibrationAccumulativeEnable;      ///< 自标定积累使能开关
    uint8_t     mSelfCalibrationAccumulativeCount;       ///< 自标定积累个数

    float       mOffLineAngleOffset{ 0.0 };              ///< 下线俯仰标定的俯仰角度
    float       mOffLinePitchAngleOffset{ 0.0 };         ///< 下线标定的补偿角度
    float       mSelfCalibrationAccAngle{ 0.0 };         ///< 自标定的每次计算角度
    float       mSelfCalibrationAngleOffset{ 0.0 };      ///< 自标定安装补偿交度
    float       mInstallAngleOffset{ 0.0 };              ///< 安装补偿交度
    float       mHeightAngleOffset{ 0.0 };               ///< 高度角补偿


    uint8_t mInterpolation{0};

    /***************************************************
     * 前雷达遮蔽
     * 0x0：No Blindness不遮挡	无故障
     * 0x1：Blindness遮挡        0xACE876（系统致盲）
     ***************************************************/
    uint8_t mMRR_SensorDirty{0};
    /***************************************************
     * 前雷达SGU失效
     * 0x0：No Failure未错位                  无故障
     * 0x1：TimesyncFailure_ModFail
     * 0x2：TimesyncCorrect_ModFail          未使用
     * 0x3：故障：错位                        无故障
     ***************************************************/
    uint8_t mMRR_SGUFail{0};
    /***************************************************
     * 前雷达错位
     * 0x0：正常
     * 0x1：失效
     * 0x2：遮掩
     * 0x3：错位
     * 0x4：Reserved4
     * 0x5：Reserved5
     * 0x6：Reserved6
     * 0x7：Reserved7
     ***************************************************/
    uint8_t mMRR_Modulation_Status{0};
    /***************************************************
     * 前雷达失效
     * 0x0：无故障：已标定成功      无故障
     * 0x1：reserved             未使用
     * 0x2：故障：未标定           0xACD278（校准未完成）
     * 0x3：动态标定              未使用
     ***************************************************/
    uint8_t mMRR_Failure{0};

    Polynomial mPolynomialL;
    Polynomial mPolynomialR;

    uint64_t    mIndex;

    void clear() {
        memset(this, 0, sizeof(RadarInfomation));
    }
}RadarInfomation;

enum VehicleVelocityType {
    VT_OBD,                                              ///< OBD
    VT_RadarOBD,                                         ///< RadarOBD
    VT_RadarEstimation,                                  ///< Radar最终预估
    VT_RadarDopplerEstimation,                           ///< Radar多普勒预估速度
    VT_MS100,                                            ///< MS100曲率
    VT_PHY                                               ///< PHY曲率
};

static const char * vehicleVelocityTypeName(VehicleVelocityType type){
    switch (type) {
    case VT_OBD:
        return "OBD";
    case VT_RadarOBD:
        return "ROBD";
    case VT_RadarEstimation:
        return "REST";
    case VT_RadarDopplerEstimation:
        return "RDEST";
    case VT_MS100:
        return "MS100";
    case VT_PHY:
        return "PHY";
    default:
        break;
    }

    return "None";
};

typedef struct VehicleInfomation {
    VehicleVelocityType    mVType;                       ///< 速度类型
    uint16_t    mActualGear;                             ///< 实际挡位信号
    float       mV;                                      ///< 车身速度(m/s)
    float       mVself;                                  ///< 车身速度(自测速)
    float       mAy;                                     ///< 纵向加速度
    float       mAx;                                     ///< 横向加速度
    float       mYawRate;                                ///< 偏航率、角速度(rad/s)
    float       mRadiusOfCurvature;                      ///< 曲率半径
    float       mSteeringAngle;                          ///< 方向盘角度
    float       mSteeringAngleSpeed;                     ///< 方向盘角速度
    float       mWhellSpeedFrontLeft;                    ///< 轮速 - 前左
    float       mWhellSpeedFrontRight;                   ///< 轮速 - 前右
    float       mWhellSpeedRearLeft;                     ///< 轮速 - 后左
    float       mWhellSpeedRearRight;                    ///< 轮速 - 后右

    void clear() {
        memset(this, 0, sizeof(VehicleInfomation));
    }
}VehicleInfomation;

typedef struct SynchronizingInformation {
    uint64_t mImageFrameIdx;
    uint64_t mHeSaiLiderFrameIdx;
    uint64_t mARS410FrameIdx;

    void clear() {
        memset(this, 0, sizeof(SynchronizingInformation));
    }
}SynchronizingInformation;

typedef struct TargetsF {
    bool mSplitAfterSavedFlag{ false };                   ///< 保存后需要分割保存
    bool mValid{ false };                                 ///< 数据有效性
    ParsedDataType mParsedDataType{ParsedDataNone};       ///< 目标类型
    ProtocolType mProtocolType{UnknownProtocol};          ///< 协议类型
    TargetF  mTargets[TARGET_MAX_COUNT];                   ///< 存放目标的数组
    uint16_t    mEffectiveNumber{ 0 };                    ///< 有效的目标数量

    RadarInfomation mRadarInfomation;                     ///< 雷达信息
    VehicleInfomation mVehicleInfomation;                 ///< 车身信息
    SynchronizingInformation mSynchronizingInformation;   ///< 同步信息

    /** @brief 重置目标数据，只清除有效目标数据 */
    void resetTargets() {
//        memset(mTargets, 0, sizeof(Target) * mEffectiveNumber);
        mEffectiveNumber = 0;
    }
    /** @brief 清除所有数据 */
    void clear() {
        memset(mTargets, 0, sizeof(mTargets));
        mEffectiveNumber = 0;
    }

    void infoClear() {
        mRadarInfomation.clear();
        mVehicleInfomation.clear();
        mSynchronizingInformation.clear();
    }


    void calculateTargetMoveStatus();
    void calculate();
}TargetsF;

typedef struct ParsedData{
    ParsedData() {
        for (int i = 0; i < ParsedDataNone; ++i) {
            mTargets[i].mParsedDataType = (ParsedDataType)i;
        }
    }

    TargetsF mTargets[ParsedDataNone];
}ParsedData;

typedef struct Point {
    uint16_t mID{ 0 };                                   ///< ID
    float    mX{ 0.0 };                                  ///< 横向(相对)距离
    float    mY{ 0.0 };                                  ///< 纵向(相对)距离
    float    mZ{ 0.0 };                                  ///< 垂直(相对)距离
}Point;

typedef struct Points {
    bool mValid{ false };                                 ///< 数据有效性
    ParsedDataType mParsedDataType{ParsedDataNone};       ///< 目标类型
    ProtocolType   mProtocolType{UnknownProtocol};        ///< 协议类型
    Point          mPoints[TARGET_MAX_COUNT];             ///< 存放目标的数组
    uint16_t       mEffectiveNumber{ 0 };                 ///< 有效的目标数量

    /** @brief 重置目标数据，只清除有效目标数据 */
    void resetPoints() {
//        memset(mPoints, 0, sizeof(Point) * mEffectiveNumber);
        mEffectiveNumber = 0;
    }
}Points;

typedef struct ParsedPoints{
    ParsedPoints() {
        for (int i = 0; i < ParsedDataNone; ++i) {
            mPoints[i].mParsedDataType = (ParsedDataType)i;
        }
    }

    Points mPoints[ParsedDataNone];
}ParsedPoints;

typedef struct ADCFrameInfo {
    unsigned long long mPackageIndex{ 0 };
    unsigned long long mFrameCount{ 0 };
    unsigned long long mSaveSize{ 0 };
}ADCFrameInfo;

}
}
