﻿#ifndef DEBUGCONTROLFORM_H
#define DEBUGCONTROLFORM_H

#include <QWidget>

namespace Analysis {
class CalculationWorker;
}

namespace Devices {
namespace Can {
class DeviceManager;
class DeviceWorkerBinFile;
}
}

namespace Analysis {
class AnalysisManager;
}

namespace Ui {
class DebugControlForm;
}

namespace Core {
class SaveManager;
};

namespace Views{
class ViewsManager;
}
class AutoRechargeForm;
class VideoPlayerForm;

class DebugControlForm : public QWidget
{
    Q_OBJECT

enum HilType{
    HilType_Normal = 1,
    HilType_Raw = 2,
    HilType_Track = 3
};

public:
    explicit DebugControlForm( Views::ViewsManager* pViewManager, Core::SaveManager *saveManager, Devices::Can::DeviceManager *deviceManager, Analysis::AnalysisManager *analysisManager, QWidget *parent = nullptr);
    ~DebugControlForm();

    void loadSettings();
    void saveSettings();


    /** @brief 通过通道区分雷达ID */
    void setBYDHDChannelRadarID(bool raw600ByChannel, int *channelRadarID, int size);
    void initBatchParam( quint8 HILMode );
    void autoHil( const QString& file );

signals:
    void HILBegin( const QString& file );
    void HILEnd( const QString& file );
    void setShowCandiFlag( bool isShow );
public slots:
    void showCurFrameNumber(quint64 num);

private slots:
    void currentFrameTimeChanged( quint64 curMs );
    void fileBeginEndTimestemp( quint64 beginMs, quint64 endMs );
    void stateChanged();

    void on_pushButtonGetOpenFiles_clicked();

    void on_pushButtonStartAndStop_clicked();

    void on_pushButtonSettings_clicked();

    void on_pushButtonNextFrame_clicked();

    void on_spinBoxBinaryInterval_valueChanged(int arg1);

    bool setHilParam();
    void setHilMode();
    void setSkipParam();
    bool setEndFrameID();


    void on_comboBoxHilType_currentIndexChanged(int index);

    void on_pushButtonPrevFrame_clicked();

    void on_pushButtonBatchHIL_clicked();


    void on_pushButtonVideoPlayer_clicked();

    void on_pushButtonVideoSkip_clicked();

    void skipToTimestamp( quint64 timestamp );

    void on_checkBoxShowCandi_stateChanged(int arg1);
    void on_pushButtonBatchGeneration_clicked();

    void on_checkBoxBatchGeneration_clicked(bool checked);

    void on_checkBoxUseEndFrameID_clicked(bool checked);
	
	void on_comboBoxEOL_currentIndexChanged(int index);

    void on_comboBoxSkipType_activated(int index);

private:
    void setFiles(const QStringList &files);
    void enableHilWidget();
    void enterHilMode( HilType type );


    Ui::DebugControlForm *ui;

    Devices::Can::DeviceWorkerBinFile *mDeviceWorkerBinFile{0};
    Analysis::CalculationWorker *CalculationWorker{0};

    QStringList mFilenames;
    Devices::Can::DeviceManager* mDevManager;
    Views::ViewsManager* mViewManager;
    Analysis::AnalysisManager* mAnalysisManager{NULL};
    Core::SaveManager *mSaveManager;

    AutoRechargeForm* mAutoRecharge{NULL};  //自动回灌
    VideoPlayerForm* mVideoPlayser{NULL};   //视频回放

    quint64 mCurrentFrameTime{0};
    quint64 mFileBeginTime{0};
    quint64 mFileEndTime{0};
};

#endif // DEBUGCONTROLFORM_H
