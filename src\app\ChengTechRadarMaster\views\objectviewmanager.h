﻿#ifndef OBJECTVIEWMANAGER_H
#define OBJECTVIEWMANAGER_H

#include <QObject>
#include <QVariant>

class AnalysisDataView;

namespace Views {
namespace ObjectView {
class ObjectView;

class ObjectViewManager : public QObject
{
    Q_OBJECT
public:
    explicit ObjectViewManager(QObject *parent = nullptr);

    ObjectView *newObjectView(QWidget *parent = nullptr);
    QList<ObjectView *> newObjectView(const QVariant &config, QWidget *parent = nullptr);

    void removeObjectView(ObjectView *view);

    QVariant objectViewConfig() const;

signals:

public slots:
    void addAnalysisDataView(AnalysisDataView *analysisDataView);
//    void addAnalysisDataView(AnalysisDataView *analysisDataView);

private:
    QList<ObjectView *> mObjectViews;
    QList<AnalysisDataView*> mAnalysisDataViews;
};

} // namespace ObjectView
} // namespace Views

#endif // OBJECTVIEWMANAGER_H
