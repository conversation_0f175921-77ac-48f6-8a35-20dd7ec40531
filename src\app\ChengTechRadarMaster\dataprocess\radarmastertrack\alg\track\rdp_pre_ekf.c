﻿/**
 * @file rdp_pre_ekf.c
 * @brief 
 * <AUTHOR> (shao<PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-09
 * 
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-09 <td>1.0f     <td>wangjuhua     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef PC_DBG_FW
#include "rdp_pre_ekf.h"
#include "rdp_main_ekf.h"
#else
#include "alg/track/rdp_pre_ekf.h"
#include "alg/track/rdp_main_ekf.h"
#endif
/**
 * @brief 计算扩展卡尔曼所需要的矩阵
 * @param a 
 * @param T 帧间隔
 * @param EKF_A 
 * @param EKF_Q 扰动矩阵实参
 */
void RDP_Track_cacEKFMatrixs(float a, float T, float EKF_A[36], float EKF_Q[36])
{
  float h1;
  float h2;
  float h3;
  float q22;
  float q23;
  float q33;

  float ap3 = a * a * a;
  float ap4 = ap3 * a;
  float ap5 = ap4 * a;
  float Tp3 = T * T * T;

  h1 = 1.0f / (a * a) * ((-1.0f + a * T) + expf(-a * T));
  h2 = 1.0f / a * (1.0f - expf(-a * T));
  h3 = expf(-a * T);

  /*  A:      x  y  vx  vy  ax  ay */
  EKF_A[0] = 1.0f;
  EKF_A[6] = 0.0f;
  EKF_A[12] = T;
  EKF_A[18] = 0.0f;
  EKF_A[24] = h1;
  EKF_A[30] = 0.0f;
  EKF_A[1] = 0.0f;
  EKF_A[7] = 1.0f;
  EKF_A[13] = 0.0f;
  EKF_A[19] = T;
  EKF_A[25] = 0.0f;
  EKF_A[31] = h1;
  EKF_A[2] = 0.0f;
  EKF_A[8] = 0.0f;
  EKF_A[14] = 1.0f;
  EKF_A[20] = 0.0f;
  EKF_A[26] = h2;
  EKF_A[32] = 0.0f;
  EKF_A[3] = 0.0f;
  EKF_A[9] = 0.0f;
  EKF_A[15] = 0.0f;
  EKF_A[21] = 1.0f;
  EKF_A[27] = 0.0f;
  EKF_A[33] = h2;
  EKF_A[4] = 0.0f;
  EKF_A[10] = 0.0f;
  EKF_A[16] = 0.0f;
  EKF_A[22] = 0.0f;
  EKF_A[28] = h3;
  EKF_A[34] = 0.0f;
  EKF_A[5] = 0.0f;
  EKF_A[11] = 0.0f;
  EKF_A[17] = 0.0f;
  EKF_A[23] = 0.0f;
  EKF_A[29] = 0.0f;
  EKF_A[35] = h3;

  /* % Q */
  /*  q */
  h1 = 1.0f / (2.0f * ap5) * (((((1.0f - expf(-2.0f * a * T)) + 2.0f *
    a * T) + 2.0f * ap3 * Tp3 / 3.0f) - 2.0f * (a *
    a) * (T * T)) - 4.0f * a * T * expf(-a * T));
  h2 = 1.0f / (2.0f * ap4) * (((((expf(-2.0f * a * T) + 1.0f) - 2.0f *
    expf(-a * T)) + 2.0f * a * T * expf(-a * T)) - 2.0f * a * T) + a * a * (T * T));
  h3 = 1.0f / (2.0f * ap3) * ((1.0f - expf(-2.0f * a * T)) - 2.0f * a * T * expf(-a * T));
  q22 = 1.0f / (2.0f * ap3) * (((4.0f * expf(-a * T) - 3.0f) - expf(-2.0f * a * T)) + 2.0f * a * T);
  q23 = 1.0f / (2.0f * (a * a)) * ((expf(-2.0f * a * T) + 1.0f) - 2.0f * expf(-a * T));
  q33 = 1.0f / (2.0f * a) * (1.0f - expf(-2.0f * a * T));

  /*  Q:            x,       y,      vx,      vy,      ax,      ay */
  EKF_Q[0] = 2.0f * a * h1;
  EKF_Q[6] = 0.0f;
  EKF_Q[12] = 2.0f * a * h2;
  EKF_Q[18] = 0.0f;
  EKF_Q[24] = 2.0f * a * h3;
  EKF_Q[30] = 0.0f;
  EKF_Q[1] = 0.0f;
  EKF_Q[7] = 2.0f * a * h1;
  EKF_Q[13] = 0.0f;
  EKF_Q[19] = 2.0f * a * h2;
  EKF_Q[25] = 0.0f;
  EKF_Q[31] = 2.0f * a * h3;
  EKF_Q[2] = 2.0f * a * h2;
  EKF_Q[8] = 0.0f;
  EKF_Q[14] = 2.0f * a * q22;
  EKF_Q[20] = 0.0f;
  EKF_Q[26] = 2.0f * a * q23;
  EKF_Q[32] = 0.0f;
  EKF_Q[3] = 0.0f;
  EKF_Q[9] = 2.0f * a * h2;
  EKF_Q[15] = 0.0f;
  EKF_Q[21] = 2.0f * a * q22;
  EKF_Q[27] = 0.0f;
  EKF_Q[33] = 2.0f * a * q23;
  EKF_Q[4] = 2.0f * a * h3;
  EKF_Q[10] = 0.0f;
  EKF_Q[16] = 2.0f * a * q23;
  EKF_Q[22] = 0.0f;
  EKF_Q[28] = 2.0f * a * q33;
  EKF_Q[34] = 0.0f;
  EKF_Q[5] = 0.0f;
  EKF_Q[11] = 2.0f * a * h3;
  EKF_Q[17] = 0.0f;
  EKF_Q[23] = 2.0f * a * q23;
  EKF_Q[29] = 0.0f;
  EKF_Q[35] = 2.0f * a * q33;
}

