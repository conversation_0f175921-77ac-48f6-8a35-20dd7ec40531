﻿#include "framebinarysaveworker.h"
#include <QThread>
#include <QDateTime>
#include <QIODevice>

namespace Analysis {

FrameBinarySaveWorker::FrameBinarySaveWorker(QObject *parent) : QObject(parent)
{
    clear();
    mSave = false;
}

FrameBinarySaveWorker::~FrameBinarySaveWorker()
{
    mFile.close();
}

void    FrameBinarySaveWorker::clear()
{
    mCurrentCnt = 0;

    for( int i=0; i<MAX_RADAR_COUNT; i++ ){
        mCanData[i].clear();
        mTargetPoint16Data[i].clear();
        mLanePointData[i].clear();
    }
}

void    FrameBinarySaveWorker::writeFileHead()
{
    FrameBinarySaveFormat_FHead fhead;
    fhead.version = 0x01020304;
    fhead.ct = QDateTime::currentDateTime().toMSecsSinceEpoch();
    fhead.fileNum = 0;
    fhead.reservedNum = 0;

    fhead.protocolNum = 4;
    fhead.protocols.append(1);
    fhead.protocols.append(2);
    fhead.protocols.append(3);
    fhead.protocols.append(4);

    fhead.reservedByte.clear();

    fhead.saveToStream( mStream );
}

bool FrameBinarySaveWorker::startSave(const QString &savePath, const QDateTime &beginTime )
{
    clear();
    mSavePath = savePath;
    mFile.setFileName(QString("%1/%2.binary").arg(savePath).arg(beginTime.toString("yyyy-MM-dd hh-mm-ss-zzz")));
    if( !mFile.open( QFile::WriteOnly ) ){
        qDebug() << "打开二进制文件失败！";
        return false;
    }
    mSave = true;
    mStream.setDevice( &mFile );
    writeFileHead();
    return true;
}

bool FrameBinarySaveWorker::stopSave()
{
    if( mFile.isOpen() )
        mFile.close();
    mSave = false;
    return true;
}

void    FrameBinarySaveWorker::writeCanHead( quint8 radarID )
{
    FrameBinarySaveFormat_Head head;
    head.type = 1;
    head.devId = radarID;
    head.ct = QDateTime::currentDateTime().toMSecsSinceEpoch();
    mCurrentCnt++;
    head.cnt1 = mCurrentCnt;
    head.cnt2 = mCurrentCnt;
    head.cnt3 = mCurrentCnt;
    head.bodyLen = mCanData[radarID].size();

   // head.Printf();
    head.saveToStream( mStream );
}

void    FrameBinarySaveWorker::writeCanBody( quint8 radarID )
{
    mStream.writeRawData( mCanData[radarID].data(), mCanData[radarID].size() );
    mCanData[radarID].clear();
}

void    FrameBinarySaveWorker::addRadarBody( quint8 radarID, const Devices::Can::CanFrame &frame, bool bEndFrame )
{
    addBody( radarID, HeadDataType::RadarData, frame, bEndFrame );
//    if( !mSave ){
//        return;
//    }

//    FrameBinarySaveFormat_Body body;
//    body.type = frame.canFD() ? 1 : 0 ;
//    body.expand = frame.extended() ? 1 : 0;
//    body.timestemp = frame.systemTimeStemp();
//    body.id = frame.id();
//    body.data = frame.data();
//    body.len = body.data.size();

//    QDataStream straem( &mCanData[radarID/*frame.radarID()*/], QIODevice::Append );
//    body.saveToStream( straem );
//    if( bEndFrame ){
//        //写入头
//        writeCanHead( radarID/*frame.radarID()*/ );
//        //写入数据主体
//        writeCanBody( radarID/*frame.radarID()*/ );
//        mFile.flush();
//    }
}

void FrameBinarySaveWorker::addTargetPoint16Body(quint8 radarID, const Devices::Can::CanFrame &frame, bool bEndFrame)
{
    addBody( radarID, HeadDataType::TargetPoint16Data, frame, bEndFrame );
}

void FrameBinarySaveWorker::addLanePointBody(quint8 radarID, const Devices::Can::CanFrame &frame, bool bEndFrame)
{
    addBody( radarID, HeadDataType::LanePointData, frame, bEndFrame );
}

void FrameBinarySaveWorker::addBody(quint8 radarID, FrameBinarySaveWorker::HeadDataType dataType, const Devices::Can::CanFrame &frame, bool bEndFrame)
{
    if( !mSave )
        return;
    FrameBinarySaveFormat_Body body;
    body.type = frame.canFD() ? 1 : 0 ;
    body.expand = frame.extended() ? 1 : 0;
    body.timestemp = frame.systemTimeStemp();
    body.id = frame.id();
    body.data = frame.data();
    body.len = body.data.size();

    switch( dataType ){
    case HeadDataType::RadarData:
        {
            QDataStream straem( &mCanData[radarID], QIODevice::Append );
            body.saveToStream( straem );
        }
        break;
    case HeadDataType::TargetPoint16Data:
        {
            QDataStream straem( &mTargetPoint16Data[radarID], QIODevice::Append );
            body.saveToStream( straem );
        }
        break;
    case HeadDataType::LanePointData:
        {
            QDataStream straem( &mLanePointData[radarID], QIODevice::Append );
            body.saveToStream( straem );
        }
        break;
    default:
        break;
    }

    if( bEndFrame )
        writeHeadAndBody( radarID, dataType );
}

void FrameBinarySaveWorker::writeHeadAndBody(quint8 radarID, FrameBinarySaveWorker::HeadDataType dataType)
{
    //写入头
    FrameBinarySaveFormat_Head head;
    head.type = dataType;
    head.devId = radarID;
    head.ct = QDateTime::currentDateTime().toMSecsSinceEpoch();
    mCurrentCnt++;
    head.cnt1 = mCurrentCnt;
    head.cnt2 = mCurrentCnt;
    head.cnt3 = mCurrentCnt;

    switch( dataType ){
    case HeadDataType::RadarData:
        head.bodyLen = mCanData[radarID].size();
        break;
    case HeadDataType::TargetPoint16Data:
        head.bodyLen = mTargetPoint16Data[radarID].size();
        break;
    case HeadDataType::LanePointData:
        head.bodyLen = mLanePointData[radarID].size();
        break;
    default:
        break;
    }

   // head.Printf();
    head.saveToStream( mStream );

    //写入数据主体
    switch( dataType ){
    case HeadDataType::RadarData:
        mStream.writeRawData( mCanData[radarID].data(), mCanData[radarID].size() );
        mCanData[radarID].clear();
        break;
    case HeadDataType::TargetPoint16Data:
        mStream.writeRawData( mTargetPoint16Data[radarID].data(), mTargetPoint16Data[radarID].size() );
        mTargetPoint16Data[radarID].clear();
        break;
    case HeadDataType::LanePointData:
        mStream.writeRawData( mLanePointData[radarID].data(), mLanePointData[radarID].size() );
        mLanePointData[radarID].clear();
        break;
    default:
        break;
    }
}

};
