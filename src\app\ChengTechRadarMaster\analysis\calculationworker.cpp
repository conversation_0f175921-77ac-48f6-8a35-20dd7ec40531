﻿#include "calculationworker.h"

#include "analysisworker.h"

#include <QThread>
#include <QTimer>
#include <QDebug>

#ifdef ALGORITHM_DEBUG
#include "dataprocess/dataprocess.h"
#endif

namespace Analysis {

CalculationWorker::CalculationWorker(AnalysisWorker *analysisWorker, QObject *parent)
    : QObject(parent),
      mAnalysisWorker(analysisWorker)
{
    for (int i = 0; i < sizeof (mAnalysisDatas) / sizeof (mAnalysisDatas[0]); ++i)
    {
        mAnalysisDatas[i].mRadarID = i;
    }
    connect(mAnalysisWorker, &AnalysisWorker::analysisFinished, this, &CalculationWorker::calculate);
    connect(mAnalysisWorker, &AnalysisWorker::analysisTargetFinished, this, &CalculationWorker::calculateTarget);
    connect(mAnalysisWorker, &AnalysisWorker::analysisTargetFinishedF, this, &CalculationWorker::calculateTargetF);
    connect(mAnalysisWorker, &AnalysisWorker::analysisTrueSystemFinished, this, &CalculationWorker::calculateTrueSystem);
}

CalculationWorker::~CalculationWorker()
{

}

void CalculationWorker::setDataProcess(bool dataProcess, bool interpolation, bool radarFusion,
                                       quint8 masterRadarID, quint8 sloveRadarID,
                                       quint8 isorgpoint, quint8 isShowCandiObj, quint8 eolmode)
{
    mDataProcess = dataProcess;
    mInterpolation = interpolation;
    mRadarFusion = radarFusion;
    mMasterRadarID = masterRadarID;
    mSlaveRadarID = sloveRadarID;
    mIsorgpoint = isorgpoint;
    mIsShowCandiObj = isShowCandiObj;
    mEolMode = eolmode;
    qDebug() << __FUNCTION__ << __LINE__ << mDataProcess;
#ifdef ALGORITHM_DEBUG
    if (mDataProcess || mInterpolation) {
        qDebug() << DataProcess::instance();
        DataProcess::instance()->init(mRadarFusion, mMasterRadarID, mSlaveRadarID, mIsorgpoint,mIsShowCandiObj);
        connect(DataProcess::instance(), &DataProcess::sloveRadarFinished, this, &CalculationWorker::calculateSlaveRadar, Qt::UniqueConnection);
        connect(this, &CalculationWorker::sigSetIsShowCandiObj,DataProcess::instance(), &DataProcess::setIsShowCandiObj, Qt::UniqueConnection);
    }
#endif
}

void CalculationWorker::setEOLData(quint8 eolType)
{
#ifdef ALGORITHM_DEBUG
    DataProcess::instance()->initEolType(eolType);
#endif
}
void CalculationWorker::setEOLRunStateFunc()
{
    DataProcess::instance()->setEOLRunState();
}

void CalculationWorker::setIsShowCandiObj(bool isShow)
{
    mIsShowCandiObj = isShow;
    emit sigSetIsShowCandiObj(mIsShowCandiObj);
}

void CalculationWorker::calculateSlaveRadar(quint8 radarID)
{
//    qDebug() << __FUNCTION__ << __LINE__ << radarID;
//    return;
#ifdef ALGORITHM_DEBUG
    AnalysisData &analysisData = mAnalysisDatas[radarID];
    const AnalysisData &sloveRadarAnalysisData = DataProcess::instance()->sloveRadarAnalysisData();

    for (int i = 0; i < FrameTargetCount; ++i)
    {
        analysisData.mTargets[i] = sloveRadarAnalysisData.mTargets[i];
    }
    analysisData.mRadarID = sloveRadarAnalysisData.mRadarID;
    analysisData.mAlarmData = sloveRadarAnalysisData.mAlarmData;                       // 告警信息
    analysisData.mVehicleData = sloveRadarAnalysisData.mVehicleData;                   // 汽车数据
    analysisData.mEndFrameData = sloveRadarAnalysisData.mEndFrameData;


    analysisData.calculate();

    calculationAlarmAndEarlyWarning(radarID);   // 计算告警和预警

    emit calculateFinished(radarID, analysisData);
#endif
}

void CalculationWorker::calculate(quint8 radarID, const AnalysisData &analysisData)
{
    // 不能整体赋值，会覆盖真值和其它不同步的目标点
    AnalysisData &data = mAnalysisDatas[radarID];

    for (int i = 0; i < FrameTargetCount; ++i)
    {
        data.mTargets[i] = analysisData.mTargets[i];
    }
    data.mRadarID = analysisData.mRadarID;
    data.mAlarmData = analysisData.mAlarmData;                       // 告警信息
    data.mVehicleData = analysisData.mVehicleData;                   // 汽车数据
    data.mEndFrameData = analysisData.mEndFrameData;

#ifdef ALGORITHM_DEBUG
    // 走数据处理
    if (mDataProcess) {
        // 主雷达
        if (!DataProcess::instance()->process(data)) {
                qDebug() << __FUNCTION__ << __LINE__ << "master radar data process error!";
        }

        if (mRadarFusion && radarID == DataProcess::instance()->sloveRadarID()) {
            return;
        }
        if (mAnalysisDatas[radarID].m16Targets.mValid) {
            emit calculateTargetFinished(radarID, Frame16Track, mAnalysisDatas[radarID].m16Targets);
        }
    }
#endif

    data.calculate();

    calculationAlarmAndEarlyWarning(radarID);   // 计算告警和预警

    emit calculateFinished(radarID, data);
}

void CalculationWorker::calculatePlayback(quint8 radarID, const AnalysisData &radarData)
{
    mAnalysisDatas[radarID] = radarData;
    mAnalysisDatas[radarID].calculate();
    calculationAlarmAndEarlyWarning(radarID);   // 计算告警和预警

    emit calculateFinished(radarID, mAnalysisDatas[radarID]);
}

void CalculationWorker::calculateTarget(quint8 radarID, int frameType)
{
    if (mDataProcess) {
        return;
    }
    switch (frameType)
    {
    case FrameRawTarget:
    case FrameTrackTarget:
        mAnalysisDatas[radarID].mTargets[frameType] = mAnalysisWorker->mAnalysisDatas[radarID].mTargets[frameType];
        mAnalysisDatas[radarID].calculateTarget((AnalysisFrameType)frameType);
        emit calculateTargetFinished(radarID, frameType, mAnalysisDatas[radarID].mTargets[frameType]);
        break;
    case Frame200Raw:
        mAnalysisDatas[radarID].m200Targets = mAnalysisWorker->mAnalysisDatas[radarID].m200Targets;
        mAnalysisDatas[radarID].calculateTarget(Frame200Raw);

        emit calculateTargetFinished(radarID, frameType, mAnalysisDatas[radarID].m200Targets);
        break;
    case Frame3Track:
//        qDebug() << __FUNCTION__ << __LINE__ << radarID
//                 << mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mValid
//                 << mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargetCount;
        mAnalysisDatas[radarID].mLockTargets = mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets;
        emit calculateTargetFinished(radarID, frameType, mAnalysisDatas[radarID].mLockTargets);
        break;
    case Frame16Track:
//        qDebug() << __FUNCTION__ << __LINE__ << radarID
//                 << mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mValid
//                 << mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount;
        mAnalysisDatas[radarID].m16Targets = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets;
//        qDebug() << __FUNCTION__ << __LINE__ << radarID << mAnalysisDatas[radarID].m16Targets.mTargetHeader.mMeasurementCount;
        emit calculateTargetFinished(radarID, frameType, mAnalysisDatas[radarID].m16Targets);
        break;
    case FrameELKTrack:
        mAnalysisDatas[radarID].mELKTargets = mAnalysisWorker->mAnalysisDatas[radarID].mELKTargets;
        emit calculateTargetFinished(radarID, frameType, mAnalysisDatas[radarID].mELKTargets);
        break;
    }
}

void CalculationWorker::calculateTargetF(const Parser::ParsedDataTypedef::TargetsF &targets)
{
    if (targets.mParsedDataType >= Parser::ParsedDataTypedef::ParsedDataNone) {
        return;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << targets.mParsedDataType << targets.mEffectiveNumber;
    Parser::ParsedDataTypedef::TargetsF &_targets = mParsedData.mTargets[targets.mParsedDataType];
    _targets = targets;
    _targets.calculate(); // 计算数据

    emit calculateFinishedF(_targets);
}

void CalculationWorker::setDeteStatistics(DeteStatisData &StatisData)
{
    for( int i=0; i<MAX_RADAR_COUNT; i++ )
    {
        memcpy(&mAnalysisDatas[i].mDeteStatisData,&StatisData,sizeof (StatisData));

        //统计清0
        mAnalysisDatas[i].mDeteStatisData.mDeteProb      = 0;
        mAnalysisDatas[i].mDeteStatisData.mDeteCnt       = 0;
        mAnalysisDatas[i].mDeteStatisData.mDeteTime      = 0;
        mAnalysisDatas[i].mDeteStatisData.mDeteAllCnt    = 0;
    }
}

void CalculationWorker::calculateTrueSystem()
{
    AnalysisData &mAnalysisData = mAnalysisDatas[TRUE_VALUE_RADAR_ID_IFS300];
    mTrueObjectInfo = mAnalysisWorker->mTrueObjectInfo;
    Target *targets = mAnalysisData.mTrueTarget;
    for (int i = 0; i < sizeof (mAnalysisData.mTrueTarget) / sizeof (mAnalysisData.mTrueTarget[0]); ++i)
    {
        targets[i] = mAnalysisWorker->mAnalysisDatas[TRUE_VALUE_RADAR_ID_IFS300].mTrueTarget[i];
//        mAlarmCalculate->calculationEarlyWarning(mAnalysisData.mRadarID, targets + i, &(mAnalysisData.mVehicleData), &(mAnalysisData.mEarlyAlarmData));   // 计算真值预警
    }
//    calculationAlarmAndEarlyWarning(analysisData->mRadarID,
//                                    mAnalysisData.mTrueTarget,
//                                    sizeof (mAnalysisData.mTrueTarget) / sizeof (mAnalysisData.mTrueTarget[0]),
//            &analysisData->mVehicleData, &analysisData->mEarlyAlarmData);
}

void CalculationWorker::calculationAlarm(Targets &targets, AlarmData &alarmData)
{
    if ((int)alarmData.mAlarmBSDObjectID >= MAX_TARGET_COUNT ||
            (int)alarmData.mAlarmDOWObjectID >= MAX_TARGET_COUNT ||
            (int)alarmData.mAlarmFCTAObjectID >= MAX_TARGET_COUNT ||
            (int)alarmData.mAlarmLCAObjectID >= MAX_TARGET_COUNT ||
            (int)alarmData.mAlarmRCTAObjectID >= MAX_TARGET_COUNT ||
            (int)alarmData.mAlarmRCWObjectID >= MAX_TARGET_COUNT) {
//        qDebug() << __FUNCTION__ << __LINE__ << "Alarm Object ID error!"
//                 << (int)alarmData.mAlarmBSDObjectID
//                    << (int)alarmData.mAlarmDOWObjectID
//                    << (int)alarmData.mAlarmFCTAObjectID
//                    << (int)alarmData.mAlarmLCAObjectID
//                    << (int)alarmData.mAlarmRCTAObjectID
//                    << (int)alarmData.mAlarmRCWObjectID;
        return;
    }
    quint16 alarmID = 0xFFFF;
    Target *target = targets.mTargets;
    if (alarmData.mAlarmBSDLevel)
    {
        alarmID = alarmData.mAlarmBSDObjectID;

        alarmData.mAlarmELKLevel = alarmData.mAlarmBSDLevel;
        alarmData.mAlarmELKObjectID = alarmData.mAlarmBSDObjectID;
    }
    if (alarmData.mAlarmDOWRLevel)
    {
        alarmID = alarmData.mAlarmDOWObjectID;
    }
    if (alarmData.mAlarmDOWFLevel)
    {
        alarmID = alarmData.mAlarmDOWObjectID;
    }
    if (alarmData.mAlarmFCTALevel)
    {
        alarmID = alarmData.mAlarmFCTAObjectID;
    }
//    if (alarmData.mAlarmFCTBLevel)
//    {
//        alarmID = alarmData.mAlarmFCTBObjectID;
//    }
//    qDebug() << __FUNCTION__ << __LINE__ << alarmData.mAlarmLCALevel << alarmData.mAlarmLCAObjectID;
    if (alarmData.mAlarmLCALevel)
    {
        alarmID = alarmData.mAlarmLCAObjectID;

        alarmData.mAlarmELKLevel = alarmData.mAlarmLCALevel;
        alarmData.mAlarmELKObjectID = alarmData.mAlarmLCAObjectID;
    }
    if (alarmData.mAlarmRCTALevel)
    {
        alarmID = alarmData.mAlarmRCTAObjectID;
    }
    if (alarmData.mAlarmRCWLevel)
    {
        alarmID = alarmData.mAlarmRCWObjectID;
    }

    if (alarmID < MAX_TARGET_COUNT) {
        for (int i = 0; i < targets.mTargetCount; ++i) {
            if(targets.mTargets[i].mID == alarmID) {
                targets.mTargets[i].mAlarmed = true;
                break;
            }
        }
    }
}

void CalculationWorker::calculationAlarmAndEarlyWarning(quint8 radarID)
{
    AlarmData &alarmData = mAnalysisDatas[radarID].mAlarmData;
    Targets &targets= mAnalysisDatas[radarID].mTargets[FrameTrackTarget];

    calculationAlarm(targets, alarmData);
}

} // namespace Analysis
