/**
 * ************************************************************************
 * @file vmm_geely.c
 * @brief 吉利车辆模式管理，根据车辆状态控制雷达行为
 * 
 * <AUTHOR>
 * @date 2024-06-17
 * 
 * ************************************************************************
 * @copyright Copyright (c) 2024  深圳承泰科技有限公司 All Rights Reserved.
 * 
 * ************************************************************************
 */

#include "vmm.h"
#include "sys_status.h"
#include "arc_exception.h"
#include "cfg.h"
#include "vehicle_cfg.h"
#include "xprintf.h"

#define CLOSE_COMMU_TIMEOUT  9000u  /**< usageMode不满足时关闭通信的时间，单位ms */

#define SLEEP_TIMEOUT       14000u  /**< 关闭通信后进入休眠的时间，单位ms */

#define QCM_ACTIVE_TIME     15u     /**< QCM上升沿时系统已active时间，单位s */

/**
 * @brief 车辆模式定义
 */
typedef enum
{
    CAR_MODE_NORMAL = 0x0,  /**< 正常模式，默认值 */
    CAR_MODE_TRNSP  = 0x1,  /**< Transport模式 */
    CAR_MODE_FCY    = 0x2,  /**< Factory模式 */
    CAR_MODE_CRASH  = 0x3,  /**< Crash模式 */
    CAR_MODE_DYNO   = 0x5,  /**< dynp模式 */
} CAR_MODE_E;

/**
 * @brief 使用模式定义
 */
typedef enum
{
    USAGE_MODE_ABDND  = 0,   /**< Abandoned模式 */
    USAGE_MODE_INACTV = 1,   /**< Inactive模式，默认值 */
    USAGE_MODE_CNVINC = 2,   /**< Convenience模式 */
    USAGE_MODE_ACTV   = 0xB, /**< Active模式 */
    USAGE_MODE_DRVG   = 0xD  /**< Driving模式 */
} USAGE_MODE_E;

/**
 * @brief ElPowerLevel定义
 */
typedef enum
{
    ElP_LEVEL_0 = 0,  /**< 无限制模式，默认值 */
    ElP_LEVEL_1 = 1,  /**< 冷启动，关闭所有非推进系统 */
    ElP_LEVEL_2 = 2,  /**< 无影响 */
    ElP_LEVEL_3 = 3,  /**< 无影响 */
    ElP_LEVEL_4 = 4,  /**< 无影响 */
    ElP_LEVEL_5 = 5,  /**< 故障模式，关闭非必要负载 */
    ElP_LEVEL_6 = 6,  /**< 无限制模式 */
} ElPOWER_LEVEL_E;

/**
 * @brief ElEnergyLevel定义
 */
typedef enum
{
    ElE_LEVEL_0 = 0,  /**< 无限制模式，默认值 */
    ElE_LEVEL_4 = 4,  /**< 关闭非必要负载 */
} ElENERGY_LEVEL_E;

/**
 * @brief QCM FAULT状态
 */
typedef enum
{
    QCM_NO_FAULT = 0,   /**< 无fault */
    QCM_FAULT    = 1,   /**< fault */
} QCM_FAULT_E;

/**
 * @brief 模块状态
 */
typedef enum
{
    MOD_INITED = 0,    /**< 初始化状态 */
    MOD_UPDATED = 1,   /**< 已收到车身信号 */
} MOD_STATUS_E;

/**
 * @brief 模式状态
 */
typedef struct
{
    MOD_STATUS_E status;
    CAR_MODE_E carMode;
    USAGE_MODE_E usageMode;
    ElPOWER_LEVEL_E ElPLevel;
    ElENERGY_LEVEL_E ElELevel;
    QCM_FAULT_E qcmFault;

    uint32_t sigUpdateTime;       /**< 收到车身数据的时间 */
    uint32_t closeCommuStartTime; /**< 关闭通信起始时间，单位ms */
    uint32_t sleepStartTime;      /**< 休眠起始时间，单位ms */
    uint32_t activeTime;          /**< 被唤醒的时间, 单位s */
    bool commuActive;             /**< 是否可通信 */
    bool needSleep;               /**< 是否进入休眠 */
    bool wakeable;                /**< 是否满足唤醒条件 */
    bool pncValid;                /**< PNC状态 */
    bool qcmDid;                  /**< 满足QCM DID保存条件 */
} stModeStatus;

static stModeStatus gVmStatus;

/**
 * @brief vmm模块初始化，将参数设置到default状态
 * <AUTHOR>
 */
void Vmm_init(void)
{
    gVmStatus.carMode = CAR_MODE_NORMAL;
    gVmStatus.usageMode = USAGE_MODE_INACTV;
    gVmStatus.ElPLevel = ElP_LEVEL_0;
    gVmStatus.ElELevel = ElE_LEVEL_0;
    gVmStatus.qcmFault = QCM_NO_FAULT;

    gVmStatus.sigUpdateTime = 0;
    gVmStatus.closeCommuStartTime = SYS_getTimeStampMs();
    gVmStatus.sleepStartTime = 0;
    gVmStatus.activeTime = (uint32_t)(SYS_getTimeStampMs() / 1000u);

    gVmStatus.commuActive = false;
    gVmStatus.needSleep = true;
    gVmStatus.wakeable = false;
    gVmStatus.status = MOD_INITED;
    gVmStatus.pncValid = true;
    gVmStatus.qcmDid = false;
}

/**
 * @brief 更新VMM状态，此接口需要定时调用，周期不大于500ms
 * <AUTHOR>
 */
void Vmm_updateStatus(void)
{
    uint32_t timeout;
    uint32_t status = arc_lock_save();

    uint32_t sigUpdateTime = gVmStatus.sigUpdateTime;
    CAR_MODE_E carMode = gVmStatus.carMode;
    USAGE_MODE_E usageMode = gVmStatus.usageMode;
    ElPOWER_LEVEL_E ElPowerLevel = gVmStatus.ElPLevel;
    ElENERGY_LEVEL_E ElEnergyLevel = gVmStatus.ElELevel; 
    //xprintf("carMode = 0x%x usageMode=0x%x ElPowerLevel = %d ElEnergyLevel = %d \r\n",carMode,usageMode,ElPowerLevel,ElEnergyLevel); 

    arc_unlock_restore(status);

    uint32_t curTimeMs = (uint32_t)SYS_getTimeStampMs();

    if (gVmStatus.status == MOD_UPDATED)
    {
        //更新通信状态和休眠状态
        if (carMode == CAR_MODE_FCY || carMode == CAR_MODE_TRNSP || carMode == CAR_MODE_CRASH)
        {
            gVmStatus.commuActive = false;
            gVmStatus.needSleep = true;
            gVmStatus.wakeable = false;
            gVmStatus.closeCommuStartTime = curTimeMs;
            gVmStatus.sleepStartTime = curTimeMs;
        }
        else if ((usageMode == USAGE_MODE_ACTV || usageMode == USAGE_MODE_DRVG))
        {
            gVmStatus.commuActive = true;
            gVmStatus.needSleep = false;
            gVmStatus.wakeable = true;
            gVmStatus.closeCommuStartTime = 0;
            gVmStatus.sleepStartTime = 0;
            //gVmStatus.activeTime = (uint32_t)(SYS_getTimeStampMs() / 1000u);
        }
        else if (gVmStatus.commuActive)
        {
            if (gVmStatus.closeCommuStartTime > 0)
            {
                timeout = curTimeMs - gVmStatus.closeCommuStartTime;
                if (timeout > CLOSE_COMMU_TIMEOUT)
                {
                    gVmStatus.commuActive = false;
                    gVmStatus.sleepStartTime = curTimeMs;
                }
            }
            else 
            {
                gVmStatus.closeCommuStartTime = sigUpdateTime;
            }
        }
        else if (!gVmStatus.needSleep)
        {
            timeout = curTimeMs - gVmStatus.sleepStartTime;
            if (timeout > SLEEP_TIMEOUT)
            {
                gVmStatus.needSleep = true;
            }
        }

        //TODO 此处需要添加是否处于诊断会话中的判断
        if (gVmStatus.ElPLevel == ElP_LEVEL_1 || gVmStatus.ElPLevel == ElP_LEVEL_5 || gVmStatus.ElELevel == ElE_LEVEL_4)
        {
            gVmStatus.needSleep = true;
            gVmStatus.wakeable = false;
        }
    }
}

/**
 * @brief 查询通信是否使能
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_commuActived(void)
{
#ifndef VMM_ON
    return true;
#else
    //return gVmStatus.commuActive && gVmStatus.pncValid;
    return gVmStatus.pncValid;//20240813 经确认，通信只受PNC控制
#endif
}

/**
 * @brief 是否满足唤醒条件
 * <AUTHOR>
 * @return enum WAKE_STATUS_E 唤醒状态
 */
enum WAKE_STATUS_E Vmm_wakeable(void)
{
    enum WAKE_STATUS_E status;

    if (gVmStatus.wakeable)
    {
        status = WAKEABLE;
    }
    else if (gVmStatus.status == MOD_INITED)
    {
        status = NO_CONDITION;
    }
    else 
    {
        status = NOT_WAKEABLE;
    }

    return status;
}

/**
 * @brief 是否需要进入休眠
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_needSleep(void)
{
    return gVmStatus.needSleep;
}

/**
 * @brief pnc是否置1
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_pncValid(void)
{
    return gVmStatus.pncValid;
}

/**
 * @brief 是否可以产生DTC
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_dtcEnable(void)
{
    return (gVmStatus.carMode != CAR_MODE_DYNO) && !gVmStatus.needSleep;
}

/**
 * @brief vmm状态是否满足active条件，如果vmm出现会导致关闭通信或休眠的状态则此处返回false
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_statusActive(void)
{
    return gVmStatus.commuActive && gVmStatus.closeCommuStartTime == 0u;
}

/**
 * @brief 设置车身VMM相关的信号
 * <AUTHOR>
 * @param[in] carMode   车辆模式
 * @param[in] usageMode 使用模式
 * @param[in] ElPowerLevel 
 * @param[in] ElEnergyLevel
 */
void Vmm_setVehicleModeSig(uint8_t carMode, uint8_t usageMode, uint8_t ElPowerLevel, uint8_t ElEnergyLevel)
{
    uint32_t status = arc_lock_save();

    //driving mode 下有效的carmode 只有normal、crash、dyno；非driving下所有carmode均有效
    if (usageMode != USAGE_MODE_DRVG 
        || carMode == CAR_MODE_NORMAL || carMode == CAR_MODE_CRASH || carMode == CAR_MODE_DYNO)
    {
        gVmStatus.carMode = carMode;
    }
    
    gVmStatus.usageMode = usageMode;
    gVmStatus.ElPLevel = ElPowerLevel;
    gVmStatus.ElELevel = ElEnergyLevel;
    gVmStatus.sigUpdateTime = SYS_getTimeStampMs();
    gVmStatus.status = MOD_UPDATED;

    arc_unlock_restore(status);
}

/**
 * @brief 设置车辆模式
 * <AUTHOR>
 * @param[in] mode      车辆模式
 */
void Vmm_setCarMode(uint8_t mode)
{
    gVmStatus.carMode = mode;
}

/**
 * @brief 设置使用模式
 * <AUTHOR>
 * @param[in] mode      使用模式
 */
void Vmm_setUsageMode(uint8_t mode)
{
    gVmStatus.usageMode = mode;
}

/**
 * @brief 设置ElPowerLevel
 * <AUTHOR>
 * @param[in] level
 */
void Vmm_setElPowerLevel(uint8_t level)
{
    gVmStatus.ElPLevel = level;
}

/**
 * @brief 设置ElEnergyLevel
 * <AUTHOR>
 * @param[in] level 
 */
void Vmm_setElEnergyLevel(uint8_t level)
{
    gVmStatus.ElELevel = level;
}

/**
 * @brief 设置QCM fault状态
 * <AUTHOR>
 * @param[in] status    comment
 */
void Vmm_setQCMFault(uint8_t status)
{
    if (status == (uint8_t)QCM_FAULT && gVmStatus.qcmFault == QCM_NO_FAULT)
    {
        uint32_t curTime = (uint32_t)(SYS_getTimeStampMs() / 1000u);

        //if (!gVmStatus.needSleep && (curTime - gVmStatus.activeTime) > QCM_ACTIVE_TIME)
        //xprintf("curTime = %d  gVmStatus.activeTime = 0x%x \r\n",curTime,gVmStatus.activeTime);
        if (((curTime - gVmStatus.activeTime) > QCM_ACTIVE_TIME)&&((gVmStatus.carMode == 0x0)||(gVmStatus.carMode == 0x1)||(gVmStatus.carMode == 0x2)||(gVmStatus.carMode == 0x5)))
        {
            gVmStatus.qcmDid = true;
        }
    }

    gVmStatus.qcmFault = (QCM_FAULT_E)status;
}

/**
 * @brief 是否需要存储QCM DID
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_needSaveQCM(void)
{
    return gVmStatus.qcmDid;
}

/**
 * @brief 复位要存储QCM DID的状态
 * <AUTHOR>
 */
void Vmm_resetSaveQCM(void)
{
    gVmStatus.qcmDid = false;
}

/**
 * @brief 获取系统激活维持的时间
 * <AUTHOR>
 * @return uint32_t 时间，单位秒
 */
uint32_t Vmm_activeLastTime(void)
{
    uint32_t lastTime = 0;

    //if (gVmStatus.activeTime != 0u)  //屏蔽原因是active单位是秒，实际唤醒时间小于1秒，所以是0
    {
        uint32_t curTime = (uint32_t)(SYS_getTimeStampMs() / 1000u);
        lastTime = curTime - gVmStatus.activeTime;
    }
    
    return lastTime;
}

/**
 * @brief 当前VMM的输入信号是否满足激活条件
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_signalActive(void)
{
    return gVmStatus.commuActive && gVmStatus.closeCommuStartTime == 0u;
}

#if defined(RADAR_AUTOSAR) && defined(VMM_ON)
/**
 * @brief 根据VMM状态和PNC网络请求控制应用报文的收发，50ms周期调度
 * <AUTHOR>
 * @param[in] level 
 */
#include "Rte_COMCbk.h"
#include "Rte_ComM_Type.h"
#include "Com_Cfg.h"
#include "ComM_Internal.h"
#include "radarCfg.h"
uint8_t poweroffflage = 0;
uint8_t poweronflage = 1;

void Vmm_commControlProsess(void)
{
#if ((VCAN_PROTOCOL_VER_USING == VCAN_PROTOCOL_KX11)||(VCAN_PROTOCOL_VER_USING == VCAN_PROTOCOL_E245))    
    uint8 return_value = 0; 
   
    static uint8_t lastPNCState = false;
    static uint8_t lastlastPNCState = false;

    uint8_t RadarId;    
    RadarId = CFG_getRadarId();
    if((RadarId == RADAR_ID_FRONT_LEFT)||(RadarId == RADAR_ID_FRONT_RIGHT))
    {
        gVmStatus.pncValid = (ComM_Pnc[0].PncState != COMM_STATE_PNC_NO);
    }
    else
    {
        gVmStatus.pncValid = (ComM_Pnc[0].PncState != COMM_STATE_PNC_NO)|(ComM_Pnc[1].PncState != COMM_STATE_PNC_NO);
    }
   
    if (poweroffflage == 0)
    {
        if ((lastPNCState == true) && (lastlastPNCState == true)
        && (gVmStatus.pncValid == FALSE))           
        {
            //xprintf("poweroffflage is %d!time is %lu\r\n", poweroffflage, RTC_TO_MS(rtc_count()));
            poweroffflage = 1;
        }
    }
    if (poweronflage == 0)
    {
        if ((lastPNCState == false) && (lastlastPNCState == false) 
        && (gVmStatus.pncValid == true))
        {
            //xprintf("poweronflage is %d!time is %lu\r\n", poweronflage, RTC_TO_MS(rtc_count()));
            poweronflage = 1;
        }
    }      

    lastlastPNCState = lastPNCState;    
    lastPNCState = gVmStatus.pncValid;
#endif
}
#endif
