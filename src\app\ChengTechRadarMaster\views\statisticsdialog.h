﻿#ifndef STATISTICSDIALOG_H
#define STATISTICSDIALOG_H

#include <QDialog>
#include "analysis/analysisdata.h"

namespace Ui {
class statisticsDialog;
}

class statisticsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit statisticsDialog(QWidget *parent = nullptr);
    QVariant getSettings() const;
    ~statisticsDialog();

    bool setSettings(const QVariant &settings);
    void settingsApply();
    void calcDeteStatis(const AnalysisData &analysisData, AnalysisFrameType frameType);
signals:
    void sigApply(DeteStatisData &StatisData);

public slots:
    void calculateFinished(quint8 radarID, const AnalysisData &analysisData);

private slots:
    void on_pushButton_Set_clicked();

    void on_pushButtonStartSt_clicked();

private:
    Ui::statisticsDialog *ui;
    DeteStatisData gStatisData;

    //统计信息
    double  mMatchCost{0};
    double  mDeteProb{0.000};
    quint64 mDeteAllCnt{0};
    quint32 mWinMask{0};
    quint32 mMatchCnt{0};
    quint32 mWinLength{0};
    qint32 mDeteObjIdx{-1};
    qint32 mLastDeteObjIdx{-1};
    qint64 mDeteTime{0};
    qint64 mDeteStartTime{0};
    Target mLastTarget;

};

#endif // STATISTICSDIALOG_H
