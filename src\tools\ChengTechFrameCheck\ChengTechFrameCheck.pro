MASTER_LIB_DEPENDS += utils devices
include(../tools.pri)
include($$MASTER_LIBS/devices/candevices.pri)

QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

CHENGTECH_FRAME_CHECK_NAME = "ChengTech Radar Frame Check"
CHENGTECH_FRAME_CHECK_VERSION = 1.0.0
TARGET = "$${CHENGTECH_FRAME_CHECK_NAME} $${CHENGTECH_FRAME_CHECK_VERSION}"

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    ctanalysisworker.cpp \
    main.cpp \
    chengtechframecheck.cpp

HEADERS += \
    chengtechframecheck.h \
    ctanalysisworker.h

FORMS += \
    chengtechframecheck.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

DISTFILES += \
    version.h.in

QMAKE_SUBSTITUTES += version.h.in
