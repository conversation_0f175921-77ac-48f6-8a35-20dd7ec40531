﻿#include "byd120targetprotocol.h"

#include <QDebug>

namespace Analysis {
namespace Protocol {

BYD120TargetProtocol::BYD120TargetProtocol(AnalysisWorker *analysisWorker, QObject *parent)
    : IAnalysisProtocol(analysisWorker, parent)
{
    mProtocolType = ProtocolBYD120;

    memset(mTargetsIndex_1, 0, sizeof (mTargetsIndex_1));
    memset(mTargetsIndex_2, 0, sizeof (mTargetsIndex_1));
}

bool BYD120TargetProtocol::analysisFrame(const Devices::Can::CanFrame &frame)
{
    switch (frame.id())
    {
    case 0x291:
        byd3TargetParse_0x291(5, frame); // 5号雷达 BYD海外
        break;
    case 0x35F:
        byd3TargetParse_0x35F(4, frame); // 4号雷达 BYD海外
        break;
    case 0x36F:
        byd3TargetParse(5, frame); // 5号雷达
        break;
    case 0x690:
        byd16TargetClear(4);
    case 0x691:
    case 0x692:
    case 0x693:
        byd16TargetParsePart_1(4, frame);
        break;
    case 0x694:
        break;
    case 0x69A:
        byd16TargetParseFinished(4, frame);
        break;
    case 0x69C: // 和6号雷达ID冲突
        if (frame.length() == 8) {
            byd16TargetParseFinished(6, frame);
        } else {
            byd16TargetParsePart_2(4, frame);
        }
        break;
    case 0x69D: // 和7号雷达ID冲突
        if (frame.length() == 8) {
            byd16TargetParseFinished(7, frame);
        } else {
            byd16TargetParsePart_2(4, frame);
        }
        break;
    case 0x695:
        byd16TargetClear(5);
    case 0x696:
    case 0x697:
    case 0x698:
        byd16TargetParsePart_1(5, frame);
        break;
    case 0x699:
        break;
    case 0x69B:
        byd16TargetParseFinished(5, frame);
        break;
    case 0x69E:
    case 0x69F:
        byd16TargetParsePart_2(5, frame);
        break;
    case 0x680:
        byd16TargetClear(6);
    case 0x681:
    case 0x682:
    case 0x683:
        byd16TargetParsePart_1(6, frame);
        break;
    case 0x68A:
    case 0x68B:
        byd16TargetParsePart_2(6, frame);
        break;
//    case 0x69C: // 和4号雷达ID冲突
//        byd16TargetParseFinished(6, frame);
        break;
    case 0x6A0:
        break;
    case 0x685:
        byd16TargetClear(7);
    case 0x686:
    case 0x687:
    case 0x688:
        byd16TargetParsePart_1(7, frame);
        break;
    case 0x689:
        break;
    case 0x68C:
    case 0x68D:
        byd16TargetParsePart_2(7, frame);
        break;
//    case 0x69D: // 和4号雷达ID冲突
//        byd16TargetParseFinished(7, frame);
        break;
    default:
        return false;
    }

    return true;
}

qint8 BYD120TargetProtocol::radarID(const Devices::Can::CanFrame &frame)
{
    qint8 radarID = -1;
    switch (frame.id())
    {
    case 0x690:
    case 0x691:
    case 0x692:
    case 0x693:
    case 0x694:
    case 0x69A:
    case 0x69C:
    case 0x69D:
        radarID = 4;
        break;
    case 0x36F://3个目标点
    case 0x695:
    case 0x696:
    case 0x697:
    case 0x698:
    case 0x699:
    case 0x69B:
    case 0x69E:
    case 0x69F:
        radarID = 5;
        break;
    case 0x680:
    case 0x681:
    case 0x682:
    case 0x683:
    case 0x684:
    case 0x68A:
    case 0x68B:
    case 0x68C:
        radarID = 6;
        break;
    case 0x685:
    case 0x686:
    case 0x687:
    case 0x688:
    case 0x68D:
    case 0x68:
        radarID = 7;
        break;
    default:
        radarID = -1;
        break;
    }
    return radarID;
}

bool BYD120TargetProtocol::byd3TargetParse_0x291(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    quint8 *data = (quint8*)(frame.data().data());
    quint8 id = (data[0]);
    if (id == 0x01) {
        mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargetCount = 0; // 注意必须使用引用
    }
    quint8 state = ((data[5] & 0x80U) >> 7);
    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargetCount; // 注意必须使用引用
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "BYD 3 Target error!";
        return true;
    }

    if (state) {
        Target &target = mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargets[targetCount++];
        target.mID = (data[0]);
        target.mX = (((data[1]) * 0.1) - 12);
        target.mY = ((((data[2] & 0xFFU) + (((uint16_t)data[3] & 0x7U) << 8)) * 0.1) - 150);
        target.mVysog = (((data[3] & 0xF8U) >> 3) + (((uint16_t)data[4] & 0xFU) << 5));
        target.mVxsog = (((data[4] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0x3U) << 4));
        //    target.Target_Warning_291s1_S = ((data[5] & 0x38U) >> 3);
        //    target.Target_State_B1_291s1_S = ((data[5] & 0x80U) >> 7);
        //    target.Target_Type_291s1_S = (data[6] & 0xFU);
        //    target.Message_Counter_291s1_S = ((data[6] & 0xF0U) >> 4);
        //    target.Checksum_291s1_S = (data[7]);

        target.mValid = true;

//        qDebug() << __FUNCTION__ << __LINE__ << target.mID << target.mX << target.mY;
    }
    if (id == 0x01) {
        m0x291Count = 0;
    }
    m0x291Count++;
    if (m0x291Count >= 3 || id >= 0x03) {
        m0x291Count = 0;
        mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mValid = true;
        mAnalysisWorker->analysisEnd(radarID, Frame3Track);
    }

    return true;
}

bool BYD120TargetProtocol::byd3TargetParse_0x35F(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargetCount = 0; // 注意必须使用引用
    quint8 *data = (quint8*)(frame.data().data());
    quint8 state = ((data[5] & 0x80U) >> 7);
    if (state == 0) {
        qDebug() << __FUNCTION__ << __LINE__ << "BYD 3 Target error!";
        return true;
    }
    Target &target = mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargets[0];
    target.mID = (data[0]);
    target.mX = (((data[1]) * 0.1) - 12);
    target.mY = ((((data[2] & 0xFFU) + (((uint16_t)data[3] & 0x7U) << 8)) * 0.1) - 150);
    target.mVysog = (((data[3] & 0xF8U) >> 3) + (((uint16_t)data[4] & 0xFU) << 5));
    target.mVxsog = (((data[4] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0x3U) << 4));
//    target.Target_Warning_291s1_35F_S = ((data[5] & 0x38U) >> 3);
//    target.Target_State_B1_291s1_35F_S = ((data[5] & 0x80U) >> 7);
//    target.Target_Type_291s1_35F_S = (data[6] & 0xFU);
//    target.Message_Counter_35Fs1_S = ((data[6] & 0xF0U) >> 4);
//    target.Checksum_35Fs1_S = (data[7]);

    target.mValid = true;

    mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargetCount = 1; // 注意必须使用引用
    mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mValid = true;

    mAnalysisWorker->analysisEnd(radarID, Frame3Track);
    mAnalysisWorker->analysisLanePointFrameEnd( radarID, frame, true );

    return true;
}

bool BYD120TargetProtocol::byd3TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    quint8 *data = (quint8*)(frame.data().data());
    typedef struct
    {
        uint64_t adjacentLaneTarget     : 8;    /* 0x1: Left_Obj, 0x2: Rear_Obj, 0x3: Right_Obj */
        uint64_t lockTargetAbscissa     : 8;    // 锁定目标_横坐标
        uint64_t lockTargetOrdinate     : 8;    // 锁定目标_纵坐标
        uint64_t goalLengthRelativeSpd  : 8;    // 锁定目标_纵向相对速度
        uint64_t goalLateRelativeSpd    : 8;    // 锁定目标_横向相对速度
        uint64_t targetStateB6          : 2;    // 目标状态
        uint64_t resv1                  : 1;
        uint64_t targetStateB1          : 1;    // 目标状态
        uint64_t targetType             : 4;    // 目标类型
        uint64_t resv2                  : 4;
        uint64_t msgCounter             : 4;    // Message_counter36F
        uint64_t Checksum               : 8;    /* Checksum = (Byte1+Byte2+Byte3...+Byte7) XOR 0xFF */
    } CAN_msg_BYD_RCR36F_t;

    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargetCount; // 注意必须使用引用
    CAN_msg_BYD_RCR36F_t *p = (CAN_msg_BYD_RCR36F_t*)data;
    if (p->adjacentLaneTarget >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "BYD 3 Target error!" << p->adjacentLaneTarget << targetCount << " >= " << MAX_TARGET_COUNT;
        return true;
    }
    Target &target = mAnalysisWorker->mAnalysisDatas[radarID].mLockTargets.mTargets[targetCount];
    target.mID = p->adjacentLaneTarget;
    target.mX = p->lockTargetAbscissa - 100.0;
    target.mY = p->lockTargetOrdinate - 100.0;
    target.mValid = (p->lockTargetOrdinate != 0xFF/* && p->lockTargetOrdinate != 0x00*/);

    mAnalysisWorker->analysisEnd(radarID, Frame3Track);
    mAnalysisWorker->analysisLanePointFrameEnd( radarID, frame, true );

    return true;
}

bool BYD120TargetProtocol::byd16TargetClear(quint8 radarID)
{
    memset(mTargetsID[radarID], 0xFF, sizeof (mTargetsID[0]));
    mTargetsIndex_1[radarID] = 0;
    mTargetsIndex_2[radarID] = 0;
    mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.clear();

    return true;
}

bool BYD120TargetProtocol::byd16TargetParsePart_1(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![64]!" << frame.idHex() << frame.dataHex();
        return false;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << radarID << frame.idHex() << frame.dataHex();
    const uint8_t *data1 = (const uint8_t *)frame.data().data();

    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount; // 注意必须使用引用
    for (int i = 0; i < 4; ++i)
    {
        const uint8_t *data = data1 + i * 12;
        int id = (data[4]);
        if (id != 0xFF && id < MAX_TARGET_COUNT && targetCount < MAX_RADAR_COUNT && mTargetsIndex_1[radarID] < 16) {
            Target *target = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets + targetCount;
            mTargetsID[radarID][mTargetsIndex_1[radarID]] = (targetCount++);
            //    qDebug() << __FUNCTION__ << __LINE__ << mTargetsIndex_1[radarID] << mTargetsID[radarID][mTargetsIndex_1[radarID]] << p->ObjID;

            target->mID = (data[4]);
            target->mExistProbability = ((data[5] & 0x3FU) * 0.015625);
            target->mObstacleProbability = (((data[5] & 0xC0U) + (((uint16_t)data[6] & 0x7U) << 2)) * 0.03125);
            target->mY = (((((data[6] & 0xF8U) >> 3) + (((uint16_t)data[7] & 0x7FU) << 5)) * 0.0625) - 128);
            target->mX = (((((data[7] & 0x80U) >> 7) + (((uint32_t)data[8]) << 1) + (((uint32_t)data[9] & 0x1FU) << 9)) * 0.015625) - 128);
            target->mAy = (((((data[9] & 0xE0U) >> 5) + (((uint32_t)data[10]) << 3) + (((uint32_t)data[11] & 0x1U) << 11)) * 0.03125) - 16);
            target->mAx = (((((data[11] & 0xFEU) >> 1) + (((uint16_t)data[12] & 0x1FU) << 7)) * 0.03125) - 16);
            target->mVy = (((((data[12] & 0xE0U) >> 5) + (((uint32_t)data[13]) << 3) + (((uint32_t)data[14] & 0x1U) << 11)) * 0.0625) - 128);
            target->mVx = (((((data[14] & 0xFEU) >> 1) + (((uint16_t)data[15] & 0x1FU) << 7)) * 0.0625) - 128);
            target->mValid = true;
#if 0
            if (target->mValid)
            {
                qDebug() << __FUNCTION__ << __LINE__
                         << radarID
                         << target->mID
                            //                 << target->mExistProbability
                            //                 << p->ObjObstacleProb * 0.03125
                         << target->mX
                         << target->mY
                         << target->mAx
                         << target->mAy
                         << target->mVx
                         << target->mVy;
            }
#endif
        }
        mTargetsIndex_1[radarID]++;
    }

    mAnalysisWorker->analysisTargetPoint16FrameEnd( radarID, frame, false );

    return true;
}

bool BYD120TargetProtocol::byd16TargetParsePart_2(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }

    const uint8_t *data = (const uint8_t *)frame.data().data();

//    userData->FL_ObjID_Checksum_68A = (data[0]);
//    userData->FL_ObjID_MsgCounter_68A = ((data[1] & 0xFFU) + (((uint16_t)data[2] & 0xFFU) << 8));
//    userData->FL_ObjID_BlockCounter_68A = (data[3] & 0xFU);

//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();

    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount; // 注意必须使用引用
    for (int i = 0; i < 8; ++i) {
        quint8 id = mTargetsID[radarID][mTargetsIndex_2[radarID]];
        if ( id != 0xFF && id < MAX_TARGET_COUNT) {
            Target *target = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets + id;
            target->mRCS = ((((data[4 + i * 4] & 0xFFU) + (((uint16_t)data[5 + i * 4] & 0xFFU) << 8)) * 0.00390625) - 128);
            target->mMotionState = (data[6 + i * 4] & 0xFU);
            target->mMotionDirection = ((data[6 + i * 4] & 0xF0U) >> 4);
            target->mDetectionStatus = (data[7 + i * 4] & 0xFU);

            target->mTrackFrameWidth = ((data[36 + i * 3] & 0x7U) * 0.5);
            target->mTrackFrameLength = (((data[36 + i * 3] & 0xF8U) >> 3) * 0.5);
            target->mTrackFrameAngle = ((((data[37 + i * 3] & 0xFFU) + (((uint16_t)data[38] & 0x3U) << 8)) * 0.01) - 3.14);
            target->mObjectType = ((data[38 + i * 3] & 0x1CU) >> 2);
            target->mRefencePointPostion = ((data[38 + i * 3] & 0x60U) >> 5);

//            qDebug() << __FUNCTION__ << __LINE__ << mTargetsIndex_2[radarID] << target->mID << id
//                     << target->mRCS
//                     << target->mMotionState
//                     << target->mMotionDirection
//                     << target->mDetectionStatus
//                     << target->mTrackFrameWidth
//                     << target->mTrackFrameLength
//                     << target->mTrackFrameAngle
//                     << target->mObjectType
//                     << target->mRefencePointPostion;
        }
        mTargetsIndex_2[radarID]++;
    }

    mAnalysisWorker->analysisTargetPoint16FrameEnd( radarID, frame, false );

    return true;
}

bool BYD120TargetProtocol::byd16TargetParseFinished(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 8)
    {
        qDebug() << __FUNCTION__ << __LINE__ << "Data length error![8]!" << frame.idHex() << frame.dataHex();
        return false;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    const QByteArray &data = frame.data();
    Targets &targets = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets;
    targets.mTargetHeader.mMeasurementCount = (((quint16)(data[1] & 0xFF) << 8) + (data[2] & 0xFF));
    targets.mValid = true;

    mAnalysisWorker->analysisEnd(radarID, Frame16Track);
    mAnalysisWorker->analysisTargetPoint16FrameEnd( radarID, frame, true );
//    qDebug() << __FUNCTION__ << __LINE__ << "16 pares end" << radarID << frame.dataHex();
    return true;
}

} // namespace Protocol
} // namespace Analysis
