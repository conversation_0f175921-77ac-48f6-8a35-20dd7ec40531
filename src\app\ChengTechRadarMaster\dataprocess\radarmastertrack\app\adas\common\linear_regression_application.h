﻿/**
 * @file linear_regression_application.h
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2025-03-14
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-11-26 <td>1.0     <td>Will <PERSON>     <td>修改内容
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef __LINEAR_REGRESSION_APPLICATION_H__
#define __LINEAR_REGRESSION_APPLICATION_H__

#ifdef ALPSPRO_ADAS
#include "rdp/track/data_process/rdp_clth_radar_lib.h"
#include "rdp/track/data_process/rdp_interface.h"
#include "adas/customizedrequirements/adas.h"
#include "adas/customizedrequirements/adas_state_machine.h"
#include "adas/customizedrequirements/adas_standard_params.h"
#include "adas/customizedrequirements/adas_alg_params.h"
#include "adas/common/linear_regression.h" 
#include "adas/generalalg/adas_manager.h"
#include "vehicle_cfg.h"
#elif defined(PC_DBG_FW)
#include "app/system_mgr/typedefs.h"
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/common/linear_regression.h" 
#include "alg/track/rdp_clth_radar_lib.h"
#include "other/temp.h"
#else
#include "app/adas/customizedrequirements/adas_state_machine.h"
#include "app/adas/customizedrequirements/adas_alg_params.h"
#include "app/adas/generalalg/adas_manager.h"
#include "app/adas/common/linear_regression.h" 
#endif

/* 错误码定义 */
typedef enum
{
    ADAS_EVAL_LR_ERR = 0,    // 拟合异常
    ADAS_EVAL_OK,            // 正常
    ADAS_EVAL_NULL_PTR,      // 空指针错误
    ADAS_EVAL_INVALID_PARAM, // 无效参数
    ADAS_EVAL_CALC_ERROR,    // 计算错误
    ADAS_EVAL_MEM_ERROR      // 内存错误
} ADAS_LR_EvalStatus;
/* 场景类型定义 */
typedef enum
{
    ADAS_SCENE_INVALID = -1,   // 无效场景场景
    ADAS_SCENE_HIGHWAY = 0,    // 高速公路场景
    ADAS_SCENE_URBAN,          // 城市道路场景
    ADAS_SCENE_CURVE,          // 弯道场景
    ADAS_SCENE_COMPLEX,        // 复杂场景
    ADAS_SCENE_STRICT,         // 严格评估场景（高精度要求）
    ADAS_SCENE_LENIENT,        // 宽松评估场景（容忍更大误差）
    ADAS_SCENE_AUTO,           // 自动检测场景类型
    ADAS_SCENE_MAX             // 枚举最大值（用于边界检查）
} ADAS_SceneType;
/* 评估参数配置结构体 */
typedef struct
{
    float r2_weight;          // R²指标权重
    float mse_weight;         // MSE指标权重
    float var_weight;         // 方差指标权重
    float sigmoid_temp;       // Sigmoid温度参数
    float sigmoid_offset;     // Sigmoid偏移量
    float decision_threshold; // 决策阈值
} ADAS_LR_EvalConfig;
/* 评估结果结构体 */
typedef struct
{
    float r_squared;           // R²值
    float variance;            // 方差
    float total_mse;           // 总均方误差
    float mse_var;             // 总均方误差,方差

    float mse_threshold;       //
    float var_threshold;       //

    float normalized_mse;      // 归一化MSE
    float normalized_var;      // 归一化方差

    float r2_score;            // R²的归一化得分 [0,1] 
    float mse_score;           // MSE的归一化得分 [0,1] 
    float var_score;           // 方差的归一化得分 [0,1] 
    float weighted_score;      // 加权得分
    float control_output;      // Sigmoid控制输出
    float threshold;           // 动态阈值
    float confidence;          // 置信度

    uint8_t decision_satus;    // 最终状态

    uint8_t lr_eval_best_mask; // 每个模型得分有最优、优和差，只有其中得最优的才可以或上掩码 

    ADAS_SceneType scene_type; // 检测到的场景类型
    ADAS_LR_EvalStatus status; // 状态码
} ADAS_LR_EvaluationResult;

int8_t ADAS_FCTAB_LinearRegression_Flow(const ALARM_OBJECT_T *pobjAlm,
                                        OBJ_NODE_STRUCT *pobjPath,
                                        const uint8_t i,
                                        const VDY_Info_t *pVDY,
                                        const OBJ_ADAS_TYPE_ENUM type);
int8_t ADAS_DOW_LinearRegression_Flow(const ALARM_OBJECT_T *pobjAlm,
                                      OBJ_NODE_STRUCT *pobjPath,
                                      const uint8_t i,
                                      const VDY_Info_t *pVDY,
                                      const OBJ_ADAS_TYPE_ENUM type,
                                      const st_Dow_State dowState);

#endif





