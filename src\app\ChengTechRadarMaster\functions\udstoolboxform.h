#ifndef UDSTOOLBOXFORM_H
#define UDSTOOLBOXFORM_H

#include "devices/canframe.h"

#include <QWidget>
#include <QMap>
#include <QFile>
#include <QTimer>

namespace Ui {
class UDSToolBoxForm;
}

namespace Functions{
class UDS;
};

namespace Devices {
namespace Can {
class DeviceManager;
}
}

class UDSToolBoxForm : public QWidget
{
    Q_OBJECT

    enum RADAR_UDS_INDEX{
        RADAR_UDS_INDEX_LF = 0, //左前雷達
        RADAR_UDS_INDEX_RF, //右前雷達
        RADAR_UDS_INDEX_LR, //左后雷達
        RADAR_UDS_INDEX_RR, //右后雷達
        RADAR_UDS_INDEX_F, //前雷達
        RADAR_UDS_INDEX_COUNT //计数，此项需置于此枚举的最后
    };

    enum RADAR_PROJECT_TYPE{
        RADAR_PROJECT_BYD_120 = 0,
        RADAR_PROJECT_GWM_140PLUS,
        RADAR_PROJECT_COUNT //计数，此项需置于此枚举的最后
    };

public:
    explicit UDSToolBoxForm(Devices::Can::DeviceManager *deviceManager, QWidget *parent = nullptr);
    ~UDSToolBoxForm();

signals:
    void msg( const QString& msg, bool bError );
//    void wakeUpFail();

private slots:
    void canFrame(const Devices::Can::CanFrame &frame);
    void frameTransmited(const Devices::Can::CanFrame &frame, bool success);
    void on_pushButtonReadSoftVersion_clicked();
    void showFrame( const Devices::Can::CanFrame &frame, bool bSend );
    void showMsg( const QString& msg, bool bError );
    void setOtherPhysicalAndResponseAddress( quint32 physical, quint32 response );

    void on_pushButtonClearSoftVersion_clicked();

    void on_pushButtonReadSoftCode_clicked();

    void on_pushButtonClearSoftCode_clicked();

    void on_pushButtonClearMsg_clicked();

    void on_pushButtonReadDTC_clicked();

    void on_pushButtonReadCfgCode_clicked();

    void on_pushButtonWriteCfgCode_LF_clicked();

    void on_pushButtonWriteCfgCode_RR_clicked();

    void on_pushButtonWriteCfgCode_RF_clicked();

    void on_pushButtonWriteCfgCode_LR_clicked();

    void on_pushButtonWriteCfgCode_F_clicked();

    void on_pushButtonClearDTC_clicked();

    void on_pushButtonClearCfgCode_clicked();

    void on_pushButtonExitFactoryMode_clicked();

    void on_comboBoxChannel_currentIndexChanged(int index);

    void on_pushButtonClearAngle_clicked();

    void on_pushButtonAngleCode_clicked();

    void on_checkBoxCycleReadDTC_stateChanged(int arg1);

//    void on_pushButtonWakeUpStart_clicked();

//    void on_pushButtonWakeUpStop_clicked();

    void deviceOpened();
    void deviceClosed();

    void on_comboBoxProject_currentIndexChanged(int index);

private:
    void initUDS();
    void initSendUDS();
    void initDTCText();
    void readSoftVersion( quint32 udsIndex ); //读取软件版本
    void readSoftCode( quint32 udsIndex ); //读取软件编码
    void readAngle( quint32 udsIndex );//读取安装角度
    void readDTC( quint32 udsIndex ); //读取DTC
    void clearDTC(quint32 udsIndex); //清除DTC
    void readCfgCode( quint32 udsIndex ); //读取配置字
    void factoryMode( quint32 udsIndex, bool bEnter ); //进入/退出工厂模式
    bool securityVerification( quint32 ); //安全验证
    void writeCfgCode( quint32 udsIndex, const QString& code );
    quint32 getDID( const QByteArray& data );
//    void warkUpTest( quint8 udsIndex, quint64 timeOut, quint64 timeInterval );
//    bool warkUpTest( quint8 udsIndex, quint64 timeOut );


    void showSoftVersion( quint32 udsIndex, const QString& version );
    void showSoftCode( quint32 udsIndex, const QString& code );
    void showRadarMsg( quint32 udsIndex, const QString& dtcMsg, bool bError );
    void showCfgCode( quint32 udsIndex, const QString& cfgCode );
    void showAngle( quint32 udsIndex, const QString& angle );

    QString getDTCText( quint32 dtc );
private:
    Ui::UDSToolBoxForm *ui;

    quint32 mResponseAddress[RADAR_PROJECT_COUNT][RADAR_UDS_INDEX_COUNT]{
        //{左前，右前，左后，右后，前雷达}
        {0x768,0x769,0x7dc,0x7ca,0x7fa},
        {0xFF,0xFF,0x7AE,0x7AF,0x7FF}
    };
    quint32 mPhysicalAddress[RADAR_PROJECT_COUNT][RADAR_UDS_INDEX_COUNT]{
        //{左前，右前，左后，右后，前雷达}
        {0x760,0x761,0x7d4,0x7c2,0x7f2},
        {0xFF,0xFF,0x76E,0x76F,0xFF},
    };
    uint32_t mMASK[RADAR_PROJECT_COUNT][RADAR_UDS_INDEX_COUNT]{
        {0x1A1, 0x1A2, 0x156, 0x156, 0x94},
        {0xFF, 0xFF, 0xFF, 0xFF, 0xFF},
    };
    Functions::UDS* mUDS[RADAR_UDS_INDEX_COUNT];
    Devices::Can::DeviceManager *mDeviceManager{NULL};
    QMap< quint32, QString > mDTCText;

    quint32 mOtherResponseAddress{0};
    quint32 mOtherPhysicalAddress{0};

    QFile mMsgLog;
    QFile mFrameLog;
    QTimer mTimer;

//    bool mWarkUpTesting{false};
//    bool mWarkUpTestResult{false};

    RADAR_PROJECT_TYPE mRadarProjectType{RADAR_PROJECT_BYD_120};
};

#endif // UDSTOOLBOXFORM_H
