﻿#include "playbackcanwoker.h"

#include "CTMRR410.h"

#ifdef ALGORITHM_DEBUG
#include "dataprocess/dataprocess.h"
#endif

#include <QDir>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonObject>
#include <QThread>
#include <QDebug>

bool ProjectData::parseCSVFile(const QString &filename)
{
    mCanDataFiles.clear();
    mTargetsFiles.clear();

    int index = filename.contains("raw", Qt::CaseInsensitive) ? FrameRawTarget : FrameTrackTarget;
    TargetsFile targets;
    targets.mTargetsFile[index].mFileName = filename;
    mTargetsFiles << targets;

    return true;
}

bool ProjectData::parseProjectFile(const QJsonObject &jsonObject, const QString &projectPath)
{
    mCanDataFiles.clear();
    mTargetsFiles.clear();

    mProjectDateTime = QDateTime::fromString(jsonObject["ProjectTime"].toString(), "yyyy-MM-dd hh:mm:ss.zzz");

    QJsonArray targetFiles = jsonObject["CanDataFiles"].toArray();
    foreach (const QJsonValue &value, targetFiles)
    {
        CanDataFile canData;
        QJsonObject obj = value.toObject();
        canData.mFileName = QDir(projectPath).absoluteFilePath(obj["FileName"].toString());
        canData.mSaveCount = obj["SaveCount"].toInt();

        qDebug() << __FUNCTION__ << __LINE__ << canData.mFileName << canData.mSaveCount;

        mCanDataFiles << canData;
    }
    QJsonArray canDateFiles = jsonObject["TargetFiles"].toArray();
    foreach (const QJsonValue &value, canDateFiles)
    {
        TargetsFile targets;
        QJsonObject obj = value.toObject();
        foreach (const QString &frameType, obj.keys())
        {
            QJsonObject rawObject = obj[frameType].toObject();
            int i = frameType.toInt();
            targets.mTargetsFile[i].mFileName  = QDir(projectPath).absoluteFilePath(rawObject["FileName"].toString());
            QJsonObject target = rawObject["SaveCount"].toObject();
            foreach (const QString &id, target.keys())
            {
                targets.mTargetsFile[i].mSaveCount[id.toUInt()]  = target[id].toInt();
            }
            qDebug() << __FUNCTION__ << __LINE__ << targets.mTargetsFile[i].mFileName << targets.mTargetsFile[i].mSaveCount;
        }

        mTargetsFiles << targets;
    }

    return true;
}

PlaybackCanWoker::PlaybackCanWoker(Devices::Can::DeviceManager *deviceManager, QObject *parent)
    : QObject(parent),
      mDeviceManager(deviceManager)
{

}

bool PlaybackCanWoker::parseCSVFile(const QString &filename)
{
    if (!mProjectData.parseCSVFile(filename))
    {
        return false;
    }

    return true;
}

bool PlaybackCanWoker::parseProjectFile(const QString &filename)
{
    mPlaybackProjectFile = filename;
    mPlaybackProjectPath = QFileInfo(mPlaybackProjectFile).absolutePath();

    QFile jsonFile(mPlaybackProjectFile);
    if(!jsonFile.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        return false;
    }
    QString value = jsonFile.readAll();
    QJsonParseError error;
    QJsonDocument document = QJsonDocument::fromJson(value.toUtf8(),&error);
    jsonFile.close();

    if(error.error != QJsonParseError::NoError)
    {
        return false;
    }

    QJsonObject jsonObject = document.object();

    if (!mProjectData.parseProjectFile(jsonObject, mPlaybackProjectPath))
    {
        return false;
    }

    return true;
}

bool PlaybackCanWoker::parseFile(AnalysisFrameType frameType, const QString &filename)
{
    mPlaybackFrameType = frameType;
    if (QFileInfo(filename).suffix() == "prj") {
        if (!parseProjectFile(filename)) {
            return false;
        }
    } else {
        if (!parseCSVFile(filename)) {
                return false;
        }
    }

    return loadData();
}

int PlaybackCanWoker::loadData()
{
    if (!mProjectData.mTargetsFiles.size())
    {
        qDebug() << __FUNCTION__ << __LINE__ << "message";
        return 0;
    }

    return loadTargetData(0);
}

void PlaybackCanWoker::initDataProcess()
{
#ifdef ALGORITHM_DEBUG
    DataProcess::instance()->init();
#endif
}

void PlaybackCanWoker::start(int startFrameIndex, bool recharge, bool single, bool algorithm, int algorithmRadarID)
{
    mRecharge = recharge;
    mSinglePlay = single;
    mAlgorithm = algorithm;
    mAlgorithmRadarID = algorithmRadarID;
    mPlayIndex = startFrameIndex - 2;
    qDebug() << __FUNCTION__ << __LINE__ << mPlaybackFrameType << mRecharge << mSinglePlay << mPlayIndex;

    if (!mPlayTimer)
    {
        mPlayTimer = new QTimer;
        mPlayTimer->setSingleShot(true);
        connect(mPlayTimer, &QTimer::timeout, this, &PlaybackCanWoker::nextPlay);
    }

    emit playStarted();
    mPlayTimer->start(50);
}

void PlaybackCanWoker::stop()
{
    if (mPlayTimer)
    {
        mPlayTimer->stop();
    }
    mPlayIndex = -1;
    emit playFinished();
}

void PlaybackCanWoker::pause()
{
    mPlayTimer->stop();
}

void PlaybackCanWoker::playNext()
{
    qDebug() << __FUNCTION__ << __LINE__;
    int index = mPlayIndex + 1;
    if (index < 0 || index >= mPlaybackRadarTargets[mPlaybackFrameType].size())
    {
        stop();
        return;
    }

//    mSinglePlay = true;
    play(index);
}

void PlaybackCanWoker::playPrev()
{
    int index = mPlayIndex - 1;
    if (index < 0 || index >= mPlaybackRadarTargets[mPlaybackFrameType].size())
    {
        return;
    }

//    mSinglePlay = true;
    play(index);
}

void PlaybackCanWoker::play(int index)
{
    if (!mDeviceManager)
    {
        stop();
        return;
    }

    if (index < 0 || index >= mPlaybackRadarTargets[mPlaybackFrameType].size())
    {
        stop();
        return;
    }
//    qDebug() << __FUNCTION__ << __LINE__ << index << mSinglePlay;

    if (!mPlayTimer)
    {
        start(1, true);
        return;
    }

    mPlayIndex = index;

//    mPlaybackRadarTargets[mPlaybackFrameType][mPlayIndex]->print();

    QMapIterator<quint16, PlaybackTarget*> i(mPlaybackRadarTargets[mPlaybackFrameType][mPlayIndex]->mRadarTargets);

    while (i.hasNext()) {
        AnalysisData analysisData;
        Targets &targets = analysisData.mTargets[mPlaybackFrameType];
        i.next();
        PlaybackTarget* playbackTargets = i.value();

        if (mRecharge)
        {
            // 发送车辆信息
            mDeviceManager->sendFrame(mChannelIndex, 0x3F0 | playbackTargets->mRadarID,
                                    encodingVehicleData(playbackTargets->mVehicleData));

            if (mPlaybackFrameType == FrameRawTarget) {
                // 发送原始目标Header信息
                mDeviceManager->sendFrame(mChannelIndex, 0x400 | playbackTargets->mRadarID,
                                          encodingRawObjectHeaderData(playbackTargets));
            } else {
                // 发送跟踪目标Header信息
                mDeviceManager->sendFrame(mChannelIndex, 0x430 | playbackTargets->mRadarID,
                                          encodingTrackObjectHeaderData(playbackTargets));
            }
        } else {
            analysisData.mRadarID = playbackTargets->mRadarID;
            targets.mTargetHeader = playbackTargets->mTargetHeader;
            memcpy(analysisData.mTrueTarget, playbackTargets->mTrueTarget, sizeof (playbackTargets->mTrueTarget));                         // 真值目标点
            analysisData.mAlarmData = playbackTargets->mAlarmData;                       // 告警信息
            analysisData.mVehicleData = playbackTargets->mVehicleData;                   // 汽车数据
            analysisData.mEndFrameData = playbackTargets->mEndFrameData;
        }

        for (int j = 0; j < playbackTargets->mTargets.size(); ) {
            if (mRecharge) {
                if (mPlaybackFrameType == FrameRawTarget) {
                    // 发送原始目标信息
                    mDeviceManager->sendFrame(mChannelIndex, 0x410 | playbackTargets->mRadarID,
                                              encodingRawObjectData(playbackTargets->mTargets,
                                                                    j,
                                                                    j + 4 <= playbackTargets->mTargets.size() ? 4 : playbackTargets->mTargets.size() % 4));
                    j += 4;
                } else {
                    // 发送跟踪目标信息
                    mDeviceManager->sendFrame(mChannelIndex, 0x440 | playbackTargets->mRadarID,
                                              encodingTrackObjectData(playbackTargets->mTargets,
                                                                      j,
                                                                      1));
                    ++j;
                }
            } else {
                targets.mValid = true;
                targets.mTargets[targets.mTargetCount] = playbackTargets->mTargets[j];
                targets.mTargets[targets.mTargetCount].mValid = true;
                targets.mTargetCount++;

                ++j;
            }
        }

        if (mRecharge)
        {
            // 发送结束帧
            mDeviceManager->sendFrame(mChannelIndex, 0x4F0 | playbackTargets->mRadarID,
                                    encodingEndFrameData(playbackTargets));
        } else {
#ifdef ALGORITHM_DEBUG
            // 需要走算法
            if (mPlaybackFrameType == FrameRawTarget && mAlgorithm && playbackTargets->mRadarID == mAlgorithmRadarID)
            {
                if (!DataProcess::instance()->process(analysisData))
                {
                    qDebug() << __FUNCTION__ << __LINE__ << "data process error!";
                }
            }
#endif
            emit analysisRadarData(i.key(), analysisData);
        }
    }

    emit playIndex(mPlayIndex, mFrameTotal, mPlaybackRadarTargets[mPlaybackFrameType][mPlayIndex]->mFirstFrameTime);

    if (!mSinglePlay)
    {
        mPlayTimer->start(50);
    }
}

void PlaybackCanWoker::nextPlay()
{
    int index = mPlayIndex + 1;
    if (index < 0 || index >= mPlaybackRadarTargets[mPlaybackFrameType].size())
    {
        qDebug() << __FUNCTION__ << __LINE__ << "message";
        stop();
        return;
    }

    play(index);
}

PlaybackCanWoker::LineType PlaybackCanWoker::lineType(const QString &line)
{
    for (unsigned int i = 0; i < TargetValue/* && i < sizeof(mHeaderString)*/; ++i)
    {
        if (line.startsWith(mHeaderString[i]))
        {
            return (LineType)i;
        }
    }

    QRegExp re("^[0-9]+");
    if (re.indexIn(line) != -1)
    {
//        qDebug() << __FUNCTION__ << __LINE__ << line;
        return TargetValue;
    }

    return Unknow;
}

int PlaybackCanWoker::loadTargetData(int index)
{
    mPlayIndex = 0;
    mSinglePlay = true;
    if (index < 0 || index > mProjectData.mTargetsFiles.size())
    {
        return 0;
    }

    for (int i = 0; i < FrameTargetCount; ++i)
    {
        qDeleteAll(mPlaybackRadarTargets[i]);
        mPlaybackRadarTargets[i].clear();

        if (mProjectData.mTargetsFiles[index].mTargetsFile[i].mFileName.isEmpty())
        {
            continue;
        }

        loadTarget((AnalysisFrameType)i, mProjectData.mTargetsFiles[index].mTargetsFile[i].mFileName);
    }

    mFrameTotal = mPlaybackRadarTargets[mPlaybackFrameType].size();
    emit playFrameTotal(mFrameTotal);
    return mFrameTotal;
}

bool PlaybackCanWoker::parseTargetHeader(const QStringList &headers)
{
//    qDebug() << __FUNCTION__ << __LINE__ << headers;

    if (headers[0] == "VehicleHeader") {
        mVehicleAnalysisTypes.clear();
        for (int i = 0; i < headers.size(); ++i) {
            mVehicleAnalysisTypes << gAnalysisType(headers[i]);
        }
    } else if (headers[0] == "AlarmHeader") {
        mAlarmAnalysisTypes.clear();
        for (int i = 0; i < headers.size(); ++i) {
            mAlarmAnalysisTypes << gAnalysisType(headers[i]);
        }
    } else if (headers[0] == "EndFrameHeader") {
        mEndFrameAnalysisTypes.clear();
        for (int i = 0; i < headers.size(); ++i) {
            mEndFrameAnalysisTypes << gAnalysisType(headers[i]);
        }
    } else if (headers[0] == "TargetHeadHeader") {
        mTargetHeadAnalysisTypes.clear();
        for (int i = 0; i < headers.size(); ++i) {
            mTargetHeadAnalysisTypes << gAnalysisType(headers[i]);
        }
    } else if (headers[0] == "ID") {
        mTargetAnalysisTypes.clear();
        for (int i = 0; i < headers.size(); ++i) {
            mTargetAnalysisTypes << gAnalysisType(headers[i]);
        }
    }

    return true;
}

bool PlaybackCanWoker::loadTarget(AnalysisFrameType frameType, const QString &filename)
{
    mPlaybackFile[frameType].setFileName(filename);
    if (!mPlaybackFile[frameType].open(QIODevice::ReadOnly | QIODevice::Text))
    {
        return false;
    }

    qDebug() << "lxw3" << QTime::currentTime();
    while (!mPlaybackFile[frameType].atEnd())
    {
        QStringList headers = QString(mPlaybackFile[frameType].readLine()).trimmed().split(",", Qt::SkipEmptyParts);
        if (!headers.size())
        {
            continue;
        }
        if (headers[0] == "Begin")
        {
            mTargetBegin = true;
            break;
        }

        parseTargetHeader(headers);
    }

    qDebug() << "lxw3" << QTime::currentTime();

    while (!mPlaybackFile[frameType].atEnd())
    {
        if (!parseLine(mPlaybackFile[frameType].readLine().trimmed(), frameType))
        {
            break;
        }
    }

    qDebug() << "lxw3" << QTime::currentTime();
    mPlaybackFile[frameType].close();
    return true;
}

bool PlaybackCanWoker::parseLine(const QString &line, AnalysisFrameType frameType)
{
//    qDebug() << __FUNCTION__ << __LINE__ << line << frameType;
    LineType type = lineType(line);
//    qDebug() << __FUNCTION__ << __LINE__ << type;
    switch (type)
    {
    case Begin:
        mTargetBegin = true;
        break;
    case RadarID:
    {
        mCurrentPlaybackTarget = new PlaybackTarget;
        mCurrentPlaybackTarget->mRadarID = line.split(",", Qt::SkipEmptyParts)[1].toUInt();
    }
        break;
    case RadarFrameNumber:
        if (mCurrentPlaybackTarget)
        {
            mCurrentPlaybackTarget->mRadarFrameNumber = line.split(",", Qt::SkipEmptyParts)[1].toUInt();
        }
        break;
    case SaveFrameNumber:
        if (mCurrentPlaybackTarget)
        {
            mCurrentPlaybackTarget->mSaveFrameNumber = line.split(",", Qt::SkipEmptyParts)[1].toUInt();
        }
        break;
    case FrameTime:
        if (mCurrentPlaybackTarget)
        {
            mCurrentPlaybackTarget->mFrameTime = QDateTime::fromString(line.split(",", Qt::SkipEmptyParts)[1], "yyyy-MM-dd hh:mm:ss.zzz");
        }
        break;
    case SystemFrameTime:
        if (mCurrentPlaybackTarget)
        {
            mCurrentPlaybackTarget->mSystemFrameTime = QDateTime::fromString(line.split(",", Qt::SkipEmptyParts)[1], "yyyy-MM-dd hh:mm:ss.zzz");
        }
        break;
    case SaveTime:
        if (mCurrentPlaybackTarget)
        {
            mCurrentPlaybackTarget->mSaveTime = QDateTime::fromString(line.split(",", Qt::SkipEmptyParts)[1], "yyyy-MM-dd hh:mm:ss.zzz");
        }
        break;
    case TargetValue:
    {
        if (mCurrentPlaybackTarget)
        {
            QStringList values = line.split(",", Qt::SkipEmptyParts);
            if (values.size() != mTargetAnalysisTypes.size())
            {
//                qDebug() << __FUNCTION__ << __LINE__ << values.size() << mTargetAnalysisTypes.size();
                return false;
            }
            Target target;
            for (int i = 0; i < values.size() && i < mTargetAnalysisTypes.size(); ++i)
            {
                target.setValue(mTargetAnalysisTypes[i], values[i].toDouble());
                target.mValid = true;
            }
            mCurrentPlaybackTarget->mTargets << target;
        }
    }
        break;
    case VehicleDataLine:
        if (mCurrentPlaybackTarget)
        {
            QStringList values = line.split(",", Qt::SkipEmptyParts);
            for (int i = 1; i < values.size() && i < mVehicleAnalysisTypes.size(); ++i)
            {
                mCurrentPlaybackTarget->mVehicleData.setValue(mVehicleAnalysisTypes[i], values[i].toDouble());
            }
        }
        break;
    case AlarmDataLine:
        if (mCurrentPlaybackTarget)
        {
            QStringList values = line.split(",", Qt::SkipEmptyParts);
//            qDebug() << __FUNCTION__ << __LINE__ << mAlarmAnalysisTypes;
            for (int i = 1; i < values.size() && i < mAlarmAnalysisTypes.size(); ++i)
            {
                mCurrentPlaybackTarget->mAlarmData.setValue(mAlarmAnalysisTypes[i], values[i].toDouble());
            }

//            qDebug() << __FUNCTION__ << __LINE__ << mAlarmAnalysisTypes << values;
        }
        break;
    case EndFrameDataLine:
        if (mCurrentPlaybackTarget)
        {
            QStringList values = line.split(",", Qt::SkipEmptyParts);
            for (int i = 1; i < values.size() && i < mEndFrameAnalysisTypes.size(); ++i)
            {
                mCurrentPlaybackTarget->mEndFrameData.setValue(mEndFrameAnalysisTypes[i], values[i].toDouble());
            }
        }
        break;
    case TargetHeadDataLine:
        if (mCurrentPlaybackTarget)
        {
            QStringList values = line.split(",", Qt::SkipEmptyParts);
            for (int i = 1; i < values.size() && i <= mTargetHeadAnalysisTypes.size(); ++i)
            {
                mCurrentPlaybackTarget->mTargetHeader.setValue(mTargetHeadAnalysisTypes[i], values[i].toDouble());
            }
//            qDebug() << __FUNCTION__ << __LINE__ << mTargetHeadAnalysisTypes << values << mCurrentPlaybackTarget->mTargetHeader.mMeasurementCount;
        }
        break;
    case TargetBegin:
        break;
    case TargetEnd:
        if (mCurrentPlaybackTarget)
        {
            mCurrentPlaybackTarget->mTotal = line.split(",", Qt::SkipEmptyParts)[2].toUInt();
        }
        break;
    case End:
        if (mCurrentPlaybackTarget)
        {
            addTarget(mCurrentPlaybackTarget, frameType);
        }
        mCurrentPlaybackTarget = 0;
        mTargetBegin = false;
        break;
    case Unknow:
        break;
    default:
        break;
    }

    return true;
}

void PlaybackCanWoker::addTarget(PlaybackTarget *target, AnalysisFrameType frameType)
{
    PlaybackRadarTarget *radarTarget = 0;
    if (!mPlaybackRadarTargets[frameType].size())
    {
        radarTarget = new PlaybackRadarTarget;
        mPlaybackRadarTargets[frameType] << radarTarget;

        radarTarget->mFirstFrameTime = target->mSystemFrameTime;
        radarTarget->mRadarTargets[target->mRadarID] = target;

        return;
    }

    radarTarget = mPlaybackRadarTargets[frameType].last();

    QMap<quint16, PlaybackTarget*>::iterator it = radarTarget->mRadarTargets.find(target->mRadarID);
    if (it != radarTarget->mRadarTargets.end())
    {
        radarTarget = new PlaybackRadarTarget;
        mPlaybackRadarTargets[frameType] << radarTarget;

        radarTarget->mFirstFrameTime = target->mSystemFrameTime;
        radarTarget->mRadarTargets[target->mRadarID] = target;

        return;
    }

    // 时间差大于75毫秒
    if (radarTarget->mFirstFrameTime.msecsTo(target->mSystemFrameTime) > 75)
    {
        radarTarget = new PlaybackRadarTarget;
        mPlaybackRadarTargets[frameType] << radarTarget;

        radarTarget->mFirstFrameTime = target->mSystemFrameTime;
        radarTarget->mRadarTargets[target->mRadarID] = target;

        return;
    }

    radarTarget->mRadarTargets[target->mRadarID] = target;
}

QByteArray PlaybackCanWoker::encodingVehicleData(const VehicleData &vehicleData)
{
    QByteArray data(32, 0);
    SRR_VehicleInfo_t *p = (SRR_VehicleInfo_t *)data.data();
    p->Veh_YawRate = (vehicleData.mYawRate + 2.093) / 0.00024;
    p->Veh_SwRctaFunc = vehicleData.mSwitchRCTAFunction;
    p->Veh_SwRctbFunc = vehicleData.mSwitchRCTBFunction;
    p->Veh_SwRcwFunc = vehicleData.mSwitchRCWFunction;
    p->Veh_SwBSDFunc = vehicleData.mSwitchBSDFunction;
    p->Veh_SwDowFunc = vehicleData.mSwitchDOWFunction;
    p->Veh_SwMainFunc = vehicleData.mSwitchMainFunction;
    p->Veh_SwFctaFunc = vehicleData.mSwitchFCTAFunction;
    p->Veh_SwFctbFunc = vehicleData.mSwitchFCTBFunction;
    p->Veh_Speed = vehicleData.mVehicleSpeed / 0.05625;
    p->Veh_DoorFrontLe = vehicleData.mDoorFrontLeft;
    p->Veh_DoorFrontRi = vehicleData.mDoorFrontRight;
    p->Veh_DoorRearLe = vehicleData.mDoorRearLeft;
    p->Veh_DoorRearRi = vehicleData.mDoorRearRight;
    p->Veh_Gear = vehicleData.mGear;
    p->Veh_KeyState = vehicleData.mKeyStatus;
    p->Veh_SecurityLock = vehicleData.mSecurityLock;
    p->Veh_TurnLightLe = vehicleData.mTurnLightLeft;
    p->Veh_TurnLightRi = vehicleData.mTurnLightRight;
    p->Veh_BrkPedalSts = vehicleData.mBrakePedalStatus;
    p->Veh_WhlSpdDirFrontLe = vehicleData.mWheelSpeedDirectionFontLeft;
    p->Veh_WhlSpdDirFrontRi = vehicleData.mWheelSpeedDirectionFontRight;
    p->Veh_WhlSpdDirRearLe = vehicleData.mWheelSpeedDirectionRearLeft;
    p->Veh_WhlSpdDirRearRi = vehicleData.mWheelSpeedDirectionRearRight;
    p->Veh_AccPedalActLevel = vehicleData.mAcceleratorPedalActiveLevel;
    p->Veh_BrkPedalActLevel = vehicleData.mBrakePedalActiveLevel;
    p->Veh_TrailerSts = vehicleData.mTrailerStatus;
    p->Veh_ESPFailSts = vehicleData.mESPFailStatus;
    p->Veh_LgtAccel = (vehicleData.mLongitudinalAcceleration + 21.592) / 0.00098;
    p->Veh_LatAccel = (vehicleData.mLateralAcceleration + 21.592) / 0.00098;
    p->Veh_WhlSpdFrontLe = vehicleData.mWheelSpeedFontLeft / 0.05625;
    p->Veh_WhlSpdFrontRi = vehicleData.mWheelSpeedFontRight / 0.05625;
    p->Veh_WhlSpdRearLe = vehicleData.mWheelSpeedRearLeft / 0.05625;
    p->Veh_WhlSpdRearRi = vehicleData.mWheelSpeedRearRight / 0.05625;
    p->Veh_SteerWheelAngle = (vehicleData.mSteeringWheelAngle + 738) / 0.2;
    p->RollingCntMsgVehInfo01 = vehicleData.mVehicleRollingCount;
    p->Veh_Radius = (vehicleData.mRadius + 32767) / 0.25;
    p->ChecksumMsgVehInfo01 = vehicleData.mVehicleChecksum;

    std::reverse(data.begin(), data.end());
    return data;
}

QByteArray PlaybackCanWoker::encodingRawObjectHeaderData(const PlaybackTarget * const playbackData)
{
    QByteArray data(16, 0);
    SRR_RawHeader_t *p = (SRR_RawHeader_t*)data.data();
    const TargetHeader &targetHeader = playbackData->mTargetHeader;

    p->RawHeaderProtVer = targetHeader.mProtocolVersion;
    p->RawHeaderNoiseCurrent = (targetHeader.mNoiseCurrent + 100) / 0.5;
    p->RawHeaderNoiseGlobal = (targetHeader.mNoiseGlobal + 100) / 0.5;
    p->RawHeaderObjNum = playbackData->mTargets.size();
    p->RawHeaderFuncCalcTime = targetHeader.mFunctionCalculationTime;
    p->RawHeaderRspTaskCycleTime = targetHeader.mResponseTaskCycleTime;
    p->RawHeaderDataSource = targetHeader.mDataSource;
    p->RawHeaderObjNumAfterCFAR = targetHeader.mNumberAfterCFAR;
    p->RawHeaderObjNumAfterFilter = targetHeader.mNumberAfterFilter;
    p->RawHeaderCurrentFrameModeIdx = targetHeader.mCurrentFrameModeIndex;
    p->RawHeaderFrameModeNum = targetHeader.mFrameModeNumber;
    p->RawHeaderRollingCnt = targetHeader.mTargetRollingCount;

    std::reverse(data.begin(), data.end());
    return data;
}

QByteArray PlaybackCanWoker::encodingRawObjectData(const QList<Target> targets, int index, int count)
{
    QByteArray data(64, 0);
    SRR_RawDetections_t *p = (SRR_RawDetections_t*)data.data();

    p->RawObjectRange01 = targets[index].mRange / 0.01;
    p->RawObjectAzimuth01 = (targets[index].mAngle + 163.84) / 0.01;
    p->RawObjectVelocity01 = (targets[index].mV + 81.92) / 0.01;
    p->RawObjectElevationAngle01 = (targets[index].mPitchAngle + 81.92) / 0.02;
    p->RawObjectProbOfExist01 = targets[index].mExistProbability;
    p->RawObjectRCS01 = (targets[index].mRCS + 30) / 0.5;
    p->RawObjectSNR01 = targets[index].mSNR / 0.5;
    p->RawObjectStatus01 = targets[index].mStatus;
    p->RawObjectDopplerVelocity01 = targets[index].mDopplerVelocity;
    p->RawObjectSubFrameID01 = targets[index].mSubframeID;
    p->RawObjectAssociatedTrkId01 = targets[index].mAssociatedTrackID;
    p->RawObjectGroupID01 = targets[index].mGroupID;

    if (count > 1) {
        index++;
        p->RawObjectRange02 = targets[index].mRange / 0.01;
        p->RawObjectAzimuth02 = (targets[index].mAngle + 163.84) / 0.01;
        p->RawObjectVelocity02 = (targets[index].mV + 81.92) / 0.01;
        p->RawObjectElevationAngle02 = (targets[index].mPitchAngle + 81.92) / 0.02;
        p->RawObjectProbOfExist02 = targets[index].mExistProbability;
        p->RawObjectRCS02 = targets[index].mRCS / 0.5;
        p->RawObjectSNR02 = targets[index].mSNR / 0.5;
        p->RawObjectStatus02 = targets[index].mStatus;
        p->RawObjectDopplerVelocity02 = targets[index].mDopplerVelocity;
        p->RawObjectSubFrameID02 = targets[index].mSubframeID;
        p->RawObjectAssociatedTrkId02 = targets[index].mAssociatedTrackID;
        p->RawObjectGroupID02 = targets[index].mGroupID;

        if (count > 2) {
            index++;
            p->RawObjectRange03 = targets[index].mRange / 0.01;
            p->RawObjectAzimuth03 = (targets[index].mAngle + 163.84) / 0.01;
            p->RawObjectVelocity03 = (targets[index].mV + 81.92) / 0.01;
            p->RawObjectElevationAngle03 = (targets[index].mPitchAngle + 81.92) / 0.02;
            p->RawObjectProbOfExist03 = targets[index].mExistProbability;
            p->RawObjectRCS03 = targets[index].mRCS / 0.5;
            p->RawObjectSNR03 = targets[index].mSNR / 0.5;
            p->RawObjectStatus03 = targets[index].mStatus;
            p->RawObjectDopplerVelocity03 = targets[index].mDopplerVelocity;
            p->RawObjectSubFrameID03 = targets[index].mSubframeID;
            p->RawObjectAssociatedTrkId03 = targets[index].mAssociatedTrackID;
            p->RawObjectGroupID03 = targets[index].mGroupID;

            if (count > 3) {
                index++;
                p->RawObjectRange04 = targets[index].mRange / 0.01;
                p->RawObjectAzimuth04 = (targets[index].mAngle + 163.84) / 0.01;
                p->RawObjectVelocity04 = (targets[index].mV + 81.92) / 0.01;
                p->RawObjectElevationAngle04 = (targets[index].mPitchAngle + 81.92) / 0.02;
                p->RawObjectProbOfExist04 = targets[index].mExistProbability;
                p->RawObjectRCS04 = targets[index].mRCS / 0.5;
                p->RawObjectSNR04 = targets[index].mSNR / 0.5;
                p->RawObjectStatus04 = targets[index].mStatus;
                p->RawObjectDopplerVelocity04 = targets[index].mDopplerVelocity;
                p->RawObjectSubFrameID04 = targets[index].mSubframeID;
                p->RawObjectAssociatedTrkId04 = targets[index].mAssociatedTrackID;
                p->RawObjectGroupID04 = targets[index].mGroupID;
            }
        }
    }

    p->RawObjectRollingCnt01 = targets[index].mRollingCount;

    std::reverse(data.begin(), data.end());
    return data;
}

QByteArray PlaybackCanWoker::encodingTrackObjectHeaderData(const PlaybackTarget * const playbackData)
{
    QByteArray data(8, 0);
    SRR_ObjectHeader_t *p = (SRR_ObjectHeader_t*)data.data();

    p->ObjHeaderChecksum = playbackData->mTargetHeader.mTargetChecksum;
    p->ObjHeaderObjNum = playbackData->mTargets.size();
    p->ObjHeaderMeasCnt = playbackData->mTargetHeader.mMeasurementCount;
    p->TrkHeaderFuncCalcTime = playbackData->mTargetHeader.mFunctionCalculationTime;
    p->TrkHeaderTaskCycleTime = playbackData->mTargetHeader.mResponseTaskCycleTime;
    p->ObjHeaderProtVer = playbackData->mTargetHeader.mProtocolVersion;
    p->ObjHeaderRollingCnt = playbackData->mTargetHeader.mTargetRollingCount;

    std::reverse(data.begin(), data.end());
    return data;
}

QByteArray PlaybackCanWoker::encodingTrackObjectData(const QList<Target> targets, int index, int count)
{
    QByteArray data(24, 0);
    SRR_ObjectList_t *p = (SRR_ObjectList_t*)data.data();

    p->ObjectChecksum = targets[index].mChecksum;
    p->ObjectID = targets[index].mID;
    p->ObjectDistLong = (targets[index].mY + 409.6) / 0.05;
    p->ObjectDistLat = (targets[index].mX + 102.4) / 0.05;
    p->ObjectVrelLong = (targets[index].mVy + 163.84) / 0.04;
    p->ObjectClass = targets[index].mClass;
    p->ObjectDistLongRms = targets[index].mYRms / 0.375;
    p->ObjectVrelLat = (targets[index].mVx + 163.84) / 0.04;
    p->ObjectArelLong = (targets[index].mAy + 10.24) / 0.01;
    p->ObjectArelLat = (targets[index].mAx + 10.24) / 0.01;
    p->ObjectLength = targets[index].mTrackFrameLength / 0.2;
    p->ObjectWidth = targets[index].mTrackFrameWidth / 0.2;
    p->ObjectMeasState = targets[index].mMeasureState;
    p->ObjectRCS = (targets[index].mRCS + 128) / 0.5;
    p->ObjectDynProp = targets[index].mDynamicProperty;
    p->ObjectHeight = targets[index].mTrackFrameHeight / 0.05;
    p->ObjectDistLatRms = targets[index].mXRms / 0.375;
    p->ObjectVrelLongRms = targets[index].mVyRms / 0.375;
    p->ObjectVrelLatRms = targets[index].mVxRms / 0.375;
    p->ObjectArelLongRms = targets[index].mAyRms / 0.375;
    p->ObjectArelLatRms = targets[index].mAxRms / 0.375;
    p->ObjectOrientationAngle = (targets[index].mAngle + 180) / 0.4;
    p->ObjectProbOfExist = targets[index].mExistProbability / 3.2258;
    p->ObjectDistAltitude = (targets[index].mZ + 1.75) / 0.05;
    p->ObjectRollingCnt = targets[index].mRollingCount;

    std::reverse(data.begin(), data.end());
    return data;
}

QByteArray PlaybackCanWoker::encodingEndFrameData(const PlaybackTarget * const playbackData)
{
    QByteArray data(24, 0);
    SRR_EndFrame_t *p = (SRR_EndFrame_t*)data.data();
    const EndFrameData &endFrameData = playbackData->mEndFrameData;

    p->EndFrameCheckSum = endFrameData.mEndFrameChecksum;
    p->EndFrameEOLInstallAngle = (endFrameData.mEndOfLineEstablishedAngle + 102.4) / 0.1;
    p->EndFrameAutoCalAngleOffset = (endFrameData.mSelfCalibrationEstablishedAngle + 102.4) / 0.1;
    p->EndFrameInterTime = endFrameData.mEndFrameIntervalTime;
    p->EndFrameFuncCalcTime = 1;
    p->EndFrameRoadSideDist = (endFrameData.mRoadSideDistance + 25) / 0.05;
    p->EndFrameRollingCnt = endFrameData.mRadarRollingCount;
    p->EndFrameTimeTick = endFrameData.mTimeTick;
    p->EndFrameMeasCnt = playbackData->mTargetHeader.mMeasurementCount;

    std::reverse(data.begin(), data.end());
    return data;
}

void PlaybackTarget::print() const
{
    qDebug() << __FUNCTION__ << __LINE__
             << mRadarID
             << mRadarFrameNumber
             << mFrameTime
             << mSystemFrameTime
             << mSaveTime
             << mTotal;
}

void PlaybackRadarTarget::print() const
{
    qDebug() << __FUNCTION__ << __LINE__ << mFirstFrameTime.toString("yyyy-MM-dd hh:mm:ss.zzz") << "=====================================";
    QMapIterator<quint16, PlaybackTarget*> i(mRadarTargets);
      while (i.hasNext()) {
          i.next();
          i.value()->print();
      }
}
