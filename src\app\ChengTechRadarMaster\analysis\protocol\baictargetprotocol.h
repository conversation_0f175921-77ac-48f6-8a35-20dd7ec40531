﻿#ifndef BAICTARGETPROTOCOL_H
#define BAICTARGETPROTOCOL_H

#include "ianalysisprotocol.h"

namespace Analysis {
namespace Protocol {

class BAICTargetProtocol : public IAnalysisProtocol
{
    Q_OBJECT
public:
    explicit BAICTargetProtocol(AnalysisWorker *analysisWorker, QObject *parent = nullptr);

    bool analysisFrame(const Devices::Can::CanFrame &frame) override;
    static qint8 radarID(const Devices::Can::CanFrame &frame);

signals:

private:
    bool baic6TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame);
    bool baic16TargetParse(quint8 radarID, const Devices::Can::CanFrame &frame);

    bool analysisFrame2(const Devices::Can::CanFrame &frame);
    bool baicVehicleSpeedParse( /*quint8 radarID,*/ const Devices::Can::CanFrame &frame);
    bool baicAlarmParse385( const Devices::Can::CanFrame &frame );
    bool baicAlarmParse386( const Devices::Can::CanFrame &frame );
    //bool baicAlarmParse385( const Devices::Can::CanFrame &frame );
    bool baicAlarmParse384( const Devices::Can::CanFrame &frame );
    bool baicVehicleParse527( const Devices::Can::CanFrame &frame );

    quint8 baicAlarmStateConver( quint8 baicState );

private:
    quint8 mBaicChannelIndex{4}; //这个值需要根据实际情况调整，代表北汽的通道  asc中的通道数-1？？？
};

} // namespace Protocol
} // namespace Analysis

#endif // BAICTARGETPROTOCOL_H
