﻿/**
 * @file apar_types.h
 * @brief 用于存放APAR的公共数据类型的定义
 * @details APAR部分存放的数据类型主要是应用部分定义的相关参数，如版本号、安装角度、雷达ID、标定参数等
 * <AUTHOR> (wang<PERSON><PERSON>@chengtech.net)
 * @version 1.0
 * @date 2022-10-16
 *
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2022-10-16 <td>1.0     <td>Wang Juhua     <td>初始版本
 * </table>
 * @copyright Copyright (c) 2022  Shenzhen Cheng-Tech Co.,Ltd.
 */

#ifndef _APAR_TYPES_H_
#define _APAR_TYPES_H_

#ifndef PC_DBG_FW
#include "typedefs.h"
#else
#include "app/system_mgr/typedefs.h"
#endif

/**
 * @brief 雷达内部的相关信息
 * 
 * @details 包括硬件版本号，硬件版本号等
 */
typedef struct
{
    /**
     * @brief 硬件版本号，长度4byte
     */
    char HwVer[4];

    /**
     * @brief 软件版本号，长度4byte
     */
    char SwVer[4];

    /**
     * @brief Boot软件版本号，长度4byte
     */
    char bootSwVer[4];

    /**
     * @brief 标定数据版本号，长度4byte
     */
    char CalVer[4];

    /**
     * @brief IP地址
     */
    char ipaddr[4];

    /**
     * @brief MAC地址
     */
    char macaddr[6];
#ifndef PC_DBG_FW
} __attribute__((packed)) radarInfo_t;
#else
}radarInfo_t;
#endif

/**
 * @brief APAR通用Header信息
 */
typedef struct
{
    /**
     * @brief Magic Number
     */
    uint32_t magicNum;

    /**
     * @brief CRC信息
     */
    uint32_t crc;

    /**
     * @brief 后续数据的长度，从Header中的CRC之后开始计算
     */
    uint16_t size;
    // uint16_t itemNum;

    /**
     * @brief 版本号
     */
    uint32_t versionNumber;
#ifndef PC_DBG_FW
} __attribute__((packed)) APAR_commonHeader_t;
#else
}APAR_commonHeader_t;
#endif

/**
 * @brief APAR数据结构体
 * 
 * @details 主要用于其他模块的调试，数据保存在Flash中，从radar_config_t结构体分离出来
 */
typedef struct stAPARData
{
    /**
     * @brief Header信息
     */
    APAR_commonHeader_t aparHeader;

    /**
     * @brief 水平方向安装角度
     */
    float aparInstallAzimuthAngle;

    /**
     * @brief 垂直方向安装角度
     */
    float aparInstallElevationAngle;

    /**
     * @brief 水平安装角度偏差，暂时没有使用
     */
    float aparInstallAzimuthAngleOffset;

    /**
     * @brief 垂直安装角度偏差，暂时没有使用
     */
    float aparInstallElevationAngleOffset;

    /**
     * @brief OEM EOL静态标定水平安装角
     */
    float aparStaticALNAzimuthAngle;

    /**
     * @brief OEM EOL静态标定垂直安装角
     */
    float aparStaticALNElevationAngle;

    /**
     * @brief 经销商售后标定水平安装角
     */
    float aparServiceDiagALNAzimuthAngle;

    /**
     * @brief 经销商售后标定垂直安装角
     */
    float aparServiceDiagALNElevationAngle;

    /**
     * @brief 行驶过程中自标定水平安装角
     */
    float aparAutoALNAzimuthAngle;

    /**
     * @brief 行驶过程中自标定垂直安装角
     */
    float aparAutoALNElevationAngle;

    /**
     * @brief 起始频率，CW模式恢复后使用
     */
    float aparFmcwStartFreq; 

    /**
     * @brief 雷达的版本号信息
     */
    radarInfo_t aparRadarInfo;

    /**
     * @brief 雷达的ID信息
     */
    uint8_t aparRadarId;

    /**
     * @brief 速度来源选择
     */
    uint8_t aparSpeedSource;

    /**
     * @brief TX通道数量选择
     */
    uint8_t aparTxSelect;

    /**
     * @brief TX模式选择
     */
    uint8_t aparTxPattern;

    /**
     * @brief 雷达扩展信息使能开关，0不发送，1发送
     */
    uint8_t aparObjExtendInfoSW; 

    /**
     * @brief 雷达扩展信息2使能开关，0不发送，1发送
     */
    uint8_t aparObjExtendInfoSW2;

    /**
     * @brief 雷达协议版本
     */
    uint8_t aparProtocolVersion;

    /**
     * @brief 老化信息ID
     */
    uint16_t aparRandomId;
    
    /**
     * @brief 老化信息发送开关
     */
    uint8_t aparAgingTest;

    // TODO:ADAS模块合入后，应用部分的参数也需要添加进来

#ifndef PC_DBG_FW
} __attribute__((packed)) APAR_CfgData_t;
#else
}APAR_CfgData_t;
#endif

/**
 * @brief Boot标志位信息
 */
typedef struct stBootFlags
{
    APAR_commonHeader_t aparHeader;//Header信息

} APAR_BootFlagData_t;

/**
 * @brief 系统参数
 * 
 * @details 系统参数应当包括启动参数、chip序列号
 */
typedef struct stSystemParameters
{
    uint32_t aparChipSN; ///<芯片序列号

} APAR_SystemParameter_t;


#endif
