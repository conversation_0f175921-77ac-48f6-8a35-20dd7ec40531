include(../../../chengtech.pri)

QT       += core gui network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

DESTDIR = $$MASTER_APP_PATH

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0


DATA_PROCESS_PREFIX_PATH = ../ChengTechRadarMaster
INCLUDEPATH += \
    $$MASTER_SOURCE_TREE/src/app/ChengTechRadarMaster \
    $$MASTER_SOURCE_TREE/src/app/ChengTechRadarMaster/dataprocess \

DEPENDPATH += $$MASTER_SOURCE_TREE/src/app/ChengTechRadarMaster
include(../ChengTechRadarMaster/dataprocess/dataprocess_code.pri)

SOURCES += \
    $$DATA_PROCESS_PREFIX_PATH/dataprocess/dataprocess.cpp \
    ../ChengTechRadarMaster/analysis/analysisdata.cpp \
    ../ChengTechRadarMaster/dataprocess/networktcpclient.cpp \
    ../ChengTechRadarMaster/dataprocess/networktcpserver.cpp \
    ../ChengTechRadarMaster/utils/loghandler.cpp \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    $$DATA_PROCESS_PREFIX_PATH/dataprocess/dataprocess.h \
    $$DATA_PROCESS_PREFIX_PATH/dataprocess/dataprocess_global.h \
    ../ChengTechRadarMaster/analysis/analysis_global.h \
    ../ChengTechRadarMaster/analysis/analysisdata.h \
    ../ChengTechRadarMaster/dataprocess/networktcpclient.h \
    ../ChengTechRadarMaster/dataprocess/networktcpserver.h \
    ../ChengTechRadarMaster/utils/loghandler.h \
    mainwindow.h

FORMS += \
    mainwindow.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
