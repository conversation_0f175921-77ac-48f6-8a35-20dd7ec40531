#ifndef PROT_PCAN_APP_H
#define PROT_PCAN_APP_H

#ifndef PC_DBG_FW
#include "sharedVar.h"
#include "radarCfg.h"
#else
#include "app/include/sharedVar.h"
#include "app/system_mgr/radarCfg.h" 
#endif


//安装校准配置
typedef struct
{
#if IS_BIG_ENDIAN == 1
    uint64_t calcEn_valid : 1;                //使能
    uint64_t calcObjRange_valid : 1;          //距离
    uint64_t calcObjRangeThreshold_valid : 1; //距离阈值
    uint64_t calcObjAngle_valid : 1;          //角度
    uint64_t calcObjAngleThreshold_valid : 1; //角度阈值
    uint64_t AngleRangeValid : 1;             //校准状态
    uint64_t calcResultSendEn_valid : 1;
    uint64_t HistParamCfgValid : 1;
    uint64_t calcEn : 1;                //使能
    uint64_t calcResultSendFlag : 1;    //
    uint64_t SNSendFlag : 1;            //
    uint64_t calcObjRange : 13;         //探测目标距离
    uint64_t calcObjRangeThreshold : 8; //距离阈值
    uint64_t calcObjAngle : 8;          //实际角度
    uint64_t calcObjAngleThreshold : 8; //角度阈值
    uint64_t AngleRange : 8;            //标定角度范围 --
    uint64_t HistAngleThr : 4;          //直方图分辨率 --
    uint64_t HistValidThr : 4;          //直方图有效个数门限 --
#else
    uint64_t HistValidThr : 4;           //直方图有效个数门限 --
    uint64_t HistAngleThr : 4;           //直方图分辨率 --
    uint64_t AngleRange : 8;             //标定角度范围 --
    uint64_t calcObjAngleThreshold : 8;  //角度阈值
    uint64_t calcObjAngle : 8;           //实际角度
    uint64_t calcObjRangeThreshold : 8;  //距离阈值
    uint64_t calcObjRange : 13;          //探测目标距离
    uint64_t SNSendFlag : 1;             //
    uint64_t calcResultSendFlag : 1;     //
    uint64_t calcEn : 1;                 //使能
    uint64_t HistParamCfgValid : 1;
    uint64_t calcResultSendEn_valid : 1;
    uint64_t AngleRangeValid : 1;             //校准状态
    uint64_t calcObjAngleThreshold_valid : 1; //角度阈值
    uint64_t calcObjAngle_valid : 1;          //角度
    uint64_t calcObjRangeThreshold_valid : 1; //距离阈值
    uint64_t calcObjRange_valid : 1;          //距离
    uint64_t calcEn_valid : 1;                //使能有效位
#endif
} stRadarInstallCalcCfg;

//安装校准扩展配置
typedef struct
{
#if IS_BIG_ENDIAN == 1
    uint64_t AngleRangeValid : 1;   //
    uint64_t SendInfoEnValid : 1;   //
    uint64_t HistAngleThrValid : 1; //
    uint64_t HistValidThrValid : 1; //
    uint64_t NoObjCntThrValid : 1;  //
    uint64_t TimeoutThrValid : 1;   //
    uint64_t WaitThrValid : 1;
    uint64_t TxSelValid : 1;
    uint64_t AngleRange : 8;   //标定角度范围
    uint64_t SendInfoEn : 8;   //发送使能
    uint64_t HistAngleThr : 4; //直方图分辨率
    uint64_t HistValidThr : 4; //直方图有效个数门限
    uint64_t Res27 : 2;        //
    uint64_t NoObjCntThr : 6;  //无目标判定帧数
    uint64_t res55 : 2;        //
    uint64_t TimeoutThr : 6;   //超时帧数
    uint64_t WaitThr : 8;      //标定等待时间
    uint64_t res10 : 4;
    uint64_t TxSel : 4; //Tx选择
#else

    uint64_t TxSel : 4; //Tx选择
    uint64_t res10 : 4;
    uint64_t WaitThr : 8;      //标定等待时间
    uint64_t TimeoutThr : 6;   //超时帧数
    uint64_t res55 : 2;        //
    uint64_t NoObjCntThr : 6;  //无目标判定帧数
    uint64_t Res27 : 2;        //
    uint64_t HistValidThr : 4; //直方图有效个数门限
    uint64_t HistAngleThr : 4; //直方图分辨率
    uint64_t SendInfoEn : 8;   //发送使能
    uint64_t AngleRange : 8;   //标定角度范围
    uint64_t TxSelValid : 1;
    uint64_t WaitThrValid : 1;
    uint64_t TimeoutThrValid : 1;   //
    uint64_t NoObjCntThrValid : 1;  //
    uint64_t HistValidThrValid : 1; //
    uint64_t HistAngleThrValid : 1; //
    uint64_t SendInfoEnValid : 1;   //
    uint64_t AngleRangeValid : 1;   //
#endif
} stInstallCalcExtCfg;

extern void radarInstallCalceParam(uint8_t canIdx,void *pData);

extern void radarWorkModeSet(uint8_t workMode);

#endif
