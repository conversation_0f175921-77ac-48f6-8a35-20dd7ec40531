include(../libs.pri)

QT += core gui widgets network

DEFINES += ALGORITHM_DEBUG

HEADERS += \
    CTMRR410.h \
    daqcontrol.h \
    daqmanager.h \
    playback_global.h \
    playbackcanwoker.h \
    playbacknetworkworker.h

FORMS += \
    daqcontrol.ui

SOURCES += \
    CTMRR410.c \
    daqcontrol.cpp \
    daqmanager.cpp \
    playbackcanwoker.cpp \
    playbacknetworkworker.cpp

LIBS += -lutils -ldevices -lanalysis -ldataprocess

RESOURCES += \
    debugsystem.qrc
