/**
 * ************************************************************************
 * @file vmm.h
 * @brief 车辆模式管理，根据车辆状态控制雷达行为
 * 
 * <AUTHOR>
 * @date 2024-06-17
 * 
 * ************************************************************************
 * @copyright Copyright (c) 2024  深圳承泰科技有限公司 All Rights Reserved.
 * 
 * ************************************************************************
 */

#ifndef VMM_H
#define VMM_H

#include "radar_types.h"
#include <stdbool.h>

/**
 * @brief 可唤醒状态
 */
enum WAKE_STATUS_E
{
    WAKEABLE,      /**< 可唤醒 */
    NOT_WAKEABLE,  /**< 不可唤醒，车身状态不运行唤醒 */
    NO_CONDITION   /**< 没有唤醒条件，没有收到车身状态信息 */
};

/**
 * @brief vmm模块初始化，将参数设置到default状态
 * <AUTHOR>
 */
void Vmm_init(void);

/**
 * @brief 更新VMM状态，此接口需要定时调用，周期不大于500ms
 * <AUTHOR>
 */
void Vmm_updateStatus(void);

/**
 * @brief 查询通信是否使能
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_commuActived(void);

/**
 * @brief 是否满足唤醒条件
 * <AUTHOR>
 * @return enum WAKE_STATUS_E 唤醒状态
 */
enum WAKE_STATUS_E Vmm_wakeable(void);

/**
 * @brief 是否需要进入休眠
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_needSleep(void);

/**
 * @brief pnc是否置1
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_pncValid(void);

/**
 * @brief vmm状态是否满足active条件，如果vmm出现会导致关闭通信或休眠的状态则此处返回false
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_statusActive(void);

/**
 * @brief 设置车身VMM相关的信号
 * <AUTHOR>
 * @param[in] carMode   车辆模式
 * @param[in] usageMode 使用模式
 * @param[in] ElPowerLevel 
 * @param[in] ElEnergyLevel
 */
void Vmm_setVehicleModeSig(uint8_t carMode, uint8_t usageMode, uint8_t ElPowerLevel, uint8_t ElEnergyLevel);

/**
 * @brief 设置QCM fault状态
 * <AUTHOR>
 * @param[in] status    comment
 */
void Vmm_setQCMFault(uint8_t status);

/**
 * @brief 是否需要存储QCM DID
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_needSaveQCM(void);

/**
 * @brief 复位要存储QCM DID的状态
 * <AUTHOR>
 */
void Vmm_resetSaveQCM(void);

/**
 * @brief 获取系统激活维持的时间
 * <AUTHOR>
 * @return uint32_t 时间，单位秒
 */
uint32_t Vmm_activeLastTime(void);

/**
 * @brief 当前VMM的输入信号是否满足激活条件
 * <AUTHOR>
 * @return true 
 * @return false 
 */
bool Vmm_signalActive(void);

void Vmm_commControlProsess(void);

extern uint8_t poweroffflage;
extern uint8_t poweronflage;
#endif
