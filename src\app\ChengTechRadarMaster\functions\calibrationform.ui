<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CalibrationForm</class>
 <widget class="QWidget" name="CalibrationForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QComboBox" name="comboBoxProtocolType">
       <item>
        <property name="text">
         <string>BYD</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>HASE</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxCalibrationType">
       <item>
        <property name="text">
         <string>售后标定</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>下线标定</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>退出工厂模式</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>VCAN 通道：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxChannelIndex">
       <item>
        <property name="text">
         <string>CAN 0</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>CAN 1</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxCAN">
       <property name="text">
        <string>CAN</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QPlainTextEdit" name="plainTextEdit"/>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonStart">
     <property name="text">
      <string>开始SDA</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonStop">
     <property name="text">
      <string>Stop</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
