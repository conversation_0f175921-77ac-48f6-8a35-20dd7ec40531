﻿#include "iinterpolation.h"

#ifdef ALGORITHM_DEBUG
#include "dataprocess/dataprocess.h"
#endif

#include <QDateTime>
#include <QFileInfo>

namespace Analysis {

IInterpolation::IInterpolation(AnalysisWorker *analysisWorker)
    : mAnalysisWorker(analysisWorker)
{

}

int IInterpolation::encodeFrame(int radarID, Devices::Can::FrameTimestamp *timestamp, RDP_TrkObjectInfo_t *outputObjList, int16_t trkValidNum, uint8_t msgCounter, Devices::Can::stCanTxMsg *frameArray)
{
    return  0;
}
#ifdef ALGORITHM_GEELY
int IInterpolation::encodeFrame(int radarID, Devices::Can::FrameTimestamp *timestamp, RDP_TrkFrameInfoGeely2_0 *outputObjList, int16_t trkValidNum, uint8_t msgCounter, Devices::Can::stCanTxMsg *frameArray)
{
    return  0;
}
#endif

void IInterpolation::setChannelRadarIDGEELY(int *channelRadarID, int size)
{

}

void IInterpolation::setChannelRadarIDBYDHO(int *channelRadarID, int size)
{

}

QString addTimestampToFilename(const QString& filename) {
    // 获取当前时间戳
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QString timestamp = currentDateTime.toString("yyyyMMddHHmmss");

    // 获取文件的扩展名和基本名称
    QFileInfo fileInfo(filename);
    QString basePath = fileInfo.absolutePath();
    QString baseName = fileInfo.completeBaseName();
    QString suffix = fileInfo.suffix();

    // 拼接新的文件名
    QString newFilename = basePath + "/" + baseName + "_" + timestamp;
    if (!suffix.isEmpty()) {
        newFilename += "." + suffix;
    }

    return newFilename;
}

bool IInterpolation::start()
{
    mFirst = true;
    if (!mFiles.size()) {
        return false;
    }
    const QString &filename = mFiles.at(0);
    QFileInfo fileInfo(filename);
    QString suffix = fileInfo.suffix();

    if (suffix == "blf") {
        if (!mWriterBLF) {
            mWriterBLF = new Devices::Can::DeviceFileBLF;
        }
        mWriter = mWriterBLF;
    } else if (suffix == "asc") {
        if (!mWriterASC) {
            mWriterASC = new Devices::Can::DeviceFileASC;
        }
        mWriter = mWriterASC;
    } else {
        return false;
    }

    const QString &newFile = addTimestampToFilename(filename);
    qDebug() << __FUNCTION__ << __LINE__ << filename << newFile;
    return mWriter->open(newFile.toLocal8Bit().data(), false);
}

bool IInterpolation::stop()
{
    if (mWriter) {
        mWriter->close();
    }
    return true;
}

void IInterpolation::injection(int radarID, const Devices::Can::CanFrame &frame)
{
    static Devices::Can::stCanTxMsg frameArray[240];
    switch (mRaw600ByChannel ? frame.id() : frame.idN()) {
    case 0x600: // 上一个周期的已解析
    {
        if (frame.length() != 16)
        {
            return;
        }
        const uint8_t *data = (const uint8_t *)frame.data().data();
        mPointListIsAhead = ((data[13] & 0x4U) >> 2); // 0：前面有插值；1：前面无插值

        qDebug() << __FUNCTION__ << __LINE__ << "message";
        if (mPointListIsAhead == 0) {
            mTimestamps.mCountInterpolation = mTimestamps.mCount > 0 ? mTimestamps.mCount - 1 : 0;
        }

        qDebug() << __FUNCTION__ << __LINE__ << "message";
        if (mTimestamps.mRawOK) {
            // 进行数据处理
            AnalysisData &analysisData = mAnalysisWorker->mAnalysisDatas[radarID];

            if (!DataProcess::instance()->process(analysisData)) {
                    qDebug() << __FUNCTION__ << __LINE__ << "master radar data process error!";
            }
            qDebug() << __FUNCTION__ << __LINE__ << "message";
            // 进行插值
            // 进行上一周期的插值
            int frameCount = DataProcess::instance()->injection(radarID, mPointListIsAhead == 0, analysisData.mEndFrameData.mFrameTimeStampGlobal, &mTimestamps, frameArray, this);
            qDebug() << __FUNCTION__ << __LINE__ << radarID << mTimestamps.mCount << mTimestamps.mCountInterpolation << frameCount;
            for (int i = 0; i < frameCount; ++i) {
                Devices::Can::stCanTxMsg* pMsg = frameArray + i;
                if (pMsg->valid) {
                    Devices::Can::CanFrame::Ptr frame = Devices::Can::CanFrame::New(
                                0,
                                pMsg->channel,
                                Devices::Can::CanFrame::RX,
                                pMsg->id,
                                pMsg->data,
                                pMsg->dlc,
                                pMsg->timestamp,
                                false,
                                true);
                    mWriter->writeData(frame);
//                    qDebug() << __FUNCTION__ << __LINE__ << pMsg->channel << QString::number(pMsg->id, 16) << pMsg->timestamp;
                }
            }
        }

        if (mPointListIsAhead == 0) {
            if (mTimestamps.mCount > 1) {
                memcpy(mTimestamps.mTimestamps + 0, mTimestamps.mTimestamps + mTimestamps.mCount - 1, sizeof(Devices::Can::FrameTimestamp));
                mTimestamps.mCount = 1;
            }
        } else {
            mTimestamps.mCount = 0;
        }
//        qDebug() << __FUNCTION__ << __LINE__ << radarID << mTimestamps.mCount;

        mTimestamps.mRawOK = false;
    }
        break;
    case 0x4F0: // 原始点结束帧
    case 0x605:
        qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
        mTimestamps.mRawOK = true;
        break;
    }
}

} // namespace Analysis


/**
 * @brief crc16 CCITT-FALSE Verify the checksum of data.
 *
 * @param [in] *data
 *     The pointer of the data buffer.
 * @param [in] *length
 *     The length of data.
 *
 * @retval
 *
 */
uint16_t CRC16_CCITT_FALSE(uint8_t *pMsg, uint32_t dataLen)
{
        uint16_t wCRCin = 0xFFFF;
        uint16_t wCPoly = 0x1021;
        uint8_t  wChar = 0;

        //跳过两个字节，仰望U9 新增信号CCU，前两个字节[0],[1]为crc16校验码
        uint8_t *p = pMsg + 2;
        uint32_t len = dataLen - 2;

        while (len--)
        {
                wChar = *(p++);
                wCRCin ^= (wChar << 8);
                for(int i = 0; i < 8; i++)
                {
                        if(wCRCin & 0x8000)
                                wCRCin = (wCRCin << 1) ^ wCPoly;
                        else
                                wCRCin = wCRCin << 1;
                }
        }

        return (wCRCin) ;
}
