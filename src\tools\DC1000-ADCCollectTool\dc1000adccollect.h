﻿#ifndef DC1000ADCCOLLECT_H
#define DC1000ADCCOLLECT_H

#include <QMainWindow>

#include "analysisdata.h"

class QLabel;

class CANDeviceZLG;
class CANSaveWorker;
class <PERSON><PERSON>arseWorker;
class <PERSON>Worker;
class DC1000Worker;

namespace Ui {
class DC1000ADCCollect;
}

class DC1000ADCCollect : public QMainWindow
{
    Q_OBJECT

public:
    explicit DC1000ADCCollect(QWidget *parent = nullptr);
    ~DC1000ADCCollect();

signals:
    void closed();
    void startDC1000Worker(int frameSize, unsigned long long size, int canChannel, int chirpCount, int radarID, int vechileJump,
                           const QString &ADCFilename, const QString &CANFilename);

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void clientConnected(unsigned short port, bool ok);

    void collectStatusChanged();

    void imShow(unsigned long long saveIndex);

    void saveFilename(const QString &filename);

    void on_pushButtonOpenCANDevice_clicked();

    void on_pushButtonOpenCamera_clicked();

    void on_pushButtonCameraRefresh_clicked();

    void on_pushButtonConnect_clicked();

    void on_pushButtonStart_clicked();

    void on_pushButtonOpenSavePath_clicked();

    void on_actionTargetViewSettings_triggered();

    void on_pushButtonFileADC_clicked();

    void on_pushButtonFileCAN_clicked();

private:
    void saveSttings();
    void loadSettings();
    void appendLog(const QString &text);

    Ui::DC1000ADCCollect *ui;

    QLabel *mLabelSaveFilename{0};

    CANDeviceZLG *mCANDeviceZLG{0};
    CANSaveWorker *mCANSaveWorker{0};
    CANParseWorker *mCANParseWorker{0};
    CameraWorker *mCameraWorker{0};
    DC1000Worker *mDC1000Worker{0};
};

#endif // DC1000ADCCOLLECT_H
