﻿#include "analysisdataview.h"
#include "ui_analysisdataview.h"

#include "analysismodel.h"
#include "analysisdatatableview.h"
#include "utils/flowlayout.h"
#include "widget/alarmwidget.h"

#include <QAction>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QSpacerItem>
#include <QPainter>
#include <QStyle>
#include <QPushButton>
#include <QDialog>
#include <QCheckBox>
#include <QDateTime>
#include <QDebug>

namespace Views {
namespace AnalysisView {

AnalysisDataView::AnalysisDataView(quint8 radarID, AnalysisData *analysisData, QWidget *parent) :
    AnalysisDataViewI(radarID, parent),
    mRadarID(radarID),
    mAnalysisData(analysisData)
{
    mRadarType = AngularRadar;

    setupUi();

    mActionDBCConfig = new QAction("DBC配置", this);

    connect(&mTimerFrameBreaking, &QTimer::timeout, this, &AnalysisDataView::frameBreaking);

    //默认隐藏故障信息
//    on_pushButtonShowErrorInfomation_clicked();
    showFaultInfo( true );
    showVersionInfo( false );
    showAngleInfo( false );
}

AnalysisDataView::~AnalysisDataView()
{
}

int AnalysisDataView::radarID() const
{
    return  mRadarID;
}

void AnalysisDataView::analysisTargets(quint8 radarID, int frameType, const Targets &targets)
{
    foreach (AnalysisDataTableView *view, mAnalysisDataTableViews) {
        QAbstractTableModel *model = view->model();
        if (!model || (view->viewType() != frameType)) {
            continue;
        }
        int viewType = view->viewType();
        switch(viewType) {
        case Frame16Track:
//            qDebug() << __FUNCTION__ << __LINE__ << radarID << frameType << targets.mTargetHeader.mMeasurementCount;
            mTarget16Timestamp->setText(QDateTime::fromMSecsSinceEpoch(targets.mTargetHeader.mMeasurementCount).toString("yyyy-MM-dd hh:mm:ss.zzz"));
        case FrameRawTarget:
        case FrameTrackTarget:
        case Frame200Raw:
        case Frame3Track:
            ((AnalysisModel*)model)->targets(radarID, viewType, targets);
//            qDebug() << __FUNCTION__ << __LINE__ << radarID << frameType << targets.mTargetHeader.mMeasurementCount;
        default:
            return;
        }
    }
}

void AnalysisDataView::analysisRadarData(/*TargetModel*/int targetModel, quint8 radarID, AnalysisData *analysisData)
{
    if (mFrameBreakingFirst || mTimerFrameBreaking.isActive()) {
        mTimerFrameBreaking.start(10 * 1000);
        mFrameBreakingFirst = false;
    }
    AlarmData &alarmData = analysisData->mAlarmData;
    EndFrameData &endFrameData = analysisData->mEndFrameData;
    VehicleData &vehicleData = analysisData->mVehicleData;
    Targets &targetsRaw = analysisData->mTargets[FrameRawTarget];
    Targets &targetsTrack = analysisData->mTargets[FrameTrackTarget];
    Targets &targets16 = analysisData->m16Targets;
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << targets16.mTargetHeader.mMeasurementCount << analysisData->m16Targets.mTargetHeader.mMeasurementCount;

    /**************************** 表格信息 ****************************/
#if 1
    foreach (AnalysisDataTableView *view, mAnalysisDataTableViews) {
        QAbstractTableModel *model = view->model();
        if (!model) {
            continue;
        }
        int viewType = view->viewType();
        switch(viewType) {
        case FrameRawTarget:
            ((AnalysisModel*)model)->targets(radarID, viewType, targetsRaw);
            break;
        case FrameTrackTarget:
            ((AnalysisModel*)model)->targets(radarID, viewType, targetsTrack);
            break;
        case FrameAlarm:
            ((AlarmDataModel*)model)->setAlarmData(alarmData);
            break;
        case FrameEndFrame:
            ((EndFrameModel*)model)->setEndFrameData(endFrameData);
            break;
        case FrameVehicle:
            ((VehicleDataModel*)model)->setVehicleData(vehicleData);
            break;
        case Frame200Raw:
            ((AnalysisModel*)model)->targets(radarID, viewType, analysisData->m200Targets);
            break;
        case Frame3Track:
            ((AnalysisModel*)model)->targets(radarID, viewType, analysisData->mLockTargets);
            break;
        case Frame16Track:
            ((AnalysisModel*)model)->targets(radarID, viewType, analysisData->m16Targets);
            break;
        default:
            return;
        }
    }
#endif

    /**************************** 原始点信息 ****************************/
    if (mRawMeasurementCount != 0xFFFFFFFF && targetsRaw.mTargetHeader.mMeasurementCount < mRawMeasurementCount)
    {
        qWarning() << __FUNCTION__ << __LINE__
                   << QString("Radar[%1] Raw Measurement Count Error! History[%2] -> Current[%3] FAIL TIME: %4")
                      .arg(radarID)
                      .arg(mRawMeasurementCount)
                      .arg(targetsRaw.mTargetHeader.mMeasurementCount)
                      .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"));
        mLabelMeasurementCount->setStyleSheet("background-color: rgb(255, 0, 0);");
    }
    mRawMeasurementCount = targetsRaw.mTargetHeader.mMeasurementCount;
    mLabelResponseTaskCycleTime->setText(QString("%1/%2")
                                         .arg(targetsRaw.mTargetHeader.mTargetIntervalTime, 3)
                                         .arg(targetsRaw.mTargetHeader.mResponseTaskCycleTime, 3));

    mLabelFrequencyHoppingState->setText(QString("%1/%2")
                                         .arg(QString::number(targetsRaw.mTargetHeader.mFrequencyHoppingState))
                                         .arg(QString::number(targetsRaw.mTargetHeader.mHeaderCurFrameMode)));
    mLabelTunnelsceneStateRaw->setText(QString("%1/%2")
                                       .arg(QString::number(targetsRaw.mTargetHeader.mTunnelsceneState))
                                       .arg(QString::number(targetsRaw.mTargetHeader.mHeaderSenceFlag)));
    mLabelWaveType->setText(QString::number(targetsRaw.mTargetHeader.mWaveType));
    mLabelNoiseCurrent->setText(QString::number(targetsRaw.mTargetHeader.mNoiseCurrent));
    mLabelNoiseGlobal->setText(QString::number(targetsRaw.mTargetHeader.mNoiseGlobal));
    //遮挡信息
    mLabelBlockagePercentRaw->setStyleSheet( targetsRaw.mTargetHeader.mBlockageFlag != 0 ? "background-color: rgb(255, 0, 0);" : "");
    mLabelBlockagePercentRaw->setText( QString("%1:%2%")
                                       .arg(targetsRaw.mTargetHeader.mBlockageFlag)
                                       .arg(targetsRaw.mTargetHeader.mBlockagePercent, 5, 'f', 2));
    //干扰信息
    mLabelInterferencePercentRaw->setStyleSheet( targetsRaw.mTargetHeader.mInterferenceFlag != 0 ? "background-color: rgb(255, 0, 0);" : "");
    mLabelInterferencePercentRaw->setText( QString("%1:%2%")
                                           .arg(targetsRaw.mTargetHeader.mInterferenceFlag)
                                           .arg( targetsRaw.mTargetHeader.mInterferencePercent, 5, 'f', 2));

    /**************************** 跟踪点信息 ****************************/
    //遮挡信息
    mLabelBlockagePercentTrack->setStyleSheet( targetsTrack.mTargetHeader.mBlockageFlag != 0 ? "background-color: rgb(255, 0, 0);" : "");
    mLabelBlockagePercentTrack->setText( QString("%1:%2%")
                                         .arg(targetsTrack.mTargetHeader.mBlockageFlag)
                                         .arg(targetsTrack.mTargetHeader.mBlockagePercent, 5, 'f', 2));
    //干扰信息
    mLabelInterferencePercentTrack->setStyleSheet( targetsTrack.mTargetHeader.mInterferenceFlag != 0 ? "background-color: rgb(255, 0, 0);" : "");
    mLabelInterferencePercentTrack->setText( QString("%1:%2%")
                                             .arg(targetsTrack.mTargetHeader.mInterferenceFlag)
                                             .arg( targetsTrack.mTargetHeader.mInterferencePercent, 5, 'f', 2));
    mLabelTunnelsceneStateTrack->setText(QString::number(targetsTrack.mTargetHeader.mTunnelsceneState));
//    qDebug() << __FUNCTION__ << __LINE__ << targetsTrack.mTargetHeader.mInterferencePercent;


    mLabelMeasurementCount->setText(QString("%1/%2")
                                    .arg(mRawMeasurementCount, 5)
                                    .arg(targetsTrack.mTargetHeader.mMeasurementCount, 5));

    labelMeasurementCounter()->setText(QString("%1:%2/%3")
                                      .arg(radarID)
                                      .arg(targetsRaw.mTargetHeader.mMeasurementCount)
                                      .arg(targetsTrack.mTargetHeader.mMeasurementCount));

//    ui->labelFrameTime->setText(endFrameData.mFrameTime.toString("hh:mm:ss.zzz"));
    mLabelEndFrameTime->setText(QDateTime::fromMSecsSinceEpoch(endFrameData.mSystemFrameTime).toString("hh:mm:ss.zzz"));
    mLabelEstablishedAngle->setText(QString("%1/%2").arg(endFrameData.mEndOfLineEstablishedAngle).arg(endFrameData.mSelfCalibrationEstablishedAngle));
    mLabelTemperature->setText(QString::number(endFrameData.mTemperature));

    bool bAutoALNStsErr = false;
    QString autoALNStsStr = "";
    //qDebug() << __FUNCTION__ << __LINE__ << (int)(endFrameData.mAutoALNSts);
    switch( (int)(endFrameData.mAutoALNSts) ){
    case 0x0:
        autoALNStsStr = "INACTIVE";
        break;
    case 0x1:
        autoALNStsStr = "RUNNING";
        break;
    case 0x2:
        autoALNStsStr = "ABORTING";
        break;
    case 0x3:
        autoALNStsStr = "NORMAL";
        break;
    case 0x4:
        autoALNStsStr = "MINMAXOUT";
        bAutoALNStsErr = true;
        break;
    case 0x5:
        autoALNStsStr = "TIMEOUT";
        bAutoALNStsErr = true;
        break;
    default:
        autoALNStsStr = "UNKNOWN";
        break;
    }
    mLabelAutoALNSts->setText( autoALNStsStr );
    mLabelAutoALNSts->setStyleSheet( bAutoALNStsErr ? "background-color: rgb(255, 0, 0);" : "");

    /**************************** 告警信息 ****************************/
#define SET_ALARM_TTC(name, valueLevel, valueState, objectTTC) \
    if (mAlarmWidget##name->alarm(alarmData.m##valueLevel, alarmData.m##valueState)) {\
        alarmTTC = alarmData.m##objectTTC;\
    }
    float alarmTTC = -1; //宏函数中赋值
    mAlarmWidgetELK->alarm(alarmData.mAlarmELKLevel, 0);
    mAlarmWidgetBSD->alarm(alarmData.mAlarmBSDLevel, alarmData.mAlarmBSDState);
    SET_ALARM_TTC(LCA,   AlarmLCALevel,      AlarmLCAState,  AlarmLCAObjectTTC)
    SET_ALARM_TTC(DOWR,  AlarmDOWRLevel,     AlarmDOWState,  AlarmDOWObjectTTC)
    SET_ALARM_TTC(DOWF,  AlarmDOWFLevel,     AlarmDOWState,  AlarmDOWObjectTTC)
    SET_ALARM_TTC(FCTA,  AlarmFCTALevel,     AlarmFCTAState, AlarmFCTAObjectTTC)
    SET_ALARM_TTC(FCTB,  AlarmFCTBLevel,     AlarmFCTBState, AlarmFCTBObjectTTC)
    SET_ALARM_TTC(RCTA,  AlarmRCTALevel,     AlarmRCTAState, AlarmRCTAObjectTTC)
    SET_ALARM_TTC(RCTB,  AlarmRCTBLevel,     AlarmRCTBState, AlarmRCTBObjectTTC)
    SET_ALARM_TTC(RCW,   AlarmRCWLevel,      AlarmRCWState,  AlarmRCWObjectTTC);
    SET_ALARM_TTC(JA,    AlarmJALevel,       AlarmJAState,   AlarmJAObjectTTC);
    mLabelAlarmTTC->setText( "TTC:" + QString::number( alarmTTC ) );//初始化
    mLabelAlarmDDCI->setText( QString("DDCAI: %1").arg( alarmData.mAlarmRCTBObjectTTC, 0, 'f', 2 ) );//初始化

    /**************************** 车身信息 ****************************/
    QString gerText = "N";
    switch ((quint32)vehicleData.mGear)
    {
    case 0:
        break;
    case 1:
        gerText = "P";
        break;
    case 2:
        gerText = "R";
        break;
    case 3:
        gerText = "N";
        break;
    case 4:
        gerText = "D";
        break;
    default:
        break;
    }

    mLabelVehicleGear->setText(gerText);
    mLabelVehicleSpeedInMeter->setText(QString("%1m/s").arg(vehicleData.mVehicleSpeed / 3.6, 7, 'f', 3));
    mLabelVehicleSpeedInKilometer->setText(QString("%1km/h").arg(vehicleData.mVehicleSpeed, 7, 'f', 3));
    mLabelVehicleRadious->setText(QString("%1").arg(vehicleData.mRadius, 10, 'f', 3));
    mLabelVehicleYawRate->setText(QString("%1").arg(vehicleData.mYawRate, 7, 'f', 3));
    mLabelVehicleTime->setText( QString( "%1-%2-%3 %4:%5:%6" )
                                    .arg((quint32)vehicleData.mVehicleYear, 4, 10, QLatin1Char('0'))
                                    .arg((quint32)vehicleData.mVehicleMonth, 2, 10, QLatin1Char('0'))
                                    .arg((quint32)vehicleData.mVehicleDay, 2, 10, QLatin1Char('0'))
                                    .arg((quint32)vehicleData.mVehicleHour, 2, 10, QLatin1Char('0'))
                                    .arg((quint32)vehicleData.mVehicleMinute, 2, 10, QLatin1Char('0'))
                                    .arg((quint32)vehicleData.mVehicleSecond, 2, 10, QLatin1Char('0')) );
    mTarget16Timestamp->setText(QDateTime::fromMSecsSinceEpoch(targets16.mTargetHeader.mMeasurementCount).toString("yyyy-MM-dd hh:mm:ss.zzz"));

#define BYTE_ARRAY_CHECK(array,value,err) \
    for( int i=0; i<array.size(); i++ ){\
        if( (quint8)array[i] != value ){ \
            err = true; \
            break; \
        } \
    };

    //故障信息
    QByteArray hex0x4DN = QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4DN, 16);
    QByteArray hex0x4EN = QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4EN, 16);
    bool bError = false;
    BYTE_ARRAY_CHECK(hex0x4DN,0,bError);
    mLabelErrorInformation0x4DN->setStyleSheet(bError ? "background-color: rgb(255, 0, 0);" : "");
    mLabelErrorInformation0x4DN->setText("0x4DN: " + hex0x4DN.toHex(' ') );
    bError = false;
    BYTE_ARRAY_CHECK(hex0x4EN,0,bError);
    mLabelErrorInformation0x4EN->setStyleSheet(bError ? "background-color: rgb(255, 0, 0);" : "");
    mLabelErrorInformation0x4EN->setText("0x4EN: " + hex0x4EN.toHex(' ') );
//    mLabelErrorInformation0x4DN->setText( QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4DN, 16).toHex(' ') );
//    mLabelErrorInformation0x4EN->setText( QByteArray::fromRawData((const char*)alarmData.mErrorInformation0x4EN, 16).toHex(' ') );

    /**************************** 版本信息 ****************************/
    quint8* p = (quint8*)analysisData->mEndFrameData.mRadarVersion[0];
    QString versionStr = QString("%1.%2.%3.%4.%5.%6").arg(p[1]).arg(p[2]).arg(p[3]).arg(p[4]).arg(p[5]).arg(p[6]);
    mLabelRadarVersion->setText( versionStr );

    //ui->labelRadarVersion->hide();

    //410协议报文丢帧信息
    quint64 total = analysisData->mEndFrameData.mCurrentRadar410FrameCount - analysisData->mEndFrameData.mInitRadar410FrameCount; //总数
    QString frameCntStr = QString::fromLocal8Bit( "总帧数:%1 \n丢帧数:%2 \n丢帧率:%3%")
                            .arg( total )
                            .arg( analysisData->mEndFrameData.mDiff410FrameCount )
                            .arg( analysisData->mEndFrameData.mDiff410FrameCountRate );
    if( analysisData->mEndFrameData.mDiff410FrameCountRate > 5 ){//丢帧率超过5%，红色警告
        mLabelFrameCount->setStyleSheet("background-color: rgb(255, 0, 0);");
    }else{
        mLabelFrameCount->setStyleSheet("");
    }
    mLabelFrameCount->setText( frameCntStr );
}

void AnalysisDataView::startView()
{
    mLabelMeasurementCount->setStyleSheet("");
    mTimerFrameBreaking.stop();
    mFrameBreakingFirst = true;
    mRawMeasurementCount = 0;
}

void AnalysisDataView::stopView()
{
    mTimerFrameBreaking.stop();
    mFrameBreakingFirst = true;
    mRawMeasurementCount = 0;
}

void AnalysisDataView::showFaultInfo(bool bShow)
{
    mLabelErrorInformation0x4DN->setVisible( bShow );
    mLabelErrorInformation0x4EN->setVisible( bShow );
}

void AnalysisDataView::showVersionInfo(bool bShow)
{
    mLabelRadarVersion->setVisible( bShow );
}

void AnalysisDataView::showAngleInfo(bool bShow)
{
    mLabelAutoALNSts->setVisible( bShow );
    mLabelEstablishedAngle->setVisible( bShow );
}

void AnalysisDataView::frameBreaking()
{
    qWarning() << __FUNCTION__ << __LINE__
               << QString("Radar[%1] Frame Breaking! FAIL TIME: %2")
                  .arg(mRadarID)
                  .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"));
    mLabelMeasurementCount->setStyleSheet("background-color: rgb(200, 0, 0);");
    mTimerFrameBreaking.stop();
}

void AnalysisDataView::updateRadarResetCount(quint64 count, quint8 channel)
{
    //复位次数
    QString resetCountStr = QString::fromLocal8Bit( "%1|%2").arg( count ).arg(channel);
    if( count == 0 ){
        mLabelRadarResetCount->setStyleSheet("");
    }else{
        mLabelRadarResetCount->setStyleSheet("background-color: rgb(255, 0, 0);");
    }
    mLabelRadarResetCount->setText( resetCountStr );
}

void AnalysisDataView::setViewAnalysisTypes(int fType, bool moving, bool continuous, const ViewTypes &types)
{
    switch(fType) {
    case FrameRawTarget:
        mMovingOnlyRaw = moving;
        mContinuousDisplayRaw = continuous;
        mAnalysisTypesRaw = types;
        break;
    case FrameTrackTarget:
        mMovingOnlyTrack = moving;
        mContinuousDisplayTrack = continuous;
        mAnalysisTypesTrack = types;
        break;
    default:
        break;
    }

    foreach (AnalysisDataTableView *view, mAnalysisDataTableViews) {
        QAbstractTableModel *model = view->model();
        if (!model) {
            continue;
        }
        int viewType = view->viewType();
        qDebug() << __FUNCTION__ << __LINE__ << viewType << fType;
        if (FrameTrackTarget == fType && viewType == Frame16Track) {
            ((AnalysisModel*)model)->setViewAnalysisTypes(moving, continuous, types);
        }
        if (FrameRawTarget == fType && viewType == Frame200Raw) {
            ((AnalysisModel*)model)->setViewAnalysisTypes(moving, continuous, types);
        }
        if (viewType != fType) {
            continue;
        }
        switch(viewType) {
        case FrameRawTarget:
        case FrameTrackTarget:
        case Frame200Raw:
        case Frame3Track:
        case Frame16Track:
            ((AnalysisModel*)model)->setViewAnalysisTypes(moving, continuous, types);
        default:
            break;
        }
        view->changeTableView();
    }
}

void AnalysisDataView::viewTypeChanged()
{
    AnalysisDataTableView *tableView = qobject_cast<AnalysisDataTableView *>(sender());
    if (!tableView) {
        return;
    }
    QAbstractTableModel *model = tableView->model();
    if (!model) {
        return;
    }

    int viewType = tableView->viewType();
    switch(viewType) {
    case FrameRawTarget:
        ((AnalysisModel*)model)->setViewAnalysisTypes(mMovingOnlyRaw, mContinuousDisplayRaw, mAnalysisTypesRaw);
        ((AnalysisModel*)model)->targets(mRadarID, viewType, mAnalysisData->mTargets[viewType]);
        connect((AnalysisModel*)model, &AnalysisModel::monitorTarget, this, &AnalysisDataView::monitorTarget);
        break;
    case FrameTrackTarget:
        ((AnalysisModel*)model)->setViewAnalysisTypes(mMovingOnlyTrack, mContinuousDisplayTrack, mAnalysisTypesTrack);
        ((AnalysisModel*)model)->targets(mRadarID, viewType, mAnalysisData->mTargets[viewType]);
        connect((AnalysisModel*)model, &AnalysisModel::monitorTarget, this, &AnalysisDataView::monitorTarget);
        break;
    case FrameAlarm:
        ((AlarmDataModel*)model)->setAlarmData(mAnalysisData->mAlarmData);
        break;
    case FrameEndFrame:
        ((EndFrameModel*)model)->setEndFrameData(mAnalysisData->mEndFrameData);
        break;
    case FrameVehicle:
        ((VehicleDataModel*)model)->setVehicleData(mAnalysisData->mVehicleData);
        break;
    case Frame200Raw:
        ((AnalysisModel*)model)->setViewAnalysisTypes(false, false, mAnalysisTypesRaw);
        ((AnalysisModel*)model)->targets(mRadarID, viewType, mAnalysisData->m200Targets);
        break;
    case Frame3Track:
        ((AnalysisModel*)model)->targets(mRadarID, viewType, mAnalysisData->mLockTargets);
        break;
    case Frame16Track:
        ((AnalysisModel*)model)->setViewAnalysisTypes(false, false, mAnalysisTypesTrack);
        ((AnalysisModel*)model)->targets(mRadarID, viewType, mAnalysisData->m16Targets);
        connect((AnalysisModel*)model, &AnalysisModel::monitorTarget, this, &AnalysisDataView::monitorTarget);
    default:
        break;
    }
    tableView->changeTableView();
}

void AnalysisDataView::changedMonitorTargetID(int frameType, quint16 id)
{
    qDebug() << __FUNCTION__ << __LINE__ << frameType << id;
    foreach (AnalysisDataTableView *view, mAnalysisDataTableViews) {
        QAbstractTableModel *model = view->model();
        int viewType = view->viewType();
        switch(viewType) {
        case FrameRawTarget:
        case FrameTrackTarget:
        case Frame16Track:
            ((AnalysisModel*)model)->changedMonitorTargetID(frameType, id);
        default:
            return;
        }
    }
}

void AnalysisDataView::on_comboBoxRadarID_currentIndexChanged(const QString &arg1)
{
    mRadarID = arg1.toInt(0, 16);
    emit radarIDChanged(mRadarID);
}

void AnalysisDataView::monitoringTrackTarget()
{
    QDialog *dialog = new QDialog(this);
    Qt::WindowFlags flags = Qt::Dialog;
    flags |= Qt::WindowCloseButtonHint | Qt::WindowMaximizeButtonHint | Qt::WindowMinimizeButtonHint;
    dialog->setWindowFlags(flags);
    dialog->setWindowTitle("Target Monitor");
    QVBoxLayout *layout = new QVBoxLayout(dialog);
    TargetsMonitor *targetsMonitor = new TargetsMonitor(dialog);
    connect(this, &AnalysisDataView::monitorTarget,
            targetsMonitor, &TargetsMonitor::monitorTarget);
    connect(targetsMonitor, &TargetsMonitor::monitorTargetIDChanged,
            this, &AnalysisDataView::changedMonitorTargetID);
    layout->addWidget(targetsMonitor);
    dialog->resize(800, 600);
    dialog->show();
}

} // namespace AnalysisView
} // namespace Views

void Views::AnalysisView::AnalysisDataView::setupUi()
{
    connect(actionMonitoringTrackTarget(), &QAction::triggered, this, &AnalysisDataView::monitoringTrackTarget);

    QWidget *widget = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(widget);
    layout->setContentsMargins(0, 0, 0, 0);

    FlowLayout *flowLayoutInfomations = new FlowLayout;
    flowLayoutInfomations->setSpacing(9);
    layout->addLayout(flowLayoutInfomations);

#define NEW_INFO_LABEL(name, text, tips) \
    name = new QLabel(QString::fromLocal8Bit(text), this); \
    name->setToolTip(QString::fromLocal8Bit(tips)); \
    flowLayoutInfomations->addWidget(name);

    NEW_INFO_LABEL(mLabelRadarResetCount, "0|0", "复位次数(次数|通道)");
    NEW_INFO_LABEL(mLabelEstablishedAngle, " 0.00/ 0.00", "安装角(产线安装角/自标定安装角)");
    NEW_INFO_LABEL(mLabelEndFrameTime, "00:00:00.000", "结束帧时间");
    NEW_INFO_LABEL(mLabelMeasurementCount, "    0/    0", "测量计数(RAW/TRACK)");
    NEW_INFO_LABEL(mLabelResponseTaskCycleTime, "  0", "响应任务周期时间");
    NEW_INFO_LABEL(mLabelVehicleGear, "D", "档位");
    NEW_INFO_LABEL(mLabelVehicleSpeedInMeter, "  0.00m/s", "车速(m/s)");
    NEW_INFO_LABEL(mLabelVehicleSpeedInKilometer, "  0.00km/h", "车速(km/h)");
    NEW_INFO_LABEL(mLabelVehicleRadious, "      0.00", "转弯半径");
    NEW_INFO_LABEL(mLabelVehicleYawRate, "   0.00", "横摆角速度");
    NEW_INFO_LABEL(mLabelVehicleTime, "0000-00-00 00:00:00", "车身时间");
    NEW_INFO_LABEL(mTarget16Timestamp, "0000-00-00 00:00:00.000", "16个目标时间");
    NEW_INFO_LABEL(mLabelBlockagePercentRaw, " 0.00%", "遮挡程度(RAW)");
    NEW_INFO_LABEL(mLabelBlockagePercentTrack, " 0.00%", "遮挡程度(TRACK)");
    NEW_INFO_LABEL(mLabelInterferencePercentRaw, " 0.00%", "干扰程度(RAW)");
    NEW_INFO_LABEL(mLabelInterferencePercentTrack, " 0.00%", "干扰程度(TRACK)");
    NEW_INFO_LABEL(mLabelAutoALNSts, "UNKNOWN", "自标定运行状态");
    NEW_INFO_LABEL(mLabelFrequencyHoppingState, "0/0", "调频状态/当前工作模式");
    NEW_INFO_LABEL(mLabelTunnelsceneStateRaw, "0/0", "隧道场景标记位/场景标识(RAW)");
    NEW_INFO_LABEL(mLabelTunnelsceneStateTrack, "0", "隧道场景标记位(Track)");
    NEW_INFO_LABEL(mLabelWaveType, "0", "AB类型(RAW)");
    NEW_INFO_LABEL(mLabelNoiseCurrent, "0", "电流噪声(RAW)");
    NEW_INFO_LABEL(mLabelNoiseGlobal, "0", "全局噪声(RAW)");
    NEW_INFO_LABEL(mLabelTemperature, "0", "雷达温度");


    FlowLayout *flowLayoutAlarms = new FlowLayout;
    flowLayoutAlarms->setSpacing(1);
    layout->addLayout(flowLayoutAlarms);
#define NEW_ALARM_WIDGET(name) \
    mAlarmWidget##name = new AlarmWidget(#name , this); \
    flowLayoutAlarms->addWidget(mAlarmWidget##name);

    NEW_ALARM_WIDGET(ELK);
    NEW_ALARM_WIDGET(BSD);
    NEW_ALARM_WIDGET(LCA);
    NEW_ALARM_WIDGET(DOWR);
    NEW_ALARM_WIDGET(DOWF);
    NEW_ALARM_WIDGET(FCTA);
    NEW_ALARM_WIDGET(FCTB);
    NEW_ALARM_WIDGET(RCTA);
    NEW_ALARM_WIDGET(RCTB);
    NEW_ALARM_WIDGET(RCW);
    NEW_ALARM_WIDGET(JA);

    mLabelAlarmTTC = new QLabel("TTC", this);
    flowLayoutAlarms->addWidget(mLabelAlarmTTC);

    mLabelAlarmDDCI = new QLabel("DDCI", this);
    flowLayoutAlarms->addWidget(mLabelAlarmDDCI);

    QVBoxLayout *layoutError = new QVBoxLayout();
    mLabelErrorInformation0x4DN = new QLabel("0x4DN:", this);
    layoutError->addWidget(mLabelErrorInformation0x4DN);
    mLabelErrorInformation0x4EN = new QLabel("0x4EN:", this);
    layoutError->addWidget(mLabelErrorInformation0x4EN);
    layout->addLayout(layoutError);

    FlowLayout *flowLayoutOthers = new FlowLayout;
    flowLayoutOthers->setSpacing(1);
    layout->addLayout(flowLayoutOthers);

    mLabelRadarVersion = new QLabel("0.0.0.0", this);
    flowLayoutOthers->addWidget(mLabelRadarVersion);
    mLabelFrameCount = new QLabel(QString::fromLocal8Bit("帧统计"), this);
    flowLayoutOthers->addWidget(mLabelFrameCount);

    addTopWidget(widget);

//    ui->splitter->setStretchFactor(0, 19);
//    ui->splitter->setStretchFactor(1, 1);
    mLabelFrameCount->hide();
}
