﻿#include "generalsettings.h"
#include "ui_generalsettings.h"

#include <QFileDialog>
#include <QDebug>

QVariant GeneralSettings::Settings::getSettings() const
{
    QMap<QString, QVariant> config;
    config["FileSaveFrameCount"] = mFileSaveFrameCount;
    config["HozonBreakShort"]                 = mHozonBreakShort;
    config["CalculateEarlyWarningScriptFile"] = mEarlyWarningSettings.mCalculateEarlyWarningScriptFile;
    config["ScriptFileCalculateEarlyWarning"] = mEarlyWarningSettings.mScriptFileCalculateEarlyWarning;

    config["AngleCompensationRaw4"]   = mAngleCompensation[4].mAngleCompensationRaw;
    config["AngleCompensationRaw5"]   = mAngleCompensation[5].mAngleCompensationRaw;
    config["AngleCompensationRaw6"]   = mAngleCompensation[6].mAngleCompensationRaw;
    config["AngleCompensationRaw7"]   = mAngleCompensation[7].mAngleCompensationRaw;
    config["AngleCompensationTrack4"] = mAngleCompensation[4].mAngleCompensationTrack;
    config["AngleCompensationTrack5"] = mAngleCompensation[5].mAngleCompensationTrack;
    config["AngleCompensationTrack6"] = mAngleCompensation[6].mAngleCompensationTrack;
    config["AngleCompensationTrack7"] = mAngleCompensation[7].mAngleCompensationTrack;
    config["UseRadarOffsetToSide4"] = mAngleCompensation[4].mUseRadarOffsetToSide;
    config["UseRadarOffsetToSide5"] = mAngleCompensation[5].mUseRadarOffsetToSide;
    config["UseRadarOffsetToSide6"] = mAngleCompensation[6].mUseRadarOffsetToSide;
    config["UseRadarOffsetToSide7"] = mAngleCompensation[7].mUseRadarOffsetToSide;


    config["BYDRaw600ByChannel"] = mBYDRaw600ByChannel;
    config["BYDHDChannelRadarID1"] = mBYDHDChannelRadarID[0];
    config["BYDHDChannelRadarID2"] = mBYDHDChannelRadarID[1];
    config["BYDHDChannelRadarID3"] = mBYDHDChannelRadarID[2];
    config["BYDHDChannelRadarID4"] = mBYDHDChannelRadarID[3];
    config["BYDHDChannelRadarID5"] = mBYDHDChannelRadarID[4];
    config["BYDHDChannelRadarID6"] = mBYDHDChannelRadarID[5];
    config["BYDHDChannelRadarID7"] = mBYDHDChannelRadarID[6];
    config["BYDHDChannelRadarID8"] = mBYDHDChannelRadarID[7];

    config["GEELYChannelRadarID1"] = mGEELYChannelRadarID[0];
    config["GEELYChannelRadarID2"] = mGEELYChannelRadarID[1];

    return config;
}

bool GeneralSettings::Settings::setSettings(const QVariant &settings)
{
    QMap<QString, QVariant> config = settings.toMap();

#define VALUE_UINT(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toUInt() : DEFAULT
#define VALUE_DOUBLE(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toDouble() : DEFAULT
#define VALUE_BOOL(KEY, DEFAULT) config.find(KEY) != config.end() ? config[KEY].toBool() : DEFAULT

    mFileSaveFrameCount = config["FileSaveFrameCount"].toInt();
    if (mFileSaveFrameCount < 100 || mFileSaveFrameCount > 100000) {
        mFileSaveFrameCount = 48000;
    }
    mHozonBreakShort                 = config["HozonBreakShort"].toBool();
    mEarlyWarningSettings.mCalculateEarlyWarningScriptFile = config["CalculateEarlyWarningScriptFile"].toString();
    mEarlyWarningSettings.mScriptFileCalculateEarlyWarning = config["ScriptFileCalculateEarlyWarning"].toBool();

    mAngleCompensation[4].mAngleCompensationRaw   = VALUE_DOUBLE("AngleCompensationRaw4", 0);
    mAngleCompensation[5].mAngleCompensationRaw   = VALUE_DOUBLE("AngleCompensationRaw5", 0);
    mAngleCompensation[6].mAngleCompensationRaw   = VALUE_DOUBLE("AngleCompensationRaw6", 0);
    mAngleCompensation[7].mAngleCompensationRaw   = VALUE_DOUBLE("AngleCompensationRaw7", 0);
    mAngleCompensation[4].mAngleCompensationTrack = VALUE_DOUBLE("AngleCompensationTrack4", 0);
    mAngleCompensation[5].mAngleCompensationTrack = VALUE_DOUBLE("AngleCompensationTrack5", 0);
    mAngleCompensation[6].mAngleCompensationTrack = VALUE_DOUBLE("AngleCompensationTrack6", 0);
    mAngleCompensation[7].mAngleCompensationTrack = VALUE_DOUBLE("AngleCompensationTrack7", 0);
    mAngleCompensation[4].mUseRadarOffsetToSide = VALUE_BOOL("UseRadarOffsetToSide4", true);
    mAngleCompensation[5].mUseRadarOffsetToSide = VALUE_BOOL("UseRadarOffsetToSide5", true);
    mAngleCompensation[6].mUseRadarOffsetToSide = VALUE_BOOL("UseRadarOffsetToSide6", true);
    mAngleCompensation[7].mUseRadarOffsetToSide = VALUE_BOOL("UseRadarOffsetToSide7", true);

    mBYDRaw600ByChannel     = VALUE_BOOL("BYDRaw600ByChannel", false);
    mBYDHDChannelRadarID[0] = VALUE_UINT("BYDHDChannelRadarID1", 4);
    mBYDHDChannelRadarID[1] = VALUE_UINT("BYDHDChannelRadarID2", 5);
    mBYDHDChannelRadarID[2] = VALUE_UINT("BYDHDChannelRadarID3", 6);
    mBYDHDChannelRadarID[3] = VALUE_UINT("BYDHDChannelRadarID4", 7);
    mBYDHDChannelRadarID[4] = VALUE_UINT("BYDHDChannelRadarID5", 0);
    mBYDHDChannelRadarID[5] = VALUE_UINT("BYDHDChannelRadarID6", 0);
    mBYDHDChannelRadarID[6] = VALUE_UINT("BYDHDChannelRadarID7", 0);
    mBYDHDChannelRadarID[7] = VALUE_UINT("BYDHDChannelRadarID8", 0);


    mGEELYChannelRadarID[0] = VALUE_UINT("GEELYChannelRadarID1", 0);
    mGEELYChannelRadarID[1] = VALUE_UINT("GEELYChannelRadarID2", 1);

    return true;
}

GeneralSettings::GeneralSettings(Settings *settings, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::GeneralSettings),
    mSettings(settings)
{
    ui->setupUi(this);

    ui->lineEditFileSaveFrameCount->setText(QString::number(mSettings->mFileSaveFrameCount));
    ui->checkBoxHozonBreakShort->setChecked(mSettings->mHozonBreakShort);

    ui->lineEditAlarmScript->setText(mSettings->mEarlyWarningSettings.mCalculateEarlyWarningScriptFile);
    ui->checkBoxAlarmScriptEnable->setChecked(mSettings->mEarlyWarningSettings.mScriptFileCalculateEarlyWarning);

    ui->lineEditAngleCompensationRaw4->setText(QString::number(  mSettings->mAngleCompensation[4].mAngleCompensationRaw  , 'f', 2));
    ui->lineEditAngleCompensationRaw5->setText(QString::number(  mSettings->mAngleCompensation[5].mAngleCompensationRaw  , 'f', 2));
    ui->lineEditAngleCompensationRaw6->setText(QString::number(  mSettings->mAngleCompensation[6].mAngleCompensationRaw  , 'f', 2));
    ui->lineEditAngleCompensationRaw7->setText(QString::number(  mSettings->mAngleCompensation[7].mAngleCompensationRaw  , 'f', 2));
    ui->lineEditAngleCompensationTrack4->setText(QString::number(mSettings->mAngleCompensation[4].mAngleCompensationTrack, 'f', 2));
    ui->lineEditAngleCompensationTrack5->setText(QString::number(mSettings->mAngleCompensation[5].mAngleCompensationTrack, 'f', 2));
    ui->lineEditAngleCompensationTrack6->setText(QString::number(mSettings->mAngleCompensation[6].mAngleCompensationTrack, 'f', 2));
    ui->lineEditAngleCompensationTrack7->setText(QString::number(mSettings->mAngleCompensation[7].mAngleCompensationTrack, 'f', 2));
    ui->checkBoxUseRadarOffsetToSide4->setChecked(mSettings->mAngleCompensation[4].mUseRadarOffsetToSide);
    ui->checkBoxUseRadarOffsetToSide5->setChecked(mSettings->mAngleCompensation[5].mUseRadarOffsetToSide);
    ui->checkBoxUseRadarOffsetToSide6->setChecked(mSettings->mAngleCompensation[6].mUseRadarOffsetToSide);
    ui->checkBoxUseRadarOffsetToSide7->setChecked(mSettings->mAngleCompensation[7].mUseRadarOffsetToSide);

    ui->checkBoxBYDRaw600ByChannel->setChecked(mSettings->mBYDRaw600ByChannel);
    ui->spinBoxBYDHDChannel_1->setValue(mSettings->mBYDHDChannelRadarID[0]);
    ui->spinBoxBYDHDChannel_2->setValue(mSettings->mBYDHDChannelRadarID[1]);
    ui->spinBoxBYDHDChannel_3->setValue(mSettings->mBYDHDChannelRadarID[2]);
    ui->spinBoxBYDHDChannel_4->setValue(mSettings->mBYDHDChannelRadarID[3]);
    ui->spinBoxBYDHDChannel_5->setValue(mSettings->mBYDHDChannelRadarID[4]);
    ui->spinBoxBYDHDChannel_6->setValue(mSettings->mBYDHDChannelRadarID[5]);
    ui->spinBoxBYDHDChannel_7->setValue(mSettings->mBYDHDChannelRadarID[6]);
    ui->spinBoxBYDHDChannel_8->setValue(mSettings->mBYDHDChannelRadarID[7]);

    ui->comboBoxGEELYChannel_1->setCurrentIndex(mSettings->mGEELYChannelRadarID[0]);
    ui->comboBoxGEELYChannel_2->setCurrentIndex(mSettings->mGEELYChannelRadarID[1]);
}

GeneralSettings::~GeneralSettings()
{
    delete ui;
}

void GeneralSettings::on_pushButtonApply_clicked()
{
    mSettings->mFileSaveFrameCount = ui->lineEditFileSaveFrameCount->text().toUInt();
    if (mSettings->mFileSaveFrameCount < 100 || mSettings->mFileSaveFrameCount > 100000) {
        mSettings->mFileSaveFrameCount = 48000;
    }
    mSettings->mHozonBreakShort = ui->checkBoxHozonBreakShort->isChecked();

    mSettings->mEarlyWarningSettings.mCalculateEarlyWarningScriptFile = ui->lineEditAlarmScript->text();
    mSettings->mEarlyWarningSettings.mScriptFileCalculateEarlyWarning = ui->checkBoxAlarmScriptEnable->isChecked();

    mSettings->mAngleCompensation[4].mAngleCompensationRaw   = ui->lineEditAngleCompensationRaw4->text().toDouble();
    mSettings->mAngleCompensation[5].mAngleCompensationRaw   = ui->lineEditAngleCompensationRaw5->text().toDouble();
    mSettings->mAngleCompensation[6].mAngleCompensationRaw   = ui->lineEditAngleCompensationRaw6->text().toDouble();
    mSettings->mAngleCompensation[7].mAngleCompensationRaw   = ui->lineEditAngleCompensationRaw7->text().toDouble();
    mSettings->mAngleCompensation[4].mAngleCompensationTrack = ui->lineEditAngleCompensationTrack4->text().toDouble();
    mSettings->mAngleCompensation[5].mAngleCompensationTrack = ui->lineEditAngleCompensationTrack5->text().toDouble();
    mSettings->mAngleCompensation[6].mAngleCompensationTrack = ui->lineEditAngleCompensationTrack6->text().toDouble();
    mSettings->mAngleCompensation[7].mAngleCompensationTrack = ui->lineEditAngleCompensationTrack7->text().toDouble();
    mSettings->mAngleCompensation[4].mUseRadarOffsetToSide = ui->checkBoxUseRadarOffsetToSide7->isChecked();
    mSettings->mAngleCompensation[5].mUseRadarOffsetToSide = ui->checkBoxUseRadarOffsetToSide7->isChecked();
    mSettings->mAngleCompensation[6].mUseRadarOffsetToSide = ui->checkBoxUseRadarOffsetToSide7->isChecked();
    mSettings->mAngleCompensation[7].mUseRadarOffsetToSide = ui->checkBoxUseRadarOffsetToSide7->isChecked();


    mSettings->mBYDRaw600ByChannel = ui->checkBoxBYDRaw600ByChannel->isChecked();
    mSettings->mBYDHDChannelRadarID[0] = ui->spinBoxBYDHDChannel_1->value();
    mSettings->mBYDHDChannelRadarID[1] = ui->spinBoxBYDHDChannel_2->value();
    mSettings->mBYDHDChannelRadarID[2] = ui->spinBoxBYDHDChannel_3->value();
    mSettings->mBYDHDChannelRadarID[3] = ui->spinBoxBYDHDChannel_4->value();
    mSettings->mBYDHDChannelRadarID[4] = ui->spinBoxBYDHDChannel_5->value();
    mSettings->mBYDHDChannelRadarID[5] = ui->spinBoxBYDHDChannel_6->value();
    mSettings->mBYDHDChannelRadarID[6] = ui->spinBoxBYDHDChannel_7->value();
    mSettings->mBYDHDChannelRadarID[7] = ui->spinBoxBYDHDChannel_8->value();


    mSettings->mGEELYChannelRadarID[0] = ui->comboBoxGEELYChannel_1->currentIndex();
    mSettings->mGEELYChannelRadarID[1] = ui->comboBoxGEELYChannel_2->currentIndex();

    emit apply();
}

void GeneralSettings::on_pushButtonOK_clicked()
{
    on_pushButtonApply_clicked();
    close();
}

void GeneralSettings::on_pushButtonAlarmScript_clicked()
{
    QString file = QFileDialog::getOpenFileName(this,
                                                QString::fromLocal8Bit("选择告警处理脚本"),
                                                ui->lineEditAlarmScript->text(),
                                                "Lua (*.lua)");
    if (file.isEmpty()) {
        return;
    }

    ui->lineEditAlarmScript->setText(file);
}
