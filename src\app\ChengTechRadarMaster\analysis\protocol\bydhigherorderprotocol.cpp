﻿#include "bydhigherorderprotocol.h"

#include <QtMath>

namespace Analysis {
namespace Protocol {

static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
    uint32_t mask = ((1 << bits) - 1);
    uint32_t extracted = data & mask;
    int32_t sign_extended = (extracted & (1 << (bits - 1))) ? (int)(extracted | (~mask)) : (int)extracted;
    return sign_extended;
}

BYDHOProtocol::BYDHOProtocol( AnalysisWorker *analysisWorker, QObject *parent )
    : IAnalysisProtocol( analysisWorker, parent )
{
    mProtocolType = ProtocolBYDHO;
}

void BYDHOProtocol::setChannelRadarID(int *channelRadarID, int size)
{
    for (int i = 0; i < size && i < (sizeof (mBYDHDChannelRadarID) / sizeof (mBYDHDChannelRadarID[0])); ++i) {
        mBYDHDChannelRadarID[i] = channelRadarID[i];
        qDebug() << __FUNCTION__ << __LINE__ << channelRadarID[i] << mBYDHDChannelRadarID[i];
    }
}

bool BYDHOProtocol::analysisFrame(const Devices::Can::CanFrame &frame)
{
    int radarID = 4;

    if (frame.channelIndex() >= 5) {
        return false;
    }
    radarID = mBYDHDChannelRadarID[frame.channelIndex()];
    if (radarID < 4 || radarID > 7) {
        return false;
    }

    bool ret = false;
    switch (frame.id())
    {
    case 0x640:
        clearRawTrack(radarID, frame);
    case 0x641:
    case 0x642:
    case 0x643:
    case 0x644:
    case 0x645:
    case 0x646:
    case 0x647:
    case 0x648:
    case 0x649:
    case 0x64A:
    case 0x64B:
    case 0x64C:
    case 0x64D:
    case 0x64E:
    case 0x64F:
    case 0x650:
    case 0x651:
    case 0x652:
    case 0x653:
    case 0x654:
    case 0x655:
    case 0x656:
    case 0x657:
    case 0x658:
        ret = decode_CR_FL_0x640(radarID, frame);
        break;
    case 0x659:
        clear16Track(radarID, frame);
    case 0x65A:
    case 0x65B:
    case 0x65C:
    case 0x65D:
    case 0x65E:
    case 0x65F:
    case 0x660:
    case 0x661:
    case 0x662:
    case 0x663:
    case 0x664:
    case 0x665:
    case 0x666:
    case 0x667:
    case 0x668:
    case 0x669:
    case 0x66A:
    case 0x66B:
    case 0x66C:
        ret = decode_CR_FL_0x659(radarID, frame);
        break;
    case 0x66D:
        ret = decode_CR_FL_0x66D(radarID, frame);
        break;
    case 0x66E:
        ret = decode_CR_FL_0x66E(radarID, frame);
        break;
    default:
        return false;
    }

//    qDebug() << __FUNCTION__ << __LINE__ << frame.length() << frame.idHex() << frame.dataHex();
    return ret;
}

bool BYDHOProtocol::clearRawTrack(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
        return false;
    }

    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[radarID];
    Targets &targets = analysisData->m200Targets;
    targets.clear();
//    qDebug() << __FUNCTION__ << __LINE__ << targets.mTargetCount;

    return true;
}

bool BYDHOProtocol::clear16Track(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
        return false;
    }

    mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.clear();

    return true;
}

bool BYDHOProtocol::decode_CR_FL_0x640(quint8 radarID, const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << frame.idHex() << frame.dataHex();
    if (frame.length() != 64) {
        return false;
    }

    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[radarID];
    Targets &targets = analysisData->m200Targets;

    const uint8_t *data = (const uint8_t *)frame.data().data();

    int &targetCount = targets.mTargetCount; // 注意必须使用引用
    int id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
//    target.Checksum_0x640_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    target.Counter_0x640_S = (data[2]);

    float range = (((data[3] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//(((data[3] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.0625);
        target.mV = (((((data[4] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0x7FU) << 4)) * 0.1) - 120);
        target.mAngle = (((((data[5] & 0x80U) >> 7) + (((uint32_t)data[6]) << 1) + (((uint32_t)data[7] & 0x1U) << 9)) * 0.003) - 1.536);
        target.mRCS = (((((data[7] & 0xFEU) >> 1) + (((uint16_t)data[8] & 0x3U) << 7)) * 0.2) - 51.1);
        target.mSNR = (((data[8] & 0xFCU) >> 2) + (((uint16_t)data[9] & 0x1U) << 6));
        target.mDynamicProperty = ((data[9] & 0x6U) >> 1);
        target.mMeasQuality = ((data[9] & 0x18U) >> 3);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;

//        qDebug() << __FUNCTION__ << __LINE__ << target.mID << target.mDynamicProperty << target.mRange << target.mAngle;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[9] & 0xE0U) >> 5) + (((uint32_t)data[10]) << 3) + (((uint32_t)data[11] & 0x1U) << 11)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[9] & 0xE0U) >> 5) + (((uint32_t)data[10]) << 3) + (((uint32_t)data[11] & 0x1U) << 11)) * 0.0625);
        target.mV = (((((data[11] & 0xFEU) >> 1) + (((uint16_t)data[12] & 0xFU) << 7)) * 0.1) - 120);
        target.mAngle = (((((data[12] & 0xF0U) >> 4) + (((uint16_t)data[13] & 0x3FU) << 4)) * 0.003) - 1.536);
        target.mRCS = (((((data[13] & 0xC0U) >> 6) + (((uint16_t)data[14] & 0x7FU) << 2)) * 0.2) - 51.1);
        target.mSNR = (((data[14] & 0x80U) >> 7) + (((uint16_t)data[15] & 0x3FU) << 1));
        target.mDynamicProperty = ((data[15] & 0xC0U) >> 6);
        target.mMeasQuality = (data[16] & 0x3U);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[16] & 0xFCU) >> 2) + (((uint16_t)data[17] & 0x3FU) << 6)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[16] & 0xFCU) >> 2) + (((uint16_t)data[17] & 0x3FU) << 6)) * 0.0625);
        target.mV = (((((data[17] & 0xC0U) >> 6) + (((uint32_t)data[18]) << 2) + (((uint32_t)data[19] & 0x1U) << 10)) * 0.1) - 120);
        target.mAngle = (((((data[19] & 0xFEU) >> 1) + (((uint16_t)data[20] & 0x7U) << 7)) * 0.003) - 1.536);
        target.mRCS = (((((data[20] & 0xF8U) >> 3) + (((uint16_t)data[21] & 0xFU) << 5)) * 0.2) - 51.1);
        target.mSNR = (((data[21] & 0xF0U) >> 4) + (((uint16_t)data[22] & 0x7U) << 4));
        target.mDynamicProperty = ((data[22] & 0x18U) >> 3);
        target.mMeasQuality = ((data[22] & 0x60U) >> 5);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;

//        qDebug() << __FUNCTION__ << __LINE__ << target.mID << target.mDynamicProperty << target.mRange << target.mAngle;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[22] & 0x80U) >> 7) + (((uint32_t)data[23]) << 1) + (((uint32_t)data[24] & 0x7U) << 9)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[22] & 0x80U) >> 7) + (((uint32_t)data[23]) << 1) + (((uint32_t)data[24] & 0x7U) << 9)) * 0.0625);
        target.mV = (((((data[24] & 0xF8U) >> 3) + (((uint16_t)data[25] & 0x3FU) << 5)) * 0.1) - 120);
        target.mAngle = (((((data[25] & 0xC0U) >> 6) + (((uint16_t)data[26] & 0xFFU) << 2)) * 0.003) - 1.536);
        target.mRCS = ((((data[27] & 0xFFU) + (((uint16_t)data[28] & 0x1U) << 8)) * 0.2) - 51.1);
        target.mSNR = ((data[28] & 0xFEU) >> 1);
        target.mDynamicProperty = (data[29] & 0x3U);
        target.mMeasQuality = ((data[29] & 0xCU) >> 2);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[29] & 0xF0U) >> 4) + (((uint16_t)data[30] & 0xFFU) << 4)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[29] & 0xF0U) >> 4) + (((uint16_t)data[30] & 0xFFU) << 4)) * 0.0625);
        target.mV = ((((data[31] & 0xFFU) + (((uint16_t)data[32] & 0x7U) << 8)) * 0.1) - 120);
        target.mAngle = (((((data[32] & 0xF8U) >> 3) + (((uint16_t)data[33] & 0x1FU) << 5)) * 0.003) - 1.536);
        target.mRCS = (((((data[33] & 0xE0U) >> 5) + (((uint16_t)data[34] & 0x3FU) << 3)) * 0.2) - 51.1);
        target.mSNR = (((data[34] & 0xC0U) >> 6) + (((uint16_t)data[35] & 0x1FU) << 2));
        target.mDynamicProperty = ((data[35] & 0x60U) >> 5);
        target.mMeasQuality = (((data[35] & 0x80U) >> 7) + (((uint16_t)data[36] & 0x1U) << 1));

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[36] & 0xFEU) >> 1) + (((uint16_t)data[37] & 0x1FU) << 7)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[36] & 0xFEU) >> 1) + (((uint16_t)data[37] & 0x1FU) << 7)) * 0.0625);
        target.mV = (((((data[37] & 0xE0U) >> 5) + (((uint16_t)data[38] & 0xFFU) << 3)) * 0.1) - 120);
        target.mAngle = ((((data[39] & 0xFFU) + (((uint16_t)data[40] & 0x3U) << 8)) * 0.003) - 1.536);
        target.mRCS = (((((data[40] & 0xFCU) >> 2) + (((uint16_t)data[41] & 0x7U) << 6)) * 0.2) - 51.1);
        target.mSNR = (((data[41] & 0xF8U) >> 3) + (((uint16_t)data[42] & 0x3U) << 5));
        target.mDynamicProperty = ((data[42] & 0xCU) >> 2);
        target.mMeasQuality = ((data[42] & 0x30U) >> 4);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[42] & 0xC0U) >> 6) + (((uint32_t)data[43]) << 2) + (((uint32_t)data[44] & 0x3U) << 10)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;

        target.mRange = range;//((((data[42] & 0xC0U) >> 6) + (((uint32_t)data[43]) << 2) + (((uint32_t)data[44] & 0x3U) << 10)) * 0.0625);
        target.mV = (((((data[44] & 0xFCU) >> 2) + (((uint16_t)data[45] & 0x1FU) << 6)) * 0.1) - 120);
        target.mAngle = (((((data[45] & 0xE0U) >> 5) + (((uint16_t)data[46] & 0x7FU) << 3)) * 0.003) - 1.536);
        target.mRCS = (((((data[46] & 0x80U) >> 7) + (((uint16_t)data[47] & 0xFFU) << 1)) * 0.2) - 51.1);
        target.mSNR = (data[48] & 0x7FU);
        target.mDynamicProperty = (((data[48] & 0x80U) >> 7) + (((uint16_t)data[49] & 0x1U) << 1));
        target.mMeasQuality = ((data[49] & 0x6U) >> 1);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[49] & 0xF8U) >> 3) + (((uint16_t)data[50] & 0x7FU) << 5)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[49] & 0xF8U) >> 3) + (((uint16_t)data[50] & 0x7FU) << 5)) * 0.0625);
        target.mV = (((((data[50] & 0x80U) >> 7) + (((uint32_t)data[51]) << 1) + (((uint32_t)data[52] & 0x3U) << 9)) * 0.1) - 120);
        target.mAngle = (((((data[52] & 0xFCU) >> 2) + (((uint16_t)data[53] & 0xFU) << 6)) * 0.003) - 1.536);
        target.mRCS = (((((data[53] & 0xF0U) >> 4) + (((uint16_t)data[54] & 0x1FU) << 4)) * 0.2) - 51.1);
        target.mSNR = (((data[54] & 0xE0U) >> 5) + (((uint16_t)data[55] & 0xFU) << 3));
        target.mDynamicProperty = ((data[55] & 0x30U) >> 4);
        target.mMeasQuality = ((data[55] & 0xC0U) >> 6);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }
    return true;
}

bool BYDHOProtocol::decode_CR_FL_0x640_MRR(quint8 radarID, const Devices::Can::CanFrame &frame)
{
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << frame.idHex() << frame.dataHex();
    if (frame.length() != 64) {
        return false;
    }

    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[radarID];
    Targets &targets = analysisData->m200Targets;

    const uint8_t *data = (const uint8_t *)frame.data().data();

    int &targetCount = targets.mTargetCount; // 注意必须使用引用
    int id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }
//    target.Checksum_601_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    target.Counter_601_S = (data[2]);


    float range = (((data[3] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//(((data[3] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.0625);
        target.mV = (((((data[4] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0x7FU) << 4)) * 0.1) - 120);
        target.mAngle = (((((data[5] & 0x80U) >> 7) + (((uint32_t)data[6]) << 1) + (((uint32_t)data[7] & 0x1U) << 9)) * 0.003) - 1.536);
        target.mPitchAngle = ((((data[7] & 0xFEU) >> 1) * 0.005) - 0.315);
        target.mRCS = ((((data[8] & 0xFFU) + (((uint16_t)data[9] & 0x1U) << 8)) * 0.2) - 51.1);
        target.mSNR = ((data[9] & 0xFEU) >> 1);
        target.mDynamicProperty = (data[10] & 0x3U);
        target.mMeasQuality = ((data[10] & 0xCU) >> 2);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;

//        qDebug() << __FUNCTION__ << __LINE__ << target.mID << target.mDynamicProperty << target.mRange << target.mAngle;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[10] & 0xF0U) >> 4) + (((uint16_t)data[11] & 0xFFU) << 4)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[10] & 0xF0U) >> 4) + (((uint16_t)data[11] & 0xFFU) << 4)) * 0.0625);
        target.mV = ((((data[12] & 0xFFU) + (((uint16_t)data[13] & 0x7U) << 8)) * 0.1) - 120);
        target.mAngle = (((((data[13] & 0xF8U) >> 3) + (((uint16_t)data[14] & 0x1FU) << 5)) * 0.003) - 1.536);
        target.mPitchAngle = (((((data[14] & 0xE0U) >> 5) + (((uint16_t)data[15] & 0xFU) << 3)) * 0.005) - 0.315);
        target.mRCS = (((((data[15] & 0xF0U) >> 4) + (((uint16_t)data[16] & 0x1FU) << 4)) * 0.2) - 51.1);
        target.mSNR = ((((data[16] & 0xE0U) >> 5) + (((uint16_t)data[17] & 0xFU) << 3)));
        target.mDynamicProperty = ((data[17] & 0x30U) >> 4);
        target.mMeasQuality = ((data[17] & 0xC0U) >> 6);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = (((data[18] & 0xFFU) + (((uint16_t)data[19] & 0xFU) << 8)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;(((data[18] & 0xFFU) + (((uint16_t)data[19] & 0xFU) << 8)) * 0.0625);
        target.mV = (((((data[19] & 0xF0U) >> 4) + (((uint16_t)data[20] & 0x7FU) << 4)) * 0.1) - 120);
        target.mAngle = (((((data[20] & 0x80U) >> 7) + (((uint32_t)data[21]) << 1) + (((uint32_t)data[22] & 0x1U) << 9)) * 0.003) - 1.536);
        target.mPitchAngle = ((((data[22] & 0xFEU) >> 1) * 0.005) - 0.315);
        target.mRCS = ((((data[23] & 0xFFU) + (((uint16_t)data[24] & 0x1U) << 8)) * 0.2) - 51.1);
        target.mSNR = ((data[24] & 0xFEU) >> 1);
        target.mDynamicProperty = (data[25] & 0x3U);
        target.mMeasQuality = ((data[25] & 0xCU) >> 2);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;

//        qDebug() << __FUNCTION__ << __LINE__ << target.mID << target.mDynamicProperty << target.mRange << target.mAngle;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[25] & 0xF0U) >> 4) + (((uint16_t)data[26] & 0xFFU) << 4)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[25] & 0xF0U) >> 4) + (((uint16_t)data[26] & 0xFFU) << 4)) * 0.0625);
        target.mV = ((((data[27] & 0xFFU) + (((uint16_t)data[28] & 0x7U) << 8)) * 0.1) - 120);
        target.mAngle = (((((data[28] & 0xF8U) >> 3) + (((uint16_t)data[29] & 0x1FU) << 5)) * 0.003) - 1.536);
        target.mPitchAngle = (((((data[29] & 0xE0U) >> 5) + (((uint16_t)data[30] & 0xFU) << 3)) * 0.005) - 0.315);
        target.mRCS = (((((data[30] & 0xF0U) >> 4) + (((uint16_t)data[31] & 0x1FU) << 4)) * 0.2) - 51.1);
        target.mSNR = (((data[31] & 0xE0U) >> 5) + (((uint16_t)data[32] & 0xFU) << 3));
        target.mDynamicProperty = ((data[32] & 0x30U) >> 4);
        target.mMeasQuality = ((data[32] & 0xC0U) >> 6);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = (((data[33] & 0xFFU) + (((uint16_t)data[34] & 0xFU) << 8)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//(((data[33] & 0xFFU) + (((uint16_t)data[34] & 0xFU) << 8)) * 0.0625);
        target.mV = (((((data[34] & 0xF0U) >> 4) + (((uint16_t)data[35] & 0x7FU) << 4)) * 0.1) - 120);
        target.mAngle = (((((data[35] & 0x80U) >> 7) + (((uint32_t)data[36]) << 1) + (((uint32_t)data[37] & 0x1U) << 9)) * 0.003) - 1.536);
        target.mPitchAngle = ((((data[37] & 0xFEU) >> 1) * 0.005) - 0.315);
        target.mRCS = ((((data[38] & 0xFFU) + (((uint16_t)data[39] & 0x1U) << 8)) * 0.2) - 51.1);
        target.mSNR = ((data[39] & 0xFEU) >> 1);
        target.mDynamicProperty = (data[40] & 0x3U);
        target.mMeasQuality = ((data[40] & 0xCU) >> 2);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[40] & 0xF0U) >> 4) + (((uint16_t)data[41] & 0xFFU) << 4)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[40] & 0xF0U) >> 4) + (((uint16_t)data[41] & 0xFFU) << 4)) * 0.0625);
        target.mV = ((((data[42] & 0xFFU) + (((uint16_t)data[43] & 0x7U) << 8)) * 0.1) - 120);
        target.mAngle = (((((data[43] & 0xF8U) >> 3) + (((uint16_t)data[44] & 0x1FU) << 5)) * 0.003) - 1.536);
        target.mPitchAngle = (((((data[44] & 0xE0U) >> 5) + (((uint16_t)data[45] & 0xFU) << 3)) * 0.005) - 0.315);
        target.mRCS = (((((data[45] & 0xF0U) >> 4) + (((uint16_t)data[46] & 0x1FU) << 4)) * 0.2) - 51.1);
        target.mSNR = (((data[46] & 0xE0U) >> 5) + (((uint16_t)data[47] & 0xFU) << 3));
        target.mDynamicProperty = ((data[47] & 0x30U) >> 4);
        target.mMeasQuality = ((data[47] & 0xC0U) >> 6);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = (((data[48] & 0xFFU) + (((uint16_t)data[49] & 0xFU) << 8)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//(((data[48] & 0xFFU) + (((uint16_t)data[49] & 0xFU) << 8)) * 0.0625);
        target.mV = (((((data[49] & 0xF0U) >> 4) + (((uint16_t)data[50] & 0x7FU) << 4)) * 0.1) - 120);
        target.mAngle = (((((data[50] & 0x80U) >> 7) + (((uint32_t)data[51]) << 1) + (((uint32_t)data[52] & 0x1U) << 9)) * 0.003) - 1.536);
        target.mPitchAngle = ((((data[52] & 0xFEU) >> 1) * 0.005) - 0.315);
        target.mRCS = ((((data[53] & 0xFFU) + (((uint16_t)data[54] & 0x1U) << 8)) * 0.2) - 51.1);
        target.mSNR = ((data[54] & 0xFEU) >> 1);
        target.mDynamicProperty = (data[55] & 0x3U);
        target.mMeasQuality = ((data[55] & 0xCU) >> 2);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }

    id = targetCount;
    if (id >= MAX_TARGET_COUNT || targetCount >= MAX_TARGET_COUNT) {
        qDebug() << __FUNCTION__ << __LINE__ << "Raw target id error!" << id << targetCount << " >= " << MAX_TARGET_COUNT << frame.idHex() << frame.dataHex();
        return true;
    }

    range = ((((data[55] & 0xF0U) >> 4) + (((uint16_t)data[56] & 0xFFU) << 4)) * 0.0625);
    if (range > 0.0f ) {
        Target &target = targets.mTargets[targetCount++];
        target.mID = id;
        target.mRange = range;//((((data[55] & 0xF0U) >> 4) + (((uint16_t)data[56] & 0xFFU) << 4)) * 0.0625);
        target.mV = ((((data[57] & 0xFFU) + (((uint16_t)data[58] & 0x7U) << 8)) * 0.1) - 120);
        target.mAngle = (((((data[58] & 0xF8U) >> 3) + (((uint16_t)data[59] & 0x1FU) << 5)) * 0.003) - 1.536);
        target.mPitchAngle = (((((data[59] & 0xE0U) >> 5) + (((uint16_t)data[60] & 0xFU) << 3)) * 0.005) - 0.315);
        target.mRCS = (((((data[60] & 0xF0U) >> 4) + (((uint16_t)data[61] & 0x1FU) << 4)) * 0.2) - 51.1);
        target.mSNR = (((data[61] & 0xE0U) >> 5) + (((uint16_t)data[62] & 0xFU) << 3));
        target.mDynamicProperty = ((data[62] & 0x30U) >> 4);
        target.mMeasQuality = ((data[62] & 0xC0U) >> 6);

        target.mProtocolType = ProtocolBYDHO;
        target.mValid = true;
    }
    return true;
}

bool BYDHOProtocol::decode_CR_FL_0x659(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 64) {
        return false;
    }

    const uint8_t *data = (const uint8_t *)frame.data().data();

//    target.Checksum_0x659_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    target.Counter_0x659_S = (data[2]);

//    qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();
    int &targetCount =  mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount; // 注意必须使用引用
    if (data[50] != 0xFF) {
        Target &target = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets[targetCount++];
        target.mProtocolType = ProtocolBYDHO;

        target.mID = (data[50]);
        target.mObstacleProbability = (data[52] & 0x7FU);

        target.mY = ((((data[3] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.0875) - 100);
        target.mX = (((((data[4] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0xFFU) << 4)) * 0.0875) - 179.113);
        target.mVysog = ((((data[6] & 0xFFU) + (((uint16_t)data[7] & 0x7U) << 8)) * 0.125) - 128);
        target.mVxsog = (((((data[7] & 0xF8U) >> 3) + (((uint16_t)data[8] & 0x3FU) << 5)) * 0.125) - 128);
        target.mAy = (((((data[8] & 0xC0U) >> 6) + (((uint16_t)data[9] & 0x7FU) << 2)) * 0.125) - 31.875);
        target.mAx = (((((data[9] & 0x80U) >> 7) + (((uint16_t)data[10] & 0xFFU) << 1)) * 0.125) - 31.875);
        target.mYStd = ((data[11]) * 0.05);
        target.mXStd = ((data[12]) * 0.1);
        target.mVyStd = ((data[13] & 0x7FU) * 0.1);
        target.mVxStd = ((((data[13] & 0x80U) >> 7) + (((uint16_t)data[14] & 0x3FU) << 1)) * 0.2);
        target.mAyStd = ((((data[14] & 0xC0U) >> 6) + (((uint16_t)data[15] & 0x1FU) << 2)) * 0.25);
        target.mAxStd = ((((data[15] & 0xE0U) >> 5) + (((uint16_t)data[16] & 0x1FU) << 3)) * 0.25);
        target.mRCS = (((((data[16] & 0xE0U) >> 5) + (((uint16_t)data[17] & 0x3FU) << 3)) * 0.2) - 51.1);
        target.mTrackFrameLength = ((((data[17] & 0xC0U) >> 6) + (((uint16_t)data[18] & 0x1FU) << 2)) * 0.2);
        target.mTrackFrameWidth = ((((data[18] & 0xE0U) >> 5) + (((uint16_t)data[19] & 0xFU) << 3)) * 0.2);
        target.mTrackFrameAngle = (((((data[19] & 0xF0U) >> 4) + (((uint16_t)data[20] & 0x3FU) << 4)) * 0.01) - 5.11);
//        target.FL_OD_OrientationStd_Obj_00 = ((((data[20] & 0xC0U) >> 6) + (((uint16_t)data[21] & 0x3U) << 2)) * 0.002);
//        target.FL_OD_RefPoint_Obj_00 = ((data[21] & 0xCU) >> 2);
        target.mClass = ((data[21] & 0x70U) >> 4);
        target.mDynamicProperty = (((data[21] & 0x80U) >> 7) + (((uint16_t)data[22] & 0x3U) << 1));
        target.mExistProbability = (((data[22] & 0xFCU) >> 2) + (((uint16_t)data[23] & 0x1U) << 6));
        target.mMirrProblty = ((data[23] & 0xFEU) >> 1);
        target.mTrackSts = (data[24] & 0x3U);
        target.mTrackLifeCycleCnt = (((data[24] & 0xFCU) >> 2) + (((uint32_t)data[25]) << 6) + (((uint32_t)data[26] & 0x3U) << 14));
//        target.FL_OD_StableFlag_00 = ((data[26] & 0x4U) >> 2);


        target.mValid = (target.mX != 0 || target.mY != 0);

//        qDebug() << __FUNCTION__ << __LINE__ << radarID << target.mID << target.mX << target.mY;
    }
    if (data[51] != 0xFF) {
        Target &target = mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargets[targetCount++];
        target.mProtocolType = ProtocolBYDHO;

        target.mID = (data[51]);
        target.mObstacleProbability = (data[53] & 0x7FU);

        target.mY = (((((data[26] & 0xF8U) >> 3) + (((uint16_t)data[27] & 0x7FU) << 5)) * 0.0875) - 100);
        target.mX = (((((data[27] & 0x80U) >> 7) + (((uint32_t)data[28]) << 1) + (((uint32_t)data[29] & 0x7U) << 9)) * 0.0875) - 179.113);
        target.mVysog = (((((data[29] & 0xF8U) >> 3) + (((uint16_t)data[30] & 0x3FU) << 5)) * 0.125) - 128);
        target.mVxsog = (((((data[30] & 0xC0U) >> 6) + (((uint32_t)data[31]) << 2) + (((uint32_t)data[32] & 0x1U) << 10)) * 0.125) - 128);
        target.mAy = (((((data[32] & 0xFEU) >> 1) + (((uint16_t)data[33] & 0x3U) << 7)) * 0.125) - 31.875);
        target.mAx = (((((data[33] & 0xFCU) >> 2) + (((uint16_t)data[34] & 0x7U) << 6)) * 0.125) - 31.875);
        target.mYStd = ((((data[34] & 0xF8U) >> 3) + (((uint16_t)data[35] & 0x7U) << 5)) * 0.05);
        target.mXStd = ((((data[35] & 0xF8U) >> 3) + (((uint16_t)data[36] & 0x7U) << 5)) * 0.1);
        target.mVyStd = ((((data[36] & 0xF8U) >> 3) + (((uint16_t)data[37] & 0x3U) << 5)) * 0.1);
        target.mVxStd = ((((data[37] & 0xFCU) >> 2) + (((uint16_t)data[38] & 0x1U) << 6)) * 0.2);
        target.mAyStd = (((data[38] & 0xFEU) >> 1) * 0.25);
        target.mAxStd = ((data[39]) * 0.25);
        target.mRCS = ((((data[40] & 0xFFU) + (((uint16_t)data[41] & 0x1U) << 8)) * 0.2) - 51.1);
        target.mTrackFrameLength = (((data[41] & 0xFEU) >> 1) * 0.2);
        target.mTrackFrameWidth = ((data[42] & 0x7FU) * 0.2);
        target.mTrackFrameAngle = (((((data[42] & 0x80U) >> 7) + (((uint32_t)data[43]) << 1) + (((uint32_t)data[44] & 0x1U) << 9)) * 0.01) - 5.11);
//        target.FL_OD_OrientationStd_Obj_01 = (((data[44] & 0x1EU) >> 1) * 0.002);
//        target.FL_OD_RefPoint_Obj_01 = ((data[44] & 0x60U) >> 5);
        target.mClass = (((data[44] & 0x80U) >> 7) + (((uint16_t)data[45] & 0x3U) << 1));
        target.mDynamicProperty = ((data[45] & 0x1CU) >> 2);
        target.mExistProbability = (((data[45] & 0xE0U) >> 5) + (((uint16_t)data[46] & 0xFU) << 3));
        target.mMirrProblty = (((data[46] & 0xF0U) >> 4) + (((uint16_t)data[47] & 0x7U) << 4));
        target.mTrackSts = ((data[47] & 0x18U) >> 3);
        target.mTrackLifeCycleCnt = (((data[47] & 0xE0U) >> 5) + (((uint32_t)data[48]) << 3) + (((uint32_t)data[49] & 0x1FU) << 11));
//        target.FL_OD_StableFlag_01 = ((data[49] & 0x20U) >> 5);

        target.mValid = (target.mX != 0 || target.mY != 0);

//        qDebug() << __FUNCTION__ << __LINE__ << radarID << target.mID << target.mX << target.mY;
    }

    mAnalysisWorker->analysisTargetPoint16FrameEnd( radarID, frame, false );

    return true;
}

bool BYDHOProtocol::decode_CR_FL_0x66D(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 32) {
        return false;
    }

    quint8* data = (quint8*)(frame.data().data());

    AnalysisData *analysisData = &mAnalysisWorker->mAnalysisDatas[radarID];
    Targets &targets = analysisData->m200Targets;

//    target.Checksum_0x66D_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    target.Counter_0x66D_S = (data[2]);
    quint64 mesTimestamp = ((data[3] & 0xFFU) + (((uint32_t)data[4]) << 8) + (((uint32_t)data[5]) << 16) + (((uint32_t)data[6] & 0xFFU) << 24));
    quint64 timestamp = ((data[7] & 0xFFU) + (((uint32_t)data[8]) << 8) + (((uint32_t)data[9]) << 16) + (((uint32_t)data[10] & 0xFFU) << 24));
    timestamp = mesTimestamp * 1000 + timestamp / 1000000;
//    target.RR_RDI_NS_TimeStampLocal = ((data[11] & 0xFFU) + (((uint32_t)data[12]) << 8) + (((uint32_t)data[13]) << 16) + (((uint32_t)data[14] & 0xFFU) << 24));
//    target.RR_RDI_NS_Latency = ((((data[15] & 0xFFU) + (((uint16_t)data[16] & 0x7U) << 8)) * 0.1) + 30);
//    target.RR_RDI_NS_MeasCounter = (((data[16] & 0xF8U) >> 3) + (((uint32_t)data[17]) << 5) + (((uint32_t)data[18] & 0x7U) << 13));
//    target.RR_RDI_NS_CycleCounter = (((data[18] & 0xF8U) >> 3) + (((uint32_t)data[19]) << 5) + (((uint32_t)data[20] & 0x7U) << 13));
//    target.RR_RDI_NS_NumOfClusters = (((data[20] & 0xF8U) >> 3) + (((uint16_t)data[21] & 0xFU) << 5));
//    target.RR_RDI_NS_AmbigFreeDopplerRange = ((((data[21] & 0xF0U) >> 4) + (((uint16_t)data[22] & 0xFFU) << 4)) * 0.01);
//    target.RR_RDI_NS_MaxDetectionRange = (data[23]);
//    target.RR_RDI_NS_RangeResolution = ((data[24] & 0x7FU) * 0.01);
//    target.RR_RDI_NS_TaskValidFlag = ((data[24] & 0x80U) >> 7);
//    target.RR_RDI_NS_ExtendedCycleFlag = (data[25] & 0x1U);
//    target.RR_RDI_NS_EgoVelocity = (((((data[25] & 0xFEU) >> 1) + (((uint16_t)data[26] & 0xFU) << 7)) * 0.125) - 128);
//    target.RR_RDI_NS_EgoVelocityStd = ((((data[26] & 0xF0U) >> 4) + (((uint16_t)data[27] & 0x7U) << 4)) * 0.1);
//    target.RR_RDI_NS_EgoAcceleration = (((((data[27] & 0xF8U) >> 3) + (((uint16_t)data[28] & 0xFU) << 5)) * 0.125) - 31.875);
//    target.RR_RDI_NS_EgoYawRate = (((((data[28] & 0xF0U) >> 4) + (((uint16_t)data[29] & 0x3FU) << 4)) * 0.001) - 0.511);
//    target.RR_RDI_NS_EgoCurvature = ((((data[29] & 0xC0U) >> 6) + (((uint16_t)data[30] & 0x3FU) << 2)) * 0.001);
//    target.ARS_RDI_TimesynSts = (data[31]);

//    qDebug() << __FUNCTION__ << __LINE__ << radarID << timestamp;

    targets.mTargetHeader.mMeasurementCount = timestamp;
    targets.mValid = true;

    mAnalysisWorker->analysisEnd(radarID, Frame200Raw);

    return true;
}

bool BYDHOProtocol::decode_CR_FL_0x66E(quint8 radarID, const Devices::Can::CanFrame &frame)
{
    if (frame.length() != 32) {
        return false;
    }
//    qDebug() << __FUNCTION__ << __LINE__ << radarID << mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetCount;

    quint8* data = (quint8*)(frame.data().data());

    // e1 87 f2 66   3783783014 s       17271705296155
    // 80 d4 b5 31   2161423665 ns                6155352200
    // 3783783014 2161 -> 37837830142161 -> 3169-01-13 04:22:22
//    target.Checksum_62E_S = ((data[0] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
//    target.Counter_62E_S = (data[2]);
    quint64 mesTimestamp = ((data[3] & 0xFFU) + (((uint32_t)data[4]) << 8) + (((uint32_t)data[5]) << 16) + (((uint32_t)data[6] & 0xFFU) << 24));
    quint64 timestamp = ((data[7] & 0xFFU) + (((uint32_t)data[8]) << 8) + (((uint32_t)data[9]) << 16) + (((uint32_t)data[10] & 0xFFU) << 24));
//    qDebug() << __FUNCTION__ << __LINE__ << mesTimestamp << timestamp;
    timestamp = mesTimestamp * 1000 + timestamp / 1000000;

//    qDebug() << __FUNCTION__ << __LINE__ << radarID << timestamp << QDateTime::fromMSecsSinceEpoch(timestamp).toString("yyyy-MM-dd hh:mm:ss.zzz");

    mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mTargetHeader.mMeasurementCount = timestamp;
    mAnalysisWorker->mAnalysisDatas[radarID].m16Targets.mValid = true;
    mAnalysisWorker->analysisEnd(radarID, Frame16Track);
    mAnalysisWorker->analysisTargetPoint16FrameEnd( radarID, frame, true );

    return true;
}

}
}
