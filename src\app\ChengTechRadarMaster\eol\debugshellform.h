#ifndef DEBUGSHELLFORM_H
#define DEBUGSHELLFORM_H

#include <QWidget>

namespace Ui {
class DebugShellForm;
}

class DebugShellForm : public QWidget
{
    Q_OBJECT

public:
    explicit DebugShellForm(QWidget *parent = nullptr);
    ~DebugShellForm();

    virtual void showEvent(QShowEvent *event) override;

signals:
    void shellCmd( const QString& cmd );
    void clearHistoryCmd();
    void quickCmdChanged( const QStringList& cmds );

public slots:
    void showText( const QString& text );
    void refreshHistoryCmdList();
    void refreshQuickCmdList();
    void deleteQuickCmd();
    void addQuickCmd();

private slots:
    void on_comboBoxCmdList_currentIndexChanged(int index);

    void on_pushButtonCmdList_clicked();

    void on_listWidgetCmd_doubleClicked(const QModelIndex &index);

    void on_listWidgetCmd_customContextMenuRequested(const QPoint &pos);

private:
    Ui::DebugShellForm *ui;
};

#endif // DEBUGSHELLFORM_H
