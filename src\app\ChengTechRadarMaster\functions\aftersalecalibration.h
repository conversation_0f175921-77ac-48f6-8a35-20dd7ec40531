﻿#ifndef AFTERSALECALIBRATION_H
#define AFTERSALECALIBRATION_H

#include "functions_global.h"

#include <QDialog>
#include <QTimer>
#include <QFile>

namespace Ui {
class AfterSaleCalibration;
}

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Functions {

class AfterSaleCalibrationWorker;

class FUNCTIONS_EXPORT AfterSaleCalibration : public QDialog
{
    Q_OBJECT

public:
    explicit AfterSaleCalibration(Devices::Can::DeviceManager *deviceManager, QWidget *parent = nullptr);
    ~AfterSaleCalibration();

private slots:
    void message(int index, const QString &msg);
    void calibrationFinished(int index);
    void calibrationStarted(int index);
    void sendOrRecvCanFrame( int index, bool bSend, quint64 id, const QString& data );

    void on_pushButtonStopCalibration_clicked();

    void on_pushButtonStartCalibration_clicked();

    void on_pushButtonCalibrationResult_clicked();

    void on_comboBoxProtocol_currentIndexChanged(int index);

private:
    void openCanLogFile();
    void closeCanLogFile();


private:
    Ui::AfterSaleCalibration *ui;

    AfterSaleCalibrationWorker *mAfterSaleCalibrationWorker{0};
    QTimer mTimerResult[4];
    QFile mCanLogFile[4];
};

} // namespace Functions

#endif // AFTERSALECALIBRATION_H
