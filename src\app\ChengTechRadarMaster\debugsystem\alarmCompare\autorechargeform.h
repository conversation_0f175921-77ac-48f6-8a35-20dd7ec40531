#ifndef AUTORECHARGEFORM_H
#define AUTORECHARGEFORM_H

#include <QMap>
#include <QSet>
#include <QWidget>
#include <QFileSystemModel>
#include <QDirModel>

namespace Ui {
class AutoRechargeForm;
}

namespace Analysis {
class AnalysisWorker;
class AnalysisSaveWorker;
};

namespace Devices {
namespace Can {
class DeviceManager;
}
}



class QStandardItem;
class DebugControlForm;
class AlarmCompare;
class AutoRechargeRawAndTarget;


class AutoRechargeFileModel : public QDirModel
{
private:
    struct FileAddInfo{ //附加信息
        bool checked{false};  //附加标识  是否选中
        quint32 total{0};   //binary子节点数量
        quint32 opCnt{0};   //回灌的binary子节点数量
        quint32 equalCnt{0}; //回灌结果一致的binary子节点数量
        quint32 notEqualCnt{0}; //回灌结果不一致的binary子节点数量

        void clearCount(){ total = opCnt = equalCnt = notEqualCnt = 0; };
    };
public:
    AutoRechargeFileModel( QObject *parent = nullptr );

    void setEqualFlag( const QString& file, bool bEqual );

    void getCheckedFiles( const QModelIndex& index, QStringList& files );

    quint32 totalFileAddInfo( const QModelIndex& index );
    void exportResult( QFile* file, const QModelIndex& index, quint32 indent = 0, QFile* csvFile = NULL );


protected: //虚函数
    QVariant data( const QModelIndex &index, int role /*= Qt::DisplayRole*/ ) const;
    bool setData( const QModelIndex &index, const QVariant &value, int role /*= Qt::EditRole*/ );
    Qt::ItemFlags flags(const QModelIndex &index) const override;

private:
    QMap<QString, FileAddInfo> mFileAddInfoMap; //附加信息
};






class AutoRechargeForm : public QWidget
{
    Q_OBJECT

public:
    explicit AutoRechargeForm(/*DebugControlForm* pDebugCtrl,*/ Devices::Can::DeviceManager* pDeviceManager, Analysis::AnalysisWorker* pAnalysisWorker, QWidget *parent = nullptr);
    ~AutoRechargeForm();

private slots:
    void on_pushButtonSelectPath_clicked();

    void on_pushButtonRecharge_clicked();

    void showMsg( const QString& msg, bool bError = false );

    void begin( const QString& file );
    void end( const QString& file );

    void on_treeView_doubleClicked(const QModelIndex &index);

    void on_pushButtonClearMsg_clicked();

    void on_pushButtonStop_clicked();

private:
    void run();
    bool cmpareAlarm( const QString& file );
    bool exportResult();
    void initChannelCount();
    bool setRechargeParam();
    void enablePushButton( bool enable );


private:
    Ui::AutoRechargeForm *ui;
    Analysis::AnalysisSaveWorker* mAnalysisSaveWorker{NULL};
    Analysis::AnalysisWorker* mAnalysisWorker{NULL};
    Devices::Can::DeviceManager* mDeviceManager{NULL};
    AlarmCompare* mAlarmCompare{NULL};

    AutoRechargeFileModel* mDirModel{NULL};


    QString mPath;
    QStringList mFiles;  //记录需要回灌的文件
    int mCurrentFilesIndex{0}; //当前回灌到了那个文件
    bool mRun{false};

    AutoRechargeRawAndTarget* mAutoRecharge{NULL};
};

#endif // AUTORECHARGEFORM_H
