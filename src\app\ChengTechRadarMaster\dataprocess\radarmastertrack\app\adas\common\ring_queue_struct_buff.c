﻿/**
*********************************************************************************************************
*               Copyright (C) SHL. All rights reserved.
**********************************************************************************************************
* 文件名file:concave.c
* 简述brief:
* 作者author:
* 日期date:
* 版本version:
*********************************************************************************************************
*/

/***********************
* * 标准库头文件
***********************/
#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <stddef.h>

/***********************
* * 本地头文件
***********************/
#include "ring_queue_struct_buff.h"

/***
* * fuction:
* * brief: 环形队列FIFO型初始化
* * param: None
* * retval: None
* * remarks: None
***/
RingQueueFIFO_FlexibleBuffer_TypeDef* RingQueue_FIFO_Init(RingQueueFIFO_FlexibleBuffer_TypeDef* pRingSoftBuff, const unsigned int len)
{
    struct __Tag_RingQueueFIFO_FlexibleBuffer_TypeDef* ptr = pRingSoftBuff;
    ptr = (RingQueueFIFO_FlexibleBuffer_TypeDef *)malloc(sizeof(struct __Tag_RingQueueFIFO_FlexibleBuffer_TypeDef) + (len * sizeof(asx)));
    if (ptr == NULL)
    {
        printf("FIFO_Init Alloc Error!\r\n");
        return NULL;
    }

    ptr->Write = 0;
    ptr->Read = 0;
    ptr->Len = 0;

    return (ptr);
}

/***
* * fuction:
* * brief: 判断环形队列是否已经满了
* * param: None
* * retval: None
* * remarks: 1，满了，0，未满
***/
int RingQueue_GetIsFull_Statue(RingQueueFIFO_FlexibleBuffer_TypeDef* ptr)
{
    /*!< 如果写位置减去读位置等于队列长度，就说明这环形队列已经满了 */
    if ((ptr->Write - ptr->Read) == RING_QUEUE_LEN)
    {
        return (1);
    }
    else
    {
        return (0);
    }
}

/***
* * fuction:
* * brief: 判断环形队列为空
* * param: None
* * retval: None
* * remarks: 1,为空，0，非空
***/
int RingQueue_GetIsEmpty_Status(RingQueueFIFO_FlexibleBuffer_TypeDef* ptr)
{
    /*!< 如果写位置和读的位置相等，就说明这个环形队列为空 */
    if (ptr->Write == ptr->Read)
    {
        return (1);
    }
    else
    {
        return (0);
    }
}

/***
* * fuction:
* * brief: 插入数据
* * param: None
* * retval: None
* * remarks: 1,为空，0，非空
***/
int RingQueue_Insert(RingQueueFIFO_FlexibleBuffer_TypeDef *ptr, asx* data)
{
    if (ptr == NULL)
    {
        printf("ring_queue_buff_insert():pring_buff is null\n");
        return (-1);
    }

    if (RingQueue_GetIsFull_Statue(ptr) == 1)
    {
        printf("ring_queue_buff_insert():pring_buff is full\n");
        return (-2);
    }

    /*!< 保证写的位置不会超过总长度，如果超过了总长度，就会从第一个位置重新开始写。*/
    ptr->Data[(ptr->Write % RING_QUEUE_LEN)] = *data;
    // printf("RingQueue_Insert ptr->Data:%0.2f, %0.2f\n", ptr->Data[(ptr->Write % RING_QUEUE_LEN)].x, ptr->Data[(ptr->Write % RING_QUEUE_LEN)].y);
    ptr->Write++;
    //  printf("RingQueue_Insert():insert data->\n");
    //  printf("%d %d\n", data, ptr->Write); 

    return (0);
}

/***
* * fuction:
* * brief: 读取环形队列数据
* * param: None
* * retval: None
* * remarks: None
***/
int RingQueue_ReadData(RingQueueFIFO_FlexibleBuffer_TypeDef* ptr, asx *data)
{
    if (ptr == NULL)
    {
        printf("ring_queue_buff_get(): p_ring_buff is null\n");
        return -1;
    }

    if (RingQueue_GetIsEmpty_Status(ptr) == 1)
    {
        printf("ring_queue_buff_get(): p_ring_buff is empty\n");
        return -2;
    }

    *data = ptr->Data[(ptr->Read % RING_QUEUE_LEN)];
    ptr->Read++;
    // printf("RingQueue_ReadData data:%0.2f, %0.2f\n", data->x, data->y);

    return 0;
}

/***
* * fuction:
* * brief: 读取环形队列数据
* * param: None
* * retval: None
* * remarks: 0，销毁成功，-1，传输队列为空
***/
int RingQueue_Destory(RingQueueFIFO_FlexibleBuffer_TypeDef* ptr)
{
    if (ptr == NULL)
    {
        printf("RingQueue_Destory(): ptr is null\n");
        return (-1);
    }

    free(ptr);

    return (0);
}
