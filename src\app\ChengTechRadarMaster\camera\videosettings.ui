<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>VideoSettingsUi</class>
 <widget class="QDialog" name="VideoSettingsUi">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>561</width>
    <height>369</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Video Settings</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4">
   <item row="0" column="0">
    <widget class="QScrollArea" name="scrollArea">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>543</width>
        <height>250</height>
       </rect>
      </property>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0">
        <widget class="QGroupBox" name="groupBox">
         <property name="title">
          <string>Audio</string>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0" colspan="2">
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>Audio Codec:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0" colspan="2">
           <widget class="QComboBox" name="audioCodecBox"/>
          </item>
          <item row="2" column="0" colspan="2">
           <widget class="QLabel" name="label_5">
            <property name="text">
             <string>Sample Rate:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0" colspan="2">
           <widget class="QComboBox" name="audioSampleRateBox"/>
          </item>
          <item row="4" column="0">
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>Quality:</string>
            </property>
           </widget>
          </item>
          <item row="4" column="1">
           <widget class="QSlider" name="audioQualitySlider">
            <property name="maximum">
             <number>4</number>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="0" column="1" rowspan="3">
        <widget class="QGroupBox" name="groupBox_2">
         <property name="title">
          <string>Video</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_2">
          <item row="0" column="0" colspan="2">
           <widget class="QLabel" name="label_8">
            <property name="text">
             <string>Resolution:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0" colspan="2">
           <widget class="QComboBox" name="videoResolutionBox"/>
          </item>
          <item row="2" column="0" colspan="2">
           <widget class="QLabel" name="label_9">
            <property name="text">
             <string>Framerate:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0" colspan="2">
           <widget class="QComboBox" name="videoFramerateBox"/>
          </item>
          <item row="4" column="0" colspan="2">
           <widget class="QLabel" name="label_6">
            <property name="text">
             <string>Video Codec:</string>
            </property>
           </widget>
          </item>
          <item row="5" column="0" colspan="2">
           <widget class="QComboBox" name="videoCodecBox"/>
          </item>
          <item row="6" column="0">
           <widget class="QLabel" name="label_7">
            <property name="text">
             <string>Quality:</string>
            </property>
           </widget>
          </item>
          <item row="6" column="1">
           <widget class="QSlider" name="videoQualitySlider">
            <property name="maximum">
             <number>4</number>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>Container Format:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QComboBox" name="containerFormatBox"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item row="1" column="0">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>14</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="0">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>VideoSettingsUi</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>322</x>
     <y>272</y>
    </hint>
    <hint type="destinationlabel">
     <x>44</x>
     <y>230</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>VideoSettingsUi</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>405</x>
     <y>262</y>
    </hint>
    <hint type="destinationlabel">
     <x>364</x>
     <y>227</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
