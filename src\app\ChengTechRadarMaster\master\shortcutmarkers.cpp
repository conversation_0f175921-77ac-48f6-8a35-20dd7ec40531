﻿#include "shortcutmarkers.h"

#include <utils/flowlayout.h>

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QPlainTextEdit>
#include <QDateTime>
#include <QFileDialog>
#include <QMessageBox>
#include <QTextDocument>
#include <QTextBlock>
#include <QDebug>

ShortcutMarkers::ShortcutMarkers(QWidget *parent) :
    QWidget(parent)
{
    setupUi();

    // Qt::CTRL + Qt::Key_CapsLock
    mShortcutMarkers
            << ShortcutMarker{QString::fromLocal8Bit("误报"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("漏报"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("假点"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("BSD"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("DOW"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("FCTA"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("FCTB"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("LCA"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("RCTA"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("RCTB"), QKeySequence()}
            << ShortcutMarker{QString::fromLocal8Bit("RCW"), QKeySequence()};

    setShortcutMarkers(mShortcutMarkers);
}

ShortcutMarkers::~ShortcutMarkers()
{

}

void ShortcutMarkers::setShortcutMarkers(const QList<ShortcutMarker> &markers)
{
    mShortcutMarkers = markers;
    QLayoutItem *item = 0;
    while((item = mFlowLayout->takeAt(0)) != nullptr) {
        delete item->widget();
        delete item;
    }

    foreach (const ShortcutMarker &marker, mShortcutMarkers) {
        QString key = marker.mKey.isEmpty() ? "" : QString("(%1)").arg(marker.mKey.toString());
        QPushButton *pushButton = new QPushButton(QString("%1%2").arg(marker.mText).arg(key), this);
        pushButton->setShortcut(marker.mKey);
        pushButton->setObjectName(marker.mText);

        connect(pushButton, &QPushButton::clicked, this, &ShortcutMarkers::record);

        mFlowLayout->addWidget(pushButton);
    }
}

void ShortcutMarkers::clear()
{
    mPlainTextEditMarkers->clear();
}

void ShortcutMarkers::exportToFile()
{
    QString dir = QFileDialog::getExistingDirectory(this, tr("Open Directory"), "./",
                    QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);

    QString fileName = QString::fromLocal8Bit( "快捷标记结果-%1.txt" )
            .arg( QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz") );

    QFile file( dir + "/" + fileName );
    if( !file.open( QIODevice::WriteOnly ) ){
        QMessageBox::warning( this, QString::fromLocal8Bit("错误"),
                              QString::fromLocal8Bit("打开文件失败![%1]").arg( file.fileName() ) );
        qDebug() << __FUNCTION__ << __LINE__ << QString::fromLocal8Bit("打开文件失败![%1]").arg( file.fileName() );
        return;
    }

    QTextDocument *document=Q_NULLPTR;
    QTextBlock textBlock;
    document = mPlainTextEditMarkers->document();
    for( textBlock=document->begin(); textBlock!=document->end(); textBlock=textBlock.next() ){
        file.write( textBlock.text().toLocal8Bit() );
        file.write( "\n" );
    }
    file.close();
    QMessageBox::information( this, QString::fromLocal8Bit("提示"),
                          QString::fromLocal8Bit("导出完成" ) );
}

void ShortcutMarkers::record()
{
    QPushButton *pushButton = qobject_cast<QPushButton*>(sender());
    if (!pushButton) {
        return;
    }

    mPlainTextEditMarkers->appendPlainText(QString::fromLocal8Bit("【%1】 %2")
                                           .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                                           .arg(pushButton->objectName())
                                           );
}

void ShortcutMarkers::setupUi()
{
    QVBoxLayout *layout = new QVBoxLayout(this);

    mFlowLayout = new FlowLayout;

    layout->addLayout(mFlowLayout);

    mPlainTextEditMarkers = new QPlainTextEdit(this);

    layout->addWidget(mPlainTextEditMarkers);

    QHBoxLayout *layoutOperation = new QHBoxLayout;

    QPushButton *pushButtonClear = new QPushButton(QString::fromLocal8Bit("清空"), this);
    QPushButton *pushButtonExport = new QPushButton(QString::fromLocal8Bit("导出"), this);
    connect(pushButtonClear, &QPushButton::clicked, this, &ShortcutMarkers::clear);
    connect(pushButtonExport, &QPushButton::clicked, this, &ShortcutMarkers::exportToFile);


    layoutOperation->addWidget(pushButtonClear);
    layoutOperation->addWidget(pushButtonExport);

    layout->addLayout(layoutOperation);
}
