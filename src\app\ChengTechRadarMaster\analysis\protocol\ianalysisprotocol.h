﻿#ifndef IANALYSISPROTOCOL_H
#define IANALYSISPROTOCOL_H

#include <QObject>
#include <QJsonArray>
#include <QJsonObject>

#include "analysis/analysisworker.h"
#include "analysis/analysisdata.h"
#include "devices/canframe.h"

namespace Analysis {

namespace Protocol {

class IAnalysisProtocol : public QObject
{
    Q_OBJECT
public:
    static QStringList protocolNames() { return QStringList{"DBC", "ChengTech", "ChengTech 410"}; }
    static IAnalysisProtocol *newAnalysisProtocol(ProtocolType protocolType, AnalysisWorker *analysisWorker, QObject *parent = nullptr);

    explicit IAnalysisProtocol(AnalysisWorker *analysisWorker, QObject *parent = nullptr);

    virtual bool analysisFrame(const Devices::Can::CanFrame &frame ) = 0;
    virtual void setAnalysisConfig(const QJsonObject &config);
    virtual QJsonObject analysisConfig() const;

    ProtocolType protocolType() const { return mProtocolType; }

    void analysisEnd(int radarID, bool assigned);
    void analysisEnd(Parser::ParsedDataTypedef::ParsedDataType fType);


signals:

protected:
    ProtocolType mProtocolType{ProtocolUnknown};
    AnalysisWorker *mAnalysisWorker{0};

    bool mNewBegin[MAX_RADAR_COUNT];
};

} // namespace Protocol
} // namespace Analysis

#endif // IANALYSISPROTOCOL_H
