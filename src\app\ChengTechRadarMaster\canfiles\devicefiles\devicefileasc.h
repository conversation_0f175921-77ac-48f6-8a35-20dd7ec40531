﻿#ifndef DEVICEFILEASC_H
#define DEVICEFILEASC_H

#include "idevicefile.h"

#include <fstream>

namespace Devices {
    namespace Can {
		class DeviceFileASC : 
			public IDeviceFile
		{
		public:
			explicit DeviceFileASC(CANDeviceFile *device = 0);

			bool openFile() override;
			bool closeFile() override;
			bool readData() override;
            bool writeData(const CanFrame::Ptr pFrame) override;

		private:
			bool readDataVersion_7(const std::string &line);
			bool readDataVersion_12(const std::string &line);
			bool parseHeader();
			bool parseDate(const std::string &line);
			bool parseVersion(const std::string &line);
			bool writeHeader(uint64_t timestamp);

			std::fstream mInFileStream;

			uint32_t mMajorVersion{ 0 };
			uint32_t mMinorVersion{ 0 };
			uint32_t mPatchVersion{ 0 };
		};
	}
}

#endif // DEVICEFILEASC_H
