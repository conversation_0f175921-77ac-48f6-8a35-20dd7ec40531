
#ifndef _APP_PCAN_PROTOCOL_H_
#define _APP_PCAN_PROTOCOL_H_

#include "cfg.h"
// #include "rsp_types.h"
// #include "rdp_types.h"

#define CAN_MSG_ID1(id) (id + radar_config_using->compli_para.radarId)

#define RAW_OBJ_HEADER_BASE             (0x400)
#define RAW_OBJ_LIST_BASE               (0x410)
#define TRK_OBJ_HEADER_BASE             (0x430)
#define TRK_OBJ_LIST_BASE               (0x440)
#define RADAR_INFO1_BASE                (0x450)
#define RADAR_INFO2_BASE                (0x460)
#define RADAR_INFO3_BASE                (0x470)
#define RADAR_INFO4_BASE                (0x480)
#define RADAR_INFO5_BASE                (0x490)
#define ALARM_OBJ_BASE                  (0x4C0)
#define ERROR_CURRENT_BASE              (0x4D0)
#define ERROR_CONFIRM_BASE              (0x4E0)
#define FRAME_END_BASE1                 (0x4F0)
#define VEHICLE_INFO_BASE               (0x3F0)

#define RAW_OBJ_HEADER                  CAN_MSG_ID1(RAW_OBJ_HEADER_BASE)
#define RAW_OBJ_LIST                    CAN_MSG_ID1(RAW_OBJ_LIST_BASE)
#define TRK_OBJ_HEADER                  CAN_MSG_ID1(TRK_OBJ_HEADER_BASE)
#define TRK_OBJ_LIST                    CAN_MSG_ID1(TRK_OBJ_LIST_BASE)
#define RADAR_INFO1                     CAN_MSG_ID1(RADAR_INFO1_BASE)
#define RADAR_INFO2                     CAN_MSG_ID1(RADAR_INFO2_BASE)
#define RADAR_INFO3                     CAN_MSG_ID1(RADAR_INFO3_BASE)
#define RADAR_INFO4                     CAN_MSG_ID1(RADAR_INFO4_BASE)
#define RADAR_INFO5                     CAN_MSG_ID1(RADAR_INFO5_BASE)
#define ALARM_OBJ                       CAN_MSG_ID1(ALARM_OBJ_BASE)
#define ERROR_CURRENT                   CAN_MSG_ID1(ERROR_CURRENT_BASE)
#define ERROR_CONFIRM                   CAN_MSG_ID1(ERROR_CONFIRM_BASE)
#define FRAME_END1                       CAN_MSG_ID1(FRAME_END_BASE1)
#define VEHICLE_INFO                    CAN_MSG_ID1(VEHICLE_INFO_BASE)

#define DEBUG_ID                  CAN_MSG_ID1(0x300)

typedef struct
{
    uint64_t detHeaderRollingCnt : 4;
    uint64_t detHeaderProtVer : 4;
    uint64_t detHeaderProfileCnt : 4;
    uint64_t detRspCycleTime : 9;//信号处理的时间周期
    uint64_t detRspCalcTime : 9;//信号处理的耗时
    uint64_t detHeaderObjNum : 10;
    uint64_t detHeaderMeasCnt : 16;
    uint64_t detHeaderChecksum : 8;
} COM_detObjectHeader_t;


 typedef struct
 {
    // uint64_t Resv2 : 32;
    uint64_t detObjRollingCnt               :  4;
    uint64_t detObjDynPro                   :  4;
    uint64_t Resv1                          : 24;
    uint64_t Resv                           :  6;
    uint64_t detObjID                       : 10;
    uint64_t detObjSNR                      : 11;
    uint64_t detObjMag                      : 11;
    uint64_t detObjRCS                      : 11;
    uint64_t detObjElevationAngle           : 15;
    uint64_t detObjProbOfExist              :  8;
    uint64_t detObjVelocity                 : 16;
    uint64_t detObjAzimuth                  : 15;
    uint64_t detObjRange                    : 17;
    uint64_t detObjChecksum                 :  8;   

 }COM_detList20Byte_t;

 typedef struct
{
    uint64_t endFrameRollingCnt : 4;
    uint64_t endFrameRoadSideDist : 10; //边线距离
    uint64_t Resv : 8;
    uint64_t endFrameSpeedMode : 4;
    uint64_t endFrameInterTime : 8; 
    uint64_t endFrameAutoCalAngleOffset : 11;
    uint64_t endFrameEOLInstallAngle : 11;
    uint64_t endFrameCheckSum : 8; 
}COM_endFrame_t;

typedef struct
{
    uint64_t ver1 : 8;
    uint64_t ver2 : 8;
    uint64_t ver3 : 8;
    uint64_t ver4 : 8;
    uint64_t resv1 : 32;
} COM_versionDebug_t;

typedef struct
{
    uint64_t trkHeaderRollingCnt : 4;
    uint64_t trkHeaderProtVer : 4;
    uint64_t rdpTaskTimeCycle : 9; //数据处理的时间间隔
    uint64_t rdpFuncCalcTime : 9;  //信号处理的计算时间
    uint64_t resv0 : 6;
    uint64_t trkHeaderMeasCnt : 16;
    uint64_t trkHeaderObjNum : 8;
    uint64_t trkHeaderChecksum : 8;
} COM_trkObjectHeader_t;

 typedef struct
 {
    uint64_t trkObjRollingCnt : 3;
    uint64_t trkObjDistAltitude : 8;
    uint64_t trkObjProbOfExist : 5;
    uint64_t Resv1 : 1;
    uint64_t trkObjOrientationAngle : 10;
    uint64_t trkObjArelLatRms : 5;
    uint64_t trkObjArelLongRms : 5;
    uint64_t trkObjVrelLatRms : 5;
    uint64_t trkObjVrelLongRms : 5;
    uint64_t trkObjDistLatRms : 5;
    uint64_t trkObjHeight : 8;
    uint64_t trkObjDyncPro : 4;

    uint64_t trkObjRCS : 9;
    uint64_t trkObjMeasState : 3;
    uint64_t resv0 : 1;
    uint64_t trkObjWidth : 8;
    uint64_t trkObjLength : 8;
    uint64_t trkObjArelLat : 11;
    uint64_t trkObjArelLong : 11;
    uint64_t trkObjVrelLat : 13;

    uint64_t trkObjDistLongRms : 5;
    uint64_t trkObjClass : 4;
    uint64_t trkObjVrelLong : 13;
    uint64_t trkObjDistLat : 12;
    uint64_t trkObjDistLong : 14;
    uint64_t trkObjID : 8;
    uint64_t trkObjChecksum : 8; 

 }COM_trkList24Byte_t;


  typedef struct
 {
     uint64_t vehResv0 : 46;
    //  uint64_t vehRoadSideDist : 10; //边线距离
     uint64_t vehRadius : 18;       //转弯半径
     uint64_t vehRollingCnt : 4;
     uint64_t vehResv1 : 3;
     uint64_t vehSteerWhlAngle : 13;
     uint64_t vehWhlSpdRR : 13;
     uint64_t vehWhlSpdRL : 13;
     uint64_t vehWhlSpdFR : 13;
     uint64_t vehWhlSpdFL : 13;
     uint64_t vehLatAccel : 16;
     uint64_t vehLgtAccel : 16;
     uint64_t vehEspFailSts : 1;
     uint64_t vehTrailerSts : 1;
     uint64_t vehBrkPedelActLevel : 7;
     uint64_t vehAccPedelActLevel : 7;
     uint64_t vehWhlSpdDirRR : 1;
     uint64_t vehWhlSpdDirRL : 1;
     uint64_t vehWhlSpdDirFR : 1;
     uint64_t vehWhlSpdDirFL : 1;
     uint64_t vehBrkPedalSts : 2;
     uint64_t vehTurnLightRi : 1;
     uint64_t vehTurnLightLe : 1;
     uint64_t vehSecurityLock : 4;
     uint64_t vehKeyStatus : 4;
     uint64_t vehGear : 3;
     uint64_t vehDoorRearRi : 2;
     uint64_t vehDoorRearLe : 2;
     uint64_t vehDoorFrontRi : 2;
     uint64_t vehDoorFrontLe : 2;
     uint64_t vehSpeedInMps : 13;
     uint64_t vehSwFctbFunc : 1;
     uint64_t vehSwFctaFunc : 1;
     uint64_t vehSwMainFunc : 1;
     uint64_t vehSwDowFunc : 1;
     uint64_t vehSwBsdFunc : 1;
     uint64_t vehSwRcwFunc : 1;
     uint64_t vehSwRctbFunc : 1;
     uint64_t vehSwRctaFunc : 1;
     uint64_t vehYawrate : 16;
     uint64_t vehChecksum : 8;

 } COM_vehSignalToPcan_t;

 typedef struct
 {
     uint64_t RollingCntMsg : 4;
     uint64_t MountID1Sts : 2;
     uint64_t Resv1 : 56;
     uint64_t Resv2 : 26;
     uint64_t NoofRawTarget : 8;
     uint64_t Tx2Power : 8;
     uint64_t Tx1Power : 8;
     uint64_t Tx2ObjectNum : 8;
     uint64_t Tx1ObjectNum : 8;

     uint64_t MountID0Sts : 2;
     uint64_t NtcTemperature : 11;
     uint64_t ChipTemperature : 11;
     uint64_t LocalSystemTime : 32;
     uint64_t ChecksumMsg : 8;
 } COM_radarInfo1_24Byte_t;

  typedef struct
 {
     uint64_t RollingCntMsg0x464 : 4;
     uint64_t ReferencePLLSts : 1;
     uint64_t FmcwPLLSts : 1;
     uint64_t BatteryVoltage : 10;
     uint64_t Resv : 8;
     uint64_t RL_TemperatureRaw : 32;
     uint64_t PMICWdgSts : 8;

     uint64_t PMICUV : 8;
     uint64_t PMICOV : 8;
     uint64_t TimeGapPcanSend : 8;
     uint64_t TimeGapVcanSend : 8;
     uint64_t ChipSerielNumber : 32;

     uint64_t ChipSocCupLoad : 8;
     uint64_t TimeGapDataProcess : 8;
     uint64_t TimeGapSignalProcess : 8;
     uint64_t BatteryVoltageRaw : 32;
     uint64_t ChecksumMsg : 8;
 } COM_radarInfo2_24Byte_t;

  typedef struct
 {
     uint64_t RollingCntMsg0x474 : 4;
     uint64_t Resv : 5;
     uint64_t CAN1SendErrCnt : 5;
     uint64_t CAN1ReinitCnt : 3;
     uint64_t CAN1BusoffCnt : 3;
     uint64_t CAN0SendErrCnt : 6;
     uint64_t CAN0ReinitCnt : 3;
     uint64_t CAN0BusoffCnt : 3;
     uint64_t ChipSafetyRegDigitalSts : 32;

     uint64_t ChipSafetyRegRfSts : 32;
     uint64_t LedVoltageAdcBase : 16;
     uint64_t LedVoltageAdcValue : 16;

     uint64_t I2CReadReturnVlaue : 16;
     uint64_t I2CErrStep : 4;
     uint64_t FlashErrStep : 4;
     uint64_t FlashWriteErrCode : 16;
     uint64_t FlashReadErrCode : 16;
     uint64_t ChecksumMsg0x474 : 8;

 } COM_radarInfo3_24Byte_t;

typedef struct
 {
     uint64_t Resv1 : 4;
     uint64_t Sys_VcanConnected : 1;
     uint64_t Resv2 : 59;

     uint64_t Resv3 : 40;
     uint64_t DrvFunc_AlarmModule : 8;
     uint64_t DrvFunc_BeepState : 2;
     uint64_t Sys_SystemState : 4;
     uint64_t DrvFunc_LedState : 2;
     uint64_t Resv4 : 8;

 } COM_radarInfo4_16Byte_t;

 typedef struct
 {
     uint64_t RollingCnt : 4;
     uint64_t Resv1 : 28;
     uint64_t VerSwByte3 : 8;
     uint64_t VerSwByte2 : 8;
     uint64_t VerSwByte1 : 8;
     uint64_t VerSwByte0 : 8;

     uint64_t VerHwByte3 : 8;
     uint64_t VerHwByte2 : 8;
     uint64_t VerHwByte1 : 8;
     uint64_t VerHwByte0 : 8;
     uint64_t VerBootByte3 : 8;
     uint64_t VerBootByte2 : 8;
     uint64_t VerBootByte1 : 8;
     uint64_t VerBootByte0 : 8;
 } COM_radarInfo5_t;

typedef struct
{
    uint64_t Resv : 26;
    uint64_t AlarmRcwState : 2;
    uint64_t AlarmFctbState : 2;
    uint64_t AlarmFctaState : 2;
    uint64_t AlarmRctbState : 2;
    uint64_t AlarmRctaState : 2;
    uint64_t AlarmDowState : 2;
    uint64_t AlarmLcaState : 2;
    uint64_t AlarmBsdState : 2;
    
    uint64_t AlarmFctaLevel : 2;
    uint64_t AlarmRctaLevel : 2;
    uint64_t AlarmRcwLevel : 2;
    uint64_t AlarmDowLevelRear : 2;
    uint64_t AlarmDowLevelFront : 2;
    uint64_t AlarmLcaLevel : 2;
    uint64_t AlarmBsdLevel : 2;
    uint64_t AlarmFctbObjID : 8;

    uint64_t AlarmFctbObjTtc : 8;
    uint64_t AlarmFctaObjTtc : 8;
    uint64_t AlarmFctaObjID : 8;
    uint64_t AlarmRctbObjTtc : 8;
    uint64_t AlarmRctbObjID : 8;
    uint64_t AlarmRctaObjTtc : 8;
    uint64_t AlarmRctaObjID : 8;
    uint64_t AlarmRcwObjTtc : 8;

    uint64_t AlarmRcwObjID : 8;
    uint64_t AlarmDowObjTtc : 8;
    uint64_t AlarmDowObjID : 8;
    uint64_t AlarmLcaObjTtc : 8;
    uint64_t AlarmLcaObjID : 8;
    uint64_t AlarmBsdObjID : 8;
    uint64_t DrvFunc_AlarmModule : 8;
    uint64_t Resv1 : 8;

} COM_alarmObjInfo24Byte_t;


typedef struct
{
    uint64_t RollingCntMsg0x494 : 4;
    uint64_t Resv1 : 20;
    uint64_t ERR_SM905RFPower : 1;
    uint64_t ERR_SM906IFLpSat : 1;
    uint64_t ERR_SM907IFLpInput : 1;
    uint64_t ERR_SM908IRLpInput : 1;
    uint64_t ERR_SM910OverTemp : 1;
    uint64_t ERR_SM911ChirpMonitChk : 1;
    uint64_t Resv2 : 2;

    uint64_t ERR_SM133XIPSRamEcc : 1;
    uint64_t ERR_SM201VGAGainCheck : 1;
    uint64_t ERR_SM204SwCrcForI2C : 1;
    uint64_t ERR_SM206ExternalWd : 1;
    uint64_t ERR_SM207ExtrFlashCrc : 1;
    uint64_t ERR_SM805CfgRegRead : 1;
    uint64_t ERR_SM901LdoTest : 1;
    uint64_t ERR_SM904BandgapVoltMonit : 1;

    uint64_t ERR_SM104BBSRamEcc : 1;
    uint64_t ERR_SM105BBRomEcc : 1;
    uint64_t ERR_SM106CpuRamEcc : 1;
    uint64_t ERR_SM107CpuRomEcc : 1;
    uint64_t ERR_SM108OtpEcc : 1;
    uint64_t ERR_SM109SRamEcc : 1;
    uint64_t ERR_SM129CfgRegProt : 1;
    uint64_t ERR_SM130FmcwChirp : 1;

    uint64_t ERR_SM10Vga2OverSaturation : 1;
    uint64_t ERR_SM11IfException : 1;
    uint64_t ERR_SM12RfException : 1;
    uint64_t ERR_SM13Chirp : 1;
    uint64_t ERR_SM14FmcwOverTemp : 1;
    uint64_t ERR_SM101BasebandLbist : 1;
    uint64_t ERR_SM102CpuTcmEcc : 1;
    uint64_t ERR_SM103CpuCacheEcc : 1;

    uint64_t ERR_SM1LDO : 1;
    uint64_t ERR_SM2AVDD33Power : 1;
    uint64_t ERR_SM3DVDD11Power : 1;
    uint64_t ERR_SM4BandGapVoltage : 1;
    uint64_t ERR_SM5CPUClockLock : 1;
    uint64_t ERR_SM6RFPower : 1;
    uint64_t ERR_SM8TiaOverSaturation : 1;
    uint64_t ERR_SM9Vga1OverSaturation : 1;

    uint64_t Resv3 : 16;
    uint64_t ERR_InputVoltNotCalib : 1;
    uint64_t ERR_TimeSync : 1;
    uint64_t ERR_SWHWCompatCheck : 1;
    uint64_t ERR_SensorBlind : 1;
    uint64_t ERR_SensorPosDect : 1;
    uint64_t Resv4 : 3;
    uint64_t ERR_ChipSafetyFail : 1;

    uint64_t ERR_I2CComm : 1;
    uint64_t ERR_AddrLineUnstab : 1;
    uint64_t ERR_PPARFlt : 1;
    uint64_t ERR_Blockage : 1;
    uint64_t ERR_Interference : 1;
    uint64_t ERR_IMUCommFlt : 1;
    uint64_t ERR_PMICReset : 1;

    uint64_t ERR_VcanSendTimeout : 1;
    uint64_t ERR_PcanSendTimeout : 1;
    uint64_t ERR_LED1NotCalib : 1;
    uint64_t ERR_LED1ShortToGND : 1;
    uint64_t ERR_LED1OpenCircurt : 1;
    uint64_t ERR_LED2NotCalib : 1;
    uint64_t ERR_LED2ShortToGND : 1;
    uint64_t ERR_LED2OpenCircurt : 1;

    uint64_t ERR_IntMemoryFlt : 1;
    uint64_t ERR_MmicInitParamSet : 1;
    uint64_t ERR_MmicInitProc : 1;
    uint64_t ERR_PMICVoltFlt : 1;
    uint64_t ERR_WDogReset : 1;
    uint64_t ERR_AbnormalInterrupt : 1;
    uint64_t ERR_TimeGapOfDataProc : 1;
    uint64_t ERR_TimeGapOfSignalProc : 1;

    uint64_t ERR_VCANBusoff : 1;
    uint64_t ERR_PCANBusoff : 1;
    uint64_t ERR_VBATHigh : 1;
    uint64_t ERR_VBATLow : 1;
    uint64_t ERR_OverTemp : 1;
    uint64_t ERR_UnderTemp : 1;
    uint64_t ERR_NotALN : 1;
    uint64_t ERR_PCANLostCom : 1;
    uint64_t ChecksumMsg0x494 : 8;

} COM_errorTestCurrent16Byte_t;

typedef struct
{
    uint64_t RollingCntMsg0x494 : 4;
    uint64_t Resv1 : 20;
    uint64_t ERR_SM905RFPower : 1;
    uint64_t ERR_SM906IFLpSat : 1;
    uint64_t ERR_SM907IFLpInput : 1;
    uint64_t ERR_SM908IRLpInput : 1;
    uint64_t ERR_SM910OverTemp : 1;
    uint64_t ERR_SM911ChirpMonitChk : 1;
    uint64_t Resv2 : 2;

    uint64_t ERR_SM133XIPSRamEcc : 1;
    uint64_t ERR_SM201VGAGainCheck : 1;
    uint64_t ERR_SM204SwCrcForI2C : 1;
    uint64_t ERR_SM206ExternalWd : 1;
    uint64_t ERR_SM207ExtrFlashCrc : 1;
    uint64_t ERR_SM805CfgRegRead : 1;
    uint64_t ERR_SM901LdoTest : 1;
    uint64_t ERR_SM904BandgapVoltMonit : 1;

    uint64_t ERR_SM104BBSRamEcc : 1;
    uint64_t ERR_SM105BBRomEcc : 1;
    uint64_t ERR_SM106CpuRamEcc : 1;
    uint64_t ERR_SM107CpuRomEcc : 1;
    uint64_t ERR_SM108OtpEcc : 1;
    uint64_t ERR_SM109SRamEcc : 1;
    uint64_t ERR_SM129CfgRegProt : 1;
    uint64_t ERR_SM130FmcwChirp : 1;

    uint64_t ERR_SM10Vga2OverSaturation : 1;
    uint64_t ERR_SM11IfException : 1;
    uint64_t ERR_SM12RfException : 1;
    uint64_t ERR_SM13Chirp : 1;
    uint64_t ERR_SM14FmcwOverTemp : 1;
    uint64_t ERR_SM101BasebandLbist : 1;
    uint64_t ERR_SM102CpuTcmEcc : 1;
    uint64_t ERR_SM103CpuCacheEcc : 1;

    uint64_t ERR_SM1LDO : 1;
    uint64_t ERR_SM2AVDD33Power : 1;
    uint64_t ERR_SM3DVDD11Power : 1;
    uint64_t ERR_SM4BandGapVoltage : 1;
    uint64_t ERR_SM5CPUClockLock : 1;
    uint64_t ERR_SM6RFPower : 1;
    uint64_t ERR_SM8TiaOverSaturation : 1;
    uint64_t ERR_SM9Vga1OverSaturation : 1;

    uint64_t Resv3 : 16;
    uint64_t ERR_InputVoltNotCalib : 1;
    uint64_t ERR_TimeSync : 1;
    uint64_t ERR_SWHWCompatCheck : 1;
    uint64_t ERR_SensorBlind : 1;
    uint64_t ERR_SensorPosDect : 1;
    uint64_t Resv4 : 3;
    uint64_t ERR_ChipSafetyFail : 1;

    uint64_t ERR_I2CComm : 1;
    uint64_t ERR_AddrLineUnstab : 1;
    uint64_t ERR_PPARFlt : 1;
    uint64_t ERR_Blockage : 1;
    uint64_t ERR_Interference : 1;
    uint64_t ERR_IMUCommFlt : 1;
    uint64_t ERR_PMICReset : 1;

    uint64_t ERR_VcanSendTimeout : 1;
    uint64_t ERR_PcanSendTimeout : 1;
    uint64_t ERR_LED1NotCalib : 1;
    uint64_t ERR_LED1ShortToGND : 1;
    uint64_t ERR_LED1OpenCircurt : 1;
    uint64_t ERR_LED2NotCalib : 1;
    uint64_t ERR_LED2ShortToGND : 1;
    uint64_t ERR_LED2OpenCircurt : 1;

    uint64_t ERR_IntMemoryFlt : 1;
    uint64_t ERR_MmicInitParamSet : 1;
    uint64_t ERR_MmicInitProc : 1;
    uint64_t ERR_PMICVoltFlt : 1;
    uint64_t ERR_WDogReset : 1;
    uint64_t ERR_AbnormalInterrupt : 1;
    uint64_t ERR_TimeGapOfDataProc : 1;
    uint64_t ERR_TimeGapOfSignalProc : 1;

    uint64_t ERR_VCANBusoff : 1;
    uint64_t ERR_PCANBusoff : 1;
    uint64_t ERR_VBATHigh : 1;
    uint64_t ERR_VBATLow : 1;
    uint64_t ERR_OverTemp : 1;
    uint64_t ERR_UnderTemp : 1;
    uint64_t ERR_NotALN : 1;
    uint64_t ERR_PCANLostCom : 1;

    uint64_t ChecksumMsg0x494 : 8;

} COM_errorTestConfirm16Byte_t;

typedef struct
{
    uint64_t rdpTimeDiff1 : 8;
    uint64_t rdpTimeDiff2 : 8;
    uint64_t rdpTimeDiff3 : 8;
    uint64_t rdpTimeDiff4 : 8;
    uint64_t rdpTimeDiff5 : 8;
    uint64_t rdpTimeDiff6 : 8;
    uint64_t rdpTimeDiff7 : 8;
    uint64_t rdpTimeDiff8 : 8;// RDP周期
    
    uint64_t rspTimeDiff1 : 8;
    uint64_t rspTimeDiff2 : 8;
    uint64_t rspTimeDiff3 : 8;
    uint64_t rspTimeDiff4 : 8;
    uint64_t rspTimeDiff5 : 8;
    uint64_t rspTimeDiff6 : 8;
    uint64_t rspTimeDiff7 : 8;
    uint64_t rspTimeDiff8 : 8; // RSP周期

} COM_debugInfo_t;

/*************** Private Protocol, Little-Endian  ***************/
/*
 * 0x32D message, contains 2 objects of adjacent lane.
 * Left radar will send this meesage to right radar over PCAN.
 */
typedef struct
{
    /* Obj 1 */
    uint64_t adjacentLaneTarget_1     : 8;    /* 0x1: Left_Obj, 0x2: Rear_Obj, 0x3: Right_Obj */
    uint64_t lockTargetAbscissa_1     : 8;    // 锁定目标_横坐标
    uint64_t lockTargetOrdinate_1     : 8;    // 锁定目标_纵坐标
    uint64_t goalLengthRelativeSpd_1  : 8;    // 锁定目标_纵向相对速度
    uint64_t goalLateRelativeSpd_1    : 8;    // 锁定目标_横向相对速度
    uint64_t targetStateB6_1          : 2;    // 目标状态
    uint64_t resv1_1                  : 1;
    uint64_t targetStateB1_1          : 1;    // 目标状态
    uint64_t targetType_1             : 4;    // 目标类型
    uint64_t resv2_1                  : 4;
    uint64_t msgCounter_1             : 4;    // Message_counter36F
    uint64_t Checksum_1               : 8;    /* Checksum = (Byte1+Byte2+Byte3...+Byte7) XOR 0xFF */
    /* Obj 2 */
    uint64_t adjacentLaneTarget_2     : 8;    /* 0x1: Left_Obj, 0x2: Rear_Obj, 0x3: Right_Obj */
    uint64_t lockTargetAbscissa_2     : 8;    // 锁定目标_横坐标
    uint64_t lockTargetOrdinate_2     : 8;    // 锁定目标_纵坐标
    uint64_t goalLengthRelativeSpd_2  : 8;    // 锁定目标_纵向相对速度
    uint64_t goalLateRelativeSpd_2    : 8;    // 锁定目标_横向相对速度
    uint64_t targetStateB6_2          : 2;    // 目标状态
    uint64_t resv1_2                  : 1;
    uint64_t targetStateB1_2          : 1;    // 目标状态
    uint64_t targetType_2             : 4;    // 目标类型
    uint64_t resv2_2                  : 4;
    uint64_t msgCounter_2             : 4;    // Message_counter36F
    uint64_t Checksum_2               : 8;    /* Checksum = (Byte1+Byte2+Byte3...+Byte7) XOR 0xFF */
} CAN_msg_TwoObj_32D_t; 
/*************** Private Protocol, Little-Endian  ***************/


// void COM_sendDetObjectHeaderAndList(uint8_t canIdx , const RSP_DetObjectList_t *detObjectList);
// void COM_sendTrkObjectHeaderAndList(uint8_t canIdx , const RDP_TrkObjectList_t *trkObjectList);

void COM_sendDetObjectList(uint8_t canIdx);

void COM_sendTrkObjectList(uint8_t canIdx);

void COM_sendEndFrame(uint8_t canIdx);

void COM_sendVehicleInfoEcho(uint8_t canIdx);

void COM_sendAlarmInfo(uint8_t canIdx);

void APP_receiveFuncStateRLByBYDprotocol(uint8_t *pData);

void sendRadarInfo1(uint8_t canIdx);

void sendRadarInfo2(uint8_t canIdx);

void sendRadarInfo3(uint8_t canIdx);

void sendRadarInfo4(uint8_t canIdx);

void sendRadarInfo5(uint8_t canIdx);

void COM_sendDebugInfo(uint8_t canIdx);

CAN_msg_TwoObj_32D_t* APP_getLeftRadarTwoNearObjs(void);
CAN_msg_TwoObj_32D_t* APP_getRightRadarTwoNearObjs(void);
void PCAN_rightRadarSendTwoObjsToPCAN(uint8_t canIdx);
void PCAN_leftRadarSendTwoObjsToRightRadar(CAN_msg_TwoObj_32D_t *leftRadarTwoObj);
void PCAN_rightRadarRecvTwoObjsFromLeftRadar(uint8_t *pData);

#endif
