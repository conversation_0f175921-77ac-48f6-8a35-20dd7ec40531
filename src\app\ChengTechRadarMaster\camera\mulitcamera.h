﻿#ifndef MUILTCAMERA_H
#define MUILTCAMERA_H

#include "camera_global.h"

#include <QMainWindow>
#include <QCamera>
#include <QCameraInfo>
#include <QScopedPointer>

namespace Ui {
class MulitCamera;
}

//class FFmpegCamera;
class OpenCVCamera;

namespace Camera {



class CAMERA_EXPORT MulitCamera : public QMainWindow
{
    Q_OBJECT

public:
    static bool isOpened(const QCameraInfo &cameraInfo);

    explicit MulitCamera(const QCameraInfo &cameraInfo, const QString alais, QWidget *parent = nullptr);
    ~MulitCamera();

    bool isOpened();
    bool isSaveing();

signals:
    void open();

    void close();

    void startSaveVideo(const QString &savePath, quint64 saveTime);

    void stopSave();

    /** @brief 别名改变信号 */
    void aliasChenged(const QString alias);

    /** @brief 摄像头退出信号 */
    void cameraQuit(const QString &description);

    void cameraSaveIndex(int cIndex, long long sIndex);

public slots:
    /** @brief 关闭摄像头 */
    void quit();

    /** @brief 设置摄像头 */
    void settings();

    /** @brief 设置别名 */
    void setAlias();

    /** @brief 摄像头状态改变 */
    void updateCameraState(QCamera::State state);

private:
    Ui::MulitCamera *ui;

//    FFmpegCamera *mCamera;
    OpenCVCamera *mCamera;

//    QCamera* mCamera{0};        ///< 摄像头
    QCameraInfo mCameraInfo;    ///< 摄像头信息
    QString mAlias;             ///< 别名

    static int mCameraOpenCount;
    static QMap<QString /*description + deviecname*/, Camera::MulitCamera*> mListCameraInfo;     ///< 摄像头信息
};

} // namespace Camera

#endif // MUILTCAMERA_H
