﻿#include "networktcpserver.h"

#include <QTcpServer>
#include <QTcpSocket>
#include <QNetworkInterface>
#include <QMutex>

NetworkTcpServer::NetworkTcpServer(QObject *parent) : QObject(parent)
{

}

void NetworkTcpServer::openTCPServer(const QString &IP, quint16 port, bool analyIP)
{
    if (mOpened) {
        return;
    }

    if (!mTcpServer)
    {
        mTcpServer = new QTcpServer(this);
        connect(mTcpServer, &QTcpServer::newConnection, this, &NetworkTcpServer::connectNewTCPClient);
    }

    QHostAddress address = QHostAddress::Any;
    if (!analyIP && !IP.isEmpty())
    {
        address.setAddress(IP);
    }

    mTcpServer->listen(address, port);
    if (!mTcpServer->isListening())
    {
        qDebug() << __FUNCTION__ << __LINE__ << mTcpServer->errorString();
        emit tcpServerClosed();
        return;
    }

    mOpened = true;

    emit tcpServerOpened(true, IP);
}

void NetworkTcpServer::closeTCPServer()
{
    if (mTcpServer)
    {
        mTcpServer->close();
    }

    mOpened = false;

    emit tcpServerClosed();
}

void NetworkTcpServer::connectNewTCPClient()
{
    qDebug() << __FUNCTION__ << __LINE__;
    while (mTcpServer->hasPendingConnections())
    {
        mTcpClient = mTcpServer->nextPendingConnection();

        connect(mTcpClient, SIGNAL(readyRead()), this, SLOT(readTCPClientData()));
        connect(mTcpClient, SIGNAL(disconnected()), this, SLOT(disconnectedTCPClient()));
        connect(mTcpClient, SIGNAL(disconnected()), mTcpClient, SLOT(deleteLater()));

        connect(this, &NetworkTcpServer::write, this, &NetworkTcpServer::writeData);

        emit tcpClientConnected(mTcpClient->peerAddress().toString(), mTcpClient->peerPort());
        mClientConnected = true;
        break;
    }
}

void NetworkTcpServer::disconnectedTCPClient()
{
    qDebug() << __FUNCTION__ << __LINE__;
    emit tcpClientDisonnected();
    mClientConnected = false;
}

void NetworkTcpServer::readTCPClientData()
{
    QMutexLocker locker(&mReadMutex);
    QTcpSocket *tcpSocket = qobject_cast<QTcpSocket *>(sender());

    const QString recv_text=QString::fromUtf8(tcpSocket->readAll());

    qDebug() << __FUNCTION__ << __LINE__ << recv_text;

    emit read(recv_text.toUtf8());
}

void NetworkTcpServer::writeData(const QByteArray &data)
{
//    qDebug() << __FUNCTION__ << __LINE__ << data.mid(0, 4).toHex();
    if (mClientConnected)
    {
        int len = mTcpClient->write(data);
//        qDebug() << __FUNCTION__ << __LINE__ << len << data.mid(0, 4).toHex();
    }
}
