#ifndef ECUUPDATEFORM_H
#define ECUUPDATEFORM_H

#include <QWidget>
#include <QDomDocument>
#include <QTableView>
//#include <QTableWidget>
#include <QTextBrowser>


namespace Ui {
class ECUUpdateForm;
}

namespace Devices {
namespace Can {
class DeviceManager;
}
}

namespace Devices{
namespace Can{
class CanFrame;
}
}

class ECUUpdateItem;
class ECUUpdateModel;

class ECUUpdateForm : public QWidget
{
    Q_OBJECT

public:
    explicit ECUUpdateForm(Devices::Can::DeviceManager * deviceManager, QWidget *parent = nullptr);
    ~ECUUpdateForm();


private slots:
    void on_pushButtonSelect_clicked();

    void on_pushButtonLoad_clicked();

    void on_pushButtonRun_clicked();

    void showCanFrame(const Devices::Can::CanFrame &frame);
    void scrollTo( int row, quint8 channelIndex );
    void clear();

private:
    bool analysisShFile( const QString& fileName );
    bool analysisShExFile( const QString& fileName );
    bool createECUUpdateItem( const QDomElement& element );
    bool updateAddrs();
    bool getChildElement(const QDomElement &element, const QString &tagName, QDomElement& childElement);
    void insertSendCmdItem( const QDomElement &element );
    void updateWholeWordMatchForGeneralItem( const QDomElement &element );
    void updateSubStrMatchForGeneralItem( const QDomElement &element );


    void createTables();

private:
    Ui::ECUUpdateForm *ui;
    ECUUpdateItem* mSettingItem{NULL};
    Devices::Can::DeviceManager * mDeviceManager{NULL};

    QList<QTableView*> mCmdTables;
    QList<QTextBrowser*> mCanFrameLogs;
    QList<ECUUpdateModel*> mModels;
};

#endif // ECUUPDATEFORM_H
