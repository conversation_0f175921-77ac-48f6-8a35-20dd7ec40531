<?xml version="1.0"?>
<info locale="device_locale_strings.xml">
	<device>
		<value>0</value>
		<meta>
			<visible>false</visible>
			<type>options.int32</type>
			<desc>设备索引</desc>
			<options>
				<option type="int32" value="0" desc="0"></option>
				<option type="int32" value="1" desc="1"></option>
				<option type="int32" value="2" desc="2"></option>
				<option type="int32" value="3" desc="3"></option>
				<option type="int32" value="4" desc="4"></option>
				<option type="int32" value="5" desc="5"></option>
				<option type="int32" value="6" desc="6"></option>
				<option type="int32" value="7" desc="7"></option>
				<option type="int32" value="8" desc="8"></option>
				<option type="int32" value="9" desc="9"></option>
				<option type="int32" value="10" desc="10"></option>
				<option type="int32" value="11" desc="11"></option>
				<option type="int32" value="12" desc="12"></option>
				<option type="int32" value="13" desc="13"></option>
				<option type="int32" value="14" desc="14"></option>
				<option type="int32" value="15" desc="15"></option>
				<option type="int32" value="16" desc="16"></option>
				<option type="int32" value="17" desc="17"></option>
				<option type="int32" value="18" desc="18"></option>
				<option type="int32" value="19" desc="19"></option>
				<option type="int32" value="20" desc="20"></option>
				<option type="int32" value="21" desc="21"></option>
				<option type="int32" value="22" desc="22"></option>
				<option type="int32" value="23" desc="23"></option>
				<option type="int32" value="24" desc="24"></option>
				<option type="int32" value="25" desc="25"></option>
				<option type="int32" value="26" desc="26"></option>
				<option type="int32" value="27" desc="27"></option>
				<option type="int32" value="28" desc="28"></option>
				<option type="int32" value="29" desc="29"></option>
				<option type="int32" value="30" desc="30"></option>
				<option type="int32" value="31" desc="31"></option>
				<option type="int32" value="32" desc="32"></option>
				<option type="int32" value="33" desc="33"></option>
				<option type="int32" value="34" desc="34"></option>
				<option type="int32" value="35" desc="35"></option>
				<option type="int32" value="36" desc="36"></option>
				<option type="int32" value="37" desc="37"></option>
				<option type="int32" value="38" desc="38"></option>
				<option type="int32" value="39" desc="39"></option>
				<option type="int32" value="40" desc="40"></option>
				<option type="int32" value="41" desc="41"></option>
				<option type="int32" value="42" desc="42"></option>
				<option type="int32" value="43" desc="43"></option>
				<option type="int32" value="44" desc="44"></option>
				<option type="int32" value="45" desc="45"></option>
				<option type="int32" value="46" desc="46"></option>
				<option type="int32" value="47" desc="47"></option>
				<option type="int32" value="48" desc="48"></option>
				<option type="int32" value="49" desc="49"></option>
				<option type="int32" value="50" desc="50"></option>
				<option type="int32" value="51" desc="51"></option>
				<option type="int32" value="52" desc="52"></option>
				<option type="int32" value="53" desc="53"></option>
				<option type="int32" value="54" desc="54"></option>
				<option type="int32" value="55" desc="55"></option>
				<option type="int32" value="56" desc="56"></option>
				<option type="int32" value="57" desc="57"></option>
				<option type="int32" value="58" desc="58"></option>
				<option type="int32" value="59" desc="59"></option>
				<option type="int32" value="60" desc="60"></option>
				<option type="int32" value="61" desc="61"></option>
				<option type="int32" value="62" desc="62"></option>
				<option type="int32" value="63" desc="63"></option>
				<option type="int32" value="64" desc="64"></option>
				<option type="int32" value="65" desc="65"></option>
				<option type="int32" value="66" desc="66"></option>
				<option type="int32" value="67" desc="67"></option>
				<option type="int32" value="68" desc="68"></option>
				<option type="int32" value="69" desc="69"></option>
				<option type="int32" value="70" desc="70"></option>
				<option type="int32" value="71" desc="71"></option>
				<option type="int32" value="72" desc="72"></option>
				<option type="int32" value="73" desc="73"></option>
				<option type="int32" value="74" desc="74"></option>
				<option type="int32" value="75" desc="75"></option>
				<option type="int32" value="76" desc="76"></option>
				<option type="int32" value="77" desc="77"></option>
				<option type="int32" value="78" desc="78"></option>
				<option type="int32" value="79" desc="79"></option>
				<option type="int32" value="80" desc="80"></option>
			</options>
		</meta>
	</device>
	<channel>
		<value>0</value>
		<meta>
			<visible>false</visible>
			<type>options.int32</type>
			<desc>通道号</desc>
			<options>
				<option type="int32" value="0" desc="Channel 0"></option>
			</options>
		</meta>
		<channel_0 stream="channel_0" case="parent-value=0">
			<work_mode flag="0x0004" at_initcan="pre">
				<value>1</value>
				<meta>
					<type>options.int32</type>
					<desc>工作模式</desc>
					<options>
						<option type="int32" value="1" desc="mode_server"></option>
						<option type="int32" value="0" desc="mode_client"></option>
					</options>
				</meta>
			</work_mode>
			<local_port flag="0x0002" at_initcan="pre">
				<value>4001</value>
				<meta>
					<visible>$/info/channel/channel_0/work_mode == 0</visible>
					<type>uint32</type>
					<desc>本地端口</desc>
				</meta>
			</local_port>
			<ip flag="0x0000" at_initcan="pre">
				<value>*************</value>
				<meta>
					<visible>$/info/channel/channel_0/work_mode == 1</visible>
					<type>string</type>
					<desc>ip</desc>
				</meta>
			</ip>
			<work_port flag="0x0001" at_initcan="pre">
				<value>4003</value>
				<meta>
					<visible>$/info/channel/channel_0/work_mode == 1</visible>
					<type>uint32</type>
					<desc>工作端口</desc>
				</meta>
			</work_port>
		</channel_0>
	</channel>
</info>
