﻿#include "hesailider.h"

Pandar64_Header reverseHeaderData(Pandar64_Header *header)
{
    Pandar64_Header data = *header;

    data.EEFF = BIG_LITTLE_SWAP16(header->EEFF);

    return data;
}

Pandar64_Tail reverseTailData(Pandar64_Tail *tail)
{
    Pandar64_Tail data = *tail;

    data.MotorSpeed = BIG_LITTLE_SWAP16(tail->MotorSpeed);
    data.Timestamp = BIG_LITTLE_SWAP32(tail->Timestamp);
    data.UDPSequence = BIG_LITTLE_SWAP32(tail->UDPSequence);

    return data;
}
