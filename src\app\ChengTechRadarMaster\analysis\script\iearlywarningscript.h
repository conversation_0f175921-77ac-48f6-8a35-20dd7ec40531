﻿#ifndef IEARLYWARNINGSCRIPT_H
#define IEARLYWARNINGSCRIPT_H

#include <QObject>
#include <QList>

#include "analysis/analysisdata.h"

typedef struct EarlyWarningData{
    quint16 mID{0};
    AlarmType mType{Alarm_None};
    quint16 mLevel{0};
    double  mTTC{0};
}EarlyWarningType;

class IEarlyWarningScript : public QObject
{
    Q_OBJECT
public:
    explicit IEarlyWarningScript(QObject *parent = nullptr);

    bool load(const QString &filename);
    bool unload();

    bool isItialized() const { return mItialized; }

    /** @brief 加载脚本 */
    virtual bool loadScript(const QString &filename) = 0;
    /** @brief 卸载脚本 */
    virtual bool unloadScript() = 0;
    /** @brief 自车满足的报警类型 */
    virtual quint16 vehicleEarlyWarning(int radarID, quint16 alarmTypes, VehicleData *vehicleData) = 0;
    /** @brief 计算告警 */
    virtual bool earlyWaring(int radarID, Target *target, quint16 alarmTypes, quint16 vehicleAlarmTypes, VehicleData *vehicleData, QList<EarlyWarningData> &earlyWarningDatas) = 0;

signals:

protected:
    bool mItialized{false};

};

#endif // IEARLYWARNINGSCRIPT_H
