/*
 * @Author: your name
 * @Date: 2021-10-14 11:00:18
 * @LastEditTime: 2022-03-14 17:12:20
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \fw_base_original_sdk\calterah\common\user\msg_struct_typedef.h
 */

#ifndef _MSG_STRUCT_TYPEDEF_H_
#define _MSG_STRUCT_TYPEDEF_H_

//协议类型
// #define PROTOCOL_VER_0 0                  //距离精度 0.2 		速度精度 0.2 	角度精度 0.25
// #define PROTOCOL_VER_1 1                  //距离精度 0.1 		速度精度 0.1 	角度精度 0.25
// #define PROTOCOL_VER_2 2                  //距离精度 0.05 	速度精度 0.1 	角度精度 0.25
// #define PROTOCOL_VER_3 3				  //距离精度 0.2 		速度精度 0.2 	角度精度 0.01
// #define PROTOCOL_VER_4 4				  //  目前支持的上位机为canfd的那个版本
// #define PROTOCOL_VER_USING PROTOCOL_VER_4 //目前使用协议

typedef struct
{
    uint64_t tx1_obj_num_high_2bit : 2;
    uint64_t tx2_obj_num : 8;
    uint64_t ntc_temperature : 11;
    uint64_t chip_temperature : 11;
    uint64_t local_system_time : 32;
} msg0x302First8byte_t;

typedef struct
{
    uint64_t hw_ver_byte2 : 8;
    uint64_t hw_ver_byte1 : 8;
    uint64_t hw_ver_byte0 : 8;
    uint64_t tx2_power : 8;
    uint64_t tx1_power : 8;
    uint64_t mount_id_status : 2;
    uint64_t resv0 : 6;
    uint64_t batt_volt : 10;
    uint64_t tx1_obj_num_low_6bit : 6;
} msg0x302Second8byte_t;

typedef struct
{
    uint64_t time_gap_data_proc : 8;
    uint64_t time_gap_sigl_proc : 8;
    uint64_t num_of_raw_target : 8;
    uint64_t sw_ver_byte3 : 8;
    uint64_t sw_ver_byte2 : 8;
    uint64_t sw_ver_byte1 : 8;
    uint64_t sw_ver_byte0 : 8;
    uint64_t hw_ver_byte3 : 8;
} msg0x302Third8byte_t;

typedef struct
{
    uint64_t resv0 : 16;
    uint64_t sctl_status : 16;
    uint64_t plcm_status : 7;
    uint64_t plcm_en : 1;
    uint64_t msg_counter : 8;
    uint64_t time_gap_pcan_send : 8;
    uint64_t time_gap_vcan_send : 8;
} msg0x302Fourth8byte_t;


typedef struct
{
    uint64_t led_adc_base : 16;
    uint64_t led_adc_value : 16;
    uint64_t return_value_read_I2C : 16;
    uint64_t return_value_code_flash : 16;
} msg0x303First8byte_t;

typedef struct
{
    uint64_t resv0 : 1;
    uint64_t can0_send_err_cnt : 6;
    uint64_t can0_busoff_cnt : 3;
    uint64_t can0_reinit_cnt : 3;
    uint64_t can1_send_err_cnt : 5;
    uint64_t can1_busoff_cnt : 3;
    uint64_t can1_reinit_cnt : 3;
    uint64_t soc_cpu_load : 8;
    uint64_t pmic_under_volt : 8;
    uint64_t pmic_over_volt : 8;
    uint64_t reference_pll_status : 1;
    uint64_t fmcw_pll_status : 1;
    uint64_t resv2 : 10;
    uint64_t err_step_read_I2C : 4;
} msg0x303Second8byte_t;

typedef struct
{
    uint64_t chip_safety_rf_st_reg : 32;
    uint64_t chip_serial_number : 32;
} msg0x303Third8byte_t;

typedef struct
{
    // uint64_t resv : 16;
    uint64_t can1_error_type : 8;
    uint64_t can0_error_type : 8;
    uint64_t flash_error_step : 8;
    uint64_t pmic_wtd_st: 8;
    uint64_t chip_safety_digital_st_reg : 32;
} msg0x303Fourth8byte_t;


//目标列表头信息结构体
typedef struct
{
    uint64_t resv2 : 8;        //保留
    uint64_t senceFlag : 16;   //场景标识
    uint64_t resv : 10;        //保留
    uint64_t AgcType : 1;      //AGC类型
    uint64_t AgcSW : 1;        //AGC开关
    uint64_t version : 4;      //CAN协议版本
    uint64_t measCounter : 16; //周期计数
    uint64_t objectNum : 8;    //目标数量
} stObjStartMsg;

#if (PROTOCOL_VER_USING == PROTOCOL_VER_3)
typedef struct
{
    uint64_t snr : 8;          //SNR
    uint64_t latVel : 9;    //横向速度
    uint64_t angle : 15;    //角度
    uint64_t velocity : 11; //速度
    uint64_t range : 13;    //距离
    uint64_t objId : 8;     //目标ID
} stObjInfoMsg;   //角度分辨率提升

#elif (PROTOCOL_VER_USING == PROTOCOL_VER_4)
typedef struct
{
    uint64_t snr : 8;          //SNR
    // uint64_t dynProp : 3;      //运动状态
    uint64_t resv1 : 1;
    uint64_t resv2 : 2;
    uint64_t latVel : 9;    //横向速度
    uint64_t angle : 12;    //角度
    uint64_t velocity : 11; //速度
    uint64_t range : 13;    //距离
    uint64_t objId : 8;     //目标ID
} stObjInfoMsg;   //角度分辨率提升

#else

//目标信息结构体
typedef struct
{
    uint64_t snr : 8;          //SNR
    uint64_t dynProp : 3;      //运动状态
    uint64_t resv : 2;
    uint64_t latVel : 9;    //横向速度
    uint64_t angle : 10;    //角度
    uint64_t velocity : 11; //速度
    uint64_t range : 13;    //距离
    uint64_t objId : 8;     //目标ID
} stObjInfoMsg;

#endif

//目标列表头信息结构体
typedef struct
{
    uint64_t objectNum : 24;   //目标数量
    uint64_t PassWord : 40;    //保留
} ETC_stObjStartMsg;

typedef struct
{
    uint64_t snr : 8;          //SNR
    // uint64_t dynProp : 3;      //运动状态
    uint64_t resv1 : 1;
    uint64_t resv2 : 2;
    uint64_t latVel : 9;    //横向速度
    uint64_t angle : 12;    //角度
    uint64_t velocity : 11; //速度
    uint64_t range : 13;    //距离
    uint64_t objId : 8;     //目标ID
} RawObjInfo_t;

typedef struct
{
    uint64_t snr : 8;          //SNR
    uint64_t dynProp : 3;      //运动状态
    uint64_t latVel : 9;    //横向速度
    uint64_t angle : 12;    //角度
    uint64_t velocity : 11; //速度
    uint64_t range : 13;    //距离
    uint64_t objId : 8;     //目标ID
} TrkObjInfo_t;


//雷达结束帧
typedef struct
{
    uint64_t res2 : 15;
    uint64_t horizontalOffset : 6;    //横向偏移补偿
    uint64_t installAngleOffset : 11; //安装补偿角度
    uint64_t frameIntervalTime : 8;   //帧间隔时间
    uint64_t speedMode : 4;           //1-OBD 2-自测速
    uint64_t selfEn : 4;
    uint64_t angleOffset : 11; //自标定补偿角度
    uint64_t res : 1;
    uint64_t selfState : 4;
} stEndFrameMsg;


//原始目标信息扩展结构体
typedef struct
{
    uint64_t objSort : 16;  //高度角
    uint64_t obj_acc_y : 8;
    uint64_t obj_acc_x : 8;
    uint64_t dbfMag : 8;
    uint64_t objHeightAngle : 8; //高度角
    uint64_t objHeigh : 8;       //高度
    uint64_t objId : 8;          //目标ID
} stRawObjExtMsg;

//目标信息扩展结构体
typedef struct
{
    uint64_t objSort : 16;       //高度角
    uint64_t obj_acc_y : 8;
    uint64_t obj_acc_x : 8;
    uint64_t trkReiability : 8;
    uint64_t objHeightAngle : 8; //高度角
    uint64_t objHeigh : 8;       //高度
    uint64_t objId : 8;          //目标ID
} stObjExtMsg;

//目标信息扩展结构体 2
typedef struct
{
    uint64_t res : 2;            //
    uint64_t objMeasState : 3;   //测量状态、卡尔曼状态
    uint64_t objAngleRms : 5;    //rms
    uint64_t objAyRms : 5;       //rms
    uint64_t objAxRms : 5;       //rms
    uint64_t objVyRms : 5;       //rms
    uint64_t objVxRms : 5;       //rms
    uint64_t objYRms : 5;        //rms
    uint64_t objXRms : 5;        //rms
    uint64_t objWidth : 8;       //宽度
    uint64_t objlength : 8;      //高度
    uint64_t objId : 8;          //目标ID
} stObjExtMsg2;

typedef struct
{
    uint64_t MessageCounter : 8;
    uint64_t resv0 : 40;
    uint64_t LCASlaveSensorError : 1;    //0: normal, 1: failure
    uint64_t LCAWarnLedError : 1;        //0: normal, 1: failure
    uint64_t LCASystemState : 1;         //0: off 1: On
    uint64_t LCASwStsFeedback : 2;       //0: No Request, 1: LCA Switch off,  2: LCA Switch on,  3: Reserved
    uint64_t RCTASwStsFeedback : 2;      //0: No Request, 1: RCTA Switch OFF, 2: RCTA System ON, 3: Reserved
    uint64_t RCTASwStsFeedbackV : 1;     //0: Valid, 1: Invalid
    uint64_t sig_LCAWarningMode : 2;     //0: LCA/BSD, 1: RCTA, 2: Reserve, 3: Reserve
    uint64_t sig_LCAWarningLevel : 2;    //0: No warning, 1: warning level 1, 2: warning level 2, 3: Reserve
    uint64_t LCASystemWarmingEnable : 1; //0: Enable, 1: Off
    uint64_t LCASystemFailure : 1;       //0: normal; 1: failure
    uint64_t LCAMainSensorError : 1;     //0: normal, 1: failure
    uint64_t LCASwStsFeedbackV : 1;      //0: Valid, 1: Invalid
} msgLCA_369;

#endif