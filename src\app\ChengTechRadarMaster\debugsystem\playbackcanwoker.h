﻿#ifndef PLAYBACKCANWOKER_H
#define PLAYBACKCANWOKER_H

#include <QObject>
#include <QFile>
#include <QMap>
#include <QJsonObject>
#include <QJsonArray>
#include <QTimer>

#include "devices/devicemanager.h"
#include "analysis/analysisdata.h"

typedef struct CanDataFile
{
    QString mFileName;
    qint64 mSaveCount{0};
}CanDataFile;

typedef struct TargetFile
{
    QString mFileName;
    QMap<quint16, qint64> mSaveCount;
}TargetFile;

typedef struct TargetsFile
{
    TargetFile mTargetsFile[FrameTargetCount];
}TargetsFile;

typedef struct PlaybackTarget
{
    void print() const;

    quint8 mRadarID{0};
    quint64 mRadarFrameNumber{0};
    quint64 mSaveFrameNumber{0};
    QDateTime mFrameTime;
    QDateTime mSystemFrameTime;
    QDateTime mSaveTime;
    QList<Target> mTargets;

    TargetHeader mTargetHeader;
    Target mTrueTarget[5];                         // 真值目标点
    AlarmData mAlarmData;                       // 告警信息
    VehicleData mVehicleData;                   // 汽车数据
    EndFrameData mEndFrameData;

    quint32 mTotal{0};
}PlaybackTarget;

typedef struct PlaybackRadarTarget
{
    ~PlaybackRadarTarget() { qDeleteAll(mRadarTargets); mRadarTargets.clear(); }
    void print() const;

    QDateTime mFirstFrameTime;
    QMap<quint16, PlaybackTarget*> mRadarTargets;
}PlaybackRadarTarget;

class ProjectData
{
public:
    ProjectData(){}

    bool parseCSVFile(const QString &filename);
    bool parseProjectFile(const QJsonObject &jsonObject, const QString &projectPath);

    QDateTime mProjectDateTime;
    QList<CanDataFile> mCanDataFiles;
    QList<TargetsFile> mTargetsFiles;
};

class PlaybackCanWoker : public QObject
{
    Q_OBJECT
public:
    enum LineType
    {
        Begin,
        RadarID,
        RadarFrameNumber,
        SaveFrameNumber,
        FrameTime,
        SystemFrameTime,
        SaveTime,
        VehicleDataLine,
        AlarmDataLine,
        EndFrameDataLine,
        TargetHeadDataLine,
        TargetBegin,
        TargetEnd,
        End,
        TargetValue,
        Unknow
    };
    QString mHeaderString[Unknow]{
        "Begin",
        "RadarID",
        "RadarFrameNumber",
        "SaveFrameNumber",
        "FrameTime",
        "SystemFrameTime",
        "SaveTime",
        "VehicleData",
        "AlarmData",
        "EndFrameData",
        "TargetHeadData",
        "TargetBegin",
        "TargetEnd",
        "End",
        "TargetValue",
    };
    explicit PlaybackCanWoker(Devices::Can::DeviceManager *deviceManager, QObject *parent = nullptr);

    void setSingleStep(bool single) { mSinglePlay = single; }
    void setChannelIndex(int index) { mChannelIndex = index; }
    void setAlgorithmRadarID(quint8 id) { mAlgorithmRadarID = id; }
    void setAlgorithm(bool yes) { mAlgorithm = yes; }

    bool parseCSVFile(const QString &filename);
    bool parseProjectFile(const QString &filename);
    bool parseFile(AnalysisFrameType frameType, const QString &filename);
    int loadData();

    void initDataProcess();

signals:
    void playFrameTotal(int total);
    void playIndex(int index, int total, const QDateTime &time);
    void playStarted();
    void playFinished();

    void analysisTargets(quint8 radarID, /*AnalysisFrameType*/int frameType, const Targets &targets);
    void analysisRadarData(quint8 radarID, const AnalysisData &radarData);

public slots:
    void start(int startFrameIndex, bool recharge = false, bool single = false, bool algorithm = false, int algorithmRadarID = 0);
    void stop();
    void pause();
    void playNext();
    void playPrev();
    void play(int index);

private slots:
    void nextPlay();

private:
    LineType lineType(const QString &line);
    int loadTargetData(int index);
    bool parseTargetHeader(const QStringList &headers);
    bool loadTarget(AnalysisFrameType frameType, const QString &filename);
    bool parseLine(const QString &line, AnalysisFrameType frameType);
    void addTarget(PlaybackTarget *target, AnalysisFrameType frameType);

    QByteArray encodingVehicleData(const struct VehicleData &vehicleData);
    QByteArray encodingRawObjectHeaderData(const struct PlaybackTarget * const playbackData);
    QByteArray encodingRawObjectData(const QList<Target> targets, int index, int count = 1);
    QByteArray encodingTrackObjectHeaderData(const struct PlaybackTarget * const playbackData);
    QByteArray encodingTrackObjectData(const QList<Target> targets, int index, int count = 1);
    QByteArray encodingEndFrameData(const struct PlaybackTarget * const playbackData);

    AnalysisFrameType mPlaybackFrameType{FrameRawTarget};
    bool mRecharge{false};
    bool mSinglePlay{false};
    bool mAlgorithm{false};
    int mAlgorithmRadarID{0};

    ProjectData mProjectData;

    QString mPlaybackProjectFile;
    QString mPlaybackProjectPath;

    QFile mPlaybackFile[FrameTargetCount];
    QList<AnalysisType> mVehicleAnalysisTypes;
    QList<AnalysisType> mAlarmAnalysisTypes;
    QList<AnalysisType> mEndFrameAnalysisTypes;
    QList<AnalysisType> mTargetHeadAnalysisTypes;
    QList<AnalysisType> mTargetAnalysisTypes;
    bool mTargetBegin{false};

    QList<PlaybackRadarTarget*> mPlaybackRadarTargets[FrameTargetCount];
    PlaybackTarget *mCurrentPlaybackTarget;

    int mFrameTotal{0};
    int mPlayIndex{-1};
    QTimer *mPlayTimer{0};

    Devices::Can::DeviceManager *mDeviceManager{0};
    int mChannelIndex{0};
};

#endif // PLAYBACKCANWOKER_H
