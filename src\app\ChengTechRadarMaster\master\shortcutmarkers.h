﻿#ifndef SHORTCUTMARKERS_H
#define SHORTCUTMARKERS_H

#include <QWidget>

typedef struct ShortcutMarker
{
    QString mText;
    QKeySequence mKey;
}ShortcutMarker;

class FlowLayout;
class QPlainTextEdit;

class ShortcutMarkers : public QWidget
{
    Q_OBJECT

public:
    explicit ShortcutMarkers(QWidget *parent = nullptr);
    ~ShortcutMarkers();

    void setShortcutMarkers(const QList<ShortcutMarker> &markers);

public slots:
    void clear();
    void exportToFile();

private slots:
    void record();

private:
    void setupUi();

    QList<ShortcutMarker> mShortcutMarkers;
    FlowLayout *mFlowLayout{0};
    QPlainTextEdit *mPlainTextEditMarkers{0};
};

#endif // SHORTCUTMARKERS_H
