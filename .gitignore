# Directory
.*/
*.sln
/build/*
# 忽略所有 bin/Radar Master X.X.X_Debug 目录 
/bin/RadarMaster_Debug/*
/bin/RadarMaster_Release/*
/bin/Radar\ Master_Debug/*
/bin/Radar\ Master_Release/*
/bin/Radar\ Master\ *_Debug/*
/bin/Radar\ Master\ *_Release/*
/build/*
/src/app/ChengTechRadarMaster/settings.ini
/src/app/ChengTechRadarMaster/debug/*
/src/app/dataprocess/dataprocess.qtvscr
/src/app/dataprocess/debug/*.obj
/libs/
/src/app/ChengTechRadarMaster/Radar Master *.qtvscr
/src/app/ChengTechRadarMaster/Radar Master *.vcxproj
/src/app/ChengTechRadarMaster/Radar Master *.vcxproj.filters
/src/app/ChengTechRadarMaster/Radar Master *_resource.rc
/src/app/ChengTechRadarMaster/RadarMaster *.qtvscr
/src/app/ChengTechRadarMaster/RadarMaster *.vcxproj
/src/app/ChengTechRadarMaster/RadarMaster *.vcxproj.filters
/src/app/ChengTechRadarMaster/RadarMaster *_resource.rc
/src/app/ChengTechRadarMaster/ui_*.h
/src/app/ChengTechRadarMaster/*_alarmwidget.h
/src/app/ChengTechRadarMaster/*_analysisdatatableview.h
/src/app/ChengTechRadarMaster/*_analysisdataviewf.h
/src/app/ChengTechRadarMaster/*_calibrationform.h
/src/app/ChengTechRadarMaster/*_ctbinaryfiletools.h
/src/app/ChengTechRadarMaster/*_dataprocessform.h
/src/app/ChengTechRadarMaster/*_filebatchparsingform.h
/src/app/ChengTechRadarMaster/*_generalsettings.h
/src/app/ChengTechRadarMaster/*_hesailiderconfigdialog.h
/src/app/ChengTechRadarMaster/*_radarresetworkerform.h
/src/app/ChengTechRadarMaster/*_statisticsdialog.h
/src/app/ChengTechRadarMaster/app_version.h
/src/app/ChengTechRadarMaster/*.log
/src/app/ChengTechRadarMaster/release/*
/src/app/dataprocess/debug/dataprocess.tlog/CL.11728.write.1.tlog
/src/libs/devices/
/src/libs/libs.sln
/src/libs/network/
/src/libs/utils/
/src/app/dataprocess/debug/*.tlog
/src/app/dataprocess/debug/dataprocess.tlog/*.tlog
/src/app/dataprocess/debug/*.log

*.user

# vscode temp files
*.vscode/

# 仍然跟踪特定的 DLL 文件
# !/bin/RadarMaster_Debug/*.dll
# !/bin/RadarMaster_Debug/settings.ini
# !/bin/RadarMaster_Debug/config.ini
# !/bin/Radar\ Master_Debug/*.dll
# !/bin/Radar\ Master_Debug/settings.ini
# !/bin/Radar\ Master_Debug/config.ini
# !/bin/Radar\ Master\ *_Debug/*.dll
# !/bin/Radar\ Master\ *_Debug/settings.ini
# !/bin/Radar\ Master\ *_Debug/config.ini 
