﻿#ifndef TRUETARGETSMONITOR_H
#define TRUETARGETSMONITOR_H

#include "analysis/analysisdata.h"

#include <QWidget>

class QCPGraph;
class QCPItemTracer;
class QCPItemText;

class QTabWidget;

namespace Ui {
class trueTargetMonitor;
class trueTargetsMonitor;
}

namespace Analysis{
class AnalysisManager;
};

class trueTargetMonitor_CB
{
public:
    virtual QString getMsg( quint64 index ) = 0;
};

class trueTargetMonitor : public QWidget
{
    Q_OBJECT

public:
    explicit trueTargetMonitor(trueTargetMonitor_CB* cb, QWidget *parent = nullptr);
    ~trueTargetMonitor();

    void setTitle( const QString& title ); //标题
    void setUnit( const QString& unit ); //单位

    void addData( double targetValue, double searchValue, double trueValue, bool bSearchErr );
    void clear();
    void exportPdf( const QString& path );

public slots:
    //设置曲线是否显示
    void showGraph( bool bTarget, bool bSearch, bool bTrue );

private slots:
    void showInfo(QMouseEvent *e);

private:
    void showTitle();
    bool updateXAxisRang( double max );
    bool updateYAxisRang( double min, double max );
    void updateAxis();
    bool getSearchGraph( bool bErrValue, QCPGraph** preGraph, QCPGraph** curGraph );
    //更新差值
    void updateDiffValue( double targetValue, double searchValue, double trueValue, bool bSearchErr );
    //更新折线
    void updateGraph( double targetValue, double searchValue, double trueValue, bool bSearchErr );

private:
    QCPGraph *mGraphTarget{0};  //指定跟踪点 折线
    QCPGraph *mGraphTrue{0};    //真值 折线
    QList<QCPGraph*> mGraphSearch; //搜寻跟踪点折线

    QCPItemTracer* mTracer{0};
    QCPItemText* mTracerLabel{0}; //悬浮文本

    double mPreSearchValue{0};
    bool mPreSearchValueErr{false};
    bool mShowSearchGraph{true};


private:
    quint64 mDataCount{0};  //点数量
    quint64 mMaxX{100};     //X轴最大值
    double mMaxY{1};        //Y轴最大值
    double mMinY{0};        //Y轴最大值
    QString mUnit{"Unit"};      //Y轴单位
    QString mTitle{"Title"};     //标题
    //qint32 mCurTargetID{-1};  //当前的跟踪点ID

    double mTargetMaxDiff{0}; //指定跟踪点  最大差值
    double mTargetAveDiff{0}; //指定跟踪点  平均差值
    double mSearchMaxDiff{0}; //搜寻跟踪点  最大差值
    double mSearchAveDiff{0}; //搜寻跟踪点  平均差值

private:
    Ui::trueTargetMonitor *ui;
    trueTargetMonitor_CB* mCallBack;
};

class trueTargetsMonitor : public QWidget , public trueTargetMonitor_CB
{
    Q_OBJECT

    struct nodeItem{
        qint64 targetNo; //跟踪点计数
        qint32 searchTargetID{-1}; //搜寻的跟踪点ID
        quint32 targetID; //跟踪点ID
    };

    struct monitorItem{
        AnalysisType monitorType;
        trueTargetMonitor* monitor;
    };

public:
    explicit trueTargetsMonitor(Analysis::AnalysisManager* analysisMgr, QWidget *parent = nullptr);
    ~trueTargetsMonitor();
public:
    virtual QString getMsg( quint64 index );

public slots:
    void analysisFinished(quint8 radarID, const AnalysisData &analysisData);
    void clear();

private slots:
    void on_pushButtonStart_clicked();

    void on_pushButtonStop_clicked();

    void on_pushButtonClear_clicked();

    void showGraph();

    void on_pushButtonExportPDF_clicked();

private:
    void init();
    void createGraph();
    void createMonitor( AnalysisType type );
    const Target* calcTraget( const AnalysisData &analysisData); //计算跟踪点ID
    void startOrStop( bool bStart );

private:
//    trueTargetMonitor* mMonitorX{NULL};
//    trueTargetMonitor* mMonitorY{NULL};
//    trueTargetMonitor* mMonitorVX{NULL};
//    trueTargetMonitor* mMonitorVY{NULL};
    QList<monitorItem> mMonitorList;
    QTabWidget* mTabWidget{NULL};

private:
    quint8 mRadarID{5};
    quint32 mTargetID{0}; //指定的跟踪点ID
    quint32 mSearchRang{5};//搜寻范围
    quint8 mTrueTargetIndex{0};
//    bool mAutoTargetID{true};
    QList<nodeItem> mNodes;

private:
    Ui::trueTargetsMonitor *ui;
    Analysis::AnalysisManager* mAnalysisMgr{NULL};
};

#endif // TRUETARGETSMONITOR_H
