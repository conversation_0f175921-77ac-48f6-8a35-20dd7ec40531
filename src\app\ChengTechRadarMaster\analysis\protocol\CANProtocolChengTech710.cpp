﻿#include "CANProtocolChengTech710.h"

#define _USE_MATH_DEFINES //需要放在math前,之后才可以使用M_PI等match定义参数
#include <math.h>
#include <iostream>

#define TX_CH_LONG_RANGE 0x1       //远距离天线
#define TX_CH_SHORT_RANGE 0x2      //近距离天线

namespace Analysis {

namespace Protocol {
        CANProtocolChengTech710::CANProtocolChengTech710(AnalysisWorker *analysisWorker, QObject *parent)
            : IAnalysisProtocol(analysisWorker, parent)
        {
		}
		CANProtocolChengTech710::~CANProtocolChengTech710()
		{
		}
        bool CANProtocolChengTech710::analysisFrame(const Devices::Can::CanFrame &frame)
		{
//            frame.print();
			bool ret = false;
            switch (frame.id())
			{
            case 0x81:
                ret = parse0x81(frame);
                break;
            case 0x82:
                ret = parse0x82(frame);
                break;
			case 0x5F0:
                ret = parse0x5F0(frame);
				break;
            case 0x600:
                ret = parse0x600(frame);
                break;
            case 0x710:
                ret = parse0x710(frame);
                break;
            case 0x720:
                ret = parse0x720(frame);
                break;
			default:
                return false;
			}

//            qDebug() << __FUNCTION__ << __LINE__ << frame.idHex() << frame.dataHex();

            return ret;
        }

        bool CANProtocolChengTech710::parse0x81(const Devices::Can::CanFrame &frame)
        {
            if (frame.length() != 8) {
                    return false;
            }

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            const uint8_t *data = (const uint8_t *)frame.data().data();

            Parser::ParsedDataTypedef::VehicleInfomation &vehicleInfo = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mVehicleInfomation;
            vehicleInfo.mYawRate = ((((data[1]) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.1) - 102.4);
            vehicleInfo.mWhellSpeedRearRight = ((((data[3] & 0xF0U) >> 4) + (((uint16_t)data[2] & 0xFFU) << 4)) * 0.1);
            vehicleInfo.mWhellSpeedRearLeft = (((data[4]) + (((uint16_t)data[3] & 0xFU) << 8)) * 0.1);
            vehicleInfo.mWhellSpeedFrontRight = ((((data[6] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0xFFU) << 4)) * 0.1);
            vehicleInfo.mWhellSpeedFrontLeft = (((data[7]) + (((uint16_t)data[6] & 0xFU) << 8)) * 0.1);

            return true;
        }

        bool CANProtocolChengTech710::parse0x82(const Devices::Can::CanFrame &frame)
        {
            if (frame.length() != 8) {
                    return false;
            }

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            const uint8_t *data = (const uint8_t *)frame.data().data();

            Parser::ParsedDataTypedef::VehicleInfomation &vehicleInfo = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mVehicleInfomation;
            vehicleInfo.mV = (((((data[1] & 0xF0U) >> 4) + (((uint16_t)data[0] & 0xFFU) << 4)) * 0.025) - 20);
            vehicleInfo.mActualGear = (data[1] & 0xFU);
            vehicleInfo.mAx = (((((data[3] & 0xF0U) >> 4) + (((uint16_t)data[2] & 0xFFU) << 4)) * 0.0271267) - 21.593);
            vehicleInfo.mAy = ((((data[4]) + (((uint16_t)data[3] & 0xFU) << 8)) * 0.0271267) - 21.593);
            vehicleInfo.mSteeringAngleSpeed = (((data[5])) * 0.25);
            vehicleInfo.mSteeringAngle = (((data[7]) + (((uint16_t)data[6] & 0xFFU) << 8)) * 0.1);

            return true;
        }

        static int32_t decode_sign_bit(uint32_t data, uint8_t bits) {
            uint32_t mask = ((1 << bits) - 1);
            uint32_t extracted = data & mask;
            int32_t sign_extended = (extracted & (1 << (bits - 1))) ? (int)(extracted | (~mask)) : (int)extracted;
            return sign_extended;
        }

        bool CANProtocolChengTech710::parse0x5F0(const Devices::Can::CanFrame &frame)
		{
            if (frame.length() != 8) {
                return false;
            }
            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            const uint8_t *data = (const uint8_t *)frame.data().data();
            Parser::ParsedDataTypedef::RadarInfomation &radarInfo = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mRadarInfomation;
            //                    radarInfo.mProcessingTime = (((uint16_t)data[1]) << 8) + data[0];

            //std::cout << std::hex << (uint32_t)data[1] << " " << (uint32_t)data[0] << " " << std::dec << mParsedData->mRawTargets.mProcessingTime << std::endl;

            radarInfo.mSelfCalibrationAccumulativeCount = ((data[0]) * 4);
            radarInfo.mSelfCalibrationAccAngle = ((decode_sign_bit(data[1], 8)) * 0.1);
            radarInfo.mOffLinePitchAngleOffset = ((decode_sign_bit(data[2], 8)) * 0.1);
            radarInfo.mOffLinePitchAngleOffset = ((decode_sign_bit(data[3], 8)) * 0.1);
            radarInfo.mFrameIntervalTime = (data[4]);
            radarInfo.mSpeedMode = ((data[5] & 0xF0U) >> 4);
            radarInfo.mSelfCalibrationAccumulativeEnable = (data[5] & 0xFU);
            radarInfo.mSelfCalibrationAngleOffset = ((decode_sign_bit(data[6], 8)) * 0.1);
            radarInfo.mSelfCalibrationBuffCount = (data[7]);

            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetTrack].mProtocolType = Parser::ParsedDataTypedef::ChengTech710;
            analysisEnd(Parser::ParsedDataTypedef::TargetRaw);

            return false; // 因为其它协议可能也使用0x5F0作为结束帧
		}

        bool CANProtocolChengTech710::parse0x600(const Devices::Can::CanFrame &frame)
		{
            if (frame.length() == 16) {
                parse0x600_VERSION_7_8(frame);
            } else if (frame.length() == 8){
                parse0x600_VERSION_1_6(frame);
            } else {
				return false;
			}

            return true;
        }

        bool CANProtocolChengTech710::parse0x600_VERSION_1_6(const Devices::Can::CanFrame &frame)
        {
            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].resetTargets();

            const uint8_t *data = (const uint8_t *)frame.data().data();
            Parser::ParsedDataTypedef::TargetsF &targets = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw];
            targets.mRadarInfomation.mUploadObjectNumber = (data[0]);
            targets.mRadarInfomation.mMeasurementCounter = ((data[2] & 0xFFU) + (((uint16_t)data[1] & 0xFFU) << 8));
            targets.mRadarInfomation.mProtocolVersion = ((data[3] & 0xF0U) >> 4);
            targets.mRadarInfomation.mTxChannel = ((data[3] & 0x8U) >> 3);
            targets.mRadarInfomation.mNoise = ((data[4] & 0xFFU) + (((uint16_t)data[3] & 0x7U) << 8));
            targets.mRadarInfomation.mSenceFlag = ((data[6] & 0xFFU) + (((uint16_t)data[5] & 0xFFU) << 8));
            targets.mRadarInfomation.mWaveType = ((data[7] & 0x80) >> 7);
            targets.mRadarInfomation.mUploadObjectNumber += ((data[7] & 0x60U) >> 6) * 256;
//            header.resv = (data[7] & 0x1F);
            return true;

        }

        bool CANProtocolChengTech710::parse0x600_VERSION_7_8(const Devices::Can::CanFrame &frame)
        {
            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].resetTargets();

            const uint8_t *data = (const uint8_t *)frame.data().data();
            Parser::ParsedDataTypedef::TargetsF &targets = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw];

            targets.mRadarInfomation.mProtocolVersion = (data[0]);
            targets.mRadarInfomation.mNoise = ((data[1]) * 0.5);
//            DataAnalysisRTargetInfoFrame->noise_global = ((data[2]) * 0.5);
            targets.mRadarInfomation.mSenceFlag = (data[3]);
        //	userData->rawHeadMagOffset = ((((data[4] & 0xF0U) >> 4) * 32) - 256);
        //	userData->rawHeadRollingCnt = (data[4] & 0xFU);
            targets.mRadarInfomation.mUploadObjectNumber = (((data[6] & 0xC0U) >> 6) + (((uint16_t)data[5] & 0xFFU) << 2));
        //    userData->rawHeadCurFrameMode = ((data[6] & 0x3CU) >> 2);
            targets.mRadarInfomation.mWaveType = ((data[6] & 0x2U) >> 1);
        //    userData->rawHeadFrameInterval = (((data[7] & 0xFFU) + (((uint16_t)data[6] & 0x1U) << 8)) * 0.5);
            targets.mRadarInfomation.mMeasurementCounter = ((data[9] & 0xFFU) + (((uint16_t)data[8] & 0xFFU) << 8));
        //    userData->rawHeadRspTaskCycleTime = ((((data[11] & 0x80U) >> 7) + (((uint16_t)data[10] & 0xFFU) << 1)) * 0.5);
        //    userData->rawHeadNumAfterCFAR = (((data[12] & 0xE0U) >> 5) + (((uint16_t)data[11] & 0x7FU) << 3));
        //    userData->rawHeadCheckSum = (data[15]);

            return true;
        }

        bool CANProtocolChengTech710::parse0x710_8(const Devices::Can::CanFrame &frame)
		{
            if (frame.length() != 8) {
				return false;
			}

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }

            const uint8_t *data = (const uint8_t *)frame.data().data();
            Parser::ParsedDataTypedef::TargetF &target = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
            target.mValid = true;
            switch(mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mRadarInfomation.mProtocolVersion) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            {
                target.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;

                target.mMAG = ((data[0] & 0xFEU) >> 1);
                target.mSNR = ((((data[2] & 0x80U) >> 7) + (((uint32_t)data[1]) << 1) + (((uint32_t)data[0] & 0x1U) << 9)) * 0.2);
                uint16_t angle = (((data[3] & 0xF8U) >> 3) + (((uint16_t)data[2] & 0x7FU) << 5));
                target.mAngle = (((float)((int16_t)((angle & 0x800) ? (angle | 0xF000) : (angle & 0xFFF)))) * 0.05);
                uint16_t v = (((data[5] & 0x80U) >> 7) + (((uint32_t)data[4]) << 1) + (((uint32_t)data[3] & 0x7U) << 9));
                target.mV = (((float)((int16_t)((v & 0x800) ? (v | 0xF000) : (v & 0xFFF)))) * 0.05);
                uint16_t range = (((data[6] & 0xFCU) >> 2) + (((uint16_t)data[5] & 0x7FU) << 6));
                target.mRange = ((((data[6] & 0xFCU) >> 2) + (((uint16_t)data[5] & 0x7FU) << 6)) * 0.05);
                target.mPitchAngle = ((((data[7] & 0xFCU) >> 2) + (((uint16_t)data[6] & 0x3U) << 6)) * 0.15);
                target.mTXChannel = (data[7] & 0x3U);

                if (target.mTXChannel == TX_CH_SHORT_RANGE) {
                    target.mPitchAngle = target.mPitchAngle * 0.3f;
                }

                target.mX = target.mRange * std::sinf(target.mAngle * M_PI / 180);
                target.mZ = target.mRange * std::sinf(target.mPitchAngle * M_PI / 180);
                target.mY = std::sqrt(target.mRange * target.mRange - target.mX * target.mX - target.mZ * target.mZ);//target.mRange * std::cosf(target.mAngle * M_PI / 180);
            }
                break;
            case 6:
            default:
                break;
            }

            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;

            return true;
        }

        bool CANProtocolChengTech710::parse0x710_64_V6(const Devices::Can::CanFrame &frame)
		{
            if (frame.length() != 64) {
				return false;
			}

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            const uint8_t *data = (const uint8_t *)frame.data().data();
			for (int i = 0; i < 4; ++i, data += 16) {
				//目标1
                if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
					return false;
				}
				//qDebug() << __FUNCTION__ << __LINE__ << data << i << QString::number(data[0], 16);
				float distance = ((((data[1] & 0xF8U) >> 3) + (((uint16_t)data[0] & 0xFFU) << 5)) * 0.05);
				if (distance == 0)
				{
					continue;
				}

                Parser::ParsedDataTypedef::TargetF &target = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
				target.mValid = true;

                target.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;
				target.mRange = ((((data[1] & 0xF8U) >> 3) + (((uint16_t)data[0] & 0xFFU) << 5)) * 0.05);
                target.mTXChannel = ((data[1] & 0x6U) >> 1);
                target.mUsing = (data[1] & 0x1U);
//                target.Obj1_Res_1 = (data[2]);
                target.mV = (((((data[4] & 0xF0U) >> 4) + (((uint16_t)data[3] & 0xFFU) << 4)) * 0.05) - 102.4);
                target.mVy = ((((data[5] & 0xFFU) + (((uint16_t)data[4] & 0xFU) << 8)) * 0.05) - 102.4);
				target.mAngle = (((((data[7] & 0xF0U) >> 4) + (((uint16_t)data[6] & 0xFFU) << 4)) * 0.05) - 102.4);
				target.mPitchAngle = ((((data[8] & 0xFFU) + (((uint16_t)data[7] & 0xFU) << 8)) * 0.05) - 102.4);
                target.mRCS = (((((data[10] & 0xF0U) >> 4) + (((uint16_t)data[9] & 0xFFU) << 4)) * 0.05) - 102.4);
                target.mSNR = (((data[11] & 0xFFU) + (((uint16_t)data[10] & 0xFU) << 8)) * 0.05);
                target.mMAG = ((data[12] & 0xFEU) >> 1);
//                target.Obj1_Res_2 = (((data[13] & 0xF0U) >> 4) + (((uint16_t)data[12] & 0x1U) << 4));
                target.mMatchFlag = ((data[13] & 0xCU) >> 2);
//                target.Obj1_Res_3 = ((data[14] & 0xFFU) + (((uint16_t)data[13] & 0x3U) << 8));
//                target.Obj1_Res_4 = (data[15]);

                target.mX = target.mRange * std::sinf(target.mAngle * M_PI / 180);
				target.mZ = target.mRange * std::sinf(target.mPitchAngle * M_PI / 180);
                target.mY = target.mRange * std::cosf(target.mAngle * M_PI / 180);
//				target.mY = std::sqrt(target.mRange * target.mRange - target.mX * target.mX - target.mZ * target.mZ);//target.mRange * std::cosf(target.mAngle * M_PI / 180);

                //std::cout
                //	<< target.mID << " " << target.mRange << " " << target.mAngle << " " << target.mPitchAngle << " "
                //	<< target.mX << " " << target.mY << " " << target.mZ << " "
                //	<< (target.mRange * target.mRange - target.mX * target.mX - target.mZ * target.mZ) << std::endl;

                mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;
			}

            return true;
        }

        bool CANProtocolChengTech710::parse0x710_64_V7(const Devices::Can::CanFrame &frame)
        {
            if (frame.length() != 64) {
                qDebug() << __FUNCTION__ << __LINE__ << "Data length error![64]!" << frame.idHex() << frame.dataHex();
                return false;
            }

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            const uint8_t *data = (const uint8_t *)frame.data().data();
            //目标1
            if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }
            float distance = (((data[1] & 0xFFU) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.01);
            if(distance == 0) {
                return true;
            }
            Parser::ParsedDataTypedef::TargetF &target_1 = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
            target_1.mValid = true;

            target_1.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;

            target_1.mRange = (((data[1] & 0xFFU) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.01);
            target_1.mV = (((((data[3] & 0xF0U) >> 4) + (((uint16_t)data[2] & 0xFFU) << 4)) * 0.05) - 112.4);
            target_1.mAngle = ((((data[4] & 0xFFU) + (((uint16_t)data[3] & 0xFU) << 8)) * 0.05) - 102.4);
            target_1.mPitchAngle = (((((data[6] & 0xF0U) >> 4) + (((uint16_t)data[5] & 0xFFU) << 4)) * 0.05) - 102.4);
            target_1.mMatchFlag = ((data[6] & 0xCU) >> 2);
        //    target_1.obj1_rawVeloBin = ((data[7] & 0xFFU) + (((uint16_t)data[6] & 0x3U) << 8));
            target_1.mRCS = (((data[8]) * 0.5) - 50);
            target_1.mSNR = ((data[9]) * 0.5);
            target_1.mKalmanState = (((data[11] & 0xC0U) >> 6) + (((uint16_t)data[10] & 0xFFU) << 2));
        //    target_1.obj1_rawMeasQuality = ((data[11] & 0x3FU) * 2);
            target_1.mMAG = ((data[12] & 0xFEU) >> 1);
            target_1.mValid = target_1.mRange > 0;
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;

        //    qDebug() << __FUNCTION__ << __LINE__ << QString::number(id, 16) << datAry.toHex(' ');
        //    qDebug() << __FUNCTION__ << __LINE__ << gRawRecObjCnt << target_1.distance << target_1.angle;

            if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }
            distance = (((data[17] & 0xFFU) + (((uint16_t)data[16] & 0xFFU) << 8)) * 0.01);
            if(distance == 0) {
                return true;
            }

            Parser::ParsedDataTypedef::TargetF &target_2 = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
            target_2.mValid = true;
            target_2.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;

            target_2.mRange = (((data[17] & 0xFFU) + (((uint16_t)data[16] & 0xFFU) << 8)) * 0.01);
            target_2.mV = (((((data[19] & 0xF0U) >> 4) + (((uint16_t)data[18] & 0xFFU) << 4)) * 0.05) - 112.4);
            target_2.mAngle = ((((data[20] & 0xFFU) + (((uint16_t)data[19] & 0xFU) << 8)) * 0.05) - 102.4);
            target_2.mPitchAngle = (((((data[22] & 0xF0U) >> 4) + (((uint16_t)data[21] & 0xFFU) << 4)) * 0.05) - 102.4);
            target_2.mMatchFlag = ((data[22] & 0xCU) >> 2);
        //    target_2.obj2_rawVeloBin = ((data[23] & 0xFFU) + (((uint16_t)data[22] & 0x3U) << 8));
            target_2.mRCS = (((data[24]) * 0.5) - 50);
            target_2.mSNR = ((data[25]) * 0.5);
            target_2.mKalmanState = (((data[27] & 0xC0U) >> 6) + (((uint16_t)data[26] & 0xFFU) << 2));
        //    target_2.obj2_rawMeasQuality = ((data[27] & 0x3FU) * 2);
            target_2.mMAG = ((data[28] & 0xFEU) >> 1);
            target_2.mValid = target_2.mRange > 0;
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;

            if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }
            distance = (((data[33] & 0xFFU) + (((uint16_t)data[32] & 0xFFU) << 8)) * 0.01);
            if(distance == 0) {
                return true;
            }

            Parser::ParsedDataTypedef::TargetF &target_3 = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
            target_3.mValid = true;
            target_3.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;

            target_3.mRange = (((data[33] & 0xFFU) + (((uint16_t)data[32] & 0xFFU) << 8)) * 0.01);
            target_3.mV = (((((data[35] & 0xF0U) >> 4) + (((uint16_t)data[34] & 0xFFU) << 4)) * 0.05) - 112.4);
            target_3.mAngle = ((((data[36] & 0xFFU) + (((uint16_t)data[35] & 0xFU) << 8)) * 0.05) - 102.4);
            target_3.mPitchAngle = (((((data[38] & 0xF0U) >> 4) + (((uint16_t)data[37] & 0xFFU) << 4)) * 0.05) - 102.4);
            target_3.mMatchFlag = ((data[38] & 0xCU) >> 2);
        //    target_3.obj3_rawVeloBin = ((data[39] & 0xFFU) + (((uint16_t)data[38] & 0x3U) << 8));
            target_3.mRCS = (((data[40]) * 0.5) - 50);
            target_3.mSNR = ((data[41]) * 0.5);
            target_3.mKalmanState = (((data[43] & 0xC0U) >> 6) + (((uint16_t)data[42] & 0xFFU) << 2));
        //    target_3.obj3_rawMeasQuality = ((data[43] & 0x3FU) * 2);
            target_3.mMAG = ((data[44] & 0xFEU) >> 1);
            target_3.mValid = target_3.mRange > 0;
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;

            if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }
            distance = (((data[49] & 0xFFU) + (((uint16_t)data[48] & 0xFFU) << 8)) * 0.01);
            if(distance == 0) {
                return true;
            }

            Parser::ParsedDataTypedef::TargetF &target_4 = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
            target_4.mValid = true;
            target_4.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;

            target_4.mRange = (((data[49] & 0xFFU) + (((uint16_t)data[48] & 0xFFU) << 8)) * 0.01);
            target_4.mV = (((((data[51] & 0xF0U) >> 4) + (((uint16_t)data[50] & 0xFFU) << 4)) * 0.05) - 112.4);
            target_4.mAngle = ((((data[52] & 0xFFU) + (((uint16_t)data[51] & 0xFU) << 8)) * 0.05) - 102.4);
            target_4.mPitchAngle = (((((data[54] & 0xF0U) >> 4) + (((uint16_t)data[53] & 0xFFU) << 4)) * 0.05) - 102.4);
            target_4.mMatchFlag = ((data[54] & 0xCU) >> 2);
        //    target_4.obj4_rawVeloBin = ((data[55] & 0xFFU) + (((uint16_t)data[54] & 0x3U) << 8));
            target_4.mRCS = (((data[56]) * 0.5) - 50);
            target_4.mSNR = ((data[57]) * 0.5);
            target_4.mKalmanState = (((data[59] & 0xC0U) >> 6) + (((uint16_t)data[58] & 0xFFU) << 2));
        //    target_4.obj4_rawMeasQuality = ((data[59] & 0x3FU) * 2);
            target_4.mMAG = ((data[60] & 0xFEU) >> 1);
            target_4.mValid = target_4.mRange > 0;
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;

        //    userData->rawRollingCnt = (data[62] & 0xFU);
            //	  userData->rawCheckSum = (data[63]);
            return true;
        }

        bool CANProtocolChengTech710::parse0x710_64_V8(const Devices::Can::CanFrame &frame)
        {
            if (frame.length() != 64) {
                qDebug() << __FUNCTION__ << __LINE__ << "Data length error![64]!" << frame.idHex() << frame.dataHex();
                return false;
            }

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            const uint8_t *data = (const uint8_t *)frame.data().data();

            //目标1
            if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }
            float distance = (((data[1] & 0xFFU) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.01);
            if(distance == 0) {
                return true;
            }
            Parser::ParsedDataTypedef::TargetF &target_1 = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
            target_1.mValid = true;
            target_1.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;
            target_1.mRange = (((data[1]) + (((uint16_t)data[0] & 0xFFU) << 8)) * 0.01);
            target_1.mV = (((((data[3] & 0xF0U) >> 4) + (((uint16_t)data[2] & 0xFFU) << 4)) * 0.05) - 112.4);
            target_1.mAngle = ((((data[4]) + (((uint16_t)data[3] & 0xFU) << 8)) * 0.05) - 102.4);
        //    target_1.obj1_rawVeloBin = ((data[6] & 0xC0U) >> 6) + (((uint16_t)data[5] & 0xFFU) << 2);
            target_1.mRCS = (((((data[7] & 0xC0U) >> 6) + (((uint16_t)data[6] & 0x3FU) << 2)) * 0.5) - 50);
        //    target_1.mMeasQuality = (((data[7] & 0x3FU)) * 2);
            target_1.mPitchAngle = (((((data[9] & 0xF0U) >> 4) + (((uint16_t)data[8] & 0xFFU) << 4)) * 0.05) - 102.4);
            target_1.mSNR = ((((data[10] & 0xF0U) >> 4) + (((uint16_t)data[9] & 0xFU) << 4)) * 0.5);
            target_1.mTAG = (Parser::ParsedDataTypedef::TargetTag)(((data[11] & 0xFCU) >> 2) + (((uint16_t)data[10] & 0xFU) << 6));
            target_1.mMAG = ((data[12] & 0xF8U) >> 3) + (((uint16_t)data[11] & 0x3U) << 5);
        //    target_1.mReserve = ((data[15] & 0xC0U) >> 6) + (((uint32_t)data[14]) << 2) + (((uint32_t)data[13]) << 10) + (((uint32_t)data[12] & 0x7U) << 18);
            target_1.mUsing = 1;

            target_1.mMatchFlag = (data[23] & 0x3U);

        //    target_1.mX = target_1.mRange * std::sinf(target_1.mAngle * M_PI / 180);
        //    target_1.mZ = target_1.mRange * std::sinf(target_1.mPitchAngle * M_PI / 180);
        //    target_1.mY = target_1.mRange * std::cosf(target_1.mAngle * M_PI / 180);


            target_1.mValid = target_1.mRange > 0;
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;

        //    qDebug() << __FUNCTION__ << __LINE__ << target_1.mID << target_1.mRange << target_1.mPitchAngle << target_1.mX << target_1.mY;


            if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }
            distance = (((data[17] & 0xFFU) + (((uint16_t)data[16] & 0xFFU) << 8)) * 0.01);
            if(distance == 0) {
                return true;
            }
            Parser::ParsedDataTypedef::TargetF &target_2 = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
            target_2.mValid = true;
            target_2.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;
        //    target_2.mMeasQuality = (((data[15] & 0x3FU)) * 2);
            target_2.mRange = (((data[17]) + (((uint16_t)data[16] & 0xFFU) << 8)) * 0.01);
            target_2.mV = (((((data[19] & 0xF0U) >> 4) + (((uint16_t)data[18] & 0xFFU) << 4)) * 0.05) - 112.4);
            target_2.mAngle = ((((data[20]) + (((uint16_t)data[19] & 0xFU) << 8)) * 0.05) - 102.4);
            target_2.mPitchAngle = (((((data[22] & 0xF0U) >> 4) + (((uint16_t)data[21] & 0xFFU) << 4)) * 0.05) - 102.4);
        //    target_2.obj2_rawVeloBin = ((data[23] & 0xFCU) >> 2) + (((uint16_t)data[22] & 0xFU) << 6);

            target_2.mRCS = ((((data[24])) * 0.5) - 50);
            target_2.mSNR = (((data[25])) * 0.5);
            target_2.mTAG = (Parser::ParsedDataTypedef::TargetTag)(((data[27] & 0xC0U) >> 6) + (((uint16_t)data[26] & 0xFFU) << 2));
            target_2.mMAG = ((data[28] & 0x80U) >> 7) + (((uint16_t)data[27] & 0x3FU) << 1);
        //    target_2.mReserve = ((data[30] & 0xFCU) >> 2) + (((uint32_t)data[29]) << 6) + (((uint32_t)data[28] & 0x7FU) << 14);
            target_2.mMatchFlag = (data[31] & 0x3U);
            target_2.mUsing = 1;

        //    target_2.mX = target_2.mRange * std::sinf(target_2.mAngle * M_PI / 180);
        //    target_2.mZ = target_2.mRange * std::sinf(target_2.mPitchAngle * M_PI / 180);
        //    target_2.mY = target_2.mRange * std::cosf(target_2.mAngle * M_PI / 180);

        //    qDebug() << __FUNCTION__ << __LINE__ << target_2.mID << target_2.mRange << target_2.mPitchAngle << target_2.mX << target_2.mY;
            target_2.mValid = target_2.mRange > 0;
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;


            if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }
            distance = (((data[33] & 0xFFU) + (((uint16_t)data[32] & 0xFFU) << 8)) * 0.01);
            if(distance == 0) {
                return true;
            }
            Parser::ParsedDataTypedef::TargetF &target_3 = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
            target_3.mValid = true;
            target_3.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;
            target_3.mRCS = (((((data[31] & 0xFCU) >> 2) + (((uint16_t)data[30] & 0x3U) << 6)) * 0.5) - 50);

            target_3.mRange = (((data[33]) + (((uint16_t)data[32] & 0xFFU) << 8)) * 0.01);
            target_3.mV = (((((data[35] & 0xF0U) >> 4) + (((uint16_t)data[34] & 0xFFU) << 4)) * 0.05) - 112.4);
            target_3.mAngle = ((((data[36]) + (((uint16_t)data[35] & 0xFU) << 8)) * 0.05) - 102.4);
            target_3.mPitchAngle = (((((data[38] & 0xF0U) >> 4) + (((uint16_t)data[37] & 0xFFU) << 4)) * 0.05) - 102.4);
            target_3.mMatchFlag = ((data[38] & 0xCU) >> 2);
        //    target_3.obj3_rawVeloBin = (data[39]) + (((uint16_t)data[38] & 0x3U) << 8);
            target_3.mSNR = (((data[40])) * 0.5);
            target_3.mTAG = (Parser::ParsedDataTypedef::TargetTag)(((data[42] & 0xC0U) >> 6) + (((uint16_t)data[41] & 0xFFU) << 2));
        //    target_3.mMeasQuality = (((data[42] & 0x3FU)) * 2);
            target_3.mMAG = ((data[43] & 0xFEU) >> 1);
        //    target_3.mReserve = ((data[46] & 0xF0U) >> 4) + (((uint32_t)data[45]) << 4) + (((uint32_t)data[44]) << 12) + (((uint32_t)data[43] & 0x1U) << 20);
            target_3.mUsing = 1;

        //    target_3.mX = target_3.mRange * std::sinf(target_3.mAngle * M_PI / 180);
        //    target_3.mZ = target_3.mRange * std::sinf(target_3.mPitchAngle * M_PI / 180);
        //    target_3.mY = target_3.mRange * std::cosf(target_3.mAngle * M_PI / 180);

        //    qDebug() << __FUNCTION__ << __LINE__ << target_3.mID << target_3.mRange << target_3.mPitchAngle << target_3.mX << target_3.mY;

            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;


            if (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }
            distance = (((data[49] & 0xFFU) + (((uint16_t)data[48] & 0xFFU) << 8)) * 0.01);
            if(distance == 0) {
                return true;
            }
            Parser::ParsedDataTypedef::TargetF &target_4 = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber];
            target_4.mValid = true;
            target_4.mID = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber;
            target_4.mV = ((((data[47]) + (((uint16_t)data[46] & 0xFU) << 8)) * 0.05) - 112.4);
            target_4.mRange = (((data[49]) + (((uint16_t)data[48] & 0xFFU) << 8)) * 0.01);
            target_4.mAngle = (((((data[51] & 0xF0U) >> 4) + (((uint16_t)data[50] & 0xFFU) << 4)) * 0.05) - 102.4);
            target_4.mPitchAngle = ((((data[52]) + (((uint16_t)data[51] & 0xFU) << 8)) * 0.05) - 102.4);
        //    target_4.obj4_rawVeloBin = ((data[54] & 0xC0U) >> 6) + (((uint16_t)data[53] & 0xFFU) << 2);
            target_4.mRCS = (((((data[55] & 0xC0U) >> 6) + (((uint16_t)data[54] & 0x3FU) << 2)) * 0.5) - 50);
        //    target_4.mMeasQuality = (((data[55] & 0x3FU)) * 2);
            target_4.mSNR = (((data[56])) * 0.5);
            target_4.mMatchFlag = ((data[57] & 0xC0U) >> 6);
            target_4.mTAG = (Parser::ParsedDataTypedef::TargetTag)(((data[58] & 0xF0U) >> 4) + (((uint16_t)data[57] & 0x3FU) << 4));
            target_4.mMAG = ((data[59] & 0xE0U) >> 5) + (((uint16_t)data[58] & 0xFU) << 3);
        //    target_4.mReserve = (data[61]) + (((uint32_t)data[60]) << 8) + (((uint32_t)data[59] & 0x1FU) << 16);
        //    target_4.re00 = ((data[62] & 0xF0U) >> 4);
        //    target_4.rawRollingCnt = (data[62] & 0xFU);
        //    target_4.rawCheckSum = (data[63]);
            target_4.mUsing = 1;

        //    target_4.mX = target_4.mRange * std::sinf(target_4.mAngle * M_PI / 180);
        //    target_4.mZ = target_4.mRange * std::sinf(target_4.mPitchAngle * M_PI / 180);
        //    target_4.mY = target_4.mRange * std::cosf(target_4.mAngle * M_PI / 180);
            target_4.mValid = target_4.mRange > 0;
            mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber++;

        //    qDebug() << __FUNCTION__ << __LINE__ << target_4.mID << target_4.mRange << target_4.mPitchAngle << target_4.mX << target_4.mY;
            return true;
        }

        bool CANProtocolChengTech710::parse0x710_64(const Devices::Can::CanFrame &frame)
        {
            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            switch (mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mRadarInfomation.mProtocolVersion) {
            case 6:
                return parse0x710_64_V6(frame);
            case 7:
                return parse0x710_64_V7(frame);
            case 8:
                return parse0x710_64_V8(frame);
            }

            return false;
        }

        bool CANProtocolChengTech710::parse0x710(const Devices::Can::CanFrame &frame)
		{
            switch (frame.length()){
			case 8:
                return parse0x710_8(frame);
			case 64:
                return parse0x710_64(frame);
			default:
				break;
			}

            return false;
        }

        bool CANProtocolChengTech710::parse0x720(const Devices::Can::CanFrame &frame)
        {
            if (frame.length() != 8) {
                return false;
            }

            Parser::ParsedDataTypedef::ParsedData *mParsedData  = &(mAnalysisWorker->mParsedData);
            uint16_t effectiveNumber = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mEffectiveNumber - 1;
            if (effectiveNumber >= TARGET_MAX_COUNT) {
                return false;
            }

            const uint8_t *data = (const uint8_t *)frame.data().data();

            Parser::ParsedDataTypedef::TargetF &target = mParsedData->mTargets[Parser::ParsedDataTypedef::TargetRaw].mTargets[effectiveNumber];

            target.mDMLMode = ((data[0] & 0x80U) >> 7);
//            target.res = ((data[1] & 0xFCU) >> 2) + (((uint16_t)data[0] & 0x7FU) << 6);
            target.mDeltaVelocity = ((((data[2] & 0xFCU) >> 2) + (((uint16_t)data[1] & 0x3U) << 6)) * 0.1);
            target.mDeltaRange = ((((data[3] & 0xFCU) >> 2) + (((uint16_t)data[2] & 0x3U) << 6)) * 0.1);
            target.mThreshold3T = (((data[4]) + (((uint16_t)data[3] & 0x3U) << 8)) * 0.1);
            target.mMatchFlag = ((data[5] & 0xC0U) >> 6);
            int16_t a = (((data[6] & 0xFCU) >> 2) + (((uint16_t)data[5] & 0x3FU) << 6));
            target.mAy = (((float)((int16_t)((a & 0x800) ? (a | 0xF000) : (a & 0xFFF)))) * 0.05);
            target.mRCS = ((data[7]) + (((uint16_t)data[6] & 0x3U) << 8));

            return true;
        }
	}
}
