﻿#pragma once

#include "ianalysisprotocol.h"

#include <vector>

namespace Analysis {

namespace Protocol {
        class CANProtocolHozonADF :
            public IAnalysisProtocol
		{
		public:
            CANProtocolHozonADF(AnalysisWorker *analysisWorker, QObject *parent = nullptr);
            ~CANProtocolHozonADF();

            bool analysisFrame(const Devices::Can::CanFrame &frame) override;
            void setHozonBreakShort(bool breakShort) { mBreakShort = breakShort; }

        private:
            bool parseHeaderTrack(const Devices::Can::CanFrame &frame, int radarID);
            bool parseTargetTrack(const Devices::Can::CanFrame &frame, int radarID);
            void analysisEndADF(int radarID);

            bool mBreakShort{true};
		};
	}
}

